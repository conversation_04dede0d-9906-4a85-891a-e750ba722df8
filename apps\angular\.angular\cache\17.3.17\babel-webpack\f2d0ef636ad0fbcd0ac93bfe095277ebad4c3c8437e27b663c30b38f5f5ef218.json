{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/tooltip\";\nimport * as i8 from \"@ngx-validate/core\";\nimport * as i9 from \"@angular/common\";\nfunction UpdateCtsSettingDialogComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" System User Password last updated at: \", i0.ɵɵpipeBind2(2, 1, ctx_r1.data == null ? null : ctx_r1.data.systemUserPasswordUpdatedAt, \"dd/MM/yyyy\"), \" \");\n  }\n}\nfunction UpdateCtsSettingDialogComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1.selectedFile.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFile.name, \" \");\n  }\n}\nfunction UpdateCtsSettingDialogComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdateCtsSettingDialogComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" SFTP SSH Key last updated at: \", i0.ɵɵpipeBind2(2, 1, ctx_r1.data == null ? null : ctx_r1.data.sftpSSHKeyUpdatedAt, \"dd/MM/yyyy\"), \" \");\n  }\n}\nexport let UpdateCtsSettingDialogComponent = /*#__PURE__*/(() => {\n  class UpdateCtsSettingDialogComponent {\n    get displaySftpSshKey() {\n      const key = this.data?.sftpSSHKey;\n      if (key && key.length > 30) {\n        return `${key.substring(0, 30)}...`;\n      }\n      return key || '';\n    }\n    constructor(dialogRef, data, fb) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.fb = fb;\n      this.selectedFile = null;\n      this.passwordVisible = false;\n      this.form = this.fb.group({\n        systemUserName: [data?.systemUserName || '', Validators.required],\n        systemUserPassword: [data?.systemUserPassword, Validators.required],\n        sftpUserName: [data?.sftpUserName || '', Validators.required],\n        sftpSshKey: [this.displaySftpSshKey || ''],\n        file: [null]\n      });\n    }\n    onFileChange(event) {\n      const file = event.target.files[0];\n      if (file) {\n        this.selectedFile = file;\n        this.form.patchValue({\n          file\n        });\n        this.form.get('file')?.updateValueAndValidity();\n      } else {\n        this.selectedFile = null;\n        this.form.patchValue({\n          file: null\n        });\n        this.form.get('file')?.updateValueAndValidity();\n      }\n    }\n    togglePasswordVisibility() {\n      this.passwordVisible = !this.passwordVisible;\n    }\n    onCancel() {\n      this.dialogRef.close();\n    }\n    onSubmit() {\n      if (this.form.invalid) return;\n      this.dialogRef.close({\n        ...this.form.value,\n        id: this.data?.id,\n        file: this.selectedFile\n      });\n    }\n    static {\n      this.ɵfac = function UpdateCtsSettingDialogComponent_Factory(t) {\n        return new (t || UpdateCtsSettingDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UpdateCtsSettingDialogComponent,\n        selectors: [[\"app-update-cts-setting-dialog\"]],\n        decls: 57,\n        vars: 8,\n        consts: [[\"noFile\", \"\"], [\"fileInput\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"row\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"col-md-4\"], [1, \"w-100\"], [\"matInput\", \"\", \"formControlName\", \"systemUserName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"systemUserPassword\", \"required\", \"\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"last-updated\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"sftpUserName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"sftpSshKey\", \"readonly\", \"\"], [1, \"file-upload-group\"], [1, \"file-upload-label\"], [1, \"file-upload-row\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"class\", \"file-name\", 3, \"matTooltip\", 4, \"ngIf\", \"ngIfElse\"], [\"accept\", \"*\", \"type\", \"file\", 1, \"file-input\", 3, \"change\"], [\"align\", \"end\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"last-updated\"], [1, \"file-name\", 3, \"matTooltip\"], [1, \"file-placeholder\"]],\n        template: function UpdateCtsSettingDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n            i0.ɵɵtext(3, \"CTS Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 5)(5, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵelement(6, \"i\", 7);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(7, \"form\", 8);\n            i0.ɵɵlistener(\"ngSubmit\", function UpdateCtsSettingDialogComponent_Template_form_ngSubmit_7_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"div\", 3)(10, \"div\", 9)(11, \"mat-form-field\", 10)(12, \"mat-label\");\n            i0.ɵɵtext(13, \"System User Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"input\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 9)(16, \"mat-form-field\", 10)(17, \"mat-label\");\n            i0.ɵɵtext(18, \"System User Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"input\", 12);\n            i0.ɵɵelementStart(20, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_20_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.togglePasswordVisibility());\n            });\n            i0.ɵɵelementStart(21, \"mat-icon\");\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(23, \"div\", 3);\n            i0.ɵɵtemplate(24, UpdateCtsSettingDialogComponent_div_24_Template, 3, 4, \"div\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 3)(26, \"div\", 9)(27, \"mat-form-field\", 10)(28, \"mat-label\");\n            i0.ɵɵtext(29, \"SFTP User Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 9)(32, \"mat-form-field\", 10)(33, \"mat-label\");\n            i0.ɵɵtext(34, \"SFTP SSH Key\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 17)(38, \"label\", 18);\n            i0.ɵɵtext(39, \"Upload New Key File\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 19)(41, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_41_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r3 = i0.ɵɵreference(49);\n              return i0.ɵɵresetView(fileInput_r3.click());\n            });\n            i0.ɵɵelementStart(42, \"mat-icon\");\n            i0.ɵɵtext(43, \"upload_file\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(44, \" Choose File \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(45, UpdateCtsSettingDialogComponent_span_45_Template, 2, 2, \"span\", 21)(46, UpdateCtsSettingDialogComponent_ng_template_46_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"input\", 22, 1);\n            i0.ɵɵlistener(\"change\", function UpdateCtsSettingDialogComponent_Template_input_change_48_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileChange($event));\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(50, \"div\", 3);\n            i0.ɵɵtemplate(51, UpdateCtsSettingDialogComponent_div_51_Template, 3, 4, \"div\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"mat-dialog-actions\", 23)(53, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_53_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵtext(54, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"button\", 25);\n            i0.ɵɵtext(56, \"Save\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const noFile_r4 = i0.ɵɵreference(47);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.form);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"type\", ctx.passwordVisible ? \"text\" : \"password\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.passwordVisible ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.systemUserPasswordUpdatedAt);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedFile)(\"ngIfElse\", noFile_r4);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.sftpSSHKeyUpdatedAt);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n          }\n        },\n        dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.FormGroupDirective, i2.FormControlName, i3.MatInput, i4.MatFormField, i4.MatLabel, i4.MatSuffix, i5.MatIcon, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i6.MatButton, i6.MatIconButton, i7.MatTooltip, i8.ValidationGroupDirective, i8.ValidationDirective, i9.NgIf, i9.DatePipe],\n        styles: [\".title[_ngcontent-%COMP%]{font-size:1.3em;color:#00779b;display:block}.modal-action-button[_ngcontent-%COMP%]{font-size:1em}.action-buttons[_ngcontent-%COMP%]{margin-top:16px;display:flex}.w-100[_ngcontent-%COMP%]{width:100%}.last-updated[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:-10px;margin-bottom:10px}.file-name-row[_ngcontent-%COMP%]{min-height:24px;display:flex;align-items:center;margin-top:2px}.aligned-message[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:8px;margin-bottom:8px}.file-upload-group[_ngcontent-%COMP%]{margin-bottom:16px;display:flex;flex-direction:column}.file-upload-label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.file-upload-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-bottom:4px}.file-input[_ngcontent-%COMP%]{display:none}.file-name[_ngcontent-%COMP%]{flex:1 1 auto;max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333;cursor:pointer}.file-placeholder[_ngcontent-%COMP%]{color:#888;font-style:italic}\"]\n      });\n    }\n  }\n  return UpdateCtsSettingDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}