{"ast": null, "code": "import _asyncToGenerator from \"c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClient } from '@angular/common/http';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AppConfigService {\n  constructor(httpBackend) {\n    this.httpBackend = httpBackend;\n    this.http = new HttpClient(this.httpBackend);\n  }\n  init(source) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.envConfig = yield lastValueFrom(_this.http.get(source));\n    })();\n  }\n  getEnvConfig() {\n    return this.envConfig;\n  }\n  static {\n    this.ɵfac = function AppConfigService_Factory(t) {\n      return new (t || AppConfigService)(i0.ɵɵinject(i1.HttpBackend));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AppConfigService,\n      factory: AppConfigService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpClient", "lastValueFrom", "AppConfigService", "constructor", "httpBackend", "http", "init", "source", "_this", "_asyncToGenerator", "envConfig", "get", "getEnvConfig", "i0", "ɵɵinject", "i1", "HttpBackend", "factory", "ɵfac", "providedIn"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\services\\app.config.service.ts"], "sourcesContent": ["\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpBackend } from '@angular/common/http';\r\n\r\nimport { lastValueFrom } from 'rxjs';\r\n\r\nimport { IAppConfig } from '../interfaces';\r\n\r\n\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AppConfigService {\r\n\r\n    /*\r\n        src/configuration/app.config.json by default in source contains settings for local development\r\n        This application will ALWAYS load contents from this file\r\n\r\n        During release pipelines, file transforms are performed to copy the contents of the\r\n        respective app.config.XXX.json files (dev, qa, prod, etc) into this one\r\n\r\n        Anywhere in the application, where app settings are required, bind the instance of this service in its constructor\r\n\r\n        e.g. constructor(private appConfigService: AppConfigService) { }\r\n        ...\r\n        const clientId = this.appConfigService.settings.clientId\r\n    */\r\n\r\n    private envConfig!: IAppConfig;\r\n    private http: HttpClient;\r\n\r\n\r\n\r\n    constructor(private readonly httpBackend: HttpBackend) {\r\n\r\n        this.http = new HttpClient(this.httpBackend);\r\n    }\r\n\r\n\r\n\r\n    public async init(source: string): Promise<void> {\r\n\r\n        this.envConfig = await lastValueFrom(this.http.get<IAppConfig>(source));\r\n    }\r\n\r\n    public getEnvConfig(): IAppConfig {\r\n\r\n        return this.envConfig;\r\n    }\r\n}\r\n"], "mappings": ";AAEA,SAASA,UAAU,QAAqB,sBAAsB;AAE9D,SAASC,aAAa,QAAQ,MAAM;;;AAOpC,OAAM,MAAOC,gBAAgB;EAqBzBC,YAA6BC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAEpC,IAAI,CAACC,IAAI,GAAG,IAAIL,UAAU,CAAC,IAAI,CAACI,WAAW,CAAC;EAChD;EAIaE,IAAIA,CAACC,MAAc;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAE5BD,KAAI,CAACE,SAAS,SAAST,aAAa,CAACO,KAAI,CAACH,IAAI,CAACM,GAAG,CAAaJ,MAAM,CAAC,CAAC;IAAC;EAC5E;EAEOK,YAAYA,CAAA;IAEf,OAAO,IAAI,CAACF,SAAS;EACzB;;;uBApCSR,gBAAgB,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAhBd,gBAAgB;MAAAe,OAAA,EAAhBf,gBAAgB,CAAAgB,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}