{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Optional, SkipSelf, Component, ChangeDetectionStrategy, Input, ViewChild, InjectionToken, inject, Injectable, Pipe, ChangeDetectorRef, Injector, Directive, ViewChildren, EventEmitter, LOCALE_ID, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { ReactiveFormsModule, ControlContainer, Validators, FormGroupDirective, FormsModule, UntypedFormGroup, UntypedFormControl } from '@angular/forms';\nimport * as i1 from '@ng-bootstrap/ng-bootstrap';\nimport { NgbInputDatepicker, NgbTimepicker, NgbDatepickerModule, NgbTimepickerModule, NgbDateAdapter, NgbTimeAdapter, NgbTooltip, NgbTypeaheadModule, NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport { LinkedList } from '@abp/utils';\nimport { DateTimeAdapter, DisabledDirective, DateAdapter, TimeAdapter, EllipsisDirective, AbpVisibleDirective, NgxDatatableDefaultDirective, NgxDatatableListDirective, ThemeSharedModule } from '@abp/ng.theme.shared';\nimport * as i5 from '@angular/common';\nimport { CommonModule, NgClass, NgTemplateOutlet, formatDate, AsyncPipe, NgComponentOutlet } from '@angular/common';\nimport * as i2$1 from '@abp/ng.core';\nimport { RestService, ConfigStateService, AbpValidators, TrackByService, ShowPasswordDirective, PermissionDirective, LocalizationModule, escapeHtmlChars, PermissionService, getShortDateShortTimeFormat, getShortTimeFormat, getShortDateFormat, LocalizationService, createLocalizationPipeKeyGenerator, CoreModule } from '@abp/ng.core';\nimport { of, merge, pipe, zip } from 'rxjs';\nimport { map, debounceTime, distinctUntilChanged, switchMap, filter, take } from 'rxjs/operators';\nimport * as i3 from '@ngx-validate/core';\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\nimport * as i1$1 from '@swimlane/ngx-datatable';\nimport { NgxDatatableModule } from '@swimlane/ngx-datatable';\nconst _c0 = [\"field\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nconst _c1 = () => ({\n  $implicit: \"form-check-label\"\n});\nconst _c2 = () => ({\n  standalone: true\n});\nconst _c3 = (a0, a1) => ({\n  \"fa-eye-slash\": a0,\n  \"fa-eye\": a1\n});\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngComponentOutlet\", ctx_r0.prop.template)(\"ngComponentOutletInjector\", ctx_r0.injectorForCustomComponent);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_3_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_3_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelement(1, \"input\", 22, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name)(\"autocomplete\", ctx_r0.prop.autocomplete)(\"type\", ctx_r0.getType(ctx_r0.prop))(\"abpDisabled\", ctx_r0.disabled)(\"readonly\", ctx_r0.readonly);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 23);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", ctx_r0.prop.name);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_5_ng_template_3_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"input\", 25, 1);\n    i0.ɵɵtemplate(3, ExtensibleFormPropComponent_ng_container_0_ng_template_5_ng_template_3_Template, 0, 0, \"ng-template\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name)(\"abpDisabled\", ctx_r0.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction0(5, _c1));\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_6_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_6_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.key, \" \");\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_6_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementStart(1, \"select\", 27, 1);\n    i0.ɵɵrepeaterCreate(3, ExtensibleFormPropComponent_ng_container_0_ng_template_6_For_4_Template, 2, 2, \"option\", 28, _forTrack0);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name)(\"abpDisabled\", ctx_r0.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(i0.ɵɵpipeBind1(5, 4, ctx_r0.options$));\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_7_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_7_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", option_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r4.key, \" \");\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_7_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementStart(1, \"select\", 29, 1);\n    i0.ɵɵtemplate(3, ExtensibleFormPropComponent_ng_container_0_ng_template_7_option_3_Template, 2, 2, \"option\", 30);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name)(\"abpDisabled\", ctx_r0.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(4, 6, ctx_r0.options$))(\"ngForTrackBy\", ctx_r0.track.by(\"value\"));\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_8_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_8_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementStart(1, \"div\", 31, 2)(3, \"input\", 32, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.typeaheadModel, $event) || (ctx_r0.typeaheadModel = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectItem\", function ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template_input_selectItem_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.setTypeaheadValue($event.item));\n    })(\"blur\", function ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template_input_blur_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.setTypeaheadValue(ctx_r0.typeaheadModel));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const typeahead_r6 = i0.ɵɵreference(2);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-invalid\", typeahead_r6.classList.contains(\"is-invalid\"));\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"autocomplete\", ctx_r0.prop.autocomplete)(\"abpDisabled\", ctx_r0.disabled)(\"ngbTypeahead\", ctx_r0.search)(\"editable\", false)(\"inputFormatter\", ctx_r0.typeaheadFormatter)(\"resultFormatter\", ctx_r0.typeaheadFormatter)(\"ngModelOptions\", i0.ɵɵpureFunction0(13, _c2));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.typeaheadModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControlName\", ctx_r0.prop.name);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_9_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_9_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementStart(1, \"input\", 33, 3);\n    i0.ɵɵlistener(\"click\", function ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template_input_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const datepicker_r8 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(datepicker_r8.open());\n    })(\"keyup.space\", function ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template_input_keyup_space_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const datepicker_r8 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(datepicker_r8.open());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_10_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_10_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelement(1, \"ngb-timepicker\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", ctx_r0.prop.name);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_11_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_11_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelement(1, \"abp-extensible-date-time-picker\", 35);\n    i0.ɵɵpipe(2, \"async\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"prop\", ctx_r0.prop)(\"meridian\", i0.ɵɵpipeBind1(2, 3, ctx_r0.meridian$));\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_12_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_12_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelement(1, \"textarea\", 36, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name)(\"abpDisabled\", ctx_r0.disabled)(\"readonly\", ctx_r0.readonly);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_13_ng_template_0_Template(rf, ctx) {}\nfunction ExtensibleFormPropComponent_ng_container_0_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_ng_template_13_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementStart(1, \"div\", 37);\n    i0.ɵɵelement(2, \"input\", 38);\n    i0.ɵɵelementStart(3, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ExtensibleFormPropComponent_ng_container_0_ng_template_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.showPassword = !ctx_r0.showPassword);\n    });\n    i0.ɵɵelement(4, \"i\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const label_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", label_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", ctx_r0.prop.id)(\"formControlName\", ctx_r0.prop.name)(\"abpShowPassword\", ctx_r0.showPassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c3, !ctx_r0.showPassword, ctx_r0.showPassword));\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r0.prop.formText));\n  }\n}\nfunction ExtensibleFormPropComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 5);\n    i0.ɵɵtemplate(1, ExtensibleFormPropComponent_ng_container_0_ng_template_1_Template, 1, 2, \"ng-template\", 6);\n    i0.ɵɵelementStart(2, \"div\", 7);\n    i0.ɵɵtemplate(3, ExtensibleFormPropComponent_ng_container_0_ng_template_3_Template, 3, 7, \"ng-template\", 8)(4, ExtensibleFormPropComponent_ng_container_0_ng_template_4_Template, 1, 1, \"ng-template\", 9)(5, ExtensibleFormPropComponent_ng_container_0_ng_template_5_Template, 4, 6, \"ng-template\", 10)(6, ExtensibleFormPropComponent_ng_container_0_ng_template_6_Template, 6, 6, \"ng-template\", 11)(7, ExtensibleFormPropComponent_ng_container_0_ng_template_7_Template, 5, 8, \"ng-template\", 12)(8, ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template, 6, 14, \"ng-template\", 13)(9, ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template, 3, 3, \"ng-template\", 14)(10, ExtensibleFormPropComponent_ng_container_0_ng_template_10_Template, 2, 2, \"ng-template\", 15)(11, ExtensibleFormPropComponent_ng_container_0_ng_template_11_Template, 3, 5, \"ng-template\", 16)(12, ExtensibleFormPropComponent_ng_container_0_ng_template_12_Template, 3, 5, \"ng-template\", 17)(13, ExtensibleFormPropComponent_ng_container_0_ng_template_13_Template, 5, 8, \"ng-template\", 18)(14, ExtensibleFormPropComponent_ng_container_0_Conditional_14_Template, 3, 3, \"small\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r0.getComponent(ctx_r0.prop));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.containerClassName);\n    i0.ɵɵadvance(12);\n    i0.ɵɵconditional(14, ctx_r0.prop.formText ? 14 : -1);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_template_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, ctx_r0.prop.displayTextResolver(ctx_r0.data)), \" \");\n  }\n}\nfunction ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, \"::\" + ctx_r0.prop.displayName), \" \");\n  }\n}\nfunction ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, ctx_r0.prop.displayName), \" \");\n  }\n}\nfunction ExtensibleFormPropComponent_ng_template_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_0_Template, 2, 3)(1, ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_1_Template, 2, 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r0.prop.isExtra ? 0 : 1);\n  }\n}\nfunction ExtensibleFormPropComponent_ng_template_1_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 42);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 2, ctx_r0.prop.tooltip.text))(\"placement\", ctx_r0.prop.tooltip.placement || \"auto\");\n  }\n}\nfunction ExtensibleFormPropComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 41);\n    i0.ɵɵtemplate(1, ExtensibleFormPropComponent_ng_template_1_Conditional_1_Template, 2, 3)(2, ExtensibleFormPropComponent_ng_template_1_Conditional_2_Template, 2, 1);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ExtensibleFormPropComponent_ng_template_1_Conditional_4_Template, 2, 4, \"i\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classes_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"htmlFor\", ctx_r0.prop.id)(\"ngClass\", classes_r10 || \"form-label\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.prop.displayTextResolver ? 1 : 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.asterisk, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r0.prop.tooltip ? 4 : -1);\n  }\n}\nconst _forTrack1 = ($index, $item) => $item.name;\nconst _c4 = (a0, a1, a2) => ({\n  groupedProp: a0,\n  data: a1,\n  isFirstGroup: a2\n});\nfunction ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelementContainer(1, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const groupedProp_r3 = ctx_r1.$implicit;\n    const ɵi_2_r4 = ctx_r1.$index;\n    i0.ɵɵnextContext(2);\n    const propListTemplate_r5 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngClass\", groupedProp_r3.group == null ? null : groupedProp_r3.group.className);\n    i0.ɵɵattribute(\"data-name\", (groupedProp_r3.group == null ? null : groupedProp_r3.group.name) || (groupedProp_r3.group == null ? null : groupedProp_r3.group.className));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", propListTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(4, _c4, groupedProp_r3, data_r1, ɵi_2_r4 === 0));\n  }\n}\nfunction ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 3);\n  }\n  if (rf & 2) {\n    const data_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const groupedProp_r3 = ctx_r1.$implicit;\n    const ɵi_2_r4 = ctx_r1.$index;\n    i0.ɵɵnextContext(2);\n    const propListTemplate_r5 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", propListTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c4, groupedProp_r3, data_r1, ɵi_2_r4 === 0));\n  }\n}\nfunction ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_1_Template, 2, 8, \"div\", 2)(2, ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_2_Template, 1, 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const data_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const groupedProp_r3 = ctx_r1.$implicit;\n    const i_r6 = ctx_r1.$index;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r6.isAnyGroupMemberVisible(i_r6, data_r1) && (groupedProp_r3.group == null ? null : groupedProp_r3.group.className) ? 1 : 2);\n  }\n}\nfunction ExtensibleFormComponent_Conditional_0_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Template, 3, 1, \"ng-container\", 1);\n  }\n  if (rf & 2) {\n    const groupedProp_r3 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"abpPropDataFromList\", groupedProp_r3.formPropList)(\"abpPropDataWithRecord\", ctx_r6.record);\n  }\n}\nfunction ExtensibleFormComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, ExtensibleFormComponent_Conditional_0_For_1_Template, 1, 2, \"ng-container\", null, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r6.groupedPropList.items);\n  }\n}\nfunction ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 4);\n    i0.ɵɵelement(1, \"abp-extensible-form-prop\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prop_r8 = i0.ɵɵnextContext(2).$implicit;\n    const data_r9 = i0.ɵɵnextContext().data;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", ctx_r6.extraPropertiesKey);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(prop_r8.className);\n    i0.ɵɵproperty(\"prop\", prop_r8)(\"data\", data_r9);\n  }\n}\nfunction ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-extensible-form-prop\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    const prop_r8 = ctx_r9.$implicit;\n    const ɵindex_17_r11 = ctx_r9.$index;\n    const ctx_r11 = i0.ɵɵnextContext();\n    const data_r9 = ctx_r11.data;\n    const isFirstGroup_r13 = ctx_r11.isFirstGroup;\n    i0.ɵɵclassMap(prop_r8.className);\n    i0.ɵɵproperty(\"prop\", prop_r8)(\"data\", data_r9)(\"first\", ɵindex_17_r11 === 0)(\"isFirstGroup\", isFirstGroup_r13);\n  }\n}\nfunction ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Conditional_0_Template, 1, 6, \"abp-extensible-form-prop\", 6);\n  }\n  if (rf & 2) {\n    const prop_r8 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r6.form.get(prop_r8.name) ? 0 : -1);\n  }\n}\nfunction ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_0_Template, 2, 5, \"ng-container\", 4)(1, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const prop_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r6.extraProperties.controls[prop_r8.name] ? 0 : 1);\n  }\n}\nfunction ExtensibleFormComponent_ng_template_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Template, 2, 1);\n  }\n  if (rf & 2) {\n    const prop_r8 = ctx.$implicit;\n    const data_r9 = i0.ɵɵnextContext().data;\n    i0.ɵɵconditional(0, prop_r8.visible(data_r9) ? 0 : -1);\n  }\n}\nfunction ExtensibleFormComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, ExtensibleFormComponent_ng_template_1_For_1_Template, 1, 1, null, null, _forTrack1);\n  }\n  if (rf & 2) {\n    const groupedProp_r14 = ctx.groupedProp;\n    i0.ɵɵrepeater(groupedProp_r14.formPropList);\n  }\n}\nconst _forTrack2 = ($index, $item) => $item.text;\nconst _c5 = a0 => ({\n  $implicit: a0\n});\nfunction GridActionsComponent_Conditional_0_For_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 4);\n  }\n  if (rf & 2) {\n    const action_r1 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const dropDownBtnItemTmp_r2 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", dropDownBtnItemTmp_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, action_r1));\n  }\n}\nfunction GridActionsComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"button\", 5);\n    i0.ɵɵelement(2, \"i\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵrepeaterCreate(6, GridActionsComponent_Conditional_0_For_7_Template, 1, 4, \"ng-container\", 4, _forTrack2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"me-1\", ctx_r2.icon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 4, ctx_r2.text), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵrepeater(ctx_r2.actionList);\n  }\n}\nfunction GridActionsComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 4);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const btnTmp_r4 = i0.ɵɵreference(7);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btnTmp_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, ctx_r2.actionList.get(0).value));\n  }\n}\nfunction GridActionsComponent_ng_template_2_Conditional_0_button_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GridActionsComponent_ng_template_2_Conditional_0_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function GridActionsComponent_ng_template_2_Conditional_0_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const action_r6 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(action_r6.action(ctx_r2.data));\n    });\n    i0.ɵɵtemplate(1, GridActionsComponent_ng_template_2_Conditional_0_button_0_ng_container_1_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵnextContext();\n    const buttonContentTmp_r7 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buttonContentTmp_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, action_r6));\n  }\n}\nfunction GridActionsComponent_ng_template_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_2_Conditional_0_button_0_Template, 2, 4, \"button\", 9);\n  }\n  if (rf & 2) {\n    const action_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"abpPermission\", action_r6.permission)(\"abpPermissionRunChangeDetection\", false);\n  }\n}\nfunction GridActionsComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_2_Conditional_0_Template, 1, 2, \"button\", 8);\n  }\n  if (rf & 2) {\n    const action_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, action_r6.visible(ctx_r2.data) ? 0 : -1);\n  }\n}\nfunction GridActionsComponent_ng_template_4_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, action_r8.text));\n  }\n}\nfunction GridActionsComponent_ng_template_4_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, action_r8.text));\n  }\n}\nfunction GridActionsComponent_ng_template_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_4_Conditional_1_Conditional_0_Template, 3, 3, \"span\")(1, GridActionsComponent_ng_template_4_Conditional_1_Conditional_1_Template, 3, 3);\n  }\n  if (rf & 2) {\n    const action_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional(0, action_r8.icon ? 0 : 1);\n  }\n}\nfunction GridActionsComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 6);\n    i0.ɵɵtemplate(1, GridActionsComponent_ng_template_4_Conditional_1_Template, 2, 1);\n  }\n  if (rf & 2) {\n    const action_r8 = ctx.$implicit;\n    i0.ɵɵclassProp(\"me-1\", action_r8.icon && !action_r8.showOnlyIcon);\n    i0.ɵɵproperty(\"ngClass\", action_r8.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, !action_r8.showOnlyIcon ? 1 : -1);\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n    i0.ɵɵlistener(\"click\", function GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const action_r10 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(action_r10.action(ctx_r2.data));\n    });\n    i0.ɵɵtemplate(2, GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_ng_container_2_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r10 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵnextContext();\n    const buttonContentTmp_r7 = i0.ɵɵreference(5);\n    i0.ɵɵstyleMap(action_r10.btnStyle);\n    i0.ɵɵclassMap(action_r10.btnClass);\n    i0.ɵɵproperty(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 8, action_r10.tooltip.text))(\"placement\", action_r10.tooltip.placement || \"auto\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buttonContentTmp_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c5, action_r10));\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template, 3, 12, \"button\", 14);\n  }\n  if (rf & 2) {\n    const action_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"abpPermission\", action_r10.permission)(\"abpPermissionRunChangeDetection\", false);\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const action_r10 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(action_r10.action(ctx_r2.data));\n    });\n    i0.ɵɵtemplate(1, GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_ng_container_1_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r10 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵnextContext();\n    const buttonContentTmp_r7 = i0.ɵɵreference(5);\n    i0.ɵɵstyleMap(action_r10.btnStyle);\n    i0.ɵɵclassMap(action_r10.btnClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buttonContentTmp_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c5, action_r10));\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template, 2, 8, \"button\", 16);\n  }\n  if (rf & 2) {\n    const action_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"abpPermission\", action_r10.permission)(\"abpPermissionRunChangeDetection\", false);\n  }\n}\nfunction GridActionsComponent_ng_template_6_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_Template, 1, 2, \"button\", 13)(1, GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_Template, 1, 2);\n  }\n  if (rf & 2) {\n    const action_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional(0, action_r10.tooltip ? 0 : 1);\n  }\n}\nfunction GridActionsComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Template, 2, 1);\n  }\n  if (rf & 2) {\n    const action_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, action_r10.visible(ctx_r2.data) ? 0 : -1);\n  }\n}\nconst _c6 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction ExtensibleTableComponent_Conditional_1_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-grid-actions\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const row_r2 = ctx_r0.row;\n    const i_r3 = ctx_r0.rowIndex;\n    i0.ɵɵproperty(\"index\", i_r3)(\"record\", row_r2);\n  }\n}\nfunction ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Conditional_0_Template, 1, 2, \"abp-grid-actions\", 6);\n  }\n  if (rf & 2) {\n    const row_r2 = i0.ɵɵnextContext().row;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r3.isVisibleActions(row_r2) ? 0 : -1);\n  }\n}\nfunction ExtensibleTableComponent_Conditional_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleTableComponent_Conditional_1_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 5)(1, ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const row_r2 = ctx.row;\n    const i_r3 = ctx.rowIndex;\n    const gridActions_r5 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.actionsTemplate || gridActions_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c6, row_r2, i_r3));\n  }\n}\nfunction ExtensibleTableComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngx-datatable-column\", 2);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n    i0.ɵɵtemplate(2, ExtensibleTableComponent_Conditional_1_ng_template_2_Template, 3, 5, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"name\", i0.ɵɵpipeBind1(1, 4, ctx_r3.actionsText))(\"maxWidth\", ctx_r3.columnWidths[0])(\"width\", ctx_r3.columnWidths[0])(\"sortable\", false);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelement(3, \"i\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r6 = i0.ɵɵnextContext().column;\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 3, prop_r7.tooltip.text))(\"placement\", prop_r7.tooltip.placement || \"auto\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", column_r6.name, \" \");\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const column_r6 = i0.ɵɵnextContext().column;\n    i0.ɵɵtextInterpolate1(\" \", column_r6.name, \" \");\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_0_Template, 4, 5, \"span\", 9)(1, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵconditional(0, prop_r7.tooltip ? 0 : 1);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"click\", function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      const row_r10 = ctx_r8.row;\n      const i_r11 = ctx_r8.index;\n      const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(prop_r7.action && prop_r7.action({\n        getInjected: ctx_r3.getInjected,\n        record: row_r10,\n        index: i_r11\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r10 = i0.ɵɵnextContext(3).row;\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"pointer\", prop_r7.action);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 4, row_r10[\"_\" + prop_r7.name] == null ? null : row_r10[\"_\" + prop_r7.name].value), i0.ɵɵsanitizeHtml)(\"ngClass\", ctx_r3.entityPropTypeClasses[prop_r7.type]);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_ng_container_0_Template, 1, 0, \"ng-container\", 15);\n  }\n  if (rf & 2) {\n    const row_r10 = i0.ɵɵnextContext(3).row;\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngComponentOutlet\", row_r10[\"_\" + prop_r7.name].component)(\"ngComponentOutletInjector\", row_r10[\"_\" + prop_r7.name].injector);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template, 2, 6, \"div\", 13)(2, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_Template, 1, 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const row_r10 = i0.ɵɵnextContext(2).row;\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, !row_r10[\"_\" + prop_r7.name].component ? 1 : 2);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Template, 3, 1, \"ng-container\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const row_r10 = i0.ɵɵnextContext().row;\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpVisible\", row_r10[\"_\" + prop_r7.name] == null ? null : row_r10[\"_\" + prop_r7.name].visible);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_Template, 2, 1, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const prop_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"abpPermission\", prop_r7.permission)(\"abpPermissionRunChangeDetection\", false);\n  }\n}\nfunction ExtensibleTableComponent_For_3_ngx_datatable_column_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngx-datatable-column\", 3);\n    i0.ɵɵpipe(1, \"abpLocalization\");\n    i0.ɵɵtemplate(2, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Template, 2, 1, \"ng-template\", 8)(3, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_Template, 1, 2, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    const prop_r7 = ctx_r11.$implicit;\n    const i_r13 = ctx_r11.$index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"width\", ctx_r3.columnWidths[i_r13 + 1] || 200)(\"name\", i0.ɵɵpipeBind1(1, 4, prop_r7.displayName))(\"prop\", prop_r7.name)(\"sortable\", prop_r7.sortable);\n  }\n}\nfunction ExtensibleTableComponent_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_Template, 4, 6, \"ngx-datatable-column\", 7);\n  }\n  if (rf & 2) {\n    const prop_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"abpVisible\", prop_r7.columnVisible(ctx_r3.getInjected));\n  }\n}\nconst _forTrack3 = ($index, $item) => $item.component || $item.action;\nfunction PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵpipe(1, \"createInjector\");\n  }\n  if (rf & 2) {\n    const action_r1 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngComponentOutlet\", ctx)(\"ngComponentOutletInjector\", i0.ɵɵpipeBind3(1, 2, ctx_r1.record, action_r1, ctx_r1));\n  }\n}\nfunction PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const action_r1 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(action_r1.action(ctx_r1.data));\n    });\n    i0.ɵɵelement(1, \"i\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const toolbarAction_r4 = ctx;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"ngClass\", (toolbarAction_r4 == null ? null : toolbarAction_r4.btnClass) ? toolbarAction_r4 == null ? null : toolbarAction_r4.btnClass : ctx_r1.defaultBtnClass);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"me-1\", toolbarAction_r4 == null ? null : toolbarAction_r4.icon);\n    i0.ɵɵproperty(\"ngClass\", toolbarAction_r4 == null ? null : toolbarAction_r4.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, toolbarAction_r4 == null ? null : toolbarAction_r4.text), \" \");\n  }\n}\nfunction PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template, 4, 7, \"button\", 5);\n  }\n  if (rf & 2) {\n    let tmp_13_0;\n    const action_r1 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, (tmp_13_0 = ctx_r1.asToolbarAction(action_r1).value) ? 0 : -1, tmp_13_0);\n  }\n}\nfunction PageToolbarComponent_For_2_Conditional_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_Template, 2, 6, \"ng-container\")(2, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Template, 1, 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const action_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, (tmp_12_0 = action_r1.component) ? 1 : 2, tmp_12_0);\n  }\n}\nfunction PageToolbarComponent_For_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Template, 3, 1, \"ng-container\", 3);\n  }\n  if (rf & 2) {\n    const action_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"abpPermission\", action_r1.permission)(\"abpPermissionRunChangeDetection\", false);\n  }\n}\nfunction PageToolbarComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, PageToolbarComponent_For_2_Conditional_1_Template, 1, 2, \"ng-container\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r1 = ctx.$implicit;\n    const ɵ$index_3_r5 = ctx.$index;\n    const ɵ$count_3_r6 = ctx.$count;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"pe-0\", ɵ$index_3_r5 === ɵ$count_3_r6 - 1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, action_r1.visible(ctx_r1.data) ? 1 : -1);\n  }\n}\nclass PropList extends LinkedList {}\nclass PropData {\n  get data() {\n    return {\n      getInjected: this.getInjected,\n      index: this.index,\n      record: this.record\n    };\n  }\n}\nclass Prop {\n  constructor(type, name, displayName, permission, visible = _ => true, isExtra = false, template, className, formText, tooltip, displayTextResolver) {\n    this.type = type;\n    this.name = name;\n    this.displayName = displayName;\n    this.permission = permission;\n    this.visible = visible;\n    this.isExtra = isExtra;\n    this.template = template;\n    this.className = className;\n    this.formText = formText;\n    this.tooltip = tooltip;\n    this.displayTextResolver = displayTextResolver;\n    this.displayName = this.displayName || this.name;\n  }\n}\nclass PropsFactory {\n  constructor() {\n    this.contributorCallbacks = {};\n  }\n  get(name) {\n    this.contributorCallbacks[name] = this.contributorCallbacks[name] || [];\n    return new this._ctor(this.contributorCallbacks[name]);\n  }\n}\nclass Props {\n  get props() {\n    const propList = new this._ctor();\n    this.callbackList.forEach(callback => callback(propList));\n    return propList;\n  }\n  constructor(callbackList) {\n    this.callbackList = callbackList;\n  }\n  addContributor(contributeCallback) {\n    this.callbackList.push(contributeCallback);\n  }\n  clearContributors() {\n    while (this.callbackList.length) this.callbackList.pop();\n  }\n}\nclass FormPropList extends PropList {}\nclass FormProps extends Props {\n  constructor() {\n    super(...arguments);\n    this._ctor = FormPropList;\n  }\n}\nclass GroupedFormPropList {\n  constructor() {\n    this.items = [];\n    this.count = 1;\n  }\n  addItem(item) {\n    const groupName = item.group?.name;\n    let group = this.items.find(i => i.group?.name === groupName);\n    if (group) {\n      group.formPropList.addTail(item);\n    } else {\n      group = {\n        formPropList: new FormPropList(),\n        group: item.group || {\n          name: `default${this.count++}`,\n          className: item.group?.className\n        }\n      };\n      group.formPropList.addHead(item);\n      this.items.push(group);\n    }\n  }\n}\nclass CreateFormPropsFactory extends PropsFactory {\n  constructor() {\n    super(...arguments);\n    this._ctor = FormProps;\n  }\n}\nclass EditFormPropsFactory extends PropsFactory {\n  constructor() {\n    super(...arguments);\n    this._ctor = FormProps;\n  }\n}\nclass FormProp extends Prop {\n  constructor(options) {\n    super(options.type, options.name, options.displayName || '', options.permission || '', options.visible, options.isExtra, options.template, options.className, options.formText, options.tooltip);\n    this.group = options.group;\n    this.className = options.className;\n    this.formText = options.formText;\n    this.tooltip = options.tooltip;\n    this.asyncValidators = options.asyncValidators || (_ => []);\n    this.validators = options.validators || (_ => []);\n    this.disabled = options.disabled || (_ => false);\n    this.readonly = options.readonly || (_ => false);\n    this.autocomplete = options.autocomplete || 'off';\n    this.options = options.options;\n    this.id = options.id || options.name;\n    const defaultValue = options.defaultValue;\n    this.defaultValue = isFalsyValue(defaultValue) ? defaultValue : defaultValue || '';\n    this.displayTextResolver = options.displayTextResolver;\n  }\n  static create(options) {\n    return new FormProp(options);\n  }\n  static createMany(arrayOfOptions) {\n    return arrayOfOptions.map(FormProp.create);\n  }\n}\nclass FormPropData extends PropData {\n  constructor(injector, record) {\n    super();\n    this.record = record;\n    this.getInjected = injector.get.bind(injector);\n  }\n}\nfunction isFalsyValue(defaultValue) {\n  return [0, '', false].indexOf(defaultValue) > -1;\n}\nfunction selfFactory(dependency) {\n  return dependency;\n}\nclass ExtensibleDateTimePickerComponent {\n  constructor(cdRef) {\n    this.cdRef = cdRef;\n    this.meridian = false;\n  }\n  setDate(dateStr) {\n    this.date.writeValue(dateStr);\n  }\n  setTime(dateStr) {\n    this.time.writeValue(dateStr);\n  }\n  static {\n    this.ɵfac = function ExtensibleDateTimePickerComponent_Factory(t) {\n      return new (t || ExtensibleDateTimePickerComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ExtensibleDateTimePickerComponent,\n      selectors: [[\"abp-extensible-date-time-picker\"]],\n      viewQuery: function ExtensibleDateTimePickerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NgbInputDatepicker, 5);\n          i0.ɵɵviewQuery(NgbTimepicker, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.date = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.time = _t.first);\n        }\n      },\n      inputs: {\n        prop: \"prop\",\n        meridian: \"meridian\"\n      },\n      exportAs: [\"abpExtensibleDateTimePicker\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([], [{\n        provide: ControlContainer,\n        useFactory: selfFactory,\n        deps: [[new Optional(), new SkipSelf(), ControlContainer]]\n      }, {\n        provide: NgbDateAdapter,\n        useClass: DateTimeAdapter\n      }, {\n        provide: NgbTimeAdapter,\n        useClass: DateTimeAdapter\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[\"datepicker\", \"ngbDatepicker\"], [\"timepicker\", \"\"], [\"ngbDatepicker\", \"\", \"type\", \"text\", 1, \"form-control\", 3, \"ngModelChange\", \"click\", \"keyup.space\", \"id\", \"formControlName\"], [3, \"ngModelChange\", \"formControlName\", \"meridian\"]],\n      template: function ExtensibleDateTimePickerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"input\", 2, 0);\n          i0.ɵɵlistener(\"ngModelChange\", function ExtensibleDateTimePickerComponent_Template_input_ngModelChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setTime($event));\n          })(\"click\", function ExtensibleDateTimePickerComponent_Template_input_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const datepicker_r2 = i0.ɵɵreference(1);\n            return i0.ɵɵresetView(datepicker_r2.open());\n          })(\"keyup.space\", function ExtensibleDateTimePickerComponent_Template_input_keyup_space_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const datepicker_r2 = i0.ɵɵreference(1);\n            return i0.ɵɵresetView(datepicker_r2.open());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"ngb-timepicker\", 3, 1);\n          i0.ɵɵlistener(\"ngModelChange\", function ExtensibleDateTimePickerComponent_Template_ngb_timepicker_ngModelChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setDate($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"id\", ctx.prop.id)(\"formControlName\", ctx.prop.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControlName\", ctx.prop.name)(\"meridian\", ctx.meridian);\n        }\n      },\n      dependencies: [CommonModule, NgbDatepickerModule, i1.NgbInputDatepicker, ReactiveFormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, NgbTimepickerModule, i1.NgbTimepicker],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensibleDateTimePickerComponent, [{\n    type: Component,\n    args: [{\n      exportAs: 'abpExtensibleDateTimePicker',\n      standalone: true,\n      imports: [CommonModule, NgbDatepickerModule, ReactiveFormsModule, NgbTimepickerModule],\n      selector: 'abp-extensible-date-time-picker',\n      template: `\n    <input\n      [id]=\"prop.id\"\n      [formControlName]=\"prop.name\"\n      (ngModelChange)=\"setTime($event)\"\n      (click)=\"datepicker.open()\"\n      (keyup.space)=\"datepicker.open()\"\n      ngbDatepicker\n      #datepicker=\"ngbDatepicker\"\n      type=\"text\"\n      class=\"form-control\"\n    />\n    <ngb-timepicker\n      #timepicker\n      [formControlName]=\"prop.name\"\n      (ngModelChange)=\"setDate($event)\"\n      [meridian]=\"meridian\"\n    ></ngb-timepicker>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      viewProviders: [{\n        provide: ControlContainer,\n        useFactory: selfFactory,\n        deps: [[new Optional(), new SkipSelf(), ControlContainer]]\n      }, {\n        provide: NgbDateAdapter,\n        useClass: DateTimeAdapter\n      }, {\n        provide: NgbTimeAdapter,\n        useClass: DateTimeAdapter\n      }]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    prop: [{\n      type: Input\n    }],\n    meridian: [{\n      type: Input\n    }],\n    date: [{\n      type: ViewChild,\n      args: [NgbInputDatepicker]\n    }],\n    time: [{\n      type: ViewChild,\n      args: [NgbTimepicker]\n    }]\n  });\n})();\nconst EXTENSIONS_IDENTIFIER = new InjectionToken('EXTENSIONS_IDENTIFIER');\nconst EXTENSIONS_ACTION_TYPE = new InjectionToken('EXTENSIONS_ACTION_TYPE');\nconst EXTENSIONS_ACTION_DATA = new InjectionToken('EXTENSIONS_ACTION_DATA');\nconst EXTENSIONS_ACTION_CALLBACK = new InjectionToken('EXTENSIONS_ACTION_DATA');\nconst PROP_DATA_STREAM = new InjectionToken('PROP_DATA_STREAM');\nconst ENTITY_PROP_TYPE_CLASSES = new InjectionToken('ENTITY_PROP_TYPE_CLASSES', {\n  factory: () => ({})\n});\nconst EXTENSIONS_FORM_PROP = new InjectionToken('EXTENSIONS_FORM_PROP');\nconst EXTENSIONS_FORM_PROP_DATA = new InjectionToken('EXTENSIONS_FORM_PROP_DATA');\nconst EXTRA_PROPERTIES_KEY = 'extraProperties';\nconst TYPEAHEAD_TEXT_SUFFIX = '_Text';\nconst TYPEAHEAD_TEXT_SUFFIX_REGEX = /_Text$/;\nfunction createTypeaheadOptions(lookup) {\n  return (data, searchText) => searchText && data ? data.getInjected(RestService).request({\n    method: 'GET',\n    url: lookup.url || '',\n    params: {\n      [lookup.filterParamName || '']: searchText\n    }\n  }, {\n    apiName: 'Default'\n  }).pipe(map(response => {\n    const list = response[lookup.resultListPropertyName || ''];\n    const mapToOption = item => ({\n      key: item[lookup.displayPropertyName || ''],\n      value: item[lookup.valuePropertyName || '']\n    });\n    return list.map(mapToOption);\n  })) : of([]);\n}\nfunction getTypeaheadType(lookup, name) {\n  if (!!lookup.url) {\n    return \"typeahead\" /* ePropType.Typeahead */;\n  } else {\n    return name.endsWith(TYPEAHEAD_TEXT_SUFFIX) ? \"hidden\" /* ePropType.Hidden */ : undefined;\n  }\n}\nfunction createTypeaheadDisplayNameGenerator(displayNameGeneratorFn, properties) {\n  return (displayName, fallback) => {\n    const name = removeTypeaheadTextSuffix(fallback.name || '');\n    return displayNameGeneratorFn(displayName || properties[name].displayName, {\n      name,\n      resource: fallback.resource\n    });\n  };\n}\nfunction addTypeaheadTextSuffix(name) {\n  return name + TYPEAHEAD_TEXT_SUFFIX;\n}\nfunction hasTypeaheadTextSuffix(name) {\n  return TYPEAHEAD_TEXT_SUFFIX_REGEX.test(name);\n}\nfunction removeTypeaheadTextSuffix(name) {\n  return name.replace(TYPEAHEAD_TEXT_SUFFIX_REGEX, '');\n}\nclass ExtensibleFormPropService {\n  constructor() {\n    this.#configStateService = inject(ConfigStateService);\n    this.meridian$ = this.#configStateService.getDeep$('localization.currentCulture.dateTimeFormat.shortTimePattern').pipe(map(shortTimePattern => (shortTimePattern || '').includes('tt')));\n  }\n  #configStateService;\n  isRequired(validator) {\n    return validator === Validators.required || validator === AbpValidators.required || validator.name === 'required';\n  }\n  getComponent(prop) {\n    if (prop.template) {\n      return 'template';\n    }\n    switch (prop.type) {\n      case \"boolean\" /* ePropType.Boolean */:\n        return 'checkbox';\n      case \"date\" /* ePropType.Date */:\n        return 'date';\n      case \"datetime\" /* ePropType.DateTime */:\n        return 'dateTime';\n      case \"hidden\" /* ePropType.Hidden */:\n        return 'hidden';\n      case \"multiselect\" /* ePropType.MultiSelect */:\n        return 'multiselect';\n      case \"text\" /* ePropType.Text */:\n        return 'textarea';\n      case \"time\" /* ePropType.Time */:\n        return 'time';\n      case \"typeahead\" /* ePropType.Typeahead */:\n        return 'typeahead';\n      case \"passwordinputgroup\" /* ePropType.PasswordInputGroup */:\n        return 'passwordinputgroup';\n      default:\n        return prop.options ? 'select' : 'input';\n    }\n  }\n  getType(prop) {\n    switch (prop.type) {\n      case \"date\" /* ePropType.Date */:\n      case \"string\" /* ePropType.String */:\n        return 'text';\n      case \"boolean\" /* ePropType.Boolean */:\n        return 'checkbox';\n      case \"number\" /* ePropType.Number */:\n        return 'number';\n      case \"email\" /* ePropType.Email */:\n        return 'email';\n      case \"password\" /* ePropType.Password */:\n        return 'password';\n      case \"passwordinputgroup\" /* ePropType.PasswordInputGroup */:\n        return 'passwordinputgroup';\n      default:\n        return 'hidden';\n    }\n  }\n  calcAsterisks(validators) {\n    if (!validators) return '';\n    const required = validators.find(v => this.isRequired(v));\n    return required ? '*' : '';\n  }\n  static {\n    this.ɵfac = function ExtensibleFormPropService_Factory(t) {\n      return new (t || ExtensibleFormPropService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ExtensibleFormPropService,\n      factory: ExtensibleFormPropService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensibleFormPropService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass CreateInjectorPipe {\n  transform(_, action, context) {\n    const get = (token, notFoundValue, options) => {\n      const componentData = context.getData();\n      const componentDataCallback = data => {\n        data = data ?? context.getData();\n        return action.action(data);\n      };\n      let extensionData;\n      switch (token) {\n        case EXTENSIONS_ACTION_DATA:\n          extensionData = componentData;\n          break;\n        case EXTENSIONS_ACTION_CALLBACK:\n          extensionData = componentDataCallback;\n          break;\n        default:\n          extensionData = context.getInjected.call(context.injector, token, notFoundValue, options);\n      }\n      return extensionData;\n    };\n    return {\n      get\n    };\n  }\n  static {\n    this.ɵfac = function CreateInjectorPipe_Factory(t) {\n      return new (t || CreateInjectorPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"createInjector\",\n      type: CreateInjectorPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CreateInjectorPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'createInjector',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass ExtensibleFormPropComponent {\n  constructor() {\n    this.service = inject(ExtensibleFormPropService);\n    this.cdRef = inject(ChangeDetectorRef);\n    this.track = inject(TrackByService);\n    this.#groupDirective = inject(FormGroupDirective);\n    this.injector = inject(Injector);\n    this.form = this.#groupDirective.form;\n    this.asterisk = '';\n    this.containerClassName = 'mb-2';\n    this.showPassword = false;\n    this.options$ = of([]);\n    this.validators = [];\n    this.passwordKey = \"ThemeShared.Extensions.PasswordComponent\" /* eExtensibleComponents.PasswordComponent */;\n    this.disabledFn = data => false;\n    this.search = text$ => text$ ? text$.pipe(debounceTime(300), distinctUntilChanged(), switchMap(text => this.prop?.options?.(this.data, text) || of([]))) : of([]);\n    this.typeaheadFormatter = option => option.key;\n    this.meridian$ = this.service.meridian$;\n  }\n  #groupDirective;\n  get disabled() {\n    return this.disabledFn(this.data);\n  }\n  setTypeaheadValue(selectedOption) {\n    this.typeaheadModel = selectedOption || {\n      key: null,\n      value: null\n    };\n    const {\n      key,\n      value\n    } = this.typeaheadModel;\n    const [keyControl, valueControl] = this.getTypeaheadControls();\n    if (valueControl?.value && !value) valueControl.markAsDirty();\n    keyControl?.setValue(key);\n    valueControl?.setValue(value);\n  }\n  get isInvalid() {\n    const control = this.form.get(this.prop.name);\n    return control?.touched && control.invalid;\n  }\n  getTypeaheadControls() {\n    const {\n      name\n    } = this.prop;\n    const extraPropName = `${EXTRA_PROPERTIES_KEY}.${name}`;\n    const keyControl = this.form.get(addTypeaheadTextSuffix(extraPropName)) || this.form.get(addTypeaheadTextSuffix(name));\n    const valueControl = this.form.get(extraPropName) || this.form.get(name);\n    return [keyControl, valueControl];\n  }\n  setAsterisk() {\n    this.asterisk = this.service.calcAsterisks(this.validators);\n  }\n  ngAfterViewInit() {\n    if (this.isFirstGroup && this.first && this.fieldRef) {\n      this.fieldRef.nativeElement.focus();\n    }\n  }\n  getComponent(prop) {\n    return this.service.getComponent(prop);\n  }\n  getType(prop) {\n    return this.service.getType(prop);\n  }\n  ngOnChanges({\n    prop,\n    data\n  }) {\n    const currentProp = prop?.currentValue;\n    const {\n      options,\n      readonly,\n      disabled,\n      validators,\n      className,\n      template\n    } = currentProp || {};\n    if (template) {\n      this.injectorForCustomComponent = Injector.create({\n        providers: [{\n          provide: EXTENSIONS_FORM_PROP,\n          useValue: currentProp\n        }, {\n          provide: EXTENSIONS_FORM_PROP_DATA,\n          useValue: data?.currentValue?.record\n        }, {\n          provide: ControlContainer,\n          useExisting: FormGroupDirective\n        }],\n        parent: this.injector\n      });\n    }\n    if (options) this.options$ = options(this.data);\n    if (readonly) this.readonly = readonly(this.data);\n    if (disabled) {\n      this.disabledFn = disabled;\n    }\n    if (validators) {\n      this.validators = validators(this.data);\n      this.setAsterisk();\n    }\n    if (className !== undefined) {\n      this.containerClassName = className;\n    }\n    const [keyControl, valueControl] = this.getTypeaheadControls();\n    if (keyControl && valueControl) this.typeaheadModel = {\n      key: keyControl.value,\n      value: valueControl.value\n    };\n  }\n  static {\n    this.ɵfac = function ExtensibleFormPropComponent_Factory(t) {\n      return new (t || ExtensibleFormPropComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ExtensibleFormPropComponent,\n      selectors: [[\"abp-extensible-form-prop\"]],\n      viewQuery: function ExtensibleFormPropComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fieldRef = _t.first);\n        }\n      },\n      inputs: {\n        data: \"data\",\n        prop: \"prop\",\n        first: \"first\",\n        isFirstGroup: \"isFirstGroup\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ExtensibleFormPropService], [{\n        provide: ControlContainer,\n        useFactory: selfFactory,\n        deps: [[new Optional(), new SkipSelf(), ControlContainer]]\n      }, {\n        provide: NgbDateAdapter,\n        useClass: DateAdapter\n      }, {\n        provide: NgbTimeAdapter,\n        useClass: TimeAdapter\n      }]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"label\", \"\"], [\"field\", \"\"], [\"typeahead\", \"\"], [\"datepicker\", \"ngbDatepicker\"], [3, \"ngSwitch\", 4, \"abpPermission\", \"abpPermissionRunChangeDetection\"], [3, \"ngSwitch\"], [\"ngSwitchCase\", \"template\"], [1, \"mb-2\", 3, \"ngClass\"], [\"ngSwitchCase\", \"input\"], [\"ngSwitchCase\", \"hidden\"], [\"ngSwitchCase\", \"checkbox\"], [\"ngSwitchCase\", \"select\"], [\"ngSwitchCase\", \"multiselect\"], [\"ngSwitchCase\", \"typeahead\"], [\"ngSwitchCase\", \"date\"], [\"ngSwitchCase\", \"time\"], [\"ngSwitchCase\", \"dateTime\"], [\"ngSwitchCase\", \"textarea\"], [\"ngSwitchCase\", \"passwordinputgroup\"], [1, \"text-muted\", \"d-block\"], [4, \"ngComponentOutlet\", \"ngComponentOutletInjector\"], [3, \"ngTemplateOutlet\"], [1, \"form-control\", 3, \"id\", \"formControlName\", \"autocomplete\", \"type\", \"abpDisabled\", \"readonly\"], [\"type\", \"hidden\", 3, \"formControlName\"], [\"validationTarget\", \"\", 1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"formControlName\", \"abpDisabled\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"form-select\", \"form-control\", 3, \"id\", \"formControlName\", \"abpDisabled\"], [3, \"ngValue\"], [\"multiple\", \"multiple\", 1, \"form-select\", \"form-control\", 3, \"id\", \"formControlName\", \"abpDisabled\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"validationStyle\", \"\", \"validationTarget\", \"\", 1, \"position-relative\"], [1, \"form-control\", 3, \"ngModelChange\", \"selectItem\", \"blur\", \"id\", \"autocomplete\", \"abpDisabled\", \"ngbTypeahead\", \"editable\", \"inputFormatter\", \"resultFormatter\", \"ngModelOptions\", \"ngModel\"], [\"ngbDatepicker\", \"\", \"type\", \"text\", 1, \"form-control\", 3, \"click\", \"keyup.space\", \"id\", \"formControlName\"], [3, \"formControlName\"], [3, \"prop\", \"meridian\"], [1, \"form-control\", 3, \"id\", \"formControlName\", \"abpDisabled\", \"readonly\"], [\"validationTarget\", \"\", 1, \"input-group\", \"form-group\"], [1, \"form-control\", 3, \"id\", \"formControlName\", \"abpShowPassword\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", 3, \"ngClass\"], [3, \"htmlFor\", \"ngClass\"], [\"container\", \"body\", 1, \"bi\", \"bi-info-circle\", 3, \"ngbTooltip\", \"placement\"]],\n      template: function ExtensibleFormPropComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Template, 15, 3, \"ng-container\", 4)(1, ExtensibleFormPropComponent_ng_template_1_Template, 5, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"abpPermission\", ctx.prop.permission)(\"abpPermissionRunChangeDetection\", false);\n        }\n      },\n      dependencies: [ExtensibleDateTimePickerComponent, NgbDatepickerModule, i1.NgbInputDatepicker, NgbTimepickerModule, i1.NgbTimepicker, ReactiveFormsModule, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.SelectControlValueAccessor, i2.SelectMultipleControlValueAccessor, i2.NgControlStatus, i2.FormControlName, DisabledDirective, NgxValidateCoreModule, i3.ValidationStyleDirective, i3.ValidationTargetDirective, i3.ValidationDirective, NgbTooltip, NgbTypeaheadModule, i1.NgbTypeahead, ShowPasswordDirective, PermissionDirective, LocalizationModule, i2$1.LocalizationPipe, CommonModule, i5.NgClass, i5.NgComponentOutlet, i5.NgForOf, i5.NgTemplateOutlet, i5.NgSwitch, i5.NgSwitchCase, i5.AsyncPipe, FormsModule, i2.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensibleFormPropComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-extensible-form-prop',\n      standalone: true,\n      imports: [ExtensibleDateTimePickerComponent, NgbDatepickerModule, NgbTimepickerModule, ReactiveFormsModule, DisabledDirective, NgxValidateCoreModule, NgbTooltip, NgbTypeaheadModule, CreateInjectorPipe, ShowPasswordDirective, PermissionDirective, LocalizationModule, CommonModule, FormsModule],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [ExtensibleFormPropService],\n      viewProviders: [{\n        provide: ControlContainer,\n        useFactory: selfFactory,\n        deps: [[new Optional(), new SkipSelf(), ControlContainer]]\n      }, {\n        provide: NgbDateAdapter,\n        useClass: DateAdapter\n      }, {\n        provide: NgbTimeAdapter,\n        useClass: TimeAdapter\n      }],\n      template: \"<ng-container\\r\\n  [ngSwitch]=\\\"getComponent(prop)\\\"\\r\\n  *abpPermission=\\\"prop.permission; runChangeDetection: false\\\"\\r\\n>\\r\\n  <ng-template ngSwitchCase=\\\"template\\\">\\r\\n    <ng-container *ngComponentOutlet=\\\"prop.template; injector: injectorForCustomComponent\\\">\\r\\n    </ng-container>\\r\\n  </ng-template>\\r\\n\\r\\n  <div [ngClass]=\\\"containerClassName\\\" class=\\\"mb-2\\\">\\r\\n    <ng-template ngSwitchCase=\\\"input\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <input\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [autocomplete]=\\\"prop.autocomplete\\\"\\r\\n        [type]=\\\"getType(prop)\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        [readonly]=\\\"readonly\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"hidden\\\">\\r\\n      <input [formControlName]=\\\"prop.name\\\" type=\\\"hidden\\\" />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"checkbox\\\">\\r\\n      <div class=\\\"form-check\\\" validationTarget>\\r\\n        <input\\r\\n          #field\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [formControlName]=\\\"prop.name\\\"\\r\\n          [abpDisabled]=\\\"disabled\\\"\\r\\n          type=\\\"checkbox\\\"\\r\\n          class=\\\"form-check-input\\\"\\r\\n        />\\r\\n        <ng-template\\r\\n          [ngTemplateOutlet]=\\\"label\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ $implicit: 'form-check-label' }\\\"\\r\\n        ></ng-template>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"select\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <select\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        class=\\\"form-select form-control\\\"\\r\\n      >\\r\\n        @for (option of options$ | async; track option.value) {\\r\\n          <option [ngValue]=\\\"option.value\\\">\\r\\n            {{ option.key }}\\r\\n          </option>\\r\\n        }\\r\\n      </select>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"multiselect\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <select\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        multiple=\\\"multiple\\\"\\r\\n        class=\\\"form-select form-control\\\"\\r\\n      >\\r\\n        <option\\r\\n          *ngFor=\\\"let option of options$ | async; trackBy: track.by('value')\\\"\\r\\n          [ngValue]=\\\"option.value\\\"\\r\\n        >\\r\\n          {{ option.key }}\\r\\n        </option>\\r\\n      </select>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"typeahead\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <div #typeahead class=\\\"position-relative\\\" validationStyle validationTarget>\\r\\n        <input\\r\\n          #field\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [autocomplete]=\\\"prop.autocomplete\\\"\\r\\n          [abpDisabled]=\\\"disabled\\\"\\r\\n          [ngbTypeahead]=\\\"search\\\"\\r\\n          [editable]=\\\"false\\\"\\r\\n          [inputFormatter]=\\\"typeaheadFormatter\\\"\\r\\n          [resultFormatter]=\\\"typeaheadFormatter\\\"\\r\\n          [ngModelOptions]=\\\"{ standalone: true }\\\"\\r\\n          [(ngModel)]=\\\"typeaheadModel\\\"\\r\\n          (selectItem)=\\\"setTypeaheadValue($event.item)\\\"\\r\\n          (blur)=\\\"setTypeaheadValue(typeaheadModel)\\\"\\r\\n          [class.is-invalid]=\\\"typeahead.classList.contains('is-invalid')\\\"\\r\\n          class=\\\"form-control\\\"\\r\\n        />\\r\\n        <input [formControlName]=\\\"prop.name\\\" type=\\\"hidden\\\" />\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"date\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <input\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        (click)=\\\"datepicker.open()\\\"\\r\\n        (keyup.space)=\\\"datepicker.open()\\\"\\r\\n        ngbDatepicker\\r\\n        #datepicker=\\\"ngbDatepicker\\\"\\r\\n        type=\\\"text\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"time\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <ngb-timepicker [formControlName]=\\\"prop.name\\\"></ngb-timepicker>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"dateTime\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <abp-extensible-date-time-picker [prop]=\\\"prop\\\" [meridian]=\\\"meridian$ | async\\\" />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"textarea\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <textarea\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        [readonly]=\\\"readonly\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      ></textarea>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"passwordinputgroup\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <div class=\\\"input-group form-group\\\" validationTarget>\\r\\n        <input\\r\\n          class=\\\"form-control\\\"\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [formControlName]=\\\"prop.name\\\"\\r\\n          [abpShowPassword]=\\\"showPassword\\\"\\r\\n        />\\r\\n        <button class=\\\"btn btn-secondary\\\" type=\\\"button\\\" (click)=\\\"showPassword = !showPassword\\\">\\r\\n          <i\\r\\n            class=\\\"fa\\\"\\r\\n            aria-hidden=\\\"true\\\"\\r\\n            [ngClass]=\\\"{\\r\\n              'fa-eye-slash': !showPassword,\\r\\n              'fa-eye': showPassword,\\r\\n            }\\\"\\r\\n          ></i>\\r\\n        </button>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    @if (prop.formText) {\\r\\n      <small class=\\\"text-muted d-block\\\">{{ prop.formText | abpLocalization }}</small>\\r\\n    }\\r\\n  </div>\\r\\n</ng-container>\\r\\n\\r\\n<ng-template #label let-classes>\\r\\n  <label [htmlFor]=\\\"prop.id\\\" [ngClass]=\\\"classes || 'form-label'\\\">\\r\\n    @if (prop.displayTextResolver) {\\r\\n      {{ prop.displayTextResolver(data) | abpLocalization }}\\r\\n    } @else {\\r\\n      @if (prop.isExtra) {\\r\\n        {{ '::' + prop.displayName | abpLocalization }}\\r\\n      } @else {\\r\\n        {{ prop.displayName | abpLocalization }}\\r\\n      }\\r\\n    }\\r\\n    {{ asterisk }}\\r\\n    @if (prop.tooltip) {\\r\\n      <i\\r\\n        [ngbTooltip]=\\\"prop.tooltip.text | abpLocalization\\\"\\r\\n        [placement]=\\\"prop.tooltip.placement || 'auto'\\\"\\r\\n        container=\\\"body\\\"\\r\\n        class=\\\"bi bi-info-circle\\\"\\r\\n      ></i>\\r\\n    }\\r\\n  </label>\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], null, {\n    data: [{\n      type: Input\n    }],\n    prop: [{\n      type: Input\n    }],\n    first: [{\n      type: Input\n    }],\n    isFirstGroup: [{\n      type: Input\n    }],\n    fieldRef: [{\n      type: ViewChild,\n      args: ['field']\n    }]\n  });\n})();\nclass ActionList extends LinkedList {}\nclass ActionData {\n  get data() {\n    return {\n      getInjected: this.getInjected,\n      index: this.index,\n      record: this.record\n    };\n  }\n}\nclass Action {\n  constructor(permission, visible = () => true, action = () => {}, btnClass, btnStyle) {\n    this.permission = permission;\n    this.visible = visible;\n    this.action = action;\n    this.btnClass = btnClass;\n    this.btnStyle = btnStyle;\n  }\n}\nclass ActionsFactory {\n  constructor() {\n    this.contributorCallbacks = {};\n  }\n  get(name) {\n    this.contributorCallbacks[name] = this.contributorCallbacks[name] || [];\n    return new this._ctor(this.contributorCallbacks[name]);\n  }\n}\nclass Actions {\n  get actions() {\n    const actionList = new this._ctor();\n    this.callbackList.forEach(callback => callback(actionList));\n    return actionList;\n  }\n  constructor(callbackList) {\n    this.callbackList = callbackList;\n  }\n  addContributor(contributeCallback) {\n    this.callbackList.push(contributeCallback);\n  }\n  clearContributors() {\n    while (this.callbackList.length) this.callbackList.pop();\n  }\n}\nclass EntityActionList extends ActionList {}\nclass EntityActions extends Actions {\n  constructor() {\n    super(...arguments);\n    this._ctor = EntityActionList;\n  }\n}\nclass EntityActionsFactory extends ActionsFactory {\n  constructor() {\n    super(...arguments);\n    this._ctor = EntityActions;\n  }\n}\nclass EntityAction extends Action {\n  constructor(options) {\n    super(options.permission || '', options.visible, options.action);\n    this.text = options.text;\n    this.icon = options.icon || '';\n    this.btnClass = options.btnClass || 'btn btn-primary text-center';\n    this.btnStyle = options.btnStyle;\n    this.showOnlyIcon = options.showOnlyIcon || false;\n    this.tooltip = options.tooltip;\n  }\n  static create(options) {\n    return new EntityAction(options);\n  }\n  static createMany(arrayOfOptions) {\n    return arrayOfOptions.map(EntityAction.create);\n  }\n}\nclass EntityPropList extends PropList {}\nclass EntityProps extends Props {\n  constructor() {\n    super(...arguments);\n    this._ctor = EntityPropList;\n  }\n}\nclass EntityPropsFactory extends PropsFactory {\n  constructor() {\n    super(...arguments);\n    this._ctor = EntityProps;\n  }\n}\nclass EntityProp extends Prop {\n  constructor(options) {\n    super(options.type, options.name, options.displayName || '', options.permission || '', options.visible, options.isExtra);\n    this.columnVisible = options.columnVisible || (() => true);\n    this.columnWidth = options.columnWidth;\n    this.sortable = options.sortable || false;\n    this.valueResolver = options.valueResolver || (data => of(escapeHtmlChars(data.record[this.name])));\n    if (options.action) {\n      this.action = options.action;\n    }\n    if (options.component) {\n      this.component = options.component;\n    }\n    if (options.enumList) {\n      this.enumList = options.enumList;\n    }\n    this.tooltip = options.tooltip;\n  }\n  static create(options) {\n    return new EntityProp(options);\n  }\n  static createMany(arrayOfOptions) {\n    return arrayOfOptions.map(EntityProp.create);\n  }\n}\nclass ToolbarActionList extends ActionList {}\nclass ToolbarActions extends Actions {\n  constructor() {\n    super(...arguments);\n    this._ctor = ToolbarActionList;\n  }\n}\nclass ToolbarActionsFactory extends ActionsFactory {\n  constructor() {\n    super(...arguments);\n    this._ctor = ToolbarActions;\n  }\n}\nclass ToolbarAction extends Action {\n  constructor(options) {\n    super(options.permission || '', options.visible, options.action);\n    this.text = options.text;\n    this.icon = options.icon || '';\n    if (options.btnClass) {\n      this.btnClass = options.btnClass;\n    }\n  }\n  static create(options) {\n    return new ToolbarAction(options);\n  }\n  static createMany(arrayOfOptions) {\n    return arrayOfOptions.map(ToolbarAction.create);\n  }\n}\nclass ToolbarComponent extends Action {\n  constructor(options) {\n    super(options.permission || '', options.visible, options.action);\n    this.component = options.component;\n  }\n  static create(options) {\n    return new ToolbarComponent(options);\n  }\n  static createMany(arrayOfOptions) {\n    return arrayOfOptions.map(ToolbarComponent.create);\n  }\n}\nclass ExtensionsService {\n  constructor() {\n    this.entityActions = new EntityActionsFactory();\n    this.toolbarActions = new ToolbarActionsFactory();\n    this.entityProps = new EntityPropsFactory();\n    this.createFormProps = new CreateFormPropsFactory();\n    this.editFormProps = new EditFormPropsFactory();\n  }\n  static {\n    this.ɵfac = function ExtensionsService_Factory(t) {\n      return new (t || ExtensionsService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ExtensionsService,\n      factory: ExtensionsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensionsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/* eslint-disable @angular-eslint/no-input-rename */\nclass PropDataDirective extends PropData {\n  constructor(tempRef, vcRef, injector) {\n    super();\n    this.tempRef = tempRef;\n    this.vcRef = vcRef;\n    this.getInjected = injector.get.bind(injector);\n  }\n  ngOnChanges() {\n    this.vcRef.clear();\n    this.vcRef.createEmbeddedView(this.tempRef, {\n      $implicit: this.data,\n      index: 0\n    });\n  }\n  ngOnDestroy() {\n    this.vcRef.clear();\n  }\n  static {\n    this.ɵfac = function PropDataDirective_Factory(t) {\n      return new (t || PropDataDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PropDataDirective,\n      selectors: [[\"\", \"abpPropData\", \"\"]],\n      inputs: {\n        propList: [i0.ɵɵInputFlags.None, \"abpPropDataFromList\", \"propList\"],\n        record: [i0.ɵɵInputFlags.None, \"abpPropDataWithRecord\", \"record\"],\n        index: [i0.ɵɵInputFlags.None, \"abpPropDataAtIndex\", \"index\"]\n      },\n      exportAs: [\"abpPropData\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PropDataDirective, [{\n    type: Directive,\n    args: [{\n      exportAs: 'abpPropData',\n      selector: '[abpPropData]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.Injector\n  }], {\n    propList: [{\n      type: Input,\n      args: ['abpPropDataFromList']\n    }],\n    record: [{\n      type: Input,\n      args: ['abpPropDataWithRecord']\n    }],\n    index: [{\n      type: Input,\n      args: ['abpPropDataAtIndex']\n    }]\n  });\n})();\nclass ExtensibleFormComponent {\n  constructor() {\n    this.cdRef = inject(ChangeDetectorRef);\n    this.track = inject(TrackByService);\n    this.container = inject(ControlContainer);\n    this.extensions = inject(ExtensionsService);\n    this.identifier = inject(EXTENSIONS_IDENTIFIER);\n    this.extraPropertiesKey = EXTRA_PROPERTIES_KEY;\n  }\n  set selectedRecord(record) {\n    const type = !record || JSON.stringify(record) === '{}' ? 'create' : 'edit';\n    const propList = this.extensions[`${type}FormProps`].get(this.identifier).props;\n    this.groupedPropList = this.createGroupedList(propList);\n    this.record = record;\n  }\n  get form() {\n    return this.container ? this.container.control : {\n      controls: {}\n    };\n  }\n  get extraProperties() {\n    return this.form.controls.extraProperties || {\n      controls: {}\n    };\n  }\n  createGroupedList(propList) {\n    const groupedFormPropList = new GroupedFormPropList();\n    propList.forEach(item => {\n      groupedFormPropList.addItem(item.value);\n    });\n    return groupedFormPropList;\n  }\n  //TODO: Reactor this method\n  isAnyGroupMemberVisible(index, data) {\n    const {\n      items\n    } = this.groupedPropList;\n    const formPropList = items[index].formPropList.toArray();\n    return formPropList.some(prop => prop.visible(data));\n  }\n  static {\n    this.ɵfac = function ExtensibleFormComponent_Factory(t) {\n      return new (t || ExtensibleFormComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ExtensibleFormComponent,\n      selectors: [[\"abp-extensible-form\"]],\n      viewQuery: function ExtensibleFormComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ExtensibleFormPropComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.formProps = _t);\n        }\n      },\n      inputs: {\n        selectedRecord: \"selectedRecord\"\n      },\n      exportAs: [\"abpExtensibleForm\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([], [{\n        provide: ControlContainer,\n        useFactory: selfFactory,\n        deps: [[new Optional(), new SkipSelf(), ControlContainer]]\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 1,\n      consts: [[\"propListTemplate\", \"\"], [4, \"abpPropData\", \"abpPropDataFromList\", \"abpPropDataWithRecord\"], [3, \"ngClass\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"formGroupName\"], [3, \"prop\", \"data\"], [3, \"class\", \"prop\", \"data\", \"first\", \"isFirstGroup\"], [3, \"prop\", \"data\", \"first\", \"isFirstGroup\"]],\n      template: function ExtensibleFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ExtensibleFormComponent_Conditional_0_Template, 2, 0)(1, ExtensibleFormComponent_ng_template_1_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.form ? 0 : -1);\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgTemplateOutlet, PropDataDirective, ReactiveFormsModule, i2.NgControlStatusGroup, i2.FormGroupName, ExtensibleFormPropComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensibleFormComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      exportAs: 'abpExtensibleForm',\n      selector: 'abp-extensible-form',\n      imports: [CommonModule, PropDataDirective, ReactiveFormsModule, ExtensibleFormPropComponent],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      viewProviders: [{\n        provide: ControlContainer,\n        useFactory: selfFactory,\n        deps: [[new Optional(), new SkipSelf(), ControlContainer]]\n      }],\n      template: \"@if (form) {\\r\\n  @for (groupedProp of groupedPropList.items; track i; let i = $index; let first = $first) {\\r\\n    <ng-container *abpPropData=\\\"let data; fromList: groupedProp.formPropList; withRecord: record\\\">\\r\\n      @if (isAnyGroupMemberVisible(i, data) && groupedProp.group?.className) {\\r\\n        <div\\r\\n          [ngClass]=\\\"groupedProp.group?.className\\\"\\r\\n          [attr.data-name]=\\\"groupedProp.group?.name || groupedProp.group?.className\\\"\\r\\n        >\\r\\n          <ng-container\\r\\n            [ngTemplateOutlet]=\\\"propListTemplate\\\"\\r\\n            [ngTemplateOutletContext]=\\\"{ groupedProp: groupedProp, data: data, isFirstGroup: first}\\\"\\r\\n          >\\r\\n          </ng-container>\\r\\n        </div>\\r\\n      } @else {\\r\\n        <ng-container\\r\\n          [ngTemplateOutlet]=\\\"propListTemplate\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ groupedProp: groupedProp, data: data, isFirstGroup: first }\\\"\\r\\n        >\\r\\n        </ng-container>\\r\\n      }\\r\\n    </ng-container>\\r\\n  }\\r\\n}\\r\\n\\r\\n<ng-template let-groupedProp=\\\"groupedProp\\\" let-data=\\\"data\\\" let-isFirstGroup=\\\"isFirstGroup\\\" #propListTemplate>\\r\\n  @for (prop of groupedProp.formPropList; let index = $index; let first = $first; track prop.name) {\\r\\n    @if (prop.visible(data)) {\\r\\n      @if (extraProperties.controls[prop.name]) {\\r\\n        <ng-container [formGroupName]=\\\"extraPropertiesKey\\\">\\r\\n          <abp-extensible-form-prop [prop]=\\\"prop\\\" [data]=\\\"data\\\" [class]=\\\"prop.className\\\" />\\r\\n        </ng-container>\\r\\n      } @else {\\r\\n        @if (form.get(prop.name)) {\\r\\n          <abp-extensible-form-prop\\r\\n            [class]=\\\"prop.className\\\"\\r\\n            [prop]=\\\"prop\\\"\\r\\n            [data]=\\\"data\\\"\\r\\n            [first]=\\\"first\\\"\\r\\n            [isFirstGroup]=\\\"isFirstGroup\\\"\\r\\n          />\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], null, {\n    formProps: [{\n      type: ViewChildren,\n      args: [ExtensibleFormPropComponent]\n    }],\n    selectedRecord: [{\n      type: Input\n    }]\n  });\n})();\n\n// Fix for https://github.com/angular/angular/issues/23904\n// @dynamic\nclass AbstractActionsComponent extends ActionData {\n  constructor(injector) {\n    super();\n    this.getInjected = injector.get.bind(injector);\n    const extensions = injector.get(ExtensionsService);\n    const name = injector.get(EXTENSIONS_IDENTIFIER);\n    const type = injector.get(EXTENSIONS_ACTION_TYPE);\n    this.actionList = extensions[type].get(name).actions;\n  }\n  static {\n    this.ɵfac = function AbstractActionsComponent_Factory(t) {\n      return new (t || AbstractActionsComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: AbstractActionsComponent,\n      inputs: {\n        record: \"record\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractActionsComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.Injector\n  }], {\n    record: [{\n      type: Input\n    }]\n  });\n})();\nclass GridActionsComponent extends AbstractActionsComponent {\n  constructor(injector) {\n    super(injector);\n    this.icon = 'fa fa-cog';\n    this.text = '';\n    this.trackByFn = (_, item) => item.text;\n  }\n  static {\n    this.ɵfac = function GridActionsComponent_Factory(t) {\n      return new (t || GridActionsComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: GridActionsComponent,\n      selectors: [[\"abp-grid-actions\"]],\n      inputs: {\n        icon: \"icon\",\n        index: \"index\",\n        text: \"text\"\n      },\n      exportAs: [\"abpGridActions\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: EXTENSIONS_ACTION_TYPE,\n        useValue: 'entityActions'\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 2,\n      consts: [[\"dropDownBtnItemTmp\", \"\"], [\"buttonContentTmp\", \"\"], [\"btnTmp\", \"\"], [\"ngbDropdown\", \"\", \"container\", \"body\", 1, \"d-inline-block\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"data-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"dropdown-toggle\"], [3, \"ngClass\"], [\"ngbDropdownMenu\", \"\"], [\"ngbDropdownItem\", \"\", \"type\", \"button\"], [\"ngbDropdownItem\", \"\", \"type\", \"button\", 3, \"click\", 4, \"abpPermission\", \"abpPermissionRunChangeDetection\"], [\"ngbDropdownItem\", \"\", \"type\", \"button\", 3, \"click\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"abpEllipsis\", \"\"], [\"type\", \"button\", \"container\", \"body\", 3, \"class\", \"style\", \"ngbTooltip\", \"placement\"], [\"type\", \"button\", \"container\", \"body\", 3, \"class\", \"style\", \"ngbTooltip\", \"placement\", \"click\", 4, \"abpPermission\", \"abpPermissionRunChangeDetection\"], [\"type\", \"button\", \"container\", \"body\", 3, \"click\", \"ngbTooltip\", \"placement\"], [\"type\", \"button\", 3, \"class\", \"style\", \"click\", 4, \"abpPermission\", \"abpPermissionRunChangeDetection\"], [\"type\", \"button\", 3, \"click\"]],\n      template: function GridActionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, GridActionsComponent_Conditional_0_Template, 8, 6, \"div\", 3)(1, GridActionsComponent_Conditional_1_Template, 1, 4, \"ng-container\", 4)(2, GridActionsComponent_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, GridActionsComponent_ng_template_4_Template, 2, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, GridActionsComponent_ng_template_6_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.actionList.length > 1 ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.actionList.length === 1 ? 1 : -1);\n        }\n      },\n      dependencies: [NgbDropdownModule, i1.NgbDropdown, i1.NgbDropdownToggle, i1.NgbDropdownMenu, i1.NgbDropdownItem, i1.NgbDropdownButtonItem, EllipsisDirective, PermissionDirective, NgClass, LocalizationModule, i2$1.LocalizationPipe, NgTemplateOutlet, NgbTooltipModule, i1.NgbTooltip],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GridActionsComponent, [{\n    type: Component,\n    args: [{\n      exportAs: 'abpGridActions',\n      standalone: true,\n      imports: [NgbDropdownModule, EllipsisDirective, PermissionDirective, NgClass, LocalizationModule, NgTemplateOutlet, NgbTooltipModule],\n      selector: 'abp-grid-actions',\n      providers: [{\n        provide: EXTENSIONS_ACTION_TYPE,\n        useValue: 'entityActions'\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"@if (actionList.length > 1) {\\r\\n  <div ngbDropdown container=\\\"body\\\" class=\\\"d-inline-block\\\">\\r\\n    <button\\r\\n      class=\\\"btn btn-primary btn-sm dropdown-toggle\\\"\\r\\n      data-toggle=\\\"dropdown\\\"\\r\\n      aria-haspopup=\\\"true\\\"\\r\\n      ngbDropdownToggle\\r\\n    >\\r\\n      <i [ngClass]=\\\"icon\\\" [class.me-1]=\\\"icon\\\"></i>{{ text | abpLocalization }}\\r\\n    </button>\\r\\n    <div ngbDropdownMenu>\\r\\n      @for (action of actionList; track action.text) {\\r\\n        <ng-container\\r\\n          [ngTemplateOutlet]=\\\"dropDownBtnItemTmp\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ $implicit: action }\\\"\\r\\n        >\\r\\n        </ng-container>\\r\\n      }\\r\\n    </div>\\r\\n  </div>\\r\\n}\\r\\n\\r\\n@if (actionList.length === 1) {\\r\\n  <ng-container\\r\\n    [ngTemplateOutlet]=\\\"btnTmp\\\"\\r\\n    [ngTemplateOutletContext]=\\\"{ $implicit: actionList.get(0).value }\\\"\\r\\n  ></ng-container>\\r\\n}\\r\\n\\r\\n<ng-template #dropDownBtnItemTmp let-action>\\r\\n  @if (action.visible(data)) {\\r\\n    <button\\r\\n      ngbDropdownItem\\r\\n      *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n      (click)=\\\"action.action(data)\\\"\\r\\n      type=\\\"button\\\"\\r\\n    >\\r\\n      <ng-container\\r\\n        *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n      ></ng-container>\\r\\n    </button>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #buttonContentTmp let-action>\\r\\n  <i [ngClass]=\\\"action.icon\\\" [class.me-1]=\\\"action.icon && !action.showOnlyIcon\\\"></i>\\r\\n  @if (!action.showOnlyIcon) {\\r\\n    @if (action.icon) {\\r\\n      <span>{{ action.text | abpLocalization }}</span>\\r\\n    } @else {\\r\\n      <div abpEllipsis>{{ action.text | abpLocalization }}</div>\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #btnTmp let-action>\\r\\n  @if (action.visible(data)) {\\r\\n    @if (action.tooltip) {\\r\\n      <button\\r\\n        *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n        (click)=\\\"action.action(data)\\\"\\r\\n        type=\\\"button\\\"\\r\\n        [class]=\\\"action.btnClass\\\"\\r\\n        [style]=\\\"action.btnStyle\\\"\\r\\n        [ngbTooltip]=\\\"action.tooltip.text | abpLocalization\\\"\\r\\n        [placement]=\\\"action.tooltip.placement || 'auto'\\\"\\r\\n        container=\\\"body\\\"\\r\\n      >\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n        ></ng-container>\\r\\n      </button>\\r\\n    } @else {\\r\\n      <button\\r\\n        *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n        (click)=\\\"action.action(data)\\\"\\r\\n        type=\\\"button\\\"\\r\\n        [class]=\\\"action.btnClass\\\"\\r\\n        [style]=\\\"action.btnStyle\\\"\\r\\n      >\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n        ></ng-container>\\r\\n      </button>\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], () => [{\n    type: i0.Injector\n  }], {\n    icon: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }]\n  });\n})();\nconst DEFAULT_ACTIONS_COLUMN_WIDTH = 150;\nclass ExtensibleTableComponent {\n  set actionsText(value) {\n    this._actionsText = value;\n  }\n  get actionsText() {\n    return this._actionsText ?? (this.actionList.length > 1 ? 'AbpUi::Actions' : '');\n  }\n  set actionsColumnWidth(width) {\n    this.setColumnWidths(width ? Number(width) : undefined);\n  }\n  #injector;\n  constructor() {\n    this.tableActivate = new EventEmitter();\n    this.trackByFn = (_, item) => item.name;\n    this.locale = inject(LOCALE_ID);\n    this.config = inject(ConfigStateService);\n    this.entityPropTypeClasses = inject(ENTITY_PROP_TYPE_CLASSES);\n    this.#injector = inject(Injector);\n    this.getInjected = this.#injector.get.bind(this.#injector);\n    this.permissionService = this.#injector.get(PermissionService);\n    const extensions = this.#injector.get(ExtensionsService);\n    const name = this.#injector.get(EXTENSIONS_IDENTIFIER);\n    this.propList = extensions.entityProps.get(name).props;\n    this.actionList = extensions['entityActions'].get(name).actions;\n    this.hasAtLeastOnePermittedAction = this.permissionService.filterItemsByPolicy(this.actionList.toArray().map(action => ({\n      requiredPolicy: action.permission\n    }))).length > 0;\n    this.setColumnWidths(DEFAULT_ACTIONS_COLUMN_WIDTH);\n  }\n  setColumnWidths(actionsColumn) {\n    const widths = [actionsColumn];\n    this.propList.forEach(({\n      value: prop\n    }) => {\n      widths.push(prop.columnWidth);\n    });\n    this.columnWidths = widths;\n  }\n  getDate(value, format) {\n    return value && format ? formatDate(value, format, this.locale) : '';\n  }\n  getIcon(value) {\n    return value ? '<div class=\"text-success\"><i class=\"fa fa-check\" aria-hidden=\"true\"></i></div>' : '<div class=\"text-danger\"><i class=\"fa fa-times\" aria-hidden=\"true\"></i></div>';\n  }\n  getEnum(rowValue, list) {\n    if (!list || list.length < 1) return rowValue;\n    const {\n      key\n    } = list.find(({\n      value\n    }) => value === rowValue) || {};\n    return key;\n  }\n  getContent(prop, data) {\n    return prop.valueResolver(data).pipe(map(value => {\n      switch (prop.type) {\n        case \"boolean\" /* ePropType.Boolean */:\n          return this.getIcon(value);\n        case \"date\" /* ePropType.Date */:\n          return this.getDate(value, getShortDateFormat(this.config));\n        case \"time\" /* ePropType.Time */:\n          return this.getDate(value, getShortTimeFormat(this.config));\n        case \"datetime\" /* ePropType.DateTime */:\n          return this.getDate(value, getShortDateShortTimeFormat(this.config));\n        case \"enum\" /* ePropType.Enum */:\n          return this.getEnum(value, prop.enumList || []);\n        default:\n          return value;\n        // More types can be handled in the future\n      }\n    }));\n  }\n  ngOnChanges({\n    data\n  }) {\n    if (!data?.currentValue) return;\n    if (data.currentValue.length < 1) {\n      this.list.totalCount = this.recordsTotal;\n    }\n    this.data = data.currentValue.map((record, index) => {\n      this.propList.forEach(prop => {\n        const propData = {\n          getInjected: this.getInjected,\n          record,\n          index\n        };\n        const value = this.getContent(prop.value, propData);\n        const propKey = `_${prop.value.name}`;\n        record[propKey] = {\n          visible: prop.value.visible(propData),\n          value\n        };\n        if (prop.value.component) {\n          record[propKey].injector = Injector.create({\n            providers: [{\n              provide: PROP_DATA_STREAM,\n              useValue: value\n            }],\n            parent: this.#injector\n          });\n          record[propKey].component = prop.value.component;\n        }\n      });\n      return record;\n    });\n  }\n  isVisibleActions(rowData) {\n    const actions = this.actionList.toArray();\n    const visibleActions = actions.filter(action => {\n      const {\n        visible,\n        permission\n      } = action;\n      let isVisible = true;\n      let hasPermission = true;\n      if (visible) {\n        isVisible = visible({\n          record: rowData,\n          getInjected: this.getInjected\n        });\n      }\n      if (permission) {\n        hasPermission = this.permissionService.getGrantedPolicy(permission);\n      }\n      return isVisible && hasPermission;\n    });\n    return visibleActions.length > 0;\n  }\n  static {\n    this.ɵfac = function ExtensibleTableComponent_Factory(t) {\n      return new (t || ExtensibleTableComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ExtensibleTableComponent,\n      selectors: [[\"abp-extensible-table\"]],\n      inputs: {\n        actionsText: \"actionsText\",\n        data: \"data\",\n        list: \"list\",\n        recordsTotal: \"recordsTotal\",\n        actionsColumnWidth: \"actionsColumnWidth\",\n        actionsTemplate: \"actionsTemplate\"\n      },\n      outputs: {\n        tableActivate: \"tableActivate\"\n      },\n      exportAs: [\"abpExtensibleTable\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[\"gridActions\", \"\"], [\"default\", \"\", 3, \"activate\", \"rows\", \"count\", \"list\"], [3, \"name\", \"maxWidth\", \"width\", \"sortable\"], [3, \"width\", \"name\", \"prop\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"text\", \"AbpUi::Actions\", 3, \"index\", \"record\"], [3, \"width\", \"name\", \"prop\", \"sortable\", 4, \"abpVisible\"], [\"ngx-datatable-header-template\", \"\"], [\"container\", \"body\", 3, \"ngbTooltip\", \"placement\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-info-circle\"], [4, \"abpPermission\", \"abpPermissionRunChangeDetection\"], [4, \"abpVisible\"], [3, \"innerHTML\", \"ngClass\", \"pointer\"], [3, \"click\", \"innerHTML\", \"ngClass\"], [4, \"ngComponentOutlet\", \"ngComponentOutletInjector\"]],\n      template: function ExtensibleTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ngx-datatable\", 1);\n          i0.ɵɵlistener(\"activate\", function ExtensibleTableComponent_Template_ngx_datatable_activate_0_listener($event) {\n            return ctx.tableActivate.emit($event);\n          });\n          i0.ɵɵtemplate(1, ExtensibleTableComponent_Conditional_1_Template, 3, 6, \"ngx-datatable-column\", 2);\n          i0.ɵɵrepeaterCreate(2, ExtensibleTableComponent_For_3_Template, 1, 1, \"ngx-datatable-column\", 3, _forTrack1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"rows\", ctx.data)(\"count\", ctx.recordsTotal)(\"list\", ctx.list);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.actionsTemplate || ctx.actionList.length && ctx.hasAtLeastOnePermittedAction ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx.propList);\n        }\n      },\n      dependencies: [AbpVisibleDirective, NgxDatatableModule, i1$1.DatatableComponent, i1$1.DataTableColumnDirective, i1$1.DataTableColumnHeaderDirective, i1$1.DataTableColumnCellDirective, GridActionsComponent, NgbTooltip, NgxDatatableDefaultDirective, NgxDatatableListDirective, PermissionDirective, LocalizationModule, i2$1.LocalizationPipe, AsyncPipe, NgTemplateOutlet, NgComponentOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensibleTableComponent, [{\n    type: Component,\n    args: [{\n      exportAs: 'abpExtensibleTable',\n      selector: 'abp-extensible-table',\n      standalone: true,\n      imports: [AbpVisibleDirective, NgxDatatableModule, GridActionsComponent, NgbTooltip, NgxDatatableDefaultDirective, NgxDatatableListDirective, PermissionDirective, LocalizationModule, AsyncPipe, NgTemplateOutlet, NgComponentOutlet],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ngx-datatable\\r\\n  default\\r\\n  [rows]=\\\"data\\\"\\r\\n  [count]=\\\"recordsTotal\\\"\\r\\n  [list]=\\\"list\\\"\\r\\n  (activate)=\\\"tableActivate.emit($event)\\\"\\r\\n>\\r\\n  @if (actionsTemplate || (actionList.length && hasAtLeastOnePermittedAction)) {\\r\\n    <ngx-datatable-column\\r\\n      [name]=\\\"actionsText | abpLocalization\\\"\\r\\n      [maxWidth]=\\\"columnWidths[0]\\\"\\r\\n      [width]=\\\"columnWidths[0]\\\"\\r\\n      [sortable]=\\\"false\\\"\\r\\n    >\\r\\n      <ng-template let-row=\\\"row\\\" let-i=\\\"rowIndex\\\" ngx-datatable-cell-template>\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"actionsTemplate || gridActions; context: { $implicit: row, index: i }\\\"\\r\\n        ></ng-container>\\r\\n        <ng-template #gridActions>\\r\\n          @if (isVisibleActions(row)) {\\r\\n            <abp-grid-actions [index]=\\\"i\\\" [record]=\\\"row\\\" text=\\\"AbpUi::Actions\\\"></abp-grid-actions>\\r\\n          }\\r\\n        </ng-template>\\r\\n      </ng-template>\\r\\n    </ngx-datatable-column>\\r\\n  }\\r\\n  @for (prop of propList; track prop.name; let i = $index) {\\r\\n    <ngx-datatable-column\\r\\n      *abpVisible=\\\"prop.columnVisible(getInjected)\\\"\\r\\n      [width]=\\\"columnWidths[i + 1] || 200\\\"\\r\\n      [name]=\\\"prop.displayName | abpLocalization\\\"\\r\\n      [prop]=\\\"prop.name\\\"\\r\\n      [sortable]=\\\"prop.sortable\\\"\\r\\n    >\\r\\n      <ng-template ngx-datatable-header-template let-column=\\\"column\\\">\\r\\n        @if (prop.tooltip) {\\r\\n          <span\\r\\n            [ngbTooltip]=\\\"prop.tooltip.text | abpLocalization\\\"\\r\\n            [placement]=\\\"prop.tooltip.placement || 'auto'\\\"\\r\\n            container=\\\"body\\\"\\r\\n          >\\r\\n            {{ column.name }} <i class=\\\"fa fa-info-circle\\\" aria-hidden=\\\"true\\\"></i>\\r\\n          </span>\\r\\n        } @else {\\r\\n          {{ column.name }}\\r\\n        }\\r\\n      </ng-template>\\r\\n      <ng-template let-row=\\\"row\\\" let-i=\\\"index\\\" ngx-datatable-cell-template>\\r\\n        <ng-container *abpPermission=\\\"prop.permission; runChangeDetection: false\\\">\\r\\n          <ng-container *abpVisible=\\\"row['_' + prop.name]?.visible\\\">\\r\\n            @if (!row['_' + prop.name].component) {\\r\\n              <div\\r\\n                [innerHTML]=\\\"row['_' + prop.name]?.value | async\\\"\\r\\n                (click)=\\\"\\r\\n                  prop.action && prop.action({ getInjected: getInjected, record: row, index: i })\\r\\n                \\\"\\r\\n                [ngClass]=\\\"entityPropTypeClasses[prop.type]\\\"\\r\\n                [class.pointer]=\\\"prop.action\\\"\\r\\n              ></div>\\r\\n            } @else {\\r\\n              <ng-container\\r\\n                *ngComponentOutlet=\\\"\\r\\n                  row['_' + prop.name].component;\\r\\n                  injector: row['_' + prop.name].injector\\r\\n                \\\"\\r\\n              ></ng-container>\\r\\n            }\\r\\n          </ng-container>\\r\\n        </ng-container>\\r\\n      </ng-template>\\r\\n    </ngx-datatable-column>\\r\\n  }\\r\\n</ngx-datatable>\\r\\n\"\n    }]\n  }], () => [], {\n    actionsText: [{\n      type: Input\n    }],\n    data: [{\n      type: Input\n    }],\n    list: [{\n      type: Input\n    }],\n    recordsTotal: [{\n      type: Input\n    }],\n    actionsColumnWidth: [{\n      type: Input\n    }],\n    actionsTemplate: [{\n      type: Input\n    }],\n    tableActivate: [{\n      type: Output\n    }]\n  });\n})();\nclass PageToolbarComponent extends AbstractActionsComponent {\n  constructor(injector) {\n    super(injector);\n    this.injector = injector;\n    this.defaultBtnClass = 'btn btn-sm btn-primary';\n    this.getData = () => this.data;\n    this.trackByFn = (_, item) => item.action || item.component;\n  }\n  asToolbarAction(value) {\n    return {\n      value: value\n    };\n  }\n  static {\n    this.ɵfac = function PageToolbarComponent_Factory(t) {\n      return new (t || PageToolbarComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PageToolbarComponent,\n      selectors: [[\"abp-page-toolbar\"]],\n      exportAs: [\"abpPageToolbar\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: EXTENSIONS_ACTION_TYPE,\n        useValue: 'toolbarActions'\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[\"id\", \"AbpContentToolbar\", 1, \"row\", \"justify-content-end\", \"mx-0\", \"gap-2\"], [1, \"col-auto\", \"px-0\", \"pt-0\", 3, \"pe-0\"], [1, \"col-auto\", \"px-0\", \"pt-0\"], [4, \"abpPermission\", \"abpPermissionRunChangeDetection\"], [4, \"ngComponentOutlet\", \"ngComponentOutletInjector\"], [\"type\", \"button\", 1, \"d-inline-flex\", \"align-items-center\", \"gap-1\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"d-inline-flex\", \"align-items-center\", \"gap-1\", 3, \"click\", \"ngClass\"], [3, \"ngClass\"]],\n      template: function PageToolbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵrepeaterCreate(1, PageToolbarComponent_For_2_Template, 2, 3, \"div\", 1, _forTrack3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx.actionList);\n        }\n      },\n      dependencies: [CreateInjectorPipe, PermissionDirective, LocalizationModule, i2$1.LocalizationPipe, NgClass, NgComponentOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PageToolbarComponent, [{\n    type: Component,\n    args: [{\n      exportAs: 'abpPageToolbar',\n      selector: 'abp-page-toolbar',\n      standalone: true,\n      imports: [CreateInjectorPipe, PermissionDirective, LocalizationModule, NgClass, NgComponentOutlet],\n      providers: [{\n        provide: EXTENSIONS_ACTION_TYPE,\n        useValue: 'toolbarActions'\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"row justify-content-end mx-0 gap-2\\\" id=\\\"AbpContentToolbar\\\">\\r\\n  @for (action of actionList; track action.component || action.action; let last = $last) {\\r\\n  <div class=\\\"col-auto px-0 pt-0\\\" [class.pe-0]=\\\"last\\\">\\r\\n    @if (action.visible(data)) {\\r\\n    <ng-container *abpPermission=\\\"action.permission; runChangeDetection: false\\\">\\r\\n      @if (action.component; as component) {\\r\\n      <ng-container\\r\\n        *ngComponentOutlet=\\\"component; injector: record | createInjector: action:this\\\"\\r\\n      ></ng-container>\\r\\n\\r\\n      }@else {\\r\\n         @if (asToolbarAction(action).value; as toolbarAction ) {\\r\\n          <button\\r\\n            (click)=\\\"action.action(data)\\\"\\r\\n            type=\\\"button\\\"\\r\\n            [ngClass]=\\\"toolbarAction?.btnClass ? toolbarAction?.btnClass : defaultBtnClass\\\"\\r\\n            class=\\\"d-inline-flex align-items-center gap-1\\\"\\r\\n          >\\r\\n            <i [ngClass]=\\\"toolbarAction?.icon\\\" [class.me-1]=\\\"toolbarAction?.icon\\\"></i>\\r\\n            {{ toolbarAction?.text | abpLocalization }}\\r\\n          </button>\\r\\n        } \\r\\n      }\\r\\n    </ng-container>\\r\\n    }\\r\\n  </div>\\r\\n  }\\r\\n</div>\\r\\n\\r\\n\"\n    }]\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nvar objectExtensions = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nconst EXTENSIBLE_FORM_VIEW_PROVIDER = {\n  provide: ControlContainer,\n  useExisting: FormGroupDirective\n};\nfunction mergeWithDefaultActions(extension, defaultActions, ...contributors) {\n  Object.keys(defaultActions).forEach(name => {\n    const actions = extension.get(name);\n    actions.clearContributors();\n    actions.addContributor(actionList => actionList.addManyTail(defaultActions[name]));\n    contributors.forEach(contributor => (contributor[name] || []).forEach(callback => actions.addContributor(callback)));\n  });\n}\nfunction generateFormFromProps(data) {\n  const extensions = data.getInjected(ExtensionsService);\n  const identifier = data.getInjected(EXTENSIONS_IDENTIFIER);\n  const form = new UntypedFormGroup({});\n  const extraForm = new UntypedFormGroup({});\n  form.addControl(EXTRA_PROPERTIES_KEY, extraForm);\n  const record = data.record || {};\n  const type = JSON.stringify(record) === '{}' ? 'create' : 'edit';\n  const props = extensions[`${type}FormProps`].get(identifier).props;\n  const extraProperties = record[EXTRA_PROPERTIES_KEY] || {};\n  props.forEach(({\n    value: prop\n  }) => {\n    const name = prop.name;\n    const isExtraProperty = prop.isExtra || name in extraProperties;\n    let value = isExtraProperty ? extraProperties[name] : name in record ? record[name] : undefined;\n    if (typeof value === 'undefined') value = prop.defaultValue;\n    if (value) {\n      let adapter;\n      switch (prop.type) {\n        case \"date\" /* ePropType.Date */:\n          adapter = new DateAdapter();\n          value = adapter.toModel(adapter.fromModel(value));\n          break;\n        case \"time\" /* ePropType.Time */:\n          adapter = new TimeAdapter();\n          value = adapter.toModel(adapter.fromModel(value));\n          break;\n        case \"datetime\" /* ePropType.DateTime */:\n          adapter = new DateTimeAdapter();\n          value = adapter.toModel(adapter.fromModel(value));\n          break;\n        default:\n          break;\n      }\n    }\n    const formControl = new UntypedFormControl(value, {\n      asyncValidators: prop.asyncValidators(data),\n      validators: prop.validators(data)\n    });\n    (isExtraProperty ? extraForm : form).addControl(name, formControl);\n  });\n  return form;\n}\nfunction createExtraPropertyValueResolver(name) {\n  return data => of(data.record[EXTRA_PROPERTIES_KEY][name]);\n}\nfunction mergeWithDefaultProps(extension, defaultProps, ...contributors) {\n  Object.keys(defaultProps).forEach(name => {\n    const props = extension.get(name);\n    props.clearContributors();\n    props.addContributor(propList => propList.addManyTail(defaultProps[name]));\n    contributors.forEach(contributor => (contributor[name] || []).forEach(callback => props.addContributor(callback)));\n  });\n}\nfunction createEnum(members) {\n  const enumObject = {};\n  members.forEach(({\n    name = '',\n    value\n  }) => {\n    enumObject[enumObject[name] = value] = name;\n  });\n  return enumObject;\n}\nfunction createEnumValueResolver(enumType, lookupEnum, propName) {\n  return data => {\n    const value = data.record[EXTRA_PROPERTIES_KEY][propName];\n    const key = lookupEnum.transformed[value];\n    const l10n = data.getInjected(LocalizationService);\n    const localizeEnum = createEnumLocalizer(l10n, enumType, lookupEnum);\n    return createLocalizationStream(l10n, localizeEnum(key));\n  };\n}\nfunction createEnumOptions(enumType, lookupEnum) {\n  return data => {\n    const l10n = data.getInjected(LocalizationService);\n    const localizeEnum = createEnumLocalizer(l10n, enumType, lookupEnum);\n    return createLocalizationStream(l10n, lookupEnum.fields.map(({\n      name = '',\n      value\n    }) => ({\n      key: localizeEnum(name),\n      value\n    })));\n  };\n}\nfunction createLocalizationStream(l10n, mapTarget) {\n  return merge(of(null), l10n.languageChange$).pipe(map(() => mapTarget));\n}\nfunction createEnumLocalizer(l10n, enumType, lookupEnum) {\n  const resource = lookupEnum.localizationResource;\n  const shortType = getShortEnumType(enumType);\n  return key => l10n.localizeWithFallbackSync([resource || ''], ['Enum:' + shortType + '.' + key, shortType + '.' + key, key], key);\n}\nfunction getShortEnumType(enumType) {\n  return enumType.split('.').pop();\n}\nfunction createDisplayNameLocalizationPipeKeyGenerator(localization) {\n  const generateLocalizationPipeKey = createLocalizationPipeKeyGenerator(localization);\n  return (displayName, fallback) => {\n    if (displayName && displayName.name) return generateLocalizationPipeKey([displayName.resource || ''], [displayName.name], displayName.name);\n    const key = generateLocalizationPipeKey([fallback.resource || ''], ['DisplayName:' + fallback.name], undefined);\n    if (key) return key;\n    return generateLocalizationPipeKey([fallback.resource || ''], [fallback.name || ''], fallback.name);\n  };\n}\nfunction getValidatorsFromProperty(property) {\n  const validators = [];\n  property.attributes.forEach(attr => {\n    if (attr.typeSimple && attr.typeSimple in AbpValidators) {\n      validators.push(AbpValidators[attr.typeSimple](attr.config));\n    }\n  });\n  return validators;\n}\nfunction selectObjectExtensions(configState) {\n  return configState.getOne$('objectExtensions');\n}\nfunction selectLocalization(configState) {\n  return configState.getOne$('localization');\n}\nfunction selectEnums(configState) {\n  return selectObjectExtensions(configState).pipe(map(extensions => Object.keys(extensions.enums).reduce((acc, key) => {\n    const {\n      fields,\n      localizationResource\n    } = extensions.enums[key];\n    acc[key] = {\n      fields,\n      localizationResource,\n      transformed: createEnum(fields)\n    };\n    return acc;\n  }, {})));\n}\nfunction getObjectExtensionEntitiesFromStore(configState, moduleKey) {\n  return selectObjectExtensions(configState).pipe(map(extensions => {\n    if (!extensions) return null;\n    return (extensions.modules[moduleKey] || {}).entities;\n  }), map(entities => isUndefined(entities) ? {} : entities), filter(Boolean), take(1));\n}\nfunction mapEntitiesToContributors(configState, resource) {\n  return pipe(switchMap(entities => zip(selectLocalization(configState), selectEnums(configState)).pipe(map(([localization, enums]) => {\n    const generateDisplayName = createDisplayNameLocalizationPipeKeyGenerator(localization);\n    return Object.keys(entities).reduce((acc, key) => {\n      acc.prop[key] = [];\n      acc.createForm[key] = [];\n      acc.editForm[key] = [];\n      const entity = entities[key];\n      if (!entity) return acc;\n      const properties = entity.properties;\n      if (!properties) return acc;\n      const mapPropertiesToContributors = createPropertiesToContributorsMapper(generateDisplayName, resource, enums);\n      return mapPropertiesToContributors(properties, acc, key);\n    }, {\n      prop: {},\n      createForm: {},\n      editForm: {}\n    });\n  }))), take(1));\n}\nfunction createPropertiesToContributorsMapper(generateDisplayName, resource, enums) {\n  return (properties, contributors, key) => {\n    const isExtra = true;\n    const generateTypeaheadDisplayName = createTypeaheadDisplayNameGenerator(generateDisplayName, properties);\n    Object.keys(properties).forEach(name => {\n      const property = properties[name];\n      const propName = name;\n      const lookup = property.ui.lookup || {};\n      const type = getTypeaheadType(lookup, name) || getTypeFromProperty(property);\n      const generateDN = hasTypeaheadTextSuffix(name) ? generateTypeaheadDisplayName : generateDisplayName;\n      const displayName = generateDN(property.displayName, {\n        name,\n        resource\n      });\n      if (property.ui.onTable.isVisible) {\n        const sortable = Boolean(property.ui.onTable.isSortable);\n        const columnWidth = type === \"boolean\" /* ePropType.Boolean */ ? 150 : 250;\n        const valueResolver = type === \"enum\" /* ePropType.Enum */ && property.type ? createEnumValueResolver(property.type, enums[property.type], propName) : createExtraPropertyValueResolver(propName);\n        const entityProp = new EntityProp({\n          type,\n          name: propName,\n          displayName,\n          sortable,\n          columnWidth,\n          valueResolver,\n          isExtra\n        });\n        const contributor = propList => propList.addTail(entityProp);\n        contributors.prop[key].push(contributor);\n      }\n      const isOnCreateForm = property.ui.onCreateForm.isVisible;\n      const isOnEditForm = property.ui.onEditForm.isVisible;\n      if (isOnCreateForm || isOnEditForm) {\n        const defaultValue = property.defaultValue;\n        const formText = property.formText;\n        const validators = () => getValidatorsFromProperty(property);\n        let options;\n        if (type === \"enum\" /* ePropType.Enum */) options = createEnumOptions(propName, enums[property.type || '']);else if (type === \"typeahead\" /* ePropType.Typeahead */) options = createTypeaheadOptions(lookup);\n        const formProp = new FormProp({\n          type,\n          name: propName,\n          displayName,\n          options,\n          defaultValue,\n          validators,\n          isExtra,\n          formText\n        });\n        const formContributor = propList => propList.addTail(formProp);\n        if (isOnCreateForm) contributors.createForm[key].push(formContributor);\n        if (isOnEditForm) contributors.editForm[key].push(formContributor);\n      }\n    });\n    return contributors;\n  };\n}\nfunction getTypeFromProperty(property) {\n  return property?.typeSimple?.replace(/\\?$/, '');\n}\nfunction isUndefined(obj) {\n  return typeof obj === 'undefined';\n}\nconst importWithExport = [DisabledDirective, ExtensibleDateTimePickerComponent, ExtensibleFormPropComponent, GridActionsComponent, PropDataDirective, PageToolbarComponent, CreateInjectorPipe, ExtensibleFormComponent, ExtensibleTableComponent];\nclass ExtensibleModule {\n  static {\n    this.ɵfac = function ExtensibleModule_Factory(t) {\n      return new (t || ExtensibleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ExtensibleModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule, NgbDatepickerModule, NgbDropdownModule, NgbTimepickerModule, NgbTypeaheadModule, NgbTooltipModule, ExtensibleDateTimePickerComponent, ExtensibleFormPropComponent, GridActionsComponent, PageToolbarComponent, ExtensibleFormComponent, ExtensibleTableComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtensibleModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule, NgbDatepickerModule, NgbDropdownModule, NgbTimepickerModule, NgbTypeaheadModule, NgbTooltipModule, ...importWithExport],\n      exports: [...importWithExport]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ActionList, CreateFormPropsFactory, CreateInjectorPipe, ENTITY_PROP_TYPE_CLASSES, EXTENSIBLE_FORM_VIEW_PROVIDER, EXTENSIONS_ACTION_CALLBACK, EXTENSIONS_ACTION_DATA, EXTENSIONS_ACTION_TYPE, EXTENSIONS_FORM_PROP, EXTENSIONS_FORM_PROP_DATA, EXTENSIONS_IDENTIFIER, EXTRA_PROPERTIES_KEY, EditFormPropsFactory, EntityAction, EntityActionList, EntityActions, EntityActionsFactory, EntityProp, EntityPropList, EntityProps, EntityPropsFactory, ExtensibleDateTimePickerComponent, ExtensibleFormComponent, ExtensibleFormPropComponent, ExtensibleModule, ExtensibleTableComponent, ExtensionsService, FormProp, FormPropData, FormPropList, FormProps, GridActionsComponent, objectExtensions as ObjectExtensions, PROP_DATA_STREAM, PageToolbarComponent, PropDataDirective, PropList, ToolbarAction, ToolbarActionList, ToolbarActions, ToolbarActionsFactory, ToolbarComponent, createExtraPropertyValueResolver, generateFormFromProps, getObjectExtensionEntitiesFromStore, mapEntitiesToContributors, mergeWithDefaultActions, mergeWithDefaultProps };", "map": {"version": 3, "names": ["i0", "Optional", "SkipSelf", "Component", "ChangeDetectionStrategy", "Input", "ViewChild", "InjectionToken", "inject", "Injectable", "<PERSON><PERSON>", "ChangeDetectorRef", "Injector", "Directive", "ViewChildren", "EventEmitter", "LOCALE_ID", "Output", "NgModule", "i2", "ReactiveFormsModule", "ControlContainer", "Validators", "FormGroupDirective", "FormsModule", "UntypedFormGroup", "UntypedFormControl", "i1", "NgbInputDatepicker", "NgbTimepicker", "NgbDatepickerModule", "NgbTimepickerModule", "NgbDateAdapter", "NgbTimeAdapter", "NgbTooltip", "NgbTypeaheadModule", "NgbDropdownModule", "NgbTooltipModule", "LinkedList", "DateTimeAdapter", "DisabledDirective", "DateAdapter", "TimeAdapter", "EllipsisDirective", "AbpVisibleDirective", "NgxDatatableDefaultDirective", "NgxDatatableListDirective", "ThemeSharedModule", "i5", "CommonModule", "Ng<PERSON><PERSON>", "NgTemplateOutlet", "formatDate", "AsyncPipe", "NgComponentOutlet", "i2$1", "RestService", "ConfigStateService", "AbpValidators", "TrackByService", "ShowPasswordDirective", "PermissionDirective", "LocalizationModule", "escapeHtmlChars", "PermissionService", "getShortDateShortTimeFormat", "getShortTimeFormat", "getShortDateFormat", "LocalizationService", "createLocalizationPipeKeyGenerator", "CoreModule", "of", "merge", "pipe", "zip", "map", "debounceTime", "distinctUntilChanged", "switchMap", "filter", "take", "i3", "NgxValidateCoreModule", "i1$1", "NgxDatatableModule", "_c0", "_forTrack0", "$index", "$item", "value", "_c1", "$implicit", "_c2", "standalone", "_c3", "a0", "a1", "ExtensibleFormPropComponent_ng_container_0_ng_template_1_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "ExtensibleFormPropComponent_ng_container_0_ng_template_1_Template", "ɵɵtemplate", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "prop", "template", "injectorForCustomComponent", "ExtensibleFormPropComponent_ng_container_0_ng_template_3_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_3_Template", "ɵɵelement", "label_r2", "ɵɵreference", "ɵɵadvance", "id", "name", "autocomplete", "getType", "disabled", "readonly", "ExtensibleFormPropComponent_ng_container_0_ng_template_4_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_5_ng_template_3_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_5_Template", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵpureFunction0", "ExtensibleFormPropComponent_ng_container_0_ng_template_6_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_6_For_4_Template", "ɵɵtext", "option_r3", "ɵɵtextInterpolate1", "key", "ExtensibleFormPropComponent_ng_container_0_ng_template_6_Template", "ɵɵrepeaterCreate", "ɵɵpipe", "ɵɵrepeater", "ɵɵpipeBind1", "options$", "ExtensibleFormPropComponent_ng_container_0_ng_template_7_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_7_option_3_Template", "option_r4", "ExtensibleFormPropComponent_ng_container_0_ng_template_7_Template", "track", "by", "ExtensibleFormPropComponent_ng_container_0_ng_template_8_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template", "_r5", "ɵɵgetCurrentView", "ɵɵtwoWayListener", "ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template_input_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "ɵɵtwoWayBindingSet", "typeaheadModel", "ɵɵresetView", "ɵɵlistener", "ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template_input_selectItem_3_listener", "setTypeaheadValue", "item", "ExtensibleFormPropComponent_ng_container_0_ng_template_8_Template_input_blur_3_listener", "typeahead_r6", "ɵɵclassProp", "classList", "contains", "search", "typeaheadFormatter", "ɵɵtwoWayProperty", "ExtensibleFormPropComponent_ng_container_0_ng_template_9_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template", "_r7", "ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template_input_click_1_listener", "datepicker_r8", "open", "ExtensibleFormPropComponent_ng_container_0_ng_template_9_Template_input_keyup_space_1_listener", "ExtensibleFormPropComponent_ng_container_0_ng_template_10_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_10_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_11_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_11_Template", "meridian$", "ExtensibleFormPropComponent_ng_container_0_ng_template_12_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_12_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_13_ng_template_0_Template", "ExtensibleFormPropComponent_ng_container_0_ng_template_13_Template", "_r9", "ExtensibleFormPropComponent_ng_container_0_ng_template_13_Template_button_click_3_listener", "showPassword", "ɵɵpureFunction2", "ExtensibleFormPropComponent_ng_container_0_Conditional_14_Template", "ɵɵtextInterpolate", "formText", "ExtensibleFormPropComponent_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "getComponent", "containerClassName", "ɵɵconditional", "ExtensibleFormPropComponent_ng_template_1_Conditional_1_Template", "displayTextResolver", "data", "ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_0_Template", "displayName", "ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_1_Template", "ExtensibleFormPropComponent_ng_template_1_Conditional_2_Template", "isExtra", "ExtensibleFormPropComponent_ng_template_1_Conditional_4_Template", "tooltip", "text", "placement", "ExtensibleFormPropComponent_ng_template_1_Template", "classes_r10", "asterisk", "_forTrack1", "_c4", "a2", "groupedProp", "isFirstGroup", "ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_1_Template", "data_r1", "ctx_r1", "groupedProp_r3", "ɵi_2_r4", "propListTemplate_r5", "group", "className", "ɵɵattribute", "ɵɵpureFunction3", "ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_2_Template", "ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Template", "i_r6", "ctx_r6", "isAnyGroupMemberVisible", "ExtensibleFormComponent_Conditional_0_For_1_Template", "formPropList", "record", "ExtensibleFormComponent_Conditional_0_Template", "ɵɵrepeaterTrackByIndex", "groupedPropList", "items", "ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_0_Template", "prop_r8", "data_r9", "extraPropertiesKey", "ɵɵclassMap", "ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Conditional_0_Template", "ctx_r9", "ɵindex_17_r11", "ctx_r11", "isFirstGroup_r13", "ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Template", "form", "get", "ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Template", "extraProperties", "controls", "ExtensibleFormComponent_ng_template_1_For_1_Template", "visible", "ExtensibleFormComponent_ng_template_1_Template", "groupedProp_r14", "_forTrack2", "_c5", "GridActionsComponent_Conditional_0_For_7_Template", "action_r1", "dropDownBtnItemTmp_r2", "ɵɵpureFunction1", "GridActionsComponent_Conditional_0_Template", "ctx_r2", "icon", "actionList", "GridActionsComponent_Conditional_1_Template", "btnTmp_r4", "GridActionsComponent_ng_template_2_Conditional_0_button_0_ng_container_1_Template", "GridActionsComponent_ng_template_2_Conditional_0_button_0_Template", "GridActionsComponent_ng_template_2_Conditional_0_button_0_Template_button_click_0_listener", "action_r6", "action", "buttonContentTmp_r7", "GridActionsComponent_ng_template_2_Conditional_0_Template", "permission", "GridActionsComponent_ng_template_2_Template", "GridActionsComponent_ng_template_4_Conditional_1_Conditional_0_Template", "action_r8", "GridActionsComponent_ng_template_4_Conditional_1_Conditional_1_Template", "GridActionsComponent_ng_template_4_Conditional_1_Template", "GridActionsComponent_ng_template_4_Template", "showOnlyIcon", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_ng_container_2_Template", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template_button_click_0_listener", "action_r10", "ɵɵstyleMap", "btnStyle", "btnClass", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_Template", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_ng_container_1_Template", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template", "_r11", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template_button_click_0_listener", "GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_Template", "GridActionsComponent_ng_template_6_Conditional_0_Template", "GridActionsComponent_ng_template_6_Template", "_c6", "index", "ExtensibleTableComponent_Conditional_1_ng_template_2_ng_container_0_Template", "ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Conditional_0_Template", "row_r2", "row", "i_r3", "rowIndex", "ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Template", "ctx_r3", "isVisibleActions", "ExtensibleTableComponent_Conditional_1_ng_template_2_Template", "ɵɵtemplateRefExtractor", "gridActions_r5", "actionsTemplate", "ExtensibleTableComponent_Conditional_1_Template", "actionsText", "columnWidths", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_0_Template", "column_r6", "column", "prop_r7", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_1_Template", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Template", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template", "_r8", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template_div_click_0_listener", "ctx_r8", "row_r10", "i_r11", "getInjected", "ɵɵsanitizeHtml", "entityPropTypeClasses", "type", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_ng_container_0_Template", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_Template", "component", "injector", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Template", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_Template", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_Template", "ExtensibleTableComponent_For_3_ngx_datatable_column_0_Template", "i_r13", "sortable", "ExtensibleTableComponent_For_3_Template", "columnVisible", "_forTrack3", "PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_ng_container_0_Template", "PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_Template", "ɵɵpipeBind3", "PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template", "_r3", "PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template_button_click_0_listener", "toolbarAction_r4", "defaultBtnClass", "PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Template", "tmp_13_0", "asToolbarAction", "PageToolbarComponent_For_2_Conditional_1_ng_container_0_Template", "tmp_12_0", "PageToolbarComponent_For_2_Conditional_1_Template", "PageToolbarComponent_For_2_Template", "ɵ$index_3_r5", "ɵ$count_3_r6", "$count", "PropList", "PropData", "Prop", "constructor", "_", "PropsFactory", "contributorCallbacks", "_ctor", "Props", "props", "propList", "callbackList", "for<PERSON>ach", "callback", "addContributor", "contributeCallback", "push", "clearContributors", "length", "pop", "FormPropList", "FormProps", "arguments", "GroupedFormPropList", "count", "addItem", "groupName", "find", "i", "addTail", "addHead", "CreateFormPropsFactory", "EditFormPropsFactory", "FormProp", "options", "asyncValidators", "validators", "defaultValue", "isFalsyValue", "create", "createMany", "arrayOfOptions", "FormPropData", "bind", "indexOf", "selfFactory", "dependency", "ExtensibleDateTimePickerComponent", "cdRef", "meridian", "setDate", "dateStr", "date", "writeValue", "setTime", "time", "ɵfac", "ExtensibleDateTimePickerComponent_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "ExtensibleDateTimePickerComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useFactory", "deps", "useClass", "ɵɵStandaloneFeature", "decls", "vars", "consts", "ExtensibleDateTimePickerComponent_Template", "_r1", "ExtensibleDateTimePickerComponent_Template_input_ngModelChange_0_listener", "ExtensibleDateTimePickerComponent_Template_input_click_0_listener", "datepicker_r2", "ExtensibleDateTimePickerComponent_Template_input_keyup_space_0_listener", "ExtensibleDateTimePickerComponent_Template_ngb_timepicker_ngModelChange_2_listener", "dependencies", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "imports", "selector", "OnPush", "viewProviders", "EXTENSIONS_IDENTIFIER", "EXTENSIONS_ACTION_TYPE", "EXTENSIONS_ACTION_DATA", "EXTENSIONS_ACTION_CALLBACK", "PROP_DATA_STREAM", "ENTITY_PROP_TYPE_CLASSES", "factory", "EXTENSIONS_FORM_PROP", "EXTENSIONS_FORM_PROP_DATA", "EXTRA_PROPERTIES_KEY", "TYPEAHEAD_TEXT_SUFFIX", "TYPEAHEAD_TEXT_SUFFIX_REGEX", "createTypeaheadOptions", "lookup", "searchText", "request", "method", "url", "params", "filterParamName", "apiName", "response", "list", "resultListPropertyName", "mapToOption", "displayPropertyName", "valuePropertyName", "getTypeaheadType", "endsWith", "undefined", "createTypeaheadDisplayNameGenerator", "displayNameGeneratorFn", "properties", "fallback", "removeTypeaheadTextSuffix", "resource", "addTypeaheadTextSuffix", "hasTypeaheadTextSuffix", "test", "replace", "ExtensibleFormPropService", "configStateService", "getDeep$", "shortTimePattern", "includes", "isRequired", "validator", "required", "calcAsterisks", "v", "ExtensibleFormPropService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "CreateInjectorPipe", "transform", "context", "notFoundValue", "componentData", "getData", "componentDataCallback", "extensionData", "call", "CreateInjectorPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "pure", "ExtensibleFormPropComponent", "service", "groupDirective", "<PERSON><PERSON><PERSON>", "disabledFn", "text$", "option", "selectedOption", "keyControl", "valueControl", "getTypeaheadControls", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setValue", "isInvalid", "control", "touched", "invalid", "extraPropName", "setAsterisk", "ngAfterViewInit", "fieldRef", "nativeElement", "focus", "ngOnChanges", "currentProp", "currentValue", "providers", "useValue", "useExisting", "parent", "ExtensibleFormPropComponent_Factory", "ExtensibleFormPropComponent_Query", "ɵɵNgOnChangesFeature", "ExtensibleFormPropComponent_Template", "NgSelectOption", "ɵNgSelectMultipleOption", "CheckboxControlValueAccessor", "SelectControlValueAccessor", "SelectMultipleControlValueAccessor", "ValidationStyleDirective", "ValidationTargetDirective", "ValidationDirective", "NgbTypeahead", "LocalizationPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgSwitch", "NgSwitchCase", "NgModel", "ActionList", "ActionData", "Action", "ActionsFactory", "Actions", "actions", "EntityActionList", "EntityActions", "EntityActionsFactory", "EntityAction", "EntityPropList", "EntityProps", "EntityPropsFactory", "EntityProp", "columnWidth", "valueResolver", "enumList", "ToolbarActionList", "ToolbarActions", "ToolbarActionsFactory", "ToolbarAction", "ToolbarComponent", "ExtensionsService", "entityActions", "toolbarActions", "entityProps", "createFormProps", "editFormProps", "ExtensionsService_Factory", "providedIn", "PropDataDirective", "tempRef", "vcRef", "clear", "createEmbeddedView", "ngOnDestroy", "PropDataDirective_Factory", "TemplateRef", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "ɵɵInputFlags", "None", "ɵɵInheritDefinitionFeature", "ExtensibleFormComponent", "container", "extensions", "identifier", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createGroupedList", "groupedFormPropList", "toArray", "some", "ExtensibleFormComponent_Factory", "ExtensibleFormComponent_Query", "formProps", "ExtensibleFormComponent_Template", "NgControlStatusGroup", "FormGroupName", "AbstractActionsComponent", "AbstractActionsComponent_Factory", "GridActionsComponent", "trackByFn", "GridActionsComponent_Factory", "GridActionsComponent_Template", "NgbDropdown", "NgbDropdownToggle", "NgbDropdownMenu", "NgbDropdownItem", "NgbDropdownButtonItem", "DEFAULT_ACTIONS_COLUMN_WIDTH", "ExtensibleTableComponent", "_actionsText", "actionsColumnWidth", "width", "setColumnWidths", "Number", "tableActivate", "locale", "config", "permissionService", "hasAtLeastOnePermittedAction", "filterItemsByPolicy", "requiredPolicy", "actionsColumn", "widths", "getDate", "format", "getIcon", "getEnum", "rowValue", "get<PERSON>ontent", "totalCount", "recordsTotal", "propData", "<PERSON><PERSON><PERSON>", "rowData", "visibleActions", "isVisible", "hasPermission", "getGrantedPolicy", "ExtensibleTableComponent_Factory", "outputs", "ExtensibleTableComponent_Template", "ExtensibleTableComponent_Template_ngx_datatable_activate_0_listener", "emit", "DatatableComponent", "DataTableColumnDirective", "DataTableColumnHeaderDirective", "DataTableColumnCellDirective", "PageToolbarComponent", "PageToolbarComponent_Factory", "PageToolbarComponent_Template", "objectExtensions", "Object", "freeze", "__proto__", "EXTENSIBLE_FORM_VIEW_PROVIDER", "mergeWithDefaultActions", "extension", "defaultActions", "contributors", "keys", "addManyTail", "contributor", "generateFormFromProps", "extraForm", "addControl", "isExtraProperty", "adapter", "toModel", "fromModel", "formControl", "createExtraPropertyValueResolver", "mergeWithDefaultProps", "defaultProps", "createEnum", "members", "enumObject", "createEnumValueResolver", "enumType", "lookupEnum", "propName", "transformed", "l10n", "localizeEnum", "createEnumLocalizer", "createLocalizationStream", "createEnumOptions", "fields", "mapTarget", "languageChange$", "localizationResource", "shortType", "getShortEnumType", "localizeWithFallbackSync", "split", "createDisplayNameLocalizationPipeKeyGenerator", "localization", "generateLocalizationPipeKey", "getValidatorsFromProperty", "property", "attributes", "attr", "typeSimple", "selectObjectExtensions", "configState", "getOne$", "selectLocalization", "selectEnums", "enums", "reduce", "acc", "getObjectExtensionEntitiesFromStore", "module<PERSON>ey", "modules", "entities", "isUndefined", "Boolean", "mapEntitiesToContributors", "generateDisplayName", "createForm", "editForm", "entity", "mapPropertiesToContributors", "createPropertiesToContributorsMapper", "generateTypeaheadDisplayName", "ui", "getTypeFromProperty", "generateDN", "onTable", "isSortable", "entityProp", "isOnCreateForm", "onCreateForm", "isOnEditForm", "onEditForm", "formProp", "formContributor", "obj", "importWithExport", "ExtensibleModule", "ExtensibleModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports", "ObjectExtensions"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@abp/ng.components/fesm2022/abp-ng.components-extensible.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Optional, SkipSelf, Component, ChangeDetectionStrategy, Input, ViewChild, InjectionToken, inject, Injectable, Pipe, ChangeDetectorRef, Injector, Directive, ViewChildren, EventEmitter, LOCALE_ID, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { ReactiveFormsModule, ControlContainer, Validators, FormGroupDirective, FormsModule, UntypedFormGroup, UntypedFormControl } from '@angular/forms';\nimport * as i1 from '@ng-bootstrap/ng-bootstrap';\nimport { NgbInputDatepicker, NgbTimepicker, NgbDatepickerModule, NgbTimepickerModule, NgbDateAdapter, NgbTimeAdapter, NgbTooltip, NgbTypeaheadModule, NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport { LinkedList } from '@abp/utils';\nimport { DateTimeAdapter, DisabledDirective, DateAdapter, TimeAdapter, EllipsisDirective, AbpVisibleDirective, NgxDatatableDefaultDirective, NgxDatatableListDirective, ThemeSharedModule } from '@abp/ng.theme.shared';\nimport * as i5 from '@angular/common';\nimport { CommonModule, NgClass, NgTemplateOutlet, formatDate, AsyncPipe, NgComponentOutlet } from '@angular/common';\nimport * as i2$1 from '@abp/ng.core';\nimport { RestService, ConfigStateService, AbpValidators, TrackByService, ShowPasswordDirective, PermissionDirective, LocalizationModule, escapeHtmlChars, PermissionService, getShortDateShortTimeFormat, getShortTimeFormat, getShortDateFormat, LocalizationService, createLocalizationPipeKeyGenerator, CoreModule } from '@abp/ng.core';\nimport { of, merge, pipe, zip } from 'rxjs';\nimport { map, debounceTime, distinctUntilChanged, switchMap, filter, take } from 'rxjs/operators';\nimport * as i3 from '@ngx-validate/core';\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\nimport * as i1$1 from '@swimlane/ngx-datatable';\nimport { NgxDatatableModule } from '@swimlane/ngx-datatable';\n\nclass PropList extends LinkedList {\n}\nclass PropData {\n    get data() {\n        return {\n            getInjected: this.getInjected,\n            index: this.index,\n            record: this.record,\n        };\n    }\n}\nclass Prop {\n    constructor(type, name, displayName, permission, visible = _ => true, isExtra = false, template, className, formText, tooltip, displayTextResolver) {\n        this.type = type;\n        this.name = name;\n        this.displayName = displayName;\n        this.permission = permission;\n        this.visible = visible;\n        this.isExtra = isExtra;\n        this.template = template;\n        this.className = className;\n        this.formText = formText;\n        this.tooltip = tooltip;\n        this.displayTextResolver = displayTextResolver;\n        this.displayName = this.displayName || this.name;\n    }\n}\nclass PropsFactory {\n    constructor() {\n        this.contributorCallbacks = {};\n    }\n    get(name) {\n        this.contributorCallbacks[name] = this.contributorCallbacks[name] || [];\n        return new this._ctor(this.contributorCallbacks[name]);\n    }\n}\nclass Props {\n    get props() {\n        const propList = new this._ctor();\n        this.callbackList.forEach(callback => callback(propList));\n        return propList;\n    }\n    constructor(callbackList) {\n        this.callbackList = callbackList;\n    }\n    addContributor(contributeCallback) {\n        this.callbackList.push(contributeCallback);\n    }\n    clearContributors() {\n        while (this.callbackList.length)\n            this.callbackList.pop();\n    }\n}\n\nclass FormPropList extends PropList {\n}\nclass FormProps extends Props {\n    constructor() {\n        super(...arguments);\n        this._ctor = FormPropList;\n    }\n}\nclass GroupedFormPropList {\n    constructor() {\n        this.items = [];\n        this.count = 1;\n    }\n    addItem(item) {\n        const groupName = item.group?.name;\n        let group = this.items.find(i => i.group?.name === groupName);\n        if (group) {\n            group.formPropList.addTail(item);\n        }\n        else {\n            group = {\n                formPropList: new FormPropList(),\n                group: item.group || { name: `default${this.count++}`, className: item.group?.className },\n            };\n            group.formPropList.addHead(item);\n            this.items.push(group);\n        }\n    }\n}\nclass CreateFormPropsFactory extends PropsFactory {\n    constructor() {\n        super(...arguments);\n        this._ctor = FormProps;\n    }\n}\nclass EditFormPropsFactory extends PropsFactory {\n    constructor() {\n        super(...arguments);\n        this._ctor = FormProps;\n    }\n}\nclass FormProp extends Prop {\n    constructor(options) {\n        super(options.type, options.name, options.displayName || '', options.permission || '', options.visible, options.isExtra, options.template, options.className, options.formText, options.tooltip);\n        this.group = options.group;\n        this.className = options.className;\n        this.formText = options.formText;\n        this.tooltip = options.tooltip;\n        this.asyncValidators = options.asyncValidators || (_ => []);\n        this.validators = options.validators || (_ => []);\n        this.disabled = options.disabled || (_ => false);\n        this.readonly = options.readonly || (_ => false);\n        this.autocomplete = options.autocomplete || 'off';\n        this.options = options.options;\n        this.id = options.id || options.name;\n        const defaultValue = options.defaultValue;\n        this.defaultValue = isFalsyValue(defaultValue) ? defaultValue : defaultValue || '';\n        this.displayTextResolver = options.displayTextResolver;\n    }\n    static create(options) {\n        return new FormProp(options);\n    }\n    static createMany(arrayOfOptions) {\n        return arrayOfOptions.map(FormProp.create);\n    }\n}\nclass FormPropData extends PropData {\n    constructor(injector, record) {\n        super();\n        this.record = record;\n        this.getInjected = injector.get.bind(injector);\n    }\n}\nfunction isFalsyValue(defaultValue) {\n    return [0, '', false].indexOf(defaultValue) > -1;\n}\n\nfunction selfFactory(dependency) {\n    return dependency;\n}\n\nclass ExtensibleDateTimePickerComponent {\n    constructor(cdRef) {\n        this.cdRef = cdRef;\n        this.meridian = false;\n    }\n    setDate(dateStr) {\n        this.date.writeValue(dateStr);\n    }\n    setTime(dateStr) {\n        this.time.writeValue(dateStr);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleDateTimePickerComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: ExtensibleDateTimePickerComponent, isStandalone: true, selector: \"abp-extensible-date-time-picker\", inputs: { prop: \"prop\", meridian: \"meridian\" }, viewQueries: [{ propertyName: \"date\", first: true, predicate: NgbInputDatepicker, descendants: true }, { propertyName: \"time\", first: true, predicate: NgbTimepicker, descendants: true }], exportAs: [\"abpExtensibleDateTimePicker\"], ngImport: i0, template: `\r\n    <input\r\n      [id]=\"prop.id\"\r\n      [formControlName]=\"prop.name\"\r\n      (ngModelChange)=\"setTime($event)\"\r\n      (click)=\"datepicker.open()\"\r\n      (keyup.space)=\"datepicker.open()\"\r\n      ngbDatepicker\r\n      #datepicker=\"ngbDatepicker\"\r\n      type=\"text\"\r\n      class=\"form-control\"\r\n    />\r\n    <ngb-timepicker\r\n      #timepicker\r\n      [formControlName]=\"prop.name\"\r\n      (ngModelChange)=\"setDate($event)\"\r\n      [meridian]=\"meridian\"\r\n    ></ngb-timepicker>\r\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"ngmodule\", type: NgbDatepickerModule }, { kind: \"directive\", type: i1.NgbInputDatepicker, selector: \"input[ngbDatepicker]\", inputs: [\"autoClose\", \"contentTemplate\", \"datepickerClass\", \"dayTemplate\", \"dayTemplateData\", \"displayMonths\", \"firstDayOfWeek\", \"footerTemplate\", \"markDisabled\", \"minDate\", \"maxDate\", \"navigation\", \"outsideDays\", \"placement\", \"popperOptions\", \"restoreFocus\", \"showWeekNumbers\", \"startDate\", \"container\", \"positionTarget\", \"weekdays\", \"disabled\"], outputs: [\"dateSelect\", \"navigate\", \"closed\"], exportAs: [\"ngbDatepicker\"] }, { kind: \"ngmodule\", type: ReactiveFormsModule }, { kind: \"directive\", type: i2.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i2.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i2.FormControlName, selector: \"[formControlName]\", inputs: [\"formControlName\", \"disabled\", \"ngModel\"], outputs: [\"ngModelChange\"] }, { kind: \"ngmodule\", type: NgbTimepickerModule }, { kind: \"component\", type: i1.NgbTimepicker, selector: \"ngb-timepicker\", inputs: [\"meridian\", \"spinners\", \"seconds\", \"hourStep\", \"minuteStep\", \"secondStep\", \"readonlyInputs\", \"size\"], exportAs: [\"ngbTimepicker\"] }], viewProviders: [\n            {\n                provide: ControlContainer,\n                useFactory: selfFactory,\n                deps: [[new Optional(), new SkipSelf(), ControlContainer]],\n            },\n            {\n                provide: NgbDateAdapter,\n                useClass: DateTimeAdapter,\n            },\n            {\n                provide: NgbTimeAdapter,\n                useClass: DateTimeAdapter,\n            },\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleDateTimePickerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    exportAs: 'abpExtensibleDateTimePicker',\n                    standalone: true,\n                    imports: [CommonModule, NgbDatepickerModule, ReactiveFormsModule, NgbTimepickerModule],\n                    selector: 'abp-extensible-date-time-picker',\n                    template: `\r\n    <input\r\n      [id]=\"prop.id\"\r\n      [formControlName]=\"prop.name\"\r\n      (ngModelChange)=\"setTime($event)\"\r\n      (click)=\"datepicker.open()\"\r\n      (keyup.space)=\"datepicker.open()\"\r\n      ngbDatepicker\r\n      #datepicker=\"ngbDatepicker\"\r\n      type=\"text\"\r\n      class=\"form-control\"\r\n    />\r\n    <ngb-timepicker\r\n      #timepicker\r\n      [formControlName]=\"prop.name\"\r\n      (ngModelChange)=\"setDate($event)\"\r\n      [meridian]=\"meridian\"\r\n    ></ngb-timepicker>\r\n  `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    viewProviders: [\n                        {\n                            provide: ControlContainer,\n                            useFactory: selfFactory,\n                            deps: [[new Optional(), new SkipSelf(), ControlContainer]],\n                        },\n                        {\n                            provide: NgbDateAdapter,\n                            useClass: DateTimeAdapter,\n                        },\n                        {\n                            provide: NgbTimeAdapter,\n                            useClass: DateTimeAdapter,\n                        },\n                    ],\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { prop: [{\n                type: Input\n            }], meridian: [{\n                type: Input\n            }], date: [{\n                type: ViewChild,\n                args: [NgbInputDatepicker]\n            }], time: [{\n                type: ViewChild,\n                args: [NgbTimepicker]\n            }] } });\n\nconst EXTENSIONS_IDENTIFIER = new InjectionToken('EXTENSIONS_IDENTIFIER');\nconst EXTENSIONS_ACTION_TYPE = new InjectionToken('EXTENSIONS_ACTION_TYPE');\nconst EXTENSIONS_ACTION_DATA = new InjectionToken('EXTENSIONS_ACTION_DATA');\nconst EXTENSIONS_ACTION_CALLBACK = new InjectionToken('EXTENSIONS_ACTION_DATA');\nconst PROP_DATA_STREAM = new InjectionToken('PROP_DATA_STREAM');\nconst ENTITY_PROP_TYPE_CLASSES = new InjectionToken('ENTITY_PROP_TYPE_CLASSES', {\n    factory: () => ({}),\n});\nconst EXTENSIONS_FORM_PROP = new InjectionToken('EXTENSIONS_FORM_PROP');\nconst EXTENSIONS_FORM_PROP_DATA = new InjectionToken('EXTENSIONS_FORM_PROP_DATA');\n\nconst EXTRA_PROPERTIES_KEY = 'extraProperties';\n\nconst TYPEAHEAD_TEXT_SUFFIX = '_Text';\nconst TYPEAHEAD_TEXT_SUFFIX_REGEX = /_Text$/;\nfunction createTypeaheadOptions(lookup) {\n    return (data, searchText) => searchText && data\n        ? data\n            .getInjected(RestService)\n            .request({\n            method: 'GET',\n            url: lookup.url || '',\n            params: {\n                [lookup.filterParamName || '']: searchText,\n            },\n        }, { apiName: 'Default' })\n            .pipe(map((response) => {\n            const list = response[lookup.resultListPropertyName || ''];\n            const mapToOption = (item) => ({\n                key: item[lookup.displayPropertyName || ''],\n                value: item[lookup.valuePropertyName || ''],\n            });\n            return list.map(mapToOption);\n        }))\n        : of([]);\n}\nfunction getTypeaheadType(lookup, name) {\n    if (!!lookup.url) {\n        return \"typeahead\" /* ePropType.Typeahead */;\n    }\n    else {\n        return name.endsWith(TYPEAHEAD_TEXT_SUFFIX) ? \"hidden\" /* ePropType.Hidden */ : undefined;\n    }\n}\nfunction createTypeaheadDisplayNameGenerator(displayNameGeneratorFn, properties) {\n    return (displayName, fallback) => {\n        const name = removeTypeaheadTextSuffix(fallback.name || '');\n        return displayNameGeneratorFn(displayName || properties[name].displayName, {\n            name,\n            resource: fallback.resource,\n        });\n    };\n}\nfunction addTypeaheadTextSuffix(name) {\n    return name + TYPEAHEAD_TEXT_SUFFIX;\n}\nfunction hasTypeaheadTextSuffix(name) {\n    return TYPEAHEAD_TEXT_SUFFIX_REGEX.test(name);\n}\nfunction removeTypeaheadTextSuffix(name) {\n    return name.replace(TYPEAHEAD_TEXT_SUFFIX_REGEX, '');\n}\n\nclass ExtensibleFormPropService {\n    constructor() {\n        this.#configStateService = inject(ConfigStateService);\n        this.meridian$ = this.#configStateService\n            .getDeep$('localization.currentCulture.dateTimeFormat.shortTimePattern')\n            .pipe(map((shortTimePattern) => (shortTimePattern || '').includes('tt')));\n    }\n    #configStateService;\n    isRequired(validator) {\n        return (validator === Validators.required ||\n            validator === AbpValidators.required ||\n            validator.name === 'required');\n    }\n    getComponent(prop) {\n        if (prop.template) {\n            return 'template';\n        }\n        switch (prop.type) {\n            case \"boolean\" /* ePropType.Boolean */:\n                return 'checkbox';\n            case \"date\" /* ePropType.Date */:\n                return 'date';\n            case \"datetime\" /* ePropType.DateTime */:\n                return 'dateTime';\n            case \"hidden\" /* ePropType.Hidden */:\n                return 'hidden';\n            case \"multiselect\" /* ePropType.MultiSelect */:\n                return 'multiselect';\n            case \"text\" /* ePropType.Text */:\n                return 'textarea';\n            case \"time\" /* ePropType.Time */:\n                return 'time';\n            case \"typeahead\" /* ePropType.Typeahead */:\n                return 'typeahead';\n            case \"passwordinputgroup\" /* ePropType.PasswordInputGroup */:\n                return 'passwordinputgroup';\n            default:\n                return prop.options ? 'select' : 'input';\n        }\n    }\n    getType(prop) {\n        switch (prop.type) {\n            case \"date\" /* ePropType.Date */:\n            case \"string\" /* ePropType.String */:\n                return 'text';\n            case \"boolean\" /* ePropType.Boolean */:\n                return 'checkbox';\n            case \"number\" /* ePropType.Number */:\n                return 'number';\n            case \"email\" /* ePropType.Email */:\n                return 'email';\n            case \"password\" /* ePropType.Password */:\n                return 'password';\n            case \"passwordinputgroup\" /* ePropType.PasswordInputGroup */:\n                return 'passwordinputgroup';\n            default:\n                return 'hidden';\n        }\n    }\n    calcAsterisks(validators) {\n        if (!validators)\n            return '';\n        const required = validators.find(v => this.isRequired(v));\n        return required ? '*' : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormPropService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormPropService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormPropService, decorators: [{\n            type: Injectable\n        }] });\n\nclass CreateInjectorPipe {\n    transform(_, action, context) {\n        const get = (token, notFoundValue, options) => {\n            const componentData = context.getData();\n            const componentDataCallback = (data) => {\n                data = data ?? context.getData();\n                return action.action(data);\n            };\n            let extensionData;\n            switch (token) {\n                case EXTENSIONS_ACTION_DATA:\n                    extensionData = componentData;\n                    break;\n                case EXTENSIONS_ACTION_CALLBACK:\n                    extensionData = componentDataCallback;\n                    break;\n                default:\n                    extensionData = context.getInjected.call(context.injector, token, notFoundValue, options);\n            }\n            return extensionData;\n        };\n        return { get };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: CreateInjectorPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: CreateInjectorPipe, isStandalone: true, name: \"createInjector\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: CreateInjectorPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'createInjector',\n                    standalone: true,\n                }]\n        }] });\n\nclass ExtensibleFormPropComponent {\n    constructor() {\n        this.service = inject(ExtensibleFormPropService);\n        this.cdRef = inject(ChangeDetectorRef);\n        this.track = inject(TrackByService);\n        this.#groupDirective = inject(FormGroupDirective);\n        this.injector = inject(Injector);\n        this.form = this.#groupDirective.form;\n        this.asterisk = '';\n        this.containerClassName = 'mb-2';\n        this.showPassword = false;\n        this.options$ = of([]);\n        this.validators = [];\n        this.passwordKey = \"ThemeShared.Extensions.PasswordComponent\" /* eExtensibleComponents.PasswordComponent */;\n        this.disabledFn = (data) => false;\n        this.search = (text$) => text$\n            ? text$.pipe(debounceTime(300), distinctUntilChanged(), switchMap(text => this.prop?.options?.(this.data, text) || of([])))\n            : of([]);\n        this.typeaheadFormatter = (option) => option.key;\n        this.meridian$ = this.service.meridian$;\n    }\n    #groupDirective;\n    get disabled() {\n        return this.disabledFn(this.data);\n    }\n    setTypeaheadValue(selectedOption) {\n        this.typeaheadModel = selectedOption || { key: null, value: null };\n        const { key, value } = this.typeaheadModel;\n        const [keyControl, valueControl] = this.getTypeaheadControls();\n        if (valueControl?.value && !value)\n            valueControl.markAsDirty();\n        keyControl?.setValue(key);\n        valueControl?.setValue(value);\n    }\n    get isInvalid() {\n        const control = this.form.get(this.prop.name);\n        return control?.touched && control.invalid;\n    }\n    getTypeaheadControls() {\n        const { name } = this.prop;\n        const extraPropName = `${EXTRA_PROPERTIES_KEY}.${name}`;\n        const keyControl = this.form.get(addTypeaheadTextSuffix(extraPropName)) ||\n            this.form.get(addTypeaheadTextSuffix(name));\n        const valueControl = this.form.get(extraPropName) || this.form.get(name);\n        return [keyControl, valueControl];\n    }\n    setAsterisk() {\n        this.asterisk = this.service.calcAsterisks(this.validators);\n    }\n    ngAfterViewInit() {\n        if (this.isFirstGroup && this.first && this.fieldRef) {\n            this.fieldRef.nativeElement.focus();\n        }\n    }\n    getComponent(prop) {\n        return this.service.getComponent(prop);\n    }\n    getType(prop) {\n        return this.service.getType(prop);\n    }\n    ngOnChanges({ prop, data }) {\n        const currentProp = prop?.currentValue;\n        const { options, readonly, disabled, validators, className, template } = currentProp || {};\n        if (template) {\n            this.injectorForCustomComponent = Injector.create({\n                providers: [\n                    {\n                        provide: EXTENSIONS_FORM_PROP,\n                        useValue: currentProp,\n                    },\n                    {\n                        provide: EXTENSIONS_FORM_PROP_DATA,\n                        useValue: data?.currentValue?.record,\n                    },\n                    { provide: ControlContainer, useExisting: FormGroupDirective },\n                ],\n                parent: this.injector,\n            });\n        }\n        if (options)\n            this.options$ = options(this.data);\n        if (readonly)\n            this.readonly = readonly(this.data);\n        if (disabled) {\n            this.disabledFn = disabled;\n        }\n        if (validators) {\n            this.validators = validators(this.data);\n            this.setAsterisk();\n        }\n        if (className !== undefined) {\n            this.containerClassName = className;\n        }\n        const [keyControl, valueControl] = this.getTypeaheadControls();\n        if (keyControl && valueControl)\n            this.typeaheadModel = { key: keyControl.value, value: valueControl.value };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormPropComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: ExtensibleFormPropComponent, isStandalone: true, selector: \"abp-extensible-form-prop\", inputs: { data: \"data\", prop: \"prop\", first: \"first\", isFirstGroup: \"isFirstGroup\" }, providers: [ExtensibleFormPropService], viewQueries: [{ propertyName: \"fieldRef\", first: true, predicate: [\"field\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<ng-container\\r\\n  [ngSwitch]=\\\"getComponent(prop)\\\"\\r\\n  *abpPermission=\\\"prop.permission; runChangeDetection: false\\\"\\r\\n>\\r\\n  <ng-template ngSwitchCase=\\\"template\\\">\\r\\n    <ng-container *ngComponentOutlet=\\\"prop.template; injector: injectorForCustomComponent\\\">\\r\\n    </ng-container>\\r\\n  </ng-template>\\r\\n\\r\\n  <div [ngClass]=\\\"containerClassName\\\" class=\\\"mb-2\\\">\\r\\n    <ng-template ngSwitchCase=\\\"input\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <input\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [autocomplete]=\\\"prop.autocomplete\\\"\\r\\n        [type]=\\\"getType(prop)\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        [readonly]=\\\"readonly\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"hidden\\\">\\r\\n      <input [formControlName]=\\\"prop.name\\\" type=\\\"hidden\\\" />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"checkbox\\\">\\r\\n      <div class=\\\"form-check\\\" validationTarget>\\r\\n        <input\\r\\n          #field\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [formControlName]=\\\"prop.name\\\"\\r\\n          [abpDisabled]=\\\"disabled\\\"\\r\\n          type=\\\"checkbox\\\"\\r\\n          class=\\\"form-check-input\\\"\\r\\n        />\\r\\n        <ng-template\\r\\n          [ngTemplateOutlet]=\\\"label\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ $implicit: 'form-check-label' }\\\"\\r\\n        ></ng-template>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"select\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <select\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        class=\\\"form-select form-control\\\"\\r\\n      >\\r\\n        @for (option of options$ | async; track option.value) {\\r\\n          <option [ngValue]=\\\"option.value\\\">\\r\\n            {{ option.key }}\\r\\n          </option>\\r\\n        }\\r\\n      </select>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"multiselect\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <select\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        multiple=\\\"multiple\\\"\\r\\n        class=\\\"form-select form-control\\\"\\r\\n      >\\r\\n        <option\\r\\n          *ngFor=\\\"let option of options$ | async; trackBy: track.by('value')\\\"\\r\\n          [ngValue]=\\\"option.value\\\"\\r\\n        >\\r\\n          {{ option.key }}\\r\\n        </option>\\r\\n      </select>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"typeahead\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <div #typeahead class=\\\"position-relative\\\" validationStyle validationTarget>\\r\\n        <input\\r\\n          #field\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [autocomplete]=\\\"prop.autocomplete\\\"\\r\\n          [abpDisabled]=\\\"disabled\\\"\\r\\n          [ngbTypeahead]=\\\"search\\\"\\r\\n          [editable]=\\\"false\\\"\\r\\n          [inputFormatter]=\\\"typeaheadFormatter\\\"\\r\\n          [resultFormatter]=\\\"typeaheadFormatter\\\"\\r\\n          [ngModelOptions]=\\\"{ standalone: true }\\\"\\r\\n          [(ngModel)]=\\\"typeaheadModel\\\"\\r\\n          (selectItem)=\\\"setTypeaheadValue($event.item)\\\"\\r\\n          (blur)=\\\"setTypeaheadValue(typeaheadModel)\\\"\\r\\n          [class.is-invalid]=\\\"typeahead.classList.contains('is-invalid')\\\"\\r\\n          class=\\\"form-control\\\"\\r\\n        />\\r\\n        <input [formControlName]=\\\"prop.name\\\" type=\\\"hidden\\\" />\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"date\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <input\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        (click)=\\\"datepicker.open()\\\"\\r\\n        (keyup.space)=\\\"datepicker.open()\\\"\\r\\n        ngbDatepicker\\r\\n        #datepicker=\\\"ngbDatepicker\\\"\\r\\n        type=\\\"text\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"time\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <ngb-timepicker [formControlName]=\\\"prop.name\\\"></ngb-timepicker>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"dateTime\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <abp-extensible-date-time-picker [prop]=\\\"prop\\\" [meridian]=\\\"meridian$ | async\\\" />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"textarea\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <textarea\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        [readonly]=\\\"readonly\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      ></textarea>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"passwordinputgroup\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <div class=\\\"input-group form-group\\\" validationTarget>\\r\\n        <input\\r\\n          class=\\\"form-control\\\"\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [formControlName]=\\\"prop.name\\\"\\r\\n          [abpShowPassword]=\\\"showPassword\\\"\\r\\n        />\\r\\n        <button class=\\\"btn btn-secondary\\\" type=\\\"button\\\" (click)=\\\"showPassword = !showPassword\\\">\\r\\n          <i\\r\\n            class=\\\"fa\\\"\\r\\n            aria-hidden=\\\"true\\\"\\r\\n            [ngClass]=\\\"{\\r\\n              'fa-eye-slash': !showPassword,\\r\\n              'fa-eye': showPassword,\\r\\n            }\\\"\\r\\n          ></i>\\r\\n        </button>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    @if (prop.formText) {\\r\\n      <small class=\\\"text-muted d-block\\\">{{ prop.formText | abpLocalization }}</small>\\r\\n    }\\r\\n  </div>\\r\\n</ng-container>\\r\\n\\r\\n<ng-template #label let-classes>\\r\\n  <label [htmlFor]=\\\"prop.id\\\" [ngClass]=\\\"classes || 'form-label'\\\">\\r\\n    @if (prop.displayTextResolver) {\\r\\n      {{ prop.displayTextResolver(data) | abpLocalization }}\\r\\n    } @else {\\r\\n      @if (prop.isExtra) {\\r\\n        {{ '::' + prop.displayName | abpLocalization }}\\r\\n      } @else {\\r\\n        {{ prop.displayName | abpLocalization }}\\r\\n      }\\r\\n    }\\r\\n    {{ asterisk }}\\r\\n    @if (prop.tooltip) {\\r\\n      <i\\r\\n        [ngbTooltip]=\\\"prop.tooltip.text | abpLocalization\\\"\\r\\n        [placement]=\\\"prop.tooltip.placement || 'auto'\\\"\\r\\n        container=\\\"body\\\"\\r\\n        class=\\\"bi bi-info-circle\\\"\\r\\n      ></i>\\r\\n    }\\r\\n  </label>\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"component\", type: ExtensibleDateTimePickerComponent, selector: \"abp-extensible-date-time-picker\", inputs: [\"prop\", \"meridian\"], exportAs: [\"abpExtensibleDateTimePicker\"] }, { kind: \"ngmodule\", type: NgbDatepickerModule }, { kind: \"directive\", type: i1.NgbInputDatepicker, selector: \"input[ngbDatepicker]\", inputs: [\"autoClose\", \"contentTemplate\", \"datepickerClass\", \"dayTemplate\", \"dayTemplateData\", \"displayMonths\", \"firstDayOfWeek\", \"footerTemplate\", \"markDisabled\", \"minDate\", \"maxDate\", \"navigation\", \"outsideDays\", \"placement\", \"popperOptions\", \"restoreFocus\", \"showWeekNumbers\", \"startDate\", \"container\", \"positionTarget\", \"weekdays\", \"disabled\"], outputs: [\"dateSelect\", \"navigate\", \"closed\"], exportAs: [\"ngbDatepicker\"] }, { kind: \"ngmodule\", type: NgbTimepickerModule }, { kind: \"component\", type: i1.NgbTimepicker, selector: \"ngb-timepicker\", inputs: [\"meridian\", \"spinners\", \"seconds\", \"hourStep\", \"minuteStep\", \"secondStep\", \"readonlyInputs\", \"size\"], exportAs: [\"ngbTimepicker\"] }, { kind: \"ngmodule\", type: ReactiveFormsModule }, { kind: \"directive\", type: i2.NgSelectOption, selector: \"option\", inputs: [\"ngValue\", \"value\"] }, { kind: \"directive\", type: i2.ɵNgSelectMultipleOption, selector: \"option\", inputs: [\"ngValue\", \"value\"] }, { kind: \"directive\", type: i2.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i2.CheckboxControlValueAccessor, selector: \"input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]\" }, { kind: \"directive\", type: i2.SelectControlValueAccessor, selector: \"select:not([multiple])[formControlName],select:not([multiple])[formControl],select:not([multiple])[ngModel]\", inputs: [\"compareWith\"] }, { kind: \"directive\", type: i2.SelectMultipleControlValueAccessor, selector: \"select[multiple][formControlName],select[multiple][formControl],select[multiple][ngModel]\", inputs: [\"compareWith\"] }, { kind: \"directive\", type: i2.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i2.FormControlName, selector: \"[formControlName]\", inputs: [\"formControlName\", \"disabled\", \"ngModel\"], outputs: [\"ngModelChange\"] }, { kind: \"directive\", type: DisabledDirective, selector: \"[abpDisabled]\", inputs: [\"abpDisabled\"] }, { kind: \"ngmodule\", type: NgxValidateCoreModule }, { kind: \"directive\", type: i3.ValidationStyleDirective, selector: \"[validationStyle]\", exportAs: [\"validationStyle\"] }, { kind: \"directive\", type: i3.ValidationTargetDirective, selector: \"[validationTarget]\", exportAs: [\"validationTarget\"] }, { kind: \"directive\", type: i3.ValidationDirective, selector: \"[formControl],[formControlName]\", exportAs: [\"validationDirective\"] }, { kind: \"directive\", type: NgbTooltip, selector: \"[ngbTooltip]\", inputs: [\"animation\", \"autoClose\", \"placement\", \"popperOptions\", \"triggers\", \"positionTarget\", \"container\", \"disableTooltip\", \"tooltipClass\", \"tooltipContext\", \"openDelay\", \"closeDelay\", \"ngbTooltip\"], outputs: [\"shown\", \"hidden\"], exportAs: [\"ngbTooltip\"] }, { kind: \"ngmodule\", type: NgbTypeaheadModule }, { kind: \"directive\", type: i1.NgbTypeahead, selector: \"input[ngbTypeahead]\", inputs: [\"autocomplete\", \"container\", \"editable\", \"focusFirst\", \"inputFormatter\", \"ngbTypeahead\", \"resultFormatter\", \"resultTemplate\", \"selectOnExact\", \"showHint\", \"placement\", \"popperOptions\", \"popupClass\"], outputs: [\"selectItem\"], exportAs: [\"ngbTypeahead\"] }, { kind: \"directive\", type: ShowPasswordDirective, selector: \"[abpShowPassword]\", inputs: [\"abpShowPassword\"] }, { kind: \"directive\", type: PermissionDirective, selector: \"[abpPermission]\", inputs: [\"abpPermission\", \"abpPermissionRunChangeDetection\"] }, { kind: \"ngmodule\", type: LocalizationModule }, { kind: \"pipe\", type: i2$1.LocalizationPipe, name: \"abpLocalization\" }, { kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i5.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i5.NgComponentOutlet, selector: \"[ngComponentOutlet]\", inputs: [\"ngComponentOutlet\", \"ngComponentOutletInputs\", \"ngComponentOutletInjector\", \"ngComponentOutletContent\", \"ngComponentOutletNgModule\", \"ngComponentOutletNgModuleFactory\"] }, { kind: \"directive\", type: i5.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i5.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i5.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i5.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"pipe\", type: i5.AsyncPipe, name: \"async\" }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i2.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }], viewProviders: [\n            {\n                provide: ControlContainer,\n                useFactory: selfFactory,\n                deps: [[new Optional(), new SkipSelf(), ControlContainer]],\n            },\n            { provide: NgbDateAdapter, useClass: DateAdapter },\n            { provide: NgbTimeAdapter, useClass: TimeAdapter },\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormPropComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'abp-extensible-form-prop', standalone: true, imports: [\n                        ExtensibleDateTimePickerComponent,\n                        NgbDatepickerModule,\n                        NgbTimepickerModule,\n                        ReactiveFormsModule,\n                        DisabledDirective,\n                        NgxValidateCoreModule,\n                        NgbTooltip,\n                        NgbTypeaheadModule,\n                        CreateInjectorPipe,\n                        ShowPasswordDirective,\n                        PermissionDirective,\n                        LocalizationModule,\n                        CommonModule,\n                        FormsModule,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [ExtensibleFormPropService], viewProviders: [\n                        {\n                            provide: ControlContainer,\n                            useFactory: selfFactory,\n                            deps: [[new Optional(), new SkipSelf(), ControlContainer]],\n                        },\n                        { provide: NgbDateAdapter, useClass: DateAdapter },\n                        { provide: NgbTimeAdapter, useClass: TimeAdapter },\n                    ], template: \"<ng-container\\r\\n  [ngSwitch]=\\\"getComponent(prop)\\\"\\r\\n  *abpPermission=\\\"prop.permission; runChangeDetection: false\\\"\\r\\n>\\r\\n  <ng-template ngSwitchCase=\\\"template\\\">\\r\\n    <ng-container *ngComponentOutlet=\\\"prop.template; injector: injectorForCustomComponent\\\">\\r\\n    </ng-container>\\r\\n  </ng-template>\\r\\n\\r\\n  <div [ngClass]=\\\"containerClassName\\\" class=\\\"mb-2\\\">\\r\\n    <ng-template ngSwitchCase=\\\"input\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <input\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [autocomplete]=\\\"prop.autocomplete\\\"\\r\\n        [type]=\\\"getType(prop)\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        [readonly]=\\\"readonly\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"hidden\\\">\\r\\n      <input [formControlName]=\\\"prop.name\\\" type=\\\"hidden\\\" />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"checkbox\\\">\\r\\n      <div class=\\\"form-check\\\" validationTarget>\\r\\n        <input\\r\\n          #field\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [formControlName]=\\\"prop.name\\\"\\r\\n          [abpDisabled]=\\\"disabled\\\"\\r\\n          type=\\\"checkbox\\\"\\r\\n          class=\\\"form-check-input\\\"\\r\\n        />\\r\\n        <ng-template\\r\\n          [ngTemplateOutlet]=\\\"label\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ $implicit: 'form-check-label' }\\\"\\r\\n        ></ng-template>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"select\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <select\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        class=\\\"form-select form-control\\\"\\r\\n      >\\r\\n        @for (option of options$ | async; track option.value) {\\r\\n          <option [ngValue]=\\\"option.value\\\">\\r\\n            {{ option.key }}\\r\\n          </option>\\r\\n        }\\r\\n      </select>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"multiselect\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <select\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        multiple=\\\"multiple\\\"\\r\\n        class=\\\"form-select form-control\\\"\\r\\n      >\\r\\n        <option\\r\\n          *ngFor=\\\"let option of options$ | async; trackBy: track.by('value')\\\"\\r\\n          [ngValue]=\\\"option.value\\\"\\r\\n        >\\r\\n          {{ option.key }}\\r\\n        </option>\\r\\n      </select>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"typeahead\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <div #typeahead class=\\\"position-relative\\\" validationStyle validationTarget>\\r\\n        <input\\r\\n          #field\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [autocomplete]=\\\"prop.autocomplete\\\"\\r\\n          [abpDisabled]=\\\"disabled\\\"\\r\\n          [ngbTypeahead]=\\\"search\\\"\\r\\n          [editable]=\\\"false\\\"\\r\\n          [inputFormatter]=\\\"typeaheadFormatter\\\"\\r\\n          [resultFormatter]=\\\"typeaheadFormatter\\\"\\r\\n          [ngModelOptions]=\\\"{ standalone: true }\\\"\\r\\n          [(ngModel)]=\\\"typeaheadModel\\\"\\r\\n          (selectItem)=\\\"setTypeaheadValue($event.item)\\\"\\r\\n          (blur)=\\\"setTypeaheadValue(typeaheadModel)\\\"\\r\\n          [class.is-invalid]=\\\"typeahead.classList.contains('is-invalid')\\\"\\r\\n          class=\\\"form-control\\\"\\r\\n        />\\r\\n        <input [formControlName]=\\\"prop.name\\\" type=\\\"hidden\\\" />\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"date\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <input\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        (click)=\\\"datepicker.open()\\\"\\r\\n        (keyup.space)=\\\"datepicker.open()\\\"\\r\\n        ngbDatepicker\\r\\n        #datepicker=\\\"ngbDatepicker\\\"\\r\\n        type=\\\"text\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"time\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <ngb-timepicker [formControlName]=\\\"prop.name\\\"></ngb-timepicker>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"dateTime\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <abp-extensible-date-time-picker [prop]=\\\"prop\\\" [meridian]=\\\"meridian$ | async\\\" />\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"textarea\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <textarea\\r\\n        #field\\r\\n        [id]=\\\"prop.id\\\"\\r\\n        [formControlName]=\\\"prop.name\\\"\\r\\n        [abpDisabled]=\\\"disabled\\\"\\r\\n        [readonly]=\\\"readonly\\\"\\r\\n        class=\\\"form-control\\\"\\r\\n      ></textarea>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template ngSwitchCase=\\\"passwordinputgroup\\\">\\r\\n      <ng-template [ngTemplateOutlet]=\\\"label\\\"></ng-template>\\r\\n      <div class=\\\"input-group form-group\\\" validationTarget>\\r\\n        <input\\r\\n          class=\\\"form-control\\\"\\r\\n          [id]=\\\"prop.id\\\"\\r\\n          [formControlName]=\\\"prop.name\\\"\\r\\n          [abpShowPassword]=\\\"showPassword\\\"\\r\\n        />\\r\\n        <button class=\\\"btn btn-secondary\\\" type=\\\"button\\\" (click)=\\\"showPassword = !showPassword\\\">\\r\\n          <i\\r\\n            class=\\\"fa\\\"\\r\\n            aria-hidden=\\\"true\\\"\\r\\n            [ngClass]=\\\"{\\r\\n              'fa-eye-slash': !showPassword,\\r\\n              'fa-eye': showPassword,\\r\\n            }\\\"\\r\\n          ></i>\\r\\n        </button>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    @if (prop.formText) {\\r\\n      <small class=\\\"text-muted d-block\\\">{{ prop.formText | abpLocalization }}</small>\\r\\n    }\\r\\n  </div>\\r\\n</ng-container>\\r\\n\\r\\n<ng-template #label let-classes>\\r\\n  <label [htmlFor]=\\\"prop.id\\\" [ngClass]=\\\"classes || 'form-label'\\\">\\r\\n    @if (prop.displayTextResolver) {\\r\\n      {{ prop.displayTextResolver(data) | abpLocalization }}\\r\\n    } @else {\\r\\n      @if (prop.isExtra) {\\r\\n        {{ '::' + prop.displayName | abpLocalization }}\\r\\n      } @else {\\r\\n        {{ prop.displayName | abpLocalization }}\\r\\n      }\\r\\n    }\\r\\n    {{ asterisk }}\\r\\n    @if (prop.tooltip) {\\r\\n      <i\\r\\n        [ngbTooltip]=\\\"prop.tooltip.text | abpLocalization\\\"\\r\\n        [placement]=\\\"prop.tooltip.placement || 'auto'\\\"\\r\\n        container=\\\"body\\\"\\r\\n        class=\\\"bi bi-info-circle\\\"\\r\\n      ></i>\\r\\n    }\\r\\n  </label>\\r\\n</ng-template>\\r\\n\" }]\n        }], propDecorators: { data: [{\n                type: Input\n            }], prop: [{\n                type: Input\n            }], first: [{\n                type: Input\n            }], isFirstGroup: [{\n                type: Input\n            }], fieldRef: [{\n                type: ViewChild,\n                args: ['field']\n            }] } });\n\nclass ActionList extends LinkedList {\n}\nclass ActionData {\n    get data() {\n        return {\n            getInjected: this.getInjected,\n            index: this.index,\n            record: this.record,\n        };\n    }\n}\nclass Action {\n    constructor(permission, visible = () => true, action = () => { }, btnClass, btnStyle) {\n        this.permission = permission;\n        this.visible = visible;\n        this.action = action;\n        this.btnClass = btnClass;\n        this.btnStyle = btnStyle;\n    }\n}\nclass ActionsFactory {\n    constructor() {\n        this.contributorCallbacks = {};\n    }\n    get(name) {\n        this.contributorCallbacks[name] = this.contributorCallbacks[name] || [];\n        return new this._ctor(this.contributorCallbacks[name]);\n    }\n}\nclass Actions {\n    get actions() {\n        const actionList = new this._ctor();\n        this.callbackList.forEach(callback => callback(actionList));\n        return actionList;\n    }\n    constructor(callbackList) {\n        this.callbackList = callbackList;\n    }\n    addContributor(contributeCallback) {\n        this.callbackList.push(contributeCallback);\n    }\n    clearContributors() {\n        while (this.callbackList.length)\n            this.callbackList.pop();\n    }\n}\n\nclass EntityActionList extends ActionList {\n}\nclass EntityActions extends Actions {\n    constructor() {\n        super(...arguments);\n        this._ctor = EntityActionList;\n    }\n}\nclass EntityActionsFactory extends ActionsFactory {\n    constructor() {\n        super(...arguments);\n        this._ctor = EntityActions;\n    }\n}\nclass EntityAction extends Action {\n    constructor(options) {\n        super(options.permission || '', options.visible, options.action);\n        this.text = options.text;\n        this.icon = options.icon || '';\n        this.btnClass = options.btnClass || 'btn btn-primary text-center';\n        this.btnStyle = options.btnStyle;\n        this.showOnlyIcon = options.showOnlyIcon || false;\n        this.tooltip = options.tooltip;\n    }\n    static create(options) {\n        return new EntityAction(options);\n    }\n    static createMany(arrayOfOptions) {\n        return arrayOfOptions.map(EntityAction.create);\n    }\n}\n\nclass EntityPropList extends PropList {\n}\nclass EntityProps extends Props {\n    constructor() {\n        super(...arguments);\n        this._ctor = EntityPropList;\n    }\n}\nclass EntityPropsFactory extends PropsFactory {\n    constructor() {\n        super(...arguments);\n        this._ctor = EntityProps;\n    }\n}\nclass EntityProp extends Prop {\n    constructor(options) {\n        super(options.type, options.name, options.displayName || '', options.permission || '', options.visible, options.isExtra);\n        this.columnVisible = options.columnVisible || (() => true);\n        this.columnWidth = options.columnWidth;\n        this.sortable = options.sortable || false;\n        this.valueResolver =\n            options.valueResolver ||\n                (data => of(escapeHtmlChars(data.record[this.name])));\n        if (options.action) {\n            this.action = options.action;\n        }\n        if (options.component) {\n            this.component = options.component;\n        }\n        if (options.enumList) {\n            this.enumList = options.enumList;\n        }\n        this.tooltip = options.tooltip;\n    }\n    static create(options) {\n        return new EntityProp(options);\n    }\n    static createMany(arrayOfOptions) {\n        return arrayOfOptions.map(EntityProp.create);\n    }\n}\n\nclass ToolbarActionList extends ActionList {\n}\nclass ToolbarActions extends Actions {\n    constructor() {\n        super(...arguments);\n        this._ctor = ToolbarActionList;\n    }\n}\nclass ToolbarActionsFactory extends ActionsFactory {\n    constructor() {\n        super(...arguments);\n        this._ctor = ToolbarActions;\n    }\n}\nclass ToolbarAction extends Action {\n    constructor(options) {\n        super(options.permission || '', options.visible, options.action);\n        this.text = options.text;\n        this.icon = options.icon || '';\n        if (options.btnClass) {\n            this.btnClass = options.btnClass;\n        }\n    }\n    static create(options) {\n        return new ToolbarAction(options);\n    }\n    static createMany(arrayOfOptions) {\n        return arrayOfOptions.map(ToolbarAction.create);\n    }\n}\nclass ToolbarComponent extends Action {\n    constructor(options) {\n        super(options.permission || '', options.visible, options.action);\n        this.component = options.component;\n    }\n    static create(options) {\n        return new ToolbarComponent(options);\n    }\n    static createMany(arrayOfOptions) {\n        return arrayOfOptions.map(ToolbarComponent.create);\n    }\n}\n\nclass ExtensionsService {\n    constructor() {\n        this.entityActions = new EntityActionsFactory();\n        this.toolbarActions = new ToolbarActionsFactory();\n        this.entityProps = new EntityPropsFactory();\n        this.createFormProps = new CreateFormPropsFactory();\n        this.editFormProps = new EditFormPropsFactory();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensionsService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensionsService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensionsService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\n/* eslint-disable @angular-eslint/no-input-rename */\nclass PropDataDirective extends PropData {\n    constructor(tempRef, vcRef, injector) {\n        super();\n        this.tempRef = tempRef;\n        this.vcRef = vcRef;\n        this.getInjected = injector.get.bind(injector);\n    }\n    ngOnChanges() {\n        this.vcRef.clear();\n        this.vcRef.createEmbeddedView(this.tempRef, {\n            $implicit: this.data,\n            index: 0,\n        });\n    }\n    ngOnDestroy() {\n        this.vcRef.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PropDataDirective, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: PropDataDirective, isStandalone: true, selector: \"[abpPropData]\", inputs: { propList: [\"abpPropDataFromList\", \"propList\"], record: [\"abpPropDataWithRecord\", \"record\"], index: [\"abpPropDataAtIndex\", \"index\"] }, exportAs: [\"abpPropData\"], usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PropDataDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    exportAs: 'abpPropData',\n                    selector: '[abpPropData]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: i0.Injector }], propDecorators: { propList: [{\n                type: Input,\n                args: ['abpPropDataFromList']\n            }], record: [{\n                type: Input,\n                args: ['abpPropDataWithRecord']\n            }], index: [{\n                type: Input,\n                args: ['abpPropDataAtIndex']\n            }] } });\n\nclass ExtensibleFormComponent {\n    constructor() {\n        this.cdRef = inject(ChangeDetectorRef);\n        this.track = inject(TrackByService);\n        this.container = inject(ControlContainer);\n        this.extensions = inject(ExtensionsService);\n        this.identifier = inject(EXTENSIONS_IDENTIFIER);\n        this.extraPropertiesKey = EXTRA_PROPERTIES_KEY;\n    }\n    set selectedRecord(record) {\n        const type = !record || JSON.stringify(record) === '{}' ? 'create' : 'edit';\n        const propList = this.extensions[`${type}FormProps`].get(this.identifier).props;\n        this.groupedPropList = this.createGroupedList(propList);\n        this.record = record;\n    }\n    get form() {\n        return (this.container ? this.container.control : { controls: {} });\n    }\n    get extraProperties() {\n        return (this.form.controls.extraProperties || { controls: {} });\n    }\n    createGroupedList(propList) {\n        const groupedFormPropList = new GroupedFormPropList();\n        propList.forEach(item => {\n            groupedFormPropList.addItem(item.value);\n        });\n        return groupedFormPropList;\n    }\n    //TODO: Reactor this method\n    isAnyGroupMemberVisible(index, data) {\n        const { items } = this.groupedPropList;\n        const formPropList = items[index].formPropList.toArray();\n        return formPropList.some(prop => prop.visible(data));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: ExtensibleFormComponent, isStandalone: true, selector: \"abp-extensible-form\", inputs: { selectedRecord: \"selectedRecord\" }, viewQueries: [{ propertyName: \"formProps\", predicate: ExtensibleFormPropComponent, descendants: true }], exportAs: [\"abpExtensibleForm\"], ngImport: i0, template: \"@if (form) {\\r\\n  @for (groupedProp of groupedPropList.items; track i; let i = $index; let first = $first) {\\r\\n    <ng-container *abpPropData=\\\"let data; fromList: groupedProp.formPropList; withRecord: record\\\">\\r\\n      @if (isAnyGroupMemberVisible(i, data) && groupedProp.group?.className) {\\r\\n        <div\\r\\n          [ngClass]=\\\"groupedProp.group?.className\\\"\\r\\n          [attr.data-name]=\\\"groupedProp.group?.name || groupedProp.group?.className\\\"\\r\\n        >\\r\\n          <ng-container\\r\\n            [ngTemplateOutlet]=\\\"propListTemplate\\\"\\r\\n            [ngTemplateOutletContext]=\\\"{ groupedProp: groupedProp, data: data, isFirstGroup: first}\\\"\\r\\n          >\\r\\n          </ng-container>\\r\\n        </div>\\r\\n      } @else {\\r\\n        <ng-container\\r\\n          [ngTemplateOutlet]=\\\"propListTemplate\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ groupedProp: groupedProp, data: data, isFirstGroup: first }\\\"\\r\\n        >\\r\\n        </ng-container>\\r\\n      }\\r\\n    </ng-container>\\r\\n  }\\r\\n}\\r\\n\\r\\n<ng-template let-groupedProp=\\\"groupedProp\\\" let-data=\\\"data\\\" let-isFirstGroup=\\\"isFirstGroup\\\" #propListTemplate>\\r\\n  @for (prop of groupedProp.formPropList; let index = $index; let first = $first; track prop.name) {\\r\\n    @if (prop.visible(data)) {\\r\\n      @if (extraProperties.controls[prop.name]) {\\r\\n        <ng-container [formGroupName]=\\\"extraPropertiesKey\\\">\\r\\n          <abp-extensible-form-prop [prop]=\\\"prop\\\" [data]=\\\"data\\\" [class]=\\\"prop.className\\\" />\\r\\n        </ng-container>\\r\\n      } @else {\\r\\n        @if (form.get(prop.name)) {\\r\\n          <abp-extensible-form-prop\\r\\n            [class]=\\\"prop.className\\\"\\r\\n            [prop]=\\\"prop\\\"\\r\\n            [data]=\\\"data\\\"\\r\\n            [first]=\\\"first\\\"\\r\\n            [isFirstGroup]=\\\"isFirstGroup\\\"\\r\\n          />\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i5.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i5.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: PropDataDirective, selector: \"[abpPropData]\", inputs: [\"abpPropDataFromList\", \"abpPropDataWithRecord\", \"abpPropDataAtIndex\"], exportAs: [\"abpPropData\"] }, { kind: \"ngmodule\", type: ReactiveFormsModule }, { kind: \"directive\", type: i2.NgControlStatusGroup, selector: \"[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]\" }, { kind: \"directive\", type: i2.FormGroupName, selector: \"[formGroupName]\", inputs: [\"formGroupName\"] }, { kind: \"component\", type: ExtensibleFormPropComponent, selector: \"abp-extensible-form-prop\", inputs: [\"data\", \"prop\", \"first\", \"isFirstGroup\"] }], viewProviders: [\n            {\n                provide: ControlContainer,\n                useFactory: selfFactory,\n                deps: [[new Optional(), new SkipSelf(), ControlContainer]],\n            },\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleFormComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, exportAs: 'abpExtensibleForm', selector: 'abp-extensible-form', imports: [CommonModule, PropDataDirective, ReactiveFormsModule, ExtensibleFormPropComponent], changeDetection: ChangeDetectionStrategy.OnPush, viewProviders: [\n                        {\n                            provide: ControlContainer,\n                            useFactory: selfFactory,\n                            deps: [[new Optional(), new SkipSelf(), ControlContainer]],\n                        },\n                    ], template: \"@if (form) {\\r\\n  @for (groupedProp of groupedPropList.items; track i; let i = $index; let first = $first) {\\r\\n    <ng-container *abpPropData=\\\"let data; fromList: groupedProp.formPropList; withRecord: record\\\">\\r\\n      @if (isAnyGroupMemberVisible(i, data) && groupedProp.group?.className) {\\r\\n        <div\\r\\n          [ngClass]=\\\"groupedProp.group?.className\\\"\\r\\n          [attr.data-name]=\\\"groupedProp.group?.name || groupedProp.group?.className\\\"\\r\\n        >\\r\\n          <ng-container\\r\\n            [ngTemplateOutlet]=\\\"propListTemplate\\\"\\r\\n            [ngTemplateOutletContext]=\\\"{ groupedProp: groupedProp, data: data, isFirstGroup: first}\\\"\\r\\n          >\\r\\n          </ng-container>\\r\\n        </div>\\r\\n      } @else {\\r\\n        <ng-container\\r\\n          [ngTemplateOutlet]=\\\"propListTemplate\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ groupedProp: groupedProp, data: data, isFirstGroup: first }\\\"\\r\\n        >\\r\\n        </ng-container>\\r\\n      }\\r\\n    </ng-container>\\r\\n  }\\r\\n}\\r\\n\\r\\n<ng-template let-groupedProp=\\\"groupedProp\\\" let-data=\\\"data\\\" let-isFirstGroup=\\\"isFirstGroup\\\" #propListTemplate>\\r\\n  @for (prop of groupedProp.formPropList; let index = $index; let first = $first; track prop.name) {\\r\\n    @if (prop.visible(data)) {\\r\\n      @if (extraProperties.controls[prop.name]) {\\r\\n        <ng-container [formGroupName]=\\\"extraPropertiesKey\\\">\\r\\n          <abp-extensible-form-prop [prop]=\\\"prop\\\" [data]=\\\"data\\\" [class]=\\\"prop.className\\\" />\\r\\n        </ng-container>\\r\\n      } @else {\\r\\n        @if (form.get(prop.name)) {\\r\\n          <abp-extensible-form-prop\\r\\n            [class]=\\\"prop.className\\\"\\r\\n            [prop]=\\\"prop\\\"\\r\\n            [data]=\\\"data\\\"\\r\\n            [first]=\\\"first\\\"\\r\\n            [isFirstGroup]=\\\"isFirstGroup\\\"\\r\\n          />\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\" }]\n        }], propDecorators: { formProps: [{\n                type: ViewChildren,\n                args: [ExtensibleFormPropComponent]\n            }], selectedRecord: [{\n                type: Input\n            }] } });\n\n// Fix for https://github.com/angular/angular/issues/23904\n// @dynamic\nclass AbstractActionsComponent extends ActionData {\n    constructor(injector) {\n        super();\n        this.getInjected = injector.get.bind(injector);\n        const extensions = injector.get(ExtensionsService);\n        const name = injector.get(EXTENSIONS_IDENTIFIER);\n        const type = injector.get(EXTENSIONS_ACTION_TYPE);\n        this.actionList = extensions[type].get(name).actions;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AbstractActionsComponent, deps: [{ token: i0.Injector }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: AbstractActionsComponent, inputs: { record: \"record\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AbstractActionsComponent, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.Injector }], propDecorators: { record: [{\n                type: Input\n            }] } });\n\nclass GridActionsComponent extends AbstractActionsComponent {\n    constructor(injector) {\n        super(injector);\n        this.icon = 'fa fa-cog';\n        this.text = '';\n        this.trackByFn = (_, item) => item.text;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: GridActionsComponent, deps: [{ token: i0.Injector }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: GridActionsComponent, isStandalone: true, selector: \"abp-grid-actions\", inputs: { icon: \"icon\", index: \"index\", text: \"text\" }, providers: [\n            {\n                provide: EXTENSIONS_ACTION_TYPE,\n                useValue: 'entityActions',\n            },\n        ], exportAs: [\"abpGridActions\"], usesInheritance: true, ngImport: i0, template: \"@if (actionList.length > 1) {\\r\\n  <div ngbDropdown container=\\\"body\\\" class=\\\"d-inline-block\\\">\\r\\n    <button\\r\\n      class=\\\"btn btn-primary btn-sm dropdown-toggle\\\"\\r\\n      data-toggle=\\\"dropdown\\\"\\r\\n      aria-haspopup=\\\"true\\\"\\r\\n      ngbDropdownToggle\\r\\n    >\\r\\n      <i [ngClass]=\\\"icon\\\" [class.me-1]=\\\"icon\\\"></i>{{ text | abpLocalization }}\\r\\n    </button>\\r\\n    <div ngbDropdownMenu>\\r\\n      @for (action of actionList; track action.text) {\\r\\n        <ng-container\\r\\n          [ngTemplateOutlet]=\\\"dropDownBtnItemTmp\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ $implicit: action }\\\"\\r\\n        >\\r\\n        </ng-container>\\r\\n      }\\r\\n    </div>\\r\\n  </div>\\r\\n}\\r\\n\\r\\n@if (actionList.length === 1) {\\r\\n  <ng-container\\r\\n    [ngTemplateOutlet]=\\\"btnTmp\\\"\\r\\n    [ngTemplateOutletContext]=\\\"{ $implicit: actionList.get(0).value }\\\"\\r\\n  ></ng-container>\\r\\n}\\r\\n\\r\\n<ng-template #dropDownBtnItemTmp let-action>\\r\\n  @if (action.visible(data)) {\\r\\n    <button\\r\\n      ngbDropdownItem\\r\\n      *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n      (click)=\\\"action.action(data)\\\"\\r\\n      type=\\\"button\\\"\\r\\n    >\\r\\n      <ng-container\\r\\n        *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n      ></ng-container>\\r\\n    </button>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #buttonContentTmp let-action>\\r\\n  <i [ngClass]=\\\"action.icon\\\" [class.me-1]=\\\"action.icon && !action.showOnlyIcon\\\"></i>\\r\\n  @if (!action.showOnlyIcon) {\\r\\n    @if (action.icon) {\\r\\n      <span>{{ action.text | abpLocalization }}</span>\\r\\n    } @else {\\r\\n      <div abpEllipsis>{{ action.text | abpLocalization }}</div>\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #btnTmp let-action>\\r\\n  @if (action.visible(data)) {\\r\\n    @if (action.tooltip) {\\r\\n      <button\\r\\n        *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n        (click)=\\\"action.action(data)\\\"\\r\\n        type=\\\"button\\\"\\r\\n        [class]=\\\"action.btnClass\\\"\\r\\n        [style]=\\\"action.btnStyle\\\"\\r\\n        [ngbTooltip]=\\\"action.tooltip.text | abpLocalization\\\"\\r\\n        [placement]=\\\"action.tooltip.placement || 'auto'\\\"\\r\\n        container=\\\"body\\\"\\r\\n      >\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n        ></ng-container>\\r\\n      </button>\\r\\n    } @else {\\r\\n      <button\\r\\n        *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n        (click)=\\\"action.action(data)\\\"\\r\\n        type=\\\"button\\\"\\r\\n        [class]=\\\"action.btnClass\\\"\\r\\n        [style]=\\\"action.btnStyle\\\"\\r\\n      >\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n        ></ng-container>\\r\\n      </button>\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"ngmodule\", type: NgbDropdownModule }, { kind: \"directive\", type: i1.NgbDropdown, selector: \"[ngbDropdown]\", inputs: [\"autoClose\", \"dropdownClass\", \"open\", \"placement\", \"popperOptions\", \"container\", \"display\"], outputs: [\"openChange\"], exportAs: [\"ngbDropdown\"] }, { kind: \"directive\", type: i1.NgbDropdownToggle, selector: \"[ngbDropdownToggle]\" }, { kind: \"directive\", type: i1.NgbDropdownMenu, selector: \"[ngbDropdownMenu]\" }, { kind: \"directive\", type: i1.NgbDropdownItem, selector: \"[ngbDropdownItem]\", inputs: [\"tabindex\", \"disabled\"] }, { kind: \"directive\", type: i1.NgbDropdownButtonItem, selector: \"button[ngbDropdownItem]\" }, { kind: \"directive\", type: EllipsisDirective, selector: \"[abpEllipsis]\", inputs: [\"abpEllipsis\", \"title\", \"abpEllipsisEnabled\"] }, { kind: \"directive\", type: PermissionDirective, selector: \"[abpPermission]\", inputs: [\"abpPermission\", \"abpPermissionRunChangeDetection\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"ngmodule\", type: LocalizationModule }, { kind: \"pipe\", type: i2$1.LocalizationPipe, name: \"abpLocalization\" }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"ngmodule\", type: NgbTooltipModule }, { kind: \"directive\", type: i1.NgbTooltip, selector: \"[ngbTooltip]\", inputs: [\"animation\", \"autoClose\", \"placement\", \"popperOptions\", \"triggers\", \"positionTarget\", \"container\", \"disableTooltip\", \"tooltipClass\", \"tooltipContext\", \"openDelay\", \"closeDelay\", \"ngbTooltip\"], outputs: [\"shown\", \"hidden\"], exportAs: [\"ngbTooltip\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: GridActionsComponent, decorators: [{\n            type: Component,\n            args: [{ exportAs: 'abpGridActions', standalone: true, imports: [\n                        NgbDropdownModule,\n                        EllipsisDirective,\n                        PermissionDirective,\n                        NgClass,\n                        LocalizationModule,\n                        NgTemplateOutlet,\n                        NgbTooltipModule,\n                    ], selector: 'abp-grid-actions', providers: [\n                        {\n                            provide: EXTENSIONS_ACTION_TYPE,\n                            useValue: 'entityActions',\n                        },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, template: \"@if (actionList.length > 1) {\\r\\n  <div ngbDropdown container=\\\"body\\\" class=\\\"d-inline-block\\\">\\r\\n    <button\\r\\n      class=\\\"btn btn-primary btn-sm dropdown-toggle\\\"\\r\\n      data-toggle=\\\"dropdown\\\"\\r\\n      aria-haspopup=\\\"true\\\"\\r\\n      ngbDropdownToggle\\r\\n    >\\r\\n      <i [ngClass]=\\\"icon\\\" [class.me-1]=\\\"icon\\\"></i>{{ text | abpLocalization }}\\r\\n    </button>\\r\\n    <div ngbDropdownMenu>\\r\\n      @for (action of actionList; track action.text) {\\r\\n        <ng-container\\r\\n          [ngTemplateOutlet]=\\\"dropDownBtnItemTmp\\\"\\r\\n          [ngTemplateOutletContext]=\\\"{ $implicit: action }\\\"\\r\\n        >\\r\\n        </ng-container>\\r\\n      }\\r\\n    </div>\\r\\n  </div>\\r\\n}\\r\\n\\r\\n@if (actionList.length === 1) {\\r\\n  <ng-container\\r\\n    [ngTemplateOutlet]=\\\"btnTmp\\\"\\r\\n    [ngTemplateOutletContext]=\\\"{ $implicit: actionList.get(0).value }\\\"\\r\\n  ></ng-container>\\r\\n}\\r\\n\\r\\n<ng-template #dropDownBtnItemTmp let-action>\\r\\n  @if (action.visible(data)) {\\r\\n    <button\\r\\n      ngbDropdownItem\\r\\n      *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n      (click)=\\\"action.action(data)\\\"\\r\\n      type=\\\"button\\\"\\r\\n    >\\r\\n      <ng-container\\r\\n        *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n      ></ng-container>\\r\\n    </button>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #buttonContentTmp let-action>\\r\\n  <i [ngClass]=\\\"action.icon\\\" [class.me-1]=\\\"action.icon && !action.showOnlyIcon\\\"></i>\\r\\n  @if (!action.showOnlyIcon) {\\r\\n    @if (action.icon) {\\r\\n      <span>{{ action.text | abpLocalization }}</span>\\r\\n    } @else {\\r\\n      <div abpEllipsis>{{ action.text | abpLocalization }}</div>\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #btnTmp let-action>\\r\\n  @if (action.visible(data)) {\\r\\n    @if (action.tooltip) {\\r\\n      <button\\r\\n        *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n        (click)=\\\"action.action(data)\\\"\\r\\n        type=\\\"button\\\"\\r\\n        [class]=\\\"action.btnClass\\\"\\r\\n        [style]=\\\"action.btnStyle\\\"\\r\\n        [ngbTooltip]=\\\"action.tooltip.text | abpLocalization\\\"\\r\\n        [placement]=\\\"action.tooltip.placement || 'auto'\\\"\\r\\n        container=\\\"body\\\"\\r\\n      >\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n        ></ng-container>\\r\\n      </button>\\r\\n    } @else {\\r\\n      <button\\r\\n        *abpPermission=\\\"action.permission; runChangeDetection: false\\\"\\r\\n        (click)=\\\"action.action(data)\\\"\\r\\n        type=\\\"button\\\"\\r\\n        [class]=\\\"action.btnClass\\\"\\r\\n        [style]=\\\"action.btnStyle\\\"\\r\\n      >\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"buttonContentTmp; context: { $implicit: action }\\\"\\r\\n        ></ng-container>\\r\\n      </button>\\r\\n    }\\r\\n  }\\r\\n</ng-template>\\r\\n\" }]\n        }], ctorParameters: () => [{ type: i0.Injector }], propDecorators: { icon: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], text: [{\n                type: Input\n            }] } });\n\nconst DEFAULT_ACTIONS_COLUMN_WIDTH = 150;\nclass ExtensibleTableComponent {\n    set actionsText(value) {\n        this._actionsText = value;\n    }\n    get actionsText() {\n        return this._actionsText ?? (this.actionList.length > 1 ? 'AbpUi::Actions' : '');\n    }\n    set actionsColumnWidth(width) {\n        this.setColumnWidths(width ? Number(width) : undefined);\n    }\n    #injector;\n    constructor() {\n        this.tableActivate = new EventEmitter();\n        this.trackByFn = (_, item) => item.name;\n        this.locale = inject(LOCALE_ID);\n        this.config = inject(ConfigStateService);\n        this.entityPropTypeClasses = inject(ENTITY_PROP_TYPE_CLASSES);\n        this.#injector = inject(Injector);\n        this.getInjected = this.#injector.get.bind(this.#injector);\n        this.permissionService = this.#injector.get(PermissionService);\n        const extensions = this.#injector.get(ExtensionsService);\n        const name = this.#injector.get(EXTENSIONS_IDENTIFIER);\n        this.propList = extensions.entityProps.get(name).props;\n        this.actionList = extensions['entityActions'].get(name)\n            .actions;\n        this.hasAtLeastOnePermittedAction =\n            this.permissionService.filterItemsByPolicy(this.actionList.toArray().map(action => ({ requiredPolicy: action.permission }))).length > 0;\n        this.setColumnWidths(DEFAULT_ACTIONS_COLUMN_WIDTH);\n    }\n    setColumnWidths(actionsColumn) {\n        const widths = [actionsColumn];\n        this.propList.forEach(({ value: prop }) => {\n            widths.push(prop.columnWidth);\n        });\n        this.columnWidths = widths;\n    }\n    getDate(value, format) {\n        return value && format ? formatDate(value, format, this.locale) : '';\n    }\n    getIcon(value) {\n        return value\n            ? '<div class=\"text-success\"><i class=\"fa fa-check\" aria-hidden=\"true\"></i></div>'\n            : '<div class=\"text-danger\"><i class=\"fa fa-times\" aria-hidden=\"true\"></i></div>';\n    }\n    getEnum(rowValue, list) {\n        if (!list || list.length < 1)\n            return rowValue;\n        const { key } = list.find(({ value }) => value === rowValue) || {};\n        return key;\n    }\n    getContent(prop, data) {\n        return prop.valueResolver(data).pipe(map(value => {\n            switch (prop.type) {\n                case \"boolean\" /* ePropType.Boolean */:\n                    return this.getIcon(value);\n                case \"date\" /* ePropType.Date */:\n                    return this.getDate(value, getShortDateFormat(this.config));\n                case \"time\" /* ePropType.Time */:\n                    return this.getDate(value, getShortTimeFormat(this.config));\n                case \"datetime\" /* ePropType.DateTime */:\n                    return this.getDate(value, getShortDateShortTimeFormat(this.config));\n                case \"enum\" /* ePropType.Enum */:\n                    return this.getEnum(value, prop.enumList || []);\n                default:\n                    return value;\n                // More types can be handled in the future\n            }\n        }));\n    }\n    ngOnChanges({ data }) {\n        if (!data?.currentValue)\n            return;\n        if (data.currentValue.length < 1) {\n            this.list.totalCount = this.recordsTotal;\n        }\n        this.data = data.currentValue.map((record, index) => {\n            this.propList.forEach(prop => {\n                const propData = { getInjected: this.getInjected, record, index };\n                const value = this.getContent(prop.value, propData);\n                const propKey = `_${prop.value.name}`;\n                record[propKey] = {\n                    visible: prop.value.visible(propData),\n                    value,\n                };\n                if (prop.value.component) {\n                    record[propKey].injector = Injector.create({\n                        providers: [\n                            {\n                                provide: PROP_DATA_STREAM,\n                                useValue: value,\n                            },\n                        ],\n                        parent: this.#injector,\n                    });\n                    record[propKey].component = prop.value.component;\n                }\n            });\n            return record;\n        });\n    }\n    isVisibleActions(rowData) {\n        const actions = this.actionList.toArray();\n        const visibleActions = actions.filter(action => {\n            const { visible, permission } = action;\n            let isVisible = true;\n            let hasPermission = true;\n            if (visible) {\n                isVisible = visible({ record: rowData, getInjected: this.getInjected });\n            }\n            if (permission) {\n                hasPermission = this.permissionService.getGrantedPolicy(permission);\n            }\n            return isVisible && hasPermission;\n        });\n        return visibleActions.length > 0;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleTableComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: ExtensibleTableComponent, isStandalone: true, selector: \"abp-extensible-table\", inputs: { actionsText: \"actionsText\", data: \"data\", list: \"list\", recordsTotal: \"recordsTotal\", actionsColumnWidth: \"actionsColumnWidth\", actionsTemplate: \"actionsTemplate\" }, outputs: { tableActivate: \"tableActivate\" }, exportAs: [\"abpExtensibleTable\"], usesOnChanges: true, ngImport: i0, template: \"<ngx-datatable\\r\\n  default\\r\\n  [rows]=\\\"data\\\"\\r\\n  [count]=\\\"recordsTotal\\\"\\r\\n  [list]=\\\"list\\\"\\r\\n  (activate)=\\\"tableActivate.emit($event)\\\"\\r\\n>\\r\\n  @if (actionsTemplate || (actionList.length && hasAtLeastOnePermittedAction)) {\\r\\n    <ngx-datatable-column\\r\\n      [name]=\\\"actionsText | abpLocalization\\\"\\r\\n      [maxWidth]=\\\"columnWidths[0]\\\"\\r\\n      [width]=\\\"columnWidths[0]\\\"\\r\\n      [sortable]=\\\"false\\\"\\r\\n    >\\r\\n      <ng-template let-row=\\\"row\\\" let-i=\\\"rowIndex\\\" ngx-datatable-cell-template>\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"actionsTemplate || gridActions; context: { $implicit: row, index: i }\\\"\\r\\n        ></ng-container>\\r\\n        <ng-template #gridActions>\\r\\n          @if (isVisibleActions(row)) {\\r\\n            <abp-grid-actions [index]=\\\"i\\\" [record]=\\\"row\\\" text=\\\"AbpUi::Actions\\\"></abp-grid-actions>\\r\\n          }\\r\\n        </ng-template>\\r\\n      </ng-template>\\r\\n    </ngx-datatable-column>\\r\\n  }\\r\\n  @for (prop of propList; track prop.name; let i = $index) {\\r\\n    <ngx-datatable-column\\r\\n      *abpVisible=\\\"prop.columnVisible(getInjected)\\\"\\r\\n      [width]=\\\"columnWidths[i + 1] || 200\\\"\\r\\n      [name]=\\\"prop.displayName | abpLocalization\\\"\\r\\n      [prop]=\\\"prop.name\\\"\\r\\n      [sortable]=\\\"prop.sortable\\\"\\r\\n    >\\r\\n      <ng-template ngx-datatable-header-template let-column=\\\"column\\\">\\r\\n        @if (prop.tooltip) {\\r\\n          <span\\r\\n            [ngbTooltip]=\\\"prop.tooltip.text | abpLocalization\\\"\\r\\n            [placement]=\\\"prop.tooltip.placement || 'auto'\\\"\\r\\n            container=\\\"body\\\"\\r\\n          >\\r\\n            {{ column.name }} <i class=\\\"fa fa-info-circle\\\" aria-hidden=\\\"true\\\"></i>\\r\\n          </span>\\r\\n        } @else {\\r\\n          {{ column.name }}\\r\\n        }\\r\\n      </ng-template>\\r\\n      <ng-template let-row=\\\"row\\\" let-i=\\\"index\\\" ngx-datatable-cell-template>\\r\\n        <ng-container *abpPermission=\\\"prop.permission; runChangeDetection: false\\\">\\r\\n          <ng-container *abpVisible=\\\"row['_' + prop.name]?.visible\\\">\\r\\n            @if (!row['_' + prop.name].component) {\\r\\n              <div\\r\\n                [innerHTML]=\\\"row['_' + prop.name]?.value | async\\\"\\r\\n                (click)=\\\"\\r\\n                  prop.action && prop.action({ getInjected: getInjected, record: row, index: i })\\r\\n                \\\"\\r\\n                [ngClass]=\\\"entityPropTypeClasses[prop.type]\\\"\\r\\n                [class.pointer]=\\\"prop.action\\\"\\r\\n              ></div>\\r\\n            } @else {\\r\\n              <ng-container\\r\\n                *ngComponentOutlet=\\\"\\r\\n                  row['_' + prop.name].component;\\r\\n                  injector: row['_' + prop.name].injector\\r\\n                \\\"\\r\\n              ></ng-container>\\r\\n            }\\r\\n          </ng-container>\\r\\n        </ng-container>\\r\\n      </ng-template>\\r\\n    </ngx-datatable-column>\\r\\n  }\\r\\n</ngx-datatable>\\r\\n\", dependencies: [{ kind: \"directive\", type: AbpVisibleDirective, selector: \"[abpVisible]\", inputs: [\"abpVisible\"] }, { kind: \"ngmodule\", type: NgxDatatableModule }, { kind: \"component\", type: i1$1.DatatableComponent, selector: \"ngx-datatable\", inputs: [\"targetMarkerTemplate\", \"rows\", \"groupRowsBy\", \"groupedRows\", \"columns\", \"selected\", \"scrollbarV\", \"scrollbarH\", \"rowHeight\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"externalPaging\", \"externalSorting\", \"limit\", \"count\", \"offset\", \"loadingIndicator\", \"selectionType\", \"reorderable\", \"swapColumns\", \"sortType\", \"sorts\", \"cssClasses\", \"messages\", \"rowClass\", \"selectCheck\", \"displayCheck\", \"groupExpansionDefault\", \"trackByProp\", \"selectAllRowsOnPage\", \"virtualization\", \"treeFromRelation\", \"treeToRelation\", \"summaryRow\", \"summaryHeight\", \"summaryPosition\", \"rowIdentity\"], outputs: [\"scroll\", \"activate\", \"select\", \"sort\", \"page\", \"reorder\", \"resize\", \"tableContextmenu\", \"treeAction\"] }, { kind: \"directive\", type: i1$1.DataTableColumnDirective, selector: \"ngx-datatable-column\", inputs: [\"name\", \"prop\", \"frozenLeft\", \"frozenRight\", \"flexGrow\", \"resizeable\", \"comparator\", \"pipe\", \"sortable\", \"draggable\", \"canAutoResize\", \"minWidth\", \"width\", \"maxWidth\", \"checkboxable\", \"headerCheckboxable\", \"headerClass\", \"cellClass\", \"isTreeColumn\", \"treeLevelIndent\", \"summaryFunc\", \"summaryTemplate\", \"cellTemplate\", \"headerTemplate\", \"treeToggleTemplate\"] }, { kind: \"directive\", type: i1$1.DataTableColumnHeaderDirective, selector: \"[ngx-datatable-header-template]\" }, { kind: \"directive\", type: i1$1.DataTableColumnCellDirective, selector: \"[ngx-datatable-cell-template]\" }, { kind: \"component\", type: GridActionsComponent, selector: \"abp-grid-actions\", inputs: [\"icon\", \"index\", \"text\"], exportAs: [\"abpGridActions\"] }, { kind: \"directive\", type: NgbTooltip, selector: \"[ngbTooltip]\", inputs: [\"animation\", \"autoClose\", \"placement\", \"popperOptions\", \"triggers\", \"positionTarget\", \"container\", \"disableTooltip\", \"tooltipClass\", \"tooltipContext\", \"openDelay\", \"closeDelay\", \"ngbTooltip\"], outputs: [\"shown\", \"hidden\"], exportAs: [\"ngbTooltip\"] }, { kind: \"directive\", type: NgxDatatableDefaultDirective, selector: \"ngx-datatable[default]\", inputs: [\"class\"], exportAs: [\"ngxDatatableDefault\"] }, { kind: \"directive\", type: NgxDatatableListDirective, selector: \"ngx-datatable[list]\", inputs: [\"list\"], exportAs: [\"ngxDatatableList\"] }, { kind: \"directive\", type: PermissionDirective, selector: \"[abpPermission]\", inputs: [\"abpPermission\", \"abpPermissionRunChangeDetection\"] }, { kind: \"ngmodule\", type: LocalizationModule }, { kind: \"pipe\", type: i2$1.LocalizationPipe, name: \"abpLocalization\" }, { kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgComponentOutlet, selector: \"[ngComponentOutlet]\", inputs: [\"ngComponentOutlet\", \"ngComponentOutletInputs\", \"ngComponentOutletInjector\", \"ngComponentOutletContent\", \"ngComponentOutletNgModule\", \"ngComponentOutletNgModuleFactory\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleTableComponent, decorators: [{\n            type: Component,\n            args: [{ exportAs: 'abpExtensibleTable', selector: 'abp-extensible-table', standalone: true, imports: [\n                        AbpVisibleDirective,\n                        NgxDatatableModule,\n                        GridActionsComponent,\n                        NgbTooltip,\n                        NgxDatatableDefaultDirective,\n                        NgxDatatableListDirective,\n                        PermissionDirective,\n                        LocalizationModule,\n                        AsyncPipe,\n                        NgTemplateOutlet,\n                        NgComponentOutlet,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ngx-datatable\\r\\n  default\\r\\n  [rows]=\\\"data\\\"\\r\\n  [count]=\\\"recordsTotal\\\"\\r\\n  [list]=\\\"list\\\"\\r\\n  (activate)=\\\"tableActivate.emit($event)\\\"\\r\\n>\\r\\n  @if (actionsTemplate || (actionList.length && hasAtLeastOnePermittedAction)) {\\r\\n    <ngx-datatable-column\\r\\n      [name]=\\\"actionsText | abpLocalization\\\"\\r\\n      [maxWidth]=\\\"columnWidths[0]\\\"\\r\\n      [width]=\\\"columnWidths[0]\\\"\\r\\n      [sortable]=\\\"false\\\"\\r\\n    >\\r\\n      <ng-template let-row=\\\"row\\\" let-i=\\\"rowIndex\\\" ngx-datatable-cell-template>\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"actionsTemplate || gridActions; context: { $implicit: row, index: i }\\\"\\r\\n        ></ng-container>\\r\\n        <ng-template #gridActions>\\r\\n          @if (isVisibleActions(row)) {\\r\\n            <abp-grid-actions [index]=\\\"i\\\" [record]=\\\"row\\\" text=\\\"AbpUi::Actions\\\"></abp-grid-actions>\\r\\n          }\\r\\n        </ng-template>\\r\\n      </ng-template>\\r\\n    </ngx-datatable-column>\\r\\n  }\\r\\n  @for (prop of propList; track prop.name; let i = $index) {\\r\\n    <ngx-datatable-column\\r\\n      *abpVisible=\\\"prop.columnVisible(getInjected)\\\"\\r\\n      [width]=\\\"columnWidths[i + 1] || 200\\\"\\r\\n      [name]=\\\"prop.displayName | abpLocalization\\\"\\r\\n      [prop]=\\\"prop.name\\\"\\r\\n      [sortable]=\\\"prop.sortable\\\"\\r\\n    >\\r\\n      <ng-template ngx-datatable-header-template let-column=\\\"column\\\">\\r\\n        @if (prop.tooltip) {\\r\\n          <span\\r\\n            [ngbTooltip]=\\\"prop.tooltip.text | abpLocalization\\\"\\r\\n            [placement]=\\\"prop.tooltip.placement || 'auto'\\\"\\r\\n            container=\\\"body\\\"\\r\\n          >\\r\\n            {{ column.name }} <i class=\\\"fa fa-info-circle\\\" aria-hidden=\\\"true\\\"></i>\\r\\n          </span>\\r\\n        } @else {\\r\\n          {{ column.name }}\\r\\n        }\\r\\n      </ng-template>\\r\\n      <ng-template let-row=\\\"row\\\" let-i=\\\"index\\\" ngx-datatable-cell-template>\\r\\n        <ng-container *abpPermission=\\\"prop.permission; runChangeDetection: false\\\">\\r\\n          <ng-container *abpVisible=\\\"row['_' + prop.name]?.visible\\\">\\r\\n            @if (!row['_' + prop.name].component) {\\r\\n              <div\\r\\n                [innerHTML]=\\\"row['_' + prop.name]?.value | async\\\"\\r\\n                (click)=\\\"\\r\\n                  prop.action && prop.action({ getInjected: getInjected, record: row, index: i })\\r\\n                \\\"\\r\\n                [ngClass]=\\\"entityPropTypeClasses[prop.type]\\\"\\r\\n                [class.pointer]=\\\"prop.action\\\"\\r\\n              ></div>\\r\\n            } @else {\\r\\n              <ng-container\\r\\n                *ngComponentOutlet=\\\"\\r\\n                  row['_' + prop.name].component;\\r\\n                  injector: row['_' + prop.name].injector\\r\\n                \\\"\\r\\n              ></ng-container>\\r\\n            }\\r\\n          </ng-container>\\r\\n        </ng-container>\\r\\n      </ng-template>\\r\\n    </ngx-datatable-column>\\r\\n  }\\r\\n</ngx-datatable>\\r\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { actionsText: [{\n                type: Input\n            }], data: [{\n                type: Input\n            }], list: [{\n                type: Input\n            }], recordsTotal: [{\n                type: Input\n            }], actionsColumnWidth: [{\n                type: Input\n            }], actionsTemplate: [{\n                type: Input\n            }], tableActivate: [{\n                type: Output\n            }] } });\n\nclass PageToolbarComponent extends AbstractActionsComponent {\n    constructor(injector) {\n        super(injector);\n        this.injector = injector;\n        this.defaultBtnClass = 'btn btn-sm btn-primary';\n        this.getData = () => this.data;\n        this.trackByFn = (_, item) => item.action || item.component;\n    }\n    asToolbarAction(value) {\n        return {\n            value: value,\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PageToolbarComponent, deps: [{ token: i0.Injector }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: PageToolbarComponent, isStandalone: true, selector: \"abp-page-toolbar\", providers: [\n            {\n                provide: EXTENSIONS_ACTION_TYPE,\n                useValue: 'toolbarActions',\n            },\n        ], exportAs: [\"abpPageToolbar\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"row justify-content-end mx-0 gap-2\\\" id=\\\"AbpContentToolbar\\\">\\r\\n  @for (action of actionList; track action.component || action.action; let last = $last) {\\r\\n  <div class=\\\"col-auto px-0 pt-0\\\" [class.pe-0]=\\\"last\\\">\\r\\n    @if (action.visible(data)) {\\r\\n    <ng-container *abpPermission=\\\"action.permission; runChangeDetection: false\\\">\\r\\n      @if (action.component; as component) {\\r\\n      <ng-container\\r\\n        *ngComponentOutlet=\\\"component; injector: record | createInjector: action:this\\\"\\r\\n      ></ng-container>\\r\\n\\r\\n      }@else {\\r\\n         @if (asToolbarAction(action).value; as toolbarAction ) {\\r\\n          <button\\r\\n            (click)=\\\"action.action(data)\\\"\\r\\n            type=\\\"button\\\"\\r\\n            [ngClass]=\\\"toolbarAction?.btnClass ? toolbarAction?.btnClass : defaultBtnClass\\\"\\r\\n            class=\\\"d-inline-flex align-items-center gap-1\\\"\\r\\n          >\\r\\n            <i [ngClass]=\\\"toolbarAction?.icon\\\" [class.me-1]=\\\"toolbarAction?.icon\\\"></i>\\r\\n            {{ toolbarAction?.text | abpLocalization }}\\r\\n          </button>\\r\\n        } \\r\\n      }\\r\\n    </ng-container>\\r\\n    }\\r\\n  </div>\\r\\n  }\\r\\n</div>\\r\\n\\r\\n\", dependencies: [{ kind: \"pipe\", type: CreateInjectorPipe, name: \"createInjector\" }, { kind: \"directive\", type: PermissionDirective, selector: \"[abpPermission]\", inputs: [\"abpPermission\", \"abpPermissionRunChangeDetection\"] }, { kind: \"ngmodule\", type: LocalizationModule }, { kind: \"pipe\", type: i2$1.LocalizationPipe, name: \"abpLocalization\" }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: NgComponentOutlet, selector: \"[ngComponentOutlet]\", inputs: [\"ngComponentOutlet\", \"ngComponentOutletInputs\", \"ngComponentOutletInjector\", \"ngComponentOutletContent\", \"ngComponentOutletNgModule\", \"ngComponentOutletNgModuleFactory\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PageToolbarComponent, decorators: [{\n            type: Component,\n            args: [{ exportAs: 'abpPageToolbar', selector: 'abp-page-toolbar', standalone: true, imports: [\n                        CreateInjectorPipe,\n                        PermissionDirective,\n                        LocalizationModule,\n                        NgClass,\n                        NgComponentOutlet,\n                    ], providers: [\n                        {\n                            provide: EXTENSIONS_ACTION_TYPE,\n                            useValue: 'toolbarActions',\n                        },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"row justify-content-end mx-0 gap-2\\\" id=\\\"AbpContentToolbar\\\">\\r\\n  @for (action of actionList; track action.component || action.action; let last = $last) {\\r\\n  <div class=\\\"col-auto px-0 pt-0\\\" [class.pe-0]=\\\"last\\\">\\r\\n    @if (action.visible(data)) {\\r\\n    <ng-container *abpPermission=\\\"action.permission; runChangeDetection: false\\\">\\r\\n      @if (action.component; as component) {\\r\\n      <ng-container\\r\\n        *ngComponentOutlet=\\\"component; injector: record | createInjector: action:this\\\"\\r\\n      ></ng-container>\\r\\n\\r\\n      }@else {\\r\\n         @if (asToolbarAction(action).value; as toolbarAction ) {\\r\\n          <button\\r\\n            (click)=\\\"action.action(data)\\\"\\r\\n            type=\\\"button\\\"\\r\\n            [ngClass]=\\\"toolbarAction?.btnClass ? toolbarAction?.btnClass : defaultBtnClass\\\"\\r\\n            class=\\\"d-inline-flex align-items-center gap-1\\\"\\r\\n          >\\r\\n            <i [ngClass]=\\\"toolbarAction?.icon\\\" [class.me-1]=\\\"toolbarAction?.icon\\\"></i>\\r\\n            {{ toolbarAction?.text | abpLocalization }}\\r\\n          </button>\\r\\n        } \\r\\n      }\\r\\n    </ng-container>\\r\\n    }\\r\\n  </div>\\r\\n  }\\r\\n</div>\\r\\n\\r\\n\" }]\n        }], ctorParameters: () => [{ type: i0.Injector }] });\n\nvar objectExtensions = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst EXTENSIBLE_FORM_VIEW_PROVIDER = { provide: ControlContainer, useExisting: FormGroupDirective };\n\nfunction mergeWithDefaultActions(extension, defaultActions, ...contributors) {\n    Object.keys(defaultActions).forEach((name) => {\n        const actions = extension.get(name);\n        actions.clearContributors();\n        actions.addContributor((actionList) => actionList.addManyTail(defaultActions[name]));\n        contributors.forEach(contributor => (contributor[name] || []).forEach((callback) => actions.addContributor(callback)));\n    });\n}\n\nfunction generateFormFromProps(data) {\n    const extensions = data.getInjected((ExtensionsService));\n    const identifier = data.getInjected(EXTENSIONS_IDENTIFIER);\n    const form = new UntypedFormGroup({});\n    const extraForm = new UntypedFormGroup({});\n    form.addControl(EXTRA_PROPERTIES_KEY, extraForm);\n    const record = data.record || {};\n    const type = JSON.stringify(record) === '{}' ? 'create' : 'edit';\n    const props = extensions[`${type}FormProps`].get(identifier).props;\n    const extraProperties = record[EXTRA_PROPERTIES_KEY] || {};\n    props.forEach(({ value: prop }) => {\n        const name = prop.name;\n        const isExtraProperty = prop.isExtra || name in extraProperties;\n        let value = isExtraProperty ? extraProperties[name] : name in record ? record[name] : undefined;\n        if (typeof value === 'undefined')\n            value = prop.defaultValue;\n        if (value) {\n            let adapter;\n            switch (prop.type) {\n                case \"date\" /* ePropType.Date */:\n                    adapter = new DateAdapter();\n                    value = adapter.toModel(adapter.fromModel(value));\n                    break;\n                case \"time\" /* ePropType.Time */:\n                    adapter = new TimeAdapter();\n                    value = adapter.toModel(adapter.fromModel(value));\n                    break;\n                case \"datetime\" /* ePropType.DateTime */:\n                    adapter = new DateTimeAdapter();\n                    value = adapter.toModel(adapter.fromModel(value));\n                    break;\n                default:\n                    break;\n            }\n        }\n        const formControl = new UntypedFormControl(value, {\n            asyncValidators: prop.asyncValidators(data),\n            validators: prop.validators(data),\n        });\n        (isExtraProperty ? extraForm : form).addControl(name, formControl);\n    });\n    return form;\n}\n\nfunction createExtraPropertyValueResolver(name) {\n    return (data) => of(data.record[EXTRA_PROPERTIES_KEY][name]);\n}\nfunction mergeWithDefaultProps(extension, defaultProps, ...contributors) {\n    Object.keys(defaultProps).forEach((name) => {\n        const props = extension.get(name);\n        props.clearContributors();\n        props.addContributor((propList) => propList.addManyTail(defaultProps[name]));\n        contributors.forEach(contributor => (contributor[name] || []).forEach((callback) => props.addContributor(callback)));\n    });\n}\n\nfunction createEnum(members) {\n    const enumObject = {};\n    members.forEach(({ name = '', value }) => {\n        enumObject[(enumObject[name] = value)] = name;\n    });\n    return enumObject;\n}\nfunction createEnumValueResolver(enumType, lookupEnum, propName) {\n    return data => {\n        const value = data.record[EXTRA_PROPERTIES_KEY][propName];\n        const key = lookupEnum.transformed[value];\n        const l10n = data.getInjected(LocalizationService);\n        const localizeEnum = createEnumLocalizer(l10n, enumType, lookupEnum);\n        return createLocalizationStream(l10n, localizeEnum(key));\n    };\n}\nfunction createEnumOptions(enumType, lookupEnum) {\n    return data => {\n        const l10n = data.getInjected(LocalizationService);\n        const localizeEnum = createEnumLocalizer(l10n, enumType, lookupEnum);\n        return createLocalizationStream(l10n, lookupEnum.fields.map(({ name = '', value }) => ({\n            key: localizeEnum(name),\n            value,\n        })));\n    };\n}\nfunction createLocalizationStream(l10n, mapTarget) {\n    return merge(of(null), l10n.languageChange$).pipe(map(() => mapTarget));\n}\nfunction createEnumLocalizer(l10n, enumType, lookupEnum) {\n    const resource = lookupEnum.localizationResource;\n    const shortType = getShortEnumType(enumType);\n    return key => l10n.localizeWithFallbackSync([resource || ''], ['Enum:' + shortType + '.' + key, shortType + '.' + key, key], key);\n}\nfunction getShortEnumType(enumType) {\n    return enumType.split('.').pop();\n}\n\nfunction createDisplayNameLocalizationPipeKeyGenerator(localization) {\n    const generateLocalizationPipeKey = createLocalizationPipeKeyGenerator(localization);\n    return (displayName, fallback) => {\n        if (displayName && displayName.name)\n            return generateLocalizationPipeKey([displayName.resource || ''], [displayName.name], displayName.name);\n        const key = generateLocalizationPipeKey([fallback.resource || ''], ['DisplayName:' + fallback.name], undefined);\n        if (key)\n            return key;\n        return generateLocalizationPipeKey([fallback.resource || ''], [fallback.name || ''], fallback.name);\n    };\n}\n\nfunction getValidatorsFromProperty(property) {\n    const validators = [];\n    property.attributes.forEach(attr => {\n        if (attr.typeSimple && attr.typeSimple in AbpValidators) {\n            validators.push(AbpValidators[attr.typeSimple](attr.config));\n        }\n    });\n    return validators;\n}\n\nfunction selectObjectExtensions(configState) {\n    return configState.getOne$('objectExtensions');\n}\nfunction selectLocalization(configState) {\n    return configState.getOne$('localization');\n}\nfunction selectEnums(configState) {\n    return selectObjectExtensions(configState).pipe(map((extensions) => Object.keys(extensions.enums).reduce((acc, key) => {\n        const { fields, localizationResource } = extensions.enums[key];\n        acc[key] = {\n            fields,\n            localizationResource,\n            transformed: createEnum(fields),\n        };\n        return acc;\n    }, {})));\n}\nfunction getObjectExtensionEntitiesFromStore(configState, moduleKey) {\n    return selectObjectExtensions(configState).pipe(map(extensions => {\n        if (!extensions)\n            return null;\n        return (extensions.modules[moduleKey] || {})\n            .entities;\n    }), map(entities => (isUndefined(entities) ? {} : entities)), filter(Boolean), take(1));\n}\nfunction mapEntitiesToContributors(configState, resource) {\n    return pipe(switchMap((entities) => zip(selectLocalization(configState), selectEnums(configState)).pipe(map(([localization, enums]) => {\n        const generateDisplayName = createDisplayNameLocalizationPipeKeyGenerator(localization);\n        return Object.keys(entities).reduce((acc, key) => {\n            acc.prop[key] = [];\n            acc.createForm[key] = [];\n            acc.editForm[key] = [];\n            const entity = entities[key];\n            if (!entity)\n                return acc;\n            const properties = entity.properties;\n            if (!properties)\n                return acc;\n            const mapPropertiesToContributors = createPropertiesToContributorsMapper(generateDisplayName, resource, enums);\n            return mapPropertiesToContributors(properties, acc, key);\n        }, {\n            prop: {},\n            createForm: {},\n            editForm: {},\n        });\n    }))), take(1));\n}\nfunction createPropertiesToContributorsMapper(generateDisplayName, resource, enums) {\n    return (properties, contributors, key) => {\n        const isExtra = true;\n        const generateTypeaheadDisplayName = createTypeaheadDisplayNameGenerator(generateDisplayName, properties);\n        Object.keys(properties).forEach((name) => {\n            const property = properties[name];\n            const propName = name;\n            const lookup = property.ui.lookup || {};\n            const type = getTypeaheadType(lookup, name) || getTypeFromProperty(property);\n            const generateDN = hasTypeaheadTextSuffix(name)\n                ? generateTypeaheadDisplayName\n                : generateDisplayName;\n            const displayName = generateDN(property.displayName, { name, resource });\n            if (property.ui.onTable.isVisible) {\n                const sortable = Boolean(property.ui.onTable.isSortable);\n                const columnWidth = type === \"boolean\" /* ePropType.Boolean */ ? 150 : 250;\n                const valueResolver = type === \"enum\" /* ePropType.Enum */ &&\n                    property.type\n                    ? createEnumValueResolver(property.type, enums[property.type], propName)\n                    : createExtraPropertyValueResolver(propName);\n                const entityProp = new EntityProp({\n                    type,\n                    name: propName,\n                    displayName,\n                    sortable,\n                    columnWidth,\n                    valueResolver,\n                    isExtra,\n                });\n                const contributor = (propList) => propList.addTail(entityProp);\n                contributors.prop[key].push(contributor);\n            }\n            const isOnCreateForm = property.ui.onCreateForm.isVisible;\n            const isOnEditForm = property.ui.onEditForm.isVisible;\n            if (isOnCreateForm || isOnEditForm) {\n                const defaultValue = property.defaultValue;\n                const formText = property.formText;\n                const validators = () => getValidatorsFromProperty(property);\n                let options;\n                if (type === \"enum\" /* ePropType.Enum */)\n                    options = createEnumOptions(propName, enums[property.type || '']);\n                else if (type === \"typeahead\" /* ePropType.Typeahead */)\n                    options = createTypeaheadOptions(lookup);\n                const formProp = new FormProp({\n                    type,\n                    name: propName,\n                    displayName,\n                    options,\n                    defaultValue,\n                    validators,\n                    isExtra,\n                    formText,\n                });\n                const formContributor = (propList) => propList.addTail(formProp);\n                if (isOnCreateForm)\n                    contributors.createForm[key].push(formContributor);\n                if (isOnEditForm)\n                    contributors.editForm[key].push(formContributor);\n            }\n        });\n        return contributors;\n    };\n}\nfunction getTypeFromProperty(property) {\n    return property?.typeSimple?.replace(/\\?$/, '');\n}\nfunction isUndefined(obj) {\n    return typeof obj === 'undefined';\n}\n\nconst importWithExport = [\n    DisabledDirective,\n    ExtensibleDateTimePickerComponent,\n    ExtensibleFormPropComponent,\n    GridActionsComponent,\n    PropDataDirective,\n    PageToolbarComponent,\n    CreateInjectorPipe,\n    ExtensibleFormComponent,\n    ExtensibleTableComponent,\n];\nclass ExtensibleModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleModule, imports: [CoreModule,\n            ThemeSharedModule,\n            NgxValidateCoreModule,\n            NgbDatepickerModule,\n            NgbDropdownModule,\n            NgbTimepickerModule,\n            NgbTypeaheadModule,\n            NgbTooltipModule, DisabledDirective,\n            ExtensibleDateTimePickerComponent,\n            ExtensibleFormPropComponent,\n            GridActionsComponent,\n            PropDataDirective,\n            PageToolbarComponent,\n            CreateInjectorPipe,\n            ExtensibleFormComponent,\n            ExtensibleTableComponent], exports: [DisabledDirective,\n            ExtensibleDateTimePickerComponent,\n            ExtensibleFormPropComponent,\n            GridActionsComponent,\n            PropDataDirective,\n            PageToolbarComponent,\n            CreateInjectorPipe,\n            ExtensibleFormComponent,\n            ExtensibleTableComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleModule, imports: [CoreModule,\n            ThemeSharedModule,\n            NgxValidateCoreModule,\n            NgbDatepickerModule,\n            NgbDropdownModule,\n            NgbTimepickerModule,\n            NgbTypeaheadModule,\n            NgbTooltipModule, ExtensibleDateTimePickerComponent,\n            ExtensibleFormPropComponent,\n            GridActionsComponent,\n            PageToolbarComponent,\n            ExtensibleFormComponent,\n            ExtensibleTableComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ExtensibleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [],\n                    imports: [\n                        CoreModule,\n                        ThemeSharedModule,\n                        NgxValidateCoreModule,\n                        NgbDatepickerModule,\n                        NgbDropdownModule,\n                        NgbTimepickerModule,\n                        NgbTypeaheadModule,\n                        NgbTooltipModule,\n                        ...importWithExport,\n                    ],\n                    exports: [...importWithExport],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ActionList, CreateFormPropsFactory, CreateInjectorPipe, ENTITY_PROP_TYPE_CLASSES, EXTENSIBLE_FORM_VIEW_PROVIDER, EXTENSIONS_ACTION_CALLBACK, EXTENSIONS_ACTION_DATA, EXTENSIONS_ACTION_TYPE, EXTENSIONS_FORM_PROP, EXTENSIONS_FORM_PROP_DATA, EXTENSIONS_IDENTIFIER, EXTRA_PROPERTIES_KEY, EditFormPropsFactory, EntityAction, EntityActionList, EntityActions, EntityActionsFactory, EntityProp, EntityPropList, EntityProps, EntityPropsFactory, ExtensibleDateTimePickerComponent, ExtensibleFormComponent, ExtensibleFormPropComponent, ExtensibleModule, ExtensibleTableComponent, ExtensionsService, FormProp, FormPropData, FormPropList, FormProps, GridActionsComponent, objectExtensions as ObjectExtensions, PROP_DATA_STREAM, PageToolbarComponent, PropDataDirective, PropList, ToolbarAction, ToolbarActionList, ToolbarActions, ToolbarActionsFactory, ToolbarComponent, createExtraPropertyValueResolver, generateFormFromProps, getObjectExtensionEntitiesFromStore, mapEntitiesToContributors, mergeWithDefaultActions, mergeWithDefaultProps };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACnP,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,gBAAgB;AACzJ,OAAO,KAAKC,EAAE,MAAM,4BAA4B;AAChD,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,4BAA4B;AAC7N,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,4BAA4B,EAAEC,yBAAyB,EAAEC,iBAAiB,QAAQ,sBAAsB;AACvN,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,QAAQ,iBAAiB;AACnH,OAAO,KAAKC,IAAI,MAAM,cAAc;AACpC,SAASC,WAAW,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,2BAA2B,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,kCAAkC,EAAEC,UAAU,QAAQ,cAAc;AAC3U,SAASC,EAAE,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,MAAM;AAC3C,SAASC,GAAG,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AACjG,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,OAAO,KAAKC,IAAI,MAAM,yBAAyB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAyB;AAAC,MAAAC,GAAA;AAAA,MAAAC,UAAA,GAAAA,CAAAC,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,KAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAAC,SAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA,gBAAAD,EAAA;EAAA,UAAAC;AAAA;AAAA,SAAAC,iFAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoJuClG,EAAE,CAAAoG,kBAAA,EAqWkoB,CAAC;EAAA;AAAA;AAAA,SAAAC,kEAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWroBlG,EAAE,CAAAsG,UAAA,IAAAL,gFAAA,0BAqW2mB,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAK,MAAA,GArW9mBvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,sBAAAF,MAAA,CAAAG,IAAA,CAAAC,QAqWokB,CAAC,8BAAAJ,MAAA,CAAAK,0BAAmC,CAAC;EAAA;AAAA;AAAA,SAAAC,gFAAAX,EAAA,EAAAC,GAAA;AAAA,SAAAW,kEAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW3mBlG,EAAE,CAAAsG,UAAA,IAAAO,+EAAA,yBAqWqzB,CAAC;IArWxzB7G,EAAE,CAAA+G,SAAA,kBAqWunC,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GArW1nCvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqWozB,CAAC;IArWvzBhH,EAAE,CAAAkH,SAAA,CAqWi4B,CAAC;IArWp4BlH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWi4B,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA0C,CAAC,iBAAAb,MAAA,CAAAG,IAAA,CAAAW,YAA+C,CAAC,SAAAd,MAAA,CAAAe,OAAA,CAAAf,MAAA,CAAAG,IAAA,CAAmC,CAAC,gBAAAH,MAAA,CAAAgB,QAAqC,CAAC,aAAAhB,MAAA,CAAAiB,QAAkC,CAAC;EAAA;AAAA;AAAA,SAAAC,kEAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW5kClG,EAAE,CAAA+G,SAAA,eAqWiwC,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GArWpwCvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,oBAAAF,MAAA,CAAAG,IAAA,CAAAU,IAqW8uC,CAAC;EAAA;AAAA;AAAA,SAAAM,gFAAAxB,EAAA,EAAAC,GAAA;AAAA,SAAAwB,kEAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWjvClG,EAAE,CAAA4H,cAAA,aAqW+3C,CAAC;IArWl4C5H,EAAE,CAAA+G,SAAA,kBAqW6mD,CAAC;IArWhnD/G,EAAE,CAAAsG,UAAA,IAAAoB,+EAAA,yBAqWywD,CAAC;IArW5wD1H,EAAE,CAAA6H,YAAA,CAqWuyD,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GArW1yDvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAkH,SAAA,CAqWm8C,CAAC;IArWt8ClH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWm8C,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA4C,CAAC,gBAAAb,MAAA,CAAAgB,QAAuC,CAAC;IArW3hDvH,EAAE,CAAAkH,SAAA,EAqW+qD,CAAC;IArWlrDlH,EAAE,CAAAyG,UAAA,qBAAAO,QAqW+qD,CAAC,4BArWlrDhH,EAAE,CAAA8H,eAAA,IAAApC,GAAA,CAqW4vD,CAAC;EAAA;AAAA;AAAA,SAAAqC,gFAAA7B,EAAA,EAAAC,GAAA;AAAA,SAAA6B,wEAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW/vDlG,EAAE,CAAA4H,cAAA,gBAqW6uE,CAAC;IArWhvE5H,EAAE,CAAAiI,MAAA,EAqW2xE,CAAC;IArW9xEjI,EAAE,CAAA6H,YAAA,CAqWoyE,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAgC,SAAA,GAAA/B,GAAA,CAAAR,SAAA;IArWvyE3F,EAAE,CAAAyG,UAAA,YAAAyB,SAAA,CAAAzC,KAqW4uE,CAAC;IArW/uEzF,EAAE,CAAAkH,SAAA,CAqW2xE,CAAC;IArW9xElH,EAAE,CAAAmI,kBAAA,MAAAD,SAAA,CAAAE,GAAA,KAqW2xE,CAAC;EAAA;AAAA;AAAA,SAAAC,kEAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW9xElG,EAAE,CAAAsG,UAAA,IAAAyB,+EAAA,yBAqWk6D,CAAC;IArWr6D/H,EAAE,CAAA4H,cAAA,mBAqWynE,CAAC;IArW5nE5H,EAAE,CAAAsI,gBAAA,IAAAN,uEAAA,sBAAA1C,UAqWizE,CAAC;IArWpzEtF,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CAqWo0E,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GArWv0EvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqWi6D,CAAC;IArWp6DhH,EAAE,CAAAkH,SAAA,CAqW++D,CAAC;IArWl/DlH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqW++D,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA0C,CAAC,gBAAAb,MAAA,CAAAgB,QAAqC,CAAC;IArWnkEvH,EAAE,CAAAkH,SAAA,EAqWizE,CAAC;IArWpzElH,EAAE,CAAAwI,UAAA,CAAFxI,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAAmC,QAAA,CAqWizE,CAAC;EAAA;AAAA;AAAA,SAAAC,gFAAAzC,EAAA,EAAAC,GAAA;AAAA,SAAAyC,2EAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWpzElG,EAAE,CAAA4H,cAAA,gBAqWu1F,CAAC;IArW11F5H,EAAE,CAAAiI,MAAA,EAqWi4F,CAAC;IArWp4FjI,EAAE,CAAA6H,YAAA,CAqW04F,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA2C,SAAA,GAAA1C,GAAA,CAAAR,SAAA;IArW74F3F,EAAE,CAAAyG,UAAA,YAAAoC,SAAA,CAAApD,KAqW00F,CAAC;IArW70FzF,EAAE,CAAAkH,SAAA,CAqWi4F,CAAC;IArWp4FlH,EAAE,CAAAmI,kBAAA,MAAAU,SAAA,CAAAT,GAAA,KAqWi4F,CAAC;EAAA;AAAA;AAAA,SAAAU,kEAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWp4FlG,EAAE,CAAAsG,UAAA,IAAAqC,+EAAA,yBAqWo8E,CAAC;IArWv8E3I,EAAE,CAAA4H,cAAA,mBAqW4rF,CAAC;IArW/rF5H,EAAE,CAAAsG,UAAA,IAAAsC,0EAAA,oBAqWu1F,CAAC;IArW11F5I,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CAqW65F,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GArWh6FvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqWm8E,CAAC;IArWt8EhH,EAAE,CAAAkH,SAAA,CAqWihF,CAAC;IArWphFlH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWihF,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA0C,CAAC,gBAAAb,MAAA,CAAAgB,QAAqC,CAAC;IArWrmFvH,EAAE,CAAAkH,SAAA,EAqWswF,CAAC;IArWzwFlH,EAAE,CAAAyG,UAAA,YAAFzG,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAAmC,QAAA,CAqWswF,CAAC,iBAAAnC,MAAA,CAAAwC,KAAA,CAAAC,EAAA,SAAyB,CAAC;EAAA;AAAA;AAAA,SAAAC,gFAAA/C,EAAA,EAAAC,GAAA;AAAA,SAAA+C,kEAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiD,GAAA,GArWnyFnJ,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAAsG,UAAA,IAAA2C,+EAAA,yBAqW2hG,CAAC;IArW9hGjJ,EAAE,CAAA4H,cAAA,gBAqWgoG,CAAC,kBAA4qB,CAAC;IArWhzH5H,EAAE,CAAAqJ,gBAAA,2BAAAC,iGAAAC,MAAA;MAAFvJ,EAAE,CAAAwJ,aAAA,CAAAL,GAAA;MAAA,MAAA5C,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;MAAFxG,EAAE,CAAAyJ,kBAAA,CAAAlD,MAAA,CAAAmD,cAAA,EAAAH,MAAA,MAAAhD,MAAA,CAAAmD,cAAA,GAAAH,MAAA;MAAA,OAAFvJ,EAAE,CAAA2J,WAAA,CAAAJ,MAAA;IAAA,CAqWqjH,CAAC;IArWxjHvJ,EAAE,CAAA4J,UAAA,wBAAAC,8FAAAN,MAAA;MAAFvJ,EAAE,CAAAwJ,aAAA,CAAAL,GAAA;MAAA,MAAA5C,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CAqWmlHpD,MAAA,CAAAuD,iBAAA,CAAAP,MAAA,CAAAQ,IAA6B,CAAC;IAAA,CAAC,CAAC,kBAAAC,wFAAA;MArWrnHhK,EAAE,CAAAwJ,aAAA,CAAAL,GAAA;MAAA,MAAA5C,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CAqW0oHpD,MAAA,CAAAuD,iBAAA,CAAAvD,MAAA,CAAAmD,cAAgC,CAAC;IAAA,CAAC,CAAC;IArW/qH1J,EAAE,CAAA6H,YAAA,CAqW6yH,CAAC;IArWhzH7H,EAAE,CAAA+G,SAAA,eAqWk3H,CAAC;IArWr3H/G,EAAE,CAAA6H,YAAA,CAqWk4H,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA+D,YAAA,GArWr4HjK,EAAE,CAAAiH,WAAA;IAAA,MAAAV,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqW0hG,CAAC;IArW7hGhH,EAAE,CAAAkH,SAAA,EAqW2vH,CAAC;IArW9vHlH,EAAE,CAAAkK,WAAA,eAAAD,YAAA,CAAAE,SAAA,CAAAC,QAAA,cAqW2vH,CAAC;IArW9vHpK,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWosG,CAAC,iBAAAZ,MAAA,CAAAG,IAAA,CAAAW,YAAiD,CAAC,gBAAAd,MAAA,CAAAgB,QAAuC,CAAC,iBAAAhB,MAAA,CAAA8D,MAAsC,CAAC,kBAAiC,CAAC,mBAAA9D,MAAA,CAAA+D,kBAAoD,CAAC,oBAAA/D,MAAA,CAAA+D,kBAAqD,CAAC,mBArWr9GtK,EAAE,CAAA8H,eAAA,KAAAlC,GAAA,CAqWygH,CAAC;IArW5gH5F,EAAE,CAAAuK,gBAAA,YAAAhE,MAAA,CAAAmD,cAqWqjH,CAAC;IArWxjH1J,EAAE,CAAAkH,SAAA,EAqW+1H,CAAC;IArWl2HlH,EAAE,CAAAyG,UAAA,oBAAAF,MAAA,CAAAG,IAAA,CAAAU,IAqW+1H,CAAC;EAAA;AAAA;AAAA,SAAAoD,gFAAAtE,EAAA,EAAAC,GAAA;AAAA,SAAAsE,kEAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwE,GAAA,GArWl2H1K,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAAsG,UAAA,IAAAkE,+EAAA,yBAqW2/H,CAAC;IArW9/HxK,EAAE,CAAA4H,cAAA,kBAqWi0I,CAAC;IArWp0I5H,EAAE,CAAA4J,UAAA,mBAAAe,yFAAA;MAAF3K,EAAE,CAAAwJ,aAAA,CAAAkB,GAAA;MAAA,MAAAE,aAAA,GAAF5K,EAAE,CAAAiH,WAAA;MAAA,OAAFjH,EAAE,CAAA2J,WAAA,CAqWunIiB,aAAA,CAAAC,IAAA,CAAgB,CAAC;IAAA,CAAC,CAAC,yBAAAC,+FAAA;MArW5oI9K,EAAE,CAAAwJ,aAAA,CAAAkB,GAAA;MAAA,MAAAE,aAAA,GAAF5K,EAAE,CAAAiH,WAAA;MAAA,OAAFjH,EAAE,CAAA2J,WAAA,CAqWsqIiB,aAAA,CAAAC,IAAA,CAAgB,CAAC;IAAA,CAAC,CAAC;IArW3rI7K,EAAE,CAAA6H,YAAA,CAqWi0I,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GArWp0IvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqW0/H,CAAC;IArW7/HhH,EAAE,CAAAkH,SAAA,CAqWqjI,CAAC;IArWxjIlH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWqjI,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA0C,CAAC;EAAA;AAAA;AAAA,SAAA2D,iFAAA7E,EAAA,EAAAC,GAAA;AAAA,SAAA6E,mEAAA9E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWnmIlG,EAAE,CAAAsG,UAAA,IAAAyE,gFAAA,yBAqW07I,CAAC;IArW77I/K,EAAE,CAAA+G,SAAA,wBAqWmhJ,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GArWthJvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqWy7I,CAAC;IArW57IhH,EAAE,CAAAkH,SAAA,CAqWigJ,CAAC;IArWpgJlH,EAAE,CAAAyG,UAAA,oBAAAF,MAAA,CAAAG,IAAA,CAAAU,IAqWigJ,CAAC;EAAA;AAAA;AAAA,SAAA6D,iFAAA/E,EAAA,EAAAC,GAAA;AAAA,SAAA+E,mEAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWpgJlG,EAAE,CAAAsG,UAAA,IAAA2E,gFAAA,yBAqWgpJ,CAAC;IArWnpJjL,EAAE,CAAA+G,SAAA,yCAqW4vJ,CAAC;IArW/vJ/G,EAAE,CAAAuI,MAAA;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAK,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqW+oJ,CAAC;IArWlpJhH,EAAE,CAAAkH,SAAA,CAqWwtJ,CAAC;IArW3tJlH,EAAE,CAAAyG,UAAA,SAAAF,MAAA,CAAAG,IAqWwtJ,CAAC,aArW3tJ1G,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAA4E,SAAA,CAqWyvJ,CAAC;EAAA;AAAA;AAAA,SAAAC,iFAAAlF,EAAA,EAAAC,GAAA;AAAA,SAAAkF,mEAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW5vJlG,EAAE,CAAAsG,UAAA,IAAA8E,gFAAA,yBAqWy3J,CAAC;IArW53JpL,EAAE,CAAA+G,SAAA,qBAqWonK,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GArWvnKvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqWw3J,CAAC;IArW33JhH,EAAE,CAAAkH,SAAA,CAqWw8J,CAAC;IArW38JlH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWw8J,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA0C,CAAC,gBAAAb,MAAA,CAAAgB,QAAqC,CAAC,aAAAhB,MAAA,CAAAiB,QAAkC,CAAC;EAAA;AAAA;AAAA,SAAA8D,iFAAApF,EAAA,EAAAC,GAAA;AAAA,SAAAoF,mEAAArF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsF,GAAA,GArW/jKxL,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAAsG,UAAA,IAAAgF,gFAAA,yBAqW2vK,CAAC;IArW9vKtL,EAAE,CAAA4H,cAAA,aAqW00K,CAAC;IArW70K5H,EAAE,CAAA+G,SAAA,eAqWygL,CAAC;IArW5gL/G,EAAE,CAAA4H,cAAA,gBAqWknL,CAAC;IArWrnL5H,EAAE,CAAA4J,UAAA,mBAAA6B,2FAAA;MAAFzL,EAAE,CAAAwJ,aAAA,CAAAgC,GAAA;MAAA,MAAAjF,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CAAApD,MAAA,CAAAmF,YAAA,IAAAnF,MAAA,CAAAmF,YAAA;IAAA,CAqWinL,CAAC;IArWpnL1L,EAAE,CAAA+G,SAAA,WAqW81L,CAAC;IArWj2L/G,EAAE,CAAA6H,YAAA,CAqWm3L,CAAC,CAAe,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GArWt4LvG,EAAE,CAAAwG,aAAA;IAAA,MAAAQ,QAAA,GAAFhH,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAO,QAqW0vK,CAAC;IArW7vKhH,EAAE,CAAAkH,SAAA,EAqW85K,CAAC;IArWj6KlH,EAAE,CAAAyG,UAAA,OAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqW85K,CAAC,oBAAAZ,MAAA,CAAAG,IAAA,CAAAU,IAA4C,CAAC,oBAAAb,MAAA,CAAAmF,YAA+C,CAAC;IArW9/K1L,EAAE,CAAAkH,SAAA,EAqW20L,CAAC;IArW90LlH,EAAE,CAAAyG,UAAA,YAAFzG,EAAE,CAAA2L,eAAA,IAAA7F,GAAA,GAAAS,MAAA,CAAAmF,YAAA,EAAAnF,MAAA,CAAAmF,YAAA,CAqW20L,CAAC;EAAA;AAAA;AAAA,SAAAE,mEAAA1F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW90LlG,EAAE,CAAA4H,cAAA,eAqWw+L,CAAC;IArW3+L5H,EAAE,CAAAiI,MAAA,EAqW6gM,CAAC;IArWhhMjI,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CAqWqhM,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GArWxhMvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAkH,SAAA,CAqW6gM,CAAC;IArWhhMlH,EAAE,CAAA6L,iBAAA,CAAF7L,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAAG,IAAA,CAAAoF,QAAA,CAqW6gM,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAA7F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWhhMlG,EAAE,CAAAgM,uBAAA,KAqW6d,CAAC;IArWhehM,EAAE,CAAAsG,UAAA,IAAAD,iEAAA,wBAqW0gB,CAAC;IArW7gBrG,EAAE,CAAA4H,cAAA,YAqWqtB,CAAC;IArWxtB5H,EAAE,CAAAsG,UAAA,IAAAQ,iEAAA,wBAqWiwB,CAAC,IAAAW,iEAAA,wBAA4b,CAAC,IAAAE,iEAAA,yBAA2I,CAAC,IAAAU,iEAAA,yBAAmiB,CAAC,IAAAS,iEAAA,yBAAiiB,CAAC,IAAAI,iEAAA,0BAAslB,CAAC,IAAAuB,iEAAA,yBAA+9B,CAAC,KAAAO,kEAAA,yBAA8b,CAAC,KAAAE,kEAAA,yBAAqN,CAAC,KAAAG,kEAAA,yBAAwO,CAAC,KAAAE,kEAAA,yBAAiY,CAAC,KAAAK,kEAAA,mBAAkvB,CAAC;IArW77L5L,EAAE,CAAA6H,YAAA,CAqW0iM,CAAC;IArW7iM7H,EAAE,CAAAiM,qBAAA;EAAA;EAAA,IAAA/F,EAAA;IAAA,MAAAK,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,aAAAF,MAAA,CAAA2F,YAAA,CAAA3F,MAAA,CAAAG,IAAA,CAqWqZ,CAAC;IArWxZ1G,EAAE,CAAAkH,SAAA,EAqWqsB,CAAC;IArWxsBlH,EAAE,CAAAyG,UAAA,YAAAF,MAAA,CAAA4F,kBAqWqsB,CAAC;IArWxsBnM,EAAE,CAAAkH,SAAA,GAqW8hM,CAAC;IArWjiMlH,EAAE,CAAAoM,aAAA,KAAA7F,MAAA,CAAAG,IAAA,CAAAoF,QAAA,UAqW8hM,CAAC;EAAA;AAAA;AAAA,SAAAO,iEAAAnG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWjiMlG,EAAE,CAAAiI,MAAA,EAqW8xM,CAAC;IArWjyMjI,EAAE,CAAAuI,MAAA;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAK,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAmI,kBAAA,MAAFnI,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAAG,IAAA,CAAA4F,mBAAA,CAAA/F,MAAA,CAAAgG,IAAA,OAqW8xM,CAAC;EAAA;AAAA;AAAA,SAAAC,+EAAAtG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWjyMlG,EAAE,CAAAiI,MAAA,EAqW04M,CAAC;IArW74MjI,EAAE,CAAAuI,MAAA;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAK,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAmI,kBAAA,MAAFnI,EAAE,CAAAyI,WAAA,cAAAlC,MAAA,CAAAG,IAAA,CAAA+F,WAAA,MAqW04M,CAAC;EAAA;AAAA;AAAA,SAAAC,+EAAAxG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArW74MlG,EAAE,CAAAiI,MAAA,EAqWi9M,CAAC;IArWp9MjI,EAAE,CAAAuI,MAAA;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAK,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAmI,kBAAA,MAAFnI,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAAG,IAAA,CAAA+F,WAAA,MAqWi9M,CAAC;EAAA;AAAA;AAAA,SAAAE,iEAAAzG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWp9MlG,EAAE,CAAAsG,UAAA,IAAAkG,8EAAA,MAqWq0M,CAAC,IAAAE,8EAAA,MAA6E,CAAC;EAAA;EAAA,IAAAxG,EAAA;IAAA,MAAAK,MAAA,GArWt5MvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,IAAA7F,MAAA,CAAAG,IAAA,CAAAkG,OAAA,QAqWk9M,CAAC;EAAA;AAAA;AAAA,SAAAC,iEAAA3G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWr9MlG,EAAE,CAAA+G,SAAA,WAqWyuN,CAAC;IArW5uN/G,EAAE,CAAAuI,MAAA;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAK,MAAA,GAAFvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,eAAFzG,EAAE,CAAAyI,WAAA,OAAAlC,MAAA,CAAAG,IAAA,CAAAoG,OAAA,CAAAC,IAAA,CAqWylN,CAAC,cAAAxG,MAAA,CAAAG,IAAA,CAAAoG,OAAA,CAAAE,SAAA,UAA2D,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWxpNlG,EAAE,CAAA4H,cAAA,eAqW8qM,CAAC;IArWjrM5H,EAAE,CAAAsG,UAAA,IAAA+F,gEAAA,MAqWstM,CAAC,IAAAM,gEAAA,MAAgF,CAAC;IArW1yM3M,EAAE,CAAAiI,MAAA,EAqWy/M,CAAC;IArW5/MjI,EAAE,CAAAsG,UAAA,IAAAuG,gEAAA,eAqW6gN,CAAC;IArWhhN7M,EAAE,CAAA6H,YAAA,CAqWgwN,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAgH,WAAA,GAAA/G,GAAA,CAAAR,SAAA;IAAA,MAAAY,MAAA,GArWnwNvG,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,YAAAF,MAAA,CAAAG,IAAA,CAAAS,EAqWuoM,CAAC,YAAA+F,WAAA,gBAAqC,CAAC;IArWhrMlN,EAAE,CAAAkH,SAAA,CAqW29M,CAAC;IArW99MlH,EAAE,CAAAoM,aAAA,IAAA7F,MAAA,CAAAG,IAAA,CAAA4F,mBAAA,QAqW29M,CAAC;IArW99MtM,EAAE,CAAAkH,SAAA,EAqWy/M,CAAC;IArW5/MlH,EAAE,CAAAmI,kBAAA,MAAA5B,MAAA,CAAA4G,QAAA,KAqWy/M,CAAC;IArW5/MnN,EAAE,CAAAkH,SAAA,CAqWkvN,CAAC;IArWrvNlH,EAAE,CAAAoM,aAAA,IAAA7F,MAAA,CAAAG,IAAA,CAAAoG,OAAA,SAqWkvN,CAAC;EAAA;AAAA;AAAA,MAAAM,UAAA,GAAAA,CAAA7H,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAA4B,IAAA;AAAA,MAAAiG,GAAA,GAAAA,CAAAtH,EAAA,EAAAC,EAAA,EAAAsH,EAAA;EAAAC,WAAA,EAAAxH,EAAA;EAAAwG,IAAA,EAAAvG,EAAA;EAAAwH,YAAA,EAAAF;AAAA;AAAA,SAAAG,kFAAAvH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArWrvNlG,EAAE,CAAA4H,cAAA,YAspB+uB,CAAC;IAtpBlvB5H,EAAE,CAAAoG,kBAAA,KAspBu9B,CAAC;IAtpB19BpG,EAAE,CAAA6H,YAAA,CAspBy+B,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAwH,OAAA,GAtpB5+B1N,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAA,MAAAgI,MAAA,GAAF3N,EAAE,CAAAwG,aAAA;IAAA,MAAAoH,cAAA,GAAAD,MAAA,CAAAhI,SAAA;IAAA,MAAAkI,OAAA,GAAAF,MAAA,CAAApI,MAAA;IAAFvF,EAAE,CAAAwG,aAAA;IAAA,MAAAsH,mBAAA,GAAF9N,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,YAAAmH,cAAA,CAAAG,KAAA,kBAAAH,cAAA,CAAAG,KAAA,CAAAC,SAspBwoB,CAAC;IAtpB3oBhO,EAAE,CAAAiO,WAAA,eAAAL,cAAA,CAAAG,KAAA,kBAAAH,cAAA,CAAAG,KAAA,CAAA3G,IAAA,MAAAwG,cAAA,CAAAG,KAAA,kBAAAH,cAAA,CAAAG,KAAA,CAAAC,SAAA;IAAFhO,EAAE,CAAAkH,SAAA,CAspBi0B,CAAC;IAtpBp0BlH,EAAE,CAAAyG,UAAA,qBAAAqH,mBAspBi0B,CAAC,4BAtpBp0B9N,EAAE,CAAAkO,eAAA,IAAAb,GAAA,EAAAO,cAAA,EAAAF,OAAA,EAAAG,OAAA,OAspB26B,CAAC;EAAA;AAAA;AAAA,SAAAM,kFAAAjI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpB96BlG,EAAE,CAAAoG,kBAAA,KAspB2tC,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAwH,OAAA,GAtpB9tC1N,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAA,MAAAgI,MAAA,GAAF3N,EAAE,CAAAwG,aAAA;IAAA,MAAAoH,cAAA,GAAAD,MAAA,CAAAhI,SAAA;IAAA,MAAAkI,OAAA,GAAAF,MAAA,CAAApI,MAAA;IAAFvF,EAAE,CAAAwG,aAAA;IAAA,MAAAsH,mBAAA,GAAF9N,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAqH,mBAspB0kC,CAAC,4BAtpB7kC9N,EAAE,CAAAkO,eAAA,IAAAb,GAAA,EAAAO,cAAA,EAAAF,OAAA,EAAAG,OAAA,OAspBmrC,CAAC;EAAA;AAAA;AAAA,SAAAO,oEAAAlI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBtrClG,EAAE,CAAAgM,uBAAA,EAspB8e,CAAC;IAtpBjfhM,EAAE,CAAAsG,UAAA,IAAAmH,iFAAA,gBAspBgkB,CAAC,IAAAU,iFAAA,MAA2b,CAAC;IAtpB//BnO,EAAE,CAAAiM,qBAAA;EAAA;EAAA,IAAA/F,EAAA;IAAA,MAAAwH,OAAA,GAAAvH,GAAA,CAAAR,SAAA;IAAA,MAAAgI,MAAA,GAAF3N,EAAE,CAAAwG,aAAA;IAAA,MAAAoH,cAAA,GAAAD,MAAA,CAAAhI,SAAA;IAAA,MAAA0I,IAAA,GAAAV,MAAA,CAAApI,MAAA;IAAA,MAAA+I,MAAA,GAAFtO,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAkH,SAAA,CAspBsuC,CAAC;IAtpBzuClH,EAAE,CAAAoM,aAAA,IAAAkC,MAAA,CAAAC,uBAAA,CAAAF,IAAA,EAAAX,OAAA,MAAAE,cAAA,CAAAG,KAAA,kBAAAH,cAAA,CAAAG,KAAA,CAAAC,SAAA,SAspBsuC,CAAC;EAAA;AAAA;AAAA,SAAAQ,qDAAAtI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBzuClG,EAAE,CAAAsG,UAAA,IAAA8H,mEAAA,yBAspB8e,CAAC;EAAA;EAAA,IAAAlI,EAAA;IAAA,MAAA0H,cAAA,GAAAzH,GAAA,CAAAR,SAAA;IAAA,MAAA2I,MAAA,GAtpBjftO,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,wBAAAmH,cAAA,CAAAa,YAspByd,CAAC,0BAAAH,MAAA,CAAAI,MAAiB,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAzI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpB9elG,EAAE,CAAAsI,gBAAA,IAAAkG,oDAAA,8BAAFxO,EAAE,CAAA4O,sBAspBowC,CAAC;EAAA;EAAA,IAAA1I,EAAA;IAAA,MAAAoI,MAAA,GAtpBvwCtO,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAwI,UAAA,CAAA8F,MAAA,CAAAO,eAAA,CAAAC,KAspBowC,CAAC;EAAA;AAAA;AAAA,SAAAC,iFAAA7I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBvwClG,EAAE,CAAAgM,uBAAA,KAspBooD,CAAC;IAtpBvoDhM,EAAE,CAAA+G,SAAA,iCAspByuD,CAAC;IAtpB5uD/G,EAAE,CAAAiM,qBAAA;EAAA;EAAA,IAAA/F,EAAA;IAAA,MAAA8I,OAAA,GAAFhP,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAA,MAAAsJ,OAAA,GAAFjP,EAAE,CAAAwG,aAAA,GAAA+F,IAAA;IAAA,MAAA+B,MAAA,GAAFtO,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,kBAAA6H,MAAA,CAAAY,kBAspBmoD,CAAC;IAtpBtoDlP,EAAE,CAAAkH,SAAA,CAspBsuD,CAAC;IAtpBzuDlH,EAAE,CAAAmP,UAAA,CAAAH,OAAA,CAAAhB,SAspBsuD,CAAC;IAtpBzuDhO,EAAE,CAAAyG,UAAA,SAAAuI,OAspB2rD,CAAC,SAAAC,OAAe,CAAC;EAAA;AAAA;AAAA,SAAAG,+FAAAlJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpB9sDlG,EAAE,CAAA+G,SAAA,iCAspB6iE,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAmJ,MAAA,GAtpBhjErP,EAAE,CAAAwG,aAAA;IAAA,MAAAwI,OAAA,GAAAK,MAAA,CAAA1J,SAAA;IAAA,MAAA2J,aAAA,GAAAD,MAAA,CAAA9J,MAAA;IAAA,MAAAgK,OAAA,GAAFvP,EAAE,CAAAwG,aAAA;IAAA,MAAAyI,OAAA,GAAAM,OAAA,CAAAhD,IAAA;IAAA,MAAAiD,gBAAA,GAAAD,OAAA,CAAA/B,YAAA;IAAFxN,EAAE,CAAAmP,UAAA,CAAAH,OAAA,CAAAhB,SAspB+4D,CAAC;IAtpBl5DhO,EAAE,CAAAyG,UAAA,SAAAuI,OAspB86D,CAAC,SAAAC,OAA8B,CAAC,UAAAK,aAAA,MAAgC,CAAC,iBAAAE,gBAA8C,CAAC;EAAA;AAAA;AAAA,SAAAC,iFAAAvJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBhiElG,EAAE,CAAAsG,UAAA,IAAA8I,8FAAA,qCAspB8zD,CAAC;EAAA;EAAA,IAAAlJ,EAAA;IAAA,MAAA8I,OAAA,GAtpBj0DhP,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAA,MAAA2I,MAAA,GAAFtO,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,IAAAkC,MAAA,CAAAoB,IAAA,CAAAC,GAAA,CAAAX,OAAA,CAAA5H,IAAA,UAspB0jE,CAAC;EAAA;AAAA;AAAA,SAAAwI,mEAAA1J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpB7jElG,EAAE,CAAAsG,UAAA,IAAAyI,gFAAA,yBAspBmkD,CAAC,IAAAU,gFAAA,MAAmN,CAAC;EAAA;EAAA,IAAAvJ,EAAA;IAAA,MAAA8I,OAAA,GAtpB1xDhP,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAA,MAAA2I,MAAA,GAAFtO,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,IAAAkC,MAAA,CAAAuB,eAAA,CAAAC,QAAA,CAAAd,OAAA,CAAA5H,IAAA,SAspBqkE,CAAC;EAAA;AAAA;AAAA,SAAA2I,qDAAA7J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBxkElG,EAAE,CAAAsG,UAAA,IAAAsJ,kEAAA,MAspB8gD,CAAC;EAAA;EAAA,IAAA1J,EAAA;IAAA,MAAA8I,OAAA,GAAA7I,GAAA,CAAAR,SAAA;IAAA,MAAAsJ,OAAA,GAtpBjhDjP,EAAE,CAAAwG,aAAA,GAAA+F,IAAA;IAAFvM,EAAE,CAAAoM,aAAA,IAAA4C,OAAA,CAAAgB,OAAA,CAAAf,OAAA,UAspB8kE,CAAC;EAAA;AAAA;AAAA,SAAAgB,+CAAA/J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBjlElG,EAAE,CAAAsI,gBAAA,IAAAyH,oDAAA,oBAAA3C,UAspBqlE,CAAC;EAAA;EAAA,IAAAlH,EAAA;IAAA,MAAAgK,eAAA,GAAA/J,GAAA,CAAAoH,WAAA;IAtpBxlEvN,EAAE,CAAAwI,UAAA,CAAA0H,eAAA,CAAAzB,YAspBqlE,CAAC;EAAA;AAAA;AAAA,MAAA0B,UAAA,GAAAA,CAAA5K,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAuH,IAAA;AAAA,MAAAqD,GAAA,GAAArK,EAAA;EAAAJ,SAAA,EAAAI;AAAA;AAAA,SAAAsK,kDAAAnK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtpBxlElG,EAAE,CAAAoG,kBAAA,KA+sBwnB,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAoK,SAAA,GAAAnK,GAAA,CAAAR,SAAA;IA/sB3nB3F,EAAE,CAAAwG,aAAA;IAAA,MAAA+J,qBAAA,GAAFvQ,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAA8J,qBA+sB+gB,CAAC,4BA/sBlhBvQ,EAAE,CAAAwQ,eAAA,IAAAJ,GAAA,EAAAE,SAAA,CA+sBglB,CAAC;EAAA;AAAA;AAAA,SAAAG,4CAAAvK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBnlBlG,EAAE,CAAA4H,cAAA,YA+sBkF,CAAC,eAA8K,CAAC;IA/sBpQ5H,EAAE,CAAA+G,SAAA,UA+sB2T,CAAC;IA/sB9T/G,EAAE,CAAAiI,MAAA,EA+sB+V,CAAC;IA/sBlWjI,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CA+sBwW,CAAC;IA/sB3W7H,EAAE,CAAA4H,cAAA,YA+sBqY,CAAC;IA/sBxY5H,EAAE,CAAAsI,gBAAA,IAAA+H,iDAAA,2BAAAF,UA+sBmoB,CAAC;IA/sBtoBnQ,EAAE,CAAA6H,YAAA,CA+sBipB,CAAC,CAAW,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAwK,MAAA,GA/sBhqB1Q,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAkH,SAAA,EA+sBsT,CAAC;IA/sBzTlH,EAAE,CAAAkK,WAAA,SAAAwG,MAAA,CAAAC,IA+sBsT,CAAC;IA/sBzT3Q,EAAE,CAAAyG,UAAA,YAAAiK,MAAA,CAAAC,IA+sBgS,CAAC;IA/sBnS3Q,EAAE,CAAAkH,SAAA,CA+sB+V,CAAC;IA/sBlWlH,EAAE,CAAAmI,kBAAA,KAAFnI,EAAE,CAAAyI,WAAA,OAAAiI,MAAA,CAAA3D,IAAA,MA+sB+V,CAAC;IA/sBlW/M,EAAE,CAAAkH,SAAA,EA+sBmoB,CAAC;IA/sBtoBlH,EAAE,CAAAwI,UAAA,CAAAkI,MAAA,CAAAE,UA+sBmoB,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA3K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBtoBlG,EAAE,CAAAoG,kBAAA,KA+sBm2B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAwK,MAAA,GA/sBt2B1Q,EAAE,CAAAwG,aAAA;IAAA,MAAAsK,SAAA,GAAF9Q,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAyG,UAAA,qBAAAqK,SA+sBiwB,CAAC,4BA/sBpwB9Q,EAAE,CAAAwQ,eAAA,IAAAJ,GAAA,EAAAM,MAAA,CAAAE,UAAA,CAAAjB,GAAA,IAAAlK,KAAA,CA+sB60B,CAAC;EAAA;AAAA;AAAA,SAAAsL,kFAAA7K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBh1BlG,EAAE,CAAAoG,kBAAA,EA+sB6vC,CAAC;EAAA;AAAA;AAAA,SAAA4K,mEAAA9K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiD,GAAA,GA/sBhwCnJ,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAA4H,cAAA,gBA+sB0nC,CAAC;IA/sB7nC5H,EAAE,CAAA4J,UAAA,mBAAAqH,2FAAA;MAAFjR,EAAE,CAAAwJ,aAAA,CAAAL,GAAA;MAAA,MAAA+H,SAAA,GAAFlR,EAAE,CAAAwG,aAAA,IAAAb,SAAA;MAAA,MAAA+K,MAAA,GAAF1Q,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CA+sBokCuH,SAAA,CAAAC,MAAA,CAAAT,MAAA,CAAAnE,IAAkB,CAAC;IAAA,CAAC,CAAC;IA/sB3lCvM,EAAE,CAAAsG,UAAA,IAAAyK,iFAAA,0BA+sB8uC,CAAC;IA/sBjvC/Q,EAAE,CAAA6H,YAAA,CA+sB8wC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAgL,SAAA,GA/sBjxClR,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAwG,aAAA;IAAA,MAAA4K,mBAAA,GAAFpR,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAkH,SAAA,CA+sBmsC,CAAC;IA/sBtsClH,EAAE,CAAAyG,UAAA,qBAAA2K,mBA+sBmsC,CAAC,4BA/sBtsCpR,EAAE,CAAAwQ,eAAA,IAAAJ,GAAA,EAAAc,SAAA,CA+sBiuC,CAAC;EAAA;AAAA;AAAA,SAAAG,0DAAAnL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBpuClG,EAAE,CAAAsG,UAAA,IAAA0K,kEAAA,mBA+sB0nC,CAAC;EAAA;EAAA,IAAA9K,EAAA;IAAA,MAAAgL,SAAA,GA/sB7nClR,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,kBAAAyK,SAAA,CAAAI,UA+sBohC,CAAC,yCAAwB,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAArL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBhjClG,EAAE,CAAAsG,UAAA,IAAA+K,yDAAA,mBA+sB87B,CAAC;EAAA;EAAA,IAAAnL,EAAA;IAAA,MAAAgL,SAAA,GAAA/K,GAAA,CAAAR,SAAA;IAAA,MAAA+K,MAAA,GA/sBj8B1Q,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,IAAA8E,SAAA,CAAAlB,OAAA,CAAAU,MAAA,CAAAnE,IAAA,UA+sBqxC,CAAC;EAAA;AAAA;AAAA,SAAAiF,wEAAAtL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBxxClG,EAAE,CAAA4H,cAAA,UA+sBkgD,CAAC;IA/sBrgD5H,EAAE,CAAAiI,MAAA,EA+sBqiD,CAAC;IA/sBxiDjI,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CA+sB4iD,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAuL,SAAA,GA/sB/iDzR,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAkH,SAAA,CA+sBqiD,CAAC;IA/sBxiDlH,EAAE,CAAA6L,iBAAA,CAAF7L,EAAE,CAAAyI,WAAA,OAAAgJ,SAAA,CAAA1E,IAAA,CA+sBqiD,CAAC;EAAA;AAAA;AAAA,SAAA2E,wEAAAxL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBxiDlG,EAAE,CAAA4H,cAAA,aA+sBwlD,CAAC;IA/sB3lD5H,EAAE,CAAAiI,MAAA,EA+sB2nD,CAAC;IA/sB9nDjI,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CA+sBioD,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAuL,SAAA,GA/sBpoDzR,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAkH,SAAA,CA+sB2nD,CAAC;IA/sB9nDlH,EAAE,CAAA6L,iBAAA,CAAF7L,EAAE,CAAAyI,WAAA,OAAAgJ,SAAA,CAAA1E,IAAA,CA+sB2nD,CAAC;EAAA;AAAA;AAAA,SAAA4E,0DAAAzL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sB9nDlG,EAAE,CAAAsG,UAAA,IAAAkL,uEAAA,cA+sBk/C,CAAC,IAAAE,uEAAA,MAA0E,CAAC;EAAA;EAAA,IAAAxL,EAAA;IAAA,MAAAuL,SAAA,GA/sBhkDzR,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAF3F,EAAE,CAAAoM,aAAA,IAAAqF,SAAA,CAAAd,IAAA,QA+sB0oD,CAAC;EAAA;AAAA;AAAA,SAAAiB,4CAAA1L,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sB7oDlG,EAAE,CAAA+G,SAAA,UA+sBq7C,CAAC;IA/sBx7C/G,EAAE,CAAAsG,UAAA,IAAAqL,yDAAA,MA+sBu9C,CAAC;EAAA;EAAA,IAAAzL,EAAA;IAAA,MAAAuL,SAAA,GAAAtL,GAAA,CAAAR,SAAA;IA/sB19C3F,EAAE,CAAAkK,WAAA,SAAAuH,SAAA,CAAAd,IAAA,KAAAc,SAAA,CAAAI,YA+sBg7C,CAAC;IA/sBn7C7R,EAAE,CAAAyG,UAAA,YAAAgL,SAAA,CAAAd,IA+sB23C,CAAC;IA/sB93C3Q,EAAE,CAAAkH,SAAA,CA+sBipD,CAAC;IA/sBppDlH,EAAE,CAAAoM,aAAA,KAAAqF,SAAA,CAAAI,YAAA,SA+sBipD,CAAC;EAAA;AAAA;AAAA,SAAAC,gGAAA5L,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBppDlG,EAAE,CAAAoG,kBAAA,EA+sB6yE,CAAC;EAAA;AAAA;AAAA,SAAA2L,iFAAA7L,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsF,GAAA,GA/sBhzExL,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAA4H,cAAA,gBA+sBoqE,CAAC;IA/sBvqE5H,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA4J,UAAA,mBAAAoI,yGAAA;MAAFhS,EAAE,CAAAwJ,aAAA,CAAAgC,GAAA;MAAA,MAAAyG,UAAA,GAAFjS,EAAE,CAAAwG,aAAA,IAAAb,SAAA;MAAA,MAAA+K,MAAA,GAAF1Q,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CA+sB83DsI,UAAA,CAAAd,MAAA,CAAAT,MAAA,CAAAnE,IAAkB,CAAC;IAAA,CAAC,CAAC;IA/sBr5DvM,EAAE,CAAAsG,UAAA,IAAAwL,+FAAA,0BA+sB8xE,CAAC;IA/sBjyE9R,EAAE,CAAA6H,YAAA,CA+sBg0E,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA+L,UAAA,GA/sBn0EjS,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAwG,aAAA;IAAA,MAAA4K,mBAAA,GAAFpR,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAkS,UAAA,CAAAD,UAAA,CAAAE,QA+sB2/D,CAAC;IA/sB9/DnS,EAAE,CAAAmP,UAAA,CAAA8C,UAAA,CAAAG,QA+sBo9D,CAAC;IA/sBv9DpS,EAAE,CAAAyG,UAAA,eAAFzG,EAAE,CAAAyI,WAAA,OAAAwJ,UAAA,CAAAnF,OAAA,CAAAC,IAAA,CA+sB6jE,CAAC,cAAAkF,UAAA,CAAAnF,OAAA,CAAAE,SAAA,UAA6D,CAAC;IA/sB9nEhN,EAAE,CAAAkH,SAAA,EA+sBivE,CAAC;IA/sBpvElH,EAAE,CAAAyG,UAAA,qBAAA2K,mBA+sBivE,CAAC,4BA/sBpvEpR,EAAE,CAAAwQ,eAAA,KAAAJ,GAAA,EAAA6B,UAAA,CA+sB+wE,CAAC;EAAA;AAAA;AAAA,SAAAI,wEAAAnM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBlxElG,EAAE,CAAAsG,UAAA,IAAAyL,gFAAA,qBA+sBoqE,CAAC;EAAA;EAAA,IAAA7L,EAAA;IAAA,MAAA+L,UAAA,GA/sBvqEjS,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,kBAAAwL,UAAA,CAAAX,UA+sB40D,CAAC,yCAAwB,CAAC;EAAA;AAAA;AAAA,SAAAgB,gGAAApM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBx2DlG,EAAE,CAAAoG,kBAAA,EA+sBqtF,CAAC;EAAA;AAAA;AAAA,SAAAmM,iFAAArM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsM,IAAA,GA/sBxtFxS,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAA4H,cAAA,gBA+sB4kF,CAAC;IA/sB/kF5H,EAAE,CAAA4J,UAAA,mBAAA6I,yGAAA;MAAFzS,EAAE,CAAAwJ,aAAA,CAAAgJ,IAAA;MAAA,MAAAP,UAAA,GAAFjS,EAAE,CAAAwG,aAAA,IAAAb,SAAA;MAAA,MAAA+K,MAAA,GAAF1Q,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CA+sBo8EsI,UAAA,CAAAd,MAAA,CAAAT,MAAA,CAAAnE,IAAkB,CAAC;IAAA,CAAC,CAAC;IA/sB39EvM,EAAE,CAAAsG,UAAA,IAAAgM,+FAAA,0BA+sBssF,CAAC;IA/sBzsFtS,EAAE,CAAA6H,YAAA,CA+sBwuF,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA+L,UAAA,GA/sB3uFjS,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAwG,aAAA;IAAA,MAAA4K,mBAAA,GAAFpR,EAAE,CAAAiH,WAAA;IAAFjH,EAAE,CAAAkS,UAAA,CAAAD,UAAA,CAAAE,QA+sBikF,CAAC;IA/sBpkFnS,EAAE,CAAAmP,UAAA,CAAA8C,UAAA,CAAAG,QA+sB0hF,CAAC;IA/sB7hFpS,EAAE,CAAAkH,SAAA,CA+sBypF,CAAC;IA/sB5pFlH,EAAE,CAAAyG,UAAA,qBAAA2K,mBA+sBypF,CAAC,4BA/sB5pFpR,EAAE,CAAAwQ,eAAA,IAAAJ,GAAA,EAAA6B,UAAA,CA+sBurF,CAAC;EAAA;AAAA;AAAA,SAAAS,wEAAAxM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sB1rFlG,EAAE,CAAAsG,UAAA,IAAAiM,gFAAA,oBA+sB4kF,CAAC;EAAA;EAAA,IAAArM,EAAA;IAAA,MAAA+L,UAAA,GA/sB/kFjS,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,kBAAAwL,UAAA,CAAAX,UA+sBk5E,CAAC,yCAAwB,CAAC;EAAA;AAAA;AAAA,SAAAqB,0DAAAzM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sB96ElG,EAAE,CAAAsG,UAAA,IAAA+L,uEAAA,oBA+sB2wD,CAAC,IAAAK,uEAAA,MAAqkB,CAAC;EAAA;EAAA,IAAAxM,EAAA;IAAA,MAAA+L,UAAA,GA/sBp1EjS,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAF3F,EAAE,CAAAoM,aAAA,IAAA6F,UAAA,CAAAnF,OAAA,QA+sBivF,CAAC;EAAA;AAAA;AAAA,SAAA8F,4CAAA1M,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sBpvFlG,EAAE,CAAAsG,UAAA,IAAAqM,yDAAA,MA+sB6uD,CAAC;EAAA;EAAA,IAAAzM,EAAA;IAAA,MAAA+L,UAAA,GAAA9L,GAAA,CAAAR,SAAA;IAAA,MAAA+K,MAAA,GA/sBhvD1Q,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,IAAA6F,UAAA,CAAAjC,OAAA,CAAAU,MAAA,CAAAnE,IAAA,UA+sBwvF,CAAC;EAAA;AAAA;AAAA,MAAAsG,GAAA,GAAAA,CAAA9M,EAAA,EAAAC,EAAA;EAAAL,SAAA,EAAAI,EAAA;EAAA+M,KAAA,EAAA9M;AAAA;AAAA,SAAA+M,6EAAA7M,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/sB3vFlG,EAAE,CAAAoG,kBAAA,EA+1B0hC,CAAC;EAAA;AAAA;AAAA,SAAA4M,0FAAA9M,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1B7hClG,EAAE,CAAA+G,SAAA,yBA+1ButC,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GA/1B1tCvG,EAAE,CAAAwG,aAAA;IAAA,MAAAyM,MAAA,GAAA1M,MAAA,CAAA2M,GAAA;IAAA,MAAAC,IAAA,GAAA5M,MAAA,CAAA6M,QAAA;IAAFpT,EAAE,CAAAyG,UAAA,UAAA0M,IA+1B0pC,CAAC,WAAAF,MAAgB,CAAC;EAAA;AAAA;AAAA,SAAAI,4EAAAnN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1B9qClG,EAAE,CAAAsG,UAAA,IAAA0M,yFAAA,6BA+1B2mC,CAAC;EAAA;EAAA,IAAA9M,EAAA;IAAA,MAAA+M,MAAA,GA/1B9mCjT,EAAE,CAAAwG,aAAA,GAAA0M,GAAA;IAAA,MAAAI,MAAA,GAAFtT,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,IAAAkH,MAAA,CAAAC,gBAAA,CAAAN,MAAA,UA+1BsuC,CAAC;EAAA;AAAA;AAAA,SAAAO,8DAAAtN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1BzuClG,EAAE,CAAAsG,UAAA,IAAAyM,4EAAA,yBA+1B2gC,CAAC,IAAAM,2EAAA,gCA/1B9gCrT,EAAE,CAAAyT,sBA+1BgkC,CAAC;EAAA;EAAA,IAAAvN,EAAA;IAAA,MAAA+M,MAAA,GAAA9M,GAAA,CAAA+M,GAAA;IAAA,MAAAC,IAAA,GAAAhN,GAAA,CAAAiN,QAAA;IAAA,MAAAM,cAAA,GA/1BnkC1T,EAAE,CAAAiH,WAAA;IAAA,MAAAqM,MAAA,GAAFtT,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,qBAAA6M,MAAA,CAAAK,eAAA,IAAAD,cA+1Bu9B,CAAC,4BA/1B19B1T,EAAE,CAAA2L,eAAA,IAAAkH,GAAA,EAAAI,MAAA,EAAAE,IAAA,CA+1B4/B,CAAC;EAAA;AAAA;AAAA,SAAAS,gDAAA1N,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1B//BlG,EAAE,CAAA4H,cAAA,6BA+1BsyB,CAAC;IA/1BzyB5H,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAAsG,UAAA,IAAAkN,6DAAA,wBA+1B43B,CAAC;IA/1B/3BxT,EAAE,CAAA6H,YAAA,CA+1BuzC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAoN,MAAA,GA/1B1zCtT,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,SAAFzG,EAAE,CAAAyI,WAAA,OAAA6K,MAAA,CAAAO,WAAA,CA+1BkrB,CAAC,aAAAP,MAAA,CAAAQ,YAAA,GAAuC,CAAC,UAAAR,MAAA,CAAAQ,YAAA,GAAoC,CAAC,kBAA6B,CAAC;EAAA;AAAA;AAAA,SAAAC,2FAAA7N,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1BhyBlG,EAAE,CAAA4H,cAAA,aA+1B27D,CAAC;IA/1B97D5H,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAAiI,MAAA,EA+1B69D,CAAC;IA/1Bh+DjI,EAAE,CAAA+G,SAAA,WA+1BqhE,CAAC;IA/1BxhE/G,EAAE,CAAA6H,YAAA,CA+1B0iE,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA8N,SAAA,GA/1B7iEhU,EAAE,CAAAwG,aAAA,GAAAyN,MAAA;IAAA,MAAAC,OAAA,GAAFlU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,eAAFzG,EAAE,CAAAyI,WAAA,OAAAyL,OAAA,CAAApH,OAAA,CAAAC,IAAA,CA+1B00D,CAAC,cAAAmH,OAAA,CAAApH,OAAA,CAAAE,SAAA,UAA+D,CAAC;IA/1B74DhN,EAAE,CAAAkH,SAAA,EA+1B69D,CAAC;IA/1Bh+DlH,EAAE,CAAAmI,kBAAA,MAAA6L,SAAA,CAAA5M,IAAA,KA+1B69D,CAAC;EAAA;AAAA;AAAA,SAAA+M,2FAAAjO,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1Bh+DlG,EAAE,CAAAiI,MAAA,EA+1B0mE,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAAA,MAAA8N,SAAA,GA/1B7mEhU,EAAE,CAAAwG,aAAA,GAAAyN,MAAA;IAAFjU,EAAE,CAAAmI,kBAAA,MAAA6L,SAAA,CAAA5M,IAAA,KA+1B0mE,CAAC;EAAA;AAAA;AAAA,SAAAgN,6EAAAlO,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1B7mElG,EAAE,CAAAsG,UAAA,IAAAyN,0FAAA,iBA+1BmvD,CAAC,IAAAI,0FAAA,MAA2U,CAAC;EAAA;EAAA,IAAAjO,EAAA;IAAA,MAAAgO,OAAA,GA/1BlkElU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAoM,aAAA,IAAA8H,OAAA,CAAApH,OAAA,QA+1B2mE,CAAC;EAAA;AAAA;AAAA,SAAAuH,yHAAAnO,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoO,GAAA,GA/1B9mEtU,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAA4H,cAAA,aA+1B6yF,CAAC;IA/1BhzF5H,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA4J,UAAA,mBAAA2K,8IAAA;MAAFvU,EAAE,CAAAwJ,aAAA,CAAA8K,GAAA;MAAA,MAAAE,MAAA,GAAFxU,EAAE,CAAAwG,aAAA;MAAA,MAAAiO,OAAA,GAAAD,MAAA,CAAAtB,GAAA;MAAA,MAAAwB,KAAA,GAAAF,MAAA,CAAA1B,KAAA;MAAA,MAAAoB,OAAA,GAAFlU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;MAAA,MAAA2N,MAAA,GAAFtT,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CAAAuK,OAAA,CAAA/C,MAAA,IA+1BmmF+C,OAAA,CAAA/C,MAAA,CAAY;QAAAwD,WAAA,EAAArB,MAAA,CAAAqB,WAAA;QAAAjG,MAAA,EAAA+F,OAAA;QAAA3B,KAAA,EAAA4B;MAAkD,CAAC,CAAC;IAAA,CAAE,CAAC;IA/1BxqF1U,EAAE,CAAA6H,YAAA,CA+1BmzF,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAuO,OAAA,GA/1BtzFzU,EAAE,CAAAwG,aAAA,IAAA0M,GAAA;IAAA,MAAAgB,OAAA,GAAFlU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAA,MAAA2N,MAAA,GAAFtT,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAkK,WAAA,YAAAgK,OAAA,CAAA/C,MA+1B0xF,CAAC;IA/1B7xFnR,EAAE,CAAAyG,UAAA,cAAFzG,EAAE,CAAAyI,WAAA,OAAAgM,OAAA,OAAAP,OAAA,CAAA9M,IAAA,mBAAAqN,OAAA,OAAAP,OAAA,CAAA9M,IAAA,EAAA3B,KAAA,GAAFzF,EAAE,CAAA4U,cA+1B4gF,CAAC,YAAAtB,MAAA,CAAAuB,qBAAA,CAAAX,OAAA,CAAAY,IAAA,CAA0N,CAAC;EAAA;AAAA;AAAA,SAAAC,wIAAA7O,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1B1uFlG,EAAE,CAAAoG,kBAAA,EA+1B8jG,CAAC;EAAA;AAAA;AAAA,SAAA4O,yHAAA9O,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1BjkGlG,EAAE,CAAAsG,UAAA,IAAAyO,uIAAA,0BA+1B+iG,CAAC;EAAA;EAAA,IAAA7O,EAAA;IAAA,MAAAuO,OAAA,GA/1BljGzU,EAAE,CAAAwG,aAAA,IAAA0M,GAAA;IAAA,MAAAgB,OAAA,GAAFlU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,sBAAAgO,OAAA,OAAAP,OAAA,CAAA9M,IAAA,EAAA6N,SA+1B++F,CAAC,8BAAAR,OAAA,OAAAP,OAAA,CAAA9M,IAAA,EAAA8N,QAAsC,CAAC;EAAA;AAAA;AAAA,SAAAC,2GAAAjP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1BzhGlG,EAAE,CAAAgM,uBAAA,EA+1Bw3E,CAAC;IA/1B33EhM,EAAE,CAAAsG,UAAA,IAAA+N,wHAAA,iBA+1B+6E,CAAC,IAAAW,wHAAA,MAA4Z,CAAC;IA/1B/0FhV,EAAE,CAAAiM,qBAAA;EAAA;EAAA,IAAA/F,EAAA;IAAA,MAAAuO,OAAA,GAAFzU,EAAE,CAAAwG,aAAA,IAAA0M,GAAA;IAAA,MAAAgB,OAAA,GAAFlU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAkH,SAAA,CA+1B+kG,CAAC;IA/1BllGlH,EAAE,CAAAoM,aAAA,KAAAqI,OAAA,OAAAP,OAAA,CAAA9M,IAAA,EAAA6N,SAAA,QA+1B+kG,CAAC;EAAA;AAAA;AAAA,SAAAG,4FAAAlP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1BllGlG,EAAE,CAAAgM,uBAAA,EA+1B8yE,CAAC;IA/1BjzEhM,EAAE,CAAAsG,UAAA,IAAA6O,0GAAA,0BA+1Bw3E,CAAC;IA/1B33EnV,EAAE,CAAAiM,qBAAA;EAAA;EAAA,IAAA/F,EAAA;IAAA,MAAAuO,OAAA,GAAFzU,EAAE,CAAAwG,aAAA,GAAA0M,GAAA;IAAA,MAAAgB,OAAA,GAAFlU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAkH,SAAA,CA+1Bq3E,CAAC;IA/1Bx3ElH,EAAE,CAAAyG,UAAA,eAAAgO,OAAA,OAAAP,OAAA,CAAA9M,IAAA,mBAAAqN,OAAA,OAAAP,OAAA,CAAA9M,IAAA,EAAA4I,OA+1Bq3E,CAAC;EAAA;AAAA;AAAA,SAAAqF,6EAAAnP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1Bx3ElG,EAAE,CAAAsG,UAAA,IAAA8O,2FAAA,0BA+1B8yE,CAAC;EAAA;EAAA,IAAAlP,EAAA;IAAA,MAAAgO,OAAA,GA/1BjzElU,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,kBAAAyN,OAAA,CAAA5C,UA+1BkxE,CAAC,yCAAwB,CAAC;EAAA;AAAA;AAAA,SAAAgE,+DAAApP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1B9yElG,EAAE,CAAA4H,cAAA,6BA+1BwoD,CAAC;IA/1B3oD5H,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAAsG,UAAA,IAAA8N,4EAAA,wBA+1BmtD,CAAC,IAAAiB,4EAAA,wBAAkgB,CAAC;IA/1BztErV,EAAE,CAAA6H,YAAA,CA+1B8rG,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAqJ,OAAA,GA/1BjsGvP,EAAE,CAAAwG,aAAA;IAAA,MAAA0N,OAAA,GAAA3E,OAAA,CAAA5J,SAAA;IAAA,MAAA4P,KAAA,GAAAhG,OAAA,CAAAhK,MAAA;IAAA,MAAA+N,MAAA,GAAFtT,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,UAAA6M,MAAA,CAAAQ,YAAA,CAAAyB,KAAA,YA+1BogD,CAAC,SA/1BvgDvV,EAAE,CAAAyI,WAAA,OAAAyL,OAAA,CAAAzH,WAAA,CA+1B2jD,CAAC,SAAAyH,OAAA,CAAA9M,IAA6B,CAAC,aAAA8M,OAAA,CAAAsB,QAAqC,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAAvP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1BloDlG,EAAE,CAAAsG,UAAA,IAAAgP,8DAAA,iCA+1BwoD,CAAC;EAAA;EAAA,IAAApP,EAAA;IAAA,MAAAgO,OAAA,GAAA/N,GAAA,CAAAR,SAAA;IAAA,MAAA2N,MAAA,GA/1B3oDtT,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,eAAAyN,OAAA,CAAAwB,aAAA,CAAApC,MAAA,CAAAqB,WAAA,CA+1Bk9C,CAAC;EAAA;AAAA;AAAA,MAAAgB,UAAA,GAAAA,CAAApQ,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAyP,SAAA,IAAAzP,KAAA,CAAA2L,MAAA;AAAA,SAAAyE,8FAAA1P,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/1Br9ClG,EAAE,CAAAoG,kBAAA,EAm5BghB,CAAC;EAAA;AAAA;AAAA,SAAAyP,+EAAA3P,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn5BnhBlG,EAAE,CAAAsG,UAAA,IAAAsP,6FAAA,yBAm5BigB,CAAC;IAn5BpgB5V,EAAE,CAAAuI,MAAA;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAoK,SAAA,GAAFtQ,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAA,MAAAgI,MAAA,GAAF3N,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,sBAAAN,GAm5Bsc,CAAC,8BAn5BzcnG,EAAE,CAAA8V,WAAA,OAAAnI,MAAA,CAAAe,MAAA,EAAA4B,SAAA,EAAA3C,MAAA,CAm5Bof,CAAC;EAAA;AAAA;AAAA,SAAAoI,6FAAA7P,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8P,GAAA,GAn5BvfhW,EAAE,CAAAoJ,gBAAA;IAAFpJ,EAAE,CAAA4H,cAAA,eAm5B83B,CAAC;IAn5Bj4B5H,EAAE,CAAA4J,UAAA,mBAAAqM,qHAAA;MAAFjW,EAAE,CAAAwJ,aAAA,CAAAwM,GAAA;MAAA,MAAA1F,SAAA,GAAFtQ,EAAE,CAAAwG,aAAA,IAAAb,SAAA;MAAA,MAAAgI,MAAA,GAAF3N,EAAE,CAAAwG,aAAA;MAAA,OAAFxG,EAAE,CAAA2J,WAAA,CAm5B2pB2G,SAAA,CAAAa,MAAA,CAAAxD,MAAA,CAAApB,IAAkB,CAAC;IAAA,CAAC,CAAC;IAn5BlrBvM,EAAE,CAAA+G,SAAA,UAm5B49B,CAAC;IAn5B/9B/G,EAAE,CAAAiI,MAAA,EAm5BqiC,CAAC;IAn5BxiCjI,EAAE,CAAAuI,MAAA;IAAFvI,EAAE,CAAA6H,YAAA,CAm5B8iC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAgQ,gBAAA,GAAA/P,GAAA;IAAA,MAAAwH,MAAA,GAn5BjjC3N,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAyG,UAAA,aAAAyP,gBAAA,kBAAAA,gBAAA,CAAA9D,QAAA,IAAA8D,gBAAA,kBAAAA,gBAAA,CAAA9D,QAAA,GAAAzE,MAAA,CAAAwI,eAm5B+yB,CAAC;IAn5BlzBnW,EAAE,CAAAkH,SAAA,CAm5Bu9B,CAAC;IAn5B19BlH,EAAE,CAAAkK,WAAA,SAAAgM,gBAAA,kBAAAA,gBAAA,CAAAvF,IAm5Bu9B,CAAC;IAn5B19B3Q,EAAE,CAAAyG,UAAA,YAAAyP,gBAAA,kBAAAA,gBAAA,CAAAvF,IAm5Bk7B,CAAC;IAn5Br7B3Q,EAAE,CAAAkH,SAAA,CAm5BqiC,CAAC;IAn5BxiClH,EAAE,CAAAmI,kBAAA,MAAFnI,EAAE,CAAAyI,WAAA,OAAAyN,gBAAA,kBAAAA,gBAAA,CAAAnJ,IAAA,MAm5BqiC,CAAC;EAAA;AAAA;AAAA,SAAAqJ,+EAAAlQ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn5BxiClG,EAAE,CAAAsG,UAAA,IAAAyP,4FAAA,mBAm5B2mB,CAAC;EAAA;EAAA,IAAA7P,EAAA;IAAA,IAAAmQ,QAAA;IAAA,MAAA/F,SAAA,GAn5B9mBtQ,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAA,MAAAgI,MAAA,GAAF3N,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAoM,aAAA,KAAAiK,QAAA,GAAA1I,MAAA,CAAA2I,eAAA,CAAAhG,SAAA,EAAA7K,KAAA,YAAA4Q,QAm5B2jC,CAAC;EAAA;AAAA;AAAA,SAAAE,iEAAArQ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn5B9jClG,EAAE,CAAAgM,uBAAA,EAm5BmV,CAAC;IAn5BtVhM,EAAE,CAAAsG,UAAA,IAAAuP,8EAAA,sBAm5BmY,CAAC,IAAAO,8EAAA,MAAkK,CAAC;IAn5BziBpW,EAAE,CAAAiM,qBAAA;EAAA;EAAA,IAAA/F,EAAA;IAAA,IAAAsQ,QAAA;IAAA,MAAAlG,SAAA,GAAFtQ,EAAE,CAAAwG,aAAA,IAAAb,SAAA;IAAF3F,EAAE,CAAAkH,SAAA,CAm5BukC,CAAC;IAn5B1kClH,EAAE,CAAAoM,aAAA,KAAAoK,QAAA,GAAAlG,SAAA,CAAA2E,SAAA,WAAAuB,QAm5BukC,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAvQ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn5B1kClG,EAAE,CAAAsG,UAAA,IAAAiQ,gEAAA,yBAm5BmV,CAAC;EAAA;EAAA,IAAArQ,EAAA;IAAA,MAAAoK,SAAA,GAn5BtVtQ,EAAE,CAAAwG,aAAA,GAAAb,SAAA;IAAF3F,EAAE,CAAAyG,UAAA,kBAAA6J,SAAA,CAAAgB,UAm5BuT,CAAC,yCAAwB,CAAC;EAAA;AAAA;AAAA,SAAAoF,oCAAAxQ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn5BnVlG,EAAE,CAAA4H,cAAA,YAm5ByN,CAAC;IAn5B5N5H,EAAE,CAAAsG,UAAA,IAAAmQ,iDAAA,sBAm5B6P,CAAC;IAn5BhQzW,EAAE,CAAA6H,YAAA,CAm5BmnC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAoK,SAAA,GAAAnK,GAAA,CAAAR,SAAA;IAAA,MAAAgR,YAAA,GAAAxQ,GAAA,CAAAZ,MAAA;IAAA,MAAAqR,YAAA,GAAAzQ,GAAA,CAAA0Q,MAAA;IAAA,MAAAlJ,MAAA,GAn5BtnC3N,EAAE,CAAAwG,aAAA;IAAFxG,EAAE,CAAAkK,WAAA,SAAAyM,YAAA,KAAAC,YAAA,IAm5BwN,CAAC;IAn5B3N5W,EAAE,CAAAkH,SAAA,CAm5BumC,CAAC;IAn5B1mClH,EAAE,CAAAoM,aAAA,IAAAkE,SAAA,CAAAN,OAAA,CAAArC,MAAA,CAAApB,IAAA,UAm5BumC,CAAC;EAAA;AAAA;AAriC9sC,MAAMuK,QAAQ,SAASxU,UAAU,CAAC;AAElC,MAAMyU,QAAQ,CAAC;EACX,IAAIxK,IAAIA,CAAA,EAAG;IACP,OAAO;MACHoI,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B7B,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBpE,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL;AACJ;AACA,MAAMsI,IAAI,CAAC;EACPC,WAAWA,CAACnC,IAAI,EAAE1N,IAAI,EAAEqF,WAAW,EAAE6E,UAAU,EAAEtB,OAAO,GAAGkH,CAAC,IAAI,IAAI,EAAEtK,OAAO,GAAG,KAAK,EAAEjG,QAAQ,EAAEqH,SAAS,EAAElC,QAAQ,EAAEgB,OAAO,EAAER,mBAAmB,EAAE;IAChJ,IAAI,CAACwI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC1N,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACqF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC6E,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACtB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACpD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACqH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAClC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACR,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACG,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,IAAI,CAACrF,IAAI;EACpD;AACJ;AACA,MAAM+P,YAAY,CAAC;EACfF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,oBAAoB,GAAG,CAAC,CAAC;EAClC;EACAzH,GAAGA,CAACvI,IAAI,EAAE;IACN,IAAI,CAACgQ,oBAAoB,CAAChQ,IAAI,CAAC,GAAG,IAAI,CAACgQ,oBAAoB,CAAChQ,IAAI,CAAC,IAAI,EAAE;IACvE,OAAO,IAAI,IAAI,CAACiQ,KAAK,CAAC,IAAI,CAACD,oBAAoB,CAAChQ,IAAI,CAAC,CAAC;EAC1D;AACJ;AACA,MAAMkQ,KAAK,CAAC;EACR,IAAIC,KAAKA,CAAA,EAAG;IACR,MAAMC,QAAQ,GAAG,IAAI,IAAI,CAACH,KAAK,CAAC,CAAC;IACjC,IAAI,CAACI,YAAY,CAACC,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACH,QAAQ,CAAC,CAAC;IACzD,OAAOA,QAAQ;EACnB;EACAP,WAAWA,CAACQ,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAG,cAAcA,CAACC,kBAAkB,EAAE;IAC/B,IAAI,CAACJ,YAAY,CAACK,IAAI,CAACD,kBAAkB,CAAC;EAC9C;EACAE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACN,YAAY,CAACO,MAAM,EAC3B,IAAI,CAACP,YAAY,CAACQ,GAAG,CAAC,CAAC;EAC/B;AACJ;AAEA,MAAMC,YAAY,SAASpB,QAAQ,CAAC;AAEpC,MAAMqB,SAAS,SAASb,KAAK,CAAC;EAC1BL,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGa,YAAY;EAC7B;AACJ;AACA,MAAMG,mBAAmB,CAAC;EACtBpB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnI,KAAK,GAAG,EAAE;IACf,IAAI,CAACwJ,KAAK,GAAG,CAAC;EAClB;EACAC,OAAOA,CAACxO,IAAI,EAAE;IACV,MAAMyO,SAAS,GAAGzO,IAAI,CAACgE,KAAK,EAAE3G,IAAI;IAClC,IAAI2G,KAAK,GAAG,IAAI,CAACe,KAAK,CAAC2J,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3K,KAAK,EAAE3G,IAAI,KAAKoR,SAAS,CAAC;IAC7D,IAAIzK,KAAK,EAAE;MACPA,KAAK,CAACU,YAAY,CAACkK,OAAO,CAAC5O,IAAI,CAAC;IACpC,CAAC,MACI;MACDgE,KAAK,GAAG;QACJU,YAAY,EAAE,IAAIyJ,YAAY,CAAC,CAAC;QAChCnK,KAAK,EAAEhE,IAAI,CAACgE,KAAK,IAAI;UAAE3G,IAAI,EAAE,UAAU,IAAI,CAACkR,KAAK,EAAE,EAAE;UAAEtK,SAAS,EAAEjE,IAAI,CAACgE,KAAK,EAAEC;QAAU;MAC5F,CAAC;MACDD,KAAK,CAACU,YAAY,CAACmK,OAAO,CAAC7O,IAAI,CAAC;MAChC,IAAI,CAAC+E,KAAK,CAACgJ,IAAI,CAAC/J,KAAK,CAAC;IAC1B;EACJ;AACJ;AACA,MAAM8K,sBAAsB,SAAS1B,YAAY,CAAC;EAC9CF,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGc,SAAS;EAC1B;AACJ;AACA,MAAMW,oBAAoB,SAAS3B,YAAY,CAAC;EAC5CF,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGc,SAAS;EAC1B;AACJ;AACA,MAAMY,QAAQ,SAAS/B,IAAI,CAAC;EACxBC,WAAWA,CAAC+B,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAClE,IAAI,EAAEkE,OAAO,CAAC5R,IAAI,EAAE4R,OAAO,CAACvM,WAAW,IAAI,EAAE,EAAEuM,OAAO,CAAC1H,UAAU,IAAI,EAAE,EAAE0H,OAAO,CAAChJ,OAAO,EAAEgJ,OAAO,CAACpM,OAAO,EAAEoM,OAAO,CAACrS,QAAQ,EAAEqS,OAAO,CAAChL,SAAS,EAAEgL,OAAO,CAAClN,QAAQ,EAAEkN,OAAO,CAAClM,OAAO,CAAC;IAChM,IAAI,CAACiB,KAAK,GAAGiL,OAAO,CAACjL,KAAK;IAC1B,IAAI,CAACC,SAAS,GAAGgL,OAAO,CAAChL,SAAS;IAClC,IAAI,CAAClC,QAAQ,GAAGkN,OAAO,CAAClN,QAAQ;IAChC,IAAI,CAACgB,OAAO,GAAGkM,OAAO,CAAClM,OAAO;IAC9B,IAAI,CAACmM,eAAe,GAAGD,OAAO,CAACC,eAAe,KAAK/B,CAAC,IAAI,EAAE,CAAC;IAC3D,IAAI,CAACgC,UAAU,GAAGF,OAAO,CAACE,UAAU,KAAKhC,CAAC,IAAI,EAAE,CAAC;IACjD,IAAI,CAAC3P,QAAQ,GAAGyR,OAAO,CAACzR,QAAQ,KAAK2P,CAAC,IAAI,KAAK,CAAC;IAChD,IAAI,CAAC1P,QAAQ,GAAGwR,OAAO,CAACxR,QAAQ,KAAK0P,CAAC,IAAI,KAAK,CAAC;IAChD,IAAI,CAAC7P,YAAY,GAAG2R,OAAO,CAAC3R,YAAY,IAAI,KAAK;IACjD,IAAI,CAAC2R,OAAO,GAAGA,OAAO,CAACA,OAAO;IAC9B,IAAI,CAAC7R,EAAE,GAAG6R,OAAO,CAAC7R,EAAE,IAAI6R,OAAO,CAAC5R,IAAI;IACpC,MAAM+R,YAAY,GAAGH,OAAO,CAACG,YAAY;IACzC,IAAI,CAACA,YAAY,GAAGC,YAAY,CAACD,YAAY,CAAC,GAAGA,YAAY,GAAGA,YAAY,IAAI,EAAE;IAClF,IAAI,CAAC7M,mBAAmB,GAAG0M,OAAO,CAAC1M,mBAAmB;EAC1D;EACA,OAAO+M,MAAMA,CAACL,OAAO,EAAE;IACnB,OAAO,IAAID,QAAQ,CAACC,OAAO,CAAC;EAChC;EACA,OAAOM,UAAUA,CAACC,cAAc,EAAE;IAC9B,OAAOA,cAAc,CAAC5U,GAAG,CAACoU,QAAQ,CAACM,MAAM,CAAC;EAC9C;AACJ;AACA,MAAMG,YAAY,SAASzC,QAAQ,CAAC;EAChCE,WAAWA,CAAC/B,QAAQ,EAAExG,MAAM,EAAE;IAC1B,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACiG,WAAW,GAAGO,QAAQ,CAACvF,GAAG,CAAC8J,IAAI,CAACvE,QAAQ,CAAC;EAClD;AACJ;AACA,SAASkE,YAAYA,CAACD,YAAY,EAAE;EAChC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAACO,OAAO,CAACP,YAAY,CAAC,GAAG,CAAC,CAAC;AACpD;AAEA,SAASQ,WAAWA,CAACC,UAAU,EAAE;EAC7B,OAAOA,UAAU;AACrB;AAEA,MAAMC,iCAAiC,CAAC;EACpC5C,WAAWA,CAAC6C,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,OAAOA,CAACC,OAAO,EAAE;IACb,IAAI,CAACC,IAAI,CAACC,UAAU,CAACF,OAAO,CAAC;EACjC;EACAG,OAAOA,CAACH,OAAO,EAAE;IACb,IAAI,CAACI,IAAI,CAACF,UAAU,CAACF,OAAO,CAAC;EACjC;EACA;IAAS,IAAI,CAACK,IAAI,YAAAC,0CAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFX,iCAAiC,EAA3C7Z,EAAE,CAAAya,iBAAA,CAA2Dza,EAAE,CAACW,iBAAiB;IAAA,CAA4C;EAAE;EAC/N;IAAS,IAAI,CAAC+Z,IAAI,kBAD8E1a,EAAE,CAAA2a,iBAAA;MAAA7F,IAAA,EACJ+E,iCAAiC;MAAAe,SAAA;MAAAC,SAAA,WAAAC,wCAAA5U,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD/BlG,EAAE,CAAA+a,WAAA,CAC8MnZ,kBAAkB;UADlO5B,EAAE,CAAA+a,WAAA,CACuSlZ,aAAa;QAAA;QAAA,IAAAqE,EAAA;UAAA,IAAA8U,EAAA;UADtThb,EAAE,CAAAib,cAAA,CAAAD,EAAA,GAAFhb,EAAE,CAAAkb,WAAA,QAAA/U,GAAA,CAAA+T,IAAA,GAAAc,EAAA,CAAAG,KAAA;UAAFnb,EAAE,CAAAib,cAAA,CAAAD,EAAA,GAAFhb,EAAE,CAAAkb,WAAA,QAAA/U,GAAA,CAAAkU,IAAA,GAAAW,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,MAAA;QAAA1U,IAAA;QAAAqT,QAAA;MAAA;MAAAsB,QAAA;MAAAxV,UAAA;MAAAyV,QAAA,GAAFtb,EAAE,CAAAub,kBAAA,KAmBw3C,CACl9C;QACIC,OAAO,EAAEna,gBAAgB;QACzBoa,UAAU,EAAE9B,WAAW;QACvB+B,IAAI,EAAE,CAAC,CAAC,IAAIzb,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEmB,gBAAgB,CAAC;MAC7D,CAAC,EACD;QACIma,OAAO,EAAExZ,cAAc;QACvB2Z,QAAQ,EAAEpZ;MACd,CAAC,EACD;QACIiZ,OAAO,EAAEvZ,cAAc;QACvB0Z,QAAQ,EAAEpZ;MACd,CAAC,CACJ,GAjC2FvC,EAAE,CAAA4b,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApV,QAAA,WAAAqV,2CAAA9V,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA+V,GAAA,GAAFjc,EAAE,CAAAoJ,gBAAA;UAAFpJ,EAAE,CAAA4H,cAAA,iBAYjG,CAAC;UAZ8F5H,EAAE,CAAA4J,UAAA,2BAAAsS,0EAAA3S,MAAA;YAAFvJ,EAAE,CAAAwJ,aAAA,CAAAyS,GAAA;YAAA,OAAFjc,EAAE,CAAA2J,WAAA,CAK/ExD,GAAA,CAAAiU,OAAA,CAAA7Q,MAAc,CAAC;UAAA,EAAC,mBAAA4S,kEAAA;YAL6Dnc,EAAE,CAAAwJ,aAAA,CAAAyS,GAAA;YAAA,MAAAG,aAAA,GAAFpc,EAAE,CAAAiH,WAAA;YAAA,OAAFjH,EAAE,CAAA2J,WAAA,CAMvFyS,aAAA,CAAAvR,IAAA,CAAgB,CAAC;UAAA,EAAC,yBAAAwR,wEAAA;YANmErc,EAAE,CAAAwJ,aAAA,CAAAyS,GAAA;YAAA,MAAAG,aAAA,GAAFpc,EAAE,CAAAiH,WAAA;YAAA,OAAFjH,EAAE,CAAA2J,WAAA,CAOjFyS,aAAA,CAAAvR,IAAA,CAAgB,CAAC;UAAA,EAAC;UAP6D7K,EAAE,CAAA6H,YAAA,CAYjG,CAAC;UAZ8F7H,EAAE,CAAA4H,cAAA,0BAkBlG,CAAC;UAlB+F5H,EAAE,CAAA4J,UAAA,2BAAA0S,mFAAA/S,MAAA;YAAFvJ,EAAE,CAAAwJ,aAAA,CAAAyS,GAAA;YAAA,OAAFjc,EAAE,CAAA2J,WAAA,CAgB/ExD,GAAA,CAAA6T,OAAA,CAAAzQ,MAAc,CAAC;UAAA,EAAC;UAhB6DvJ,EAAE,CAAA6H,YAAA,CAkBjF,CAAC;QAAA;QAAA,IAAA3B,EAAA;UAlB8ElG,EAAE,CAAAyG,UAAA,OAAAN,GAAA,CAAAO,IAAA,CAAAS,EAGnF,CAAC,oBAAAhB,GAAA,CAAAO,IAAA,CAAAU,IACc,CAAC;UAJiEpH,EAAE,CAAAkH,SAAA,EAepE,CAAC;UAfiElH,EAAE,CAAAyG,UAAA,oBAAAN,GAAA,CAAAO,IAAA,CAAAU,IAepE,CAAC,aAAAjB,GAAA,CAAA4T,QAET,CAAC;QAAA;MAAA;MAAAwC,YAAA,GAEmCtZ,YAAY,EAA8BnB,mBAAmB,EAA+BH,EAAE,CAACC,kBAAkB,EAAweR,mBAAmB,EAA+BD,EAAE,CAACqb,oBAAoB,EAAyPrb,EAAE,CAACsb,eAAe,EAAsFtb,EAAE,CAACub,eAAe,EAA6I3a,mBAAmB,EAA+BJ,EAAE,CAACE,aAAa;MAAA8a,aAAA;MAAAC,eAAA;IAAA,EAcluC;EAAE;AAClE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnCoG7c,EAAE,CAAA8c,iBAAA,CAmCXjD,iCAAiC,EAAc,CAAC;IAC/H/E,IAAI,EAAE3U,SAAS;IACf4c,IAAI,EAAE,CAAC;MACC1B,QAAQ,EAAE,6BAA6B;MACvCxV,UAAU,EAAE,IAAI;MAChBmX,OAAO,EAAE,CAAC/Z,YAAY,EAAEnB,mBAAmB,EAAEV,mBAAmB,EAAEW,mBAAmB,CAAC;MACtFkb,QAAQ,EAAE,iCAAiC;MAC3CtW,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBiW,eAAe,EAAExc,uBAAuB,CAAC8c,MAAM;MAC/CC,aAAa,EAAE,CACX;QACI3B,OAAO,EAAEna,gBAAgB;QACzBoa,UAAU,EAAE9B,WAAW;QACvB+B,IAAI,EAAE,CAAC,CAAC,IAAIzb,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEmB,gBAAgB,CAAC;MAC7D,CAAC,EACD;QACIma,OAAO,EAAExZ,cAAc;QACvB2Z,QAAQ,EAAEpZ;MACd,CAAC,EACD;QACIiZ,OAAO,EAAEvZ,cAAc;QACvB0Z,QAAQ,EAAEpZ;MACd,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEuS,IAAI,EAAE9U,EAAE,CAACW;EAAkB,CAAC,CAAC,EAAkB;IAAE+F,IAAI,EAAE,CAAC;MAC7EoO,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAE0Z,QAAQ,EAAE,CAAC;MACXjF,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAE6Z,IAAI,EAAE,CAAC;MACPpF,IAAI,EAAExU,SAAS;MACfyc,IAAI,EAAE,CAACnb,kBAAkB;IAC7B,CAAC,CAAC;IAAEyY,IAAI,EAAE,CAAC;MACPvF,IAAI,EAAExU,SAAS;MACfyc,IAAI,EAAE,CAAClb,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMub,qBAAqB,GAAG,IAAI7c,cAAc,CAAC,uBAAuB,CAAC;AACzE,MAAM8c,sBAAsB,GAAG,IAAI9c,cAAc,CAAC,wBAAwB,CAAC;AAC3E,MAAM+c,sBAAsB,GAAG,IAAI/c,cAAc,CAAC,wBAAwB,CAAC;AAC3E,MAAMgd,0BAA0B,GAAG,IAAIhd,cAAc,CAAC,wBAAwB,CAAC;AAC/E,MAAMid,gBAAgB,GAAG,IAAIjd,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAMkd,wBAAwB,GAAG,IAAIld,cAAc,CAAC,0BAA0B,EAAE;EAC5Emd,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;AACtB,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAG,IAAIpd,cAAc,CAAC,sBAAsB,CAAC;AACvE,MAAMqd,yBAAyB,GAAG,IAAIrd,cAAc,CAAC,2BAA2B,CAAC;AAEjF,MAAMsd,oBAAoB,GAAG,iBAAiB;AAE9C,MAAMC,qBAAqB,GAAG,OAAO;AACrC,MAAMC,2BAA2B,GAAG,QAAQ;AAC5C,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAO,CAAC1R,IAAI,EAAE2R,UAAU,KAAKA,UAAU,IAAI3R,IAAI,GACzCA,IAAI,CACDoI,WAAW,CAACnR,WAAW,CAAC,CACxB2a,OAAO,CAAC;IACTC,MAAM,EAAE,KAAK;IACbC,GAAG,EAAEJ,MAAM,CAACI,GAAG,IAAI,EAAE;IACrBC,MAAM,EAAE;MACJ,CAACL,MAAM,CAACM,eAAe,IAAI,EAAE,GAAGL;IACpC;EACJ,CAAC,EAAE;IAAEM,OAAO,EAAE;EAAU,CAAC,CAAC,CACrB/Z,IAAI,CAACE,GAAG,CAAE8Z,QAAQ,IAAK;IACxB,MAAMC,IAAI,GAAGD,QAAQ,CAACR,MAAM,CAACU,sBAAsB,IAAI,EAAE,CAAC;IAC1D,MAAMC,WAAW,GAAI7U,IAAI,KAAM;MAC3B3B,GAAG,EAAE2B,IAAI,CAACkU,MAAM,CAACY,mBAAmB,IAAI,EAAE,CAAC;MAC3CpZ,KAAK,EAAEsE,IAAI,CAACkU,MAAM,CAACa,iBAAiB,IAAI,EAAE;IAC9C,CAAC,CAAC;IACF,OAAOJ,IAAI,CAAC/Z,GAAG,CAACia,WAAW,CAAC;EAChC,CAAC,CAAC,CAAC,GACDra,EAAE,CAAC,EAAE,CAAC;AAChB;AACA,SAASwa,gBAAgBA,CAACd,MAAM,EAAE7W,IAAI,EAAE;EACpC,IAAI,CAAC,CAAC6W,MAAM,CAACI,GAAG,EAAE;IACd,OAAO,WAAW,CAAC;EACvB,CAAC,MACI;IACD,OAAOjX,IAAI,CAAC4X,QAAQ,CAAClB,qBAAqB,CAAC,GAAG,QAAQ,CAAC,yBAAyBmB,SAAS;EAC7F;AACJ;AACA,SAASC,mCAAmCA,CAACC,sBAAsB,EAAEC,UAAU,EAAE;EAC7E,OAAO,CAAC3S,WAAW,EAAE4S,QAAQ,KAAK;IAC9B,MAAMjY,IAAI,GAAGkY,yBAAyB,CAACD,QAAQ,CAACjY,IAAI,IAAI,EAAE,CAAC;IAC3D,OAAO+X,sBAAsB,CAAC1S,WAAW,IAAI2S,UAAU,CAAChY,IAAI,CAAC,CAACqF,WAAW,EAAE;MACvErF,IAAI;MACJmY,QAAQ,EAAEF,QAAQ,CAACE;IACvB,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASC,sBAAsBA,CAACpY,IAAI,EAAE;EAClC,OAAOA,IAAI,GAAG0W,qBAAqB;AACvC;AACA,SAAS2B,sBAAsBA,CAACrY,IAAI,EAAE;EAClC,OAAO2W,2BAA2B,CAAC2B,IAAI,CAACtY,IAAI,CAAC;AACjD;AACA,SAASkY,yBAAyBA,CAAClY,IAAI,EAAE;EACrC,OAAOA,IAAI,CAACuY,OAAO,CAAC5B,2BAA2B,EAAE,EAAE,CAAC;AACxD;AAEA,MAAM6B,yBAAyB,CAAC;EAC5B3I,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,CAAC4I,kBAAkB,GAAGrf,MAAM,CAACiD,kBAAkB,CAAC;IACrD,IAAI,CAAC0H,SAAS,GAAG,IAAI,CAAC,CAAC0U,kBAAkB,CACpCC,QAAQ,CAAC,6DAA6D,CAAC,CACvErb,IAAI,CAACE,GAAG,CAAEob,gBAAgB,IAAK,CAACA,gBAAgB,IAAI,EAAE,EAAEC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACjF;EACA,CAACH,kBAAkB;EACnBI,UAAUA,CAACC,SAAS,EAAE;IAClB,OAAQA,SAAS,KAAK5e,UAAU,CAAC6e,QAAQ,IACrCD,SAAS,KAAKxc,aAAa,CAACyc,QAAQ,IACpCD,SAAS,CAAC9Y,IAAI,KAAK,UAAU;EACrC;EACA8E,YAAYA,CAACxF,IAAI,EAAE;IACf,IAAIA,IAAI,CAACC,QAAQ,EAAE;MACf,OAAO,UAAU;IACrB;IACA,QAAQD,IAAI,CAACoO,IAAI;MACb,KAAK,SAAS,CAAC;QACX,OAAO,UAAU;MACrB,KAAK,MAAM,CAAC;QACR,OAAO,MAAM;MACjB,KAAK,UAAU,CAAC;QACZ,OAAO,UAAU;MACrB,KAAK,QAAQ,CAAC;QACV,OAAO,QAAQ;MACnB,KAAK,aAAa,CAAC;QACf,OAAO,aAAa;MACxB,KAAK,MAAM,CAAC;QACR,OAAO,UAAU;MACrB,KAAK,MAAM,CAAC;QACR,OAAO,MAAM;MACjB,KAAK,WAAW,CAAC;QACb,OAAO,WAAW;MACtB,KAAK,oBAAoB,CAAC;QACtB,OAAO,oBAAoB;MAC/B;QACI,OAAOpO,IAAI,CAACsS,OAAO,GAAG,QAAQ,GAAG,OAAO;IAChD;EACJ;EACA1R,OAAOA,CAACZ,IAAI,EAAE;IACV,QAAQA,IAAI,CAACoO,IAAI;MACb,KAAK,MAAM,CAAC;MACZ,KAAK,QAAQ,CAAC;QACV,OAAO,MAAM;MACjB,KAAK,SAAS,CAAC;QACX,OAAO,UAAU;MACrB,KAAK,QAAQ,CAAC;QACV,OAAO,QAAQ;MACnB,KAAK,OAAO,CAAC;QACT,OAAO,OAAO;MAClB,KAAK,UAAU,CAAC;QACZ,OAAO,UAAU;MACrB,KAAK,oBAAoB,CAAC;QACtB,OAAO,oBAAoB;MAC/B;QACI,OAAO,QAAQ;IACvB;EACJ;EACAsL,aAAaA,CAAClH,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,EACX,OAAO,EAAE;IACb,MAAMiH,QAAQ,GAAGjH,UAAU,CAACT,IAAI,CAAC4H,CAAC,IAAI,IAAI,CAACJ,UAAU,CAACI,CAAC,CAAC,CAAC;IACzD,OAAOF,QAAQ,GAAG,GAAG,GAAG,EAAE;EAC9B;EACA;IAAS,IAAI,CAAC7F,IAAI,YAAAgG,kCAAA9F,CAAA;MAAA,YAAAA,CAAA,IAAwFoF,yBAAyB;IAAA,CAAoD;EAAE;EACzL;IAAS,IAAI,CAACW,KAAK,kBA3N6EvgB,EAAE,CAAAwgB,kBAAA;MAAAC,KAAA,EA2NYb,yBAAyB;MAAAlC,OAAA,EAAzBkC,yBAAyB,CAAAtF;IAAA,EAAG;EAAE;AAChJ;AACA;EAAA,QAAAuC,SAAA,oBAAAA,SAAA,KA7NoG7c,EAAE,CAAA8c,iBAAA,CA6NX8C,yBAAyB,EAAc,CAAC;IACvH9K,IAAI,EAAErU;EACV,CAAC,CAAC;AAAA;AAEV,MAAMigB,kBAAkB,CAAC;EACrBC,SAASA,CAACzJ,CAAC,EAAE/F,MAAM,EAAEyP,OAAO,EAAE;IAC1B,MAAMjR,GAAG,GAAGA,CAAC8Q,KAAK,EAAEI,aAAa,EAAE7H,OAAO,KAAK;MAC3C,MAAM8H,aAAa,GAAGF,OAAO,CAACG,OAAO,CAAC,CAAC;MACvC,MAAMC,qBAAqB,GAAIzU,IAAI,IAAK;QACpCA,IAAI,GAAGA,IAAI,IAAIqU,OAAO,CAACG,OAAO,CAAC,CAAC;QAChC,OAAO5P,MAAM,CAACA,MAAM,CAAC5E,IAAI,CAAC;MAC9B,CAAC;MACD,IAAI0U,aAAa;MACjB,QAAQR,KAAK;QACT,KAAKnD,sBAAsB;UACvB2D,aAAa,GAAGH,aAAa;UAC7B;QACJ,KAAKvD,0BAA0B;UAC3B0D,aAAa,GAAGD,qBAAqB;UACrC;QACJ;UACIC,aAAa,GAAGL,OAAO,CAACjM,WAAW,CAACuM,IAAI,CAACN,OAAO,CAAC1L,QAAQ,EAAEuL,KAAK,EAAEI,aAAa,EAAE7H,OAAO,CAAC;MACjG;MACA,OAAOiI,aAAa;IACxB,CAAC;IACD,OAAO;MAAEtR;IAAI,CAAC;EAClB;EACA;IAAS,IAAI,CAAC2K,IAAI,YAAA6G,2BAAA3G,CAAA;MAAA,YAAAA,CAAA,IAAwFkG,kBAAkB;IAAA,CAA8C;EAAE;EAC5K;IAAS,IAAI,CAACU,KAAK,kBAzP6EphB,EAAE,CAAAqhB,YAAA;MAAAja,IAAA;MAAA0N,IAAA,EAyPM4L,kBAAkB;MAAAY,IAAA;MAAAzb,UAAA;IAAA,EAA+C;EAAE;AAC/K;AACA;EAAA,QAAAgX,SAAA,oBAAAA,SAAA,KA3PoG7c,EAAE,CAAA8c,iBAAA,CA2PX4D,kBAAkB,EAAc,CAAC;IAChH5L,IAAI,EAAEpU,IAAI;IACVqc,IAAI,EAAE,CAAC;MACC3V,IAAI,EAAE,gBAAgB;MACtBvB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM0b,2BAA2B,CAAC;EAC9BtK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuK,OAAO,GAAGhhB,MAAM,CAACof,yBAAyB,CAAC;IAChD,IAAI,CAAC9F,KAAK,GAAGtZ,MAAM,CAACG,iBAAiB,CAAC;IACtC,IAAI,CAACoI,KAAK,GAAGvI,MAAM,CAACmD,cAAc,CAAC;IACnC,IAAI,CAAC,CAAC8d,cAAc,GAAGjhB,MAAM,CAACe,kBAAkB,CAAC;IACjD,IAAI,CAAC2T,QAAQ,GAAG1U,MAAM,CAACI,QAAQ,CAAC;IAChC,IAAI,CAAC8O,IAAI,GAAG,IAAI,CAAC,CAAC+R,cAAc,CAAC/R,IAAI;IACrC,IAAI,CAACvC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAChB,kBAAkB,GAAG,MAAM;IAChC,IAAI,CAACT,YAAY,GAAG,KAAK;IACzB,IAAI,CAAChD,QAAQ,GAAGnE,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,CAAC2U,UAAU,GAAG,EAAE;IACpB,IAAI,CAACwI,WAAW,GAAG,0CAA0C,CAAC;IAC9D,IAAI,CAACC,UAAU,GAAIpV,IAAI,IAAK,KAAK;IACjC,IAAI,CAAClC,MAAM,GAAIuX,KAAK,IAAKA,KAAK,GACxBA,KAAK,CAACnd,IAAI,CAACG,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,CAAC,CAAC,EAAEC,SAAS,CAACiI,IAAI,IAAI,IAAI,CAACrG,IAAI,EAAEsS,OAAO,GAAG,IAAI,CAACzM,IAAI,EAAEQ,IAAI,CAAC,IAAIxI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GACzHA,EAAE,CAAC,EAAE,CAAC;IACZ,IAAI,CAAC+F,kBAAkB,GAAIuX,MAAM,IAAKA,MAAM,CAACzZ,GAAG;IAChD,IAAI,CAAC+C,SAAS,GAAG,IAAI,CAACqW,OAAO,CAACrW,SAAS;EAC3C;EACA,CAACsW,cAAc;EACf,IAAIla,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoa,UAAU,CAAC,IAAI,CAACpV,IAAI,CAAC;EACrC;EACAzC,iBAAiBA,CAACgY,cAAc,EAAE;IAC9B,IAAI,CAACpY,cAAc,GAAGoY,cAAc,IAAI;MAAE1Z,GAAG,EAAE,IAAI;MAAE3C,KAAK,EAAE;IAAK,CAAC;IAClE,MAAM;MAAE2C,GAAG;MAAE3C;IAAM,CAAC,GAAG,IAAI,CAACiE,cAAc;IAC1C,MAAM,CAACqY,UAAU,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC9D,IAAID,YAAY,EAAEvc,KAAK,IAAI,CAACA,KAAK,EAC7Buc,YAAY,CAACE,WAAW,CAAC,CAAC;IAC9BH,UAAU,EAAEI,QAAQ,CAAC/Z,GAAG,CAAC;IACzB4Z,YAAY,EAAEG,QAAQ,CAAC1c,KAAK,CAAC;EACjC;EACA,IAAI2c,SAASA,CAAA,EAAG;IACZ,MAAMC,OAAO,GAAG,IAAI,CAAC3S,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjJ,IAAI,CAACU,IAAI,CAAC;IAC7C,OAAOib,OAAO,EAAEC,OAAO,IAAID,OAAO,CAACE,OAAO;EAC9C;EACAN,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAE7a;IAAK,CAAC,GAAG,IAAI,CAACV,IAAI;IAC1B,MAAM8b,aAAa,GAAG,GAAG3E,oBAAoB,IAAIzW,IAAI,EAAE;IACvD,MAAM2a,UAAU,GAAG,IAAI,CAACrS,IAAI,CAACC,GAAG,CAAC6P,sBAAsB,CAACgD,aAAa,CAAC,CAAC,IACnE,IAAI,CAAC9S,IAAI,CAACC,GAAG,CAAC6P,sBAAsB,CAACpY,IAAI,CAAC,CAAC;IAC/C,MAAM4a,YAAY,GAAG,IAAI,CAACtS,IAAI,CAACC,GAAG,CAAC6S,aAAa,CAAC,IAAI,IAAI,CAAC9S,IAAI,CAACC,GAAG,CAACvI,IAAI,CAAC;IACxE,OAAO,CAAC2a,UAAU,EAAEC,YAAY,CAAC;EACrC;EACAS,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtV,QAAQ,GAAG,IAAI,CAACqU,OAAO,CAACpB,aAAa,CAAC,IAAI,CAAClH,UAAU,CAAC;EAC/D;EACAwJ,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAClV,YAAY,IAAI,IAAI,CAAC2N,KAAK,IAAI,IAAI,CAACwH,QAAQ,EAAE;MAClD,IAAI,CAACA,QAAQ,CAACC,aAAa,CAACC,KAAK,CAAC,CAAC;IACvC;EACJ;EACA3W,YAAYA,CAACxF,IAAI,EAAE;IACf,OAAO,IAAI,CAAC8a,OAAO,CAACtV,YAAY,CAACxF,IAAI,CAAC;EAC1C;EACAY,OAAOA,CAACZ,IAAI,EAAE;IACV,OAAO,IAAI,CAAC8a,OAAO,CAACla,OAAO,CAACZ,IAAI,CAAC;EACrC;EACAoc,WAAWA,CAAC;IAAEpc,IAAI;IAAE6F;EAAK,CAAC,EAAE;IACxB,MAAMwW,WAAW,GAAGrc,IAAI,EAAEsc,YAAY;IACtC,MAAM;MAAEhK,OAAO;MAAExR,QAAQ;MAAED,QAAQ;MAAE2R,UAAU;MAAElL,SAAS;MAAErH;IAAS,CAAC,GAAGoc,WAAW,IAAI,CAAC,CAAC;IAC1F,IAAIpc,QAAQ,EAAE;MACV,IAAI,CAACC,0BAA0B,GAAGhG,QAAQ,CAACyY,MAAM,CAAC;QAC9C4J,SAAS,EAAE,CACP;UACIzH,OAAO,EAAEmC,oBAAoB;UAC7BuF,QAAQ,EAAEH;QACd,CAAC,EACD;UACIvH,OAAO,EAAEoC,yBAAyB;UAClCsF,QAAQ,EAAE3W,IAAI,EAAEyW,YAAY,EAAEtU;QAClC,CAAC,EACD;UAAE8M,OAAO,EAAEna,gBAAgB;UAAE8hB,WAAW,EAAE5hB;QAAmB,CAAC,CACjE;QACD6hB,MAAM,EAAE,IAAI,CAAClO;MACjB,CAAC,CAAC;IACN;IACA,IAAI8D,OAAO,EACP,IAAI,CAACtQ,QAAQ,GAAGsQ,OAAO,CAAC,IAAI,CAACzM,IAAI,CAAC;IACtC,IAAI/E,QAAQ,EACR,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC,IAAI,CAAC+E,IAAI,CAAC;IACvC,IAAIhF,QAAQ,EAAE;MACV,IAAI,CAACoa,UAAU,GAAGpa,QAAQ;IAC9B;IACA,IAAI2R,UAAU,EAAE;MACZ,IAAI,CAACA,UAAU,GAAGA,UAAU,CAAC,IAAI,CAAC3M,IAAI,CAAC;MACvC,IAAI,CAACkW,WAAW,CAAC,CAAC;IACtB;IACA,IAAIzU,SAAS,KAAKiR,SAAS,EAAE;MACzB,IAAI,CAAC9S,kBAAkB,GAAG6B,SAAS;IACvC;IACA,MAAM,CAAC+T,UAAU,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC9D,IAAIF,UAAU,IAAIC,YAAY,EAC1B,IAAI,CAACtY,cAAc,GAAG;MAAEtB,GAAG,EAAE2Z,UAAU,CAACtc,KAAK;MAAEA,KAAK,EAAEuc,YAAY,CAACvc;IAAM,CAAC;EAClF;EACA;IAAS,IAAI,CAAC6U,IAAI,YAAA+I,oCAAA7I,CAAA;MAAA,YAAAA,CAAA,IAAwF+G,2BAA2B;IAAA,CAAmD;EAAE;EAC1L;IAAS,IAAI,CAAC7G,IAAI,kBArW8E1a,EAAE,CAAA2a,iBAAA;MAAA7F,IAAA,EAqWJyM,2BAA2B;MAAA3G,SAAA;MAAAC,SAAA,WAAAyI,kCAAApd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArWzBlG,EAAE,CAAA+a,WAAA,CAAA1V,GAAA;QAAA;QAAA,IAAAa,EAAA;UAAA,IAAA8U,EAAA;UAAFhb,EAAE,CAAAib,cAAA,CAAAD,EAAA,GAAFhb,EAAE,CAAAkb,WAAA,QAAA/U,GAAA,CAAAwc,QAAA,GAAA3H,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,MAAA;QAAA7O,IAAA;QAAA7F,IAAA;QAAAyU,KAAA;QAAA3N,YAAA;MAAA;MAAA3H,UAAA;MAAAyV,QAAA,GAAFtb,EAAE,CAAAub,kBAAA,CAqWoL,CAACqE,yBAAyB,CAAC,EAAoqX,CAC78X;QACIpE,OAAO,EAAEna,gBAAgB;QACzBoa,UAAU,EAAE9B,WAAW;QACvB+B,IAAI,EAAE,CAAC,CAAC,IAAIzb,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEmB,gBAAgB,CAAC;MAC7D,CAAC,EACD;QAAEma,OAAO,EAAExZ,cAAc;QAAE2Z,QAAQ,EAAElZ;MAAY,CAAC,EAClD;QAAE+Y,OAAO,EAAEvZ,cAAc;QAAE0Z,QAAQ,EAAEjZ;MAAY,CAAC,CACrD,GA7W2F1C,EAAE,CAAAujB,oBAAA,EAAFvjB,EAAE,CAAA4b,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApV,QAAA,WAAA6c,qCAAAtd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlG,EAAE,CAAAsG,UAAA,IAAAyF,mDAAA,0BAqW6d,CAAC,IAAAkB,kDAAA,gCArWhejN,EAAE,CAAAyT,sBAqWqmM,CAAC;QAAA;QAAA,IAAAvN,EAAA;UArWxmMlG,EAAE,CAAAyG,UAAA,kBAAAN,GAAA,CAAAO,IAAA,CAAA4K,UAqW6b,CAAC,yCAAwB,CAAC;QAAA;MAAA;MAAAiL,YAAA,GAA62M1C,iCAAiC,EAAoJ/X,mBAAmB,EAA+BH,EAAE,CAACC,kBAAkB,EAAweG,mBAAmB,EAA+BJ,EAAE,CAACE,aAAa,EAAsMT,mBAAmB,EAA+BD,EAAE,CAACsiB,cAAc,EAAiFtiB,EAAE,CAACuiB,uBAAuB,EAAiFviB,EAAE,CAACqb,oBAAoB,EAAyPrb,EAAE,CAACwiB,4BAA4B,EAAkJxiB,EAAE,CAACyiB,0BAA0B,EAAiLziB,EAAE,CAAC0iB,kCAAkC,EAA+J1iB,EAAE,CAACsb,eAAe,EAAsFtb,EAAE,CAACub,eAAe,EAA8Ila,iBAAiB,EAAkF0C,qBAAqB,EAA+BD,EAAE,CAAC6e,wBAAwB,EAA6F7e,EAAE,CAAC8e,yBAAyB,EAA+F9e,EAAE,CAAC+e,mBAAmB,EAA+G9hB,UAAU,EAA0TC,kBAAkB,EAA+BR,EAAE,CAACsiB,YAAY,EAAsUrgB,qBAAqB,EAA2FC,mBAAmB,EAAyHC,kBAAkB,EAA0BP,IAAI,CAAC2gB,gBAAgB,EAAuDjhB,YAAY,EAA+BD,EAAE,CAACE,OAAO,EAAoFF,EAAE,CAACM,iBAAiB,EAAoPN,EAAE,CAACmhB,OAAO,EAAmHnhB,EAAE,CAACG,gBAAgB,EAAoJH,EAAE,CAACohB,QAAQ,EAA6EphB,EAAE,CAACqhB,YAAY,EAAgFrhB,EAAE,CAACK,SAAS,EAA6C7B,WAAW,EAA+BL,EAAE,CAACmjB,OAAO;MAAA3H,aAAA;MAAAC,eAAA;IAAA,EAQttX;EAAE;AAClE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/WoG7c,EAAE,CAAA8c,iBAAA,CA+WXyE,2BAA2B,EAAc,CAAC;IACzHzM,IAAI,EAAE3U,SAAS;IACf4c,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE,0BAA0B;MAAEpX,UAAU,EAAE,IAAI;MAAEmX,OAAO,EAAE,CAC9DnD,iCAAiC,EACjC/X,mBAAmB,EACnBC,mBAAmB,EACnBX,mBAAmB,EACnBoB,iBAAiB,EACjB0C,qBAAqB,EACrBhD,UAAU,EACVC,kBAAkB,EAClBue,kBAAkB,EAClB9c,qBAAqB,EACrBC,mBAAmB,EACnBC,kBAAkB,EAClBb,YAAY,EACZzB,WAAW,CACd;MAAEob,eAAe,EAAExc,uBAAuB,CAAC8c,MAAM;MAAE+F,SAAS,EAAE,CAACrD,yBAAyB,CAAC;MAAEzC,aAAa,EAAE,CACvG;QACI3B,OAAO,EAAEna,gBAAgB;QACzBoa,UAAU,EAAE9B,WAAW;QACvB+B,IAAI,EAAE,CAAC,CAAC,IAAIzb,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEmB,gBAAgB,CAAC;MAC7D,CAAC,EACD;QAAEma,OAAO,EAAExZ,cAAc;QAAE2Z,QAAQ,EAAElZ;MAAY,CAAC,EAClD;QAAE+Y,OAAO,EAAEvZ,cAAc;QAAE0Z,QAAQ,EAAEjZ;MAAY,CAAC,CACrD;MAAEiE,QAAQ,EAAE;IAAw7M,CAAC;EACl9M,CAAC,CAAC,QAAkB;IAAE4F,IAAI,EAAE,CAAC;MACrBuI,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEqG,IAAI,EAAE,CAAC;MACPoO,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAE8a,KAAK,EAAE,CAAC;MACRrG,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEmN,YAAY,EAAE,CAAC;MACfsH,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEsiB,QAAQ,EAAE,CAAC;MACX7N,IAAI,EAAExU,SAAS;MACfyc,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwH,UAAU,SAASjiB,UAAU,CAAC;AAEpC,MAAMkiB,UAAU,CAAC;EACb,IAAIjY,IAAIA,CAAA,EAAG;IACP,OAAO;MACHoI,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B7B,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBpE,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL;AACJ;AACA,MAAM+V,MAAM,CAAC;EACTxN,WAAWA,CAAC3F,UAAU,EAAEtB,OAAO,GAAGA,CAAA,KAAM,IAAI,EAAEmB,MAAM,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAEiB,QAAQ,EAAED,QAAQ,EAAE;IAClF,IAAI,CAACb,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACtB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACiB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA,MAAMuS,cAAc,CAAC;EACjBzN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,oBAAoB,GAAG,CAAC,CAAC;EAClC;EACAzH,GAAGA,CAACvI,IAAI,EAAE;IACN,IAAI,CAACgQ,oBAAoB,CAAChQ,IAAI,CAAC,GAAG,IAAI,CAACgQ,oBAAoB,CAAChQ,IAAI,CAAC,IAAI,EAAE;IACvE,OAAO,IAAI,IAAI,CAACiQ,KAAK,CAAC,IAAI,CAACD,oBAAoB,CAAChQ,IAAI,CAAC,CAAC;EAC1D;AACJ;AACA,MAAMud,OAAO,CAAC;EACV,IAAIC,OAAOA,CAAA,EAAG;IACV,MAAMhU,UAAU,GAAG,IAAI,IAAI,CAACyG,KAAK,CAAC,CAAC;IACnC,IAAI,CAACI,YAAY,CAACC,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAAC/G,UAAU,CAAC,CAAC;IAC3D,OAAOA,UAAU;EACrB;EACAqG,WAAWA,CAACQ,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAG,cAAcA,CAACC,kBAAkB,EAAE;IAC/B,IAAI,CAACJ,YAAY,CAACK,IAAI,CAACD,kBAAkB,CAAC;EAC9C;EACAE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACN,YAAY,CAACO,MAAM,EAC3B,IAAI,CAACP,YAAY,CAACQ,GAAG,CAAC,CAAC;EAC/B;AACJ;AAEA,MAAM4M,gBAAgB,SAASN,UAAU,CAAC;AAE1C,MAAMO,aAAa,SAASH,OAAO,CAAC;EAChC1N,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGwN,gBAAgB;EACjC;AACJ;AACA,MAAME,oBAAoB,SAASL,cAAc,CAAC;EAC9CzN,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGyN,aAAa;EAC9B;AACJ;AACA,MAAME,YAAY,SAASP,MAAM,CAAC;EAC9BxN,WAAWA,CAAC+B,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC1H,UAAU,IAAI,EAAE,EAAE0H,OAAO,CAAChJ,OAAO,EAAEgJ,OAAO,CAAC7H,MAAM,CAAC;IAChE,IAAI,CAACpE,IAAI,GAAGiM,OAAO,CAACjM,IAAI;IACxB,IAAI,CAAC4D,IAAI,GAAGqI,OAAO,CAACrI,IAAI,IAAI,EAAE;IAC9B,IAAI,CAACyB,QAAQ,GAAG4G,OAAO,CAAC5G,QAAQ,IAAI,6BAA6B;IACjE,IAAI,CAACD,QAAQ,GAAG6G,OAAO,CAAC7G,QAAQ;IAChC,IAAI,CAACN,YAAY,GAAGmH,OAAO,CAACnH,YAAY,IAAI,KAAK;IACjD,IAAI,CAAC/E,OAAO,GAAGkM,OAAO,CAAClM,OAAO;EAClC;EACA,OAAOuM,MAAMA,CAACL,OAAO,EAAE;IACnB,OAAO,IAAIgM,YAAY,CAAChM,OAAO,CAAC;EACpC;EACA,OAAOM,UAAUA,CAACC,cAAc,EAAE;IAC9B,OAAOA,cAAc,CAAC5U,GAAG,CAACqgB,YAAY,CAAC3L,MAAM,CAAC;EAClD;AACJ;AAEA,MAAM4L,cAAc,SAASnO,QAAQ,CAAC;AAEtC,MAAMoO,WAAW,SAAS5N,KAAK,CAAC;EAC5BL,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAG4N,cAAc;EAC/B;AACJ;AACA,MAAME,kBAAkB,SAAShO,YAAY,CAAC;EAC1CF,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAG6N,WAAW;EAC5B;AACJ;AACA,MAAME,UAAU,SAASpO,IAAI,CAAC;EAC1BC,WAAWA,CAAC+B,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAClE,IAAI,EAAEkE,OAAO,CAAC5R,IAAI,EAAE4R,OAAO,CAACvM,WAAW,IAAI,EAAE,EAAEuM,OAAO,CAAC1H,UAAU,IAAI,EAAE,EAAE0H,OAAO,CAAChJ,OAAO,EAAEgJ,OAAO,CAACpM,OAAO,CAAC;IACxH,IAAI,CAAC8I,aAAa,GAAGsD,OAAO,CAACtD,aAAa,KAAK,MAAM,IAAI,CAAC;IAC1D,IAAI,CAAC2P,WAAW,GAAGrM,OAAO,CAACqM,WAAW;IACtC,IAAI,CAAC7P,QAAQ,GAAGwD,OAAO,CAACxD,QAAQ,IAAI,KAAK;IACzC,IAAI,CAAC8P,aAAa,GACdtM,OAAO,CAACsM,aAAa,KAChB/Y,IAAI,IAAIhI,EAAE,CAACR,eAAe,CAACwI,IAAI,CAACmC,MAAM,CAAC,IAAI,CAACtH,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI4R,OAAO,CAAC7H,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG6H,OAAO,CAAC7H,MAAM;IAChC;IACA,IAAI6H,OAAO,CAAC/D,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAG+D,OAAO,CAAC/D,SAAS;IACtC;IACA,IAAI+D,OAAO,CAACuM,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAGvM,OAAO,CAACuM,QAAQ;IACpC;IACA,IAAI,CAACzY,OAAO,GAAGkM,OAAO,CAAClM,OAAO;EAClC;EACA,OAAOuM,MAAMA,CAACL,OAAO,EAAE;IACnB,OAAO,IAAIoM,UAAU,CAACpM,OAAO,CAAC;EAClC;EACA,OAAOM,UAAUA,CAACC,cAAc,EAAE;IAC9B,OAAOA,cAAc,CAAC5U,GAAG,CAACygB,UAAU,CAAC/L,MAAM,CAAC;EAChD;AACJ;AAEA,MAAMmM,iBAAiB,SAASjB,UAAU,CAAC;AAE3C,MAAMkB,cAAc,SAASd,OAAO,CAAC;EACjC1N,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGmO,iBAAiB;EAClC;AACJ;AACA,MAAME,qBAAqB,SAAShB,cAAc,CAAC;EAC/CzN,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGmB,SAAS,CAAC;IACnB,IAAI,CAACf,KAAK,GAAGoO,cAAc;EAC/B;AACJ;AACA,MAAME,aAAa,SAASlB,MAAM,CAAC;EAC/BxN,WAAWA,CAAC+B,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC1H,UAAU,IAAI,EAAE,EAAE0H,OAAO,CAAChJ,OAAO,EAAEgJ,OAAO,CAAC7H,MAAM,CAAC;IAChE,IAAI,CAACpE,IAAI,GAAGiM,OAAO,CAACjM,IAAI;IACxB,IAAI,CAAC4D,IAAI,GAAGqI,OAAO,CAACrI,IAAI,IAAI,EAAE;IAC9B,IAAIqI,OAAO,CAAC5G,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG4G,OAAO,CAAC5G,QAAQ;IACpC;EACJ;EACA,OAAOiH,MAAMA,CAACL,OAAO,EAAE;IACnB,OAAO,IAAI2M,aAAa,CAAC3M,OAAO,CAAC;EACrC;EACA,OAAOM,UAAUA,CAACC,cAAc,EAAE;IAC9B,OAAOA,cAAc,CAAC5U,GAAG,CAACghB,aAAa,CAACtM,MAAM,CAAC;EACnD;AACJ;AACA,MAAMuM,gBAAgB,SAASnB,MAAM,CAAC;EAClCxN,WAAWA,CAAC+B,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC1H,UAAU,IAAI,EAAE,EAAE0H,OAAO,CAAChJ,OAAO,EAAEgJ,OAAO,CAAC7H,MAAM,CAAC;IAChE,IAAI,CAAC8D,SAAS,GAAG+D,OAAO,CAAC/D,SAAS;EACtC;EACA,OAAOoE,MAAMA,CAACL,OAAO,EAAE;IACnB,OAAO,IAAI4M,gBAAgB,CAAC5M,OAAO,CAAC;EACxC;EACA,OAAOM,UAAUA,CAACC,cAAc,EAAE;IAC9B,OAAOA,cAAc,CAAC5U,GAAG,CAACihB,gBAAgB,CAACvM,MAAM,CAAC;EACtD;AACJ;AAEA,MAAMwM,iBAAiB,CAAC;EACpB5O,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6O,aAAa,GAAG,IAAIf,oBAAoB,CAAC,CAAC;IAC/C,IAAI,CAACgB,cAAc,GAAG,IAAIL,qBAAqB,CAAC,CAAC;IACjD,IAAI,CAACM,WAAW,GAAG,IAAIb,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACc,eAAe,GAAG,IAAIpN,sBAAsB,CAAC,CAAC;IACnD,IAAI,CAACqN,aAAa,GAAG,IAAIpN,oBAAoB,CAAC,CAAC;EACnD;EACA;IAAS,IAAI,CAACwB,IAAI,YAAA6L,0BAAA3L,CAAA;MAAA,YAAAA,CAAA,IAAwFqL,iBAAiB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACtF,KAAK,kBAnkB6EvgB,EAAE,CAAAwgB,kBAAA;MAAAC,KAAA,EAmkBYoF,iBAAiB;MAAAnI,OAAA,EAAjBmI,iBAAiB,CAAAvL,IAAA;MAAA8L,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KArkBoG7c,EAAE,CAAA8c,iBAAA,CAqkBX+I,iBAAiB,EAAc,CAAC;IAC/G/Q,IAAI,EAAErU,UAAU;IAChBsc,IAAI,EAAE,CAAC;MACCqJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMC,iBAAiB,SAAStP,QAAQ,CAAC;EACrCE,WAAWA,CAACqP,OAAO,EAAEC,KAAK,EAAErR,QAAQ,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACoR,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC5R,WAAW,GAAGO,QAAQ,CAACvF,GAAG,CAAC8J,IAAI,CAACvE,QAAQ,CAAC;EAClD;EACA4N,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyD,KAAK,CAACC,KAAK,CAAC,CAAC;IAClB,IAAI,CAACD,KAAK,CAACE,kBAAkB,CAAC,IAAI,CAACH,OAAO,EAAE;MACxC3gB,SAAS,EAAE,IAAI,CAAC4G,IAAI;MACpBuG,KAAK,EAAE;IACX,CAAC,CAAC;EACN;EACA4T,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAAC;EACtB;EACA;IAAS,IAAI,CAAClM,IAAI,YAAAqM,0BAAAnM,CAAA;MAAA,YAAAA,CAAA,IAAwF6L,iBAAiB,EA9lB3BrmB,EAAE,CAAAya,iBAAA,CA8lB2Cza,EAAE,CAAC4mB,WAAW,GA9lB3D5mB,EAAE,CAAAya,iBAAA,CA8lBsEza,EAAE,CAAC6mB,gBAAgB,GA9lB3F7mB,EAAE,CAAAya,iBAAA,CA8lBsGza,EAAE,CAACY,QAAQ;IAAA,CAA4C;EAAE;EACjQ;IAAS,IAAI,CAACkmB,IAAI,kBA/lB8E9mB,EAAE,CAAA+mB,iBAAA;MAAAjS,IAAA,EA+lBJuR,iBAAiB;MAAAzL,SAAA;MAAAQ,MAAA;QAAA5D,QAAA,GA/lBfxX,EAAE,CAAAgnB,YAAA,CAAAC,IAAA;QAAAvY,MAAA,GAAF1O,EAAE,CAAAgnB,YAAA,CAAAC,IAAA;QAAAnU,KAAA,GAAF9S,EAAE,CAAAgnB,YAAA,CAAAC,IAAA;MAAA;MAAA5L,QAAA;MAAAxV,UAAA;MAAAyV,QAAA,GAAFtb,EAAE,CAAAknB,0BAAA,EAAFlnB,EAAE,CAAAujB,oBAAA;IAAA,EA+lBoS;EAAE;AAC5Y;AACA;EAAA,QAAA1G,SAAA,oBAAAA,SAAA,KAjmBoG7c,EAAE,CAAA8c,iBAAA,CAimBXuJ,iBAAiB,EAAc,CAAC;IAC/GvR,IAAI,EAAEjU,SAAS;IACfkc,IAAI,EAAE,CAAC;MACC1B,QAAQ,EAAE,aAAa;MACvB4B,QAAQ,EAAE,eAAe;MACzBpX,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiP,IAAI,EAAE9U,EAAE,CAAC4mB;EAAY,CAAC,EAAE;IAAE9R,IAAI,EAAE9U,EAAE,CAAC6mB;EAAiB,CAAC,EAAE;IAAE/R,IAAI,EAAE9U,EAAE,CAACY;EAAS,CAAC,CAAC,EAAkB;IAAE4W,QAAQ,EAAE,CAAC;MACjI1C,IAAI,EAAEzU,KAAK;MACX0c,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAErO,MAAM,EAAE,CAAC;MACToG,IAAI,EAAEzU,KAAK;MACX0c,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC,CAAC;IAAEjK,KAAK,EAAE,CAAC;MACRgC,IAAI,EAAEzU,KAAK;MACX0c,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoK,uBAAuB,CAAC;EAC1BlQ,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6C,KAAK,GAAGtZ,MAAM,CAACG,iBAAiB,CAAC;IACtC,IAAI,CAACoI,KAAK,GAAGvI,MAAM,CAACmD,cAAc,CAAC;IACnC,IAAI,CAACyjB,SAAS,GAAG5mB,MAAM,CAACa,gBAAgB,CAAC;IACzC,IAAI,CAACgmB,UAAU,GAAG7mB,MAAM,CAACqlB,iBAAiB,CAAC;IAC3C,IAAI,CAACyB,UAAU,GAAG9mB,MAAM,CAAC4c,qBAAqB,CAAC;IAC/C,IAAI,CAAClO,kBAAkB,GAAG2O,oBAAoB;EAClD;EACA,IAAI0J,cAAcA,CAAC7Y,MAAM,EAAE;IACvB,MAAMoG,IAAI,GAAG,CAACpG,MAAM,IAAI8Y,IAAI,CAACC,SAAS,CAAC/Y,MAAM,CAAC,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;IAC3E,MAAM8I,QAAQ,GAAG,IAAI,CAAC6P,UAAU,CAAC,GAAGvS,IAAI,WAAW,CAAC,CAACnF,GAAG,CAAC,IAAI,CAAC2X,UAAU,CAAC,CAAC/P,KAAK;IAC/E,IAAI,CAAC1I,eAAe,GAAG,IAAI,CAAC6Y,iBAAiB,CAAClQ,QAAQ,CAAC;IACvD,IAAI,CAAC9I,MAAM,GAAGA,MAAM;EACxB;EACA,IAAIgB,IAAIA,CAAA,EAAG;IACP,OAAQ,IAAI,CAAC0X,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC/E,OAAO,GAAG;MAAEvS,QAAQ,EAAE,CAAC;IAAE,CAAC;EACtE;EACA,IAAID,eAAeA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACH,IAAI,CAACI,QAAQ,CAACD,eAAe,IAAI;MAAEC,QAAQ,EAAE,CAAC;IAAE,CAAC;EAClE;EACA4X,iBAAiBA,CAAClQ,QAAQ,EAAE;IACxB,MAAMmQ,mBAAmB,GAAG,IAAItP,mBAAmB,CAAC,CAAC;IACrDb,QAAQ,CAACE,OAAO,CAAC3N,IAAI,IAAI;MACrB4d,mBAAmB,CAACpP,OAAO,CAACxO,IAAI,CAACtE,KAAK,CAAC;IAC3C,CAAC,CAAC;IACF,OAAOkiB,mBAAmB;EAC9B;EACA;EACApZ,uBAAuBA,CAACuE,KAAK,EAAEvG,IAAI,EAAE;IACjC,MAAM;MAAEuC;IAAM,CAAC,GAAG,IAAI,CAACD,eAAe;IACtC,MAAMJ,YAAY,GAAGK,KAAK,CAACgE,KAAK,CAAC,CAACrE,YAAY,CAACmZ,OAAO,CAAC,CAAC;IACxD,OAAOnZ,YAAY,CAACoZ,IAAI,CAACnhB,IAAI,IAAIA,IAAI,CAACsJ,OAAO,CAACzD,IAAI,CAAC,CAAC;EACxD;EACA;IAAS,IAAI,CAAC+N,IAAI,YAAAwN,gCAAAtN,CAAA;MAAA,YAAAA,CAAA,IAAwF2M,uBAAuB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAACzM,IAAI,kBAtpB8E1a,EAAE,CAAA2a,iBAAA;MAAA7F,IAAA,EAspBJqS,uBAAuB;MAAAvM,SAAA;MAAAC,SAAA,WAAAkN,8BAAA7hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtpBrBlG,EAAE,CAAA+a,WAAA,CAspB8KwG,2BAA2B;QAAA;QAAA,IAAArb,EAAA;UAAA,IAAA8U,EAAA;UAtpB3Mhb,EAAE,CAAAib,cAAA,CAAAD,EAAA,GAAFhb,EAAE,CAAAkb,WAAA,QAAA/U,GAAA,CAAA6hB,SAAA,GAAAhN,EAAA;QAAA;MAAA;MAAAI,MAAA;QAAAmM,cAAA;MAAA;MAAAlM,QAAA;MAAAxV,UAAA;MAAAyV,QAAA,GAAFtb,EAAE,CAAAub,kBAAA,KAspB0jG,CACppG;QACIC,OAAO,EAAEna,gBAAgB;QACzBoa,UAAU,EAAE9B,WAAW;QACvB+B,IAAI,EAAE,CAAC,CAAC,IAAIzb,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEmB,gBAAgB,CAAC;MAC7D,CAAC,CACJ,GA5pB2FrB,EAAE,CAAA4b,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApV,QAAA,WAAAshB,iCAAA/hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlG,EAAE,CAAAsG,UAAA,IAAAqI,8CAAA,MAspBsS,CAAC,IAAAsB,8CAAA,gCAtpBzSjQ,EAAE,CAAAyT,sBAspBo4C,CAAC;QAAA;QAAA,IAAAvN,EAAA;UAtpBv4ClG,EAAE,CAAAoM,aAAA,IAAAjG,GAAA,CAAAuJ,IAAA,SAspBywC,CAAC;QAAA;MAAA;MAAA6M,YAAA,GAA84BtZ,YAAY,EAA+BD,EAAE,CAACE,OAAO,EAAoFF,EAAE,CAACG,gBAAgB,EAAoJkjB,iBAAiB,EAAoKjlB,mBAAmB,EAA+BD,EAAE,CAAC+mB,oBAAoB,EAAqI/mB,EAAE,CAACgnB,aAAa,EAAuF5G,2BAA2B;MAAA5E,aAAA;MAAAC,eAAA;IAAA,EAMn/F;EAAE;AAClE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9pBoG7c,EAAE,CAAA8c,iBAAA,CA8pBXqK,uBAAuB,EAAc,CAAC;IACrHrS,IAAI,EAAE3U,SAAS;IACf4c,IAAI,EAAE,CAAC;MAAElX,UAAU,EAAE,IAAI;MAAEwV,QAAQ,EAAE,mBAAmB;MAAE4B,QAAQ,EAAE,qBAAqB;MAAED,OAAO,EAAE,CAAC/Z,YAAY,EAAEojB,iBAAiB,EAAEjlB,mBAAmB,EAAEmgB,2BAA2B,CAAC;MAAE3E,eAAe,EAAExc,uBAAuB,CAAC8c,MAAM;MAAEC,aAAa,EAAE,CAC7O;QACI3B,OAAO,EAAEna,gBAAgB;QACzBoa,UAAU,EAAE9B,WAAW;QACvB+B,IAAI,EAAE,CAAC,CAAC,IAAIzb,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEmB,gBAAgB,CAAC;MAC7D,CAAC,CACJ;MAAEsF,QAAQ,EAAE;IAAo1D,CAAC;EAC92D,CAAC,CAAC,QAAkB;IAAEqhB,SAAS,EAAE,CAAC;MAC1BlT,IAAI,EAAEhU,YAAY;MAClBic,IAAI,EAAE,CAACwE,2BAA2B;IACtC,CAAC,CAAC;IAAEgG,cAAc,EAAE,CAAC;MACjBzS,IAAI,EAAEzU;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA,MAAM+nB,wBAAwB,SAAS5D,UAAU,CAAC;EAC9CvN,WAAWA,CAAC/B,QAAQ,EAAE;IAClB,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,WAAW,GAAGO,QAAQ,CAACvF,GAAG,CAAC8J,IAAI,CAACvE,QAAQ,CAAC;IAC9C,MAAMmS,UAAU,GAAGnS,QAAQ,CAACvF,GAAG,CAACkW,iBAAiB,CAAC;IAClD,MAAMze,IAAI,GAAG8N,QAAQ,CAACvF,GAAG,CAACyN,qBAAqB,CAAC;IAChD,MAAMtI,IAAI,GAAGI,QAAQ,CAACvF,GAAG,CAAC0N,sBAAsB,CAAC;IACjD,IAAI,CAACzM,UAAU,GAAGyW,UAAU,CAACvS,IAAI,CAAC,CAACnF,GAAG,CAACvI,IAAI,CAAC,CAACwd,OAAO;EACxD;EACA;IAAS,IAAI,CAACtK,IAAI,YAAA+N,iCAAA7N,CAAA;MAAA,YAAAA,CAAA,IAAwF4N,wBAAwB,EAzrBlCpoB,EAAE,CAAAya,iBAAA,CAyrBkDza,EAAE,CAACY,QAAQ;IAAA,CAA4C;EAAE;EAC7M;IAAS,IAAI,CAACkmB,IAAI,kBA1rB8E9mB,EAAE,CAAA+mB,iBAAA;MAAAjS,IAAA,EA0rBJsT,wBAAwB;MAAAhN,MAAA;QAAA1M,MAAA;MAAA;MAAA4M,QAAA,GA1rBtBtb,EAAE,CAAAknB,0BAAA;IAAA,EA0rB0F;EAAE;AAClM;AACA;EAAA,QAAArK,SAAA,oBAAAA,SAAA,KA5rBoG7c,EAAE,CAAA8c,iBAAA,CA4rBXsL,wBAAwB,EAAc,CAAC;IACtHtT,IAAI,EAAEjU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiU,IAAI,EAAE9U,EAAE,CAACY;EAAS,CAAC,CAAC,EAAkB;IAAE8N,MAAM,EAAE,CAAC;MACtEoG,IAAI,EAAEzU;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMioB,oBAAoB,SAASF,wBAAwB,CAAC;EACxDnR,WAAWA,CAAC/B,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf,IAAI,CAACvE,IAAI,GAAG,WAAW;IACvB,IAAI,CAAC5D,IAAI,GAAG,EAAE;IACd,IAAI,CAACwb,SAAS,GAAG,CAACrR,CAAC,EAAEnN,IAAI,KAAKA,IAAI,CAACgD,IAAI;EAC3C;EACA;IAAS,IAAI,CAACuN,IAAI,YAAAkO,6BAAAhO,CAAA;MAAA,YAAAA,CAAA,IAAwF8N,oBAAoB,EAzsB9BtoB,EAAE,CAAAya,iBAAA,CAysB8Cza,EAAE,CAACY,QAAQ;IAAA,CAA4C;EAAE;EACzM;IAAS,IAAI,CAAC8Z,IAAI,kBA1sB8E1a,EAAE,CAAA2a,iBAAA;MAAA7F,IAAA,EA0sBJwT,oBAAoB;MAAA1N,SAAA;MAAAQ,MAAA;QAAAzK,IAAA;QAAAmC,KAAA;QAAA/F,IAAA;MAAA;MAAAsO,QAAA;MAAAxV,UAAA;MAAAyV,QAAA,GA1sBlBtb,EAAE,CAAAub,kBAAA,CA0sBuI,CACjO;QACIC,OAAO,EAAE6B,sBAAsB;QAC/B6F,QAAQ,EAAE;MACd,CAAC,CACJ,GA/sB2FljB,EAAE,CAAAknB,0BAAA,EAAFlnB,EAAE,CAAA4b,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApV,QAAA,WAAA8hB,8BAAAviB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlG,EAAE,CAAAsG,UAAA,IAAAmK,2CAAA,gBA+sBe,CAAC,IAAAI,2CAAA,yBAAyrB,CAAC,IAAAU,2CAAA,gCA/sB5sBvR,EAAE,CAAAyT,sBA+sB45B,CAAC,IAAA7B,2CAAA,gCA/sB/5B5R,EAAE,CAAAyT,sBA+sBy1C,CAAC,IAAAb,2CAAA,gCA/sB51C5S,EAAE,CAAAyT,sBA+sB2sD,CAAC;QAAA;QAAA,IAAAvN,EAAA;UA/sB9sDlG,EAAE,CAAAoM,aAAA,IAAAjG,GAAA,CAAAyK,UAAA,CAAAoH,MAAA,aA+sBkqB,CAAC;UA/sBrqBhY,EAAE,CAAAkH,SAAA,CA+sBw2B,CAAC;UA/sB32BlH,EAAE,CAAAoM,aAAA,IAAAjG,GAAA,CAAAyK,UAAA,CAAAoH,MAAA,eA+sBw2B,CAAC;QAAA;MAAA;MAAAuE,YAAA,GAAk9Dna,iBAAiB,EAA+BT,EAAE,CAAC+mB,WAAW,EAAoN/mB,EAAE,CAACgnB,iBAAiB,EAAgEhnB,EAAE,CAACinB,eAAe,EAA8DjnB,EAAE,CAACknB,eAAe,EAAgGlnB,EAAE,CAACmnB,qBAAqB,EAAoEnmB,iBAAiB,EAAkHkB,mBAAmB,EAA0HX,OAAO,EAAmFY,kBAAkB,EAA0BP,IAAI,CAAC2gB,gBAAgB,EAAwD/gB,gBAAgB,EAAmJd,gBAAgB,EAA+BV,EAAE,CAACO,UAAU;MAAAya,aAAA;MAAAC,eAAA;IAAA,EAAsV;EAAE;AAC/jJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjtBoG7c,EAAE,CAAA8c,iBAAA,CAitBXwL,oBAAoB,EAAc,CAAC;IAClHxT,IAAI,EAAE3U,SAAS;IACf4c,IAAI,EAAE,CAAC;MAAE1B,QAAQ,EAAE,gBAAgB;MAAExV,UAAU,EAAE,IAAI;MAAEmX,OAAO,EAAE,CACpD5a,iBAAiB,EACjBO,iBAAiB,EACjBkB,mBAAmB,EACnBX,OAAO,EACPY,kBAAkB,EAClBX,gBAAgB,EAChBd,gBAAgB,CACnB;MAAE4a,QAAQ,EAAE,kBAAkB;MAAEgG,SAAS,EAAE,CACxC;QACIzH,OAAO,EAAE6B,sBAAsB;QAC/B6F,QAAQ,EAAE;MACd,CAAC,CACJ;MAAEtG,eAAe,EAAExc,uBAAuB,CAAC8c,MAAM;MAAEvW,QAAQ,EAAE;IAA+xF,CAAC;EAC12F,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEmO,IAAI,EAAE9U,EAAE,CAACY;EAAS,CAAC,CAAC,EAAkB;IAAE+P,IAAI,EAAE,CAAC;MACpEmE,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEyS,KAAK,EAAE,CAAC;MACRgC,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAE0M,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAEzU;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0oB,4BAA4B,GAAG,GAAG;AACxC,MAAMC,wBAAwB,CAAC;EAC3B,IAAInV,WAAWA,CAACpO,KAAK,EAAE;IACnB,IAAI,CAACwjB,YAAY,GAAGxjB,KAAK;EAC7B;EACA,IAAIoO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoV,YAAY,KAAK,IAAI,CAACrY,UAAU,CAACoH,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,EAAE,CAAC;EACpF;EACA,IAAIkR,kBAAkBA,CAACC,KAAK,EAAE;IAC1B,IAAI,CAACC,eAAe,CAACD,KAAK,GAAGE,MAAM,CAACF,KAAK,CAAC,GAAGlK,SAAS,CAAC;EAC3D;EACA,CAAC/J,QAAQ;EACT+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqS,aAAa,GAAG,IAAIvoB,YAAY,CAAC,CAAC;IACvC,IAAI,CAACwnB,SAAS,GAAG,CAACrR,CAAC,EAAEnN,IAAI,KAAKA,IAAI,CAAC3C,IAAI;IACvC,IAAI,CAACmiB,MAAM,GAAG/oB,MAAM,CAACQ,SAAS,CAAC;IAC/B,IAAI,CAACwoB,MAAM,GAAGhpB,MAAM,CAACiD,kBAAkB,CAAC;IACxC,IAAI,CAACoR,qBAAqB,GAAGrU,MAAM,CAACid,wBAAwB,CAAC;IAC7D,IAAI,CAAC,CAACvI,QAAQ,GAAG1U,MAAM,CAACI,QAAQ,CAAC;IACjC,IAAI,CAAC+T,WAAW,GAAG,IAAI,CAAC,CAACO,QAAQ,CAACvF,GAAG,CAAC8J,IAAI,CAAC,IAAI,CAAC,CAACvE,QAAQ,CAAC;IAC1D,IAAI,CAACuU,iBAAiB,GAAG,IAAI,CAAC,CAACvU,QAAQ,CAACvF,GAAG,CAAC3L,iBAAiB,CAAC;IAC9D,MAAMqjB,UAAU,GAAG,IAAI,CAAC,CAACnS,QAAQ,CAACvF,GAAG,CAACkW,iBAAiB,CAAC;IACxD,MAAMze,IAAI,GAAG,IAAI,CAAC,CAAC8N,QAAQ,CAACvF,GAAG,CAACyN,qBAAqB,CAAC;IACtD,IAAI,CAAC5F,QAAQ,GAAG6P,UAAU,CAACrB,WAAW,CAACrW,GAAG,CAACvI,IAAI,CAAC,CAACmQ,KAAK;IACtD,IAAI,CAAC3G,UAAU,GAAGyW,UAAU,CAAC,eAAe,CAAC,CAAC1X,GAAG,CAACvI,IAAI,CAAC,CAClDwd,OAAO;IACZ,IAAI,CAAC8E,4BAA4B,GAC7B,IAAI,CAACD,iBAAiB,CAACE,mBAAmB,CAAC,IAAI,CAAC/Y,UAAU,CAACgX,OAAO,CAAC,CAAC,CAACjjB,GAAG,CAACwM,MAAM,KAAK;MAAEyY,cAAc,EAAEzY,MAAM,CAACG;IAAW,CAAC,CAAC,CAAC,CAAC,CAAC0G,MAAM,GAAG,CAAC;IAC3I,IAAI,CAACoR,eAAe,CAACL,4BAA4B,CAAC;EACtD;EACAK,eAAeA,CAACS,aAAa,EAAE;IAC3B,MAAMC,MAAM,GAAG,CAACD,aAAa,CAAC;IAC9B,IAAI,CAACrS,QAAQ,CAACE,OAAO,CAAC,CAAC;MAAEjS,KAAK,EAAEiB;IAAK,CAAC,KAAK;MACvCojB,MAAM,CAAChS,IAAI,CAACpR,IAAI,CAAC2e,WAAW,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAACvR,YAAY,GAAGgW,MAAM;EAC9B;EACAC,OAAOA,CAACtkB,KAAK,EAAEukB,MAAM,EAAE;IACnB,OAAOvkB,KAAK,IAAIukB,MAAM,GAAG5mB,UAAU,CAACqC,KAAK,EAAEukB,MAAM,EAAE,IAAI,CAACT,MAAM,CAAC,GAAG,EAAE;EACxE;EACAU,OAAOA,CAACxkB,KAAK,EAAE;IACX,OAAOA,KAAK,GACN,gFAAgF,GAChF,+EAA+E;EACzF;EACAykB,OAAOA,CAACC,QAAQ,EAAEzL,IAAI,EAAE;IACpB,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC1G,MAAM,GAAG,CAAC,EACxB,OAAOmS,QAAQ;IACnB,MAAM;MAAE/hB;IAAI,CAAC,GAAGsW,IAAI,CAACjG,IAAI,CAAC,CAAC;MAAEhT;IAAM,CAAC,KAAKA,KAAK,KAAK0kB,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClE,OAAO/hB,GAAG;EACd;EACAgiB,UAAUA,CAAC1jB,IAAI,EAAE6F,IAAI,EAAE;IACnB,OAAO7F,IAAI,CAAC4e,aAAa,CAAC/Y,IAAI,CAAC,CAAC9H,IAAI,CAACE,GAAG,CAACc,KAAK,IAAI;MAC9C,QAAQiB,IAAI,CAACoO,IAAI;QACb,KAAK,SAAS,CAAC;UACX,OAAO,IAAI,CAACmV,OAAO,CAACxkB,KAAK,CAAC;QAC9B,KAAK,MAAM,CAAC;UACR,OAAO,IAAI,CAACskB,OAAO,CAACtkB,KAAK,EAAEtB,kBAAkB,CAAC,IAAI,CAACqlB,MAAM,CAAC,CAAC;QAC/D,KAAK,MAAM,CAAC;UACR,OAAO,IAAI,CAACO,OAAO,CAACtkB,KAAK,EAAEvB,kBAAkB,CAAC,IAAI,CAACslB,MAAM,CAAC,CAAC;QAC/D,KAAK,UAAU,CAAC;UACZ,OAAO,IAAI,CAACO,OAAO,CAACtkB,KAAK,EAAExB,2BAA2B,CAAC,IAAI,CAACulB,MAAM,CAAC,CAAC;QACxE,KAAK,MAAM,CAAC;UACR,OAAO,IAAI,CAACU,OAAO,CAACzkB,KAAK,EAAEiB,IAAI,CAAC6e,QAAQ,IAAI,EAAE,CAAC;QACnD;UACI,OAAO9f,KAAK;QAChB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACAqd,WAAWA,CAAC;IAAEvW;EAAK,CAAC,EAAE;IAClB,IAAI,CAACA,IAAI,EAAEyW,YAAY,EACnB;IACJ,IAAIzW,IAAI,CAACyW,YAAY,CAAChL,MAAM,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAC0G,IAAI,CAAC2L,UAAU,GAAG,IAAI,CAACC,YAAY;IAC5C;IACA,IAAI,CAAC/d,IAAI,GAAGA,IAAI,CAACyW,YAAY,CAACre,GAAG,CAAC,CAAC+J,MAAM,EAAEoE,KAAK,KAAK;MACjD,IAAI,CAAC0E,QAAQ,CAACE,OAAO,CAAChR,IAAI,IAAI;QAC1B,MAAM6jB,QAAQ,GAAG;UAAE5V,WAAW,EAAE,IAAI,CAACA,WAAW;UAAEjG,MAAM;UAAEoE;QAAM,CAAC;QACjE,MAAMrN,KAAK,GAAG,IAAI,CAAC2kB,UAAU,CAAC1jB,IAAI,CAACjB,KAAK,EAAE8kB,QAAQ,CAAC;QACnD,MAAMC,OAAO,GAAG,IAAI9jB,IAAI,CAACjB,KAAK,CAAC2B,IAAI,EAAE;QACrCsH,MAAM,CAAC8b,OAAO,CAAC,GAAG;UACdxa,OAAO,EAAEtJ,IAAI,CAACjB,KAAK,CAACuK,OAAO,CAACua,QAAQ,CAAC;UACrC9kB;QACJ,CAAC;QACD,IAAIiB,IAAI,CAACjB,KAAK,CAACwP,SAAS,EAAE;UACtBvG,MAAM,CAAC8b,OAAO,CAAC,CAACtV,QAAQ,GAAGtU,QAAQ,CAACyY,MAAM,CAAC;YACvC4J,SAAS,EAAE,CACP;cACIzH,OAAO,EAAEgC,gBAAgB;cACzB0F,QAAQ,EAAEzd;YACd,CAAC,CACJ;YACD2d,MAAM,EAAE,IAAI,CAAC,CAAClO;UAClB,CAAC,CAAC;UACFxG,MAAM,CAAC8b,OAAO,CAAC,CAACvV,SAAS,GAAGvO,IAAI,CAACjB,KAAK,CAACwP,SAAS;QACpD;MACJ,CAAC,CAAC;MACF,OAAOvG,MAAM;IACjB,CAAC,CAAC;EACN;EACA6E,gBAAgBA,CAACkX,OAAO,EAAE;IACtB,MAAM7F,OAAO,GAAG,IAAI,CAAChU,UAAU,CAACgX,OAAO,CAAC,CAAC;IACzC,MAAM8C,cAAc,GAAG9F,OAAO,CAAC7f,MAAM,CAACoM,MAAM,IAAI;MAC5C,MAAM;QAAEnB,OAAO;QAAEsB;MAAW,CAAC,GAAGH,MAAM;MACtC,IAAIwZ,SAAS,GAAG,IAAI;MACpB,IAAIC,aAAa,GAAG,IAAI;MACxB,IAAI5a,OAAO,EAAE;QACT2a,SAAS,GAAG3a,OAAO,CAAC;UAAEtB,MAAM,EAAE+b,OAAO;UAAE9V,WAAW,EAAE,IAAI,CAACA;QAAY,CAAC,CAAC;MAC3E;MACA,IAAIrD,UAAU,EAAE;QACZsZ,aAAa,GAAG,IAAI,CAACnB,iBAAiB,CAACoB,gBAAgB,CAACvZ,UAAU,CAAC;MACvE;MACA,OAAOqZ,SAAS,IAAIC,aAAa;IACrC,CAAC,CAAC;IACF,OAAOF,cAAc,CAAC1S,MAAM,GAAG,CAAC;EACpC;EACA;IAAS,IAAI,CAACsC,IAAI,YAAAwQ,iCAAAtQ,CAAA;MAAA,YAAAA,CAAA,IAAwFwO,wBAAwB;IAAA,CAAmD;EAAE;EACvL;IAAS,IAAI,CAACtO,IAAI,kBA/1B8E1a,EAAE,CAAA2a,iBAAA;MAAA7F,IAAA,EA+1BJkU,wBAAwB;MAAApO,SAAA;MAAAQ,MAAA;QAAAvH,WAAA;QAAAtH,IAAA;QAAAmS,IAAA;QAAA4L,YAAA;QAAApB,kBAAA;QAAAvV,eAAA;MAAA;MAAAoX,OAAA;QAAAzB,aAAA;MAAA;MAAAjO,QAAA;MAAAxV,UAAA;MAAAyV,QAAA,GA/1BtBtb,EAAE,CAAAujB,oBAAA,EAAFvjB,EAAE,CAAA4b,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApV,QAAA,WAAAqkB,kCAAA9kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlG,EAAE,CAAA4H,cAAA,sBA+1B+gB,CAAC;UA/1BlhB5H,EAAE,CAAA4J,UAAA,sBAAAqhB,oEAAA1hB,MAAA;YAAA,OA+1B+epD,GAAA,CAAAmjB,aAAA,CAAA4B,IAAA,CAAA3hB,MAAyB,CAAC;UAAA,CAAC,CAAC;UA/1B7gBvJ,EAAE,CAAAsG,UAAA,IAAAsN,+CAAA,iCA+1BmmB,CAAC;UA/1BtmB5T,EAAE,CAAAsI,gBAAA,IAAAmN,uCAAA,mCAAArI,UA+1BqsG,CAAC;UA/1BxsGpN,EAAE,CAAA6H,YAAA,CA+1BytG,CAAC;QAAA;QAAA,IAAA3B,EAAA;UA/1B5tGlG,EAAE,CAAAyG,UAAA,SAAAN,GAAA,CAAAoG,IA+1Bwa,CAAC,UAAApG,GAAA,CAAAmkB,YAA6B,CAAC,SAAAnkB,GAAA,CAAAuY,IAAoB,CAAC;UA/1B9d1e,EAAE,CAAAkH,SAAA,CA+1B8zC,CAAC;UA/1Bj0ClH,EAAE,CAAAoM,aAAA,IAAAjG,GAAA,CAAAwN,eAAA,IAAAxN,GAAA,CAAAyK,UAAA,CAAAoH,MAAA,IAAA7R,GAAA,CAAAujB,4BAAA,SA+1B8zC,CAAC;UA/1Bj0C1pB,EAAE,CAAAkH,SAAA,CA+1BqsG,CAAC;UA/1BxsGlH,EAAE,CAAAwI,UAAA,CAAArC,GAAA,CAAAqR,QA+1BqsG,CAAC;QAAA;MAAA;MAAA+E,YAAA,GAAqE3Z,mBAAmB,EAAgFwC,kBAAkB,EAA+BD,IAAI,CAACgmB,kBAAkB,EAAwvBhmB,IAAI,CAACimB,wBAAwB,EAAkbjmB,IAAI,CAACkmB,8BAA8B,EAA4ElmB,IAAI,CAACmmB,4BAA4B,EAA0EhD,oBAAoB,EAA8HpmB,UAAU,EAA2TW,4BAA4B,EAAyHC,yBAAyB,EAAkHe,mBAAmB,EAAyHC,kBAAkB,EAA0BP,IAAI,CAAC2gB,gBAAgB,EAAmD7gB,SAAS,EAA8CF,gBAAgB,EAAoJG,iBAAiB;MAAAqZ,aAAA;MAAAC,eAAA;IAAA,EAA+Q;EAAE;AACj7M;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAj2BoG7c,EAAE,CAAA8c,iBAAA,CAi2BXkM,wBAAwB,EAAc,CAAC;IACtHlU,IAAI,EAAE3U,SAAS;IACf4c,IAAI,EAAE,CAAC;MAAE1B,QAAQ,EAAE,oBAAoB;MAAE4B,QAAQ,EAAE,sBAAsB;MAAEpX,UAAU,EAAE,IAAI;MAAEmX,OAAO,EAAE,CAC1Fpa,mBAAmB,EACnBwC,kBAAkB,EAClBkjB,oBAAoB,EACpBpmB,UAAU,EACVW,4BAA4B,EAC5BC,yBAAyB,EACzBe,mBAAmB,EACnBC,kBAAkB,EAClBT,SAAS,EACTF,gBAAgB,EAChBG,iBAAiB,CACpB;MAAEsZ,eAAe,EAAExc,uBAAuB,CAAC8c,MAAM;MAAEvW,QAAQ,EAAE;IAAw2F,CAAC;EACn7F,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEkN,WAAW,EAAE,CAAC;MACtDiB,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEkM,IAAI,EAAE,CAAC;MACPuI,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEqe,IAAI,EAAE,CAAC;MACP5J,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEiqB,YAAY,EAAE,CAAC;MACfxV,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAE6oB,kBAAkB,EAAE,CAAC;MACrBpU,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEsT,eAAe,EAAE,CAAC;MAClBmB,IAAI,EAAEzU;IACV,CAAC,CAAC;IAAEipB,aAAa,EAAE,CAAC;MAChBxU,IAAI,EAAE7T;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsqB,oBAAoB,SAASnD,wBAAwB,CAAC;EACxDnR,WAAWA,CAAC/B,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACiB,eAAe,GAAG,wBAAwB;IAC/C,IAAI,CAAC4K,OAAO,GAAG,MAAM,IAAI,CAACxU,IAAI;IAC9B,IAAI,CAACgc,SAAS,GAAG,CAACrR,CAAC,EAAEnN,IAAI,KAAKA,IAAI,CAACoH,MAAM,IAAIpH,IAAI,CAACkL,SAAS;EAC/D;EACAqB,eAAeA,CAAC7Q,KAAK,EAAE;IACnB,OAAO;MACHA,KAAK,EAAEA;IACX,CAAC;EACL;EACA;IAAS,IAAI,CAAC6U,IAAI,YAAAkR,6BAAAhR,CAAA;MAAA,YAAAA,CAAA,IAAwF+Q,oBAAoB,EA74B9BvrB,EAAE,CAAAya,iBAAA,CA64B8Cza,EAAE,CAACY,QAAQ;IAAA,CAA4C;EAAE;EACzM;IAAS,IAAI,CAAC8Z,IAAI,kBA94B8E1a,EAAE,CAAA2a,iBAAA;MAAA7F,IAAA,EA84BJyW,oBAAoB;MAAA3Q,SAAA;MAAAS,QAAA;MAAAxV,UAAA;MAAAyV,QAAA,GA94BlBtb,EAAE,CAAAub,kBAAA,CA84B+E,CACzK;QACIC,OAAO,EAAE6B,sBAAsB;QAC/B6F,QAAQ,EAAE;MACd,CAAC,CACJ,GAn5B2FljB,EAAE,CAAAknB,0BAAA,EAAFlnB,EAAE,CAAA4b,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApV,QAAA,WAAA8kB,8BAAAvlB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlG,EAAE,CAAA4H,cAAA,YAm5B6D,CAAC;UAn5BhE5H,EAAE,CAAAsI,gBAAA,IAAAoO,mCAAA,kBAAAf,UAm5B0nC,CAAC;UAn5B7nC3V,EAAE,CAAA6H,YAAA,CAm5BooC,CAAC;QAAA;QAAA,IAAA3B,EAAA;UAn5BvoClG,EAAE,CAAAkH,SAAA,CAm5B0nC,CAAC;UAn5B7nClH,EAAE,CAAAwI,UAAA,CAAArC,GAAA,CAAAyK,UAm5B0nC,CAAC;QAAA;MAAA;MAAA2L,YAAA,GAA0DmE,kBAAkB,EAAuD7c,mBAAmB,EAAyHC,kBAAkB,EAA0BP,IAAI,CAAC2gB,gBAAgB,EAAwDhhB,OAAO,EAAoFI,iBAAiB;MAAAqZ,aAAA;MAAAC,eAAA;IAAA,EAA+Q;EAAE;AACt+D;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAr5BoG7c,EAAE,CAAA8c,iBAAA,CAq5BXyO,oBAAoB,EAAc,CAAC;IAClHzW,IAAI,EAAE3U,SAAS;IACf4c,IAAI,EAAE,CAAC;MAAE1B,QAAQ,EAAE,gBAAgB;MAAE4B,QAAQ,EAAE,kBAAkB;MAAEpX,UAAU,EAAE,IAAI;MAAEmX,OAAO,EAAE,CAClF0D,kBAAkB,EAClB7c,mBAAmB,EACnBC,kBAAkB,EAClBZ,OAAO,EACPI,iBAAiB,CACpB;MAAE2f,SAAS,EAAE,CACV;QACIzH,OAAO,EAAE6B,sBAAsB;QAC/B6F,QAAQ,EAAE;MACd,CAAC,CACJ;MAAEtG,eAAe,EAAExc,uBAAuB,CAAC8c,MAAM;MAAEvW,QAAQ,EAAE;IAA6pC,CAAC;EACxuC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEmO,IAAI,EAAE9U,EAAE,CAACY;EAAS,CAAC,CAAC;AAAA;AAEzD,IAAI8qB,gBAAgB,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EAC9CC,SAAS,EAAE;AACf,CAAC,CAAC;AAEF,MAAMC,6BAA6B,GAAG;EAAEtQ,OAAO,EAAEna,gBAAgB;EAAE8hB,WAAW,EAAE5hB;AAAmB,CAAC;AAEpG,SAASwqB,uBAAuBA,CAACC,SAAS,EAAEC,cAAc,EAAE,GAAGC,YAAY,EAAE;EACzEP,MAAM,CAACQ,IAAI,CAACF,cAAc,CAAC,CAACvU,OAAO,CAAEtQ,IAAI,IAAK;IAC1C,MAAMwd,OAAO,GAAGoH,SAAS,CAACrc,GAAG,CAACvI,IAAI,CAAC;IACnCwd,OAAO,CAAC7M,iBAAiB,CAAC,CAAC;IAC3B6M,OAAO,CAAChN,cAAc,CAAEhH,UAAU,IAAKA,UAAU,CAACwb,WAAW,CAACH,cAAc,CAAC7kB,IAAI,CAAC,CAAC,CAAC;IACpF8kB,YAAY,CAACxU,OAAO,CAAC2U,WAAW,IAAI,CAACA,WAAW,CAACjlB,IAAI,CAAC,IAAI,EAAE,EAAEsQ,OAAO,CAAEC,QAAQ,IAAKiN,OAAO,CAAChN,cAAc,CAACD,QAAQ,CAAC,CAAC,CAAC;EAC1H,CAAC,CAAC;AACN;AAEA,SAAS2U,qBAAqBA,CAAC/f,IAAI,EAAE;EACjC,MAAM8a,UAAU,GAAG9a,IAAI,CAACoI,WAAW,CAAEkR,iBAAkB,CAAC;EACxD,MAAMyB,UAAU,GAAG/a,IAAI,CAACoI,WAAW,CAACyI,qBAAqB,CAAC;EAC1D,MAAM1N,IAAI,GAAG,IAAIjO,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM8qB,SAAS,GAAG,IAAI9qB,gBAAgB,CAAC,CAAC,CAAC,CAAC;EAC1CiO,IAAI,CAAC8c,UAAU,CAAC3O,oBAAoB,EAAE0O,SAAS,CAAC;EAChD,MAAM7d,MAAM,GAAGnC,IAAI,CAACmC,MAAM,IAAI,CAAC,CAAC;EAChC,MAAMoG,IAAI,GAAG0S,IAAI,CAACC,SAAS,CAAC/Y,MAAM,CAAC,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;EAChE,MAAM6I,KAAK,GAAG8P,UAAU,CAAC,GAAGvS,IAAI,WAAW,CAAC,CAACnF,GAAG,CAAC2X,UAAU,CAAC,CAAC/P,KAAK;EAClE,MAAM1H,eAAe,GAAGnB,MAAM,CAACmP,oBAAoB,CAAC,IAAI,CAAC,CAAC;EAC1DtG,KAAK,CAACG,OAAO,CAAC,CAAC;IAAEjS,KAAK,EAAEiB;EAAK,CAAC,KAAK;IAC/B,MAAMU,IAAI,GAAGV,IAAI,CAACU,IAAI;IACtB,MAAMqlB,eAAe,GAAG/lB,IAAI,CAACkG,OAAO,IAAIxF,IAAI,IAAIyI,eAAe;IAC/D,IAAIpK,KAAK,GAAGgnB,eAAe,GAAG5c,eAAe,CAACzI,IAAI,CAAC,GAAGA,IAAI,IAAIsH,MAAM,GAAGA,MAAM,CAACtH,IAAI,CAAC,GAAG6X,SAAS;IAC/F,IAAI,OAAOxZ,KAAK,KAAK,WAAW,EAC5BA,KAAK,GAAGiB,IAAI,CAACyS,YAAY;IAC7B,IAAI1T,KAAK,EAAE;MACP,IAAIinB,OAAO;MACX,QAAQhmB,IAAI,CAACoO,IAAI;QACb,KAAK,MAAM,CAAC;UACR4X,OAAO,GAAG,IAAIjqB,WAAW,CAAC,CAAC;UAC3BgD,KAAK,GAAGinB,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,SAAS,CAACnnB,KAAK,CAAC,CAAC;UACjD;QACJ,KAAK,MAAM,CAAC;UACRinB,OAAO,GAAG,IAAIhqB,WAAW,CAAC,CAAC;UAC3B+C,KAAK,GAAGinB,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,SAAS,CAACnnB,KAAK,CAAC,CAAC;UACjD;QACJ,KAAK,UAAU,CAAC;UACZinB,OAAO,GAAG,IAAInqB,eAAe,CAAC,CAAC;UAC/BkD,KAAK,GAAGinB,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,SAAS,CAACnnB,KAAK,CAAC,CAAC;UACjD;QACJ;UACI;MACR;IACJ;IACA,MAAMonB,WAAW,GAAG,IAAInrB,kBAAkB,CAAC+D,KAAK,EAAE;MAC9CwT,eAAe,EAAEvS,IAAI,CAACuS,eAAe,CAAC1M,IAAI,CAAC;MAC3C2M,UAAU,EAAExS,IAAI,CAACwS,UAAU,CAAC3M,IAAI;IACpC,CAAC,CAAC;IACF,CAACkgB,eAAe,GAAGF,SAAS,GAAG7c,IAAI,EAAE8c,UAAU,CAACplB,IAAI,EAAEylB,WAAW,CAAC;EACtE,CAAC,CAAC;EACF,OAAOnd,IAAI;AACf;AAEA,SAASod,gCAAgCA,CAAC1lB,IAAI,EAAE;EAC5C,OAAQmF,IAAI,IAAKhI,EAAE,CAACgI,IAAI,CAACmC,MAAM,CAACmP,oBAAoB,CAAC,CAACzW,IAAI,CAAC,CAAC;AAChE;AACA,SAAS2lB,qBAAqBA,CAACf,SAAS,EAAEgB,YAAY,EAAE,GAAGd,YAAY,EAAE;EACrEP,MAAM,CAACQ,IAAI,CAACa,YAAY,CAAC,CAACtV,OAAO,CAAEtQ,IAAI,IAAK;IACxC,MAAMmQ,KAAK,GAAGyU,SAAS,CAACrc,GAAG,CAACvI,IAAI,CAAC;IACjCmQ,KAAK,CAACQ,iBAAiB,CAAC,CAAC;IACzBR,KAAK,CAACK,cAAc,CAAEJ,QAAQ,IAAKA,QAAQ,CAAC4U,WAAW,CAACY,YAAY,CAAC5lB,IAAI,CAAC,CAAC,CAAC;IAC5E8kB,YAAY,CAACxU,OAAO,CAAC2U,WAAW,IAAI,CAACA,WAAW,CAACjlB,IAAI,CAAC,IAAI,EAAE,EAAEsQ,OAAO,CAAEC,QAAQ,IAAKJ,KAAK,CAACK,cAAc,CAACD,QAAQ,CAAC,CAAC,CAAC;EACxH,CAAC,CAAC;AACN;AAEA,SAASsV,UAAUA,CAACC,OAAO,EAAE;EACzB,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrBD,OAAO,CAACxV,OAAO,CAAC,CAAC;IAAEtQ,IAAI,GAAG,EAAE;IAAE3B;EAAM,CAAC,KAAK;IACtC0nB,UAAU,CAAEA,UAAU,CAAC/lB,IAAI,CAAC,GAAG3B,KAAK,CAAE,GAAG2B,IAAI;EACjD,CAAC,CAAC;EACF,OAAO+lB,UAAU;AACrB;AACA,SAASC,uBAAuBA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EAC7D,OAAOhhB,IAAI,IAAI;IACX,MAAM9G,KAAK,GAAG8G,IAAI,CAACmC,MAAM,CAACmP,oBAAoB,CAAC,CAAC0P,QAAQ,CAAC;IACzD,MAAMnlB,GAAG,GAAGklB,UAAU,CAACE,WAAW,CAAC/nB,KAAK,CAAC;IACzC,MAAMgoB,IAAI,GAAGlhB,IAAI,CAACoI,WAAW,CAACvQ,mBAAmB,CAAC;IAClD,MAAMspB,YAAY,GAAGC,mBAAmB,CAACF,IAAI,EAAEJ,QAAQ,EAAEC,UAAU,CAAC;IACpE,OAAOM,wBAAwB,CAACH,IAAI,EAAEC,YAAY,CAACtlB,GAAG,CAAC,CAAC;EAC5D,CAAC;AACL;AACA,SAASylB,iBAAiBA,CAACR,QAAQ,EAAEC,UAAU,EAAE;EAC7C,OAAO/gB,IAAI,IAAI;IACX,MAAMkhB,IAAI,GAAGlhB,IAAI,CAACoI,WAAW,CAACvQ,mBAAmB,CAAC;IAClD,MAAMspB,YAAY,GAAGC,mBAAmB,CAACF,IAAI,EAAEJ,QAAQ,EAAEC,UAAU,CAAC;IACpE,OAAOM,wBAAwB,CAACH,IAAI,EAAEH,UAAU,CAACQ,MAAM,CAACnpB,GAAG,CAAC,CAAC;MAAEyC,IAAI,GAAG,EAAE;MAAE3B;IAAM,CAAC,MAAM;MACnF2C,GAAG,EAAEslB,YAAY,CAACtmB,IAAI,CAAC;MACvB3B;IACJ,CAAC,CAAC,CAAC,CAAC;EACR,CAAC;AACL;AACA,SAASmoB,wBAAwBA,CAACH,IAAI,EAAEM,SAAS,EAAE;EAC/C,OAAOvpB,KAAK,CAACD,EAAE,CAAC,IAAI,CAAC,EAAEkpB,IAAI,CAACO,eAAe,CAAC,CAACvpB,IAAI,CAACE,GAAG,CAAC,MAAMopB,SAAS,CAAC,CAAC;AAC3E;AACA,SAASJ,mBAAmBA,CAACF,IAAI,EAAEJ,QAAQ,EAAEC,UAAU,EAAE;EACrD,MAAM/N,QAAQ,GAAG+N,UAAU,CAACW,oBAAoB;EAChD,MAAMC,SAAS,GAAGC,gBAAgB,CAACd,QAAQ,CAAC;EAC5C,OAAOjlB,GAAG,IAAIqlB,IAAI,CAACW,wBAAwB,CAAC,CAAC7O,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,OAAO,GAAG2O,SAAS,GAAG,GAAG,GAAG9lB,GAAG,EAAE8lB,SAAS,GAAG,GAAG,GAAG9lB,GAAG,EAAEA,GAAG,CAAC,EAAEA,GAAG,CAAC;AACrI;AACA,SAAS+lB,gBAAgBA,CAACd,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACpW,GAAG,CAAC,CAAC;AACpC;AAEA,SAASqW,6CAA6CA,CAACC,YAAY,EAAE;EACjE,MAAMC,2BAA2B,GAAGnqB,kCAAkC,CAACkqB,YAAY,CAAC;EACpF,OAAO,CAAC9hB,WAAW,EAAE4S,QAAQ,KAAK;IAC9B,IAAI5S,WAAW,IAAIA,WAAW,CAACrF,IAAI,EAC/B,OAAOonB,2BAA2B,CAAC,CAAC/hB,WAAW,CAAC8S,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC9S,WAAW,CAACrF,IAAI,CAAC,EAAEqF,WAAW,CAACrF,IAAI,CAAC;IAC1G,MAAMgB,GAAG,GAAGomB,2BAA2B,CAAC,CAACnP,QAAQ,CAACE,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,cAAc,GAAGF,QAAQ,CAACjY,IAAI,CAAC,EAAE6X,SAAS,CAAC;IAC/G,IAAI7W,GAAG,EACH,OAAOA,GAAG;IACd,OAAOomB,2BAA2B,CAAC,CAACnP,QAAQ,CAACE,QAAQ,IAAI,EAAE,CAAC,EAAE,CAACF,QAAQ,CAACjY,IAAI,IAAI,EAAE,CAAC,EAAEiY,QAAQ,CAACjY,IAAI,CAAC;EACvG,CAAC;AACL;AAEA,SAASqnB,yBAAyBA,CAACC,QAAQ,EAAE;EACzC,MAAMxV,UAAU,GAAG,EAAE;EACrBwV,QAAQ,CAACC,UAAU,CAACjX,OAAO,CAACkX,IAAI,IAAI;IAChC,IAAIA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,IAAInrB,aAAa,EAAE;MACrDwV,UAAU,CAACpB,IAAI,CAACpU,aAAa,CAACkrB,IAAI,CAACC,UAAU,CAAC,CAACD,IAAI,CAACpF,MAAM,CAAC,CAAC;IAChE;EACJ,CAAC,CAAC;EACF,OAAOtQ,UAAU;AACrB;AAEA,SAAS4V,sBAAsBA,CAACC,WAAW,EAAE;EACzC,OAAOA,WAAW,CAACC,OAAO,CAAC,kBAAkB,CAAC;AAClD;AACA,SAASC,kBAAkBA,CAACF,WAAW,EAAE;EACrC,OAAOA,WAAW,CAACC,OAAO,CAAC,cAAc,CAAC;AAC9C;AACA,SAASE,WAAWA,CAACH,WAAW,EAAE;EAC9B,OAAOD,sBAAsB,CAACC,WAAW,CAAC,CAACtqB,IAAI,CAACE,GAAG,CAAE0iB,UAAU,IAAKsE,MAAM,CAACQ,IAAI,CAAC9E,UAAU,CAAC8H,KAAK,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEjnB,GAAG,KAAK;IACnH,MAAM;MAAE0lB,MAAM;MAAEG;IAAqB,CAAC,GAAG5G,UAAU,CAAC8H,KAAK,CAAC/mB,GAAG,CAAC;IAC9DinB,GAAG,CAACjnB,GAAG,CAAC,GAAG;MACP0lB,MAAM;MACNG,oBAAoB;MACpBT,WAAW,EAAEP,UAAU,CAACa,MAAM;IAClC,CAAC;IACD,OAAOuB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ;AACA,SAASC,mCAAmCA,CAACP,WAAW,EAAEQ,SAAS,EAAE;EACjE,OAAOT,sBAAsB,CAACC,WAAW,CAAC,CAACtqB,IAAI,CAACE,GAAG,CAAC0iB,UAAU,IAAI;IAC9D,IAAI,CAACA,UAAU,EACX,OAAO,IAAI;IACf,OAAO,CAACA,UAAU,CAACmI,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC,CAAC,EACtCE,QAAQ;EACjB,CAAC,CAAC,EAAE9qB,GAAG,CAAC8qB,QAAQ,IAAKC,WAAW,CAACD,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAS,CAAC,EAAE1qB,MAAM,CAAC4qB,OAAO,CAAC,EAAE3qB,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3F;AACA,SAAS4qB,yBAAyBA,CAACb,WAAW,EAAExP,QAAQ,EAAE;EACtD,OAAO9a,IAAI,CAACK,SAAS,CAAE2qB,QAAQ,IAAK/qB,GAAG,CAACuqB,kBAAkB,CAACF,WAAW,CAAC,EAAEG,WAAW,CAACH,WAAW,CAAC,CAAC,CAACtqB,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC4pB,YAAY,EAAEY,KAAK,CAAC,KAAK;IACnI,MAAMU,mBAAmB,GAAGvB,6CAA6C,CAACC,YAAY,CAAC;IACvF,OAAO5C,MAAM,CAACQ,IAAI,CAACsD,QAAQ,CAAC,CAACL,MAAM,CAAC,CAACC,GAAG,EAAEjnB,GAAG,KAAK;MAC9CinB,GAAG,CAAC3oB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE;MAClBinB,GAAG,CAACS,UAAU,CAAC1nB,GAAG,CAAC,GAAG,EAAE;MACxBinB,GAAG,CAACU,QAAQ,CAAC3nB,GAAG,CAAC,GAAG,EAAE;MACtB,MAAM4nB,MAAM,GAAGP,QAAQ,CAACrnB,GAAG,CAAC;MAC5B,IAAI,CAAC4nB,MAAM,EACP,OAAOX,GAAG;MACd,MAAMjQ,UAAU,GAAG4Q,MAAM,CAAC5Q,UAAU;MACpC,IAAI,CAACA,UAAU,EACX,OAAOiQ,GAAG;MACd,MAAMY,2BAA2B,GAAGC,oCAAoC,CAACL,mBAAmB,EAAEtQ,QAAQ,EAAE4P,KAAK,CAAC;MAC9G,OAAOc,2BAA2B,CAAC7Q,UAAU,EAAEiQ,GAAG,EAAEjnB,GAAG,CAAC;IAC5D,CAAC,EAAE;MACC1B,IAAI,EAAE,CAAC,CAAC;MACRopB,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE,CAAC;IACf,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,EAAE/qB,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB;AACA,SAASkrB,oCAAoCA,CAACL,mBAAmB,EAAEtQ,QAAQ,EAAE4P,KAAK,EAAE;EAChF,OAAO,CAAC/P,UAAU,EAAE8M,YAAY,EAAE9jB,GAAG,KAAK;IACtC,MAAMwE,OAAO,GAAG,IAAI;IACpB,MAAMujB,4BAA4B,GAAGjR,mCAAmC,CAAC2Q,mBAAmB,EAAEzQ,UAAU,CAAC;IACzGuM,MAAM,CAACQ,IAAI,CAAC/M,UAAU,CAAC,CAAC1H,OAAO,CAAEtQ,IAAI,IAAK;MACtC,MAAMsnB,QAAQ,GAAGtP,UAAU,CAAChY,IAAI,CAAC;MACjC,MAAMmmB,QAAQ,GAAGnmB,IAAI;MACrB,MAAM6W,MAAM,GAAGyQ,QAAQ,CAAC0B,EAAE,CAACnS,MAAM,IAAI,CAAC,CAAC;MACvC,MAAMnJ,IAAI,GAAGiK,gBAAgB,CAACd,MAAM,EAAE7W,IAAI,CAAC,IAAIipB,mBAAmB,CAAC3B,QAAQ,CAAC;MAC5E,MAAM4B,UAAU,GAAG7Q,sBAAsB,CAACrY,IAAI,CAAC,GACzC+oB,4BAA4B,GAC5BN,mBAAmB;MACzB,MAAMpjB,WAAW,GAAG6jB,UAAU,CAAC5B,QAAQ,CAACjiB,WAAW,EAAE;QAAErF,IAAI;QAAEmY;MAAS,CAAC,CAAC;MACxE,IAAImP,QAAQ,CAAC0B,EAAE,CAACG,OAAO,CAAC5F,SAAS,EAAE;QAC/B,MAAMnV,QAAQ,GAAGma,OAAO,CAACjB,QAAQ,CAAC0B,EAAE,CAACG,OAAO,CAACC,UAAU,CAAC;QACxD,MAAMnL,WAAW,GAAGvQ,IAAI,KAAK,SAAS,CAAC,0BAA0B,GAAG,GAAG,GAAG;QAC1E,MAAMwQ,aAAa,GAAGxQ,IAAI,KAAK,MAAM,CAAC,wBAClC4Z,QAAQ,CAAC5Z,IAAI,GACXsY,uBAAuB,CAACsB,QAAQ,CAAC5Z,IAAI,EAAEqa,KAAK,CAACT,QAAQ,CAAC5Z,IAAI,CAAC,EAAEyY,QAAQ,CAAC,GACtET,gCAAgC,CAACS,QAAQ,CAAC;QAChD,MAAMkD,UAAU,GAAG,IAAIrL,UAAU,CAAC;UAC9BtQ,IAAI;UACJ1N,IAAI,EAAEmmB,QAAQ;UACd9gB,WAAW;UACX+I,QAAQ;UACR6P,WAAW;UACXC,aAAa;UACb1Y;QACJ,CAAC,CAAC;QACF,MAAMyf,WAAW,GAAI7U,QAAQ,IAAKA,QAAQ,CAACmB,OAAO,CAAC8X,UAAU,CAAC;QAC9DvE,YAAY,CAACxlB,IAAI,CAAC0B,GAAG,CAAC,CAAC0P,IAAI,CAACuU,WAAW,CAAC;MAC5C;MACA,MAAMqE,cAAc,GAAGhC,QAAQ,CAAC0B,EAAE,CAACO,YAAY,CAAChG,SAAS;MACzD,MAAMiG,YAAY,GAAGlC,QAAQ,CAAC0B,EAAE,CAACS,UAAU,CAAClG,SAAS;MACrD,IAAI+F,cAAc,IAAIE,YAAY,EAAE;QAChC,MAAMzX,YAAY,GAAGuV,QAAQ,CAACvV,YAAY;QAC1C,MAAMrN,QAAQ,GAAG4iB,QAAQ,CAAC5iB,QAAQ;QAClC,MAAMoN,UAAU,GAAGA,CAAA,KAAMuV,yBAAyB,CAACC,QAAQ,CAAC;QAC5D,IAAI1V,OAAO;QACX,IAAIlE,IAAI,KAAK,MAAM,CAAC,sBAChBkE,OAAO,GAAG6U,iBAAiB,CAACN,QAAQ,EAAE4B,KAAK,CAACT,QAAQ,CAAC5Z,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KACjE,IAAIA,IAAI,KAAK,WAAW,CAAC,2BAC1BkE,OAAO,GAAGgF,sBAAsB,CAACC,MAAM,CAAC;QAC5C,MAAM6S,QAAQ,GAAG,IAAI/X,QAAQ,CAAC;UAC1BjE,IAAI;UACJ1N,IAAI,EAAEmmB,QAAQ;UACd9gB,WAAW;UACXuM,OAAO;UACPG,YAAY;UACZD,UAAU;UACVtM,OAAO;UACPd;QACJ,CAAC,CAAC;QACF,MAAMilB,eAAe,GAAIvZ,QAAQ,IAAKA,QAAQ,CAACmB,OAAO,CAACmY,QAAQ,CAAC;QAChE,IAAIJ,cAAc,EACdxE,YAAY,CAAC4D,UAAU,CAAC1nB,GAAG,CAAC,CAAC0P,IAAI,CAACiZ,eAAe,CAAC;QACtD,IAAIH,YAAY,EACZ1E,YAAY,CAAC6D,QAAQ,CAAC3nB,GAAG,CAAC,CAAC0P,IAAI,CAACiZ,eAAe,CAAC;MACxD;IACJ,CAAC,CAAC;IACF,OAAO7E,YAAY;EACvB,CAAC;AACL;AACA,SAASmE,mBAAmBA,CAAC3B,QAAQ,EAAE;EACnC,OAAOA,QAAQ,EAAEG,UAAU,EAAElP,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACnD;AACA,SAAS+P,WAAWA,CAACsB,GAAG,EAAE;EACtB,OAAO,OAAOA,GAAG,KAAK,WAAW;AACrC;AAEA,MAAMC,gBAAgB,GAAG,CACrBzuB,iBAAiB,EACjBqX,iCAAiC,EACjC0H,2BAA2B,EAC3B+G,oBAAoB,EACpBjC,iBAAiB,EACjBkF,oBAAoB,EACpB7K,kBAAkB,EAClByG,uBAAuB,EACvB6B,wBAAwB,CAC3B;AACD,MAAMkI,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC5W,IAAI,YAAA6W,yBAAA3W,CAAA;MAAA,YAAAA,CAAA,IAAwF0W,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBA1qC8EpxB,EAAE,CAAAqxB,gBAAA;MAAAvc,IAAA,EA0qCSoc;IAAgB,EAuBvF;EAAE;EACtC;IAAS,IAAI,CAACI,IAAI,kBAlsC8EtxB,EAAE,CAAAuxB,gBAAA;MAAAvU,OAAA,GAksCqC1Y,UAAU,EACzIvB,iBAAiB,EACjBmC,qBAAqB,EACrBpD,mBAAmB,EACnBM,iBAAiB,EACjBL,mBAAmB,EACnBI,kBAAkB,EAClBE,gBAAgB,EAAEwX,iCAAiC,EACnD0H,2BAA2B,EAC3B+G,oBAAoB,EACpBiD,oBAAoB,EACpBpE,uBAAuB,EACvB6B,wBAAwB;IAAA,EAAI;EAAE;AAC1C;AACA;EAAA,QAAAnM,SAAA,oBAAAA,SAAA,KAhtCoG7c,EAAE,CAAA8c,iBAAA,CAgtCXoU,gBAAgB,EAAc,CAAC;IAC9Gpc,IAAI,EAAE5T,QAAQ;IACd6b,IAAI,EAAE,CAAC;MACCyU,YAAY,EAAE,EAAE;MAChBxU,OAAO,EAAE,CACL1Y,UAAU,EACVvB,iBAAiB,EACjBmC,qBAAqB,EACrBpD,mBAAmB,EACnBM,iBAAiB,EACjBL,mBAAmB,EACnBI,kBAAkB,EAClBE,gBAAgB,EAChB,GAAG4uB,gBAAgB,CACtB;MACDQ,OAAO,EAAE,CAAC,GAAGR,gBAAgB;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS1M,UAAU,EAAE1L,sBAAsB,EAAE6H,kBAAkB,EAAEjD,wBAAwB,EAAEqO,6BAA6B,EAAEvO,0BAA0B,EAAED,sBAAsB,EAAED,sBAAsB,EAAEM,oBAAoB,EAAEC,yBAAyB,EAAER,qBAAqB,EAAES,oBAAoB,EAAE/E,oBAAoB,EAAEkM,YAAY,EAAEH,gBAAgB,EAAEC,aAAa,EAAEC,oBAAoB,EAAEK,UAAU,EAAEH,cAAc,EAAEC,WAAW,EAAEC,kBAAkB,EAAEtL,iCAAiC,EAAEsN,uBAAuB,EAAE5F,2BAA2B,EAAE2P,gBAAgB,EAAElI,wBAAwB,EAAEnD,iBAAiB,EAAE9M,QAAQ,EAAES,YAAY,EAAEtB,YAAY,EAAEC,SAAS,EAAEmQ,oBAAoB,EAAEoD,gBAAgB,IAAIgG,gBAAgB,EAAElU,gBAAgB,EAAE+N,oBAAoB,EAAElF,iBAAiB,EAAEvP,QAAQ,EAAE6O,aAAa,EAAEH,iBAAiB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEE,gBAAgB,EAAEkH,gCAAgC,EAAER,qBAAqB,EAAEgD,mCAAmC,EAAEM,yBAAyB,EAAE7D,uBAAuB,EAAEgB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}