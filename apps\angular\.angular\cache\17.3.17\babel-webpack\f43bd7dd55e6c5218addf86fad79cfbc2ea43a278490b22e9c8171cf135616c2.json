{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport { BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { FileUnitsEnum } from '@app/shared/constants/fileUnitsEnum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/essresource-files\";\nimport * as i2 from \"@angular/material/progress-spinner\";\nimport * as i3 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../search-headers/search-headers.component\";\nfunction ResourcePageComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"mode\", \"indeterminate\")(\"diameter\", 100);\n  }\n}\nexport let ResourcePageComponent = /*#__PURE__*/(() => {\n  class ResourcePageComponent extends AppComponentBase {\n    constructor(injector, resourceApiService) {\n      super(injector);\n      this.resourceApiService = resourceApiService;\n      this.resourseList = [];\n      this.filteredList = [];\n      this.isLoading = false;\n      this.currentNameFilter = \"\";\n      this.currentPageIndex = 0;\n      this.pageSizeOptions = [10, 20, 50, 100];\n      this.historyRequstDto = {\n        maxResultCount: 10,\n        sorting: \"periodEndYear desc\",\n        skipCount: 0\n      };\n      this.resourceRequestDto = {\n        maxResultCount: 10,\n        sorting: \"fileName asc\",\n        skipCount: 0\n      };\n      this.TableId = 'resource-table';\n      this.declarationHistoryColumns = [{\n        columnId: 'fileName',\n        type: BdoTableColumnType.Link,\n        minWidth: 700,\n        isSortable: true,\n        columnName: 'Name'\n      }, {\n        columnId: 'lastModified',\n        type: BdoTableColumnType.Date,\n        minWidth: 100,\n        isSortable: true,\n        columnName: 'Last Modified'\n      }, {\n        columnId: 'size',\n        type: BdoTableColumnType.String,\n        minWidth: 100,\n        isSortable: true,\n        columnName: 'Size'\n      }];\n      this.PageSize = 5;\n    }\n    ngOnInit() {\n      this.isLoading = true;\n      this.getListofFiles();\n    }\n    sizeConverter(fileSize, fileUnit) {\n      const fileUnitMapper = {\n        0: size => size + ' ' + FileUnitsEnum.Bytes,\n        1: size => size + ' ' + FileUnitsEnum.Kilobytes,\n        2: size => size + ' ' + FileUnitsEnum.Megabytes,\n        3: size => size + ' ' + FileUnitsEnum.Gigabytes,\n        4: size => size + ' ' + FileUnitsEnum.Terabytes\n      };\n      return fileUnitMapper[fileUnit] ? fileUnitMapper[fileUnit](fileSize) : '';\n    }\n    getListofFiles() {\n      this.filteredList = [];\n      this.resourseList = [];\n      this.resourceApiService.getAllResourceFilesByInputAndFilterName(this.resourceRequestDto, this.currentNameFilter).subscribe(result => {\n        result.resourceFiles.forEach(element => {\n          const size = this.sizeConverter(element.displaySize, element.displayUnit);\n          this.resourseList.push({\n            id: element.fileName,\n            name: element.fileName,\n            lastModified: element.lastModified,\n            size: size\n          });\n        });\n        this.isLoading = false;\n        this.filteredList = this.resourseList;\n        this.setTableData();\n      });\n    }\n    onLazyLoadEvent(event) {\n      var sortDir = event.isAscending ? \"asc\" /* SortDirection.ASCENDING */ : \"desc\" /* SortDirection.DESCENDING */;\n      this.resourceRequestDto.sorting = event.sortField + ' ' + sortDir;\n      this.getListofFiles();\n    }\n    filterTable() {\n      const filterByName = this.currentNameFilter;\n      if (filterByName) {\n        this.filteredList = this.resourseList.filter(element => element.name.toLowerCase().replace(/\\s+/g, '').includes(this.currentNameFilter.toLowerCase().replace(/\\s+/g, '')));\n      } else {\n        this.filteredList = this.resourseList;\n      }\n      this.setTableData();\n    }\n    onSearchClick(event) {\n      this.currentNameFilter = event.fileName;\n      this.getListofFiles();\n    }\n    onClearClick() {\n      this.currentNameFilter = '';\n      this.setTableData();\n    }\n    setTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = false;\n      tableData.tableId = this.TableId;\n      tableData.totalRecords = this.filteredList.length;\n      tableData.data = this.filteredList.map(x => {\n        return {\n          id: x.id,\n          rawData: x,\n          cells: [{\n            columnId: 'fileName',\n            value: x.name\n          }, {\n            columnId: 'lastModified',\n            value: x.lastModified\n          }, {\n            columnId: 'size',\n            value: x.size\n          }]\n        };\n      });\n      this.tableService.setGridData(tableData);\n    }\n    onLinkClick(event) {\n      this.downloadFile(event.rawData.name);\n    }\n    generateDummyBase64String() {\n      const dummyText = 'This is a sample text for testing.';\n      const base64String = btoa(dummyText); // Convert text to base64\n      return base64String;\n    }\n    downloadFile(filename) {\n      this.resourceApiService.downloadResourceFileByFileName(filename).subscribe(blob => {\n        this.saveBlobToFile(blob, filename);\n      });\n    }\n    saveBlobToFile(blob, fileName) {\n      // Create a blob URL\n      const blobURL = window.URL.createObjectURL(blob);\n      // Create an anchor element for the download\n      const a = document.createElement(\"a\");\n      a.href = blobURL;\n      a.download = fileName || 'ess-download-file.dat'; // Provide a default file name if none is provided\n      // Append the anchor to the document\n      document.body.appendChild(a);\n      // Simulate a click on the anchor to initiate the download\n      a.click();\n      // Clean up: remove the anchor and revoke the blob URL\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(blobURL);\n    }\n    /*\n    downloadFile(filename:string):void{\n      this.resourceApiService.downloadResourceFileByFileName(filename).subscribe(result => {\n        const blob = this.base64ToBlob(result.fileContent.toString(),  'application/' + result.fileType);\n        saveAs(blob, result.fileName);\n      })\n    }\n    */\n    base64ToBlob(base64, contentType) {\n      const byteCharacters = atob(base64);\n      const byteArray = new Uint8Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteArray[i] = byteCharacters.charCodeAt(i);\n      }\n      return new Blob([byteArray], {\n        type: contentType\n      });\n    }\n    static {\n      this.ɵfac = function ResourcePageComponent_Factory(t) {\n        return new (t || ResourcePageComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.ESSResourceFileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ResourcePageComponent,\n        selectors: [[\"app-resource-page\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 7,\n        vars: 12,\n        consts: [[\"class\", \"mat-spinner-center resource-spinner\", 4, \"ngIf\"], [3, \"hidden\"], [1, \"title-color-font\"], [3, \"onSearchClick\", \"onClearClick\"], [\"scrollHeight\", \"50.3em \", \"defaultSortColumnId\", \"fileName\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"hidePagination\", \"rowSelectable\", \"pageIndex\", \"pageSizeOptions\", \"pageSize\", \"isVirtualScroll\", \"lazyLoad\"], [1, \"mat-spinner-center\", \"resource-spinner\"], [1, \"mat-spinner-color\", 3, \"mode\", \"diameter\"]],\n        template: function ResourcePageComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ResourcePageComponent_div_0_Template, 2, 2, \"div\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\")(3, \"h1\", 2);\n            i0.ɵɵtext(4, \"RESOURCES\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"app-search-headers\", 3);\n            i0.ɵɵlistener(\"onSearchClick\", function ResourcePageComponent_Template_app_search_headers_onSearchClick_5_listener($event) {\n              return ctx.onSearchClick($event);\n            })(\"onClearClick\", function ResourcePageComponent_Template_app_search_headers_onClearClick_5_listener() {\n              return ctx.onClearClick();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"bdo-table\", 4);\n            i0.ɵɵlistener(\"onLazyLoad\", function ResourcePageComponent_Template_bdo_table_onLazyLoad_6_listener($event) {\n              return ctx.onLazyLoadEvent($event);\n            })(\"onLinkClick\", function ResourcePageComponent_Template_bdo_table_onLinkClick_6_listener($event) {\n              return ctx.onLinkClick($event);\n            });\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"hidden\", ctx.isLoading);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.declarationHistoryColumns)(\"defaultSortOrder\", \"asc\")(\"hidePagination\", true)(\"rowSelectable\", false)(\"pageIndex\", ctx.currentPageIndex)(\"pageSizeOptions\", ctx.pageSizeOptions)(\"pageSize\", ctx.PageSize)(\"isVirtualScroll\", false)(\"lazyLoad\", true);\n          }\n        },\n        dependencies: [i2.MatProgressSpinner, i3.BdoTableComponent, i4.NgIf, i5.SearchHeadersComponent],\n        styles: [\".resource-spinner[_ngcontent-%COMP%]{transform:translate(44em,18em)!important}\"]\n      });\n    }\n  }\n  return ResourcePageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}