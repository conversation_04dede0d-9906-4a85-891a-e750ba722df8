{"ast": null, "code": "import { ListService } from '@abp/ng.core';\nimport { Confirmation } from '@abp/ng.theme.shared';\nimport { EXTENSIONS_IDENTIFIER, FormPropData, generateFormFromProps } from '@abp/ng.components/extensible';\nimport { finalize, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nimport * as i2 from \"@abp/ng.theme.shared\";\nimport * as i3 from \"@volo/abp.ng.identity/proxy\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@abp/ng.components/extensible\";\nimport * as i6 from \"@volo/abp.commercial.ng.ui\";\nimport * as i7 from \"@ngx-validate/core\";\nimport * as i8 from \"@abp/ng.components/page\";\nimport * as i9 from \"../claim-modal/claim-modal.component\";\nimport * as i10 from \"../permission-management/permission-management.component\";\nconst _c0 = [\"modalContent\"];\nconst _c1 = () => ({\n  value: \"R\"\n});\nconst _c2 = a0 => ({\n  value: a0\n});\nconst _c3 = () => ({\n  value: true\n});\nconst _c4 = a0 => ({\n  value: a0,\n  twoWay: true\n});\nconst _c5 = (a0, a1, a2, a3) => ({\n  providerName: a0,\n  providerKey: a1,\n  hideBadges: a2,\n  visible: a3\n});\nconst _c6 = a0 => ({\n  visibleChange: a0\n});\nconst _c7 = (a0, a1) => ({\n  inputs: a0,\n  outputs: a1,\n  componentKey: \"PermissionManagement.PermissionManagementComponent\"\n});\nfunction RolesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, (ctx_r1.selected == null ? null : ctx_r1.selected.id) ? \"AbpIdentity::Edit\" : \"AbpIdentity::NewRole\"));\n  }\n}\nfunction RolesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 11);\n    i0.ɵɵlistener(\"ngSubmit\", function RolesComponent_ng_template_9_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.save());\n    });\n    i0.ɵɵelement(1, \"abp-extensible-form\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedRecord\", ctx_r1.selected);\n  }\n}\nfunction RolesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"abp-button\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpIdentity::Cancel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, \"AbpIdentity::Save\"));\n  }\n}\nfunction RolesComponent_abp_custom_permission_management_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"abp-custom-permission-management\", 15);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function RolesComponent_abp_custom_permission_management_13_Template_abp_custom_permission_management_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.visiblePermissions, $event) || (ctx_r1.visiblePermissions = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.visiblePermissions);\n    i0.ɵɵproperty(\"providerKey\", ctx_r1.providerKey)(\"hideBadges\", true);\n  }\n}\nexport class RolesComponent {\n  constructor(list, confirmationService, service, configState, injector) {\n    this.list = list;\n    this.confirmationService = confirmationService;\n    this.service = service;\n    this.configState = configState;\n    this.injector = injector;\n    this.data = {\n      items: [],\n      totalCount: 0\n    };\n    this.visiblePermissions = false;\n    this.modalBusy = false;\n    this.visibleClaims = false;\n    this.claimSubject = {};\n    //determines whether logged on user has admin role\n    this.hasAdminRole = false;\n    this.onVisiblePermissionChange = value => {\n      this.visiblePermissions = value;\n    };\n    this.currentUser = this.configState.getOne('currentUser');\n    this.hasAdminRole = this.currentUser.roles.includes('admin');\n  }\n  createForm() {\n    const data = new FormPropData(this.injector, this.selected);\n    this.form = generateFormFromProps(data);\n  }\n  hookToQuery() {\n    this.list.hookToQuery(query => this.service.getList(query)).subscribe(res => {\n      this.data = res;\n      //if user doesn't have admin role, exclude it from the list\n      if (!this.hasAdminRole) {\n        this.data.items = res.items.filter(item => item.name !== 'admin');\n        this.data.totalCount = this.data.items.length;\n      }\n    });\n  }\n  ngOnInit() {\n    this.hookToQuery();\n  }\n  openModal() {\n    this.createForm();\n    this.isModalVisible = true;\n  }\n  onAdd() {\n    this.selected = {};\n    this.openModal();\n  }\n  onEdit(id) {\n    this.service.get(id).pipe(take(1)).subscribe(selectedRole => {\n      this.selected = selectedRole;\n      this.openModal();\n    });\n  }\n  save() {\n    if (!this.form.valid) return;\n    this.modalBusy = true;\n    const {\n      id\n    } = this.selected;\n    (id ? this.service.update(id, {\n      ...this.selected,\n      ...this.form.value,\n      id: this.selected.id,\n      concurrencyStamp: this.selected.concurrencyStamp\n    }) : this.service.create(this.form.value)).pipe(finalize(() => this.modalBusy = false)).subscribe(() => {\n      this.list.get();\n      this.isModalVisible = false;\n    });\n  }\n  delete(id, name) {\n    this.confirmationService.warn('AbpIdentity::RoleDeletionConfirmationMessage', 'AbpIdentity::AreYouSure', {\n      messageLocalizationParams: [name]\n    }).subscribe(status => {\n      if (status === Confirmation.Status.confirm) {\n        this.service.delete(id).subscribe(() => this.list.get());\n      }\n    });\n  }\n  onManageClaims(id) {\n    this.claimSubject = {\n      id,\n      type: 'roles'\n    };\n    this.visibleClaims = true;\n  }\n  openPermissionsModal(providerKey) {\n    this.providerKey = providerKey;\n    setTimeout(() => {\n      this.visiblePermissions = true;\n    }, 0);\n  }\n  static {\n    this.ɵfac = function RolesComponent_Factory(t) {\n      return new (t || RolesComponent)(i0.ɵɵdirectiveInject(i1.ListService), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i3.IdentityRoleService), i0.ɵɵdirectiveInject(i1.ConfigStateService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RolesComponent,\n      selectors: [[\"abp-roles\"]],\n      viewQuery: function RolesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalContent = _t.first);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([ListService, {\n        provide: EXTENSIONS_IDENTIFIER,\n        useValue: \"Identity.RolesComponent\" /* eIdentityComponents.Roles */\n      }])],\n      decls: 15,\n      vars: 29,\n      consts: [[\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [3, \"title\", \"toolbar\"], [\"id\", \"identity-roles-wrapper\"], [\"localizationSourceName\", \"AbpIdentity\", 3, \"list\"], [1, \"card\"], [3, \"data\", \"recordsTotal\", \"list\"], [3, \"visibleChange\", \"visible\", \"busy\"], [\"providerName\", \"R\", 3, \"visible\", \"providerKey\", \"hideBadges\", \"visibleChange\", 4, \"abpReplaceableTemplate\"], [3, \"visibleChange\", \"visible\", \"subject\"], [\"id\", \"roleForm\", \"validateOnSubmit\", \"\", 3, \"ngSubmit\", \"formGroup\"], [3, \"selectedRecord\"], [\"type\", \"button\", \"abpClose\", \"\", 1, \"btn\", \"btn-secondary\"], [\"iconClass\", \"fa fa-check\", \"buttonType\", \"submit\", \"formName\", \"roleForm\"], [\"providerName\", \"R\", 3, \"visibleChange\", \"visible\", \"providerKey\", \"hideBadges\"]],\n      template: function RolesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"abp-page\", 3);\n          i0.ɵɵpipe(1, \"abpLocalization\");\n          i0.ɵɵelementStart(2, \"div\", 4);\n          i0.ɵɵelement(3, \"abp-advanced-entity-filters\", 5);\n          i0.ɵɵelementStart(4, \"div\", 6);\n          i0.ɵɵelement(5, \"abp-extensible-table\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"abp-modal\", 8);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function RolesComponent_Template_abp_modal_visibleChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isModalVisible, $event) || (ctx.isModalVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(7, RolesComponent_ng_template_7_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(9, RolesComponent_ng_template_9_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(11, RolesComponent_ng_template_11_Template, 6, 6, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, RolesComponent_abp_custom_permission_management_13_Template, 1, 3, \"abp-custom-permission-management\", 9);\n          i0.ɵɵelementStart(14, \"abp-claim-modal\", 10);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function RolesComponent_Template_abp_claim_modal_visibleChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.visibleClaims, $event) || (ctx.visibleClaims = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 11, \"AbpIdentity::Roles\"))(\"toolbar\", ctx.data.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"list\", ctx.list);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.data.items)(\"recordsTotal\", ctx.data.totalCount)(\"list\", ctx.list);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.isModalVisible);\n          i0.ɵɵproperty(\"busy\", ctx.modalBusy);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction2(26, _c7, i0.ɵɵpureFunction4(19, _c5, i0.ɵɵpureFunction0(13, _c1), i0.ɵɵpureFunction1(14, _c2, ctx.providerKey), i0.ɵɵpureFunction0(16, _c3), i0.ɵɵpureFunction1(17, _c4, ctx.visiblePermissions)), i0.ɵɵpureFunction1(24, _c6, ctx.onVisiblePermissionChange)));\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visibleClaims);\n          i0.ɵɵproperty(\"subject\", ctx.claimSubject);\n        }\n      },\n      dependencies: [i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, i1.FormSubmitDirective, i1.ReplaceableTemplateDirective, i5.ExtensibleFormComponent, i5.ExtensibleTableComponent, i6.AdvancedEntityFiltersComponent, i7.ValidationGroupDirective, i2.ButtonComponent, i2.ModalComponent, i2.ModalCloseDirective, i8.PageComponent, i9.ClaimModalComponent, i10.CustomPermissionManagementComponent, i1.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ListService", "Confirmation", "EXTENSIONS_IDENTIFIER", "FormPropData", "generateFormFromProps", "finalize", "take", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ctx_r1", "selected", "id", "ɵɵlistener", "RolesComponent_ng_template_9_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "save", "ɵɵelement", "ɵɵproperty", "form", "ɵɵtextInterpolate1", "ɵɵtwoWayListener", "RolesComponent_abp_custom_permission_management_13_Template_abp_custom_permission_management_visibleChange_0_listener", "$event", "_r4", "ɵɵtwoWayBindingSet", "visiblePermissions", "ɵɵtwoWayProperty", "providerKey", "RolesComponent", "constructor", "list", "confirmationService", "service", "configState", "injector", "data", "items", "totalCount", "modalBusy", "visibleClaims", "claimSubject", "hasAdminRole", "onVisiblePermissionChange", "value", "currentUser", "getOne", "roles", "includes", "createForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "query", "getList", "subscribe", "res", "filter", "item", "name", "length", "ngOnInit", "openModal", "isModalVisible", "onAdd", "onEdit", "get", "pipe", "selectedR<PERSON>", "valid", "update", "concurrencyStamp", "create", "delete", "warn", "messageLocalizationParams", "status", "Status", "confirm", "onManageClaims", "type", "openPermissionsModal", "setTimeout", "ɵɵdirectiveInject", "i1", "i2", "ConfirmationService", "i3", "IdentityRoleService", "ConfigStateService", "Injector", "selectors", "viewQuery", "RolesComponent_Query", "rf", "ctx", "provide", "useValue", "decls", "vars", "consts", "template", "RolesComponent_Template", "RolesComponent_Template_abp_modal_visibleChange_6_listener", "_r1", "ɵɵtemplate", "RolesComponent_ng_template_7_Template", "ɵɵtemplateRefExtractor", "RolesComponent_ng_template_9_Template", "RolesComponent_ng_template_11_Template", "RolesComponent_abp_custom_permission_management_13_Template", "RolesComponent_Template_abp_claim_modal_visibleChange_14_listener", "ɵɵpureFunction2", "_c7", "ɵɵpureFunction4", "_c5", "ɵɵpureFunction0", "_c1", "ɵɵpureFunction1", "_c2", "_c3", "_c4", "_c6"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\components\\roles\\roles.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\components\\roles\\roles.component.html"], "sourcesContent": ["import { ConfigStateService, CurrentUserDto, ListService, PagedResultDto } from '@abp/ng.core';\r\nimport { Confirmation, ConfirmationService } from '@abp/ng.theme.shared';\r\nimport {\r\n  EXTENSIONS_IDENTIFIER,\r\n  FormPropData,\r\n  generateFormFromProps,\r\n} from '@abp/ng.components/extensible';\r\nimport { Component, Injector, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { UntypedFormGroup } from '@angular/forms';\r\nimport {\r\n  GetIdentityRoleListInput,\r\n  IdentityRoleDto,\r\n  IdentityRoleService,\r\n} from '@volo/abp.ng.identity/proxy';\r\nimport { finalize, take } from 'rxjs/operators';\r\nimport { eIdentityComponents } from '../../enums/components';\r\n\r\n@Component({\r\n  selector: 'abp-roles',\r\n  templateUrl: './roles.component.html',\r\n  providers: [\r\n    ListService,\r\n    {\r\n      provide: EXTENSIONS_IDENTIFIER,\r\n      useValue: eIdentityComponents.Roles,\r\n    },\r\n  ],\r\n})\r\nexport class RolesComponent implements OnInit {\r\n  data: PagedResultDto<IdentityRoleDto> = { items: [], totalCount: 0 };\r\n\r\n  form: UntypedFormGroup;\r\n\r\n  selected: IdentityRoleDto;\r\n\r\n  isModalVisible: boolean;\r\n\r\n  visiblePermissions = false;\r\n\r\n  providerKey: string;\r\n\r\n  modalBusy = false;\r\n\r\n  visibleClaims = false;\r\n\r\n  claimSubject = {} as { id: string; type: 'roles' | 'users' };\r\n\r\n  @ViewChild('modalContent')\r\n  modalContent: TemplateRef<any>;\r\n\r\n  currentUser: CurrentUserDto;\r\n\r\n  //determines whether logged on user has admin role\r\n  hasAdminRole: boolean = false;\r\n\r\n  onVisiblePermissionChange = (value: boolean) => {\r\n    this.visiblePermissions = value;\r\n  };\r\n\r\n  constructor(\r\n    public readonly list: ListService<GetIdentityRoleListInput>,\r\n    protected confirmationService: ConfirmationService,\r\n    protected service: IdentityRoleService,\r\n    private configState: ConfigStateService,\r\n    protected injector: Injector,\r\n  ) {\r\n    this.currentUser = this.configState.getOne('currentUser') as CurrentUserDto;\r\n    this.hasAdminRole = this.currentUser.roles.includes('admin');\r\n  }\r\n\r\n  private createForm() {\r\n    const data = new FormPropData(this.injector, this.selected);\r\n    this.form = generateFormFromProps(data);\r\n  }\r\n\r\n  private hookToQuery() {\r\n    this.list.hookToQuery(query => this.service.getList(query)).subscribe(res => {\r\n      this.data = res;\r\n\r\n      //if user doesn't have admin role, exclude it from the list\r\n      if (!this.hasAdminRole) {\r\n        this.data.items = res.items.filter((item) => item.name !== 'admin');\r\n        this.data.totalCount = this.data.items.length;\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.hookToQuery();\r\n  }\r\n\r\n  openModal() {\r\n    this.createForm();\r\n    this.isModalVisible = true;\r\n  }\r\n\r\n  onAdd() {\r\n    this.selected = {} as IdentityRoleDto;\r\n    this.openModal();\r\n  }\r\n\r\n  onEdit(id: string) {\r\n    this.service\r\n      .get(id)\r\n      .pipe(take(1))\r\n      .subscribe(selectedRole => {\r\n        this.selected = selectedRole;\r\n        this.openModal();\r\n      });\r\n  }\r\n\r\n  save() {\r\n    if (!this.form.valid) return;\r\n    this.modalBusy = true;\r\n\r\n    const { id } = this.selected;\r\n\r\n    (id\r\n      ? this.service.update(id, {\r\n          ...this.selected,\r\n          ...this.form.value,\r\n          id: this.selected.id,\r\n          concurrencyStamp: this.selected.concurrencyStamp,\r\n        })\r\n      : this.service.create(this.form.value)\r\n    )\r\n      .pipe(finalize(() => (this.modalBusy = false)))\r\n      .subscribe(() => {\r\n        this.list.get();\r\n        this.isModalVisible = false;\r\n      });\r\n  }\r\n\r\n  delete(id: string, name: string) {\r\n    this.confirmationService\r\n      .warn('AbpIdentity::RoleDeletionConfirmationMessage', 'AbpIdentity::AreYouSure', {\r\n        messageLocalizationParams: [name],\r\n      })\r\n      .subscribe((status: Confirmation.Status) => {\r\n        if (status === Confirmation.Status.confirm) {\r\n          this.service.delete(id).subscribe(() => this.list.get());\r\n        }\r\n      });\r\n  }\r\n\r\n  onManageClaims(id: string) {\r\n    this.claimSubject = {\r\n      id,\r\n      type: 'roles',\r\n    };\r\n\r\n    this.visibleClaims = true;\r\n  }\r\n\r\n  openPermissionsModal(providerKey: string) {\r\n    this.providerKey = providerKey;\r\n    setTimeout(() => {\r\n      this.visiblePermissions = true;\r\n    }, 0);\r\n  }\r\n}\r\n", "<abp-page [title]=\"'AbpIdentity::Roles' | abpLocalization\" [toolbar]=\"data.items\">\r\n  <div id=\"identity-roles-wrapper\">\r\n    <abp-advanced-entity-filters\r\n      localizationSourceName=\"AbpIdentity\"\r\n      [list]=\"list\"\r\n    ></abp-advanced-entity-filters>\r\n\r\n    <div class=\"card\">\r\n      <abp-extensible-table\r\n        [data]=\"data.items\"\r\n        [recordsTotal]=\"data.totalCount\"\r\n        [list]=\"list\"\r\n      ></abp-extensible-table>\r\n    </div>\r\n  </div>\r\n</abp-page>\r\n\r\n<abp-modal [(visible)]=\"isModalVisible\" [busy]=\"modalBusy\">\r\n  <ng-template #abpHeader>\r\n    <h3>{{ (selected?.id ? 'AbpIdentity::Edit' : 'AbpIdentity::NewRole') | abpLocalization }}</h3>\r\n  </ng-template>\r\n\r\n  <ng-template #abpBody>\r\n    <form [formGroup]=\"form\" id=\"roleForm\" (ngSubmit)=\"save()\" validateOnSubmit>\r\n      <abp-extensible-form [selectedRecord]=\"selected\"></abp-extensible-form>\r\n    </form>\r\n  </ng-template>\r\n\r\n  <ng-template #abpFooter>\r\n    <button type=\"button\" class=\"btn btn-secondary\" abpClose>\r\n      {{ 'AbpIdentity::Cancel' | abpLocalization }}\r\n    </button>\r\n    <abp-button iconClass=\"fa fa-check\" buttonType=\"submit\" formName=\"roleForm\">{{\r\n      'AbpIdentity::Save' | abpLocalization\r\n    }}</abp-button>\r\n  </ng-template>\r\n</abp-modal>\r\n\r\n<abp-custom-permission-management\r\n  *abpReplaceableTemplate=\"{\r\n    inputs: {\r\n      providerName: { value: 'R' },\r\n      providerKey: { value: providerKey },\r\n      hideBadges: { value: true },\r\n      visible: { value: visiblePermissions, twoWay: true }\r\n    },\r\n    outputs: { visibleChange: onVisiblePermissionChange },\r\n    componentKey: 'PermissionManagement.PermissionManagementComponent'\r\n  }\"\r\n  [(visible)]=\"visiblePermissions\"\r\n  [providerKey]=\"providerKey\"\r\n  providerName=\"R\"\r\n  [hideBadges]=\"true\"\r\n>\r\n</abp-custom-permission-management>\r\n\r\n<abp-claim-modal [(visible)]=\"visibleClaims\" [subject]=\"claimSubject\"></abp-claim-modal>\r\n"], "mappings": "AAAA,SAA6CA,WAAW,QAAwB,cAAc;AAC9F,SAASC,YAAY,QAA6B,sBAAsB;AACxE,SACEC,qBAAqB,EACrBC,YAAY,EACZC,qBAAqB,QAChB,+BAA+B;AAQtC,SAASC,QAAQ,EAAEC,IAAI,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICK3CC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1FH,EAAA,CAAAI,SAAA,EAAqF;IAArFJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,QAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,EAAA,kDAAqF;;;;;;IAIzFT,EAAA,CAAAC,cAAA,eAA4E;IAArCD,EAAA,CAAAU,UAAA,sBAAAC,+DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAYR,MAAA,CAAAS,IAAA,EAAM;IAAA,EAAC;IACxDhB,EAAA,CAAAiB,SAAA,8BAAuE;IACzEjB,EAAA,CAAAG,YAAA,EAAO;;;;IAFDH,EAAA,CAAAkB,UAAA,cAAAX,MAAA,CAAAY,IAAA,CAAkB;IACDnB,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAkB,UAAA,mBAAAX,MAAA,CAAAC,QAAA,CAA2B;;;;;IAKlDR,EAAA,CAAAC,cAAA,iBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,qBAA4E;IAAAD,EAAA,CAAAE,MAAA,GAE1E;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;IAJbH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAM,WAAA,mCACF;IAC4EN,EAAA,CAAAI,SAAA,GAE1E;IAF0EJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4BAE1E;;;;;;IAINN,EAAA,CAAAC,cAAA,2CAeC;IAJCD,EAAA,CAAAqB,gBAAA,2BAAAC,sHAAAC,MAAA;MAAAvB,EAAA,CAAAY,aAAA,CAAAY,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAyB,kBAAA,CAAAlB,MAAA,CAAAmB,kBAAA,EAAAH,MAAA,MAAAhB,MAAA,CAAAmB,kBAAA,GAAAH,MAAA;MAAA,OAAAvB,EAAA,CAAAe,WAAA,CAAAQ,MAAA;IAAA,EAAgC;IAKlCvB,EAAA,CAAAG,YAAA,EAAmC;;;;IALjCH,EAAA,CAAA2B,gBAAA,YAAApB,MAAA,CAAAmB,kBAAA,CAAgC;IAGhC1B,EAFA,CAAAkB,UAAA,gBAAAX,MAAA,CAAAqB,WAAA,CAA2B,oBAER;;;ADxBrB,OAAM,MAAOC,cAAc;EA+BzBC,YACkBC,IAA2C,EACjDC,mBAAwC,EACxCC,OAA4B,EAC9BC,WAA+B,EAC7BC,QAAkB;IAJZ,KAAAJ,IAAI,GAAJA,IAAI;IACV,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,OAAO,GAAPA,OAAO;IACT,KAAAC,WAAW,GAAXA,WAAW;IACT,KAAAC,QAAQ,GAARA,QAAQ;IAnCpB,KAAAC,IAAI,GAAoC;MAAEC,KAAK,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAC,CAAE;IAQpE,KAAAZ,kBAAkB,GAAG,KAAK;IAI1B,KAAAa,SAAS,GAAG,KAAK;IAEjB,KAAAC,aAAa,GAAG,KAAK;IAErB,KAAAC,YAAY,GAAG,EAA6C;IAO5D;IACA,KAAAC,YAAY,GAAY,KAAK;IAE7B,KAAAC,yBAAyB,GAAIC,KAAc,IAAI;MAC7C,IAAI,CAAClB,kBAAkB,GAAGkB,KAAK;IACjC,CAAC;IASC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACX,WAAW,CAACY,MAAM,CAAC,aAAa,CAAmB;IAC3E,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACG,WAAW,CAACE,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC;EAC9D;EAEQC,UAAUA,CAAA;IAChB,MAAMb,IAAI,GAAG,IAAIxC,YAAY,CAAC,IAAI,CAACuC,QAAQ,EAAE,IAAI,CAAC3B,QAAQ,CAAC;IAC3D,IAAI,CAACW,IAAI,GAAGtB,qBAAqB,CAACuC,IAAI,CAAC;EACzC;EAEQc,WAAWA,CAAA;IACjB,IAAI,CAACnB,IAAI,CAACmB,WAAW,CAACC,KAAK,IAAI,IAAI,CAAClB,OAAO,CAACmB,OAAO,CAACD,KAAK,CAAC,CAAC,CAACE,SAAS,CAACC,GAAG,IAAG;MAC1E,IAAI,CAAClB,IAAI,GAAGkB,GAAG;MAEf;MACA,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE;QACtB,IAAI,CAACN,IAAI,CAACC,KAAK,GAAGiB,GAAG,CAACjB,KAAK,CAACkB,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,OAAO,CAAC;QACnE,IAAI,CAACrB,IAAI,CAACE,UAAU,GAAG,IAAI,CAACF,IAAI,CAACC,KAAK,CAACqB,MAAM;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,EAAE;EACpB;EAEAU,SAASA,CAAA;IACP,IAAI,CAACX,UAAU,EAAE;IACjB,IAAI,CAACY,cAAc,GAAG,IAAI;EAC5B;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACtD,QAAQ,GAAG,EAAqB;IACrC,IAAI,CAACoD,SAAS,EAAE;EAClB;EAEAG,MAAMA,CAACtD,EAAU;IACf,IAAI,CAACwB,OAAO,CACT+B,GAAG,CAACvD,EAAE,CAAC,CACPwD,IAAI,CAAClE,IAAI,CAAC,CAAC,CAAC,CAAC,CACbsD,SAAS,CAACa,YAAY,IAAG;MACxB,IAAI,CAAC1D,QAAQ,GAAG0D,YAAY;MAC5B,IAAI,CAACN,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEA5C,IAAIA,CAAA;IACF,IAAI,CAAC,IAAI,CAACG,IAAI,CAACgD,KAAK,EAAE;IACtB,IAAI,CAAC5B,SAAS,GAAG,IAAI;IAErB,MAAM;MAAE9B;IAAE,CAAE,GAAG,IAAI,CAACD,QAAQ;IAE5B,CAACC,EAAE,GACC,IAAI,CAACwB,OAAO,CAACmC,MAAM,CAAC3D,EAAE,EAAE;MACtB,GAAG,IAAI,CAACD,QAAQ;MAChB,GAAG,IAAI,CAACW,IAAI,CAACyB,KAAK;MAClBnC,EAAE,EAAE,IAAI,CAACD,QAAQ,CAACC,EAAE;MACpB4D,gBAAgB,EAAE,IAAI,CAAC7D,QAAQ,CAAC6D;KACjC,CAAC,GACF,IAAI,CAACpC,OAAO,CAACqC,MAAM,CAAC,IAAI,CAACnD,IAAI,CAACyB,KAAK,CAAC,EAErCqB,IAAI,CAACnE,QAAQ,CAAC,MAAO,IAAI,CAACyC,SAAS,GAAG,KAAM,CAAC,CAAC,CAC9Cc,SAAS,CAAC,MAAK;MACd,IAAI,CAACtB,IAAI,CAACiC,GAAG,EAAE;MACf,IAAI,CAACH,cAAc,GAAG,KAAK;IAC7B,CAAC,CAAC;EACN;EAEAU,MAAMA,CAAC9D,EAAU,EAAEgD,IAAY;IAC7B,IAAI,CAACzB,mBAAmB,CACrBwC,IAAI,CAAC,8CAA8C,EAAE,yBAAyB,EAAE;MAC/EC,yBAAyB,EAAE,CAAChB,IAAI;KACjC,CAAC,CACDJ,SAAS,CAAEqB,MAA2B,IAAI;MACzC,IAAIA,MAAM,KAAKhF,YAAY,CAACiF,MAAM,CAACC,OAAO,EAAE;QAC1C,IAAI,CAAC3C,OAAO,CAACsC,MAAM,CAAC9D,EAAE,CAAC,CAAC4C,SAAS,CAAC,MAAM,IAAI,CAACtB,IAAI,CAACiC,GAAG,EAAE,CAAC;MAC1D;IACF,CAAC,CAAC;EACN;EAEAa,cAAcA,CAACpE,EAAU;IACvB,IAAI,CAACgC,YAAY,GAAG;MAClBhC,EAAE;MACFqE,IAAI,EAAE;KACP;IAED,IAAI,CAACtC,aAAa,GAAG,IAAI;EAC3B;EAEAuC,oBAAoBA,CAACnD,WAAmB;IACtC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9BoD,UAAU,CAAC,MAAK;MACd,IAAI,CAACtD,kBAAkB,GAAG,IAAI;IAChC,CAAC,EAAE,CAAC,CAAC;EACP;;;uBAnIWG,cAAc,EAAA7B,EAAA,CAAAiF,iBAAA,CAAAC,EAAA,CAAAzF,WAAA,GAAAO,EAAA,CAAAiF,iBAAA,CAAAE,EAAA,CAAAC,mBAAA,GAAApF,EAAA,CAAAiF,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAAtF,EAAA,CAAAiF,iBAAA,CAAAC,EAAA,CAAAK,kBAAA,GAAAvF,EAAA,CAAAiF,iBAAA,CAAAjF,EAAA,CAAAwF,QAAA;IAAA;EAAA;;;YAAd3D,cAAc;MAAA4D,SAAA;MAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;uCARd,CACTnG,WAAW,EACX;QACEqG,OAAO,EAAEnG,qBAAqB;QAC9BoG,QAAQ;OACT,CACF;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC1BH5F,EAAA,CAAAC,cAAA,kBAAkF;;UAChFD,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAiB,SAAA,qCAG+B;UAE/BjB,EAAA,CAAAC,cAAA,aAAkB;UAChBD,EAAA,CAAAiB,SAAA,8BAIwB;UAG9BjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACG;UAEXH,EAAA,CAAAC,cAAA,mBAA2D;UAAhDD,EAAA,CAAAqB,gBAAA,2BAAAgF,2DAAA9E,MAAA;YAAAvB,EAAA,CAAAY,aAAA,CAAA0F,GAAA;YAAAtG,EAAA,CAAAyB,kBAAA,CAAAoE,GAAA,CAAAhC,cAAA,EAAAtC,MAAA,MAAAsE,GAAA,CAAAhC,cAAA,GAAAtC,MAAA;YAAA,OAAAvB,EAAA,CAAAe,WAAA,CAAAQ,MAAA;UAAA,EAA4B;UAWrCvB,EAVA,CAAAuG,UAAA,IAAAC,qCAAA,gCAAAxG,EAAA,CAAAyG,sBAAA,CAAwB,IAAAC,qCAAA,gCAAA1G,EAAA,CAAAyG,sBAAA,CAIF,KAAAE,sCAAA,gCAAA3G,EAAA,CAAAyG,sBAAA,CAME;UAQ1BzG,EAAA,CAAAG,YAAA,EAAY;UAEZH,EAAA,CAAAuG,UAAA,KAAAK,2DAAA,8CAeC;UAGD5G,EAAA,CAAAC,cAAA,2BAAsE;UAArDD,EAAA,CAAAqB,gBAAA,2BAAAwF,kEAAAtF,MAAA;YAAAvB,EAAA,CAAAY,aAAA,CAAA0F,GAAA;YAAAtG,EAAA,CAAAyB,kBAAA,CAAAoE,GAAA,CAAArD,aAAA,EAAAjB,MAAA,MAAAsE,GAAA,CAAArD,aAAA,GAAAjB,MAAA;YAAA,OAAAvB,EAAA,CAAAe,WAAA,CAAAQ,MAAA;UAAA,EAA2B;UAA0BvB,EAAA,CAAAG,YAAA,EAAkB;;;UAxD7BH,EAAjD,CAAAkB,UAAA,UAAAlB,EAAA,CAAAM,WAAA,8BAAgD,YAAAuF,GAAA,CAAAzD,IAAA,CAAAC,KAAA,CAAuB;UAI3ErC,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAkB,UAAA,SAAA2E,GAAA,CAAA9D,IAAA,CAAa;UAKX/B,EAAA,CAAAI,SAAA,GAAmB;UAEnBJ,EAFA,CAAAkB,UAAA,SAAA2E,GAAA,CAAAzD,IAAA,CAAAC,KAAA,CAAmB,iBAAAwD,GAAA,CAAAzD,IAAA,CAAAE,UAAA,CACa,SAAAuD,GAAA,CAAA9D,IAAA,CACnB;UAMV/B,EAAA,CAAAI,SAAA,EAA4B;UAA5BJ,EAAA,CAAA2B,gBAAA,YAAAkE,GAAA,CAAAhC,cAAA,CAA4B;UAAC7D,EAAA,CAAAkB,UAAA,SAAA2E,GAAA,CAAAtD,SAAA,CAAkB;UAsBvDvC,EAAA,CAAAI,SAAA,GAQ+D;UAR/DJ,EAAA,CAAAkB,UAAA,2BAAAlB,EAAA,CAAA8G,eAAA,KAAAC,GAAA,EAAA/G,EAAA,CAAAgH,eAAA,KAAAC,GAAA,EAAAjH,EAAA,CAAAkH,eAAA,KAAAC,GAAA,GAAAnH,EAAA,CAAAoH,eAAA,KAAAC,GAAA,EAAAxB,GAAA,CAAAjE,WAAA,GAAA5B,EAAA,CAAAkH,eAAA,KAAAI,GAAA,GAAAtH,EAAA,CAAAoH,eAAA,KAAAG,GAAA,EAAA1B,GAAA,CAAAnE,kBAAA,IAAA1B,EAAA,CAAAoH,eAAA,KAAAI,GAAA,EAAA3B,GAAA,CAAAlD,yBAAA,GAQ+D;UASjD3C,EAAA,CAAAI,SAAA,EAA2B;UAA3BJ,EAAA,CAAA2B,gBAAA,YAAAkE,GAAA,CAAArD,aAAA,CAA2B;UAACxC,EAAA,CAAAkB,UAAA,YAAA2E,GAAA,CAAApD,YAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}