{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/ca-dashboard-service\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/common\";\n/** Rendering section \"CARRYING ON ACTIVITY UNDER EACH CATEGORY\" in the third tab \"Additional Statistics\" in Dashboard page. */\nexport let CarryingOnActivityByCategoryComponent = /*#__PURE__*/(() => {\n  class CarryingOnActivityByCategoryComponent extends AppComponentBase {\n    constructor(injector, dashboardService) {\n      super(injector);\n      this.dashboardService = dashboardService;\n      this.datas = [];\n    }\n    ngOnInit() {}\n    ngOnChanges(changes) {\n      if (changes.dashboardData) {\n        this.datas = this.getData(this.dashboardData);\n      }\n    }\n    getData(inputData) {\n      return this.dashboardService.getDataForCarryingOnActivityUnderEachCategory(inputData);\n    }\n    static {\n      this.ɵfac = function CarryingOnActivityByCategoryComponent_Factory(t) {\n        return new (t || CarryingOnActivityByCategoryComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.CADashboardService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CarryingOnActivityByCategoryComponent,\n        selectors: [[\"app-carrying-on-activity-by-category\"]],\n        inputs: {\n          dashboardData: \"dashboardData\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n        decls: 62,\n        vars: 44,\n        consts: [[1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"]],\n        template: function CarryingOnActivityByCategoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n            i0.ɵɵtext(3, \"CARRYING ON ACTIVITY UNDER EACH CATEGORY\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 1)(6, \"div\", 2);\n            i0.ɵɵtext(7, \"Banking Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 2);\n            i0.ɵɵtext(9, \"Distribution and service centre Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 2);\n            i0.ɵɵtext(11, \"Finance and leasing Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 2);\n            i0.ɵɵtext(13, \"Fund Management Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 2);\n            i0.ɵɵtext(15, \"Headquarters Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 2);\n            i0.ɵɵtext(17, \"Holding company or Holding Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 2);\n            i0.ɵɵtext(19, \"Insurance Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 2);\n            i0.ɵɵtext(21, \"Intellectual Property Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 2);\n            i0.ɵɵtext(23, \"Shipping Business\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 2);\n            i0.ɵɵtext(25, \" Total Number of Entities (Carrying on Relevant Activity) \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 2);\n            i0.ɵɵtext(27, \" Total Number of Entities (No Relevant Activity) \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 1)(29, \"div\", 3);\n            i0.ɵɵtext(30);\n            i0.ɵɵpipe(31, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 3);\n            i0.ɵɵtext(33);\n            i0.ɵɵpipe(34, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 3);\n            i0.ɵɵtext(36);\n            i0.ɵɵpipe(37, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"div\", 3);\n            i0.ɵɵtext(39);\n            i0.ɵɵpipe(40, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 3);\n            i0.ɵɵtext(42);\n            i0.ɵɵpipe(43, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"div\", 3);\n            i0.ɵɵtext(45);\n            i0.ɵɵpipe(46, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 3);\n            i0.ɵɵtext(48);\n            i0.ɵɵpipe(49, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"div\", 3);\n            i0.ɵɵtext(51);\n            i0.ɵɵpipe(52, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"div\", 3);\n            i0.ɵɵtext(54);\n            i0.ɵɵpipe(55, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"div\", 3);\n            i0.ɵɵtext(57);\n            i0.ɵɵpipe(58, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"div\", 3);\n            i0.ɵɵtext(60);\n            i0.ɵɵpipe(61, \"number\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(30);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(31, 11, ctx.datas[0].bankBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(34, 14, ctx.datas[0].disrBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(37, 17, ctx.datas[0].finaBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(40, 20, ctx.datas[0].fundBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(43, 23, ctx.datas[0].headBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(46, 26, ctx.datas[0].holdBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(49, 29, ctx.datas[0].insuBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(52, 32, ctx.datas[0].intelBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 35, ctx.datas[0].shipBAValue, \"1.0-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(58, 38, ctx.datas[0].totalNumberOfEntitiesWithRelevantActivity, \"1.0-2\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(61, 41, ctx.datas[0].totalNumberOfEntitiesNoRelevantActivity, \"1.0-2\"), \" \");\n          }\n        },\n        dependencies: [i2.MatCard, i2.MatCardContent, i2.MatCardHeader, i2.MatCardTitle, i3.DecimalPipe],\n        encapsulation: 2\n      });\n    }\n  }\n  return CarryingOnActivityByCategoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}