{"ast": null, "code": "import { EntityAction } from '@abp/ng.components/extensible';\nimport { RolesComponent } from '../../components/roles/roles.component';\nexport const DEFAULT_ROLES_ENTITY_ACTIONS = EntityAction.createMany([{\n  text: 'AbpIdentity::Edit',\n  action: data => {\n    const component = data.getInjected(RolesComponent);\n    component.onEdit(data.record.id);\n  },\n  permission: 'AbpIdentity.Roles.Update',\n  visible: data => data.getInjected(RolesComponent).hasAdminRole\n},\n/*{\n  text: 'AbpIdentity::Claims',\n  action: data => {\n    const component = data.getInjected(RolesComponent);\n    component.onManageClaims(data.record.id);\n  },\n  permission: 'AbpIdentity.Roles.Update',\n},*/\n{\n  text: 'AbpIdentity::Permissions',\n  action: data => {\n    const component = data.getInjected(RolesComponent);\n    component.openPermissionsModal(data.record.name);\n  },\n  permission: 'AbpIdentity.Roles.ManagePermissions'\n},\n/*{\n  text: 'AbpIdentity::ChangeHistory',\n  action: data => {\n    const showHistory = data.getInjected(SHOW_ENTITY_HISTORY);\n    showHistory(data.record.id, 'Volo.Abp.Identity.IdentityRole');\n  },\n  permission: 'AuditLogging.ViewChangeHistory:Volo.Abp.Identity.IdentityRole',\n  visible: data => Boolean(data.getInjected(SHOW_ENTITY_HISTORY, null)),\n},*/\n{\n  text: 'AbpIdentity::Delete',\n  action: data => {\n    const component = data.getInjected(RolesComponent);\n    component.delete(data.record.id, data.record.name);\n  },\n  permission: 'AbpIdentity.Roles.Delete',\n  visible: data => !data.record.isStatic && data.getInjected(RolesComponent).hasAdminRole\n}]);", "map": {"version": 3, "names": ["EntityAction", "RolesComponent", "DEFAULT_ROLES_ENTITY_ACTIONS", "createMany", "text", "action", "data", "component", "getInjected", "onEdit", "record", "id", "permission", "visible", "hasAdminRole", "openPermissionsModal", "name", "delete", "isStatic"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\defaults\\roles\\default-roles-entity-actions.ts"], "sourcesContent": ["import { EntityAction } from '@abp/ng.components/extensible'\r\nimport { IdentityRoleDto } from '@volo/abp.ng.identity/proxy';\r\nimport { RolesComponent } from '../../components/roles/roles.component';\r\n\r\nexport const DEFAULT_ROLES_ENTITY_ACTIONS = EntityAction.createMany<IdentityRoleDto>([\r\n  {\r\n    text: 'AbpIdentity::Edit',\r\n    action: data => {\r\n      const component = data.getInjected(RolesComponent);\r\n      component.onEdit(data.record.id);\r\n    },\r\n    permission: 'AbpIdentity.Roles.Update',\r\n    visible: data => data.getInjected(RolesComponent).hasAdminRole\r\n  },\r\n  /*{\r\n    text: 'AbpIdentity::Claims',\r\n    action: data => {\r\n      const component = data.getInjected(RolesComponent);\r\n      component.onManageClaims(data.record.id);\r\n    },\r\n    permission: 'AbpIdentity.Roles.Update',\r\n  },*/\r\n  {\r\n    text: 'AbpIdentity::Permissions',\r\n    action: data => {\r\n      const component = data.getInjected(RolesComponent);\r\n      component.openPermissionsModal(data.record.name);\r\n    },\r\n    permission: 'AbpIdentity.Roles.ManagePermissions',\r\n  },\r\n  /*{\r\n    text: 'AbpIdentity::ChangeHistory',\r\n    action: data => {\r\n      const showHistory = data.getInjected(SHOW_ENTITY_HISTORY);\r\n      showHistory(data.record.id, 'Volo.Abp.Identity.IdentityRole');\r\n    },\r\n    permission: 'AuditLogging.ViewChangeHistory:Volo.Abp.Identity.IdentityRole',\r\n    visible: data => Boolean(data.getInjected(SHOW_ENTITY_HISTORY, null)),\r\n  },*/\r\n  {\r\n    text: 'AbpIdentity::Delete',\r\n    action: data => {\r\n      const component = data.getInjected(RolesComponent);\r\n      component.delete(data.record.id, data.record.name);\r\n    },\r\n    permission: 'AbpIdentity.Roles.Delete',\r\n    visible: data => !data.record.isStatic && data.getInjected(RolesComponent).hasAdminRole,\r\n  },\r\n]);\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,+BAA+B;AAE5D,SAASC,cAAc,QAAQ,wCAAwC;AAEvE,OAAO,MAAMC,4BAA4B,GAAGF,YAAY,CAACG,UAAU,CAAkB,CACnF;EACEC,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAEC,IAAI,IAAG;IACb,MAAMC,SAAS,GAAGD,IAAI,CAACE,WAAW,CAACP,cAAc,CAAC;IAClDM,SAAS,CAACE,MAAM,CAACH,IAAI,CAACI,MAAM,CAACC,EAAE,CAAC;EAClC,CAAC;EACDC,UAAU,EAAE,0BAA0B;EACtCC,OAAO,EAAEP,IAAI,IAAIA,IAAI,CAACE,WAAW,CAACP,cAAc,CAAC,CAACa;CACnD;AACD;;;;;;;;AAQA;EACEV,IAAI,EAAE,0BAA0B;EAChCC,MAAM,EAAEC,IAAI,IAAG;IACb,MAAMC,SAAS,GAAGD,IAAI,CAACE,WAAW,CAACP,cAAc,CAAC;IAClDM,SAAS,CAACQ,oBAAoB,CAACT,IAAI,CAACI,MAAM,CAACM,IAAI,CAAC;EAClD,CAAC;EACDJ,UAAU,EAAE;CACb;AACD;;;;;;;;;AASA;EACER,IAAI,EAAE,qBAAqB;EAC3BC,MAAM,EAAEC,IAAI,IAAG;IACb,MAAMC,SAAS,GAAGD,IAAI,CAACE,WAAW,CAACP,cAAc,CAAC;IAClDM,SAAS,CAACU,MAAM,CAACX,IAAI,CAACI,MAAM,CAACC,EAAE,EAAEL,IAAI,CAACI,MAAM,CAACM,IAAI,CAAC;EACpD,CAAC;EACDJ,UAAU,EAAE,0BAA0B;EACtCC,OAAO,EAAEP,IAAI,IAAI,CAACA,IAAI,CAACI,MAAM,CAACQ,QAAQ,IAAIZ,IAAI,CAACE,WAAW,CAACP,cAAc,CAAC,CAACa;CAC5E,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}