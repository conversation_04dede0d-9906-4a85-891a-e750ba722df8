{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['М.А', 'М'],\n  abbreviated: ['М.А', 'М'],\n  wide: ['Милоддан Аввалги', 'Милодий']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-чор.', '2-чор.', '3-чор.', '4-чор.'],\n  wide: ['1-чорак', '2-чорак', '3-чорак', '4-чорак']\n};\nvar monthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв', 'фев', 'мар', 'апр', 'май', 'июн', 'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'],\n  wide: ['январ', 'феврал', 'март', 'апрел', 'май', 'июн', 'июл', 'август', 'сентабр', 'октабр', 'ноябр', 'декабр']\n};\nvar dayValues = {\n  narrow: ['Я', 'Д', 'С', 'Ч', 'П', 'Ж', 'Ш'],\n  short: ['як', 'ду', 'се', 'чо', 'па', 'жу', 'ша'],\n  abbreviated: ['якш', 'душ', 'сеш', 'чор', 'пай', 'жум', 'шан'],\n  wide: ['якшанба', 'душанба', 'сешанба', 'чоршанба', 'пайшанба', 'жума', 'шанба']\n};\nvar dayPeriodValues = {\n  any: {\n    am: 'П.О.',\n    pm: 'П.К.',\n    midnight: 'ярим тун',\n    noon: 'пешин',\n    morning: 'эрталаб',\n    afternoon: 'пешиндан кейин',\n    evening: 'кечаси',\n    night: 'тун'\n  }\n};\nvar formattingDayPeriodValues = {\n  any: {\n    am: 'П.О.',\n    pm: 'П.К.',\n    midnight: 'ярим тун',\n    noon: 'пешин',\n    morning: 'эрталаб',\n    afternoon: 'пешиндан кейин',\n    evening: 'кечаси',\n    night: 'тун'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'any'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "any", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/uz-Cyrl/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['М.А', 'М'],\n  abbreviated: ['М.А', 'М'],\n  wide: ['Милоддан Аввалги', 'Милодий']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-чор.', '2-чор.', '3-чор.', '4-чор.'],\n  wide: ['1-чорак', '2-чорак', '3-чорак', '4-чорак']\n};\nvar monthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв', 'фев', 'мар', 'апр', 'май', 'июн', 'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'],\n  wide: ['январ', 'феврал', 'март', 'апрел', 'май', 'июн', 'июл', 'август', 'сентабр', 'октабр', 'ноябр', 'декабр']\n};\nvar dayValues = {\n  narrow: ['Я', 'Д', 'С', 'Ч', 'П', 'Ж', 'Ш'],\n  short: ['як', 'ду', 'се', 'чо', 'па', 'жу', 'ша'],\n  abbreviated: ['якш', 'душ', 'сеш', 'чор', 'пай', 'жум', 'шан'],\n  wide: ['якшанба', 'душанба', 'сешанба', 'чоршанба', 'пайшанба', 'жума', 'шанба']\n};\nvar dayPeriodValues = {\n  any: {\n    am: 'П.О.',\n    pm: 'П.К.',\n    midnight: 'ярим тун',\n    noon: 'пешин',\n    morning: 'эрталаб',\n    afternoon: 'пешиндан кейин',\n    evening: 'кечаси',\n    night: 'тун'\n  }\n};\nvar formattingDayPeriodValues = {\n  any: {\n    am: 'П.О.',\n    pm: 'П.К.',\n    midnight: 'ярим тун',\n    noon: 'пешин',\n    morning: 'эрталаб',\n    afternoon: 'пешиндан кейин',\n    evening: 'кечаси',\n    night: 'тун'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'any'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpBC,WAAW,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACzBC,IAAI,EAAE,CAAC,kBAAkB,EAAE,SAAS;AACtC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrDC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;AACnD,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;AAClH,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO;AACjF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBC,GAAG,EAAE;IACHC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BT,GAAG,EAAE;IACHC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,KAAK;IACnBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}