{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { APP_INITIALIZER, NgModule } from '@angular/core';\nimport { LANGUAGE_MANAGEMENT_FEATURES_PROVIDERS } from '@volo/abp.ng.language-management/common';\nimport { RoutesService } from '@abp/ng.core';\nconst LANGUAGE_MANAGEMENT_ROUTE_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureRoutes,\n  deps: [RoutesService],\n  multi: true\n}];\nfunction configureRoutes(routes) {\n  return () => {\n    routes.add([{\n      name: \"LanguageManagement::LanguageManagement\" /* eLanguageManagementRouteNames.LanguageManagement */,\n      path: '/language-management',\n      layout: \"application\" /* eLayoutType.application */,\n      parentName: \"AbpUiNavigation::Menu:Administration\" /* eThemeSharedRouteNames.Administration */,\n      iconClass: 'fa fa-globe',\n      order: 4,\n      requiredPolicy: \"LanguageManagement.Languages || LanguageManagement.LanguageTexts\" /* eLanguageManagementPolicyNames.LanguageManagement */\n    }, {\n      path: '/language-management/languages',\n      name: \"LanguageManagement::Languages\" /* eLanguageManagementRouteNames.Languages */,\n      parentName: \"LanguageManagement::LanguageManagement\" /* eLanguageManagementRouteNames.LanguageManagement */,\n      order: 1,\n      requiredPolicy: \"LanguageManagement.Languages\" /* eLanguageManagementPolicyNames.Languages */\n    }, {\n      path: '/language-management/texts',\n      name: \"LanguageManagement::LanguageTexts\" /* eLanguageManagementRouteNames.LanguageTexts */,\n      parentName: \"LanguageManagement::LanguageManagement\" /* eLanguageManagementRouteNames.LanguageManagement */,\n      order: 2,\n      requiredPolicy: \"LanguageManagement.LanguageTexts\" /* eLanguageManagementPolicyNames.LanguageTexts */\n    }]);\n  };\n}\nlet LanguageManagementConfigModule = /*#__PURE__*/(() => {\n  class LanguageManagementConfigModule {\n    static forRoot() {\n      return {\n        ngModule: LanguageManagementConfigModule,\n        providers: [LANGUAGE_MANAGEMENT_ROUTE_PROVIDERS, LANGUAGE_MANAGEMENT_FEATURES_PROVIDERS]\n      };\n    }\n    static {\n      this.ɵfac = function LanguageManagementConfigModule_Factory(t) {\n        return new (t || LanguageManagementConfigModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: LanguageManagementConfigModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return LanguageManagementConfigModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LANGUAGE_MANAGEMENT_ROUTE_PROVIDERS, LanguageManagementConfigModule, configureRoutes };\n//# sourceMappingURL=volo-abp.ng.language-management-config.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}