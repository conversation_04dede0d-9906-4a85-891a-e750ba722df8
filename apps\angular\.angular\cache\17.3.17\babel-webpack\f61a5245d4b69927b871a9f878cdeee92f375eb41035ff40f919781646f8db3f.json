{"ast": null, "code": "// Same as fr\nimport formatDistance from \"../fr/_lib/formatDistance/index.js\";\nimport formatRelative from \"../fr/_lib/formatRelative/index.js\";\nimport localize from \"../fr/_lib/localize/index.js\";\nimport match from \"../fr/_lib/match/index.js\";\n// Unique for fr-CA\nimport formatLong from \"./_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale (Canada).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n * <AUTHOR> [@gpetrioli]{@link https://github.com/gpetrioli}\n */\nvar locale = {\n  code: 'fr-CA',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  // Unique for fr-CA\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}