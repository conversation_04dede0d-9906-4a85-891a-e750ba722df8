{"ast": null, "code": "import { InternalStore } from '@abp/ng.core';\nimport * as i0 from \"@angular/core\";\nexport class OrganizationUnitsStateService {\n  constructor() {\n    this.store = new InternalStore({});\n    this.state$ = this.store.sliceState(state => state);\n  }\n  getSelectedNode$() {\n    return this.store.sliceState(state => state.selectedUnit);\n  }\n  getSelectedNode() {\n    return this.store.state.selectedUnit;\n  }\n  setSelectedUnit(selectedUnit) {\n    this.store.patch({\n      selectedUnit\n    });\n  }\n  static {\n    this.ɵfac = function OrganizationUnitsStateService_Factory(t) {\n      return new (t || OrganizationUnitsStateService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OrganizationUnitsStateService,\n      factory: OrganizationUnitsStateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["InternalStore", "OrganizationUnitsStateService", "constructor", "store", "state$", "sliceState", "state", "getSelectedNode$", "<PERSON><PERSON><PERSON><PERSON>", "getSelectedNode", "setSelectedUnit", "patch", "factory", "ɵfac", "providedIn"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\services\\organization-units-state.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { InternalStore } from '@abp/ng.core';\r\nimport { OrganizationUnitWithDetailsDto } from '@volo/abp.ng.identity/proxy';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OrganizationUnitsStateService {\r\n  private store = new InternalStore({} as OrganizationUnitsState);\r\n  state$ = this.store.sliceState(state => state);\r\n\r\n  getSelectedNode$(): Observable<OrganizationUnitWithDetailsDto | undefined> {\r\n    return this.store.sliceState(state => state.selectedUnit);\r\n  }\r\n\r\n  getSelectedNode(): OrganizationUnitWithDetailsDto | undefined {\r\n    return this.store.state.selectedUnit;\r\n  }\r\n  setSelectedUnit(selectedUnit: OrganizationUnitWithDetailsDto | undefined) {\r\n    this.store.patch({ selectedUnit });\r\n  }\r\n}\r\n\r\nexport interface OrganizationUnitsState {\r\n  selectedUnit: OrganizationUnitWithDetailsDto | undefined;\r\n}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,cAAc;;AAO5C,OAAM,MAAOC,6BAA6B;EAH1CC,YAAA;IAIU,KAAAC,KAAK,GAAG,IAAIH,aAAa,CAAC,EAA4B,CAAC;IAC/D,KAAAI,MAAM,GAAG,IAAI,CAACD,KAAK,CAACE,UAAU,CAACC,KAAK,IAAIA,KAAK,CAAC;;EAE9CC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACJ,KAAK,CAACE,UAAU,CAACC,KAAK,IAAIA,KAAK,CAACE,YAAY,CAAC;EAC3D;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACN,KAAK,CAACG,KAAK,CAACE,YAAY;EACtC;EACAE,eAAeA,CAACF,YAAwD;IACtE,IAAI,CAACL,KAAK,CAACQ,KAAK,CAAC;MAAEH;IAAY,CAAE,CAAC;EACpC;;;uBAbWP,6BAA6B;IAAA;EAAA;;;aAA7BA,6BAA6B;MAAAW,OAAA,EAA7BX,6BAA6B,CAAAY,IAAA;MAAAC,UAAA,EAF5B;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}