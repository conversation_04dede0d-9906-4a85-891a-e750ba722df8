{"ast": null, "code": "import { EntityProp } from '@abp/ng.components/extensible';\nimport { of } from 'rxjs';\nexport const DEFAULT_ORGANIZATION_MEMBERS_ENTITY_PROPS = EntityProp.createMany([{\n  type: \"string\" /* ePropType.String */,\n  name: 'userName',\n  displayName: 'AbpIdentity::UserName',\n  sortable: true,\n  columnWidth: 180\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'email',\n  displayName: 'AbpIdentity::EmailAddress',\n  sortable: true,\n  columnWidth: 200,\n  valueResolver: data => {\n    const {\n      email,\n      emailConfirmed\n    } = data.record;\n    return of((email || '') + (emailConfirmed ? `<i class=\"fa fa-check text-success ms-1\"></i>` : ''));\n  }\n}]);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}