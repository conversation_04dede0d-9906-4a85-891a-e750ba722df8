{"ast": null, "code": "import { CoreModule } from '@abp/ng.core';\nimport { ThemeSharedModule } from '@abp/ng.theme.shared';\nimport { NgbDropdownModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\nimport { AuthorityDelegationComponent } from './authority-delegation.component';\nimport { ListAuthorityDelegationComponent } from './list-authority-delegation.component';\nimport { MyDelegatedUsersComponent } from './my-delegated-users.component';\nimport { CreateUserDelegateComponent } from './create-user-delegate.component';\nimport { CommercialUiModule } from '@volo/abp.commercial.ng.ui';\nimport * as i0 from \"@angular/core\";\nconst declarationsWithExports = [AuthorityDelegationComponent, ListAuthorityDelegationComponent, MyDelegatedUsersComponent, CreateUserDelegateComponent];\nexport class AuthorityDelegationModule {\n  static {\n    this.ɵfac = function AuthorityDelegationModule_Factory(t) {\n      return new (t || AuthorityDelegationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthorityDelegationModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CoreModule, ThemeSharedModule, NgbDropdownModule, NgbNavModule, NgxValidateCoreModule, CommercialUiModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthorityDelegationModule, {\n    declarations: [AuthorityDelegationComponent, ListAuthorityDelegationComponent, MyDelegatedUsersComponent, CreateUserDelegateComponent],\n    imports: [CoreModule, ThemeSharedModule, NgbDropdownModule, NgbNavModule, NgxValidateCoreModule, CommercialUiModule],\n    exports: [AuthorityDelegationComponent, ListAuthorityDelegationComponent, MyDelegatedUsersComponent, CreateUserDelegateComponent]\n  });\n})();", "map": {"version": 3, "names": ["CoreModule", "ThemeSharedModule", "NgbDropdownModule", "NgbNavModule", "NgxValidateCoreModule", "AuthorityDelegationComponent", "ListAuthorityDelegationComponent", "MyDelegatedUsersComponent", "CreateUserDelegateComponent", "CommercialUiModule", "declarationsWithExports", "AuthorityDelegationModule", "declarations", "imports", "exports"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity-config\\components\\authority-delegation\\authority-delegation.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CoreModule } from '@abp/ng.core';\r\nimport { ThemeSharedModule } from '@abp/ng.theme.shared';\r\nimport { NgbDropdownModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\r\nimport { AuthorityDelegationComponent } from './authority-delegation.component';\r\nimport { ListAuthorityDelegationComponent } from './list-authority-delegation.component';\r\nimport { MyDelegatedUsersComponent } from './my-delegated-users.component';\r\nimport { CreateUserDelegateComponent } from './create-user-delegate.component';\r\nimport { CommercialUiModule } from '@volo/abp.commercial.ng.ui';\r\n\r\nconst declarationsWithExports = [\r\n  AuthorityDelegationComponent,\r\n  ListAuthorityDelegationComponent,\r\n  MyDelegatedUsersComponent,\r\n  CreateUserDelegateComponent,\r\n];\r\n\r\n@NgModule({\r\n  declarations: [...declarationsWithExports],\r\n  exports: [...declarationsWithExports],\r\n  imports: [\r\n    CoreModule,\r\n    ThemeSharedModule,\r\n    NgbDropdownModule,\r\n    NgbNavModule,\r\n    NgxValidateCoreModule,\r\n    CommercialUiModule,\r\n  ],\r\n})\r\nexport class AuthorityDelegationModule {}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,4BAA4B;AAC5E,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,4BAA4B,QAAQ,kCAAkC;AAC/E,SAASC,gCAAgC,QAAQ,uCAAuC;AACxF,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,kBAAkB,QAAQ,4BAA4B;;AAE/D,MAAMC,uBAAuB,GAAG,CAC9BL,4BAA4B,EAC5BC,gCAAgC,EAChCC,yBAAyB,EACzBC,2BAA2B,CAC5B;AAcD,OAAM,MAAOG,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBARlCX,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBK,kBAAkB;IAAA;EAAA;;;2EAGTE,yBAAyB;IAAAC,YAAA,GAlBpCP,4BAA4B,EAC5BC,gCAAgC,EAChCC,yBAAyB,EACzBC,2BAA2B;IAAAK,OAAA,GAOzBb,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBK,kBAAkB;IAAAK,OAAA,GAfpBT,4BAA4B,EAC5BC,gCAAgC,EAChCC,yBAAyB,EACzBC,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}