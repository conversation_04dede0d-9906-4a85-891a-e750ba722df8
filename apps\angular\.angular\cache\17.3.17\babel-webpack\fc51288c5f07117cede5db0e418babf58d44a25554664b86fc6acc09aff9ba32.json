{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ps\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['ps', [['غ.م.', 'غ.و.'], u, u], u, [['S', 'M', 'T', 'W', 'T', 'F', 'S'], ['يونۍ', 'دونۍ', 'درېنۍ', 'څلرنۍ', 'پينځنۍ', 'جمعه', 'اونۍ'], u, u], u, [['ج', 'ف', 'م', 'ا', 'م', 'ج', 'ج', 'ا', 'س', 'ا', 'ن', 'د'], ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای', 'اګست', 'سېپتمبر', 'اکتوبر', 'نومبر', 'دسمبر'], u], [['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر', 'دسمبر'], ['جنوري', 'فېبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر', 'دسمبر']], [['له میلاد وړاندې', 'م.'], u, ['له میلاد څخه وړاندې', 'له میلاد څخه وروسته']], 6, [4, 5], ['y/M/d', 'y MMM d', 'د y د MMMM d', 'EEEE د y د MMMM d'], ['H:mm', 'H:mm:ss', 'H:mm:ss (z)', 'H:mm:ss (zzzz)'], ['{1} {0}', u, u, u], [',', '.', ';', '%', '\\u200e+', '\\u200e−', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'], 'AFN', '؋', 'افغانۍ', {\n    'AFN': ['؋'],\n    'JPY': ['JP¥', '¥']\n  }, 'rtl', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/ps.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/ps\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'ps',\n        [['غ.م.', 'غ.و.'], u, u],\n        u,\n        [\n            ['S', 'M', 'T', 'W', 'T', 'F', 'S'],\n            ['يونۍ', 'دونۍ', 'درېنۍ', 'څلرنۍ', 'پينځنۍ', 'جمعه', 'اونۍ'], u, u\n        ],\n        u,\n        [\n            ['ج', 'ف', 'م', 'ا', 'م', 'ج', 'ج', 'ا', 'س', 'ا', 'ن', 'د'],\n            [\n                'جنوري', 'فبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای', 'اګست', 'سېپتمبر', 'اکتوبر',\n                'نومبر', 'دسمبر'\n            ],\n            u\n        ],\n        [\n            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n            [\n                'جنوري', 'فبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر',\n                'دسمبر'\n            ],\n            [\n                'جنوري', 'فېبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر',\n                'نومبر', 'دسمبر'\n            ]\n        ],\n        [['له میلاد وړاندې', 'م.'], u, ['له میلاد څخه وړاندې', 'له میلاد څخه وروسته']],\n        6,\n        [4, 5],\n        ['y/M/d', 'y MMM d', 'د y د MMMM d', 'EEEE د y د MMMM d'],\n        ['H:mm', 'H:mm:ss', 'H:mm:ss (z)', 'H:mm:ss (zzzz)'],\n        ['{1} {0}', u, u, u],\n        [',', '.', ';', '%', '\\u200e+', '\\u200e−', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'],\n        'AFN',\n        '؋',\n        'افغانۍ',\n        { 'AFN': ['؋'], 'JPY': ['JP¥', '¥'] },\n        'rtl',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EACxBA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACnC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAEA,CAAC,CACrE,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EACrF,OAAO,EAAE,OAAO,CACnB,EACDA,CAAC,CACJ,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAC/D,CACI,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAC7F,OAAO,CACV,EACD,CACI,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EACrF,OAAO,EAAE,OAAO,CACnB,CACJ,EACD,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,EAC9E,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAC,EACzD,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,gBAAgB,CAAC,EACpD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC1E,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,GAAG,EACH,QAAQ,EACR;IAAE,KAAK,EAAE,CAAC,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EAAE,CAAC,EACrC,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}