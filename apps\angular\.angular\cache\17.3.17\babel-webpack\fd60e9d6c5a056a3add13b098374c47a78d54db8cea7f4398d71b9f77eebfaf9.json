{"ast": null, "code": "import { environment } from \"@environments/environment\";\nconst htmlSpinner = `      \n<div class=\"spinner\"></div>\n<style>\n  \n  .spinner {\n    border: 6px solid rgba(0, 0, 0, 0.1);\n    border-top: 6px solid #333;\n    border-radius: 50%;\n    width: 6.5em;\n    height: 6.5em;\n    animation: spin 1s linear infinite;\n    position:absolute;\n    bottom: 9.5em;\n    left: 45.5em;\n    background: #fff;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n</style>\n`;\nexport const ipBusinessQuestion = {\n  name: \"IntellectualPropertyBusiness\",\n  title: \"******* Intellectual Property Business\",\n  elementsJSON: [{\n    \"type\": \"boolean\",\n    \"name\": \"intelPropPartOfFinancialPeriod\",\n    \"title\": \"4b Carried on for only part of the Financial Period?\",\n    \"isRequired\": true\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropPartOfFinancialPeriodStartDate\",\n    \"title\": \"4c Start Date\",\n    \"enableIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\n    \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\n    \"inputType\": \"date\",\n    \"validators\": [{\n      \"type\": \"expression\",\n      \"expression\": \"relevantActivityDateValid({FinancialPeriodStartDate},{FinancialPeriodEndDate},{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriodStartDate})\",\n      \"text\": \"The start date cannot be earlier than {FinancialPeriodStartDate} and the start date cannot be later than {FinancialPeriodEndDate}\"\n    }],\n    \"width\": \"22em\",\n    \"minWidth\": \"22em\",\n    \"maxWidth\": \"22em\"\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropPartOfFinancialPeriodEndDate\",\n    \"startWithNewLine\": false,\n    \"title\": \"4d End Date\",\n    \"enableIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\n    \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\n    \"inputType\": \"date\",\n    \"validators\": [{\n      \"type\": \"expression\",\n      \"expression\": \"relevantActivityDateValid({FinancialPeriodStartDate},{FinancialPeriodEndDate},{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriodEndDate})\",\n      \"text\": \"The end date cannot be earlier than {FinancialPeriodStartDate} and the end date cannot be later than {FinancialPeriodEndDate}\"\n    }],\n    \"width\": \"22em\",\n    \"minWidth\": \"22em\",\n    \"maxWidth\": \"22em\"\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusGrossIncome\",\n    \"title\": \"6a.1a Total Gross Income for the relevant activity during the financial period.\",\n    \"width\": \"45em\",\n    \"minWidth\": \"45em\",\n    \"maxWidth\": \"45em\",\n    \"columns\": [{\n      name: \"currency\",\n      title: \"Currency\",\n      cellType: \"text\",\n      readOnly: true,\n      defaultValue: \"USD\",\n      \"width\": \"5em\",\n      \"minWidth\": \"5em\",\n      \"maxWidth\": \"5em\"\n    }, {\n      name: \"currencyValue\",\n      title: \"Gross Income\",\n      cellType: \"text\",\n      isRequired: true,\n      inputMask: \"decimal\",\n      options: {\n        allowMinus: false,\n        digits: 2,\n        unmaskAsNumber: true,\n        prefix: \"\",\n        suffix: \"\",\n        digitsOptional: true,\n        stripLeadingZeroes: false\n      },\n      defaultValue: null,\n      \"width\": \"35em\",\n      \"minWidth\": \"35em\",\n      \"maxWidth\": \"35em\"\n    }],\n    \"allowAddRows\": false,\n    \"allowRemoveRows\": false,\n    \"rowCount\": 1,\n    \"maxRowCount\": 1\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropBusNetBookValue\",\n    \"title\": \"6a.1b Net book values of tangible assets, equipment or physical assets held in the course of carrying out the relevant activity.\",\n    \"isRequired\": true,\n    \"inputMask\": \"decimal\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"digits\": 2,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digitsOptional\": true,\n      \"stripLeadingZeroes\": false\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"comment\",\n    \"name\": \"intelPropBusDescOfAssests\",\n    \"title\": \"6a.1c Please provide a description of the nature of any equipment and other tangible or physical assets located within the Bahamas used in connection with the relevant activity.\",\n    \"isRequired\": true,\n    \"maxLength\": 255,\n    \"autoGrow\": true,\n    \"allowResize\": false,\n    \"rows\": 1\n  }, {\n    \"type\": \"boolean\",\n    \"name\": \"intelPropBusActivityDirectedManagedBahamas\",\n    \"title\": \"6a.2a Is the activity directed and managed in the Bahamas?\",\n    \"isRequired\": true\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusDetailsPersonResponsibleRelevantActivity\",\n    \"title\": \"6a.2b Please provide details of the persons responsible for direction and management of the relevant activity.\",\n    \"isRequired\": true,\n    \"columns\": [{\n      \"name\": \"name\",\n      \"title\": \"Name\",\n      \"isRequired\": true,\n      \"cellType\": \"comment\",\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"maxLength\": 150,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 150\n      }]\n    }, {\n      \"title\": \"Resident in the Bahamas?\",\n      \"isRequired\": true,\n      \"name\": \"residentInIheBahamas\",\n      \"cellType\": \"boolean\"\n    }, {\n      \"title\": \"Relation to the entity\",\n      \"isRequired\": true,\n      \"name\": \"relationToTheEntity\",\n      \"cellType\": \"comment\",\n      \"maxLength\": 100,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 100\n      }]\n    }],\n    \"rowCount\": 0\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropBusNumberOfBoardMeetings\",\n    \"title\": \"6a.2c Number of board meetings the entity held during the financial period with relation to this activity.\",\n    \"isRequired\": true,\n    \"inputMask\": \"integer\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"stripLeadingZeroes\": false,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digits\": 0,\n      \"digitsOptional\": true,\n      \"max\": 999\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropNumberOfBoardMeetingsInBahamas\",\n    \"title\": \"6a.2d Number of board meetings the entity held during the financial period with relation to this activity **in the Bahamas**.\",\n    \"isRequired\": true,\n    \"inputMask\": \"integer\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"stripLeadingZeroes\": false,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digits\": 0,\n      \"digitsOptional\": true,\n      \"max\": 999\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropBusQuorumForBoardMeetings\",\n    \"title\": \"6a.2e Quorum for board meetings\",\n    \"isRequired\": true,\n    \"inputMask\": \"integer\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"stripLeadingZeroes\": false,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digits\": 0,\n      \"digitsOptional\": true,\n      \"max\": 999\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"boolean\",\n    \"name\": \"quorumOfDirectorsPhysicalPresentYN\",\n    \"title\": \"6a.2f Of these board meetings held in the Bahamas, was the quorum of directors physically present in the Bahamas?\",\n    \"isRequired\": true\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusListOfDirectorsForManagmentOfMeetings\",\n    \"title\": \"6a.2g Please provide a list of Quorum of directors responsible for direction and management that attended each of the meetings.\",\n    \"isRequired\": true,\n    \"columns\": [{\n      name: \"meetingNumber\",\n      title: \"Meeting Number\",\n      \"cellType\": \"text\",\n      \"inputMask\": \"integer\",\n      \"isRequired\": true,\n      \"options\": {\n        \"allowMinus\": false,\n        \"stripLeadingZeroes\": false,\n        \"unmaskAsNumber\": true,\n        \"prefix\": \"\",\n        \"suffix\": \"\",\n        \"digits\": 0,\n        \"digitsOptional\": true,\n        \"max\": 999\n      }\n    }, {\n      name: \"name\",\n      title: \"Name\",\n      \"cellType\": \"comment\",\n      \"isRequired\": true,\n      \"maxLength\": 150,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 150\n      }]\n    }, {\n      name: \"physicallyPresent\",\n      title: \"Physically Present?\",\n      \"cellType\": \"boolean\",\n      \"isRequired\": true\n    }, {\n      name: \"yearsRelevantExperience\",\n      title: \"Years of Relevant Experience\",\n      \"cellType\": \"text\",\n      \"inputMask\": \"decimal\",\n      \"options\": {\n        \"allowMinus\": false,\n        \"digits\": 2,\n        \"unmaskAsNumber\": true,\n        \"prefix\": \"\",\n        \"suffix\": \"\",\n        \"digitsOptional\": true,\n        \"stripLeadingZeroes\": false\n      },\n      \"width\": \"15em\",\n      \"minWidth\": \"15em\",\n      \"maxWidth\": \"15em\",\n      \"isRequired\": true,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 100\n      }]\n    }, {\n      name: \"qualification\",\n      title: \"Qualification\",\n      \"cellType\": \"comment\",\n      \"isRequired\": true,\n      \"maxLength\": 100,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 100\n      }]\n    }],\n    \"rowCount\": 0\n  }, {\n    \"type\": \"boolean\",\n    \"name\": \"intelPropBusMinutesOfBoardMeetingsInBahamas\",\n    \"title\": \"6a.2h Are the minutes of all board meetings kept in the Bahamas?\",\n    \"isRequired\": true\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusTotalExpenditureInOperations\",\n    \"title\": \"6a.3a Total expenditure incurred in the operations of the relevant activity during the financial period (including outsourcing, if applicable).\",\n    \"width\": \"45em\",\n    \"minWidth\": \"45em\",\n    \"maxWidth\": \"45em\",\n    \"columns\": [{\n      name: \"currency\",\n      title: \"Currency\",\n      cellType: \"text\",\n      readOnly: true,\n      defaultValue: \"USD\",\n      \"width\": \"5em\",\n      \"minWidth\": \"5em\",\n      \"maxWidth\": \"5em\"\n    }, {\n      name: \"currencyValue\",\n      title: \"Total Expenditure\",\n      cellType: \"text\",\n      isRequired: true,\n      inputMask: \"decimal\",\n      options: {\n        allowMinus: false,\n        digits: 2,\n        unmaskAsNumber: true,\n        prefix: \"\",\n        suffix: \"\",\n        digitsOptional: true,\n        stripLeadingZeroes: false\n      },\n      defaultValue: null,\n      \"width\": \"35em\",\n      \"minWidth\": \"35em\",\n      \"maxWidth\": \"35em\"\n    }],\n    \"allowAddRows\": false,\n    \"allowRemoveRows\": false,\n    \"rowCount\": 1,\n    \"maxRowCount\": 1\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusTotalExpenditureInBahamas\",\n    \"title\": \"6a.3b Total expenditure incurred **in the Bahamas** in the operations of the relevant activity during the financial period (including outsourcing, if applicable).\",\n    \"width\": \"45em\",\n    \"minWidth\": \"45em\",\n    \"maxWidth\": \"45em\",\n    \"columns\": [{\n      name: \"currency\",\n      title: \"Currency\",\n      cellType: \"text\",\n      readOnly: true,\n      defaultValue: \"USD\",\n      \"width\": \"5em\",\n      \"minWidth\": \"5em\",\n      \"maxWidth\": \"5em\"\n    }, {\n      name: \"currencyValue\",\n      title: \"Total Expenditure\",\n      cellType: \"text\",\n      isRequired: true,\n      inputMask: \"decimal\",\n      options: {\n        allowMinus: false,\n        digits: 2,\n        unmaskAsNumber: true,\n        prefix: \"\",\n        suffix: \"\",\n        digitsOptional: true,\n        stripLeadingZeroes: false\n      },\n      defaultValue: null,\n      \"width\": \"35em\",\n      \"minWidth\": \"35em\",\n      \"maxWidth\": \"35em\"\n    }],\n    \"allowAddRows\": false,\n    \"allowRemoveRows\": false,\n    \"rowCount\": 1,\n    \"maxRowCount\": 1\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropBusTotalEmployAndCorporateLegalEntity\",\n    \"title\": \"6a.4a Total number of full-time equivalent employees of the corporate and legal entity.\",\n    \"isRequired\": true,\n    \"validators\": [{\n      \"type\": \"numeric\",\n      \"minValue\": -1\n    }],\n    \"inputMask\": \"decimal\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"digits\": 3,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digitsOptional\": true,\n      \"stripLeadingZeroes\": false\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropBusTotalEmployeesEngaged\",\n    \"title\": \"6a.4b Total number of full-time equivalent employees engaged in the relevant activity.\",\n    \"isRequired\": true,\n    \"validators\": [{\n      \"type\": \"numeric\",\n      \"minValue\": -1\n    }],\n    \"inputMask\": \"decimal\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"digits\": 3,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digitsOptional\": true,\n      \"stripLeadingZeroes\": false\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"text\",\n    \"name\": \"intelPropBusTotalEngagedEmployeesInBahamas\",\n    \"title\": \"6a.4c Total number of full-time equivalent employees engaged in the relevant activity physically present **in the Bahamas**.\",\n    \"isRequired\": true,\n    \"validators\": [{\n      \"type\": \"numeric\",\n      \"minValue\": -1\n    }],\n    \"inputMask\": \"decimal\",\n    \"options\": {\n      \"allowMinus\": false,\n      \"digits\": 3,\n      \"unmaskAsNumber\": true,\n      \"prefix\": \"\",\n      \"suffix\": \"\",\n      \"digitsOptional\": true,\n      \"stripLeadingZeroes\": false\n    },\n    \"size\": 50,\n    \"width\": null,\n    \"minWidth\": null,\n    \"maxWidth\": null\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusEmployeeQualificaitions\",\n    \"title\": \"6a.4d Provide details on qualifications of the employees in 6a.4c\",\n    \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusTotalEngagedEmployeesInBahamas} > 0  = true\",\n    \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusTotalEngagedEmployeesInBahamas} > 0\",\n    \"columns\": [{\n      \"title\": \"Name\",\n      \"name\": \"name\",\n      \"cellType\": \"comment\",\n      \"isRequired\": true,\n      \"maxLength\": 150,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 150\n      }]\n    }, {\n      \"title\": \"Qualification\",\n      \"name\": \"qualification\",\n      \"cellType\": \"comment\",\n      \"isRequired\": true,\n      \"maxLength\": 150,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 150\n      }]\n    }, {\n      \"title\": \"Years of relevant experience\",\n      \"name\": \"yearsofRelevantExperience\",\n      \"cellType\": \"text\",\n      \"isRequired\": true,\n      \"inputMask\": \"decimal\",\n      \"options\": {\n        \"allowMinus\": false,\n        \"digits\": 2,\n        \"unmaskAsNumber\": true,\n        \"prefix\": \"\",\n        \"suffix\": \"\",\n        \"digitsOptional\": true,\n        \"stripLeadingZeroes\": false\n      },\n      \"width\": \"15em\",\n      \"minWidth\": \"15em\",\n      \"maxWidth\": \"15em\"\n    }, {\n      \"name\": \"contractType\",\n      \"title\": \"Contract Type\",\n      \"cellType\": \"comment\",\n      \"maxLength\": 150,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 150\n      }]\n    }],\n    \"rowCount\": 0\n  }, {\n    \"type\": \"matrixdynamic\",\n    \"name\": \"intelPropBusAddressesOfPremises\",\n    \"title\": \"6a.5a Provide addresses of all premises within the Bahamas used in connection with the relevant activity.\",\n    \"isRequired\": true,\n    \"columns\": [{\n      \"title\": \"Address Line 1\",\n      \"name\": \"addressLine1\",\n      \"cellType\": \"comment\",\n      \"isRequired\": true,\n      \"maxLength\": 255,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 255\n      }]\n    }, {\n      \"title\": \"Address Line 2\",\n      \"name\": \"addressLine2\",\n      \"cellType\": \"comment\",\n      \"maxLength\": 255,\n      \"autoGrow\": true,\n      \"allowResize\": false,\n      \"rows\": 1,\n      \"validators\": [{\n        \"type\": \"text\",\n        \"maxLength\": 255\n      }]\n    }, {\n      \"title\": \"Country\",\n      \"name\": \"country\",\n      \"cellType\": \"text\",\n      \"readOnly\": true,\n      \"defaultValue\": \"Bahamas\"\n    }],\n    \"rowCount\": 0\n  }, {\n    \"type\": \"tagbox\",\n    \"name\": \"intelPropBusCIGAInBahamasForRelevantActivity\",\n    \"title\": \"6a.6a Select Core Income Generating Activities conducted carried out in the Bahamas for the relevant activity.\",\n    \"isRequired\": true,\n    \"choicesByUrl\": {\n      \"url\": environment['apis']['default']['url'] + \"/api/lookup-service/ciga\",\n      \"path\": \"items\",\n      \"valueName\": \"id\",\n      \"titleName\": \"name\"\n    },\n    \"showNoneItem\": true\n  }, {\n    type: \"text\",\n    name: \"intelPropBusCIGAInBahamasForRelevantActivityComment\",\n    visibleIf: \"\",\n    title: \"CIGA Other (Please Specify)\",\n    maxLength: 255,\n    isRequired: true\n  }, {\n    \"type\": \"boolean\",\n    \"name\": \"intelPropBusIsEntityHighRisk\",\n    \"title\": \"6a.7 Is the entity a high-risk intellectual property entity?\",\n    \"isRequired\": true\n  }, {\n    \"type\": \"panel\",\n    \"name\": \"9A7Questions\",\n    \"elements\": [{\n      \"type\": \"text\",\n      \"name\": \"intelPropBusRelevantIPHeld\",\n      \"title\": \"6a.7a Identify the relevant IP asset which it holds.\",\n      \"isRequired\": true,\n      \"maxLength\": 255\n    }, {\n      \"type\": \"text\",\n      \"name\": \"intelPropBusHowIPAssetGenerateIncome\",\n      \"title\": \"6a.7b Explain how the intellectual property asset is being used to generate income.\",\n      \"isRequired\": true,\n      \"maxLength\": 255\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"intelPropBusEmployeeResponsibleGenerationIncome\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"intelPropBusEmployeeResponsibleGenerationIncomeResponse\",\n        \"title\": \"Response\",\n        \"isRequired\": true,\n        \"maxLength\": 1500\n      }, {\n        \"type\": \"file\",\n        \"name\": \"intelPropBusEmployeeResponsibleGenerationIncomeAttachment\",\n        \"showPreview\": false,\n        \"title\": \"Supporting Documentation (Image or PDF files only)\",\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.intelPropBusEmployeeResponsibleGenerationIncomeAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForintelPropBusEmployeeResponsibleGenerationIncomeAttachment\",\n        visibleIf: \"{intelPropBusEmployeeResponsibleGenerationIncomeAttachmentSpinner}\"\n      }],\n      \"title\": \"6a.7c Identify the decisions for which each employee is responsible in respect of the generation of income from the intellectual property asset.\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"intelPropBusStrategicDecisions\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"intelPropBusStrategicDecisionsResponse\",\n        \"title\": \"Response\",\n        \"isRequired\": true,\n        \"maxLength\": 1500\n      }, {\n        \"type\": \"file\",\n        \"name\": \"intelPropBusStrategicDecisionsAttachment\",\n        \"showPreview\": false,\n        \"title\": \"Supporting Documentation (Image or PDF files only)\",\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.intelPropBusStrategicDecisionsAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForintelPropBusStrategicDecisionsAttachment\",\n        visibleIf: \"{intelPropBusStrategicDecisionsAttachmentSpinner}\"\n      }],\n      \"title\": \"6a.7d The nature and history of strategic decisions (if any) taken by the entity in the Bahamas.\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"intelPropBusNatureOfTradingActivity\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"intelPropBusNatureOfTradingActivityResponse\",\n        \"title\": \"Response\",\n        \"isRequired\": true,\n        \"maxLength\": 1500\n      }, {\n        \"type\": \"file\",\n        \"name\": \"intelPropBusNatureOfTradingActivityAttachment\",\n        \"title\": \"Supporting Documentation (Image or PDF files only)\",\n        \"showPreview\": false,\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.intelPropBusNatureOfTradingActivityAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForintelPropBusNatureOfTradingActivityAttachment\",\n        visibleIf: \"{intelPropBusNatureOfTradingActivityAttachmentSpinner}\"\n      }],\n      \"title\": \"6a.7e The nature and history of the trading activities (if any carried out in the Bahamas by which) the intellectual property assets is exploited for the purpose of generating income from third parties.\"\n    }]\n  }, {\n    \"type\": \"panel\",\n    \"name\": \"highRiskEntityQuestions\",\n    //\"title\": \"6a.8 High Risk Entity Questions\",\n    \"elements\": [{\n      \"type\": \"matrixdynamic\",\n      \"name\": \"highRiskGrossIncomeRoyalties\",\n      \"title\": \"6a.8a Gross income through Royalties, if applicable.\",\n      \"width\": \"45em\",\n      \"minWidth\": \"45em\",\n      \"maxWidth\": \"45em\",\n      \"columns\": [{\n        name: \"currency\",\n        title: \"Currency\",\n        cellType: \"text\",\n        readOnly: true,\n        defaultValue: \"USD\",\n        \"width\": \"5em\",\n        \"minWidth\": \"5em\",\n        \"maxWidth\": \"5em\"\n      }, {\n        name: \"currencyValue\",\n        title: \"Gross Income\",\n        cellType: \"text\",\n        inputMask: \"decimal\",\n        options: {\n          allowMinus: false,\n          digits: 2,\n          unmaskAsNumber: true,\n          prefix: \"\",\n          suffix: \"\",\n          digitsOptional: true,\n          stripLeadingZeroes: false\n        },\n        defaultValue: null,\n        \"width\": \"35em\",\n        \"minWidth\": \"35em\",\n        \"maxWidth\": \"35em\"\n      }],\n      \"allowAddRows\": false,\n      \"allowRemoveRows\": false,\n      \"rowCount\": 1,\n      \"maxRowCount\": 1\n    }, {\n      \"type\": \"matrixdynamic\",\n      \"name\": \"highRiskGrossIncomeThroughGains\",\n      \"title\": \"6a.8b Gross income through Gains from sale of IP asset, if applicable.\",\n      \"width\": \"45em\",\n      \"minWidth\": \"45em\",\n      \"maxWidth\": \"45em\",\n      \"columns\": [{\n        name: \"currency\",\n        title: \"Currency\",\n        cellType: \"text\",\n        readOnly: true,\n        defaultValue: \"USD\",\n        \"width\": \"5em\",\n        \"minWidth\": \"5em\",\n        \"maxWidth\": \"5em\"\n      }, {\n        name: \"currencyValue\",\n        title: \"Gross Income\",\n        cellType: \"text\",\n        inputMask: \"decimal\",\n        options: {\n          allowMinus: false,\n          digits: 2,\n          unmaskAsNumber: true,\n          prefix: \"\",\n          suffix: \"\",\n          digitsOptional: true,\n          stripLeadingZeroes: false\n        },\n        defaultValue: null,\n        \"width\": \"35em\",\n        \"minWidth\": \"35em\",\n        \"maxWidth\": \"35em\"\n      }],\n      \"allowAddRows\": false,\n      \"allowRemoveRows\": false,\n      \"rowCount\": 1,\n      \"maxRowCount\": 1\n    }, {\n      \"type\": \"matrixdynamic\",\n      \"name\": \"highRiskGrossIncomeThroughOthers\",\n      \"title\": \"6a.8c Gross income through others\",\n      \"width\": \"45em\",\n      \"minWidth\": \"45em\",\n      \"maxWidth\": \"45em\",\n      \"columns\": [{\n        name: \"currency\",\n        title: \"Currency\",\n        cellType: \"text\",\n        readOnly: true,\n        defaultValue: \"USD\",\n        \"width\": \"5em\",\n        \"minWidth\": \"5em\",\n        \"maxWidth\": \"5em\"\n      }, {\n        name: \"currencyValue\",\n        title: \"Gross Income\",\n        cellType: \"text\",\n        inputMask: \"decimal\",\n        options: {\n          allowMinus: false,\n          digits: 2,\n          unmaskAsNumber: true,\n          prefix: \"\",\n          suffix: \"\",\n          digitsOptional: true,\n          stripLeadingZeroes: false\n        },\n        defaultValue: null,\n        \"width\": \"35em\",\n        \"minWidth\": \"35em\",\n        \"maxWidth\": \"35em\"\n      }],\n      \"allowAddRows\": false,\n      \"allowRemoveRows\": false,\n      \"rowCount\": 1,\n      \"maxRowCount\": 1\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"highRiskBusinessPlanDetailsQuestion\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"highRiskBusinessPlanDetailsResponse\",\n        \"title\": \"Response\",\n        \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n        \"maxLength\": 1500\n      }, {\n        \"type\": \"file\",\n        \"name\": \"highRiskBusinessPlanDetailsAttachment\",\n        \"title\": \"Supporting Documentation (Image or PDF files only)\",\n        \"showPreview\": false,\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.highRiskBusinessPlanDetailsAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForhighRiskBusinessPlanDetailsAttachment\",\n        visibleIf: \"{highRiskBusinessPlanDetailsAttachmentSpinner}\"\n      }],\n      \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n      \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n      \"title\": \"6a.8d Provide detailed business plans which explain the commercial rationale of holding the intellectual property assets in the Bahamas.\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"highRiskEvidenceDecisionMakingInBahamasQuestion\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"highRiskEvidenceDecisionMakingInBahamasResponse\",\n        \"title\": \"Response\",\n        \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n        \"maxLength\": 1500\n      }, {\n        \"type\": \"file\",\n        \"name\": \"highRiskEvidenceDecisionMakingInBahamasAttachment\",\n        \"title\": \"Supporting Documentation (Image or PDF files only)\",\n        \"showPreview\": false,\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.highRiskEvidenceDecisionMakingInBahamasAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForhighRiskEvidenceDecisionMakingInBahamasAttachment\",\n        visibleIf: \"{highRiskEvidenceDecisionMakingInBahamasAttachmentSpinner}\"\n      }],\n      \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n      \"title\": \"6a.8e Provide concrete evidence that decision-making is taking place within the Bahamas, including but not limited to, minutes of meetings which have taken place in the Bahamas.\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"highRiskOtherFacts\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"highRiskOtherFactsResponse\",\n        \"title\": \"Response\",\n        \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n        \"maxLength\": 1500\n      }, {\n        \"type\": \"file\",\n        \"name\": \"highRiskOtherFactsAttachment\",\n        \"title\": \"Supporting Documentation (Image or PDF files only)\",\n        \"showPreview\": false,\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.highRiskOtherFactsAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForhighRiskOtherFactsAttachment\",\n        visibleIf: \"{highRiskOtherFactsAttachmentSpinner}\"\n      }],\n      \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\n      \"title\": \"6a.8f Any other facts and matters necessary to demonstrate compliance?\"\n    }],\n    \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\"\n  }, {\n    \"type\": \"outsourcing\",\n    \"name\": \"outsourcingIntellectualPropertyBusiness\",\n    \"visibleIf\": \"{relevantActRelevantActivities} anyof ['other', 'Intellectual property business']\",\n    \"title\": \"Outsourcing Intellectual Property Business\",\n    \"hideNumber\": true,\n    \"titleLocation\": \"hidden\"\n  }]\n};", "map": {"version": 3, "names": ["environment", "htmlSpinner", "ipBusinessQuestion", "name", "title", "elementsJSON", "cellType", "readOnly", "defaultValue", "isRequired", "inputMask", "options", "allowMinus", "digits", "unmaskAsNumber", "prefix", "suffix", "digitsOptional", "stripLeadingZeroes", "type", "visibleIf", "max<PERSON><PERSON><PERSON>", "html"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\composite-questions\\ip-business.ts"], "sourcesContent": ["import { environment } from \"@environments/environment\"\r\n\r\nconst htmlSpinner = `      \r\n<div class=\"spinner\"></div>\r\n<style>\r\n  \r\n  .spinner {\r\n    border: 6px solid rgba(0, 0, 0, 0.1);\r\n    border-top: 6px solid #333;\r\n    border-radius: 50%;\r\n    width: 6.5em;\r\n    height: 6.5em;\r\n    animation: spin 1s linear infinite;\r\n    position:absolute;\r\n    bottom: 9.5em;\r\n    left: 45.5em;\r\n    background: #fff;\r\n  }\r\n  \r\n  @keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n  }\r\n</style>\r\n`\r\nexport const ipBusinessQuestion = {\r\n    name: \"IntellectualPropertyBusiness\",\r\n  title: \"******* Intellectual Property Business\",\r\n  elementsJSON: [\r\n    {\r\n      \"type\": \"boolean\",\r\n      \"name\": \"intelPropPartOfFinancialPeriod\",\r\n      \"title\": \"4b Carried on for only part of the Financial Period?\",\r\n      \"isRequired\": true\r\n    },\r\n    {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropPartOfFinancialPeriodStartDate\",\r\n      \"title\": \"4c Start Date\",\r\n      \"enableIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\r\n      \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\r\n      \"inputType\": \"date\",\r\n      \"validators\": [\r\n        {\r\n            \"type\": \"expression\",\r\n            \"expression\": \"relevantActivityDateValid({FinancialPeriodStartDate},{FinancialPeriodEndDate},{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriodStartDate})\",\r\n            \"text\": \"The start date cannot be earlier than {FinancialPeriodStartDate} and the start date cannot be later than {FinancialPeriodEndDate}\"\r\n        },          \r\n      ],\r\n      \"width\": \"22em\",\r\n      \"minWidth\": \"22em\",\r\n      \"maxWidth\": \"22em\"\r\n    },\r\n    {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropPartOfFinancialPeriodEndDate\",\r\n      \"startWithNewLine\": false,\r\n      \"title\": \"4d End Date\",\r\n      \"enableIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\r\n      \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriod} = true\",\r\n      \"inputType\": \"date\",\r\n      \"validators\": [\r\n        {\r\n            \"type\": \"expression\",\r\n            \"expression\": \"relevantActivityDateValid({FinancialPeriodStartDate},{FinancialPeriodEndDate},{IntellectualPropertyBusiness.intelPropPartOfFinancialPeriodEndDate})\",\r\n            \"text\": \"The end date cannot be earlier than {FinancialPeriodStartDate} and the end date cannot be later than {FinancialPeriodEndDate}\"\r\n        },          \r\n      ],\r\n      \"width\": \"22em\",\r\n      \"minWidth\": \"22em\",\r\n      \"maxWidth\": \"22em\"\r\n    },\r\n    {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusGrossIncome\",\r\n      \"title\": \"6a.1a Total Gross Income for the relevant activity during the financial period.\",\r\n      \"width\": \"45em\",\r\n      \"minWidth\": \"45em\",\r\n      \"maxWidth\": \"45em\",\r\n      \"columns\": [\r\n        {\r\n          name: \"currency\",\r\n          title: \"Currency\",\r\n          cellType: \"text\",\r\n          readOnly: true,\r\n          defaultValue: \"USD\",\r\n          \"width\": \"5em\",\r\n          \"minWidth\": \"5em\",\r\n          \"maxWidth\": \"5em\"\r\n        },\r\n        {\r\n          name: \"currencyValue\",\r\n          title: \"Gross Income\", \r\n          cellType: \"text\",\r\n          isRequired: true,\r\n          inputMask: \"decimal\",\r\n          options: {\r\n            allowMinus: false,\r\n            digits: 2,\r\n            unmaskAsNumber: true,\r\n            prefix: \"\",\r\n            suffix: \"\",\r\n            digitsOptional: true,\r\n            stripLeadingZeroes: false,\r\n          },\r\n          defaultValue: null,\r\n          \"width\": \"35em\",\r\n          \"minWidth\": \"35em\",\r\n          \"maxWidth\": \"35em\"\r\n        }\r\n      ],\r\n      \"allowAddRows\": false,\r\n      \"allowRemoveRows\": false,\r\n      \"rowCount\": 1,\r\n      \"maxRowCount\": 1\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropBusNetBookValue\",\r\n      \"title\": \"6a.1b Net book values of tangible assets, equipment or physical assets held in the course of carrying out the relevant activity.\",\r\n      \"isRequired\": true,\r\n      \"inputMask\": \"decimal\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"digits\": 2,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digitsOptional\": true,\r\n        \"stripLeadingZeroes\": false,\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n    },\r\n     {\r\n      \"type\": \"comment\",\r\n      \"name\": \"intelPropBusDescOfAssests\",\r\n      \"title\": \"6a.1c Please provide a description of the nature of any equipment and other tangible or physical assets located within the Bahamas used in connection with the relevant activity.\",\r\n      \"isRequired\": true,\r\n      \"maxLength\": 255,\r\n      \"autoGrow\": true,\r\n      \"allowResize\": false,\r\n      \"rows\": 1,\r\n     },\r\n     {\r\n      \"type\": \"boolean\",\r\n      \"name\": \"intelPropBusActivityDirectedManagedBahamas\",\r\n      \"title\": \"6a.2a Is the activity directed and managed in the Bahamas?\",\r\n      \"isRequired\": true\r\n     },\r\n     {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusDetailsPersonResponsibleRelevantActivity\",\r\n      \"title\": \"6a.2b Please provide details of the persons responsible for direction and management of the relevant activity.\",\r\n      \"isRequired\": true,\r\n      \"columns\": [\r\n       {\r\n        \"name\": \"name\",\r\n         \"title\":\"Name\",\r\n         \"isRequired\": true,\r\n        \"cellType\": \"comment\",\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"maxLength\": 150,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 150\r\n         }\r\n        ]\r\n       },\r\n       {\r\n        \"title\": \"Resident in the Bahamas?\",\r\n        \"isRequired\": true,\r\n        \"name\": \"residentInIheBahamas\",\r\n        \"cellType\": \"boolean\"\r\n       },\r\n       {\r\n        \"title\": \"Relation to the entity\",\r\n        \"isRequired\": true,\r\n        \"name\": \"relationToTheEntity\",\r\n        \"cellType\": \"comment\",\r\n        \"maxLength\": 100,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 100\r\n         }\r\n        ]\r\n       }\r\n      ],\r\n      \"rowCount\": 0\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropBusNumberOfBoardMeetings\",\r\n      \"title\": \"6a.2c Number of board meetings the entity held during the financial period with relation to this activity.\",\r\n      \"isRequired\": true,\r\n      \"inputMask\": \"integer\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"stripLeadingZeroes\": false,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digits\": 0,\r\n        \"digitsOptional\": true,\r\n        \"max\":999\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropNumberOfBoardMeetingsInBahamas\",\r\n      \"title\": \"6a.2d Number of board meetings the entity held during the financial period with relation to this activity **in the Bahamas**.\",\r\n      \"isRequired\": true,\r\n      \"inputMask\": \"integer\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"stripLeadingZeroes\": false,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digits\": 0,\r\n        \"digitsOptional\": true,\r\n        \"max\":999\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropBusQuorumForBoardMeetings\",\r\n      \"title\": \"6a.2e Quorum for board meetings\",\r\n      \"isRequired\": true,\r\n      \"inputMask\": \"integer\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"stripLeadingZeroes\": false,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digits\": 0,\r\n        \"digitsOptional\": true,\r\n        \"max\":999\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n    },\r\n    {\r\n      \"type\": \"boolean\",\r\n      \"name\": \"quorumOfDirectorsPhysicalPresentYN\",\r\n      \"title\": \"6a.2f Of these board meetings held in the Bahamas, was the quorum of directors physically present in the Bahamas?\",\r\n      \"isRequired\": true,\r\n    },\r\n    {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusListOfDirectorsForManagmentOfMeetings\",\r\n      \"title\": \"6a.2g Please provide a list of Quorum of directors responsible for direction and management that attended each of the meetings.\",\r\n      \"isRequired\": true,\r\n      \"columns\": [\r\n        {\r\n        name: \"meetingNumber\",\r\n        title: \"Meeting Number\",\r\n        \"cellType\": \"text\",\r\n        \"inputMask\": \"integer\",\r\n        \"isRequired\": true,\r\n        \"options\": {\r\n          \"allowMinus\": false,\r\n          \"stripLeadingZeroes\": false,\r\n          \"unmaskAsNumber\": true,\r\n          \"prefix\": \"\",\r\n          \"suffix\": \"\",\r\n          \"digits\": 0,\r\n          \"digitsOptional\": true,\r\n          \"max\":999\r\n        }\r\n      },\r\n      {\r\n        name: \"name\",\r\n        title: \"Name\",\r\n        \"cellType\": \"comment\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 150,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n        {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 150\r\n        }\r\n        ]\r\n      },\r\n      {\r\n        name: \"physicallyPresent\",\r\n        title: \"Physically Present?\",\r\n        \"cellType\": \"boolean\",\r\n        \"isRequired\": true,\r\n      },\r\n      {\r\n        name: \"yearsRelevantExperience\",\r\n        title: \"Years of Relevant Experience\",\r\n        \"cellType\": \"text\",\r\n        \"inputMask\": \"decimal\",\r\n        \"options\": {\r\n          \"allowMinus\": false,\r\n          \"digits\": 2,\r\n          \"unmaskAsNumber\": true,\r\n          \"prefix\": \"\",\r\n          \"suffix\": \"\",\r\n          \"digitsOptional\": true,\r\n        \"stripLeadingZeroes\": false,\r\n        },\r\n        \"width\": \"15em\",\r\n        \"minWidth\": \"15em\",\r\n        \"maxWidth\": \"15em\",\r\n        \"isRequired\": true,\r\n        \"validators\": [\r\n        {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 100\r\n        }\r\n        ]\r\n      },\r\n      {\r\n        name: \"qualification\",\r\n        title: \"Qualification\",\r\n        \"cellType\": \"comment\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 100,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n        {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 100\r\n        }\r\n        ]\r\n      }\r\n      ],\r\n      \"rowCount\": 0\r\n    },\r\n     {\r\n      \"type\": \"boolean\",\r\n      \"name\": \"intelPropBusMinutesOfBoardMeetingsInBahamas\",\r\n      \"title\": \"6a.2h Are the minutes of all board meetings kept in the Bahamas?\",\r\n      \"isRequired\": true,\r\n     },\r\n     {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusTotalExpenditureInOperations\",\r\n      \"title\": \"6a.3a Total expenditure incurred in the operations of the relevant activity during the financial period (including outsourcing, if applicable).\",\r\n      \"width\": \"45em\",\r\n      \"minWidth\": \"45em\",\r\n      \"maxWidth\": \"45em\",\r\n      \"columns\": [\r\n        {\r\n          name: \"currency\",\r\n          title: \"Currency\",\r\n          cellType: \"text\",\r\n          readOnly: true,\r\n          defaultValue: \"USD\",\r\n          \"width\": \"5em\",\r\n          \"minWidth\": \"5em\",\r\n          \"maxWidth\": \"5em\"\r\n        },\r\n        {\r\n          name: \"currencyValue\",\r\n          title: \"Total Expenditure\", \r\n          cellType: \"text\",\r\n          isRequired: true,\r\n          inputMask: \"decimal\",\r\n          options: {\r\n            allowMinus: false,\r\n            digits: 2,\r\n            unmaskAsNumber: true,\r\n            prefix: \"\",\r\n            suffix: \"\",\r\n            digitsOptional: true,\r\n            stripLeadingZeroes: false,\r\n          },\r\n          defaultValue: null,\r\n          \"width\": \"35em\",\r\n          \"minWidth\": \"35em\",\r\n          \"maxWidth\": \"35em\"\r\n        }\r\n      ],\r\n      \"allowAddRows\": false,\r\n      \"allowRemoveRows\": false,\r\n      \"rowCount\": 1,\r\n      \"maxRowCount\": 1\r\n     },\r\n     {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusTotalExpenditureInBahamas\",\r\n      \"title\": \"6a.3b Total expenditure incurred **in the Bahamas** in the operations of the relevant activity during the financial period (including outsourcing, if applicable).\",\r\n      \"width\": \"45em\",\r\n      \"minWidth\": \"45em\",\r\n      \"maxWidth\": \"45em\",\r\n      \"columns\": [\r\n        {\r\n          name: \"currency\",\r\n          title: \"Currency\",\r\n          cellType: \"text\",\r\n          readOnly: true,\r\n          defaultValue: \"USD\",\r\n          \"width\": \"5em\",\r\n          \"minWidth\": \"5em\",\r\n          \"maxWidth\": \"5em\"\r\n        },\r\n        {\r\n          name: \"currencyValue\",\r\n          title: \"Total Expenditure\", \r\n          cellType: \"text\",\r\n          isRequired: true,\r\n          inputMask: \"decimal\",\r\n          options: {\r\n            allowMinus: false,\r\n            digits: 2,\r\n            unmaskAsNumber: true,\r\n            prefix: \"\",\r\n            suffix: \"\",\r\n            digitsOptional: true,\r\n            stripLeadingZeroes: false,\r\n          },\r\n          defaultValue: null,\r\n          \"width\": \"35em\",\r\n          \"minWidth\": \"35em\",\r\n          \"maxWidth\": \"35em\"\r\n        }\r\n      ],\r\n      \"allowAddRows\": false,\r\n      \"allowRemoveRows\": false,\r\n      \"rowCount\": 1,\r\n      \"maxRowCount\": 1\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropBusTotalEmployAndCorporateLegalEntity\",\r\n      \"title\": \"6a.4a Total number of full-time equivalent employees of the corporate and legal entity.\",\r\n      \"isRequired\": true,\r\n      \"validators\": [\r\n       {\r\n        \"type\": \"numeric\",\r\n        \"minValue\": -1\r\n       }\r\n      ],\r\n      \"inputMask\": \"decimal\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"digits\": 3,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digitsOptional\": true,\r\n        \"stripLeadingZeroes\": false,\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropBusTotalEmployeesEngaged\",\r\n      \"title\": \"6a.4b Total number of full-time equivalent employees engaged in the relevant activity.\",\r\n      \"isRequired\": true,\r\n      \"validators\": [\r\n       {\r\n        \"type\": \"numeric\",\r\n        \"minValue\": -1\r\n       }\r\n      ],\r\n      \"inputMask\": \"decimal\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"digits\": 3,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digitsOptional\": true,\r\n        \"stripLeadingZeroes\": false,\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n     },\r\n     {\r\n      \"type\": \"text\",\r\n      \"name\": \"intelPropBusTotalEngagedEmployeesInBahamas\",\r\n      \"title\": \"6a.4c Total number of full-time equivalent employees engaged in the relevant activity physically present **in the Bahamas**.\",\r\n      \"isRequired\": true,\r\n      \"validators\": [\r\n       {\r\n        \"type\": \"numeric\",\r\n        \"minValue\": -1\r\n       }\r\n      ],\r\n      \"inputMask\": \"decimal\",\r\n      \"options\": {\r\n        \"allowMinus\": false,\r\n        \"digits\": 3,\r\n        \"unmaskAsNumber\": true,\r\n        \"prefix\": \"\",\r\n        \"suffix\": \"\",\r\n        \"digitsOptional\": true,\r\n        \"stripLeadingZeroes\": false,\r\n      },\r\n      \"size\": 50,\r\n      \"width\": null,\r\n      \"minWidth\": null,\r\n      \"maxWidth\": null\r\n     },\r\n     {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusEmployeeQualificaitions\",\r\n      \"title\": \"6a.4d Provide details on qualifications of the employees in 6a.4c\",\r\n      \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusTotalEngagedEmployeesInBahamas} > 0  = true\",\r\n      \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusTotalEngagedEmployeesInBahamas} > 0\",\r\n      \"columns\": [\r\n       {\r\n        \"title\": \"Name\",\r\n        \"name\": \"name\",\r\n        \"cellType\": \"comment\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 150,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 150\r\n         }\r\n        ]\r\n       },\r\n       {\r\n        \"title\": \"Qualification\",\r\n        \"name\": \"qualification\",\r\n        \"cellType\": \"comment\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 150,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 150\r\n         }\r\n        ]\r\n       },\r\n       {\r\n        \"title\": \"Years of relevant experience\",\r\n        \"name\": \"yearsofRelevantExperience\",\r\n        \"cellType\": \"text\",\r\n        \"isRequired\": true,\r\n        \"inputMask\": \"decimal\",\r\n        \"options\": {\r\n          \"allowMinus\": false,\r\n          \"digits\": 2,\r\n          \"unmaskAsNumber\": true,\r\n          \"prefix\": \"\",\r\n          \"suffix\": \"\",\r\n          \"digitsOptional\": true,\r\n          \"stripLeadingZeroes\": false,\r\n        },\r\n        \"width\": \"15em\",\r\n        \"minWidth\": \"15em\",\r\n      \"maxWidth\": \"15em\"\r\n       },\r\n       {\r\n        \"name\": \"contractType\",\r\n        \"title\": \"Contract Type\",\r\n        \"cellType\": \"comment\",\r\n        \"maxLength\": 150,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 150\r\n         }\r\n        ]\r\n       }\r\n      ],\r\n      \"rowCount\": 0\r\n     },\r\n     {\r\n      \"type\": \"matrixdynamic\",\r\n      \"name\": \"intelPropBusAddressesOfPremises\",\r\n      \"title\": \"6a.5a Provide addresses of all premises within the Bahamas used in connection with the relevant activity.\",\r\n      \"isRequired\": true,\r\n      \"columns\": [\r\n       {\r\n        \"title\": \"Address Line 1\",\r\n        \"name\": \"addressLine1\",\r\n        \"cellType\": \"comment\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 255,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 255\r\n         }\r\n        ]\r\n       },\r\n       {\r\n        \"title\": \"Address Line 2\",\r\n        \"name\": \"addressLine2\",\r\n        \"cellType\": \"comment\",\r\n        \"maxLength\": 255,\r\n        \"autoGrow\": true,\r\n        \"allowResize\": false,\r\n        \"rows\": 1,\r\n        \"validators\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"maxLength\": 255\r\n         }\r\n        ]\r\n       },\r\n       {\r\n        \"title\": \"Country\",\r\n        \"name\": \"country\",\r\n        \"cellType\": \"text\",\r\n        \"readOnly\": true,\r\n        \"defaultValue\": \"Bahamas\",\r\n       }\r\n      ],\r\n      \"rowCount\": 0\r\n     },\r\n     {\r\n      \"type\": \"tagbox\",\r\n      \"name\": \"intelPropBusCIGAInBahamasForRelevantActivity\",\r\n      \"title\": \"6a.6a Select Core Income Generating Activities conducted carried out in the Bahamas for the relevant activity.\",\r\n      \"isRequired\": true,\r\n      \"choicesByUrl\": {\r\n       \"url\": environment['apis']['default']['url'] + \"/api/lookup-service/ciga\",\r\n       \"path\": \"items\",\r\n       \"valueName\": \"id\",\r\n       \"titleName\": \"name\"\r\n      },\r\n      \"showNoneItem\": true\r\n     },\r\n     {\r\n      type: \"text\",\r\n      name: \"intelPropBusCIGAInBahamasForRelevantActivityComment\",\r\n      visibleIf: \"\",\r\n      title: \"CIGA Other (Please Specify)\",\r\n      maxLength: 255,\r\n      isRequired: true\r\n     },\r\n     {\r\n      \"type\": \"boolean\",\r\n      \"name\": \"intelPropBusIsEntityHighRisk\",\r\n      \"title\": \"6a.7 Is the entity a high-risk intellectual property entity?\",\r\n      \"isRequired\": true\r\n     },\r\n     {\r\n      \"type\": \"panel\",\r\n      \"name\": \"9A7Questions\",\r\n      \"elements\": [\r\n       {\r\n        \"type\": \"text\",\r\n        \"name\": \"intelPropBusRelevantIPHeld\",\r\n        \"title\": \"6a.7a Identify the relevant IP asset which it holds.\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 255\r\n       },\r\n       {\r\n        \"type\": \"text\",\r\n        \"name\": \"intelPropBusHowIPAssetGenerateIncome\",\r\n        \"title\": \"6a.7b Explain how the intellectual property asset is being used to generate income.\",\r\n        \"isRequired\": true,\r\n        \"maxLength\": 255\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"intelPropBusEmployeeResponsibleGenerationIncome\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"autoGrow\": true,\r\n          \"allowResize\": false,\r\n          \"rows\": 4,\r\n          \"name\": \"intelPropBusEmployeeResponsibleGenerationIncomeResponse\",\r\n          \"title\": \"Response\",\r\n          \"isRequired\": true,\r\n          \"maxLength\": 1500\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"intelPropBusEmployeeResponsibleGenerationIncomeAttachment\",\r\n          \"showPreview\": false,\r\n          \"title\": \"Supporting Documentation (Image or PDF files only)\",\r\n          \"allowMultiple\": true,\r\n          \"maxSize\": 10485760,\r\n          \"storeDataAsText\":false,\r\n          \"waitForUpload\":true,\r\n          \"validators\": [\r\n            {\r\n                \"type\": \"expression\",\r\n                \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.intelPropBusEmployeeResponsibleGenerationIncomeAttachment})\",\r\n                \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\r\n            }              \r\n          ],\r\n          \"acceptedTypes\":\".pdf,.jpg,.jpeg,.png,.bmp\"\r\n         },\r\n         {\r\n          type: \"html\",\r\n          html: htmlSpinner,\r\n          name: \"spinnerForintelPropBusEmployeeResponsibleGenerationIncomeAttachment\",\r\n          visibleIf: \"{intelPropBusEmployeeResponsibleGenerationIncomeAttachmentSpinner}\"\r\n         },\r\n        ],\r\n        \"title\": \"6a.7c Identify the decisions for which each employee is responsible in respect of the generation of income from the intellectual property asset.\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"intelPropBusStrategicDecisions\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"autoGrow\": true,\r\n          \"allowResize\": false,\r\n          \"rows\": 4,\r\n          \"name\": \"intelPropBusStrategicDecisionsResponse\",\r\n          \"title\": \"Response\",\r\n          \"isRequired\": true,\r\n          \"maxLength\": 1500\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"intelPropBusStrategicDecisionsAttachment\",\r\n          \"showPreview\": false,\r\n          \"title\": \"Supporting Documentation (Image or PDF files only)\",\r\n          \"allowMultiple\": true,\r\n          \"maxSize\": 10485760,\r\n          \"storeDataAsText\":false,\r\n          \"waitForUpload\":true,\r\n          \"validators\": [\r\n            {\r\n                \"type\": \"expression\",\r\n                \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.intelPropBusStrategicDecisionsAttachment})\",\r\n                \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\r\n            }              \r\n          ],\r\n          \"acceptedTypes\":\".pdf,.jpg,.jpeg,.png,.bmp\"\r\n         },\r\n         {\r\n          type: \"html\",\r\n          html: htmlSpinner,\r\n          name: \"spinnerForintelPropBusStrategicDecisionsAttachment\",\r\n          visibleIf: \"{intelPropBusStrategicDecisionsAttachmentSpinner}\"\r\n         },\r\n        ],\r\n        \"title\": \"6a.7d The nature and history of strategic decisions (if any) taken by the entity in the Bahamas.\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"intelPropBusNatureOfTradingActivity\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"autoGrow\": true,\r\n          \"allowResize\": false,\r\n          \"rows\": 4,\r\n          \"name\": \"intelPropBusNatureOfTradingActivityResponse\",\r\n          \"title\": \"Response\",\r\n          \"isRequired\": true,\r\n          \"maxLength\": 1500\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"intelPropBusNatureOfTradingActivityAttachment\",\r\n          \"title\": \"Supporting Documentation (Image or PDF files only)\",\r\n          \"showPreview\": false,\r\n          \"allowMultiple\": true,\r\n          \"maxSize\": 10485760,\r\n          \"storeDataAsText\":false,\r\n          \"waitForUpload\":true,\r\n          \"validators\": [\r\n            {\r\n                \"type\": \"expression\",\r\n                \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.intelPropBusNatureOfTradingActivityAttachment})\",\r\n                \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\r\n            }              \r\n          ],\r\n          \"acceptedTypes\":\".pdf,.jpg,.jpeg,.png,.bmp\"\r\n         },\r\n         {\r\n          type: \"html\",\r\n          html: htmlSpinner,\r\n          name: \"spinnerForintelPropBusNatureOfTradingActivityAttachment\",\r\n          visibleIf: \"{intelPropBusNatureOfTradingActivityAttachmentSpinner}\"\r\n         },\r\n        ],\r\n        \"title\": \"6a.7e The nature and history of the trading activities (if any carried out in the Bahamas by which) the intellectual property assets is exploited for the purpose of generating income from third parties.\",\r\n       }\r\n      ],\r\n     },\r\n     {\r\n      \"type\": \"panel\",\r\n      \"name\": \"highRiskEntityQuestions\",\r\n      //\"title\": \"6a.8 High Risk Entity Questions\",\r\n      \"elements\": [\r\n       {\r\n        \"type\": \"matrixdynamic\",\r\n        \"name\": \"highRiskGrossIncomeRoyalties\",\r\n        \"title\": \"6a.8a Gross income through Royalties, if applicable.\",\r\n        \"width\": \"45em\",\r\n        \"minWidth\": \"45em\",\r\n      \"maxWidth\": \"45em\",\r\n        \"columns\": [\r\n          {\r\n            name: \"currency\",\r\n            title: \"Currency\",\r\n            cellType: \"text\",\r\n            readOnly: true,\r\n            defaultValue: \"USD\",\r\n            \"width\": \"5em\",\r\n            \"minWidth\": \"5em\",\r\n            \"maxWidth\": \"5em\"\r\n          },\r\n          {\r\n            name: \"currencyValue\",\r\n            title: \"Gross Income\", \r\n            cellType: \"text\",\r\n            inputMask: \"decimal\",\r\n            options: {\r\n              allowMinus: false,\r\n              digits: 2,\r\n              unmaskAsNumber: true,\r\n              prefix: \"\",\r\n              suffix: \"\",\r\n              digitsOptional: true,\r\n              stripLeadingZeroes: false,\r\n            },\r\n            defaultValue: null,\r\n            \"width\": \"35em\",\r\n            \"minWidth\": \"35em\",\r\n            \"maxWidth\": \"35em\"\r\n          }\r\n        ],\r\n        \"allowAddRows\": false,\r\n        \"allowRemoveRows\": false,\r\n        \"rowCount\": 1,\r\n        \"maxRowCount\": 1\r\n       },\r\n       {\r\n        \"type\": \"matrixdynamic\",\r\n        \"name\": \"highRiskGrossIncomeThroughGains\",\r\n        \"title\": \"6a.8b Gross income through Gains from sale of IP asset, if applicable.\",\r\n        \"width\": \"45em\",\r\n        \"minWidth\": \"45em\",\r\n        \"maxWidth\": \"45em\",\r\n        \"columns\": [\r\n          {\r\n            name: \"currency\",\r\n            title: \"Currency\",\r\n            cellType: \"text\",\r\n            readOnly: true,\r\n            defaultValue: \"USD\",\r\n            \"width\": \"5em\",\r\n            \"minWidth\": \"5em\",\r\n            \"maxWidth\": \"5em\"\r\n          },\r\n          {\r\n            name: \"currencyValue\",\r\n            title: \"Gross Income\", \r\n            cellType: \"text\",\r\n            inputMask: \"decimal\",\r\n            options: {\r\n              allowMinus: false,\r\n              digits: 2,\r\n              unmaskAsNumber: true,\r\n              prefix: \"\",\r\n              suffix: \"\",\r\n              digitsOptional: true,\r\n              stripLeadingZeroes: false,\r\n            },\r\n            defaultValue: null,\r\n            \"width\": \"35em\",\r\n            \"minWidth\": \"35em\",\r\n            \"maxWidth\": \"35em\",\r\n          }\r\n        ],\r\n        \"allowAddRows\": false,\r\n        \"allowRemoveRows\": false,\r\n        \"rowCount\": 1,\r\n        \"maxRowCount\": 1\r\n       },\r\n       {\r\n        \"type\": \"matrixdynamic\",\r\n        \"name\": \"highRiskGrossIncomeThroughOthers\",\r\n        \"title\": \"6a.8c Gross income through others\",\r\n        \"width\": \"45em\",\r\n        \"minWidth\": \"45em\",\r\n        \"maxWidth\": \"45em\",\r\n        \"columns\": [\r\n          {\r\n            name: \"currency\",\r\n            title: \"Currency\",\r\n            cellType: \"text\",\r\n            readOnly: true,\r\n            defaultValue: \"USD\",\r\n            \"width\": \"5em\",\r\n            \"minWidth\": \"5em\",\r\n            \"maxWidth\": \"5em\",\r\n          },\r\n          {\r\n            name: \"currencyValue\",\r\n            title: \"Gross Income\", \r\n            cellType: \"text\",\r\n            inputMask: \"decimal\",\r\n            options: {\r\n              allowMinus: false,\r\n              digits: 2,\r\n              unmaskAsNumber: true,\r\n              prefix: \"\",\r\n              suffix: \"\",\r\n              digitsOptional: true,\r\n              stripLeadingZeroes: false,\r\n            },\r\n            defaultValue: null,\r\n            \"width\": \"35em\",\r\n            \"minWidth\": \"35em\",\r\n            \"maxWidth\": \"35em\",\r\n          }\r\n        ],\r\n        \"allowAddRows\": false,\r\n        \"allowRemoveRows\": false,\r\n        \"rowCount\": 1,\r\n        \"maxRowCount\": 1\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"highRiskBusinessPlanDetailsQuestion\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"autoGrow\": true,\r\n          \"allowResize\": false,\r\n          \"rows\": 4,\r\n          \"name\": \"highRiskBusinessPlanDetailsResponse\",\r\n          \"title\": \"Response\",\r\n          \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n          \"maxLength\": 1500\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"highRiskBusinessPlanDetailsAttachment\",\r\n          \"title\": \"Supporting Documentation (Image or PDF files only)\",\r\n          \"showPreview\": false,\r\n          \"allowMultiple\": true,\r\n          \"maxSize\": 10485760,\r\n          \"storeDataAsText\":false,\r\n          \"waitForUpload\":true,\r\n          \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n          \"validators\": [\r\n            {\r\n                \"type\": \"expression\",\r\n                \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.highRiskBusinessPlanDetailsAttachment})\",\r\n                \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\r\n            }              \r\n          ],\r\n          \"acceptedTypes\":\".pdf,.jpg,.jpeg,.png,.bmp\"\r\n         },\r\n         {\r\n          type: \"html\",\r\n          html: htmlSpinner,\r\n          name: \"spinnerForhighRiskBusinessPlanDetailsAttachment\",\r\n          visibleIf: \"{highRiskBusinessPlanDetailsAttachmentSpinner}\"\r\n         },\r\n        ],\r\n        \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n        \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n        \"title\": \"6a.8d Provide detailed business plans which explain the commercial rationale of holding the intellectual property assets in the Bahamas.\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"highRiskEvidenceDecisionMakingInBahamasQuestion\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"autoGrow\": true,\r\n          \"allowResize\": false,\r\n          \"rows\": 4,\r\n          \"name\": \"highRiskEvidenceDecisionMakingInBahamasResponse\",\r\n          \"title\": \"Response\",\r\n          \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n          \"maxLength\": 1500\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"highRiskEvidenceDecisionMakingInBahamasAttachment\",\r\n          \"title\": \"Supporting Documentation (Image or PDF files only)\",\r\n          \"showPreview\": false,\r\n          \"allowMultiple\": true,\r\n          \"maxSize\": 10485760,\r\n          \"storeDataAsText\":false,\r\n          \"waitForUpload\":true,\r\n          \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n          \"validators\": [\r\n            {\r\n                \"type\": \"expression\",\r\n                \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.highRiskEvidenceDecisionMakingInBahamasAttachment})\",\r\n                \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\r\n            }              \r\n          ],\r\n          \"acceptedTypes\":\".pdf,.jpg,.jpeg,.png,.bmp\"\r\n         },\r\n         {\r\n          type: \"html\",\r\n          html: htmlSpinner,\r\n          name: \"spinnerForhighRiskEvidenceDecisionMakingInBahamasAttachment\",\r\n          visibleIf: \"{highRiskEvidenceDecisionMakingInBahamasAttachmentSpinner}\"\r\n         },\r\n        ],\r\n        \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n        \"title\": \"6a.8e Provide concrete evidence that decision-making is taking place within the Bahamas, including but not limited to, minutes of meetings which have taken place in the Bahamas.\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"highRiskOtherFacts\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"autoGrow\": true,\r\n          \"allowResize\": false,\r\n          \"rows\": 4,\r\n          \"name\": \"highRiskOtherFactsResponse\",\r\n          \"title\": \"Response\",\r\n          \"requiredIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n          \"maxLength\": 1500\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"highRiskOtherFactsAttachment\",\r\n          \"title\": \"Supporting Documentation (Image or PDF files only)\",\r\n          \"showPreview\": false,\r\n          \"allowMultiple\": true,\r\n          \"maxSize\": 10485760,\r\n          \"storeDataAsText\":false,\r\n          \"waitForUpload\":true,\r\n          \"validators\": [\r\n            {\r\n                \"type\": \"expression\",\r\n                \"expression\": \"isAcceptedFileType({IntellectualPropertyBusiness.highRiskOtherFactsAttachment})\",\r\n                \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\r\n            }\r\n          ],\r\n          \"acceptedTypes\":\".pdf,.jpg,.jpeg,.png,.bmp\"\r\n         },\r\n         {\r\n          type: \"html\",\r\n          html: htmlSpinner,\r\n          name: \"spinnerForhighRiskOtherFactsAttachment\",\r\n          visibleIf: \"{highRiskOtherFactsAttachmentSpinner}\"\r\n         },\r\n        ],\r\n        \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n        \"title\": \"6a.8f Any other facts and matters necessary to demonstrate compliance?\"\r\n       }\r\n      ],\r\n      \"visibleIf\": \"{IntellectualPropertyBusiness.intelPropBusIsEntityHighRisk} = true\",\r\n     },\r\n     {\r\n      \"type\": \"outsourcing\",\r\n      \"name\": \"outsourcingIntellectualPropertyBusiness\",\r\n      \"visibleIf\": \"{relevantActRelevantActivities} anyof ['other', 'Intellectual property business']\",\r\n      \"title\": \"Outsourcing Intellectual Property Business\",\r\n      \"hideNumber\": true,\r\n      \"titleLocation\": \"hidden\"\r\n    },\r\n  ]\r\n}\r\n\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,2BAA2B;AAEvD,MAAMC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;CAsBnB;AACD,OAAO,MAAMC,kBAAkB,GAAG;EAC9BC,IAAI,EAAE,8BAA8B;EACtCC,KAAK,EAAE,wCAAwC;EAC/CC,YAAY,EAAE,CACZ;IACE,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,gCAAgC;IACxC,OAAO,EAAE,sDAAsD;IAC/D,YAAY,EAAE;GACf,EACD;IACE,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,yCAAyC;IACjD,OAAO,EAAE,eAAe;IACxB,UAAU,EAAE,sEAAsE;IAClF,YAAY,EAAE,sEAAsE;IACpF,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,CACZ;MACI,MAAM,EAAE,YAAY;MACpB,YAAY,EAAE,uJAAuJ;MACrK,MAAM,EAAE;KACX,CACF;IACD,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE;GACb,EACD;IACE,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,uCAAuC;IAC/C,kBAAkB,EAAE,KAAK;IACzB,OAAO,EAAE,aAAa;IACtB,UAAU,EAAE,sEAAsE;IAClF,YAAY,EAAE,sEAAsE;IACpF,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,CACZ;MACI,MAAM,EAAE,YAAY;MACpB,YAAY,EAAE,qJAAqJ;MACnK,MAAM,EAAE;KACX,CACF;IACD,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE;GACb,EACD;IACE,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,yBAAyB;IACjC,OAAO,EAAE,iFAAiF;IAC1F,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE,CACT;MACEF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnB,OAAO,EAAE,KAAK;MACd,UAAU,EAAE,KAAK;MACjB,UAAU,EAAE;KACb,EACD;MACEL,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,cAAc;MACrBE,QAAQ,EAAE,MAAM;MAChBG,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,SAAS;MACpBC,OAAO,EAAE;QACPC,UAAU,EAAE,KAAK;QACjBC,MAAM,EAAE,CAAC;QACTC,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,cAAc,EAAE,IAAI;QACpBC,kBAAkB,EAAE;OACrB;MACDV,YAAY,EAAE,IAAI;MAClB,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE;KACb,CACF;IACD,cAAc,EAAE,KAAK;IACrB,iBAAiB,EAAE,KAAK;IACxB,UAAU,EAAE,CAAC;IACb,aAAa,EAAE;GACf,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,0BAA0B;IAClC,OAAO,EAAE,kIAAkI;IAC3I,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,gBAAgB,EAAE,IAAI;MACtB,oBAAoB,EAAE;KACvB;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACb,EACA;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,2BAA2B;IACnC,OAAO,EAAE,mLAAmL;IAC5L,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,GAAG;IAChB,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,KAAK;IACpB,MAAM,EAAE;GACR,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,4CAA4C;IACpD,OAAO,EAAE,4DAA4D;IACrE,YAAY,EAAE;GACd,EACD;IACC,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,sDAAsD;IAC9D,OAAO,EAAE,gHAAgH;IACzH,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,CACV;MACC,MAAM,EAAE,MAAM;MACb,OAAO,EAAC,MAAM;MACd,YAAY,EAAE,IAAI;MACnB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,WAAW,EAAE,GAAG;MAChB,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,EACD;MACC,OAAO,EAAE,0BAA0B;MACnC,YAAY,EAAE,IAAI;MAClB,MAAM,EAAE,sBAAsB;MAC9B,UAAU,EAAE;KACZ,EACD;MACC,OAAO,EAAE,wBAAwB;MACjC,YAAY,EAAE,IAAI;MAClB,MAAM,EAAE,qBAAqB;MAC7B,UAAU,EAAE,SAAS;MACrB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,CACD;IACD,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,mCAAmC;IAC3C,OAAO,EAAE,4GAA4G;IACrH,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,oBAAoB,EAAE,KAAK;MAC3B,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,KAAK,EAAC;KACP;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,yCAAyC;IACjD,OAAO,EAAE,+HAA+H;IACxI,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,oBAAoB,EAAE,KAAK;MAC3B,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,KAAK,EAAC;KACP;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,oCAAoC;IAC5C,OAAO,EAAE,iCAAiC;IAC1C,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,oBAAoB,EAAE,KAAK;MAC3B,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,KAAK,EAAC;KACP;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACb,EACD;IACE,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,oCAAoC;IAC5C,OAAO,EAAE,mHAAmH;IAC5H,YAAY,EAAE;GACf,EACD;IACE,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,mDAAmD;IAC3D,OAAO,EAAE,iIAAiI;IAC1I,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,CACT;MACAL,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,gBAAgB;MACvB,UAAU,EAAE,MAAM;MAClB,WAAW,EAAE,SAAS;MACtB,YAAY,EAAE,IAAI;MAClB,SAAS,EAAE;QACT,YAAY,EAAE,KAAK;QACnB,oBAAoB,EAAE,KAAK;QAC3B,gBAAgB,EAAE,IAAI;QACtB,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,CAAC;QACX,gBAAgB,EAAE,IAAI;QACtB,KAAK,EAAC;;KAET,EACD;MACED,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACb,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACd;QACE,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACd;KAEF,EACD;MACED,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,qBAAqB;MAC5B,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE;KACf,EACD;MACED,IAAI,EAAE,yBAAyB;MAC/BC,KAAK,EAAE,8BAA8B;MACrC,UAAU,EAAE,MAAM;MAClB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE;QACT,YAAY,EAAE,KAAK;QACnB,QAAQ,EAAE,CAAC;QACX,gBAAgB,EAAE,IAAI;QACtB,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;QACZ,gBAAgB,EAAE,IAAI;QACxB,oBAAoB,EAAE;OACrB;MACD,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE,MAAM;MAClB,YAAY,EAAE,IAAI;MAClB,YAAY,EAAE,CACd;QACE,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACd;KAEF,EACD;MACED,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,eAAe;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACd;QACE,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACd;KAEF,CACA;IACD,UAAU,EAAE;GACb,EACA;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,6CAA6C;IACrD,OAAO,EAAE,kEAAkE;IAC3E,YAAY,EAAE;GACd,EACD;IACC,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,0CAA0C;IAClD,OAAO,EAAE,iJAAiJ;IAC1J,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE,CACT;MACED,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnB,OAAO,EAAE,KAAK;MACd,UAAU,EAAE,KAAK;MACjB,UAAU,EAAE;KACb,EACD;MACEL,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,mBAAmB;MAC1BE,QAAQ,EAAE,MAAM;MAChBG,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,SAAS;MACpBC,OAAO,EAAE;QACPC,UAAU,EAAE,KAAK;QACjBC,MAAM,EAAE,CAAC;QACTC,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,cAAc,EAAE,IAAI;QACpBC,kBAAkB,EAAE;OACrB;MACDV,YAAY,EAAE,IAAI;MAClB,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE;KACb,CACF;IACD,cAAc,EAAE,KAAK;IACrB,iBAAiB,EAAE,KAAK;IACxB,UAAU,EAAE,CAAC;IACb,aAAa,EAAE;GACf,EACD;IACC,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,uCAAuC;IAC/C,OAAO,EAAE,oKAAoK;IAC7K,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE,CACT;MACEL,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnB,OAAO,EAAE,KAAK;MACd,UAAU,EAAE,KAAK;MACjB,UAAU,EAAE;KACb,EACD;MACEL,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,mBAAmB;MAC1BE,QAAQ,EAAE,MAAM;MAChBG,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,SAAS;MACpBC,OAAO,EAAE;QACPC,UAAU,EAAE,KAAK;QACjBC,MAAM,EAAE,CAAC;QACTC,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,cAAc,EAAE,IAAI;QACpBC,kBAAkB,EAAE;OACrB;MACDV,YAAY,EAAE,IAAI;MAClB,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE;KACb,CACF;IACD,cAAc,EAAE,KAAK;IACrB,iBAAiB,EAAE,KAAK;IACxB,UAAU,EAAE,CAAC;IACb,aAAa,EAAE;GACf,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,gDAAgD;IACxD,OAAO,EAAE,yFAAyF;IAClG,YAAY,EAAE,IAAI;IAClB,YAAY,EAAE,CACb;MACC,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,CAAC;KACb,CACD;IACD,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,gBAAgB,EAAE,IAAI;MACtB,oBAAoB,EAAE;KACvB;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,mCAAmC;IAC3C,OAAO,EAAE,wFAAwF;IACjG,YAAY,EAAE,IAAI;IAClB,YAAY,EAAE,CACb;MACC,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,CAAC;KACb,CACD;IACD,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,gBAAgB,EAAE,IAAI;MACtB,oBAAoB,EAAE;KACvB;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,4CAA4C;IACpD,OAAO,EAAE,8HAA8H;IACvI,YAAY,EAAE,IAAI;IAClB,YAAY,EAAE,CACb;MACC,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,CAAC;KACb,CACD;IACD,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT,YAAY,EAAE,KAAK;MACnB,QAAQ,EAAE,CAAC;MACX,gBAAgB,EAAE,IAAI;MACtB,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,EAAE;MACZ,gBAAgB,EAAE,IAAI;MACtB,oBAAoB,EAAE;KACvB;IACD,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,qCAAqC;IAC7C,OAAO,EAAE,mEAAmE;IAC5E,WAAW,EAAE,uFAAuF;IACpG,YAAY,EAAE,+EAA+E;IAC7F,SAAS,EAAE,CACV;MACC,OAAO,EAAE,MAAM;MACf,MAAM,EAAE,MAAM;MACd,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,EACD;MACC,OAAO,EAAE,eAAe;MACxB,MAAM,EAAE,eAAe;MACvB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,EACD;MACC,OAAO,EAAE,8BAA8B;MACvC,MAAM,EAAE,2BAA2B;MACnC,UAAU,EAAE,MAAM;MAClB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE;QACT,YAAY,EAAE,KAAK;QACnB,QAAQ,EAAE,CAAC;QACX,gBAAgB,EAAE,IAAI;QACtB,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;QACZ,gBAAgB,EAAE,IAAI;QACtB,oBAAoB,EAAE;OACvB;MACD,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MACpB,UAAU,EAAE;KACV,EACD;MACC,MAAM,EAAE,cAAc;MACtB,OAAO,EAAE,eAAe;MACxB,UAAU,EAAE,SAAS;MACrB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,CACD;IACD,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,iCAAiC;IACzC,OAAO,EAAE,2GAA2G;IACpH,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,CACV;MACC,OAAO,EAAE,gBAAgB;MACzB,MAAM,EAAE,cAAc;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,EACD;MACC,OAAO,EAAE,gBAAgB;MACzB,MAAM,EAAE,cAAc;MACtB,UAAU,EAAE,SAAS;MACrB,WAAW,EAAE,GAAG;MAChB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,KAAK;MACpB,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,CACb;QACC,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;OACb;KAEF,EACD;MACC,OAAO,EAAE,SAAS;MAClB,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE,IAAI;MAChB,cAAc,EAAE;KAChB,CACD;IACD,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,8CAA8C;IACtD,OAAO,EAAE,gHAAgH;IACzH,YAAY,EAAE,IAAI;IAClB,cAAc,EAAE;MACf,KAAK,EAAER,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,0BAA0B;MACzE,MAAM,EAAE,OAAO;MACf,WAAW,EAAE,IAAI;MACjB,WAAW,EAAE;KACb;IACD,cAAc,EAAE;GAChB,EACD;IACCmB,IAAI,EAAE,MAAM;IACZhB,IAAI,EAAE,qDAAqD;IAC3DiB,SAAS,EAAE,EAAE;IACbhB,KAAK,EAAE,6BAA6B;IACpCiB,SAAS,EAAE,GAAG;IACdZ,UAAU,EAAE;GACZ,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,8BAA8B;IACtC,OAAO,EAAE,8DAA8D;IACvE,YAAY,EAAE;GACd,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,cAAc;IACtB,UAAU,EAAE,CACX;MACC,MAAM,EAAE,MAAM;MACd,MAAM,EAAE,4BAA4B;MACpC,OAAO,EAAE,sDAAsD;MAC/D,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE;KACb,EACD;MACC,MAAM,EAAE,MAAM;MACd,MAAM,EAAE,sCAAsC;MAC9C,OAAO,EAAE,qFAAqF;MAC9F,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE;KACb,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,iDAAiD;MACzD,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,yDAAyD;QACjE,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,2DAA2D;QACnE,aAAa,EAAE,KAAK;QACpB,OAAO,EAAE,oDAAoD;QAC7D,eAAe,EAAE,IAAI;QACrB,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAC,KAAK;QACvB,eAAe,EAAC,IAAI;QACpB,YAAY,EAAE,CACZ;UACI,MAAM,EAAE,YAAY;UACpB,YAAY,EAAE,8GAA8G;UAC5H,MAAM,EAAE;SACX,CACF;QACD,eAAe,EAAC;OAChB,EACD;QACCU,IAAI,EAAE,MAAM;QACZG,IAAI,EAAErB,WAAW;QACjBE,IAAI,EAAE,qEAAqE;QAC3EiB,SAAS,EAAE;OACX,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,gCAAgC;MACxC,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,wCAAwC;QAChD,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,0CAA0C;QAClD,aAAa,EAAE,KAAK;QACpB,OAAO,EAAE,oDAAoD;QAC7D,eAAe,EAAE,IAAI;QACrB,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAC,KAAK;QACvB,eAAe,EAAC,IAAI;QACpB,YAAY,EAAE,CACZ;UACI,MAAM,EAAE,YAAY;UACpB,YAAY,EAAE,6FAA6F;UAC3G,MAAM,EAAE;SACX,CACF;QACD,eAAe,EAAC;OAChB,EACD;QACCD,IAAI,EAAE,MAAM;QACZG,IAAI,EAAErB,WAAW;QACjBE,IAAI,EAAE,oDAAoD;QAC1DiB,SAAS,EAAE;OACX,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,qCAAqC;MAC7C,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,6CAA6C;QACrD,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,+CAA+C;QACvD,OAAO,EAAE,oDAAoD;QAC7D,aAAa,EAAE,KAAK;QACpB,eAAe,EAAE,IAAI;QACrB,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAC,KAAK;QACvB,eAAe,EAAC,IAAI;QACpB,YAAY,EAAE,CACZ;UACI,MAAM,EAAE,YAAY;UACpB,YAAY,EAAE,kGAAkG;UAChH,MAAM,EAAE;SACX,CACF;QACD,eAAe,EAAC;OAChB,EACD;QACCD,IAAI,EAAE,MAAM;QACZG,IAAI,EAAErB,WAAW;QACjBE,IAAI,EAAE,yDAAyD;QAC/DiB,SAAS,EAAE;OACX,CACD;MACD,OAAO,EAAE;KACT;GAEF,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,yBAAyB;IACjC;IACA,UAAU,EAAE,CACX;MACC,MAAM,EAAE,eAAe;MACvB,MAAM,EAAE,8BAA8B;MACtC,OAAO,EAAE,sDAAsD;MAC/D,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MACpB,UAAU,EAAE,MAAM;MAChB,SAAS,EAAE,CACT;QACEjB,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,UAAU;QACjBE,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,KAAK;QACnB,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE;OACb,EACD;QACEL,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,cAAc;QACrBE,QAAQ,EAAE,MAAM;QAChBI,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE;UACPC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,CAAC;UACTC,cAAc,EAAE,IAAI;UACpBC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,cAAc,EAAE,IAAI;UACpBC,kBAAkB,EAAE;SACrB;QACDV,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE;OACb,CACF;MACD,cAAc,EAAE,KAAK;MACrB,iBAAiB,EAAE,KAAK;MACxB,UAAU,EAAE,CAAC;MACb,aAAa,EAAE;KACf,EACD;MACC,MAAM,EAAE,eAAe;MACvB,MAAM,EAAE,iCAAiC;MACzC,OAAO,EAAE,wEAAwE;MACjF,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE,MAAM;MAClB,SAAS,EAAE,CACT;QACEL,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,UAAU;QACjBE,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,KAAK;QACnB,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE;OACb,EACD;QACEL,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,cAAc;QACrBE,QAAQ,EAAE,MAAM;QAChBI,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE;UACPC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,CAAC;UACTC,cAAc,EAAE,IAAI;UACpBC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,cAAc,EAAE,IAAI;UACpBC,kBAAkB,EAAE;SACrB;QACDV,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE;OACb,CACF;MACD,cAAc,EAAE,KAAK;MACrB,iBAAiB,EAAE,KAAK;MACxB,UAAU,EAAE,CAAC;MACb,aAAa,EAAE;KACf,EACD;MACC,MAAM,EAAE,eAAe;MACvB,MAAM,EAAE,kCAAkC;MAC1C,OAAO,EAAE,mCAAmC;MAC5C,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE,MAAM;MAClB,SAAS,EAAE,CACT;QACEL,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,UAAU;QACjBE,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,KAAK;QACnB,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE;OACb,EACD;QACEL,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,cAAc;QACrBE,QAAQ,EAAE,MAAM;QAChBI,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE;UACPC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,CAAC;UACTC,cAAc,EAAE,IAAI;UACpBC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,cAAc,EAAE,IAAI;UACpBC,kBAAkB,EAAE;SACrB;QACDV,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE;OACb,CACF;MACD,cAAc,EAAE,KAAK;MACrB,iBAAiB,EAAE,KAAK;MACxB,UAAU,EAAE,CAAC;MACb,aAAa,EAAE;KACf,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,qCAAqC;MAC7C,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,qCAAqC;QAC7C,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,oEAAoE;QAClF,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,uCAAuC;QAC/C,OAAO,EAAE,oDAAoD;QAC7D,aAAa,EAAE,KAAK;QACpB,eAAe,EAAE,IAAI;QACrB,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAC,KAAK;QACvB,eAAe,EAAC,IAAI;QACpB,YAAY,EAAE,oEAAoE;QAClF,YAAY,EAAE,CACZ;UACI,MAAM,EAAE,YAAY;UACpB,YAAY,EAAE,0FAA0F;UACxG,MAAM,EAAE;SACX,CACF;QACD,eAAe,EAAC;OAChB,EACD;QACCW,IAAI,EAAE,MAAM;QACZG,IAAI,EAAErB,WAAW;QACjBE,IAAI,EAAE,iDAAiD;QACvDiB,SAAS,EAAE;OACX,CACD;MACD,WAAW,EAAE,oEAAoE;MACjF,YAAY,EAAE,oEAAoE;MAClF,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,iDAAiD;MACzD,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,iDAAiD;QACzD,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,oEAAoE;QAClF,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,mDAAmD;QAC3D,OAAO,EAAE,oDAAoD;QAC7D,aAAa,EAAE,KAAK;QACpB,eAAe,EAAE,IAAI;QACrB,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAC,KAAK;QACvB,eAAe,EAAC,IAAI;QACpB,YAAY,EAAE,oEAAoE;QAClF,YAAY,EAAE,CACZ;UACI,MAAM,EAAE,YAAY;UACpB,YAAY,EAAE,sGAAsG;UACpH,MAAM,EAAE;SACX,CACF;QACD,eAAe,EAAC;OAChB,EACD;QACCD,IAAI,EAAE,MAAM;QACZG,IAAI,EAAErB,WAAW;QACjBE,IAAI,EAAE,6DAA6D;QACnEiB,SAAS,EAAE;OACX,CACD;MACD,WAAW,EAAE,oEAAoE;MACjF,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,oBAAoB;MAC5B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,4BAA4B;QACpC,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,oEAAoE;QAClF,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,8BAA8B;QACtC,OAAO,EAAE,oDAAoD;QAC7D,aAAa,EAAE,KAAK;QACpB,eAAe,EAAE,IAAI;QACrB,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAC,KAAK;QACvB,eAAe,EAAC,IAAI;QACpB,YAAY,EAAE,CACZ;UACI,MAAM,EAAE,YAAY;UACpB,YAAY,EAAE,iFAAiF;UAC/F,MAAM,EAAE;SACX,CACF;QACD,eAAe,EAAC;OAChB,EACD;QACCD,IAAI,EAAE,MAAM;QACZG,IAAI,EAAErB,WAAW;QACjBE,IAAI,EAAE,wCAAwC;QAC9CiB,SAAS,EAAE;OACX,CACD;MACD,WAAW,EAAE,oEAAoE;MACjF,OAAO,EAAE;KACT,CACD;IACD,WAAW,EAAE;GACb,EACD;IACC,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,yCAAyC;IACjD,WAAW,EAAE,mFAAmF;IAChG,OAAO,EAAE,4CAA4C;IACrD,YAAY,EAAE,IAAI;IAClB,eAAe,EAAE;GAClB;CAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}