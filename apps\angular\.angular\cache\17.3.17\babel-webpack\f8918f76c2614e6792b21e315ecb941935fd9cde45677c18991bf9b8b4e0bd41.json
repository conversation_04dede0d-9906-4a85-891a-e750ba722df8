{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"kea\", [[\"am\", \"pm\"], u, u], u, [[\"D\", \"S\", \"T\", \"K\", \"K\", \"S\", \"S\"], [\"dum\", \"sig\", \"ter\", \"kua\", \"kin\", \"ses\", \"sab\"], [\"dumingu\", \"sigunda-fera\", \"tersa-fera\", \"kuarta-fera\", \"kinta-fera\", \"sesta-fera\", \"sábadu\"], [\"du\", \"si\", \"te\", \"ku\", \"ki\", \"se\", \"sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"<PERSON>\", \"<PERSON>\", \"J\", \"A\", \"<PERSON>\", \"O\", \"N\", \"D\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>br\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>uv\", \"<PERSON>z\"], [\"<PERSON>ru\", \"<PERSON><PERSON>u\", \"<PERSON>u\", \"<PERSON>bril\", \"<PERSON>u\", \"Junhu\", \"<PERSON>hu\", \"A<PERSON>tu\", \"<PERSON>enbru\", \"Otubru\", \"Nuvenbru\", \"<PERSON>zenbru\"]], u, [[\"<PERSON>\", \"D<PERSON>\"], u, [\"antis di <PERSON>tu\", \"dispos di <PERSON>tu\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d 'di' MMMM 'di' y\", \"EEEE, d 'di' MMMM 'di' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"CVE\", \"​\", \"Skudu Kabuverdianu\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"CVE\": [\"​\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"THB\": [\"฿\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/kea.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"kea\", [[\"am\", \"pm\"], u, u], u, [[\"D\", \"S\", \"T\", \"K\", \"K\", \"S\", \"S\"], [\"dum\", \"sig\", \"ter\", \"kua\", \"kin\", \"ses\", \"sab\"], [\"dumingu\", \"sigunda-fera\", \"tersa-fera\", \"kuarta-fera\", \"kinta-fera\", \"sesta-fera\", \"sábadu\"], [\"du\", \"si\", \"te\", \"ku\", \"ki\", \"se\", \"sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"<PERSON>\", \"<PERSON>\", \"J\", \"A\", \"<PERSON>\", \"O\", \"N\", \"D\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>br\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>uv\", \"<PERSON>z\"], [\"<PERSON>ru\", \"<PERSON><PERSON>u\", \"<PERSON>u\", \"<PERSON>bril\", \"<PERSON>u\", \"Junhu\", \"<PERSON>hu\", \"A<PERSON>tu\", \"<PERSON>enbru\", \"Otubru\", \"Nuvenbru\", \"<PERSON>zenbru\"]], u, [[\"<PERSON>\", \"D<PERSON>\"], u, [\"antis di <PERSON>tu\", \"dispos di <PERSON>tu\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d 'di' MMMM 'di' y\", \"EEEE, d 'di' MMMM 'di' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"CVE\", \"​\", \"Skudu Kabuverdianu\", { \"AUD\": [\"AU$\", \"$\"], \"CVE\": [\"​\"], \"JPY\": [\"JP¥\", \"¥\"], \"THB\": [\"฿\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,0BAA0B,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,oBAAoB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}