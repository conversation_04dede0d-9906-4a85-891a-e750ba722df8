{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\nimport { LogLevel } from \"./ILogger\";\nimport { isArrayBuffer } from \"./Utils\";\nexport class XhrHttpClient extends HttpClient {\n  constructor(logger) {\n    super();\n    this._logger = logger;\n  }\n  /** @inheritDoc */\n  send(request) {\n    // Check that abort was not signaled before calling send\n    if (request.abortSignal && request.abortSignal.aborted) {\n      return Promise.reject(new AbortError());\n    }\n    if (!request.method) {\n      return Promise.reject(new Error(\"No method defined.\"));\n    }\n    if (!request.url) {\n      return Promise.reject(new Error(\"No url defined.\"));\n    }\n    return new Promise((resolve, reject) => {\n      const xhr = new XMLHttpRequest();\n      xhr.open(request.method, request.url, true);\n      xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\n      xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\n      if (request.content === \"\") {\n        request.content = undefined;\n      }\n      if (request.content) {\n        // Explicitly setting the Content-Type header for React Native on Android platform.\n        if (isArrayBuffer(request.content)) {\n          xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\n        } else {\n          xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\n        }\n      }\n      const headers = request.headers;\n      if (headers) {\n        Object.keys(headers).forEach(header => {\n          xhr.setRequestHeader(header, headers[header]);\n        });\n      }\n      if (request.responseType) {\n        xhr.responseType = request.responseType;\n      }\n      if (request.abortSignal) {\n        request.abortSignal.onabort = () => {\n          xhr.abort();\n          reject(new AbortError());\n        };\n      }\n      if (request.timeout) {\n        xhr.timeout = request.timeout;\n      }\n      xhr.onload = () => {\n        if (request.abortSignal) {\n          request.abortSignal.onabort = null;\n        }\n        if (xhr.status >= 200 && xhr.status < 300) {\n          resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\n        } else {\n          reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\n        }\n      };\n      xhr.onerror = () => {\n        this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\n        reject(new HttpError(xhr.statusText, xhr.status));\n      };\n      xhr.ontimeout = () => {\n        this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\n        reject(new TimeoutError());\n      };\n      xhr.send(request.content);\n    });\n  }\n}", "map": {"version": 3, "names": ["AbortError", "HttpError", "TimeoutError", "HttpClient", "HttpResponse", "LogLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "XhrHttpClient", "constructor", "logger", "_logger", "send", "request", "abortSignal", "aborted", "Promise", "reject", "method", "Error", "url", "resolve", "xhr", "XMLHttpRequest", "open", "withCredentials", "undefined", "setRequestHeader", "content", "headers", "Object", "keys", "for<PERSON>ach", "header", "responseType", "<PERSON>ab<PERSON>", "abort", "timeout", "onload", "status", "statusText", "response", "responseText", "onerror", "log", "Warning", "ontimeout"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\nexport class XhrHttpClient extends HttpClient {\r\n    constructor(logger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n    /** @inheritDoc */\r\n    send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n            xhr.open(request.method, request.url, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if (isArrayBuffer(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                }\r\n                else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                    xhr.setRequestHeader(header, headers[header]);\r\n                });\r\n            }\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                }\r\n                else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA,SAASA,UAAU,EAAEC,SAAS,EAAEC,YAAY,QAAQ,UAAU;AAC9D,SAASC,UAAU,EAAEC,YAAY,QAAQ,cAAc;AACvD,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAO,MAAMC,aAAa,SAASJ,UAAU,CAAC;EAC1CK,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAGD,MAAM;EACzB;EACA;EACAE,IAAIA,CAACC,OAAO,EAAE;IACV;IACA,IAAIA,OAAO,CAACC,WAAW,IAAID,OAAO,CAACC,WAAW,CAACC,OAAO,EAAE;MACpD,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIhB,UAAU,CAAC,CAAC,CAAC;IAC3C;IACA,IAAI,CAACY,OAAO,CAACK,MAAM,EAAE;MACjB,OAAOF,OAAO,CAACC,MAAM,CAAC,IAAIE,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1D;IACA,IAAI,CAACN,OAAO,CAACO,GAAG,EAAE;MACd,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIE,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACvD;IACA,OAAO,IAAIH,OAAO,CAAC,CAACK,OAAO,EAAEJ,MAAM,KAAK;MACpC,MAAMK,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAChCD,GAAG,CAACE,IAAI,CAACX,OAAO,CAACK,MAAM,EAAEL,OAAO,CAACO,GAAG,EAAE,IAAI,CAAC;MAC3CE,GAAG,CAACG,eAAe,GAAGZ,OAAO,CAACY,eAAe,KAAKC,SAAS,GAAG,IAAI,GAAGb,OAAO,CAACY,eAAe;MAC5FH,GAAG,CAACK,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;MAC1D,IAAId,OAAO,CAACe,OAAO,KAAK,EAAE,EAAE;QACxBf,OAAO,CAACe,OAAO,GAAGF,SAAS;MAC/B;MACA,IAAIb,OAAO,CAACe,OAAO,EAAE;QACjB;QACA,IAAIrB,aAAa,CAACM,OAAO,CAACe,OAAO,CAAC,EAAE;UAChCN,GAAG,CAACK,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;QACpE,CAAC,MACI;UACDL,GAAG,CAACK,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;QACpE;MACJ;MACA,MAAME,OAAO,GAAGhB,OAAO,CAACgB,OAAO;MAC/B,IAAIA,OAAO,EAAE;QACTC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CACfG,OAAO,CAAEC,MAAM,IAAK;UACrBX,GAAG,CAACK,gBAAgB,CAACM,MAAM,EAAEJ,OAAO,CAACI,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC;MACN;MACA,IAAIpB,OAAO,CAACqB,YAAY,EAAE;QACtBZ,GAAG,CAACY,YAAY,GAAGrB,OAAO,CAACqB,YAAY;MAC3C;MACA,IAAIrB,OAAO,CAACC,WAAW,EAAE;QACrBD,OAAO,CAACC,WAAW,CAACqB,OAAO,GAAG,MAAM;UAChCb,GAAG,CAACc,KAAK,CAAC,CAAC;UACXnB,MAAM,CAAC,IAAIhB,UAAU,CAAC,CAAC,CAAC;QAC5B,CAAC;MACL;MACA,IAAIY,OAAO,CAACwB,OAAO,EAAE;QACjBf,GAAG,CAACe,OAAO,GAAGxB,OAAO,CAACwB,OAAO;MACjC;MACAf,GAAG,CAACgB,MAAM,GAAG,MAAM;QACf,IAAIzB,OAAO,CAACC,WAAW,EAAE;UACrBD,OAAO,CAACC,WAAW,CAACqB,OAAO,GAAG,IAAI;QACtC;QACA,IAAIb,GAAG,CAACiB,MAAM,IAAI,GAAG,IAAIjB,GAAG,CAACiB,MAAM,GAAG,GAAG,EAAE;UACvClB,OAAO,CAAC,IAAIhB,YAAY,CAACiB,GAAG,CAACiB,MAAM,EAAEjB,GAAG,CAACkB,UAAU,EAAElB,GAAG,CAACmB,QAAQ,IAAInB,GAAG,CAACoB,YAAY,CAAC,CAAC;QAC3F,CAAC,MACI;UACDzB,MAAM,CAAC,IAAIf,SAAS,CAACoB,GAAG,CAACmB,QAAQ,IAAInB,GAAG,CAACoB,YAAY,IAAIpB,GAAG,CAACkB,UAAU,EAAElB,GAAG,CAACiB,MAAM,CAAC,CAAC;QACzF;MACJ,CAAC;MACDjB,GAAG,CAACqB,OAAO,GAAG,MAAM;QAChB,IAAI,CAAChC,OAAO,CAACiC,GAAG,CAACtC,QAAQ,CAACuC,OAAO,EAAE,4BAA4BvB,GAAG,CAACiB,MAAM,KAAKjB,GAAG,CAACkB,UAAU,GAAG,CAAC;QAChGvB,MAAM,CAAC,IAAIf,SAAS,CAACoB,GAAG,CAACkB,UAAU,EAAElB,GAAG,CAACiB,MAAM,CAAC,CAAC;MACrD,CAAC;MACDjB,GAAG,CAACwB,SAAS,GAAG,MAAM;QAClB,IAAI,CAACnC,OAAO,CAACiC,GAAG,CAACtC,QAAQ,CAACuC,OAAO,EAAE,4BAA4B,CAAC;QAChE5B,MAAM,CAAC,IAAId,YAAY,CAAC,CAAC,CAAC;MAC9B,CAAC;MACDmB,GAAG,CAACV,IAAI,CAACC,OAAO,CAACe,OAAO,CAAC;IAC7B,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}