{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"gsw-FR\", [[\"vorm.\", \"nam.\"], u, [\"am Vormittag\", \"am Namittag\"]], [[\"vorm.\", \"nam.\"], u, [\"Vormittag\", \"Namittag\"]], [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"Su.\", \"Mä.\", \"Zi.\", \"Mi.\", \"Du.\", \"Fr.\", \"Sa.\"], [\"Sunntig\", \"<PERSON><PERSON><PERSON>ntig\", \"<PERSON>iischtig\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>ti<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\"], [\"<PERSON>.\", \"<PERSON><PERSON>.\", \"Zi.\", \"<PERSON>.\", \"<PERSON>.\", \"<PERSON>.\", \"Sa.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"N\", \"<PERSON>\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>r\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"Aug\", \"<PERSON>\", \"<PERSON>t\", \"<PERSON>\", \"<PERSON>z\"], [\"<PERSON>uar\", \"<PERSON>ruar\", \"<PERSON><PERSON>rz\", \"April\", \"<PERSON>\", \"<PERSON>i\", \"<PERSON>i\", \"Auguscht\", \"Sept<PERSON>mber\", \"Oktoober\", \"Novämber\", \"Dezämber\"]], u, [[\"v. Chr.\", \"n. Chr.\"], u, u], 1, [6, 0], [\"dd.MM.yy\", \"dd.MM.y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \"’\", \";\", \"%\", \"+\", \"−\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Euro\", {\n  \"ATS\": [\"öS\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}