{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\nexport var LogLevel;\n(function (LogLevel) {\n  /** Log level for very low severity diagnostic messages. */\n  LogLevel[LogLevel[\"Trace\"] = 0] = \"Trace\";\n  /** Log level for low severity diagnostic messages. */\n  LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\n  /** Log level for informational diagnostic messages. */\n  LogLevel[LogLevel[\"Information\"] = 2] = \"Information\";\n  /** Log level for diagnostic messages that indicate a non-fatal problem. */\n  LogLevel[LogLevel[\"Warning\"] = 3] = \"Warning\";\n  /** Log level for diagnostic messages that indicate a failure in the current operation. */\n  LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\n  /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\n  LogLevel[LogLevel[\"Critical\"] = 5] = \"Critical\";\n  /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\n  LogLevel[LogLevel[\"None\"] = 6] = \"None\";\n})(LogLevel || (LogLevel = {}));", "map": {"version": 3, "names": ["LogLevel"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/signalr/dist/esm/ILogger.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport var LogLevel;\r\n(function (LogLevel) {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Trace\"] = 0] = \"Trace\";\r\n    /** Log level for low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\r\n    /** Log level for informational diagnostic messages. */\r\n    LogLevel[LogLevel[\"Information\"] = 2] = \"Information\";\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    LogLevel[LogLevel[\"Warning\"] = 3] = \"Warning\";\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    LogLevel[LogLevel[\"Critical\"] = 5] = \"Critical\";\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    LogLevel[LogLevel[\"None\"] = 6] = \"None\";\r\n})(LogLevel || (LogLevel = {}));\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjB;EACAA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzC;EACAA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzC;EACAA,QAAQ,CAACA,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EACrD;EACAA,QAAQ,CAACA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC7C;EACAA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzC;EACAA,QAAQ,CAACA,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC/C;EACAA,QAAQ,CAACA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AAC3C,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}