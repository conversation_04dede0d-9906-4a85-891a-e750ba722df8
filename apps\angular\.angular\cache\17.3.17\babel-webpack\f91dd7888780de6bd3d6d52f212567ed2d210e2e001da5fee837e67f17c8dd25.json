{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"mas-TZ\", [[\"Ɛnkakɛnyá\", \"Ɛndámâ\"], u, u], u, [[\"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"1\"], [\"Jpi\", \"Jtt\", \"Jnn\", \"Jtn\", \"<PERSON>h\", \"<PERSON>ju\", \"<PERSON><PERSON>\"], [\"Jumap<PERSON>l<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Jumat<PERSON>ɔ\", \"Alaámisi\", \"Jumáa\", \"Jumamósi\"], [\"Jpi\", \"Jtt\", \"Jnn\", \"Jtn\", \"<PERSON>h\", \"<PERSON>ju\", \"<PERSON><PERSON>\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"<PERSON>\", \"Ar<PERSON>\", \"Ɔɛn\", \"<PERSON><PERSON>\", \"<PERSON>ép\", \"Rok\", \"Sás\", \"Bɔ́r\", \"Kús\", \"Gís\", \"Shʉ́\", \"Ntʉ́\"], [\"Oladalʉ́\", \"Arát\", \"Ɔɛnɨ́ɔɨŋɔk\", \"Olodoyíóríê inkókúâ\", \"Oloilépūnyīē inkókúâ\", \"Kújúɔrɔk\", \"Mórusásin\", \"Ɔlɔ́ɨ́bɔ́rárɛ\", \"Kúshîn\", \"Olgísan\", \"Pʉshʉ́ka\", \"Ntʉ́ŋʉ́s\"]], u, [[\"MY\", \"EY\"], u, [\"Meínō Yɛ́sʉ\", \"Eínō Yɛ́sʉ\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"TZS\", \"TSh\", \"Iropiyianí e Tanzania\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KES\": [\"Ksh\"],\n  \"TZS\": [\"TSh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/mas-TZ.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"mas-TZ\", [[\"Ɛnkakɛnyá\", \"Ɛndámâ\"], u, u], u, [[\"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"1\"], [\"Jpi\", \"Jtt\", \"Jnn\", \"Jtn\", \"<PERSON>h\", \"<PERSON>ju\", \"<PERSON><PERSON>\"], [\"Jumap<PERSON>l<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Jumat<PERSON>ɔ\", \"Alaámisi\", \"Jumáa\", \"Jumamósi\"], [\"Jpi\", \"Jtt\", \"Jnn\", \"Jtn\", \"<PERSON>h\", \"<PERSON>ju\", \"<PERSON><PERSON>\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"<PERSON>\", \"Ar<PERSON>\", \"Ɔɛn\", \"<PERSON><PERSON>\", \"<PERSON>ép\", \"Rok\", \"Sás\", \"Bɔ́r\", \"Kús\", \"Gís\", \"Shʉ́\", \"Ntʉ́\"], [\"Oladalʉ́\", \"Arát\", \"Ɔɛnɨ́ɔɨŋɔk\", \"Olodoyíóríê inkókúâ\", \"Oloilépūnyīē inkókúâ\", \"Kújúɔrɔk\", \"Mórusásin\", \"Ɔlɔ́ɨ́bɔ́rárɛ\", \"Kúshîn\", \"Olgísan\", \"Pʉshʉ́ka\", \"Ntʉ́ŋʉ́s\"]], u, [[\"MY\", \"EY\"], u, [\"Meínō Yɛ́sʉ\", \"Eínō Yɛ́sʉ\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"TZS\", \"TSh\", \"Iropiyianí e Tanzania\", { \"JPY\": [\"JP¥\", \"¥\"], \"KES\": [\"Ksh\"], \"TZS\": [\"TSh\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}