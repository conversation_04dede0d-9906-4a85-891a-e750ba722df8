{"ast": null, "code": "import { environment } from \"@environments/environment\";\nconst htmlSpinner = `      \n<div class=\"spinner\"></div>\n<style>\n  \n  .spinner {\n    border: 6px solid rgba(0, 0, 0, 0.1);\n    border-top: 6px solid #333;\n    border-radius: 50%;\n    width: 6.5em;\n    height: 6.5em;\n    animation: spin 1s linear infinite;\n    position:absolute;\n    bottom: 9.5em;\n    left: 52em;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n</style>\n`;\nexport const compositeQuestionJson = {\n  \"title\": \"Economic Substance Declaration\",\n  \"logoPosition\": \"right\",\n  \"pages\": [{\n    \"name\": \"financialPeriod\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"financialPeriodPanel\",\n      \"elements\": [{\n        \"type\": \"boolean\",\n        \"name\": \"financialPeriodChange\",\n        \"title\": \"2a Has an application been made and confirmed with Minister of Finance to change your financial period?\",\n        \"isRequired\": true,\n        \"width\": \"60em\",\n        \"minWidth\": \"60em\",\n        \"maxWidth\": \"60em\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"financialPeriodStartDate\",\n        \"title\": \"2b Financial Period Start Date\",\n        \"isRequired\": true,\n        \"inputType\": \"date\",\n        \"width\": \"22em\",\n        \"minWidth\": \"22em\",\n        \"maxWidth\": \"22em\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"financialPeriodEndDate\",\n        \"title\": \"2c Financial Period End Date\",\n        \"isRequired\": true,\n        \"inputType\": \"date\",\n        \"width\": \"22em\",\n        \"minWidth\": \"22em\",\n        \"maxWidth\": \"22em\",\n        \"titleLocation\": \"top\",\n        \"startWithNewLine\": false\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"subjectToReclassification\",\n        \"visibleIf\": \"{financialPeriodEndDate} >= '2022-01-01' and {financialPeriodEndDate} <= '2023-12-31'\",\n        \"title\": \"2d Is the entity subject to reclassification of a non-included passive holding entity to a pure equity holding (PEH) entity under the Act (CESRA 2023)?\",\n        \"requiredIf\": \"{financialPeriodEndDate} >= '2022-01-01' and {financialPeriodEndDate} <= '2023-12-31'\",\n        \"isRequired\": true,\n        \"titleLocation\": \"top\"\n      }],\n      \"questionTitleLocation\": \"hidden\"\n    }],\n    \"title\": \"1.2.1 FINANCIAL PERIOD\"\n  }, {\n    \"name\": \"entityDetails\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"entityDetailsPanel\",\n      \"elements\": [{\n        \"type\": \"text\",\n        \"name\": \"entityDetailsTIN\",\n        \"title\": \"3a Entity Tax Payer Identification Number (TIN), if applicable.\",\n        \"maxLength\": 100,\n        \"width\": \"40em\",\n        \"minWidth\": \"40em\",\n        \"maxWidth\": \"40em\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"entityDetailsBusinessSameAsRegisteredAddress\",\n        \"title\": \"3b Physical Business Address is same as registered address?\",\n        \"isRequired\": true,\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"entityDetailsEnterDifferntBusinessAddress\",\n        \"visibleIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} notempty\",\n        \"title\": \"3b.i Physical Business Address\",\n        \"enableIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n        \"requiredIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n        \"titleLocation\": \"top\",\n        \"columns\": [{\n          \"name\": \"addressLine1\",\n          \"title\": \"Address Line 1\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"isRequired\": true,\n          \"enableIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"maxLength\": 100,\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"addressLine2\",\n          \"title\": \"Address Line 2\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"enableIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"maxLength\": 100,\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"country\",\n          \"title\": \"Country\",\n          \"cellType\": \"dropdown\",\n          \"isRequired\": true,\n          \"enableIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n          \"choicesByUrl\": {\n            \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n            \"path\": \"\",\n            \"valueName\": \"id\",\n            \"titleName\": \"name\"\n          },\n          \"titleLocation\": \"top\"\n        }],\n        \"rowCount\": 0\n      }],\n      \"questionTitleLocation\": \"hidden\"\n    }],\n    \"title\": \"ENTITY DETAILS\"\n  }, {\n    \"name\": \"relevantActivity\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"relevantActivityPanel\",\n      \"elements\": [{\n        \"type\": \"tagbox\",\n        \"name\": \"relevantActRelevantActivities\",\n        \"title\": \"4a Relevant activity that was carried on during Financial Period.\",\n        \"isRequired\": true,\n        \"choicesByUrl\": {\n          \"url\": environment['apis']['default']['url'] + \"api/lookup-service/relevantActivity?Sorting=name\",\n          \"path\": \"items\",\n          \"valueName\": \"name\",\n          \"titleName\": \"name\"\n        },\n        \"showNoneItem\": true,\n        \"allowClear\": false,\n        \"titleLocation\": \"top\"\n      }],\n      \"questionTitleLocation\": \"hidden\"\n    }],\n    \"title\": \"1.2.3 RELEVANT ACTIVITY\"\n  }, {\n    \"name\": \"taxResidency\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"taxResidencyPanel\",\n      \"elements\": [{\n        \"type\": \"boolean\",\n        \"name\": \"taxResidency100PercentBahamian\",\n        \"title\": \"5a Are you a 100% Bahamian/resident-owned and Core Income Generated Activity (CIGA) conducted in the Bahamas?\",\n        \"isRequired\": true,\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"taxResidencyIsInvestmentFund\",\n        \"visibleIf\": \"{taxResidency100PercentBahamian} = false\",\n        \"title\": \"5b Are you an Investment Fund according to the Investment Funds Act, 2019 (No. 2 of 2019)?\",\n        \"requiredIf\": \"{taxResidency100PercentBahamian} = false\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"entityDetailsAnnualIncome\",\n        \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false\",\n        \"requiredIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false\",\n        \"title\": \"5c Gross total annual income of the entity\",\n        \"hideNumber\": true,\n        \"width\": \"45em\",\n        \"minWidth\": \"45em\",\n        \"maxWidth\": \"45em\",\n        \"titleLocation\": \"top\",\n        \"columns\": [{\n          \"name\": \"currency\",\n          \"title\": \"Currency\",\n          \"cellType\": \"text\",\n          \"readOnly\": true,\n          \"defaultValue\": \"USD\",\n          \"width\": \"5em\",\n          \"minWidth\": \"5em\",\n          \"maxWidth\": \"5em\",\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"currencyValue\",\n          \"title\": \"Annual Income\",\n          \"cellType\": \"text\",\n          \"isRequired\": true,\n          \"inputMask\": \"decimal\",\n          \"validators\": [{\n            \"type\": \"numeric\",\n            \"minValue\": -1\n          }],\n          \"options\": {\n            \"allowMinus\": false,\n            \"digits\": 2,\n            \"unmaskAsNumber\": true,\n            \"prefix\": \"\",\n            \"suffix\": \"\",\n            \"digitsOptional\": true,\n            \"stripLeadingZeroes\": false\n          },\n          \"defaultValue\": null,\n          \"width\": \"35em\",\n          \"minWidth\": \"35em\",\n          \"maxWidth\": \"35em\",\n          \"titleLocation\": \"top\"\n        }],\n        \"allowAddRows\": false,\n        \"allowRemoveRows\": false,\n        \"rowCount\": 1,\n        \"maxRowCount\": 1\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"partOfMNEGroup\",\n        \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false\",\n        \"title\": \"5d Are you a part of the MNE Group?\",\n        \"requiredIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"entityDetailsNameMNE\",\n        \"visibleIf\": \"{partOfMNEGroup} = true\",\n        \"requiredIf\": \"{partOfMNEGroup} = true\",\n        \"title\": \"5d.i Name of the Multinational Enterprise (MNE) group\",\n        \"maxLength\": 100,\n        \"width\": \"40em\",\n        \"minWidth\": \"40em\",\n        \"maxWidth\": \"40em\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"taxResidencyOutsideBahamas\",\n        \"visibleIf\": \"{taxResidencyIsInvestmentFund} = false\",\n        \"title\": \"5e Does the entity intend to make a claim of tax residency outside of the Bahamas?\",\n        \"requiredIf\": \"{taxResidencyIsInvestmentFund} = false\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"dropdown\",\n        \"name\": \"taxResidencyJurisdictionEntityIsTaxResident\",\n        \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"title\": \"5e.i Jurisdiction in which the entity is tax resident.\",\n        \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"choicesByUrl\": {\n          \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n          \"path\": \"\",\n          \"valueName\": \"id\",\n          \"titleName\": \"name\"\n        },\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"taxResidencyTaxpayerIDNumber\",\n        \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"title\": \"5e.ii Taxpayer Identification number (TIN) or other identification reference number.\",\n        \"isRequired\": true,\n        \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"maxLength\": 100,\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"file\",\n        \"name\": \"taxResidencyEvidenceOfTaxResidency\",\n        \"showPreview\": false,\n        \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"title\": \"5e.iii Upload evidence of Tax Residency in another jurisdiction. (Image or PDF files only)\",\n        \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({taxResidencyEvidenceOfTaxResidency})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"titleLocation\": \"top\",\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerFortaxResidencyEvidenceOfTaxResidency\",\n        visibleIf: \"{taxResidencyEvidenceOfTaxResidencySpinner}\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"taxResidencyHasParent\",\n        \"title\": \"5f Does Entity have an ultimate parent entity?\",\n        \"visibleIf\": \"{taxResidencyIsInvestmentFund} = false\",\n        \"isRequired\": true,\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"taxResidencyUltimateParentEntityInfo\",\n        \"visibleIf\": \"{taxResidencyHasParent} = true\",\n        \"title\": \"Ultimate Parent Entity\",\n        \"requiredIf\": \"{taxResidencyHasParent} = true\",\n        \"columns\": [{\n          \"name\": \"name\",\n          \"title\": \"Name\",\n          \"cellType\": \"comment\",\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"maxLength\": 100,\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"alternativeName\",\n          \"title\": \"Alternative Name\",\n          \"cellType\": \"comment\",\n          \"maxLength\": 100,\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"incorporationNumber\",\n          \"title\": \"Incorporation Number or its equivalent\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"maxLength\": 20,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 20\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"identificationNumber\",\n          \"title\": \"Identification Number\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"maxLength\": 100,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"jurisdiction\",\n          \"title\": \"Jurisdiction of Formation\",\n          \"cellType\": \"dropdown\",\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"choicesByUrl\": {\n            \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n            \"path\": \"\",\n            \"valueName\": \"id\",\n            \"titleName\": \"name\"\n          },\n          \"titleLocation\": \"top\"\n        }],\n        \"rowCount\": 0\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"hasImmediateParent\",\n        \"title\": \"5g Does Entity have an immediate parent entity?\",\n        \"visibleIf\": \"{taxResidencyIsInvestmentFund} = false\",\n        \"isRequired\": true,\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"taxResidencyImmediateParentEntity\",\n        \"visibleIf\": \"{hasImmediateParent} = true\",\n        \"title\": \"Immediate Parent Entity\",\n        \"requiredIf\": \"{hasImmediateParent} = true\",\n        \"titleLocation\": \"top\",\n        \"columns\": [{\n          \"name\": \"name\",\n          \"title\": \"Name\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"maxLength\": 100,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"alternativeName\",\n          \"title\": \"Alternative Name\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"maxLength\": 100,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"incorporationNumber\",\n          \"title\": \"Incorporation Number or its equivalent\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"maxLength\": 20,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 20\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"identificationNumber\",\n          \"title\": \"Identification Number\",\n          \"cellType\": \"comment\",\n          \"autoGrow\": true,\n          \"allowResize\": false,\n          \"rows\": 1,\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"maxLength\": 100,\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }],\n          \"titleLocation\": \"top\"\n        }, {\n          \"name\": \"jurisdiction\",\n          \"title\": \"Jurisdiction of Formation\",\n          \"cellType\": \"dropdown\",\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"choicesByUrl\": {\n            \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n            \"path\": \"\",\n            \"valueName\": \"id\",\n            \"titleName\": \"name\"\n          },\n          \"titleLocation\": \"top\"\n        }],\n        \"rowCount\": 0\n      }],\n      \"questionTitleLocation\": \"hidden\"\n    }],\n    \"visibleIf\": \"{relevantActRelevantActivities} <> ['none']\",\n    \"title\": \"1.2.4 TAX RESIDENCY\"\n  }, {\n    \"name\": \"activityDetail\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"activityDetailPanel\",\n      \"elements\": [{\n        \"type\": \"intellectualpropertybusiness\",\n        \"name\": \"intellectualPropertyBusiness\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Intellectual property business']\",\n        \"title\": \"Intellectual Property Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"holdingbusinessquestions\",\n        \"name\": \"holdingBusinessQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Holding business']\",\n        \"title\": \"Holding Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"bankingotherrelevantactivities\",\n        \"name\": \"bankingQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Banking business']\",\n        \"title\": \"Banking Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"insuranceotherrelevantactivities\",\n        \"name\": \"insuranceQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Insurance business']\",\n        \"title\": \"Insurance Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"fundmanagmentotherrelevantactivities\",\n        \"name\": \"fundManagmentQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Fund management business']\",\n        \"title\": \"Fund Management Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"financeotherrelevantactivities\",\n        \"name\": \"financeQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Finance and leasing business']\",\n        \"title\": \"Finance and Leasing Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"headquartersotherrelevantactivities\",\n        \"name\": \"headquartersQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Headquarters business']\",\n        \"title\": \"Headquarters Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"shippingotherrelevantactivities\",\n        \"name\": \"shippingQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Shipping business']\",\n        \"title\": \"Shipping Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"distributionotherrelevantactivities\",\n        \"name\": \"distributionQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Distribution and service centre business']\",\n        \"title\": \"Distribution and Service Centre Business\",\n        \"hideNumber\": true,\n        \"state\": \"expanded\",\n        \"titleLocation\": \"top\"\n      }],\n      \"questionTitleLocation\": \"hidden\"\n    }],\n    \"title\": \"ACTIVITY DETAILS\",\n    \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false and {taxResidencyOutsideBahamas} = false and {relevantActRelevantActivities} <> ['none']\"\n  }, {\n    \"name\": \"Supporting_Details\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"Supporting_DetailsPanel\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"autoGrow\": true,\n        \"allowResize\": false,\n        \"rows\": 4,\n        \"name\": \"comments\",\n        \"title\": \"7a(i) Provide comments supporting your Economic Substance Declaration.\",\n        \"maxLength\": 255,\n        \"titleLocation\": \"top\"\n      }, {\n        \"type\": \"comment\",\n        \"name\": \"noActivityComments\",\n        \"title\": \"7a(ii) Provide comments supporting your Economic Substance Declaration: if your entity is carrying no relevant activity, explain what activity is being conducted by the entity.\",\n        \"maxLength\": 255,\n        \"titleLocation\": \"top\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['none']\",\n        \"requiredIf\": \"{relevantActRelevantActivities} anyof ['none']\"\n      }, {\n        \"type\": \"file\",\n        \"name\": \"supportingAttachment\",\n        \"showPreview\": false,\n        \"title\": \"Supporting Attachments (Image or PDF files only)\",\n        \"allowMultiple\": true,\n        \"maxSize\": 10485760,\n        \"validators\": [{\n          \"type\": \"expression\",\n          \"expression\": \"isAcceptedFileType({supportingAttachment})\",\n          \"text\": \"You can select .png, .jpg, .bmp, .jpeg, .pdf files\"\n        }],\n        \"titleLocation\": \"top\",\n        \"storeDataAsText\": false,\n        \"waitForUpload\": true,\n        \"acceptedTypes\": \".pdf,.jpg,.jpeg,.png,.bmp\"\n      }, {\n        type: \"html\",\n        html: htmlSpinner,\n        name: \"spinnerForsupportingAttachment\",\n        visibleIf: \"{supportingAttachmentSpinner}\"\n      }],\n      \"questionTitleLocation\": \"hidden\"\n    }],\n    \"title\": \"1.1.6 SUPPORTING DETAILS\"\n  }],\n  \"triggers\": [{\n    \"type\": \"setvalue\",\n    \"expression\": \"{financialPeriodChange} = 'true'\",\n    \"setToName\": \"financialPeriodStartDate\"\n  }, {\n    \"type\": \"setvalue\",\n    \"expression\": \"{financialPeriodChange} = 'true'\",\n    \"setToName\": \"financialPeriodEndDate\"\n  }, {\n    \"type\": \"setvalue\",\n    \"expression\": \"{entityDetailsBusinessSameAsRegisteredAddress} = 'false'\",\n    \"setToName\": \"entityDetailsEnterDifferntBusinessAddress\"\n  }],\n  \"widthMode\": \"responsive\",\n  \"showTitle\": false,\n  \"clearInvisibleValues\": \"onHiddenContainer\",\n  \"showPreviewBeforeComplete\": \"showAllQuestions\",\n  \"showQuestionNumbers\": 'false'\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}