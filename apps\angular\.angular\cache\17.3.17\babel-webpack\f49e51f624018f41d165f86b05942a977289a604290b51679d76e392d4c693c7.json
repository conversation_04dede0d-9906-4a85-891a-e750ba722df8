{"ast": null, "code": "import { mapEnumToOptions } from '@abp/ng.core';\nexport var RedFlagType = /*#__PURE__*/function (RedFlagType) {\n  RedFlagType[RedFlagType[\"WithoutParam\"] = 1] = \"WithoutParam\";\n  RedFlagType[RedFlagType[\"WithParam\"] = 2] = \"WithParam\";\n  return RedFlagType;\n}(RedFlagType || {});\nexport const redFlagTypeOptions = mapEnumToOptions(RedFlagType);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}