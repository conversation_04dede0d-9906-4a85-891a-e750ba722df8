{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = startOfUTCISOWeek;\nvar _index = _interopRequireDefault(require(\"../../toDate/index.js\"));\nvar _index2 = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\nfunction startOfUTCISOWeek(dirtyDate) {\n  (0, _index2.default)(1, arguments);\n  var weekStartsOn = 1;\n  var date = (0, _index.default)(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}\nmodule.exports = exports.default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "startOfUTCISOWeek", "_index", "_index2", "dirtyDate", "arguments", "weekStartsOn", "date", "day", "getUTCDay", "diff", "setUTCDate", "getUTCDate", "setUTCHours", "module"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/_lib/startOfUTCISOWeek/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = startOfUTCISOWeek;\nvar _index = _interopRequireDefault(require(\"../../toDate/index.js\"));\nvar _index2 = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\nfunction startOfUTCISOWeek(dirtyDate) {\n  (0, _index2.default)(1, arguments);\n  var weekStartsOn = 1;\n  var date = (0, _index.default)(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,iBAAiB;AACnC,IAAIC,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACrE,IAAIQ,OAAO,GAAGT,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACzE,SAASM,iBAAiBA,CAACG,SAAS,EAAE;EACpC,CAAC,CAAC,EAAED,OAAO,CAACP,OAAO,EAAE,CAAC,EAAES,SAAS,CAAC;EAClC,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,CAACN,OAAO,EAAEQ,SAAS,CAAC;EACzC,IAAII,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;EAC1B,IAAIC,IAAI,GAAG,CAACF,GAAG,GAAGF,YAAY,GAAG,CAAC,GAAG,CAAC,IAAIE,GAAG,GAAGF,YAAY;EAC5DC,IAAI,CAACI,UAAU,CAACJ,IAAI,CAACK,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzCH,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B,OAAON,IAAI;AACb;AACAO,MAAM,CAACf,OAAO,GAAGA,OAAO,CAACH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}