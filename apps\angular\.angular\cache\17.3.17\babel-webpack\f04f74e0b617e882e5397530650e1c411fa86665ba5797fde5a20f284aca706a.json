{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\n/** Rendering \"# OF TAX RESIDENTS OUTSIDE OF BAHAMAS\" widget in\n * \"Statistics\" Tab in CA Dashboard page.\n * TODO. No need on Oct\n *  */\nexport let TaxResidentsOutsideBahamasComponent = /*#__PURE__*/(() => {\n  class TaxResidentsOutsideBahamasComponent extends AppComponentBase {\n    constructor(injector) {\n      super(injector);\n    }\n    ngOnChanges(changes) {\n      if (changes.statsSummaryData && this.statsSummaryData) {\n        console.log(this.statsSummaryData);\n      }\n    }\n    ngOnInit() {}\n    static {\n      this.ɵfac = function TaxResidentsOutsideBahamasComponent_Factory(t) {\n        return new (t || TaxResidentsOutsideBahamasComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TaxResidentsOutsideBahamasComponent,\n        selectors: [[\"app-tax-resident-outside-bahamas\"]],\n        inputs: {\n          statsSummaryData: \"statsSummaryData\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n        decls: 14,\n        vars: 2,\n        consts: [[1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"]],\n        template: function TaxResidentsOutsideBahamasComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n            i0.ɵɵtext(2, \"Tax Residents Outside Bahamas:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\")(4, \"div\", 1)(5, \"div\", 2);\n            i0.ɵɵtext(6, \"# Of assessments not started\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 2);\n            i0.ɵɵtext(8, \"# Of assessments closed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 1)(10, \"div\", 3);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 3);\n            i0.ɵɵtext(13);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData == null ? null : ctx.statsSummaryData.numOfAssessmentNotStartedOutsideBahamas);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData == null ? null : ctx.statsSummaryData.numOfAssessmentClosedBahamas);\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return TaxResidentsOutsideBahamasComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}