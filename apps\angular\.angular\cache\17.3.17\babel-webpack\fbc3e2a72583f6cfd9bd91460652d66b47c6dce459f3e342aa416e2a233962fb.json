{"ast": null, "code": "export * from './compliance-emails.service';\nexport * from './models';", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\saas-service\\lib\\proxy\\bdo\\ess\\saas-service\\compliance-emails\\index.ts"], "sourcesContent": ["export * from './compliance-emails.service';\r\nexport * from './models';\r\n"], "mappings": "AAAA,cAAc,6BAA6B;AAC3C,cAAc,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}