{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PhoneNumberService = /*#__PURE__*/(() => {\n  class PhoneNumberService {\n    constructor() {\n      this.phoneNumber = '';\n      this.modelVisible = false;\n    }\n    setPhoneNumber(phoneNumber) {\n      this.phoneNumber = phoneNumber;\n    }\n    getPhoneNumber() {\n      return this.phoneNumber;\n    }\n    setPhoneNumberObejct(phoneNumberObject) {\n      this.phoneNumberObject = phoneNumberObject;\n    }\n    getPhoneNumberObject() {\n      return this.phoneNumberObject;\n    }\n    getModelVisible() {\n      return this.modelVisible;\n    }\n    setModelVisible(value) {\n      this.modelVisible = value;\n      if (this.modelVisible === false) {\n        this.phoneNumber = '';\n        this.phoneNumberObject = null;\n      }\n    }\n    static {\n      this.ɵfac = function PhoneNumberService_Factory(t) {\n        return new (t || PhoneNumberService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PhoneNumberService,\n        factory: PhoneNumberService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PhoneNumberService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}