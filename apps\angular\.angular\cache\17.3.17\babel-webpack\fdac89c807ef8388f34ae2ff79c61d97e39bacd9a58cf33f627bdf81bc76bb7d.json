{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"or\", [[\"ପୂ\", \"ଅ\"], [\"AM\", \"PM\"], u], [[\"AM\", \"ଅପରାହ୍ନ\"], [\"ପୂର୍ବାହ୍ନ\", \"ଅପରାହ୍ନ\"], u], [[\"ର\", \"ସୋ\", \"ମ\", \"ବୁ\", \"ଗୁ\", \"ଶୁ\", \"ଶ\"], [\"ରବି\", \"ସୋମ\", \"ମଙ୍ଗଳ\", \"ବୁଧ\", \"ଗୁରୁ\", \"ଶୁକ୍ର\", \"ଶନି\"], [\"ରବିବାର\", \"ସୋମବାର\", \"ମଙ୍ଗଳବାର\", \"ବୁଧବାର\", \"ଗୁରୁବାର\", \"ଶୁକ୍ରବାର\", \"ଶନିବାର\"], [\"ରବି\", \"ସୋମ\", \"ମଙ୍ଗଳ\", \"ବୁଧ\", \"ଗୁରୁ\", \"ଶୁକ୍ର\", \"ଶନି\"]], u, [[\"ଜା\", \"ଫେ\", \"ମା\", \"ଅ\", \"ମଇ\", \"ଜୁ\", \"ଜୁ\", \"ଅ\", \"ସେ\", \"ଅ\", \"ନ\", \"ଡି\"], [\"ଜାନୁଆରୀ\", \"ଫେବୃଆରୀ\", \"ମାର୍ଚ୍ଚ\", \"ଅପ୍ରେଲ\", \"ମଇ\", \"ଜୁନ\", \"ଜୁଲାଇ\", \"ଅଗଷ୍ଟ\", \"ସେପ୍ଟେମ୍ବର\", \"ଅକ୍ଟୋବର\", \"ନଭେମ୍ବର\", \"ଡିସେମ୍ବର\"], u], u, [[\"BC\", \"AD\"], u, [\"ଖ୍ରୀଷ୍ଟପୂର୍ବ\", \"ଖ୍ରୀଷ୍ଟାବ୍ଦ\"]], 0, [0, 0], [\"M/d/yy\", \"MMM d, y\", \"MMMM d, y\", \"EEEE, MMMM d, y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{0} ଠାରେ {1}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"INR\", \"₹\", \"ଭାରତୀୟ ଟଙ୍କା\", {\n  \"BYN\": [u, \"р.\"],\n  \"PHP\": [u, \"₱\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoib3IuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9vci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwwQ0FBMEM7QUFDMUMsTUFBTSxDQUFDLEdBQUcsU0FBUyxDQUFDO0FBRXBCLFNBQVMsTUFBTSxDQUFDLEdBQVc7SUFDM0IsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBRWQsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsT0FBTyxDQUFDLENBQUM7QUFDVCxDQUFDO0FBRUQsZUFBZSxDQUFDLElBQUksRUFBQyxDQUFDLENBQUMsSUFBSSxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUMsU0FBUyxDQUFDLEVBQUMsQ0FBQyxXQUFXLEVBQUMsU0FBUyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxJQUFJLEVBQUMsR0FBRyxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sRUFBQyxPQUFPLEVBQUMsS0FBSyxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsUUFBUSxFQUFDLFVBQVUsRUFBQyxRQUFRLEVBQUMsU0FBUyxFQUFDLFVBQVUsRUFBQyxRQUFRLENBQUMsRUFBQyxDQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLEVBQUMsT0FBTyxFQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxHQUFHLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsR0FBRyxFQUFDLElBQUksRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsU0FBUyxFQUFDLFNBQVMsRUFBQyxTQUFTLEVBQUMsUUFBUSxFQUFDLElBQUksRUFBQyxLQUFLLEVBQUMsT0FBTyxFQUFDLE9BQU8sRUFBQyxZQUFZLEVBQUMsU0FBUyxFQUFDLFNBQVMsRUFBQyxVQUFVLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLElBQUksRUFBQyxJQUFJLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxjQUFjLEVBQUMsYUFBYSxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsVUFBVSxFQUFDLFdBQVcsRUFBQyxpQkFBaUIsQ0FBQyxFQUFDLENBQUMsUUFBUSxFQUFDLFdBQVcsRUFBQyxhQUFhLEVBQUMsZ0JBQWdCLENBQUMsRUFBQyxDQUFDLFVBQVUsRUFBQyxDQUFDLEVBQUMsY0FBYyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLGNBQWMsRUFBQyxRQUFRLEVBQUMsV0FBVyxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxHQUFHLEVBQUMsY0FBYyxFQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLElBQUksQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxHQUFHLENBQUMsRUFBQyxFQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUSElTIENPREUgSVMgR0VORVJBVEVEIC0gRE8gTk9UIE1PRElGWS5cbmNvbnN0IHUgPSB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIHBsdXJhbCh2YWw6IG51bWJlcik6IG51bWJlciB7XG5jb25zdCBuID0gdmFsO1xuXG5pZiAobiA9PT0gMSlcbiAgICByZXR1cm4gMTtcbnJldHVybiA1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBbXCJvclwiLFtbXCLgrKrgrYJcIixcIuCshVwiXSxbXCJBTVwiLFwiUE1cIl0sdV0sW1tcIkFNXCIsXCLgrIXgrKrgrLDgrL7grLngrY3grKhcIl0sW1wi4Kyq4K2C4Kyw4K2N4Kys4Ky+4Ky54K2N4KyoXCIsXCLgrIXgrKrgrLDgrL7grLngrY3grKhcIl0sdV0sW1tcIuCssFwiLFwi4Ky44K2LXCIsXCLgrK5cIixcIuCsrOCtgVwiLFwi4KyX4K2BXCIsXCLgrLbgrYFcIixcIuCstlwiXSxbXCLgrLDgrKzgrL9cIixcIuCsuOCti+CsrlwiLFwi4Kyu4KyZ4K2N4KyX4KyzXCIsXCLgrKzgrYHgrKdcIixcIuCsl+CtgeCssOCtgVwiLFwi4Ky24K2B4KyV4K2N4KywXCIsXCLgrLbgrKjgrL9cIl0sW1wi4Kyw4Kys4Ky/4Kys4Ky+4KywXCIsXCLgrLjgrYvgrK7grKzgrL7grLBcIixcIuCsruCsmeCtjeCsl+Css+CsrOCsvuCssFwiLFwi4Kys4K2B4Kyn4Kys4Ky+4KywXCIsXCLgrJfgrYHgrLDgrYHgrKzgrL7grLBcIixcIuCstuCtgeCsleCtjeCssOCsrOCsvuCssFwiLFwi4Ky24Kyo4Ky/4Kys4Ky+4KywXCJdLFtcIuCssOCsrOCsv1wiLFwi4Ky44K2L4KyuXCIsXCLgrK7grJngrY3grJfgrLNcIixcIuCsrOCtgeCsp1wiLFwi4KyX4K2B4Kyw4K2BXCIsXCLgrLbgrYHgrJXgrY3grLBcIixcIuCstuCsqOCsv1wiXV0sdSxbW1wi4Kyc4Ky+XCIsXCLgrKvgrYdcIixcIuCsruCsvlwiLFwi4KyFXCIsXCLgrK7grIdcIixcIuCsnOCtgVwiLFwi4Kyc4K2BXCIsXCLgrIVcIixcIuCsuOCth1wiLFwi4KyFXCIsXCLgrKhcIixcIuCsoeCsv1wiXSxbXCLgrJzgrL7grKjgrYHgrIbgrLDgrYBcIixcIuCsq+Cth+CsrOCtg+CshuCssOCtgFwiLFwi4Kyu4Ky+4Kyw4K2N4Kya4K2N4KyaXCIsXCLgrIXgrKrgrY3grLDgrYfgrLJcIixcIuCsruCsh1wiLFwi4Kyc4K2B4KyoXCIsXCLgrJzgrYHgrLLgrL7grIdcIixcIuCsheCsl+Cst+CtjeCsn1wiLFwi4Ky44K2H4Kyq4K2N4Kyf4K2H4Kyu4K2N4Kys4KywXCIsXCLgrIXgrJXgrY3grJ/grYvgrKzgrLBcIixcIuCsqOCsreCth+CsruCtjeCsrOCssFwiLFwi4Kyh4Ky/4Ky44K2H4Kyu4K2N4Kys4KywXCJdLHVdLHUsW1tcIkJDXCIsXCJBRFwiXSx1LFtcIuCsluCtjeCssOCtgOCst+CtjeCsn+CsquCtguCssOCtjeCsrFwiLFwi4KyW4K2N4Kyw4K2A4Ky34K2N4Kyf4Ky+4Kys4K2N4KymXCJdXSwwLFswLDBdLFtcIk0vZC95eVwiLFwiTU1NIGQsIHlcIixcIk1NTU0gZCwgeVwiLFwiRUVFRSwgTU1NTSBkLCB5XCJdLFtcImg6bW0gYVwiLFwiaDptbTpzcyBhXCIsXCJoOm1tOnNzIGEgelwiLFwiaDptbTpzcyBhIHp6enpcIl0sW1wiezF9LCB7MH1cIix1LFwiezB9IOCsoOCsvuCssOCthyB7MX1cIix1XSxbXCIuXCIsXCIsXCIsXCI7XCIsXCIlXCIsXCIrXCIsXCItXCIsXCJFXCIsXCLDl1wiLFwi4oCwXCIsXCLiiJ5cIixcIk5hTlwiLFwiOlwiXSxbXCIjLCMjLCMjMC4jIyNcIixcIiMsIyMwJVwiLFwiwqQjLCMjMC4wMFwiLFwiI0UwXCJdLFwiSU5SXCIsXCLigrlcIixcIuCsreCsvuCssOCspOCtgOCtnyDgrJ/grJngrY3grJXgrL5cIix7XCJCWU5cIjpbdSxcItGALlwiXSxcIlBIUFwiOlt1LFwi4oKxXCJdfSxcImx0clwiLCBwbHVyYWxdO1xuIl19", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}