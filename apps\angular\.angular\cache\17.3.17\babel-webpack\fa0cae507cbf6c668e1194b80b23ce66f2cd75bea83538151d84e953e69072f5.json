{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"ky\", [[\"тң\", \"тк\"], u, [\"таңкы\", \"түштөн кийинки\"]], u, [[\"Ж\", \"Д\", \"Ш\", \"Ш\", \"Б\", \"Ж\", \"И\"], [\"жек.\", \"дүй.\", \"шейш.\", \"шарш.\", \"бейш.\", \"жума\", \"ишм.\"], [\"жекшемби\", \"дүйшөмбү\", \"шейшемби\", \"шаршемби\", \"бейшемби\", \"жума\", \"ишемби\"], [\"жш.\", \"дш.\", \"шш.\", \"шр.\", \"бш.\", \"жм.\", \"иш.\"]], u, [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"янв.\", \"фев.\", \"мар.\", \"апр.\", \"май\", \"июн.\", \"июл.\", \"авг.\", \"сен.\", \"окт.\", \"ноя.\", \"дек.\"], [\"январь\", \"февраль\", \"март\", \"апрель\", \"май\", \"июнь\", \"июль\", \"август\", \"сентябрь\", \"октябрь\", \"ноябрь\", \"декабрь\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"Янв\", \"Фев\", \"Мар\", \"Апр\", \"Май\", \"Июн\", \"Июл\", \"Авг\", \"Сен\", \"Окт\", \"Ноя\", \"Дек\"], [\"Январь\", \"Февраль\", \"Март\", \"Апрель\", \"Май\", \"Июнь\", \"Июль\", \"Август\", \"Сентябрь\", \"Октябрь\", \"Ноябрь\", \"Декабрь\"]], [[\"б.з.ч.\", \"б.з.\"], u, [\"биздин заманга чейин\", \"биздин заман\"]], 1, [6, 0], [\"d/M/yy\", \"y-'ж'., d-MMM\", \"y-'ж'., d-MMMM\", \"y-'ж'., d-MMMM, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"сан эмес\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"KGS\", \"сом\", \"Кыргызстан сому\", {\n  \"AUD\": [u, \"$\"],\n  \"BMD\": [u, \"BD$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BSD\": [u, \"B$\"],\n  \"BYN\": [u, \"р.\"],\n  \"BZD\": [u, \"BZ$\"],\n  \"CAD\": [u, \"C$\"],\n  \"DOP\": [u, \"RD$\"],\n  \"EGP\": [u, \"LE\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"HK$\"],\n  \"HRK\": [u, \"Kn\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JMD\": [u, \"J$\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KGS\": [\"сом\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TTD\": [u, \"TT$\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"XCD\": [u, \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ky.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"ky\", [[\"тң\", \"тк\"], u, [\"таңкы\", \"түштөн кийинки\"]], u, [[\"Ж\", \"Д\", \"Ш\", \"Ш\", \"Б\", \"Ж\", \"И\"], [\"жек.\", \"дүй.\", \"шейш.\", \"шарш.\", \"бейш.\", \"жума\", \"ишм.\"], [\"жекшемби\", \"дүйшөмбү\", \"шейшемби\", \"шаршемби\", \"бейшемби\", \"жума\", \"ишемби\"], [\"жш.\", \"дш.\", \"шш.\", \"шр.\", \"бш.\", \"жм.\", \"иш.\"]], u, [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"янв.\", \"фев.\", \"мар.\", \"апр.\", \"май\", \"июн.\", \"июл.\", \"авг.\", \"сен.\", \"окт.\", \"ноя.\", \"дек.\"], [\"январь\", \"февраль\", \"март\", \"апрель\", \"май\", \"июнь\", \"июль\", \"август\", \"сентябрь\", \"октябрь\", \"ноябрь\", \"декабрь\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"Янв\", \"Фев\", \"Мар\", \"Апр\", \"Май\", \"Июн\", \"Июл\", \"Авг\", \"Сен\", \"Окт\", \"Ноя\", \"Дек\"], [\"Январь\", \"Февраль\", \"Март\", \"Апрель\", \"Май\", \"Июнь\", \"Июль\", \"Август\", \"Сентябрь\", \"Октябрь\", \"Ноябрь\", \"Декабрь\"]], [[\"б.з.ч.\", \"б.з.\"], u, [\"биздин заманга чейин\", \"биздин заман\"]], 1, [6, 0], [\"d/M/yy\", \"y-'ж'., d-MMM\", \"y-'ж'., d-MMMM\", \"y-'ж'., d-MMMM, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"сан эмес\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"KGS\", \"сом\", \"Кыргызстан сому\", { \"AUD\": [u, \"$\"], \"BMD\": [u, \"BD$\"], \"BRL\": [u, \"R$\"], \"BSD\": [u, \"B$\"], \"BYN\": [u, \"р.\"], \"BZD\": [u, \"BZ$\"], \"CAD\": [u, \"C$\"], \"DOP\": [u, \"RD$\"], \"EGP\": [u, \"LE\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"HK$\"], \"HRK\": [u, \"Kn\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"JMD\": [u, \"J$\"], \"JPY\": [\"JP¥\", \"¥\"], \"KGS\": [\"сом\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TTD\": [u, \"TT$\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"XCD\": [u, \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}