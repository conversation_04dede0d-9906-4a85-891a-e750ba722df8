{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"ta\", [[\"மு.ப\", \"பி.ப\"], [\"முற்பகல்\", \"பிற்பகல்\"], u], u, [[\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"], [\"ஞாயி.\", \"திங்.\", \"செவ்.\", \"புத.\", \"வியா.\", \"வெள்.\", \"சனி\"], [\"ஞாயிறு\", \"திங்கள்\", \"செவ்வாய்\", \"புதன்\", \"வியாழன்\", \"வெள்ளி\", \"சனி\"], [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"]], u, [[\"ஜ\", \"பி\", \"மா\", \"ஏ\", \"மே\", \"ஜூ\", \"ஜூ\", \"ஆ\", \"செ\", \"அ\", \"ந\", \"டி\"], [\"ஜன.\", \"பிப்.\", \"மார்.\", \"ஏப்.\", \"மே\", \"ஜூன்\", \"ஜூலை\", \"ஆக.\", \"செப்.\", \"அக்.\", \"நவ.\", \"டிச.\"], [\"ஜனவரி\", \"பிப்ரவரி\", \"மார்ச்\", \"ஏப்ரல்\", \"மே\", \"ஜூன்\", \"ஜூலை\", \"ஆகஸ்ட்\", \"செப்டம்பர்\", \"அக்டோபர்\", \"நவம்பர்\", \"டிசம்பர்\"]], u, [[\"கி.மு.\", \"கி.பி.\"], u, [\"கிறிஸ்துவுக்கு முன்\", \"அன்னோ டோமினி\"]], 0, [0, 0], [\"d/M/yy\", \"d MMM, y\", \"d MMMM, y\", \"EEEE, d MMMM, y\"], [\"a h:mm\", \"a h:mm:ss\", \"a h:mm:ss z\", \"a h:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} அன்று {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤ #,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"இந்திய ரூபாய்\", {\n  \"BYN\": [u, \"р.\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ta.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"ta\", [[\"மு.ப\", \"பி.ப\"], [\"முற்பகல்\", \"பிற்பகல்\"], u], u, [[\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"], [\"ஞாயி.\", \"திங்.\", \"செவ்.\", \"புத.\", \"வியா.\", \"வெள்.\", \"சனி\"], [\"ஞாயிறு\", \"திங்கள்\", \"செவ்வாய்\", \"புதன்\", \"வியாழன்\", \"வெள்ளி\", \"சனி\"], [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"]], u, [[\"ஜ\", \"பி\", \"மா\", \"ஏ\", \"மே\", \"ஜூ\", \"ஜூ\", \"ஆ\", \"செ\", \"அ\", \"ந\", \"டி\"], [\"ஜன.\", \"பிப்.\", \"மார்.\", \"ஏப்.\", \"மே\", \"ஜூன்\", \"ஜூலை\", \"ஆக.\", \"செப்.\", \"அக்.\", \"நவ.\", \"டிச.\"], [\"ஜனவரி\", \"பிப்ரவரி\", \"மார்ச்\", \"ஏப்ரல்\", \"மே\", \"ஜூன்\", \"ஜூலை\", \"ஆகஸ்ட்\", \"செப்டம்பர்\", \"அக்டோபர்\", \"நவம்பர்\", \"டிசம்பர்\"]], u, [[\"கி.மு.\", \"கி.பி.\"], u, [\"கிறிஸ்துவுக்கு முன்\", \"அன்னோ டோமினி\"]], 0, [0, 0], [\"d/M/yy\", \"d MMM, y\", \"d MMMM, y\", \"EEEE, d MMMM, y\"], [\"a h:mm\", \"a h:mm:ss\", \"a h:mm:ss z\", \"a h:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} அன்று {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤ #,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"இந்திய ரூபாய்\", { \"BYN\": [u, \"р.\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEJ,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEA,CAAC,EAAE,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,eAAe,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,eAAe,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}