{"ast": null, "code": "import toDate from \"../../../../toDate/index.js\";\nimport isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\n// Adapted from the `ru` translation\n\nvar weekdays = ['неделя', 'понеделник', 'вторник', 'сряда', 'четвъртък', 'петък', 'събота'];\nfunction lastWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'миналата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'миналия \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = weekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'във \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction nextWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следващата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следващия \" + weekday + \" в' p\";\n  }\n}\nvar lastWeekFormatToken = function lastWeekFormatToken(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nvar nextWeekFormatToken = function nextWeekFormatToken(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormatToken,\n  yesterday: \"'вчера в' p\",\n  today: \"'днес в' p\",\n  tomorrow: \"'утре в' p\",\n  nextWeek: nextWeekFormatToken,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["toDate", "isSameUTCWeek", "weekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "lastWeekFormatToken", "dirtyDate", "baseDate", "options", "date", "getUTCDay", "nextWeekFormatToken", "formatRelativeLocale", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/bg/_lib/formatRelative/index.js"], "sourcesContent": ["import toDate from \"../../../../toDate/index.js\";\nimport isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\n// Adapted from the `ru` translation\n\nvar weekdays = ['неделя', 'понеделник', 'вторник', 'сряда', 'четвъртък', 'петък', 'събота'];\nfunction lastWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'миналата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'миналия \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = weekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'във \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction nextWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следващата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следващия \" + weekday + \" в' p\";\n  }\n}\nvar lastWeekFormatToken = function lastWeekFormatToken(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nvar nextWeekFormatToken = function nextWeekFormatToken(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormatToken,\n  yesterday: \"'вчера в' p\",\n  today: \"'днес в' p\",\n  tomorrow: \"'утре в' p\",\n  nextWeek: nextWeekFormatToken,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,6BAA6B;AAChD,OAAOC,aAAa,MAAM,yCAAyC;AACnE;;AAEA,IAAIC,QAAQ,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC3F,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAC3B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,OAAO;IACzC,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,WAAW,GAAGA,OAAO,GAAG,OAAO;EAC1C;AACF;AACA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAC3B,IAAIA,GAAG,KAAK,CAAC,CAAC,WAAW;IACvB,OAAO,OAAO,GAAGC,OAAO,GAAG,OAAO;EACpC,CAAC,MAAM;IACL,OAAO,KAAK,GAAGA,OAAO,GAAG,OAAO;EAClC;AACF;AACA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAC3B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,cAAc,GAAGC,OAAO,GAAG,OAAO;IAC3C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGA,OAAO,GAAG,OAAO;EAC5C;AACF;AACA,IAAIG,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACnF,IAAIC,IAAI,GAAGZ,MAAM,CAACS,SAAS,CAAC;EAC5B,IAAIL,GAAG,GAAGQ,IAAI,CAACC,SAAS,CAAC,CAAC;EAC1B,IAAIZ,aAAa,CAACW,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IAC1C,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;EACtB;AACF,CAAC;AACD,IAAIU,mBAAmB,GAAG,SAASA,mBAAmBA,CAACL,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACnF,IAAIC,IAAI,GAAGZ,MAAM,CAACS,SAAS,CAAC;EAC5B,IAAIL,GAAG,GAAGQ,IAAI,CAACC,SAAS,CAAC,CAAC;EAC1B,IAAIZ,aAAa,CAACW,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IAC1C,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;EACtB;AACF,CAAC;AACD,IAAIW,oBAAoB,GAAG;EACzBZ,QAAQ,EAAEK,mBAAmB;EAC7BQ,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,YAAY;EACtBX,QAAQ,EAAEO,mBAAmB;EAC7BK,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAET,IAAI,EAAEF,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIW,MAAM,GAAGP,oBAAoB,CAACM,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOW,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}