{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/yo-BJ\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    return 5;\n  }\n  exports.default = ['yo-BJ', [['Àárɔ̀', 'Ɔ̀sán'], u, u], u, [['À', 'A', 'Ì', 'Ɔ', 'Ɔ', 'Ɛ', 'À'], ['Àìk', 'Aj', 'Ìsɛ́g', 'Ɔjɔ́r', 'Ɔjɔ́b', 'Ɛt', 'Àbám'], ['Ɔjɔ́ Àìkú', 'Ɔjɔ́ Ajé', 'Ɔjɔ́ Ìsɛ́gun', 'Ɔjɔ́rú', 'Ɔjɔ́bɔ', 'Ɔjɔ́ Ɛtì', 'Ɔjɔ́ Àbámɛ́ta'], ['Àìkú', 'Ajé', 'Ìsɛ́gun', 'Ɔjɔ́rú', 'Ɔjɔ́bɔ', 'Ɛtì', 'Àbámɛ́ta']], [['À', 'A', 'Ì', 'Ɔ', 'Ɔ', 'Ɛ', 'À'], ['Àìk', 'Aj', 'Ìsɛ́g', 'Ɔjɔ́r', 'Ɔjɔ́b', 'Ɛt', 'Àbám'], ['Àìkú', 'Ajé', 'Ìsɛ́gun', 'Ɔjɔ́rú', 'Ɔjɔ́bɔ', 'Ɛtì', 'Àbámɛ́ta'], u], [['S', 'È', 'Ɛ', 'Ì', 'Ɛ̀', 'Ò', 'A', 'Ò', 'O', 'Ɔ̀', 'B', 'Ɔ̀'], ['Shɛ́r', 'Èrèl', 'Ɛrɛ̀n', 'Ìgb', 'Ɛ̀bi', 'Òkú', 'Agɛ', 'Ògú', 'Owe', 'Ɔ̀wà', 'Bél', 'Ɔ̀pɛ'], ['Oshù Shɛ́rɛ́', 'Oshù Èrèlè', 'Oshù Ɛrɛ̀nà', 'Oshù Ìgbé', 'Oshù Ɛ̀bibi', 'Oshù Òkúdu', 'Oshù Agɛmɔ', 'Oshù Ògún', 'Oshù Owewe', 'Oshù Ɔ̀wàrà', 'Oshù Bélú', 'Oshù Ɔ̀pɛ̀']], [['S', 'È', 'Ɛ', 'Ì', 'Ɛ̀', 'Ò', 'A', 'Ò', 'O', 'Ɔ̀', 'B', 'Ɔ̀'], ['Shɛ́', 'Èr', 'Ɛr', 'Ìg', 'Ɛ̀b', 'Òk', 'Ag', 'Òg', 'Ow', 'Ɔ̀w', 'Bé', 'Ɔ̀p'], ['Shɛ́rɛ́', 'Èrèlè', 'Ɛrɛ̀nà', 'Ìgbé', 'Ɛ̀bibi', 'Òkúdu', 'Agɛmɔ', 'Ògún', 'Owewe', 'Ɔ̀wàrà', 'Bélú', 'Ɔ̀pɛ̀']], [['BCE', 'AD'], u, ['Saju Kristi', 'Lehin Kristi']], 1, [6, 0], ['d/M/y', 'd MM y', 'd MMM y', 'EEEE, d MMM y'], ['H:m', 'H:m:s', 'H:mm:ss z', 'HH:mm:ss zzzz'], ['{1} {0}', u, u, u], ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'], 'XOF', 'CFA', 'Faransi ti Orílɛ́ède BIKEAO', {\n    'JPY': ['JP¥', '¥'],\n    'NGN': ['₦'],\n    'RUB': ['₽']\n  }, 'ltr', plural];\n});\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}