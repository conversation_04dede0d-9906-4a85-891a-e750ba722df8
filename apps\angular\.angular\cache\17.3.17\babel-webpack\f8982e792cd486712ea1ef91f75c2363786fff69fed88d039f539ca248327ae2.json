{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['BC', 'AC'],\n  abbreviated: ['紀元前', '西暦'],\n  wide: ['紀元前', '西暦']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['第1四半期', '第2四半期', '第3四半期', '第4四半期']\n};\nvar monthValues = {\n  narrow: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n  abbreviated: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n  wide: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']\n};\nvar dayValues = {\n  narrow: ['日', '月', '火', '水', '木', '金', '土'],\n  short: ['日', '月', '火', '水', '木', '金', '土'],\n  abbreviated: ['日', '月', '火', '水', '木', '金', '土'],\n  wide: ['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: '午前',\n    pm: '午後',\n    midnight: '深夜',\n    noon: '正午',\n    morning: '朝',\n    afternoon: '午後',\n    evening: '夜',\n    night: '深夜'\n  },\n  abbreviated: {\n    am: '午前',\n    pm: '午後',\n    midnight: '深夜',\n    noon: '正午',\n    morning: '朝',\n    afternoon: '午後',\n    evening: '夜',\n    night: '深夜'\n  },\n  wide: {\n    am: '午前',\n    pm: '午後',\n    midnight: '深夜',\n    noon: '正午',\n    morning: '朝',\n    afternoon: '午後',\n    evening: '夜',\n    night: '深夜'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: '午前',\n    pm: '午後',\n    midnight: '深夜',\n    noon: '正午',\n    morning: '朝',\n    afternoon: '午後',\n    evening: '夜',\n    night: '深夜'\n  },\n  abbreviated: {\n    am: '午前',\n    pm: '午後',\n    midnight: '深夜',\n    noon: '正午',\n    morning: '朝',\n    afternoon: '午後',\n    evening: '夜',\n    night: '深夜'\n  },\n  wide: {\n    am: '午前',\n    pm: '午後',\n    midnight: '深夜',\n    noon: '正午',\n    morning: '朝',\n    afternoon: '午後',\n    evening: '夜',\n    night: '深夜'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  switch (unit) {\n    case 'year':\n      return \"\".concat(number, \"\\u5E74\");\n    case 'quarter':\n      return \"\\u7B2C\".concat(number, \"\\u56DB\\u534A\\u671F\");\n    case 'month':\n      return \"\".concat(number, \"\\u6708\");\n    case 'week':\n      return \"\\u7B2C\".concat(number, \"\\u9031\");\n    case 'date':\n      return \"\".concat(number, \"\\u65E5\");\n    case 'hour':\n      return \"\".concat(number, \"\\u6642\");\n    case 'minute':\n      return \"\".concat(number, \"\\u5206\");\n    case 'second':\n      return \"\".concat(number, \"\\u79D2\");\n    default:\n      return \"\".concat(number);\n  }\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}