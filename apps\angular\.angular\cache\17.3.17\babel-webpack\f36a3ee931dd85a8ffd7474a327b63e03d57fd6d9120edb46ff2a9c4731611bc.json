{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n  if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11)) return 1;\n  if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 && !(i % 100 >= 12 && i % 100 <= 14) || f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 && !(f % 100 >= 12 && f % 100 <= 14)) return 3;\n  return 5;\n}\nexport default [\"sr-Cyrl-XK\", [[\"AM\", \"PM\"], u, u], [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"н\", \"п\", \"у\", \"с\", \"ч\", \"п\", \"с\"], [\"нед\", \"пон\", \"уто\", \"сре\", \"чет\", \"пет\", \"суб\"], [\"недеља\", \"понедељак\", \"уторак\", \"среда\", \"четвртак\", \"петак\", \"субота\"], [\"не\", \"по\", \"ут\", \"ср\", \"че\", \"пе\", \"су\"]], u, [[\"ј\", \"ф\", \"м\", \"а\", \"м\", \"ј\", \"ј\", \"а\", \"с\", \"о\", \"н\", \"д\"], [\"јан\", \"феб\", \"март\", \"апр\", \"мај\", \"јун\", \"јул\", \"авг\", \"септ\", \"окт\", \"нов\", \"дец\"], [\"јануар\", \"фебруар\", \"март\", \"април\", \"мај\", \"јун\", \"јул\", \"август\", \"септембар\", \"октобар\", \"новембар\", \"децембар\"]], u, [[\"п.н.е.\", \"н.е.\"], [\"п. н. е.\", \"н. е.\"], [\"пре нове ере\", \"нове ере\"]], 1, [6, 0], [\"d.M.yy.\", \"d. M. y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Евро\", {\n  \"AUD\": [u, \"$\"],\n  \"BAM\": [\"КМ\", \"KM\"],\n  \"BYN\": [u, \"р.\"],\n  \"GEL\": [u, \"ლ\"],\n  \"KRW\": [u, \"₩\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"VND\": [u, \"₫\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length", "f", "parseInt"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/sr-Cyrl-XK.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n    if (v === 0 && (i % 10 === 1 && !(i % 100 === 11)) || f % 10 === 1 && !(f % 100 === 11))\n        return 1;\n    if (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)) || f % 10 === Math.floor(f % 10) && (f % 10 >= 2 && f % 10 <= 4) && !(f % 100 >= 12 && f % 100 <= 14))\n        return 3;\n    return 5;\n}\nexport default [\"sr-Cyrl-XK\", [[\"AM\", \"PM\"], u, u], [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"н\", \"п\", \"у\", \"с\", \"ч\", \"п\", \"с\"], [\"нед\", \"пон\", \"уто\", \"сре\", \"чет\", \"пет\", \"суб\"], [\"недеља\", \"понедељак\", \"уторак\", \"среда\", \"четвртак\", \"петак\", \"субота\"], [\"не\", \"по\", \"ут\", \"ср\", \"че\", \"пе\", \"су\"]], u, [[\"ј\", \"ф\", \"м\", \"а\", \"м\", \"ј\", \"ј\", \"а\", \"с\", \"о\", \"н\", \"д\"], [\"јан\", \"феб\", \"март\", \"апр\", \"мај\", \"јун\", \"јул\", \"авг\", \"септ\", \"окт\", \"нов\", \"дец\"], [\"јануар\", \"фебруар\", \"март\", \"април\", \"мај\", \"јун\", \"јул\", \"август\", \"септембар\", \"октобар\", \"новембар\", \"децембар\"]], u, [[\"п.н.е.\", \"н.е.\"], [\"п. н. е.\", \"н. е.\"], [\"пре нове ере\", \"нове ере\"]], 1, [6, 0], [\"d.M.yy.\", \"d. M. y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Евро\", { \"AUD\": [u, \"$\"], \"BAM\": [\"КМ\", \"KM\"], \"BYN\": [u, \"р.\"], \"GEL\": [u, \"ლ\"], \"KRW\": [u, \"₩\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"], \"VND\": [u, \"₫\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;IAAEC,CAAC,GAAGC,QAAQ,CAACX,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;EAChK,IAAIF,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAE,IAAIQ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,EACnF,OAAO,CAAC;EACZ,IAAIJ,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAE,IAAIQ,CAAC,GAAG,EAAE,KAAKP,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,EACrN,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEb,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}