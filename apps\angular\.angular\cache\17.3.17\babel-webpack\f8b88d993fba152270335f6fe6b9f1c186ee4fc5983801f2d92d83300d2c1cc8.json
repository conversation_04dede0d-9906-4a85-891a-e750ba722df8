{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { getDocument, isFunction } from \"@microsoft/applicationinsights-core-js\";\nexport function createDomEvent(eventName) {\n  var event = null;\n  if (isFunction(Event)) {\n    // Use Event constructor when available\n    event = new Event(eventName);\n  } else {\n    // Event has no constructor in IE\n    var doc = getDocument();\n    if (doc && doc.createEvent) {\n      event = doc.createEvent(\"Event\");\n      event.initEvent(eventName, true, true);\n    }\n  }\n  return event;\n}", "map": {"version": 3, "names": ["getDocument", "isFunction", "createDomEvent", "eventName", "event", "Event", "doc", "createEvent", "initEvent"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/DomHelperFuncs.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { getDocument, isFunction } from \"@microsoft/applicationinsights-core-js\";\r\nexport function createDomEvent(eventName) {\r\n    var event = null;\r\n    if (isFunction(Event)) { // Use Event constructor when available\r\n        event = new Event(eventName);\r\n    }\r\n    else { // Event has no constructor in IE\r\n        var doc = getDocument();\r\n        if (doc && doc.createEvent) {\r\n            event = doc.createEvent(\"Event\");\r\n            event.initEvent(eventName, true, true);\r\n        }\r\n    }\r\n    return event;\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,WAAW,EAAEC,UAAU,QAAQ,wCAAwC;AAChF,OAAO,SAASC,cAAcA,CAACC,SAAS,EAAE;EACtC,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIH,UAAU,CAACI,KAAK,CAAC,EAAE;IAAE;IACrBD,KAAK,GAAG,IAAIC,KAAK,CAACF,SAAS,CAAC;EAChC,CAAC,MACI;IAAE;IACH,IAAIG,GAAG,GAAGN,WAAW,CAAC,CAAC;IACvB,IAAIM,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE;MACxBH,KAAK,GAAGE,GAAG,CAACC,WAAW,CAAC,OAAO,CAAC;MAChCH,KAAK,CAACI,SAAS,CAACL,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1C;EACJ;EACA,OAAOC,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}