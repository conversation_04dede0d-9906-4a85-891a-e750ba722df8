{"ast": null, "code": "import * as i2 from '@abp/ng.core';\nimport { getRoutePath, MultiTenancyService, RoutesService, ConfigStateService, SessionStateService, EnvironmentService, SubscriptionService, RouterEvents } from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { map, finalize } from 'rxjs/operators';\nimport * as i1 from '@abp/ng.theme.shared';\nclass AuthWrapperService {\n  get isMultiTenancyEnabled$() {\n    return this.configState.getDeep$('multiTenancy.isEnabled');\n  }\n  get languages$() {\n    return this.configState.getDeep$('localization.languages');\n  }\n  get enableLocalLogin$() {\n    return this.configState.getSetting$('Abp.Account.EnableLocalLogin').pipe(map(value => value?.toLowerCase() !== 'false'));\n  }\n  get isTenantBoxVisible() {\n    return this.tenantBoxVisible && this.multiTenancy.isTenantBoxVisible;\n  }\n  set isTenantBoxVisible(value) {\n    this.multiTenancy.isTenantBoxVisible = value;\n  }\n  get defaultLanguage$() {\n    return this.languages$.pipe(map(languages => {\n      const lang = languages?.find(l => l.cultureName === this.selectedLangCulture) || {};\n      return {\n        displayName: lang.displayName || '',\n        flagIcon: lang.flagIcon\n      };\n    }));\n  }\n  get dropdownLanguages$() {\n    return this.languages$.pipe(map(languages => languages?.filter(lang => lang.cultureName !== this.selectedLangCulture) || []));\n  }\n  get selectedLangCulture() {\n    return this.sessionState.getLanguage();\n  }\n  get appInfo() {\n    return this.environment.getEnvironment().application;\n  }\n  get pageLabel() {\n    const path = getRoutePath(this.router);\n    const route = this.routes.search({\n      path\n    });\n    return route?.name || '';\n  }\n  get tenantBoxVisible() {\n    return this.getMostInnerChild().data.tenantBoxVisible ?? true;\n  }\n  constructor(injector) {\n    this.injector = injector;\n    this.tenantBoxKey = 'Account.TenantBoxComponent';\n    this.logoKey = 'Account.LogoComponent';\n    this.multiTenancy = injector.get(MultiTenancyService);\n    this.router = injector.get(Router);\n    this.route = injector.get(ActivatedRoute);\n    this.routes = injector.get(RoutesService);\n    this.configState = injector.get(ConfigStateService);\n    this.sessionState = injector.get(SessionStateService);\n    this.environment = injector.get(EnvironmentService);\n    this.subscription = injector.get(SubscriptionService);\n    this.routerEvents = injector.get(RouterEvents);\n  }\n  onChangeLang(cultureName) {\n    this.sessionState.setLanguage(cultureName);\n  }\n  getMostInnerChild() {\n    let child = this.route.snapshot;\n    let depth = 0;\n    const depthLimit = 10;\n    while (child.firstChild && depth < depthLimit) {\n      child = child.firstChild;\n      depth++;\n    }\n    return child;\n  }\n  static {\n    this.ɵfac = function AuthWrapperService_Factory(t) {\n      return new (t || AuthWrapperService)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthWrapperService,\n      factory: AuthWrapperService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthWrapperService, [{\n    type: Injectable\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nclass TenantBoxService {\n  constructor(toasterService, tenantService, sessionState, configState) {\n    this.toasterService = toasterService;\n    this.tenantService = tenantService;\n    this.sessionState = sessionState;\n    this.configState = configState;\n    this.currentTenant$ = this.sessionState.getTenant$();\n  }\n  onSwitch() {\n    const tenant = this.sessionState.getTenant();\n    this.name = tenant?.name;\n    this.isModalVisible = true;\n  }\n  save() {\n    if (!this.name) {\n      this.setTenant(null);\n      this.isModalVisible = false;\n      return;\n    }\n    this.modalBusy = true;\n    this.tenantService.findTenantByName(this.name).pipe(finalize(() => this.modalBusy = false)).subscribe(({\n      success,\n      isActive,\n      tenantId: id,\n      ...tenant\n    }) => {\n      if (!success || !isActive) {\n        this.showError();\n        return;\n      }\n      this.setTenant({\n        ...tenant,\n        id,\n        isAvailable: true\n      });\n      this.isModalVisible = false;\n    });\n  }\n  setTenant(tenant) {\n    this.sessionState.setTenant(tenant);\n    this.configState.refreshAppState().subscribe();\n  }\n  showError() {\n    this.toasterService.error('AbpUiMultiTenancy::GivenTenantIsNotAvailable', 'AbpUi::Error', {\n      messageLocalizationParams: [this.name]\n    });\n  }\n  static {\n    this.ɵfac = function TenantBoxService_Factory(t) {\n      return new (t || TenantBoxService)(i0.ɵɵinject(i1.ToasterService), i0.ɵɵinject(i2.AbpTenantService), i0.ɵɵinject(i2.SessionStateService), i0.ɵɵinject(i2.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TenantBoxService,\n      factory: TenantBoxService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TenantBoxService, [{\n    type: Injectable\n  }], () => [{\n    type: i1.ToasterService\n  }, {\n    type: i2.AbpTenantService\n  }, {\n    type: i2.SessionStateService\n  }, {\n    type: i2.ConfigStateService\n  }], null);\n})();\n\n/*\n * Public API Surface of account-core\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AuthWrapperService, TenantBoxService };", "map": {"version": 3, "names": ["i2", "getRoutePath", "MultiTenancyService", "RoutesService", "ConfigStateService", "SessionStateService", "EnvironmentService", "SubscriptionService", "RouterEvents", "i0", "Injectable", "Router", "ActivatedRoute", "map", "finalize", "i1", "AuthWrapperService", "isMultiTenancyEnabled$", "configState", "getDeep$", "languages$", "enableLocalLogin$", "getSetting$", "pipe", "value", "toLowerCase", "isTenantBoxVisible", "tenantBoxVisible", "multiTenancy", "defaultLanguage$", "languages", "lang", "find", "l", "cultureName", "selectedLangCulture", "displayName", "flagIcon", "dropdownLanguages$", "filter", "sessionState", "getLanguage", "appInfo", "environment", "getEnvironment", "application", "pageLabel", "path", "router", "route", "routes", "search", "name", "getMostInnerChild", "data", "constructor", "injector", "tenantBoxKey", "logoKey", "get", "subscription", "routerEvents", "onChangeLang", "setLanguage", "child", "snapshot", "depth", "depthLimit", "<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "AuthWrapperService_Factory", "t", "ɵɵinject", "Injector", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "TenantBoxService", "toasterService", "tenantService", "currentTenant$", "getTenant$", "onSwitch", "tenant", "<PERSON><PERSON><PERSON><PERSON>", "isModalVisible", "save", "<PERSON><PERSON><PERSON><PERSON>", "modalBusy", "findTenantByName", "subscribe", "success", "isActive", "tenantId", "id", "showError", "isAvailable", "refreshAppState", "error", "messageLocalizationParams", "TenantBoxService_Factory", "ToasterService", "AbpTenantService"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@volo/abp.ng.account.core/fesm2022/volo-abp.ng.account.core.mjs"], "sourcesContent": ["import * as i2 from '@abp/ng.core';\nimport { getRoutePath, MultiTenancyService, RoutesService, ConfigStateService, SessionStateService, EnvironmentService, SubscriptionService, RouterEvents } from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { map, finalize } from 'rxjs/operators';\nimport * as i1 from '@abp/ng.theme.shared';\n\nclass AuthWrapperService {\n    get isMultiTenancyEnabled$() {\n        return this.configState.getDeep$('multiTenancy.isEnabled');\n    }\n    get languages$() {\n        return this.configState.getDeep$('localization.languages');\n    }\n    get enableLocalLogin$() {\n        return this.configState\n            .getSetting$('Abp.Account.EnableLocalLogin')\n            .pipe(map(value => value?.toLowerCase() !== 'false'));\n    }\n    get isTenantBoxVisible() {\n        return this.tenantBoxVisible && this.multiTenancy.isTenantBoxVisible;\n    }\n    set isTenantBoxVisible(value) {\n        this.multiTenancy.isTenantBoxVisible = value;\n    }\n    get defaultLanguage$() {\n        return this.languages$.pipe(map(languages => {\n            const lang = languages?.find(l => l.cultureName === this.selectedLangCulture) || {};\n            return {\n                displayName: lang.displayName || '',\n                flagIcon: lang.flagIcon,\n            };\n        }));\n    }\n    get dropdownLanguages$() {\n        return this.languages$.pipe(map(languages => languages?.filter(lang => lang.cultureName !== this.selectedLangCulture) || []));\n    }\n    get selectedLangCulture() {\n        return this.sessionState.getLanguage();\n    }\n    get appInfo() {\n        return this.environment.getEnvironment().application;\n    }\n    get pageLabel() {\n        const path = getRoutePath(this.router);\n        const route = this.routes.search({ path });\n        return route?.name || '';\n    }\n    get tenantBoxVisible() {\n        return this.getMostInnerChild().data.tenantBoxVisible ?? true;\n    }\n    constructor(injector) {\n        this.injector = injector;\n        this.tenantBoxKey = 'Account.TenantBoxComponent';\n        this.logoKey = 'Account.LogoComponent';\n        this.multiTenancy = injector.get(MultiTenancyService);\n        this.router = injector.get(Router);\n        this.route = injector.get(ActivatedRoute);\n        this.routes = injector.get(RoutesService);\n        this.configState = injector.get(ConfigStateService);\n        this.sessionState = injector.get(SessionStateService);\n        this.environment = injector.get(EnvironmentService);\n        this.subscription = injector.get(SubscriptionService);\n        this.routerEvents = injector.get(RouterEvents);\n    }\n    onChangeLang(cultureName) {\n        this.sessionState.setLanguage(cultureName);\n    }\n    getMostInnerChild() {\n        let child = this.route.snapshot;\n        let depth = 0;\n        const depthLimit = 10;\n        while (child.firstChild && depth < depthLimit) {\n            child = child.firstChild;\n            depth++;\n        }\n        return child;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AuthWrapperService, deps: [{ token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AuthWrapperService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AuthWrapperService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.Injector }] });\n\nclass TenantBoxService {\n    constructor(toasterService, tenantService, sessionState, configState) {\n        this.toasterService = toasterService;\n        this.tenantService = tenantService;\n        this.sessionState = sessionState;\n        this.configState = configState;\n        this.currentTenant$ = this.sessionState.getTenant$();\n    }\n    onSwitch() {\n        const tenant = this.sessionState.getTenant();\n        this.name = tenant?.name;\n        this.isModalVisible = true;\n    }\n    save() {\n        if (!this.name) {\n            this.setTenant(null);\n            this.isModalVisible = false;\n            return;\n        }\n        this.modalBusy = true;\n        this.tenantService\n            .findTenantByName(this.name)\n            .pipe(finalize(() => (this.modalBusy = false)))\n            .subscribe(({ success, isActive, tenantId: id, ...tenant }) => {\n            if (!success || !isActive) {\n                this.showError();\n                return;\n            }\n            this.setTenant({ ...tenant, id, isAvailable: true });\n            this.isModalVisible = false;\n        });\n    }\n    setTenant(tenant) {\n        this.sessionState.setTenant(tenant);\n        this.configState.refreshAppState().subscribe();\n    }\n    showError() {\n        this.toasterService.error('AbpUiMultiTenancy::GivenTenantIsNotAvailable', 'AbpUi::Error', {\n            messageLocalizationParams: [this.name],\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TenantBoxService, deps: [{ token: i1.ToasterService }, { token: i2.AbpTenantService }, { token: i2.SessionStateService }, { token: i2.ConfigStateService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TenantBoxService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TenantBoxService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i1.ToasterService }, { type: i2.AbpTenantService }, { type: i2.SessionStateService }, { type: i2.ConfigStateService }] });\n\n/*\n * Public API Surface of account-core\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AuthWrapperService, TenantBoxService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,cAAc;AAClC,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,YAAY,QAAQ,cAAc;AAC/K,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,MAAM,EAAEC,cAAc,QAAQ,iBAAiB;AACxD,SAASC,GAAG,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9C,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAE1C,MAAMC,kBAAkB,CAAC;EACrB,IAAIC,sBAAsBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,wBAAwB,CAAC;EAC9D;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,WAAW,CAACC,QAAQ,CAAC,wBAAwB,CAAC;EAC9D;EACA,IAAIE,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACH,WAAW,CAClBI,WAAW,CAAC,8BAA8B,CAAC,CAC3CC,IAAI,CAACV,GAAG,CAACW,KAAK,IAAIA,KAAK,EAAEC,WAAW,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC;EAC7D;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,YAAY,CAACF,kBAAkB;EACxE;EACA,IAAIA,kBAAkBA,CAACF,KAAK,EAAE;IAC1B,IAAI,CAACI,YAAY,CAACF,kBAAkB,GAAGF,KAAK;EAChD;EACA,IAAIK,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACT,UAAU,CAACG,IAAI,CAACV,GAAG,CAACiB,SAAS,IAAI;MACzC,MAAMC,IAAI,GAAGD,SAAS,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC,CAAC;MACnF,OAAO;QACHC,WAAW,EAAEL,IAAI,CAACK,WAAW,IAAI,EAAE;QACnCC,QAAQ,EAAEN,IAAI,CAACM;MACnB,CAAC;IACL,CAAC,CAAC,CAAC;EACP;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAClB,UAAU,CAACG,IAAI,CAACV,GAAG,CAACiB,SAAS,IAAIA,SAAS,EAAES,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACG,WAAW,KAAK,IAAI,CAACC,mBAAmB,CAAC,IAAI,EAAE,CAAC,CAAC;EACjI;EACA,IAAIA,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACK,YAAY,CAACC,WAAW,CAAC,CAAC;EAC1C;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,WAAW,CAACC,cAAc,CAAC,CAAC,CAACC,WAAW;EACxD;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,MAAMC,IAAI,GAAG9C,YAAY,CAAC,IAAI,CAAC+C,MAAM,CAAC;IACtC,MAAMC,KAAK,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC;MAAEJ;IAAK,CAAC,CAAC;IAC1C,OAAOE,KAAK,EAAEG,IAAI,IAAI,EAAE;EAC5B;EACA,IAAIzB,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC0B,iBAAiB,CAAC,CAAC,CAACC,IAAI,CAAC3B,gBAAgB,IAAI,IAAI;EACjE;EACA4B,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAG,4BAA4B;IAChD,IAAI,CAACC,OAAO,GAAG,uBAAuB;IACtC,IAAI,CAAC9B,YAAY,GAAG4B,QAAQ,CAACG,GAAG,CAACzD,mBAAmB,CAAC;IACrD,IAAI,CAAC8C,MAAM,GAAGQ,QAAQ,CAACG,GAAG,CAAChD,MAAM,CAAC;IAClC,IAAI,CAACsC,KAAK,GAAGO,QAAQ,CAACG,GAAG,CAAC/C,cAAc,CAAC;IACzC,IAAI,CAACsC,MAAM,GAAGM,QAAQ,CAACG,GAAG,CAACxD,aAAa,CAAC;IACzC,IAAI,CAACe,WAAW,GAAGsC,QAAQ,CAACG,GAAG,CAACvD,kBAAkB,CAAC;IACnD,IAAI,CAACoC,YAAY,GAAGgB,QAAQ,CAACG,GAAG,CAACtD,mBAAmB,CAAC;IACrD,IAAI,CAACsC,WAAW,GAAGa,QAAQ,CAACG,GAAG,CAACrD,kBAAkB,CAAC;IACnD,IAAI,CAACsD,YAAY,GAAGJ,QAAQ,CAACG,GAAG,CAACpD,mBAAmB,CAAC;IACrD,IAAI,CAACsD,YAAY,GAAGL,QAAQ,CAACG,GAAG,CAACnD,YAAY,CAAC;EAClD;EACAsD,YAAYA,CAAC5B,WAAW,EAAE;IACtB,IAAI,CAACM,YAAY,CAACuB,WAAW,CAAC7B,WAAW,CAAC;EAC9C;EACAmB,iBAAiBA,CAAA,EAAG;IAChB,IAAIW,KAAK,GAAG,IAAI,CAACf,KAAK,CAACgB,QAAQ;IAC/B,IAAIC,KAAK,GAAG,CAAC;IACb,MAAMC,UAAU,GAAG,EAAE;IACrB,OAAOH,KAAK,CAACI,UAAU,IAAIF,KAAK,GAAGC,UAAU,EAAE;MAC3CH,KAAK,GAAGA,KAAK,CAACI,UAAU;MACxBF,KAAK,EAAE;IACX;IACA,OAAOF,KAAK;EAChB;EACA;IAAS,IAAI,CAACK,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFvD,kBAAkB,EAA5BP,EAAE,CAAA+D,QAAA,CAA4C/D,EAAE,CAACgE,QAAQ;IAAA,CAA6C;EAAE;EACxM;IAAS,IAAI,CAACC,KAAK,kBAD6EjE,EAAE,CAAAkE,kBAAA;MAAAC,KAAA,EACY5D,kBAAkB;MAAA6D,OAAA,EAAlB7D,kBAAkB,CAAAqD;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAHoGrE,EAAE,CAAAsE,iBAAA,CAGX/D,kBAAkB,EAAc,CAAC;IAChHgE,IAAI,EAAEtE;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsE,IAAI,EAAEvE,EAAE,CAACgE;EAAS,CAAC,CAAC;AAAA;AAEzD,MAAMQ,gBAAgB,CAAC;EACnB1B,WAAWA,CAAC2B,cAAc,EAAEC,aAAa,EAAE3C,YAAY,EAAEtB,WAAW,EAAE;IAClE,IAAI,CAACgE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC3C,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACtB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACkE,cAAc,GAAG,IAAI,CAAC5C,YAAY,CAAC6C,UAAU,CAAC,CAAC;EACxD;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMC,MAAM,GAAG,IAAI,CAAC/C,YAAY,CAACgD,SAAS,CAAC,CAAC;IAC5C,IAAI,CAACpC,IAAI,GAAGmC,MAAM,EAAEnC,IAAI;IACxB,IAAI,CAACqC,cAAc,GAAG,IAAI;EAC9B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACtC,IAAI,EAAE;MACZ,IAAI,CAACuC,SAAS,CAAC,IAAI,CAAC;MACpB,IAAI,CAACF,cAAc,GAAG,KAAK;MAC3B;IACJ;IACA,IAAI,CAACG,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,aAAa,CACbU,gBAAgB,CAAC,IAAI,CAACzC,IAAI,CAAC,CAC3B7B,IAAI,CAACT,QAAQ,CAAC,MAAO,IAAI,CAAC8E,SAAS,GAAG,KAAM,CAAC,CAAC,CAC9CE,SAAS,CAAC,CAAC;MAAEC,OAAO;MAAEC,QAAQ;MAAEC,QAAQ,EAAEC,EAAE;MAAE,GAAGX;IAAO,CAAC,KAAK;MAC/D,IAAI,CAACQ,OAAO,IAAI,CAACC,QAAQ,EAAE;QACvB,IAAI,CAACG,SAAS,CAAC,CAAC;QAChB;MACJ;MACA,IAAI,CAACR,SAAS,CAAC;QAAE,GAAGJ,MAAM;QAAEW,EAAE;QAAEE,WAAW,EAAE;MAAK,CAAC,CAAC;MACpD,IAAI,CAACX,cAAc,GAAG,KAAK;IAC/B,CAAC,CAAC;EACN;EACAE,SAASA,CAACJ,MAAM,EAAE;IACd,IAAI,CAAC/C,YAAY,CAACmD,SAAS,CAACJ,MAAM,CAAC;IACnC,IAAI,CAACrE,WAAW,CAACmF,eAAe,CAAC,CAAC,CAACP,SAAS,CAAC,CAAC;EAClD;EACAK,SAASA,CAAA,EAAG;IACR,IAAI,CAACjB,cAAc,CAACoB,KAAK,CAAC,8CAA8C,EAAE,cAAc,EAAE;MACtFC,yBAAyB,EAAE,CAAC,IAAI,CAACnD,IAAI;IACzC,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACiB,IAAI,YAAAmC,yBAAAjC,CAAA;MAAA,YAAAA,CAAA,IAAwFU,gBAAgB,EAhD1BxE,EAAE,CAAA+D,QAAA,CAgD0CzD,EAAE,CAAC0F,cAAc,GAhD7DhG,EAAE,CAAA+D,QAAA,CAgDwExE,EAAE,CAAC0G,gBAAgB,GAhD7FjG,EAAE,CAAA+D,QAAA,CAgDwGxE,EAAE,CAACK,mBAAmB,GAhDhII,EAAE,CAAA+D,QAAA,CAgD2IxE,EAAE,CAACI,kBAAkB;IAAA,CAA6C;EAAE;EACjT;IAAS,IAAI,CAACsE,KAAK,kBAjD6EjE,EAAE,CAAAkE,kBAAA;MAAAC,KAAA,EAiDYK,gBAAgB;MAAAJ,OAAA,EAAhBI,gBAAgB,CAAAZ;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAnDoGrE,EAAE,CAAAsE,iBAAA,CAmDXE,gBAAgB,EAAc,CAAC;IAC9GD,IAAI,EAAEtE;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsE,IAAI,EAAEjE,EAAE,CAAC0F;EAAe,CAAC,EAAE;IAAEzB,IAAI,EAAEhF,EAAE,CAAC0G;EAAiB,CAAC,EAAE;IAAE1B,IAAI,EAAEhF,EAAE,CAACK;EAAoB,CAAC,EAAE;IAAE2E,IAAI,EAAEhF,EAAE,CAACI;EAAmB,CAAC,CAAC;AAAA;;AAEjK;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASY,kBAAkB,EAAEiE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}