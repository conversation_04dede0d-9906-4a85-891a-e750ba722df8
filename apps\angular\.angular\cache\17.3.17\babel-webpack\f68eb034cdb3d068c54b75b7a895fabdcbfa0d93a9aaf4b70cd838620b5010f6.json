{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, booleanAttribute, Directive, Optional, Inject, ContentChildren, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatRipple, MatPseudoCheckbox, MatCommonModule, MatRippleModule } from '@angular/material/core';\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nconst _c0 = [\"button\"];\nconst _c1 = [\"*\"];\nfunction MatButtonToggle_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction MatButtonToggle_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nconst MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS', {\n  providedIn: 'root',\n  factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    hideSingleSelectionIndicator: false,\n    hideMultipleSelectionIndicator: false\n  };\n}\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nconst MAT_BUTTON_TOGGLE_GROUP = new InjectionToken('MatButtonToggleGroup');\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatButtonToggleGroup),\n  multi: true\n};\n// Counter used to generate unique IDs.\nlet uniqueIdCounter = 0;\n/** Change event object emitted by button toggle. */\nclass MatButtonToggleChange {\n  constructor(/** The button toggle that emits the event. */\n  source, /** The value assigned to the button toggle. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\nclass MatButtonToggleGroup {\n  /** `name` attribute for the underlying `input` element. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._markButtonsForCheck();\n  }\n  /** Value of the toggle group. */\n  get value() {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    if (this.multiple) {\n      return selected.map(toggle => toggle.value);\n    }\n    return selected[0] ? selected[0].value : undefined;\n  }\n  set value(newValue) {\n    this._setSelectionByValue(newValue);\n    this.valueChange.emit(this.value);\n  }\n  /** Selected button toggles in the group. */\n  get selected() {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    return this.multiple ? selected : selected[0] || null;\n  }\n  /** Whether multiple button toggles can be selected. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    this._multiple = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether multiple button toggle group is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n  get hideMultipleSelectionIndicator() {\n    return this._hideMultipleSelectionIndicator;\n  }\n  set hideMultipleSelectionIndicator(value) {\n    this._hideMultipleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  constructor(_changeDetector, defaultOptions) {\n    this._changeDetector = _changeDetector;\n    this._multiple = false;\n    this._disabled = false;\n    /**\n     * The method to be called in order to update ngModel.\n     * Now `ngModel` binding is not supported in multiple selection mode.\n     */\n    this._controlValueAccessorChangeFn = () => {};\n    /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n    this._onTouched = () => {};\n    this._name = `mat-button-toggle-group-${uniqueIdCounter++}`;\n    /**\n     * Event that emits whenever the value of the group changes.\n     * Used to facilitate two-way data binding.\n     * @docs-private\n     */\n    this.valueChange = new EventEmitter();\n    /** Event emitted when the group's value changes. */\n    this.change = new EventEmitter();\n    this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n  }\n  ngAfterContentInit() {\n    this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value Value to be set to the model.\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(toggle) {\n    const event = new MatButtonToggleChange(toggle, this.value);\n    this._rawValue = event.value;\n    this._controlValueAccessorChangeFn(event.value);\n    this.change.emit(event);\n  }\n  /**\n   * Syncs a button toggle's selected state with the model value.\n   * @param toggle Toggle to be synced.\n   * @param select Whether the toggle should be selected.\n   * @param isUserInput Whether the change was a result of a user interaction.\n   * @param deferEvents Whether to defer emitting the change events.\n   */\n  _syncButtonToggle(toggle, select, isUserInput = false, deferEvents = false) {\n    // Deselect the currently-selected toggle, if we're in single-selection\n    // mode and the button being toggled isn't selected at the moment.\n    if (!this.multiple && this.selected && !toggle.checked) {\n      this.selected.checked = false;\n    }\n    if (this._selectionModel) {\n      if (select) {\n        this._selectionModel.select(toggle);\n      } else {\n        this._selectionModel.deselect(toggle);\n      }\n    } else {\n      deferEvents = true;\n    }\n    // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n    // the side-effect is that we may end up updating the model value out of sequence in others\n    // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n    if (deferEvents) {\n      Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n    } else {\n      this._updateModelValue(toggle, isUserInput);\n    }\n  }\n  /** Checks whether a button toggle is selected. */\n  _isSelected(toggle) {\n    return this._selectionModel && this._selectionModel.isSelected(toggle);\n  }\n  /** Determines whether a button toggle should be checked on init. */\n  _isPrechecked(toggle) {\n    if (typeof this._rawValue === 'undefined') {\n      return false;\n    }\n    if (this.multiple && Array.isArray(this._rawValue)) {\n      return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n    }\n    return toggle.value === this._rawValue;\n  }\n  /** Updates the selection state of the toggles in the group based on a value. */\n  _setSelectionByValue(value) {\n    this._rawValue = value;\n    if (!this._buttonToggles) {\n      return;\n    }\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Value must be an array in multiple-selection mode.');\n      }\n      this._clearSelection();\n      value.forEach(currentValue => this._selectValue(currentValue));\n    } else {\n      this._clearSelection();\n      this._selectValue(value);\n    }\n  }\n  /** Clears the selected toggles. */\n  _clearSelection() {\n    this._selectionModel.clear();\n    this._buttonToggles.forEach(toggle => toggle.checked = false);\n  }\n  /** Selects a value if there's a toggle that corresponds to it. */\n  _selectValue(value) {\n    const correspondingOption = this._buttonToggles.find(toggle => {\n      return toggle.value != null && toggle.value === value;\n    });\n    if (correspondingOption) {\n      correspondingOption.checked = true;\n      this._selectionModel.select(correspondingOption);\n    }\n  }\n  /** Syncs up the group's value with the model and emits the change event. */\n  _updateModelValue(toggle, isUserInput) {\n    // Only emit the change event for user input.\n    if (isUserInput) {\n      this._emitChangeEvent(toggle);\n    }\n    // Note: we emit this one no matter whether it was a user interaction, because\n    // it is used by Angular to sync up the two-way data binding.\n    this.valueChange.emit(this.value);\n  }\n  /** Marks all of the child button toggles to be checked. */\n  _markButtonsForCheck() {\n    this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n  }\n  static {\n    this.ɵfac = function MatButtonToggleGroup_Factory(t) {\n      return new (t || MatButtonToggleGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatButtonToggleGroup,\n      selectors: [[\"mat-button-toggle-group\"]],\n      contentQueries: function MatButtonToggleGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatButtonToggle, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonToggles = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"group\", 1, \"mat-button-toggle-group\"],\n      hostVars: 5,\n      hostBindings: function MatButtonToggleGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", ctx.disabled);\n          i0.ɵɵclassProp(\"mat-button-toggle-vertical\", ctx.vertical)(\"mat-button-toggle-group-appearance-standard\", ctx.appearance === \"standard\");\n        }\n      },\n      inputs: {\n        appearance: \"appearance\",\n        name: \"name\",\n        vertical: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"vertical\", \"vertical\", booleanAttribute],\n        value: \"value\",\n        multiple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiple\", \"multiple\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        hideSingleSelectionIndicator: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n        hideMultipleSelectionIndicator: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideMultipleSelectionIndicator\", \"hideMultipleSelectionIndicator\", booleanAttribute]\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        change: \"change\"\n      },\n      exportAs: [\"matButtonToggleGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n        provide: MAT_BUTTON_TOGGLE_GROUP,\n        useExisting: MatButtonToggleGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggleGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-button-toggle-group',\n      providers: [MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n        provide: MAT_BUTTON_TOGGLE_GROUP,\n        useExisting: MatButtonToggleGroup\n      }],\n      host: {\n        'role': 'group',\n        'class': 'mat-button-toggle-group',\n        '[attr.aria-disabled]': 'disabled',\n        '[class.mat-button-toggle-vertical]': 'vertical',\n        '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"'\n      },\n      exportAs: 'matButtonToggleGroup',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n    }]\n  }], {\n    _buttonToggles: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatButtonToggle), {\n        // Note that this would technically pick up toggles\n        // from nested groups, but that's not a case that we support.\n        descendants: true\n      }]\n    }],\n    appearance: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    vertical: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideMultipleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/** Single button inside of a toggle group. */\nclass MatButtonToggle {\n  /** Unique ID for the underlying `button` element. */\n  get buttonId() {\n    return `${this.id}-button`;\n  }\n  /** The appearance style of the button. */\n  get appearance() {\n    return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n  }\n  set appearance(value) {\n    this._appearance = value;\n  }\n  /** Whether the button is checked. */\n  get checked() {\n    return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n  }\n  set checked(value) {\n    if (value !== this._checked) {\n      this._checked = value;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the button is disabled. */\n  get disabled() {\n    return this._disabled || this.buttonToggleGroup && this.buttonToggleGroup.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  constructor(toggleGroup, _changeDetectorRef, _elementRef, _focusMonitor, defaultTabIndex, defaultOptions) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._checked = false;\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    this.ariaLabelledby = null;\n    this._disabled = false;\n    /** Event emitted when the group value changes. */\n    this.change = new EventEmitter();\n    const parsedTabIndex = Number(defaultTabIndex);\n    this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n    this.buttonToggleGroup = toggleGroup;\n    this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n  }\n  ngOnInit() {\n    const group = this.buttonToggleGroup;\n    this.id = this.id || `mat-button-toggle-${uniqueIdCounter++}`;\n    if (group) {\n      if (group._isPrechecked(this)) {\n        this.checked = true;\n      } else if (group._isSelected(this) !== this._checked) {\n        // As side effect of the circular dependency between the toggle group and the button,\n        // we may end up in a state where the button is supposed to be checked on init, but it\n        // isn't, because the checked value was assigned too early. This can happen when Ivy\n        // assigns the static input value before the `ngOnInit` has run.\n        group._syncButtonToggle(this, this._checked);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n  ngOnDestroy() {\n    const group = this.buttonToggleGroup;\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    // Remove the toggle from the selection once it's destroyed. Needs to happen\n    // on the next tick in order to avoid \"changed after checked\" errors.\n    if (group && group._isSelected(this)) {\n      group._syncButtonToggle(this, false, false, true);\n    }\n  }\n  /** Focuses the button. */\n  focus(options) {\n    this._buttonElement.nativeElement.focus(options);\n  }\n  /** Checks the button toggle due to an interaction with the underlying native button. */\n  _onButtonClick() {\n    const newChecked = this._isSingleSelector() ? true : !this._checked;\n    if (newChecked !== this._checked) {\n      this._checked = newChecked;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n        this.buttonToggleGroup._onTouched();\n      }\n    }\n    // Emit a change event when it's the single selector\n    this.change.emit(new MatButtonToggleChange(this, this.value));\n  }\n  /**\n   * Marks the button toggle as needing checking for change detection.\n   * This method is exposed because the parent button toggle group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When the group value changes, the button will not be notified.\n    // Use `markForCheck` to explicit update button toggle's status.\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Gets the name that should be assigned to the inner DOM node. */\n  _getButtonName() {\n    if (this._isSingleSelector()) {\n      return this.buttonToggleGroup.name;\n    }\n    return this.name || null;\n  }\n  /** Whether the toggle is in single selection mode. */\n  _isSingleSelector() {\n    return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n  }\n  static {\n    this.ɵfac = function MatButtonToggle_Factory(t) {\n      return new (t || MatButtonToggle)(i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_GROUP, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatButtonToggle,\n      selectors: [[\"mat-button-toggle\"]],\n      viewQuery: function MatButtonToggle_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonElement = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"presentation\", 1, \"mat-button-toggle\"],\n      hostVars: 12,\n      hostBindings: function MatButtonToggle_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatButtonToggle_focus_HostBindingHandler() {\n            return ctx.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"id\", ctx.id)(\"name\", null);\n          i0.ɵɵclassProp(\"mat-button-toggle-standalone\", !ctx.buttonToggleGroup)(\"mat-button-toggle-checked\", ctx.checked)(\"mat-button-toggle-disabled\", ctx.disabled)(\"mat-button-toggle-appearance-standard\", ctx.appearance === \"standard\");\n        }\n      },\n      inputs: {\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        id: \"id\",\n        name: \"name\",\n        value: \"value\",\n        tabIndex: \"tabIndex\",\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        appearance: \"appearance\",\n        checked: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"checked\", \"checked\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matButtonToggle\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 8,\n      vars: 11,\n      consts: [[\"button\", \"\"], [\"type\", \"button\", 1, \"mat-button-toggle-button\", \"mat-focus-indicator\", 3, \"click\", \"id\", \"disabled\"], [1, \"mat-button-toggle-label-content\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"mat-button-toggle-focus-overlay\"], [\"matRipple\", \"\", 1, \"mat-button-toggle-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n      template: function MatButtonToggle_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 1, 0);\n          i0.ɵɵlistener(\"click\", function MatButtonToggle_Template_button_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onButtonClick());\n          });\n          i0.ɵɵelementStart(2, \"span\", 2);\n          i0.ɵɵtemplate(3, MatButtonToggle_Conditional_3_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(4, MatButtonToggle_Conditional_4_Template, 1, 1, \"mat-pseudo-checkbox\", 3);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(6, \"span\", 4)(7, \"span\", 5);\n        }\n        if (rf & 2) {\n          const button_r3 = i0.ɵɵreference(1);\n          i0.ɵɵproperty(\"id\", ctx.buttonId)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-pressed\", ctx.checked)(\"name\", ctx._getButtonName())(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx.buttonToggleGroup && ctx.checked && !ctx.buttonToggleGroup.multiple && !ctx.buttonToggleGroup.hideSingleSelectionIndicator ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.buttonToggleGroup && ctx.checked && ctx.buttonToggleGroup.multiple && !ctx.buttonToggleGroup.hideMultipleSelectionIndicator ? 4 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleTrigger\", button_r3)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled);\n        }\n      },\n      dependencies: [MatRipple, MatPseudoCheckbox],\n      styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape);border:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-standard-button-toggle-selected-state-text-color )}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-legacy-button-toggle-selected-state-text-color )}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-legacy-button-toggle-disabled-state-text-color )}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color);background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font);font-size:var(--mat-standard-button-toggle-label-text-size);line-height:var(--mat-standard-button-toggle-label-text-line-height);font-weight:var(--mat-standard-button-toggle-label-text-weight);letter-spacing:var(--mat-standard-button-toggle-label-text-tracking)}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color)}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color);background-color:var(--mat-standard-button-toggle-selected-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-standard-button-toggle-disabled-selected-state-text-color )}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color)}.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity)}.mat-button-toggle-appearance-standard.cdk-keyboard-focused:not(.mat-button-toggle-disabled) .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity)}@media(hover: none){.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape);border-bottom-right-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape);border-bottom-left-radius:var(--mat-standard-button-toggle-shape)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-button-toggle',\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matButtonToggle',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n        '[class.mat-button-toggle-checked]': 'checked',\n        '[class.mat-button-toggle-disabled]': 'disabled',\n        '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n        'class': 'mat-button-toggle',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.id]': 'id',\n        '[attr.name]': 'null',\n        '(focus)': 'focus()',\n        'role': 'presentation'\n      },\n      standalone: true,\n      imports: [MatRipple, MatPseudoCheckbox],\n      template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.tabindex]=\\\"disabled ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"checked\\\"\\n        [disabled]=\\\"disabled || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <!-- Render checkmark at the beginning for single-selection. -->\\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <!-- Render checkmark at the beginning for multiple-selection. -->\\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\",\n      styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape);border:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-standard-button-toggle-selected-state-text-color )}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-legacy-button-toggle-selected-state-text-color )}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-legacy-button-toggle-disabled-state-text-color )}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color);background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font);font-size:var(--mat-standard-button-toggle-label-text-size);line-height:var(--mat-standard-button-toggle-label-text-line-height);font-weight:var(--mat-standard-button-toggle-label-text-weight);letter-spacing:var(--mat-standard-button-toggle-label-text-tracking)}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color)}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color);background-color:var(--mat-standard-button-toggle-selected-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-standard-button-toggle-disabled-selected-state-text-color )}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color)}.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity)}.mat-button-toggle-appearance-standard.cdk-keyboard-focused:not(.mat-button-toggle-disabled) .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity)}@media(hover: none){.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape);border-bottom-right-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape);border-bottom-left-radius:var(--mat-standard-button-toggle-shape)}\"]\n    }]\n  }], () => [{\n    type: MatButtonToggleGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_BUTTON_TOGGLE_GROUP]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n    }]\n  }], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    _buttonElement: [{\n      type: ViewChild,\n      args: ['button']\n    }],\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    appearance: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }]\n  });\n})();\nclass MatButtonToggleModule {\n  static {\n    this.ɵfac = function MatButtonToggleModule_Factory(t) {\n      return new (t || MatButtonToggleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatButtonToggleModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatRippleModule, MatButtonToggle, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n      exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, MAT_BUTTON_TOGGLE_GROUP, MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY, MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, MatButtonToggle, MatButtonToggleChange, MatButtonToggleGroup, MatButtonToggleModule };", "map": {"version": 3, "names": ["i1", "SelectionModel", "i0", "InjectionToken", "forwardRef", "EventEmitter", "booleanAttribute", "Directive", "Optional", "Inject", "ContentChildren", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "<PERSON><PERSON><PERSON><PERSON>", "MatPseudoCheckbox", "MatCommonModule", "MatRippleModule", "_c0", "_c1", "MatButtonToggle_Conditional_3_Template", "rf", "ctx", "ɵɵelement", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "disabled", "MatButtonToggle_Conditional_4_Template", "MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY", "hideSingleSelectionIndicator", "hideMultipleSelectionIndicator", "MAT_BUTTON_TOGGLE_GROUP", "MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR", "provide", "useExisting", "MatButtonToggleGroup", "multi", "uniqueIdCounter", "MatButtonToggleChange", "constructor", "source", "value", "name", "_name", "_markButtonsForCheck", "selected", "_selectionModel", "multiple", "map", "toggle", "undefined", "newValue", "_setSelectionByValue", "valueChange", "emit", "_multiple", "_disabled", "_hideSingleSelectionIndicator", "_hideMultipleSelectionIndicator", "_changeDetector", "defaultOptions", "_controlValueAccessorChangeFn", "_onTouched", "change", "appearance", "ngOnInit", "ngAfterContentInit", "select", "_buttonToggles", "filter", "checked", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "_emitChangeEvent", "event", "_rawValue", "_syncButtonToggle", "isUserInput", "deferEvents", "deselect", "Promise", "resolve", "then", "_updateModelValue", "_isSelected", "isSelected", "_isPrechecked", "Array", "isArray", "some", "ngDevMode", "Error", "_clearSelection", "for<PERSON>ach", "currentValue", "_selectValue", "clear", "correspondingOption", "find", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "MatButtonToggleGroup_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "contentQueries", "MatButtonToggleGroup_ContentQueries", "dirIndex", "ɵɵcontentQuery", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "hostVars", "hostBindings", "MatButtonToggleGroup_HostBindings", "ɵɵattribute", "ɵɵclassProp", "vertical", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵsetClassMetadata", "args", "selector", "providers", "host", "decorators", "descendants", "transform", "buttonId", "id", "buttonToggleGroup", "_appearance", "_checked", "_changeDetectorRef", "toggleGroup", "_elementRef", "_focusMonitor", "defaultTabIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedTabIndex", "Number", "tabIndex", "group", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "focus", "options", "_buttonElement", "nativeElement", "_onButtonClick", "newChecked", "_isSingleSelector", "_getButtonName", "MatButtonToggle_Factory", "ElementRef", "FocusMonitor", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatButtonToggle_Query", "ɵɵviewQuery", "first", "MatButtonToggle_HostBindings", "ɵɵlistener", "MatButtonToggle_focus_HostBindingHandler", "aria<PERSON><PERSON><PERSON>", "None", "disable<PERSON><PERSON><PERSON>", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatButtonToggle_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "Mat<PERSON><PERSON><PERSON>Toggle_Template_button_click_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵtemplate", "ɵɵprojection", "ɵɵelementEnd", "button_r3", "ɵɵreference", "ɵɵadvance", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "OnPush", "imports", "MatButtonToggleModule", "MatButtonToggleModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/material/fesm2022/button-toggle.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, booleanAttribute, Directive, Optional, Inject, ContentChildren, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatRipple, MatPseudoCheckbox, MatCommonModule, MatRippleModule } from '@angular/material/core';\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nconst MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS', {\n    providedIn: 'root',\n    factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY,\n});\nfunction MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        hideSingleSelectionIndicator: false,\n        hideMultipleSelectionIndicator: false,\n    };\n}\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nconst MAT_BUTTON_TOGGLE_GROUP = new InjectionToken('MatButtonToggleGroup');\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatButtonToggleGroup),\n    multi: true,\n};\n// Counter used to generate unique IDs.\nlet uniqueIdCounter = 0;\n/** Change event object emitted by button toggle. */\nclass MatButtonToggleChange {\n    constructor(\n    /** The button toggle that emits the event. */\n    source, \n    /** The value assigned to the button toggle. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\nclass MatButtonToggleGroup {\n    /** `name` attribute for the underlying `input` element. */\n    get name() {\n        return this._name;\n    }\n    set name(value) {\n        this._name = value;\n        this._markButtonsForCheck();\n    }\n    /** Value of the toggle group. */\n    get value() {\n        const selected = this._selectionModel ? this._selectionModel.selected : [];\n        if (this.multiple) {\n            return selected.map(toggle => toggle.value);\n        }\n        return selected[0] ? selected[0].value : undefined;\n    }\n    set value(newValue) {\n        this._setSelectionByValue(newValue);\n        this.valueChange.emit(this.value);\n    }\n    /** Selected button toggles in the group. */\n    get selected() {\n        const selected = this._selectionModel ? this._selectionModel.selected : [];\n        return this.multiple ? selected : selected[0] || null;\n    }\n    /** Whether multiple button toggles can be selected. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        this._multiple = value;\n        this._markButtonsForCheck();\n    }\n    /** Whether multiple button toggle group is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._markButtonsForCheck();\n    }\n    /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._markButtonsForCheck();\n    }\n    /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n    get hideMultipleSelectionIndicator() {\n        return this._hideMultipleSelectionIndicator;\n    }\n    set hideMultipleSelectionIndicator(value) {\n        this._hideMultipleSelectionIndicator = value;\n        this._markButtonsForCheck();\n    }\n    constructor(_changeDetector, defaultOptions) {\n        this._changeDetector = _changeDetector;\n        this._multiple = false;\n        this._disabled = false;\n        /**\n         * The method to be called in order to update ngModel.\n         * Now `ngModel` binding is not supported in multiple selection mode.\n         */\n        this._controlValueAccessorChangeFn = () => { };\n        /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n        this._onTouched = () => { };\n        this._name = `mat-button-toggle-group-${uniqueIdCounter++}`;\n        /**\n         * Event that emits whenever the value of the group changes.\n         * Used to facilitate two-way data binding.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        /** Event emitted when the group's value changes. */\n        this.change = new EventEmitter();\n        this.appearance =\n            defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n        this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n        this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n    }\n    ngAfterContentInit() {\n        this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value Value to be set to the model.\n     */\n    writeValue(value) {\n        this.value = value;\n        this._changeDetector.markForCheck();\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent(toggle) {\n        const event = new MatButtonToggleChange(toggle, this.value);\n        this._rawValue = event.value;\n        this._controlValueAccessorChangeFn(event.value);\n        this.change.emit(event);\n    }\n    /**\n     * Syncs a button toggle's selected state with the model value.\n     * @param toggle Toggle to be synced.\n     * @param select Whether the toggle should be selected.\n     * @param isUserInput Whether the change was a result of a user interaction.\n     * @param deferEvents Whether to defer emitting the change events.\n     */\n    _syncButtonToggle(toggle, select, isUserInput = false, deferEvents = false) {\n        // Deselect the currently-selected toggle, if we're in single-selection\n        // mode and the button being toggled isn't selected at the moment.\n        if (!this.multiple && this.selected && !toggle.checked) {\n            this.selected.checked = false;\n        }\n        if (this._selectionModel) {\n            if (select) {\n                this._selectionModel.select(toggle);\n            }\n            else {\n                this._selectionModel.deselect(toggle);\n            }\n        }\n        else {\n            deferEvents = true;\n        }\n        // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n        // the side-effect is that we may end up updating the model value out of sequence in others\n        // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n        if (deferEvents) {\n            Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n        }\n        else {\n            this._updateModelValue(toggle, isUserInput);\n        }\n    }\n    /** Checks whether a button toggle is selected. */\n    _isSelected(toggle) {\n        return this._selectionModel && this._selectionModel.isSelected(toggle);\n    }\n    /** Determines whether a button toggle should be checked on init. */\n    _isPrechecked(toggle) {\n        if (typeof this._rawValue === 'undefined') {\n            return false;\n        }\n        if (this.multiple && Array.isArray(this._rawValue)) {\n            return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n        }\n        return toggle.value === this._rawValue;\n    }\n    /** Updates the selection state of the toggles in the group based on a value. */\n    _setSelectionByValue(value) {\n        this._rawValue = value;\n        if (!this._buttonToggles) {\n            return;\n        }\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Value must be an array in multiple-selection mode.');\n            }\n            this._clearSelection();\n            value.forEach((currentValue) => this._selectValue(currentValue));\n        }\n        else {\n            this._clearSelection();\n            this._selectValue(value);\n        }\n    }\n    /** Clears the selected toggles. */\n    _clearSelection() {\n        this._selectionModel.clear();\n        this._buttonToggles.forEach(toggle => (toggle.checked = false));\n    }\n    /** Selects a value if there's a toggle that corresponds to it. */\n    _selectValue(value) {\n        const correspondingOption = this._buttonToggles.find(toggle => {\n            return toggle.value != null && toggle.value === value;\n        });\n        if (correspondingOption) {\n            correspondingOption.checked = true;\n            this._selectionModel.select(correspondingOption);\n        }\n    }\n    /** Syncs up the group's value with the model and emits the change event. */\n    _updateModelValue(toggle, isUserInput) {\n        // Only emit the change event for user input.\n        if (isUserInput) {\n            this._emitChangeEvent(toggle);\n        }\n        // Note: we emit this one no matter whether it was a user interaction, because\n        // it is used by Angular to sync up the two-way data binding.\n        this.valueChange.emit(this.value);\n    }\n    /** Marks all of the child button toggles to be checked. */\n    _markButtonsForCheck() {\n        this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggleGroup, deps: [{ token: i0.ChangeDetectorRef }, { token: MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatButtonToggleGroup, isStandalone: true, selector: \"mat-button-toggle-group\", inputs: { appearance: \"appearance\", name: \"name\", vertical: [\"vertical\", \"vertical\", booleanAttribute], value: \"value\", multiple: [\"multiple\", \"multiple\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], hideMultipleSelectionIndicator: [\"hideMultipleSelectionIndicator\", \"hideMultipleSelectionIndicator\", booleanAttribute] }, outputs: { valueChange: \"valueChange\", change: \"change\" }, host: { attributes: { \"role\": \"group\" }, properties: { \"attr.aria-disabled\": \"disabled\", \"class.mat-button-toggle-vertical\": \"vertical\", \"class.mat-button-toggle-group-appearance-standard\": \"appearance === \\\"standard\\\"\" }, classAttribute: \"mat-button-toggle-group\" }, providers: [\n            MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR,\n            { provide: MAT_BUTTON_TOGGLE_GROUP, useExisting: MatButtonToggleGroup },\n        ], queries: [{ propertyName: \"_buttonToggles\", predicate: i0.forwardRef(() => MatButtonToggle), descendants: true }], exportAs: [\"matButtonToggleGroup\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggleGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-button-toggle-group',\n                    providers: [\n                        MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR,\n                        { provide: MAT_BUTTON_TOGGLE_GROUP, useExisting: MatButtonToggleGroup },\n                    ],\n                    host: {\n                        'role': 'group',\n                        'class': 'mat-button-toggle-group',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[class.mat-button-toggle-vertical]': 'vertical',\n                        '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"',\n                    },\n                    exportAs: 'matButtonToggleGroup',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n                }] }], propDecorators: { _buttonToggles: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatButtonToggle), {\n                        // Note that this would technically pick up toggles\n                        // from nested groups, but that's not a case that we support.\n                        descendants: true,\n                    }]\n            }], appearance: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], vertical: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], valueChange: [{\n                type: Output\n            }], multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], change: [{\n                type: Output\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hideMultipleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/** Single button inside of a toggle group. */\nclass MatButtonToggle {\n    /** Unique ID for the underlying `button` element. */\n    get buttonId() {\n        return `${this.id}-button`;\n    }\n    /** The appearance style of the button. */\n    get appearance() {\n        return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n    }\n    set appearance(value) {\n        this._appearance = value;\n    }\n    /** Whether the button is checked. */\n    get checked() {\n        return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n    }\n    set checked(value) {\n        if (value !== this._checked) {\n            this._checked = value;\n            if (this.buttonToggleGroup) {\n                this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the button is disabled. */\n    get disabled() {\n        return this._disabled || (this.buttonToggleGroup && this.buttonToggleGroup.disabled);\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    constructor(toggleGroup, _changeDetectorRef, _elementRef, _focusMonitor, defaultTabIndex, defaultOptions) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._checked = false;\n        /**\n         * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n         */\n        this.ariaLabelledby = null;\n        this._disabled = false;\n        /** Event emitted when the group value changes. */\n        this.change = new EventEmitter();\n        const parsedTabIndex = Number(defaultTabIndex);\n        this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n        this.buttonToggleGroup = toggleGroup;\n        this.appearance =\n            defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    }\n    ngOnInit() {\n        const group = this.buttonToggleGroup;\n        this.id = this.id || `mat-button-toggle-${uniqueIdCounter++}`;\n        if (group) {\n            if (group._isPrechecked(this)) {\n                this.checked = true;\n            }\n            else if (group._isSelected(this) !== this._checked) {\n                // As side effect of the circular dependency between the toggle group and the button,\n                // we may end up in a state where the button is supposed to be checked on init, but it\n                // isn't, because the checked value was assigned too early. This can happen when Ivy\n                // assigns the static input value before the `ngOnInit` has run.\n                group._syncButtonToggle(this, this._checked);\n            }\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n        const group = this.buttonToggleGroup;\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        // Remove the toggle from the selection once it's destroyed. Needs to happen\n        // on the next tick in order to avoid \"changed after checked\" errors.\n        if (group && group._isSelected(this)) {\n            group._syncButtonToggle(this, false, false, true);\n        }\n    }\n    /** Focuses the button. */\n    focus(options) {\n        this._buttonElement.nativeElement.focus(options);\n    }\n    /** Checks the button toggle due to an interaction with the underlying native button. */\n    _onButtonClick() {\n        const newChecked = this._isSingleSelector() ? true : !this._checked;\n        if (newChecked !== this._checked) {\n            this._checked = newChecked;\n            if (this.buttonToggleGroup) {\n                this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n                this.buttonToggleGroup._onTouched();\n            }\n        }\n        // Emit a change event when it's the single selector\n        this.change.emit(new MatButtonToggleChange(this, this.value));\n    }\n    /**\n     * Marks the button toggle as needing checking for change detection.\n     * This method is exposed because the parent button toggle group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n        // When the group value changes, the button will not be notified.\n        // Use `markForCheck` to explicit update button toggle's status.\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Gets the name that should be assigned to the inner DOM node. */\n    _getButtonName() {\n        if (this._isSingleSelector()) {\n            return this.buttonToggleGroup.name;\n        }\n        return this.name || null;\n    }\n    /** Whether the toggle is in single selection mode. */\n    _isSingleSelector() {\n        return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggle, deps: [{ token: MAT_BUTTON_TOGGLE_GROUP, optional: true }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i1.FocusMonitor }, { token: 'tabindex', attribute: true }, { token: MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatButtonToggle, isStandalone: true, selector: \"mat-button-toggle\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], id: \"id\", name: \"name\", value: \"value\", tabIndex: \"tabIndex\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], appearance: \"appearance\", checked: [\"checked\", \"checked\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { change: \"change\" }, host: { attributes: { \"role\": \"presentation\" }, listeners: { \"focus\": \"focus()\" }, properties: { \"class.mat-button-toggle-standalone\": \"!buttonToggleGroup\", \"class.mat-button-toggle-checked\": \"checked\", \"class.mat-button-toggle-disabled\": \"disabled\", \"class.mat-button-toggle-appearance-standard\": \"appearance === \\\"standard\\\"\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.id\": \"id\", \"attr.name\": \"null\" }, classAttribute: \"mat-button-toggle\" }, viewQueries: [{ propertyName: \"_buttonElement\", first: true, predicate: [\"button\"], descendants: true }], exportAs: [\"matButtonToggle\"], ngImport: i0, template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.tabindex]=\\\"disabled ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"checked\\\"\\n        [disabled]=\\\"disabled || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <!-- Render checkmark at the beginning for single-selection. -->\\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <!-- Render checkmark at the beginning for multiple-selection. -->\\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\", styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape);border:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-standard-button-toggle-selected-state-text-color )}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-legacy-button-toggle-selected-state-text-color )}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-legacy-button-toggle-disabled-state-text-color )}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color);background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font);font-size:var(--mat-standard-button-toggle-label-text-size);line-height:var(--mat-standard-button-toggle-label-text-line-height);font-weight:var(--mat-standard-button-toggle-label-text-weight);letter-spacing:var(--mat-standard-button-toggle-label-text-tracking)}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color)}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color);background-color:var(--mat-standard-button-toggle-selected-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-standard-button-toggle-disabled-selected-state-text-color )}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color)}.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity)}.mat-button-toggle-appearance-standard.cdk-keyboard-focused:not(.mat-button-toggle-disabled) .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity)}@media(hover: none){.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape);border-bottom-right-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape);border-bottom-left-radius:var(--mat-standard-button-toggle-shape)}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggle, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-button-toggle', encapsulation: ViewEncapsulation.None, exportAs: 'matButtonToggle', changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n                        '[class.mat-button-toggle-checked]': 'checked',\n                        '[class.mat-button-toggle-disabled]': 'disabled',\n                        '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n                        'class': 'mat-button-toggle',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.id]': 'id',\n                        '[attr.name]': 'null',\n                        '(focus)': 'focus()',\n                        'role': 'presentation',\n                    }, standalone: true, imports: [MatRipple, MatPseudoCheckbox], template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.tabindex]=\\\"disabled ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"checked\\\"\\n        [disabled]=\\\"disabled || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <!-- Render checkmark at the beginning for single-selection. -->\\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <!-- Render checkmark at the beginning for multiple-selection. -->\\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\", styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape);border:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-standard-button-toggle-selected-state-text-color )}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var( --mat-legacy-button-toggle-selected-state-text-color )}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-legacy-button-toggle-disabled-state-text-color )}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color);background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font);font-size:var(--mat-standard-button-toggle-label-text-size);line-height:var(--mat-standard-button-toggle-label-text-line-height);font-weight:var(--mat-standard-button-toggle-label-text-weight);letter-spacing:var(--mat-standard-button-toggle-label-text-tracking)}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color)}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color);background-color:var(--mat-standard-button-toggle-selected-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var( --mat-standard-button-toggle-disabled-selected-state-text-color )}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color)}.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity)}.mat-button-toggle-appearance-standard.cdk-keyboard-focused:not(.mat-button-toggle-disabled) .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity)}@media(hover: none){.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape);border-bottom-right-radius:var(--mat-standard-button-toggle-shape)}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape);border-bottom-left-radius:var(--mat-standard-button-toggle-shape)}\"] }]\n        }], ctorParameters: () => [{ type: MatButtonToggleGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_BUTTON_TOGGLE_GROUP]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i1.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n                }] }], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], _buttonElement: [{\n                type: ViewChild,\n                args: ['button']\n            }], id: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], appearance: [{\n                type: Input\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], change: [{\n                type: Output\n            }] } });\n\nclass MatButtonToggleModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggleModule, imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle], exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggleModule, imports: [MatCommonModule, MatRippleModule, MatButtonToggle, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatButtonToggleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n                    exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, MAT_BUTTON_TOGGLE_GROUP, MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY, MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, MatButtonToggle, MatButtonToggleChange, MatButtonToggleGroup, MatButtonToggleModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9O,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;;AAEvG;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+PoGzB,EAAE,CAAA2B,SAAA,4BAoLk9D,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApLr9D5B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA8B,UAAA,aAAAF,MAAA,CAAAG,QAoL41D,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApL/1DzB,EAAE,CAAA2B,SAAA,4BAoLu4E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApL14E5B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA8B,UAAA,aAAAF,MAAA,CAAAG,QAoLixE,CAAC;EAAA;AAAA;AA/ax3E,MAAME,iCAAiC,GAAG,IAAIhC,cAAc,CAAC,mCAAmC,EAAE;EAC9FiC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF,SAASA,+CAA+CA,CAAA,EAAG;EACvD,OAAO;IACHC,4BAA4B,EAAE,KAAK;IACnCC,8BAA8B,EAAE;EACpC,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,IAAItC,cAAc,CAAC,sBAAsB,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA,MAAMuC,sCAAsC,GAAG;EAC3CC,OAAO,EAAExB,iBAAiB;EAC1ByB,WAAW,EAAExC,UAAU,CAAC,MAAMyC,oBAAoB,CAAC;EACnDC,KAAK,EAAE;AACX,CAAC;AACD;AACA,IAAIC,eAAe,GAAG,CAAC;AACvB;AACA,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA,MAAMN,oBAAoB,CAAC;EACvB;EACA,IAAIO,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACD,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,GAAGF,KAAK;IAClB,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAIH,KAAKA,CAAA,EAAG;IACR,MAAMI,QAAQ,GAAG,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACD,QAAQ,GAAG,EAAE;IAC1E,IAAI,IAAI,CAACE,QAAQ,EAAE;MACf,OAAOF,QAAQ,CAACG,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACR,KAAK,CAAC;IAC/C;IACA,OAAOI,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACJ,KAAK,GAAGS,SAAS;EACtD;EACA,IAAIT,KAAKA,CAACU,QAAQ,EAAE;IAChB,IAAI,CAACC,oBAAoB,CAACD,QAAQ,CAAC;IACnC,IAAI,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAACb,KAAK,CAAC;EACrC;EACA;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,MAAMA,QAAQ,GAAG,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACD,QAAQ,GAAG,EAAE;IAC1E,OAAO,IAAI,CAACE,QAAQ,GAAGF,QAAQ,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;EACzD;EACA;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACQ,SAAS;EACzB;EACA,IAAIR,QAAQA,CAACN,KAAK,EAAE;IAChB,IAAI,CAACc,SAAS,GAAGd,KAAK;IACtB,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAIrB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACiC,SAAS;EACzB;EACA,IAAIjC,QAAQA,CAACkB,KAAK,EAAE;IAChB,IAAI,CAACe,SAAS,GAAGf,KAAK;IACtB,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAIf,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAAC4B,6BAA6B;EAC7C;EACA,IAAI5B,4BAA4BA,CAACY,KAAK,EAAE;IACpC,IAAI,CAACgB,6BAA6B,GAAGhB,KAAK;IAC1C,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAId,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAAC4B,+BAA+B;EAC/C;EACA,IAAI5B,8BAA8BA,CAACW,KAAK,EAAE;IACtC,IAAI,CAACiB,+BAA+B,GAAGjB,KAAK;IAC5C,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACAL,WAAWA,CAACoB,eAAe,EAAEC,cAAc,EAAE;IACzC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACJ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACK,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C;IACA,IAAI,CAACC,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAACnB,KAAK,GAAG,2BAA2BN,eAAe,EAAE,EAAE;IAC3D;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgB,WAAW,GAAG,IAAI1D,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACoE,MAAM,GAAG,IAAIpE,YAAY,CAAC,CAAC;IAChC,IAAI,CAACqE,UAAU,GACXJ,cAAc,IAAIA,cAAc,CAACI,UAAU,GAAGJ,cAAc,CAACI,UAAU,GAAG,UAAU;IACxF,IAAI,CAACnC,4BAA4B,GAAG+B,cAAc,EAAE/B,4BAA4B,IAAI,KAAK;IACzF,IAAI,CAACC,8BAA8B,GAAG8B,cAAc,EAAE9B,8BAA8B,IAAI,KAAK;EACjG;EACAmC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACnB,eAAe,GAAG,IAAIvD,cAAc,CAAC,IAAI,CAACwD,QAAQ,EAAEG,SAAS,EAAE,KAAK,CAAC;EAC9E;EACAgB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,eAAe,CAACqB,MAAM,CAAC,GAAG,IAAI,CAACC,cAAc,CAACC,MAAM,CAACpB,MAAM,IAAIA,MAAM,CAACqB,OAAO,CAAC,CAAC;EACxF;EACA;AACJ;AACA;AACA;EACIC,UAAUA,CAAC9B,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACkB,eAAe,CAACa,YAAY,CAAC,CAAC;EACvC;EACA;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACb,6BAA6B,GAAGa,EAAE;EAC3C;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACZ,UAAU,GAAGY,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACtD,QAAQ,GAAGsD,UAAU;EAC9B;EACA;EACAC,gBAAgBA,CAAC7B,MAAM,EAAE;IACrB,MAAM8B,KAAK,GAAG,IAAIzC,qBAAqB,CAACW,MAAM,EAAE,IAAI,CAACR,KAAK,CAAC;IAC3D,IAAI,CAACuC,SAAS,GAAGD,KAAK,CAACtC,KAAK;IAC5B,IAAI,CAACoB,6BAA6B,CAACkB,KAAK,CAACtC,KAAK,CAAC;IAC/C,IAAI,CAACsB,MAAM,CAACT,IAAI,CAACyB,KAAK,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,iBAAiBA,CAAChC,MAAM,EAAEkB,MAAM,EAAEe,WAAW,GAAG,KAAK,EAAEC,WAAW,GAAG,KAAK,EAAE;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAACpC,QAAQ,IAAI,IAAI,CAACF,QAAQ,IAAI,CAACI,MAAM,CAACqB,OAAO,EAAE;MACpD,IAAI,CAACzB,QAAQ,CAACyB,OAAO,GAAG,KAAK;IACjC;IACA,IAAI,IAAI,CAACxB,eAAe,EAAE;MACtB,IAAIqB,MAAM,EAAE;QACR,IAAI,CAACrB,eAAe,CAACqB,MAAM,CAAClB,MAAM,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAACH,eAAe,CAACsC,QAAQ,CAACnC,MAAM,CAAC;MACzC;IACJ,CAAC,MACI;MACDkC,WAAW,GAAG,IAAI;IACtB;IACA;IACA;IACA;IACA,IAAIA,WAAW,EAAE;MACbE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,iBAAiB,CAACvC,MAAM,EAAEiC,WAAW,CAAC,CAAC;IAC7E,CAAC,MACI;MACD,IAAI,CAACM,iBAAiB,CAACvC,MAAM,EAAEiC,WAAW,CAAC;IAC/C;EACJ;EACA;EACAO,WAAWA,CAACxC,MAAM,EAAE;IAChB,OAAO,IAAI,CAACH,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC4C,UAAU,CAACzC,MAAM,CAAC;EAC1E;EACA;EACA0C,aAAaA,CAAC1C,MAAM,EAAE;IAClB,IAAI,OAAO,IAAI,CAAC+B,SAAS,KAAK,WAAW,EAAE;MACvC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACjC,QAAQ,IAAI6C,KAAK,CAACC,OAAO,CAAC,IAAI,CAACb,SAAS,CAAC,EAAE;MAChD,OAAO,IAAI,CAACA,SAAS,CAACc,IAAI,CAACrD,KAAK,IAAIQ,MAAM,CAACR,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKQ,MAAM,CAACR,KAAK,CAAC;IACvF;IACA,OAAOQ,MAAM,CAACR,KAAK,KAAK,IAAI,CAACuC,SAAS;EAC1C;EACA;EACA5B,oBAAoBA,CAACX,KAAK,EAAE;IACxB,IAAI,CAACuC,SAAS,GAAGvC,KAAK;IACtB,IAAI,CAAC,IAAI,CAAC2B,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,IAAI,CAACrB,QAAQ,IAAIN,KAAK,EAAE;MACxB,IAAI,CAACmD,KAAK,CAACC,OAAO,CAACpD,KAAK,CAAC,KAAK,OAAOsD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMC,KAAK,CAAC,oDAAoD,CAAC;MACrE;MACA,IAAI,CAACC,eAAe,CAAC,CAAC;MACtBxD,KAAK,CAACyD,OAAO,CAAEC,YAAY,IAAK,IAAI,CAACC,YAAY,CAACD,YAAY,CAAC,CAAC;IACpE,CAAC,MACI;MACD,IAAI,CAACF,eAAe,CAAC,CAAC;MACtB,IAAI,CAACG,YAAY,CAAC3D,KAAK,CAAC;IAC5B;EACJ;EACA;EACAwD,eAAeA,CAAA,EAAG;IACd,IAAI,CAACnD,eAAe,CAACuD,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACjC,cAAc,CAAC8B,OAAO,CAACjD,MAAM,IAAKA,MAAM,CAACqB,OAAO,GAAG,KAAM,CAAC;EACnE;EACA;EACA8B,YAAYA,CAAC3D,KAAK,EAAE;IAChB,MAAM6D,mBAAmB,GAAG,IAAI,CAAClC,cAAc,CAACmC,IAAI,CAACtD,MAAM,IAAI;MAC3D,OAAOA,MAAM,CAACR,KAAK,IAAI,IAAI,IAAIQ,MAAM,CAACR,KAAK,KAAKA,KAAK;IACzD,CAAC,CAAC;IACF,IAAI6D,mBAAmB,EAAE;MACrBA,mBAAmB,CAAChC,OAAO,GAAG,IAAI;MAClC,IAAI,CAACxB,eAAe,CAACqB,MAAM,CAACmC,mBAAmB,CAAC;IACpD;EACJ;EACA;EACAd,iBAAiBA,CAACvC,MAAM,EAAEiC,WAAW,EAAE;IACnC;IACA,IAAIA,WAAW,EAAE;MACb,IAAI,CAACJ,gBAAgB,CAAC7B,MAAM,CAAC;IACjC;IACA;IACA;IACA,IAAI,CAACI,WAAW,CAACC,IAAI,CAAC,IAAI,CAACb,KAAK,CAAC;EACrC;EACA;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACwB,cAAc,EAAE8B,OAAO,CAACjD,MAAM,IAAIA,MAAM,CAACuD,aAAa,CAAC,CAAC,CAAC;EAClE;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFxE,oBAAoB,EAA9B3C,EAAE,CAAAoH,iBAAA,CAA8CpH,EAAE,CAACqH,iBAAiB,GAApErH,EAAE,CAAAoH,iBAAA,CAA+EnF,iCAAiC;IAAA,CAA4D;EAAE;EAChR;IAAS,IAAI,CAACqF,IAAI,kBAD8EtH,EAAE,CAAAuH,iBAAA;MAAAC,IAAA,EACJ7E,oBAAoB;MAAA8E,SAAA;MAAAC,cAAA,WAAAC,oCAAAlG,EAAA,EAAAC,GAAA,EAAAkG,QAAA;QAAA,IAAAnG,EAAA;UADlBzB,EAAE,CAAA6H,cAAA,CAAAD,QAAA,EAIhBE,eAAe;QAAA;QAAA,IAAArG,EAAA;UAAA,IAAAsG,EAAA;UAJD/H,EAAE,CAAAgI,cAAA,CAAAD,EAAA,GAAF/H,EAAE,CAAAiI,WAAA,QAAAvG,GAAA,CAAAkD,cAAA,GAAAmD,EAAA;QAAA;MAAA;MAAAG,SAAA,WAConB,OAAO;MAAAC,QAAA;MAAAC,YAAA,WAAAC,kCAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD7nBzB,EAAE,CAAAsI,WAAA,kBAAA5G,GAAA,CAAAK,QAAA;UAAF/B,EAAE,CAAAuI,WAAA,+BAAA7G,GAAA,CAAA8G,QACe,CAAC,gDAAA9G,GAAA,CAAA8C,UAAA,KAAL,UAAI,CAAC;QAAA;MAAA;MAAAiE,MAAA;QAAAjE,UAAA;QAAAtB,IAAA;QAAAsF,QAAA,GADlBxI,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,0BACgKvI,gBAAgB;QAAA6C,KAAA;QAAAM,QAAA,GADlLvD,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,0BACsOvI,gBAAgB;QAAA2B,QAAA,GADxP/B,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,0BAC4RvI,gBAAgB;QAAAiC,4BAAA,GAD9SrC,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,kEAC8YvI,gBAAgB;QAAAkC,8BAAA,GADhatC,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,sEACsgBvI,gBAAgB;MAAA;MAAAwI,OAAA;QAAA/E,WAAA;QAAAU,MAAA;MAAA;MAAAsE,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADxhB/I,EAAE,CAAAgJ,kBAAA,CAC62B,CACv8BxG,sCAAsC,EACtC;QAAEC,OAAO,EAAEF,uBAAuB;QAAEG,WAAW,EAAEC;MAAqB,CAAC,CAC1E,GAJ2F3C,EAAE,CAAAiJ,wBAAA;IAAA,EAI2E;EAAE;AACnL;AACA;EAAA,QAAA1C,SAAA,oBAAAA,SAAA,KANoGvG,EAAE,CAAAkJ,iBAAA,CAMXvG,oBAAoB,EAAc,CAAC;IAClH6E,IAAI,EAAEnH,SAAS;IACf8I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,yBAAyB;MACnCC,SAAS,EAAE,CACP7G,sCAAsC,EACtC;QAAEC,OAAO,EAAEF,uBAAuB;QAAEG,WAAW,EAAEC;MAAqB,CAAC,CAC1E;MACD2G,IAAI,EAAE;QACF,MAAM,EAAE,OAAO;QACf,OAAO,EAAE,yBAAyB;QAClC,sBAAsB,EAAE,UAAU;QAClC,oCAAoC,EAAE,UAAU;QAChD,qDAAqD,EAAE;MAC3D,CAAC;MACDT,QAAQ,EAAE,sBAAsB;MAChCC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtB,IAAI,EAAExH,EAAE,CAACqH;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAE9D,SAAS;IAAE6F,UAAU,EAAE,CAAC;MAC/E/B,IAAI,EAAElH;IACV,CAAC,EAAE;MACCkH,IAAI,EAAEjH,MAAM;MACZ4I,IAAI,EAAE,CAAClH,iCAAiC;IAC5C,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE2C,cAAc,EAAE,CAAC;MAC1C4C,IAAI,EAAEhH,eAAe;MACrB2I,IAAI,EAAE,CAACjJ,UAAU,CAAC,MAAM4H,eAAe,CAAC,EAAE;QAClC;QACA;QACA0B,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAEhF,UAAU,EAAE,CAAC;MACbgD,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEyC,IAAI,EAAE,CAAC;MACPsE,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAE+H,QAAQ,EAAE,CAAC;MACXhB,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6C,KAAK,EAAE,CAAC;MACRuE,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEoD,WAAW,EAAE,CAAC;MACd2D,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAE6C,QAAQ,EAAE,CAAC;MACXiE,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2B,QAAQ,EAAE,CAAC;MACXyF,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmE,MAAM,EAAE,CAAC;MACTiD,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAE2B,4BAA4B,EAAE,CAAC;MAC/BmF,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkC,8BAA8B,EAAE,CAAC;MACjCkF,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM0H,eAAe,CAAC;EAClB;EACA,IAAI4B,QAAQA,CAAA,EAAG;IACX,OAAO,GAAG,IAAI,CAACC,EAAE,SAAS;EAC9B;EACA;EACA,IAAInF,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACoF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACpF,UAAU,GAAG,IAAI,CAACqF,WAAW;EACxF;EACA,IAAIrF,UAAUA,CAACvB,KAAK,EAAE;IAClB,IAAI,CAAC4G,WAAW,GAAG5G,KAAK;EAC5B;EACA;EACA,IAAI6B,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8E,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC3D,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC6D,QAAQ;EAC5F;EACA,IAAIhF,OAAOA,CAAC7B,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAAC6G,QAAQ,EAAE;MACzB,IAAI,CAACA,QAAQ,GAAG7G,KAAK;MACrB,IAAI,IAAI,CAAC2G,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACnE,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACqE,QAAQ,CAAC;MACjE;MACA,IAAI,CAACC,kBAAkB,CAAC/E,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACA,IAAIjD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACiC,SAAS,IAAK,IAAI,CAAC4F,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC7H,QAAS;EACxF;EACA,IAAIA,QAAQA,CAACkB,KAAK,EAAE;IAChB,IAAI,CAACe,SAAS,GAAGf,KAAK;EAC1B;EACAF,WAAWA,CAACiH,WAAW,EAAED,kBAAkB,EAAEE,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAE/F,cAAc,EAAE;IACtG,IAAI,CAAC2F,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACJ,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACM,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACpG,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACO,MAAM,GAAG,IAAIpE,YAAY,CAAC,CAAC;IAChC,MAAMkK,cAAc,GAAGC,MAAM,CAACH,eAAe,CAAC;IAC9C,IAAI,CAACI,QAAQ,GAAGF,cAAc,IAAIA,cAAc,KAAK,CAAC,GAAGA,cAAc,GAAG,IAAI;IAC9E,IAAI,CAACT,iBAAiB,GAAGI,WAAW;IACpC,IAAI,CAACxF,UAAU,GACXJ,cAAc,IAAIA,cAAc,CAACI,UAAU,GAAGJ,cAAc,CAACI,UAAU,GAAG,UAAU;EAC5F;EACAC,QAAQA,CAAA,EAAG;IACP,MAAM+F,KAAK,GAAG,IAAI,CAACZ,iBAAiB;IACpC,IAAI,CAACD,EAAE,GAAG,IAAI,CAACA,EAAE,IAAI,qBAAqB9G,eAAe,EAAE,EAAE;IAC7D,IAAI2H,KAAK,EAAE;MACP,IAAIA,KAAK,CAACrE,aAAa,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,CAACrB,OAAO,GAAG,IAAI;MACvB,CAAC,MACI,IAAI0F,KAAK,CAACvE,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC6D,QAAQ,EAAE;QAChD;QACA;QACA;QACA;QACAU,KAAK,CAAC/E,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACqE,QAAQ,CAAC;MAChD;IACJ;EACJ;EACAW,eAAeA,CAAA,EAAG;IACd,IAAI,CAACP,aAAa,CAACQ,OAAO,CAAC,IAAI,CAACT,WAAW,EAAE,IAAI,CAAC;EACtD;EACAU,WAAWA,CAAA,EAAG;IACV,MAAMH,KAAK,GAAG,IAAI,CAACZ,iBAAiB;IACpC,IAAI,CAACM,aAAa,CAACU,cAAc,CAAC,IAAI,CAACX,WAAW,CAAC;IACnD;IACA;IACA,IAAIO,KAAK,IAAIA,KAAK,CAACvE,WAAW,CAAC,IAAI,CAAC,EAAE;MAClCuE,KAAK,CAAC/E,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;IACrD;EACJ;EACA;EACAoF,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACC,cAAc,CAACC,aAAa,CAACH,KAAK,CAACC,OAAO,CAAC;EACpD;EACA;EACAG,cAAcA,CAAA,EAAG;IACb,MAAMC,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAACrB,QAAQ;IACnE,IAAIoB,UAAU,KAAK,IAAI,CAACpB,QAAQ,EAAE;MAC9B,IAAI,CAACA,QAAQ,GAAGoB,UAAU;MAC1B,IAAI,IAAI,CAACtB,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACnE,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACqE,QAAQ,EAAE,IAAI,CAAC;QACnE,IAAI,CAACF,iBAAiB,CAACtF,UAAU,CAAC,CAAC;MACvC;IACJ;IACA;IACA,IAAI,CAACC,MAAM,CAACT,IAAI,CAAC,IAAIhB,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAACG,KAAK,CAAC,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACI+D,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,IAAI,CAAC+C,kBAAkB,CAAC/E,YAAY,CAAC,CAAC;EAC1C;EACA;EACAoG,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACD,iBAAiB,CAAC,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACvB,iBAAiB,CAAC1G,IAAI;IACtC;IACA,OAAO,IAAI,CAACA,IAAI,IAAI,IAAI;EAC5B;EACA;EACAiI,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACvB,iBAAiB,IAAI,CAAC,IAAI,CAACA,iBAAiB,CAACrG,QAAQ;EACrE;EACA;IAAS,IAAI,CAAC0D,IAAI,YAAAoE,wBAAAlE,CAAA;MAAA,YAAAA,CAAA,IAAwFW,eAAe,EAnLzB9H,EAAE,CAAAoH,iBAAA,CAmLyC7E,uBAAuB,MAnLlEvC,EAAE,CAAAoH,iBAAA,CAmL6FpH,EAAE,CAACqH,iBAAiB,GAnLnHrH,EAAE,CAAAoH,iBAAA,CAmL8HpH,EAAE,CAACsL,UAAU,GAnL7ItL,EAAE,CAAAoH,iBAAA,CAmLwJtH,EAAE,CAACyL,YAAY,GAnLzKvL,EAAE,CAAAwL,iBAAA,CAmLoL,UAAU,GAnLhMxL,EAAE,CAAAoH,iBAAA,CAmL4NnF,iCAAiC;IAAA,CAA4D;EAAE;EAC7Z;IAAS,IAAI,CAACwJ,IAAI,kBApL8EzL,EAAE,CAAA0L,iBAAA;MAAAlE,IAAA,EAoLJM,eAAe;MAAAL,SAAA;MAAAkE,SAAA,WAAAC,sBAAAnK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApLbzB,EAAE,CAAA6L,WAAA,CAAAvK,GAAA;QAAA;QAAA,IAAAG,EAAA;UAAA,IAAAsG,EAAA;UAAF/H,EAAE,CAAAgI,cAAA,CAAAD,EAAA,GAAF/H,EAAE,CAAAiI,WAAA,QAAAvG,GAAA,CAAAqJ,cAAA,GAAAhD,EAAA,CAAA+D,KAAA;QAAA;MAAA;MAAA5D,SAAA,WAoL8e,cAAc;MAAAC,QAAA;MAAAC,YAAA,WAAA2D,6BAAAtK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApL9fzB,EAAE,CAAAgM,UAAA,mBAAAC,yCAAA;YAAA,OAoLJvK,GAAA,CAAAmJ,KAAA,CAAM,CAAC;UAAA,CAAO,CAAC;QAAA;QAAA,IAAApJ,EAAA;UApLbzB,EAAE,CAAAsI,WAAA,eAoLJ,IAAI,qBAAJ,IAAI,QAAA5G,GAAA,CAAAiI,EAAA,UAAJ,IAAI;UApLF3J,EAAE,CAAAuI,WAAA,kCAAA7G,GAAA,CAAAkI,iBAoLU,CAAC,8BAAAlI,GAAA,CAAAoD,OAAD,CAAC,+BAAApD,GAAA,CAAAK,QAAD,CAAC,0CAAAL,GAAA,CAAA8C,UAAA,eAAD,CAAC;QAAA;MAAA;MAAAiE,MAAA;QAAAyD,SAAA,GApLblM,EAAE,CAAA0I,YAAA,CAAAyD,IAAA;QAAA/B,cAAA,GAAFpK,EAAE,CAAA0I,YAAA,CAAAyD,IAAA;QAAAxC,EAAA;QAAAzG,IAAA;QAAAD,KAAA;QAAAsH,QAAA;QAAA6B,aAAA,GAAFpM,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,oCAoLyRvI,gBAAgB;QAAAoE,UAAA;QAAAM,OAAA,GApL3S9E,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,wBAoLsWvI,gBAAgB;QAAA2B,QAAA,GApLxX/B,EAAE,CAAA0I,YAAA,CAAAC,0BAAA,0BAoL4ZvI,gBAAgB;MAAA;MAAAwI,OAAA;QAAArE,MAAA;MAAA;MAAAsE,QAAA;MAAAC,UAAA;MAAAC,QAAA,GApL9a/I,EAAE,CAAAiJ,wBAAA,EAAFjJ,EAAE,CAAAqM,mBAAA;MAAAC,kBAAA,EAAA/K,GAAA;MAAAgL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAlL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAmL,GAAA,GAAF5M,EAAE,CAAA6M,gBAAA;UAAF7M,EAAE,CAAA8M,eAAA;UAAF9M,EAAE,CAAA+M,cAAA,kBAoLm/C,CAAC;UApLt/C/M,EAAE,CAAAgM,UAAA,mBAAAgB,iDAAA;YAAFhN,EAAE,CAAAiN,aAAA,CAAAL,GAAA;YAAA,OAAF5M,EAAE,CAAAkN,WAAA,CAoLi+CxL,GAAA,CAAAuJ,cAAA,CAAe,CAAC;UAAA,CAAC,CAAC;UApLr/CjL,EAAE,CAAA+M,cAAA,aAoLuiD,CAAC;UApL1iD/M,EAAE,CAAAmN,UAAA,IAAA3L,sCAAA,gCAoLyuD,CAAC,IAAAQ,sCAAA,gCAAob,CAAC;UApLjqEhC,EAAE,CAAAoN,YAAA,EAoL66E,CAAC;UApLh7EpN,EAAE,CAAAqN,YAAA,CAoLw7E,CAAC,CAAU,CAAC;UApLt8ErN,EAAE,CAAA2B,SAAA,aAoL8/E,CAAC,aAAmK,CAAC;QAAA;QAAA,IAAAF,EAAA;UAAA,MAAA6L,SAAA,GApLrqFtN,EAAE,CAAAuN,WAAA;UAAFvN,EAAE,CAAA8B,UAAA,OAAAJ,GAAA,CAAAgI,QAoL8rC,CAAC,aAAAhI,GAAA,CAAAK,QAAA,QAAuI,CAAC;UApLz0C/B,EAAE,CAAAsI,WAAA,aAAA5G,GAAA,CAAAK,QAAA,QAAAL,GAAA,CAAA6I,QAAA,kBAAA7I,GAAA,CAAAoD,OAAA,UAAApD,GAAA,CAAA0J,cAAA,kBAAA1J,GAAA,CAAAwK,SAAA,qBAAAxK,GAAA,CAAA0I,cAAA;UAAFpK,EAAE,CAAAwN,SAAA,EAoLy9D,CAAC;UApL59DxN,EAAE,CAAAyN,aAAA,IAAA/L,GAAA,CAAAkI,iBAAA,IAAAlI,GAAA,CAAAoD,OAAA,KAAApD,GAAA,CAAAkI,iBAAA,CAAArG,QAAA,KAAA7B,GAAA,CAAAkI,iBAAA,CAAAvH,4BAAA,SAoLy9D,CAAC;UApL59DrC,EAAE,CAAAwN,SAAA,CAoL84E,CAAC;UApLj5ExN,EAAE,CAAAyN,aAAA,IAAA/L,GAAA,CAAAkI,iBAAA,IAAAlI,GAAA,CAAAoD,OAAA,IAAApD,GAAA,CAAAkI,iBAAA,CAAArG,QAAA,KAAA7B,GAAA,CAAAkI,iBAAA,CAAAtH,8BAAA,SAoL84E,CAAC;UApLj5EtC,EAAE,CAAAwN,SAAA,EAoLslF,CAAC;UApLzlFxN,EAAE,CAAA8B,UAAA,qBAAAwL,SAoLslF,CAAC,sBAAA5L,GAAA,CAAA0K,aAAA,IAAA1K,GAAA,CAAAK,QAAiE,CAAC;QAAA;MAAA;MAAA2L,YAAA,GAA2qPxM,SAAS,EAAwPC,iBAAiB;MAAAwM,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkL;EAAE;AACh3V;AACA;EAAA,QAAAtH,SAAA,oBAAAA,SAAA,KAtLoGvG,EAAE,CAAAkJ,iBAAA,CAsLXpB,eAAe,EAAc,CAAC;IAC7GN,IAAI,EAAE7G,SAAS;IACfwI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEwE,aAAa,EAAEhN,iBAAiB,CAACuL,IAAI;MAAEtD,QAAQ,EAAE,iBAAiB;MAAEgF,eAAe,EAAEhN,uBAAuB,CAACiN,MAAM;MAAExE,IAAI,EAAE;QACvJ,sCAAsC,EAAE,oBAAoB;QAC5D,mCAAmC,EAAE,SAAS;QAC9C,oCAAoC,EAAE,UAAU;QAChD,+CAA+C,EAAE,2BAA2B;QAC5E,OAAO,EAAE,mBAAmB;QAC5B,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,WAAW,EAAE,IAAI;QACjB,aAAa,EAAE,MAAM;QACrB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE;MACZ,CAAC;MAAER,UAAU,EAAE,IAAI;MAAEiF,OAAO,EAAE,CAAC7M,SAAS,EAAEC,iBAAiB,CAAC;MAAEuL,QAAQ,EAAE,kmDAAkmD;MAAEiB,MAAM,EAAE,CAAC,smPAAsmP;IAAE,CAAC;EAC1yS,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnG,IAAI,EAAE7E,oBAAoB;IAAE4G,UAAU,EAAE,CAAC;MAC1D/B,IAAI,EAAElH;IACV,CAAC,EAAE;MACCkH,IAAI,EAAEjH,MAAM;MACZ4I,IAAI,EAAE,CAAC5G,uBAAuB;IAClC,CAAC;EAAE,CAAC,EAAE;IAAEiF,IAAI,EAAExH,EAAE,CAACqH;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAExH,EAAE,CAACsL;EAAW,CAAC,EAAE;IAAE9D,IAAI,EAAE1H,EAAE,CAACyL;EAAa,CAAC,EAAE;IAAE/D,IAAI,EAAE9D,SAAS;IAAE6F,UAAU,EAAE,CAAC;MACtH/B,IAAI,EAAE1G,SAAS;MACfqI,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAE3B,IAAI,EAAE9D,SAAS;IAAE6F,UAAU,EAAE,CAAC;MAClC/B,IAAI,EAAElH;IACV,CAAC,EAAE;MACCkH,IAAI,EAAEjH,MAAM;MACZ4I,IAAI,EAAE,CAAClH,iCAAiC;IAC5C,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEiK,SAAS,EAAE,CAAC;MACrC1E,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEiB,cAAc,EAAE,CAAC;MACjB5C,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE4B,cAAc,EAAE,CAAC;MACjBvD,IAAI,EAAEzG,SAAS;MACfoI,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEQ,EAAE,EAAE,CAAC;MACLnC,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEyC,IAAI,EAAE,CAAC;MACPsE,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEwC,KAAK,EAAE,CAAC;MACRuE,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAE8J,QAAQ,EAAE,CAAC;MACX/C,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAE2L,aAAa,EAAE,CAAC;MAChB5E,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoE,UAAU,EAAE,CAAC;MACbgD,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEqE,OAAO,EAAE,CAAC;MACV0C,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2B,QAAQ,EAAE,CAAC;MACXyF,IAAI,EAAE/G,KAAK;MACX0I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAErJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmE,MAAM,EAAE,CAAC;MACTiD,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsN,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAC/G,IAAI,YAAAgH,8BAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAwF6G,qBAAqB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACE,IAAI,kBApP8ElO,EAAE,CAAAmO,gBAAA;MAAA3G,IAAA,EAoPSwG;IAAqB,EAA0J;EAAE;EAC5R;IAAS,IAAI,CAACI,IAAI,kBArP8EpO,EAAE,CAAAqO,gBAAA;MAAAN,OAAA,GAqP0C3M,eAAe,EAAEC,eAAe,EAAEyG,eAAe,EAAE1G,eAAe;IAAA,EAAI;EAAE;AACxN;AACA;EAAA,QAAAmF,SAAA,oBAAAA,SAAA,KAvPoGvG,EAAE,CAAAkJ,iBAAA,CAuPX8E,qBAAqB,EAAc,CAAC;IACnHxG,IAAI,EAAExG,QAAQ;IACdmI,IAAI,EAAE,CAAC;MACC4E,OAAO,EAAE,CAAC3M,eAAe,EAAEC,eAAe,EAAEsB,oBAAoB,EAAEmF,eAAe,CAAC;MAClFwG,OAAO,EAAE,CAAClN,eAAe,EAAEuB,oBAAoB,EAAEmF,eAAe;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS7F,iCAAiC,EAAEM,uBAAuB,EAAEH,+CAA+C,EAAEI,sCAAsC,EAAEsF,eAAe,EAAEhF,qBAAqB,EAAEH,oBAAoB,EAAEqL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}