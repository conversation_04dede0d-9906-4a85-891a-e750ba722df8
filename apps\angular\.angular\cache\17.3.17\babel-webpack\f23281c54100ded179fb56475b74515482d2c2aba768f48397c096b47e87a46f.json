{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ar-LB\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 0) return 0;\n    if (n === 1) return 1;\n    if (n === 2) return 2;\n    if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10) return 3;\n    if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99) return 4;\n    return 5;\n  }\n  exports.default = ['ar-LB', [['ص', 'م'], u, u], [['ص', 'م'], u, ['صباحًا', 'مساءً']], [['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'], ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'], u, ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت']], u, [['ك', 'ش', 'آ', 'ن', 'أ', 'ح', 'ت', 'آ', 'أ', 'ت', 'ت', 'ك'], ['كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران', 'تموز', 'آب', 'أيلول', 'تشرين الأول', 'تشرين الثاني', 'كانون الأول'], u], u, [['ق.م', 'م'], u, ['قبل الميلاد', 'ميلادي']], 1, [6, 0], ['d\\u200f/M\\u200f/y', 'dd\\u200f/MM\\u200f/y', 'd MMMM y', 'EEEE، d MMMM y'], ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'], ['{1} {0}', u, u, u], [',', '.', ';', '\\u200e%\\u200e', '\\u200e+', '\\u200e-', 'E', '×', '‰', '∞', 'ليس رقمًا', ':'], ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'], 'LBP', 'ل.ل.\\u200f', 'جنيه لبناني', {\n    'AED': ['د.إ.\\u200f'],\n    'ARS': [u, 'AR$'],\n    'AUD': ['AU$'],\n    'BBD': [u, 'BB$'],\n    'BHD': ['د.ب.\\u200f'],\n    'BMD': [u, 'BM$'],\n    'BND': [u, 'BN$'],\n    'BSD': [u, 'BS$'],\n    'BZD': [u, 'BZ$'],\n    'CAD': ['CA$'],\n    'CLP': [u, 'CL$'],\n    'CNY': ['CN¥'],\n    'COP': [u, 'CO$'],\n    'CUP': [u, 'CU$'],\n    'DOP': [u, 'DO$'],\n    'DZD': ['د.ج.\\u200f'],\n    'EGP': ['ج.م.\\u200f', 'E£'],\n    'FJD': [u, 'FJ$'],\n    'GBP': ['UK£'],\n    'GYD': [u, 'GY$'],\n    'HKD': ['HK$'],\n    'IQD': ['د.ع.\\u200f'],\n    'IRR': ['ر.إ.'],\n    'JMD': [u, 'JM$'],\n    'JOD': ['د.أ.\\u200f'],\n    'JPY': ['JP¥'],\n    'KWD': ['د.ك.\\u200f'],\n    'KYD': [u, 'KY$'],\n    'LBP': ['ل.ل.\\u200f', 'L£'],\n    'LRD': [u, '$LR'],\n    'LYD': ['د.ل.\\u200f'],\n    'MAD': ['د.م.\\u200f'],\n    'MRU': ['أ.م.'],\n    'MXN': ['MX$'],\n    'NZD': ['NZ$'],\n    'OMR': ['ر.ع.\\u200f'],\n    'QAR': ['ر.ق.\\u200f'],\n    'SAR': ['ر.س.\\u200f'],\n    'SBD': [u, 'SB$'],\n    'SDD': ['د.س.\\u200f'],\n    'SRD': [u, 'SR$'],\n    'SYP': ['ل.س.\\u200f', '£'],\n    'THB': ['฿'],\n    'TND': ['د.ت.\\u200f'],\n    'TTD': [u, 'TT$'],\n    'TWD': ['NT$'],\n    'USD': ['US$'],\n    'UYU': [u, 'UY$'],\n    'XXX': ['***'],\n    'YER': ['ر.ي.\\u200f']\n  }, 'rtl', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "Math", "floor", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/ar-LB.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/ar-LB\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 0)\n            return 0;\n        if (n === 1)\n            return 1;\n        if (n === 2)\n            return 2;\n        if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10)\n            return 3;\n        if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99)\n            return 4;\n        return 5;\n    }\n    exports.default = [\n        'ar-LB',\n        [['ص', 'م'], u, u],\n        [['ص', 'م'], u, ['صباحًا', 'مساءً']],\n        [\n            ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n            ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'], u,\n            ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت']\n        ],\n        u,\n        [\n            ['ك', 'ش', 'آ', 'ن', 'أ', 'ح', 'ت', 'آ', 'أ', 'ت', 'ت', 'ك'],\n            [\n                'كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران', 'تموز', 'آب', 'أيلول',\n                'تشرين الأول', 'تشرين الثاني', 'كانون الأول'\n            ],\n            u\n        ],\n        u,\n        [['ق.م', 'م'], u, ['قبل الميلاد', 'ميلادي']],\n        1,\n        [6, 0],\n        ['d\\u200f/M\\u200f/y', 'dd\\u200f/MM\\u200f/y', 'd MMMM y', 'EEEE، d MMMM y'],\n        ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'],\n        ['{1} {0}', u, u, u],\n        [',', '.', ';', '\\u200e%\\u200e', '\\u200e+', '\\u200e-', 'E', '×', '‰', '∞', 'ليس رقمًا', ':'],\n        ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'],\n        'LBP',\n        'ل.ل.\\u200f',\n        'جنيه لبناني',\n        {\n            'AED': ['د.إ.\\u200f'],\n            'ARS': [u, 'AR$'],\n            'AUD': ['AU$'],\n            'BBD': [u, 'BB$'],\n            'BHD': ['د.ب.\\u200f'],\n            'BMD': [u, 'BM$'],\n            'BND': [u, 'BN$'],\n            'BSD': [u, 'BS$'],\n            'BZD': [u, 'BZ$'],\n            'CAD': ['CA$'],\n            'CLP': [u, 'CL$'],\n            'CNY': ['CN¥'],\n            'COP': [u, 'CO$'],\n            'CUP': [u, 'CU$'],\n            'DOP': [u, 'DO$'],\n            'DZD': ['د.ج.\\u200f'],\n            'EGP': ['ج.م.\\u200f', 'E£'],\n            'FJD': [u, 'FJ$'],\n            'GBP': ['UK£'],\n            'GYD': [u, 'GY$'],\n            'HKD': ['HK$'],\n            'IQD': ['د.ع.\\u200f'],\n            'IRR': ['ر.إ.'],\n            'JMD': [u, 'JM$'],\n            'JOD': ['د.أ.\\u200f'],\n            'JPY': ['JP¥'],\n            'KWD': ['د.ك.\\u200f'],\n            'KYD': [u, 'KY$'],\n            'LBP': ['ل.ل.\\u200f', 'L£'],\n            'LRD': [u, '$LR'],\n            'LYD': ['د.ل.\\u200f'],\n            'MAD': ['د.م.\\u200f'],\n            'MRU': ['أ.م.'],\n            'MXN': ['MX$'],\n            'NZD': ['NZ$'],\n            'OMR': ['ر.ع.\\u200f'],\n            'QAR': ['ر.ق.\\u200f'],\n            'SAR': ['ر.س.\\u200f'],\n            'SBD': [u, 'SB$'],\n            'SDD': ['د.س.\\u200f'],\n            'SRD': [u, 'SR$'],\n            'SYP': ['ل.س.\\u200f', '£'],\n            'THB': ['฿'],\n            'TND': ['د.ت.\\u200f'],\n            'TTD': [u, 'TT$'],\n            'TWD': ['NT$'],\n            'USD': ['US$'],\n            'UYU': [u, 'UY$'],\n            'XXX': ['***'],\n            'YER': ['ر.ي.\\u200f']\n        },\n        'rtl',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC5E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,IAAIA,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,EAChE,OAAO,CAAC;IACZ,IAAIA,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,EACjE,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACc,OAAO,GAAG,CACd,OAAO,EACP,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAEL,CAAC,EAAEA,CAAC,CAAC,EAClB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAEA,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EACpC,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACnC,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAEA,CAAC,EAC5E,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAC9D,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAChF,aAAa,EAAE,cAAc,EAAE,aAAa,CAC/C,EACDA,CAAC,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAEA,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAC5C,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAC1E,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EACxD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,CAAC,EAC5F,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,YAAY,EACZ,aAAa,EACb;IACI,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC;IAC3B,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC;IAC3B,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,YAAY,EAAE,GAAG,CAAC;IAC1B,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,YAAY,CAAC;IACrB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,YAAY;EACxB,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}