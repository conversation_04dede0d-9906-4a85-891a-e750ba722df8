{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { isNullOrUndefined, objFor<PERSON><PERSON><PERSON><PERSON>, throwError, toISOString } from \"@microsoft/applicationinsights-core-js\";\nimport { strIkey, strNotSpecified } from \"./Constants\";\nimport { dataSanitizeString } from \"./Telemetry/Common/DataSanitizer\";\nimport { _DYN_NAME } from \"./__DynamicConstants\";\n/**\r\n * Create a telemetry item that the 1DS channel understands\r\n * @param item domain specific properties; part B\r\n * @param baseType telemetry item type. ie PageViewData\r\n * @param envelopeName name of the envelope. ie Microsoft.ApplicationInsights.<instrumentation key>.PageView\r\n * @param customProperties user defined custom properties; part C\r\n * @param systemProperties system properties that are added to the context; part A\r\n * @returns ITelemetryItem that is sent to channel\r\n */\nexport function createTelemetryItem(item, baseType, envelopeName, logger, customProperties, systemProperties) {\n  var _a;\n  envelopeName = dataSanitizeString(logger, envelopeName) || strNotSpecified;\n  if (isNullOrUndefined(item) || isNullOrUndefined(baseType) || isNullOrUndefined(envelopeName)) {\n    throwError(\"Input doesn't contain all required fields\");\n  }\n  var iKey = \"\";\n  if (item[strIkey]) {\n    iKey = item[strIkey];\n    delete item[strIkey];\n  }\n  var telemetryItem = (_a = {}, _a[_DYN_NAME /* @min:name */] = envelopeName, _a.time = toISOString(new Date()), _a.iKey = iKey, _a.ext = systemProperties ? systemProperties : {}, _a.tags = [], _a.data = {}, _a.baseType = baseType, _a.baseData = item // Part B\n  , _a);\n  // Part C\n  if (!isNullOrUndefined(customProperties)) {\n    objForEachKey(customProperties, function (prop, value) {\n      telemetryItem.data[prop] = value;\n    });\n  }\n  return telemetryItem;\n}\nvar TelemetryItemCreator = /** @class */function () {\n  function TelemetryItemCreator() {}\n  /**\r\n   * Create a telemetry item that the 1DS channel understands\r\n   * @param item domain specific properties; part B\r\n   * @param baseType telemetry item type. ie PageViewData\r\n   * @param envelopeName name of the envelope. ie Microsoft.ApplicationInsights.<instrumentation key>.PageView\r\n   * @param customProperties user defined custom properties; part C\r\n   * @param systemProperties system properties that are added to the context; part A\r\n   * @returns ITelemetryItem that is sent to channel\r\n   */\n  TelemetryItemCreator.create = createTelemetryItem;\n  return TelemetryItemCreator;\n}();\nexport { TelemetryItemCreator };", "map": {"version": 3, "names": ["isNullOrUndefined", "objFor<PERSON><PERSON><PERSON>", "throwError", "toISOString", "strIkey", "strNotSpecified", "dataSanitizeString", "_DYN_NAME", "createTelemetryItem", "item", "baseType", "envelopeName", "logger", "customProperties", "systemProperties", "_a", "i<PERSON>ey", "telemetryItem", "time", "Date", "ext", "tags", "data", "baseData", "prop", "value", "TelemetryItemCreator", "create"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/TelemetryItemCreator.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { isNullOrUndefined, objFor<PERSON><PERSON><PERSON><PERSON>, throwError, toISOString } from \"@microsoft/applicationinsights-core-js\";\r\nimport { strIkey, strNotSpecified } from \"./Constants\";\r\nimport { dataSanitizeString } from \"./Telemetry/Common/DataSanitizer\";\r\nimport { _DYN_NAME } from \"./__DynamicConstants\";\r\n/**\r\n * Create a telemetry item that the 1DS channel understands\r\n * @param item domain specific properties; part B\r\n * @param baseType telemetry item type. ie PageViewData\r\n * @param envelopeName name of the envelope. ie Microsoft.ApplicationInsights.<instrumentation key>.PageView\r\n * @param customProperties user defined custom properties; part C\r\n * @param systemProperties system properties that are added to the context; part A\r\n * @returns ITelemetryItem that is sent to channel\r\n */\r\nexport function createTelemetryItem(item, baseType, envelopeName, logger, customProperties, systemProperties) {\r\n    var _a;\r\n    envelopeName = dataSanitizeString(logger, envelopeName) || strNotSpecified;\r\n    if (isNullOrUndefined(item) ||\r\n        isNullOrUndefined(baseType) ||\r\n        isNullOrUndefined(envelopeName)) {\r\n        throwError(\"Input doesn't contain all required fields\");\r\n    }\r\n    var iKey = \"\";\r\n    if (item[strIkey]) {\r\n        iKey = item[strIkey];\r\n        delete item[strIkey];\r\n    }\r\n    var telemetryItem = (_a = {},\r\n        _a[_DYN_NAME /* @min:name */] = envelopeName,\r\n        _a.time = toISOString(new Date()),\r\n        _a.iKey = iKey,\r\n        _a.ext = systemProperties ? systemProperties : {},\r\n        _a.tags = [],\r\n        _a.data = {},\r\n        _a.baseType = baseType,\r\n        _a.baseData = item // Part B\r\n    ,\r\n        _a);\r\n    // Part C\r\n    if (!isNullOrUndefined(customProperties)) {\r\n        objForEachKey(customProperties, function (prop, value) {\r\n            telemetryItem.data[prop] = value;\r\n        });\r\n    }\r\n    return telemetryItem;\r\n}\r\nvar TelemetryItemCreator = /** @class */ (function () {\r\n    function TelemetryItemCreator() {\r\n    }\r\n    /**\r\n     * Create a telemetry item that the 1DS channel understands\r\n     * @param item domain specific properties; part B\r\n     * @param baseType telemetry item type. ie PageViewData\r\n     * @param envelopeName name of the envelope. ie Microsoft.ApplicationInsights.<instrumentation key>.PageView\r\n     * @param customProperties user defined custom properties; part C\r\n     * @param systemProperties system properties that are added to the context; part A\r\n     * @returns ITelemetryItem that is sent to channel\r\n     */\r\n    TelemetryItemCreator.create = createTelemetryItem;\r\n    return TelemetryItemCreator;\r\n}());\r\nexport { TelemetryItemCreator };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,iBAAiB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,WAAW,QAAQ,wCAAwC;AAClH,SAASC,OAAO,EAAEC,eAAe,QAAQ,aAAa;AACtD,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,SAAS,QAAQ,sBAAsB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAE;EAC1G,IAAIC,EAAE;EACNJ,YAAY,GAAGL,kBAAkB,CAACM,MAAM,EAAED,YAAY,CAAC,IAAIN,eAAe;EAC1E,IAAIL,iBAAiB,CAACS,IAAI,CAAC,IACvBT,iBAAiB,CAACU,QAAQ,CAAC,IAC3BV,iBAAiB,CAACW,YAAY,CAAC,EAAE;IACjCT,UAAU,CAAC,2CAA2C,CAAC;EAC3D;EACA,IAAIc,IAAI,GAAG,EAAE;EACb,IAAIP,IAAI,CAACL,OAAO,CAAC,EAAE;IACfY,IAAI,GAAGP,IAAI,CAACL,OAAO,CAAC;IACpB,OAAOK,IAAI,CAACL,OAAO,CAAC;EACxB;EACA,IAAIa,aAAa,IAAIF,EAAE,GAAG,CAAC,CAAC,EACxBA,EAAE,CAACR,SAAS,CAAC,gBAAgB,GAAGI,YAAY,EAC5CI,EAAE,CAACG,IAAI,GAAGf,WAAW,CAAC,IAAIgB,IAAI,CAAC,CAAC,CAAC,EACjCJ,EAAE,CAACC,IAAI,GAAGA,IAAI,EACdD,EAAE,CAACK,GAAG,GAAGN,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,CAAC,EACjDC,EAAE,CAACM,IAAI,GAAG,EAAE,EACZN,EAAE,CAACO,IAAI,GAAG,CAAC,CAAC,EACZP,EAAE,CAACL,QAAQ,GAAGA,QAAQ,EACtBK,EAAE,CAACQ,QAAQ,GAAGd,IAAI,CAAC;EAAA,EAEnBM,EAAE,CAAC;EACP;EACA,IAAI,CAACf,iBAAiB,CAACa,gBAAgB,CAAC,EAAE;IACtCZ,aAAa,CAACY,gBAAgB,EAAE,UAAUW,IAAI,EAAEC,KAAK,EAAE;MACnDR,aAAa,CAACK,IAAI,CAACE,IAAI,CAAC,GAAGC,KAAK;IACpC,CAAC,CAAC;EACN;EACA,OAAOR,aAAa;AACxB;AACA,IAAIS,oBAAoB,GAAG,aAAe,YAAY;EAClD,SAASA,oBAAoBA,CAAA,EAAG,CAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,oBAAoB,CAACC,MAAM,GAAGnB,mBAAmB;EACjD,OAAOkB,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}