{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { PhoneNumberUtil } from 'google-libphonenumber';\nimport { CountryISO } from 'ngx-intl-tel-input';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/shared/services/phone-number.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngx-validate/core\";\nimport * as i4 from \"ngx-intl-tel-input\";\nexport class PhoneNumberComponent {\n  constructor(phoneNumberService, el) {\n    this.phoneNumberService = phoneNumberService;\n    this.el = el;\n    this.phoneNumber = '';\n    this.styles = ['form-control'];\n    this.phoneForm = new FormGroup({\n      phoneNumber: new FormControl(undefined, [Validators.required])\n    });\n    this.styleOptions = {\n      formControl: \"form-control-custom\",\n      invalid: \"invalid-form\"\n    };\n    this.preferredCountries = [CountryISO.Bahamas];\n  }\n  ngOnInit() {\n    this.firstLoad = true;\n    this.errorLabelClass = 'hidden';\n    this.errorLabel = 'This field is required.';\n    const phoneNumberUtil = PhoneNumberUtil.getInstance();\n    const phoneObject = this.phoneNumberService.getPhoneNumberObject();\n    if (phoneObject && phoneObject.e164Number != '') {\n      // need to set the value if its an edit \n      phoneObject.countryCode = phoneNumberUtil.getRegionCodeForNumber(phoneNumberUtil.parse(phoneObject.e164Number));\n      phoneObject.number = phoneNumberUtil.getNationalSignificantNumber(phoneNumberUtil.parse(phoneObject.e164Number));\n    }\n    this.phoneForm.get(\"phoneNumber\").setValue(phoneObject); // if its an add doenst matter if phoneObject is undefined since it will be blank anyway\n    this.phoneForm.get(\"phoneNumber\").valueChanges.subscribe(result => {\n      this.phoneNumber = result?.e164Number;\n      this.phoneNumberService.setPhoneNumber(result?.e164Number);\n      this.phoneNumberService.setPhoneNumberObejct(result);\n      //this.phoneForm.controls.phoneNumber.setErrors({ 'invalidPhone': true })\n      if (!this.validatePhone(result)) {\n        // depending on the validation of the number we add or remove the class for invalid\n        const styleString = this.styleOptions.formControl + \" \" + this.styleOptions.invalid;\n        this.styles[0] = styleString;\n        this.errorLabelClass = 'text-danger';\n      } else {\n        this.errorLabelClass = 'hidden';\n        const styleString = this.styleOptions.formControl;\n        this.styles[0] = styleString;\n      }\n    });\n  }\n  ngOnChanges() {\n    console.log('t');\n  }\n  validatePhone(phoneNumber) {\n    if (this.firstLoad) {\n      this.firstLoad = false;\n      return true;\n    }\n    if (phoneNumber === null) {\n      this.errorLabel = 'This field is required.';\n      return false;\n    }\n    if (Object.keys(phoneNumber).length === 0) {\n      var phoneInput = document.getElementById('phone-number');\n      phoneInput.classList.remove('is-invalid');\n      return true;\n    }\n    if (phoneNumber?.countryCode !== \"\" && phoneNumber.e164Number !== \"\") {\n      var phoneInput = document.getElementById('phone-number');\n      phoneInput.classList.remove('is-invalid');\n      return true;\n    }\n    if (!phoneNumber.countryCode || phoneNumber.countryCode == '') {\n      this.errorLabel = 'Country Code is required.';\n      return false;\n    }\n    this.errorLabel = 'This field is required.';\n    return false;\n  }\n  static {\n    this.ɵfac = function PhoneNumberComponent_Factory(t) {\n      return new (t || PhoneNumberComponent)(i0.ɵɵdirectiveInject(i1.PhoneNumberService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PhoneNumberComponent,\n      selectors: [[\"app-phone-number\"]],\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 8,\n      vars: 13,\n      consts: [[\"f\", \"ngForm\"], [3, \"formGroup\"], [\"for\", \"phoneNumber\", 2, \"display\", \"block\"], [\"formControlName\", \"phoneNumber\", \"name\", \"phoneNumber\", \"inputId\", \"phone-number\", 3, \"cssClass\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"searchCountryFlag\", \"selectFirstCountry\", \"maxLength\", \"phoneValidation\", \"separateDialCode\", \"preferredCountries\"], [\"id\", \"phone-number-error-label\"]],\n      template: function PhoneNumberComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 1, 0)(2, \"div\")(3, \"label\", 2);\n          i0.ɵɵtext(4, \" Phone Number * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"ngx-intl-tel-input\", 3);\n          i0.ɵɵelementStart(6, \"label\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.phoneForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"cssClass\", ctx.styles)(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"maxLength\", 15)(\"phoneValidation\", false)(\"separateDialCode\", true)(\"preferredCountries\", ctx.preferredCountries);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.errorLabelClass);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.errorLabel);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i3.ValidationGroupDirective, i3.ValidationDirective, i4.NgxIntlTelInputComponent, i4.NativeElementInjectorDirective],\n      styles: [\".invalid-form {\\n  border-color: #c00d49 !important;\\n  padding-right: calc(1.5em + 1.35rem);\\n  background-image: url('data:image/svg+xml,%3csvg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 12\\\" width=\\\"12\\\" height=\\\"12\\\" fill=\\\"none\\\" stroke=\\\"%23c00d49\\\"%3e%3ccircle cx=\\\"6\\\" cy=\\\"6\\\" r=\\\"4.5\\\"/%3e%3cpath stroke-linejoin=\\\"round\\\" d=\\\"M5.8 3.6h.4L6 6.5z\\\"/%3e%3ccircle cx=\\\"6\\\" cy=\\\"8.2\\\" r=\\\".6\\\" fill=\\\"%23c00d49\\\" stroke=\\\"none\\\"/%3e%3c/svg%3e');\\n  background-repeat: no-repeat;\\n  background-position: right calc(0.375em + 0.3375rem) center;\\n  background-size: calc(0.75em + 0.675rem) calc(0.75em + 0.675rem);\\n}\\n\\n.hidden {\\n  display: none;\\n}\\n\\n.form-control-custom {\\n  display: block;\\n  width: 100%;\\n  padding: 0.675rem 1.25rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  line-height: 1.5;\\n  color: #325168;\\n  background-color: #f0f4f7;\\n  background-clip: padding-box;\\n  border: 1px solid #e8eef3;\\n  appearance: none;\\n  border-radius: 0.5rem;\\n  box-shadow: none;\\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\\n}\\n\\n.form-control-custom:focus-visible {\\n  outline: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "PhoneNumberUtil", "CountryISO", "PhoneNumberComponent", "constructor", "phoneNumberService", "el", "phoneNumber", "styles", "phoneForm", "undefined", "required", "styleOptions", "formControl", "invalid", "preferredCountries", "Bahamas", "ngOnInit", "firstLoad", "errorLabelClass", "error<PERSON><PERSON><PERSON>", "phoneNumberUtil", "getInstance", "phoneObject", "getPhoneNumberObject", "e164Number", "countryCode", "getRegionCodeForNumber", "parse", "number", "getNationalSignificantNumber", "get", "setValue", "valueChanges", "subscribe", "result", "setPhoneNumber", "setPhoneNumberObejct", "validatePhone", "styleString", "ngOnChanges", "console", "log", "Object", "keys", "length", "phoneInput", "document", "getElementById", "classList", "remove", "i0", "ɵɵdirectiveInject", "i1", "PhoneNumberService", "ElementRef", "selectors", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "PhoneNumberComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵproperty", "ɵɵadvance", "ɵɵclassMap", "ɵɵtextInterpolate"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\components\\phone-number\\phone-number.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\components\\phone-number\\phone-number.component.html"], "sourcesContent": ["\r\nimport { Component, ElementRef, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\n\r\nimport { PhoneNumberUtil } from 'google-libphonenumber';\r\n\r\n\r\nimport { PhoneNumberObject, PhoneNumberService } from '@app/shared/services/phone-number.service';\r\nimport { CountryISO } from 'ngx-intl-tel-input';\r\n\r\n\r\n@Component({\r\n    selector: 'app-phone-number',\r\n    templateUrl: './phone-number.component.html',\r\n    styleUrls: ['./phone-number.component.scss'],\r\n    encapsulation: ViewEncapsulation.None, // Add this line\r\n})\r\nexport class PhoneNumberComponent implements OnInit {\r\n\r\n    public phoneNumber = '';\r\n    public styles = ['form-control'];\r\n    public phoneForm = new FormGroup({\r\n        phoneNumber: new FormControl(undefined, [Validators.required])\r\n    });\r\n\r\n    private styleOptions = { formControl: \"form-control-custom\", invalid: \"invalid-form\" }\r\n    public preferredCountries = [CountryISO.Bahamas];\r\n\r\n\r\n    constructor(private phoneNumberService: PhoneNumberService, private el: ElementRef) { }\r\n\r\n    public firstLoad:boolean;\r\n    public isInvalid:boolean;\r\n    public errorLabelClass:string;\r\n    public errorLabel:string;\r\n\r\n    ngOnInit(): void {\r\n\r\n        this.firstLoad = true;\r\n        this.errorLabelClass = 'hidden';\r\n        this.errorLabel = 'This field is required.'\r\n\r\n        const phoneNumberUtil = PhoneNumberUtil.getInstance();\r\n        const phoneObject: PhoneNumberObject = this.phoneNumberService.getPhoneNumberObject();\r\n\r\n        if (phoneObject && phoneObject.e164Number != '') { // need to set the value if its an edit \r\n            phoneObject.countryCode = phoneNumberUtil.getRegionCodeForNumber(phoneNumberUtil.parse(phoneObject.e164Number));\r\n            phoneObject.number = phoneNumberUtil.getNationalSignificantNumber(phoneNumberUtil.parse(phoneObject.e164Number));\r\n        }\r\n        this.phoneForm.get(\"phoneNumber\").setValue(phoneObject); // if its an add doenst matter if phoneObject is undefined since it will be blank anyway\r\n\r\n        this.phoneForm.get(\"phoneNumber\").valueChanges.subscribe(result => {\r\n            this.phoneNumber = result?.e164Number;\r\n            this.phoneNumberService.setPhoneNumber(result?.e164Number);\r\n            this.phoneNumberService.setPhoneNumberObejct(result);\r\n\r\n            //this.phoneForm.controls.phoneNumber.setErrors({ 'invalidPhone': true })\r\n\r\n            if (!this.validatePhone(result)) { // depending on the validation of the number we add or remove the class for invalid\r\n                const styleString = this.styleOptions.formControl + \" \" + this.styleOptions.invalid;\r\n                this.styles[0] = styleString;\r\n                this.errorLabelClass = 'text-danger';\r\n            }\r\n            else {\r\n                this.errorLabelClass = 'hidden';\r\n                const styleString = this.styleOptions.formControl;\r\n                this.styles[0] = styleString;\r\n            }\r\n\r\n        });\r\n\r\n\r\n    }\r\n\r\n    ngOnChanges():void {\r\n        console.log('t');\r\n    }\r\n\r\n    validatePhone(phoneNumber: PhoneNumberObject): boolean {\r\n        if(this.firstLoad)\r\n        {\r\n            this.firstLoad = false;\r\n            return true;\r\n        }\r\n        if (phoneNumber === null) {\r\n            this.errorLabel = 'This field is required.'\r\n            return false;\r\n        }\r\n        if (Object.keys(phoneNumber).length === 0) {\r\n            var phoneInput = document.getElementById('phone-number');\r\n            phoneInput.classList.remove('is-invalid');\r\n            return true;\r\n        }\r\n        if (phoneNumber?.countryCode !== \"\" && phoneNumber.e164Number !== \"\") {\r\n            var phoneInput = document.getElementById('phone-number');\r\n            phoneInput.classList.remove('is-invalid');\r\n            return true;\r\n        }\r\n        if(!phoneNumber.countryCode || phoneNumber.countryCode == '')\r\n        {\r\n            this.errorLabel = 'Country Code is required.';\r\n            return false;\r\n        }        \r\n        this.errorLabel = 'This field is required.'\r\n        return false;\r\n    }\r\n}\r\n", "<form #f=\"ngForm\" [formGroup]=\"phoneForm\">\r\n    <div>\r\n      <label for=\"phoneNumber\" style=\"display: block\">\r\n        Phone Number *\r\n      </label>\r\n      <ngx-intl-tel-input\r\n        [cssClass]=\"styles\"\r\n        [enableAutoCountrySelect]=\"true\"\r\n        [enablePlaceholder]=\"true\"\r\n        [searchCountryFlag]=\"true\"\r\n        [selectFirstCountry]=\"false\"\r\n        [maxLength]=\"15\"\r\n        [phoneValidation]=\"false\"\r\n        [separateDialCode]=\"true\"\r\n        [preferredCountries] = \"preferredCountries\"\r\n        formControlName=\"phoneNumber\"\r\n        name=\"phoneNumber\"\r\n        inputId=\"phone-number\"\r\n      ></ngx-intl-tel-input>\r\n      <label [class]=\"errorLabelClass\" id=\"phone-number-error-label\">{{errorLabel}}</label>\r\n    </div>\r\n</form>\r\n"], "mappings": "AAEA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,eAAe,QAAQ,uBAAuB;AAIvD,SAASC,UAAU,QAAQ,oBAAoB;;;;;;AAS/C,OAAM,MAAOC,oBAAoB;EAY7BC,YAAoBC,kBAAsC,EAAUC,EAAc;IAA9D,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,EAAE,GAAFA,EAAE;IAV/D,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,MAAM,GAAG,CAAC,cAAc,CAAC;IACzB,KAAAC,SAAS,GAAG,IAAIV,SAAS,CAAC;MAC7BQ,WAAW,EAAE,IAAIT,WAAW,CAACY,SAAS,EAAE,CAACV,UAAU,CAACW,QAAQ,CAAC;KAChE,CAAC;IAEM,KAAAC,YAAY,GAAG;MAAEC,WAAW,EAAE,qBAAqB;MAAEC,OAAO,EAAE;IAAc,CAAE;IAC/E,KAAAC,kBAAkB,GAAG,CAACb,UAAU,CAACc,OAAO,CAAC;EAGsC;EAOtFC,QAAQA,CAAA;IAEJ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,eAAe,GAAG,QAAQ;IAC/B,IAAI,CAACC,UAAU,GAAG,yBAAyB;IAE3C,MAAMC,eAAe,GAAGpB,eAAe,CAACqB,WAAW,EAAE;IACrD,MAAMC,WAAW,GAAsB,IAAI,CAAClB,kBAAkB,CAACmB,oBAAoB,EAAE;IAErF,IAAID,WAAW,IAAIA,WAAW,CAACE,UAAU,IAAI,EAAE,EAAE;MAAE;MAC/CF,WAAW,CAACG,WAAW,GAAGL,eAAe,CAACM,sBAAsB,CAACN,eAAe,CAACO,KAAK,CAACL,WAAW,CAACE,UAAU,CAAC,CAAC;MAC/GF,WAAW,CAACM,MAAM,GAAGR,eAAe,CAACS,4BAA4B,CAACT,eAAe,CAACO,KAAK,CAACL,WAAW,CAACE,UAAU,CAAC,CAAC;IACpH;IACA,IAAI,CAAChB,SAAS,CAACsB,GAAG,CAAC,aAAa,CAAC,CAACC,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC;IAEzD,IAAI,CAACd,SAAS,CAACsB,GAAG,CAAC,aAAa,CAAC,CAACE,YAAY,CAACC,SAAS,CAACC,MAAM,IAAG;MAC9D,IAAI,CAAC5B,WAAW,GAAG4B,MAAM,EAAEV,UAAU;MACrC,IAAI,CAACpB,kBAAkB,CAAC+B,cAAc,CAACD,MAAM,EAAEV,UAAU,CAAC;MAC1D,IAAI,CAACpB,kBAAkB,CAACgC,oBAAoB,CAACF,MAAM,CAAC;MAEpD;MAEA,IAAI,CAAC,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,EAAE;QAAE;QAC/B,MAAMI,WAAW,GAAG,IAAI,CAAC3B,YAAY,CAACC,WAAW,GAAG,GAAG,GAAG,IAAI,CAACD,YAAY,CAACE,OAAO;QACnF,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;QAC5B,IAAI,CAACpB,eAAe,GAAG,aAAa;MACxC,CAAC,MACI;QACD,IAAI,CAACA,eAAe,GAAG,QAAQ;QAC/B,MAAMoB,WAAW,GAAG,IAAI,CAAC3B,YAAY,CAACC,WAAW;QACjD,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;MAChC;IAEJ,CAAC,CAAC;EAGN;EAEAC,WAAWA,CAAA;IACPC,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC;EACpB;EAEAJ,aAAaA,CAAC/B,WAA8B;IACxC,IAAG,IAAI,CAACW,SAAS,EACjB;MACI,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,OAAO,IAAI;IACf;IACA,IAAIX,WAAW,KAAK,IAAI,EAAE;MACtB,IAAI,CAACa,UAAU,GAAG,yBAAyB;MAC3C,OAAO,KAAK;IAChB;IACA,IAAIuB,MAAM,CAACC,IAAI,CAACrC,WAAW,CAAC,CAACsC,MAAM,KAAK,CAAC,EAAE;MACvC,IAAIC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;MACxDF,UAAU,CAACG,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MACzC,OAAO,IAAI;IACf;IACA,IAAI3C,WAAW,EAAEmB,WAAW,KAAK,EAAE,IAAInB,WAAW,CAACkB,UAAU,KAAK,EAAE,EAAE;MAClE,IAAIqB,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;MACxDF,UAAU,CAACG,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MACzC,OAAO,IAAI;IACf;IACA,IAAG,CAAC3C,WAAW,CAACmB,WAAW,IAAInB,WAAW,CAACmB,WAAW,IAAI,EAAE,EAC5D;MACI,IAAI,CAACN,UAAU,GAAG,2BAA2B;MAC7C,OAAO,KAAK;IAChB;IACA,IAAI,CAACA,UAAU,GAAG,yBAAyB;IAC3C,OAAO,KAAK;EAChB;;;uBAxFSjB,oBAAoB,EAAAgD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA;IAAA;EAAA;;;YAApBpD,oBAAoB;MAAAqD,SAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf3Bb,EAFN,CAAAe,cAAA,iBAA0C,UACjC,eAC6C;UAC9Cf,EAAA,CAAAgB,MAAA,uBACF;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACRjB,EAAA,CAAAkB,SAAA,4BAasB;UACtBlB,EAAA,CAAAe,cAAA,eAA+D;UAAAf,EAAA,CAAAgB,MAAA,GAAc;UAEnFhB,EAFmF,CAAAiB,YAAA,EAAQ,EACjF,EACH;;;UArBWjB,EAAA,CAAAmB,UAAA,cAAAL,GAAA,CAAAxD,SAAA,CAAuB;UAMjC0C,EAAA,CAAAoB,SAAA,GAAmB;UAQnBpB,EARA,CAAAmB,UAAA,aAAAL,GAAA,CAAAzD,MAAA,CAAmB,iCACa,2BACN,2BACA,6BACE,iBACZ,0BACS,0BACA,uBAAAyD,GAAA,CAAAlD,kBAAA,CACkB;UAKtCoC,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,CAAAP,GAAA,CAAA9C,eAAA,CAAyB;UAA+BgC,EAAA,CAAAoB,SAAA,EAAc;UAAdpB,EAAA,CAAAsB,iBAAA,CAAAR,GAAA,CAAA7C,UAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}