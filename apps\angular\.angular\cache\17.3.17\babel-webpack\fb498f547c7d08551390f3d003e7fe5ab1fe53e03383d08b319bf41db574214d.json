{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\nimport { LogLevel } from \"./ILogger\";\nimport { HttpTransportType, TransferFormat } from \"./ITransport\";\nimport { LongPollingTransport } from \"./LongPollingTransport\";\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\nimport { WebSocketTransport } from \"./WebSocketTransport\";\nconst MAX_REDIRECTS = 100;\n/** @private */\nexport class HttpConnection {\n  constructor(url, options = {}) {\n    this._stopPromiseResolver = () => {};\n    this.features = {};\n    this._negotiateVersion = 1;\n    Arg.isRequired(url, \"url\");\n    this._logger = createLogger(options.logger);\n    this.baseUrl = this._resolveUrl(url);\n    options = options || {};\n    options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\n    if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\n      options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\n    } else {\n      throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\n    }\n    options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\n    let webSocketModule = null;\n    let eventSourceModule = null;\n    if (Platform.isNode && typeof require !== \"undefined\") {\n      // In order to ignore the dynamic require in webpack builds we need to do this magic\n      // @ts-ignore: TS doesn't know about these names\n      const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\n      webSocketModule = requireFunc(\"ws\");\n      eventSourceModule = requireFunc(\"eventsource\");\n    }\n    if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\n      options.WebSocket = WebSocket;\n    } else if (Platform.isNode && !options.WebSocket) {\n      if (webSocketModule) {\n        options.WebSocket = webSocketModule;\n      }\n    }\n    if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\n      options.EventSource = EventSource;\n    } else if (Platform.isNode && !options.EventSource) {\n      if (typeof eventSourceModule !== \"undefined\") {\n        options.EventSource = eventSourceModule;\n      }\n    }\n    this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\n    this._connectionState = \"Disconnected\" /* Disconnected */;\n    this._connectionStarted = false;\n    this._options = options;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n  start(transferFormat) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      transferFormat = transferFormat || TransferFormat.Binary;\n      Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n      _this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\n      if (_this._connectionState !== \"Disconnected\" /* Disconnected */) {\n        return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\n      }\n      _this._connectionState = \"Connecting\" /* Connecting */;\n      _this._startInternalPromise = _this._startInternal(transferFormat);\n      yield _this._startInternalPromise;\n      // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\n      if (_this._connectionState === \"Disconnecting\" /* Disconnecting */) {\n        // stop() was called and transitioned the client into the Disconnecting state.\n        const message = \"Failed to start the HttpConnection before stop() was called.\";\n        _this._logger.log(LogLevel.Error, message);\n        // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\n        yield _this._stopPromise;\n        return Promise.reject(new AbortError(message));\n      } else if (_this._connectionState !== \"Connected\" /* Connected */) {\n        // stop() was called and transitioned the client into the Disconnecting state.\n        const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\n        _this._logger.log(LogLevel.Error, message);\n        return Promise.reject(new AbortError(message));\n      }\n      _this._connectionStarted = true;\n    })();\n  }\n  send(data) {\n    if (this._connectionState !== \"Connected\" /* Connected */) {\n      return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\n    }\n    if (!this._sendQueue) {\n      this._sendQueue = new TransportSendQueue(this.transport);\n    }\n    // Transport will not be null if state is connected\n    return this._sendQueue.send(data);\n  }\n  stop(error) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2._connectionState === \"Disconnected\" /* Disconnected */) {\n        _this2._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\n        return Promise.resolve();\n      }\n      if (_this2._connectionState === \"Disconnecting\" /* Disconnecting */) {\n        _this2._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\n        return _this2._stopPromise;\n      }\n      _this2._connectionState = \"Disconnecting\" /* Disconnecting */;\n      _this2._stopPromise = new Promise(resolve => {\n        // Don't complete stop() until stopConnection() completes.\n        _this2._stopPromiseResolver = resolve;\n      });\n      // stopInternal should never throw so just observe it.\n      yield _this2._stopInternal(error);\n      yield _this2._stopPromise;\n    })();\n  }\n  _stopInternal(error) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // Set error as soon as possible otherwise there is a race between\n      // the transport closing and providing an error and the error from a close message\n      // We would prefer the close message error.\n      _this3._stopError = error;\n      try {\n        yield _this3._startInternalPromise;\n      } catch (e) {\n        // This exception is returned to the user as a rejected Promise from the start method.\n      }\n      // The transport's onclose will trigger stopConnection which will run our onclose event.\n      // The transport should always be set if currently connected. If it wasn't set, it's likely because\n      // stop was called during start() and start() failed.\n      if (_this3.transport) {\n        try {\n          yield _this3.transport.stop();\n        } catch (e) {\n          _this3._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\n          _this3._stopConnection();\n        }\n        _this3.transport = undefined;\n      } else {\n        _this3._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\n      }\n    })();\n  }\n  _startInternal(transferFormat) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // Store the original base url and the access token factory since they may change\n      // as part of negotiating\n      let url = _this4.baseUrl;\n      _this4._accessTokenFactory = _this4._options.accessTokenFactory;\n      _this4._httpClient._accessTokenFactory = _this4._accessTokenFactory;\n      try {\n        if (_this4._options.skipNegotiation) {\n          if (_this4._options.transport === HttpTransportType.WebSockets) {\n            // No need to add a connection ID in this case\n            _this4.transport = _this4._constructTransport(HttpTransportType.WebSockets);\n            // We should just call connect directly in this case.\n            // No fallback or negotiate in this case.\n            yield _this4._startTransport(url, transferFormat);\n          } else {\n            throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\n          }\n        } else {\n          let negotiateResponse = null;\n          let redirects = 0;\n          do {\n            negotiateResponse = yield _this4._getNegotiationResponse(url);\n            // the user tries to stop the connection when it is being started\n            if (_this4._connectionState === \"Disconnecting\" /* Disconnecting */ || _this4._connectionState === \"Disconnected\" /* Disconnected */) {\n              throw new AbortError(\"The connection was stopped during negotiation.\");\n            }\n            if (negotiateResponse.error) {\n              throw new Error(negotiateResponse.error);\n            }\n            if (negotiateResponse.ProtocolVersion) {\n              throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\n            }\n            if (negotiateResponse.url) {\n              url = negotiateResponse.url;\n            }\n            if (negotiateResponse.accessToken) {\n              // Replace the current access token factory with one that uses\n              // the returned access token\n              const accessToken = negotiateResponse.accessToken;\n              _this4._accessTokenFactory = () => accessToken;\n              // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\n              _this4._httpClient._accessToken = accessToken;\n              _this4._httpClient._accessTokenFactory = undefined;\n            }\n            redirects++;\n          } while (negotiateResponse.url && redirects < MAX_REDIRECTS);\n          if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\n            throw new Error(\"Negotiate redirection limit exceeded.\");\n          }\n          yield _this4._createTransport(url, _this4._options.transport, negotiateResponse, transferFormat);\n        }\n        if (_this4.transport instanceof LongPollingTransport) {\n          _this4.features.inherentKeepAlive = true;\n        }\n        if (_this4._connectionState === \"Connecting\" /* Connecting */) {\n          // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\n          // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\n          _this4._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\n          _this4._connectionState = \"Connected\" /* Connected */;\n        }\n        // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\n        // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\n        // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\n      } catch (e) {\n        _this4._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\n        _this4._connectionState = \"Disconnected\" /* Disconnected */;\n        _this4.transport = undefined;\n        // if start fails, any active calls to stop assume that start will complete the stop promise\n        _this4._stopPromiseResolver();\n        return Promise.reject(e);\n      }\n    })();\n  }\n  _getNegotiationResponse(url) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const headers = {};\n      const [name, value] = getUserAgentHeader();\n      headers[name] = value;\n      const negotiateUrl = _this5._resolveNegotiateUrl(url);\n      _this5._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\n      try {\n        const response = yield _this5._httpClient.post(negotiateUrl, {\n          content: \"\",\n          headers: {\n            ...headers,\n            ..._this5._options.headers\n          },\n          timeout: _this5._options.timeout,\n          withCredentials: _this5._options.withCredentials\n        });\n        if (response.statusCode !== 200) {\n          return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\n        }\n        const negotiateResponse = JSON.parse(response.content);\n        if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\n          // Negotiate version 0 doesn't use connectionToken\n          // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\n          negotiateResponse.connectionToken = negotiateResponse.connectionId;\n        }\n        return negotiateResponse;\n      } catch (e) {\n        let errorMessage = \"Failed to complete negotiation with the server: \" + e;\n        if (e instanceof HttpError) {\n          if (e.statusCode === 404) {\n            errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\n          }\n        }\n        _this5._logger.log(LogLevel.Error, errorMessage);\n        return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\n      }\n    })();\n  }\n  _createConnectUrl(url, connectionToken) {\n    if (!connectionToken) {\n      return url;\n    }\n    return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\n  }\n  _createTransport(url, requestedTransport, negotiateResponse, requestedTransferFormat) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      let connectUrl = _this6._createConnectUrl(url, negotiateResponse.connectionToken);\n      if (_this6._isITransport(requestedTransport)) {\n        _this6._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\n        _this6.transport = requestedTransport;\n        yield _this6._startTransport(connectUrl, requestedTransferFormat);\n        _this6.connectionId = negotiateResponse.connectionId;\n        return;\n      }\n      const transportExceptions = [];\n      const transports = negotiateResponse.availableTransports || [];\n      let negotiate = negotiateResponse;\n      for (const endpoint of transports) {\n        const transportOrError = _this6._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\n        if (transportOrError instanceof Error) {\n          // Store the error and continue, we don't want to cause a re-negotiate in these cases\n          transportExceptions.push(`${endpoint.transport} failed:`);\n          transportExceptions.push(transportOrError);\n        } else if (_this6._isITransport(transportOrError)) {\n          _this6.transport = transportOrError;\n          if (!negotiate) {\n            try {\n              negotiate = yield _this6._getNegotiationResponse(url);\n            } catch (ex) {\n              return Promise.reject(ex);\n            }\n            connectUrl = _this6._createConnectUrl(url, negotiate.connectionToken);\n          }\n          try {\n            yield _this6._startTransport(connectUrl, requestedTransferFormat);\n            _this6.connectionId = negotiate.connectionId;\n            return;\n          } catch (ex) {\n            _this6._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\n            negotiate = undefined;\n            transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\n            if (_this6._connectionState !== \"Connecting\" /* Connecting */) {\n              const message = \"Failed to select transport before stop() was called.\";\n              _this6._logger.log(LogLevel.Debug, message);\n              return Promise.reject(new AbortError(message));\n            }\n          }\n        }\n      }\n      if (transportExceptions.length > 0) {\n        return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\n      }\n      return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\n    })();\n  }\n  _constructTransport(transport) {\n    switch (transport) {\n      case HttpTransportType.WebSockets:\n        if (!this._options.WebSocket) {\n          throw new Error(\"'WebSocket' is not supported in your environment.\");\n        }\n        return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent, this._options.WebSocket, this._options.headers || {});\n      case HttpTransportType.ServerSentEvents:\n        if (!this._options.EventSource) {\n          throw new Error(\"'EventSource' is not supported in your environment.\");\n        }\n        return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\n      case HttpTransportType.LongPolling:\n        return new LongPollingTransport(this._httpClient, this._logger, this._options);\n      default:\n        throw new Error(`Unknown transport: ${transport}.`);\n    }\n  }\n  _startTransport(url, transferFormat) {\n    this.transport.onreceive = this.onreceive;\n    this.transport.onclose = e => this._stopConnection(e);\n    return this.transport.connect(url, transferFormat);\n  }\n  _resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat) {\n    const transport = HttpTransportType[endpoint.transport];\n    if (transport === null || transport === undefined) {\n      this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\n      return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\n    } else {\n      if (transportMatches(requestedTransport, transport)) {\n        const transferFormats = endpoint.transferFormats.map(s => TransferFormat[s]);\n        if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\n          if (transport === HttpTransportType.WebSockets && !this._options.WebSocket || transport === HttpTransportType.ServerSentEvents && !this._options.EventSource) {\n            this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\n            return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\n          } else {\n            this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\n            try {\n              return this._constructTransport(transport);\n            } catch (ex) {\n              return ex;\n            }\n          }\n        } else {\n          this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\n          return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\n        }\n      } else {\n        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\n        return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\n      }\n    }\n  }\n  _isITransport(transport) {\n    return transport && typeof transport === \"object\" && \"connect\" in transport;\n  }\n  _stopConnection(error) {\n    this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\n    this.transport = undefined;\n    // If we have a stopError, it takes precedence over the error from the transport\n    error = this._stopError || error;\n    this._stopError = undefined;\n    if (this._connectionState === \"Disconnected\" /* Disconnected */) {\n      this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\n      return;\n    }\n    if (this._connectionState === \"Connecting\" /* Connecting */) {\n      this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\n      throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\n    }\n    if (this._connectionState === \"Disconnecting\" /* Disconnecting */) {\n      // A call to stop() induced this call to stopConnection and needs to be completed.\n      // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\n      this._stopPromiseResolver();\n    }\n    if (error) {\n      this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\n    } else {\n      this._logger.log(LogLevel.Information, \"Connection disconnected.\");\n    }\n    if (this._sendQueue) {\n      this._sendQueue.stop().catch(e => {\n        this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\n      });\n      this._sendQueue = undefined;\n    }\n    this.connectionId = undefined;\n    this._connectionState = \"Disconnected\" /* Disconnected */;\n    if (this._connectionStarted) {\n      this._connectionStarted = false;\n      try {\n        if (this.onclose) {\n          this.onclose(error);\n        }\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\n      }\n    }\n  }\n  _resolveUrl(url) {\n    // startsWith is not supported in IE\n    if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\n      return url;\n    }\n    if (!Platform.isBrowser) {\n      throw new Error(`Cannot resolve '${url}'.`);\n    }\n    // Setting the url to the href propery of an anchor tag handles normalization\n    // for us. There are 3 main cases.\n    // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\n    // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\n    // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\n    const aTag = window.document.createElement(\"a\");\n    aTag.href = url;\n    this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\n    return aTag.href;\n  }\n  _resolveNegotiateUrl(url) {\n    const index = url.indexOf(\"?\");\n    let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\n    if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\n      negotiateUrl += \"/\";\n    }\n    negotiateUrl += \"negotiate\";\n    negotiateUrl += index === -1 ? \"\" : url.substring(index);\n    if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\n      negotiateUrl += index === -1 ? \"?\" : \"&\";\n      negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\n    }\n    return negotiateUrl;\n  }\n}\nfunction transportMatches(requestedTransport, actualTransport) {\n  return !requestedTransport || (actualTransport & requestedTransport) !== 0;\n}\n/** @private */\nexport class TransportSendQueue {\n  constructor(_transport) {\n    this._transport = _transport;\n    this._buffer = [];\n    this._executing = true;\n    this._sendBufferedData = new PromiseSource();\n    this._transportResult = new PromiseSource();\n    this._sendLoopPromise = this._sendLoop();\n  }\n  send(data) {\n    this._bufferData(data);\n    if (!this._transportResult) {\n      this._transportResult = new PromiseSource();\n    }\n    return this._transportResult.promise;\n  }\n  stop() {\n    this._executing = false;\n    this._sendBufferedData.resolve();\n    return this._sendLoopPromise;\n  }\n  _bufferData(data) {\n    if (this._buffer.length && typeof this._buffer[0] !== typeof data) {\n      throw new Error(`Expected data to be of type ${typeof this._buffer} but was of type ${typeof data}`);\n    }\n    this._buffer.push(data);\n    this._sendBufferedData.resolve();\n  }\n  _sendLoop() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      while (true) {\n        yield _this7._sendBufferedData.promise;\n        if (!_this7._executing) {\n          if (_this7._transportResult) {\n            _this7._transportResult.reject(\"Connection stopped.\");\n          }\n          break;\n        }\n        _this7._sendBufferedData = new PromiseSource();\n        const transportResult = _this7._transportResult;\n        _this7._transportResult = undefined;\n        const data = typeof _this7._buffer[0] === \"string\" ? _this7._buffer.join(\"\") : TransportSendQueue._concatBuffers(_this7._buffer);\n        _this7._buffer.length = 0;\n        try {\n          yield _this7._transport.send(data);\n          transportResult.resolve();\n        } catch (error) {\n          transportResult.reject(error);\n        }\n      }\n    })();\n  }\n  static _concatBuffers(arrayBuffers) {\n    const totalLength = arrayBuffers.map(b => b.byteLength).reduce((a, b) => a + b);\n    const result = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const item of arrayBuffers) {\n      result.set(new Uint8Array(item), offset);\n      offset += item.byteLength;\n    }\n    return result.buffer;\n  }\n}\nclass PromiseSource {\n  constructor() {\n    this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\n  }\n  resolve() {\n    this._resolver();\n  }\n  reject(reason) {\n    this._rejecter(reason);\n  }\n}", "map": {"version": 3, "names": ["AccessTokenHttpClient", "DefaultHttpClient", "AggregateErrors", "DisabledTransportError", "FailedToNegotiateWithServerError", "FailedToStartTransportError", "HttpError", "UnsupportedTransportError", "AbortError", "LogLevel", "HttpTransportType", "TransferFormat", "LongPollingTransport", "ServerSentEventsTransport", "Arg", "createLogger", "getUserAgentHeader", "Platform", "WebSocketTransport", "MAX_REDIRECTS", "HttpConnection", "constructor", "url", "options", "_stopPromiseResolver", "features", "_negotiateVersion", "isRequired", "_logger", "logger", "baseUrl", "_resolveUrl", "logMessageContent", "undefined", "withCredentials", "Error", "timeout", "webSocketModule", "eventSourceModule", "isNode", "require", "requireFunc", "__webpack_require__", "__non_webpack_require__", "WebSocket", "EventSource", "_httpClient", "httpClient", "accessTokenFactory", "_connectionState", "_connectionStarted", "_options", "onreceive", "onclose", "start", "transferFormat", "_this", "_asyncToGenerator", "Binary", "isIn", "log", "Debug", "Promise", "reject", "_startInternalPromise", "_startInternal", "message", "_stopPromise", "send", "data", "_sendQueue", "TransportSendQueue", "transport", "stop", "error", "_this2", "resolve", "_stopInternal", "_this3", "_stopError", "e", "_stopConnection", "_this4", "_accessTokenFactory", "skipNegotiation", "WebSockets", "_constructTransport", "_startTransport", "negotiateResponse", "redirects", "_getNegotiationResponse", "ProtocolVersion", "accessToken", "_accessToken", "_createTransport", "inherentKeepAlive", "_this5", "headers", "name", "value", "negotiateUrl", "_resolveNegotiateUrl", "response", "post", "content", "statusCode", "JSON", "parse", "negotiateVersion", "connectionToken", "connectionId", "errorMessage", "_createConnectUrl", "indexOf", "requestedTransport", "requestedTransferFormat", "_this6", "connectUrl", "_isITransport", "transportExceptions", "transports", "availableTransports", "negotiate", "endpoint", "transportOrError", "_resolveTransportOrError", "push", "ex", "length", "join", "ServerSentEvents", "LongPolling", "connect", "transportMatches", "transferFormats", "map", "s", "Warning", "Information", "catch", "lastIndexOf", "<PERSON><PERSON><PERSON><PERSON>", "aTag", "window", "document", "createElement", "href", "index", "substring", "actualTransport", "_transport", "_buffer", "_executing", "_sendBufferedData", "PromiseSource", "_transportResult", "_sendLoopPromise", "_sendLoop", "_bufferData", "promise", "_this7", "transportResult", "_concatBuffers", "arrayBuffers", "totalLength", "b", "byteLength", "reduce", "a", "result", "Uint8Array", "offset", "item", "set", "buffer", "_resolver", "_rejecter", "reason"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/signalr/dist/esm/HttpConnection.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\nconst MAX_REDIRECTS = 100;\r\n/** @private */\r\nexport class HttpConnection {\r\n    constructor(url, options = {}) {\r\n        this._stopPromiseResolver = () => { };\r\n        this.features = {};\r\n        this._negotiateVersion = 1;\r\n        Arg.isRequired(url, \"url\");\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        }\r\n        else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n        let webSocketModule = null;\r\n        let eventSourceModule = null;\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        }\r\n        else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        }\r\n        else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n        this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\r\n        this._connectionState = \"Disconnected\" /* Disconnected */;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async start(transferFormat) {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n        if (this._connectionState !== \"Disconnected\" /* Disconnected */) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this._connectionState = \"Connecting\" /* Connecting */;\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState === \"Disconnecting\" /* Disconnecting */) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n        else if (this._connectionState !== \"Connected\" /* Connected */) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n        this._connectionStarted = true;\r\n    }\r\n    send(data) {\r\n        if (this._connectionState !== \"Connected\" /* Connected */) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport);\r\n        }\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n    async stop(error) {\r\n        if (this._connectionState === \"Disconnected\" /* Disconnected */) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n        if (this._connectionState === \"Disconnecting\" /* Disconnecting */) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n        this._connectionState = \"Disconnecting\" /* Disconnecting */;\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n    async _stopInternal(error) {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n        try {\r\n            await this._startInternalPromise;\r\n        }\r\n        catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n            this.transport = undefined;\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n    async _startInternal(transferFormat) {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n        this._httpClient._accessTokenFactory = this._accessTokenFactory;\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                }\r\n                else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            }\r\n            else {\r\n                let negotiateResponse = null;\r\n                let redirects = 0;\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === \"Disconnecting\" /* Disconnecting */ || this._connectionState === \"Disconnected\" /* Disconnected */) {\r\n                        throw new AbortError(\"The connection was stopped during negotiation.\");\r\n                    }\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n                    if (negotiateResponse.ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                        // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\r\n                        this._httpClient._accessToken = accessToken;\r\n                        this._httpClient._accessTokenFactory = undefined;\r\n                    }\r\n                    redirects++;\r\n                } while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n            if (this._connectionState === \"Connecting\" /* Connecting */) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = \"Connected\" /* Connected */;\r\n            }\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        }\r\n        catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = \"Disconnected\" /* Disconnected */;\r\n            this.transport = undefined;\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n    async _getNegotiationResponse(url) {\r\n        const headers = {};\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n            const negotiateResponse = JSON.parse(response.content);\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            return negotiateResponse;\r\n        }\r\n        catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n    _createConnectUrl(url, connectionToken) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n    async _createTransport(url, requestedTransport, negotiateResponse, requestedTransferFormat) {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n        const transportExceptions = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            }\r\n            else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    }\r\n                    catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                }\r\n                catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n                    if (this._connectionState !== \"Connecting\" /* Connecting */) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new AbortError(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n    _constructTransport(transport) {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n    _startTransport(url, transferFormat) {\r\n        this.transport.onreceive = this.onreceive;\r\n        this.transport.onclose = (e) => this._stopConnection(e);\r\n        return this.transport.connect(url, transferFormat);\r\n    }\r\n    _resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat) {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        }\r\n        else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    }\r\n                    else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            return this._constructTransport(transport);\r\n                        }\r\n                        catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                }\r\n                else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            }\r\n            else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n    _isITransport(transport) {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n    _stopConnection(error) {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n        this.transport = undefined;\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n        if (this._connectionState === \"Disconnected\" /* Disconnected */) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n        if (this._connectionState === \"Connecting\" /* Connecting */) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n        if (this._connectionState === \"Disconnecting\" /* Disconnecting */) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n        this.connectionId = undefined;\r\n        this._connectionState = \"Disconnected\" /* Disconnected */;\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n    _resolveUrl(url) {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n    _resolveNegotiateUrl(url) {\r\n        const index = url.indexOf(\"?\");\r\n        let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n        if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\r\n            negotiateUrl += index === -1 ? \"?\" : \"&\";\r\n            negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\r\n        }\r\n        return negotiateUrl;\r\n    }\r\n}\r\nfunction transportMatches(requestedTransport, actualTransport) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    constructor(_transport) {\r\n        this._transport = _transport;\r\n        this._buffer = [];\r\n        this._executing = true;\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n    send(data) {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n    stop() {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n    _bufferData(data) {\r\n        if (this._buffer.length && typeof (this._buffer[0]) !== typeof (data)) {\r\n            throw new Error(`Expected data to be of type ${typeof (this._buffer)} but was of type ${typeof (data)}`);\r\n        }\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n    async _sendLoop() {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n                break;\r\n            }\r\n            this._sendBufferedData = new PromiseSource();\r\n            const transportResult = this._transportResult;\r\n            this._transportResult = undefined;\r\n            const data = typeof (this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n            this._buffer.length = 0;\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            }\r\n            catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n    static _concatBuffers(arrayBuffers) {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n        return result.buffer;\r\n    }\r\n}\r\nclass PromiseSource {\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n    resolve() {\r\n        this._resolver();\r\n    }\r\n    reject(reason) {\r\n        this._rejecter(reason);\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,eAAe,EAAEC,sBAAsB,EAAEC,gCAAgC,EAAEC,2BAA2B,EAAEC,SAAS,EAAEC,yBAAyB,EAAEC,UAAU,QAAQ,UAAU;AACnL,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,cAAc;AAChE,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,GAAG,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,QAAQ,QAAQ,SAAS;AACzE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,MAAMC,aAAa,GAAG,GAAG;AACzB;AACA,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3B,IAAI,CAACC,oBAAoB,GAAG,MAAM,CAAE,CAAC;IACrC,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1BZ,GAAG,CAACa,UAAU,CAACL,GAAG,EAAE,KAAK,CAAC;IAC1B,IAAI,CAACM,OAAO,GAAGb,YAAY,CAACQ,OAAO,CAACM,MAAM,CAAC;IAC3C,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,WAAW,CAACT,GAAG,CAAC;IACpCC,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvBA,OAAO,CAACS,iBAAiB,GAAGT,OAAO,CAACS,iBAAiB,KAAKC,SAAS,GAAG,KAAK,GAAGV,OAAO,CAACS,iBAAiB;IACvG,IAAI,OAAOT,OAAO,CAACW,eAAe,KAAK,SAAS,IAAIX,OAAO,CAACW,eAAe,KAAKD,SAAS,EAAE;MACvFV,OAAO,CAACW,eAAe,GAAGX,OAAO,CAACW,eAAe,KAAKD,SAAS,GAAG,IAAI,GAAGV,OAAO,CAACW,eAAe;IACpG,CAAC,MACI;MACD,MAAM,IAAIC,KAAK,CAAC,iEAAiE,CAAC;IACtF;IACAZ,OAAO,CAACa,OAAO,GAAGb,OAAO,CAACa,OAAO,KAAKH,SAAS,GAAG,GAAG,GAAG,IAAI,GAAGV,OAAO,CAACa,OAAO;IAC9E,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,iBAAiB,GAAG,IAAI;IAC5B,IAAIrB,QAAQ,CAACsB,MAAM,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;MACnD;MACA;MACA,MAAMC,WAAW,GAAG,OAAOC,mBAAmB,KAAK,UAAU,GAAGC,uBAAuB,GAAGH,OAAO;MACjGH,eAAe,GAAGI,WAAW,CAAC,IAAI,CAAC;MACnCH,iBAAiB,GAAGG,WAAW,CAAC,aAAa,CAAC;IAClD;IACA,IAAI,CAACxB,QAAQ,CAACsB,MAAM,IAAI,OAAOK,SAAS,KAAK,WAAW,IAAI,CAACrB,OAAO,CAACqB,SAAS,EAAE;MAC5ErB,OAAO,CAACqB,SAAS,GAAGA,SAAS;IACjC,CAAC,MACI,IAAI3B,QAAQ,CAACsB,MAAM,IAAI,CAAChB,OAAO,CAACqB,SAAS,EAAE;MAC5C,IAAIP,eAAe,EAAE;QACjBd,OAAO,CAACqB,SAAS,GAAGP,eAAe;MACvC;IACJ;IACA,IAAI,CAACpB,QAAQ,CAACsB,MAAM,IAAI,OAAOM,WAAW,KAAK,WAAW,IAAI,CAACtB,OAAO,CAACsB,WAAW,EAAE;MAChFtB,OAAO,CAACsB,WAAW,GAAGA,WAAW;IACrC,CAAC,MACI,IAAI5B,QAAQ,CAACsB,MAAM,IAAI,CAAChB,OAAO,CAACsB,WAAW,EAAE;MAC9C,IAAI,OAAOP,iBAAiB,KAAK,WAAW,EAAE;QAC1Cf,OAAO,CAACsB,WAAW,GAAGP,iBAAiB;MAC3C;IACJ;IACA,IAAI,CAACQ,WAAW,GAAG,IAAI9C,qBAAqB,CAACuB,OAAO,CAACwB,UAAU,IAAI,IAAI9C,iBAAiB,CAAC,IAAI,CAAC2B,OAAO,CAAC,EAAEL,OAAO,CAACyB,kBAAkB,CAAC;IACnI,IAAI,CAACC,gBAAgB,GAAG,cAAc,CAAC;IACvC,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,QAAQ,GAAG5B,OAAO;IACvB,IAAI,CAAC6B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;EACMC,KAAKA,CAACC,cAAc,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxBF,cAAc,GAAGA,cAAc,IAAI5C,cAAc,CAAC+C,MAAM;MACxD5C,GAAG,CAAC6C,IAAI,CAACJ,cAAc,EAAE5C,cAAc,EAAE,gBAAgB,CAAC;MAC1D6C,KAAI,CAAC5B,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,6CAA6ClD,cAAc,CAAC4C,cAAc,CAAC,IAAI,CAAC;MACjH,IAAIC,KAAI,CAACP,gBAAgB,KAAK,cAAc,CAAC,oBAAoB;QAC7D,OAAOa,OAAO,CAACC,MAAM,CAAC,IAAI5B,KAAK,CAAC,yEAAyE,CAAC,CAAC;MAC/G;MACAqB,KAAI,CAACP,gBAAgB,GAAG,YAAY,CAAC;MACrCO,KAAI,CAACQ,qBAAqB,GAAGR,KAAI,CAACS,cAAc,CAACV,cAAc,CAAC;MAChE,MAAMC,KAAI,CAACQ,qBAAqB;MAChC;MACA,IAAIR,KAAI,CAACP,gBAAgB,KAAK,eAAe,CAAC,qBAAqB;QAC/D;QACA,MAAMiB,OAAO,GAAG,8DAA8D;QAC9EV,KAAI,CAAC5B,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE+B,OAAO,CAAC;QACzC;QACA,MAAMV,KAAI,CAACW,YAAY;QACvB,OAAOL,OAAO,CAACC,MAAM,CAAC,IAAIvD,UAAU,CAAC0D,OAAO,CAAC,CAAC;MAClD,CAAC,MACI,IAAIV,KAAI,CAACP,gBAAgB,KAAK,WAAW,CAAC,iBAAiB;QAC5D;QACA,MAAMiB,OAAO,GAAG,6GAA6G;QAC7HV,KAAI,CAAC5B,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE+B,OAAO,CAAC;QACzC,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIvD,UAAU,CAAC0D,OAAO,CAAC,CAAC;MAClD;MACAV,KAAI,CAACN,kBAAkB,GAAG,IAAI;IAAC;EACnC;EACAkB,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,IAAI,CAACpB,gBAAgB,KAAK,WAAW,CAAC,iBAAiB;MACvD,OAAOa,OAAO,CAACC,MAAM,CAAC,IAAI5B,KAAK,CAAC,qEAAqE,CAAC,CAAC;IAC3G;IACA,IAAI,CAAC,IAAI,CAACmC,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAIC,kBAAkB,CAAC,IAAI,CAACC,SAAS,CAAC;IAC5D;IACA;IACA,OAAO,IAAI,CAACF,UAAU,CAACF,IAAI,CAACC,IAAI,CAAC;EACrC;EACMI,IAAIA,CAACC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MACd,IAAIkB,MAAI,CAAC1B,gBAAgB,KAAK,cAAc,CAAC,oBAAoB;QAC7D0B,MAAI,CAAC/C,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,+BAA+Ba,KAAK,wEAAwE,CAAC;QAC9I,OAAOZ,OAAO,CAACc,OAAO,CAAC,CAAC;MAC5B;MACA,IAAID,MAAI,CAAC1B,gBAAgB,KAAK,eAAe,CAAC,qBAAqB;QAC/D0B,MAAI,CAAC/C,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,+BAA+Ba,KAAK,yEAAyE,CAAC;QAC/I,OAAOC,MAAI,CAACR,YAAY;MAC5B;MACAQ,MAAI,CAAC1B,gBAAgB,GAAG,eAAe,CAAC;MACxC0B,MAAI,CAACR,YAAY,GAAG,IAAIL,OAAO,CAAEc,OAAO,IAAK;QACzC;QACAD,MAAI,CAACnD,oBAAoB,GAAGoD,OAAO;MACvC,CAAC,CAAC;MACF;MACA,MAAMD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAC;MAC/B,MAAMC,MAAI,CAACR,YAAY;IAAC;EAC5B;EACMU,aAAaA,CAACH,KAAK,EAAE;IAAA,IAAAI,MAAA;IAAA,OAAArB,iBAAA;MACvB;MACA;MACA;MACAqB,MAAI,CAACC,UAAU,GAAGL,KAAK;MACvB,IAAI;QACA,MAAMI,MAAI,CAACd,qBAAqB;MACpC,CAAC,CACD,OAAOgB,CAAC,EAAE;QACN;MAAA;MAEJ;MACA;MACA;MACA,IAAIF,MAAI,CAACN,SAAS,EAAE;QAChB,IAAI;UACA,MAAMM,MAAI,CAACN,SAAS,CAACC,IAAI,CAAC,CAAC;QAC/B,CAAC,CACD,OAAOO,CAAC,EAAE;UACNF,MAAI,CAAClD,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE,gDAAgD6C,CAAC,IAAI,CAAC;UACvFF,MAAI,CAACG,eAAe,CAAC,CAAC;QAC1B;QACAH,MAAI,CAACN,SAAS,GAAGvC,SAAS;MAC9B,CAAC,MACI;QACD6C,MAAI,CAAClD,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,wFAAwF,CAAC;MAC9H;IAAC;EACL;EACMI,cAAcA,CAACV,cAAc,EAAE;IAAA,IAAA2B,MAAA;IAAA,OAAAzB,iBAAA;MACjC;MACA;MACA,IAAInC,GAAG,GAAG4D,MAAI,CAACpD,OAAO;MACtBoD,MAAI,CAACC,mBAAmB,GAAGD,MAAI,CAAC/B,QAAQ,CAACH,kBAAkB;MAC3DkC,MAAI,CAACpC,WAAW,CAACqC,mBAAmB,GAAGD,MAAI,CAACC,mBAAmB;MAC/D,IAAI;QACA,IAAID,MAAI,CAAC/B,QAAQ,CAACiC,eAAe,EAAE;UAC/B,IAAIF,MAAI,CAAC/B,QAAQ,CAACqB,SAAS,KAAK9D,iBAAiB,CAAC2E,UAAU,EAAE;YAC1D;YACAH,MAAI,CAACV,SAAS,GAAGU,MAAI,CAACI,mBAAmB,CAAC5E,iBAAiB,CAAC2E,UAAU,CAAC;YACvE;YACA;YACA,MAAMH,MAAI,CAACK,eAAe,CAACjE,GAAG,EAAEiC,cAAc,CAAC;UACnD,CAAC,MACI;YACD,MAAM,IAAIpB,KAAK,CAAC,8EAA8E,CAAC;UACnG;QACJ,CAAC,MACI;UACD,IAAIqD,iBAAiB,GAAG,IAAI;UAC5B,IAAIC,SAAS,GAAG,CAAC;UACjB,GAAG;YACCD,iBAAiB,SAASN,MAAI,CAACQ,uBAAuB,CAACpE,GAAG,CAAC;YAC3D;YACA,IAAI4D,MAAI,CAACjC,gBAAgB,KAAK,eAAe,CAAC,uBAAuBiC,MAAI,CAACjC,gBAAgB,KAAK,cAAc,CAAC,oBAAoB;cAC9H,MAAM,IAAIzC,UAAU,CAAC,gDAAgD,CAAC;YAC1E;YACA,IAAIgF,iBAAiB,CAACd,KAAK,EAAE;cACzB,MAAM,IAAIvC,KAAK,CAACqD,iBAAiB,CAACd,KAAK,CAAC;YAC5C;YACA,IAAIc,iBAAiB,CAACG,eAAe,EAAE;cACnC,MAAM,IAAIxD,KAAK,CAAC,8LAA8L,CAAC;YACnN;YACA,IAAIqD,iBAAiB,CAAClE,GAAG,EAAE;cACvBA,GAAG,GAAGkE,iBAAiB,CAAClE,GAAG;YAC/B;YACA,IAAIkE,iBAAiB,CAACI,WAAW,EAAE;cAC/B;cACA;cACA,MAAMA,WAAW,GAAGJ,iBAAiB,CAACI,WAAW;cACjDV,MAAI,CAACC,mBAAmB,GAAG,MAAMS,WAAW;cAC5C;cACAV,MAAI,CAACpC,WAAW,CAAC+C,YAAY,GAAGD,WAAW;cAC3CV,MAAI,CAACpC,WAAW,CAACqC,mBAAmB,GAAGlD,SAAS;YACpD;YACAwD,SAAS,EAAE;UACf,CAAC,QAAQD,iBAAiB,CAAClE,GAAG,IAAImE,SAAS,GAAGtE,aAAa;UAC3D,IAAIsE,SAAS,KAAKtE,aAAa,IAAIqE,iBAAiB,CAAClE,GAAG,EAAE;YACtD,MAAM,IAAIa,KAAK,CAAC,uCAAuC,CAAC;UAC5D;UACA,MAAM+C,MAAI,CAACY,gBAAgB,CAACxE,GAAG,EAAE4D,MAAI,CAAC/B,QAAQ,CAACqB,SAAS,EAAEgB,iBAAiB,EAAEjC,cAAc,CAAC;QAChG;QACA,IAAI2B,MAAI,CAACV,SAAS,YAAY5D,oBAAoB,EAAE;UAChDsE,MAAI,CAACzD,QAAQ,CAACsE,iBAAiB,GAAG,IAAI;QAC1C;QACA,IAAIb,MAAI,CAACjC,gBAAgB,KAAK,YAAY,CAAC,kBAAkB;UACzD;UACA;UACAiC,MAAI,CAACtD,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,4CAA4C,CAAC;UAC9EqB,MAAI,CAACjC,gBAAgB,GAAG,WAAW,CAAC;QACxC;QACA;QACA;QACA;MACJ,CAAC,CACD,OAAO+B,CAAC,EAAE;QACNE,MAAI,CAACtD,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE,kCAAkC,GAAG6C,CAAC,CAAC;QACxEE,MAAI,CAACjC,gBAAgB,GAAG,cAAc,CAAC;QACvCiC,MAAI,CAACV,SAAS,GAAGvC,SAAS;QAC1B;QACAiD,MAAI,CAAC1D,oBAAoB,CAAC,CAAC;QAC3B,OAAOsC,OAAO,CAACC,MAAM,CAACiB,CAAC,CAAC;MAC5B;IAAC;EACL;EACMU,uBAAuBA,CAACpE,GAAG,EAAE;IAAA,IAAA0E,MAAA;IAAA,OAAAvC,iBAAA;MAC/B,MAAMwC,OAAO,GAAG,CAAC,CAAC;MAClB,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGnF,kBAAkB,CAAC,CAAC;MAC1CiF,OAAO,CAACC,IAAI,CAAC,GAAGC,KAAK;MACrB,MAAMC,YAAY,GAAGJ,MAAI,CAACK,oBAAoB,CAAC/E,GAAG,CAAC;MACnD0E,MAAI,CAACpE,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,gCAAgCuC,YAAY,GAAG,CAAC;MACjF,IAAI;QACA,MAAME,QAAQ,SAASN,MAAI,CAAClD,WAAW,CAACyD,IAAI,CAACH,YAAY,EAAE;UACvDI,OAAO,EAAE,EAAE;UACXP,OAAO,EAAE;YAAE,GAAGA,OAAO;YAAE,GAAGD,MAAI,CAAC7C,QAAQ,CAAC8C;UAAQ,CAAC;UACjD7D,OAAO,EAAE4D,MAAI,CAAC7C,QAAQ,CAACf,OAAO;UAC9BF,eAAe,EAAE8D,MAAI,CAAC7C,QAAQ,CAACjB;QACnC,CAAC,CAAC;QACF,IAAIoE,QAAQ,CAACG,UAAU,KAAK,GAAG,EAAE;UAC7B,OAAO3C,OAAO,CAACC,MAAM,CAAC,IAAI5B,KAAK,CAAC,mDAAmDmE,QAAQ,CAACG,UAAU,GAAG,CAAC,CAAC;QAC/G;QACA,MAAMjB,iBAAiB,GAAGkB,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,OAAO,CAAC;QACtD,IAAI,CAAChB,iBAAiB,CAACoB,gBAAgB,IAAIpB,iBAAiB,CAACoB,gBAAgB,GAAG,CAAC,EAAE;UAC/E;UACA;UACApB,iBAAiB,CAACqB,eAAe,GAAGrB,iBAAiB,CAACsB,YAAY;QACtE;QACA,OAAOtB,iBAAiB;MAC5B,CAAC,CACD,OAAOR,CAAC,EAAE;QACN,IAAI+B,YAAY,GAAG,kDAAkD,GAAG/B,CAAC;QACzE,IAAIA,CAAC,YAAY1E,SAAS,EAAE;UACxB,IAAI0E,CAAC,CAACyB,UAAU,KAAK,GAAG,EAAE;YACtBM,YAAY,GAAGA,YAAY,GAAG,qFAAqF;UACvH;QACJ;QACAf,MAAI,CAACpE,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE4E,YAAY,CAAC;QAC9C,OAAOjD,OAAO,CAACC,MAAM,CAAC,IAAI3D,gCAAgC,CAAC2G,YAAY,CAAC,CAAC;MAC7E;IAAC;EACL;EACAC,iBAAiBA,CAAC1F,GAAG,EAAEuF,eAAe,EAAE;IACpC,IAAI,CAACA,eAAe,EAAE;MAClB,OAAOvF,GAAG;IACd;IACA,OAAOA,GAAG,IAAIA,GAAG,CAAC2F,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,MAAMJ,eAAe,EAAE;EAChF;EACMf,gBAAgBA,CAACxE,GAAG,EAAE4F,kBAAkB,EAAE1B,iBAAiB,EAAE2B,uBAAuB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3D,iBAAA;MACxF,IAAI4D,UAAU,GAAGD,MAAI,CAACJ,iBAAiB,CAAC1F,GAAG,EAAEkE,iBAAiB,CAACqB,eAAe,CAAC;MAC/E,IAAIO,MAAI,CAACE,aAAa,CAACJ,kBAAkB,CAAC,EAAE;QACxCE,MAAI,CAACxF,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,yEAAyE,CAAC;QAC3GuD,MAAI,CAAC5C,SAAS,GAAG0C,kBAAkB;QACnC,MAAME,MAAI,CAAC7B,eAAe,CAAC8B,UAAU,EAAEF,uBAAuB,CAAC;QAC/DC,MAAI,CAACN,YAAY,GAAGtB,iBAAiB,CAACsB,YAAY;QAClD;MACJ;MACA,MAAMS,mBAAmB,GAAG,EAAE;MAC9B,MAAMC,UAAU,GAAGhC,iBAAiB,CAACiC,mBAAmB,IAAI,EAAE;MAC9D,IAAIC,SAAS,GAAGlC,iBAAiB;MACjC,KAAK,MAAMmC,QAAQ,IAAIH,UAAU,EAAE;QAC/B,MAAMI,gBAAgB,GAAGR,MAAI,CAACS,wBAAwB,CAACF,QAAQ,EAAET,kBAAkB,EAAEC,uBAAuB,CAAC;QAC7G,IAAIS,gBAAgB,YAAYzF,KAAK,EAAE;UACnC;UACAoF,mBAAmB,CAACO,IAAI,CAAC,GAAGH,QAAQ,CAACnD,SAAS,UAAU,CAAC;UACzD+C,mBAAmB,CAACO,IAAI,CAACF,gBAAgB,CAAC;QAC9C,CAAC,MACI,IAAIR,MAAI,CAACE,aAAa,CAACM,gBAAgB,CAAC,EAAE;UAC3CR,MAAI,CAAC5C,SAAS,GAAGoD,gBAAgB;UACjC,IAAI,CAACF,SAAS,EAAE;YACZ,IAAI;cACAA,SAAS,SAASN,MAAI,CAAC1B,uBAAuB,CAACpE,GAAG,CAAC;YACvD,CAAC,CACD,OAAOyG,EAAE,EAAE;cACP,OAAOjE,OAAO,CAACC,MAAM,CAACgE,EAAE,CAAC;YAC7B;YACAV,UAAU,GAAGD,MAAI,CAACJ,iBAAiB,CAAC1F,GAAG,EAAEoG,SAAS,CAACb,eAAe,CAAC;UACvE;UACA,IAAI;YACA,MAAMO,MAAI,CAAC7B,eAAe,CAAC8B,UAAU,EAAEF,uBAAuB,CAAC;YAC/DC,MAAI,CAACN,YAAY,GAAGY,SAAS,CAACZ,YAAY;YAC1C;UACJ,CAAC,CACD,OAAOiB,EAAE,EAAE;YACPX,MAAI,CAACxF,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE,kCAAkCwF,QAAQ,CAACnD,SAAS,MAAMuD,EAAE,EAAE,CAAC;YAChGL,SAAS,GAAGzF,SAAS;YACrBsF,mBAAmB,CAACO,IAAI,CAAC,IAAIzH,2BAA2B,CAAC,GAAGsH,QAAQ,CAACnD,SAAS,YAAYuD,EAAE,EAAE,EAAErH,iBAAiB,CAACiH,QAAQ,CAACnD,SAAS,CAAC,CAAC,CAAC;YACvI,IAAI4C,MAAI,CAACnE,gBAAgB,KAAK,YAAY,CAAC,kBAAkB;cACzD,MAAMiB,OAAO,GAAG,sDAAsD;cACtEkD,MAAI,CAACxF,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAEK,OAAO,CAAC;cACzC,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIvD,UAAU,CAAC0D,OAAO,CAAC,CAAC;YAClD;UACJ;QACJ;MACJ;MACA,IAAIqD,mBAAmB,CAACS,MAAM,GAAG,CAAC,EAAE;QAChC,OAAOlE,OAAO,CAACC,MAAM,CAAC,IAAI7D,eAAe,CAAC,yEAAyEqH,mBAAmB,CAACU,IAAI,CAAC,GAAG,CAAC,EAAE,EAAEV,mBAAmB,CAAC,CAAC;MAC7K;MACA,OAAOzD,OAAO,CAACC,MAAM,CAAC,IAAI5B,KAAK,CAAC,6EAA6E,CAAC,CAAC;IAAC;EACpH;EACAmD,mBAAmBA,CAACd,SAAS,EAAE;IAC3B,QAAQA,SAAS;MACb,KAAK9D,iBAAiB,CAAC2E,UAAU;QAC7B,IAAI,CAAC,IAAI,CAAClC,QAAQ,CAACP,SAAS,EAAE;UAC1B,MAAM,IAAIT,KAAK,CAAC,mDAAmD,CAAC;QACxE;QACA,OAAO,IAAIjB,kBAAkB,CAAC,IAAI,CAAC4B,WAAW,EAAE,IAAI,CAACqC,mBAAmB,EAAE,IAAI,CAACvD,OAAO,EAAE,IAAI,CAACuB,QAAQ,CAACnB,iBAAiB,EAAE,IAAI,CAACmB,QAAQ,CAACP,SAAS,EAAE,IAAI,CAACO,QAAQ,CAAC8C,OAAO,IAAI,CAAC,CAAC,CAAC;MAClL,KAAKvF,iBAAiB,CAACwH,gBAAgB;QACnC,IAAI,CAAC,IAAI,CAAC/E,QAAQ,CAACN,WAAW,EAAE;UAC5B,MAAM,IAAIV,KAAK,CAAC,qDAAqD,CAAC;QAC1E;QACA,OAAO,IAAItB,yBAAyB,CAAC,IAAI,CAACiC,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC+C,YAAY,EAAE,IAAI,CAACjE,OAAO,EAAE,IAAI,CAACuB,QAAQ,CAAC;MACtH,KAAKzC,iBAAiB,CAACyH,WAAW;QAC9B,OAAO,IAAIvH,oBAAoB,CAAC,IAAI,CAACkC,WAAW,EAAE,IAAI,CAAClB,OAAO,EAAE,IAAI,CAACuB,QAAQ,CAAC;MAClF;QACI,MAAM,IAAIhB,KAAK,CAAC,sBAAsBqC,SAAS,GAAG,CAAC;IAC3D;EACJ;EACAe,eAAeA,CAACjE,GAAG,EAAEiC,cAAc,EAAE;IACjC,IAAI,CAACiB,SAAS,CAACpB,SAAS,GAAG,IAAI,CAACA,SAAS;IACzC,IAAI,CAACoB,SAAS,CAACnB,OAAO,GAAI2B,CAAC,IAAK,IAAI,CAACC,eAAe,CAACD,CAAC,CAAC;IACvD,OAAO,IAAI,CAACR,SAAS,CAAC4D,OAAO,CAAC9G,GAAG,EAAEiC,cAAc,CAAC;EACtD;EACAsE,wBAAwBA,CAACF,QAAQ,EAAET,kBAAkB,EAAEC,uBAAuB,EAAE;IAC5E,MAAM3C,SAAS,GAAG9D,iBAAiB,CAACiH,QAAQ,CAACnD,SAAS,CAAC;IACvD,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKvC,SAAS,EAAE;MAC/C,IAAI,CAACL,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,uBAAuB8D,QAAQ,CAACnD,SAAS,+CAA+C,CAAC;MAC1H,OAAO,IAAIrC,KAAK,CAAC,uBAAuBwF,QAAQ,CAACnD,SAAS,+CAA+C,CAAC;IAC9G,CAAC,MACI;MACD,IAAI6D,gBAAgB,CAACnB,kBAAkB,EAAE1C,SAAS,CAAC,EAAE;QACjD,MAAM8D,eAAe,GAAGX,QAAQ,CAACW,eAAe,CAACC,GAAG,CAAEC,CAAC,IAAK7H,cAAc,CAAC6H,CAAC,CAAC,CAAC;QAC9E,IAAIF,eAAe,CAACrB,OAAO,CAACE,uBAAuB,CAAC,IAAI,CAAC,EAAE;UACvD,IAAK3C,SAAS,KAAK9D,iBAAiB,CAAC2E,UAAU,IAAI,CAAC,IAAI,CAAClC,QAAQ,CAACP,SAAS,IACtE4B,SAAS,KAAK9D,iBAAiB,CAACwH,gBAAgB,IAAI,CAAC,IAAI,CAAC/E,QAAQ,CAACN,WAAY,EAAE;YAClF,IAAI,CAACjB,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,uBAAuBnD,iBAAiB,CAAC8D,SAAS,CAAC,qDAAqD,CAAC;YAC1I,OAAO,IAAIjE,yBAAyB,CAAC,IAAIG,iBAAiB,CAAC8D,SAAS,CAAC,yCAAyC,EAAEA,SAAS,CAAC;UAC9H,CAAC,MACI;YACD,IAAI,CAAC5C,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,wBAAwBnD,iBAAiB,CAAC8D,SAAS,CAAC,IAAI,CAAC;YAC1F,IAAI;cACA,OAAO,IAAI,CAACc,mBAAmB,CAACd,SAAS,CAAC;YAC9C,CAAC,CACD,OAAOuD,EAAE,EAAE;cACP,OAAOA,EAAE;YACb;UACJ;QACJ,CAAC,MACI;UACD,IAAI,CAACnG,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,uBAAuBnD,iBAAiB,CAAC8D,SAAS,CAAC,gEAAgE7D,cAAc,CAACwG,uBAAuB,CAAC,IAAI,CAAC;UAChM,OAAO,IAAIhF,KAAK,CAAC,IAAIzB,iBAAiB,CAAC8D,SAAS,CAAC,sBAAsB7D,cAAc,CAACwG,uBAAuB,CAAC,GAAG,CAAC;QACtH;MACJ,CAAC,MACI;QACD,IAAI,CAACvF,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,uBAAuBnD,iBAAiB,CAAC8D,SAAS,CAAC,0CAA0C,CAAC;QAC/H,OAAO,IAAIrE,sBAAsB,CAAC,IAAIO,iBAAiB,CAAC8D,SAAS,CAAC,8BAA8B,EAAEA,SAAS,CAAC;MAChH;IACJ;EACJ;EACA8C,aAAaA,CAAC9C,SAAS,EAAE;IACrB,OAAOA,SAAS,IAAI,OAAQA,SAAU,KAAK,QAAQ,IAAI,SAAS,IAAIA,SAAS;EACjF;EACAS,eAAeA,CAACP,KAAK,EAAE;IACnB,IAAI,CAAC9C,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,iCAAiCa,KAAK,2BAA2B,IAAI,CAACzB,gBAAgB,GAAG,CAAC;IAC3H,IAAI,CAACuB,SAAS,GAAGvC,SAAS;IAC1B;IACAyC,KAAK,GAAG,IAAI,CAACK,UAAU,IAAIL,KAAK;IAChC,IAAI,CAACK,UAAU,GAAG9C,SAAS;IAC3B,IAAI,IAAI,CAACgB,gBAAgB,KAAK,cAAc,CAAC,oBAAoB;MAC7D,IAAI,CAACrB,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACoD,KAAK,EAAE,yCAAyCa,KAAK,4EAA4E,CAAC;MAC5J;IACJ;IACA,IAAI,IAAI,CAACzB,gBAAgB,KAAK,YAAY,CAAC,kBAAkB;MACzD,IAAI,CAACrB,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACgI,OAAO,EAAE,yCAAyC/D,KAAK,wEAAwE,CAAC;MAC1J,MAAM,IAAIvC,KAAK,CAAC,iCAAiCuC,KAAK,qEAAqE,CAAC;IAChI;IACA,IAAI,IAAI,CAACzB,gBAAgB,KAAK,eAAe,CAAC,qBAAqB;MAC/D;MACA;MACA,IAAI,CAACzB,oBAAoB,CAAC,CAAC;IAC/B;IACA,IAAIkD,KAAK,EAAE;MACP,IAAI,CAAC9C,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE,uCAAuCuC,KAAK,IAAI,CAAC;IACtF,CAAC,MACI;MACD,IAAI,CAAC9C,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACiI,WAAW,EAAE,0BAA0B,CAAC;IACtE;IACA,IAAI,IAAI,CAACpE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACG,IAAI,CAAC,CAAC,CAACkE,KAAK,CAAE3D,CAAC,IAAK;QAChC,IAAI,CAACpD,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE,0CAA0C6C,CAAC,IAAI,CAAC;MACrF,CAAC,CAAC;MACF,IAAI,CAACV,UAAU,GAAGrC,SAAS;IAC/B;IACA,IAAI,CAAC6E,YAAY,GAAG7E,SAAS;IAC7B,IAAI,CAACgB,gBAAgB,GAAG,cAAc,CAAC;IACvC,IAAI,IAAI,CAACC,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,GAAG,KAAK;MAC/B,IAAI;QACA,IAAI,IAAI,CAACG,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACqB,KAAK,CAAC;QACvB;MACJ,CAAC,CACD,OAAOM,CAAC,EAAE;QACN,IAAI,CAACpD,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAAC0B,KAAK,EAAE,0BAA0BuC,KAAK,kBAAkBM,CAAC,IAAI,CAAC;MAC5F;IACJ;EACJ;EACAjD,WAAWA,CAACT,GAAG,EAAE;IACb;IACA,IAAIA,GAAG,CAACsH,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,IAAItH,GAAG,CAACsH,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;MAC7E,OAAOtH,GAAG;IACd;IACA,IAAI,CAACL,QAAQ,CAAC4H,SAAS,EAAE;MACrB,MAAM,IAAI1G,KAAK,CAAC,mBAAmBb,GAAG,IAAI,CAAC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA,MAAMwH,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CH,IAAI,CAACI,IAAI,GAAG5H,GAAG;IACf,IAAI,CAACM,OAAO,CAACgC,GAAG,CAACnD,QAAQ,CAACiI,WAAW,EAAE,gBAAgBpH,GAAG,SAASwH,IAAI,CAACI,IAAI,IAAI,CAAC;IACjF,OAAOJ,IAAI,CAACI,IAAI;EACpB;EACA7C,oBAAoBA,CAAC/E,GAAG,EAAE;IACtB,MAAM6H,KAAK,GAAG7H,GAAG,CAAC2F,OAAO,CAAC,GAAG,CAAC;IAC9B,IAAIb,YAAY,GAAG9E,GAAG,CAAC8H,SAAS,CAAC,CAAC,EAAED,KAAK,KAAK,CAAC,CAAC,GAAG7H,GAAG,CAAC0G,MAAM,GAAGmB,KAAK,CAAC;IACtE,IAAI/C,YAAY,CAACA,YAAY,CAAC4B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAC/C5B,YAAY,IAAI,GAAG;IACvB;IACAA,YAAY,IAAI,WAAW;IAC3BA,YAAY,IAAI+C,KAAK,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG7H,GAAG,CAAC8H,SAAS,CAACD,KAAK,CAAC;IACxD,IAAI/C,YAAY,CAACa,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;MACjDb,YAAY,IAAI+C,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;MACxC/C,YAAY,IAAI,mBAAmB,GAAG,IAAI,CAAC1E,iBAAiB;IAChE;IACA,OAAO0E,YAAY;EACvB;AACJ;AACA,SAASiC,gBAAgBA,CAACnB,kBAAkB,EAAEmC,eAAe,EAAE;EAC3D,OAAO,CAACnC,kBAAkB,IAAK,CAACmC,eAAe,GAAGnC,kBAAkB,MAAM,CAAE;AAChF;AACA;AACA,OAAO,MAAM3C,kBAAkB,CAAC;EAC5BlD,WAAWA,CAACiI,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAIC,aAAa,CAAC,CAAC;IAC5C,IAAI,CAACC,gBAAgB,GAAG,IAAID,aAAa,CAAC,CAAC;IAC3C,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;EAC5C;EACAzF,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACyF,WAAW,CAACzF,IAAI,CAAC;IACtB,IAAI,CAAC,IAAI,CAACsF,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAID,aAAa,CAAC,CAAC;IAC/C;IACA,OAAO,IAAI,CAACC,gBAAgB,CAACI,OAAO;EACxC;EACAtF,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC+E,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,iBAAiB,CAAC7E,OAAO,CAAC,CAAC;IAChC,OAAO,IAAI,CAACgF,gBAAgB;EAChC;EACAE,WAAWA,CAACzF,IAAI,EAAE;IACd,IAAI,IAAI,CAACkF,OAAO,CAACvB,MAAM,IAAI,OAAQ,IAAI,CAACuB,OAAO,CAAC,CAAC,CAAE,KAAK,OAAQlF,IAAK,EAAE;MACnE,MAAM,IAAIlC,KAAK,CAAC,+BAA+B,OAAQ,IAAI,CAACoH,OAAQ,oBAAoB,OAAQlF,IAAK,EAAE,CAAC;IAC5G;IACA,IAAI,CAACkF,OAAO,CAACzB,IAAI,CAACzD,IAAI,CAAC;IACvB,IAAI,CAACoF,iBAAiB,CAAC7E,OAAO,CAAC,CAAC;EACpC;EACMiF,SAASA,CAAA,EAAG;IAAA,IAAAG,MAAA;IAAA,OAAAvG,iBAAA;MACd,OAAO,IAAI,EAAE;QACT,MAAMuG,MAAI,CAACP,iBAAiB,CAACM,OAAO;QACpC,IAAI,CAACC,MAAI,CAACR,UAAU,EAAE;UAClB,IAAIQ,MAAI,CAACL,gBAAgB,EAAE;YACvBK,MAAI,CAACL,gBAAgB,CAAC5F,MAAM,CAAC,qBAAqB,CAAC;UACvD;UACA;QACJ;QACAiG,MAAI,CAACP,iBAAiB,GAAG,IAAIC,aAAa,CAAC,CAAC;QAC5C,MAAMO,eAAe,GAAGD,MAAI,CAACL,gBAAgB;QAC7CK,MAAI,CAACL,gBAAgB,GAAG1H,SAAS;QACjC,MAAMoC,IAAI,GAAG,OAAQ2F,MAAI,CAACT,OAAO,CAAC,CAAC,CAAE,KAAK,QAAQ,GAC9CS,MAAI,CAACT,OAAO,CAACtB,IAAI,CAAC,EAAE,CAAC,GACrB1D,kBAAkB,CAAC2F,cAAc,CAACF,MAAI,CAACT,OAAO,CAAC;QACnDS,MAAI,CAACT,OAAO,CAACvB,MAAM,GAAG,CAAC;QACvB,IAAI;UACA,MAAMgC,MAAI,CAACV,UAAU,CAAClF,IAAI,CAACC,IAAI,CAAC;UAChC4F,eAAe,CAACrF,OAAO,CAAC,CAAC;QAC7B,CAAC,CACD,OAAOF,KAAK,EAAE;UACVuF,eAAe,CAAClG,MAAM,CAACW,KAAK,CAAC;QACjC;MACJ;IAAC;EACL;EACA,OAAOwF,cAAcA,CAACC,YAAY,EAAE;IAChC,MAAMC,WAAW,GAAGD,YAAY,CAAC5B,GAAG,CAAE8B,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKG,CAAC,GAAGH,CAAC,CAAC;IACjF,MAAMI,MAAM,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;IAC1C,IAAIO,MAAM,GAAG,CAAC;IACd,KAAK,MAAMC,IAAI,IAAIT,YAAY,EAAE;MAC7BM,MAAM,CAACI,GAAG,CAAC,IAAIH,UAAU,CAACE,IAAI,CAAC,EAAED,MAAM,CAAC;MACxCA,MAAM,IAAIC,IAAI,CAACN,UAAU;IAC7B;IACA,OAAOG,MAAM,CAACK,MAAM;EACxB;AACJ;AACA,MAAMpB,aAAa,CAAC;EAChBrI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0I,OAAO,GAAG,IAAIjG,OAAO,CAAC,CAACc,OAAO,EAAEb,MAAM,KAAK,CAAC,IAAI,CAACgH,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC,GAAG,CAACpG,OAAO,EAAEb,MAAM,CAAC,CAAC;EACzG;EACAa,OAAOA,CAAA,EAAG;IACN,IAAI,CAACmG,SAAS,CAAC,CAAC;EACpB;EACAhH,MAAMA,CAACkH,MAAM,EAAE;IACX,IAAI,CAACD,SAAS,CAACC,MAAM,CAAC;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}