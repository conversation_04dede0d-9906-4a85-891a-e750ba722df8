{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/dsb\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    var i = Math.floor(Math.abs(n)),\n      v = n.toString().replace(/^[^.]*\\.?/, '').length,\n      f = parseInt(n.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n    if (v === 0 && i % 100 === 1 || f % 100 === 1) return 1;\n    if (v === 0 && i % 100 === 2 || f % 100 === 2) return 2;\n    if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 || f % 100 === Math.floor(f % 100) && f % 100 >= 3 && f % 100 <= 4) return 3;\n    return 5;\n  }\n  exports.default = ['dsb', [['dop.', 'wótp.'], ['dopołdnja', 'wótpołdnja'], u], [['dopołdnja', 'wótpołdnja'], u, u], [['n', 'p', 'w', 's', 's', 'p', 's'], ['nje', 'pón', 'wał', 'srj', 'stw', 'pět', 'sob'], ['njeźela', 'pónjeźele', 'wałtora', 'srjoda', 'stwórtk', 'pětk', 'sobota'], ['nj', 'pó', 'wa', 'sr', 'st', 'pě', 'so']], u, [['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'], ['jan.', 'feb.', 'měr.', 'apr.', 'maj.', 'jun.', 'jul.', 'awg.', 'sep.', 'okt.', 'now.', 'dec.'], ['januara', 'februara', 'měrca', 'apryla', 'maja', 'junija', 'julija', 'awgusta', 'septembra', 'oktobra', 'nowembra', 'decembra']], [['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'], ['jan', 'feb', 'měr', 'apr', 'maj', 'jun', 'jul', 'awg', 'sep', 'okt', 'now', 'dec'], ['januar', 'februar', 'měrc', 'apryl', 'maj', 'junij', 'julij', 'awgust', 'september', 'oktober', 'nowember', 'december']], [['pś.Chr.n.', 'pó Chr.n.'], u, ['pśed Kristusowym naroźenim', 'pó Kristusowem naroźenju']], 1, [6, 0], ['d.M.yy', 'd.M.y', 'd. MMMM y', 'EEEE, d. MMMM y'], ['H:mm', 'H:mm:ss', 'H:mm:ss z', 'H:mm:ss zzzz'], ['{1} {0}', u, u, u], [',', '.', ';', '%', '+', '-', 'E', '·', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'], 'EUR', '€', 'euro', {\n    'AUD': [u, '$'],\n    'PLN': ['zł'],\n    'THB': ['฿']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "i", "Math", "floor", "abs", "toString", "replace", "length", "f", "parseInt", "default"], "sources": ["c:/Temp/node_modules/@angular/common/locales/dsb.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/dsb\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        var i = Math.floor(Math.abs(n)), v = n.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(n.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n        if (v === 0 && i % 100 === 1 || f % 100 === 1)\n            return 1;\n        if (v === 0 && i % 100 === 2 || f % 100 === 2)\n            return 2;\n        if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 ||\n            f % 100 === Math.floor(f % 100) && f % 100 >= 3 && f % 100 <= 4)\n            return 3;\n        return 5;\n    }\n    exports.default = [\n        'dsb',\n        [['dop.', 'wótp.'], ['dopołdnja', 'wótpołdnja'], u],\n        [['dopołdnja', 'wótpołdnja'], u, u],\n        [\n            ['n', 'p', 'w', 's', 's', 'p', 's'], ['nje', 'pón', 'wał', 'srj', 'stw', 'pět', 'sob'],\n            ['njeźela', 'pónjeźele', 'wałtora', 'srjoda', 'stwórtk', 'pětk', 'sobota'],\n            ['nj', 'pó', 'wa', 'sr', 'st', 'pě', 'so']\n        ],\n        u,\n        [\n            ['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n            [\n                'jan.', 'feb.', 'měr.', 'apr.', 'maj.', 'jun.', 'jul.', 'awg.', 'sep.', 'okt.', 'now.', 'dec.'\n            ],\n            [\n                'januara', 'februara', 'měrca', 'apryla', 'maja', 'junija', 'julija', 'awgusta', 'septembra',\n                'oktobra', 'nowembra', 'decembra'\n            ]\n        ],\n        [\n            ['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n            ['jan', 'feb', 'měr', 'apr', 'maj', 'jun', 'jul', 'awg', 'sep', 'okt', 'now', 'dec'],\n            [\n                'januar', 'februar', 'měrc', 'apryl', 'maj', 'junij', 'julij', 'awgust', 'september',\n                'oktober', 'nowember', 'december'\n            ]\n        ],\n        [['pś.Chr.n.', 'pó Chr.n.'], u, ['pśed Kristusowym naroźenim', 'pó Kristusowem naroźenju']],\n        1,\n        [6, 0],\n        ['d.M.yy', 'd.M.y', 'd. MMMM y', 'EEEE, d. MMMM y'],\n        ['H:mm', 'H:mm:ss', 'H:mm:ss z', 'H:mm:ss zzzz'],\n        ['{1} {0}', u, u, u],\n        [',', '.', ';', '%', '+', '-', 'E', '·', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'],\n        'EUR',\n        '€',\n        'euro',\n        { 'AUD': [u, '$'], 'PLN': ['zł'], 'THB': ['฿'] },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,6BAA6B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC1E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;MAAEV,CAAC,GAAGU,CAAC,CAACK,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;MAAEC,CAAC,GAAGC,QAAQ,CAACT,CAAC,CAACK,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;IAC/I,IAAIhB,CAAC,KAAK,CAAC,IAAIW,CAAC,GAAG,GAAG,KAAK,CAAC,IAAIO,CAAC,GAAG,GAAG,KAAK,CAAC,EACzC,OAAO,CAAC;IACZ,IAAIlB,CAAC,KAAK,CAAC,IAAIW,CAAC,GAAG,GAAG,KAAK,CAAC,IAAIO,CAAC,GAAG,GAAG,KAAK,CAAC,EACzC,OAAO,CAAC;IACZ,IAAIlB,CAAC,KAAK,CAAC,IAAIW,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,IAC1EO,CAAC,GAAG,GAAG,KAAKN,IAAI,CAACC,KAAK,CAACK,CAAC,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,EAC/D,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAnB,OAAO,CAACqB,OAAO,GAAG,CACd,KAAK,EACL,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC,EAAEZ,CAAC,CAAC,EACnD,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACnC,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACtF,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,EAC1E,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAC7C,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CACjG,EACD,CACI,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAC5F,SAAS,EAAE,UAAU,EAAE,UAAU,CACpC,CACJ,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACpF,CACI,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EACpF,SAAS,EAAE,UAAU,EAAE,UAAU,CACpC,CACJ,EACD,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAEA,CAAC,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,CAAC,CAAC,EAC3F,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAAC,EACnD,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EAChD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAC7C,KAAK,EACL,GAAG,EACH,MAAM,EACN;IAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,IAAI,CAAC;IAAE,KAAK,EAAE,CAAC,GAAG;EAAE,CAAC,EAChD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}