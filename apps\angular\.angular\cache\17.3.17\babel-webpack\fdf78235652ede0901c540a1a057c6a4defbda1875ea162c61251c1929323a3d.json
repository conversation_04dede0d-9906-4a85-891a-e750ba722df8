{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { getDocument, isFunction } from \"@microsoft/applicationinsights-core-js\";\nexport function createDomEvent(eventName) {\n  var event = null;\n  if (isFunction(Event)) {\n    // Use Event constructor when available\n    event = new Event(eventName);\n  } else {\n    // Event has no constructor in IE\n    var doc = getDocument();\n    if (doc && doc.createEvent) {\n      event = doc.createEvent(\"Event\");\n      event.initEvent(eventName, true, true);\n    }\n  }\n  return event;\n}\n//# sourceMappingURL=DomHelperFuncs.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}