{"ast": null, "code": "import cloneObject from 'date-fns/_lib/cloneObject/index.js';\nimport format from '../format/index.js';\nimport utcToZonedTime from '../utcToZonedTime/index.js';\n\n/**\r\n * @name formatInTimeZone\r\n * @category Time Zone Helpers\r\n * @summary Gets the offset in milliseconds between the time zone and Universal Coordinated Time (UTC)\r\n *\r\n * @param {Date|String|Number} date - the date representing the local time / real UTC time\r\n * @param {String} timeZone - the time zone this date should be formatted for; can be an offset or IANA time zone\r\n * @param {String} formatStr - the string of tokens\r\n * @param {OptionsWithTZ} [options] - the object with options. See [Options]{@link https://date-fns.org/docs/Options}\r\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link\r\n *   https://date-fns.org/docs/toDate}\r\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\r\n * @param {Number} [options.firstWeekContainsDate=1] - the day of January, which is\r\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See\r\n *   [Locale]{@link https://date-fns.org/docs/Locale}\r\n * @param {Boolean} [options.awareOfUnicodeTokens=false] - if true, allows usage of Unicode tokens causes confusion:\r\n *   - Some of the day of year tokens (`D`, `DD`) that are confused with the day of month tokens (`d`, `dd`).\r\n *   - Some of the local week-numbering year tokens (`YY`, `YYYY`) that are confused with the calendar year tokens\r\n *   (`yy`, `yyyy`). See: https://git.io/fxCyr\r\n * @param {String} [options.timeZone=''] - used to specify the IANA time zone offset of a date String.\r\n * @returns {String} the formatted date string\r\n */\nexport default function formatInTimeZone(date, timeZone, formatStr, options) {\n  var extendedOptions = cloneObject(options);\n  extendedOptions.timeZone = timeZone;\n  extendedOptions.originalDate = date;\n  return format(utcToZonedTime(date, timeZone), formatStr, extendedOptions);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}