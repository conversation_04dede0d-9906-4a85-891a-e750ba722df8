{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"sn\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"S\", \"M\", \"C\", \"C\", \"C\", \"C\", \"M\"], [\"Svo\", \"Muv\", \"Chp\", \"Cht\", \"Chn\", \"Chs\", \"Mug\"], [\"<PERSON>von<PERSON>\", \"<PERSON>vhuro\", \"Chip<PERSON>\", \"Chitatu\", \"China\", \"Chishanu\", \"Mugovera\"], [\"Sv\", \"Mu\", \"Cp\", \"Ct\", \"Cn\", \"Cs\", \"Mg\"]], u, [[\"N\", \"K\", \"K\", \"K\", \"C\", \"C\", \"C\", \"N\", \"G\", \"G\", \"M\", \"Z\"], [\"Ndi\", \"Kuk\", \"Kur\", \"Kub\", \"Chv\", \"<PERSON>k\", \"<PERSON>g\", \"<PERSON>ya\", \"<PERSON>\", \"Gum\", \"Mbu\", \"<PERSON>vi\"], [\"N<PERSON>\", \"<PERSON>kadzi\", \"<PERSON>rume\", \"<PERSON>bvumbi\", \"<PERSON>vabvu\", \"<PERSON>kumi\", \"<PERSON>kunguru\", \"<PERSON>yamavhuvhu\", \"<PERSON>yana\", \"<PERSON><PERSON>guru\", \"<PERSON>budzi\", \"<PERSON>vita\"]], u, [[\"BC\", \"AD\"], u, [\"Kristo asati auya\", \"mugore ramambo vedu\"]], 0, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"USD\", \"US$\", \"Dora re Amerika\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/sn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"sn\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"S\", \"M\", \"C\", \"C\", \"C\", \"C\", \"M\"], [\"Svo\", \"Muv\", \"Chp\", \"Cht\", \"Chn\", \"Chs\", \"Mug\"], [\"<PERSON>von<PERSON>\", \"<PERSON>vhuro\", \"Chip<PERSON>\", \"Chitatu\", \"China\", \"Chishanu\", \"Mugovera\"], [\"Sv\", \"Mu\", \"Cp\", \"Ct\", \"Cn\", \"Cs\", \"Mg\"]], u, [[\"N\", \"K\", \"K\", \"K\", \"C\", \"C\", \"C\", \"N\", \"G\", \"G\", \"M\", \"Z\"], [\"Ndi\", \"Kuk\", \"Kur\", \"Kub\", \"Chv\", \"<PERSON>k\", \"<PERSON>g\", \"<PERSON>ya\", \"<PERSON>\", \"Gum\", \"Mbu\", \"<PERSON>vi\"], [\"N<PERSON>\", \"<PERSON>kadzi\", \"<PERSON>rume\", \"<PERSON>bvumbi\", \"<PERSON>vabvu\", \"<PERSON>kumi\", \"<PERSON>kunguru\", \"<PERSON>yamavhuvhu\", \"<PERSON>yana\", \"<PERSON><PERSON>guru\", \"<PERSON>budzi\", \"<PERSON>vita\"]], u, [[\"BC\", \"AD\"], u, [\"Kristo asati auya\", \"mugore ramambo vedu\"]], 0, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"USD\", \"US$\", \"Dora re Amerika\", { \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}