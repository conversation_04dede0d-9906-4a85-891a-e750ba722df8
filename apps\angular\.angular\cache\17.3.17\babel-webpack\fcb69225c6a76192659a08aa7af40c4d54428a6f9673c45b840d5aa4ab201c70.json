{"ast": null, "code": "import { SelectionModel, isDataSource } from '@angular/cdk/collections';\nimport { isObservable, Subject, BehaviorSubject, of } from 'rxjs';\nimport { take, filter, takeUntil } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, numberAttribute, booleanAttribute, NgModule } from '@angular/core';\nimport * as i2 from '@angular/cdk/bidi';\n\n/** Base tree control. It has basic toggle/expand/collapse operations on a single data node. */\nclass BaseTreeControl {\n  constructor() {\n    /** A selection model with multi-selection to track expansion status. */\n    this.expansionModel = new SelectionModel(true);\n  }\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode) {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n  /** Expands one single data node. */\n  expand(dataNode) {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n  /** Collapses one single data node. */\n  collapse(dataNode) {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode) {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode) {\n    this.expansionModel.isSelected(this._trackByValue(dataNode)) ? this.collapseDescendants(dataNode) : this.expandDescendants(dataNode);\n  }\n  /** Collapse all dataNodes in the tree. */\n  collapseAll() {\n    this.expansionModel.clear();\n  }\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  _trackByValue(value) {\n    return this.trackBy ? this.trackBy(value) : value;\n  }\n}\n\n/** Flat tree control. Able to expand/collapse a subtree recursively for flattened tree. */\nclass FlatTreeControl extends BaseTreeControl {\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(getLevel, isExpandable, options) {\n    super();\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode) {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results = [];\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n\n/** Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type. */\nclass NestedTreeControl extends BaseTreeControl {\n  /** Construct with nested tree function getChildren. */\n  constructor(getChildren, options) {\n    super();\n    this.getChildren = getChildren;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode) {\n    const descendants = [];\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n  /** A helper function to get descendants recursively. */\n  _getDescendants(descendants, dataNode) {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach(child => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass CdkTreeNodeOutlet {\n  constructor(viewContainer, _node) {\n    this.viewContainer = viewContainer;\n    this._node = _node;\n  }\n  static {\n    this.ɵfac = function CdkTreeNodeOutlet_Factory(t) {\n      return new (t || CdkTreeNodeOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_TREE_NODE_OUTLET_NODE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodeOutlet,\n      selectors: [[\"\", \"cdkTreeNodeOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TREE_NODE_OUTLET_NODE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n  constructor(data) {\n    this.$implicit = data;\n  }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass CdkTreeNodeDef {\n  /** @docs-private */\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkTreeNodeDef_Factory(t) {\n      return new (t || CdkTreeNodeDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodeDef,\n      selectors: [[\"\", \"cdkTreeNodeDef\", \"\"]],\n      inputs: {\n        when: [i0.ɵɵInputFlags.None, \"cdkTreeNodeDefWhen\", \"when\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'cdkTreeNodeDefWhen'\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there are tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n  return Error(`Could not find a tree control for the tree.`);\n}\n/**\n * Returns an error to be thrown when tree control did not implement functions for flat/nested node.\n * @docs-private\n */\nfunction getTreeControlFunctionsMissingError() {\n  return Error(`Could not find functions for nested/flat tree in tree control.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nclass CdkTree {\n  /**\n   * Provides a stream containing the latest data array to render. Influenced by the tree's\n   * stream of view window (what dataNodes are currently on screen).\n   * Data source can be an observable of data array, or a data array to render.\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  constructor(_differs, _changeDetectorRef) {\n    this._differs = _differs;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Subject that emits when the component has been destroyed. */\n    this._onDestroy = new Subject();\n    /** Level of nodes */\n    this._levels = new Map();\n    // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n    //     Remove the MAX_VALUE in viewChange\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     */\n    this.viewChange = new BehaviorSubject({\n      start: 0,\n      end: Number.MAX_VALUE\n    });\n  }\n  ngOnInit() {\n    this._dataDiffer = this._differs.find([]).create(this.trackBy);\n    if (!this.treeControl && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlMissingError();\n    }\n  }\n  ngOnDestroy() {\n    this._nodeOutlet.viewContainer.clear();\n    this.viewChange.complete();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n  }\n  ngAfterContentChecked() {\n    const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n    if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMultipleDefaultNodeDefsError();\n    }\n    this._defaultNodeDef = defaultNodeDefs[0];\n    if (this.dataSource && this._nodeDefs && !this._dataSubscription) {\n      this._observeRenderChanges();\n    }\n  }\n  // TODO(tinayuangao): Work on keyboard traversal and actions, make sure it's working for RTL\n  //     and nested trees.\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the node outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n    // Remove the all dataNodes if there is now no data source\n    if (!dataSource) {\n      this._nodeOutlet.viewContainer.clear();\n    }\n    this._dataSource = dataSource;\n    if (this._nodeDefs) {\n      this._observeRenderChanges();\n    }\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    let dataStream;\n    if (isDataSource(this._dataSource)) {\n      dataStream = this._dataSource.connect(this);\n    } else if (isObservable(this._dataSource)) {\n      dataStream = this._dataSource;\n    } else if (Array.isArray(this._dataSource)) {\n      dataStream = of(this._dataSource);\n    }\n    if (dataStream) {\n      this._dataSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => this.renderNodeChanges(data));\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTreeNoValidDataSourceError();\n    }\n  }\n  /** Check for changes made in the data and render each change (node added/removed/moved). */\n  renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n    const changes = dataDiffer.diff(data);\n    if (!changes) {\n      return;\n    }\n    changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n      if (item.previousIndex == null) {\n        this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n      } else if (currentIndex == null) {\n        viewContainer.remove(adjustedPreviousIndex);\n        this._levels.delete(item.item);\n      } else {\n        const view = viewContainer.get(adjustedPreviousIndex);\n        viewContainer.move(view, currentIndex);\n      }\n    });\n    this._changeDetectorRef.detectChanges();\n  }\n  /**\n   * Finds the matching node definition that should be used for this node data. If there is only\n   * one node definition, it is returned. Otherwise, find the node definition that has a when\n   * predicate that returns true with the data. If none return true, return the default node\n   * definition.\n   */\n  _getNodeDef(data, i) {\n    if (this._nodeDefs.length === 1) {\n      return this._nodeDefs.first;\n    }\n    const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n    if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMissingMatchingNodeDefError();\n    }\n    return nodeDef;\n  }\n  /**\n   * Create the embedded view for the data node template and place it in the correct index location\n   * within the data node view container.\n   */\n  insertNode(nodeData, index, viewContainer, parentData) {\n    const node = this._getNodeDef(nodeData, index);\n    // Node context that will be provided to created embedded view\n    const context = new CdkTreeNodeOutletContext(nodeData);\n    // If the tree is flat tree, then use the `getLevel` function in flat tree control\n    // Otherwise, use the level of parent node.\n    if (this.treeControl.getLevel) {\n      context.level = this.treeControl.getLevel(nodeData);\n    } else if (typeof parentData !== 'undefined' && this._levels.has(parentData)) {\n      context.level = this._levels.get(parentData) + 1;\n    } else {\n      context.level = 0;\n    }\n    this._levels.set(nodeData, context.level);\n    // Use default tree nodeOutlet, or nested node's nodeOutlet\n    const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n    container.createEmbeddedView(node.template, context, index);\n    // Set the data to just created `CdkTreeNode`.\n    // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n    //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n    if (CdkTreeNode.mostRecentTreeNode) {\n      CdkTreeNode.mostRecentTreeNode.data = nodeData;\n    }\n  }\n  static {\n    this.ɵfac = function CdkTree_Factory(t) {\n      return new (t || CdkTree)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkTree,\n      selectors: [[\"cdk-tree\"]],\n      contentQueries: function CdkTree_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeDef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeDefs = _t);\n        }\n      },\n      viewQuery: function CdkTree_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkTreeNodeOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"tree\", 1, \"cdk-tree\"],\n      inputs: {\n        dataSource: \"dataSource\",\n        treeControl: \"treeControl\",\n        trackBy: \"trackBy\"\n      },\n      exportAs: [\"cdkTree\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkTreeNodeOutlet\", \"\"]],\n      template: function CdkTree_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkTreeNodeOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTree, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-tree',\n      exportAs: 'cdkTree',\n      template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'cdk-tree',\n        'role': 'tree'\n      },\n      encapsulation: ViewEncapsulation.None,\n      // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n      // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n      // declared elsewhere, they are checked when their declaration points are checked.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [CdkTreeNodeOutlet]\n    }]\n  }], () => [{\n    type: i0.IterableDiffers\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    dataSource: [{\n      type: Input\n    }],\n    treeControl: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [CdkTreeNodeOutlet, {\n        static: true\n      }]\n    }],\n    _nodeDefs: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeDef, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nclass CdkTreeNode {\n  /**\n   * The role of the tree node.\n   * @deprecated The correct role is 'treeitem', 'group' should not be used. This input will be\n   *   removed in a future version.\n   * @breaking-change 12.0.0 Remove this input\n   */\n  get role() {\n    return 'treeitem';\n  }\n  set role(_role) {\n    // TODO: move to host after View Engine deprecation\n    this._elementRef.nativeElement.setAttribute('role', _role);\n  }\n  /**\n   * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n   * in `CdkTree` and set the data to it.\n   */\n  static {\n    this.mostRecentTreeNode = null;\n  }\n  /** The tree node's data. */\n  get data() {\n    return this._data;\n  }\n  set data(value) {\n    if (value !== this._data) {\n      this._data = value;\n      this._setRoleFromData();\n      this._dataChanges.next();\n    }\n  }\n  get isExpanded() {\n    return this._tree.treeControl.isExpanded(this._data);\n  }\n  get level() {\n    // If the treeControl has a getLevel method, use it to get the level. Otherwise read the\n    // aria-level off the parent node and use it as the level for this node (note aria-level is\n    // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n    return this._tree.treeControl.getLevel ? this._tree.treeControl.getLevel(this._data) : this._parentNodeAriaLevel;\n  }\n  constructor(_elementRef, _tree) {\n    this._elementRef = _elementRef;\n    this._tree = _tree;\n    /** Subject that emits when the component has been destroyed. */\n    this._destroyed = new Subject();\n    /** Emits when the node's data has changed. */\n    this._dataChanges = new Subject();\n    CdkTreeNode.mostRecentTreeNode = this;\n    this.role = 'treeitem';\n  }\n  ngOnInit() {\n    this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n    this._elementRef.nativeElement.setAttribute('aria-level', `${this.level + 1}`);\n  }\n  ngOnDestroy() {\n    // If this is the last tree node being destroyed,\n    // clear out the reference to avoid leaking memory.\n    if (CdkTreeNode.mostRecentTreeNode === this) {\n      CdkTreeNode.mostRecentTreeNode = null;\n    }\n    this._dataChanges.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Focuses the menu item. Implements for FocusableOption. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  // TODO: role should eventually just be set in the component host\n  _setRoleFromData() {\n    if (!this._tree.treeControl.isExpandable && !this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlFunctionsMissingError();\n    }\n    this.role = 'treeitem';\n  }\n  static {\n    this.ɵfac = function CdkTreeNode_Factory(t) {\n      return new (t || CdkTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CdkTree));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNode,\n      selectors: [[\"cdk-tree-node\"]],\n      hostAttrs: [1, \"cdk-tree-node\"],\n      hostVars: 1,\n      hostBindings: function CdkTreeNode_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-expanded\", ctx.isExpanded);\n        }\n      },\n      inputs: {\n        role: \"role\"\n      },\n      exportAs: [\"cdkTreeNode\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-tree-node',\n      exportAs: 'cdkTreeNode',\n      host: {\n        'class': 'cdk-tree-node',\n        '[attr.aria-expanded]': 'isExpanded'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: CdkTree\n  }], {\n    role: [{\n      type: Input\n    }]\n  });\n})();\nfunction getParentNodeAriaLevel(nodeElement) {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level'));\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\nfunction isNodeElement(element) {\n  const classList = element.classList;\n  return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nclass CdkNestedTreeNode extends CdkTreeNode {\n  constructor(elementRef, tree, _differs) {\n    super(elementRef, tree);\n    this._differs = _differs;\n  }\n  ngAfterContentInit() {\n    this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n    if (!this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlFunctionsMissingError();\n    }\n    const childrenNodes = this._tree.treeControl.getChildren(this.data);\n    if (Array.isArray(childrenNodes)) {\n      this.updateChildrenNodes(childrenNodes);\n    } else if (isObservable(childrenNodes)) {\n      childrenNodes.pipe(takeUntil(this._destroyed)).subscribe(result => this.updateChildrenNodes(result));\n    }\n    this.nodeOutlet.changes.pipe(takeUntil(this._destroyed)).subscribe(() => this.updateChildrenNodes());\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngOnDestroy() {\n    this._clear();\n    super.ngOnDestroy();\n  }\n  /** Add children dataNodes to the NodeOutlet */\n  updateChildrenNodes(children) {\n    const outlet = this._getNodeOutlet();\n    if (children) {\n      this._children = children;\n    }\n    if (outlet && this._children) {\n      const viewContainer = outlet.viewContainer;\n      this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n    } else {\n      // Reset the data differ if there's no children nodes displayed\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Clear the children dataNodes. */\n  _clear() {\n    const outlet = this._getNodeOutlet();\n    if (outlet) {\n      outlet.viewContainer.clear();\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Gets the outlet for the current node. */\n  _getNodeOutlet() {\n    const outlets = this.nodeOutlet;\n    // Note that since we use `descendants: true` on the query, we have to ensure\n    // that we don't pick up the outlet of a child node by accident.\n    return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n  }\n  static {\n    this.ɵfac = function CdkNestedTreeNode_Factory(t) {\n      return new (t || CdkNestedTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkNestedTreeNode,\n      selectors: [[\"cdk-nested-tree-node\"]],\n      contentQueries: function CdkNestedTreeNode_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeOutlet, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeOutlet = _t);\n        }\n      },\n      hostAttrs: [1, \"cdk-nested-tree-node\"],\n      exportAs: [\"cdkNestedTreeNode\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNode,\n        useExisting: CdkNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: CdkNestedTreeNode\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-nested-tree-node',\n      exportAs: 'cdkNestedTreeNode',\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: CdkNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: CdkNestedTreeNode\n      }],\n      host: {\n        'class': 'cdk-nested-tree-node'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: CdkTree\n  }, {\n    type: i0.IterableDiffers\n  }], {\n    nodeOutlet: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeOutlet, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nclass CdkTreeNodePadding {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  /**\n   * The indent for each level. Can be a number or a CSS string.\n   * Default number 40px from material design menu sub-menu spec.\n   */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n  constructor(_treeNode, _tree, _element, _dir) {\n    this._treeNode = _treeNode;\n    this._tree = _tree;\n    this._element = _element;\n    this._dir = _dir;\n    /** Subject that emits when the component has been destroyed. */\n    this._destroyed = new Subject();\n    /** CSS units used for the indentation value. */\n    this.indentUnits = 'px';\n    this._indent = 40;\n    this._setPadding();\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n    }\n    // In Ivy the indentation binding might be set before the tree node's data has been added,\n    // which means that we'll miss the first render. We have to subscribe to changes in the\n    // data to ensure that everything is up to date.\n    _treeNode._dataChanges.subscribe(() => this._setPadding());\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n  _paddingIndent() {\n    const nodeLevel = this._treeNode.data && this._tree.treeControl.getLevel ? this._tree.treeControl.getLevel(this._treeNode.data) : null;\n    const level = this._level == null ? nodeLevel : this._level;\n    return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n  }\n  _setPadding(forceChange = false) {\n    const padding = this._paddingIndent();\n    if (padding !== this._currentPadding || forceChange) {\n      const element = this._element.nativeElement;\n      const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n      const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n      element.style[paddingProp] = padding || '';\n      element.style[resetProp] = '';\n      this._currentPadding = padding;\n    }\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setLevelInput(value) {\n    // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n    // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n    // they set 0 explicitly.\n    this._level = isNaN(value) ? null : value;\n    this._setPadding();\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setIndentInput(indent) {\n    let value = indent;\n    let units = 'px';\n    if (typeof indent === 'string') {\n      const parts = indent.split(cssUnitPattern);\n      value = parts[0];\n      units = parts[1] || units;\n    }\n    this.indentUnits = units;\n    this._indent = numberAttribute(value);\n    this._setPadding();\n  }\n  static {\n    this.ɵfac = function CdkTreeNodePadding_Factory(t) {\n      return new (t || CdkTreeNodePadding)(i0.ɵɵdirectiveInject(CdkTreeNode), i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodePadding,\n      selectors: [[\"\", \"cdkTreeNodePadding\", \"\"]],\n      inputs: {\n        level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTreeNodePadding\", \"level\", numberAttribute],\n        indent: [i0.ɵɵInputFlags.None, \"cdkTreeNodePaddingIndent\", \"indent\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodePadding]',\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkTreeNode\n  }, {\n    type: CdkTree\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['cdkTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Node toggle to expand/collapse the node.\n */\nclass CdkTreeNodeToggle {\n  constructor(_tree, _treeNode) {\n    this._tree = _tree;\n    this._treeNode = _treeNode;\n    /** Whether expand/collapse the node recursively. */\n    this.recursive = false;\n  }\n  _toggle(event) {\n    this.recursive ? this._tree.treeControl.toggleDescendants(this._treeNode.data) : this._tree.treeControl.toggle(this._treeNode.data);\n    event.stopPropagation();\n  }\n  static {\n    this.ɵfac = function CdkTreeNodeToggle_Factory(t) {\n      return new (t || CdkTreeNodeToggle)(i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(CdkTreeNode));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodeToggle,\n      selectors: [[\"\", \"cdkTreeNodeToggle\", \"\"]],\n      hostBindings: function CdkTreeNodeToggle_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkTreeNodeToggle_click_HostBindingHandler($event) {\n            return ctx._toggle($event);\n          });\n        }\n      },\n      inputs: {\n        recursive: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeToggle]',\n      host: {\n        '(click)': '_toggle($event)'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkTree\n  }, {\n    type: CdkTreeNode\n  }], {\n    recursive: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodeToggleRecursive',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet];\nclass CdkTreeModule {\n  static {\n    this.ɵfac = function CdkTreeModule_Factory(t) {\n      return new (t || CdkTreeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkTreeModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: EXPORTED_DECLARATIONS,\n      exports: EXPORTED_DECLARATIONS\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getTreeControlFunctionsMissingError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };", "map": {"version": 3, "names": ["SelectionModel", "isDataSource", "isObservable", "Subject", "BehaviorSubject", "of", "take", "filter", "takeUntil", "i0", "InjectionToken", "Directive", "Inject", "Optional", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "ViewChild", "ContentChildren", "numberAttribute", "booleanAttribute", "NgModule", "i2", "BaseTreeControl", "constructor", "expansionModel", "toggle", "dataNode", "_trackByValue", "expand", "select", "collapse", "deselect", "isExpanded", "isSelected", "toggleDescendants", "collapseDescendants", "expandDescendants", "collapseAll", "clear", "toBeProcessed", "push", "getDescendants", "map", "value", "trackBy", "FlatTreeControl", "getLevel", "isExpandable", "options", "startIndex", "dataNodes", "indexOf", "results", "i", "length", "expandAll", "node", "NestedTreeControl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allNodes", "reduce", "accumulator", "descendants", "_getDescendants", "splice", "childrenNodes", "Array", "isArray", "for<PERSON>ach", "child", "pipe", "Boolean", "subscribe", "children", "CDK_TREE_NODE_OUTLET_NODE", "CdkTreeNodeOutlet", "viewContainer", "_node", "ɵfac", "CdkTreeNodeOutlet_Factory", "t", "ɵɵdirectiveInject", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "undefined", "decorators", "CdkTreeNodeOutletContext", "data", "$implicit", "CdkTreeNodeDef", "template", "CdkTreeNodeDef_Factory", "TemplateRef", "inputs", "when", "ɵɵInputFlags", "None", "name", "alias", "getTreeNoValidDataSourceError", "Error", "getTreeMultipleDefaultNodeDefsError", "getTreeMissingMatchingNodeDefError", "getTreeControlMissingError", "getTreeControlFunctionsMissingError", "CdkTree", "dataSource", "_dataSource", "_switchDataSource", "_differs", "_changeDetectorRef", "_onD<PERSON>roy", "_levels", "Map", "viewChange", "start", "end", "Number", "MAX_VALUE", "ngOnInit", "_data<PERSON><PERSON>er", "find", "create", "treeControl", "ngOnDestroy", "_nodeOutlet", "complete", "next", "disconnect", "_dataSubscription", "unsubscribe", "ngAfterContentChecked", "defaultNodeDefs", "_nodeDefs", "def", "_defaultNodeDef", "_observe<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataStream", "connect", "renderNodeChanges", "<PERSON><PERSON><PERSON><PERSON>", "parentData", "changes", "diff", "forEachOperation", "item", "adjustedPreviousIndex", "currentIndex", "previousIndex", "insertNode", "remove", "delete", "view", "get", "move", "detectChanges", "_getNodeDef", "first", "nodeDef", "nodeData", "index", "context", "level", "has", "set", "container", "createEmbeddedView", "CdkTreeNode", "mostRecentTreeNode", "CdkTree_Factory", "Iterable<PERSON><PERSON><PERSON>", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "CdkTree_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "CdkTree_Query", "ɵɵviewQuery", "hostAttrs", "exportAs", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "CdkTree_Template", "ɵɵelementContainer", "dependencies", "encapsulation", "host", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "static", "role", "_role", "_elementRef", "nativeElement", "setAttribute", "_data", "_setRoleFromData", "_dataChanges", "_tree", "_parentNodeAriaLevel", "_destroyed", "getParentNodeAriaLevel", "focus", "CdkTreeNode_Factory", "ElementRef", "hostVars", "hostBindings", "CdkTreeNode_HostBindings", "ɵɵattribute", "nodeElement", "parent", "parentElement", "isNodeElement", "classList", "contains", "getAttribute", "element", "CdkNestedTreeNode", "elementRef", "tree", "ngAfterContentInit", "updateChildrenNodes", "result", "nodeOutlet", "_clear", "outlet", "_getNodeOutlet", "_children", "outlets", "CdkNestedTreeNode_Factory", "CdkNestedTreeNode_ContentQueries", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "providers", "cssUnitPattern", "CdkTreeNodePadding", "_level", "_setLevelInput", "indent", "_indent", "_setIndentInput", "_treeNode", "_element", "_dir", "indentUnits", "_setPadding", "change", "_paddingIndent", "nodeLevel", "forceChange", "padding", "_currentPadding", "paddingProp", "resetProp", "style", "isNaN", "units", "parts", "split", "CdkTreeNodePadding_Factory", "Directionality", "HasDecoratorInputTransform", "ɵɵInputTransformsFeature", "transform", "CdkTreeNodeToggle", "recursive", "_toggle", "event", "stopPropagation", "CdkTreeNodeToggle_Factory", "CdkTreeNodeToggle_HostBindings", "ɵɵlistener", "CdkTreeNodeToggle_click_HostBindingHandler", "$event", "EXPORTED_DECLARATIONS", "CdkTreeModule", "CdkTreeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/cdk/fesm2022/tree.mjs"], "sourcesContent": ["import { SelectionModel, isDataSource } from '@angular/cdk/collections';\nimport { isObservable, Subject, BehaviorSubject, of } from 'rxjs';\nimport { take, filter, takeUntil } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, numberAttribute, booleanAttribute, NgModule } from '@angular/core';\nimport * as i2 from '@angular/cdk/bidi';\n\n/** Base tree control. It has basic toggle/expand/collapse operations on a single data node. */\nclass BaseTreeControl {\n    constructor() {\n        /** A selection model with multi-selection to track expansion status. */\n        this.expansionModel = new SelectionModel(true);\n    }\n    /** Toggles one single data node's expanded/collapsed state. */\n    toggle(dataNode) {\n        this.expansionModel.toggle(this._trackByValue(dataNode));\n    }\n    /** Expands one single data node. */\n    expand(dataNode) {\n        this.expansionModel.select(this._trackByValue(dataNode));\n    }\n    /** Collapses one single data node. */\n    collapse(dataNode) {\n        this.expansionModel.deselect(this._trackByValue(dataNode));\n    }\n    /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n    isExpanded(dataNode) {\n        return this.expansionModel.isSelected(this._trackByValue(dataNode));\n    }\n    /** Toggles a subtree rooted at `node` recursively. */\n    toggleDescendants(dataNode) {\n        this.expansionModel.isSelected(this._trackByValue(dataNode))\n            ? this.collapseDescendants(dataNode)\n            : this.expandDescendants(dataNode);\n    }\n    /** Collapse all dataNodes in the tree. */\n    collapseAll() {\n        this.expansionModel.clear();\n    }\n    /** Expands a subtree rooted at given data node recursively. */\n    expandDescendants(dataNode) {\n        let toBeProcessed = [dataNode];\n        toBeProcessed.push(...this.getDescendants(dataNode));\n        this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n    }\n    /** Collapses a subtree rooted at given data node recursively. */\n    collapseDescendants(dataNode) {\n        let toBeProcessed = [dataNode];\n        toBeProcessed.push(...this.getDescendants(dataNode));\n        this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n    }\n    _trackByValue(value) {\n        return this.trackBy ? this.trackBy(value) : value;\n    }\n}\n\n/** Flat tree control. Able to expand/collapse a subtree recursively for flattened tree. */\nclass FlatTreeControl extends BaseTreeControl {\n    /** Construct with flat tree data node functions getLevel and isExpandable. */\n    constructor(getLevel, isExpandable, options) {\n        super();\n        this.getLevel = getLevel;\n        this.isExpandable = isExpandable;\n        this.options = options;\n        if (this.options) {\n            this.trackBy = this.options.trackBy;\n        }\n    }\n    /**\n     * Gets a list of the data node's subtree of descendent data nodes.\n     *\n     * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n     * with correct levels.\n     */\n    getDescendants(dataNode) {\n        const startIndex = this.dataNodes.indexOf(dataNode);\n        const results = [];\n        // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n        // The level of descendants of a tree node must be greater than the level of the given\n        // tree node.\n        // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n        // If we reach a node whose level is greater than the level of the tree node, we hit a\n        // sibling of an ancestor.\n        for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n            results.push(this.dataNodes[i]);\n        }\n        return results;\n    }\n    /**\n     * Expands all data nodes in the tree.\n     *\n     * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n     * data nodes of the tree.\n     */\n    expandAll() {\n        this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n    }\n}\n\n/** Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type. */\nclass NestedTreeControl extends BaseTreeControl {\n    /** Construct with nested tree function getChildren. */\n    constructor(getChildren, options) {\n        super();\n        this.getChildren = getChildren;\n        this.options = options;\n        if (this.options) {\n            this.trackBy = this.options.trackBy;\n        }\n    }\n    /**\n     * Expands all dataNodes in the tree.\n     *\n     * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n     * data nodes of the tree.\n     */\n    expandAll() {\n        this.expansionModel.clear();\n        const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n        this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n    }\n    /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n    getDescendants(dataNode) {\n        const descendants = [];\n        this._getDescendants(descendants, dataNode);\n        // Remove the node itself\n        return descendants.splice(1);\n    }\n    /** A helper function to get descendants recursively. */\n    _getDescendants(descendants, dataNode) {\n        descendants.push(dataNode);\n        const childrenNodes = this.getChildren(dataNode);\n        if (Array.isArray(childrenNodes)) {\n            childrenNodes.forEach((child) => this._getDescendants(descendants, child));\n        }\n        else if (isObservable(childrenNodes)) {\n            // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n            // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n            childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n                for (const child of children) {\n                    this._getDescendants(descendants, child);\n                }\n            });\n        }\n    }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass CdkTreeNodeOutlet {\n    constructor(viewContainer, _node) {\n        this.viewContainer = viewContainer;\n        this._node = _node;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodeOutlet, deps: [{ token: i0.ViewContainerRef }, { token: CDK_TREE_NODE_OUTLET_NODE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkTreeNodeOutlet, isStandalone: true, selector: \"[cdkTreeNodeOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodeOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodeOutlet]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TREE_NODE_OUTLET_NODE]\n                }, {\n                    type: Optional\n                }] }] });\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n    constructor(data) {\n        this.$implicit = data;\n    }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass CdkTreeNodeDef {\n    /** @docs-private */\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodeDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkTreeNodeDef, isStandalone: true, selector: \"[cdkTreeNodeDef]\", inputs: { when: [\"cdkTreeNodeDefWhen\", \"when\"] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodeDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodeDef]',\n                    inputs: [{ name: 'when', alias: 'cdkTreeNodeDefWhen' }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n    return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n    return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n    return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there are tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n    return Error(`Could not find a tree control for the tree.`);\n}\n/**\n * Returns an error to be thrown when tree control did not implement functions for flat/nested node.\n * @docs-private\n */\nfunction getTreeControlFunctionsMissingError() {\n    return Error(`Could not find functions for nested/flat tree in tree control.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nclass CdkTree {\n    /**\n     * Provides a stream containing the latest data array to render. Influenced by the tree's\n     * stream of view window (what dataNodes are currently on screen).\n     * Data source can be an observable of data array, or a data array to render.\n     */\n    get dataSource() {\n        return this._dataSource;\n    }\n    set dataSource(dataSource) {\n        if (this._dataSource !== dataSource) {\n            this._switchDataSource(dataSource);\n        }\n    }\n    constructor(_differs, _changeDetectorRef) {\n        this._differs = _differs;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Subject that emits when the component has been destroyed. */\n        this._onDestroy = new Subject();\n        /** Level of nodes */\n        this._levels = new Map();\n        // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n        //     Remove the MAX_VALUE in viewChange\n        /**\n         * Stream containing the latest information on what rows are being displayed on screen.\n         * Can be used by the data source to as a heuristic of what data should be provided.\n         */\n        this.viewChange = new BehaviorSubject({\n            start: 0,\n            end: Number.MAX_VALUE,\n        });\n    }\n    ngOnInit() {\n        this._dataDiffer = this._differs.find([]).create(this.trackBy);\n        if (!this.treeControl && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeControlMissingError();\n        }\n    }\n    ngOnDestroy() {\n        this._nodeOutlet.viewContainer.clear();\n        this.viewChange.complete();\n        this._onDestroy.next();\n        this._onDestroy.complete();\n        if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n            this.dataSource.disconnect(this);\n        }\n        if (this._dataSubscription) {\n            this._dataSubscription.unsubscribe();\n            this._dataSubscription = null;\n        }\n    }\n    ngAfterContentChecked() {\n        const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n        if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeMultipleDefaultNodeDefsError();\n        }\n        this._defaultNodeDef = defaultNodeDefs[0];\n        if (this.dataSource && this._nodeDefs && !this._dataSubscription) {\n            this._observeRenderChanges();\n        }\n    }\n    // TODO(tinayuangao): Work on keyboard traversal and actions, make sure it's working for RTL\n    //     and nested trees.\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the node outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n        if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n            this.dataSource.disconnect(this);\n        }\n        if (this._dataSubscription) {\n            this._dataSubscription.unsubscribe();\n            this._dataSubscription = null;\n        }\n        // Remove the all dataNodes if there is now no data source\n        if (!dataSource) {\n            this._nodeOutlet.viewContainer.clear();\n        }\n        this._dataSource = dataSource;\n        if (this._nodeDefs) {\n            this._observeRenderChanges();\n        }\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n        let dataStream;\n        if (isDataSource(this._dataSource)) {\n            dataStream = this._dataSource.connect(this);\n        }\n        else if (isObservable(this._dataSource)) {\n            dataStream = this._dataSource;\n        }\n        else if (Array.isArray(this._dataSource)) {\n            dataStream = of(this._dataSource);\n        }\n        if (dataStream) {\n            this._dataSubscription = dataStream\n                .pipe(takeUntil(this._onDestroy))\n                .subscribe(data => this.renderNodeChanges(data));\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getTreeNoValidDataSourceError();\n        }\n    }\n    /** Check for changes made in the data and render each change (node added/removed/moved). */\n    renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n        const changes = dataDiffer.diff(data);\n        if (!changes) {\n            return;\n        }\n        changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n            if (item.previousIndex == null) {\n                this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n            }\n            else if (currentIndex == null) {\n                viewContainer.remove(adjustedPreviousIndex);\n                this._levels.delete(item.item);\n            }\n            else {\n                const view = viewContainer.get(adjustedPreviousIndex);\n                viewContainer.move(view, currentIndex);\n            }\n        });\n        this._changeDetectorRef.detectChanges();\n    }\n    /**\n     * Finds the matching node definition that should be used for this node data. If there is only\n     * one node definition, it is returned. Otherwise, find the node definition that has a when\n     * predicate that returns true with the data. If none return true, return the default node\n     * definition.\n     */\n    _getNodeDef(data, i) {\n        if (this._nodeDefs.length === 1) {\n            return this._nodeDefs.first;\n        }\n        const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n        if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeMissingMatchingNodeDefError();\n        }\n        return nodeDef;\n    }\n    /**\n     * Create the embedded view for the data node template and place it in the correct index location\n     * within the data node view container.\n     */\n    insertNode(nodeData, index, viewContainer, parentData) {\n        const node = this._getNodeDef(nodeData, index);\n        // Node context that will be provided to created embedded view\n        const context = new CdkTreeNodeOutletContext(nodeData);\n        // If the tree is flat tree, then use the `getLevel` function in flat tree control\n        // Otherwise, use the level of parent node.\n        if (this.treeControl.getLevel) {\n            context.level = this.treeControl.getLevel(nodeData);\n        }\n        else if (typeof parentData !== 'undefined' && this._levels.has(parentData)) {\n            context.level = this._levels.get(parentData) + 1;\n        }\n        else {\n            context.level = 0;\n        }\n        this._levels.set(nodeData, context.level);\n        // Use default tree nodeOutlet, or nested node's nodeOutlet\n        const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n        container.createEmbeddedView(node.template, context, index);\n        // Set the data to just created `CdkTreeNode`.\n        // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n        //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n        if (CdkTreeNode.mostRecentTreeNode) {\n            CdkTreeNode.mostRecentTreeNode.data = nodeData;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTree, deps: [{ token: i0.IterableDiffers }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkTree, isStandalone: true, selector: \"cdk-tree\", inputs: { dataSource: \"dataSource\", treeControl: \"treeControl\", trackBy: \"trackBy\" }, host: { attributes: { \"role\": \"tree\" }, classAttribute: \"cdk-tree\" }, queries: [{ propertyName: \"_nodeDefs\", predicate: CdkTreeNodeDef, descendants: true }], viewQueries: [{ propertyName: \"_nodeOutlet\", first: true, predicate: CdkTreeNodeOutlet, descendants: true, static: true }], exportAs: [\"cdkTree\"], ngImport: i0, template: `<ng-container cdkTreeNodeOutlet></ng-container>`, isInline: true, dependencies: [{ kind: \"directive\", type: CdkTreeNodeOutlet, selector: \"[cdkTreeNodeOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTree, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-tree',\n                    exportAs: 'cdkTree',\n                    template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n                    host: {\n                        'class': 'cdk-tree',\n                        'role': 'tree',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n                    // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n                    // declared elsewhere, they are checked when their declaration points are checked.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    standalone: true,\n                    imports: [CdkTreeNodeOutlet],\n                }]\n        }], ctorParameters: () => [{ type: i0.IterableDiffers }, { type: i0.ChangeDetectorRef }], propDecorators: { dataSource: [{\n                type: Input\n            }], treeControl: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], _nodeOutlet: [{\n                type: ViewChild,\n                args: [CdkTreeNodeOutlet, { static: true }]\n            }], _nodeDefs: [{\n                type: ContentChildren,\n                args: [CdkTreeNodeDef, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nclass CdkTreeNode {\n    /**\n     * The role of the tree node.\n     * @deprecated The correct role is 'treeitem', 'group' should not be used. This input will be\n     *   removed in a future version.\n     * @breaking-change 12.0.0 Remove this input\n     */\n    get role() {\n        return 'treeitem';\n    }\n    set role(_role) {\n        // TODO: move to host after View Engine deprecation\n        this._elementRef.nativeElement.setAttribute('role', _role);\n    }\n    /**\n     * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n     * in `CdkTree` and set the data to it.\n     */\n    static { this.mostRecentTreeNode = null; }\n    /** The tree node's data. */\n    get data() {\n        return this._data;\n    }\n    set data(value) {\n        if (value !== this._data) {\n            this._data = value;\n            this._setRoleFromData();\n            this._dataChanges.next();\n        }\n    }\n    get isExpanded() {\n        return this._tree.treeControl.isExpanded(this._data);\n    }\n    get level() {\n        // If the treeControl has a getLevel method, use it to get the level. Otherwise read the\n        // aria-level off the parent node and use it as the level for this node (note aria-level is\n        // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n        return this._tree.treeControl.getLevel\n            ? this._tree.treeControl.getLevel(this._data)\n            : this._parentNodeAriaLevel;\n    }\n    constructor(_elementRef, _tree) {\n        this._elementRef = _elementRef;\n        this._tree = _tree;\n        /** Subject that emits when the component has been destroyed. */\n        this._destroyed = new Subject();\n        /** Emits when the node's data has changed. */\n        this._dataChanges = new Subject();\n        CdkTreeNode.mostRecentTreeNode = this;\n        this.role = 'treeitem';\n    }\n    ngOnInit() {\n        this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n        this._elementRef.nativeElement.setAttribute('aria-level', `${this.level + 1}`);\n    }\n    ngOnDestroy() {\n        // If this is the last tree node being destroyed,\n        // clear out the reference to avoid leaking memory.\n        if (CdkTreeNode.mostRecentTreeNode === this) {\n            CdkTreeNode.mostRecentTreeNode = null;\n        }\n        this._dataChanges.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Focuses the menu item. Implements for FocusableOption. */\n    focus() {\n        this._elementRef.nativeElement.focus();\n    }\n    // TODO: role should eventually just be set in the component host\n    _setRoleFromData() {\n        if (!this._tree.treeControl.isExpandable &&\n            !this._tree.treeControl.getChildren &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeControlFunctionsMissingError();\n        }\n        this.role = 'treeitem';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNode, deps: [{ token: i0.ElementRef }, { token: CdkTree }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkTreeNode, isStandalone: true, selector: \"cdk-tree-node\", inputs: { role: \"role\" }, host: { properties: { \"attr.aria-expanded\": \"isExpanded\" }, classAttribute: \"cdk-tree-node\" }, exportAs: [\"cdkTreeNode\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-tree-node',\n                    exportAs: 'cdkTreeNode',\n                    host: {\n                        'class': 'cdk-tree-node',\n                        '[attr.aria-expanded]': 'isExpanded',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: CdkTree }], propDecorators: { role: [{\n                type: Input\n            }] } });\nfunction getParentNodeAriaLevel(nodeElement) {\n    let parent = nodeElement.parentElement;\n    while (parent && !isNodeElement(parent)) {\n        parent = parent.parentElement;\n    }\n    if (!parent) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw Error('Incorrect tree structure containing detached node.');\n        }\n        else {\n            return -1;\n        }\n    }\n    else if (parent.classList.contains('cdk-nested-tree-node')) {\n        return numberAttribute(parent.getAttribute('aria-level'));\n    }\n    else {\n        // The ancestor element is the cdk-tree itself\n        return 0;\n    }\n}\nfunction isNodeElement(element) {\n    const classList = element.classList;\n    return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nclass CdkNestedTreeNode extends CdkTreeNode {\n    constructor(elementRef, tree, _differs) {\n        super(elementRef, tree);\n        this._differs = _differs;\n    }\n    ngAfterContentInit() {\n        this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n        if (!this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeControlFunctionsMissingError();\n        }\n        const childrenNodes = this._tree.treeControl.getChildren(this.data);\n        if (Array.isArray(childrenNodes)) {\n            this.updateChildrenNodes(childrenNodes);\n        }\n        else if (isObservable(childrenNodes)) {\n            childrenNodes\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(result => this.updateChildrenNodes(result));\n        }\n        this.nodeOutlet.changes\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.updateChildrenNodes());\n    }\n    // This is a workaround for https://github.com/angular/angular/issues/23091\n    // In aot mode, the lifecycle hooks from parent class are not called.\n    ngOnInit() {\n        super.ngOnInit();\n    }\n    ngOnDestroy() {\n        this._clear();\n        super.ngOnDestroy();\n    }\n    /** Add children dataNodes to the NodeOutlet */\n    updateChildrenNodes(children) {\n        const outlet = this._getNodeOutlet();\n        if (children) {\n            this._children = children;\n        }\n        if (outlet && this._children) {\n            const viewContainer = outlet.viewContainer;\n            this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n        }\n        else {\n            // Reset the data differ if there's no children nodes displayed\n            this._dataDiffer.diff([]);\n        }\n    }\n    /** Clear the children dataNodes. */\n    _clear() {\n        const outlet = this._getNodeOutlet();\n        if (outlet) {\n            outlet.viewContainer.clear();\n            this._dataDiffer.diff([]);\n        }\n    }\n    /** Gets the outlet for the current node. */\n    _getNodeOutlet() {\n        const outlets = this.nodeOutlet;\n        // Note that since we use `descendants: true` on the query, we have to ensure\n        // that we don't pick up the outlet of a child node by accident.\n        return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkNestedTreeNode, deps: [{ token: i0.ElementRef }, { token: CdkTree }, { token: i0.IterableDiffers }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkNestedTreeNode, isStandalone: true, selector: \"cdk-nested-tree-node\", host: { classAttribute: \"cdk-nested-tree-node\" }, providers: [\n            { provide: CdkTreeNode, useExisting: CdkNestedTreeNode },\n            { provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: CdkNestedTreeNode },\n        ], queries: [{ propertyName: \"nodeOutlet\", predicate: CdkTreeNodeOutlet, descendants: true }], exportAs: [\"cdkNestedTreeNode\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkNestedTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-nested-tree-node',\n                    exportAs: 'cdkNestedTreeNode',\n                    providers: [\n                        { provide: CdkTreeNode, useExisting: CdkNestedTreeNode },\n                        { provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: CdkNestedTreeNode },\n                    ],\n                    host: {\n                        'class': 'cdk-nested-tree-node',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: CdkTree }, { type: i0.IterableDiffers }], propDecorators: { nodeOutlet: [{\n                type: ContentChildren,\n                args: [CdkTreeNodeOutlet, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nclass CdkTreeNodePadding {\n    /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n    get level() {\n        return this._level;\n    }\n    set level(value) {\n        this._setLevelInput(value);\n    }\n    /**\n     * The indent for each level. Can be a number or a CSS string.\n     * Default number 40px from material design menu sub-menu spec.\n     */\n    get indent() {\n        return this._indent;\n    }\n    set indent(indent) {\n        this._setIndentInput(indent);\n    }\n    constructor(_treeNode, _tree, _element, _dir) {\n        this._treeNode = _treeNode;\n        this._tree = _tree;\n        this._element = _element;\n        this._dir = _dir;\n        /** Subject that emits when the component has been destroyed. */\n        this._destroyed = new Subject();\n        /** CSS units used for the indentation value. */\n        this.indentUnits = 'px';\n        this._indent = 40;\n        this._setPadding();\n        if (_dir) {\n            _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n        }\n        // In Ivy the indentation binding might be set before the tree node's data has been added,\n        // which means that we'll miss the first render. We have to subscribe to changes in the\n        // data to ensure that everything is up to date.\n        _treeNode._dataChanges.subscribe(() => this._setPadding());\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n    _paddingIndent() {\n        const nodeLevel = this._treeNode.data && this._tree.treeControl.getLevel\n            ? this._tree.treeControl.getLevel(this._treeNode.data)\n            : null;\n        const level = this._level == null ? nodeLevel : this._level;\n        return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n    }\n    _setPadding(forceChange = false) {\n        const padding = this._paddingIndent();\n        if (padding !== this._currentPadding || forceChange) {\n            const element = this._element.nativeElement;\n            const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n            const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n            element.style[paddingProp] = padding || '';\n            element.style[resetProp] = '';\n            this._currentPadding = padding;\n        }\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setLevelInput(value) {\n        // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n        // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n        // they set 0 explicitly.\n        this._level = isNaN(value) ? null : value;\n        this._setPadding();\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setIndentInput(indent) {\n        let value = indent;\n        let units = 'px';\n        if (typeof indent === 'string') {\n            const parts = indent.split(cssUnitPattern);\n            value = parts[0];\n            units = parts[1] || units;\n        }\n        this.indentUnits = units;\n        this._indent = numberAttribute(value);\n        this._setPadding();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodePadding, deps: [{ token: CdkTreeNode }, { token: CdkTree }, { token: i0.ElementRef }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkTreeNodePadding, isStandalone: true, selector: \"[cdkTreeNodePadding]\", inputs: { level: [\"cdkTreeNodePadding\", \"level\", numberAttribute], indent: [\"cdkTreeNodePaddingIndent\", \"indent\"] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodePadding, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodePadding]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkTreeNode }, { type: CdkTree }, { type: i0.ElementRef }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { level: [{\n                type: Input,\n                args: [{ alias: 'cdkTreeNodePadding', transform: numberAttribute }]\n            }], indent: [{\n                type: Input,\n                args: ['cdkTreeNodePaddingIndent']\n            }] } });\n\n/**\n * Node toggle to expand/collapse the node.\n */\nclass CdkTreeNodeToggle {\n    constructor(_tree, _treeNode) {\n        this._tree = _tree;\n        this._treeNode = _treeNode;\n        /** Whether expand/collapse the node recursively. */\n        this.recursive = false;\n    }\n    _toggle(event) {\n        this.recursive\n            ? this._tree.treeControl.toggleDescendants(this._treeNode.data)\n            : this._tree.treeControl.toggle(this._treeNode.data);\n        event.stopPropagation();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodeToggle, deps: [{ token: CdkTree }, { token: CdkTreeNode }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkTreeNodeToggle, isStandalone: true, selector: \"[cdkTreeNodeToggle]\", inputs: { recursive: [\"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute] }, host: { listeners: { \"click\": \"_toggle($event)\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeNodeToggle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodeToggle]',\n                    host: {\n                        '(click)': '_toggle($event)',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkTree }, { type: CdkTreeNode }], propDecorators: { recursive: [{\n                type: Input,\n                args: [{ alias: 'cdkTreeNodeToggleRecursive', transform: booleanAttribute }]\n            }] } });\n\nconst EXPORTED_DECLARATIONS = [\n    CdkNestedTreeNode,\n    CdkTreeNodeDef,\n    CdkTreeNodePadding,\n    CdkTreeNodeToggle,\n    CdkTree,\n    CdkTreeNode,\n    CdkTreeNodeOutlet,\n];\nclass CdkTreeModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeModule, imports: [CdkNestedTreeNode,\n            CdkTreeNodeDef,\n            CdkTreeNodePadding,\n            CdkTreeNodeToggle,\n            CdkTree,\n            CdkTreeNode,\n            CdkTreeNodeOutlet], exports: [CdkNestedTreeNode,\n            CdkTreeNodeDef,\n            CdkTreeNodePadding,\n            CdkTreeNodeToggle,\n            CdkTree,\n            CdkTreeNode,\n            CdkTreeNodeOutlet] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTreeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: EXPORTED_DECLARATIONS,\n                    exports: EXPORTED_DECLARATIONS,\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getTreeControlFunctionsMissingError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,YAAY,QAAQ,0BAA0B;AACvE,SAASC,YAAY,EAAEC,OAAO,EAAEC,eAAe,EAAEC,EAAE,QAAQ,MAAM;AACjE,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,eAAe;AAClN,OAAO,KAAKC,EAAE,MAAM,mBAAmB;;AAEvC;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,cAAc,GAAG,IAAI1B,cAAc,CAAC,IAAI,CAAC;EAClD;EACA;EACA2B,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACF,cAAc,CAACC,MAAM,CAAC,IAAI,CAACE,aAAa,CAACD,QAAQ,CAAC,CAAC;EAC5D;EACA;EACAE,MAAMA,CAACF,QAAQ,EAAE;IACb,IAAI,CAACF,cAAc,CAACK,MAAM,CAAC,IAAI,CAACF,aAAa,CAACD,QAAQ,CAAC,CAAC;EAC5D;EACA;EACAI,QAAQA,CAACJ,QAAQ,EAAE;IACf,IAAI,CAACF,cAAc,CAACO,QAAQ,CAAC,IAAI,CAACJ,aAAa,CAACD,QAAQ,CAAC,CAAC;EAC9D;EACA;EACAM,UAAUA,CAACN,QAAQ,EAAE;IACjB,OAAO,IAAI,CAACF,cAAc,CAACS,UAAU,CAAC,IAAI,CAACN,aAAa,CAACD,QAAQ,CAAC,CAAC;EACvE;EACA;EACAQ,iBAAiBA,CAACR,QAAQ,EAAE;IACxB,IAAI,CAACF,cAAc,CAACS,UAAU,CAAC,IAAI,CAACN,aAAa,CAACD,QAAQ,CAAC,CAAC,GACtD,IAAI,CAACS,mBAAmB,CAACT,QAAQ,CAAC,GAClC,IAAI,CAACU,iBAAiB,CAACV,QAAQ,CAAC;EAC1C;EACA;EACAW,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,cAAc,CAACc,KAAK,CAAC,CAAC;EAC/B;EACA;EACAF,iBAAiBA,CAACV,QAAQ,EAAE;IACxB,IAAIa,aAAa,GAAG,CAACb,QAAQ,CAAC;IAC9Ba,aAAa,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,cAAc,CAACf,QAAQ,CAAC,CAAC;IACpD,IAAI,CAACF,cAAc,CAACK,MAAM,CAAC,GAAGU,aAAa,CAACG,GAAG,CAACC,KAAK,IAAI,IAAI,CAAChB,aAAa,CAACgB,KAAK,CAAC,CAAC,CAAC;EACxF;EACA;EACAR,mBAAmBA,CAACT,QAAQ,EAAE;IAC1B,IAAIa,aAAa,GAAG,CAACb,QAAQ,CAAC;IAC9Ba,aAAa,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,cAAc,CAACf,QAAQ,CAAC,CAAC;IACpD,IAAI,CAACF,cAAc,CAACO,QAAQ,CAAC,GAAGQ,aAAa,CAACG,GAAG,CAACC,KAAK,IAAI,IAAI,CAAChB,aAAa,CAACgB,KAAK,CAAC,CAAC,CAAC;EAC1F;EACAhB,aAAaA,CAACgB,KAAK,EAAE;IACjB,OAAO,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACD,KAAK,CAAC,GAAGA,KAAK;EACrD;AACJ;;AAEA;AACA,MAAME,eAAe,SAASvB,eAAe,CAAC;EAC1C;EACAC,WAAWA,CAACuB,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACI,OAAO,CAACJ,OAAO;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIH,cAAcA,CAACf,QAAQ,EAAE;IACrB,MAAMuB,UAAU,GAAG,IAAI,CAACC,SAAS,CAACC,OAAO,CAACzB,QAAQ,CAAC;IACnD,MAAM0B,OAAO,GAAG,EAAE;IAClB;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIC,CAAC,GAAGJ,UAAU,GAAG,CAAC,EAAEI,CAAC,GAAG,IAAI,CAACH,SAAS,CAACI,MAAM,IAAI,IAAI,CAACR,QAAQ,CAACpB,QAAQ,CAAC,GAAG,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAACI,SAAS,CAACG,CAAC,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvHD,OAAO,CAACZ,IAAI,CAAC,IAAI,CAACU,SAAS,CAACG,CAAC,CAAC,CAAC;IACnC;IACA,OAAOD,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,SAASA,CAAA,EAAG;IACR,IAAI,CAAC/B,cAAc,CAACK,MAAM,CAAC,GAAG,IAAI,CAACqB,SAAS,CAACR,GAAG,CAACc,IAAI,IAAI,IAAI,CAAC7B,aAAa,CAAC6B,IAAI,CAAC,CAAC,CAAC;EACvF;AACJ;;AAEA;AACA,MAAMC,iBAAiB,SAASnC,eAAe,CAAC;EAC5C;EACAC,WAAWA,CAACmC,WAAW,EAAEV,OAAO,EAAE;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACU,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACV,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACI,OAAO,CAACJ,OAAO;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,SAASA,CAAA,EAAG;IACR,IAAI,CAAC/B,cAAc,CAACc,KAAK,CAAC,CAAC;IAC3B,MAAMqB,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACU,MAAM,CAAC,CAACC,WAAW,EAAEnC,QAAQ,KAAK,CAAC,GAAGmC,WAAW,EAAE,GAAG,IAAI,CAACpB,cAAc,CAACf,QAAQ,CAAC,EAAEA,QAAQ,CAAC,EAAE,EAAE,CAAC;IACnI,IAAI,CAACF,cAAc,CAACK,MAAM,CAAC,GAAG8B,QAAQ,CAACjB,GAAG,CAACc,IAAI,IAAI,IAAI,CAAC7B,aAAa,CAAC6B,IAAI,CAAC,CAAC,CAAC;EACjF;EACA;EACAf,cAAcA,CAACf,QAAQ,EAAE;IACrB,MAAMoC,WAAW,GAAG,EAAE;IACtB,IAAI,CAACC,eAAe,CAACD,WAAW,EAAEpC,QAAQ,CAAC;IAC3C;IACA,OAAOoC,WAAW,CAACE,MAAM,CAAC,CAAC,CAAC;EAChC;EACA;EACAD,eAAeA,CAACD,WAAW,EAAEpC,QAAQ,EAAE;IACnCoC,WAAW,CAACtB,IAAI,CAACd,QAAQ,CAAC;IAC1B,MAAMuC,aAAa,GAAG,IAAI,CAACP,WAAW,CAAChC,QAAQ,CAAC;IAChD,IAAIwC,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;MAC9BA,aAAa,CAACG,OAAO,CAAEC,KAAK,IAAK,IAAI,CAACN,eAAe,CAACD,WAAW,EAAEO,KAAK,CAAC,CAAC;IAC9E,CAAC,MACI,IAAIrE,YAAY,CAACiE,aAAa,CAAC,EAAE;MAClC;MACA;MACAA,aAAa,CAACK,IAAI,CAAClE,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACkE,OAAO,CAAC,CAAC,CAACC,SAAS,CAACC,QAAQ,IAAI;QAC/D,KAAK,MAAMJ,KAAK,IAAII,QAAQ,EAAE;UAC1B,IAAI,CAACV,eAAe,CAACD,WAAW,EAAEO,KAAK,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMK,yBAAyB,GAAG,IAAIlE,cAAc,CAAC,2BAA2B,CAAC;AACjF;AACA;AACA;AACA;AACA,MAAMmE,iBAAiB,CAAC;EACpBpD,WAAWA,CAACqD,aAAa,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFL,iBAAiB,EAA3BpE,EAAE,CAAA0E,iBAAA,CAA2C1E,EAAE,CAAC2E,gBAAgB,GAAhE3E,EAAE,CAAA0E,iBAAA,CAA2EP,yBAAyB;IAAA,CAA4D;EAAE;EACpQ;IAAS,IAAI,CAACS,IAAI,kBAD8E5E,EAAE,CAAA6E,iBAAA;MAAAC,IAAA,EACJV,iBAAiB;MAAAW,SAAA;MAAAC,UAAA;IAAA,EAAsE;EAAE;AAC3L;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGjF,EAAE,CAAAkF,iBAAA,CAGXd,iBAAiB,EAAc,CAAC;IAC/GU,IAAI,EAAE5E,SAAS;IACfiF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE9E,EAAE,CAAC2E;EAAiB,CAAC,EAAE;IAAEG,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9ER,IAAI,EAAE3E,MAAM;MACZgF,IAAI,EAAE,CAAChB,yBAAyB;IACpC,CAAC,EAAE;MACCW,IAAI,EAAE1E;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,MAAMmF,wBAAwB,CAAC;EAC3BvE,WAAWA,CAACwE,IAAI,EAAE;IACd,IAAI,CAACC,SAAS,GAAGD,IAAI;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,cAAc,CAAC;EACjB;EACA1E,WAAWA,CAAC2E,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACpB,IAAI,YAAAqB,uBAAAnB,CAAA;MAAA,YAAAA,CAAA,IAAwFiB,cAAc,EA/BxB1F,EAAE,CAAA0E,iBAAA,CA+BwC1E,EAAE,CAAC6F,WAAW;IAAA,CAA4C;EAAE;EACtM;IAAS,IAAI,CAACjB,IAAI,kBAhC8E5E,EAAE,CAAA6E,iBAAA;MAAAC,IAAA,EAgCJY,cAAc;MAAAX,SAAA;MAAAe,MAAA;QAAAC,IAAA,GAhCZ/F,EAAE,CAAAgG,YAAA,CAAAC,IAAA;MAAA;MAAAjB,UAAA;IAAA,EAgC+H;EAAE;AACvO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlCoGjF,EAAE,CAAAkF,iBAAA,CAkCXQ,cAAc,EAAc,CAAC;IAC5GZ,IAAI,EAAE5E,SAAS;IACfiF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BU,MAAM,EAAE,CAAC;QAAEI,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAqB,CAAC,CAAC;MACvDnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE9E,EAAE,CAAC6F;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;AACA;AACA,SAASO,6BAA6BA,CAAA,EAAG;EACrC,OAAOC,KAAK,CAAC,uCAAuC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAAA,EAAG;EAC3C,OAAOD,KAAK,CAAC,sEAAsE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,SAASE,kCAAkCA,CAAA,EAAG;EAC1C,OAAOF,KAAK,CAAC,uEAAuE,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,SAASG,0BAA0BA,CAAA,EAAG;EAClC,OAAOH,KAAK,CAAC,6CAA6C,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA,SAASI,mCAAmCA,CAAA,EAAG;EAC3C,OAAOJ,KAAK,CAAC,gEAAgE,CAAC;AAClF;;AAEA;AACA;AACA;AACA;AACA,MAAMK,OAAO,CAAC;EACV;AACJ;AACA;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACC,WAAW,KAAKD,UAAU,EAAE;MACjC,IAAI,CAACE,iBAAiB,CAACF,UAAU,CAAC;IACtC;EACJ;EACA3F,WAAWA,CAAC8F,QAAQ,EAAEC,kBAAkB,EAAE;IACtC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,UAAU,GAAG,IAAItH,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACuH,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB;IACA;IACA;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAIxH,eAAe,CAAC;MAClCyH,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEC,MAAM,CAACC;IAChB,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,WAAW,GAAG,IAAI,CAACX,QAAQ,CAACY,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,IAAI,CAACtF,OAAO,CAAC;IAC9D,IAAI,CAAC,IAAI,CAACuF,WAAW,KAAK,OAAO3C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACtE,MAAMuB,0BAA0B,CAAC,CAAC;IACtC;EACJ;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,CAACzD,aAAa,CAACtC,KAAK,CAAC,CAAC;IACtC,IAAI,CAACoF,UAAU,CAACY,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACf,UAAU,CAACgB,IAAI,CAAC,CAAC;IACtB,IAAI,CAAChB,UAAU,CAACe,QAAQ,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACnB,WAAW,IAAI,OAAO,IAAI,CAACA,WAAW,CAACqB,UAAU,KAAK,UAAU,EAAE;MACvE,IAAI,CAACtB,UAAU,CAACsB,UAAU,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACC,WAAW,CAAC,CAAC;MACpC,IAAI,CAACD,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACAE,qBAAqBA,CAAA,EAAG;IACpB,MAAMC,eAAe,GAAG,IAAI,CAACC,SAAS,CAACxI,MAAM,CAACyI,GAAG,IAAI,CAACA,GAAG,CAACxC,IAAI,CAAC;IAC/D,IAAIsC,eAAe,CAACtF,MAAM,GAAG,CAAC,KAAK,OAAOkC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/E,MAAMqB,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAACkC,eAAe,GAAGH,eAAe,CAAC,CAAC,CAAC;IACzC,IAAI,IAAI,CAAC1B,UAAU,IAAI,IAAI,CAAC2B,SAAS,IAAI,CAAC,IAAI,CAACJ,iBAAiB,EAAE;MAC9D,IAAI,CAACO,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACI5B,iBAAiBA,CAACF,UAAU,EAAE;IAC1B,IAAI,IAAI,CAACC,WAAW,IAAI,OAAO,IAAI,CAACA,WAAW,CAACqB,UAAU,KAAK,UAAU,EAAE;MACvE,IAAI,CAACtB,UAAU,CAACsB,UAAU,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACC,WAAW,CAAC,CAAC;MACpC,IAAI,CAACD,iBAAiB,GAAG,IAAI;IACjC;IACA;IACA,IAAI,CAACvB,UAAU,EAAE;MACb,IAAI,CAACmB,WAAW,CAACzD,aAAa,CAACtC,KAAK,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC6E,WAAW,GAAGD,UAAU;IAC7B,IAAI,IAAI,CAAC2B,SAAS,EAAE;MAChB,IAAI,CAACG,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA;EACAA,qBAAqBA,CAAA,EAAG;IACpB,IAAIC,UAAU;IACd,IAAIlJ,YAAY,CAAC,IAAI,CAACoH,WAAW,CAAC,EAAE;MAChC8B,UAAU,GAAG,IAAI,CAAC9B,WAAW,CAAC+B,OAAO,CAAC,IAAI,CAAC;IAC/C,CAAC,MACI,IAAIlJ,YAAY,CAAC,IAAI,CAACmH,WAAW,CAAC,EAAE;MACrC8B,UAAU,GAAG,IAAI,CAAC9B,WAAW;IACjC,CAAC,MACI,IAAIjD,KAAK,CAACC,OAAO,CAAC,IAAI,CAACgD,WAAW,CAAC,EAAE;MACtC8B,UAAU,GAAG9I,EAAE,CAAC,IAAI,CAACgH,WAAW,CAAC;IACrC;IACA,IAAI8B,UAAU,EAAE;MACZ,IAAI,CAACR,iBAAiB,GAAGQ,UAAU,CAC9B3E,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACiH,UAAU,CAAC,CAAC,CAChC/C,SAAS,CAACuB,IAAI,IAAI,IAAI,CAACoD,iBAAiB,CAACpD,IAAI,CAAC,CAAC;IACxD,CAAC,MACI,IAAI,OAAOP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMmB,6BAA6B,CAAC,CAAC;IACzC;EACJ;EACA;EACAwC,iBAAiBA,CAACpD,IAAI,EAAEqD,UAAU,GAAG,IAAI,CAACpB,WAAW,EAAEpD,aAAa,GAAG,IAAI,CAACyD,WAAW,CAACzD,aAAa,EAAEyE,UAAU,EAAE;IAC/G,MAAMC,OAAO,GAAGF,UAAU,CAACG,IAAI,CAACxD,IAAI,CAAC;IACrC,IAAI,CAACuD,OAAO,EAAE;MACV;IACJ;IACAA,OAAO,CAACE,gBAAgB,CAAC,CAACC,IAAI,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACpE,IAAIF,IAAI,CAACG,aAAa,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACC,UAAU,CAAC9D,IAAI,CAAC4D,YAAY,CAAC,EAAEA,YAAY,EAAE/E,aAAa,EAAEyE,UAAU,CAAC;MAChF,CAAC,MACI,IAAIM,YAAY,IAAI,IAAI,EAAE;QAC3B/E,aAAa,CAACkF,MAAM,CAACJ,qBAAqB,CAAC;QAC3C,IAAI,CAAClC,OAAO,CAACuC,MAAM,CAACN,IAAI,CAACA,IAAI,CAAC;MAClC,CAAC,MACI;QACD,MAAMO,IAAI,GAAGpF,aAAa,CAACqF,GAAG,CAACP,qBAAqB,CAAC;QACrD9E,aAAa,CAACsF,IAAI,CAACF,IAAI,EAAEL,YAAY,CAAC;MAC1C;IACJ,CAAC,CAAC;IACF,IAAI,CAACrC,kBAAkB,CAAC6C,aAAa,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACrE,IAAI,EAAE1C,CAAC,EAAE;IACjB,IAAI,IAAI,CAACwF,SAAS,CAACvF,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAI,CAACuF,SAAS,CAACwB,KAAK;IAC/B;IACA,MAAMC,OAAO,GAAG,IAAI,CAACzB,SAAS,CAACZ,IAAI,CAACa,GAAG,IAAIA,GAAG,CAACxC,IAAI,IAAIwC,GAAG,CAACxC,IAAI,CAACjD,CAAC,EAAE0C,IAAI,CAAC,CAAC,IAAI,IAAI,CAACgD,eAAe;IACjG,IAAI,CAACuB,OAAO,KAAK,OAAO9E,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7D,MAAMsB,kCAAkC,CAAC,CAAC;IAC9C;IACA,OAAOwD,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACIT,UAAUA,CAACU,QAAQ,EAAEC,KAAK,EAAE5F,aAAa,EAAEyE,UAAU,EAAE;IACnD,MAAM7F,IAAI,GAAG,IAAI,CAAC4G,WAAW,CAACG,QAAQ,EAAEC,KAAK,CAAC;IAC9C;IACA,MAAMC,OAAO,GAAG,IAAI3E,wBAAwB,CAACyE,QAAQ,CAAC;IACtD;IACA;IACA,IAAI,IAAI,CAACpC,WAAW,CAACrF,QAAQ,EAAE;MAC3B2H,OAAO,CAACC,KAAK,GAAG,IAAI,CAACvC,WAAW,CAACrF,QAAQ,CAACyH,QAAQ,CAAC;IACvD,CAAC,MACI,IAAI,OAAOlB,UAAU,KAAK,WAAW,IAAI,IAAI,CAAC7B,OAAO,CAACmD,GAAG,CAACtB,UAAU,CAAC,EAAE;MACxEoB,OAAO,CAACC,KAAK,GAAG,IAAI,CAAClD,OAAO,CAACyC,GAAG,CAACZ,UAAU,CAAC,GAAG,CAAC;IACpD,CAAC,MACI;MACDoB,OAAO,CAACC,KAAK,GAAG,CAAC;IACrB;IACA,IAAI,CAAClD,OAAO,CAACoD,GAAG,CAACL,QAAQ,EAAEE,OAAO,CAACC,KAAK,CAAC;IACzC;IACA,MAAMG,SAAS,GAAGjG,aAAa,GAAGA,aAAa,GAAG,IAAI,CAACyD,WAAW,CAACzD,aAAa;IAChFiG,SAAS,CAACC,kBAAkB,CAACtH,IAAI,CAAC0C,QAAQ,EAAEuE,OAAO,EAAED,KAAK,CAAC;IAC3D;IACA;IACA;IACA,IAAIO,WAAW,CAACC,kBAAkB,EAAE;MAChCD,WAAW,CAACC,kBAAkB,CAACjF,IAAI,GAAGwE,QAAQ;IAClD;EACJ;EACA;IAAS,IAAI,CAACzF,IAAI,YAAAmG,gBAAAjG,CAAA;MAAA,YAAAA,CAAA,IAAwFiC,OAAO,EAhQjB1G,EAAE,CAAA0E,iBAAA,CAgQiC1E,EAAE,CAAC2K,eAAe,GAhQrD3K,EAAE,CAAA0E,iBAAA,CAgQgE1E,EAAE,CAAC4K,iBAAiB;IAAA,CAA4C;EAAE;EACpO;IAAS,IAAI,CAACC,IAAI,kBAjQ8E7K,EAAE,CAAA8K,iBAAA;MAAAhG,IAAA,EAiQJ4B,OAAO;MAAA3B,SAAA;MAAAgG,cAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;UAjQLjL,EAAE,CAAAoL,cAAA,CAAAD,QAAA,EAiQ6PzF,cAAc;QAAA;QAAA,IAAAuF,EAAA;UAAA,IAAAI,EAAA;UAjQ7QrL,EAAE,CAAAsL,cAAA,CAAAD,EAAA,GAAFrL,EAAE,CAAAuL,WAAA,QAAAL,GAAA,CAAA5C,SAAA,GAAA+C,EAAA;QAAA;MAAA;MAAAG,SAAA,WAAAC,cAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjL,EAAE,CAAA0L,WAAA,CAiQwWtH,iBAAiB;QAAA;QAAA,IAAA6G,EAAA;UAAA,IAAAI,EAAA;UAjQ3XrL,EAAE,CAAAsL,cAAA,CAAAD,EAAA,GAAFrL,EAAE,CAAAuL,WAAA,QAAAL,GAAA,CAAApD,WAAA,GAAAuD,EAAA,CAAAvB,KAAA;QAAA;MAAA;MAAA6B,SAAA,WAiQmK,MAAM;MAAA7F,MAAA;QAAAa,UAAA;QAAAiB,WAAA;QAAAvF,OAAA;MAAA;MAAAuJ,QAAA;MAAA5G,UAAA;MAAA6G,QAAA,GAjQ3K7L,EAAE,CAAA8L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAtG,QAAA,WAAAuG,iBAAAjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjL,EAAE,CAAAmM,kBAAA,KAiQ6f,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6DhI,iBAAiB;MAAAiI,aAAA;IAAA,EAAsI;EAAE;AAC1zB;AACA;EAAA,QAAApH,SAAA,oBAAAA,SAAA,KAnQoGjF,EAAE,CAAAkF,iBAAA,CAmQXwB,OAAO,EAAc,CAAC;IACrG5B,IAAI,EAAEzE,SAAS;IACf8E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBwG,QAAQ,EAAE,SAAS;MACnBjG,QAAQ,EAAE,iDAAiD;MAC3D2G,IAAI,EAAE;QACF,OAAO,EAAE,UAAU;QACnB,MAAM,EAAE;MACZ,CAAC;MACDD,aAAa,EAAE/L,iBAAiB,CAAC2F,IAAI;MACrC;MACA;MACA;MACA;MACAsG,eAAe,EAAEhM,uBAAuB,CAACiM,OAAO;MAChDxH,UAAU,EAAE,IAAI;MAChByH,OAAO,EAAE,CAACrI,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEU,IAAI,EAAE9E,EAAE,CAAC2K;EAAgB,CAAC,EAAE;IAAE7F,IAAI,EAAE9E,EAAE,CAAC4K;EAAkB,CAAC,CAAC,EAAkB;IAAEjE,UAAU,EAAE,CAAC;MACjH7B,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEoH,WAAW,EAAE,CAAC;MACd9C,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAE6B,OAAO,EAAE,CAAC;MACVyC,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEsH,WAAW,EAAE,CAAC;MACdhD,IAAI,EAAErE,SAAS;MACf0E,IAAI,EAAE,CAACf,iBAAiB,EAAE;QAAEsI,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEpE,SAAS,EAAE,CAAC;MACZxD,IAAI,EAAEpE,eAAe;MACrByE,IAAI,EAAE,CAACO,cAAc,EAAE;QACf;QACA;QACAnC,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMiH,WAAW,CAAC;EACd;AACJ;AACA;AACA;AACA;AACA;EACI,IAAImC,IAAIA,CAAA,EAAG;IACP,OAAO,UAAU;EACrB;EACA,IAAIA,IAAIA,CAACC,KAAK,EAAE;IACZ;IACA,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,YAAY,CAAC,MAAM,EAAEH,KAAK,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACI;IAAS,IAAI,CAACnC,kBAAkB,GAAG,IAAI;EAAE;EACzC;EACA,IAAIjF,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACwH,KAAK;EACrB;EACA,IAAIxH,IAAIA,CAACpD,KAAK,EAAE;IACZ,IAAIA,KAAK,KAAK,IAAI,CAAC4K,KAAK,EAAE;MACtB,IAAI,CAACA,KAAK,GAAG5K,KAAK;MAClB,IAAI,CAAC6K,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,YAAY,CAAClF,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA,IAAIvG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC0L,KAAK,CAACvF,WAAW,CAACnG,UAAU,CAAC,IAAI,CAACuL,KAAK,CAAC;EACxD;EACA,IAAI7C,KAAKA,CAAA,EAAG;IACR;IACA;IACA;IACA,OAAO,IAAI,CAACgD,KAAK,CAACvF,WAAW,CAACrF,QAAQ,GAChC,IAAI,CAAC4K,KAAK,CAACvF,WAAW,CAACrF,QAAQ,CAAC,IAAI,CAACyK,KAAK,CAAC,GAC3C,IAAI,CAACI,oBAAoB;EACnC;EACApM,WAAWA,CAAC6L,WAAW,EAAEM,KAAK,EAAE;IAC5B,IAAI,CAACN,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACM,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACE,UAAU,GAAG,IAAI3N,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACwN,YAAY,GAAG,IAAIxN,OAAO,CAAC,CAAC;IACjC8K,WAAW,CAACC,kBAAkB,GAAG,IAAI;IACrC,IAAI,CAACkC,IAAI,GAAG,UAAU;EAC1B;EACAnF,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC4F,oBAAoB,GAAGE,sBAAsB,CAAC,IAAI,CAACT,WAAW,CAACC,aAAa,CAAC;IAClF,IAAI,CAACD,WAAW,CAACC,aAAa,CAACC,YAAY,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC5C,KAAK,GAAG,CAAC,EAAE,CAAC;EAClF;EACAtC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI2C,WAAW,CAACC,kBAAkB,KAAK,IAAI,EAAE;MACzCD,WAAW,CAACC,kBAAkB,GAAG,IAAI;IACzC;IACA,IAAI,CAACyC,YAAY,CAACnF,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACsF,UAAU,CAACrF,IAAI,CAAC,CAAC;IACtB,IAAI,CAACqF,UAAU,CAACtF,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAwF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACV,WAAW,CAACC,aAAa,CAACS,KAAK,CAAC,CAAC;EAC1C;EACA;EACAN,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACE,KAAK,CAACvF,WAAW,CAACpF,YAAY,IACpC,CAAC,IAAI,CAAC2K,KAAK,CAACvF,WAAW,CAACzE,WAAW,KAClC,OAAO8B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMwB,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAACkG,IAAI,GAAG,UAAU;EAC1B;EACA;IAAS,IAAI,CAACpI,IAAI,YAAAiJ,oBAAA/I,CAAA;MAAA,YAAAA,CAAA,IAAwF+F,WAAW,EAxXrBxK,EAAE,CAAA0E,iBAAA,CAwXqC1E,EAAE,CAACyN,UAAU,GAxXpDzN,EAAE,CAAA0E,iBAAA,CAwX+DgC,OAAO;IAAA,CAA4C;EAAE;EACtN;IAAS,IAAI,CAAC9B,IAAI,kBAzX8E5E,EAAE,CAAA6E,iBAAA;MAAAC,IAAA,EAyXJ0F,WAAW;MAAAzF,SAAA;MAAA4G,SAAA;MAAA+B,QAAA;MAAAC,YAAA,WAAAC,yBAAA3C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzXTjL,EAAE,CAAA6N,WAAA,kBAAA3C,GAAA,CAAAzJ,UAAA;QAAA;MAAA;MAAAqE,MAAA;QAAA6G,IAAA;MAAA;MAAAf,QAAA;MAAA5G,UAAA;IAAA,EAyX2N;EAAE;AACnU;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3XoGjF,EAAE,CAAAkF,iBAAA,CA2XXsF,WAAW,EAAc,CAAC;IACzG1F,IAAI,EAAE5E,SAAS;IACfiF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBwG,QAAQ,EAAE,aAAa;MACvBU,IAAI,EAAE;QACF,OAAO,EAAE,eAAe;QACxB,sBAAsB,EAAE;MAC5B,CAAC;MACDtH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE9E,EAAE,CAACyN;EAAW,CAAC,EAAE;IAAE3I,IAAI,EAAE4B;EAAQ,CAAC,CAAC,EAAkB;IAAEiG,IAAI,EAAE,CAAC;MACzF7H,IAAI,EAAEtE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,SAAS8M,sBAAsBA,CAACQ,WAAW,EAAE;EACzC,IAAIC,MAAM,GAAGD,WAAW,CAACE,aAAa;EACtC,OAAOD,MAAM,IAAI,CAACE,aAAa,CAACF,MAAM,CAAC,EAAE;IACrCA,MAAM,GAAGA,MAAM,CAACC,aAAa;EACjC;EACA,IAAI,CAACD,MAAM,EAAE;IACT,IAAI,OAAO9I,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMoB,KAAK,CAAC,oDAAoD,CAAC;IACrE,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ,CAAC,MACI,IAAI0H,MAAM,CAACG,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;IACxD,OAAOxN,eAAe,CAACoN,MAAM,CAACK,YAAY,CAAC,YAAY,CAAC,CAAC;EAC7D,CAAC,MACI;IACD;IACA,OAAO,CAAC;EACZ;AACJ;AACA,SAASH,aAAaA,CAACI,OAAO,EAAE;EAC5B,MAAMH,SAAS,GAAGG,OAAO,CAACH,SAAS;EACnC,OAAO,CAAC,EAAEA,SAAS,EAAEC,QAAQ,CAAC,sBAAsB,CAAC,IAAID,SAAS,EAAEC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC7F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,SAAS9D,WAAW,CAAC;EACxCxJ,WAAWA,CAACuN,UAAU,EAAEC,IAAI,EAAE1H,QAAQ,EAAE;IACpC,KAAK,CAACyH,UAAU,EAAEC,IAAI,CAAC;IACvB,IAAI,CAAC1H,QAAQ,GAAGA,QAAQ;EAC5B;EACA2H,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAChH,WAAW,GAAG,IAAI,CAACX,QAAQ,CAACY,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,IAAI,CAACwF,KAAK,CAAC9K,OAAO,CAAC;IACpE,IAAI,CAAC,IAAI,CAAC8K,KAAK,CAACvF,WAAW,CAACzE,WAAW,KAAK,OAAO8B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACxF,MAAMwB,mCAAmC,CAAC,CAAC;IAC/C;IACA,MAAM/C,aAAa,GAAG,IAAI,CAACyJ,KAAK,CAACvF,WAAW,CAACzE,WAAW,CAAC,IAAI,CAACqC,IAAI,CAAC;IACnE,IAAI7B,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;MAC9B,IAAI,CAACgL,mBAAmB,CAAChL,aAAa,CAAC;IAC3C,CAAC,MACI,IAAIjE,YAAY,CAACiE,aAAa,CAAC,EAAE;MAClCA,aAAa,CACRK,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACsN,UAAU,CAAC,CAAC,CAChCpJ,SAAS,CAAC0K,MAAM,IAAI,IAAI,CAACD,mBAAmB,CAACC,MAAM,CAAC,CAAC;IAC9D;IACA,IAAI,CAACC,UAAU,CAAC7F,OAAO,CAClBhF,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACsN,UAAU,CAAC,CAAC,CAChCpJ,SAAS,CAAC,MAAM,IAAI,CAACyK,mBAAmB,CAAC,CAAC,CAAC;EACpD;EACA;EACA;EACAlH,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;EACpB;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgH,MAAM,CAAC,CAAC;IACb,KAAK,CAAChH,WAAW,CAAC,CAAC;EACvB;EACA;EACA6G,mBAAmBA,CAACxK,QAAQ,EAAE;IAC1B,MAAM4K,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACpC,IAAI7K,QAAQ,EAAE;MACV,IAAI,CAAC8K,SAAS,GAAG9K,QAAQ;IAC7B;IACA,IAAI4K,MAAM,IAAI,IAAI,CAACE,SAAS,EAAE;MAC1B,MAAM3K,aAAa,GAAGyK,MAAM,CAACzK,aAAa;MAC1C,IAAI,CAAC8I,KAAK,CAACvE,iBAAiB,CAAC,IAAI,CAACoG,SAAS,EAAE,IAAI,CAACvH,WAAW,EAAEpD,aAAa,EAAE,IAAI,CAAC2I,KAAK,CAAC;IAC7F,CAAC,MACI;MACD;MACA,IAAI,CAACvF,WAAW,CAACuB,IAAI,CAAC,EAAE,CAAC;IAC7B;EACJ;EACA;EACA6F,MAAMA,CAAA,EAAG;IACL,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACpC,IAAID,MAAM,EAAE;MACRA,MAAM,CAACzK,aAAa,CAACtC,KAAK,CAAC,CAAC;MAC5B,IAAI,CAAC0F,WAAW,CAACuB,IAAI,CAAC,EAAE,CAAC;IAC7B;EACJ;EACA;EACA+F,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAACL,UAAU;IAC/B;IACA;IACA,OAAOK,OAAO,IAAIA,OAAO,CAACvH,IAAI,CAACoH,MAAM,IAAI,CAACA,MAAM,CAACxK,KAAK,IAAIwK,MAAM,CAACxK,KAAK,KAAK,IAAI,CAAC;EACpF;EACA;IAAS,IAAI,CAACC,IAAI,YAAA2K,0BAAAzK,CAAA;MAAA,YAAAA,CAAA,IAAwF6J,iBAAiB,EAve3BtO,EAAE,CAAA0E,iBAAA,CAue2C1E,EAAE,CAACyN,UAAU,GAve1DzN,EAAE,CAAA0E,iBAAA,CAueqEgC,OAAO,GAve9E1G,EAAE,CAAA0E,iBAAA,CAueyF1E,EAAE,CAAC2K,eAAe;IAAA,CAA4C;EAAE;EAC3P;IAAS,IAAI,CAAC/F,IAAI,kBAxe8E5E,EAAE,CAAA6E,iBAAA;MAAAC,IAAA,EAweJwJ,iBAAiB;MAAAvJ,SAAA;MAAAgG,cAAA,WAAAoE,iCAAAlE,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;UAxefjL,EAAE,CAAAoL,cAAA,CAAAD,QAAA,EA2exC/G,iBAAiB;QAAA;QAAA,IAAA6G,EAAA;UAAA,IAAAI,EAAA;UA3eqBrL,EAAE,CAAAsL,cAAA,CAAAD,EAAA,GAAFrL,EAAE,CAAAuL,WAAA,QAAAL,GAAA,CAAA0D,UAAA,GAAAvD,EAAA;QAAA;MAAA;MAAAM,SAAA;MAAAC,QAAA;MAAA5G,UAAA;MAAA6G,QAAA,GAAF7L,EAAE,CAAAoP,kBAAA,CAwekI,CAC5N;QAAEC,OAAO,EAAE7E,WAAW;QAAE8E,WAAW,EAAEhB;MAAkB,CAAC,EACxD;QAAEe,OAAO,EAAElL,yBAAyB;QAAEmL,WAAW,EAAEhB;MAAkB,CAAC,CACzE,GA3e2FtO,EAAE,CAAAuP,0BAAA;IAAA,EA2ewE;EAAE;AAChL;AACA;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KA7eoGjF,EAAE,CAAAkF,iBAAA,CA6eXoJ,iBAAiB,EAAc,CAAC;IAC/GxJ,IAAI,EAAE5E,SAAS;IACfiF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCwG,QAAQ,EAAE,mBAAmB;MAC7B4D,SAAS,EAAE,CACP;QAAEH,OAAO,EAAE7E,WAAW;QAAE8E,WAAW,EAAEhB;MAAkB,CAAC,EACxD;QAAEe,OAAO,EAAElL,yBAAyB;QAAEmL,WAAW,EAAEhB;MAAkB,CAAC,CACzE;MACDhC,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDtH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE9E,EAAE,CAACyN;EAAW,CAAC,EAAE;IAAE3I,IAAI,EAAE4B;EAAQ,CAAC,EAAE;IAAE5B,IAAI,EAAE9E,EAAE,CAAC2K;EAAgB,CAAC,CAAC,EAAkB;IAAEiE,UAAU,EAAE,CAAC;MAC7H9J,IAAI,EAAEpE,eAAe;MACrByE,IAAI,EAAE,CAACf,iBAAiB,EAAE;QAClB;QACA;QACAb,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMkM,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;EACA,IAAIvF,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACwF,MAAM;EACtB;EACA,IAAIxF,KAAKA,CAAC/H,KAAK,EAAE;IACb,IAAI,CAACwN,cAAc,CAACxN,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;EACI,IAAIyN,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACE,eAAe,CAACF,MAAM,CAAC;EAChC;EACA7O,WAAWA,CAACgP,SAAS,EAAE7C,KAAK,EAAE8C,QAAQ,EAAEC,IAAI,EAAE;IAC1C,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC7C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC8C,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC7C,UAAU,GAAG,IAAI3N,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACyQ,WAAW,GAAG,IAAI;IACvB,IAAI,CAACL,OAAO,GAAG,EAAE;IACjB,IAAI,CAACM,WAAW,CAAC,CAAC;IAClB,IAAIF,IAAI,EAAE;MACNA,IAAI,CAACG,MAAM,CAACtM,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACsN,UAAU,CAAC,CAAC,CAACpJ,SAAS,CAAC,MAAM,IAAI,CAACmM,WAAW,CAAC,IAAI,CAAC,CAAC;IACxF;IACA;IACA;IACA;IACAJ,SAAS,CAAC9C,YAAY,CAACjJ,SAAS,CAAC,MAAM,IAAI,CAACmM,WAAW,CAAC,CAAC,CAAC;EAC9D;EACAvI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwF,UAAU,CAACrF,IAAI,CAAC,CAAC;IACtB,IAAI,CAACqF,UAAU,CAACtF,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAuI,cAAcA,CAAA,EAAG;IACb,MAAMC,SAAS,GAAG,IAAI,CAACP,SAAS,CAACxK,IAAI,IAAI,IAAI,CAAC2H,KAAK,CAACvF,WAAW,CAACrF,QAAQ,GAClE,IAAI,CAAC4K,KAAK,CAACvF,WAAW,CAACrF,QAAQ,CAAC,IAAI,CAACyN,SAAS,CAACxK,IAAI,CAAC,GACpD,IAAI;IACV,MAAM2E,KAAK,GAAG,IAAI,CAACwF,MAAM,IAAI,IAAI,GAAGY,SAAS,GAAG,IAAI,CAACZ,MAAM;IAC3D,OAAO,OAAOxF,KAAK,KAAK,QAAQ,GAAG,GAAGA,KAAK,GAAG,IAAI,CAAC2F,OAAO,GAAG,IAAI,CAACK,WAAW,EAAE,GAAG,IAAI;EAC1F;EACAC,WAAWA,CAACI,WAAW,GAAG,KAAK,EAAE;IAC7B,MAAMC,OAAO,GAAG,IAAI,CAACH,cAAc,CAAC,CAAC;IACrC,IAAIG,OAAO,KAAK,IAAI,CAACC,eAAe,IAAIF,WAAW,EAAE;MACjD,MAAMnC,OAAO,GAAG,IAAI,CAAC4B,QAAQ,CAACnD,aAAa;MAC3C,MAAM6D,WAAW,GAAG,IAAI,CAACT,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC9N,KAAK,KAAK,KAAK,GAAG,cAAc,GAAG,aAAa;MAC3F,MAAMwO,SAAS,GAAGD,WAAW,KAAK,aAAa,GAAG,cAAc,GAAG,aAAa;MAChFtC,OAAO,CAACwC,KAAK,CAACF,WAAW,CAAC,GAAGF,OAAO,IAAI,EAAE;MAC1CpC,OAAO,CAACwC,KAAK,CAACD,SAAS,CAAC,GAAG,EAAE;MAC7B,IAAI,CAACF,eAAe,GAAGD,OAAO;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIb,cAAcA,CAACxN,KAAK,EAAE;IAClB;IACA;IACA;IACA,IAAI,CAACuN,MAAM,GAAGmB,KAAK,CAAC1O,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;IACzC,IAAI,CAACgO,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIL,eAAeA,CAACF,MAAM,EAAE;IACpB,IAAIzN,KAAK,GAAGyN,MAAM;IAClB,IAAIkB,KAAK,GAAG,IAAI;IAChB,IAAI,OAAOlB,MAAM,KAAK,QAAQ,EAAE;MAC5B,MAAMmB,KAAK,GAAGnB,MAAM,CAACoB,KAAK,CAACxB,cAAc,CAAC;MAC1CrN,KAAK,GAAG4O,KAAK,CAAC,CAAC,CAAC;MAChBD,KAAK,GAAGC,KAAK,CAAC,CAAC,CAAC,IAAID,KAAK;IAC7B;IACA,IAAI,CAACZ,WAAW,GAAGY,KAAK;IACxB,IAAI,CAACjB,OAAO,GAAGnP,eAAe,CAACyB,KAAK,CAAC;IACrC,IAAI,CAACgO,WAAW,CAAC,CAAC;EACtB;EACA;IAAS,IAAI,CAAC7L,IAAI,YAAA2M,2BAAAzM,CAAA;MAAA,YAAAA,CAAA,IAAwFiL,kBAAkB,EArmB5B1P,EAAE,CAAA0E,iBAAA,CAqmB4C8F,WAAW,GArmBzDxK,EAAE,CAAA0E,iBAAA,CAqmBoEgC,OAAO,GArmB7E1G,EAAE,CAAA0E,iBAAA,CAqmBwF1E,EAAE,CAACyN,UAAU,GArmBvGzN,EAAE,CAAA0E,iBAAA,CAqmBkH5D,EAAE,CAACqQ,cAAc;IAAA,CAA4D;EAAE;EACnS;IAAS,IAAI,CAACvM,IAAI,kBAtmB8E5E,EAAE,CAAA6E,iBAAA;MAAAC,IAAA,EAsmBJ4K,kBAAkB;MAAA3K,SAAA;MAAAe,MAAA;QAAAqE,KAAA,GAtmBhBnK,EAAE,CAAAgG,YAAA,CAAAoL,0BAAA,iCAsmBuHzQ,eAAe;QAAAkP,MAAA,GAtmBxI7P,EAAE,CAAAgG,YAAA,CAAAC,IAAA;MAAA;MAAAjB,UAAA;MAAA6G,QAAA,GAAF7L,EAAE,CAAAqR,wBAAA;IAAA,EAsmB0M;EAAE;AAClT;AACA;EAAA,QAAApM,SAAA,oBAAAA,SAAA,KAxmBoGjF,EAAE,CAAAkF,iBAAA,CAwmBXwK,kBAAkB,EAAc,CAAC;IAChH5K,IAAI,EAAE5E,SAAS;IACfiF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE0F;EAAY,CAAC,EAAE;IAAE1F,IAAI,EAAE4B;EAAQ,CAAC,EAAE;IAAE5B,IAAI,EAAE9E,EAAE,CAACyN;EAAW,CAAC,EAAE;IAAE3I,IAAI,EAAEhE,EAAE,CAACqQ,cAAc;IAAE7L,UAAU,EAAE,CAAC;MAC1HR,IAAI,EAAE1E;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE+J,KAAK,EAAE,CAAC;MACjCrF,IAAI,EAAEtE,KAAK;MACX2E,IAAI,EAAE,CAAC;QAAEgB,KAAK,EAAE,oBAAoB;QAAEmL,SAAS,EAAE3Q;MAAgB,CAAC;IACtE,CAAC,CAAC;IAAEkP,MAAM,EAAE,CAAC;MACT/K,IAAI,EAAEtE,KAAK;MACX2E,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMoM,iBAAiB,CAAC;EACpBvQ,WAAWA,CAACmM,KAAK,EAAE6C,SAAS,EAAE;IAC1B,IAAI,CAAC7C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6C,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACwB,SAAS,GAAG,KAAK;EAC1B;EACAC,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAACF,SAAS,GACR,IAAI,CAACrE,KAAK,CAACvF,WAAW,CAACjG,iBAAiB,CAAC,IAAI,CAACqO,SAAS,CAACxK,IAAI,CAAC,GAC7D,IAAI,CAAC2H,KAAK,CAACvF,WAAW,CAAC1G,MAAM,CAAC,IAAI,CAAC8O,SAAS,CAACxK,IAAI,CAAC;IACxDkM,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACpN,IAAI,YAAAqN,0BAAAnN,CAAA;MAAA,YAAAA,CAAA,IAAwF8M,iBAAiB,EAxoB3BvR,EAAE,CAAA0E,iBAAA,CAwoB2CgC,OAAO,GAxoBpD1G,EAAE,CAAA0E,iBAAA,CAwoB+D8F,WAAW;IAAA,CAA4C;EAAE;EAC1N;IAAS,IAAI,CAAC5F,IAAI,kBAzoB8E5E,EAAE,CAAA6E,iBAAA;MAAAC,IAAA,EAyoBJyM,iBAAiB;MAAAxM,SAAA;MAAA4I,YAAA,WAAAkE,+BAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzoBfjL,EAAE,CAAA8R,UAAA,mBAAAC,2CAAAC,MAAA;YAAA,OAyoBJ9G,GAAA,CAAAuG,OAAA,CAAAO,MAAc,CAAC;UAAA,CAAC,CAAC;QAAA;MAAA;MAAAlM,MAAA;QAAA0L,SAAA,GAzoBfxR,EAAE,CAAAgG,YAAA,CAAAoL,0BAAA,6CAyoBqIxQ,gBAAgB;MAAA;MAAAoE,UAAA;MAAA6G,QAAA,GAzoBvJ7L,EAAE,CAAAqR,wBAAA;IAAA,EAyoB8N;EAAE;AACtU;AACA;EAAA,QAAApM,SAAA,oBAAAA,SAAA,KA3oBoGjF,EAAE,CAAAkF,iBAAA,CA2oBXqM,iBAAiB,EAAc,CAAC;IAC/GzM,IAAI,EAAE5E,SAAS;IACfiF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BkH,IAAI,EAAE;QACF,SAAS,EAAE;MACf,CAAC;MACDtH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE4B;EAAQ,CAAC,EAAE;IAAE5B,IAAI,EAAE0F;EAAY,CAAC,CAAC,EAAkB;IAAEgH,SAAS,EAAE,CAAC;MAC5F1M,IAAI,EAAEtE,KAAK;MACX2E,IAAI,EAAE,CAAC;QAAEgB,KAAK,EAAE,4BAA4B;QAAEmL,SAAS,EAAE1Q;MAAiB,CAAC;IAC/E,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqR,qBAAqB,GAAG,CAC1B3D,iBAAiB,EACjB5I,cAAc,EACdgK,kBAAkB,EAClB6B,iBAAiB,EACjB7K,OAAO,EACP8D,WAAW,EACXpG,iBAAiB,CACpB;AACD,MAAM8N,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC3N,IAAI,YAAA4N,sBAAA1N,CAAA;MAAA,YAAAA,CAAA,IAAwFyN,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBApqB8EpS,EAAE,CAAAqS,gBAAA;MAAAvN,IAAA,EAoqBSoN;IAAa,EAY3F;EAAE;EAC/B;IAAS,IAAI,CAACI,IAAI,kBAjrB8EtS,EAAE,CAAAuS,gBAAA,IAirByB;EAAE;AACjI;AACA;EAAA,QAAAtN,SAAA,oBAAAA,SAAA,KAnrBoGjF,EAAE,CAAAkF,iBAAA,CAmrBXgN,aAAa,EAAc,CAAC;IAC3GpN,IAAI,EAAEjE,QAAQ;IACdsE,IAAI,EAAE,CAAC;MACCsH,OAAO,EAAEwF,qBAAqB;MAC9BO,OAAO,EAAEP;IACb,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASlR,eAAe,EAAEoD,yBAAyB,EAAEmK,iBAAiB,EAAE5H,OAAO,EAAEwL,aAAa,EAAE1H,WAAW,EAAE9E,cAAc,EAAEtB,iBAAiB,EAAEmB,wBAAwB,EAAEmK,kBAAkB,EAAE6B,iBAAiB,EAAEjP,eAAe,EAAEY,iBAAiB,EAAEuD,mCAAmC,EAAED,0BAA0B,EAAED,kCAAkC,EAAED,mCAAmC,EAAEF,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}