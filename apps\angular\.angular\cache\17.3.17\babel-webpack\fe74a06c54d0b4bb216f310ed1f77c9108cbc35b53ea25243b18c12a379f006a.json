{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nimport { DEFAULT_MY_SECURITY_LOGS_ENTITY_ACTIONS } from '../defaults/default-my-security-logs-entity-actions';\nimport { DEFAULT_MY_SECURITY_LOGS_ENTITY_PROPS } from '../defaults/default-my-security-logs-entity-props';\nimport { DEFAULT_MY_SECURITY_LOGS_TOOLBAR_ACTIONS } from '../defaults/default-my-security-logs-toolbar-actions';\nimport { DEFAULT_PERSONAL_SETTINGS_UPDATE_FORM_PROPS } from '../defaults/default-personal-settings-form-props';\nexport const DEFAULT_ACCOUNT_ENTITY_ACTIONS = {\n  [\"Account.MySecurityLogs\" /* eAccountComponents.MySecurityLogs */]: DEFAULT_MY_SECURITY_LOGS_ENTITY_ACTIONS\n};\nexport const DEFAULT_ACCOUNT_TOOLBAR_ACTIONS = {\n  [\"Account.MySecurityLogs\" /* eAccountComponents.MySecurityLogs */]: DEFAULT_MY_SECURITY_LOGS_TOOLBAR_ACTIONS\n};\nexport const DEFAULT_ACCOUNT_ENTITY_PROPS = {\n  [\"Account.MySecurityLogs\" /* eAccountComponents.MySecurityLogs */]: DEFAULT_MY_SECURITY_LOGS_ENTITY_PROPS\n};\nexport const DEFAULT_ACCOUNT_FORM_PROPS = {\n  [\"Account.PersonalSettingsComponent\" /* eAccountComponents.PersonalSettings */]: DEFAULT_PERSONAL_SETTINGS_UPDATE_FORM_PROPS\n};\nexport const ACCOUNT_ENTITY_ACTION_CONTRIBUTORS = new InjectionToken('ACCOUNT_ENTITY_ACTION_CONTRIBUTORS');\nexport const ACCOUNT_TOOLBAR_ACTION_CONTRIBUTORS = new InjectionToken('ACCOUNT_TOOLBAR_ACTION_CONTRIBUTORS');\nexport const ACCOUNT_ENTITY_PROP_CONTRIBUTORS = new InjectionToken('ACCOUNT_ENTITY_PROP_CONTRIBUTORS');\nexport const ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS = new InjectionToken('ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}