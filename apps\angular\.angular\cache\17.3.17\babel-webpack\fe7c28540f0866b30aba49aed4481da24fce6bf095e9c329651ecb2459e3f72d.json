{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Core, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n\"use strict\";\n\nimport { __spreadArrayFn as __spreadArray } from \"@microsoft/applicationinsights-shims\";\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\nimport { objCreateFn } from \"@microsoft/applicationinsights-shims\";\nimport { _DYN_ADD_NOTIFICATION_LIS1, _DYN_CONFIG, _DYN_FLUSH, _DYN_GET_NOTIFY_MGR, _DYN_GET_PLUGIN, _DYN_GET_PROCESS_TEL_CONT0, _DYN_IDENTIFIER, _DYN_INITIALIZE, _DYN_INSTRUMENTATION_KEY, _DYN_IS_ASYNC, _DYN_IS_INITIALIZED, _DYN_LENGTH, _DYN_LOGGER, _DY<PERSON>_MESSAGE, _DYN_MESSAGE_ID, _DY<PERSON>_NAME, _DY<PERSON>_ON_COMPLETE, _DYN_PROCESS_NEXT, _DYN_PUSH, _DYN_REMOVE_NOTIFICATION_2, _DYN_SET_ENABLED, _DYN_SPLICE, _DYN_STOP_POLLING_INTERNA3, _DYN_TEARDOWN, _DYN_TIME, _DYN__EXTENSIONS } from \"../__DynamicConstants\";\nimport { ChannelControllerPriority, createChannelControllerPlugin, createChannelQueues } from \"./ChannelController\";\nimport { createCookieMgr } from \"./CookieMgr\";\nimport { createUniqueNamespace } from \"./DataCacheHelper\";\nimport { getDebugListener } from \"./DbgExtensionUtils\";\nimport { DiagnosticLogger, _InternalLogMessage, _throwInternal, _warnToConsole } from \"./DiagnosticLogger\";\nimport { arrForEach, arrIndexOf, getCfgValue, getSetValue, isFunction, isNullOrUndefined, objExtend, objFreeze, proxyFunctionAs, proxyFunctions, throwError, toISOString } from \"./HelperFuncs\";\nimport { STR_CHANNELS, STR_CORE, STR_CREATE_PERF_MGR, STR_DISABLED, STR_EVENTS_DISCARDED, STR_EVENTS_SEND_REQUEST, STR_EVENTS_SENT, STR_EXTENSIONS, STR_EXTENSION_CONFIG, STR_GET_PERF_MGR, STR_PRIORITY } from \"./InternalConstants\";\nimport { PerfManager, getGblPerfMgr } from \"./PerfManager\";\nimport { createProcessTelemetryContext, createProcessTelemetryUnloadContext, createProcessTelemetryUpdateContext, createTelemetryProxyChain } from \"./ProcessTelemetryContext\";\nimport { _getPluginState, createDistributedTraceContext, initializePlugins, sortPlugins } from \"./TelemetryHelpers\";\nimport { TelemetryInitializerPlugin } from \"./TelemetryInitializerPlugin\";\nimport { createUnloadHandlerContainer } from \"./UnloadHandlerContainer\";\nvar strValidationError = \"Plugins must provide initialize method\";\nvar strNotificationManager = \"_notificationManager\";\nvar strSdkUnloadingError = \"SDK is still unloading...\";\nvar strSdkNotInitialized = \"SDK is not initialized\";\n// const strPluginUnloadFailed = \"Failed to unload plugin\";\nvar defaultInitConfig = {\n  // Have the Diagnostic Logger default to log critical errors to the console\n  loggingLevelConsole: 1 /* eLoggingSeverity.CRITICAL */\n};\n/**\r\n * Helper to create the default performance manager\r\n * @param core\r\n * @param notificationMgr\r\n */\nfunction _createPerfManager(core, notificationMgr) {\n  return new PerfManager(notificationMgr);\n}\nfunction _validateExtensions(logger, channelPriority, allExtensions) {\n  var _a;\n  // Concat all available extensions\n  var coreExtensions = [];\n  // Check if any two extensions have the same priority, then warn to console\n  // And extract the local extensions from the\n  var extPriorities = {};\n  // Extension validation\n  arrForEach(allExtensions, function (ext) {\n    // Check for ext.initialize\n    if (isNullOrUndefined(ext) || isNullOrUndefined(ext[_DYN_INITIALIZE /* @min:%2einitialize */])) {\n      throwError(strValidationError);\n    }\n    var extPriority = ext[STR_PRIORITY /* @min:%2epriority */];\n    var identifier = ext[_DYN_IDENTIFIER /* @min:%2eidentifier */];\n    if (ext && extPriority) {\n      if (!isNullOrUndefined(extPriorities[extPriority])) {\n        _warnToConsole(logger, \"Two extensions have same priority #\" + extPriority + \" - \" + extPriorities[extPriority] + \", \" + identifier);\n      } else {\n        // set a value\n        extPriorities[extPriority] = identifier;\n      }\n    }\n    // Split extensions to core and channelController\n    if (!extPriority || extPriority < channelPriority) {\n      // Add to core extension that will be managed by BaseCore\n      coreExtensions[_DYN_PUSH /* @min:%2epush */](ext);\n    }\n  });\n  return _a = {\n    all: allExtensions\n  }, _a[STR_CORE /* @min:core */] = coreExtensions, _a;\n}\nfunction _isPluginPresent(thePlugin, plugins) {\n  var exists = false;\n  arrForEach(plugins, function (plugin) {\n    if (plugin === thePlugin) {\n      exists = true;\n      return -1;\n    }\n  });\n  return exists;\n}\nfunction _createDummyNotificationManager() {\n  var _a;\n  return objCreateFn((_a = {}, _a[_DYN_ADD_NOTIFICATION_LIS1 /* @min:addNotificationListener */] = function (listener) {}, _a[_DYN_REMOVE_NOTIFICATION_2 /* @min:removeNotificationListener */] = function (listener) {}, _a[STR_EVENTS_SENT /* @min:eventsSent */] = function (events) {}, _a[STR_EVENTS_DISCARDED /* @min:eventsDiscarded */] = function (events, reason) {}, _a[STR_EVENTS_SEND_REQUEST /* @min:eventsSendRequest */] = function (sendReason, isAsync) {}, _a));\n}\nvar BaseCore = /** @class */function () {\n  function BaseCore() {\n    // NOTE!: DON'T set default values here, instead set them in the _initDefaults() function as it is also called during teardown()\n    var _config;\n    var _isInitialized;\n    var _eventQueue;\n    var _notificationManager;\n    var _perfManager;\n    var _cfgPerfManager;\n    var _cookieManager;\n    var _pluginChain;\n    var _configExtensions;\n    var _coreExtensions;\n    var _channelControl;\n    var _channelConfig;\n    var _channelQueue;\n    var _isUnloading;\n    var _telemetryInitializerPlugin;\n    var _internalLogsEventName;\n    var _evtNamespace;\n    var _unloadHandlers;\n    var _debugListener;\n    var _traceCtx;\n    /**\r\n     * Internal log poller\r\n     */\n    var _internalLogPoller = 0;\n    var _forceStopInternalLogPoller = false;\n    dynamicProto(BaseCore, this, function (_self) {\n      // Set the default values (also called during teardown)\n      _initDefaults();\n      _self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */] = function () {\n        return _isInitialized;\n      };\n      // Creating the self.initialize = ()\n      _self[_DYN_INITIALIZE /* @min:%2einitialize */] = function (config, extensions, logger, notificationManager) {\n        if (_isUnloading) {\n          throwError(strSdkUnloadingError);\n        }\n        // Make sure core is only initialized once\n        if (_self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */]()) {\n          throwError(\"Core should not be initialized more than once\");\n        }\n        _config = config || {};\n        _self[_DYN_CONFIG /* @min:%2econfig */] = _config;\n        if (isNullOrUndefined(config[_DYN_INSTRUMENTATION_KEY /* @min:%2einstrumentationKey */])) {\n          throwError(\"Please provide instrumentation key\");\n        }\n        _notificationManager = notificationManager;\n        // For backward compatibility only\n        _self[strNotificationManager] = notificationManager;\n        _initDebugListener();\n        _initPerfManager();\n        // add notification to the extensions in the config so other plugins can access it\n        _initExtConfig();\n        if (logger) {\n          _self[_DYN_LOGGER /* @min:%2elogger */] = logger;\n        }\n        var cfgExtensions = getSetValue(_config, STR_EXTENSIONS, []);\n        // Extension validation\n        _configExtensions = [];\n        _configExtensions[_DYN_PUSH /* @min:%2epush */].apply(_configExtensions, __spreadArray(__spreadArray([], extensions, false), cfgExtensions, false));\n        _channelConfig = getSetValue(_config, STR_CHANNELS, []);\n        _initPluginChain(null);\n        if (!_channelQueue || _channelQueue[_DYN_LENGTH /* @min:%2elength */] === 0) {\n          throwError(\"No \" + STR_CHANNELS + \" available\");\n        }\n        _isInitialized = true;\n        _self.releaseQueue();\n      };\n      _self.getTransmissionControls = function () {\n        var controls = [];\n        if (_channelQueue) {\n          arrForEach(_channelQueue, function (channels) {\n            controls[_DYN_PUSH /* @min:%2epush */](channels.queue);\n          });\n        }\n        return objFreeze(controls);\n      };\n      _self.track = function (telemetryItem) {\n        // setup default iKey if not passed in\n        telemetryItem.iKey = telemetryItem.iKey || _config[_DYN_INSTRUMENTATION_KEY /* @min:%2einstrumentationKey */];\n        // add default timestamp if not passed in\n        telemetryItem[_DYN_TIME /* @min:%2etime */] = telemetryItem[_DYN_TIME /* @min:%2etime */] || toISOString(new Date());\n        // Common Schema 4.0\n        telemetryItem.ver = telemetryItem.ver || \"4.0\";\n        if (!_isUnloading && _self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */]()) {\n          // Process the telemetry plugin chain\n          _createTelCtx()[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](telemetryItem);\n        } else {\n          // Queue events until all extensions are initialized\n          _eventQueue[_DYN_PUSH /* @min:%2epush */](telemetryItem);\n        }\n      };\n      _self[_DYN_GET_PROCESS_TEL_CONT0 /* @min:%2egetProcessTelContext */] = _createTelCtx;\n      _self[_DYN_GET_NOTIFY_MGR /* @min:%2egetNotifyMgr */] = function () {\n        if (!_notificationManager) {\n          // Create Dummy notification manager\n          _notificationManager = _createDummyNotificationManager();\n          // For backward compatibility only\n          _self[strNotificationManager] = _notificationManager;\n        }\n        return _notificationManager;\n      };\n      /**\r\n       * Adds a notification listener. The SDK calls methods on the listener when an appropriate notification is raised.\r\n       * The added plugins must raise notifications. If the plugins do not implement the notifications, then no methods will be\r\n       * called.\r\n       * @param {INotificationListener} listener - An INotificationListener object.\r\n       */\n      _self[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */] = function (listener) {\n        if (_notificationManager) {\n          _notificationManager[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */](listener);\n        }\n      };\n      /**\r\n       * Removes all instances of the listener.\r\n       * @param {INotificationListener} listener - INotificationListener to remove.\r\n       */\n      _self[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */] = function (listener) {\n        if (_notificationManager) {\n          _notificationManager[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */](listener);\n        }\n      };\n      _self.getCookieMgr = function () {\n        if (!_cookieManager) {\n          _cookieManager = createCookieMgr(_config, _self[_DYN_LOGGER /* @min:%2elogger */]);\n        }\n        return _cookieManager;\n      };\n      _self.setCookieMgr = function (cookieMgr) {\n        _cookieManager = cookieMgr;\n      };\n      _self[STR_GET_PERF_MGR /* @min:%2egetPerfMgr */] = function () {\n        if (!_perfManager && !_cfgPerfManager) {\n          if (getCfgValue(_config.enablePerfMgr)) {\n            var createPerfMgr = getCfgValue(_config[STR_CREATE_PERF_MGR /* @min:%2ecreatePerfMgr */]);\n            if (isFunction(createPerfMgr)) {\n              _cfgPerfManager = createPerfMgr(_self, _self[_DYN_GET_NOTIFY_MGR /* @min:%2egetNotifyMgr */]());\n            }\n          }\n        }\n        return _perfManager || _cfgPerfManager || getGblPerfMgr();\n      };\n      _self.setPerfMgr = function (perfMgr) {\n        _perfManager = perfMgr;\n      };\n      _self.eventCnt = function () {\n        return _eventQueue[_DYN_LENGTH /* @min:%2elength */];\n      };\n      _self.releaseQueue = function () {\n        if (_isInitialized && _eventQueue[_DYN_LENGTH /* @min:%2elength */] > 0) {\n          var eventQueue = _eventQueue;\n          _eventQueue = [];\n          arrForEach(eventQueue, function (event) {\n            _createTelCtx()[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](event);\n          });\n        }\n      };\n      _self.pollInternalLogs = function (eventName) {\n        _internalLogsEventName = eventName || null;\n        _forceStopInternalLogPoller = false;\n        if (_internalLogPoller) {\n          clearInterval(_internalLogPoller);\n          _internalLogPoller = null;\n        }\n        return _startInternalLogTimer(true);\n      };\n      function _startInternalLogTimer(alwaysStart) {\n        if (!_internalLogPoller && !_forceStopInternalLogPoller) {\n          var shouldStart = alwaysStart || _self[_DYN_LOGGER /* @min:%2elogger */] && _self[_DYN_LOGGER /* @min:%2elogger */].queue[_DYN_LENGTH /* @min:%2elength */] > 0;\n          if (shouldStart) {\n            var interval = getCfgValue(_config.diagnosticLogInterval);\n            if (!interval || !(interval > 0)) {\n              interval = 10000;\n            }\n            // Keeping as an interval timer for backward compatibility as it returns the result\n            _internalLogPoller = setInterval(function () {\n              clearInterval(_internalLogPoller);\n              _internalLogPoller = 0;\n              _flushInternalLogs();\n            }, interval);\n          }\n        }\n        return _internalLogPoller;\n      }\n      _self[_DYN_STOP_POLLING_INTERNA3 /* @min:%2estopPollingInternalLogs */] = function () {\n        _forceStopInternalLogPoller = true;\n        if (_internalLogPoller) {\n          clearInterval(_internalLogPoller);\n          _internalLogPoller = 0;\n          _flushInternalLogs();\n        }\n      };\n      // Add addTelemetryInitializer\n      proxyFunctions(_self, function () {\n        return _telemetryInitializerPlugin;\n      }, [\"addTelemetryInitializer\"]);\n      _self.unload = function (isAsync, unloadComplete, cbTimeout) {\n        var _a;\n        if (isAsync === void 0) {\n          isAsync = true;\n        }\n        if (!_isInitialized) {\n          // The SDK is not initialized\n          throwError(strSdkNotInitialized);\n        }\n        // Check if the SDK still unloading so throw\n        if (_isUnloading) {\n          // The SDK is already unloading\n          throwError(strSdkUnloadingError);\n        }\n        var unloadState = (_a = {\n          reason: 50 /* TelemetryUnloadReason.SdkUnload */\n        }, _a[_DYN_IS_ASYNC /* @min:isAsync */] = isAsync, _a.flushComplete = false, _a);\n        var processUnloadCtx = createProcessTelemetryUnloadContext(_getPluginChain(), _self);\n        processUnloadCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](function () {\n          _initDefaults();\n          unloadComplete && unloadComplete(unloadState);\n        }, _self);\n        function _doUnload(flushComplete) {\n          unloadState.flushComplete = flushComplete;\n          _isUnloading = true;\n          // Run all of the unload handlers first (before unloading the plugins)\n          _unloadHandlers.run(processUnloadCtx, unloadState);\n          // Stop polling the internal logs\n          _self[_DYN_STOP_POLLING_INTERNA3 /* @min:%2estopPollingInternalLogs */]();\n          // Start unloading the components, from this point onwards the SDK should be considered to be in an unstable state\n          processUnloadCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](unloadState);\n        }\n        _flushInternalLogs();\n        if (!_flushChannels(isAsync, _doUnload, 6 /* SendRequestReason.SdkUnload */, cbTimeout)) {\n          _doUnload(false);\n        }\n      };\n      _self[_DYN_GET_PLUGIN /* @min:%2egetPlugin */] = _getPlugin;\n      _self.addPlugin = function (plugin, replaceExisting, isAsync, addCb) {\n        if (!plugin) {\n          addCb && addCb(false);\n          _logOrThrowError(strValidationError);\n          return;\n        }\n        var existingPlugin = _getPlugin(plugin[_DYN_IDENTIFIER /* @min:%2eidentifier */]);\n        if (existingPlugin && !replaceExisting) {\n          addCb && addCb(false);\n          _logOrThrowError(\"Plugin [\" + plugin[_DYN_IDENTIFIER /* @min:%2eidentifier */] + \"] is already loaded!\");\n          return;\n        }\n        var updateState = {\n          reason: 16 /* TelemetryUpdateReason.PluginAdded */\n        };\n        function _addPlugin(removed) {\n          _configExtensions[_DYN_PUSH /* @min:%2epush */](plugin);\n          updateState.added = [plugin];\n          // Re-Initialize the plugin chain\n          _initPluginChain(updateState);\n          addCb && addCb(true);\n        }\n        if (existingPlugin) {\n          var removedPlugins_1 = [existingPlugin.plugin];\n          var unloadState = {\n            reason: 2 /* TelemetryUnloadReason.PluginReplace */,\n            isAsync: !!isAsync\n          };\n          _removePlugins(removedPlugins_1, unloadState, function (removed) {\n            if (!removed) {\n              // Previous plugin was successfully removed or was not installed\n              addCb && addCb(false);\n            } else {\n              updateState.removed = removedPlugins_1;\n              updateState.reason |= 32 /* TelemetryUpdateReason.PluginRemoved */;\n              _addPlugin(true);\n            }\n          });\n        } else {\n          _addPlugin(false);\n        }\n      };\n      _self.evtNamespace = function () {\n        return _evtNamespace;\n      };\n      _self[_DYN_FLUSH /* @min:%2eflush */] = _flushChannels;\n      _self.getTraceCtx = function (createNew) {\n        if (!_traceCtx) {\n          _traceCtx = createDistributedTraceContext();\n        }\n        return _traceCtx;\n      };\n      _self.setTraceCtx = function (traceCtx) {\n        _traceCtx = traceCtx || null;\n      };\n      // Create the addUnloadCb\n      proxyFunctionAs(_self, \"addUnloadCb\", function () {\n        return _unloadHandlers;\n      }, \"add\");\n      function _initDefaults() {\n        _isInitialized = false;\n        // Use a default logger so initialization errors are not dropped on the floor with full logging\n        _config = objExtend(true, {}, defaultInitConfig);\n        _self[_DYN_CONFIG /* @min:%2econfig */] = _config;\n        _self[_DYN_LOGGER /* @min:%2elogger */] = new DiagnosticLogger(_config);\n        _self[_DYN__EXTENSIONS /* @min:%2e_extensions */] = [];\n        _telemetryInitializerPlugin = new TelemetryInitializerPlugin();\n        _eventQueue = [];\n        _notificationManager = null;\n        _perfManager = null;\n        _cfgPerfManager = null;\n        _cookieManager = null;\n        _pluginChain = null;\n        _coreExtensions = null;\n        _configExtensions = [];\n        _channelControl = null;\n        _channelConfig = null;\n        _channelQueue = null;\n        _isUnloading = false;\n        _internalLogsEventName = null;\n        _evtNamespace = createUniqueNamespace(\"AIBaseCore\", true);\n        _unloadHandlers = createUnloadHandlerContainer();\n        _traceCtx = null;\n      }\n      function _createTelCtx() {\n        var theCtx = createProcessTelemetryContext(_getPluginChain(), _config, _self);\n        theCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](_startInternalLogTimer);\n        return theCtx;\n      }\n      // Initialize or Re-initialize the plugins\n      function _initPluginChain(updateState) {\n        // Extension validation\n        var theExtensions = _validateExtensions(_self[_DYN_LOGGER /* @min:%2elogger */], ChannelControllerPriority, _configExtensions);\n        _coreExtensions = theExtensions[STR_CORE /* @min:%2ecore */];\n        _pluginChain = null;\n        // Sort the complete set of extensions by priority\n        var allExtensions = theExtensions.all;\n        // Initialize the Channel Queues and the channel plugins first\n        _channelQueue = objFreeze(createChannelQueues(_channelConfig, allExtensions, _self));\n        if (_channelControl) {\n          // During add / remove of a plugin this may get called again, so don't re-add if already present\n          // But we also want the controller as the last, so remove if already present\n          // And reusing the existing instance, just in case an installed plugin has a reference and\n          // is using it.\n          var idx = arrIndexOf(allExtensions, _channelControl);\n          if (idx !== -1) {\n            allExtensions[_DYN_SPLICE /* @min:%2esplice */](idx, 1);\n          }\n          idx = arrIndexOf(_coreExtensions, _channelControl);\n          if (idx !== -1) {\n            _coreExtensions[_DYN_SPLICE /* @min:%2esplice */](idx, 1);\n          }\n          _channelControl._setQueue(_channelQueue);\n        } else {\n          _channelControl = createChannelControllerPlugin(_channelQueue, _self);\n        }\n        // Add on \"channelController\" as the last \"plugin\"\n        allExtensions[_DYN_PUSH /* @min:%2epush */](_channelControl);\n        _coreExtensions[_DYN_PUSH /* @min:%2epush */](_channelControl);\n        // Required to allow plugins to call core.getPlugin() during their own initialization\n        _self[_DYN__EXTENSIONS /* @min:%2e_extensions */] = sortPlugins(allExtensions);\n        // Initialize the controls\n        _channelControl[_DYN_INITIALIZE /* @min:%2einitialize */](_config, _self, allExtensions);\n        var initCtx = _createTelCtx();\n        initializePlugins(initCtx, allExtensions);\n        // Now reset the extensions to just those being managed by Basecore\n        _self[_DYN__EXTENSIONS /* @min:%2e_extensions */] = objFreeze(sortPlugins(_coreExtensions || [])).slice();\n        if (updateState) {\n          _doUpdate(updateState);\n        }\n      }\n      function _getPlugin(pluginIdentifier) {\n        var _a;\n        var theExt = null;\n        var thePlugin = null;\n        arrForEach(_self[_DYN__EXTENSIONS /* @min:%2e_extensions */], function (ext) {\n          if (ext[_DYN_IDENTIFIER /* @min:%2eidentifier */] === pluginIdentifier && ext !== _channelControl && ext !== _telemetryInitializerPlugin) {\n            thePlugin = ext;\n            return -1;\n          }\n        });\n        if (!thePlugin && _channelControl) {\n          // Check the channel Controller\n          thePlugin = _channelControl.getChannel(pluginIdentifier);\n        }\n        if (thePlugin) {\n          theExt = (_a = {\n            plugin: thePlugin\n          }, _a[_DYN_SET_ENABLED /* @min:setEnabled */] = function (enabled) {\n            _getPluginState(thePlugin)[STR_DISABLED] = !enabled;\n          }, _a.isEnabled = function () {\n            var pluginState = _getPluginState(thePlugin);\n            return !pluginState[_DYN_TEARDOWN /* @min:%2eteardown */] && !pluginState[STR_DISABLED];\n          }, _a.remove = function (isAsync, removeCb) {\n            var _a;\n            if (isAsync === void 0) {\n              isAsync = true;\n            }\n            var pluginsToRemove = [thePlugin];\n            var unloadState = (_a = {\n              reason: 1 /* TelemetryUnloadReason.PluginUnload */\n            }, _a[_DYN_IS_ASYNC /* @min:isAsync */] = isAsync, _a);\n            _removePlugins(pluginsToRemove, unloadState, function (removed) {\n              if (removed) {\n                // Re-Initialize the plugin chain\n                _initPluginChain({\n                  reason: 32 /* TelemetryUpdateReason.PluginRemoved */,\n                  removed: pluginsToRemove\n                });\n              }\n              removeCb && removeCb(removed);\n            });\n          }, _a);\n        }\n        return theExt;\n      }\n      function _getPluginChain() {\n        if (!_pluginChain) {\n          // copy the collection of extensions\n          var extensions = (_coreExtensions || []).slice();\n          // During add / remove this may get called again, so don't readd if already present\n          if (arrIndexOf(extensions, _telemetryInitializerPlugin) === -1) {\n            extensions[_DYN_PUSH /* @min:%2epush */](_telemetryInitializerPlugin);\n          }\n          _pluginChain = createTelemetryProxyChain(sortPlugins(extensions), _config, _self);\n        }\n        return _pluginChain;\n      }\n      function _removePlugins(thePlugins, unloadState, removeComplete) {\n        if (thePlugins && thePlugins[_DYN_LENGTH /* @min:%2elength */] > 0) {\n          var unloadChain = createTelemetryProxyChain(thePlugins, _config, _self);\n          var unloadCtx = createProcessTelemetryUnloadContext(unloadChain, _self);\n          unloadCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](function () {\n            var removed = false;\n            // Remove the listed config extensions\n            var newConfigExtensions = [];\n            arrForEach(_configExtensions, function (plugin, idx) {\n              if (!_isPluginPresent(plugin, thePlugins)) {\n                newConfigExtensions[_DYN_PUSH /* @min:%2epush */](plugin);\n              } else {\n                removed = true;\n              }\n            });\n            _configExtensions = newConfigExtensions;\n            // Re-Create the channel config\n            var newChannelConfig = [];\n            if (_channelConfig) {\n              arrForEach(_channelConfig, function (queue, idx) {\n                var newQueue = [];\n                arrForEach(queue, function (channel) {\n                  if (!_isPluginPresent(channel, thePlugins)) {\n                    newQueue[_DYN_PUSH /* @min:%2epush */](channel);\n                  } else {\n                    removed = true;\n                  }\n                });\n                newChannelConfig[_DYN_PUSH /* @min:%2epush */](newQueue);\n              });\n              _channelConfig = newChannelConfig;\n            }\n            removeComplete && removeComplete(removed);\n            _startInternalLogTimer();\n          });\n          unloadCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](unloadState);\n        } else {\n          removeComplete(false);\n        }\n      }\n      function _flushInternalLogs() {\n        if (_self[_DYN_LOGGER /* @min:%2elogger */] && _self[_DYN_LOGGER /* @min:%2elogger */].queue) {\n          var queue = _self[_DYN_LOGGER /* @min:%2elogger */].queue.slice(0);\n          _self[_DYN_LOGGER /* @min:%2elogger */].queue[_DYN_LENGTH /* @min:%2elength */] = 0;\n          arrForEach(queue, function (logMessage) {\n            var _a;\n            var item = (_a = {}, _a[_DYN_NAME /* @min:name */] = _internalLogsEventName ? _internalLogsEventName : \"InternalMessageId: \" + logMessage[_DYN_MESSAGE_ID /* @min:%2emessageId */], _a.iKey = getCfgValue(_config[_DYN_INSTRUMENTATION_KEY /* @min:%2einstrumentationKey */]), _a.time = toISOString(new Date()), _a.baseType = _InternalLogMessage.dataType, _a.baseData = {\n              message: logMessage[_DYN_MESSAGE /* @min:%2emessage */]\n            }, _a);\n            _self.track(item);\n          });\n        }\n      }\n      function _flushChannels(isAsync, callBack, sendReason, cbTimeout) {\n        if (_channelControl) {\n          return _channelControl[_DYN_FLUSH /* @min:%2eflush */](isAsync, callBack, sendReason || 6 /* SendRequestReason.SdkUnload */, cbTimeout);\n        }\n        callBack && callBack(false);\n        return true;\n      }\n      function _initDebugListener() {\n        var disableDbgExt = getCfgValue(_config.disableDbgExt);\n        if (disableDbgExt === true && _debugListener) {\n          // Remove any previously loaded debug listener\n          _notificationManager[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */](_debugListener);\n          _debugListener = null;\n        }\n        if (_notificationManager && !_debugListener && disableDbgExt !== true) {\n          _debugListener = getDebugListener(_config);\n          _notificationManager[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */](_debugListener);\n        }\n      }\n      function _initPerfManager() {\n        var enablePerfMgr = getCfgValue(_config.enablePerfMgr);\n        if (!enablePerfMgr && _cfgPerfManager) {\n          // Remove any existing config based performance manager\n          _cfgPerfManager = null;\n        }\n        if (enablePerfMgr) {\n          // Set the performance manager creation function if not defined\n          getSetValue(_config, STR_CREATE_PERF_MGR, _createPerfManager);\n        }\n      }\n      function _initExtConfig() {\n        var extConfig = getSetValue(_config, STR_EXTENSION_CONFIG, {});\n        extConfig.NotificationManager = _notificationManager;\n      }\n      function _doUpdate(updateState) {\n        var updateCtx = createProcessTelemetryUpdateContext(_getPluginChain(), _self);\n        updateCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](_startInternalLogTimer);\n        if (!_self._updateHook || _self._updateHook(updateCtx, updateState) !== true) {\n          updateCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](updateState);\n        }\n      }\n      function _logOrThrowError(message) {\n        var logger = _self[_DYN_LOGGER /* @min:%2elogger */];\n        if (logger) {\n          // there should always be a logger\n          _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 73 /* _eInternalMessageId.PluginException */, message);\n          _startInternalLogTimer();\n        } else {\n          throwError(message);\n        }\n      }\n    });\n  }\n  // Removed Stub for BaseCore.prototype.initialize.\n  // Removed Stub for BaseCore.prototype.getTransmissionControls.\n  // Removed Stub for BaseCore.prototype.track.\n  // Removed Stub for BaseCore.prototype.getProcessTelContext.\n  // Removed Stub for BaseCore.prototype.getNotifyMgr.\n  // Removed Stub for BaseCore.prototype.addNotificationListener.\n  // Removed Stub for BaseCore.prototype.removeNotificationListener.\n  // Removed Stub for BaseCore.prototype.getCookieMgr.\n  // Removed Stub for BaseCore.prototype.setCookieMgr.\n  // Removed Stub for BaseCore.prototype.getPerfMgr.\n  // Removed Stub for BaseCore.prototype.setPerfMgr.\n  // Removed Stub for BaseCore.prototype.eventCnt.\n  // Removed Stub for BaseCore.prototype.pollInternalLogs.\n  // Removed Stub for BaseCore.prototype.stopPollingInternalLogs.\n  // Removed Stub for BaseCore.prototype.addTelemetryInitializer.\n  // Removed Stub for BaseCore.prototype.unload.\n  // Removed Stub for BaseCore.prototype.getPlugin.\n  // Removed Stub for BaseCore.prototype.addPlugin.\n  // Removed Stub for BaseCore.prototype.evtNamespace.\n  // Removed Stub for BaseCore.prototype.addUnloadCb.\n  // Removed Stub for BaseCore.prototype.flush.\n  // Removed Stub for BaseCore.prototype.getTraceCtx.\n  // Removed Stub for BaseCore.prototype.setTraceCtx.\n  // Removed Stub for BaseCore.prototype.releaseQueue.\n  // Removed Stub for BaseCore.prototype._updateHook.\n  // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n  // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n  // this will be removed when ES3 support is dropped.\n  BaseCore.__ieDyn = 1;\n  return BaseCore;\n}();\nexport { BaseCore };", "map": {"version": 3, "names": ["__spreadArrayFn", "__spread<PERSON><PERSON>y", "dynamicProto", "objCreateFn", "_DYN_ADD_NOTIFICATION_LIS1", "_DYN_CONFIG", "_DYN_FLUSH", "_DYN_GET_NOTIFY_MGR", "_DYN_GET_PLUGIN", "_DYN_GET_PROCESS_TEL_CONT0", "_DYN_IDENTIFIER", "_DYN_INITIALIZE", "_DYN_INSTRUMENTATION_KEY", "_DYN_IS_ASYNC", "_DYN_IS_INITIALIZED", "_DYN_LENGTH", "_DYN_LOGGER", "_DYN_MESSAGE", "_DYN_MESSAGE_ID", "_DYN_NAME", "_DYN_ON_COMPLETE", "_DYN_PROCESS_NEXT", "_DYN_PUSH", "_DYN_REMOVE_NOTIFICATION_2", "_DYN_SET_ENABLED", "_DYN_SPLICE", "_DYN_STOP_POLLING_INTERNA3", "_DYN_TEARDOWN", "_DYN_TIME", "_DYN__EXTENSIONS", "ChannelControllerPriority", "createChannelControllerPlugin", "createChannelQueues", "createCookieMgr", "createUniqueNamespace", "getDebugListener", "Diagnostic<PERSON>og<PERSON>", "_InternalLogMessage", "_throwInternal", "_warnToConsole", "arrFor<PERSON>ach", "arrIndexOf", "getCfgValue", "getSetValue", "isFunction", "isNullOrUndefined", "objExtend", "objFreeze", "proxyFunctionAs", "proxyFunctions", "throwError", "toISOString", "STR_CHANNELS", "STR_CORE", "STR_CREATE_PERF_MGR", "STR_DISABLED", "STR_EVENTS_DISCARDED", "STR_EVENTS_SEND_REQUEST", "STR_EVENTS_SENT", "STR_EXTENSIONS", "STR_EXTENSION_CONFIG", "STR_GET_PERF_MGR", "STR_PRIORITY", "PerfManager", "getGblPerfMgr", "createProcessTelemetryContext", "createProcessTelemetryUnloadContext", "createProcessTelemetryUpdateContext", "createTelemetryProxyChain", "_getPluginState", "createDistributedTraceContext", "initializePlugins", "sortPlugins", "TelemetryInitializerPlugin", "createUnloadHandlerContainer", "strValidationError", "strNotificationManager", "strSdkUnloadingError", "strSdkNotInitialized", "defaultInitConfig", "loggingLevelConsole", "_createPerfManager", "core", "notificationMgr", "_validateExtensions", "logger", "channelPriority", "allExtensions", "_a", "coreExtensions", "extPriorities", "ext", "extPriority", "identifier", "all", "_isPluginPresent", "thePlugin", "plugins", "exists", "plugin", "_createDummyNotificationManager", "listener", "events", "reason", "sendReason", "isAsync", "BaseCore", "_config", "_isInitialized", "_eventQueue", "_notificationManager", "_perfManager", "_cfgPerfManager", "_cookieManager", "_plugin<PERSON><PERSON>n", "_configExtensions", "_coreExtensions", "_channelControl", "_channelConfig", "_channelQueue", "_isUnloading", "_telemetryInitializerPlugin", "_internalLogsEventName", "_evtNamespace", "_unloadHandlers", "_debugListener", "_traceCtx", "_internalLog<PERSON><PERSON>er", "_forceStopInternalLogPoller", "_self", "_initDefaults", "config", "extensions", "notificationManager", "_initDebugListener", "_initPerfManager", "_initExtConfig", "cfgExtensions", "apply", "_initPlugin<PERSON><PERSON>n", "releaseQueue", "getTransmissionControls", "controls", "channels", "queue", "track", "telemetryItem", "i<PERSON>ey", "Date", "ver", "_createTelCtx", "getCookieMgr", "setCookieMgr", "cookieMgr", "enablePerfMgr", "createPerfMgr", "setPerfMgr", "perfMgr", "eventCnt", "eventQueue", "event", "pollInternalLogs", "eventName", "clearInterval", "_startInternalLogTimer", "alwaysStart", "shouldStart", "interval", "diagnosticLogInterval", "setInterval", "_flushInternalLogs", "unload", "unloadComplete", "cbTimeout", "unloadState", "flushComplete", "processUnloadCtx", "_get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_doUnload", "run", "_flushChannels", "_getPlugin", "addPlugin", "replaceExisting", "addCb", "_logOrThrowError", "existingPlugin", "updateState", "_addPlugin", "removed", "added", "removedPlugins_1", "_removePlugins", "evtNamespace", "getTraceCtx", "createNew", "setTraceCtx", "traceCtx", "theCtx", "theExtensions", "idx", "_setQueue", "initCtx", "slice", "_doUpdate", "pluginIdentifier", "theExt", "getChannel", "enabled", "isEnabled", "pluginState", "remove", "removeCb", "pluginsToRemove", "thePlugins", "removeComplete", "unload<PERSON><PERSON><PERSON>", "unloadCtx", "newConfigExtensions", "newChannelConfig", "newQueue", "channel", "logMessage", "item", "time", "baseType", "dataType", "baseData", "message", "callBack", "disableDbgExt", "extConfig", "NotificationManager", "updateCtx", "_updateHook", "__ie<PERSON>yn"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/BaseCore.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Core, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\n\"use strict\";\r\nimport { __spreadArrayFn as __spreadArray } from \"@microsoft/applicationinsights-shims\";\r\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\r\nimport { objCreateFn } from \"@microsoft/applicationinsights-shims\";\r\nimport { _DYN_ADD_NOTIFICATION_LIS1, _DYN_CONFIG, _DYN_FLUSH, _DYN_GET_NOTIFY_MGR, _DYN_GET_PLUGIN, _DYN_GET_PROCESS_TEL_CONT0, _DYN_IDENTIFIER, _DYN_INITIALIZE, _DYN_INSTRUMENTATION_KEY, _DYN_IS_ASYNC, _DYN_IS_INITIALIZED, _DYN_LENGTH, _DYN_LOGGER, _DY<PERSON>_MESSAGE, _DYN_MESSAGE_ID, _DY<PERSON>_NAME, _DY<PERSON>_ON_COMPLETE, _DYN_PROCESS_NEXT, _DYN_PUSH, _DYN_REMOVE_NOTIFICATION_2, _DYN_SET_ENABLED, _DYN_SPLICE, _DYN_STOP_POLLING_INTERNA3, _DYN_TEARDOWN, _DYN_TIME, _DYN__EXTENSIONS } from \"../__DynamicConstants\";\r\nimport { ChannelControllerPriority, createChannelControllerPlugin, createChannelQueues } from \"./ChannelController\";\r\nimport { createCookieMgr } from \"./CookieMgr\";\r\nimport { createUniqueNamespace } from \"./DataCacheHelper\";\r\nimport { getDebugListener } from \"./DbgExtensionUtils\";\r\nimport { DiagnosticLogger, _InternalLogMessage, _throwInternal, _warnToConsole } from \"./DiagnosticLogger\";\r\nimport { arrForEach, arrIndexOf, getCfgValue, getSetValue, isFunction, isNullOrUndefined, objExtend, objFreeze, proxyFunctionAs, proxyFunctions, throwError, toISOString } from \"./HelperFuncs\";\r\nimport { STR_CHANNELS, STR_CORE, STR_CREATE_PERF_MGR, STR_DISABLED, STR_EVENTS_DISCARDED, STR_EVENTS_SEND_REQUEST, STR_EVENTS_SENT, STR_EXTENSIONS, STR_EXTENSION_CONFIG, STR_GET_PERF_MGR, STR_PRIORITY } from \"./InternalConstants\";\r\nimport { PerfManager, getGblPerfMgr } from \"./PerfManager\";\r\nimport { createProcessTelemetryContext, createProcessTelemetryUnloadContext, createProcessTelemetryUpdateContext, createTelemetryProxyChain } from \"./ProcessTelemetryContext\";\r\nimport { _getPluginState, createDistributedTraceContext, initializePlugins, sortPlugins } from \"./TelemetryHelpers\";\r\nimport { TelemetryInitializerPlugin } from \"./TelemetryInitializerPlugin\";\r\nimport { createUnloadHandlerContainer } from \"./UnloadHandlerContainer\";\r\nvar strValidationError = \"Plugins must provide initialize method\";\r\nvar strNotificationManager = \"_notificationManager\";\r\nvar strSdkUnloadingError = \"SDK is still unloading...\";\r\nvar strSdkNotInitialized = \"SDK is not initialized\";\r\n// const strPluginUnloadFailed = \"Failed to unload plugin\";\r\nvar defaultInitConfig = {\r\n    // Have the Diagnostic Logger default to log critical errors to the console\r\n    loggingLevelConsole: 1 /* eLoggingSeverity.CRITICAL */\r\n};\r\n/**\r\n * Helper to create the default performance manager\r\n * @param core\r\n * @param notificationMgr\r\n */\r\nfunction _createPerfManager(core, notificationMgr) {\r\n    return new PerfManager(notificationMgr);\r\n}\r\nfunction _validateExtensions(logger, channelPriority, allExtensions) {\r\n    var _a;\r\n    // Concat all available extensions\r\n    var coreExtensions = [];\r\n    // Check if any two extensions have the same priority, then warn to console\r\n    // And extract the local extensions from the\r\n    var extPriorities = {};\r\n    // Extension validation\r\n    arrForEach(allExtensions, function (ext) {\r\n        // Check for ext.initialize\r\n        if (isNullOrUndefined(ext) || isNullOrUndefined(ext[_DYN_INITIALIZE /* @min:%2einitialize */])) {\r\n            throwError(strValidationError);\r\n        }\r\n        var extPriority = ext[STR_PRIORITY /* @min:%2epriority */];\r\n        var identifier = ext[_DYN_IDENTIFIER /* @min:%2eidentifier */];\r\n        if (ext && extPriority) {\r\n            if (!isNullOrUndefined(extPriorities[extPriority])) {\r\n                _warnToConsole(logger, \"Two extensions have same priority #\" + extPriority + \" - \" + extPriorities[extPriority] + \", \" + identifier);\r\n            }\r\n            else {\r\n                // set a value\r\n                extPriorities[extPriority] = identifier;\r\n            }\r\n        }\r\n        // Split extensions to core and channelController\r\n        if (!extPriority || extPriority < channelPriority) {\r\n            // Add to core extension that will be managed by BaseCore\r\n            coreExtensions[_DYN_PUSH /* @min:%2epush */](ext);\r\n        }\r\n    });\r\n    return _a = {\r\n            all: allExtensions\r\n        },\r\n        _a[STR_CORE /* @min:core */] = coreExtensions,\r\n        _a;\r\n}\r\nfunction _isPluginPresent(thePlugin, plugins) {\r\n    var exists = false;\r\n    arrForEach(plugins, function (plugin) {\r\n        if (plugin === thePlugin) {\r\n            exists = true;\r\n            return -1;\r\n        }\r\n    });\r\n    return exists;\r\n}\r\nfunction _createDummyNotificationManager() {\r\n    var _a;\r\n    return objCreateFn((_a = {},\r\n        _a[_DYN_ADD_NOTIFICATION_LIS1 /* @min:addNotificationListener */] = function (listener) { },\r\n        _a[_DYN_REMOVE_NOTIFICATION_2 /* @min:removeNotificationListener */] = function (listener) { },\r\n        _a[STR_EVENTS_SENT /* @min:eventsSent */] = function (events) { },\r\n        _a[STR_EVENTS_DISCARDED /* @min:eventsDiscarded */] = function (events, reason) { },\r\n        _a[STR_EVENTS_SEND_REQUEST /* @min:eventsSendRequest */] = function (sendReason, isAsync) { },\r\n        _a));\r\n}\r\nvar BaseCore = /** @class */ (function () {\r\n    function BaseCore() {\r\n        // NOTE!: DON'T set default values here, instead set them in the _initDefaults() function as it is also called during teardown()\r\n        var _config;\r\n        var _isInitialized;\r\n        var _eventQueue;\r\n        var _notificationManager;\r\n        var _perfManager;\r\n        var _cfgPerfManager;\r\n        var _cookieManager;\r\n        var _pluginChain;\r\n        var _configExtensions;\r\n        var _coreExtensions;\r\n        var _channelControl;\r\n        var _channelConfig;\r\n        var _channelQueue;\r\n        var _isUnloading;\r\n        var _telemetryInitializerPlugin;\r\n        var _internalLogsEventName;\r\n        var _evtNamespace;\r\n        var _unloadHandlers;\r\n        var _debugListener;\r\n        var _traceCtx;\r\n        /**\r\n         * Internal log poller\r\n         */\r\n        var _internalLogPoller = 0;\r\n        var _forceStopInternalLogPoller = false;\r\n        dynamicProto(BaseCore, this, function (_self) {\r\n            // Set the default values (also called during teardown)\r\n            _initDefaults();\r\n            _self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */] = function () { return _isInitialized; };\r\n            // Creating the self.initialize = ()\r\n            _self[_DYN_INITIALIZE /* @min:%2einitialize */] = function (config, extensions, logger, notificationManager) {\r\n                if (_isUnloading) {\r\n                    throwError(strSdkUnloadingError);\r\n                }\r\n                // Make sure core is only initialized once\r\n                if (_self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */]()) {\r\n                    throwError(\"Core should not be initialized more than once\");\r\n                }\r\n                _config = config || {};\r\n                _self[_DYN_CONFIG /* @min:%2econfig */] = _config;\r\n                if (isNullOrUndefined(config[_DYN_INSTRUMENTATION_KEY /* @min:%2einstrumentationKey */])) {\r\n                    throwError(\"Please provide instrumentation key\");\r\n                }\r\n                _notificationManager = notificationManager;\r\n                // For backward compatibility only\r\n                _self[strNotificationManager] = notificationManager;\r\n                _initDebugListener();\r\n                _initPerfManager();\r\n                // add notification to the extensions in the config so other plugins can access it\r\n                _initExtConfig();\r\n                if (logger) {\r\n                    _self[_DYN_LOGGER /* @min:%2elogger */] = logger;\r\n                }\r\n                var cfgExtensions = getSetValue(_config, STR_EXTENSIONS, []);\r\n                // Extension validation\r\n                _configExtensions = [];\r\n                _configExtensions[_DYN_PUSH /* @min:%2epush */].apply(_configExtensions, __spreadArray(__spreadArray([], extensions, false), cfgExtensions, false));\r\n                _channelConfig = getSetValue(_config, STR_CHANNELS, []);\r\n                _initPluginChain(null);\r\n                if (!_channelQueue || _channelQueue[_DYN_LENGTH /* @min:%2elength */] === 0) {\r\n                    throwError(\"No \" + STR_CHANNELS + \" available\");\r\n                }\r\n                _isInitialized = true;\r\n                _self.releaseQueue();\r\n            };\r\n            _self.getTransmissionControls = function () {\r\n                var controls = [];\r\n                if (_channelQueue) {\r\n                    arrForEach(_channelQueue, function (channels) {\r\n                        controls[_DYN_PUSH /* @min:%2epush */](channels.queue);\r\n                    });\r\n                }\r\n                return objFreeze(controls);\r\n            };\r\n            _self.track = function (telemetryItem) {\r\n                // setup default iKey if not passed in\r\n                telemetryItem.iKey = telemetryItem.iKey || _config[_DYN_INSTRUMENTATION_KEY /* @min:%2einstrumentationKey */];\r\n                // add default timestamp if not passed in\r\n                telemetryItem[_DYN_TIME /* @min:%2etime */] = telemetryItem[_DYN_TIME /* @min:%2etime */] || toISOString(new Date());\r\n                // Common Schema 4.0\r\n                telemetryItem.ver = telemetryItem.ver || \"4.0\";\r\n                if (!_isUnloading && _self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */]()) {\r\n                    // Process the telemetry plugin chain\r\n                    _createTelCtx()[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](telemetryItem);\r\n                }\r\n                else {\r\n                    // Queue events until all extensions are initialized\r\n                    _eventQueue[_DYN_PUSH /* @min:%2epush */](telemetryItem);\r\n                }\r\n            };\r\n            _self[_DYN_GET_PROCESS_TEL_CONT0 /* @min:%2egetProcessTelContext */] = _createTelCtx;\r\n            _self[_DYN_GET_NOTIFY_MGR /* @min:%2egetNotifyMgr */] = function () {\r\n                if (!_notificationManager) {\r\n                    // Create Dummy notification manager\r\n                    _notificationManager = _createDummyNotificationManager();\r\n                    // For backward compatibility only\r\n                    _self[strNotificationManager] = _notificationManager;\r\n                }\r\n                return _notificationManager;\r\n            };\r\n            /**\r\n             * Adds a notification listener. The SDK calls methods on the listener when an appropriate notification is raised.\r\n             * The added plugins must raise notifications. If the plugins do not implement the notifications, then no methods will be\r\n             * called.\r\n             * @param {INotificationListener} listener - An INotificationListener object.\r\n             */\r\n            _self[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */] = function (listener) {\r\n                if (_notificationManager) {\r\n                    _notificationManager[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */](listener);\r\n                }\r\n            };\r\n            /**\r\n             * Removes all instances of the listener.\r\n             * @param {INotificationListener} listener - INotificationListener to remove.\r\n             */\r\n            _self[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */] = function (listener) {\r\n                if (_notificationManager) {\r\n                    _notificationManager[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */](listener);\r\n                }\r\n            };\r\n            _self.getCookieMgr = function () {\r\n                if (!_cookieManager) {\r\n                    _cookieManager = createCookieMgr(_config, _self[_DYN_LOGGER /* @min:%2elogger */]);\r\n                }\r\n                return _cookieManager;\r\n            };\r\n            _self.setCookieMgr = function (cookieMgr) {\r\n                _cookieManager = cookieMgr;\r\n            };\r\n            _self[STR_GET_PERF_MGR /* @min:%2egetPerfMgr */] = function () {\r\n                if (!_perfManager && !_cfgPerfManager) {\r\n                    if (getCfgValue(_config.enablePerfMgr)) {\r\n                        var createPerfMgr = getCfgValue(_config[STR_CREATE_PERF_MGR /* @min:%2ecreatePerfMgr */]);\r\n                        if (isFunction(createPerfMgr)) {\r\n                            _cfgPerfManager = createPerfMgr(_self, _self[_DYN_GET_NOTIFY_MGR /* @min:%2egetNotifyMgr */]());\r\n                        }\r\n                    }\r\n                }\r\n                return _perfManager || _cfgPerfManager || getGblPerfMgr();\r\n            };\r\n            _self.setPerfMgr = function (perfMgr) {\r\n                _perfManager = perfMgr;\r\n            };\r\n            _self.eventCnt = function () {\r\n                return _eventQueue[_DYN_LENGTH /* @min:%2elength */];\r\n            };\r\n            _self.releaseQueue = function () {\r\n                if (_isInitialized && _eventQueue[_DYN_LENGTH /* @min:%2elength */] > 0) {\r\n                    var eventQueue = _eventQueue;\r\n                    _eventQueue = [];\r\n                    arrForEach(eventQueue, function (event) {\r\n                        _createTelCtx()[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](event);\r\n                    });\r\n                }\r\n            };\r\n            _self.pollInternalLogs = function (eventName) {\r\n                _internalLogsEventName = eventName || null;\r\n                _forceStopInternalLogPoller = false;\r\n                if (_internalLogPoller) {\r\n                    clearInterval(_internalLogPoller);\r\n                    _internalLogPoller = null;\r\n                }\r\n                return _startInternalLogTimer(true);\r\n            };\r\n            function _startInternalLogTimer(alwaysStart) {\r\n                if (!_internalLogPoller && !_forceStopInternalLogPoller) {\r\n                    var shouldStart = alwaysStart || (_self[_DYN_LOGGER /* @min:%2elogger */] && _self[_DYN_LOGGER /* @min:%2elogger */].queue[_DYN_LENGTH /* @min:%2elength */] > 0);\r\n                    if (shouldStart) {\r\n                        var interval = getCfgValue(_config.diagnosticLogInterval);\r\n                        if (!interval || !(interval > 0)) {\r\n                            interval = 10000;\r\n                        }\r\n                        // Keeping as an interval timer for backward compatibility as it returns the result\r\n                        _internalLogPoller = setInterval(function () {\r\n                            clearInterval(_internalLogPoller);\r\n                            _internalLogPoller = 0;\r\n                            _flushInternalLogs();\r\n                        }, interval);\r\n                    }\r\n                }\r\n                return _internalLogPoller;\r\n            }\r\n            _self[_DYN_STOP_POLLING_INTERNA3 /* @min:%2estopPollingInternalLogs */] = function () {\r\n                _forceStopInternalLogPoller = true;\r\n                if (_internalLogPoller) {\r\n                    clearInterval(_internalLogPoller);\r\n                    _internalLogPoller = 0;\r\n                    _flushInternalLogs();\r\n                }\r\n            };\r\n            // Add addTelemetryInitializer\r\n            proxyFunctions(_self, function () { return _telemetryInitializerPlugin; }, [\"addTelemetryInitializer\"]);\r\n            _self.unload = function (isAsync, unloadComplete, cbTimeout) {\r\n                var _a;\r\n                if (isAsync === void 0) { isAsync = true; }\r\n                if (!_isInitialized) {\r\n                    // The SDK is not initialized\r\n                    throwError(strSdkNotInitialized);\r\n                }\r\n                // Check if the SDK still unloading so throw\r\n                if (_isUnloading) {\r\n                    // The SDK is already unloading\r\n                    throwError(strSdkUnloadingError);\r\n                }\r\n                var unloadState = (_a = {\r\n                        reason: 50 /* TelemetryUnloadReason.SdkUnload */\r\n                    },\r\n                    _a[_DYN_IS_ASYNC /* @min:isAsync */] = isAsync,\r\n                    _a.flushComplete = false,\r\n                    _a);\r\n                var processUnloadCtx = createProcessTelemetryUnloadContext(_getPluginChain(), _self);\r\n                processUnloadCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](function () {\r\n                    _initDefaults();\r\n                    unloadComplete && unloadComplete(unloadState);\r\n                }, _self);\r\n                function _doUnload(flushComplete) {\r\n                    unloadState.flushComplete = flushComplete;\r\n                    _isUnloading = true;\r\n                    // Run all of the unload handlers first (before unloading the plugins)\r\n                    _unloadHandlers.run(processUnloadCtx, unloadState);\r\n                    // Stop polling the internal logs\r\n                    _self[_DYN_STOP_POLLING_INTERNA3 /* @min:%2estopPollingInternalLogs */]();\r\n                    // Start unloading the components, from this point onwards the SDK should be considered to be in an unstable state\r\n                    processUnloadCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](unloadState);\r\n                }\r\n                _flushInternalLogs();\r\n                if (!_flushChannels(isAsync, _doUnload, 6 /* SendRequestReason.SdkUnload */, cbTimeout)) {\r\n                    _doUnload(false);\r\n                }\r\n            };\r\n            _self[_DYN_GET_PLUGIN /* @min:%2egetPlugin */] = _getPlugin;\r\n            _self.addPlugin = function (plugin, replaceExisting, isAsync, addCb) {\r\n                if (!plugin) {\r\n                    addCb && addCb(false);\r\n                    _logOrThrowError(strValidationError);\r\n                    return;\r\n                }\r\n                var existingPlugin = _getPlugin(plugin[_DYN_IDENTIFIER /* @min:%2eidentifier */]);\r\n                if (existingPlugin && !replaceExisting) {\r\n                    addCb && addCb(false);\r\n                    _logOrThrowError(\"Plugin [\" + plugin[_DYN_IDENTIFIER /* @min:%2eidentifier */] + \"] is already loaded!\");\r\n                    return;\r\n                }\r\n                var updateState = {\r\n                    reason: 16 /* TelemetryUpdateReason.PluginAdded */\r\n                };\r\n                function _addPlugin(removed) {\r\n                    _configExtensions[_DYN_PUSH /* @min:%2epush */](plugin);\r\n                    updateState.added = [plugin];\r\n                    // Re-Initialize the plugin chain\r\n                    _initPluginChain(updateState);\r\n                    addCb && addCb(true);\r\n                }\r\n                if (existingPlugin) {\r\n                    var removedPlugins_1 = [existingPlugin.plugin];\r\n                    var unloadState = {\r\n                        reason: 2 /* TelemetryUnloadReason.PluginReplace */,\r\n                        isAsync: !!isAsync\r\n                    };\r\n                    _removePlugins(removedPlugins_1, unloadState, function (removed) {\r\n                        if (!removed) {\r\n                            // Previous plugin was successfully removed or was not installed\r\n                            addCb && addCb(false);\r\n                        }\r\n                        else {\r\n                            updateState.removed = removedPlugins_1;\r\n                            updateState.reason |= 32 /* TelemetryUpdateReason.PluginRemoved */;\r\n                            _addPlugin(true);\r\n                        }\r\n                    });\r\n                }\r\n                else {\r\n                    _addPlugin(false);\r\n                }\r\n            };\r\n            _self.evtNamespace = function () {\r\n                return _evtNamespace;\r\n            };\r\n            _self[_DYN_FLUSH /* @min:%2eflush */] = _flushChannels;\r\n            _self.getTraceCtx = function (createNew) {\r\n                if (!_traceCtx) {\r\n                    _traceCtx = createDistributedTraceContext();\r\n                }\r\n                return _traceCtx;\r\n            };\r\n            _self.setTraceCtx = function (traceCtx) {\r\n                _traceCtx = traceCtx || null;\r\n            };\r\n            // Create the addUnloadCb\r\n            proxyFunctionAs(_self, \"addUnloadCb\", function () { return _unloadHandlers; }, \"add\");\r\n            function _initDefaults() {\r\n                _isInitialized = false;\r\n                // Use a default logger so initialization errors are not dropped on the floor with full logging\r\n                _config = objExtend(true, {}, defaultInitConfig);\r\n                _self[_DYN_CONFIG /* @min:%2econfig */] = _config;\r\n                _self[_DYN_LOGGER /* @min:%2elogger */] = new DiagnosticLogger(_config);\r\n                _self[_DYN__EXTENSIONS /* @min:%2e_extensions */] = [];\r\n                _telemetryInitializerPlugin = new TelemetryInitializerPlugin();\r\n                _eventQueue = [];\r\n                _notificationManager = null;\r\n                _perfManager = null;\r\n                _cfgPerfManager = null;\r\n                _cookieManager = null;\r\n                _pluginChain = null;\r\n                _coreExtensions = null;\r\n                _configExtensions = [];\r\n                _channelControl = null;\r\n                _channelConfig = null;\r\n                _channelQueue = null;\r\n                _isUnloading = false;\r\n                _internalLogsEventName = null;\r\n                _evtNamespace = createUniqueNamespace(\"AIBaseCore\", true);\r\n                _unloadHandlers = createUnloadHandlerContainer();\r\n                _traceCtx = null;\r\n            }\r\n            function _createTelCtx() {\r\n                var theCtx = createProcessTelemetryContext(_getPluginChain(), _config, _self);\r\n                theCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](_startInternalLogTimer);\r\n                return theCtx;\r\n            }\r\n            // Initialize or Re-initialize the plugins\r\n            function _initPluginChain(updateState) {\r\n                // Extension validation\r\n                var theExtensions = _validateExtensions(_self[_DYN_LOGGER /* @min:%2elogger */], ChannelControllerPriority, _configExtensions);\r\n                _coreExtensions = theExtensions[STR_CORE /* @min:%2ecore */];\r\n                _pluginChain = null;\r\n                // Sort the complete set of extensions by priority\r\n                var allExtensions = theExtensions.all;\r\n                // Initialize the Channel Queues and the channel plugins first\r\n                _channelQueue = objFreeze(createChannelQueues(_channelConfig, allExtensions, _self));\r\n                if (_channelControl) {\r\n                    // During add / remove of a plugin this may get called again, so don't re-add if already present\r\n                    // But we also want the controller as the last, so remove if already present\r\n                    // And reusing the existing instance, just in case an installed plugin has a reference and\r\n                    // is using it.\r\n                    var idx = arrIndexOf(allExtensions, _channelControl);\r\n                    if (idx !== -1) {\r\n                        allExtensions[_DYN_SPLICE /* @min:%2esplice */](idx, 1);\r\n                    }\r\n                    idx = arrIndexOf(_coreExtensions, _channelControl);\r\n                    if (idx !== -1) {\r\n                        _coreExtensions[_DYN_SPLICE /* @min:%2esplice */](idx, 1);\r\n                    }\r\n                    _channelControl._setQueue(_channelQueue);\r\n                }\r\n                else {\r\n                    _channelControl = createChannelControllerPlugin(_channelQueue, _self);\r\n                }\r\n                // Add on \"channelController\" as the last \"plugin\"\r\n                allExtensions[_DYN_PUSH /* @min:%2epush */](_channelControl);\r\n                _coreExtensions[_DYN_PUSH /* @min:%2epush */](_channelControl);\r\n                // Required to allow plugins to call core.getPlugin() during their own initialization\r\n                _self[_DYN__EXTENSIONS /* @min:%2e_extensions */] = sortPlugins(allExtensions);\r\n                // Initialize the controls\r\n                _channelControl[_DYN_INITIALIZE /* @min:%2einitialize */](_config, _self, allExtensions);\r\n                var initCtx = _createTelCtx();\r\n                initializePlugins(initCtx, allExtensions);\r\n                // Now reset the extensions to just those being managed by Basecore\r\n                _self[_DYN__EXTENSIONS /* @min:%2e_extensions */] = objFreeze(sortPlugins(_coreExtensions || [])).slice();\r\n                if (updateState) {\r\n                    _doUpdate(updateState);\r\n                }\r\n            }\r\n            function _getPlugin(pluginIdentifier) {\r\n                var _a;\r\n                var theExt = null;\r\n                var thePlugin = null;\r\n                arrForEach(_self[_DYN__EXTENSIONS /* @min:%2e_extensions */], function (ext) {\r\n                    if (ext[_DYN_IDENTIFIER /* @min:%2eidentifier */] === pluginIdentifier && ext !== _channelControl && ext !== _telemetryInitializerPlugin) {\r\n                        thePlugin = ext;\r\n                        return -1;\r\n                    }\r\n                });\r\n                if (!thePlugin && _channelControl) {\r\n                    // Check the channel Controller\r\n                    thePlugin = _channelControl.getChannel(pluginIdentifier);\r\n                }\r\n                if (thePlugin) {\r\n                    theExt = (_a = {\r\n                            plugin: thePlugin\r\n                        },\r\n                        _a[_DYN_SET_ENABLED /* @min:setEnabled */] = function (enabled) {\r\n                            _getPluginState(thePlugin)[STR_DISABLED] = !enabled;\r\n                        },\r\n                        _a.isEnabled = function () {\r\n                            var pluginState = _getPluginState(thePlugin);\r\n                            return !pluginState[_DYN_TEARDOWN /* @min:%2eteardown */] && !pluginState[STR_DISABLED];\r\n                        },\r\n                        _a.remove = function (isAsync, removeCb) {\r\n                            var _a;\r\n                            if (isAsync === void 0) { isAsync = true; }\r\n                            var pluginsToRemove = [thePlugin];\r\n                            var unloadState = (_a = {\r\n                                    reason: 1 /* TelemetryUnloadReason.PluginUnload */\r\n                                },\r\n                                _a[_DYN_IS_ASYNC /* @min:isAsync */] = isAsync,\r\n                                _a);\r\n                            _removePlugins(pluginsToRemove, unloadState, function (removed) {\r\n                                if (removed) {\r\n                                    // Re-Initialize the plugin chain\r\n                                    _initPluginChain({\r\n                                        reason: 32 /* TelemetryUpdateReason.PluginRemoved */,\r\n                                        removed: pluginsToRemove\r\n                                    });\r\n                                }\r\n                                removeCb && removeCb(removed);\r\n                            });\r\n                        },\r\n                        _a);\r\n                }\r\n                return theExt;\r\n            }\r\n            function _getPluginChain() {\r\n                if (!_pluginChain) {\r\n                    // copy the collection of extensions\r\n                    var extensions = (_coreExtensions || []).slice();\r\n                    // During add / remove this may get called again, so don't readd if already present\r\n                    if (arrIndexOf(extensions, _telemetryInitializerPlugin) === -1) {\r\n                        extensions[_DYN_PUSH /* @min:%2epush */](_telemetryInitializerPlugin);\r\n                    }\r\n                    _pluginChain = createTelemetryProxyChain(sortPlugins(extensions), _config, _self);\r\n                }\r\n                return _pluginChain;\r\n            }\r\n            function _removePlugins(thePlugins, unloadState, removeComplete) {\r\n                if (thePlugins && thePlugins[_DYN_LENGTH /* @min:%2elength */] > 0) {\r\n                    var unloadChain = createTelemetryProxyChain(thePlugins, _config, _self);\r\n                    var unloadCtx = createProcessTelemetryUnloadContext(unloadChain, _self);\r\n                    unloadCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](function () {\r\n                        var removed = false;\r\n                        // Remove the listed config extensions\r\n                        var newConfigExtensions = [];\r\n                        arrForEach(_configExtensions, function (plugin, idx) {\r\n                            if (!_isPluginPresent(plugin, thePlugins)) {\r\n                                newConfigExtensions[_DYN_PUSH /* @min:%2epush */](plugin);\r\n                            }\r\n                            else {\r\n                                removed = true;\r\n                            }\r\n                        });\r\n                        _configExtensions = newConfigExtensions;\r\n                        // Re-Create the channel config\r\n                        var newChannelConfig = [];\r\n                        if (_channelConfig) {\r\n                            arrForEach(_channelConfig, function (queue, idx) {\r\n                                var newQueue = [];\r\n                                arrForEach(queue, function (channel) {\r\n                                    if (!_isPluginPresent(channel, thePlugins)) {\r\n                                        newQueue[_DYN_PUSH /* @min:%2epush */](channel);\r\n                                    }\r\n                                    else {\r\n                                        removed = true;\r\n                                    }\r\n                                });\r\n                                newChannelConfig[_DYN_PUSH /* @min:%2epush */](newQueue);\r\n                            });\r\n                            _channelConfig = newChannelConfig;\r\n                        }\r\n                        removeComplete && removeComplete(removed);\r\n                        _startInternalLogTimer();\r\n                    });\r\n                    unloadCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](unloadState);\r\n                }\r\n                else {\r\n                    removeComplete(false);\r\n                }\r\n            }\r\n            function _flushInternalLogs() {\r\n                if (_self[_DYN_LOGGER /* @min:%2elogger */] && _self[_DYN_LOGGER /* @min:%2elogger */].queue) {\r\n                    var queue = _self[_DYN_LOGGER /* @min:%2elogger */].queue.slice(0);\r\n                    _self[_DYN_LOGGER /* @min:%2elogger */].queue[_DYN_LENGTH /* @min:%2elength */] = 0;\r\n                    arrForEach(queue, function (logMessage) {\r\n                        var _a;\r\n                        var item = (_a = {},\r\n                            _a[_DYN_NAME /* @min:name */] = _internalLogsEventName ? _internalLogsEventName : \"InternalMessageId: \" + logMessage[_DYN_MESSAGE_ID /* @min:%2emessageId */],\r\n                            _a.iKey = getCfgValue(_config[_DYN_INSTRUMENTATION_KEY /* @min:%2einstrumentationKey */]),\r\n                            _a.time = toISOString(new Date()),\r\n                            _a.baseType = _InternalLogMessage.dataType,\r\n                            _a.baseData = { message: logMessage[_DYN_MESSAGE /* @min:%2emessage */] },\r\n                            _a);\r\n                        _self.track(item);\r\n                    });\r\n                }\r\n            }\r\n            function _flushChannels(isAsync, callBack, sendReason, cbTimeout) {\r\n                if (_channelControl) {\r\n                    return _channelControl[_DYN_FLUSH /* @min:%2eflush */](isAsync, callBack, sendReason || 6 /* SendRequestReason.SdkUnload */, cbTimeout);\r\n                }\r\n                callBack && callBack(false);\r\n                return true;\r\n            }\r\n            function _initDebugListener() {\r\n                var disableDbgExt = getCfgValue(_config.disableDbgExt);\r\n                if (disableDbgExt === true && _debugListener) {\r\n                    // Remove any previously loaded debug listener\r\n                    _notificationManager[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */](_debugListener);\r\n                    _debugListener = null;\r\n                }\r\n                if (_notificationManager && !_debugListener && disableDbgExt !== true) {\r\n                    _debugListener = getDebugListener(_config);\r\n                    _notificationManager[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */](_debugListener);\r\n                }\r\n            }\r\n            function _initPerfManager() {\r\n                var enablePerfMgr = getCfgValue(_config.enablePerfMgr);\r\n                if (!enablePerfMgr && _cfgPerfManager) {\r\n                    // Remove any existing config based performance manager\r\n                    _cfgPerfManager = null;\r\n                }\r\n                if (enablePerfMgr) {\r\n                    // Set the performance manager creation function if not defined\r\n                    getSetValue(_config, STR_CREATE_PERF_MGR, _createPerfManager);\r\n                }\r\n            }\r\n            function _initExtConfig() {\r\n                var extConfig = getSetValue(_config, STR_EXTENSION_CONFIG, {});\r\n                extConfig.NotificationManager = _notificationManager;\r\n            }\r\n            function _doUpdate(updateState) {\r\n                var updateCtx = createProcessTelemetryUpdateContext(_getPluginChain(), _self);\r\n                updateCtx[_DYN_ON_COMPLETE /* @min:%2eonComplete */](_startInternalLogTimer);\r\n                if (!_self._updateHook || _self._updateHook(updateCtx, updateState) !== true) {\r\n                    updateCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](updateState);\r\n                }\r\n            }\r\n            function _logOrThrowError(message) {\r\n                var logger = _self[_DYN_LOGGER /* @min:%2elogger */];\r\n                if (logger) {\r\n                    // there should always be a logger\r\n                    _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 73 /* _eInternalMessageId.PluginException */, message);\r\n                    _startInternalLogTimer();\r\n                }\r\n                else {\r\n                    throwError(message);\r\n                }\r\n            }\r\n        });\r\n    }\r\n// Removed Stub for BaseCore.prototype.initialize.\r\n// Removed Stub for BaseCore.prototype.getTransmissionControls.\r\n// Removed Stub for BaseCore.prototype.track.\r\n// Removed Stub for BaseCore.prototype.getProcessTelContext.\r\n// Removed Stub for BaseCore.prototype.getNotifyMgr.\r\n// Removed Stub for BaseCore.prototype.addNotificationListener.\r\n// Removed Stub for BaseCore.prototype.removeNotificationListener.\r\n// Removed Stub for BaseCore.prototype.getCookieMgr.\r\n// Removed Stub for BaseCore.prototype.setCookieMgr.\r\n// Removed Stub for BaseCore.prototype.getPerfMgr.\r\n// Removed Stub for BaseCore.prototype.setPerfMgr.\r\n// Removed Stub for BaseCore.prototype.eventCnt.\r\n// Removed Stub for BaseCore.prototype.pollInternalLogs.\r\n// Removed Stub for BaseCore.prototype.stopPollingInternalLogs.\r\n// Removed Stub for BaseCore.prototype.addTelemetryInitializer.\r\n// Removed Stub for BaseCore.prototype.unload.\r\n// Removed Stub for BaseCore.prototype.getPlugin.\r\n// Removed Stub for BaseCore.prototype.addPlugin.\r\n// Removed Stub for BaseCore.prototype.evtNamespace.\r\n// Removed Stub for BaseCore.prototype.addUnloadCb.\r\n// Removed Stub for BaseCore.prototype.flush.\r\n// Removed Stub for BaseCore.prototype.getTraceCtx.\r\n// Removed Stub for BaseCore.prototype.setTraceCtx.\r\n// Removed Stub for BaseCore.prototype.releaseQueue.\r\n// Removed Stub for BaseCore.prototype._updateHook.\r\n    // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n    // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n    // this will be removed when ES3 support is dropped.\n    BaseCore.__ieDyn=1;\n\n    return BaseCore;\r\n}());\r\nexport { BaseCore };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,YAAY;;AACZ,SAASA,eAAe,IAAIC,aAAa,QAAQ,sCAAsC;AACvF,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,0BAA0B,EAAEC,WAAW,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,0BAA0B,EAAEC,eAAe,EAAEC,eAAe,EAAEC,wBAAwB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,0BAA0B,EAAEC,aAAa,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,uBAAuB;AACpf,SAASC,yBAAyB,EAAEC,6BAA6B,EAAEC,mBAAmB,QAAQ,qBAAqB;AACnH,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,cAAc,QAAQ,oBAAoB;AAC1G,SAASC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,cAAc,EAAEC,UAAU,EAAEC,WAAW,QAAQ,eAAe;AAC/L,SAASC,YAAY,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,qBAAqB;AACrO,SAASC,WAAW,EAAEC,aAAa,QAAQ,eAAe;AAC1D,SAASC,6BAA6B,EAAEC,mCAAmC,EAAEC,mCAAmC,EAAEC,yBAAyB,QAAQ,2BAA2B;AAC9K,SAASC,eAAe,EAAEC,6BAA6B,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,oBAAoB;AACnH,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,IAAIC,kBAAkB,GAAG,wCAAwC;AACjE,IAAIC,sBAAsB,GAAG,sBAAsB;AACnD,IAAIC,oBAAoB,GAAG,2BAA2B;AACtD,IAAIC,oBAAoB,GAAG,wBAAwB;AACnD;AACA,IAAIC,iBAAiB,GAAG;EACpB;EACAC,mBAAmB,EAAE,CAAC,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,eAAe,EAAE;EAC/C,OAAO,IAAIpB,WAAW,CAACoB,eAAe,CAAC;AAC3C;AACA,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,eAAe,EAAEC,aAAa,EAAE;EACjE,IAAIC,EAAE;EACN;EACA,IAAIC,cAAc,GAAG,EAAE;EACvB;EACA;EACA,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB;EACAlD,UAAU,CAAC+C,aAAa,EAAE,UAAUI,GAAG,EAAE;IACrC;IACA,IAAI9C,iBAAiB,CAAC8C,GAAG,CAAC,IAAI9C,iBAAiB,CAAC8C,GAAG,CAAChF,eAAe,CAAC,yBAAyB,CAAC,EAAE;MAC5FuC,UAAU,CAACyB,kBAAkB,CAAC;IAClC;IACA,IAAIiB,WAAW,GAAGD,GAAG,CAAC7B,YAAY,CAAC,uBAAuB;IAC1D,IAAI+B,UAAU,GAAGF,GAAG,CAACjF,eAAe,CAAC,yBAAyB;IAC9D,IAAIiF,GAAG,IAAIC,WAAW,EAAE;MACpB,IAAI,CAAC/C,iBAAiB,CAAC6C,aAAa,CAACE,WAAW,CAAC,CAAC,EAAE;QAChDrD,cAAc,CAAC8C,MAAM,EAAE,qCAAqC,GAAGO,WAAW,GAAG,KAAK,GAAGF,aAAa,CAACE,WAAW,CAAC,GAAG,IAAI,GAAGC,UAAU,CAAC;MACxI,CAAC,MACI;QACD;QACAH,aAAa,CAACE,WAAW,CAAC,GAAGC,UAAU;MAC3C;IACJ;IACA;IACA,IAAI,CAACD,WAAW,IAAIA,WAAW,GAAGN,eAAe,EAAE;MAC/C;MACAG,cAAc,CAACnE,SAAS,CAAC,mBAAmB,CAACqE,GAAG,CAAC;IACrD;EACJ,CAAC,CAAC;EACF,OAAOH,EAAE,GAAG;IACJM,GAAG,EAAEP;EACT,CAAC,EACDC,EAAE,CAACnC,QAAQ,CAAC,gBAAgB,GAAGoC,cAAc,EAC7CD,EAAE;AACV;AACA,SAASO,gBAAgBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC1C,IAAIC,MAAM,GAAG,KAAK;EAClB1D,UAAU,CAACyD,OAAO,EAAE,UAAUE,MAAM,EAAE;IAClC,IAAIA,MAAM,KAAKH,SAAS,EAAE;MACtBE,MAAM,GAAG,IAAI;MACb,OAAO,CAAC,CAAC;IACb;EACJ,CAAC,CAAC;EACF,OAAOA,MAAM;AACjB;AACA,SAASE,+BAA+BA,CAAA,EAAG;EACvC,IAAIZ,EAAE;EACN,OAAOrF,WAAW,EAAEqF,EAAE,GAAG,CAAC,CAAC,EACvBA,EAAE,CAACpF,0BAA0B,CAAC,mCAAmC,GAAG,UAAUiG,QAAQ,EAAE,CAAE,CAAC,EAC3Fb,EAAE,CAACjE,0BAA0B,CAAC,sCAAsC,GAAG,UAAU8E,QAAQ,EAAE,CAAE,CAAC,EAC9Fb,EAAE,CAAC9B,eAAe,CAAC,sBAAsB,GAAG,UAAU4C,MAAM,EAAE,CAAE,CAAC,EACjEd,EAAE,CAAChC,oBAAoB,CAAC,2BAA2B,GAAG,UAAU8C,MAAM,EAAEC,MAAM,EAAE,CAAE,CAAC,EACnFf,EAAE,CAAC/B,uBAAuB,CAAC,6BAA6B,GAAG,UAAU+C,UAAU,EAAEC,OAAO,EAAE,CAAE,CAAC,EAC7FjB,EAAE,CAAC,CAAC;AACZ;AACA,IAAIkB,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG;IAChB;IACA,IAAIC,OAAO;IACX,IAAIC,cAAc;IAClB,IAAIC,WAAW;IACf,IAAIC,oBAAoB;IACxB,IAAIC,YAAY;IAChB,IAAIC,eAAe;IACnB,IAAIC,cAAc;IAClB,IAAIC,YAAY;IAChB,IAAIC,iBAAiB;IACrB,IAAIC,eAAe;IACnB,IAAIC,eAAe;IACnB,IAAIC,cAAc;IAClB,IAAIC,aAAa;IACjB,IAAIC,YAAY;IAChB,IAAIC,2BAA2B;IAC/B,IAAIC,sBAAsB;IAC1B,IAAIC,aAAa;IACjB,IAAIC,eAAe;IACnB,IAAIC,cAAc;IAClB,IAAIC,SAAS;IACb;AACR;AACA;IACQ,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,2BAA2B,GAAG,KAAK;IACvC9H,YAAY,CAACwG,QAAQ,EAAE,IAAI,EAAE,UAAUuB,KAAK,EAAE;MAC1C;MACAC,aAAa,CAAC,CAAC;MACfD,KAAK,CAACnH,mBAAmB,CAAC,4BAA4B,GAAG,YAAY;QAAE,OAAO8F,cAAc;MAAE,CAAC;MAC/F;MACAqB,KAAK,CAACtH,eAAe,CAAC,yBAAyB,GAAG,UAAUwH,MAAM,EAAEC,UAAU,EAAE/C,MAAM,EAAEgD,mBAAmB,EAAE;QACzG,IAAIb,YAAY,EAAE;UACdtE,UAAU,CAAC2B,oBAAoB,CAAC;QACpC;QACA;QACA,IAAIoD,KAAK,CAACnH,mBAAmB,CAAC,4BAA4B,CAAC,CAAC,EAAE;UAC1DoC,UAAU,CAAC,+CAA+C,CAAC;QAC/D;QACAyD,OAAO,GAAGwB,MAAM,IAAI,CAAC,CAAC;QACtBF,KAAK,CAAC5H,WAAW,CAAC,qBAAqB,GAAGsG,OAAO;QACjD,IAAI9D,iBAAiB,CAACsF,MAAM,CAACvH,wBAAwB,CAAC,iCAAiC,CAAC,EAAE;UACtFsC,UAAU,CAAC,oCAAoC,CAAC;QACpD;QACA4D,oBAAoB,GAAGuB,mBAAmB;QAC1C;QACAJ,KAAK,CAACrD,sBAAsB,CAAC,GAAGyD,mBAAmB;QACnDC,kBAAkB,CAAC,CAAC;QACpBC,gBAAgB,CAAC,CAAC;QAClB;QACAC,cAAc,CAAC,CAAC;QAChB,IAAInD,MAAM,EAAE;UACR4C,KAAK,CAACjH,WAAW,CAAC,qBAAqB,GAAGqE,MAAM;QACpD;QACA,IAAIoD,aAAa,GAAG9F,WAAW,CAACgE,OAAO,EAAEhD,cAAc,EAAE,EAAE,CAAC;QAC5D;QACAwD,iBAAiB,GAAG,EAAE;QACtBA,iBAAiB,CAAC7F,SAAS,CAAC,mBAAmB,CAACoH,KAAK,CAACvB,iBAAiB,EAAElH,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEmI,UAAU,EAAE,KAAK,CAAC,EAAEK,aAAa,EAAE,KAAK,CAAC,CAAC;QACnJnB,cAAc,GAAG3E,WAAW,CAACgE,OAAO,EAAEvD,YAAY,EAAE,EAAE,CAAC;QACvDuF,gBAAgB,CAAC,IAAI,CAAC;QACtB,IAAI,CAACpB,aAAa,IAAIA,aAAa,CAACxG,WAAW,CAAC,qBAAqB,KAAK,CAAC,EAAE;UACzEmC,UAAU,CAAC,KAAK,GAAGE,YAAY,GAAG,YAAY,CAAC;QACnD;QACAwD,cAAc,GAAG,IAAI;QACrBqB,KAAK,CAACW,YAAY,CAAC,CAAC;MACxB,CAAC;MACDX,KAAK,CAACY,uBAAuB,GAAG,YAAY;QACxC,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIvB,aAAa,EAAE;UACf/E,UAAU,CAAC+E,aAAa,EAAE,UAAUwB,QAAQ,EAAE;YAC1CD,QAAQ,CAACxH,SAAS,CAAC,mBAAmB,CAACyH,QAAQ,CAACC,KAAK,CAAC;UAC1D,CAAC,CAAC;QACN;QACA,OAAOjG,SAAS,CAAC+F,QAAQ,CAAC;MAC9B,CAAC;MACDb,KAAK,CAACgB,KAAK,GAAG,UAAUC,aAAa,EAAE;QACnC;QACAA,aAAa,CAACC,IAAI,GAAGD,aAAa,CAACC,IAAI,IAAIxC,OAAO,CAAC/F,wBAAwB,CAAC,iCAAiC;QAC7G;QACAsI,aAAa,CAACtH,SAAS,CAAC,mBAAmB,GAAGsH,aAAa,CAACtH,SAAS,CAAC,mBAAmB,IAAIuB,WAAW,CAAC,IAAIiG,IAAI,CAAC,CAAC,CAAC;QACpH;QACAF,aAAa,CAACG,GAAG,GAAGH,aAAa,CAACG,GAAG,IAAI,KAAK;QAC9C,IAAI,CAAC7B,YAAY,IAAIS,KAAK,CAACnH,mBAAmB,CAAC,4BAA4B,CAAC,CAAC,EAAE;UAC3E;UACAwI,aAAa,CAAC,CAAC,CAACjI,iBAAiB,CAAC,0BAA0B,CAAC6H,aAAa,CAAC;QAC/E,CAAC,MACI;UACD;UACArC,WAAW,CAACvF,SAAS,CAAC,mBAAmB,CAAC4H,aAAa,CAAC;QAC5D;MACJ,CAAC;MACDjB,KAAK,CAACxH,0BAA0B,CAAC,mCAAmC,GAAG6I,aAAa;MACpFrB,KAAK,CAAC1H,mBAAmB,CAAC,2BAA2B,GAAG,YAAY;QAChE,IAAI,CAACuG,oBAAoB,EAAE;UACvB;UACAA,oBAAoB,GAAGV,+BAA+B,CAAC,CAAC;UACxD;UACA6B,KAAK,CAACrD,sBAAsB,CAAC,GAAGkC,oBAAoB;QACxD;QACA,OAAOA,oBAAoB;MAC/B,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;MACYmB,KAAK,CAAC7H,0BAA0B,CAAC,sCAAsC,GAAG,UAAUiG,QAAQ,EAAE;QAC1F,IAAIS,oBAAoB,EAAE;UACtBA,oBAAoB,CAAC1G,0BAA0B,CAAC,sCAAsC,CAACiG,QAAQ,CAAC;QACpG;MACJ,CAAC;MACD;AACZ;AACA;AACA;MACY4B,KAAK,CAAC1G,0BAA0B,CAAC,yCAAyC,GAAG,UAAU8E,QAAQ,EAAE;QAC7F,IAAIS,oBAAoB,EAAE;UACtBA,oBAAoB,CAACvF,0BAA0B,CAAC,yCAAyC,CAAC8E,QAAQ,CAAC;QACvG;MACJ,CAAC;MACD4B,KAAK,CAACsB,YAAY,GAAG,YAAY;QAC7B,IAAI,CAACtC,cAAc,EAAE;UACjBA,cAAc,GAAGhF,eAAe,CAAC0E,OAAO,EAAEsB,KAAK,CAACjH,WAAW,CAAC,qBAAqB,CAAC;QACtF;QACA,OAAOiG,cAAc;MACzB,CAAC;MACDgB,KAAK,CAACuB,YAAY,GAAG,UAAUC,SAAS,EAAE;QACtCxC,cAAc,GAAGwC,SAAS;MAC9B,CAAC;MACDxB,KAAK,CAACpE,gBAAgB,CAAC,yBAAyB,GAAG,YAAY;QAC3D,IAAI,CAACkD,YAAY,IAAI,CAACC,eAAe,EAAE;UACnC,IAAItE,WAAW,CAACiE,OAAO,CAAC+C,aAAa,CAAC,EAAE;YACpC,IAAIC,aAAa,GAAGjH,WAAW,CAACiE,OAAO,CAACrD,mBAAmB,CAAC,4BAA4B,CAAC;YACzF,IAAIV,UAAU,CAAC+G,aAAa,CAAC,EAAE;cAC3B3C,eAAe,GAAG2C,aAAa,CAAC1B,KAAK,EAAEA,KAAK,CAAC1H,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACnG;UACJ;QACJ;QACA,OAAOwG,YAAY,IAAIC,eAAe,IAAIhD,aAAa,CAAC,CAAC;MAC7D,CAAC;MACDiE,KAAK,CAAC2B,UAAU,GAAG,UAAUC,OAAO,EAAE;QAClC9C,YAAY,GAAG8C,OAAO;MAC1B,CAAC;MACD5B,KAAK,CAAC6B,QAAQ,GAAG,YAAY;QACzB,OAAOjD,WAAW,CAAC9F,WAAW,CAAC,qBAAqB;MACxD,CAAC;MACDkH,KAAK,CAACW,YAAY,GAAG,YAAY;QAC7B,IAAIhC,cAAc,IAAIC,WAAW,CAAC9F,WAAW,CAAC,qBAAqB,GAAG,CAAC,EAAE;UACrE,IAAIgJ,UAAU,GAAGlD,WAAW;UAC5BA,WAAW,GAAG,EAAE;UAChBrE,UAAU,CAACuH,UAAU,EAAE,UAAUC,KAAK,EAAE;YACpCV,aAAa,CAAC,CAAC,CAACjI,iBAAiB,CAAC,0BAA0B,CAAC2I,KAAK,CAAC;UACvE,CAAC,CAAC;QACN;MACJ,CAAC;MACD/B,KAAK,CAACgC,gBAAgB,GAAG,UAAUC,SAAS,EAAE;QAC1CxC,sBAAsB,GAAGwC,SAAS,IAAI,IAAI;QAC1ClC,2BAA2B,GAAG,KAAK;QACnC,IAAID,kBAAkB,EAAE;UACpBoC,aAAa,CAACpC,kBAAkB,CAAC;UACjCA,kBAAkB,GAAG,IAAI;QAC7B;QACA,OAAOqC,sBAAsB,CAAC,IAAI,CAAC;MACvC,CAAC;MACD,SAASA,sBAAsBA,CAACC,WAAW,EAAE;QACzC,IAAI,CAACtC,kBAAkB,IAAI,CAACC,2BAA2B,EAAE;UACrD,IAAIsC,WAAW,GAAGD,WAAW,IAAKpC,KAAK,CAACjH,WAAW,CAAC,qBAAqB,IAAIiH,KAAK,CAACjH,WAAW,CAAC,qBAAqB,CAACgI,KAAK,CAACjI,WAAW,CAAC,qBAAqB,GAAG,CAAE;UACjK,IAAIuJ,WAAW,EAAE;YACb,IAAIC,QAAQ,GAAG7H,WAAW,CAACiE,OAAO,CAAC6D,qBAAqB,CAAC;YACzD,IAAI,CAACD,QAAQ,IAAI,EAAEA,QAAQ,GAAG,CAAC,CAAC,EAAE;cAC9BA,QAAQ,GAAG,KAAK;YACpB;YACA;YACAxC,kBAAkB,GAAG0C,WAAW,CAAC,YAAY;cACzCN,aAAa,CAACpC,kBAAkB,CAAC;cACjCA,kBAAkB,GAAG,CAAC;cACtB2C,kBAAkB,CAAC,CAAC;YACxB,CAAC,EAAEH,QAAQ,CAAC;UAChB;QACJ;QACA,OAAOxC,kBAAkB;MAC7B;MACAE,KAAK,CAACvG,0BAA0B,CAAC,sCAAsC,GAAG,YAAY;QAClFsG,2BAA2B,GAAG,IAAI;QAClC,IAAID,kBAAkB,EAAE;UACpBoC,aAAa,CAACpC,kBAAkB,CAAC;UACjCA,kBAAkB,GAAG,CAAC;UACtB2C,kBAAkB,CAAC,CAAC;QACxB;MACJ,CAAC;MACD;MACAzH,cAAc,CAACgF,KAAK,EAAE,YAAY;QAAE,OAAOR,2BAA2B;MAAE,CAAC,EAAE,CAAC,yBAAyB,CAAC,CAAC;MACvGQ,KAAK,CAAC0C,MAAM,GAAG,UAAUlE,OAAO,EAAEmE,cAAc,EAAEC,SAAS,EAAE;QACzD,IAAIrF,EAAE;QACN,IAAIiB,OAAO,KAAK,KAAK,CAAC,EAAE;UAAEA,OAAO,GAAG,IAAI;QAAE;QAC1C,IAAI,CAACG,cAAc,EAAE;UACjB;UACA1D,UAAU,CAAC4B,oBAAoB,CAAC;QACpC;QACA;QACA,IAAI0C,YAAY,EAAE;UACd;UACAtE,UAAU,CAAC2B,oBAAoB,CAAC;QACpC;QACA,IAAIiG,WAAW,IAAItF,EAAE,GAAG;UAChBe,MAAM,EAAE,EAAE,CAAC;QACf,CAAC,EACDf,EAAE,CAAC3E,aAAa,CAAC,mBAAmB,GAAG4F,OAAO,EAC9CjB,EAAE,CAACuF,aAAa,GAAG,KAAK,EACxBvF,EAAE,CAAC;QACP,IAAIwF,gBAAgB,GAAG9G,mCAAmC,CAAC+G,eAAe,CAAC,CAAC,EAAEhD,KAAK,CAAC;QACpF+C,gBAAgB,CAAC5J,gBAAgB,CAAC,yBAAyB,CAAC,YAAY;UACpE8G,aAAa,CAAC,CAAC;UACf0C,cAAc,IAAIA,cAAc,CAACE,WAAW,CAAC;QACjD,CAAC,EAAE7C,KAAK,CAAC;QACT,SAASiD,SAASA,CAACH,aAAa,EAAE;UAC9BD,WAAW,CAACC,aAAa,GAAGA,aAAa;UACzCvD,YAAY,GAAG,IAAI;UACnB;UACAI,eAAe,CAACuD,GAAG,CAACH,gBAAgB,EAAEF,WAAW,CAAC;UAClD;UACA7C,KAAK,CAACvG,0BAA0B,CAAC,sCAAsC,CAAC,CAAC;UACzE;UACAsJ,gBAAgB,CAAC3J,iBAAiB,CAAC,0BAA0B,CAACyJ,WAAW,CAAC;QAC9E;QACAJ,kBAAkB,CAAC,CAAC;QACpB,IAAI,CAACU,cAAc,CAAC3E,OAAO,EAAEyE,SAAS,EAAE,CAAC,CAAC,mCAAmCL,SAAS,CAAC,EAAE;UACrFK,SAAS,CAAC,KAAK,CAAC;QACpB;MACJ,CAAC;MACDjD,KAAK,CAACzH,eAAe,CAAC,wBAAwB,GAAG6K,UAAU;MAC3DpD,KAAK,CAACqD,SAAS,GAAG,UAAUnF,MAAM,EAAEoF,eAAe,EAAE9E,OAAO,EAAE+E,KAAK,EAAE;QACjE,IAAI,CAACrF,MAAM,EAAE;UACTqF,KAAK,IAAIA,KAAK,CAAC,KAAK,CAAC;UACrBC,gBAAgB,CAAC9G,kBAAkB,CAAC;UACpC;QACJ;QACA,IAAI+G,cAAc,GAAGL,UAAU,CAAClF,MAAM,CAACzF,eAAe,CAAC,yBAAyB,CAAC;QACjF,IAAIgL,cAAc,IAAI,CAACH,eAAe,EAAE;UACpCC,KAAK,IAAIA,KAAK,CAAC,KAAK,CAAC;UACrBC,gBAAgB,CAAC,UAAU,GAAGtF,MAAM,CAACzF,eAAe,CAAC,yBAAyB,GAAG,sBAAsB,CAAC;UACxG;QACJ;QACA,IAAIiL,WAAW,GAAG;UACdpF,MAAM,EAAE,EAAE,CAAC;QACf,CAAC;QACD,SAASqF,UAAUA,CAACC,OAAO,EAAE;UACzB1E,iBAAiB,CAAC7F,SAAS,CAAC,mBAAmB,CAAC6E,MAAM,CAAC;UACvDwF,WAAW,CAACG,KAAK,GAAG,CAAC3F,MAAM,CAAC;UAC5B;UACAwC,gBAAgB,CAACgD,WAAW,CAAC;UAC7BH,KAAK,IAAIA,KAAK,CAAC,IAAI,CAAC;QACxB;QACA,IAAIE,cAAc,EAAE;UAChB,IAAIK,gBAAgB,GAAG,CAACL,cAAc,CAACvF,MAAM,CAAC;UAC9C,IAAI2E,WAAW,GAAG;YACdvE,MAAM,EAAE,CAAC,CAAC;YACVE,OAAO,EAAE,CAAC,CAACA;UACf,CAAC;UACDuF,cAAc,CAACD,gBAAgB,EAAEjB,WAAW,EAAE,UAAUe,OAAO,EAAE;YAC7D,IAAI,CAACA,OAAO,EAAE;cACV;cACAL,KAAK,IAAIA,KAAK,CAAC,KAAK,CAAC;YACzB,CAAC,MACI;cACDG,WAAW,CAACE,OAAO,GAAGE,gBAAgB;cACtCJ,WAAW,CAACpF,MAAM,IAAI,EAAE,CAAC;cACzBqF,UAAU,CAAC,IAAI,CAAC;YACpB;UACJ,CAAC,CAAC;QACN,CAAC,MACI;UACDA,UAAU,CAAC,KAAK,CAAC;QACrB;MACJ,CAAC;MACD3D,KAAK,CAACgE,YAAY,GAAG,YAAY;QAC7B,OAAOtE,aAAa;MACxB,CAAC;MACDM,KAAK,CAAC3H,UAAU,CAAC,oBAAoB,GAAG8K,cAAc;MACtDnD,KAAK,CAACiE,WAAW,GAAG,UAAUC,SAAS,EAAE;QACrC,IAAI,CAACrE,SAAS,EAAE;UACZA,SAAS,GAAGxD,6BAA6B,CAAC,CAAC;QAC/C;QACA,OAAOwD,SAAS;MACpB,CAAC;MACDG,KAAK,CAACmE,WAAW,GAAG,UAAUC,QAAQ,EAAE;QACpCvE,SAAS,GAAGuE,QAAQ,IAAI,IAAI;MAChC,CAAC;MACD;MACArJ,eAAe,CAACiF,KAAK,EAAE,aAAa,EAAE,YAAY;QAAE,OAAOL,eAAe;MAAE,CAAC,EAAE,KAAK,CAAC;MACrF,SAASM,aAAaA,CAAA,EAAG;QACrBtB,cAAc,GAAG,KAAK;QACtB;QACAD,OAAO,GAAG7D,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEiC,iBAAiB,CAAC;QAChDkD,KAAK,CAAC5H,WAAW,CAAC,qBAAqB,GAAGsG,OAAO;QACjDsB,KAAK,CAACjH,WAAW,CAAC,qBAAqB,GAAG,IAAIoB,gBAAgB,CAACuE,OAAO,CAAC;QACvEsB,KAAK,CAACpG,gBAAgB,CAAC,0BAA0B,GAAG,EAAE;QACtD4F,2BAA2B,GAAG,IAAIhD,0BAA0B,CAAC,CAAC;QAC9DoC,WAAW,GAAG,EAAE;QAChBC,oBAAoB,GAAG,IAAI;QAC3BC,YAAY,GAAG,IAAI;QACnBC,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAG,IAAI;QACrBC,YAAY,GAAG,IAAI;QACnBE,eAAe,GAAG,IAAI;QACtBD,iBAAiB,GAAG,EAAE;QACtBE,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAG,IAAI;QACrBC,aAAa,GAAG,IAAI;QACpBC,YAAY,GAAG,KAAK;QACpBE,sBAAsB,GAAG,IAAI;QAC7BC,aAAa,GAAGzF,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;QACzD0F,eAAe,GAAGlD,4BAA4B,CAAC,CAAC;QAChDoD,SAAS,GAAG,IAAI;MACpB;MACA,SAASwB,aAAaA,CAAA,EAAG;QACrB,IAAIgD,MAAM,GAAGrI,6BAA6B,CAACgH,eAAe,CAAC,CAAC,EAAEtE,OAAO,EAAEsB,KAAK,CAAC;QAC7EqE,MAAM,CAAClL,gBAAgB,CAAC,yBAAyB,CAACgJ,sBAAsB,CAAC;QACzE,OAAOkC,MAAM;MACjB;MACA;MACA,SAAS3D,gBAAgBA,CAACgD,WAAW,EAAE;QACnC;QACA,IAAIY,aAAa,GAAGnH,mBAAmB,CAAC6C,KAAK,CAACjH,WAAW,CAAC,qBAAqB,EAAEc,yBAAyB,EAAEqF,iBAAiB,CAAC;QAC9HC,eAAe,GAAGmF,aAAa,CAAClJ,QAAQ,CAAC,mBAAmB;QAC5D6D,YAAY,GAAG,IAAI;QACnB;QACA,IAAI3B,aAAa,GAAGgH,aAAa,CAACzG,GAAG;QACrC;QACAyB,aAAa,GAAGxE,SAAS,CAACf,mBAAmB,CAACsF,cAAc,EAAE/B,aAAa,EAAE0C,KAAK,CAAC,CAAC;QACpF,IAAIZ,eAAe,EAAE;UACjB;UACA;UACA;UACA;UACA,IAAImF,GAAG,GAAG/J,UAAU,CAAC8C,aAAa,EAAE8B,eAAe,CAAC;UACpD,IAAImF,GAAG,KAAK,CAAC,CAAC,EAAE;YACZjH,aAAa,CAAC9D,WAAW,CAAC,qBAAqB,CAAC+K,GAAG,EAAE,CAAC,CAAC;UAC3D;UACAA,GAAG,GAAG/J,UAAU,CAAC2E,eAAe,EAAEC,eAAe,CAAC;UAClD,IAAImF,GAAG,KAAK,CAAC,CAAC,EAAE;YACZpF,eAAe,CAAC3F,WAAW,CAAC,qBAAqB,CAAC+K,GAAG,EAAE,CAAC,CAAC;UAC7D;UACAnF,eAAe,CAACoF,SAAS,CAAClF,aAAa,CAAC;QAC5C,CAAC,MACI;UACDF,eAAe,GAAGtF,6BAA6B,CAACwF,aAAa,EAAEU,KAAK,CAAC;QACzE;QACA;QACA1C,aAAa,CAACjE,SAAS,CAAC,mBAAmB,CAAC+F,eAAe,CAAC;QAC5DD,eAAe,CAAC9F,SAAS,CAAC,mBAAmB,CAAC+F,eAAe,CAAC;QAC9D;QACAY,KAAK,CAACpG,gBAAgB,CAAC,0BAA0B,GAAG2C,WAAW,CAACe,aAAa,CAAC;QAC9E;QACA8B,eAAe,CAAC1G,eAAe,CAAC,yBAAyB,CAACgG,OAAO,EAAEsB,KAAK,EAAE1C,aAAa,CAAC;QACxF,IAAImH,OAAO,GAAGpD,aAAa,CAAC,CAAC;QAC7B/E,iBAAiB,CAACmI,OAAO,EAAEnH,aAAa,CAAC;QACzC;QACA0C,KAAK,CAACpG,gBAAgB,CAAC,0BAA0B,GAAGkB,SAAS,CAACyB,WAAW,CAAC4C,eAAe,IAAI,EAAE,CAAC,CAAC,CAACuF,KAAK,CAAC,CAAC;QACzG,IAAIhB,WAAW,EAAE;UACbiB,SAAS,CAACjB,WAAW,CAAC;QAC1B;MACJ;MACA,SAASN,UAAUA,CAACwB,gBAAgB,EAAE;QAClC,IAAIrH,EAAE;QACN,IAAIsH,MAAM,GAAG,IAAI;QACjB,IAAI9G,SAAS,GAAG,IAAI;QACpBxD,UAAU,CAACyF,KAAK,CAACpG,gBAAgB,CAAC,0BAA0B,EAAE,UAAU8D,GAAG,EAAE;UACzE,IAAIA,GAAG,CAACjF,eAAe,CAAC,yBAAyB,KAAKmM,gBAAgB,IAAIlH,GAAG,KAAK0B,eAAe,IAAI1B,GAAG,KAAK8B,2BAA2B,EAAE;YACtIzB,SAAS,GAAGL,GAAG;YACf,OAAO,CAAC,CAAC;UACb;QACJ,CAAC,CAAC;QACF,IAAI,CAACK,SAAS,IAAIqB,eAAe,EAAE;UAC/B;UACArB,SAAS,GAAGqB,eAAe,CAAC0F,UAAU,CAACF,gBAAgB,CAAC;QAC5D;QACA,IAAI7G,SAAS,EAAE;UACX8G,MAAM,IAAItH,EAAE,GAAG;YACPW,MAAM,EAAEH;UACZ,CAAC,EACDR,EAAE,CAAChE,gBAAgB,CAAC,sBAAsB,GAAG,UAAUwL,OAAO,EAAE;YAC5D3I,eAAe,CAAC2B,SAAS,CAAC,CAACzC,YAAY,CAAC,GAAG,CAACyJ,OAAO;UACvD,CAAC,EACDxH,EAAE,CAACyH,SAAS,GAAG,YAAY;YACvB,IAAIC,WAAW,GAAG7I,eAAe,CAAC2B,SAAS,CAAC;YAC5C,OAAO,CAACkH,WAAW,CAACvL,aAAa,CAAC,uBAAuB,IAAI,CAACuL,WAAW,CAAC3J,YAAY,CAAC;UAC3F,CAAC,EACDiC,EAAE,CAAC2H,MAAM,GAAG,UAAU1G,OAAO,EAAE2G,QAAQ,EAAE;YACrC,IAAI5H,EAAE;YACN,IAAIiB,OAAO,KAAK,KAAK,CAAC,EAAE;cAAEA,OAAO,GAAG,IAAI;YAAE;YAC1C,IAAI4G,eAAe,GAAG,CAACrH,SAAS,CAAC;YACjC,IAAI8E,WAAW,IAAItF,EAAE,GAAG;cAChBe,MAAM,EAAE,CAAC,CAAC;YACd,CAAC,EACDf,EAAE,CAAC3E,aAAa,CAAC,mBAAmB,GAAG4F,OAAO,EAC9CjB,EAAE,CAAC;YACPwG,cAAc,CAACqB,eAAe,EAAEvC,WAAW,EAAE,UAAUe,OAAO,EAAE;cAC5D,IAAIA,OAAO,EAAE;gBACT;gBACAlD,gBAAgB,CAAC;kBACbpC,MAAM,EAAE,EAAE,CAAC;kBACXsF,OAAO,EAAEwB;gBACb,CAAC,CAAC;cACN;cACAD,QAAQ,IAAIA,QAAQ,CAACvB,OAAO,CAAC;YACjC,CAAC,CAAC;UACN,CAAC,EACDrG,EAAE,CAAC;QACX;QACA,OAAOsH,MAAM;MACjB;MACA,SAAS7B,eAAeA,CAAA,EAAG;QACvB,IAAI,CAAC/D,YAAY,EAAE;UACf;UACA,IAAIkB,UAAU,GAAG,CAAChB,eAAe,IAAI,EAAE,EAAEuF,KAAK,CAAC,CAAC;UAChD;UACA,IAAIlK,UAAU,CAAC2F,UAAU,EAAEX,2BAA2B,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5DW,UAAU,CAAC9G,SAAS,CAAC,mBAAmB,CAACmG,2BAA2B,CAAC;UACzE;UACAP,YAAY,GAAG9C,yBAAyB,CAACI,WAAW,CAAC4D,UAAU,CAAC,EAAEzB,OAAO,EAAEsB,KAAK,CAAC;QACrF;QACA,OAAOf,YAAY;MACvB;MACA,SAAS8E,cAAcA,CAACsB,UAAU,EAAExC,WAAW,EAAEyC,cAAc,EAAE;QAC7D,IAAID,UAAU,IAAIA,UAAU,CAACvM,WAAW,CAAC,qBAAqB,GAAG,CAAC,EAAE;UAChE,IAAIyM,WAAW,GAAGpJ,yBAAyB,CAACkJ,UAAU,EAAE3G,OAAO,EAAEsB,KAAK,CAAC;UACvE,IAAIwF,SAAS,GAAGvJ,mCAAmC,CAACsJ,WAAW,EAAEvF,KAAK,CAAC;UACvEwF,SAAS,CAACrM,gBAAgB,CAAC,yBAAyB,CAAC,YAAY;YAC7D,IAAIyK,OAAO,GAAG,KAAK;YACnB;YACA,IAAI6B,mBAAmB,GAAG,EAAE;YAC5BlL,UAAU,CAAC2E,iBAAiB,EAAE,UAAUhB,MAAM,EAAEqG,GAAG,EAAE;cACjD,IAAI,CAACzG,gBAAgB,CAACI,MAAM,EAAEmH,UAAU,CAAC,EAAE;gBACvCI,mBAAmB,CAACpM,SAAS,CAAC,mBAAmB,CAAC6E,MAAM,CAAC;cAC7D,CAAC,MACI;gBACD0F,OAAO,GAAG,IAAI;cAClB;YACJ,CAAC,CAAC;YACF1E,iBAAiB,GAAGuG,mBAAmB;YACvC;YACA,IAAIC,gBAAgB,GAAG,EAAE;YACzB,IAAIrG,cAAc,EAAE;cAChB9E,UAAU,CAAC8E,cAAc,EAAE,UAAU0B,KAAK,EAAEwD,GAAG,EAAE;gBAC7C,IAAIoB,QAAQ,GAAG,EAAE;gBACjBpL,UAAU,CAACwG,KAAK,EAAE,UAAU6E,OAAO,EAAE;kBACjC,IAAI,CAAC9H,gBAAgB,CAAC8H,OAAO,EAAEP,UAAU,CAAC,EAAE;oBACxCM,QAAQ,CAACtM,SAAS,CAAC,mBAAmB,CAACuM,OAAO,CAAC;kBACnD,CAAC,MACI;oBACDhC,OAAO,GAAG,IAAI;kBAClB;gBACJ,CAAC,CAAC;gBACF8B,gBAAgB,CAACrM,SAAS,CAAC,mBAAmB,CAACsM,QAAQ,CAAC;cAC5D,CAAC,CAAC;cACFtG,cAAc,GAAGqG,gBAAgB;YACrC;YACAJ,cAAc,IAAIA,cAAc,CAAC1B,OAAO,CAAC;YACzCzB,sBAAsB,CAAC,CAAC;UAC5B,CAAC,CAAC;UACFqD,SAAS,CAACpM,iBAAiB,CAAC,0BAA0B,CAACyJ,WAAW,CAAC;QACvE,CAAC,MACI;UACDyC,cAAc,CAAC,KAAK,CAAC;QACzB;MACJ;MACA,SAAS7C,kBAAkBA,CAAA,EAAG;QAC1B,IAAIzC,KAAK,CAACjH,WAAW,CAAC,qBAAqB,IAAIiH,KAAK,CAACjH,WAAW,CAAC,qBAAqB,CAACgI,KAAK,EAAE;UAC1F,IAAIA,KAAK,GAAGf,KAAK,CAACjH,WAAW,CAAC,qBAAqB,CAACgI,KAAK,CAAC2D,KAAK,CAAC,CAAC,CAAC;UAClE1E,KAAK,CAACjH,WAAW,CAAC,qBAAqB,CAACgI,KAAK,CAACjI,WAAW,CAAC,qBAAqB,GAAG,CAAC;UACnFyB,UAAU,CAACwG,KAAK,EAAE,UAAU8E,UAAU,EAAE;YACpC,IAAItI,EAAE;YACN,IAAIuI,IAAI,IAAIvI,EAAE,GAAG,CAAC,CAAC,EACfA,EAAE,CAACrE,SAAS,CAAC,gBAAgB,GAAGuG,sBAAsB,GAAGA,sBAAsB,GAAG,qBAAqB,GAAGoG,UAAU,CAAC5M,eAAe,CAAC,wBAAwB,EAC7JsE,EAAE,CAAC2D,IAAI,GAAGzG,WAAW,CAACiE,OAAO,CAAC/F,wBAAwB,CAAC,iCAAiC,CAAC,EACzF4E,EAAE,CAACwI,IAAI,GAAG7K,WAAW,CAAC,IAAIiG,IAAI,CAAC,CAAC,CAAC,EACjC5D,EAAE,CAACyI,QAAQ,GAAG5L,mBAAmB,CAAC6L,QAAQ,EAC1C1I,EAAE,CAAC2I,QAAQ,GAAG;cAAEC,OAAO,EAAEN,UAAU,CAAC7M,YAAY,CAAC;YAAuB,CAAC,EACzEuE,EAAE,CAAC;YACPyC,KAAK,CAACgB,KAAK,CAAC8E,IAAI,CAAC;UACrB,CAAC,CAAC;QACN;MACJ;MACA,SAAS3C,cAAcA,CAAC3E,OAAO,EAAE4H,QAAQ,EAAE7H,UAAU,EAAEqE,SAAS,EAAE;QAC9D,IAAIxD,eAAe,EAAE;UACjB,OAAOA,eAAe,CAAC/G,UAAU,CAAC,oBAAoB,CAACmG,OAAO,EAAE4H,QAAQ,EAAE7H,UAAU,IAAI,CAAC,CAAC,mCAAmCqE,SAAS,CAAC;QAC3I;QACAwD,QAAQ,IAAIA,QAAQ,CAAC,KAAK,CAAC;QAC3B,OAAO,IAAI;MACf;MACA,SAAS/F,kBAAkBA,CAAA,EAAG;QAC1B,IAAIgG,aAAa,GAAG5L,WAAW,CAACiE,OAAO,CAAC2H,aAAa,CAAC;QACtD,IAAIA,aAAa,KAAK,IAAI,IAAIzG,cAAc,EAAE;UAC1C;UACAf,oBAAoB,CAACvF,0BAA0B,CAAC,yCAAyC,CAACsG,cAAc,CAAC;UACzGA,cAAc,GAAG,IAAI;QACzB;QACA,IAAIf,oBAAoB,IAAI,CAACe,cAAc,IAAIyG,aAAa,KAAK,IAAI,EAAE;UACnEzG,cAAc,GAAG1F,gBAAgB,CAACwE,OAAO,CAAC;UAC1CG,oBAAoB,CAAC1G,0BAA0B,CAAC,sCAAsC,CAACyH,cAAc,CAAC;QAC1G;MACJ;MACA,SAASU,gBAAgBA,CAAA,EAAG;QACxB,IAAImB,aAAa,GAAGhH,WAAW,CAACiE,OAAO,CAAC+C,aAAa,CAAC;QACtD,IAAI,CAACA,aAAa,IAAI1C,eAAe,EAAE;UACnC;UACAA,eAAe,GAAG,IAAI;QAC1B;QACA,IAAI0C,aAAa,EAAE;UACf;UACA/G,WAAW,CAACgE,OAAO,EAAErD,mBAAmB,EAAE2B,kBAAkB,CAAC;QACjE;MACJ;MACA,SAASuD,cAAcA,CAAA,EAAG;QACtB,IAAI+F,SAAS,GAAG5L,WAAW,CAACgE,OAAO,EAAE/C,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAC9D2K,SAAS,CAACC,mBAAmB,GAAG1H,oBAAoB;MACxD;MACA,SAAS8F,SAASA,CAACjB,WAAW,EAAE;QAC5B,IAAI8C,SAAS,GAAGtK,mCAAmC,CAAC8G,eAAe,CAAC,CAAC,EAAEhD,KAAK,CAAC;QAC7EwG,SAAS,CAACrN,gBAAgB,CAAC,yBAAyB,CAACgJ,sBAAsB,CAAC;QAC5E,IAAI,CAACnC,KAAK,CAACyG,WAAW,IAAIzG,KAAK,CAACyG,WAAW,CAACD,SAAS,EAAE9C,WAAW,CAAC,KAAK,IAAI,EAAE;UAC1E8C,SAAS,CAACpN,iBAAiB,CAAC,0BAA0B,CAACsK,WAAW,CAAC;QACvE;MACJ;MACA,SAASF,gBAAgBA,CAAC2C,OAAO,EAAE;QAC/B,IAAI/I,MAAM,GAAG4C,KAAK,CAACjH,WAAW,CAAC,qBAAqB;QACpD,IAAIqE,MAAM,EAAE;UACR;UACA/C,cAAc,CAAC+C,MAAM,EAAE,CAAC,CAAC,gCAAgC,EAAE,CAAC,2CAA2C+I,OAAO,CAAC;UAC/GhE,sBAAsB,CAAC,CAAC;QAC5B,CAAC,MACI;UACDlH,UAAU,CAACkL,OAAO,CAAC;QACvB;MACJ;IACJ,CAAC,CAAC;EACN;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI;EACA;EACA;EACA1H,QAAQ,CAACiI,OAAO,GAAC,CAAC;EAElB,OAAOjI,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,SAASA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}