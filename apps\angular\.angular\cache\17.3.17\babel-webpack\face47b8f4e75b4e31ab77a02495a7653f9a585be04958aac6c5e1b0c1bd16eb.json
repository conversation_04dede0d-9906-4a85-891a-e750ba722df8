{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { TextMessageFormat } from \"./TextMessageFormat\";\nimport { isArrayBuffer } from \"./Utils\";\n/** @private */\nexport class HandshakeProtocol {\n  // Handshake request is always JSON\n  writeHandshakeRequest(handshakeRequest) {\n    return TextMessageFormat.write(JSON.stringify(handshakeRequest));\n  }\n  parseHandshakeResponse(data) {\n    let messageData;\n    let remainingData;\n    if (isArrayBuffer(data)) {\n      // Format is binary but still need to read JSON text from handshake response\n      const binaryData = new Uint8Array(data);\n      const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\n      if (separatorIndex === -1) {\n        throw new Error(\"Message is incomplete.\");\n      }\n      // content before separator is handshake response\n      // optional content after is additional messages\n      const responseLength = separatorIndex + 1;\n      messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\n      remainingData = binaryData.byteLength > responseLength ? binaryData.slice(responseLength).buffer : null;\n    } else {\n      const textData = data;\n      const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\n      if (separatorIndex === -1) {\n        throw new Error(\"Message is incomplete.\");\n      }\n      // content before separator is handshake response\n      // optional content after is additional messages\n      const responseLength = separatorIndex + 1;\n      messageData = textData.substring(0, responseLength);\n      remainingData = textData.length > responseLength ? textData.substring(responseLength) : null;\n    }\n    // At this point we should have just the single handshake message\n    const messages = TextMessageFormat.parse(messageData);\n    const response = JSON.parse(messages[0]);\n    if (response.type) {\n      throw new Error(\"Expected a handshake response from the server.\");\n    }\n    const responseMessage = response;\n    // multiple messages could have arrived with handshake\n    // return additional data to be parsed as usual, or null if all parsed\n    return [remainingData, responseMessage];\n  }\n}", "map": {"version": 3, "names": ["TextMessageFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HandshakeProtocol", "writeHandshakeRequest", "handshakeRequest", "write", "JSON", "stringify", "parseHandshakeResponse", "data", "messageData", "remainingData", "binaryData", "Uint8Array", "separatorIndex", "indexOf", "RecordSeparatorCode", "Error", "responseLength", "String", "fromCharCode", "apply", "Array", "prototype", "slice", "call", "byteLength", "buffer", "textData", "RecordSeparator", "substring", "length", "messages", "parse", "response", "type", "responseMessage"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    writeHandshakeRequest(handshakeRequest) {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n    parseHandshakeResponse(data) {\r\n        let messageData;\r\n        let remainingData;\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        }\r\n        else {\r\n            const textData = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage = response;\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,SAAS;AACvC;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC3B;EACAC,qBAAqBA,CAACC,gBAAgB,EAAE;IACpC,OAAOJ,iBAAiB,CAACK,KAAK,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;EACpE;EACAI,sBAAsBA,CAACC,IAAI,EAAE;IACzB,IAAIC,WAAW;IACf,IAAIC,aAAa;IACjB,IAAIV,aAAa,CAACQ,IAAI,CAAC,EAAE;MACrB;MACA,MAAMG,UAAU,GAAG,IAAIC,UAAU,CAACJ,IAAI,CAAC;MACvC,MAAMK,cAAc,GAAGF,UAAU,CAACG,OAAO,CAACf,iBAAiB,CAACgB,mBAAmB,CAAC;MAChF,IAAIF,cAAc,KAAK,CAAC,CAAC,EAAE;QACvB,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC7C;MACA;MACA;MACA,MAAMC,cAAc,GAAGJ,cAAc,GAAG,CAAC;MACzCJ,WAAW,GAAGS,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACb,UAAU,CAACY,KAAK,CAAC,CAAC,EAAEN,cAAc,CAAC,CAAC,CAAC;MAC9GP,aAAa,GAAIC,UAAU,CAACc,UAAU,GAAGR,cAAc,GAAIN,UAAU,CAACY,KAAK,CAACN,cAAc,CAAC,CAACS,MAAM,GAAG,IAAI;IAC7G,CAAC,MACI;MACD,MAAMC,QAAQ,GAAGnB,IAAI;MACrB,MAAMK,cAAc,GAAGc,QAAQ,CAACb,OAAO,CAACf,iBAAiB,CAAC6B,eAAe,CAAC;MAC1E,IAAIf,cAAc,KAAK,CAAC,CAAC,EAAE;QACvB,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC7C;MACA;MACA;MACA,MAAMC,cAAc,GAAGJ,cAAc,GAAG,CAAC;MACzCJ,WAAW,GAAGkB,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAEZ,cAAc,CAAC;MACnDP,aAAa,GAAIiB,QAAQ,CAACG,MAAM,GAAGb,cAAc,GAAIU,QAAQ,CAACE,SAAS,CAACZ,cAAc,CAAC,GAAG,IAAI;IAClG;IACA;IACA,MAAMc,QAAQ,GAAGhC,iBAAiB,CAACiC,KAAK,CAACvB,WAAW,CAAC;IACrD,MAAMwB,QAAQ,GAAG5B,IAAI,CAAC2B,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxC,IAAIE,QAAQ,CAACC,IAAI,EAAE;MACf,MAAM,IAAIlB,KAAK,CAAC,gDAAgD,CAAC;IACrE;IACA,MAAMmB,eAAe,GAAGF,QAAQ;IAChC;IACA;IACA,OAAO,CAACvB,aAAa,EAAEyB,eAAe,CAAC;EAC3C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}