{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { differentLocales } from '@abp/ng.core';\nimport { isDevMode } from '@angular/core';\nlet localeMap = {};\nfunction registerLocale({\n  cultureNameLocaleFileMap = {},\n  errorHandlerFn = defaultLocalErrorHandlerFn\n} = {}) {\n  return locale => {\n    localeMap = {\n      ...differentLocales,\n      ...cultureNameLocaleFileMap\n    };\n    const localePath = `/locales/${localeMap[locale] || locale}`;\n    return new Promise((resolve, reject) => {\n      return import(/* webpackMode: \"lazy-once\" */\n      /* webpackChunkName: \"locales\"*/\n      /* webpackInclude: /[/\\\\](ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-<PERSON>|zh-Hant)\\.(mjs|js)$/ */\n      /* webpackExclude: /[/\\\\]global|extra/ */\n      `@angular/common${localePath}`).then(val => {\n        let module = val;\n        while (module.default) {\n          module = module.default;\n        }\n        resolve({\n          default: module\n        });\n      }).catch(error => {\n        errorHandlerFn({\n          resolve,\n          reject,\n          error,\n          locale\n        });\n      });\n    });\n  };\n}\nconst extraLocales = {};\nfunction storeLocaleData(data, localeId) {\n  extraLocales[localeId] = data;\n}\nfunction defaultLocalErrorHandlerFn(_x) {\n  return _defaultLocalErrorHandlerFn.apply(this, arguments);\n}\n/**\n * Generated bundle index. Do not edit.\n */\nfunction _defaultLocalErrorHandlerFn() {\n  _defaultLocalErrorHandlerFn = _asyncToGenerator(function* ({\n    locale,\n    resolve\n  }) {\n    if (extraLocales[locale]) {\n      resolve({\n        default: extraLocales[localeMap[locale] || locale]\n      });\n      return;\n    }\n    if (isDevMode()) {\n      console.error(`Cannot find the ${locale} locale file. You can check how can add new culture at https://docs.abp.io/en/abp/latest/UI/Angular/Localization#adding-a-new-culture`);\n    }\n    resolve();\n  });\n  return _defaultLocalErrorHandlerFn.apply(this, arguments);\n}\nexport { defaultLocalErrorHandlerFn, registerLocale, storeLocaleData };", "map": {"version": 3, "names": ["differentLocales", "isDevMode", "localeMap", "registerLocale", "cultureNameLocaleFileMap", "errorHandlerFn", "defaultLocalErrorHandlerFn", "locale", "localePath", "Promise", "resolve", "reject", "then", "val", "module", "default", "catch", "error", "extraLocales", "storeLocaleData", "data", "localeId", "_x", "_defaultLocalErrorHandlerFn", "apply", "arguments", "_asyncToGenerator", "console"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@abp/ng.core/fesm2022/abp-ng.core-locale.mjs"], "sourcesContent": ["import { differentLocales } from '@abp/ng.core';\nimport { isDevMode } from '@angular/core';\n\nlet localeMap = {};\nfunction registerLocale({ cultureNameLocaleFileMap = {}, errorHandlerFn = defaultLocalErrorHandlerFn, } = {}) {\n    return (locale) => {\n        localeMap = { ...differentLocales, ...cultureNameLocaleFileMap };\n        const localePath = `/locales/${localeMap[locale] || locale}`;\n        return new Promise((resolve, reject) => {\n            return import(\n            /* webpackMode: \"lazy-once\" */\n            /* webpackChunkName: \"locales\"*/\n            /* webpackInclude: /[/\\\\](ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant)\\.(mjs|js)$/ */\n            /* webpackExclude: /[/\\\\]global|extra/ */\n            `@angular/common${localePath}`)\n                .then(val => {\n                let module = val;\n                while (module.default) {\n                    module = module.default;\n                }\n                resolve({ default: module });\n            })\n                .catch(error => {\n                errorHandlerFn({\n                    resolve,\n                    reject,\n                    error,\n                    locale,\n                });\n            });\n        });\n    };\n}\nconst extraLocales = {};\nfunction storeLocaleData(data, localeId) {\n    extraLocales[localeId] = data;\n}\nasync function defaultLocalErrorHandlerFn({ locale, resolve }) {\n    if (extraLocales[locale]) {\n        resolve({ default: extraLocales[localeMap[locale] || locale] });\n        return;\n    }\n    if (isDevMode()) {\n        console.error(`Cannot find the ${locale} locale file. You can check how can add new culture at https://docs.abp.io/en/abp/latest/UI/Angular/Localization#adding-a-new-culture`);\n    }\n    resolve();\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { defaultLocalErrorHandlerFn, registerLocale, storeLocaleData };\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,cAAc;AAC/C,SAASC,SAAS,QAAQ,eAAe;AAEzC,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,SAASC,cAAcA,CAAC;EAAEC,wBAAwB,GAAG,CAAC,CAAC;EAAEC,cAAc,GAAGC;AAA4B,CAAC,GAAG,CAAC,CAAC,EAAE;EAC1G,OAAQC,MAAM,IAAK;IACfL,SAAS,GAAG;MAAE,GAAGF,gBAAgB;MAAE,GAAGI;IAAyB,CAAC;IAChE,MAAMI,UAAU,GAAG,YAAYN,SAAS,CAACK,MAAM,CAAC,IAAIA,MAAM,EAAE;IAC5D,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,OAAO,MAAM,CACb;MACA;MACA;MACA;MACA,kBAAkBH,UAAU,EAAE,CAAC,CAC1BI,IAAI,CAACC,GAAG,IAAI;QACb,IAAIC,MAAM,GAAGD,GAAG;QAChB,OAAOC,MAAM,CAACC,OAAO,EAAE;UACnBD,MAAM,GAAGA,MAAM,CAACC,OAAO;QAC3B;QACAL,OAAO,CAAC;UAAEK,OAAO,EAAED;QAAO,CAAC,CAAC;MAChC,CAAC,CAAC,CACGE,KAAK,CAACC,KAAK,IAAI;QAChBZ,cAAc,CAAC;UACXK,OAAO;UACPC,MAAM;UACNM,KAAK;UACLV;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;AACL;AACA,MAAMW,YAAY,GAAG,CAAC,CAAC;AACvB,SAASC,eAAeA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACrCH,YAAY,CAACG,QAAQ,CAAC,GAAGD,IAAI;AACjC;AAAC,SACcd,0BAA0BA,CAAAgB,EAAA;EAAA,OAAAC,2BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAWzC;AACA;AACA;AAFA,SAAAF,4BAAA;EAAAA,2BAAA,GAAAG,iBAAA,CAXA,WAA0C;IAAEnB,MAAM;IAAEG;EAAQ,CAAC,EAAE;IAC3D,IAAIQ,YAAY,CAACX,MAAM,CAAC,EAAE;MACtBG,OAAO,CAAC;QAAEK,OAAO,EAAEG,YAAY,CAAChB,SAAS,CAACK,MAAM,CAAC,IAAIA,MAAM;MAAE,CAAC,CAAC;MAC/D;IACJ;IACA,IAAIN,SAAS,CAAC,CAAC,EAAE;MACb0B,OAAO,CAACV,KAAK,CAAC,mBAAmBV,MAAM,uIAAuI,CAAC;IACnL;IACAG,OAAO,CAAC,CAAC;EACb,CAAC;EAAA,OAAAa,2BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAMD,SAASnB,0BAA0B,EAAEH,cAAc,EAAEgB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}