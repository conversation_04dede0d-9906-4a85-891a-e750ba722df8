{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"sah\", [[\"ЭИ\", \"ЭК\"], u, u], u, [[\"Б\", \"Б\", \"О\", \"С\", \"Ч\", \"Б\", \"С\"], [\"бс\", \"бн\", \"оп\", \"сэ\", \"чп\", \"бэ\", \"сб\"], [\"баскыһыанньа\", \"бэнидиэнньик\", \"оптуорунньук\", \"сэрэдэ\", \"чэппиэр\", \"Бээтиҥсэ\", \"субуота\"], [\"бс\", \"бн\", \"оп\", \"сэ\", \"чп\", \"бэ\", \"сб\"]], u, [[\"Т\", \"О\", \"К\", \"М\", \"Ы\", \"Б\", \"О\", \"А\", \"Б\", \"А\", \"С\", \"А\"], [\"Тохс\", \"Олун\", \"Клн\", \"Мсу\", \"Ыам\", \"Бэс\", \"Отй\", \"Атр\", \"Блҕ\", \"Алт\", \"Сэт\", \"Ахс\"], [\"Тохсунньу\", \"Олунньу\", \"Кулун тутар\", \"Муус устар\", \"Ыам ыйын\", \"Бэс ыйын\", \"От ыйын\", \"Атырдьых ыйын\", \"Балаҕан ыйын\", \"Алтынньы\", \"Сэтинньи\", \"ахсынньы\"]], [[\"Т\", \"О\", \"К\", \"М\", \"Ы\", \"Б\", \"О\", \"А\", \"Б\", \"А\", \"С\", \"А\"], [\"Тохс\", \"Олун\", \"Клн\", \"Мсу\", \"Ыам\", \"Бэс\", \"Отй\", \"Атр\", \"Блҕ\", \"Алт\", \"Сэт\", \"Ахс\"], [\"тохсунньу\", \"олунньу\", \"кулун тутар\", \"муус устар\", \"ыам ыйа\", \"бэс ыйа\", \"от ыйа\", \"атырдьых ыйа\", \"балаҕан ыйа\", \"алтынньы\", \"сэтинньи\", \"ахсынньы\"]], [[\"б. э. и.\", \"б. э\"], u, u], 1, [6, 0], [\"yy/M/d\", \"y, MMM d\", \"y, MMMM d\", \"y 'сыл' MMMM d 'күнэ', EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"чыыһыла буотах\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"RUB\", \"₽\", \"Арассыыйа солкуобайа\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"RUB\": [\"₽\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/sah.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"sah\", [[\"ЭИ\", \"ЭК\"], u, u], u, [[\"Б\", \"Б\", \"О\", \"С\", \"Ч\", \"Б\", \"С\"], [\"бс\", \"бн\", \"оп\", \"сэ\", \"чп\", \"бэ\", \"сб\"], [\"баскыһыанньа\", \"бэнидиэнньик\", \"оптуорунньук\", \"сэрэдэ\", \"чэппиэр\", \"Бээтиҥсэ\", \"субуота\"], [\"бс\", \"бн\", \"оп\", \"сэ\", \"чп\", \"бэ\", \"сб\"]], u, [[\"Т\", \"О\", \"К\", \"М\", \"Ы\", \"Б\", \"О\", \"А\", \"Б\", \"А\", \"С\", \"А\"], [\"Тохс\", \"Олун\", \"Клн\", \"Мсу\", \"Ыам\", \"Бэс\", \"Отй\", \"Атр\", \"Блҕ\", \"Алт\", \"Сэт\", \"Ахс\"], [\"Тохсунньу\", \"Олунньу\", \"Кулун тутар\", \"Муус устар\", \"Ыам ыйын\", \"Бэс ыйын\", \"От ыйын\", \"Атырдьых ыйын\", \"Балаҕан ыйын\", \"Алтынньы\", \"Сэтинньи\", \"ахсынньы\"]], [[\"Т\", \"О\", \"К\", \"М\", \"Ы\", \"Б\", \"О\", \"А\", \"Б\", \"А\", \"С\", \"А\"], [\"Тохс\", \"Олун\", \"Клн\", \"Мсу\", \"Ыам\", \"Бэс\", \"Отй\", \"Атр\", \"Блҕ\", \"Алт\", \"Сэт\", \"Ахс\"], [\"тохсунньу\", \"олунньу\", \"кулун тутар\", \"муус устар\", \"ыам ыйа\", \"бэс ыйа\", \"от ыйа\", \"атырдьых ыйа\", \"балаҕан ыйа\", \"алтынньы\", \"сэтинньи\", \"ахсынньы\"]], [[\"б. э. и.\", \"б. э\"], u, u], 1, [6, 0], [\"yy/M/d\", \"y, MMM d\", \"y, MMMM d\", \"y 'сыл' MMMM d 'күнэ', EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"чыыһыла буотах\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"RUB\", \"₽\", \"Арассыыйа солкуобайа\", { \"JPY\": [\"JP¥\", \"¥\"], \"RUB\": [\"₽\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,6BAA6B,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,gBAAgB,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,sBAAsB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}