{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = startOfUTCWeek;\nvar _index = _interopRequireDefault(require(\"../../toDate/index.js\"));\nvar _index2 = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\nvar _index3 = _interopRequireDefault(require(\"../toInteger/index.js\"));\nvar _index4 = require(\"../defaultOptions/index.js\");\nfunction startOfUTCWeek(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  (0, _index2.default)(1, arguments);\n  var defaultOptions = (0, _index4.getDefaultOptions)();\n  var weekStartsOn = (0, _index3.default)((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0);\n\n  // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = (0, _index.default)(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}\nmodule.exports = exports.default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "startOfUTCWeek", "_index", "_index2", "_index3", "_index4", "dirtyDate", "options", "_ref", "_ref2", "_ref3", "_options$weekStartsOn", "_options$locale", "_options$locale$optio", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "defaultOptions", "getDefaultOptions", "weekStartsOn", "locale", "RangeError", "date", "day", "getUTCDay", "diff", "setUTCDate", "getUTCDate", "setUTCHours", "module"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/_lib/startOfUTCWeek/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = startOfUTCWeek;\nvar _index = _interopRequireDefault(require(\"../../toDate/index.js\"));\nvar _index2 = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\nvar _index3 = _interopRequireDefault(require(\"../toInteger/index.js\"));\nvar _index4 = require(\"../defaultOptions/index.js\");\nfunction startOfUTCWeek(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  (0, _index2.default)(1, arguments);\n  var defaultOptions = (0, _index4.getDefaultOptions)();\n  var weekStartsOn = (0, _index3.default)((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0);\n\n  // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = (0, _index.default)(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,cAAc;AAChC,IAAIC,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACrE,IAAIQ,OAAO,GAAGT,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACzE,IAAIS,OAAO,GAAGV,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACtE,IAAIU,OAAO,GAAGV,OAAO,CAAC,4BAA4B,CAAC;AACnD,SAASM,cAAcA,CAACK,SAAS,EAAEC,OAAO,EAAE;EAC1C,IAAIC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EACpI,CAAC,CAAC,EAAEZ,OAAO,CAACP,OAAO,EAAE,CAAC,EAAEoB,SAAS,CAAC;EAClC,IAAIC,cAAc,GAAG,CAAC,CAAC,EAAEZ,OAAO,CAACa,iBAAiB,EAAE,CAAC;EACrD,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAEf,OAAO,CAACR,OAAO,EAAE,CAACY,IAAI,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,YAAY,MAAM,IAAI,IAAIR,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACK,eAAe,GAAGL,OAAO,CAACa,MAAM,MAAM,IAAI,IAAIR,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACL,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACM,YAAY,MAAM,IAAI,IAAIT,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGO,cAAc,CAACE,YAAY,MAAM,IAAI,IAAIV,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGG,cAAc,CAACG,MAAM,MAAM,IAAI,IAAIN,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACP,OAAO,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACI,YAAY,MAAM,IAAI,IAAIX,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;;EAEh5B;EACA,IAAI,EAAEW,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAI,CAAC,CAAC,EAAE;IAC7C,MAAM,IAAIE,UAAU,CAAC,kDAAkD,CAAC;EAC1E;EACA,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAEpB,MAAM,CAACN,OAAO,EAAEU,SAAS,CAAC;EACzC,IAAIiB,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;EAC1B,IAAIC,IAAI,GAAG,CAACF,GAAG,GAAGJ,YAAY,GAAG,CAAC,GAAG,CAAC,IAAII,GAAG,GAAGJ,YAAY;EAC5DG,IAAI,CAACI,UAAU,CAACJ,IAAI,CAACK,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzCH,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B,OAAON,IAAI;AACb;AACAO,MAAM,CAAC9B,OAAO,GAAGA,OAAO,CAACH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}