{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EsAssesmentListComponent } from './containers/es-assesment-list/es-assesment-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EsAssesmentListComponent\n}];\nexport let EsAssesmentListRoutingModule = /*#__PURE__*/(() => {\n  class EsAssesmentListRoutingModule {\n    static {\n      this.ɵfac = function EsAssesmentListRoutingModule_Factory(t) {\n        return new (t || EsAssesmentListRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: EsAssesmentListRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return EsAssesmentListRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}