{"ast": null, "code": "// Same as fr\nimport formatDistance from \"../fr/_lib/formatDistance/index.js\";\nimport localize from \"../fr/_lib/localize/index.js\";\nimport match from \"../fr/_lib/match/index.js\";\n// Unique for fr-CH\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale (Switzerland).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n * <AUTHOR> [@vanvuongngo]{@link https://github.com/vanvuongngo}\n * <AUTHOR> [@dcbn]{@link https://github.com/dcbn}\n */\nvar locale = {\n  code: 'fr-CH',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "localize", "match", "formatLong", "formatRelative", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/fr-CH/index.js"], "sourcesContent": ["// Same as fr\nimport formatDistance from \"../fr/_lib/formatDistance/index.js\";\nimport localize from \"../fr/_lib/localize/index.js\";\nimport match from \"../fr/_lib/match/index.js\";\n// Unique for fr-CH\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale (Switzerland).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n * <AUTHOR> [@vanvuongngo]{@link https://github.com/vanvuongngo}\n * <AUTHOR> [@dcbn]{@link https://github.com/dcbn}\n */\nvar locale = {\n  code: 'fr-CH',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": "AAAA;AACA,OAAOA,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,KAAK,MAAM,2BAA2B;AAC7C;AACA,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BG,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BH,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZK,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}