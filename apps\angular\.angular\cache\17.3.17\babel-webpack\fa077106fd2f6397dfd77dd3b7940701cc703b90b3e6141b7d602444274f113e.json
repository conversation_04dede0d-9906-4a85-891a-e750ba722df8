{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { SubjectSubscription } from \"./Utils\";\n/** Stream implementation to stream items to the server. */\nexport class Subject {\n  constructor() {\n    this.observers = [];\n  }\n  next(item) {\n    for (const observer of this.observers) {\n      observer.next(item);\n    }\n  }\n  error(err) {\n    for (const observer of this.observers) {\n      if (observer.error) {\n        observer.error(err);\n      }\n    }\n  }\n  complete() {\n    for (const observer of this.observers) {\n      if (observer.complete) {\n        observer.complete();\n      }\n    }\n  }\n  subscribe(observer) {\n    this.observers.push(observer);\n    return new SubjectSubscription(this, observer);\n  }\n}\n//# sourceMappingURL=Subject.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}