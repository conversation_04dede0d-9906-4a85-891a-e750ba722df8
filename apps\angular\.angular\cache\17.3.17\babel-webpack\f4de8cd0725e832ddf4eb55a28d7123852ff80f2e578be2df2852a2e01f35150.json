{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"mgo\", [[\"AM\", \"PM\"], u, u], u, [[\"A1\", \"A2\", \"A3\", \"A4\", \"A5\", \"A6\", \"A7\"], [\"Aneg 1\", \"Aneg 2\", \"Aneg 3\", \"Aneg 4\", \"Aneg 5\", \"Aneg 6\", \"Aneg 7\"], u, [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]], u, [[\"M1\", \"A2\", \"M3\", \"N4\", \"F5\", \"I6\", \"A7\", \"I8\", \"K9\", \"10\", \"11\", \"12\"], [\"mbegtug\", \"imeg àbùb<PERSON>\", \"imeg mbəŋchubi\", \"iməg ngwə̀t\", \"iməg fog\", \"iməg ichiibɔd\", \"iməg àdùmbə̀ŋ\", \"iməg ichika\", \"iməg kud\", \"iməg tèsiʼe\", \"iməg zò\", \"iməg krizmed\"], [\"iməg mbegtug\", \"imeg àbùbì\", \"imeg mbəŋchubi\", \"iməg ngwə̀t\", \"iməg fog\", \"iməg ichiibɔd\", \"iməg àdùmbə̀ŋ\", \"iməg ichika\", \"iməg kud\", \"iməg tèsiʼe\", \"iməg zò\", \"iməg krizmed\"]], u, [[\"BCE\", \"CE\"], u, u], 1, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"EEEE, y MMMM dd\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"XAF\", \"FCFA\", \"shirè\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/mgo.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"mgo\", [[\"AM\", \"PM\"], u, u], u, [[\"A1\", \"A2\", \"A3\", \"A4\", \"A5\", \"A6\", \"A7\"], [\"Aneg 1\", \"Aneg 2\", \"Aneg 3\", \"Aneg 4\", \"Aneg 5\", \"Aneg 6\", \"Aneg 7\"], u, [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]], u, [[\"M1\", \"A2\", \"M3\", \"N4\", \"F5\", \"I6\", \"A7\", \"I8\", \"K9\", \"10\", \"11\", \"12\"], [\"mbegtug\", \"imeg àbùb<PERSON>\", \"imeg mbəŋchubi\", \"iməg ngwə̀t\", \"iməg fog\", \"iməg ichiibɔd\", \"iməg àdùmbə̀ŋ\", \"iməg ichika\", \"iməg kud\", \"iməg tèsiʼe\", \"iməg zò\", \"iməg krizmed\"], [\"iməg mbegtug\", \"imeg àbùbì\", \"imeg mbəŋchubi\", \"iməg ngwə̀t\", \"iməg fog\", \"iməg ichiibɔd\", \"iməg àdùmbə̀ŋ\", \"iməg ichika\", \"iməg kud\", \"iməg tèsiʼe\", \"iməg zò\", \"iməg krizmed\"]], u, [[\"BCE\", \"CE\"], u, u], 1, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"EEEE, y MMMM dd\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"XAF\", \"FCFA\", \"shirè\", { \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAEA,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}