{"ast": null, "code": "import * as operators from '@ngrx/operators';\nimport * as i1 from 'rxjs';\nimport { merge, Observable, Subject, defer } from 'rxjs';\nimport { ignoreElements, materialize, map, catchError, filter, groupBy, mergeMap, exhaustMap, dematerialize, take, concatMap, finalize } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule, Optional, inject, makeEnvironmentProviders, ENVIRONMENT_INITIALIZER } from '@angular/core';\nimport * as i3 from '@ngrx/store';\nimport { ScannedActionsSubject, createAction, ROOT_STORE_PROVIDER, FEATURE_STATE_PROVIDER, Store } from '@ngrx/store';\nconst DEFAULT_EFFECT_CONFIG = {\n  dispatch: true,\n  functional: false,\n  useEffectsErrorHandler: true\n};\nconst CREATE_EFFECT_METADATA_KEY = '__@ngrx/effects_create__';\n\n/**\n * @description\n *\n * Creates an effect from a source and an `EffectConfig`.\n *\n * @param source A function which returns an observable or observable factory.\n * @param config A `EffectConfig` to configure the effect. By default,\n * `dispatch` is true, `functional` is false, and `useEffectsErrorHandler` is\n * true.\n * @returns If `EffectConfig`#`functional` is true, returns the source function.\n * Else, returns the source function result. When `EffectConfig`#`dispatch` is\n * true, the source function result needs to be `Observable<Action>`.\n *\n * @usageNotes\n *\n * ### Class Effects\n *\n * ```ts\n * @Injectable()\n * export class FeatureEffects {\n *   // mapping to a different action\n *   readonly effect1$ = createEffect(\n *     () => this.actions$.pipe(\n *       ofType(FeatureActions.actionOne),\n *       map(() => FeatureActions.actionTwo())\n *     )\n *   );\n *\n *   // non-dispatching effect\n *   readonly effect2$ = createEffect(\n *     () => this.actions$.pipe(\n *       ofType(FeatureActions.actionTwo),\n *       tap(() => console.log('Action Two Dispatched'))\n *     ),\n *     { dispatch: false } // FeatureActions.actionTwo is not dispatched\n *   );\n *\n *   constructor(private readonly actions$: Actions) {}\n * }\n * ```\n *\n * ### Functional Effects\n *\n * ```ts\n * // mapping to a different action\n * export const loadUsers = createEffect(\n *   (actions$ = inject(Actions), usersService = inject(UsersService)) => {\n *     return actions$.pipe(\n *       ofType(UsersPageActions.opened),\n *       exhaustMap(() => {\n *         return usersService.getAll().pipe(\n *           map((users) => UsersApiActions.usersLoadedSuccess({ users })),\n *           catchError((error) =>\n *             of(UsersApiActions.usersLoadedFailure({ error }))\n *           )\n *         );\n *       })\n *     );\n *   },\n *   { functional: true }\n * );\n *\n * // non-dispatching functional effect\n * export const logDispatchedActions = createEffect(\n *   () => inject(Actions).pipe(tap(console.log)),\n *   { functional: true, dispatch: false }\n * );\n * ```\n */\nfunction createEffect(source, config = {}) {\n  const effect = config.functional ? source : source();\n  const value = {\n    ...DEFAULT_EFFECT_CONFIG,\n    ...config // Overrides any defaults if values are provided\n  };\n  Object.defineProperty(effect, CREATE_EFFECT_METADATA_KEY, {\n    value\n  });\n  return effect;\n}\nfunction getCreateEffectMetadata(instance) {\n  const propertyNames = Object.getOwnPropertyNames(instance);\n  const metadata = propertyNames.filter(propertyName => {\n    if (instance[propertyName] && instance[propertyName].hasOwnProperty(CREATE_EFFECT_METADATA_KEY)) {\n      // If the property type has overridden `hasOwnProperty` we need to ensure\n      // that the metadata is valid (containing a `dispatch` property)\n      // https://github.com/ngrx/platform/issues/2975\n      const property = instance[propertyName];\n      return property[CREATE_EFFECT_METADATA_KEY].hasOwnProperty('dispatch');\n    }\n    return false;\n  }).map(propertyName => {\n    const metaData = instance[propertyName][CREATE_EFFECT_METADATA_KEY];\n    return {\n      propertyName,\n      ...metaData\n    };\n  });\n  return metadata;\n}\nfunction getEffectsMetadata(instance) {\n  return getSourceMetadata(instance).reduce((acc, {\n    propertyName,\n    dispatch,\n    useEffectsErrorHandler\n  }) => {\n    acc[propertyName] = {\n      dispatch,\n      useEffectsErrorHandler\n    };\n    return acc;\n  }, {});\n}\nfunction getSourceMetadata(instance) {\n  return getCreateEffectMetadata(instance);\n}\nfunction getSourceForInstance(instance) {\n  return Object.getPrototypeOf(instance);\n}\nfunction isClassInstance(obj) {\n  return !!obj.constructor && obj.constructor.name !== 'Object' && obj.constructor.name !== 'Function';\n}\nfunction isClass(classOrRecord) {\n  return typeof classOrRecord === 'function';\n}\nfunction getClasses(classesAndRecords) {\n  return classesAndRecords.filter(isClass);\n}\nfunction isToken(tokenOrRecord) {\n  return tokenOrRecord instanceof InjectionToken || isClass(tokenOrRecord);\n}\nfunction mergeEffects(sourceInstance, globalErrorHandler, effectsErrorHandler) {\n  const source = getSourceForInstance(sourceInstance);\n  const isClassBasedEffect = !!source && source.constructor.name !== 'Object';\n  const sourceName = isClassBasedEffect ? source.constructor.name : null;\n  const observables$ = getSourceMetadata(sourceInstance).map(({\n    propertyName,\n    dispatch,\n    useEffectsErrorHandler\n  }) => {\n    const observable$ = typeof sourceInstance[propertyName] === 'function' ? sourceInstance[propertyName]() : sourceInstance[propertyName];\n    const effectAction$ = useEffectsErrorHandler ? effectsErrorHandler(observable$, globalErrorHandler) : observable$;\n    if (dispatch === false) {\n      return effectAction$.pipe(ignoreElements());\n    }\n    const materialized$ = effectAction$.pipe(materialize());\n    return materialized$.pipe(map(notification => ({\n      effect: sourceInstance[propertyName],\n      notification,\n      propertyName,\n      sourceName,\n      sourceInstance\n    })));\n  });\n  return merge(...observables$);\n}\nconst MAX_NUMBER_OF_RETRY_ATTEMPTS = 10;\nfunction defaultEffectsErrorHandler(observable$, errorHandler, retryAttemptLeft = MAX_NUMBER_OF_RETRY_ATTEMPTS) {\n  return observable$.pipe(catchError(error => {\n    if (errorHandler) errorHandler.handleError(error);\n    if (retryAttemptLeft <= 1) {\n      return observable$; // last attempt\n    }\n    // Return observable that produces this particular effect\n    return defaultEffectsErrorHandler(observable$, errorHandler, retryAttemptLeft - 1);\n  }));\n}\nclass Actions extends Observable {\n  constructor(source) {\n    super();\n    if (source) {\n      this.source = source;\n    }\n  }\n  lift(operator) {\n    const observable = new Actions();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function Actions_Factory(t) {\n      return new (t || Actions)(i0.ɵɵinject(ScannedActionsSubject));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Actions,\n      factory: Actions.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Actions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Observable,\n    decorators: [{\n      type: Inject,\n      args: [ScannedActionsSubject]\n    }]\n  }], null);\n})();\n/**\n * `ofType` filters an Observable of `Actions` into an Observable of the actions\n * whose type strings are passed to it.\n *\n * For example, if `actions` has type `Actions<AdditionAction|SubstractionAction>`, and\n * the type of the `Addition` action is `add`, then\n * `actions.pipe(ofType('add'))` returns an `Observable<AdditionAction>`.\n *\n * Properly typing this function is hard and requires some advanced TS tricks\n * below.\n *\n * Type narrowing automatically works, as long as your `actions` object\n * starts with a `Actions<SomeUnionOfActions>` instead of generic `Actions`.\n *\n * For backwards compatibility, when one passes a single type argument\n * `ofType<T>('something')` the result is an `Observable<T>`. Note, that `T`\n * completely overrides any possible inference from 'something'.\n *\n * Unfortunately, for unknown 'actions: Actions' these types will produce\n * 'Observable<never>'. In such cases one has to manually set the generic type\n * like `actions.ofType<AdditionAction>('add')`.\n *\n * @usageNotes\n *\n * Filter the Actions stream on the \"customers page loaded\" action\n *\n * ```ts\n * import { ofType } from '@ngrx/effects';\n * import * fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  ofType(fromCustomers.pageLoaded)\n * )\n * ```\n */\nfunction ofType(...allowedTypes) {\n  return filter(action => allowedTypes.some(typeOrActionCreator => {\n    if (typeof typeOrActionCreator === 'string') {\n      // Comparing the string to type\n      return typeOrActionCreator === action.type;\n    }\n    // We are filtering by ActionCreator\n    return typeOrActionCreator.type === action.type;\n  }));\n}\nconst _ROOT_EFFECTS_GUARD = new InjectionToken('@ngrx/effects Internal Root Guard');\nconst USER_PROVIDED_EFFECTS = new InjectionToken('@ngrx/effects User Provided Effects');\nconst _ROOT_EFFECTS = new InjectionToken('@ngrx/effects Internal Root Effects');\nconst _ROOT_EFFECTS_INSTANCES = new InjectionToken('@ngrx/effects Internal Root Effects Instances');\nconst _FEATURE_EFFECTS = new InjectionToken('@ngrx/effects Internal Feature Effects');\nconst _FEATURE_EFFECTS_INSTANCE_GROUPS = new InjectionToken('@ngrx/effects Internal Feature Effects Instance Groups');\nconst EFFECTS_ERROR_HANDLER = new InjectionToken('@ngrx/effects Effects Error Handler', {\n  providedIn: 'root',\n  factory: () => defaultEffectsErrorHandler\n});\nconst ROOT_EFFECTS_INIT = '@ngrx/effects/init';\nconst rootEffectsInit = createAction(ROOT_EFFECTS_INIT);\nfunction reportInvalidActions(output, reporter) {\n  if (output.notification.kind === 'N') {\n    const action = output.notification.value;\n    const isInvalidAction = !isAction(action);\n    if (isInvalidAction) {\n      reporter.handleError(new Error(`Effect ${getEffectName(output)} dispatched an invalid action: ${stringify(action)}`));\n    }\n  }\n}\nfunction isAction(action) {\n  return typeof action !== 'function' && action && action.type && typeof action.type === 'string';\n}\nfunction getEffectName({\n  propertyName,\n  sourceInstance,\n  sourceName\n}) {\n  const isMethod = typeof sourceInstance[propertyName] === 'function';\n  const isClassBasedEffect = !!sourceName;\n  return isClassBasedEffect ? `\"${sourceName}.${String(propertyName)}${isMethod ? '()' : ''}\"` : `\"${String(propertyName)}()\"`;\n}\nfunction stringify(action) {\n  try {\n    return JSON.stringify(action);\n  } catch {\n    return action;\n  }\n}\nconst onIdentifyEffectsKey = 'ngrxOnIdentifyEffects';\nfunction isOnIdentifyEffects(instance) {\n  return isFunction(instance, onIdentifyEffectsKey);\n}\nconst onRunEffectsKey = 'ngrxOnRunEffects';\nfunction isOnRunEffects(instance) {\n  return isFunction(instance, onRunEffectsKey);\n}\nconst onInitEffects = 'ngrxOnInitEffects';\nfunction isOnInitEffects(instance) {\n  return isFunction(instance, onInitEffects);\n}\nfunction isFunction(instance, functionName) {\n  return instance && functionName in instance && typeof instance[functionName] === 'function';\n}\nclass EffectSources extends Subject {\n  constructor(errorHandler, effectsErrorHandler) {\n    super();\n    this.errorHandler = errorHandler;\n    this.effectsErrorHandler = effectsErrorHandler;\n  }\n  addEffects(effectSourceInstance) {\n    this.next(effectSourceInstance);\n  }\n  /**\n   * @internal\n   */\n  toActions() {\n    return this.pipe(groupBy(effectsInstance => isClassInstance(effectsInstance) ? getSourceForInstance(effectsInstance) : effectsInstance), mergeMap(source$ => {\n      return source$.pipe(groupBy(effectsInstance));\n    }), mergeMap(source$ => {\n      const effect$ = source$.pipe(exhaustMap(sourceInstance => {\n        return resolveEffectSource(this.errorHandler, this.effectsErrorHandler)(sourceInstance);\n      }), map(output => {\n        reportInvalidActions(output, this.errorHandler);\n        return output.notification;\n      }), filter(notification => notification.kind === 'N' && notification.value != null), dematerialize());\n      // start the stream with an INIT action\n      // do this only for the first Effect instance\n      const init$ = source$.pipe(take(1), filter(isOnInitEffects), map(instance => instance.ngrxOnInitEffects()));\n      return merge(effect$, init$);\n    }));\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectSources_Factory(t) {\n      return new (t || EffectSources)(i0.ɵɵinject(i0.ErrorHandler), i0.ɵɵinject(EFFECTS_ERROR_HANDLER));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EffectSources,\n      factory: EffectSources.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectSources, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.ErrorHandler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EFFECTS_ERROR_HANDLER]\n    }]\n  }], null);\n})();\nfunction effectsInstance(sourceInstance) {\n  if (isOnIdentifyEffects(sourceInstance)) {\n    return sourceInstance.ngrxOnIdentifyEffects();\n  }\n  return '';\n}\nfunction resolveEffectSource(errorHandler, effectsErrorHandler) {\n  return sourceInstance => {\n    const mergedEffects$ = mergeEffects(sourceInstance, errorHandler, effectsErrorHandler);\n    if (isOnRunEffects(sourceInstance)) {\n      return sourceInstance.ngrxOnRunEffects(mergedEffects$);\n    }\n    return mergedEffects$;\n  };\n}\nclass EffectsRunner {\n  get isStarted() {\n    return !!this.effectsSubscription;\n  }\n  constructor(effectSources, store) {\n    this.effectSources = effectSources;\n    this.store = store;\n    this.effectsSubscription = null;\n  }\n  start() {\n    if (!this.effectsSubscription) {\n      this.effectsSubscription = this.effectSources.toActions().subscribe(this.store);\n    }\n  }\n  ngOnDestroy() {\n    if (this.effectsSubscription) {\n      this.effectsSubscription.unsubscribe();\n      this.effectsSubscription = null;\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsRunner_Factory(t) {\n      return new (t || EffectsRunner)(i0.ɵɵinject(EffectSources), i0.ɵɵinject(i3.Store));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EffectsRunner,\n      factory: EffectsRunner.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsRunner, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: EffectSources\n  }, {\n    type: i3.Store\n  }], null);\n})();\nclass EffectsRootModule {\n  constructor(sources, runner, store, rootEffectsInstances, storeRootModule, storeFeatureModule, guard) {\n    this.sources = sources;\n    runner.start();\n    for (const effectsInstance of rootEffectsInstances) {\n      sources.addEffects(effectsInstance);\n    }\n    store.dispatch({\n      type: ROOT_EFFECTS_INIT\n    });\n  }\n  addEffects(effectsInstance) {\n    this.sources.addEffects(effectsInstance);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsRootModule_Factory(t) {\n      return new (t || EffectsRootModule)(i0.ɵɵinject(EffectSources), i0.ɵɵinject(EffectsRunner), i0.ɵɵinject(i3.Store), i0.ɵɵinject(_ROOT_EFFECTS_INSTANCES), i0.ɵɵinject(i3.StoreRootModule, 8), i0.ɵɵinject(i3.StoreFeatureModule, 8), i0.ɵɵinject(_ROOT_EFFECTS_GUARD, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EffectsRootModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsRootModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: EffectSources\n  }, {\n    type: EffectsRunner\n  }, {\n    type: i3.Store\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [_ROOT_EFFECTS_INSTANCES]\n    }]\n  }, {\n    type: i3.StoreRootModule,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.StoreFeatureModule,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [_ROOT_EFFECTS_GUARD]\n    }]\n  }], null);\n})();\nclass EffectsFeatureModule {\n  constructor(effectsRootModule, effectsInstanceGroups, storeRootModule, storeFeatureModule) {\n    const effectsInstances = effectsInstanceGroups.flat();\n    for (const effectsInstance of effectsInstances) {\n      effectsRootModule.addEffects(effectsInstance);\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsFeatureModule_Factory(t) {\n      return new (t || EffectsFeatureModule)(i0.ɵɵinject(EffectsRootModule), i0.ɵɵinject(_FEATURE_EFFECTS_INSTANCE_GROUPS), i0.ɵɵinject(i3.StoreRootModule, 8), i0.ɵɵinject(i3.StoreFeatureModule, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EffectsFeatureModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsFeatureModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: EffectsRootModule\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [_FEATURE_EFFECTS_INSTANCE_GROUPS]\n    }]\n  }, {\n    type: i3.StoreRootModule,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.StoreFeatureModule,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nclass EffectsModule {\n  static forFeature(...featureEffects) {\n    const effects = featureEffects.flat();\n    const effectsClasses = getClasses(effects);\n    return {\n      ngModule: EffectsFeatureModule,\n      providers: [effectsClasses, {\n        provide: _FEATURE_EFFECTS,\n        multi: true,\n        useValue: effects\n      }, {\n        provide: USER_PROVIDED_EFFECTS,\n        multi: true,\n        useValue: []\n      }, {\n        provide: _FEATURE_EFFECTS_INSTANCE_GROUPS,\n        multi: true,\n        useFactory: createEffectsInstances,\n        deps: [_FEATURE_EFFECTS, USER_PROVIDED_EFFECTS]\n      }]\n    };\n  }\n  static forRoot(...rootEffects) {\n    const effects = rootEffects.flat();\n    const effectsClasses = getClasses(effects);\n    return {\n      ngModule: EffectsRootModule,\n      providers: [effectsClasses, {\n        provide: _ROOT_EFFECTS,\n        useValue: [effects]\n      }, {\n        provide: _ROOT_EFFECTS_GUARD,\n        useFactory: _provideForRootGuard\n      }, {\n        provide: USER_PROVIDED_EFFECTS,\n        multi: true,\n        useValue: []\n      }, {\n        provide: _ROOT_EFFECTS_INSTANCES,\n        useFactory: createEffectsInstances,\n        deps: [_ROOT_EFFECTS, USER_PROVIDED_EFFECTS]\n      }]\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsModule_Factory(t) {\n      return new (t || EffectsModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EffectsModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\nfunction createEffectsInstances(effectsGroups, userProvidedEffectsGroups) {\n  const effects = [];\n  for (const effectsGroup of effectsGroups) {\n    effects.push(...effectsGroup);\n  }\n  for (const userProvidedEffectsGroup of userProvidedEffectsGroups) {\n    effects.push(...userProvidedEffectsGroup);\n  }\n  return effects.map(effectsTokenOrRecord => isToken(effectsTokenOrRecord) ? inject(effectsTokenOrRecord) : effectsTokenOrRecord);\n}\nfunction _provideForRootGuard() {\n  const runner = inject(EffectsRunner, {\n    optional: true,\n    skipSelf: true\n  });\n  const rootEffects = inject(_ROOT_EFFECTS, {\n    self: true\n  });\n  // check whether any effects are actually passed\n  const hasEffects = !(rootEffects.length === 1 && rootEffects[0].length === 0);\n  if (hasEffects && runner) {\n    throw new TypeError(`EffectsModule.forRoot() called twice. Feature modules should use EffectsModule.forFeature() instead.`);\n  }\n  return 'guarded';\n}\n\n/**\n * Wraps project fn with error handling making it safe to use in Effects.\n * Takes either a config with named properties that represent different possible\n * callbacks or project/error callbacks that are required.\n */\nfunction act(/** Allow to take either config object or project/error functions */\nconfigOrProject, errorFn) {\n  const {\n    project,\n    error,\n    complete,\n    operator,\n    unsubscribe\n  } = typeof configOrProject === 'function' ? {\n    project: configOrProject,\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    error: errorFn,\n    operator: concatMap,\n    complete: undefined,\n    unsubscribe: undefined\n  } : {\n    ...configOrProject,\n    operator: configOrProject.operator || concatMap\n  };\n  return source => defer(() => {\n    const subject = new Subject();\n    return merge(source.pipe(operator((input, index) => defer(() => {\n      let completed = false;\n      let errored = false;\n      let projectedCount = 0;\n      return project(input, index).pipe(materialize(), map(notification => {\n        switch (notification.kind) {\n          case 'E':\n            errored = true;\n            return {\n              kind: 'N',\n              value: error(notification.error, input)\n            };\n          case 'C':\n            completed = true;\n            return complete ? {\n              kind: 'N',\n              value: complete(projectedCount, input)\n            } : undefined;\n          default:\n            ++projectedCount;\n            return notification;\n        }\n      }), filter(n => n != null), dematerialize(), finalize(() => {\n        if (!completed && !errored && unsubscribe) {\n          subject.next(unsubscribe(projectedCount, input));\n        }\n      }));\n    }))), subject);\n  });\n}\n\n/**\n * @usageNotes\n *\n * ### Providing effects at the root level\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideEffects(RouterEffects)],\n * });\n * ```\n *\n * ### Providing effects at the feature level\n *\n * ```ts\n * const booksRoutes: Route[] = [\n *   {\n *     path: '',\n *     providers: [provideEffects(BooksApiEffects)],\n *     children: [\n *       { path: '', component: BookListComponent },\n *       { path: ':id', component: BookDetailsComponent },\n *     ],\n *   },\n * ];\n * ```\n */\nfunction provideEffects(...effects) {\n  const effectsClassesAndRecords = effects.flat();\n  const effectsClasses = getClasses(effectsClassesAndRecords);\n  return makeEnvironmentProviders([effectsClasses, {\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useValue: () => {\n      inject(ROOT_STORE_PROVIDER);\n      inject(FEATURE_STATE_PROVIDER, {\n        optional: true\n      });\n      const effectsRunner = inject(EffectsRunner);\n      const effectSources = inject(EffectSources);\n      const shouldInitEffects = !effectsRunner.isStarted;\n      if (shouldInitEffects) {\n        effectsRunner.start();\n      }\n      for (const effectsClassOrRecord of effectsClassesAndRecords) {\n        const effectsInstance = isClass(effectsClassOrRecord) ? inject(effectsClassOrRecord) : effectsClassOrRecord;\n        effectSources.addEffects(effectsInstance);\n      }\n      if (shouldInitEffects) {\n        const store = inject(Store);\n        store.dispatch(rootEffectsInit());\n      }\n    }\n  }]);\n}\n\n/**\n * @deprecated Use `concatLatestFrom` from `@ngrx/operators` instead.\n */\nconst concatLatestFrom = operators.concatLatestFrom;\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Actions, EFFECTS_ERROR_HANDLER, EffectSources, EffectsFeatureModule, EffectsModule, EffectsRootModule, EffectsRunner, ROOT_EFFECTS_INIT, USER_PROVIDED_EFFECTS, act, concatLatestFrom, createEffect, defaultEffectsErrorHandler, getEffectsMetadata, mergeEffects, ofType, provideEffects, rootEffectsInit };", "map": {"version": 3, "names": ["operators", "i1", "merge", "Observable", "Subject", "defer", "ignoreElements", "materialize", "map", "catchError", "filter", "groupBy", "mergeMap", "exhaustMap", "dematerialize", "take", "concatMap", "finalize", "i0", "InjectionToken", "Injectable", "Inject", "NgModule", "Optional", "inject", "makeEnvironmentProviders", "ENVIRONMENT_INITIALIZER", "i3", "ScannedActionsSubject", "createAction", "ROOT_STORE_PROVIDER", "FEATURE_STATE_PROVIDER", "Store", "DEFAULT_EFFECT_CONFIG", "dispatch", "functional", "useEffectsErrorHandler", "CREATE_EFFECT_METADATA_KEY", "createEffect", "source", "config", "effect", "value", "Object", "defineProperty", "getCreateEffectMetadata", "instance", "propertyNames", "getOwnPropertyNames", "metadata", "propertyName", "hasOwnProperty", "property", "metaData", "getEffectsMetadata", "getSourceMetadata", "reduce", "acc", "getSourceForInstance", "getPrototypeOf", "isClassInstance", "obj", "constructor", "name", "isClass", "classOrRecord", "getClasses", "classesAndRecords", "isToken", "tokenOrRecord", "mergeEffects", "sourceInstance", "globalErrorHandler", "effectsErrorHandler", "isClassBasedEffect", "sourceName", "observables$", "observable$", "effectAction$", "pipe", "materialized$", "notification", "MAX_NUMBER_OF_RETRY_ATTEMPTS", "defaultEffectsErrorHandler", "<PERSON><PERSON><PERSON><PERSON>", "retryAttemptLeft", "error", "handleError", "Actions", "lift", "operator", "observable", "ɵfac", "Actions_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "decorators", "ofType", "allowedTypes", "action", "some", "typeOrActionCreator", "_ROOT_EFFECTS_GUARD", "USER_PROVIDED_EFFECTS", "_ROOT_EFFECTS", "_ROOT_EFFECTS_INSTANCES", "_FEATURE_EFFECTS", "_FEATURE_EFFECTS_INSTANCE_GROUPS", "EFFECTS_ERROR_HANDLER", "ROOT_EFFECTS_INIT", "rootEffectsInit", "reportInvalidActions", "output", "reporter", "kind", "isInvalidAction", "isAction", "Error", "getEffectName", "stringify", "isMethod", "String", "JSON", "onIdentifyEffectsKey", "isOnIdentifyEffects", "isFunction", "onRunEffectsKey", "isOnRunEffects", "onInitEffects", "isOnInitEffects", "functionName", "EffectSources", "addEffects", "effectSourceInstance", "next", "toActions", "effectsInstance", "source$", "effect$", "resolveEffectSource", "init$", "ngrxOnInitEffects", "EffectSources_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "ngrxOnIdentifyEffects", "mergedEffects$", "ngrxOnRunEffects", "Effects<PERSON><PERSON>ner", "isStarted", "effectsSubscription", "effectSources", "store", "start", "subscribe", "ngOnDestroy", "unsubscribe", "EffectsRunner_Factory", "EffectsRootModule", "sources", "runner", "rootEffectsInstances", "storeRootModule", "storeFeatureModule", "guard", "EffectsRootModule_Factory", "StoreRootModule", "StoreFeatureModule", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "EffectsFeatureModule", "effectsRootModule", "effectsInstanceGroups", "effectsInstances", "flat", "EffectsFeatureModule_Factory", "EffectsModule", "forFeature", "featureEffects", "effects", "effectsClasses", "ngModule", "providers", "provide", "multi", "useValue", "useFactory", "createEffectsInstances", "deps", "forRoot", "rootEffects", "_provideForRootGuard", "EffectsModule_Factory", "effectsGroups", "userProvidedEffectsGroups", "effectsGroup", "push", "userProvidedEffectsGroup", "effectsTokenOrRecord", "optional", "skipSelf", "self", "hasEffects", "length", "TypeError", "act", "configOrProject", "errorFn", "project", "complete", "subject", "input", "index", "completed", "errored", "projectedCount", "n", "provideEffects", "effectsClassesAndRecords", "<PERSON><PERSON><PERSON><PERSON>", "shouldInitEffects", "effectsClassOrRecord", "concatLatestFrom"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@ngrx/effects/fesm2022/ngrx-effects.mjs"], "sourcesContent": ["import * as operators from '@ngrx/operators';\nimport * as i1 from 'rxjs';\nimport { merge, Observable, Subject, defer } from 'rxjs';\nimport { ignoreElements, materialize, map, catchError, filter, groupBy, mergeMap, exhaustMap, dematerialize, take, concatMap, finalize } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule, Optional, inject, makeEnvironmentProviders, ENVIRONMENT_INITIALIZER } from '@angular/core';\nimport * as i3 from '@ngrx/store';\nimport { ScannedActionsSubject, createAction, ROOT_STORE_PROVIDER, FEATURE_STATE_PROVIDER, Store } from '@ngrx/store';\n\nconst DEFAULT_EFFECT_CONFIG = {\n    dispatch: true,\n    functional: false,\n    useEffectsErrorHandler: true,\n};\nconst CREATE_EFFECT_METADATA_KEY = '__@ngrx/effects_create__';\n\n/**\n * @description\n *\n * Creates an effect from a source and an `EffectConfig`.\n *\n * @param source A function which returns an observable or observable factory.\n * @param config A `EffectConfig` to configure the effect. By default,\n * `dispatch` is true, `functional` is false, and `useEffectsErrorHandler` is\n * true.\n * @returns If `EffectConfig`#`functional` is true, returns the source function.\n * Else, returns the source function result. When `EffectConfig`#`dispatch` is\n * true, the source function result needs to be `Observable<Action>`.\n *\n * @usageNotes\n *\n * ### Class Effects\n *\n * ```ts\n * @Injectable()\n * export class FeatureEffects {\n *   // mapping to a different action\n *   readonly effect1$ = createEffect(\n *     () => this.actions$.pipe(\n *       ofType(FeatureActions.actionOne),\n *       map(() => FeatureActions.actionTwo())\n *     )\n *   );\n *\n *   // non-dispatching effect\n *   readonly effect2$ = createEffect(\n *     () => this.actions$.pipe(\n *       ofType(FeatureActions.actionTwo),\n *       tap(() => console.log('Action Two Dispatched'))\n *     ),\n *     { dispatch: false } // FeatureActions.actionTwo is not dispatched\n *   );\n *\n *   constructor(private readonly actions$: Actions) {}\n * }\n * ```\n *\n * ### Functional Effects\n *\n * ```ts\n * // mapping to a different action\n * export const loadUsers = createEffect(\n *   (actions$ = inject(Actions), usersService = inject(UsersService)) => {\n *     return actions$.pipe(\n *       ofType(UsersPageActions.opened),\n *       exhaustMap(() => {\n *         return usersService.getAll().pipe(\n *           map((users) => UsersApiActions.usersLoadedSuccess({ users })),\n *           catchError((error) =>\n *             of(UsersApiActions.usersLoadedFailure({ error }))\n *           )\n *         );\n *       })\n *     );\n *   },\n *   { functional: true }\n * );\n *\n * // non-dispatching functional effect\n * export const logDispatchedActions = createEffect(\n *   () => inject(Actions).pipe(tap(console.log)),\n *   { functional: true, dispatch: false }\n * );\n * ```\n */\nfunction createEffect(source, config = {}) {\n    const effect = config.functional ? source : source();\n    const value = {\n        ...DEFAULT_EFFECT_CONFIG,\n        ...config, // Overrides any defaults if values are provided\n    };\n    Object.defineProperty(effect, CREATE_EFFECT_METADATA_KEY, {\n        value,\n    });\n    return effect;\n}\nfunction getCreateEffectMetadata(instance) {\n    const propertyNames = Object.getOwnPropertyNames(instance);\n    const metadata = propertyNames\n        .filter((propertyName) => {\n        if (instance[propertyName] &&\n            instance[propertyName].hasOwnProperty(CREATE_EFFECT_METADATA_KEY)) {\n            // If the property type has overridden `hasOwnProperty` we need to ensure\n            // that the metadata is valid (containing a `dispatch` property)\n            // https://github.com/ngrx/platform/issues/2975\n            const property = instance[propertyName];\n            return property[CREATE_EFFECT_METADATA_KEY].hasOwnProperty('dispatch');\n        }\n        return false;\n    })\n        .map((propertyName) => {\n        const metaData = instance[propertyName][CREATE_EFFECT_METADATA_KEY];\n        return {\n            propertyName,\n            ...metaData,\n        };\n    });\n    return metadata;\n}\n\nfunction getEffectsMetadata(instance) {\n    return getSourceMetadata(instance).reduce((acc, { propertyName, dispatch, useEffectsErrorHandler }) => {\n        acc[propertyName] = { dispatch, useEffectsErrorHandler };\n        return acc;\n    }, {});\n}\nfunction getSourceMetadata(instance) {\n    return getCreateEffectMetadata(instance);\n}\n\nfunction getSourceForInstance(instance) {\n    return Object.getPrototypeOf(instance);\n}\nfunction isClassInstance(obj) {\n    return (!!obj.constructor &&\n        obj.constructor.name !== 'Object' &&\n        obj.constructor.name !== 'Function');\n}\nfunction isClass(classOrRecord) {\n    return typeof classOrRecord === 'function';\n}\nfunction getClasses(classesAndRecords) {\n    return classesAndRecords.filter(isClass);\n}\nfunction isToken(tokenOrRecord) {\n    return tokenOrRecord instanceof InjectionToken || isClass(tokenOrRecord);\n}\n\nfunction mergeEffects(sourceInstance, globalErrorHandler, effectsErrorHandler) {\n    const source = getSourceForInstance(sourceInstance);\n    const isClassBasedEffect = !!source && source.constructor.name !== 'Object';\n    const sourceName = isClassBasedEffect ? source.constructor.name : null;\n    const observables$ = getSourceMetadata(sourceInstance).map(({ propertyName, dispatch, useEffectsErrorHandler, }) => {\n        const observable$ = typeof sourceInstance[propertyName] === 'function'\n            ? sourceInstance[propertyName]()\n            : sourceInstance[propertyName];\n        const effectAction$ = useEffectsErrorHandler\n            ? effectsErrorHandler(observable$, globalErrorHandler)\n            : observable$;\n        if (dispatch === false) {\n            return effectAction$.pipe(ignoreElements());\n        }\n        const materialized$ = effectAction$.pipe(materialize());\n        return materialized$.pipe(map((notification) => ({\n            effect: sourceInstance[propertyName],\n            notification,\n            propertyName,\n            sourceName,\n            sourceInstance,\n        })));\n    });\n    return merge(...observables$);\n}\n\nconst MAX_NUMBER_OF_RETRY_ATTEMPTS = 10;\nfunction defaultEffectsErrorHandler(observable$, errorHandler, retryAttemptLeft = MAX_NUMBER_OF_RETRY_ATTEMPTS) {\n    return observable$.pipe(catchError((error) => {\n        if (errorHandler)\n            errorHandler.handleError(error);\n        if (retryAttemptLeft <= 1) {\n            return observable$; // last attempt\n        }\n        // Return observable that produces this particular effect\n        return defaultEffectsErrorHandler(observable$, errorHandler, retryAttemptLeft - 1);\n    }));\n}\n\nclass Actions extends Observable {\n    constructor(source) {\n        super();\n        if (source) {\n            this.source = source;\n        }\n    }\n    lift(operator) {\n        const observable = new Actions();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: Actions, deps: [{ token: ScannedActionsSubject }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    /** @nocollapse */ static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: Actions, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: Actions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Observable, decorators: [{\n                    type: Inject,\n                    args: [ScannedActionsSubject]\n                }] }] });\n/**\n * `ofType` filters an Observable of `Actions` into an Observable of the actions\n * whose type strings are passed to it.\n *\n * For example, if `actions` has type `Actions<AdditionAction|SubstractionAction>`, and\n * the type of the `Addition` action is `add`, then\n * `actions.pipe(ofType('add'))` returns an `Observable<AdditionAction>`.\n *\n * Properly typing this function is hard and requires some advanced TS tricks\n * below.\n *\n * Type narrowing automatically works, as long as your `actions` object\n * starts with a `Actions<SomeUnionOfActions>` instead of generic `Actions`.\n *\n * For backwards compatibility, when one passes a single type argument\n * `ofType<T>('something')` the result is an `Observable<T>`. Note, that `T`\n * completely overrides any possible inference from 'something'.\n *\n * Unfortunately, for unknown 'actions: Actions' these types will produce\n * 'Observable<never>'. In such cases one has to manually set the generic type\n * like `actions.ofType<AdditionAction>('add')`.\n *\n * @usageNotes\n *\n * Filter the Actions stream on the \"customers page loaded\" action\n *\n * ```ts\n * import { ofType } from '@ngrx/effects';\n * import * fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  ofType(fromCustomers.pageLoaded)\n * )\n * ```\n */\nfunction ofType(...allowedTypes) {\n    return filter((action) => allowedTypes.some((typeOrActionCreator) => {\n        if (typeof typeOrActionCreator === 'string') {\n            // Comparing the string to type\n            return typeOrActionCreator === action.type;\n        }\n        // We are filtering by ActionCreator\n        return typeOrActionCreator.type === action.type;\n    }));\n}\n\nconst _ROOT_EFFECTS_GUARD = new InjectionToken('@ngrx/effects Internal Root Guard');\nconst USER_PROVIDED_EFFECTS = new InjectionToken('@ngrx/effects User Provided Effects');\nconst _ROOT_EFFECTS = new InjectionToken('@ngrx/effects Internal Root Effects');\nconst _ROOT_EFFECTS_INSTANCES = new InjectionToken('@ngrx/effects Internal Root Effects Instances');\nconst _FEATURE_EFFECTS = new InjectionToken('@ngrx/effects Internal Feature Effects');\nconst _FEATURE_EFFECTS_INSTANCE_GROUPS = new InjectionToken('@ngrx/effects Internal Feature Effects Instance Groups');\nconst EFFECTS_ERROR_HANDLER = new InjectionToken('@ngrx/effects Effects Error Handler', { providedIn: 'root', factory: () => defaultEffectsErrorHandler });\n\nconst ROOT_EFFECTS_INIT = '@ngrx/effects/init';\nconst rootEffectsInit = createAction(ROOT_EFFECTS_INIT);\n\nfunction reportInvalidActions(output, reporter) {\n    if (output.notification.kind === 'N') {\n        const action = output.notification.value;\n        const isInvalidAction = !isAction(action);\n        if (isInvalidAction) {\n            reporter.handleError(new Error(`Effect ${getEffectName(output)} dispatched an invalid action: ${stringify(action)}`));\n        }\n    }\n}\nfunction isAction(action) {\n    return (typeof action !== 'function' &&\n        action &&\n        action.type &&\n        typeof action.type === 'string');\n}\nfunction getEffectName({ propertyName, sourceInstance, sourceName, }) {\n    const isMethod = typeof sourceInstance[propertyName] === 'function';\n    const isClassBasedEffect = !!sourceName;\n    return isClassBasedEffect\n        ? `\"${sourceName}.${String(propertyName)}${isMethod ? '()' : ''}\"`\n        : `\"${String(propertyName)}()\"`;\n}\nfunction stringify(action) {\n    try {\n        return JSON.stringify(action);\n    }\n    catch {\n        return action;\n    }\n}\n\nconst onIdentifyEffectsKey = 'ngrxOnIdentifyEffects';\nfunction isOnIdentifyEffects(instance) {\n    return isFunction(instance, onIdentifyEffectsKey);\n}\nconst onRunEffectsKey = 'ngrxOnRunEffects';\nfunction isOnRunEffects(instance) {\n    return isFunction(instance, onRunEffectsKey);\n}\nconst onInitEffects = 'ngrxOnInitEffects';\nfunction isOnInitEffects(instance) {\n    return isFunction(instance, onInitEffects);\n}\nfunction isFunction(instance, functionName) {\n    return (instance &&\n        functionName in instance &&\n        typeof instance[functionName] === 'function');\n}\n\nclass EffectSources extends Subject {\n    constructor(errorHandler, effectsErrorHandler) {\n        super();\n        this.errorHandler = errorHandler;\n        this.effectsErrorHandler = effectsErrorHandler;\n    }\n    addEffects(effectSourceInstance) {\n        this.next(effectSourceInstance);\n    }\n    /**\n     * @internal\n     */\n    toActions() {\n        return this.pipe(groupBy((effectsInstance) => isClassInstance(effectsInstance)\n            ? getSourceForInstance(effectsInstance)\n            : effectsInstance), mergeMap((source$) => {\n            return source$.pipe(groupBy(effectsInstance));\n        }), mergeMap((source$) => {\n            const effect$ = source$.pipe(exhaustMap((sourceInstance) => {\n                return resolveEffectSource(this.errorHandler, this.effectsErrorHandler)(sourceInstance);\n            }), map((output) => {\n                reportInvalidActions(output, this.errorHandler);\n                return output.notification;\n            }), filter((notification) => notification.kind === 'N' && notification.value != null), dematerialize());\n            // start the stream with an INIT action\n            // do this only for the first Effect instance\n            const init$ = source$.pipe(take(1), filter(isOnInitEffects), map((instance) => instance.ngrxOnInitEffects()));\n            return merge(effect$, init$);\n        }));\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectSources, deps: [{ token: i0.ErrorHandler }, { token: EFFECTS_ERROR_HANDLER }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    /** @nocollapse */ static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectSources, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectSources, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i0.ErrorHandler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EFFECTS_ERROR_HANDLER]\n                }] }] });\nfunction effectsInstance(sourceInstance) {\n    if (isOnIdentifyEffects(sourceInstance)) {\n        return sourceInstance.ngrxOnIdentifyEffects();\n    }\n    return '';\n}\nfunction resolveEffectSource(errorHandler, effectsErrorHandler) {\n    return (sourceInstance) => {\n        const mergedEffects$ = mergeEffects(sourceInstance, errorHandler, effectsErrorHandler);\n        if (isOnRunEffects(sourceInstance)) {\n            return sourceInstance.ngrxOnRunEffects(mergedEffects$);\n        }\n        return mergedEffects$;\n    };\n}\n\nclass EffectsRunner {\n    get isStarted() {\n        return !!this.effectsSubscription;\n    }\n    constructor(effectSources, store) {\n        this.effectSources = effectSources;\n        this.store = store;\n        this.effectsSubscription = null;\n    }\n    start() {\n        if (!this.effectsSubscription) {\n            this.effectsSubscription = this.effectSources\n                .toActions()\n                .subscribe(this.store);\n        }\n    }\n    ngOnDestroy() {\n        if (this.effectsSubscription) {\n            this.effectsSubscription.unsubscribe();\n            this.effectsSubscription = null;\n        }\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRunner, deps: [{ token: EffectSources }, { token: i3.Store }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    /** @nocollapse */ static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRunner, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRunner, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: EffectSources }, { type: i3.Store }] });\n\nclass EffectsRootModule {\n    constructor(sources, runner, store, rootEffectsInstances, storeRootModule, storeFeatureModule, guard) {\n        this.sources = sources;\n        runner.start();\n        for (const effectsInstance of rootEffectsInstances) {\n            sources.addEffects(effectsInstance);\n        }\n        store.dispatch({ type: ROOT_EFFECTS_INIT });\n    }\n    addEffects(effectsInstance) {\n        this.sources.addEffects(effectsInstance);\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRootModule, deps: [{ token: EffectSources }, { token: EffectsRunner }, { token: i3.Store }, { token: _ROOT_EFFECTS_INSTANCES }, { token: i3.StoreRootModule, optional: true }, { token: i3.StoreFeatureModule, optional: true }, { token: _ROOT_EFFECTS_GUARD, optional: true }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    /** @nocollapse */ static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRootModule }); }\n    /** @nocollapse */ static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRootModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsRootModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }], ctorParameters: () => [{ type: EffectSources }, { type: EffectsRunner }, { type: i3.Store }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [_ROOT_EFFECTS_INSTANCES]\n                }] }, { type: i3.StoreRootModule, decorators: [{\n                    type: Optional\n                }] }, { type: i3.StoreFeatureModule, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [_ROOT_EFFECTS_GUARD]\n                }] }] });\n\nclass EffectsFeatureModule {\n    constructor(effectsRootModule, effectsInstanceGroups, storeRootModule, storeFeatureModule) {\n        const effectsInstances = effectsInstanceGroups.flat();\n        for (const effectsInstance of effectsInstances) {\n            effectsRootModule.addEffects(effectsInstance);\n        }\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsFeatureModule, deps: [{ token: EffectsRootModule }, { token: _FEATURE_EFFECTS_INSTANCE_GROUPS }, { token: i3.StoreRootModule, optional: true }, { token: i3.StoreFeatureModule, optional: true }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    /** @nocollapse */ static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsFeatureModule }); }\n    /** @nocollapse */ static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsFeatureModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsFeatureModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }], ctorParameters: () => [{ type: EffectsRootModule }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [_FEATURE_EFFECTS_INSTANCE_GROUPS]\n                }] }, { type: i3.StoreRootModule, decorators: [{\n                    type: Optional\n                }] }, { type: i3.StoreFeatureModule, decorators: [{\n                    type: Optional\n                }] }] });\n\nclass EffectsModule {\n    static forFeature(...featureEffects) {\n        const effects = featureEffects.flat();\n        const effectsClasses = getClasses(effects);\n        return {\n            ngModule: EffectsFeatureModule,\n            providers: [\n                effectsClasses,\n                {\n                    provide: _FEATURE_EFFECTS,\n                    multi: true,\n                    useValue: effects,\n                },\n                {\n                    provide: USER_PROVIDED_EFFECTS,\n                    multi: true,\n                    useValue: [],\n                },\n                {\n                    provide: _FEATURE_EFFECTS_INSTANCE_GROUPS,\n                    multi: true,\n                    useFactory: createEffectsInstances,\n                    deps: [_FEATURE_EFFECTS, USER_PROVIDED_EFFECTS],\n                },\n            ],\n        };\n    }\n    static forRoot(...rootEffects) {\n        const effects = rootEffects.flat();\n        const effectsClasses = getClasses(effects);\n        return {\n            ngModule: EffectsRootModule,\n            providers: [\n                effectsClasses,\n                {\n                    provide: _ROOT_EFFECTS,\n                    useValue: [effects],\n                },\n                {\n                    provide: _ROOT_EFFECTS_GUARD,\n                    useFactory: _provideForRootGuard,\n                },\n                {\n                    provide: USER_PROVIDED_EFFECTS,\n                    multi: true,\n                    useValue: [],\n                },\n                {\n                    provide: _ROOT_EFFECTS_INSTANCES,\n                    useFactory: createEffectsInstances,\n                    deps: [_ROOT_EFFECTS, USER_PROVIDED_EFFECTS],\n                },\n            ],\n        };\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    /** @nocollapse */ static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsModule }); }\n    /** @nocollapse */ static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.0\", ngImport: i0, type: EffectsModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\nfunction createEffectsInstances(effectsGroups, userProvidedEffectsGroups) {\n    const effects = [];\n    for (const effectsGroup of effectsGroups) {\n        effects.push(...effectsGroup);\n    }\n    for (const userProvidedEffectsGroup of userProvidedEffectsGroups) {\n        effects.push(...userProvidedEffectsGroup);\n    }\n    return effects.map((effectsTokenOrRecord) => isToken(effectsTokenOrRecord)\n        ? inject(effectsTokenOrRecord)\n        : effectsTokenOrRecord);\n}\nfunction _provideForRootGuard() {\n    const runner = inject(EffectsRunner, { optional: true, skipSelf: true });\n    const rootEffects = inject(_ROOT_EFFECTS, { self: true });\n    // check whether any effects are actually passed\n    const hasEffects = !(rootEffects.length === 1 && rootEffects[0].length === 0);\n    if (hasEffects && runner) {\n        throw new TypeError(`EffectsModule.forRoot() called twice. Feature modules should use EffectsModule.forFeature() instead.`);\n    }\n    return 'guarded';\n}\n\n/**\n * Wraps project fn with error handling making it safe to use in Effects.\n * Takes either a config with named properties that represent different possible\n * callbacks or project/error callbacks that are required.\n */\nfunction act(\n/** Allow to take either config object or project/error functions */\nconfigOrProject, errorFn) {\n    const { project, error, complete, operator, unsubscribe } = typeof configOrProject === 'function'\n        ? {\n            project: configOrProject,\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            error: errorFn,\n            operator: concatMap,\n            complete: undefined,\n            unsubscribe: undefined,\n        }\n        : { ...configOrProject, operator: configOrProject.operator || concatMap };\n    return (source) => defer(() => {\n        const subject = new Subject();\n        return merge(source.pipe(operator((input, index) => defer(() => {\n            let completed = false;\n            let errored = false;\n            let projectedCount = 0;\n            return project(input, index).pipe(materialize(), map((notification) => {\n                switch (notification.kind) {\n                    case 'E':\n                        errored = true;\n                        return {\n                            kind: 'N',\n                            value: error(notification.error, input),\n                        };\n                    case 'C':\n                        completed = true;\n                        return complete\n                            ? {\n                                kind: 'N',\n                                value: complete(projectedCount, input),\n                            }\n                            : undefined;\n                    default:\n                        ++projectedCount;\n                        return notification;\n                }\n            }), filter((n) => n != null), dematerialize(), finalize(() => {\n                if (!completed && !errored && unsubscribe) {\n                    subject.next(unsubscribe(projectedCount, input));\n                }\n            }));\n        }))), subject);\n    });\n}\n\n/**\n * @usageNotes\n *\n * ### Providing effects at the root level\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideEffects(RouterEffects)],\n * });\n * ```\n *\n * ### Providing effects at the feature level\n *\n * ```ts\n * const booksRoutes: Route[] = [\n *   {\n *     path: '',\n *     providers: [provideEffects(BooksApiEffects)],\n *     children: [\n *       { path: '', component: BookListComponent },\n *       { path: ':id', component: BookDetailsComponent },\n *     ],\n *   },\n * ];\n * ```\n */\nfunction provideEffects(...effects) {\n    const effectsClassesAndRecords = effects.flat();\n    const effectsClasses = getClasses(effectsClassesAndRecords);\n    return makeEnvironmentProviders([\n        effectsClasses,\n        {\n            provide: ENVIRONMENT_INITIALIZER,\n            multi: true,\n            useValue: () => {\n                inject(ROOT_STORE_PROVIDER);\n                inject(FEATURE_STATE_PROVIDER, { optional: true });\n                const effectsRunner = inject(EffectsRunner);\n                const effectSources = inject(EffectSources);\n                const shouldInitEffects = !effectsRunner.isStarted;\n                if (shouldInitEffects) {\n                    effectsRunner.start();\n                }\n                for (const effectsClassOrRecord of effectsClassesAndRecords) {\n                    const effectsInstance = isClass(effectsClassOrRecord)\n                        ? inject(effectsClassOrRecord)\n                        : effectsClassOrRecord;\n                    effectSources.addEffects(effectsInstance);\n                }\n                if (shouldInitEffects) {\n                    const store = inject(Store);\n                    store.dispatch(rootEffectsInit());\n                }\n            },\n        },\n    ]);\n}\n\n/**\n * @deprecated Use `concatLatestFrom` from `@ngrx/operators` instead.\n */\nconst concatLatestFrom = operators.concatLatestFrom;\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Actions, EFFECTS_ERROR_HANDLER, EffectSources, EffectsFeatureModule, EffectsModule, EffectsRootModule, EffectsRunner, ROOT_EFFECTS_INIT, USER_PROVIDED_EFFECTS, act, concatLatestFrom, createEffect, defaultEffectsErrorHandler, getEffectsMetadata, mergeEffects, ofType, provideEffects, rootEffectsInit };\n"], "mappings": "AAAA,OAAO,KAAKA,SAAS,MAAM,iBAAiB;AAC5C,OAAO,KAAKC,EAAE,MAAM,MAAM;AAC1B,SAASC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACxD,SAASC,cAAc,EAAEC,WAAW,EAAEC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9J,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,wBAAwB,EAAEC,uBAAuB,QAAQ,eAAe;AACjJ,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAEC,KAAK,QAAQ,aAAa;AAErH,MAAMC,qBAAqB,GAAG;EAC1BC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,KAAK;EACjBC,sBAAsB,EAAE;AAC5B,CAAC;AACD,MAAMC,0BAA0B,GAAG,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;EACvC,MAAMC,MAAM,GAAGD,MAAM,CAACL,UAAU,GAAGI,MAAM,GAAGA,MAAM,CAAC,CAAC;EACpD,MAAMG,KAAK,GAAG;IACV,GAAGT,qBAAqB;IACxB,GAAGO,MAAM,CAAE;EACf,CAAC;EACDG,MAAM,CAACC,cAAc,CAACH,MAAM,EAAEJ,0BAA0B,EAAE;IACtDK;EACJ,CAAC,CAAC;EACF,OAAOD,MAAM;AACjB;AACA,SAASI,uBAAuBA,CAACC,QAAQ,EAAE;EACvC,MAAMC,aAAa,GAAGJ,MAAM,CAACK,mBAAmB,CAACF,QAAQ,CAAC;EAC1D,MAAMG,QAAQ,GAAGF,aAAa,CACzBrC,MAAM,CAAEwC,YAAY,IAAK;IAC1B,IAAIJ,QAAQ,CAACI,YAAY,CAAC,IACtBJ,QAAQ,CAACI,YAAY,CAAC,CAACC,cAAc,CAACd,0BAA0B,CAAC,EAAE;MACnE;MACA;MACA;MACA,MAAMe,QAAQ,GAAGN,QAAQ,CAACI,YAAY,CAAC;MACvC,OAAOE,QAAQ,CAACf,0BAA0B,CAAC,CAACc,cAAc,CAAC,UAAU,CAAC;IAC1E;IACA,OAAO,KAAK;EAChB,CAAC,CAAC,CACG3C,GAAG,CAAE0C,YAAY,IAAK;IACvB,MAAMG,QAAQ,GAAGP,QAAQ,CAACI,YAAY,CAAC,CAACb,0BAA0B,CAAC;IACnE,OAAO;MACHa,YAAY;MACZ,GAAGG;IACP,CAAC;EACL,CAAC,CAAC;EACF,OAAOJ,QAAQ;AACnB;AAEA,SAASK,kBAAkBA,CAACR,QAAQ,EAAE;EAClC,OAAOS,iBAAiB,CAACT,QAAQ,CAAC,CAACU,MAAM,CAAC,CAACC,GAAG,EAAE;IAAEP,YAAY;IAAEhB,QAAQ;IAAEE;EAAuB,CAAC,KAAK;IACnGqB,GAAG,CAACP,YAAY,CAAC,GAAG;MAAEhB,QAAQ;MAAEE;IAAuB,CAAC;IACxD,OAAOqB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA,SAASF,iBAAiBA,CAACT,QAAQ,EAAE;EACjC,OAAOD,uBAAuB,CAACC,QAAQ,CAAC;AAC5C;AAEA,SAASY,oBAAoBA,CAACZ,QAAQ,EAAE;EACpC,OAAOH,MAAM,CAACgB,cAAc,CAACb,QAAQ,CAAC;AAC1C;AACA,SAASc,eAAeA,CAACC,GAAG,EAAE;EAC1B,OAAQ,CAAC,CAACA,GAAG,CAACC,WAAW,IACrBD,GAAG,CAACC,WAAW,CAACC,IAAI,KAAK,QAAQ,IACjCF,GAAG,CAACC,WAAW,CAACC,IAAI,KAAK,UAAU;AAC3C;AACA,SAASC,OAAOA,CAACC,aAAa,EAAE;EAC5B,OAAO,OAAOA,aAAa,KAAK,UAAU;AAC9C;AACA,SAASC,UAAUA,CAACC,iBAAiB,EAAE;EACnC,OAAOA,iBAAiB,CAACzD,MAAM,CAACsD,OAAO,CAAC;AAC5C;AACA,SAASI,OAAOA,CAACC,aAAa,EAAE;EAC5B,OAAOA,aAAa,YAAYlD,cAAc,IAAI6C,OAAO,CAACK,aAAa,CAAC;AAC5E;AAEA,SAASC,YAAYA,CAACC,cAAc,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAE;EAC3E,MAAMlC,MAAM,GAAGmB,oBAAoB,CAACa,cAAc,CAAC;EACnD,MAAMG,kBAAkB,GAAG,CAAC,CAACnC,MAAM,IAAIA,MAAM,CAACuB,WAAW,CAACC,IAAI,KAAK,QAAQ;EAC3E,MAAMY,UAAU,GAAGD,kBAAkB,GAAGnC,MAAM,CAACuB,WAAW,CAACC,IAAI,GAAG,IAAI;EACtE,MAAMa,YAAY,GAAGrB,iBAAiB,CAACgB,cAAc,CAAC,CAAC/D,GAAG,CAAC,CAAC;IAAE0C,YAAY;IAAEhB,QAAQ;IAAEE;EAAwB,CAAC,KAAK;IAChH,MAAMyC,WAAW,GAAG,OAAON,cAAc,CAACrB,YAAY,CAAC,KAAK,UAAU,GAChEqB,cAAc,CAACrB,YAAY,CAAC,CAAC,CAAC,GAC9BqB,cAAc,CAACrB,YAAY,CAAC;IAClC,MAAM4B,aAAa,GAAG1C,sBAAsB,GACtCqC,mBAAmB,CAACI,WAAW,EAAEL,kBAAkB,CAAC,GACpDK,WAAW;IACjB,IAAI3C,QAAQ,KAAK,KAAK,EAAE;MACpB,OAAO4C,aAAa,CAACC,IAAI,CAACzE,cAAc,CAAC,CAAC,CAAC;IAC/C;IACA,MAAM0E,aAAa,GAAGF,aAAa,CAACC,IAAI,CAACxE,WAAW,CAAC,CAAC,CAAC;IACvD,OAAOyE,aAAa,CAACD,IAAI,CAACvE,GAAG,CAAEyE,YAAY,KAAM;MAC7CxC,MAAM,EAAE8B,cAAc,CAACrB,YAAY,CAAC;MACpC+B,YAAY;MACZ/B,YAAY;MACZyB,UAAU;MACVJ;IACJ,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC;EACF,OAAOrE,KAAK,CAAC,GAAG0E,YAAY,CAAC;AACjC;AAEA,MAAMM,4BAA4B,GAAG,EAAE;AACvC,SAASC,0BAA0BA,CAACN,WAAW,EAAEO,YAAY,EAAEC,gBAAgB,GAAGH,4BAA4B,EAAE;EAC5G,OAAOL,WAAW,CAACE,IAAI,CAACtE,UAAU,CAAE6E,KAAK,IAAK;IAC1C,IAAIF,YAAY,EACZA,YAAY,CAACG,WAAW,CAACD,KAAK,CAAC;IACnC,IAAID,gBAAgB,IAAI,CAAC,EAAE;MACvB,OAAOR,WAAW,CAAC,CAAC;IACxB;IACA;IACA,OAAOM,0BAA0B,CAACN,WAAW,EAAEO,YAAY,EAAEC,gBAAgB,GAAG,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC;AACP;AAEA,MAAMG,OAAO,SAASrF,UAAU,CAAC;EAC7B2D,WAAWA,CAACvB,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAIA,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB;EACJ;EACAkD,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,UAAU,GAAG,IAAIH,OAAO,CAAC,CAAC;IAChCG,UAAU,CAACpD,MAAM,GAAG,IAAI;IACxBoD,UAAU,CAACD,QAAQ,GAAGA,QAAQ;IAC9B,OAAOC,UAAU;EACrB;EACA;EAAmB;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,OAAO,EAAjBtE,EAAE,CAAA6E,QAAA,CAAiCnE,qBAAqB;IAAA,CAA6C;EAAE;EAC1N;EAAmB;IAAS,IAAI,CAACoE,KAAK,kBAD6E9E,EAAE,CAAA+E,kBAAA;MAAAC,KAAA,EACYV,OAAO;MAAAW,OAAA,EAAPX,OAAO,CAAAI,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACrK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHuHnF,EAAE,CAAAoF,iBAAA,CAG9Bd,OAAO,EAAc,CAAC;IACrGe,IAAI,EAAEnF,UAAU;IAChBoF,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEtG,EAAE,CAACE,UAAU;IAAEsG,UAAU,EAAE,CAAC;MACnDF,IAAI,EAAElF,MAAM;MACZmF,IAAI,EAAE,CAAC5E,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8E,MAAMA,CAAC,GAAGC,YAAY,EAAE;EAC7B,OAAOjG,MAAM,CAAEkG,MAAM,IAAKD,YAAY,CAACE,IAAI,CAAEC,mBAAmB,IAAK;IACjE,IAAI,OAAOA,mBAAmB,KAAK,QAAQ,EAAE;MACzC;MACA,OAAOA,mBAAmB,KAAKF,MAAM,CAACL,IAAI;IAC9C;IACA;IACA,OAAOO,mBAAmB,CAACP,IAAI,KAAKK,MAAM,CAACL,IAAI;EACnD,CAAC,CAAC,CAAC;AACP;AAEA,MAAMQ,mBAAmB,GAAG,IAAI5F,cAAc,CAAC,mCAAmC,CAAC;AACnF,MAAM6F,qBAAqB,GAAG,IAAI7F,cAAc,CAAC,qCAAqC,CAAC;AACvF,MAAM8F,aAAa,GAAG,IAAI9F,cAAc,CAAC,qCAAqC,CAAC;AAC/E,MAAM+F,uBAAuB,GAAG,IAAI/F,cAAc,CAAC,+CAA+C,CAAC;AACnG,MAAMgG,gBAAgB,GAAG,IAAIhG,cAAc,CAAC,wCAAwC,CAAC;AACrF,MAAMiG,gCAAgC,GAAG,IAAIjG,cAAc,CAAC,wDAAwD,CAAC;AACrH,MAAMkG,qBAAqB,GAAG,IAAIlG,cAAc,CAAC,qCAAqC,EAAE;EAAEiF,UAAU,EAAE,MAAM;EAAED,OAAO,EAAEA,CAAA,KAAMhB;AAA2B,CAAC,CAAC;AAE1J,MAAMmC,iBAAiB,GAAG,oBAAoB;AAC9C,MAAMC,eAAe,GAAG1F,YAAY,CAACyF,iBAAiB,CAAC;AAEvD,SAASE,oBAAoBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,IAAID,MAAM,CAACxC,YAAY,CAAC0C,IAAI,KAAK,GAAG,EAAE;IAClC,MAAMf,MAAM,GAAGa,MAAM,CAACxC,YAAY,CAACvC,KAAK;IACxC,MAAMkF,eAAe,GAAG,CAACC,QAAQ,CAACjB,MAAM,CAAC;IACzC,IAAIgB,eAAe,EAAE;MACjBF,QAAQ,CAACnC,WAAW,CAAC,IAAIuC,KAAK,CAAC,UAAUC,aAAa,CAACN,MAAM,CAAC,kCAAkCO,SAAS,CAACpB,MAAM,CAAC,EAAE,CAAC,CAAC;IACzH;EACJ;AACJ;AACA,SAASiB,QAAQA,CAACjB,MAAM,EAAE;EACtB,OAAQ,OAAOA,MAAM,KAAK,UAAU,IAChCA,MAAM,IACNA,MAAM,CAACL,IAAI,IACX,OAAOK,MAAM,CAACL,IAAI,KAAK,QAAQ;AACvC;AACA,SAASwB,aAAaA,CAAC;EAAE7E,YAAY;EAAEqB,cAAc;EAAEI;AAAY,CAAC,EAAE;EAClE,MAAMsD,QAAQ,GAAG,OAAO1D,cAAc,CAACrB,YAAY,CAAC,KAAK,UAAU;EACnE,MAAMwB,kBAAkB,GAAG,CAAC,CAACC,UAAU;EACvC,OAAOD,kBAAkB,GACnB,IAAIC,UAAU,IAAIuD,MAAM,CAAChF,YAAY,CAAC,GAAG+E,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG,GAChE,IAAIC,MAAM,CAAChF,YAAY,CAAC,KAAK;AACvC;AACA,SAAS8E,SAASA,CAACpB,MAAM,EAAE;EACvB,IAAI;IACA,OAAOuB,IAAI,CAACH,SAAS,CAACpB,MAAM,CAAC;EACjC,CAAC,CACD,MAAM;IACF,OAAOA,MAAM;EACjB;AACJ;AAEA,MAAMwB,oBAAoB,GAAG,uBAAuB;AACpD,SAASC,mBAAmBA,CAACvF,QAAQ,EAAE;EACnC,OAAOwF,UAAU,CAACxF,QAAQ,EAAEsF,oBAAoB,CAAC;AACrD;AACA,MAAMG,eAAe,GAAG,kBAAkB;AAC1C,SAASC,cAAcA,CAAC1F,QAAQ,EAAE;EAC9B,OAAOwF,UAAU,CAACxF,QAAQ,EAAEyF,eAAe,CAAC;AAChD;AACA,MAAME,aAAa,GAAG,mBAAmB;AACzC,SAASC,eAAeA,CAAC5F,QAAQ,EAAE;EAC/B,OAAOwF,UAAU,CAACxF,QAAQ,EAAE2F,aAAa,CAAC;AAC9C;AACA,SAASH,UAAUA,CAACxF,QAAQ,EAAE6F,YAAY,EAAE;EACxC,OAAQ7F,QAAQ,IACZ6F,YAAY,IAAI7F,QAAQ,IACxB,OAAOA,QAAQ,CAAC6F,YAAY,CAAC,KAAK,UAAU;AACpD;AAEA,MAAMC,aAAa,SAASxI,OAAO,CAAC;EAChC0D,WAAWA,CAACsB,YAAY,EAAEX,mBAAmB,EAAE;IAC3C,KAAK,CAAC,CAAC;IACP,IAAI,CAACW,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACX,mBAAmB,GAAGA,mBAAmB;EAClD;EACAoE,UAAUA,CAACC,oBAAoB,EAAE;IAC7B,IAAI,CAACC,IAAI,CAACD,oBAAoB,CAAC;EACnC;EACA;AACJ;AACA;EACIE,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjE,IAAI,CAACpE,OAAO,CAAEsI,eAAe,IAAKrF,eAAe,CAACqF,eAAe,CAAC,GACxEvF,oBAAoB,CAACuF,eAAe,CAAC,GACrCA,eAAe,CAAC,EAAErI,QAAQ,CAAEsI,OAAO,IAAK;MAC1C,OAAOA,OAAO,CAACnE,IAAI,CAACpE,OAAO,CAACsI,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC,EAAErI,QAAQ,CAAEsI,OAAO,IAAK;MACtB,MAAMC,OAAO,GAAGD,OAAO,CAACnE,IAAI,CAAClE,UAAU,CAAE0D,cAAc,IAAK;QACxD,OAAO6E,mBAAmB,CAAC,IAAI,CAAChE,YAAY,EAAE,IAAI,CAACX,mBAAmB,CAAC,CAACF,cAAc,CAAC;MAC3F,CAAC,CAAC,EAAE/D,GAAG,CAAEiH,MAAM,IAAK;QAChBD,oBAAoB,CAACC,MAAM,EAAE,IAAI,CAACrC,YAAY,CAAC;QAC/C,OAAOqC,MAAM,CAACxC,YAAY;MAC9B,CAAC,CAAC,EAAEvE,MAAM,CAAEuE,YAAY,IAAKA,YAAY,CAAC0C,IAAI,KAAK,GAAG,IAAI1C,YAAY,CAACvC,KAAK,IAAI,IAAI,CAAC,EAAE5B,aAAa,CAAC,CAAC,CAAC;MACvG;MACA;MACA,MAAMuI,KAAK,GAAGH,OAAO,CAACnE,IAAI,CAAChE,IAAI,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACgI,eAAe,CAAC,EAAElI,GAAG,CAAEsC,QAAQ,IAAKA,QAAQ,CAACwG,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC7G,OAAOpJ,KAAK,CAACiJ,OAAO,EAAEE,KAAK,CAAC;IAChC,CAAC,CAAC,CAAC;EACP;EACA;EAAmB;IAAS,IAAI,CAACzD,IAAI,YAAA2D,sBAAAzD,CAAA;MAAA,YAAAA,CAAA,IAAwF8C,aAAa,EAlJvB1H,EAAE,CAAA6E,QAAA,CAkJuC7E,EAAE,CAACsI,YAAY,GAlJxDtI,EAAE,CAAA6E,QAAA,CAkJmEsB,qBAAqB;IAAA,CAA6C;EAAE;EAC5P;EAAmB;IAAS,IAAI,CAACrB,KAAK,kBAnJ6E9E,EAAE,CAAA+E,kBAAA;MAAAC,KAAA,EAmJY0C,aAAa;MAAAzC,OAAA,EAAbyC,aAAa,CAAAhD,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArJuHnF,EAAE,CAAAoF,iBAAA,CAqJ9BsC,aAAa,EAAc,CAAC;IAC3GrC,IAAI,EAAEnF,UAAU;IAChBoF,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAErF,EAAE,CAACsI;EAAa,CAAC,EAAE;IAAEjD,IAAI,EAAEkD,SAAS;IAAEhD,UAAU,EAAE,CAAC;MAC1EF,IAAI,EAAElF,MAAM;MACZmF,IAAI,EAAE,CAACa,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,SAAS4B,eAAeA,CAAC1E,cAAc,EAAE;EACrC,IAAI8D,mBAAmB,CAAC9D,cAAc,CAAC,EAAE;IACrC,OAAOA,cAAc,CAACmF,qBAAqB,CAAC,CAAC;EACjD;EACA,OAAO,EAAE;AACb;AACA,SAASN,mBAAmBA,CAAChE,YAAY,EAAEX,mBAAmB,EAAE;EAC5D,OAAQF,cAAc,IAAK;IACvB,MAAMoF,cAAc,GAAGrF,YAAY,CAACC,cAAc,EAAEa,YAAY,EAAEX,mBAAmB,CAAC;IACtF,IAAI+D,cAAc,CAACjE,cAAc,CAAC,EAAE;MAChC,OAAOA,cAAc,CAACqF,gBAAgB,CAACD,cAAc,CAAC;IAC1D;IACA,OAAOA,cAAc;EACzB,CAAC;AACL;AAEA,MAAME,aAAa,CAAC;EAChB,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACC,mBAAmB;EACrC;EACAjG,WAAWA,CAACkG,aAAa,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,mBAAmB,GAAG,IAAI;EACnC;EACAG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACH,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACC,aAAa,CACxChB,SAAS,CAAC,CAAC,CACXmB,SAAS,CAAC,IAAI,CAACF,KAAK,CAAC;IAC9B;EACJ;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACL,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACM,WAAW,CAAC,CAAC;MACtC,IAAI,CAACN,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACA;EAAmB;IAAS,IAAI,CAACnE,IAAI,YAAA0E,sBAAAxE,CAAA;MAAA,YAAAA,CAAA,IAAwF+D,aAAa,EAlMvB3I,EAAE,CAAA6E,QAAA,CAkMuC6C,aAAa,GAlMtD1H,EAAE,CAAA6E,QAAA,CAkMiEpE,EAAE,CAACK,KAAK;IAAA,CAA6C;EAAE;EAC7O;EAAmB;IAAS,IAAI,CAACgE,KAAK,kBAnM6E9E,EAAE,CAAA+E,kBAAA;MAAAC,KAAA,EAmMY2D,aAAa;MAAA1D,OAAA,EAAb0D,aAAa,CAAAjE,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArMuHnF,EAAE,CAAAoF,iBAAA,CAqM9BuD,aAAa,EAAc,CAAC;IAC3GtD,IAAI,EAAEnF,UAAU;IAChBoF,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEqC;EAAc,CAAC,EAAE;IAAErC,IAAI,EAAE5E,EAAE,CAACK;EAAM,CAAC,CAAC;AAAA;AAE/E,MAAMuI,iBAAiB,CAAC;EACpBzG,WAAWA,CAAC0G,OAAO,EAAEC,MAAM,EAAER,KAAK,EAAES,oBAAoB,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,KAAK,EAAE;IAClG,IAAI,CAACL,OAAO,GAAGA,OAAO;IACtBC,MAAM,CAACP,KAAK,CAAC,CAAC;IACd,KAAK,MAAMjB,eAAe,IAAIyB,oBAAoB,EAAE;MAChDF,OAAO,CAAC3B,UAAU,CAACI,eAAe,CAAC;IACvC;IACAgB,KAAK,CAAC/H,QAAQ,CAAC;MAAEqE,IAAI,EAAEe;IAAkB,CAAC,CAAC;EAC/C;EACAuB,UAAUA,CAACI,eAAe,EAAE;IACxB,IAAI,CAACuB,OAAO,CAAC3B,UAAU,CAACI,eAAe,CAAC;EAC5C;EACA;EAAmB;IAAS,IAAI,CAACrD,IAAI,YAAAkF,0BAAAhF,CAAA;MAAA,YAAAA,CAAA,IAAwFyE,iBAAiB,EAtN3BrJ,EAAE,CAAA6E,QAAA,CAsN2C6C,aAAa,GAtN1D1H,EAAE,CAAA6E,QAAA,CAsNqE8D,aAAa,GAtNpF3I,EAAE,CAAA6E,QAAA,CAsN+FpE,EAAE,CAACK,KAAK,GAtNzGd,EAAE,CAAA6E,QAAA,CAsNoHmB,uBAAuB,GAtN7IhG,EAAE,CAAA6E,QAAA,CAsNwJpE,EAAE,CAACoJ,eAAe,MAtN5K7J,EAAE,CAAA6E,QAAA,CAsNuMpE,EAAE,CAACqJ,kBAAkB,MAtN9N9J,EAAE,CAAA6E,QAAA,CAsNyPgB,mBAAmB;IAAA,CAA2D;EAAE;EAC9b;EAAmB;IAAS,IAAI,CAACkE,IAAI,kBAvN8E/J,EAAE,CAAAgK,gBAAA;MAAA3E,IAAA,EAuNSgE;IAAiB,EAAG;EAAE;EACpJ;EAAmB;IAAS,IAAI,CAACY,IAAI,kBAxN8EjK,EAAE,CAAAkK,gBAAA,IAwN6B;EAAE;AACxJ;AACA;EAAA,QAAA/E,SAAA,oBAAAA,SAAA,KA1NuHnF,EAAE,CAAAoF,iBAAA,CA0N9BiE,iBAAiB,EAAc,CAAC;IAC/GhE,IAAI,EAAEjF,QAAQ;IACdkF,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAED,IAAI,EAAEqC;EAAc,CAAC,EAAE;IAAErC,IAAI,EAAEsD;EAAc,CAAC,EAAE;IAAEtD,IAAI,EAAE5E,EAAE,CAACK;EAAM,CAAC,EAAE;IAAEuE,IAAI,EAAEkD,SAAS;IAAEhD,UAAU,EAAE,CAAC;MACrHF,IAAI,EAAElF,MAAM;MACZmF,IAAI,EAAE,CAACU,uBAAuB;IAClC,CAAC;EAAE,CAAC,EAAE;IAAEX,IAAI,EAAE5E,EAAE,CAACoJ,eAAe;IAAEtE,UAAU,EAAE,CAAC;MAC3CF,IAAI,EAAEhF;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgF,IAAI,EAAE5E,EAAE,CAACqJ,kBAAkB;IAAEvE,UAAU,EAAE,CAAC;MAC9CF,IAAI,EAAEhF;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgF,IAAI,EAAEkD,SAAS;IAAEhD,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAEhF;IACV,CAAC,EAAE;MACCgF,IAAI,EAAElF,MAAM;MACZmF,IAAI,EAAE,CAACO,mBAAmB;IAC9B,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMsE,oBAAoB,CAAC;EACvBvH,WAAWA,CAACwH,iBAAiB,EAAEC,qBAAqB,EAAEZ,eAAe,EAAEC,kBAAkB,EAAE;IACvF,MAAMY,gBAAgB,GAAGD,qBAAqB,CAACE,IAAI,CAAC,CAAC;IACrD,KAAK,MAAMxC,eAAe,IAAIuC,gBAAgB,EAAE;MAC5CF,iBAAiB,CAACzC,UAAU,CAACI,eAAe,CAAC;IACjD;EACJ;EACA;EAAmB;IAAS,IAAI,CAACrD,IAAI,YAAA8F,6BAAA5F,CAAA;MAAA,YAAAA,CAAA,IAAwFuF,oBAAoB,EAlP9BnK,EAAE,CAAA6E,QAAA,CAkP8CwE,iBAAiB,GAlPjErJ,EAAE,CAAA6E,QAAA,CAkP4EqB,gCAAgC,GAlP9GlG,EAAE,CAAA6E,QAAA,CAkPyHpE,EAAE,CAACoJ,eAAe,MAlP7I7J,EAAE,CAAA6E,QAAA,CAkPwKpE,EAAE,CAACqJ,kBAAkB;IAAA,CAA2D;EAAE;EAC/W;EAAmB;IAAS,IAAI,CAACC,IAAI,kBAnP8E/J,EAAE,CAAAgK,gBAAA;MAAA3E,IAAA,EAmPS8E;IAAoB,EAAG;EAAE;EACvJ;EAAmB;IAAS,IAAI,CAACF,IAAI,kBApP8EjK,EAAE,CAAAkK,gBAAA,IAoPgC;EAAE;AAC3J;AACA;EAAA,QAAA/E,SAAA,oBAAAA,SAAA,KAtPuHnF,EAAE,CAAAoF,iBAAA,CAsP9B+E,oBAAoB,EAAc,CAAC;IAClH9E,IAAI,EAAEjF,QAAQ;IACdkF,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAED,IAAI,EAAEgE;EAAkB,CAAC,EAAE;IAAEhE,IAAI,EAAEkD,SAAS;IAAEhD,UAAU,EAAE,CAAC;MAC5EF,IAAI,EAAElF,MAAM;MACZmF,IAAI,EAAE,CAACY,gCAAgC;IAC3C,CAAC;EAAE,CAAC,EAAE;IAAEb,IAAI,EAAE5E,EAAE,CAACoJ,eAAe;IAAEtE,UAAU,EAAE,CAAC;MAC3CF,IAAI,EAAEhF;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgF,IAAI,EAAE5E,EAAE,CAACqJ,kBAAkB;IAAEvE,UAAU,EAAE,CAAC;MAC9CF,IAAI,EAAEhF;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMoK,aAAa,CAAC;EAChB,OAAOC,UAAUA,CAAC,GAAGC,cAAc,EAAE;IACjC,MAAMC,OAAO,GAAGD,cAAc,CAACJ,IAAI,CAAC,CAAC;IACrC,MAAMM,cAAc,GAAG7H,UAAU,CAAC4H,OAAO,CAAC;IAC1C,OAAO;MACHE,QAAQ,EAAEX,oBAAoB;MAC9BY,SAAS,EAAE,CACPF,cAAc,EACd;QACIG,OAAO,EAAE/E,gBAAgB;QACzBgF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAEN;MACd,CAAC,EACD;QACII,OAAO,EAAElF,qBAAqB;QAC9BmF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE;MACd,CAAC,EACD;QACIF,OAAO,EAAE9E,gCAAgC;QACzC+E,KAAK,EAAE,IAAI;QACXE,UAAU,EAAEC,sBAAsB;QAClCC,IAAI,EAAE,CAACpF,gBAAgB,EAAEH,qBAAqB;MAClD,CAAC;IAET,CAAC;EACL;EACA,OAAOwF,OAAOA,CAAC,GAAGC,WAAW,EAAE;IAC3B,MAAMX,OAAO,GAAGW,WAAW,CAAChB,IAAI,CAAC,CAAC;IAClC,MAAMM,cAAc,GAAG7H,UAAU,CAAC4H,OAAO,CAAC;IAC1C,OAAO;MACHE,QAAQ,EAAEzB,iBAAiB;MAC3B0B,SAAS,EAAE,CACPF,cAAc,EACd;QACIG,OAAO,EAAEjF,aAAa;QACtBmF,QAAQ,EAAE,CAACN,OAAO;MACtB,CAAC,EACD;QACII,OAAO,EAAEnF,mBAAmB;QAC5BsF,UAAU,EAAEK;MAChB,CAAC,EACD;QACIR,OAAO,EAAElF,qBAAqB;QAC9BmF,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE;MACd,CAAC,EACD;QACIF,OAAO,EAAEhF,uBAAuB;QAChCmF,UAAU,EAAEC,sBAAsB;QAClCC,IAAI,EAAE,CAACtF,aAAa,EAAED,qBAAqB;MAC/C,CAAC;IAET,CAAC;EACL;EACA;EAAmB;IAAS,IAAI,CAACpB,IAAI,YAAA+G,sBAAA7G,CAAA;MAAA,YAAAA,CAAA,IAAwF6F,aAAa;IAAA,CAAkD;EAAE;EAC9L;EAAmB;IAAS,IAAI,CAACV,IAAI,kBA1T8E/J,EAAE,CAAAgK,gBAAA;MAAA3E,IAAA,EA0TSoF;IAAa,EAAG;EAAE;EAChJ;EAAmB;IAAS,IAAI,CAACR,IAAI,kBA3T8EjK,EAAE,CAAAkK,gBAAA,IA2TyB;EAAE;AACpJ;AACA;EAAA,QAAA/E,SAAA,oBAAAA,SAAA,KA7TuHnF,EAAE,CAAAoF,iBAAA,CA6T9BqF,aAAa,EAAc,CAAC;IAC3GpF,IAAI,EAAEjF,QAAQ;IACdkF,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;AACV,SAAS8F,sBAAsBA,CAACM,aAAa,EAAEC,yBAAyB,EAAE;EACtE,MAAMf,OAAO,GAAG,EAAE;EAClB,KAAK,MAAMgB,YAAY,IAAIF,aAAa,EAAE;IACtCd,OAAO,CAACiB,IAAI,CAAC,GAAGD,YAAY,CAAC;EACjC;EACA,KAAK,MAAME,wBAAwB,IAAIH,yBAAyB,EAAE;IAC9Df,OAAO,CAACiB,IAAI,CAAC,GAAGC,wBAAwB,CAAC;EAC7C;EACA,OAAOlB,OAAO,CAACtL,GAAG,CAAEyM,oBAAoB,IAAK7I,OAAO,CAAC6I,oBAAoB,CAAC,GACpEzL,MAAM,CAACyL,oBAAoB,CAAC,GAC5BA,oBAAoB,CAAC;AAC/B;AACA,SAASP,oBAAoBA,CAAA,EAAG;EAC5B,MAAMjC,MAAM,GAAGjJ,MAAM,CAACqI,aAAa,EAAE;IAAEqD,QAAQ,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACxE,MAAMV,WAAW,GAAGjL,MAAM,CAACyF,aAAa,EAAE;IAAEmG,IAAI,EAAE;EAAK,CAAC,CAAC;EACzD;EACA,MAAMC,UAAU,GAAG,EAAEZ,WAAW,CAACa,MAAM,KAAK,CAAC,IAAIb,WAAW,CAAC,CAAC,CAAC,CAACa,MAAM,KAAK,CAAC,CAAC;EAC7E,IAAID,UAAU,IAAI5C,MAAM,EAAE;IACtB,MAAM,IAAI8C,SAAS,CAAC,sGAAsG,CAAC;EAC/H;EACA,OAAO,SAAS;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CACZ;AACAC,eAAe,EAAEC,OAAO,EAAE;EACtB,MAAM;IAAEC,OAAO;IAAErI,KAAK;IAAEsI,QAAQ;IAAElI,QAAQ;IAAE2E;EAAY,CAAC,GAAG,OAAOoD,eAAe,KAAK,UAAU,GAC3F;IACEE,OAAO,EAAEF,eAAe;IACxB;IACAnI,KAAK,EAAEoI,OAAO;IACdhI,QAAQ,EAAE1E,SAAS;IACnB4M,QAAQ,EAAEnE,SAAS;IACnBY,WAAW,EAAEZ;EACjB,CAAC,GACC;IAAE,GAAGgE,eAAe;IAAE/H,QAAQ,EAAE+H,eAAe,CAAC/H,QAAQ,IAAI1E;EAAU,CAAC;EAC7E,OAAQuB,MAAM,IAAKlC,KAAK,CAAC,MAAM;IAC3B,MAAMwN,OAAO,GAAG,IAAIzN,OAAO,CAAC,CAAC;IAC7B,OAAOF,KAAK,CAACqC,MAAM,CAACwC,IAAI,CAACW,QAAQ,CAAC,CAACoI,KAAK,EAAEC,KAAK,KAAK1N,KAAK,CAAC,MAAM;MAC5D,IAAI2N,SAAS,GAAG,KAAK;MACrB,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAIC,cAAc,GAAG,CAAC;MACtB,OAAOP,OAAO,CAACG,KAAK,EAAEC,KAAK,CAAC,CAAChJ,IAAI,CAACxE,WAAW,CAAC,CAAC,EAAEC,GAAG,CAAEyE,YAAY,IAAK;QACnE,QAAQA,YAAY,CAAC0C,IAAI;UACrB,KAAK,GAAG;YACJsG,OAAO,GAAG,IAAI;YACd,OAAO;cACHtG,IAAI,EAAE,GAAG;cACTjF,KAAK,EAAE4C,KAAK,CAACL,YAAY,CAACK,KAAK,EAAEwI,KAAK;YAC1C,CAAC;UACL,KAAK,GAAG;YACJE,SAAS,GAAG,IAAI;YAChB,OAAOJ,QAAQ,GACT;cACEjG,IAAI,EAAE,GAAG;cACTjF,KAAK,EAAEkL,QAAQ,CAACM,cAAc,EAAEJ,KAAK;YACzC,CAAC,GACCrE,SAAS;UACnB;YACI,EAAEyE,cAAc;YAChB,OAAOjJ,YAAY;QAC3B;MACJ,CAAC,CAAC,EAAEvE,MAAM,CAAEyN,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,EAAErN,aAAa,CAAC,CAAC,EAAEG,QAAQ,CAAC,MAAM;QAC1D,IAAI,CAAC+M,SAAS,IAAI,CAACC,OAAO,IAAI5D,WAAW,EAAE;UACvCwD,OAAO,CAAC9E,IAAI,CAACsB,WAAW,CAAC6D,cAAc,EAAEJ,KAAK,CAAC,CAAC;QACpD;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC;EAClB,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,cAAcA,CAAC,GAAGtC,OAAO,EAAE;EAChC,MAAMuC,wBAAwB,GAAGvC,OAAO,CAACL,IAAI,CAAC,CAAC;EAC/C,MAAMM,cAAc,GAAG7H,UAAU,CAACmK,wBAAwB,CAAC;EAC3D,OAAO5M,wBAAwB,CAAC,CAC5BsK,cAAc,EACd;IACIG,OAAO,EAAExK,uBAAuB;IAChCyK,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAEA,CAAA,KAAM;MACZ5K,MAAM,CAACM,mBAAmB,CAAC;MAC3BN,MAAM,CAACO,sBAAsB,EAAE;QAAEmL,QAAQ,EAAE;MAAK,CAAC,CAAC;MAClD,MAAMoB,aAAa,GAAG9M,MAAM,CAACqI,aAAa,CAAC;MAC3C,MAAMG,aAAa,GAAGxI,MAAM,CAACoH,aAAa,CAAC;MAC3C,MAAM2F,iBAAiB,GAAG,CAACD,aAAa,CAACxE,SAAS;MAClD,IAAIyE,iBAAiB,EAAE;QACnBD,aAAa,CAACpE,KAAK,CAAC,CAAC;MACzB;MACA,KAAK,MAAMsE,oBAAoB,IAAIH,wBAAwB,EAAE;QACzD,MAAMpF,eAAe,GAAGjF,OAAO,CAACwK,oBAAoB,CAAC,GAC/ChN,MAAM,CAACgN,oBAAoB,CAAC,GAC5BA,oBAAoB;QAC1BxE,aAAa,CAACnB,UAAU,CAACI,eAAe,CAAC;MAC7C;MACA,IAAIsF,iBAAiB,EAAE;QACnB,MAAMtE,KAAK,GAAGzI,MAAM,CAACQ,KAAK,CAAC;QAC3BiI,KAAK,CAAC/H,QAAQ,CAACqF,eAAe,CAAC,CAAC,CAAC;MACrC;IACJ;EACJ,CAAC,CACJ,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMkH,gBAAgB,GAAGzO,SAAS,CAACyO,gBAAgB;;AAEnD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASjJ,OAAO,EAAE6B,qBAAqB,EAAEuB,aAAa,EAAEyC,oBAAoB,EAAEM,aAAa,EAAEpB,iBAAiB,EAAEV,aAAa,EAAEvC,iBAAiB,EAAEN,qBAAqB,EAAEwG,GAAG,EAAEiB,gBAAgB,EAAEnM,YAAY,EAAE6C,0BAA0B,EAAE7B,kBAAkB,EAAEgB,YAAY,EAAEoC,MAAM,EAAE0H,cAAc,EAAE7G,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}