{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar weekdays = ['svētdienā', 'pirmdienā', 'otrdienā', 'trešdienā', 'ceturtdienā', 'piektdienā', 'sestdienā'];\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    var weekday = weekdays[date.getUTCDay()];\n    return \"'Pagā<PERSON><PERSON><PERSON> \" + weekday + \" plkst.' p\";\n  },\n  yesterday: \"'<PERSON><PERSON>r plkst.' p\",\n  today: \"'<PERSON><PERSON><PERSON> plkst.' p\",\n  tomorrow: \"'Rīt plkst.' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    var weekday = weekdays[date.getUTCDay()];\n    return \"'<PERSON><PERSON><PERSON><PERSON>j<PERSON> \" + weekday + \" plkst.' p\";\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}