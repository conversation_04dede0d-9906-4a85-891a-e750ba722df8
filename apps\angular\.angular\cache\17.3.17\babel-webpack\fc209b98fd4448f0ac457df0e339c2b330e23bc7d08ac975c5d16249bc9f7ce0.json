{"ast": null, "code": "// This file is generated automatically by `scripts/build/indices.js`. Please, don't change it.\n\nexport { default as format } from './format/index.js';\nexport { default as formatInTimeZone } from './formatInTimeZone/index.js';\nexport { default as getTimezoneOffset } from './getTimezoneOffset/index.js';\nexport { default as toDate } from './toDate/index.js';\nexport { default as utcToZonedTime } from './utcToZonedTime/index.js';\nexport { default as zonedTimeToUtc } from './zonedTimeToUtc/index.js';", "map": {"version": 3, "names": ["default", "format", "formatInTimeZone", "getTimezoneOffset", "toDate", "utcToZonedTime", "zonedTimeToUtc"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns-tz/esm/index.js"], "sourcesContent": ["// This file is generated automatically by `scripts/build/indices.js`. Please, don't change it.\n\nexport { default as format } from './format/index.js'\nexport { default as formatInTimeZone } from './formatInTimeZone/index.js'\nexport { default as getTimezoneOffset } from './getTimezoneOffset/index.js'\nexport { default as toDate } from './toDate/index.js'\nexport { default as utcToZonedTime } from './utcToZonedTime/index.js'\nexport { default as zonedTimeToUtc } from './zonedTimeToUtc/index.js'\n"], "mappings": "AAAA;;AAEA,SAASA,OAAO,IAAIC,MAAM,QAAQ,mBAAmB;AACrD,SAASD,OAAO,IAAIE,gBAAgB,QAAQ,6BAA6B;AACzE,SAASF,OAAO,IAAIG,iBAAiB,QAAQ,8BAA8B;AAC3E,SAASH,OAAO,IAAII,MAAM,QAAQ,mBAAmB;AACrD,SAASJ,OAAO,IAAIK,cAAc,QAAQ,2BAA2B;AACrE,SAASL,OAAO,IAAIM,cAAc,QAAQ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}