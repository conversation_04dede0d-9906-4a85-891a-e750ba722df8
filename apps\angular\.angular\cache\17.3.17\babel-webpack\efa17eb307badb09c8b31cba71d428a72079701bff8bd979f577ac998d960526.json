{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class FileUploadService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.uploadDeclarationImportExcelByFile = file => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Import/File/UploadDeclarationImportExcel',\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    /**\n     * Since Proxy client cannot generate associated web API method since IFormFile compile error, it needs to be manually called here.\n     * Called by CA portal information exchange import page, upload excel file.\n     *\n     */\n    this.uploadInformationExchangeImportExcelByFile = file => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Import/InfoExchange/UploadInfoExchangeImportExcel',\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.uploadBahamasCertificate = (file, password) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegration/certificate/UploadBahamasCertificate',\n      params: {\n        password\n      },\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.uploadHistoricalXml = (file, receivingCountry, financialPeriodEnd) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/CtsIntegration/UploadHistoricalXml',\n      params: {\n        receivingCountry,\n        financialPeriodEnd\n      },\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.unpackCtsPackage = file => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UnpackCtsPackage',\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.createBahamasCtsSettings = (input, file) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/bahamas-cts-settings',\n      params: {\n        systemUserName: input.systemUserName,\n        systemUserPassword: input.systemUserPassword,\n        sftpUserName: input.sftpUserName\n      },\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.updateBahamasCtsSettings = (input, file) => this.restService.request({\n      method: 'PUT',\n      url: '/api/CtsIntegrationService/bahamas-cts-settings',\n      params: {\n        id: input.id,\n        systemUserName: input.systemUserName,\n        systemUserPassword: input.systemUserPassword,\n        sftpUserName: input.sftpUserName\n      },\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function FileUploadService_Factory(t) {\n      return new (t || FileUploadService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileUploadService,\n      factory: FileUploadService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["FileUploadService", "constructor", "restService", "apiName", "uploadDeclarationImportExcelByFile", "file", "request", "method", "url", "body", "uploadInformationExchangeImportExcelByFile", "uploadBahamasCertificate", "password", "params", "uploadHistoricalXml", "receivingCountry", "financialPeriodEnd", "unpackCtsPackage", "createBahamasCtsSettings", "input", "systemUserName", "systemUserPassword", "sftpUserName", "updateBahamasCtsSettings", "id", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\services\\upload-file.service.ts"], "sourcesContent": ["import { RestService } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\nimport { UploadedFileStatus } from '../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports';\r\nimport { ValidationResult } from '../../../../proxies/economic-service/lib/proxy/fluent-validation/results';\r\nimport { UploadedInfoExchangeFileStatus } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges';\r\nimport { BahamasCertificateDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/models';\r\nimport { CtsActionResultDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-requests';\r\nimport { BahamasCtsSettingDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';\r\n\r\nexport interface ExcelUploadResultDto {\r\n  statusId: UploadedFileStatus;\r\n  statusName?: string;\r\n  validationResult: ValidationResult;\r\n}\r\n\r\nexport interface ExcelUploadInfoExchangeResultDto {\r\n  statusId: UploadedInfoExchangeFileStatus;\r\n  statusName?: string;\r\n  validationResult: ValidationResult;\r\n}\r\n\r\nexport interface UploadHistoricalXmlResultDto {\r\n  errors: string[];\r\n  success: boolean;\r\n  message?: string;\r\n}\r\n\r\nexport interface CreateBahamasCtsSettingDto {\r\n  systemUserName?: string;\r\n  systemUserPassword?: string;\r\n  sftpUserName?: string;\r\n}\r\n\r\nexport interface UpdateBahamasCtsSettingDto {\r\n  id?: string;\r\n  systemUserName?: string;\r\n  systemUserPassword?: string;\r\n  sftpUserName?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FileUploadService {\r\n  apiName = 'EconomicSubstanceService';\r\n\r\n  constructor(private restService: RestService) { }\r\n\r\n  uploadDeclarationImportExcelByFile = (file: FormData) =>\r\n    this.restService.request<any, ExcelUploadResultDto>(\r\n      {\r\n        method: 'POST',\r\n        url: '/api/ESService/Import/File/UploadDeclarationImportExcel',\r\n        body: file,\r\n      },\r\n      { apiName: this.apiName }\r\n    );\r\n\r\n  /**\r\n   * Since Proxy client cannot generate associated web API method since IFormFile compile error, it needs to be manually called here.\r\n   * Called by CA portal information exchange import page, upload excel file.\r\n   *\r\n   */\r\n  uploadInformationExchangeImportExcelByFile = (file: FormData) =>\r\n    this.restService.request<any, ExcelUploadInfoExchangeResultDto>(\r\n      {\r\n        method: 'POST',\r\n        url: '/api/ESService/Import/InfoExchange/UploadInfoExchangeImportExcel',\r\n        body: file,\r\n      },\r\n      { apiName: this.apiName }\r\n    );\r\n\r\n  uploadBahamasCertificate = (file: FormData, password: string) =>\r\n    this.restService.request<any, BahamasCertificateDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegration/certificate/UploadBahamasCertificate',\r\n      params: { password },\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n\r\n  uploadHistoricalXml = (file: FormData, receivingCountry: string, financialPeriodEnd: string) =>\r\n    this.restService.request<any, UploadHistoricalXmlResultDto>({\r\n      method: 'POST',\r\n      url: '/api/ESService/CtsIntegration/UploadHistoricalXml',\r\n      params: { receivingCountry, financialPeriodEnd },\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n\r\n  unpackCtsPackage = (file: FormData) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UnpackCtsPackage',\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n\r\n  createBahamasCtsSettings = (input: CreateBahamasCtsSettingDto, file?: FormData) =>\r\n    this.restService.request<any, BahamasCtsSettingDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/bahamas-cts-settings',\r\n      params: { systemUserName: input.systemUserName, systemUserPassword: input.systemUserPassword, sftpUserName: input.sftpUserName },\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n\r\n  updateBahamasCtsSettings = (input: UpdateBahamasCtsSettingDto, file?: FormData) =>\r\n    this.restService.request<any, BahamasCtsSettingDto>({\r\n      method: 'PUT',\r\n      url: '/api/CtsIntegrationService/bahamas-cts-settings',\r\n      params: { id: input.id, systemUserName: input.systemUserName, systemUserPassword: input.systemUserPassword, sftpUserName: input.sftpUserName },\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n}\r\n"], "mappings": ";;AA2CA,OAAM,MAAOA,iBAAiB;EAG5BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAF/B,KAAAC,OAAO,GAAG,0BAA0B;IAIpC,KAAAC,kCAAkC,GAAIC,IAAc,IAClD,IAAI,CAACH,WAAW,CAACI,OAAO,CACtB;MACEC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,yDAAyD;MAC9DC,IAAI,EAAEJ;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;IAEH;;;;;IAKA,KAAAO,0CAA0C,GAAIL,IAAc,IAC1D,IAAI,CAACH,WAAW,CAACI,OAAO,CACtB;MACEC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,kEAAkE;MACvEC,IAAI,EAAEJ;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;IAEH,KAAAQ,wBAAwB,GAAG,CAACN,IAAc,EAAEO,QAAgB,KAC1D,IAAI,CAACV,WAAW,CAACI,OAAO,CAA6B;MACnDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,0DAA0D;MAC/DK,MAAM,EAAE;QAAED;MAAQ,CAAE;MACpBH,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;IAE9B,KAAAW,mBAAmB,GAAG,CAACT,IAAc,EAAEU,gBAAwB,EAAEC,kBAA0B,KACzF,IAAI,CAACd,WAAW,CAACI,OAAO,CAAoC;MAC1DC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,mDAAmD;MACxDK,MAAM,EAAE;QAAEE,gBAAgB;QAAEC;MAAkB,CAAE;MAChDP,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;IAE9B,KAAAc,gBAAgB,GAAIZ,IAAc,IAChC,IAAI,CAACH,WAAW,CAACI,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,+DAA+D;MACpEC,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;IAE9B,KAAAe,wBAAwB,GAAG,CAACC,KAAiC,EAAEd,IAAe,KAC5E,IAAI,CAACH,WAAW,CAACI,OAAO,CAA4B;MAClDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,iDAAiD;MACtDK,MAAM,EAAE;QAAEO,cAAc,EAAED,KAAK,CAACC,cAAc;QAAEC,kBAAkB,EAAEF,KAAK,CAACE,kBAAkB;QAAEC,YAAY,EAAEH,KAAK,CAACG;MAAY,CAAE;MAChIb,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;IAE9B,KAAAoB,wBAAwB,GAAG,CAACJ,KAAiC,EAAEd,IAAe,KAC5E,IAAI,CAACH,WAAW,CAACI,OAAO,CAA4B;MAClDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,iDAAiD;MACtDK,MAAM,EAAE;QAAEW,EAAE,EAAEL,KAAK,CAACK,EAAE;QAAEJ,cAAc,EAAED,KAAK,CAACC,cAAc;QAAEC,kBAAkB,EAAEF,KAAK,CAACE,kBAAkB;QAAEC,YAAY,EAAEH,KAAK,CAACG;MAAY,CAAE;MAC9Ib,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;EArEkB;;;uBAHrCH,iBAAiB,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAjB5B,iBAAiB;MAAA6B,OAAA,EAAjB7B,iBAAiB,CAAA8B,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}