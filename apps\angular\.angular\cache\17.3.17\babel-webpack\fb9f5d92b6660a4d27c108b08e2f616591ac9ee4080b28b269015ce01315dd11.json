{"ast": null, "code": "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Occitan locale.\n * @language Occitan\n * @iso-639-2 oci\n * <AUTHOR> PAGÈS\n */\nvar locale = {\n  code: 'oc',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/oc/index.js"], "sourcesContent": ["import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Occitan locale.\n * @language Occitan\n * @iso-639-2 oci\n * <AUTHOR> PAGÈS\n */\nvar locale = {\n  code: 'oc',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,KAAK,MAAM,uBAAuB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}