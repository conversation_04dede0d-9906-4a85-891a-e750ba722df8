{"ast": null, "code": "import { InformationExchangeStatus } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\nimport { AppComponentBase } from '../../../../app-component-base';\nimport { BdoTableColumnType, BdoTableData } from '../../../../shared/components/bdo-table/bdo-table.model';\nimport { ExchangeReasonDic, InformationExchangeStatusDic, CTSUploadStatusDic, CTSUploadExchangeReasonDic } from '../../../../shared/constants';\nimport Swal from 'sweetalert2';\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\nimport { UploadHistoricalXmlDialogComponent } from '../upload-historical-xml-dialog/upload-historical-xml-dialog.component';\nimport { DecryptDataPacketDialogComponent } from '../decrypt-data-packet-dialog/decrypt-data-packet-dialog.component';\nimport { RegeneratePacketDialogComponent } from '../regenerate-packet-dialog/regenerate-packet-dialog.component';\nimport { ViewCommentDialogComponent } from '../view-comment-dialog/view-comment-dialog.component';\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/enums/ctsupload-status.enum';\nimport { UploadCtsDialogComponent } from '../upload-cts-dialog/upload-cts-dialog.component';\nimport { UpdateCtsSettingDialogComponent } from '../update-cts-setting-dialog/update-cts-setting-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i3 from \"../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges\";\nimport * as i4 from \"@abp/ng.core\";\nimport * as i5 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service\";\nimport * as i8 from \"../../../../shared/services/upload-file.service\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i11 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service\";\nimport * as i12 from \"@app/shared/services/sweetalert.service\";\nimport * as i13 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"@angular/material/input\";\nimport * as i16 from \"@angular/material/form-field\";\nimport * as i17 from \"@angular/material/icon\";\nimport * as i18 from \"@angular/material/button\";\nimport * as i19 from \"@angular/material/select\";\nimport * as i20 from \"@angular/material/core\";\nimport * as i21 from \"@angular/material/tabs\";\nimport * as i22 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i23 from \"@angular/common\";\nconst _c0 = () => [10, 20, 50, 100];\nfunction InformationExchangeMainComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"CA Certificate expired at \", i0.ɵɵpipeBind2(2, 1, ctx_r0.certificateExpirationDate, \"dd/MM/yyyy\"), \"\");\n  }\n}\nfunction InformationExchangeMainComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCtsSettingDialog());\n    });\n    i0.ɵɵtext(1, \" Update CTS Setting \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCaCertificateDialog());\n    });\n    i0.ɵɵtext(1, \" Update CA Certificate \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(0));\n    });\n    i0.ɵɵtext(1, \" Non-compliance XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(1));\n    });\n    i0.ɵɵtext(1, \" High Risk IP XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(2));\n    });\n    i0.ɵɵtext(1, \" Non-resident XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(3));\n    });\n    i0.ɵɵtext(1, \" Other Cases XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r8, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r9.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r9.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r10, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r11.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r11.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r12.code2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r12.name, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r13.description, \" \");\n  }\n}\nexport class InformationExchangeMainComponent extends AppComponentBase {\n  constructor(injector, router, informationExchangeService, informationExchangeDetailService, permissionService, dashboardService, dialog, certificateService, fileUploadService, toasterService, countryService, ctsPackageRequestService, sweetAlert, bahamasCtsSettingService) {\n    super(injector);\n    this.router = router;\n    this.informationExchangeService = informationExchangeService;\n    this.informationExchangeDetailService = informationExchangeDetailService;\n    this.permissionService = permissionService;\n    this.dashboardService = dashboardService;\n    this.dialog = dialog;\n    this.certificateService = certificateService;\n    this.fileUploadService = fileUploadService;\n    this.toasterService = toasterService;\n    this.countryService = countryService;\n    this.ctsPackageRequestService = ctsPackageRequestService;\n    this.sweetAlert = sweetAlert;\n    this.bahamasCtsSettingService = bahamasCtsSettingService;\n    this.input = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: 'ExchangeReason asc',\n      informationExchangeStatus: InformationExchangeStatus.None,\n      entityName: '',\n      year: ''\n    };\n    this.TableId = 'information_ex-results';\n    /* Work for pagination. Default value = 0, it is rendering first page by default. */\n    this.currentPageIndex = 0;\n    /** It is string year number array. */\n    this.year = [];\n    /** Selected year from Financial Period End Years dropdown, default is current year. */\n    this.selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\n    this.exchangeResultColumns = [{\n      columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Exchange Reason'\n    }, {\n      columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'RA Name'\n    }, {\n      columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n      type: BdoTableColumnType.String,\n      minWidth: 100,\n      isSortable: true,\n      columnName: 'Entity Name'\n    }, {\n      columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Incop#/Formation#'\n    }, {\n      columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n      type: BdoTableColumnType.Date,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Financial Period End Date'\n    }, {\n      columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n      type: BdoTableColumnType.Date,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Due Date'\n    }, {\n      columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Information Exchange Status'\n    }, {\n      columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'View Declaration'\n    }, {\n      columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'XML Data'\n    }, {\n      columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'View History'\n    }];\n    /** Page size setting for \"TableId\" grid */\n    this.PageSize = 10;\n    this.exchangeInformationResultRecords = [];\n    this.selectReportStatus = InformationExchangeStatus.None;\n    this.informationExchangedDic = InformationExchangeStatusDic;\n    this.TableIdS = 'information_ex_summary';\n    this.currentPageIndexS = 0;\n    this.summaryExchangeColumns = [{\n      columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports'\n    }, {\n      columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Data Packet Generated'\n    }, {\n      columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Ready for Exchange'\n    }, {\n      columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n      type: BdoTableColumnType.Number,\n      minWidth: 200,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)'\n    }, {\n      columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Not Started'\n    }];\n    /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\n    this.PageSizeS = 10;\n    this.totalRecords = 0;\n    /** Note: Only logon user with permission \"Generate XML\"\n     * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\n    this.showButton = true;\n    this.showOtherCase = true;\n    /* Default current year value as string. */\n    this.currnetYear = new Date().getFullYear().toString();\n    this.certificateExpirationDate = null;\n    this.bahamasCtsSetting = null;\n    this.isCaSystemAdmin = false;\n    // Dashboard columns for CTS Upload & Transmission\n    this.ctsDashboardColumns = [{\n      columnId: 'totalNotUploaded',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Do Not Upload'\n    }, {\n      columnId: 'totalReadyForUpload',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Ready For Upload'\n    }, {\n      columnId: 'totalFailedUpload',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Failed in Upload'\n    }, {\n      columnId: 'totalUploadedToCTS',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Uploaded to CTS'\n    }, {\n      columnId: 'totalNotEnrolled',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Receiving Country Not Enrolled'\n    }];\n    // Dashboard data for CTS Upload & Transmission\n    this.ctsDashboardList = [{\n      id: 1,\n      totalNotUploaded: 0,\n      totalReadyForUpload: 1,\n      totalFailedUpload: 1,\n      totalUploadedToCTS: 2,\n      totalNotEnrolled: 2\n    }];\n    // Grid columns for CTS Upload & Transmission\n    this.ctsUploadColumns = [{\n      columnId: 'exchangeReason',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Exchange Reason'\n    }, {\n      columnId: 'dataPacket',\n      type: BdoTableColumnType.Link,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'Data Packet'\n    }, {\n      columnId: 'fileCreationDate',\n      type: BdoTableColumnType.Date,\n      minWidth: 120,\n      isSortable: true,\n      columnName: 'File Creation Date'\n    }, {\n      columnId: 'receivingCountry',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'Receiving Country'\n    }, {\n      columnId: 'ctsUploadStatus',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'CTS Upload Status'\n    }, {\n      columnId: 'uploadedAt',\n      type: BdoTableColumnType.Date,\n      minWidth: 120,\n      isSortable: true,\n      columnName: 'Uploaded At'\n    }, {\n      columnId: 'ctsTransmissionStatus',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'CTS Transmission Status'\n    }, {\n      columnId: 'viewExchangeRecords',\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'View Exchange Records'\n    }, {\n      columnId: 'viewComments',\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'View Comments'\n    }, {\n      columnId: 'regeneratePacket',\n      type: BdoTableColumnType.SingleActionButton,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'Regenerate Packet'\n    }, {\n      columnId: 'ctsUpload',\n      type: BdoTableColumnType.SingleActionButton,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'CTS Upload'\n    }, {\n      columnId: 'excludeFromCtsUpload',\n      type: BdoTableColumnType.Checkbox,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'Exclude From CTS Upload'\n    }];\n    // CTS Upload & Transmission Dashboard\n    this.ctsUploadExchangeReasonDic = CTSUploadExchangeReasonDic;\n    this.ctsUploadStatusDic = CTSUploadStatusDic;\n    this.ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \n    this.ctsUploadResultRecords = [];\n    this.selectExchangeReason = -1;\n    this.selectCtsUploadStatus = CTSUploadStatus.NotStarted;\n    this.selectReceivingCountry = '';\n    // Table IDs and page settings\n    this.ctsDashboardTableId = 'cts_dashboard';\n    this.ctsUploadTableId = 'cts_upload_grid';\n    this.ctsUploadPageSize = 10;\n    this.ctsUploadCurrentPage = 0;\n    this.ctsUploadTotalRecords = 0;\n    this.ctsUploadInput = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: 'ExchangeReason asc',\n      ctsUploadStatus: null,\n      exchangeReason: null,\n      financialEndYear: '',\n      receivingCountry: ''\n    };\n    this.summaryExchangeList = [{\n      id: 1,\n      totalNoReport: 100,\n      totalNoReportSent: 10,\n      totalNoReportRExchange: 5,\n      totalNoReportRReview: 2,\n      totalNoReportNotSent: 5\n    }];\n  }\n  ngOnInit() {\n    this.getFiscalYears().subscribe(response => {\n      if (response && response.length > 0) {\n        this.year = [];\n        response.forEach(element => {\n          this.year.push(element.toString());\n        });\n      }\n      ;\n    });\n    if (localStorage.getItem('selectedYear')) {\n      this.selectedYear = localStorage.getItem('selectedYear') ?? this.currnetYear;\n    }\n    if (localStorage.getItem('selectReportStatus')) {\n      this.selectReportStatus = Number(localStorage.getItem('selectReportStatus'));\n    }\n    this.informationExchangeDetailService.standardMonitoringFromYear().subscribe(response => {\n      this.standardMonitoringYear = response;\n      this.IsShowOtherCase(this.selectedYear);\n    });\n    this.onLazyLoadEvent(undefined);\n    this.onLazyLoadEventS(undefined);\n    this.showButton = this.checkUserPermission();\n    // CTS Upload & Transmission Dashboard\n    // Check CA System Admin role\n    const currentUser = this.configState.getOne('currentUser');\n    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');\n    if (localStorage.getItem('ctsUploadSelectedYear')) {\n      this.ctsUploadSelectedYear = localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\n    }\n    if (localStorage.getItem('selectExchangeReason')) {\n      this.selectExchangeReason = Number(localStorage.getItem('selectExchangeReason'));\n    }\n    if (localStorage.getItem('selectCtsUploadStatus')) {\n      this.selectCtsUploadStatus = Number(localStorage.getItem('selectCtsUploadStatus'));\n    }\n    if (localStorage.getItem('selectReceivingCountry')) {\n      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\n    }\n    // Fetch certificate expiration date\n    this.getBahamasCertificateInfo();\n    this.getBahamasCtsSettingInfo();\n    this.getCountries();\n    this.onCtsUploadLazyLoadEvent(undefined);\n    this.onCtsDashboardLazyLoadEvent(undefined);\n  }\n  IsShowOtherCase(year) {\n    const selectedYearAsInt = parseInt(year, 10);\n    const standardMonitoringYearAsInt = parseInt(this.standardMonitoringYear, 10);\n    this.showOtherCase = selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\n  }\n  /** Lazy load event works for \"TableIds\" grid only. */\n  onLazyLoadEventS(event) {\n    this.currentPageIndexS = 0;\n    this.informationExchangeService.getSummaryByYearByYear(this.selectedYear).subscribe(response => {\n      this.summaryExchangeList = response;\n      setTimeout(() => {\n        this.setTableDataS();\n      }, 200);\n    });\n  }\n  /** Lazy load event works for grid \"TableId\" only. */\n  onLazyLoadEvent(event) {\n    if (event) {\n      if (this.PageSize === (event.pageSize ?? 10)) {\n        this.currentPageIndex = event.pageNumber ?? 0;\n      } else {\n        //\n        // if Page size got changed through pagination control,\n        // need to reset current page index to 0.\n        //\n        this.PageSize = event.pageSize ?? 10;\n        this.currentPageIndex = 0;\n      }\n      this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n      this.input.maxResultCount = this.PageSize ?? 10;\n      if (event.isAscending === false) {\n        this.input.sorting = `${event.sortField} desc`;\n      } else {\n        this.input.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.currentPageIndex = 0;\n      this.PageSize = 10;\n      this.input.informationExchangeStatus = this.selectReportStatus;\n      this.input.year = this.selectedYear;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n    }\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      if (response) {\n        this.totalRecords = response.totalCount;\n        this.exchangeInformationResultRecords = response.items;\n      } else {\n        this.totalRecords = 0;\n        this.exchangeInformationResultRecords = [];\n      }\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  setTableDataS() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableIdS;\n    tableData.totalRecords = 1;\n    tableData.data = this.summaryExchangeList.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n          value: x.totalNoofReports\n        }, {\n          columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n          value: x.totalNoofExchangedReports\n        }, {\n          columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n          value: x.totalNoofReadyExchangedReports\n        }, {\n          columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n          value: x.totalNoofReviewReports\n        }, {\n          columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n          value: x.totalNotSentReports\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 10);\n  }\n  getExchangeReasonDescription(input) {\n    const foundStatus = ExchangeReasonDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  getInformationExchangeStatusDescription(input) {\n    const foundStatus = InformationExchangeStatusDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  base64ToUint8Array(x) {\n    const raw = atob(x);\n    var rawLength = raw.length;\n    var array = new Uint8Array(new ArrayBuffer(rawLength));\n    for (let i = 0; i < rawLength; i++) {\n      array[i] = raw.charCodeAt(i);\n    }\n    return array;\n  }\n  downloadFile(content, name) {\n    var file = new Blob([this.base64ToUint8Array(content)]);\n    var fileURL = window.URL.createObjectURL(file);\n    var element = document.createElement('a');\n    document.body.appendChild(element);\n    element.style.display = 'none';\n    element.href = fileURL;\n    element.download = name;\n    element.click();\n    element.remove();\n  }\n  GenerateXMlByType(exchangeType) {\n    this.informationExchangeService.getXMLFilesFilterByExchangeTypeByReasonAndYear(exchangeType, this.selectedYear).subscribe(result => {\n      this.onLazyLoadEvent(undefined);\n      if (result.fileName != '') {\n        this.downloadFile(result.content.toString(), result.fileName);\n      } else Swal.fire({\n        icon: 'info',\n        title: 'XML Import',\n        text: 'No data to export.',\n        allowOutsideClick: false\n      });\n    });\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableId;\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.exchangeInformationResultRecords.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n          value: this.getExchangeReasonDescription(x.exchangeReason)\n        }, {\n          columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n          value: x.raCode\n        }, {\n          columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n          value: x.entityName\n        }, {\n          columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n          value: x.companyFormationNumber\n        }, {\n          columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n          value: x.fiscalEndDate\n        }, {\n          columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n          value: x.dueDate\n        }, {\n          columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n          value: this.getInformationExchangeStatusDescription(x.informationExchangeStatus)\n        }, {\n          columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n\n          /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\n          value: x.isMigrated === false ? 'view' : ''\n        }, {\n          columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n          value: 'XML Data'\n        }, {\n          columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n          value: x.hasHistoryRecord ? 'View History' : ''\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 100);\n  }\n  onYearChange(ob) {\n    this.selectedYear = ob.value;\n    this.IsShowOtherCase(this.selectedYear);\n    // Keep the selected Year in local storage.\n    localStorage.setItem('selectedYear', ob.value);\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.onLazyLoadEventS(undefined);\n  }\n  onSearch() {\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.onLazyLoadEventS(undefined);\n  }\n  onReportChange(ob) {\n    this.selectReportStatus = Number(ob.value);\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    // Keep the selected Report Status in local storage.\n    localStorage.setItem('selectReportStatus', ob.value);\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  onLinkClick(event) {\n    const data = event.rawData;\n    if (event.columnId === \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */) {\n      //\n      // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\n      // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\n      //\n      this.router.navigate(['/es-info-exchange/exchangedetail'], {\n        //\n        // Passed \"Id\" of table dbo.InformationExchanges.\n        //\n        queryParams: {\n          id: data.id,\n          ishistory: false\n        }\n      });\n    } else if (event.columnId === \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */) {\n      //\n      // When click the \"view\" link button in the Information Exchange records grid.\n      // Route to CaActionPageComponent.ts component\n      //\n      this.router.navigate(['/action-page'], {\n        queryParams: {\n          declarationid: data.declarationId,\n          entityid: data.corporateEntityId,\n          from: 'info-exchange'\n        }\n      });\n    } else if (event.columnId === \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */) {\n      //\n      // Open dialog to show history records. informantion-exchange-history.component.ts\n      //\n      this.openInformationExchangeHistoryDialog(data.id);\n    }\n  }\n  openInformationExchangeHistoryDialog(informationExchangeId) {\n    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\n      height: '750px',\n      width: '1200px',\n      data: {\n        informationExchangeId: informationExchangeId\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      console.log('The dialog was closed', result);\n    });\n  }\n  /** Check if logon user has permission \"Generate XML\".\n   *  Work for show/hide three xml buttons.\n   */\n  checkUserPermission() {\n    let result = false;\n    // Get current logon user object.\n    const currentUser = this.configState.getOne('currentUser');\n    if (currentUser) {\n      result = this.permissionService.getGrantedPolicy(\"DashboardService.Dashboard.GenerateXML\" /* Permissions.DASHBOARD_GENERATE_XML */);\n    }\n    return result;\n  }\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\n  getFiscalYears() {\n    return this.dashboardService.getFiscalYears().pipe();\n  }\n  // CTS Upload & Transmission Dashboard Methods\n  getCTSUploadStatusDescription(input) {\n    const foundStatus = CTSUploadStatusDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  getReceivingCountryName(input) {\n    const foundCountry = this.countries.find(status => status.code2 === input);\n    if (foundCountry) return foundCountry.name;\n    return '';\n  }\n  getCountries() {\n    this.countryService.getList({\n      sorting: \"name asc\",\n      maxResultCount: 1000\n    }).subscribe(response => {\n      this.countries = response.items;\n      this.uploadHistoricalCountries = response.items;\n      // Remove code2 with empty string and null values in countries      \n      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');\n      this.uploadHistoricalCountries = this.uploadHistoricalCountries.filter(country => country.code2 && country.code2.trim() !== '');\n      // add new country ALL in countries \n      this.countries.unshift({\n        name: 'All',\n        code2: ''\n      });\n    });\n  }\n  getBahamasCertificateInfo() {\n    return this.certificateService.getBahamasCertificateInfo().subscribe({\n      next: info => {\n        this.certificateExpirationDate = info?.expiredAt || null;\n      },\n      error: () => {\n        this.certificateExpirationDate = null;\n      }\n    });\n  }\n  getBahamasCtsSettingInfo() {\n    return this.bahamasCtsSettingService.getCurrentSettings().subscribe({\n      next: info => {\n        this.bahamasCtsSetting = info || null;\n      },\n      error: () => {\n        this.bahamasCtsSetting = null;\n      }\n    });\n  }\n  openUpdateCtsSettingDialog() {\n    const dialogRef = this.dialog.open(UpdateCtsSettingDialogComponent, {\n      width: '1200px',\n      data: this.bahamasCtsSetting || null\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (!result) return;\n      const formData = new FormData();\n      if (result.file) {\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n      }\n      const isCreating = result.id == null;\n      const serviceCall = isCreating ? this.fileUploadService.createBahamasCtsSettings(result, formData) : this.fileUploadService.updateBahamasCtsSettings(result, formData);\n      const action = isCreating ? 'create' : 'update';\n      serviceCall.subscribe({\n        next: response => {\n          if (response) {\n            this.getBahamasCtsSettingInfo();\n            this.toasterService.success(`CTS Settings successfully ${action}d.`, '', {\n              life: 5000\n            });\n          } else {\n            this.toasterService.warn(`CTS Settings could not be ${action}d. Please try again later.`, null, {\n              life: 7000\n            });\n          }\n        },\n        error: error => {\n          this.toasterService.error(`An error occurred while trying to ${action} CTS Settings.`, null, {\n            life: 5000\n          });\n          console.error(`Error ${action}ing CTS Settings:`, error);\n        }\n      });\n    });\n  }\n  openUpdateCaCertificateDialog() {\n    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\n      width: '500px',\n      data: {}\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.file) {\n        const formData = new FormData();\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n        // Only call upload if file is present\n        this.fileUploadService.uploadBahamasCertificateSecure(formData, result.password).subscribe({\n          next: response => {\n            if (response) {\n              // Fetch certificate expiration date\n              this.getBahamasCertificateInfo();\n              this.toasterService.success('Bahamas Certificate successfully uploaded', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, {\n                life: 7000\n              });\n            }\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error uploading Bahamas certificate:', error);\n          }\n        });\n      }\n    });\n  }\n  // CTS Upload & Transmission Dashboard Methods\n  setCtsDashboardTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.ctsDashboardTableId;\n    tableData.totalRecords = 1;\n    tableData.data = this.ctsDashboardList.map(x => ({\n      id: x.id,\n      rawData: x,\n      cells: [{\n        columnId: 'totalNotUploaded',\n        value: x.totalNotUploaded\n      }, {\n        columnId: 'totalReadyForUpload',\n        value: x.totalReadyForUpload\n      }, {\n        columnId: 'totalFailedUpload',\n        value: x.totalFailedUpload\n      }, {\n        columnId: 'totalUploadedToCTS',\n        value: x.totalUploadedToCTS\n      }, {\n        columnId: 'totalNotEnrolled',\n        value: x.totalNotEnrolled\n      }]\n    }));\n    setTimeout(() => this.tableService.setGridData(tableData), 10);\n  }\n  setCtsUploadTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.ctsUploadTableId;\n    tableData.totalRecords = this.ctsUploadTotalRecords;\n    tableData.data = this.ctsUploadResultRecords.map(x => ({\n      id: x.id,\n      rawData: x,\n      cells: [{\n        columnId: 'exchangeReason',\n        value: this.getExchangeReasonDescription(x.exchangeReason)\n      }, {\n        columnId: 'dataPacket',\n        value: x.dataPacket\n      }, {\n        columnId: 'fileCreationDate',\n        value: x.fileCreationDate\n      }, {\n        columnId: 'receivingCountry',\n        value: this.getReceivingCountryName(x.receivingCountry)\n      }, {\n        columnId: 'ctsUploadStatus',\n        value: this.getCTSUploadStatusDescription(x.ctsUploadStatus)\n      }, {\n        columnId: 'uploadedAt',\n        value: x.uploadedAt\n      }, {\n        columnId: 'ctsTransmissionStatus',\n        value: x.ctsTransmissionStatus\n      }, {\n        columnId: 'viewExchangeRecords',\n        value: x.viewExchangeRecords === false ? 'View' : null\n      }, {\n        columnId: 'viewComments',\n        value: x.viewComments.length > 0 ? 'View Comments' : null\n      }, {\n        columnId: 'regeneratePacket',\n        value: x.allowedActions?.includes(0) ? {\n          actionType: 'RegenerateDataPacket',\n          icon: 'refresh',\n          tooltip: \"Regenerate Packet\"\n        } : null\n      }, {\n        columnId: 'ctsUpload',\n        value: x.allowedActions?.includes(1) ? {\n          actionType: 'ctsUpload',\n          icon: 'cloud_upload',\n          tooltip: \"CTS Upload\"\n        } : null\n      }, {\n        columnId: 'excludeFromCtsUpload',\n        hide: x.allowedActions?.includes(2) || x.allowedActions?.includes(3) ? false : true,\n        value: x.excludeFromCtsUpload\n      }]\n    }));\n    setTimeout(() => this.tableService.setGridData(tableData), 100);\n  }\n  onCtsDashboardLazyLoadEvent(event) {\n    this.currentPageIndexS = 0;\n    this.ctsPackageRequestService.getSummaryByYearByYear(this.ctsUploadSelectedYear).subscribe(response => {\n      this.ctsDashboardList = response;\n      setTimeout(() => {\n        this.setCtsDashboardTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadLazyLoadEvent(event) {\n    if (event) {\n      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\n        this.ctsUploadCurrentPage = event.pageNumber ?? 0;\n      } else {\n        //\n        // if Page size got changed through pagination control,\n        // need to reset current page index to 0.\n        //\n        this.ctsUploadPageSize = event.pageSize ?? 10;\n        this.ctsUploadCurrentPage = 0;\n      }\n      this.ctsUploadInput.skipCount = (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\n      if (event.isAscending === false) {\n        this.ctsUploadInput.sorting = `${event.sortField} desc`;\n      } else {\n        this.ctsUploadInput.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.ctsUploadCurrentPage = 0;\n      this.ctsUploadPageSize = 10;\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n      this.ctsUploadInput.skipCount = 0;\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    }\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      if (response) {\n        this.ctsUploadTotalRecords = response.totalCount;\n        this.ctsUploadResultRecords = response.items;\n      } else {\n        this.ctsUploadTotalRecords = 0;\n        this.ctsUploadResultRecords = [];\n      }\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadYearChange(ob) {\n    this.ctsUploadSelectedYear = ob.value;\n    localStorage.setItem('ctsUploadSelectedYear', ob.value);\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n    this.ctsUploadInput.skipCount = 0;\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      this.ctsUploadTotalRecords = response.totalCount;\n      this.ctsUploadResultRecords = response.items;\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n    this.onCtsDashboardLazyLoadEvent(undefined);\n  }\n  onCtsUploadSearch() {\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n    this.ctsUploadInput.skipCount = 0;\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      this.ctsUploadTotalRecords = response.totalCount;\n      this.ctsUploadResultRecords = response.items;\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadExchangeReasonChange(ob) {\n    this.selectExchangeReason = Number(ob.value);\n    // Keep the selected Exchange Reason in local storage.\n    localStorage.setItem('selectExchangeReason', ob.value);\n  }\n  onCtsUploadStatusChange(ob) {\n    this.selectCtsUploadStatus = Number(ob.value);\n    // Keep the selected Upload Status in local storage.\n    localStorage.setItem('selectCtsUploadStatus', ob.value);\n  }\n  onCtsUploadReceivingCountryChange(ob) {\n    this.selectReceivingCountry = ob.value;\n    // Keep the selected Receiving Country in local storage.\n    localStorage.setItem('selectReceivingCountry', ob.value);\n  }\n  onCtsUploadLinkClick(event) {\n    if (event.columnId === 'viewExchangeRecords') {\n      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {\n        width: '1200px',\n        data: {\n          row: event.rawData\n        }\n      });\n    } else if (event.columnId === 'viewComments') {\n      this.dialog.open(ViewCommentDialogComponent, {\n        width: '1200px',\n        data: {\n          row: event.rawData\n        }\n      });\n    }\n  }\n  onCtsUploadActionClick(event) {\n    if (event.action === 'RegenerateDataPacket') {\n      const dialogRef = this.dialog.open(RegeneratePacketDialogComponent, {\n        width: '500px',\n        data: {\n          row: event?.data?.rawData\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result && result.comment) {\n          this.ctsPackageRequestService.regeneratePackage(event.data?.rawData?.ctsPackageId, result.comment).subscribe({\n            next: response => {\n              if (response.success) {\n                this.toasterService.success(response.message || 'Regenerate Package successfully requested', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn(response.message || 'Regenerate Package couldn’t be requested. Please try again later', null, {\n                  life: 7000\n                });\n              }\n              this.onCtsUploadLazyLoadEvent(undefined);\n              this.onCtsDashboardLazyLoadEvent(undefined);\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error requesting Regenerate Package:', error);\n            }\n          });\n        }\n      });\n    } else if (event.action === 'ctsUpload') {\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: \"CTS Upload\",\n        text: \"Are you sure you would like to proceed?\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.ctsPackageRequestService.uploadToCts(event.data?.rawData?.ctsPackageId).subscribe({\n            next: response => {\n              if (response.success) {\n                this.toasterService.success(response.message || 'CTS Upload successfully completed', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be completed. Please try again later', null, {\n                  life: 7000\n                });\n              }\n              this.onCtsUploadLazyLoadEvent(undefined);\n              this.onCtsDashboardLazyLoadEvent(undefined);\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error requesting CTS Upload:', error);\n            }\n          });\n        }\n      });\n    }\n  }\n  onCheckboxClick(event) {\n    // Commented out: console.log('Checkbox clicked:', event.selectedRows[0]?.rawData?.ctsPackageId);\n    // Commented out: console.log('Checkbox clicked:', event.rowId);\n    if (event.isChecked) {\n      this.ctsPackageRequestService.markAsDoNotUpload(event.rowId).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toasterService.success(response.message || 'Exclude packet from uploading successfully completed', '', {\n              life: 5000\n            });\n          } else {\n            this.toasterService.warn(response.message || 'Exclude packet from uploading  couldn’t be completed. Please try again later', null, {\n              life: 7000\n            });\n          }\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n        },\n        error: error => {\n          console.error('Error marking as Do Not Upload:', error);\n        }\n      });\n    } else {\n      this.ctsPackageRequestService.unMarkAsDoNotUpload(event.rowId).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toasterService.success(response.message || 'Include packet for uploading successfully completed', '', {\n              life: 5000\n            });\n          } else {\n            this.toasterService.warn(response.message || 'Include packet for uploading couldn’t be completed. Please try again later', null, {\n              life: 7000\n            });\n          }\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n        },\n        error: error => {\n          console.error('Error unmarking as Do Not Upload:', error);\n        }\n      });\n    }\n  }\n  // Add this method to open the Upload Historical XML dialog\n  openUploadHistoricalXmlDialog() {\n    const dialogRef = this.dialog.open(UploadHistoricalXmlDialogComponent, {\n      width: '500px',\n      data: {\n        receivingCountries: this.uploadHistoricalCountries || [],\n        fiscalYears: this.year || []\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.success) {\n        this.onCtsUploadLazyLoadEvent(undefined);\n        this.onCtsDashboardLazyLoadEvent(undefined);\n      }\n    });\n  }\n  openCtsUploadDialog() {\n    const dialogRef = this.dialog.open(UploadCtsDialogComponent, {\n      width: '500px',\n      data: {\n        fiscalYears: this.year || []\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.fiscalYear) {\n        this.ctsPackageRequestService.batchUploadToCts(result.fiscalYear, result.comment).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'CTS Upload successfully requested', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error requesting CTS Upload:', error);\n          }\n        });\n      }\n    });\n  }\n  openDecryptDialog() {\n    const dialogRef = this.dialog.open(DecryptDataPacketDialogComponent, {\n      width: '500px'\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.file) {\n        const formData = new FormData();\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n        // Only call upload if file is present        \n        this.fileUploadService.unpackCtsPackage(formData).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'Decrypt Data Packet successfully completed', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'Decrypt Data Packet couldn’t be completed. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error decrypting data packet:', error);\n          }\n        });\n      }\n    });\n  }\n  refreshAllTransmissionStatus() {\n    this.sweetAlert.fireDialog({\n      action: \"submit\",\n      title: \"Refresh All Transmission Status\",\n      text: \"Are you sure you would like to proceed?\",\n      type: \"confirm\"\n    }, confirm => {\n      if (confirm) {\n        this.ctsPackageRequestService.refreshTransmissionStatus().subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'Refresh All Transmission Status successfully requested', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'Refresh All Transmission Status couldn’t be requested. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error requesting Refresh All Transmission Status:', error);\n          }\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function InformationExchangeMainComponent_Factory(t) {\n      return new (t || InformationExchangeMainComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.InformationExchangeService), i0.ɵɵdirectiveInject(i3.InformationExchangeDetailsService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i5.CADashboardContorllerService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.CertificateService), i0.ɵɵdirectiveInject(i8.FileUploadService), i0.ɵɵdirectiveInject(i9.ToasterService), i0.ɵɵdirectiveInject(i10.CountryService), i0.ɵɵdirectiveInject(i11.CtsPackageRequestService), i0.ɵɵdirectiveInject(i12.SweetAlertService), i0.ɵɵdirectiveInject(i13.BahamasCtsSettingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InformationExchangeMainComponent,\n      selectors: [[\"app-information-exchange-main\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 91,\n      vars: 62,\n      consts: [[\"label\", \"Info Exchange Readiness\"], [1, \"top-action-row-exchange\", \"row\"], [1, \"table-container\"], [\"scrollHeight\", \"100%\", \"defaultSortColumnId\", \"uploadedDateTime\", 3, \"onLazyLoad\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\", \"pageSizeOptions\"], [1, \"top-action-column-exchange\", \"row\", \"justify-content-end\"], [1, \"col-md-auto\"], [\"class\", \"certificate-text\", 4, \"ngIf\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", 3, \"click\", 4, \"ngIf\"], [1, \"top-action-column-exchange\", \"row\"], [1, \"col-md-4\", \"col-sm-12\", \"margin-top\"], [1, \"outside-mat-label\"], [1, \"form-field-reduce-length\"], [\"placeholder\", \"Financial Period End\", 3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\", \"col-sm-12\", \"margin-top\"], [\"placeholder\", \"Report Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"matInput\", \"\", \"placeholder\", \"File Name\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\", \"col-sm-12\", \"margin-top\", \"search-button-column\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"search-button\", 3, \"click\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"label\", \"CTS Upload & Transmission\"], [1, \"row\", \"top-action-row-exchange\"], [\"scrollHeight\", \"auto\", 3, \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [1, \"top-action-column-exchange\", \"row\", \"align-items-end\", \"filters-row\"], [1, \"col-md-3\", \"col-sm-6\", \"margin-top\"], [\"placeholder\", \"Exchange Reason\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"Receiving Country\", 3, \"valueChange\", \"selectionChange\", \"value\"], [1, \"col-md-3\", \"col-sm-6\", \"margin-top\", \"d-flex\", \"align-items-end\"], [2, \"width\", \"100%\"], [\"placeholder\", \"CTS Upload Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"search-button\", \"float-end\", 3, \"click\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"onActionClick\", \"onCheckboxClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [1, \"certificate-text\"], [3, \"value\"]],\n      template: function InformationExchangeMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-tab-group\")(1, \"mat-tab\", 0)(2, \"div\", 1)(3, \"div\", 2)(4, \"bdo-table\", 3);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener($event) {\n            return ctx.onLazyLoadEventS($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵtemplate(7, InformationExchangeMainComponent_span_7_Template, 3, 4, \"span\", 6)(8, InformationExchangeMainComponent_button_8_Template, 2, 0, \"button\", 7)(9, InformationExchangeMainComponent_button_9_Template, 2, 0, \"button\", 7)(10, InformationExchangeMainComponent_button_10_Template, 2, 0, \"button\", 7)(11, InformationExchangeMainComponent_button_11_Template, 2, 0, \"button\", 7)(12, InformationExchangeMainComponent_button_12_Template, 2, 0, \"button\", 7)(13, InformationExchangeMainComponent_button_13_Template, 2, 0, \"button\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"mat-label\", 10);\n          i0.ɵɵtext(17, \"Financial Period End\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 11)(19, \"mat-select\", 12);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_19_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedYear, $event) || (ctx.selectedYear = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_19_listener($event) {\n            return ctx.onYearChange($event);\n          });\n          i0.ɵɵtemplate(20, InformationExchangeMainComponent_mat_option_20_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"mat-label\", 10);\n          i0.ɵɵtext(23, \"Report Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-form-field\", 11)(25, \"mat-select\", 15);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_25_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectReportStatus, $event) || (ctx.selectReportStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_25_listener($event) {\n            return ctx.onReportChange($event);\n          });\n          i0.ɵɵtemplate(26, InformationExchangeMainComponent_mat_option_26_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"mat-label\", 10);\n          i0.ɵɵtext(29, \"Entity Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 11)(31, \"input\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function InformationExchangeMainComponent_Template_input_ngModelChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchEntityName, $event) || (ctx.searchEntityName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 17)(33, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_33_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵtext(34, \" Search \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 1)(36, \"div\", 2)(37, \"bdo-table\", 19);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_37_listener($event) {\n            return ctx.onLazyLoadEvent($event);\n          })(\"onLinkClick\", function InformationExchangeMainComponent_Template_bdo_table_onLinkClick_37_listener($event) {\n            return ctx.onLinkClick($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"mat-tab\", 20)(39, \"div\", 21)(40, \"div\", 2);\n          i0.ɵɵelement(41, \"bdo-table\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 4)(43, \"div\", 5)(44, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_44_listener() {\n            return ctx.openDecryptDialog();\n          });\n          i0.ɵɵelementStart(45, \"mat-icon\");\n          i0.ɵɵtext(46, \"lock_open\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" Decrypt Received Data Packet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_48_listener() {\n            return ctx.openUploadHistoricalXmlDialog();\n          });\n          i0.ɵɵelementStart(49, \"mat-icon\");\n          i0.ɵɵtext(50, \"cloud_upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(51, \" Upload Historical XML\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_52_listener() {\n            return ctx.openCtsUploadDialog();\n          });\n          i0.ɵɵelementStart(53, \"mat-icon\");\n          i0.ɵɵtext(54, \"cloud_upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" CTS Upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_56_listener() {\n            return ctx.refreshAllTransmissionStatus();\n          });\n          i0.ɵɵelementStart(57, \"mat-icon\");\n          i0.ɵɵtext(58, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(59, \" Refresh Status\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 24)(61, \"div\", 25)(62, \"mat-label\", 10);\n          i0.ɵɵtext(63, \"Financial Period End\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"mat-form-field\", 11)(65, \"mat-select\", 12);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_65_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.ctsUploadSelectedYear, $event) || (ctx.ctsUploadSelectedYear = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_65_listener($event) {\n            return ctx.onCtsUploadYearChange($event);\n          });\n          i0.ɵɵtemplate(66, InformationExchangeMainComponent_mat_option_66_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(67, \"div\", 25)(68, \"mat-label\", 10);\n          i0.ɵɵtext(69, \"Exchange Reason\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"mat-form-field\", 11)(71, \"mat-select\", 26);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_71_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectExchangeReason, $event) || (ctx.selectExchangeReason = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_71_listener($event) {\n            return ctx.onCtsUploadExchangeReasonChange($event);\n          });\n          i0.ɵɵtemplate(72, InformationExchangeMainComponent_mat_option_72_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 25)(74, \"mat-label\", 10);\n          i0.ɵɵtext(75, \"Receiving Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"mat-form-field\", 11)(77, \"mat-select\", 27);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_77_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectReceivingCountry, $event) || (ctx.selectReceivingCountry = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_77_listener($event) {\n            return ctx.onCtsUploadReceivingCountryChange($event);\n          });\n          i0.ɵɵtemplate(78, InformationExchangeMainComponent_mat_option_78_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\", 28)(80, \"div\", 29)(81, \"mat-label\", 10);\n          i0.ɵɵtext(82, \"CTS Upload Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"mat-form-field\", 11)(84, \"mat-select\", 30);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_84_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectCtsUploadStatus, $event) || (ctx.selectCtsUploadStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_84_listener($event) {\n            return ctx.onCtsUploadStatusChange($event);\n          });\n          i0.ɵɵtemplate(85, InformationExchangeMainComponent_mat_option_85_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_86_listener() {\n            return ctx.onCtsUploadSearch();\n          });\n          i0.ɵɵtext(87, \"Search\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(88, \"div\", 1)(89, \"div\", 2)(90, \"bdo-table\", 32);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_90_listener($event) {\n            return ctx.onCtsUploadLazyLoadEvent($event);\n          })(\"onLinkClick\", function InformationExchangeMainComponent_Template_bdo_table_onLinkClick_90_listener($event) {\n            return ctx.onCtsUploadLinkClick($event);\n          })(\"onActionClick\", function InformationExchangeMainComponent_Template_bdo_table_onActionClick_90_listener($event) {\n            return ctx.onCtsUploadActionClick($event);\n          })(\"onCheckboxClick\", function InformationExchangeMainComponent_Template_bdo_table_onCheckboxClick_90_listener($event) {\n            return ctx.onCheckboxClick($event);\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"id\", ctx.TableIdS)(\"columns\", ctx.summaryExchangeColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndexS)(\"pageSize\", ctx.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", true)(\"lazyLoad\", true)(\"pageSizeOptions\", i0.ɵɵpureFunction0(59, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.certificateExpirationDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCaSystemAdmin);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCaSystemAdmin);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton && ctx.showOtherCase);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedYear);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.year);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectReportStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.informationExchangedDic);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchEntityName);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.exchangeResultColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSize\", ctx.PageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(60, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"id\", ctx.ctsDashboardTableId)(\"columns\", ctx.ctsDashboardColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndexS)(\"pageSize\", ctx.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", false)(\"lazyLoad\", false);\n          i0.ɵɵadvance(24);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.ctsUploadSelectedYear);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.year);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectExchangeReason);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.ctsUploadExchangeReasonDic);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectReceivingCountry);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectCtsUploadStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.ctsUploadStatusDic);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"id\", ctx.ctsUploadTableId)(\"columns\", ctx.ctsUploadColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.ctsUploadCurrentPage)(\"pageSize\", ctx.ctsUploadPageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(61, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n        }\n      },\n      dependencies: [i14.DefaultValueAccessor, i14.NgControlStatus, i15.MatInput, i16.MatFormField, i16.MatLabel, i17.MatIcon, i18.MatButton, i19.MatSelect, i20.MatOption, i21.MatTab, i21.MatTabGroup, i22.BdoTableComponent, i23.NgForOf, i23.NgIf, i14.NgModel, i23.DatePipe],\n      styles: [\".search-title[_ngcontent-%COMP%] {\\n  font-size: 2em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.display-flex-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100% !important;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  z-index: 0;\\n  position: relative;\\n  margin-right: 0.5em;\\n  min-height: 100% !important;\\n  max-width: 100% !important;\\n}\\n\\n.display-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.top-action-row-exchange[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  padding-top: 10px;\\n}\\n\\n.top-action-row-exchange-noalign[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n\\n.top-action-column-exchange[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: flex-start;\\n  margin-top: 1em;\\n}\\n\\n.margin-top[_ngcontent-%COMP%] {\\n  margin-top: 1.5em;\\n}\\n\\n.margin-left-push[_ngcontent-%COMP%] {\\n  margin-left: 30em;\\n}\\n\\n.margin-left[_ngcontent-%COMP%] {\\n  margin-left: 2em;\\n}\\n\\n.top-action-row-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.upload-import-container[_ngcontent-%COMP%] {\\n  display: flex; \\n\\n}\\n\\n.outside-mat-label[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n  margin-right: 10px;\\n  margin-top: 2em;\\n}\\n\\n.form-field-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.mat-form-field[_ngcontent-%COMP%] {\\n  margin-right: 10px; \\n\\n}\\n\\n.search-button-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  vertical-align: middle;\\n  height: 100px !important;\\n}\\n\\n@media (max-width: 1750px) {\\n  .outside-mat-label[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n    margin-right: 10px;\\n    margin-top: 0em;\\n  }\\n  .search-button[_ngcontent-%COMP%] {\\n    margin-top: 1.9em;\\n  }\\n}\\n@media (max-width: 900px) {\\n  .outside-mat-label[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n    margin-right: 10px;\\n  }\\n  .search-button[_ngcontent-%COMP%] {\\n    margin-top: 0em;\\n  }\\n}\\n.filters-row[_ngcontent-%COMP%] {\\n  margin-top: 1.5em;\\n  margin-bottom: 1em;\\n}\\n.filters-row[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filters-row[_ngcontent-%COMP%]   .search-button[_ngcontent-%COMP%] {\\n  float: right;\\n  min-width: 100px;\\n}\\n@media (max-width: 900px) {\\n  .filters-row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], .filters-row[_ngcontent-%COMP%]   .col-sm-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1em;\\n  }\\n  .filters-row[_ngcontent-%COMP%]   .search-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1em;\\n    float: none;\\n  }\\n}\\n\\n.certificate-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  margin-right: 15px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["InformationExchangeStatus", "AppComponentBase", "BdoTableColumnType", "BdoTableData", "ExchangeReasonDic", "InformationExchangeStatusDic", "CTSUploadStatusDic", "CTSUploadExchangeReasonDic", "<PERSON><PERSON>", "InformationExchangeHistoryComponent", "UpdateCaCertificateDialogComponent", "ViewAssociatedExchangeRecordsComponent", "UploadHistoricalXmlDialogComponent", "DecryptDataPacketDialogComponent", "RegeneratePacketDialogComponent", "ViewCommentDialogComponent", "CTSUploadStatus", "UploadCtsDialogComponent", "UpdateCtsSettingDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r0", "certificateExpirationDate", "ɵɵlistener", "InformationExchangeMainComponent_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "openUpdateCtsSettingDialog", "InformationExchangeMainComponent_button_9_Template_button_click_0_listener", "_r3", "openUpdateCaCertificateDialog", "InformationExchangeMainComponent_button_10_Template_button_click_0_listener", "_r4", "GenerateXMlByType", "InformationExchangeMainComponent_button_11_Template_button_click_0_listener", "_r5", "InformationExchangeMainComponent_button_12_Template_button_click_0_listener", "_r6", "InformationExchangeMainComponent_button_13_Template_button_click_0_listener", "_r7", "ɵɵproperty", "element_r8", "element_r9", "value", "description", "element_r10", "element_r11", "element_r12", "code2", "name", "element_r13", "InformationExchangeMainComponent", "constructor", "injector", "router", "informationExchangeService", "informationExchangeDetailService", "permissionService", "dashboardService", "dialog", "certificateService", "fileUploadService", "toasterService", "countryService", "ctsPackageRequestService", "<PERSON><PERSON><PERSON><PERSON>", "bahamasCtsSettingService", "input", "maxResultCount", "skip<PERSON><PERSON>nt", "sorting", "informationExchangeStatus", "None", "entityName", "year", "TableId", "currentPageIndex", "selected<PERSON>ear", "Date", "getFullYear", "toString", "exchangeResultColumns", "columnId", "type", "String", "min<PERSON><PERSON><PERSON>", "frozenLeft", "isSortable", "columnName", "Link", "PageSize", "exchangeInformationResultRecords", "selectReportStatus", "informationExchangedDic", "TableIdS", "currentPageIndexS", "summaryExchangeColumns", "Number", "PageSizeS", "totalRecords", "showButton", "showOtherCase", "currnetYear", "bahamasCtsSetting", "isCaSystemAdmin", "ctsDashboardColumns", "ctsDashboardList", "id", "totalNotUploaded", "totalReadyForUpload", "totalFailedUpload", "totalUploadedToCTS", "totalNotEnrolled", "ctsUploadColumns", "SingleActionButton", "Checkbox", "ctsUploadExchangeReasonDic", "ctsUploadStatusDic", "ctsUploadSelectedYear", "ctsUploadResultRecords", "selectExchangeReason", "selectCtsUploadStatus", "NotStarted", "selectReceivingCountry", "ctsDashboardTableId", "ctsUploadTableId", "ctsUploadPageSize", "ctsUploadCurrentPage", "ctsUploadTotalRecords", "ctsUploadInput", "ctsUploadStatus", "exchangeReason", "financialEndYear", "receivingCountry", "summaryExchangeList", "totalNoReport", "totalNoReportSent", "totalNoReportRExchange", "totalNoReportRReview", "totalNoReportNotSent", "ngOnInit", "getFiscalYears", "subscribe", "response", "length", "for<PERSON>ach", "element", "push", "localStorage", "getItem", "standardMonitoringFromYear", "standardMonitoringYear", "IsShowOtherCase", "onLazyLoadEvent", "undefined", "onLazyLoadEventS", "checkUserPermission", "currentUser", "configState", "getOne", "roles", "includes", "getBahamasCertificateInfo", "getBahamasCtsSettingInfo", "getCountries", "onCtsUploadLazyLoadEvent", "onCtsDashboardLazyLoadEvent", "selectedYearAsInt", "parseInt", "standardMonitoringYearAsInt", "event", "getSummaryByYearByYear", "setTimeout", "setTableDataS", "pageSize", "pageNumber", "isAscending", "sortField", "getALLInformationExchangeByInput", "totalCount", "items", "setTableData", "tableData", "resetToFirstPage", "tableId", "data", "map", "x", "rawData", "cells", "totalNoofReports", "totalNoofExchangedReports", "totalNoofReadyExchangedReports", "totalNoofReviewReports", "totalNotSentReports", "tableService", "setGridData", "getExchangeReasonDescription", "found<PERSON><PERSON><PERSON>", "find", "status", "getInformationExchangeStatusDescription", "base64ToUint8Array", "raw", "atob", "<PERSON><PERSON><PERSON><PERSON>", "array", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "charCodeAt", "downloadFile", "content", "file", "Blob", "fileURL", "window", "URL", "createObjectURL", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "style", "display", "href", "download", "click", "remove", "exchangeType", "getXMLFilesFilterByExchangeTypeByReasonAndYear", "result", "fileName", "fire", "icon", "title", "text", "allowOutsideClick", "raCode", "companyFormationNumber", "fiscalEndDate", "dueDate", "isMigrated", "hasHistoryRecord", "onYearChange", "ob", "setItem", "searchEntityName", "onSearch", "onReportChange", "onLinkClick", "navigate", "queryParams", "ishistory", "declarationid", "declarationId", "entityid", "corporateEntityId", "from", "openInformationExchangeHistoryDialog", "informationExchangeId", "dialogRef", "open", "height", "width", "afterClosed", "console", "log", "getGrantedPolicy", "pipe", "getCTSUploadStatusDescription", "getReceivingCountryName", "foundCountry", "countries", "getList", "uploadHistoricalCountries", "filter", "country", "trim", "unshift", "next", "info", "expiredAt", "error", "getCurrentSettings", "formData", "FormData", "append", "isCreating", "serviceCall", "createBahamasCtsSettings", "updateBahamasCtsSettings", "action", "success", "life", "warn", "uploadBahamasCertificateSecure", "password", "setCtsDashboardTableData", "setCtsUploadTableData", "dataPacket", "fileCreationDate", "uploadedAt", "ctsTransmissionStatus", "viewExchangeRecords", "viewComments", "allowedActions", "actionType", "tooltip", "hide", "excludeFromCtsUpload", "getAllCtsPackageRequestByInput", "onCtsUploadYearChange", "onCtsUploadSearch", "onCtsUploadExchangeReasonChange", "onCtsUploadStatusChange", "onCtsUploadReceivingCountryChange", "onCtsUploadLinkClick", "row", "onCtsUploadActionClick", "comment", "regeneratePackage", "ctsPackageId", "message", "fireDialog", "confirm", "uploadToCts", "onCheckboxClick", "isChecked", "markAsDoNotUpload", "rowId", "unMarkAsDoNotUpload", "openUploadHistoricalXmlDialog", "receivingCountries", "fiscalYears", "openCtsUploadDialog", "fiscalYear", "batchUploadToCts", "openDecryptDialog", "unpackCtsPackage", "refreshAllTransmissionStatus", "refreshTransmissionStatus", "ɵɵdirectiveInject", "Injector", "i1", "Router", "i2", "InformationExchangeService", "i3", "InformationExchangeDetailsService", "i4", "PermissionService", "i5", "CADashboardContorllerService", "i6", "MatDialog", "i7", "CertificateService", "i8", "FileUploadService", "i9", "ToasterService", "i10", "CountryService", "i11", "CtsPackageRequestService", "i12", "SweetAlertService", "i13", "BahamasCtsSettingService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "InformationExchangeMainComponent_Template", "rf", "ctx", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener", "$event", "ɵɵtemplate", "InformationExchangeMainComponent_span_7_Template", "InformationExchangeMainComponent_button_8_Template", "InformationExchangeMainComponent_button_9_Template", "InformationExchangeMainComponent_button_10_Template", "InformationExchangeMainComponent_button_11_Template", "InformationExchangeMainComponent_button_12_Template", "InformationExchangeMainComponent_button_13_Template", "ɵɵtwoWayListener", "InformationExchangeMainComponent_Template_mat_select_valueChange_19_listener", "ɵɵtwoWayBindingSet", "InformationExchangeMainComponent_Template_mat_select_selectionChange_19_listener", "InformationExchangeMainComponent_mat_option_20_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_25_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_25_listener", "InformationExchangeMainComponent_mat_option_26_Template", "InformationExchangeMainComponent_Template_input_ngModelChange_31_listener", "InformationExchangeMainComponent_Template_button_click_33_listener", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_37_listener", "InformationExchangeMainComponent_Template_bdo_table_onLinkClick_37_listener", "ɵɵelement", "InformationExchangeMainComponent_Template_button_click_44_listener", "InformationExchangeMainComponent_Template_button_click_48_listener", "InformationExchangeMainComponent_Template_button_click_52_listener", "InformationExchangeMainComponent_Template_button_click_56_listener", "InformationExchangeMainComponent_Template_mat_select_valueChange_65_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_65_listener", "InformationExchangeMainComponent_mat_option_66_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_71_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_71_listener", "InformationExchangeMainComponent_mat_option_72_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_77_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_77_listener", "InformationExchangeMainComponent_mat_option_78_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_84_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_84_listener", "InformationExchangeMainComponent_mat_option_85_Template", "InformationExchangeMainComponent_Template_button_click_86_listener", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_90_listener", "InformationExchangeMainComponent_Template_bdo_table_onLinkClick_90_listener", "InformationExchangeMainComponent_Template_bdo_table_onActionClick_90_listener", "InformationExchangeMainComponent_Template_bdo_table_onCheckboxClick_90_listener", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\information-exchange-main\\information-exchange-main.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\information-exchange-main\\information-exchange-main.component.html"], "sourcesContent": ["import { Component, Injector, OnDestroy, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { InformationExchangeService } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers';\r\nimport {\r\n  GetInformationExchangeDto,\r\n  InformationExchangeDto,\r\n} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/information-exchange-dashboard/dto';\r\nimport {\r\n  ExchangeReason,\r\n  InformationExchangeStatus,\r\n} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\r\nimport { AppComponentBase } from '../../../../app-component-base';\r\nimport {\r\n  BdoTableCellLinkClickEvent,\r\n  BdoTableCheckboxClickEvent,\r\n  BdoTableColumnDefinition,\r\n  BdoTableColumnType,\r\n  BdoTableData,\r\n  BdoTableRowActionClickEvent,\r\n} from '../../../../shared/components/bdo-table/bdo-table.model';\r\nimport {\r\n  ExchangeReasonDic,\r\n  ExchangeSummaryTableColumns,\r\n  InformationExchangeStatusDic,\r\n  InformationExchangeTableColumns,\r\n  Permissions,\r\n  CTSUploadStatusDic,\r\n  CTSUploadExchangeReasonDic,\r\n} from '../../../../shared/constants';\r\nimport Swal from 'sweetalert2';\r\nimport { CurrentUserDto, PermissionService } from '@abp/ng.core';\r\nimport { InformationExchangeDetailsService } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\r\nimport { CADashboardContorllerService } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers';\r\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\r\nimport { CertificateService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service';\r\nimport { FileUploadService } from '../../../../shared/services/upload-file.service';\r\nimport { ToasterService } from '@abp/ng.theme.shared';\r\nimport { CountryService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { CtsPackageRequestService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service';\r\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\r\nimport { UploadHistoricalXmlDialogComponent } from '../upload-historical-xml-dialog/upload-historical-xml-dialog.component';\r\nimport { DecryptDataPacketDialogComponent } from '../decrypt-data-packet-dialog/decrypt-data-packet-dialog.component';\r\nimport { RegeneratePacketDialogComponent } from '../regenerate-packet-dialog/regenerate-packet-dialog.component';\r\nimport { ViewCommentDialogComponent } from '../view-comment-dialog/view-comment-dialog.component';\r\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/enums/ctsupload-status.enum';\r\nimport { GetCtsPackageRequestDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/application/contracts/cts-package-requests';\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\nimport { UploadCtsDialogComponent } from '../upload-cts-dialog/upload-cts-dialog.component';\r\nimport { UpdateCtsSettingDialogComponent } from '../update-cts-setting-dialog/update-cts-setting-dialog.component';\r\nimport { BahamasCtsSettingDto, BahamasCtsSettingService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';\r\n\r\n@Component({\r\n  selector: 'app-information-exchange-main',\r\n  templateUrl: './information-exchange-main.component.html',\r\n  styleUrls: ['./information-exchange-main.component.scss']\r\n})\r\nexport class InformationExchangeMainComponent\r\n  extends AppComponentBase\r\n  implements OnInit {\r\n  input: GetInformationExchangeDto = {\r\n    maxResultCount: 10,\r\n    skipCount: 0,\r\n    sorting: 'ExchangeReason asc',\r\n    informationExchangeStatus: InformationExchangeStatus.None,\r\n    entityName: '',\r\n    year: '',\r\n  };\r\n  TableId = 'information_ex-results';\r\n  /* Work for pagination. Default value = 0, it is rendering first page by default. */\r\n  currentPageIndex = 0;\r\n  InformationExchange: InformationExchangeDto[];\r\n  /** It is string year number array. */\r\n  year = [];\r\n  /** Selected year from Financial Period End Years dropdown, default is current year. */\r\n  selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\r\n  exchangeResultColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: InformationExchangeTableColumns.EXCHANGE_REASON,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Exchange Reason',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.RA_CODE,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'RA Name',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.ENTITY_NAME,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 100,\r\n      isSortable: true,\r\n      columnName: 'Entity Name',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.INCROP_NUMBER,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Incop#/Formation#',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Financial Period End Date',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.DUE_DATE,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Due Date',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Information Exchange Status',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.VIEW_DECLARATION,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'View Declaration',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.XML_DATA,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'XML Data',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.VIEW_HISTORY,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'View History',\r\n    },\r\n  ];\r\n  /** Page size setting for \"TableId\" grid */\r\n  PageSize = 10;\r\n  exchangeInformationResultRecords = [];\r\n  selectReportStatus: number = InformationExchangeStatus.None;\r\n  searchEntityName: any;\r\n  informationExchangedDic: any = InformationExchangeStatusDic;\r\n  TableIdS = 'information_ex_summary';\r\n  currentPageIndexS = 0;\r\n  summaryExchangeColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Data Packet Generated',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Ready for Exchange',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 200,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName:\r\n        'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Not Started',\r\n    },\r\n  ];\r\n  /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\r\n  PageSizeS = 10;\r\n  totalRecords = 0;\r\n  /** Note: Only logon user with permission \"Generate XML\"\r\n   * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\r\n  showButton = true;\r\n  showOtherCase = true;\r\n  standardMonitoringYear: any;\r\n  /* Default current year value as string. */\r\n  currnetYear = new Date().getFullYear().toString();\r\n\r\n  certificateExpirationDate: string | null = null;\r\n  bahamasCtsSetting: BahamasCtsSettingDto | null = null;\r\n  isCaSystemAdmin: boolean = false;\r\n\r\n  // Dashboard columns for CTS Upload & Transmission\r\n  ctsDashboardColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: 'totalNotUploaded',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Do Not Upload',\r\n    },\r\n    {\r\n      columnId: 'totalReadyForUpload',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Ready For Upload',\r\n    },\r\n    {\r\n      columnId: 'totalFailedUpload',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Failed in Upload',\r\n    },\r\n    {\r\n      columnId: 'totalUploadedToCTS',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Uploaded to CTS',\r\n    },\r\n    {\r\n      columnId: 'totalNotEnrolled',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Receiving Country Not Enrolled',\r\n    },\r\n  ];\r\n\r\n  // Dashboard data for CTS Upload & Transmission\r\n  ctsDashboardList: any[] = [\r\n    {\r\n      id: 1,\r\n      totalNotUploaded: 0,\r\n      totalReadyForUpload: 1,\r\n      totalFailedUpload: 1,\r\n      totalUploadedToCTS: 2,\r\n      totalNotEnrolled: 2,\r\n    },\r\n  ];\r\n\r\n  // Grid columns for CTS Upload & Transmission\r\n  ctsUploadColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: 'exchangeReason',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Exchange Reason',\r\n    },\r\n    {\r\n      columnId: 'dataPacket',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'Data Packet',\r\n    },\r\n    {\r\n      columnId: 'fileCreationDate',\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 120,\r\n      isSortable: true,\r\n      columnName: 'File Creation Date',\r\n    },\r\n    {\r\n      columnId: 'receivingCountry',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'Receiving Country',\r\n    },\r\n    {\r\n      columnId: 'ctsUploadStatus',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'CTS Upload Status',\r\n    },\r\n    {\r\n      columnId: 'uploadedAt',\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 120,\r\n      isSortable: true,\r\n      columnName: 'Uploaded At',\r\n    },\r\n    {\r\n      columnId: 'ctsTransmissionStatus',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'CTS Transmission Status',\r\n    },\r\n    {\r\n      columnId: 'viewExchangeRecords',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'View Exchange Records',\r\n    },\r\n    {\r\n      columnId: 'viewComments',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'View Comments',\r\n    },\r\n    {\r\n      columnId: 'regeneratePacket',\r\n      type: BdoTableColumnType.SingleActionButton,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'Regenerate Packet',\r\n    },\r\n    {\r\n      columnId: 'ctsUpload',\r\n      type: BdoTableColumnType.SingleActionButton,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'CTS Upload',\r\n    },\r\n    {\r\n      columnId: 'excludeFromCtsUpload',\r\n      type: BdoTableColumnType.Checkbox,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'Exclude From CTS Upload',\r\n    },\r\n  ];\r\n\r\n  // CTS Upload & Transmission Dashboard\r\n  ctsUploadExchangeReasonDic: any = CTSUploadExchangeReasonDic;\r\n  ctsUploadStatusDic: any = CTSUploadStatusDic;\r\n  countries: any;\r\n  uploadHistoricalCountries: any;\r\n  ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \r\n  ctsUploadResultRecords = [];\r\n  selectExchangeReason: number = -1;\r\n  selectCtsUploadStatus: number = CTSUploadStatus.NotStarted;\r\n  selectReceivingCountry: string = '';\r\n\r\n  // Table IDs and page settings\r\n  ctsDashboardTableId = 'cts_dashboard';\r\n  ctsUploadTableId = 'cts_upload_grid';\r\n  ctsUploadPageSize = 10;\r\n  ctsUploadCurrentPage = 0;\r\n  ctsUploadTotalRecords = 0;\r\n  ctsUploadInput: GetCtsPackageRequestDto = {\r\n    maxResultCount: 10,\r\n    skipCount: 0,\r\n    sorting: 'ExchangeReason asc',\r\n    ctsUploadStatus: null,\r\n    exchangeReason: null,\r\n    financialEndYear: '',\r\n    receivingCountry: '',\r\n  };\r\n\r\n  constructor(\r\n    injector: Injector,\r\n    private router: Router,\r\n    private informationExchangeService: InformationExchangeService,\r\n    private informationExchangeDetailService: InformationExchangeDetailsService,\r\n    private permissionService: PermissionService,\r\n    private dashboardService: CADashboardContorllerService,\r\n    public dialog: MatDialog,\r\n    private certificateService: CertificateService,\r\n    private fileUploadService: FileUploadService,\r\n    private toasterService: ToasterService,\r\n    private countryService: CountryService,\r\n    private ctsPackageRequestService: CtsPackageRequestService,\r\n    private sweetAlert: SweetAlertService,\r\n    private bahamasCtsSettingService: BahamasCtsSettingService,\r\n  ) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getFiscalYears().subscribe((response) => {\r\n      if (response && response.length > 0) {\r\n        this.year = [];\r\n        response.forEach((element) => {\r\n          this.year.push(element.toString());\r\n        });\r\n      };\r\n    });\r\n\r\n    if (localStorage.getItem('selectedYear')) {\r\n      this.selectedYear =\r\n        localStorage.getItem('selectedYear') ?? this.currnetYear;\r\n    }\r\n\r\n    if (localStorage.getItem('selectReportStatus')) {\r\n      this.selectReportStatus = Number(\r\n        localStorage.getItem('selectReportStatus')\r\n      );\r\n    }\r\n    this.informationExchangeDetailService\r\n      .standardMonitoringFromYear()\r\n      .subscribe((response) => {\r\n        this.standardMonitoringYear = response;\r\n\r\n        this.IsShowOtherCase(this.selectedYear);\r\n      });\r\n\r\n    this.onLazyLoadEvent(undefined);\r\n    this.onLazyLoadEventS(undefined);\r\n\r\n    this.showButton = this.checkUserPermission();\r\n\r\n    // CTS Upload & Transmission Dashboard\r\n    // Check CA System Admin role\r\n    const currentUser = this.configState.getOne('currentUser') as CurrentUserDto;\r\n    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');\r\n\r\n    if (localStorage.getItem('ctsUploadSelectedYear')) {\r\n      this.ctsUploadSelectedYear =\r\n        localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\r\n    }\r\n\r\n    if (localStorage.getItem('selectExchangeReason')) {\r\n      this.selectExchangeReason = Number(\r\n        localStorage.getItem('selectExchangeReason')\r\n      );\r\n    }\r\n\r\n    if (localStorage.getItem('selectCtsUploadStatus')) {\r\n      this.selectCtsUploadStatus = Number(\r\n        localStorage.getItem('selectCtsUploadStatus')\r\n      );\r\n    }\r\n\r\n    if (localStorage.getItem('selectReceivingCountry')) {\r\n      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\r\n    }\r\n\r\n    // Fetch certificate expiration date\r\n    this.getBahamasCertificateInfo();\r\n    this.getBahamasCtsSettingInfo();\r\n    this.getCountries();\r\n    this.onCtsUploadLazyLoadEvent(undefined);\r\n    this.onCtsDashboardLazyLoadEvent(undefined);\r\n  }\r\n\r\n  IsShowOtherCase(year: string) {\r\n    const selectedYearAsInt: number = parseInt(year, 10);\r\n    const standardMonitoringYearAsInt: number = parseInt(\r\n      this.standardMonitoringYear,\r\n      10\r\n    );\r\n    this.showOtherCase =\r\n      selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\r\n  }\r\n\r\n  /** Lazy load event works for \"TableIds\" grid only. */\r\n  onLazyLoadEventS(event): void {\r\n    this.currentPageIndexS = 0;\r\n    this.informationExchangeService\r\n      .getSummaryByYearByYear(this.selectedYear)\r\n      .subscribe((response) => {\r\n        this.summaryExchangeList = response;\r\n        setTimeout(() => {\r\n          this.setTableDataS();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  /** Lazy load event works for grid \"TableId\" only. */\r\n  onLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.PageSize === (event.pageSize ?? 10)) {\r\n        this.currentPageIndex = event.pageNumber ?? 0;\r\n      } else {\r\n        //\r\n        // if Page size got changed through pagination control,\r\n        // need to reset current page index to 0.\r\n        //\r\n        this.PageSize = event.pageSize ?? 10;\r\n        this.currentPageIndex = 0;\r\n      }\r\n\r\n      this.input.skipCount =\r\n        (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\r\n\r\n      this.input.maxResultCount = this.PageSize ?? 10;\r\n\r\n      if (event.isAscending === false) {\r\n        this.input.sorting = `${event.sortField} desc`;\r\n      } else {\r\n        this.input.sorting = `${event.sortField} asc`;\r\n      }\r\n    } else {\r\n      this.currentPageIndex = 0;\r\n      this.PageSize = 10;\r\n      this.input.informationExchangeStatus = this.selectReportStatus;\r\n      this.input.year = this.selectedYear;\r\n      this.input.skipCount = 0;\r\n      this.input.maxResultCount = this.PageSize;\r\n    }\r\n\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.totalRecords = response.totalCount;\r\n          this.exchangeInformationResultRecords = response.items;\r\n        } else {\r\n          this.totalRecords = 0;\r\n          this.exchangeInformationResultRecords = [];\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  summaryExchangeList: any[] = [\r\n    {\r\n      id: 1,\r\n      totalNoReport: 100,\r\n      totalNoReportSent: 10,\r\n      totalNoReportRExchange: 5,\r\n      totalNoReportRReview: 2,\r\n      totalNoReportNotSent: 5,\r\n    },\r\n  ];\r\n\r\n  setTableDataS(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableIdS;\r\n    tableData.totalRecords = 1;\r\n    tableData.data = this.summaryExchangeList.map((x) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,\r\n            value: x.totalNoofReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,\r\n            value: x.totalNoofExchangedReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,\r\n            value: x.totalNoofReadyExchangedReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,\r\n            value: x.totalNoofReviewReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,\r\n            value: x.totalNotSentReports,\r\n          },\r\n        ],\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 10);\r\n  }\r\n\r\n  getExchangeReasonDescription(input: any) {\r\n    const foundStatus = ExchangeReasonDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  getInformationExchangeStatusDescription(input: any) {\r\n    const foundStatus = InformationExchangeStatusDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  base64ToUint8Array(x: string) {\r\n    const raw = atob(x);\r\n    var rawLength = raw.length;\r\n    var array = new Uint8Array(new ArrayBuffer(rawLength));\r\n\r\n    for (let i = 0; i < rawLength; i++) {\r\n      array[i] = raw.charCodeAt(i);\r\n    }\r\n    return array;\r\n  }\r\n\r\n  downloadFile(content: string, name: string) {\r\n    var file = new Blob([this.base64ToUint8Array(content)]);\r\n    var fileURL = window.URL.createObjectURL(file);\r\n\r\n    var element = document.createElement('a');\r\n    document.body.appendChild(element);\r\n    element.style.display = 'none';\r\n    element.href = fileURL;\r\n    element.download = name;\r\n    element.click();\r\n    element.remove();\r\n  }\r\n\r\n  GenerateXMlByType(exchangeType: ExchangeReason) {\r\n    this.informationExchangeService\r\n      .getXMLFilesFilterByExchangeTypeByReasonAndYear(\r\n        exchangeType,\r\n        this.selectedYear\r\n      )\r\n      .subscribe((result) => {\r\n        this.onLazyLoadEvent(undefined);\r\n        if (result.fileName != '') {\r\n          this.downloadFile(result.content.toString(), result.fileName);\r\n        } else\r\n          Swal.fire({\r\n            icon: 'info',\r\n            title: 'XML Import',\r\n            text: 'No data to export.',\r\n            allowOutsideClick: false,\r\n          });\r\n      });\r\n  }\r\n\r\n  setTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableId;\r\n    tableData.totalRecords = this.totalRecords;\r\n    tableData.data = this.exchangeInformationResultRecords.map((x) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: InformationExchangeTableColumns.EXCHANGE_REASON,\r\n            value: this.getExchangeReasonDescription(x.exchangeReason),\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.RA_CODE,\r\n            value: x.raCode,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.ENTITY_NAME,\r\n            value: x.entityName,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.INCROP_NUMBER,\r\n            value: x.companyFormationNumber,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,\r\n            value: x.fiscalEndDate,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.DUE_DATE,\r\n            value: x.dueDate,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,\r\n            value: this.getInformationExchangeStatusDescription(\r\n              x.informationExchangeStatus\r\n            ),\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.VIEW_DECLARATION,\r\n            /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\r\n            value: x.isMigrated === false ? 'view' : '',\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.XML_DATA,\r\n            value: 'XML Data',\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.VIEW_HISTORY,\r\n            value: x.hasHistoryRecord ? 'View History' : '',\r\n          },\r\n        ],\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 100);\r\n  }\r\n\r\n  onYearChange(ob) {\r\n    this.selectedYear = ob.value;\r\n    this.IsShowOtherCase(this.selectedYear);\r\n    // Keep the selected Year in local storage.\r\n    localStorage.setItem('selectedYear', ob.value);\r\n\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onLazyLoadEventS(undefined);\r\n  }\r\n\r\n  onSearch() {\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onLazyLoadEventS(undefined);\r\n  }\r\n\r\n  onReportChange(ob) {\r\n    this.selectReportStatus = Number(ob.value);\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n\r\n    // Keep the selected Report Status in local storage.\r\n    localStorage.setItem('selectReportStatus', ob.value);\r\n\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onLinkClick(event: BdoTableCellLinkClickEvent) {\r\n    const data = event.rawData as InformationExchangeDto;\r\n    if (event.columnId === InformationExchangeTableColumns.XML_DATA) {\r\n      //\r\n      // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\r\n      // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\r\n      //\r\n      this.router.navigate(['/es-info-exchange/exchangedetail'], {\r\n        //\r\n        // Passed \"Id\" of table dbo.InformationExchanges.\r\n        //\r\n        queryParams: { id: data.id, ishistory: false },\r\n      });\r\n    } else if (\r\n      event.columnId === InformationExchangeTableColumns.VIEW_DECLARATION\r\n    ) {\r\n      //\r\n      // When click the \"view\" link button in the Information Exchange records grid.\r\n      // Route to CaActionPageComponent.ts component\r\n      //\r\n      this.router.navigate(['/action-page'], {\r\n        queryParams: {\r\n          declarationid: data.declarationId,\r\n          entityid: data.corporateEntityId,\r\n          from: 'info-exchange',\r\n        },\r\n      });\r\n    } else if (\r\n      event.columnId === InformationExchangeTableColumns.VIEW_HISTORY\r\n    ) {\r\n      //\r\n      // Open dialog to show history records. informantion-exchange-history.component.ts\r\n      //\r\n      this.openInformationExchangeHistoryDialog(data.id);\r\n    }\r\n  }\r\n\r\n  openInformationExchangeHistoryDialog(informationExchangeId: string) {\r\n    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\r\n      height: '750px',\r\n      width: '1200px',\r\n      data: { informationExchangeId: informationExchangeId },\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      console.log('The dialog was closed', result);\r\n    });\r\n  }\r\n\r\n  /** Check if logon user has permission \"Generate XML\".\r\n   *  Work for show/hide three xml buttons.\r\n   */\r\n  checkUserPermission() {\r\n    let result = false;\r\n    // Get current logon user object.\r\n    const currentUser = this.configState.getOne(\r\n      'currentUser'\r\n    ) as CurrentUserDto;\r\n\r\n    if (currentUser) {\r\n      result = this.permissionService.getGrantedPolicy(\r\n        Permissions.DASHBOARD_GENERATE_XML\r\n      );\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\r\n  getFiscalYears() {\r\n    return this.dashboardService.getFiscalYears().pipe();\r\n  }\r\n\r\n\r\n  // CTS Upload & Transmission Dashboard Methods\r\n  getCTSUploadStatusDescription(input: any) {\r\n    const foundStatus = CTSUploadStatusDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  getReceivingCountryName(input: any) {\r\n    const foundCountry = this.countries.find(\r\n      (status) => status.code2 === input\r\n    );\r\n    if (foundCountry) return foundCountry.name;\r\n    return '';\r\n  }\r\n\r\n  getCountries() {\r\n    this.countryService.getList({ sorting: \"name asc\", maxResultCount: 1000 }).subscribe(response => {\r\n      this.countries = response.items;\r\n      this.uploadHistoricalCountries = response.items;\r\n      // Remove code2 with empty string and null values in countries      \r\n      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');\r\n      this.uploadHistoricalCountries = this.uploadHistoricalCountries.filter(country => country.code2 && country.code2.trim() !== '');\r\n      // add new country ALL in countries \r\n      this.countries.unshift({ name: 'All', code2: '' });\r\n    });\r\n  }\r\n\r\n  getBahamasCertificateInfo() {\r\n    return this.certificateService.getBahamasCertificateInfo().subscribe({\r\n      next: (info) => {\r\n        this.certificateExpirationDate = info?.expiredAt || null;\r\n      },\r\n      error: () => {\r\n        this.certificateExpirationDate = null;\r\n      }\r\n    });\r\n  }\r\n\r\n  getBahamasCtsSettingInfo() {\r\n    return this.bahamasCtsSettingService.getCurrentSettings().subscribe({\r\n      next: (info) => {\r\n        this.bahamasCtsSetting = info || null;\r\n      },\r\n      error: () => {\r\n        this.bahamasCtsSetting = null;\r\n      }\r\n    });\r\n  }\r\n\r\n  openUpdateCtsSettingDialog() {\r\n    const dialogRef = this.dialog.open(UpdateCtsSettingDialogComponent, {\r\n      width: '1200px',\r\n      data: this.bahamasCtsSetting || null\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (!result) return;\r\n\r\n      const formData: FormData = new FormData();\r\n      if (result.file) {\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n      }\r\n\r\n      const isCreating = result.id == null;\r\n      const serviceCall = isCreating\r\n        ? this.fileUploadService.createBahamasCtsSettings(result, formData)\r\n        : this.fileUploadService.updateBahamasCtsSettings(result, formData);\r\n\r\n      const action = isCreating ? 'create' : 'update';\r\n\r\n      serviceCall.subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.getBahamasCtsSettingInfo();\r\n            this.toasterService.success(`CTS Settings successfully ${action}d.`, '', { life: 5000 });\r\n          } else {\r\n            this.toasterService.warn(`CTS Settings could not be ${action}d. Please try again later.`, null, { life: 7000 });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.toasterService.error(`An error occurred while trying to ${action} CTS Settings.`, null, { life: 5000 });\r\n          console.error(`Error ${action}ing CTS Settings:`, error);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  openUpdateCaCertificateDialog() {\r\n    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\r\n      width: '500px',\r\n      data: {},\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.file) {\r\n        const formData: FormData = new FormData();\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n        // Only call upload if file is present\r\n        this.fileUploadService\r\n          .uploadBahamasCertificateSecure(formData, result.password).subscribe({\r\n            next: (response) => {\r\n              if (response) {\r\n                // Fetch certificate expiration date\r\n                this.getBahamasCertificateInfo();\r\n                this.toasterService.success('Bahamas Certificate successfully uploaded', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, { life: 7000 });\r\n              }\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error uploading Bahamas certificate:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n  }\r\n\r\n  // CTS Upload & Transmission Dashboard Methods\r\n  setCtsDashboardTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.ctsDashboardTableId;\r\n    tableData.totalRecords = 1;\r\n    tableData.data = this.ctsDashboardList.map(x => ({\r\n      id: x.id,\r\n      rawData: x,\r\n      cells: [\r\n        { columnId: 'totalNotUploaded', value: x.totalNotUploaded },\r\n        { columnId: 'totalReadyForUpload', value: x.totalReadyForUpload },\r\n        { columnId: 'totalFailedUpload', value: x.totalFailedUpload },\r\n        { columnId: 'totalUploadedToCTS', value: x.totalUploadedToCTS },\r\n        { columnId: 'totalNotEnrolled', value: x.totalNotEnrolled },\r\n      ],\r\n    }));\r\n    setTimeout(() => this.tableService.setGridData(tableData), 10);\r\n  }\r\n\r\n  setCtsUploadTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.ctsUploadTableId;\r\n    tableData.totalRecords = this.ctsUploadTotalRecords;\r\n    tableData.data = this.ctsUploadResultRecords.map(x => ({\r\n      id: x.id,\r\n      rawData: x,\r\n      cells: [\r\n        { columnId: 'exchangeReason', value: this.getExchangeReasonDescription(x.exchangeReason) },\r\n        { columnId: 'dataPacket', value: x.dataPacket },\r\n        { columnId: 'fileCreationDate', value: x.fileCreationDate },\r\n        { columnId: 'receivingCountry', value: this.getReceivingCountryName(x.receivingCountry) },\r\n        { columnId: 'ctsUploadStatus', value: this.getCTSUploadStatusDescription(x.ctsUploadStatus) },\r\n        { columnId: 'uploadedAt', value: x.uploadedAt },\r\n        { columnId: 'ctsTransmissionStatus', value: x.ctsTransmissionStatus },\r\n        { columnId: 'viewExchangeRecords', value: x.viewExchangeRecords === false ? 'View' : null },\r\n        { columnId: 'viewComments', value: x.viewComments.length > 0 ? 'View Comments' : null },\r\n        {\r\n          columnId: 'regeneratePacket',\r\n          value: x.allowedActions?.includes(0)\r\n            ? { actionType: 'RegenerateDataPacket', icon: 'refresh', tooltip: \"Regenerate Packet\" }\r\n            : null\r\n        },\r\n        {\r\n          columnId: 'ctsUpload',\r\n          value: x.allowedActions?.includes(1)\r\n            ? { actionType: 'ctsUpload', icon: 'cloud_upload', tooltip: \"CTS Upload\" }\r\n            : null\r\n        },\r\n        {\r\n          columnId: 'excludeFromCtsUpload',\r\n          hide: x.allowedActions?.includes(2) || x.allowedActions?.includes(3) ? false : true,\r\n          value: x.excludeFromCtsUpload\r\n        }\r\n      ],\r\n    }));\r\n\r\n    setTimeout(() => this.tableService.setGridData(tableData), 100);\r\n  }\r\n\r\n  onCtsDashboardLazyLoadEvent(event): void {\r\n    this.currentPageIndexS = 0;\r\n    this.ctsPackageRequestService\r\n      .getSummaryByYearByYear(this.ctsUploadSelectedYear)\r\n      .subscribe((response) => {\r\n        this.ctsDashboardList = response;\r\n        setTimeout(() => {\r\n          this.setCtsDashboardTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\r\n        this.ctsUploadCurrentPage = event.pageNumber ?? 0;\r\n      } else {\r\n        //\r\n        // if Page size got changed through pagination control,\r\n        // need to reset current page index to 0.\r\n        //\r\n        this.ctsUploadPageSize = event.pageSize ?? 10;\r\n        this.ctsUploadCurrentPage = 0;\r\n      }\r\n\r\n      this.ctsUploadInput.skipCount =\r\n        (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\r\n\r\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\r\n\r\n      if (event.isAscending === false) {\r\n        this.ctsUploadInput.sorting = `${event.sortField} desc`;\r\n      } else {\r\n        this.ctsUploadInput.sorting = `${event.sortField} asc`;\r\n      }\r\n    } else {\r\n      this.ctsUploadCurrentPage = 0;\r\n      this.ctsUploadPageSize = 10;\r\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\r\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\r\n      this.ctsUploadInput.skipCount = 0;\r\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    }\r\n\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.ctsUploadTotalRecords = response.totalCount;\r\n          this.ctsUploadResultRecords = response.items;\r\n        } else {\r\n          this.ctsUploadTotalRecords = 0;\r\n          this.ctsUploadResultRecords = [];\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadYearChange(ob) {\r\n    this.ctsUploadSelectedYear = ob.value;\r\n    localStorage.setItem('ctsUploadSelectedYear', ob.value);\r\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\r\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\r\n    this.ctsUploadInput.skipCount = 0;\r\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        this.ctsUploadTotalRecords = response.totalCount;\r\n        this.ctsUploadResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onCtsDashboardLazyLoadEvent(undefined);\r\n  }\r\n\r\n  onCtsUploadSearch() {\r\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\r\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\r\n    this.ctsUploadInput.skipCount = 0;\r\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        this.ctsUploadTotalRecords = response.totalCount;\r\n        this.ctsUploadResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadExchangeReasonChange(ob) {\r\n    this.selectExchangeReason = Number(ob.value);\r\n    // Keep the selected Exchange Reason in local storage.\r\n    localStorage.setItem('selectExchangeReason', ob.value);\r\n  }\r\n\r\n  onCtsUploadStatusChange(ob) {\r\n    this.selectCtsUploadStatus = Number(ob.value);\r\n    // Keep the selected Upload Status in local storage.\r\n    localStorage.setItem('selectCtsUploadStatus', ob.value);\r\n  }\r\n\r\n  onCtsUploadReceivingCountryChange(ob) {\r\n    this.selectReceivingCountry = ob.value;\r\n    // Keep the selected Receiving Country in local storage.\r\n    localStorage.setItem('selectReceivingCountry', ob.value);\r\n  }\r\n\r\n  onCtsUploadLinkClick(event: BdoTableCellLinkClickEvent) {\r\n    if (event.columnId === 'viewExchangeRecords') {\r\n      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {\r\n        width: '1200px',\r\n        data: {\r\n          row: event.rawData\r\n        }\r\n      });\r\n    }\r\n    else if (event.columnId === 'viewComments') {\r\n      this.dialog.open(ViewCommentDialogComponent, {\r\n        width: '1200px',\r\n        data: {\r\n          row: event.rawData\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onCtsUploadActionClick(event: BdoTableRowActionClickEvent) {\r\n    if (event.action === 'RegenerateDataPacket') {\r\n      const dialogRef = this.dialog.open(RegeneratePacketDialogComponent, {\r\n        width: '500px',\r\n        data: {\r\n          row: event?.data?.rawData\r\n        }\r\n      });\r\n\r\n      dialogRef.afterClosed().subscribe((result) => {\r\n        if (result && result.comment) {\r\n          this.ctsPackageRequestService\r\n            .regeneratePackage(event.data?.rawData?.ctsPackageId, result.comment).subscribe({\r\n              next: (response) => {\r\n                if (response.success) {\r\n                  this.toasterService.success(response.message || 'Regenerate Package successfully requested', '', { life: 5000 });\r\n                }\r\n                else {\r\n                  this.toasterService.warn(response.message || 'Regenerate Package couldn’t be requested. Please try again later', null, { life: 7000 });\r\n                }\r\n                this.onCtsUploadLazyLoadEvent(undefined);\r\n                this.onCtsDashboardLazyLoadEvent(undefined);\r\n              },\r\n              error: (error) => {\r\n                //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n                console.error('Error requesting Regenerate Package:', error);\r\n              }\r\n            })\r\n        }\r\n      });\r\n    }\r\n    else if (event.action === 'ctsUpload') {\r\n      this.sweetAlert.fireDialog({\r\n        action: \"submit\",\r\n        title: \"CTS Upload\",\r\n        text: \"Are you sure you would like to proceed?\",\r\n        type: \"confirm\"\r\n      }, (confirm) => {\r\n        if (confirm) {\r\n          this.ctsPackageRequestService.uploadToCts(event.data?.rawData?.ctsPackageId).subscribe({\r\n            next: (response) => {\r\n              if (response.success) {\r\n                this.toasterService.success(response.message || 'CTS Upload successfully completed', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be completed. Please try again later', null, { life: 7000 });\r\n              }\r\n              this.onCtsUploadLazyLoadEvent(undefined);\r\n              this.onCtsDashboardLazyLoadEvent(undefined);\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error requesting CTS Upload:', error);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onCheckboxClick(event: BdoTableCheckboxClickEvent) {\r\n    // Commented out: console.log('Checkbox clicked:', event.selectedRows[0]?.rawData?.ctsPackageId);\r\n    // Commented out: console.log('Checkbox clicked:', event.rowId);\r\n    if (event.isChecked) {\r\n      this.ctsPackageRequestService.markAsDoNotUpload(event.rowId).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.toasterService.success(response.message || 'Exclude packet from uploading successfully completed', '', { life: 5000 });\r\n          }\r\n          else {\r\n            this.toasterService.warn(response.message || 'Exclude packet from uploading  couldn’t be completed. Please try again later', null, { life: 7000 });\r\n          }\r\n          this.onCtsUploadLazyLoadEvent(undefined);\r\n          this.onCtsDashboardLazyLoadEvent(undefined);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error marking as Do Not Upload:', error);\r\n        }\r\n      });\r\n    } else {\r\n      this.ctsPackageRequestService.unMarkAsDoNotUpload(event.rowId).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.toasterService.success(response.message || 'Include packet for uploading successfully completed', '', { life: 5000 });\r\n          }\r\n          else {\r\n            this.toasterService.warn(response.message || 'Include packet for uploading couldn’t be completed. Please try again later', null, { life: 7000 });\r\n          }\r\n          this.onCtsUploadLazyLoadEvent(undefined);\r\n          this.onCtsDashboardLazyLoadEvent(undefined);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error unmarking as Do Not Upload:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Add this method to open the Upload Historical XML dialog\r\n  openUploadHistoricalXmlDialog() {\r\n    const dialogRef = this.dialog.open(UploadHistoricalXmlDialogComponent, {\r\n      width: '500px',\r\n      data: {\r\n        receivingCountries: this.uploadHistoricalCountries || [],\r\n        fiscalYears: this.year || []\r\n      }\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.success) {\r\n        this.onCtsUploadLazyLoadEvent(undefined);\r\n        this.onCtsDashboardLazyLoadEvent(undefined);\r\n      }\r\n    });\r\n  }\r\n\r\n  openCtsUploadDialog() {\r\n    const dialogRef = this.dialog.open(UploadCtsDialogComponent, {\r\n      width: '500px',\r\n      data: {\r\n        fiscalYears: this.year || []\r\n      }\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.fiscalYear) {\r\n        this.ctsPackageRequestService\r\n          .batchUploadToCts(result.fiscalYear, result.comment).subscribe({\r\n            next: (response) => {\r\n              if (response.success) {\r\n                this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });\r\n              }\r\n              this.onCtsUploadLazyLoadEvent(undefined);\r\n              this.onCtsDashboardLazyLoadEvent(undefined);\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error requesting CTS Upload:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n  }\r\n\r\n  openDecryptDialog() {\r\n    const dialogRef = this.dialog.open(DecryptDataPacketDialogComponent, {\r\n      width: '500px'\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.file) {\r\n        const formData: FormData = new FormData();\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n        // Only call upload if file is present        \r\n        this.fileUploadService\r\n          .unpackCtsPackage(formData).subscribe({\r\n            next: (response) => {\r\n              if (response.success) {\r\n                this.toasterService.success(response.message || 'Decrypt Data Packet successfully completed', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn(response.message || 'Decrypt Data Packet couldn’t be completed. Please try again later', null, { life: 7000 });\r\n              }\r\n              this.onCtsUploadLazyLoadEvent(undefined);\r\n              this.onCtsDashboardLazyLoadEvent(undefined);\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error decrypting data packet:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshAllTransmissionStatus() {\r\n    this.sweetAlert.fireDialog({\r\n      action: \"submit\",\r\n      title: \"Refresh All Transmission Status\",\r\n      text: \"Are you sure you would like to proceed?\",\r\n      type: \"confirm\"\r\n    }, (confirm) => {\r\n      if (confirm) {\r\n        this.ctsPackageRequestService.refreshTransmissionStatus().subscribe({\r\n          next: (response) => {\r\n            if (response.success) {\r\n              this.toasterService.success(response.message || 'Refresh All Transmission Status successfully requested', '', { life: 5000 });\r\n            }\r\n            else {\r\n              this.toasterService.warn(response.message || 'Refresh All Transmission Status couldn’t be requested. Please try again later', null, { life: 7000 });\r\n            }\r\n            this.onCtsUploadLazyLoadEvent(undefined);\r\n            this.onCtsDashboardLazyLoadEvent(undefined);\r\n          },\r\n          error: (error) => {\r\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n            console.error('Error requesting Refresh All Transmission Status:', error);\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<mat-tab-group>\r\n  <mat-tab label=\"Info Exchange Readiness\">\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"TableIdS\" [columns]=\"summaryExchangeColumns\" scrollHeight=\"100%\"\r\n          defaultSortColumnId=\"uploadedDateTime\" [defaultSortOrder]=\"'desc'\" [pageIndex]=\"currentPageIndexS\"\r\n          [pageSize]=\"PageSizeS\" [isVirtualScroll]=\"false\" [hidePagination]=\"true\" [rowSelectable]=\"true\"\r\n          [lazyLoad]=\"true\" (onLazyLoad)=\"onLazyLoadEventS($event)\" [pageSizeOptions]=\"[10, 20, 50, 100]\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-column-exchange row justify-content-end\">\r\n      <div class=\"col-md-auto\">\r\n        <span class=\"certificate-text\" *ngIf=\"certificateExpirationDate\">CA Certificate expired at {{\r\n          certificateExpirationDate | date:'dd/MM/yyyy'}}</span>\r\n        <button *ngIf=\"isCaSystemAdmin\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openUpdateCtsSettingDialog()\">\r\n          Update CTS Setting\r\n        </button>\r\n        <button *ngIf=\"isCaSystemAdmin\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openUpdateCaCertificateDialog()\">\r\n          Update CA Certificate\r\n        </button>\r\n        <button *ngIf=\"showButton\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(0)\">\r\n          Non-compliance XML\r\n        </button>\r\n        <button *ngIf=\"showButton\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(1)\">\r\n          High Risk IP XML\r\n        </button>\r\n        <button *ngIf=\"showButton\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(2)\">\r\n          Non-resident XML\r\n        </button>\r\n        <button *ngIf=\"showButton && showOtherCase\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(3)\">\r\n          Other Cases XML\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-column-exchange row\">\r\n      <div class=\"col-md-4 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Financial Period End</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Financial Period End\" [(value)]=\"selectedYear\"\r\n            (selectionChange)=\"onYearChange($event)\">\r\n            <mat-option *ngFor=\"let element of year\" [value]=\"element\">\r\n              {{ element }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Report Status</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Report Status\" [(value)]=\"selectReportStatus\"\r\n            (selectionChange)=\"onReportChange($event)\">\r\n            <mat-option *ngFor=\"let element of informationExchangedDic\" [value]=\"element.value\">\r\n              {{ element.description }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Entity Name</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <input matInput placeholder=\"File Name\" [(ngModel)]=\"searchEntityName\" />\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-2 col-sm-12 margin-top search-button-column\">\r\n        <button type=\"button\" mat-raised-button (click)=\"onSearch()\" class=\"ui-button search-button\">\r\n          Search\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"TableId\" scrollHeight=\"36vh\" [columns]=\"exchangeResultColumns\"\r\n          defaultSortColumnId=\"ExchangeReason\" [defaultSortOrder]=\"'desc'\" [pageIndex]=\"currentPageIndex\"\r\n          [pageSize]=\"PageSize\" [pageSizeOptions]=\"[10, 20, 50, 100]\" [isVirtualScroll]=\"false\" [hidePagination]=\"false\"\r\n          [rowSelectable]=\"true\" [lazyLoad]=\"true\" (onLazyLoad)=\"onLazyLoadEvent($event)\"\r\n          (onLinkClick)=\"onLinkClick($event)\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n  </mat-tab>\r\n\r\n  <mat-tab label=\"CTS Upload & Transmission\">\r\n    <!-- 1. Dashboard Section -->\r\n    <div class=\"row top-action-row-exchange\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"ctsDashboardTableId\" [columns]=\"ctsDashboardColumns\" scrollHeight=\"auto\"\r\n          [defaultSortOrder]=\"'desc'\" [pageIndex]=\"currentPageIndexS\" [pageSize]=\"PageSizeS\" [isVirtualScroll]=\"false\"\r\n          [hidePagination]=\"true\" [rowSelectable]=\"false\" [lazyLoad]=\"false\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 2. Button List Section -->\r\n    <div class=\"top-action-column-exchange row justify-content-end\">\r\n      <div class=\"col-md-auto\">      \r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openDecryptDialog()\"><mat-icon>lock_open</mat-icon> Decrypt Received Data Packet</button>\r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openUploadHistoricalXmlDialog()\"><mat-icon>cloud_upload</mat-icon> Upload Historical XML</button>\r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n        (click)=\"openCtsUploadDialog()\"><mat-icon>cloud_upload</mat-icon> CTS Upload</button>\r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n        (click)=\"refreshAllTransmissionStatus()\"><mat-icon>refresh</mat-icon> Refresh Status</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 3. Filters and Search Section -->\r\n    <div class=\"top-action-column-exchange row align-items-end filters-row\">\r\n      <div class=\"col-md-3 col-sm-6 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Financial Period End</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Financial Period End\" [(value)]=\"ctsUploadSelectedYear\"\r\n            (selectionChange)=\"onCtsUploadYearChange($event)\">\r\n            <mat-option *ngFor=\"let element of year\" [value]=\"element\">\r\n              {{ element }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-6 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Exchange Reason</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Exchange Reason\" [(value)]=\"selectExchangeReason\"\r\n            (selectionChange)=\"onCtsUploadExchangeReasonChange($event)\">\r\n            <mat-option *ngFor=\"let element of ctsUploadExchangeReasonDic\" [value]=\"element.value\">\r\n              {{ element.description }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-6 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Receiving Country</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Receiving Country\" [(value)]=\"selectReceivingCountry\"\r\n            (selectionChange)=\"onCtsUploadReceivingCountryChange($event)\">\r\n            <mat-option *ngFor=\"let element of countries\" [value]=\"element.code2\">\r\n              {{element.name}}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-6 margin-top d-flex align-items-end\">\r\n        <div style=\"width:100%\">\r\n          <mat-label class=\"outside-mat-label\">CTS Upload Status</mat-label>\r\n          <mat-form-field class=\"form-field-reduce-length\">\r\n            <mat-select placeholder=\"CTS Upload Status\" [(value)]=\"selectCtsUploadStatus\"\r\n              (selectionChange)=\"onCtsUploadStatusChange($event)\">\r\n              <mat-option *ngFor=\"let element of ctsUploadStatusDic\" [value]=\"element.value\">\r\n                {{ element.description }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n          <button type=\"button\" mat-raised-button class=\"ui-button search-button float-end\"\r\n            (click)=\"onCtsUploadSearch()\">Search</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 4. Grid Section -->\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"ctsUploadTableId\" [columns]=\"ctsUploadColumns\" scrollHeight=\"36vh\"\r\n          defaultSortColumnId=\"ExchangeReason\" [defaultSortOrder]=\"'desc'\" [pageIndex]=\"ctsUploadCurrentPage\"\r\n          [pageSize]=\"ctsUploadPageSize\" [pageSizeOptions]=\"[10, 20, 50, 100]\" [isVirtualScroll]=\"false\"\r\n          [hidePagination]=\"false\" [rowSelectable]=\"true\" [lazyLoad]=\"true\"\r\n          (onLazyLoad)=\"onCtsUploadLazyLoadEvent($event)\" (onLinkClick)=\"onCtsUploadLinkClick($event)\" (onActionClick)=\"onCtsUploadActionClick($event)\"\r\n          (onCheckboxClick)=\"onCheckboxClick($event)\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n\r\n  </mat-tab>\r\n</mat-tab-group>"], "mappings": "AAOA,SAEEA,yBAAyB,QACpB,mHAAmH;AAC1H,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAIEC,kBAAkB,EAClBC,YAAY,QAEP,yDAAyD;AAChE,SACEC,iBAAiB,EAEjBC,4BAA4B,EAG5BC,kBAAkB,EAClBC,0BAA0B,QACrB,8BAA8B;AACrC,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,mCAAmC,QAAQ,wEAAwE;AAE5H,SAASC,kCAAkC,QAAQ,wEAAwE;AAM3H,SAASC,sCAAsC,QAAQ,gFAAgF;AACvI,SAASC,kCAAkC,QAAQ,wEAAwE;AAC3H,SAASC,gCAAgC,QAAQ,oEAAoE;AACrH,SAASC,+BAA+B,QAAQ,gEAAgE;AAChH,SAASC,0BAA0B,QAAQ,sDAAsD;AACjG,SAASC,eAAe,QAAQ,sGAAsG;AAGtI,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,+BAA+B,QAAQ,kEAAkE;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICrC1GC,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,GAChB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADSH,EAAA,CAAAI,SAAA,EAChB;IADgBJ,EAAA,CAAAK,kBAAA,+BAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,yBAAA,oBAChB;;;;;;IACjDR,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAS,UAAA,mBAAAC,2EAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,0BAAA,EAA4B;IAAA,EAAC;IACtCf,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAC4C;IAA1CD,EAAA,CAAAS,UAAA,mBAAAO,2EAAA;MAAAhB,EAAA,CAAAW,aAAA,CAAAM,GAAA;MAAA,MAAAV,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAW,6BAAA,EAA+B;IAAA,EAAC;IACzClB,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAU,4EAAA;MAAAnB,EAAA,CAAAW,aAAA,CAAAS,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAa,4EAAA;MAAAtB,EAAA,CAAAW,aAAA,CAAAY,GAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAe,4EAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAiB,4EAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAgB,GAAA;MAAA,MAAApB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IASLH,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAA4B,UAAA,UAAAC,UAAA,CAAiB;IACxD7B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAwB,UAAA,MACF;;;;;IASA7B,EAAA,CAAAC,cAAA,qBAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAA4B,UAAA,UAAAE,UAAA,CAAAC,KAAA,CAAuB;IACjF/B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAyB,UAAA,CAAAE,WAAA,MACF;;;;;IA4DAhC,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAA4B,UAAA,UAAAK,WAAA,CAAiB;IACxDjC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA4B,WAAA,MACF;;;;;IASAjC,EAAA,CAAAC,cAAA,qBAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkDH,EAAA,CAAA4B,UAAA,UAAAM,WAAA,CAAAH,KAAA,CAAuB;IACpF/B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6B,WAAA,CAAAF,WAAA,MACF;;;;;IASAhC,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAA4B,UAAA,UAAAO,WAAA,CAAAC,KAAA,CAAuB;IACnEpC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA8B,WAAA,CAAAE,IAAA,MACF;;;;;IAUErC,EAAA,CAAAC,cAAA,qBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0CH,EAAA,CAAA4B,UAAA,UAAAU,WAAA,CAAAP,KAAA,CAAuB;IAC5E/B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAiC,WAAA,CAAAN,WAAA,MACF;;;ADlGd,OAAM,MAAOO,gCACX,SAAQzD,gBAAgB;EAsUxB0D,YACEC,QAAkB,EACVC,MAAc,EACdC,0BAAsD,EACtDC,gCAAmE,EACnEC,iBAAoC,EACpCC,gBAA8C,EAC/CC,MAAiB,EAChBC,kBAAsC,EACtCC,iBAAoC,EACpCC,cAA8B,EAC9BC,cAA8B,EAC9BC,wBAAkD,EAClDC,UAA6B,EAC7BC,wBAAkD;IAE1D,KAAK,CAACb,QAAQ,CAAC;IAdP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAC1B,KAAAC,gCAAgC,GAAhCA,gCAAgC;IAChC,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAlVlC,KAAAC,KAAK,GAA8B;MACjCC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,oBAAoB;MAC7BC,yBAAyB,EAAE9E,yBAAyB,CAAC+E,IAAI;MACzDC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE;KACP;IACD,KAAAC,OAAO,GAAG,wBAAwB;IAClC;IACA,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAF,IAAI,GAAG,EAAE;IACT;IACA,KAAAG,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAC;IACpD,KAAAC,qBAAqB,GAA+B,CAClD;MACEC,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACmF,IAAI;MAC7BO,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACmF,IAAI;MAC7BO,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAAC8F,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAAC8F,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAAC8F,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,CACF;IACD;IACA,KAAAE,QAAQ,GAAG,EAAE;IACb,KAAAC,gCAAgC,GAAG,EAAE;IACrC,KAAAC,kBAAkB,GAAWnG,yBAAyB,CAAC+E,IAAI;IAE3D,KAAAqB,uBAAuB,GAAQ/F,4BAA4B;IAC3D,KAAAgG,QAAQ,GAAG,wBAAwB;IACnC,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,sBAAsB,GAA+B,CACnD;MACEd,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EACR;KACH,EACD;MACEN,QAAQ;MACRC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IACD;IACA,KAAAU,SAAS,GAAG,EAAE;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB;;IAEA,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,WAAW,GAAG,IAAIxB,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE;IAEjD,KAAA5D,yBAAyB,GAAkB,IAAI;IAC/C,KAAAmF,iBAAiB,GAAgC,IAAI;IACrD,KAAAC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAAC,mBAAmB,GAA+B,CAChD;MACEvB,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,mBAAmB;MAC7BC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAExF,kBAAkB,CAACsG,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IAED;IACA,KAAAkB,gBAAgB,GAAU,CACxB;MACEC,EAAE,EAAE,CAAC;MACLC,gBAAgB,EAAE,CAAC;MACnBC,mBAAmB,EAAE,CAAC;MACtBC,iBAAiB,EAAE,CAAC;MACpBC,kBAAkB,EAAE,CAAC;MACrBC,gBAAgB,EAAE;KACnB,CACF;IAED;IACA,KAAAC,gBAAgB,GAA+B,CAC7C;MACE/B,QAAQ,EAAE,gBAAgB;MAC1BC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAExF,kBAAkB,CAAC8F,IAAI;MAC7BJ,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAExF,kBAAkB,CAACmF,IAAI;MAC7BO,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAExF,kBAAkB,CAACmF,IAAI;MAC7BO,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAExF,kBAAkB,CAACyF,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAExF,kBAAkB,CAAC8F,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,cAAc;MACxBC,IAAI,EAAExF,kBAAkB,CAAC8F,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAExF,kBAAkB,CAACuH,kBAAkB;MAC3C7B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,WAAW;MACrBC,IAAI,EAAExF,kBAAkB,CAACuH,kBAAkB;MAC3C7B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAExF,kBAAkB,CAACwH,QAAQ;MACjC9B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IAED;IACA,KAAA4B,0BAA0B,GAAQpH,0BAA0B;IAC5D,KAAAqH,kBAAkB,GAAQtH,kBAAkB;IAG5C,KAAAuH,qBAAqB,GAAG,IAAIxC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAC;IAC7D,KAAAuC,sBAAsB,GAAG,EAAE;IAC3B,KAAAC,oBAAoB,GAAW,CAAC,CAAC;IACjC,KAAAC,qBAAqB,GAAWhH,eAAe,CAACiH,UAAU;IAC1D,KAAAC,sBAAsB,GAAW,EAAE;IAEnC;IACA,KAAAC,mBAAmB,GAAG,eAAe;IACrC,KAAAC,gBAAgB,GAAG,iBAAiB;IACpC,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,cAAc,GAA4B;MACxC7D,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,oBAAoB;MAC7B4D,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE;KACnB;IAiKD,KAAAC,mBAAmB,GAAU,CAC3B;MACE3B,EAAE,EAAE,CAAC;MACL4B,aAAa,EAAE,GAAG;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,sBAAsB,EAAE,CAAC;MACzBC,oBAAoB,EAAE,CAAC;MACvBC,oBAAoB,EAAE;KACvB,CACF;EAvJD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC3C,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACtE,IAAI,GAAG,EAAE;QACdqE,QAAQ,CAACE,OAAO,CAAEC,OAAO,IAAI;UAC3B,IAAI,CAACxE,IAAI,CAACyE,IAAI,CAACD,OAAO,CAAClE,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC;MACJ;MAAC;IACH,CAAC,CAAC;IAEF,IAAIoE,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE;MACxC,IAAI,CAACxE,YAAY,GACfuE,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC/C,WAAW;IAC5D;IAEA,IAAI8C,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE;MAC9C,IAAI,CAACzD,kBAAkB,GAAGK,MAAM,CAC9BmD,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC3C;IACH;IACA,IAAI,CAAC7F,gCAAgC,CAClC8F,0BAA0B,EAAE,CAC5BR,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACQ,sBAAsB,GAAGR,QAAQ;MAEtC,IAAI,CAACS,eAAe,CAAC,IAAI,CAAC3E,YAAY,CAAC;IACzC,CAAC,CAAC;IAEJ,IAAI,CAAC4E,eAAe,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;IAEhC,IAAI,CAACtD,UAAU,GAAG,IAAI,CAACwD,mBAAmB,EAAE;IAE5C;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,aAAa,CAAmB;IAC5E,IAAI,CAACvD,eAAe,GAAG,CAAC,CAACqD,WAAW,EAAEG,KAAK,EAAEC,QAAQ,CAAC,iBAAiB,CAAC;IAExE,IAAIb,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MACjD,IAAI,CAAC/B,qBAAqB,GACxB8B,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC/C,WAAW;IACrE;IAEA,IAAI8C,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAChD,IAAI,CAAC7B,oBAAoB,GAAGvB,MAAM,CAChCmD,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAC7C;IACH;IAEA,IAAID,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MACjD,IAAI,CAAC5B,qBAAqB,GAAGxB,MAAM,CACjCmD,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAC9C;IACH;IAEA,IAAID,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;MAClD,IAAI,CAAC1B,sBAAsB,GAAGyB,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC9E;IAEA;IACA,IAAI,CAACa,yBAAyB,EAAE;IAChC,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,wBAAwB,CAACX,SAAS,CAAC;IACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;EAC7C;EAEAF,eAAeA,CAAC9E,IAAY;IAC1B,MAAM6F,iBAAiB,GAAWC,QAAQ,CAAC9F,IAAI,EAAE,EAAE,CAAC;IACpD,MAAM+F,2BAA2B,GAAWD,QAAQ,CAClD,IAAI,CAACjB,sBAAsB,EAC3B,EAAE,CACH;IACD,IAAI,CAAClD,aAAa,GAChBkE,iBAAiB,IAAIE,2BAA2B,GAAG,IAAI,GAAG,KAAK;EACnE;EAEA;EACAd,gBAAgBA,CAACe,KAAK;IACpB,IAAI,CAAC3E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACxC,0BAA0B,CAC5BoH,sBAAsB,CAAC,IAAI,CAAC9F,YAAY,CAAC,CACzCiE,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACT,mBAAmB,GAAGS,QAAQ;MACnC6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEA;EACApB,eAAeA,CAACiB,KAAK;IACnB,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAAChF,QAAQ,MAAMgF,KAAK,CAACI,QAAQ,IAAI,EAAE,CAAC,EAAE;QAC5C,IAAI,CAAClG,gBAAgB,GAAG8F,KAAK,CAACK,UAAU,IAAI,CAAC;MAC/C,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACrF,QAAQ,GAAGgF,KAAK,CAACI,QAAQ,IAAI,EAAE;QACpC,IAAI,CAAClG,gBAAgB,GAAG,CAAC;MAC3B;MAEA,IAAI,CAACT,KAAK,CAACE,SAAS,GAClB,CAAC,IAAI,CAACO,gBAAgB,IAAI,CAAC,KAAK,IAAI,CAACc,QAAQ,IAAI,EAAE,CAAC;MAEtD,IAAI,CAACvB,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ,IAAI,EAAE;MAE/C,IAAIgF,KAAK,CAACM,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAAC7G,KAAK,CAACG,OAAO,GAAG,GAAGoG,KAAK,CAACO,SAAS,OAAO;MAChD,CAAC,MAAM;QACL,IAAI,CAAC9G,KAAK,CAACG,OAAO,GAAG,GAAGoG,KAAK,CAACO,SAAS,MAAM;MAC/C;IACF,CAAC,MAAM;MACL,IAAI,CAACrG,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACc,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACvB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;MAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;MACnC,IAAI,CAACV,KAAK,CAACE,SAAS,GAAG,CAAC;MACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IAC3C;IAEA,IAAI,CAACnC,0BAA0B,CAC5B2H,gCAAgC,CAAC,IAAI,CAAC/G,KAAK,CAAC,CAC5C2E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC5C,YAAY,GAAG4C,QAAQ,CAACoC,UAAU;QACvC,IAAI,CAACxF,gCAAgC,GAAGoD,QAAQ,CAACqC,KAAK;MACxD,CAAC,MAAM;QACL,IAAI,CAACjF,YAAY,GAAG,CAAC;QACrB,IAAI,CAACR,gCAAgC,GAAG,EAAE;MAC5C;MAEAiF,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAaAR,aAAaA,CAAA;IACX,MAAMS,SAAS,GAAG,IAAI1L,YAAY,EAAE;IACpC0L,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC1F,QAAQ;IACjCwF,SAAS,CAACnF,YAAY,GAAG,CAAC;IAC1BmF,SAAS,CAACG,IAAI,GAAG,IAAI,CAACnD,mBAAmB,CAACoD,GAAG,CAAEC,CAAC,IAAI;MAClD,OAAO;QACLhF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;QACRiF,OAAO,EAAED,CAAC;QACVE,KAAK,EAAE,CACL;UACE3G,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACG;SACV,EACD;UACE5G,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACI;SACV,EACD;UACE7G,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACK;SACV,EACD;UACE9G,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACM;SACV,EACD;UACE/G,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACO;SACV;OAEJ;IACH,CAAC,CAAC;IACFtB,UAAU,CAAC,MAAK;MACd,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC;EACR;EAEAe,4BAA4BA,CAAClI,KAAU;IACrC,MAAMmI,WAAW,GAAGzM,iBAAiB,CAAC0M,IAAI,CACvCC,MAAM,IAAKA,MAAM,CAAC7J,KAAK,KAAKwB,KAAK,CACnC;IACD,IAAImI,WAAW,EAAE,OAAOA,WAAW,CAAC1J,WAAW;IAC/C,OAAO,EAAE;EACX;EAEA6J,uCAAuCA,CAACtI,KAAU;IAChD,MAAMmI,WAAW,GAAGxM,4BAA4B,CAACyM,IAAI,CAClDC,MAAM,IAAKA,MAAM,CAAC7J,KAAK,KAAKwB,KAAK,CACnC;IACD,IAAImI,WAAW,EAAE,OAAOA,WAAW,CAAC1J,WAAW;IAC/C,OAAO,EAAE;EACX;EAEA8J,kBAAkBA,CAACf,CAAS;IAC1B,MAAMgB,GAAG,GAAGC,IAAI,CAACjB,CAAC,CAAC;IACnB,IAAIkB,SAAS,GAAGF,GAAG,CAAC3D,MAAM;IAC1B,IAAI8D,KAAK,GAAG,IAAIC,UAAU,CAAC,IAAIC,WAAW,CAACH,SAAS,CAAC,CAAC;IAEtD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;MAClCH,KAAK,CAACG,CAAC,CAAC,GAAGN,GAAG,CAACO,UAAU,CAACD,CAAC,CAAC;IAC9B;IACA,OAAOH,KAAK;EACd;EAEAK,YAAYA,CAACC,OAAe,EAAEnK,IAAY;IACxC,IAAIoK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,IAAI,CAACZ,kBAAkB,CAACU,OAAO,CAAC,CAAC,CAAC;IACvD,IAAIG,OAAO,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAE9C,IAAInE,OAAO,GAAGyE,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACzCD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC5E,OAAO,CAAC;IAClCA,OAAO,CAAC6E,KAAK,CAACC,OAAO,GAAG,MAAM;IAC9B9E,OAAO,CAAC+E,IAAI,GAAGV,OAAO;IACtBrE,OAAO,CAACgF,QAAQ,GAAGjL,IAAI;IACvBiG,OAAO,CAACiF,KAAK,EAAE;IACfjF,OAAO,CAACkF,MAAM,EAAE;EAClB;EAEAnM,iBAAiBA,CAACoM,YAA4B;IAC5C,IAAI,CAAC9K,0BAA0B,CAC5B+K,8CAA8C,CAC7CD,YAAY,EACZ,IAAI,CAACxJ,YAAY,CAClB,CACAiE,SAAS,CAAEyF,MAAM,IAAI;MACpB,IAAI,CAAC9E,eAAe,CAACC,SAAS,CAAC;MAC/B,IAAI6E,MAAM,CAACC,QAAQ,IAAI,EAAE,EAAE;QACzB,IAAI,CAACrB,YAAY,CAACoB,MAAM,CAACnB,OAAO,CAACpI,QAAQ,EAAE,EAAEuJ,MAAM,CAACC,QAAQ,CAAC;MAC/D,CAAC,MACCvO,IAAI,CAACwO,IAAI,CAAC;QACRC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,oBAAoB;QAC1BC,iBAAiB,EAAE;OACpB,CAAC;IACN,CAAC,CAAC;EACN;EAEAxD,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAG,IAAI1L,YAAY,EAAE;IACpC0L,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC7G,OAAO;IAChC2G,SAAS,CAACnF,YAAY,GAAG,IAAI,CAACA,YAAY;IAC1CmF,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC9F,gCAAgC,CAAC+F,GAAG,CAAEC,CAAC,IAAI;MAC/D,OAAO;QACLhF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;QACRiF,OAAO,EAAED,CAAC;QACVE,KAAK,EAAE,CACL;UACE3G,QAAQ;UACRvC,KAAK,EAAE,IAAI,CAAC0J,4BAA4B,CAACV,CAAC,CAACxD,cAAc;SAC1D,EACD;UACEjD,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACmD;SACV,EACD;UACE5J,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAAClH;SACV,EACD;UACES,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACoD;SACV,EACD;UACE7J,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACqD;SACV,EACD;UACE9J,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACsD;SACV,EACD;UACE/J,QAAQ;UACRvC,KAAK,EAAE,IAAI,CAAC8J,uCAAuC,CACjDd,CAAC,CAACpH,yBAAyB;SAE9B,EACD;UACEW,QAAQ;;UACR;UACAvC,KAAK,EAAEgJ,CAAC,CAACuD,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG;SAC1C,EACD;UACEhK,QAAQ;UACRvC,KAAK,EAAE;SACR,EACD;UACEuC,QAAQ;UACRvC,KAAK,EAAEgJ,CAAC,CAACwD,gBAAgB,GAAG,cAAc,GAAG;SAC9C;OAEJ;IACH,CAAC,CAAC;IACFvE,UAAU,CAAC,MAAK;MACd,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA8D,YAAYA,CAACC,EAAE;IACb,IAAI,CAACxK,YAAY,GAAGwK,EAAE,CAAC1M,KAAK;IAC5B,IAAI,CAAC6G,eAAe,CAAC,IAAI,CAAC3E,YAAY,CAAC;IACvC;IACAuE,YAAY,CAACkG,OAAO,CAAC,cAAc,EAAED,EAAE,CAAC1M,KAAK,CAAC;IAE9C,IAAI,CAACwB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;IAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;IACnC,IAAI,CAACV,KAAK,CAACM,UAAU,GAAG,IAAI,CAAC8K,gBAAgB;IAC7C,IAAI,CAACpL,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IACzC,IAAI,CAACnC,0BAA0B,CAC5B2H,gCAAgC,CAAC,IAAI,CAAC/G,KAAK,CAAC,CAC5C2E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC5C,YAAY,GAAG4C,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAACxF,gCAAgC,GAAGoD,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAAC1B,gBAAgB,CAACD,SAAS,CAAC;EAClC;EAEA8F,QAAQA,CAAA;IACN,IAAI,CAACrL,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;IAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;IACnC,IAAI,CAACV,KAAK,CAACM,UAAU,GAAG,IAAI,CAAC8K,gBAAgB;IAC7C,IAAI,CAACpL,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IACzC,IAAI,CAACnC,0BAA0B,CAC5B2H,gCAAgC,CAAC,IAAI,CAAC/G,KAAK,CAAC,CAC5C2E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC5C,YAAY,GAAG4C,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAACxF,gCAAgC,GAAGoD,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAAC1B,gBAAgB,CAACD,SAAS,CAAC;EAClC;EAEA+F,cAAcA,CAACJ,EAAE;IACf,IAAI,CAACzJ,kBAAkB,GAAGK,MAAM,CAACoJ,EAAE,CAAC1M,KAAK,CAAC;IAC1C,IAAI,CAACwB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;IAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;IACnC,IAAI,CAACV,KAAK,CAACM,UAAU,GAAG,IAAI,CAAC8K,gBAAgB;IAC7C,IAAI,CAACpL,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IAEzC;IACA0D,YAAY,CAACkG,OAAO,CAAC,oBAAoB,EAAED,EAAE,CAAC1M,KAAK,CAAC;IAEpD,IAAI,CAACY,0BAA0B,CAC5B2H,gCAAgC,CAAC,IAAI,CAAC/G,KAAK,CAAC,CAC5C2E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC5C,YAAY,GAAG4C,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAACxF,gCAAgC,GAAGoD,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAqE,WAAWA,CAAChF,KAAiC;IAC3C,MAAMe,IAAI,GAAGf,KAAK,CAACkB,OAAiC;IACpD,IAAIlB,KAAK,CAACxF,QAAQ,+DAA+C;MAC/D;MACA;MACA;MACA;MACA,IAAI,CAAC5B,MAAM,CAACqM,QAAQ,CAAC,CAAC,kCAAkC,CAAC,EAAE;QACzD;QACA;QACA;QACAC,WAAW,EAAE;UAAEjJ,EAAE,EAAE8E,IAAI,CAAC9E,EAAE;UAAEkJ,SAAS,EAAE;QAAK;OAC7C,CAAC;IACJ,CAAC,MAAM,IACLnF,KAAK,CAACxF,QAAQ,gFACd;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC5B,MAAM,CAACqM,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QACrCC,WAAW,EAAE;UACXE,aAAa,EAAErE,IAAI,CAACsE,aAAa;UACjCC,QAAQ,EAAEvE,IAAI,CAACwE,iBAAiB;UAChCC,IAAI,EAAE;;OAET,CAAC;IACJ,CAAC,MAAM,IACLxF,KAAK,CAACxF,QAAQ,uEACd;MACA;MACA;MACA;MACA,IAAI,CAACiL,oCAAoC,CAAC1E,IAAI,CAAC9E,EAAE,CAAC;IACpD;EACF;EAEAwJ,oCAAoCA,CAACC,qBAA6B;IAChE,MAAMC,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAACpQ,mCAAmC,EAAE;MACtEqQ,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,QAAQ;MACf/E,IAAI,EAAE;QAAE2E,qBAAqB,EAAEA;MAAqB;KACrD,CAAC;IAEFC,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3CmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpC,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGA3E,mBAAmBA,CAAA;IACjB,IAAI2E,MAAM,GAAG,KAAK;IAClB;IACA,MAAM1E,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,CACzC,aAAa,CACI;IAEnB,IAAIF,WAAW,EAAE;MACf0E,MAAM,GAAG,IAAI,CAAC9K,iBAAiB,CAACmN,gBAAgB,mFAE/C;IACH;IAEA,OAAOrC,MAAM;EACf;EAEA;EACA1F,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACnF,gBAAgB,CAACmF,cAAc,EAAE,CAACgI,IAAI,EAAE;EACtD;EAGA;EACAC,6BAA6BA,CAAC3M,KAAU;IACtC,MAAMmI,WAAW,GAAGvM,kBAAkB,CAACwM,IAAI,CACxCC,MAAM,IAAKA,MAAM,CAAC7J,KAAK,KAAKwB,KAAK,CACnC;IACD,IAAImI,WAAW,EAAE,OAAOA,WAAW,CAAC1J,WAAW;IAC/C,OAAO,EAAE;EACX;EAEAmO,uBAAuBA,CAAC5M,KAAU;IAChC,MAAM6M,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC1E,IAAI,CACrCC,MAAM,IAAKA,MAAM,CAACxJ,KAAK,KAAKmB,KAAK,CACnC;IACD,IAAI6M,YAAY,EAAE,OAAOA,YAAY,CAAC/N,IAAI;IAC1C,OAAO,EAAE;EACX;EAEAmH,YAAYA,CAAA;IACV,IAAI,CAACrG,cAAc,CAACmN,OAAO,CAAC;MAAE5M,OAAO,EAAE,UAAU;MAAEF,cAAc,EAAE;IAAI,CAAE,CAAC,CAAC0E,SAAS,CAACC,QAAQ,IAAG;MAC9F,IAAI,CAACkI,SAAS,GAAGlI,QAAQ,CAACqC,KAAK;MAC/B,IAAI,CAAC+F,yBAAyB,GAAGpI,QAAQ,CAACqC,KAAK;MAC/C;MACA,IAAI,CAAC6F,SAAS,GAAG,IAAI,CAACA,SAAS,CAACG,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACrO,KAAK,IAAIqO,OAAO,CAACrO,KAAK,CAACsO,IAAI,EAAE,KAAK,EAAE,CAAC;MAC/F,IAAI,CAACH,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACrO,KAAK,IAAIqO,OAAO,CAACrO,KAAK,CAACsO,IAAI,EAAE,KAAK,EAAE,CAAC;MAC/H;MACA,IAAI,CAACL,SAAS,CAACM,OAAO,CAAC;QAAEtO,IAAI,EAAE,KAAK;QAAED,KAAK,EAAE;MAAE,CAAE,CAAC;IACpD,CAAC,CAAC;EACJ;EAEAkH,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAACtG,kBAAkB,CAACsG,yBAAyB,EAAE,CAACpB,SAAS,CAAC;MACnE0I,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACrQ,yBAAyB,GAAGqQ,IAAI,EAAEC,SAAS,IAAI,IAAI;MAC1D,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvQ,yBAAyB,GAAG,IAAI;MACvC;KACD,CAAC;EACJ;EAEA+I,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACjG,wBAAwB,CAAC0N,kBAAkB,EAAE,CAAC9I,SAAS,CAAC;MAClE0I,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAAClL,iBAAiB,GAAGkL,IAAI,IAAI,IAAI;MACvC,CAAC;MACDE,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACpL,iBAAiB,GAAG,IAAI;MAC/B;KACD,CAAC;EACJ;EAEA5E,0BAA0BA,CAAA;IACxB,MAAM0O,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAAC3P,+BAA+B,EAAE;MAClE6P,KAAK,EAAE,QAAQ;MACf/E,IAAI,EAAE,IAAI,CAAClF,iBAAiB,IAAI;KACjC,CAAC;IAEF8J,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAI,CAACA,MAAM,EAAE;MAEb,MAAMsD,QAAQ,GAAa,IAAIC,QAAQ,EAAE;MACzC,IAAIvD,MAAM,CAAClB,IAAI,EAAE;QACfwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACpK,IAAI,CAAC;QAC7C4O,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAClB,IAAI,CAAC;QACpCwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAAClI,IAAI,CAAC;MAC/C;MAEA,MAAM6M,UAAU,GAAGzD,MAAM,CAAC5H,EAAE,IAAI,IAAI;MACpC,MAAMsL,WAAW,GAAGD,UAAU,GAC1B,IAAI,CAACnO,iBAAiB,CAACqO,wBAAwB,CAAC3D,MAAM,EAAEsD,QAAQ,CAAC,GACjE,IAAI,CAAChO,iBAAiB,CAACsO,wBAAwB,CAAC5D,MAAM,EAAEsD,QAAQ,CAAC;MAErE,MAAMO,MAAM,GAAGJ,UAAU,GAAG,QAAQ,GAAG,QAAQ;MAE/CC,WAAW,CAACnJ,SAAS,CAAC;QACpB0I,IAAI,EAAGzI,QAAQ,IAAI;UACjB,IAAIA,QAAQ,EAAE;YACZ,IAAI,CAACoB,wBAAwB,EAAE;YAC/B,IAAI,CAACrG,cAAc,CAACuO,OAAO,CAAC,6BAA6BD,MAAM,IAAI,EAAE,EAAE,EAAE;cAAEE,IAAI,EAAE;YAAI,CAAE,CAAC;UAC1F,CAAC,MAAM;YACL,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAAC,6BAA6BH,MAAM,4BAA4B,EAAE,IAAI,EAAE;cAAEE,IAAI,EAAE;YAAI,CAAE,CAAC;UACjH;QACF,CAAC;QACDX,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC7N,cAAc,CAAC6N,KAAK,CAAC,qCAAqCS,MAAM,gBAAgB,EAAE,IAAI,EAAE;YAAEE,IAAI,EAAE;UAAI,CAAE,CAAC;UAC5G5B,OAAO,CAACiB,KAAK,CAAC,SAASS,MAAM,mBAAmB,EAAET,KAAK,CAAC;QAC1D;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA7P,6BAA6BA,CAAA;IAC3B,MAAMuO,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAACnQ,kCAAkC,EAAE;MACrEqQ,KAAK,EAAE,OAAO;MACd/E,IAAI,EAAE;KACP,CAAC;IAEF4E,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAAClB,IAAI,EAAE;QACzB,MAAMwE,QAAQ,GAAa,IAAIC,QAAQ,EAAE;QACzCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACpK,IAAI,CAAC;QAC7C4O,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAClB,IAAI,CAAC;QACpCwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAAClI,IAAI,CAAC;QAC7C;QACA,IAAI,CAACtB,iBAAiB,CACnB2O,8BAA8B,CAACX,QAAQ,EAAEtD,MAAM,CAACkE,QAAQ,CAAC,CAAC3J,SAAS,CAAC;UACnE0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,EAAE;cACZ;cACA,IAAI,CAACmB,yBAAyB,EAAE;cAChC,IAAI,CAACpG,cAAc,CAACuO,OAAO,CAAC,2CAA2C,EAAE,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAI,CAAE,CAAC;YAC9F,CAAC,MACI;cACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAAC,kEAAkE,EAAE,IAAI,EAAE;gBAAED,IAAI,EAAE;cAAI,CAAE,CAAC;YACpH;UACF,CAAC;UACDX,KAAK,EAAGA,KAAK,IAAI;YACf;YACAjB,OAAO,CAACiB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC9D;SACD,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EAEA;EACAe,wBAAwBA,CAAA;IACtB,MAAMpH,SAAS,GAAG,IAAI1L,YAAY,EAAE;IACpC0L,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC5D,mBAAmB;IAC5C0D,SAAS,CAACnF,YAAY,GAAG,CAAC;IAC1BmF,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC/E,gBAAgB,CAACgF,GAAG,CAACC,CAAC,KAAK;MAC/ChF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;MACRiF,OAAO,EAAED,CAAC;MACVE,KAAK,EAAE,CACL;QAAE3G,QAAQ,EAAE,kBAAkB;QAAEvC,KAAK,EAAEgJ,CAAC,CAAC/E;MAAgB,CAAE,EAC3D;QAAE1B,QAAQ,EAAE,qBAAqB;QAAEvC,KAAK,EAAEgJ,CAAC,CAAC9E;MAAmB,CAAE,EACjE;QAAE3B,QAAQ,EAAE,mBAAmB;QAAEvC,KAAK,EAAEgJ,CAAC,CAAC7E;MAAiB,CAAE,EAC7D;QAAE5B,QAAQ,EAAE,oBAAoB;QAAEvC,KAAK,EAAEgJ,CAAC,CAAC5E;MAAkB,CAAE,EAC/D;QAAE7B,QAAQ,EAAE,kBAAkB;QAAEvC,KAAK,EAAEgJ,CAAC,CAAC3E;MAAgB,CAAE;KAE9D,CAAC,CAAC;IACH4D,UAAU,CAAC,MAAM,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC,EAAE,EAAE,CAAC;EAChE;EAEAqH,qBAAqBA,CAAA;IACnB,MAAMrH,SAAS,GAAG,IAAI1L,YAAY,EAAE;IACpC0L,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC3D,gBAAgB;IACzCyD,SAAS,CAACnF,YAAY,GAAG,IAAI,CAAC6B,qBAAqB;IACnDsD,SAAS,CAACG,IAAI,GAAG,IAAI,CAAClE,sBAAsB,CAACmE,GAAG,CAACC,CAAC,KAAK;MACrDhF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;MACRiF,OAAO,EAAED,CAAC;MACVE,KAAK,EAAE,CACL;QAAE3G,QAAQ,EAAE,gBAAgB;QAAEvC,KAAK,EAAE,IAAI,CAAC0J,4BAA4B,CAACV,CAAC,CAACxD,cAAc;MAAC,CAAE,EAC1F;QAAEjD,QAAQ,EAAE,YAAY;QAAEvC,KAAK,EAAEgJ,CAAC,CAACiH;MAAU,CAAE,EAC/C;QAAE1N,QAAQ,EAAE,kBAAkB;QAAEvC,KAAK,EAAEgJ,CAAC,CAACkH;MAAgB,CAAE,EAC3D;QAAE3N,QAAQ,EAAE,kBAAkB;QAAEvC,KAAK,EAAE,IAAI,CAACoO,uBAAuB,CAACpF,CAAC,CAACtD,gBAAgB;MAAC,CAAE,EACzF;QAAEnD,QAAQ,EAAE,iBAAiB;QAAEvC,KAAK,EAAE,IAAI,CAACmO,6BAA6B,CAACnF,CAAC,CAACzD,eAAe;MAAC,CAAE,EAC7F;QAAEhD,QAAQ,EAAE,YAAY;QAAEvC,KAAK,EAAEgJ,CAAC,CAACmH;MAAU,CAAE,EAC/C;QAAE5N,QAAQ,EAAE,uBAAuB;QAAEvC,KAAK,EAAEgJ,CAAC,CAACoH;MAAqB,CAAE,EACrE;QAAE7N,QAAQ,EAAE,qBAAqB;QAAEvC,KAAK,EAAEgJ,CAAC,CAACqH,mBAAmB,KAAK,KAAK,GAAG,MAAM,GAAG;MAAI,CAAE,EAC3F;QAAE9N,QAAQ,EAAE,cAAc;QAAEvC,KAAK,EAAEgJ,CAAC,CAACsH,YAAY,CAACjK,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG;MAAI,CAAE,EACvF;QACE9D,QAAQ,EAAE,kBAAkB;QAC5BvC,KAAK,EAAEgJ,CAAC,CAACuH,cAAc,EAAEjJ,QAAQ,CAAC,CAAC,CAAC,GAChC;UAAEkJ,UAAU,EAAE,sBAAsB;UAAEzE,IAAI,EAAE,SAAS;UAAE0E,OAAO,EAAE;QAAmB,CAAE,GACrF;OACL,EACD;QACElO,QAAQ,EAAE,WAAW;QACrBvC,KAAK,EAAEgJ,CAAC,CAACuH,cAAc,EAAEjJ,QAAQ,CAAC,CAAC,CAAC,GAChC;UAAEkJ,UAAU,EAAE,WAAW;UAAEzE,IAAI,EAAE,cAAc;UAAE0E,OAAO,EAAE;QAAY,CAAE,GACxE;OACL,EACD;QACElO,QAAQ,EAAE,sBAAsB;QAChCmO,IAAI,EAAE1H,CAAC,CAACuH,cAAc,EAAEjJ,QAAQ,CAAC,CAAC,CAAC,IAAI0B,CAAC,CAACuH,cAAc,EAAEjJ,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI;QACnFtH,KAAK,EAAEgJ,CAAC,CAAC2H;OACV;KAEJ,CAAC,CAAC;IAEH1I,UAAU,CAAC,MAAM,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC,EAAE,GAAG,CAAC;EACjE;EAEAhB,2BAA2BA,CAACI,KAAK;IAC/B,IAAI,CAAC3E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAAC/B,wBAAwB,CAC1B2G,sBAAsB,CAAC,IAAI,CAACrD,qBAAqB,CAAC,CAClDwB,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACrC,gBAAgB,GAAGqC,QAAQ;MAChC6B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC8H,wBAAwB,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEArI,wBAAwBA,CAACK,KAAK;IAC5B,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAAC5C,iBAAiB,MAAM4C,KAAK,CAACI,QAAQ,IAAI,EAAE,CAAC,EAAE;QACrD,IAAI,CAAC/C,oBAAoB,GAAG2C,KAAK,CAACK,UAAU,IAAI,CAAC;MACnD,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACjD,iBAAiB,GAAG4C,KAAK,CAACI,QAAQ,IAAI,EAAE;QAC7C,IAAI,CAAC/C,oBAAoB,GAAG,CAAC;MAC/B;MAEA,IAAI,CAACE,cAAc,CAAC5D,SAAS,GAC3B,CAAC,IAAI,CAAC0D,oBAAoB,IAAI,CAAC,KAAK,IAAI,CAACD,iBAAiB,IAAI,EAAE,CAAC;MAEnE,IAAI,CAACG,cAAc,CAAC7D,cAAc,GAAG,IAAI,CAAC0D,iBAAiB,IAAI,EAAE;MAEjE,IAAI4C,KAAK,CAACM,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAAC/C,cAAc,CAAC3D,OAAO,GAAG,GAAGoG,KAAK,CAACO,SAAS,OAAO;MACzD,CAAC,MAAM;QACL,IAAI,CAAChD,cAAc,CAAC3D,OAAO,GAAG,GAAGoG,KAAK,CAACO,SAAS,MAAM;MACxD;IACF,CAAC,MAAM;MACL,IAAI,CAAClD,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAACD,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACG,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACd,qBAAqB;MACjE,IAAI,CAACW,cAAc,CAACE,cAAc,GAAG,IAAI,CAACX,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;MACxG,IAAI,CAACS,cAAc,CAACC,eAAe,GAAG,IAAI,CAACT,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;MAC3G,IAAI,CAACQ,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACvG,IAAI,CAACM,cAAc,CAAC5D,SAAS,GAAG,CAAC;MACjC,IAAI,CAAC4D,cAAc,CAAC7D,cAAc,GAAG,IAAI,CAAC0D,iBAAiB;IAC7D;IAEA,IAAI,CAAC9D,wBAAwB,CAC1BuP,8BAA8B,CAAC,IAAI,CAACtL,cAAc,CAAC,CACnDa,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACf,qBAAqB,GAAGe,QAAQ,CAACoC,UAAU;QAChD,IAAI,CAAC5D,sBAAsB,GAAGwB,QAAQ,CAACqC,KAAK;MAC9C,CAAC,MAAM;QACL,IAAI,CAACpD,qBAAqB,GAAG,CAAC;QAC9B,IAAI,CAACT,sBAAsB,GAAG,EAAE;MAClC;MAEAqD,UAAU,CAAC,MAAK;QACd,IAAI,CAAC+H,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAa,qBAAqBA,CAACnE,EAAE;IACtB,IAAI,CAAC/H,qBAAqB,GAAG+H,EAAE,CAAC1M,KAAK;IACrCyG,YAAY,CAACkG,OAAO,CAAC,uBAAuB,EAAED,EAAE,CAAC1M,KAAK,CAAC;IACvD,IAAI,CAACsF,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACd,qBAAqB;IACjE,IAAI,CAACW,cAAc,CAACE,cAAc,GAAG,IAAI,CAACX,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACxG,IAAI,CAACS,cAAc,CAACC,eAAe,GAAG,IAAI,CAACT,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;IAC3G,IAAI,CAACQ,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACvG,IAAI,CAACM,cAAc,CAAC5D,SAAS,GAAG,CAAC;IACjC,IAAI,CAAC4D,cAAc,CAAC7D,cAAc,GAAG,IAAI,CAAC0D,iBAAiB;IAC3D,IAAI,CAAC9D,wBAAwB,CAC1BuP,8BAA8B,CAAC,IAAI,CAACtL,cAAc,CAAC,CACnDa,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACf,qBAAqB,GAAGe,QAAQ,CAACoC,UAAU;MAChD,IAAI,CAAC5D,sBAAsB,GAAGwB,QAAQ,CAACqC,KAAK;MAE5CR,UAAU,CAAC,MAAK;QACd,IAAI,CAAC+H,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAACrI,2BAA2B,CAACZ,SAAS,CAAC;EAC7C;EAEA+J,iBAAiBA,CAAA;IACf,IAAI,CAACxL,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACd,qBAAqB;IACjE,IAAI,CAACW,cAAc,CAACE,cAAc,GAAG,IAAI,CAACX,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACxG,IAAI,CAACS,cAAc,CAACC,eAAe,GAAG,IAAI,CAACT,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;IAC3G,IAAI,CAACQ,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACvG,IAAI,CAACM,cAAc,CAAC5D,SAAS,GAAG,CAAC;IACjC,IAAI,CAAC4D,cAAc,CAAC7D,cAAc,GAAG,IAAI,CAAC0D,iBAAiB;IAC3D,IAAI,CAAC9D,wBAAwB,CAC1BuP,8BAA8B,CAAC,IAAI,CAACtL,cAAc,CAAC,CACnDa,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACf,qBAAqB,GAAGe,QAAQ,CAACoC,UAAU;MAChD,IAAI,CAAC5D,sBAAsB,GAAGwB,QAAQ,CAACqC,KAAK;MAE5CR,UAAU,CAAC,MAAK;QACd,IAAI,CAAC+H,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAe,+BAA+BA,CAACrE,EAAE;IAChC,IAAI,CAAC7H,oBAAoB,GAAGvB,MAAM,CAACoJ,EAAE,CAAC1M,KAAK,CAAC;IAC5C;IACAyG,YAAY,CAACkG,OAAO,CAAC,sBAAsB,EAAED,EAAE,CAAC1M,KAAK,CAAC;EACxD;EAEAgR,uBAAuBA,CAACtE,EAAE;IACxB,IAAI,CAAC5H,qBAAqB,GAAGxB,MAAM,CAACoJ,EAAE,CAAC1M,KAAK,CAAC;IAC7C;IACAyG,YAAY,CAACkG,OAAO,CAAC,uBAAuB,EAAED,EAAE,CAAC1M,KAAK,CAAC;EACzD;EAEAiR,iCAAiCA,CAACvE,EAAE;IAClC,IAAI,CAAC1H,sBAAsB,GAAG0H,EAAE,CAAC1M,KAAK;IACtC;IACAyG,YAAY,CAACkG,OAAO,CAAC,wBAAwB,EAAED,EAAE,CAAC1M,KAAK,CAAC;EAC1D;EAEAkR,oBAAoBA,CAACnJ,KAAiC;IACpD,IAAIA,KAAK,CAACxF,QAAQ,KAAK,qBAAqB,EAAE;MAC5C,IAAI,CAACvB,MAAM,CAAC2M,IAAI,CAAClQ,sCAAsC,EAAE;QACvDoQ,KAAK,EAAE,QAAQ;QACf/E,IAAI,EAAE;UACJqI,GAAG,EAAEpJ,KAAK,CAACkB;;OAEd,CAAC;IACJ,CAAC,MACI,IAAIlB,KAAK,CAACxF,QAAQ,KAAK,cAAc,EAAE;MAC1C,IAAI,CAACvB,MAAM,CAAC2M,IAAI,CAAC9P,0BAA0B,EAAE;QAC3CgQ,KAAK,EAAE,QAAQ;QACf/E,IAAI,EAAE;UACJqI,GAAG,EAAEpJ,KAAK,CAACkB;;OAEd,CAAC;IACJ;EACF;EAEAmI,sBAAsBA,CAACrJ,KAAkC;IACvD,IAAIA,KAAK,CAAC0H,MAAM,KAAK,sBAAsB,EAAE;MAC3C,MAAM/B,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAAC/P,+BAA+B,EAAE;QAClEiQ,KAAK,EAAE,OAAO;QACd/E,IAAI,EAAE;UACJqI,GAAG,EAAEpJ,KAAK,EAAEe,IAAI,EAAEG;;OAErB,CAAC;MAEFyE,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;QAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACyF,OAAO,EAAE;UAC5B,IAAI,CAAChQ,wBAAwB,CAC1BiQ,iBAAiB,CAACvJ,KAAK,CAACe,IAAI,EAAEG,OAAO,EAAEsI,YAAY,EAAE3F,MAAM,CAACyF,OAAO,CAAC,CAAClL,SAAS,CAAC;YAC9E0I,IAAI,EAAGzI,QAAQ,IAAI;cACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;gBACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,2CAA2C,EAAE,EAAE,EAAE;kBAAE7B,IAAI,EAAE;gBAAI,CAAE,CAAC;cAClH,CAAC,MACI;gBACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,kEAAkE,EAAE,IAAI,EAAE;kBAAE7B,IAAI,EAAE;gBAAI,CAAE,CAAC;cACxI;cACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;cACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;YAC7C,CAAC;YACDiI,KAAK,EAAGA,KAAK,IAAI;cACf;cACAjB,OAAO,CAACiB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;YAC9D;WACD,CAAC;QACN;MACF,CAAC,CAAC;IACJ,CAAC,MACI,IAAIjH,KAAK,CAAC0H,MAAM,KAAK,WAAW,EAAE;MACrC,IAAI,CAACnO,UAAU,CAACmQ,UAAU,CAAC;QACzBhC,MAAM,EAAE,QAAQ;QAChBzD,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,yCAAyC;QAC/CzJ,IAAI,EAAE;OACP,EAAGkP,OAAO,IAAI;QACb,IAAIA,OAAO,EAAE;UACX,IAAI,CAACrQ,wBAAwB,CAACsQ,WAAW,CAAC5J,KAAK,CAACe,IAAI,EAAEG,OAAO,EAAEsI,YAAY,CAAC,CAACpL,SAAS,CAAC;YACrF0I,IAAI,EAAGzI,QAAQ,IAAI;cACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;gBACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,mCAAmC,EAAE,EAAE,EAAE;kBAAE7B,IAAI,EAAE;gBAAI,CAAE,CAAC;cAC1G,CAAC,MACI;gBACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,0DAA0D,EAAE,IAAI,EAAE;kBAAE7B,IAAI,EAAE;gBAAI,CAAE,CAAC;cAChI;cACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;cACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;YAC7C,CAAC;YACDiI,KAAK,EAAGA,KAAK,IAAI;cACf;cACAjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;YACtD;WACD,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;EAEA4C,eAAeA,CAAC7J,KAAiC;IAC/C;IACA;IACA,IAAIA,KAAK,CAAC8J,SAAS,EAAE;MACnB,IAAI,CAACxQ,wBAAwB,CAACyQ,iBAAiB,CAAC/J,KAAK,CAACgK,KAAK,CAAC,CAAC5L,SAAS,CAAC;QACrE0I,IAAI,EAAGzI,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;YACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,sDAAsD,EAAE,EAAE,EAAE;cAAE7B,IAAI,EAAE;YAAI,CAAE,CAAC;UAC7H,CAAC,MACI;YACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,8EAA8E,EAAE,IAAI,EAAE;cAAE7B,IAAI,EAAE;YAAI,CAAE,CAAC;UACpJ;UACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;UACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;QAC7C,CAAC;QACDiI,KAAK,EAAGA,KAAK,IAAI;UACfjB,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC3N,wBAAwB,CAAC2Q,mBAAmB,CAACjK,KAAK,CAACgK,KAAK,CAAC,CAAC5L,SAAS,CAAC;QACvE0I,IAAI,EAAGzI,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;YACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,qDAAqD,EAAE,EAAE,EAAE;cAAE7B,IAAI,EAAE;YAAI,CAAE,CAAC;UAC5H,CAAC,MACI;YACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,4EAA4E,EAAE,IAAI,EAAE;cAAE7B,IAAI,EAAE;YAAI,CAAE,CAAC;UAClJ;UACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;UACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;QAC7C,CAAC;QACDiI,KAAK,EAAGA,KAAK,IAAI;UACfjB,OAAO,CAACiB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;OACD,CAAC;IACJ;EACF;EAEA;EACAiD,6BAA6BA,CAAA;IAC3B,MAAMvE,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAACjQ,kCAAkC,EAAE;MACrEmQ,KAAK,EAAE,OAAO;MACd/E,IAAI,EAAE;QACJoJ,kBAAkB,EAAE,IAAI,CAAC1D,yBAAyB,IAAI,EAAE;QACxD2D,WAAW,EAAE,IAAI,CAACpQ,IAAI,IAAI;;KAE7B,CAAC;IAEF2L,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAAC8D,OAAO,EAAE;QAC5B,IAAI,CAAChI,wBAAwB,CAACX,SAAS,CAAC;QACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ;EAEAqL,mBAAmBA,CAAA;IACjB,MAAM1E,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAAC5P,wBAAwB,EAAE;MAC3D8P,KAAK,EAAE,OAAO;MACd/E,IAAI,EAAE;QACJqJ,WAAW,EAAE,IAAI,CAACpQ,IAAI,IAAI;;KAE7B,CAAC;IAEF2L,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACyG,UAAU,EAAE;QAC/B,IAAI,CAAChR,wBAAwB,CAC1BiR,gBAAgB,CAAC1G,MAAM,CAACyG,UAAU,EAAEzG,MAAM,CAACyF,OAAO,CAAC,CAAClL,SAAS,CAAC;UAC7D0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;cACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,mCAAmC,EAAE,EAAE,EAAE;gBAAE7B,IAAI,EAAE;cAAI,CAAE,CAAC;YAC1G,CAAC,MACI;cACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,0DAA0D,EAAE,IAAI,EAAE;gBAAE7B,IAAI,EAAE;cAAI,CAAE,CAAC;YAChI;YACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;YACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;UAC7C,CAAC;UACDiI,KAAK,EAAGA,KAAK,IAAI;YACf;YACAjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACtD;SACD,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EAEAuD,iBAAiBA,CAAA;IACf,MAAM7E,SAAS,GAAG,IAAI,CAAC1M,MAAM,CAAC2M,IAAI,CAAChQ,gCAAgC,EAAE;MACnEkQ,KAAK,EAAE;KACR,CAAC;IAEFH,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAAClB,IAAI,EAAE;QACzB,MAAMwE,QAAQ,GAAa,IAAIC,QAAQ,EAAE;QACzCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACpK,IAAI,CAAC;QAC7C4O,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAClB,IAAI,CAAC;QACpCwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAAClI,IAAI,CAAC;QAC7C;QACA,IAAI,CAACtB,iBAAiB,CACnBsR,gBAAgB,CAACtD,QAAQ,CAAC,CAAC/I,SAAS,CAAC;UACpC0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;cACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,4CAA4C,EAAE,EAAE,EAAE;gBAAE7B,IAAI,EAAE;cAAI,CAAE,CAAC;YACnH,CAAC,MACI;cACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,mEAAmE,EAAE,IAAI,EAAE;gBAAE7B,IAAI,EAAE;cAAI,CAAE,CAAC;YACzI;YACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;YACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;UAC7C,CAAC;UACDiI,KAAK,EAAGA,KAAK,IAAI;YACf;YACAjB,OAAO,CAACiB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACvD;SACD,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EAEAyD,4BAA4BA,CAAA;IAC1B,IAAI,CAACnR,UAAU,CAACmQ,UAAU,CAAC;MACzBhC,MAAM,EAAE,QAAQ;MAChBzD,KAAK,EAAE,iCAAiC;MACxCC,IAAI,EAAE,yCAAyC;MAC/CzJ,IAAI,EAAE;KACP,EAAGkP,OAAO,IAAI;MACb,IAAIA,OAAO,EAAE;QACX,IAAI,CAACrQ,wBAAwB,CAACqR,yBAAyB,EAAE,CAACvM,SAAS,CAAC;UAClE0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACsJ,OAAO,EAAE;cACpB,IAAI,CAACvO,cAAc,CAACuO,OAAO,CAACtJ,QAAQ,CAACoL,OAAO,IAAI,wDAAwD,EAAE,EAAE,EAAE;gBAAE7B,IAAI,EAAE;cAAI,CAAE,CAAC;YAC/H,CAAC,MACI;cACH,IAAI,CAACxO,cAAc,CAACyO,IAAI,CAACxJ,QAAQ,CAACoL,OAAO,IAAI,+EAA+E,EAAE,IAAI,EAAE;gBAAE7B,IAAI,EAAE;cAAI,CAAE,CAAC;YACrJ;YACA,IAAI,CAACjI,wBAAwB,CAACX,SAAS,CAAC;YACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;UAC7C,CAAC;UACDiI,KAAK,EAAGA,KAAK,IAAI;YACf;YACAjB,OAAO,CAACiB,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UAC3E;SACD,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;;;uBApzCWxO,gCAAgC,EAAAvC,EAAA,CAAA0U,iBAAA,CAAA1U,EAAA,CAAA2U,QAAA,GAAA3U,EAAA,CAAA0U,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAA7U,EAAA,CAAA0U,iBAAA,CAAAI,EAAA,CAAAC,0BAAA,GAAA/U,EAAA,CAAA0U,iBAAA,CAAAM,EAAA,CAAAC,iCAAA,GAAAjV,EAAA,CAAA0U,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAAnV,EAAA,CAAA0U,iBAAA,CAAAU,EAAA,CAAAC,4BAAA,GAAArV,EAAA,CAAA0U,iBAAA,CAAAY,EAAA,CAAAC,SAAA,GAAAvV,EAAA,CAAA0U,iBAAA,CAAAc,EAAA,CAAAC,kBAAA,GAAAzV,EAAA,CAAA0U,iBAAA,CAAAgB,EAAA,CAAAC,iBAAA,GAAA3V,EAAA,CAAA0U,iBAAA,CAAAkB,EAAA,CAAAC,cAAA,GAAA7V,EAAA,CAAA0U,iBAAA,CAAAoB,GAAA,CAAAC,cAAA,GAAA/V,EAAA,CAAA0U,iBAAA,CAAAsB,GAAA,CAAAC,wBAAA,GAAAjW,EAAA,CAAA0U,iBAAA,CAAAwB,GAAA,CAAAC,iBAAA,GAAAnW,EAAA,CAAA0U,iBAAA,CAAA0B,GAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAhC9T,gCAAgC;MAAA+T,SAAA;MAAAC,QAAA,GAAAvW,EAAA,CAAAwW,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtDrC9W,EAJR,CAAAC,cAAA,oBAAe,iBAC4B,aACE,aACV,mBAIuE;UAA9ED,EAAA,CAAAS,UAAA,wBAAAuW,0EAAAC,MAAA;YAAA,OAAcF,GAAA,CAAAhO,gBAAA,CAAAkO,MAAA,CAAwB;UAAA,EAAC;UAG/DjX,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,aAAgE,aACrC;UAuBvBD,EAtBA,CAAAkX,UAAA,IAAAC,gDAAA,kBAAiE,IAAAC,kDAAA,oBAGxB,IAAAC,kDAAA,oBAIG,KAAAC,mDAAA,oBAIX,KAAAC,mDAAA,oBAIA,KAAAC,mDAAA,oBAIA,KAAAC,mDAAA,oBAIA;UAIrCzX,EADE,CAAAG,YAAA,EAAM,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAA4C,cACC,qBACJ;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEnEH,EADF,CAAAC,cAAA,0BAAiD,sBAEJ;UADID,EAAA,CAAA0X,gBAAA,yBAAAC,6EAAAV,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAA9S,YAAA,EAAAgT,MAAA,MAAAF,GAAA,CAAA9S,YAAA,GAAAgT,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACrEjX,EAAA,CAAAS,UAAA,6BAAAoX,iFAAAZ,MAAA;YAAA,OAAmBF,GAAA,CAAAvI,YAAA,CAAAyI,MAAA,CAAoB;UAAA,EAAC;UACxCjX,EAAA,CAAAkX,UAAA,KAAAY,uDAAA,yBAA2D;UAKjE9X,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE5DH,EADF,CAAAC,cAAA,0BAAiD,sBAEF;UADLD,EAAA,CAAA0X,gBAAA,yBAAAK,6EAAAd,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAA/R,kBAAA,EAAAiS,MAAA,MAAAF,GAAA,CAAA/R,kBAAA,GAAAiS,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UACpEjX,EAAA,CAAAS,UAAA,6BAAAuX,iFAAAf,MAAA;YAAA,OAAmBF,GAAA,CAAAlI,cAAA,CAAAoI,MAAA,CAAsB;UAAA,EAAC;UAC1CjX,EAAA,CAAAkX,UAAA,KAAAe,uDAAA,yBAAoF;UAK1FjY,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE1DH,EADF,CAAAC,cAAA,0BAAiD,iBAC0B;UAAjCD,EAAA,CAAA0X,gBAAA,2BAAAQ,0EAAAjB,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAApI,gBAAA,EAAAsI,MAAA,MAAAF,GAAA,CAAApI,gBAAA,GAAAsI,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAE1EjX,EAFI,CAAAG,YAAA,EAAyE,EAC1D,EACb;UAEJH,EADF,CAAAC,cAAA,eAAgE,kBAC+B;UAArDD,EAAA,CAAAS,UAAA,mBAAA0X,mEAAA;YAAA,OAASpB,GAAA,CAAAnI,QAAA,EAAU;UAAA,EAAC;UAC1D5O,EAAA,CAAAE,MAAA,gBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAyC,cACV,qBAKW;UAApCD,EADyC,CAAAS,UAAA,wBAAA2X,2EAAAnB,MAAA;YAAA,OAAcF,GAAA,CAAAlO,eAAA,CAAAoO,MAAA,CAAuB;UAAA,EAAC,yBAAAoB,4EAAApB,MAAA;YAAA,OAChEF,GAAA,CAAAjI,WAAA,CAAAmI,MAAA,CAAmB;UAAA,EAAC;UAI3CjX,EAHM,CAAAG,YAAA,EAAY,EACR,EACF,EACE;UAKNH,EAHJ,CAAAC,cAAA,mBAA2C,eAEA,cACV;UAC3BD,EAAA,CAAAsY,SAAA,qBAGY;UAEhBtY,EADE,CAAAG,YAAA,EAAM,EACF;UAKFH,EAFJ,CAAAC,cAAA,cAAgE,cACrC,kBAES;UAA9BD,EAAA,CAAAS,UAAA,mBAAA8X,mEAAA;YAAA,OAASxB,GAAA,CAAAzC,iBAAA,EAAmB;UAAA,EAAC;UAACtU,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,qCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpGH,EAAA,CAAAC,cAAA,kBAC4C;UAA1CD,EAAA,CAAAS,UAAA,mBAAA+X,mEAAA;YAAA,OAASzB,GAAA,CAAA/C,6BAAA,EAA+B;UAAA,EAAC;UAAChU,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5GH,EAAA,CAAAC,cAAA,kBACgC;UAAhCD,EAAA,CAAAS,UAAA,mBAAAgY,mEAAA;YAAA,OAAS1B,GAAA,CAAA5C,mBAAA,EAAqB;UAAA,EAAC;UAACnU,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBACyC;UAAzCD,EAAA,CAAAS,UAAA,mBAAAiY,mEAAA;YAAA,OAAS3B,GAAA,CAAAvC,4BAAA,EAA8B;UAAA,EAAC;UAACxU,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,uBAAc;UAExFF,EAFwF,CAAAG,YAAA,EAAS,EACzF,EACF;UAKFH,EAFJ,CAAAC,cAAA,eAAwE,eAC5B,qBACH;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEnEH,EADF,CAAAC,cAAA,0BAAiD,sBAEK;UADLD,EAAA,CAAA0X,gBAAA,yBAAAiB,6EAAA1B,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAArQ,qBAAA,EAAAuQ,MAAA,MAAAF,GAAA,CAAArQ,qBAAA,GAAAuQ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAC9EjX,EAAA,CAAAS,UAAA,6BAAAmY,iFAAA3B,MAAA;YAAA,OAAmBF,GAAA,CAAAnE,qBAAA,CAAAqE,MAAA,CAA6B;UAAA,EAAC;UACjDjX,EAAA,CAAAkX,UAAA,KAAA2B,uDAAA,yBAA2D;UAKjE7Y,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA0C,qBACH;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE9DH,EADF,CAAAC,cAAA,0BAAiD,sBAEe;UADpBD,EAAA,CAAA0X,gBAAA,yBAAAoB,6EAAA7B,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAAnQ,oBAAA,EAAAqQ,MAAA,MAAAF,GAAA,CAAAnQ,oBAAA,GAAAqQ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UACxEjX,EAAA,CAAAS,UAAA,6BAAAsY,iFAAA9B,MAAA;YAAA,OAAmBF,GAAA,CAAAjE,+BAAA,CAAAmE,MAAA,CAAuC;UAAA,EAAC;UAC3DjX,EAAA,CAAAkX,UAAA,KAAA8B,uDAAA,yBAAuF;UAK7FhZ,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA0C,qBACH;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEhEH,EADF,CAAAC,cAAA,0BAAiD,sBAEiB;UADpBD,EAAA,CAAA0X,gBAAA,yBAAAuB,6EAAAhC,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAAhQ,sBAAA,EAAAkQ,MAAA,MAAAF,GAAA,CAAAhQ,sBAAA,GAAAkQ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAC5EjX,EAAA,CAAAS,UAAA,6BAAAyY,iFAAAjC,MAAA;YAAA,OAAmBF,GAAA,CAAA/D,iCAAA,CAAAiE,MAAA,CAAyC;UAAA,EAAC;UAC7DjX,EAAA,CAAAkX,UAAA,KAAAiC,uDAAA,yBAAsE;UAK5EnZ,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAGFH,EAFJ,CAAAC,cAAA,eAAiE,eACvC,qBACe;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEhEH,EADF,CAAAC,cAAA,0BAAiD,sBAEO;UADVD,EAAA,CAAA0X,gBAAA,yBAAA0B,6EAAAnC,MAAA;YAAAjX,EAAA,CAAA4X,kBAAA,CAAAb,GAAA,CAAAlQ,qBAAA,EAAAoQ,MAAA,MAAAF,GAAA,CAAAlQ,qBAAA,GAAAoQ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAC3EjX,EAAA,CAAAS,UAAA,6BAAA4Y,iFAAApC,MAAA;YAAA,OAAmBF,GAAA,CAAAhE,uBAAA,CAAAkE,MAAA,CAA+B;UAAA,EAAC;UACnDjX,EAAA,CAAAkX,UAAA,KAAAoC,uDAAA,yBAA+E;UAInFtZ,EADE,CAAAG,YAAA,EAAa,EACE;UACjBH,EAAA,CAAAC,cAAA,kBACgC;UAA9BD,EAAA,CAAAS,UAAA,mBAAA8Y,mEAAA;YAAA,OAASxC,GAAA,CAAAlE,iBAAA,EAAmB;UAAA,EAAC;UAAC7S,EAAA,CAAAE,MAAA,cAAM;UAG5CF,EAH4C,CAAAG,YAAA,EAAS,EAC3C,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,cAAyC,cACV,qBAMmB;UAA5CD,EADA,CAAAS,UAAA,wBAAA+Y,2EAAAvC,MAAA;YAAA,OAAcF,GAAA,CAAAtN,wBAAA,CAAAwN,MAAA,CAAgC;UAAA,EAAC,yBAAAwC,4EAAAxC,MAAA;YAAA,OAAgBF,GAAA,CAAA9D,oBAAA,CAAAgE,MAAA,CAA4B;UAAA,EAAC,2BAAAyC,8EAAAzC,MAAA;YAAA,OAAkBF,GAAA,CAAA5D,sBAAA,CAAA8D,MAAA,CAA8B;UAAA,EAAC,6BAAA0C,gFAAA1C,MAAA;YAAA,OAC1HF,GAAA,CAAApD,eAAA,CAAAsD,MAAA,CAAuB;UAAA,EAAC;UAMrDjX,EALQ,CAAAG,YAAA,EAAY,EACR,EACF,EAEE,EACI;;;UA/KGH,EAAA,CAAAI,SAAA,GAAe;UAGkCJ,EAHjD,CAAA4B,UAAA,OAAAmV,GAAA,CAAA7R,QAAA,CAAe,YAAA6R,GAAA,CAAA3R,sBAAA,CAAmC,4BACO,cAAA2R,GAAA,CAAA5R,iBAAA,CAAgC,aAAA4R,GAAA,CAAAzR,SAAA,CAC5E,0BAA0B,wBAAwB,uBAAuB,kBAC9E,oBAAAtF,EAAA,CAAA4Z,eAAA,KAAAC,GAAA,EAA8E;UAMjE7Z,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAvW,yBAAA,CAA+B;UAEtDR,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAnR,eAAA,CAAqB;UAIrB5F,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAnR,eAAA,CAAqB;UAIrB5F,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAvR,UAAA,CAAgB;UAIhBxF,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAvR,UAAA,CAAgB;UAIhBxF,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAvR,UAAA,CAAgB;UAIhBxF,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAA4B,UAAA,SAAAmV,GAAA,CAAAvR,UAAA,IAAAuR,GAAA,CAAAtR,aAAA,CAAiC;UAUOzF,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAA8Z,gBAAA,UAAA/C,GAAA,CAAA9S,YAAA,CAAwB;UAErCjE,EAAA,CAAAI,SAAA,EAAO;UAAPJ,EAAA,CAAA4B,UAAA,YAAAmV,GAAA,CAAAjT,IAAA,CAAO;UASD9D,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAA8Z,gBAAA,UAAA/C,GAAA,CAAA/R,kBAAA,CAA8B;UAEpChF,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAA4B,UAAA,YAAAmV,GAAA,CAAA9R,uBAAA,CAA0B;UASpBjF,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAA8Z,gBAAA,YAAA/C,GAAA,CAAApI,gBAAA,CAA8B;UAW7D3O,EAAA,CAAAI,SAAA,GAAc;UAGAJ,EAHd,CAAA4B,UAAA,OAAAmV,GAAA,CAAAhT,OAAA,CAAc,YAAAgT,GAAA,CAAA1S,qBAAA,CAAsD,4BACb,cAAA0S,GAAA,CAAA/S,gBAAA,CAA+B,aAAA+S,GAAA,CAAAjS,QAAA,CAC1E,oBAAA9E,EAAA,CAAA4Z,eAAA,KAAAC,GAAA,EAAsC,0BAA0B,yBAAyB,uBACxF,kBAAkB;UAW/B7Z,EAAA,CAAAI,SAAA,GAA0B;UAEaJ,EAFvC,CAAA4B,UAAA,OAAAmV,GAAA,CAAA/P,mBAAA,CAA0B,YAAA+P,GAAA,CAAAlR,mBAAA,CAAgC,4BACxC,cAAAkR,GAAA,CAAA5R,iBAAA,CAAgC,aAAA4R,GAAA,CAAAzR,SAAA,CAAuB,0BAA0B,wBACrF,wBAAwB,mBAAmB;UAwBnBtF,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAA8Z,gBAAA,UAAA/C,GAAA,CAAArQ,qBAAA,CAAiC;UAE9C1G,EAAA,CAAAI,SAAA,EAAO;UAAPJ,EAAA,CAAA4B,UAAA,YAAAmV,GAAA,CAAAjT,IAAA,CAAO;UASC9D,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAA8Z,gBAAA,UAAA/C,GAAA,CAAAnQ,oBAAA,CAAgC;UAExC5G,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAA4B,UAAA,YAAAmV,GAAA,CAAAvQ,0BAAA,CAA6B;UASnBxG,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAA8Z,gBAAA,UAAA/C,GAAA,CAAAhQ,sBAAA,CAAkC;UAE5C/G,EAAA,CAAAI,SAAA,EAAY;UAAZJ,EAAA,CAAA4B,UAAA,YAAAmV,GAAA,CAAA1G,SAAA,CAAY;UAUArQ,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA8Z,gBAAA,UAAA/C,GAAA,CAAAlQ,qBAAA,CAAiC;UAE3C7G,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAA4B,UAAA,YAAAmV,GAAA,CAAAtQ,kBAAA,CAAqB;UAchDzG,EAAA,CAAAI,SAAA,GAAuB;UAGgBJ,EAHvC,CAAA4B,UAAA,OAAAmV,GAAA,CAAA9P,gBAAA,CAAuB,YAAA8P,GAAA,CAAA1Q,gBAAA,CAA6B,4BACG,cAAA0Q,GAAA,CAAA5P,oBAAA,CAAmC,aAAA4P,GAAA,CAAA7P,iBAAA,CACrE,oBAAAlH,EAAA,CAAA4Z,eAAA,KAAAC,GAAA,EAAsC,0BAA0B,yBACtE,uBAAuB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}