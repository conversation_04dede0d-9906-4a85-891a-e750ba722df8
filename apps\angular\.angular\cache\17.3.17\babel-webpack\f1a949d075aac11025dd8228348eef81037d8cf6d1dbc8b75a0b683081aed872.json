{"ast": null, "code": "import * as i1 from '@abp/ng.core';\nimport { mapEnumToOptions } from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nvar index$3 = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nlet EditionService = /*#__PURE__*/(() => {\n  class EditionService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'SaasHost';\n      this.create = (input, config) => this.restService.request({\n        method: 'POST',\n        url: '/api/saas/editions',\n        body: input\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.delete = (id, config) => this.restService.request({\n        method: 'DELETE',\n        url: `/api/saas/editions/${id}`\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.get = (id, config) => this.restService.request({\n        method: 'GET',\n        url: `/api/saas/editions/${id}`\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getAllList = config => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/editions/all'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getList = (input, config) => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/editions',\n        params: {\n          filter: input.filter,\n          sorting: input.sorting,\n          skipCount: input.skipCount,\n          maxResultCount: input.maxResultCount\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getPlanLookup = config => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/editions/plan-lookup'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getUsageStatistics = config => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/editions/statistics/usage-statistic'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.moveAllTenants = (id, editionId, config) => this.restService.request({\n        method: 'PUT',\n        url: `/api/saas/editions/${id}/move-all-tenants`,\n        params: {\n          editionId\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.update = (id, input, config) => this.restService.request({\n        method: 'PUT',\n        url: `/api/saas/editions/${id}`,\n        body: input\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n    }\n    static {\n      this.ɵfac = function EditionService_Factory(t) {\n        return new (t || EditionService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: EditionService,\n        factory: EditionService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EditionService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SubscriptionService = /*#__PURE__*/(() => {\n  class SubscriptionService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'SaasHost';\n      this.createSubscription = (editionId, tenantId, config) => this.restService.request({\n        method: 'POST',\n        url: '/api/saas/subscription',\n        params: {\n          editionId,\n          tenantId\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n    }\n    static {\n      this.ɵfac = function SubscriptionService_Factory(t) {\n        return new (t || SubscriptionService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: SubscriptionService,\n        factory: SubscriptionService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SubscriptionService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TenantService = /*#__PURE__*/(() => {\n  class TenantService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'SaasHost';\n      this.applyDatabaseMigrations = (id, config) => this.restService.request({\n        method: 'POST',\n        url: `/api/saas/tenants/${id}/apply-database-migrations`\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.checkConnectionString = (connectionString, config) => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/tenants/check-connection-string',\n        params: {\n          connectionString\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.create = (input, config) => this.restService.request({\n        method: 'POST',\n        url: '/api/saas/tenants',\n        body: input\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.delete = (id, config) => this.restService.request({\n        method: 'DELETE',\n        url: `/api/saas/tenants/${id}`\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.get = (id, config) => this.restService.request({\n        method: 'GET',\n        url: `/api/saas/tenants/${id}`\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getConnectionStrings = (id, config) => this.restService.request({\n        method: 'GET',\n        url: `/api/saas/tenants/${id}/connection-strings`\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getDatabases = config => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/tenants/databases'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getEditionLookup = config => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/tenants/lookup/editions'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getList = (input, config) => this.restService.request({\n        method: 'GET',\n        url: '/api/saas/tenants',\n        params: {\n          filter: input.filter,\n          getEditionNames: input.getEditionNames,\n          editionId: input.editionId,\n          expirationDateMin: input.expirationDateMin,\n          expirationDateMax: input.expirationDateMax,\n          activationState: input.activationState,\n          sorting: input.sorting,\n          skipCount: input.skipCount,\n          maxResultCount: input.maxResultCount\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.setPassword = (id, input, config) => this.restService.request({\n        method: 'PUT',\n        url: `/api/saas/tenants/${id}/set-password`,\n        body: input\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.update = (id, input, config) => this.restService.request({\n        method: 'PUT',\n        url: `/api/saas/tenants/${id}`,\n        body: input\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.updateConnectionStrings = (id, input, config) => this.restService.request({\n        method: 'PUT',\n        url: `/api/saas/tenants/${id}/connection-strings`,\n        body: input\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n    }\n    static {\n      this.ɵfac = function TenantService_Factory(t) {\n        return new (t || TenantService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: TenantService,\n        factory: TenantService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TenantService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nvar index$2 = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nvar PaymentRequestState = /*#__PURE__*/function (PaymentRequestState) {\n  PaymentRequestState[PaymentRequestState[\"Waiting\"] = 0] = \"Waiting\";\n  PaymentRequestState[PaymentRequestState[\"Completed\"] = 1] = \"Completed\";\n  PaymentRequestState[PaymentRequestState[\"Failed\"] = 2] = \"Failed\";\n  PaymentRequestState[PaymentRequestState[\"Refunded\"] = 3] = \"Refunded\";\n  return PaymentRequestState;\n}(PaymentRequestState || {});\nconst paymentRequestStateOptions = mapEnumToOptions(PaymentRequestState);\nvar PaymentType = /*#__PURE__*/function (PaymentType) {\n  PaymentType[PaymentType[\"OneTime\"] = 0] = \"OneTime\";\n  PaymentType[PaymentType[\"Subscription\"] = 1] = \"Subscription\";\n  return PaymentType;\n}(PaymentType || {});\nconst paymentTypeOptions = mapEnumToOptions(PaymentType);\nvar index$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  get PaymentRequestState() {\n    return PaymentRequestState;\n  },\n  get PaymentType() {\n    return PaymentType;\n  },\n  paymentRequestStateOptions: paymentRequestStateOptions,\n  paymentTypeOptions: paymentTypeOptions\n});\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Plans: index$2,\n  Requests: index$1\n});\nvar TenantActivationState = /*#__PURE__*/function (TenantActivationState) {\n  TenantActivationState[TenantActivationState[\"Active\"] = 0] = \"Active\";\n  TenantActivationState[TenantActivationState[\"ActiveWithLimitedTime\"] = 1] = \"ActiveWithLimitedTime\";\n  TenantActivationState[TenantActivationState[\"Passive\"] = 2] = \"Passive\";\n  return TenantActivationState;\n}(TenantActivationState || {});\nconst tenantActivationStateOptions = mapEnumToOptions(TenantActivationState);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { index$3 as Dtos, EditionService, index as Payment, SubscriptionService, TenantActivationState, TenantService, tenantActivationStateOptions };\n//# sourceMappingURL=volo-abp.ng.saas-proxy.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}