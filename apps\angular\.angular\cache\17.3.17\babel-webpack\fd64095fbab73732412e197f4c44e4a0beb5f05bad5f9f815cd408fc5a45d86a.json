{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n  if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11)) return 1;\n  if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 && !(i % 100 >= 12 && i % 100 <= 14) || f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 && !(f % 100 >= 12 && f % 100 <= 14)) return 3;\n  return 5;\n}\nexport default [\"sr-Cyrl\", [[\"AM\", \"PM\"], u, u], [[\"пре подне\", \"по подне\"], [\"AM\", \"PM\"], u], [[\"н\", \"п\", \"у\", \"с\", \"ч\", \"п\", \"с\"], [\"нед\", \"пон\", \"уто\", \"сре\", \"чет\", \"пет\", \"суб\"], [\"недеља\", \"понедељак\", \"уторак\", \"среда\", \"четвртак\", \"петак\", \"субота\"], [\"не\", \"по\", \"ут\", \"ср\", \"че\", \"пе\", \"су\"]], u, [[\"ј\", \"ф\", \"м\", \"а\", \"м\", \"ј\", \"ј\", \"а\", \"с\", \"о\", \"н\", \"д\"], [\"јан\", \"феб\", \"мар\", \"апр\", \"мај\", \"јун\", \"јул\", \"авг\", \"сеп\", \"окт\", \"нов\", \"дец\"], [\"јануар\", \"фебруар\", \"март\", \"април\", \"мај\", \"јун\", \"јул\", \"август\", \"септембар\", \"октобар\", \"новембар\", \"децембар\"]], u, [[\"п.н.е.\", \"н.е.\"], [\"п. н. е.\", \"н. е.\"], [\"пре нове ере\", \"нове ере\"]], 1, [6, 0], [\"d.M.yy.\", \"d. M. y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"RSD\", \"RSD\", \"српски динар\", {\n  \"AUD\": [u, \"$\"],\n  \"BAM\": [\"КМ\", \"KM\"],\n  \"BYN\": [u, \"р.\"],\n  \"GEL\": [u, \"ლ\"],\n  \"KRW\": [u, \"₩\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"VND\": [u, \"₫\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}