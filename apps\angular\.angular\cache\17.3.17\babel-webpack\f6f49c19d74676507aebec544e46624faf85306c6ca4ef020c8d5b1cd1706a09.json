{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"ne-IN\", [[\"पूर्वाह्न\", \"अपराह्न\"], u, u], u, [[\"आ\", \"सो\", \"म\", \"बु\", \"बि\", \"शु\", \"श\"], [\"आइत\", \"सोम\", \"मङ्गल\", \"बुध\", \"बिहि\", \"शुक्र\", \"शनि\"], [\"आइतबार\", \"सोमबार\", \"मङ्गलबार\", \"बुधबार\", \"बिहिबार\", \"शुक्रबार\", \"शनिबार\"], [\"आइत\", \"सोम\", \"मङ्गल\", \"बुध\", \"बिहि\", \"शुक्र\", \"शनि\"]], u, [[\"जन\", \"फेब\", \"मार्च\", \"अप्र\", \"मे\", \"जुन\", \"जुल\", \"अग\", \"सेप\", \"अक्टो\", \"नोभे\", \"डिसे\"], [\"जनवरी\", \"फेब्रुअरी\", \"मार्च\", \"अप्रिल\", \"मे\", \"जुन\", \"जुलाई\", \"अगस्ट\", \"सेप्टेम्बर\", \"अक्टोबर\", \"नोभेम्बर\", \"डिसेम्बर\"], u], [[\"जन\", \"फेेब\", \"मार्च\", \"अप्र\", \"मे\", \"जुन\", \"जुल\", \"अग\", \"सेप\", \"अक्टो\", \"नोभे\", \"डिसे\"], [\"जनवरी\", \"फेब्रुअरी\", \"मार्च\", \"अप्रिल\", \"मे\", \"जुन\", \"जुलाई\", \"अगस्ट\", \"सेप्टेम्बर\", \"अक्टोबर\", \"नोभेम्बर\", \"डिसेम्बर\"], u], [[\"ईसा पूर्व\", \"सन्\"], u, u], 0, [0, 0], [\"yy/M/d\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤ #,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"भारतीय रूपिँया\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"NPR\": [\"नेरू\", \"रू\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ne-IN.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"ne-IN\", [[\"पूर्वाह्न\", \"अपराह्न\"], u, u], u, [[\"आ\", \"सो\", \"म\", \"बु\", \"बि\", \"शु\", \"श\"], [\"आइत\", \"सोम\", \"मङ्गल\", \"बुध\", \"बिहि\", \"शुक्र\", \"शनि\"], [\"आइतबार\", \"सोमबार\", \"मङ्गलबार\", \"बुधबार\", \"बिहिबार\", \"शुक्रबार\", \"शनिबार\"], [\"आइत\", \"सोम\", \"मङ्गल\", \"बुध\", \"बिहि\", \"शुक्र\", \"शनि\"]], u, [[\"जन\", \"फेब\", \"मार्च\", \"अप्र\", \"मे\", \"जुन\", \"जुल\", \"अग\", \"सेप\", \"अक्टो\", \"नोभे\", \"डिसे\"], [\"जनवरी\", \"फेब्रुअरी\", \"मार्च\", \"अप्रिल\", \"मे\", \"जुन\", \"जुलाई\", \"अगस्ट\", \"सेप्टेम्बर\", \"अक्टोबर\", \"नोभेम्बर\", \"डिसेम्बर\"], u], [[\"जन\", \"फेेब\", \"मार्च\", \"अप्र\", \"मे\", \"जुन\", \"जुल\", \"अग\", \"सेप\", \"अक्टो\", \"नोभे\", \"डिसे\"], [\"जनवरी\", \"फेब्रुअरी\", \"मार्च\", \"अप्रिल\", \"मे\", \"जुन\", \"जुलाई\", \"अगस्ट\", \"सेप्टेम्बर\", \"अक्टोबर\", \"नोभेम्बर\", \"डिसेम्बर\"], u], [[\"ईसा पूर्व\", \"सन्\"], u, u], 0, [0, 0], [\"yy/M/d\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤ #,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"भारतीय रूपिँया\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"NPR\": [\"नेरू\", \"रू\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,SAAS,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,gBAAgB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}