{"ast": null, "code": "import { SharedModule } from '@app/shared/shared.module';\nimport { ResourcePageComponent } from './containers/resource-page/resource-page.component';\nimport { ResourcePageRoutingModule } from './resource-page.routing.module';\nimport { CommonModule } from '@angular/common';\nimport { CoreModule } from '@abp/ng.core';\nimport { SearchHeadersComponent } from './containers/search-headers/search-headers.component';\nimport * as i0 from \"@angular/core\";\nexport class ResourcePageModule {\n  static {\n    this.ɵfac = function ResourcePageModule_Factory(t) {\n      return new (t || ResourcePageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResourcePageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CommonModule, CoreModule, ResourcePageRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResourcePageModule, {\n    declarations: [ResourcePageComponent, SearchHeadersComponent],\n    imports: [SharedModule, CommonModule, CoreModule, ResourcePageRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "ResourcePageComponent", "ResourcePageRoutingModule", "CommonModule", "CoreModule", "SearchHeadersComponent", "ResourcePageModule", "declarations", "imports"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\resource-page\\resource-page.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@app/shared/shared.module';\r\n\r\nimport { ResourcePageComponent } from './containers/resource-page/resource-page.component';\r\nimport { ResourcePageRoutingModule } from './resource-page.routing.module';\r\n\r\nimport { CommonModule } from '@angular/common';\r\nimport { CoreModule } from '@abp/ng.core';\r\nimport { SearchHeadersComponent } from './containers/search-headers/search-headers.component';\r\n\r\n@NgModule({\r\n\r\n    imports: [\r\n\r\n        SharedModule,\r\n        CommonModule,\r\n        CoreModule,\r\n        ResourcePageRoutingModule,\r\n    ],\r\n\r\n    declarations: [\r\n      ResourcePageComponent,\r\n      SearchHeadersComponent\r\n    ],\r\n})\r\nexport class ResourcePageModule { }\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,2BAA2B;AAExD,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,yBAAyB,QAAQ,gCAAgC;AAE1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,sBAAsB,QAAQ,sDAAsD;;AAiB7F,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAXvBN,YAAY,EACZG,YAAY,EACZC,UAAU,EACVF,yBAAyB;IAAA;EAAA;;;2EAQpBI,kBAAkB;IAAAC,YAAA,GAJzBN,qBAAqB,EACrBI,sBAAsB;IAAAG,OAAA,GARpBR,YAAY,EACZG,YAAY,EACZC,UAAU,EACVF,yBAAyB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}