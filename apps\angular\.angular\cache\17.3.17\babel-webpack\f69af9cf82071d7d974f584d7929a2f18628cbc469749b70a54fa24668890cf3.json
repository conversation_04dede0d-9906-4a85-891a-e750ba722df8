{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\templates\\models.ts"], "sourcesContent": ["import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';\r\n\r\nexport interface ChoicesByUrl {\r\n  url?: string;\r\n  path?: string;\r\n  valueName?: string;\r\n  titleName?: string;\r\n}\r\n\r\nexport interface ColumnClass {\r\n  name?: string;\r\n  cellType?: string;\r\n  choices: string[];\r\n  title?: string;\r\n  inputType?: string;\r\n  inputWidth?: string;\r\n  size?: number;\r\n  min?: number;\r\n  max?: number;\r\n  requiredIf?: string;\r\n  validators: ValidatorClass[];\r\n  defaultValueExpression?: string;\r\n  defaultValue?: string;\r\n  readOnly?: boolean;\r\n  choicesByUrl: ChoicesByUrl;\r\n  maxLength?: number;\r\n  inputMask?: string;\r\n  numericDigits?: number;\r\n  prefix?: string;\r\n  options: Options;\r\n  allowMultiple?: boolean;\r\n  storeDataAsText?: boolean;\r\n  waitForUpload?: boolean;\r\n  acceptedTypes?: string;\r\n  maxSize?: number;\r\n  expression?: string;\r\n  isRequired?: boolean;\r\n  titleLocation?: string;\r\n  minWidth?: string;\r\n  maxWidth?: string;\r\n  html?: string;\r\n  rows?: number;\r\n  autoGrow?: boolean;\r\n  allowResize?: boolean;\r\n  showPreview?: boolean;\r\n}\r\n\r\nexport interface GetTemplateListDto extends PagedAndSortedResultRequestDto {\r\n}\r\n\r\nexport interface Options {\r\n  allowMinus?: boolean;\r\n  digits?: number;\r\n  min?: number;\r\n  max?: number;\r\n  prefix?: string;\r\n  suffix?: string;\r\n  digitsOptional?: boolean;\r\n  stripLeadingZeroes?: boolean;\r\n  unmaskAsNumber?: boolean;\r\n}\r\n\r\nexport interface PageClass {\r\n  elements: PageElementClass[];\r\n  name?: string;\r\n  title?: string;\r\n  visibleIf?: string;\r\n  type?: string;\r\n}\r\n\r\nexport interface PageElementClass {\r\n  elements: PageElementClass[];\r\n  name?: string;\r\n  questionTitleLocation?: string;\r\n  title?: string;\r\n  type?: string;\r\n  isRequired?: boolean;\r\n  visibleIf?: string;\r\n  enableIf?: string;\r\n  defaultValueExpression?: string;\r\n  defaultValue?: string;\r\n  inputType?: string;\r\n  inputWidth?: string;\r\n  size?: number;\r\n  hideNumber?: boolean;\r\n  choices: string[];\r\n  allowAddRows?: boolean;\r\n  allowRemoveRows?: boolean;\r\n  rowCount?: number;\r\n  maxRowCount?: number;\r\n  visible?: boolean;\r\n  requiredIf?: string;\r\n  maxLength?: number;\r\n  showNoneItem?: boolean;\r\n  showOtherItem?: boolean;\r\n  allowClear?: boolean;\r\n  maxSelectedChoices?: number;\r\n  minValueExpression?: string;\r\n  maxValueExpression?: string;\r\n  minErrorText?: string;\r\n  maxErrorText?: string;\r\n  columns: ColumnClass[];\r\n  choicesByUrl: ChoicesByUrl;\r\n  inputMask?: string;\r\n  numericDigits?: number;\r\n  prefix?: string;\r\n  options: Options;\r\n  allowMultiple?: boolean;\r\n  storeDataAsText?: boolean;\r\n  waitForUpload?: boolean;\r\n  acceptedTypes?: string;\r\n  maxSize?: number;\r\n  validators: ValidatorClass[];\r\n  expression?: string;\r\n  state?: string;\r\n  width?: string;\r\n  titleLocation?: string;\r\n  startWithNewLine?: boolean;\r\n  minWidth?: string;\r\n  maxWidth?: string;\r\n  html?: string;\r\n  rows?: number;\r\n  autoGrow?: boolean;\r\n  allowResize?: boolean;\r\n  showPreview?: boolean;\r\n}\r\n\r\nexport interface SurveyDto {\r\n  logoPosition?: string;\r\n  pages: PageClass[];\r\n  questionErrorLocation?: string;\r\n  showProgressBar?: string;\r\n  showQuestionNumbers?: string;\r\n  title?: string;\r\n  widthMode?: string;\r\n  triggers: Trigger[];\r\n  showTitle?: boolean;\r\n  clearInvisibleValues?: string;\r\n  showPreviewBeforeComplete?: string;\r\n}\r\n\r\nexport interface TemplateDto extends EntityDto<string> {\r\n  name: string;\r\n  description?: string;\r\n  effectiveDate?: string;\r\n  expiryDate?: string;\r\n  survey: SurveyDto;\r\n  isActive: boolean;\r\n  holdBusinessJson?: string;\r\n  ipBusinessJson?: string;\r\n  outSourcingJson?: string;\r\n  otherActivityJson?: string;\r\n}\r\n\r\nexport interface Trigger {\r\n  type?: string;\r\n  expression?: string;\r\n  setToName?: string;\r\n  setValue?: string;\r\n}\r\n\r\nexport interface ValidatorClass {\r\n  type?: string;\r\n  maxLength?: number;\r\n  expression?: string;\r\n  text?: string;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}