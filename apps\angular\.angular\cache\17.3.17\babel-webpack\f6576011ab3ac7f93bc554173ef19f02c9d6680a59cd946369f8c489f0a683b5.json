{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class RedFlagService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.getAllRedFlagSettings = config => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/RedFlag/GetAllRedFlagSettings'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getRedFlagEventsDescriptions = (declarationId, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/RedFlag/RedFlagEvents',\n      params: {\n        declarationId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getRedFlags = (request, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/RedFlag',\n      params: {\n        isAscending: request.isAscending,\n        sorting: request.sorting,\n        skipCount: request.skipCount,\n        maxResultCount: request.maxResultCount\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.processRedFlagsOnAllDeclarationsByForced = (forced, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/RedFlag/ProcessRedFlagsOnAllDeclarations',\n      params: {\n        forced\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.updateRedFlag = (redFlagDto, config) => this.restService.request({\n      method: 'PUT',\n      url: '/api/ESService/RedFlag',\n      body: redFlagDto\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function RedFlagService_Factory(t) {\n      return new (t || RedFlagService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RedFlagService,\n      factory: RedFlagService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["RedFlagService", "constructor", "restService", "apiName", "getAllRedFlagSettings", "config", "request", "method", "url", "getRedFlagEventsDescriptions", "declarationId", "params", "getRedFlags", "isAscending", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "processRedFlagsOnAllDeclarationsByForced", "forced", "updateRedFlag", "redFlagDto", "body", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\red-flags\\red-flag.service.ts"], "sourcesContent": ["import type { GenerateRedFlagsResultDto, RedFlagDto, RedFlagRequestDto, RedFlagResultDto } from './models';\r\nimport { RestService, Rest } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class RedFlagService {\r\n  apiName = 'EconomicSubstanceService';\r\n  \r\n\r\n  getAllRedFlagSettings = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, RedFlagDto[]>({\r\n      method: 'GET',\r\n      url: '/api/ESService/RedFlag/GetAllRedFlagSettings',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getRedFlagEventsDescriptions = (declarationId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string[]>({\r\n      method: 'GET',\r\n      url: '/api/ESService/RedFlag/RedFlagEvents',\r\n      params: { declarationId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getRedFlags = (request: RedFlagRequestDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, RedFlagResultDto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/RedFlag',\r\n      params: { isAscending: request.isAscending, sorting: request.sorting, skipCount: request.skipCount, maxResultCount: request.maxResultCount },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  processRedFlagsOnAllDeclarationsByForced = (forced: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, GenerateRedFlagsResultDto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/RedFlag/ProcessRedFlagsOnAllDeclarations',\r\n      params: { forced },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  updateRedFlag = (redFlagDto: RedFlagDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, RedFlagDto>({\r\n      method: 'PUT',\r\n      url: '/api/ESService/RedFlag',\r\n      body: redFlagDto,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,cAAc;EA+CzBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA9C/B,KAAAC,OAAO,GAAG,0BAA0B;IAGpC,KAAAC,qBAAqB,GAAIC,MAA6B,IACpD,IAAI,CAACH,WAAW,CAACI,OAAO,CAAoB;MAC1CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGE;IAAM,CAAE,CAAC;IAGtC,KAAAI,4BAA4B,GAAG,CAACC,aAAqB,EAAEL,MAA6B,KAClF,IAAI,CAACH,WAAW,CAACI,OAAO,CAAgB;MACtCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,sCAAsC;MAC3CG,MAAM,EAAE;QAAED;MAAa;KACxB,EACD;MAAEP,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGE;IAAM,CAAE,CAAC;IAGtC,KAAAO,WAAW,GAAG,CAACN,OAA0B,EAAED,MAA6B,KACtE,IAAI,CAACH,WAAW,CAACI,OAAO,CAAwB;MAC9CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,wBAAwB;MAC7BG,MAAM,EAAE;QAAEE,WAAW,EAAEP,OAAO,CAACO,WAAW;QAAEC,OAAO,EAAER,OAAO,CAACQ,OAAO;QAAEC,SAAS,EAAET,OAAO,CAACS,SAAS;QAAEC,cAAc,EAAEV,OAAO,CAACU;MAAc;KAC3I,EACD;MAAEb,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGE;IAAM,CAAE,CAAC;IAGtC,KAAAY,wCAAwC,GAAG,CAACC,MAAc,EAAEb,MAA6B,KACvF,IAAI,CAACH,WAAW,CAACI,OAAO,CAAiC;MACvDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,yDAAyD;MAC9DG,MAAM,EAAE;QAAEO;MAAM;KACjB,EACD;MAAEf,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGE;IAAM,CAAE,CAAC;IAGtC,KAAAc,aAAa,GAAG,CAACC,UAAsB,EAAEf,MAA6B,KACpE,IAAI,CAACH,WAAW,CAACI,OAAO,CAAkB;MACxCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,wBAAwB;MAC7Ba,IAAI,EAAED;KACP,EACD;MAAEjB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGE;IAAM,CAAE,CAAC;EAES;;;uBA/CpCL,cAAc,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAdzB,cAAc;MAAA0B,OAAA,EAAd1B,cAAc,CAAA2B,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}