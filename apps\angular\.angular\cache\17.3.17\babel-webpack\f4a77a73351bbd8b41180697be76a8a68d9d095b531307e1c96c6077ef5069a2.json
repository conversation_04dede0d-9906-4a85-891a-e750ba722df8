{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** Defines the type of a Hub Message. */\nexport var MessageType;\n(function (MessageType) {\n  /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\n  MessageType[MessageType[\"Invocation\"] = 1] = \"Invocation\";\n  /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\n  MessageType[MessageType[\"StreamItem\"] = 2] = \"StreamItem\";\n  /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\n  MessageType[MessageType[\"Completion\"] = 3] = \"Completion\";\n  /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\n  MessageType[MessageType[\"StreamInvocation\"] = 4] = \"StreamInvocation\";\n  /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\n  MessageType[MessageType[\"CancelInvocation\"] = 5] = \"CancelInvocation\";\n  /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\n  MessageType[MessageType[\"Ping\"] = 6] = \"Ping\";\n  /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\n  MessageType[MessageType[\"Close\"] = 7] = \"Close\";\n})(MessageType || (MessageType = {}));", "map": {"version": 3, "names": ["MessageType"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Defines the type of a Hub Message. */\r\nexport var MessageType;\r\n(function (MessageType) {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    MessageType[MessageType[\"Invocation\"] = 1] = \"Invocation\";\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    MessageType[MessageType[\"StreamItem\"] = 2] = \"StreamItem\";\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    MessageType[MessageType[\"Completion\"] = 3] = \"Completion\";\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    MessageType[MessageType[\"StreamInvocation\"] = 4] = \"StreamInvocation\";\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    MessageType[MessageType[\"CancelInvocation\"] = 5] = \"CancelInvocation\";\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    MessageType[MessageType[\"Ping\"] = 6] = \"Ping\";\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    MessageType[MessageType[\"Close\"] = 7] = \"Close\";\r\n})(MessageType || (MessageType = {}));\r\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACpB;EACAA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzD;EACAA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzD;EACAA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzD;EACAA,WAAW,CAACA,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACrE;EACAA,WAAW,CAACA,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACrE;EACAA,WAAW,CAACA,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC7C;EACAA,WAAW,CAACA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AACnD,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}