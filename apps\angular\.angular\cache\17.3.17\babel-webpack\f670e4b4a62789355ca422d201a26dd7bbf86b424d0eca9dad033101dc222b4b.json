{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { AllEntityPeronStatus, AllUBOStatus, EntityPersonType } from '../../../../shared/constants';\nimport * as uuid from 'uuid';\nimport { ExchangeReason } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/grid-list\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@ngx-validate/core\";\nimport * as i12 from \"@angular/common\";\nfunction RecipientDialogBoxComponent_mat_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r1.name, \" \");\n  }\n}\nfunction RecipientDialogBoxComponent_mat_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r2.name, \" \");\n  }\n}\nfunction RecipientDialogBoxComponent_mat_form_field_35_mat_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r3.name, \" \");\n  }\n}\nfunction RecipientDialogBoxComponent_mat_form_field_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"TIN Issued Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-select\", 20);\n    i0.ɵɵtemplate(4, RecipientDialogBoxComponent_mat_form_field_35_mat_option_4_Template, 2, 2, \"mat-option\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.countries);\n  }\n}\nfunction RecipientDialogBoxComponent_mat_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r5.description, \" \");\n  }\n}\nfunction RecipientDialogBoxComponent_mat_form_field_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"BO Nationality\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 21);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RecipientDialogBoxComponent_mat_form_field_46_mat_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r6.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r6.description, \" \");\n  }\n}\nfunction RecipientDialogBoxComponent_mat_form_field_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"UBO Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-select\", 22);\n    i0.ɵɵtemplate(4, RecipientDialogBoxComponent_mat_form_field_46_mat_option_4_Template, 2, 2, \"mat-option\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.boStatuses);\n  }\n}\nexport class RecipientDialogBoxComponent {\n  constructor(dialogRef, cdr, countryService, data) {\n    this.dialogRef = dialogRef;\n    this.cdr = cdr;\n    this.countryService = countryService;\n    this.data = data;\n    this.exchangeform = new FormGroup({\n      name: new FormControl('', [Validators.required]),\n      jurisdiction: new FormControl(null, [Validators.required]),\n      addressline1: new FormControl('', [Validators.required]),\n      addressline2: new FormControl(''),\n      country: new FormControl(null, [Validators.required]),\n      tin: new FormControl(''),\n      countryTin: new FormControl(''),\n      otherIden: new FormControl(''),\n      boNationality: new FormControl(''),\n      typeOfEntity: new FormControl('', [Validators.required]),\n      typeOfUBO: new FormControl('')\n    });\n    this.entityStatus = data.exchangeReason == ExchangeReason.NonResidence ? AllEntityPeronStatus : AllEntityPeronStatus.filter(x => x.value !== EntityPersonType.ReportableEntity);\n    this.boStatuses = AllUBOStatus;\n  }\n  ngOnInit() {\n    this.countryService.getList({\n      sorting: \"name asc\",\n      maxResultCount: 1000\n    }).subscribe(response => {\n      this.countries = response.items;\n      this.setFormValues(this.data.input);\n    });\n    this.onContactTinChange();\n  }\n  onContactTinChange() {\n    this.exchangeform.get('tin').valueChanges.subscribe(value => {\n      const tinCountryControl = this.exchangeform.get('countryTin');\n      const validator = [Validators.required];\n      if (value != '') tinCountryControl.setValidators(validator);else tinCountryControl.clearValidators();\n      tinCountryControl.updateValueAndValidity();\n    });\n    this.exchangeform.get('typeOfEntity').valueChanges.subscribe(value => {\n      const typeOfUBO = this.exchangeform.get('typeOfUBO');\n      const validator = [Validators.required];\n      if (value !== '' && value == 0) typeOfUBO.setValidators(validator);else typeOfUBO.clearValidators();\n      typeOfUBO.updateValueAndValidity();\n    });\n  }\n  // set value if it is an edit\n  setFormValues(input) {\n    if (input != null) {\n      this.exchangeform.patchValue({\n        name: input?.nameEntityPerson,\n        jurisdiction: this.countries.find(x => x.id === input?.jurisdictionResidence?.id),\n        addressline1: input?.address?.addressLine1 ?? '',\n        addressline2: input?.address?.addressLine2 ?? '',\n        country: this.countries.find(x => x.id === input?.address?.country?.id),\n        tin: input?.tin,\n        countryTin: this.countries.find(x => x.id === input?.tinIssuedCoutry?.id),\n        otherIden: input?.otherIdentification,\n        boNationality: input?.boNationality,\n        typeOfEntity: input?.typeOfEntityPerson,\n        typeOfUBO: input?.uboType\n      });\n      this.cdr.detectChanges();\n    } else {\n      this.exchangeform.patchValue({\n        name: '',\n        jurisdiction: null,\n        addressline1: '',\n        addressline2: '',\n        country: null,\n        tin: '',\n        countryTin: null,\n        otherIden: '',\n        boNationality: '',\n        typeOfEntity: EntityPersonType.UltimateBO,\n        typeOfUBO: null\n      });\n    }\n  }\n  // get the value of the form either it update or add\n  getFormValues() {\n    const recipientDetailsDto = {\n      id: this.data?.input?.id ?? uuid.v4(),\n      nameEntityPerson: this.exchangeform.value['name'],\n      address: {\n        addressLine1: this.exchangeform.value['addressline1'] ?? '',\n        addressLine2: this.exchangeform.value['addressline2'] ?? '',\n        country: this.countries.find(x => x.id == this.exchangeform.value['country'].id)\n      },\n      tinIssuedCoutry: this.exchangeform.value['tin'] ? this.countries.find(x => x.id == this.exchangeform.value['countryTin'].id) : null,\n      tin: this.exchangeform.value['tin'],\n      otherIdentification: this.exchangeform.value['otherIden'],\n      typeOfEntityPerson: this.exchangeform.value['typeOfEntity'],\n      boNationality: this.exchangeform.value['boNationality'],\n      uboType: this.exchangeform.value['typeOfUBO'] != null ? this.exchangeform.value['typeOfUBO'] : null,\n      jurisdictionResidence: this.countries.find(x => x.id == this.exchangeform.value['jurisdiction'].id)\n    };\n    return recipientDetailsDto;\n  }\n  onSubmit() {\n    let result = this.getFormValues();\n    this.dialogRef.close({\n      status: \"success\" /* DialogStatus.SUCCESS */,\n      data: result\n    });\n  }\n  onClose() {\n    this.dialogRef.close(\"closed\" /* DialogStatus.CLOSED */);\n  }\n  static {\n    this.ɵfac = function RecipientDialogBoxComponent_Factory(t) {\n      return new (t || RecipientDialogBoxComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.CountryService), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RecipientDialogBoxComponent,\n      selectors: [[\"app-recipient-dialog-box\"]],\n      decls: 52,\n      vars: 9,\n      consts: [[1, \"fill-extra-space\"], [1, \"font-dialog-title\"], [1, \"margin-10\", 3, \"ngSubmit\", \"formGroup\"], [\"cols\", \"1\", \"rowHeight\", \"600px\", 1, \"overflow-y:auto\"], [1, \"controler-container\"], [1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Entity/person Name\"], [\"formControlName\", \"jurisdiction\", \"placeholder\", \"Select Country\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"addressline1\", \"placeholder\", \"Address Line 1\"], [\"matInput\", \"\", \"formControlName\", \"addressline2\", \"placeholder\", \"Address Line 2\"], [\"formControlName\", \"country\", \"placeholder\", \"Select Country\"], [\"matInput\", \"\", \"formControlName\", \"tin\", \"placeholder\", \"TIN\"], [\"class\", \"full-width\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"otherIden\", \"placeholder\", \"Other Ident\"], [\"placeholder\", \"Select Type of Entity\", \"formControlName\", \"typeOfEntity\"], [\"mat-dialog-actions\", \"\", 1, \"button-row\", \"button-right\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", 1, \"button-margin\", \"ui-button\", 3, \"disabled\"], [\"mat-raised-button\", \"\", \"type\", \"reset\", 1, \"button-margin\", 3, \"click\"], [3, \"value\"], [\"placeholder\", \"Select Country\", \"formControlName\", \"countryTin\"], [\"matInput\", \"\", \"formControlName\", \"boNationality\", \"placeholder\", \"BO Nationality\"], [\"placeholder\", \"Select UBO Type\", \"formControlName\", \"typeOfUBO\"]],\n      template: function RecipientDialogBoxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\");\n          i0.ɵɵelement(1, \"span\", 0);\n          i0.ɵɵelementStart(2, \"span\", 1);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"span\", 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function RecipientDialogBoxComponent_Template_form_ngSubmit_5_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(6, \"mat-grid-list\", 3)(7, \"mat-grid-tile\")(8, \"div\", 4)(9, \"mat-form-field\", 5)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Name Of Entity/Person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-form-field\", 5)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Jurisdiction Of Tax Residence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"mat-select\", 7);\n          i0.ɵɵtemplate(17, RecipientDialogBoxComponent_mat_option_17_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 5)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Address Line 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 5)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Address Line 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 5)(27, \"mat-label\");\n          i0.ɵɵtext(28, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-select\", 11);\n          i0.ɵɵtemplate(30, RecipientDialogBoxComponent_mat_option_30_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"mat-form-field\", 5)(32, \"mat-label\");\n          i0.ɵɵtext(33, \"TIN\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, RecipientDialogBoxComponent_mat_form_field_35_Template, 5, 1, \"mat-form-field\", 13);\n          i0.ɵɵelementStart(36, \"mat-form-field\", 5)(37, \"mat-label\");\n          i0.ɵɵtext(38, \"Other Identification Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"mat-form-field\", 5)(41, \"mat-label\");\n          i0.ɵɵtext(42, \"Type Of Entity/Person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-select\", 15);\n          i0.ɵɵtemplate(44, RecipientDialogBoxComponent_mat_option_44_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(45, RecipientDialogBoxComponent_mat_form_field_45_Template, 4, 0, \"mat-form-field\", 13)(46, RecipientDialogBoxComponent_mat_form_field_46_Template, 5, 1, \"mat-form-field\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 16)(48, \"button\", 17);\n          i0.ɵɵtext(49, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function RecipientDialogBoxComponent_Template_button_click_50_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵtext(51, \"Close\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(\"Recipient Details\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.exchangeform);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.exchangeform.get(\"tin\").value != \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.entityStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.exchangeform.get(\"typeOfEntity\").value !== \"\" && ctx.exchangeform.get(\"typeOfEntity\").value == 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.exchangeform.get(\"typeOfEntity\").value !== \"\" && ctx.exchangeform.get(\"typeOfEntity\").value == 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.exchangeform.invalid);\n        }\n      },\n      dependencies: [i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i4.MatInput, i5.MatFormField, i5.MatLabel, i6.MatToolbar, i1.MatDialogActions, i7.MatButton, i8.MatGridList, i8.MatGridTile, i9.MatSelect, i10.MatOption, i11.ValidationGroupDirective, i11.ValidationDirective, i12.NgForOf, i12.NgIf],\n      styles: [\".form[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-width: 150px;\\n  max-width: 500px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.controler-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 2%;\\n  height: 600px; \\n\\n  overflow-y: auto;\\n}\\n\\n.grid-container[_ngcontent-%COMP%] {\\n  height: 600px; \\n\\n  overflow-y: auto; \\n\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlY2lwaWVudC1kaWFsb2ctYm94LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsV0FBQTtFQUVBLGdCQUFBO0VBQ0EsZ0JBQUE7QUFBRjs7QUFHQTtFQUNFLFdBQUE7QUFBRjs7QUFHQTtFQUNFLFdBQUE7RUFDQSxXQUFBO0VBQ0EsYUFBQSxFQUFBLDJCQUFBO0VBQ0EsZ0JBQUE7QUFBRjs7QUFHQTtFQUNFLGFBQUEsRUFBQSwyQkFBQTtFQUNBLGdCQUFBLEVBQUEsOEJBQUE7QUFBRiIsImZpbGUiOiJyZWNpcGllbnQtZGlhbG9nLWJveC5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi5mb3JtIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBcclxuICBtaW4td2lkdGg6IDE1MHB4O1xyXG4gIG1heC13aWR0aDogNTAwcHg7XHJcbn1cclxuXHJcbi5mdWxsLXdpZHRoIHtcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLmNvbnRyb2xlci1jb250YWluZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIHBhZGRpbmc6IDIlO1xyXG4gIGhlaWdodDogNjAwcHg7IC8qIFNldCB0aGUgZGVzaXJlZCBoZWlnaHQgKi9cclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG59XHJcblxyXG4uZ3JpZC1jb250YWluZXIge1xyXG4gIGhlaWdodDogNjAwcHg7IC8qIFNldCB0aGUgZGVzaXJlZCBoZWlnaHQgKi9cclxuICBvdmVyZmxvdy15OiBhdXRvOyAvKiBFbmFibGUgdmVydGljYWwgc2Nyb2xsaW5nICovXHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaW5mb3JtYXRpb24tZXhjaGFuZ2UvY29udGFpbmVycy9yZWNpcGllbnQtZGlhbG9nLWJveC9yZWNpcGllbnQtZGlhbG9nLWJveC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFdBQUE7RUFFQSxnQkFBQTtFQUNBLGdCQUFBO0FBQUY7O0FBR0E7RUFDRSxXQUFBO0FBQUY7O0FBR0E7RUFDRSxXQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUEsRUFBQSwyQkFBQTtFQUNBLGdCQUFBO0FBQUY7O0FBR0E7RUFDRSxhQUFBLEVBQUEsMkJBQUE7RUFDQSxnQkFBQSxFQUFBLDhCQUFBO0FBQUY7QUFDQSxnaENBQWdoQyIsInNvdXJjZXNDb250ZW50IjpbIi5mb3JtIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBcclxuICBtaW4td2lkdGg6IDE1MHB4O1xyXG4gIG1heC13aWR0aDogNTAwcHg7XHJcbn1cclxuXHJcbi5mdWxsLXdpZHRoIHtcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLmNvbnRyb2xlci1jb250YWluZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIHBhZGRpbmc6IDIlO1xyXG4gIGhlaWdodDogNjAwcHg7IC8qIFNldCB0aGUgZGVzaXJlZCBoZWlnaHQgKi9cclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG59XHJcblxyXG4uZ3JpZC1jb250YWluZXIge1xyXG4gIGhlaWdodDogNjAwcHg7IC8qIFNldCB0aGUgZGVzaXJlZCBoZWlnaHQgKi9cclxuICBvdmVyZmxvdy15OiBhdXRvOyAvKiBFbmFibGUgdmVydGljYWwgc2Nyb2xsaW5nICovXHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "MAT_DIALOG_DATA", "AllEntityPeronStatus", "AllUBOStatus", "EntityPersonType", "uuid", "ExchangeReason", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "element_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "element_r2", "element_r3", "ɵɵtemplate", "RecipientDialogBoxComponent_mat_form_field_35_mat_option_4_Template", "ctx_r3", "countries", "element_r5", "value", "description", "ɵɵelement", "element_r6", "RecipientDialogBoxComponent_mat_form_field_46_mat_option_4_Template", "boStatuses", "RecipientDialogBoxComponent", "constructor", "dialogRef", "cdr", "countryService", "data", "exchangeform", "required", "jurisdiction", "addressline1", "addressline2", "country", "tin", "countryTin", "otherIden", "boNationality", "typeOfEntity", "typeOfUBO", "entityStatus", "exchangeReason", "NonResidence", "filter", "x", "ReportableEntity", "ngOnInit", "getList", "sorting", "maxResultCount", "subscribe", "response", "items", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "input", "onContactTinChange", "get", "valueChanges", "tinCountryControl", "validator", "setValidators", "clearValidators", "updateValueAndValidity", "patchValue", "name<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "id", "jurisdictionResidence", "address", "addressLine1", "addressLine2", "tinIssuedCoutry", "otherIdentification", "typeOfEntityPerson", "uboType", "detectChanges", "UltimateBO", "getFormValues", "recipientDetailsDto", "v4", "onSubmit", "result", "close", "status", "onClose", "ɵɵdirectiveInject", "i1", "MatDialogRef", "ChangeDetectorRef", "i2", "CountryService", "selectors", "decls", "vars", "consts", "template", "RecipientDialogBoxComponent_Template", "rf", "ctx", "ɵɵlistener", "RecipientDialogBoxComponent_Template_form_ngSubmit_5_listener", "RecipientDialogBoxComponent_mat_option_17_Template", "RecipientDialogBoxComponent_mat_option_30_Template", "RecipientDialogBoxComponent_mat_form_field_35_Template", "RecipientDialogBoxComponent_mat_option_44_Template", "RecipientDialogBoxComponent_mat_form_field_45_Template", "RecipientDialogBoxComponent_mat_form_field_46_Template", "RecipientDialogBoxComponent_Template_button_click_50_listener", "ɵɵtextInterpolate", "invalid"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\recipient-dialog-box\\recipient-dialog-box.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\recipient-dialog-box\\recipient-dialog-box.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';\r\nimport { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { MAT_DIALOG_DATA,MatDialogRef } from '@angular/material/dialog';\r\nimport { CountryService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { CountryDto, RecipientDetailsDto, AddressIEDto } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\r\nimport { AllEntityPeronStatus, AllUBOStatus, DialogStatus, EntityPersonType } from '../../../../shared/constants';\r\nimport * as uuid from 'uuid';\r\nimport { ExchangeReason } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\r\n\r\nexport interface DialogData {\r\n  input: RecipientDetailsDto,\r\n  exchangeReason:ExchangeReason\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'app-recipient-dialog-box',\r\n  templateUrl: './recipient-dialog-box.component.html',\r\n  styleUrls: ['./recipient-dialog-box.component.scss']\r\n})\r\nexport class RecipientDialogBoxComponent implements OnInit\r\n{\r\n  countries: any;\r\n  entityStatus: any;\r\n  boStatuses: any;\r\n  \r\n  constructor(public dialogRef: MatDialogRef<RecipientDialogBoxComponent>,\r\n    private cdr: ChangeDetectorRef,\r\n    private countryService: CountryService, @Inject(MAT_DIALOG_DATA) public data: DialogData)\r\n  {\r\n    \r\n    this.entityStatus = data.exchangeReason == ExchangeReason.NonResidence ? AllEntityPeronStatus : AllEntityPeronStatus.filter(x => x.value !== EntityPersonType.ReportableEntity ) ;\r\n    this.boStatuses = AllUBOStatus;\r\n    \r\n  }\r\n \r\n \r\n\r\n  exchangeform: FormGroup = new FormGroup({\r\n    name: new FormControl('', [Validators.required]),\r\n    jurisdiction: new FormControl(null, [Validators.required]),\r\n    addressline1: new FormControl('', [Validators.required]),\r\n    addressline2: new FormControl(''),\r\n    country: new FormControl(null, [Validators.required]),\r\n    tin: new FormControl(''),\r\n    countryTin: new FormControl(''),\r\n    otherIden: new FormControl(''),\r\n    boNationality: new FormControl(''),\r\n    typeOfEntity: new FormControl('', [Validators.required]),\r\n    typeOfUBO: new FormControl(''),\r\n  }\r\n  );\r\n\r\n\r\n  ngOnInit(): void {\r\n    this.countryService.getList({ sorting: \"name asc\", maxResultCount: 1000 }).subscribe(response => {\r\n\r\n      this.countries = response.items;\r\n      this.setFormValues(this.data.input);\r\n    });\r\n    \r\n    this.onContactTinChange();\r\n   \r\n  }\r\n\r\n\r\n  private onContactTinChange(): void\r\n  {\r\n    this.exchangeform.get('tin').valueChanges.subscribe(value =>\r\n    {\r\n      const tinCountryControl = this.exchangeform.get('countryTin');\r\n      const validator = [Validators.required];\r\n      if (value != '')\r\n        tinCountryControl.setValidators(validator);\r\n      else\r\n        tinCountryControl.clearValidators();\r\n\r\n      tinCountryControl.updateValueAndValidity();\r\n    });\r\n\r\n\r\n    this.exchangeform.get('typeOfEntity').valueChanges.subscribe(value =>\r\n    {\r\n      const typeOfUBO = this.exchangeform.get('typeOfUBO');\r\n      const validator = [Validators.required];\r\n      \r\n      if (value !== '' && value==0)\r\n        typeOfUBO.setValidators(validator);\r\n      else\r\n        typeOfUBO.clearValidators();\r\n\r\n      typeOfUBO.updateValueAndValidity();\r\n    });\r\n\r\n  }\r\n\r\n\r\n// set value if it is an edit\r\n  setFormValues(input: RecipientDetailsDto)\r\n  {\r\n    if (input != null)\r\n    {\r\n      this.exchangeform.patchValue({\r\n        name: input?.nameEntityPerson,\r\n        jurisdiction: this.countries.find(x => x.id === input?.jurisdictionResidence?.id),\r\n        addressline1: input?.address?.addressLine1??'',\r\n        addressline2: input?.address?.addressLine2??'',\r\n        country:  this.countries.find(x => x.id === input?.address?.country?.id),\r\n        tin: input?.tin,\r\n        countryTin: this.countries.find(x => x.id === input?.tinIssuedCoutry?.id),\r\n        otherIden: input?.otherIdentification,\r\n        boNationality:input?.boNationality,\r\n        typeOfEntity: input?.typeOfEntityPerson,\r\n        typeOfUBO: input?.uboType\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n    else {\r\n     this.exchangeform.patchValue({\r\n        name: '',\r\n        jurisdiction: null,\r\n        addressline1: '',\r\n        addressline2: '',\r\n        country: null,\r\n        tin: '',\r\n        countryTin: null,\r\n        otherIden: '',\r\n        boNationality:'',\r\n       typeOfEntity: EntityPersonType.UltimateBO,\r\n        typeOfUBO: null\r\n      });\r\n    }\r\n  }\r\n\r\n  // get the value of the form either it update or add\r\n  getFormValues(): RecipientDetailsDto\r\n  {\r\n    const recipientDetailsDto: RecipientDetailsDto =\r\n      {\r\n        id: this.data?.input?.id??uuid.v4(),\r\n        nameEntityPerson: this.exchangeform.value['name'],\r\n        address: {\r\n          addressLine1: this.exchangeform.value['addressline1'] ?? '',\r\n          addressLine2: this.exchangeform.value['addressline2'] ?? '',\r\n          country: this.countries.find(x => x.id == this.exchangeform.value['country'].id)\r\n        },\r\n        \r\n       tinIssuedCoutry: this.exchangeform.value['tin'] ? this.countries.find(x => x.id == this.exchangeform.value['countryTin'].id) :null,\r\n       tin: this.exchangeform.value['tin'] ,\r\n       otherIdentification: this.exchangeform.value['otherIden'],\r\n       typeOfEntityPerson: this.exchangeform.value['typeOfEntity'],\r\n       boNationality: this.exchangeform.value['boNationality'],\r\n       uboType: this.exchangeform.value['typeOfUBO']!=null ? this.exchangeform.value['typeOfUBO']:null,\r\n        jurisdictionResidence: this.countries.find(x => x.id == this.exchangeform.value['jurisdiction'].id)\r\n      } as RecipientDetailsDto;\r\n\r\n    return recipientDetailsDto;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n  onSubmit(): void\r\n  {\r\n    let result = this.getFormValues();\r\n    this.dialogRef.close({ status: DialogStatus.SUCCESS, data: result });\r\n  }\r\n\r\n  onClose(): void {\r\n    this.dialogRef.close(DialogStatus.CLOSED);\r\n  }\r\n}\r\n\r\n", "<mat-toolbar>\r\n  <span class=\"fill-extra-space\"></span>\r\n  <span class=\"font-dialog-title\">{{\"Recipient Details\"}}</span>\r\n  <span class=\"fill-extra-space\"></span>\r\n</mat-toolbar>\r\n<form [formGroup]=\"exchangeform\" class=\"margin-10\" (ngSubmit)=\"onSubmit()\">\r\n    <mat-grid-list cols=\"1\" rowHeight=\"600px\" class=\"overflow-y:auto\">\r\n      <mat-grid-tile>\r\n        <div class=\"controler-container\">\r\n          <mat-form-field class=\"full-width\">\r\n            <mat-label>Name Of Entity/Person</mat-label>\r\n            <input matInput formControlName=\"name\" placeholder=\"Entity/person Name\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field class=\"full-width\">\r\n            <mat-label>Jurisdiction Of Tax Residence</mat-label>\r\n             <mat-select formControlName=\"jurisdiction\" placeholder=\"Select Country\"  >\r\n                <mat-option *ngFor=\"let element of countries\" [value]=\"element\">\r\n                  {{element.name}}\r\n                </mat-option>\r\n              </mat-select>\r\n            \r\n          </mat-form-field>\r\n\r\n          <mat-form-field class=\"full-width\">\r\n          <mat-label>Address Line 1</mat-label>\r\n          <input matInput formControlName=\"addressline1\" placeholder=\"Address Line 1\">\r\n         </mat-form-field>\r\n\r\n\r\n        <mat-form-field class=\"full-width\">\r\n          <mat-label>Address Line 2</mat-label>\r\n          <input matInput formControlName=\"addressline2\" placeholder=\"Address Line 2\">\r\n        </mat-form-field>\r\n\r\n\r\n    <mat-form-field class=\"full-width\">\r\n      <mat-label>Country</mat-label>\r\n      <mat-select  formControlName=\"country\" placeholder=\"Select Country\"  >\r\n        <mat-option *ngFor=\"let element of countries\" [value]=\"element\">\r\n          {{element.name}}\r\n        </mat-option>\r\n      </mat-select>\r\n  </mat-form-field>\r\n\r\n  <mat-form-field class=\"full-width\">\r\n    <mat-label>TIN</mat-label>\r\n    <input matInput formControlName=\"tin\" placeholder=\"TIN\">\r\n  </mat-form-field>\r\n\r\n  <mat-form-field class=\"full-width\" *ngIf=\"exchangeform.get('tin').value != ''\">\r\n    <mat-label>TIN Issued Country</mat-label>\r\n    <mat-select placeholder=\"Select Country\" formControlName=\"countryTin\" >\r\n        <mat-option *ngFor=\"let element of countries\" [value]=\"element\">\r\n          {{element.name}}\r\n        </mat-option>\r\n      </mat-select>\r\n  </mat-form-field>\r\n\r\n\r\n  <mat-form-field class=\"full-width\">\r\n    <mat-label>Other Identification Number</mat-label>\r\n    <input matInput formControlName=\"otherIden\" placeholder=\"Other Ident\">\r\n  </mat-form-field>\r\n\r\n  <mat-form-field class=\"full-width\">\r\n    <mat-label>Type Of Entity/Person</mat-label>\r\n      <mat-select placeholder=\"Select Type of Entity\" formControlName=\"typeOfEntity\" >\r\n        <mat-option *ngFor=\"let element of entityStatus\" [value]=\"element.value\">\r\n          {{element.description}}\r\n        </mat-option>\r\n      </mat-select>\r\n  </mat-form-field>\r\n\r\n  <mat-form-field class=\"full-width\" *ngIf=\"exchangeform.get('typeOfEntity').value !=='' && exchangeform.get('typeOfEntity').value ==0 \">\r\n    <mat-label>BO Nationality</mat-label>\r\n    <input matInput formControlName=\"boNationality\" placeholder=\"BO Nationality\">\r\n  </mat-form-field>\r\n\r\n  <mat-form-field class=\"full-width\" *ngIf=\"exchangeform.get('typeOfEntity').value !=='' && exchangeform.get('typeOfEntity').value ==0 \">\r\n    <mat-label>UBO Type</mat-label>\r\n    \r\n      <mat-select placeholder=\"Select UBO Type\" formControlName=\"typeOfUBO\">\r\n        <mat-option *ngFor=\"let element of boStatuses\" [value]=\"element.value\">\r\n          {{element.description}}\r\n        </mat-option>\r\n      </mat-select>\r\n    \r\n  </mat-form-field>\r\n        </div>\r\n      </mat-grid-tile>\r\n    </mat-grid-list>\r\n    \r\n    <div class=\"button-row button-right\" mat-dialog-actions>\r\n      <button mat-raised-button class=\"button-margin ui-button\" type=\"submit\" [disabled]=\"exchangeform.invalid\">Submit</button>\r\n      <button mat-raised-button class=\"button-margin\" (click)=\"onClose()\" type=\"reset\">Close</button>\r\n    </div>\r\n</form>\r\n"], "mappings": "AACA,SAA0BA,WAAW,EAAEC,SAAS,EAAiCC,UAAU,QAAQ,gBAAgB;AACnH,SAASC,eAAe,QAAqB,0BAA0B;AAGvE,SAASC,oBAAoB,EAAEC,YAAY,EAAgBC,gBAAgB,QAAQ,8BAA8B;AACjH,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,cAAc,QAAQ,mHAAmH;;;;;;;;;;;;;;;;ICUlIC,EAAA,CAAAC,cAAA,qBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAiB;IAC7DL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,UAAA,CAAAG,IAAA,MACF;;;;;IAoBRR,EAAA,CAAAC,cAAA,qBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAAI,UAAA,UAAAK,UAAA,CAAiB;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,UAAA,CAAAD,IAAA,MACF;;;;;IAYAR,EAAA,CAAAC,cAAA,qBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAAI,UAAA,UAAAM,UAAA,CAAiB;IAC7DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,UAAA,CAAAF,IAAA,MACF;;;;;IAJJR,EADF,CAAAC,cAAA,wBAA+E,gBAClE;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAuE;IACnED,EAAA,CAAAW,UAAA,IAAAC,mEAAA,wBAAgE;IAItEZ,EADI,CAAAG,YAAA,EAAa,EACA;;;;IAJqBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAC,SAAA,CAAY;;;;;IAe5Cd,EAAA,CAAAC,cAAA,qBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAW,UAAA,CAAAC,KAAA,CAAuB;IACtEhB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,UAAA,CAAAE,WAAA,MACF;;;;;IAKJjB,EADF,CAAAC,cAAA,wBAAuI,gBAC1H;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAkB,SAAA,gBAA6E;IAC/ElB,EAAA,CAAAG,YAAA,EAAiB;;;;;IAMXH,EAAA,CAAAC,cAAA,qBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAe,UAAA,CAAAH,KAAA,CAAuB;IACpEhB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAY,UAAA,CAAAF,WAAA,MACF;;;;;IALJjB,EADF,CAAAC,cAAA,wBAAuI,gBAC1H;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE7BH,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAW,UAAA,IAAAS,mEAAA,wBAAuE;IAK7EpB,EAFI,CAAAG,YAAA,EAAa,EAEA;;;;IALqBH,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAQ,UAAA,CAAa;;;AD/DrD,OAAM,MAAOC,2BAA2B;EAMtCC,YAAmBC,SAAoD,EAC7DC,GAAsB,EACtBC,cAA8B,EAAkCC,IAAgB;IAFvE,KAAAH,SAAS,GAATA,SAAS;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,cAAc,GAAdA,cAAc;IAAkD,KAAAC,IAAI,GAAJA,IAAI;IAU9E,KAAAC,YAAY,GAAc,IAAIpC,SAAS,CAAC;MACtCgB,IAAI,EAAE,IAAIjB,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoC,QAAQ,CAAC,CAAC;MAChDC,YAAY,EAAE,IAAIvC,WAAW,CAAC,IAAI,EAAE,CAACE,UAAU,CAACoC,QAAQ,CAAC,CAAC;MAC1DE,YAAY,EAAE,IAAIxC,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoC,QAAQ,CAAC,CAAC;MACxDG,YAAY,EAAE,IAAIzC,WAAW,CAAC,EAAE,CAAC;MACjC0C,OAAO,EAAE,IAAI1C,WAAW,CAAC,IAAI,EAAE,CAACE,UAAU,CAACoC,QAAQ,CAAC,CAAC;MACrDK,GAAG,EAAE,IAAI3C,WAAW,CAAC,EAAE,CAAC;MACxB4C,UAAU,EAAE,IAAI5C,WAAW,CAAC,EAAE,CAAC;MAC/B6C,SAAS,EAAE,IAAI7C,WAAW,CAAC,EAAE,CAAC;MAC9B8C,aAAa,EAAE,IAAI9C,WAAW,CAAC,EAAE,CAAC;MAClC+C,YAAY,EAAE,IAAI/C,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoC,QAAQ,CAAC,CAAC;MACxDU,SAAS,EAAE,IAAIhD,WAAW,CAAC,EAAE;KAC9B,CACA;IApBC,IAAI,CAACiD,YAAY,GAAGb,IAAI,CAACc,cAAc,IAAI1C,cAAc,CAAC2C,YAAY,GAAG/C,oBAAoB,GAAGA,oBAAoB,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5B,KAAK,KAAKnB,gBAAgB,CAACgD,gBAAgB,CAAE;IAChL,IAAI,CAACxB,UAAU,GAAGzB,YAAY;EAEhC;EAoBAkD,QAAQA,CAAA;IACN,IAAI,CAACpB,cAAc,CAACqB,OAAO,CAAC;MAAEC,OAAO,EAAE,UAAU;MAAEC,cAAc,EAAE;IAAI,CAAE,CAAC,CAACC,SAAS,CAACC,QAAQ,IAAG;MAE9F,IAAI,CAACrC,SAAS,GAAGqC,QAAQ,CAACC,KAAK;MAC/B,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC1B,IAAI,CAAC2B,KAAK,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAACC,kBAAkB,EAAE;EAE3B;EAGQA,kBAAkBA,CAAA;IAExB,IAAI,CAAC3B,YAAY,CAAC4B,GAAG,CAAC,KAAK,CAAC,CAACC,YAAY,CAACP,SAAS,CAAClC,KAAK,IAAG;MAE1D,MAAM0C,iBAAiB,GAAG,IAAI,CAAC9B,YAAY,CAAC4B,GAAG,CAAC,YAAY,CAAC;MAC7D,MAAMG,SAAS,GAAG,CAAClE,UAAU,CAACoC,QAAQ,CAAC;MACvC,IAAIb,KAAK,IAAI,EAAE,EACb0C,iBAAiB,CAACE,aAAa,CAACD,SAAS,CAAC,CAAC,KAE3CD,iBAAiB,CAACG,eAAe,EAAE;MAErCH,iBAAiB,CAACI,sBAAsB,EAAE;IAC5C,CAAC,CAAC;IAGF,IAAI,CAAClC,YAAY,CAAC4B,GAAG,CAAC,cAAc,CAAC,CAACC,YAAY,CAACP,SAAS,CAAClC,KAAK,IAAG;MAEnE,MAAMuB,SAAS,GAAG,IAAI,CAACX,YAAY,CAAC4B,GAAG,CAAC,WAAW,CAAC;MACpD,MAAMG,SAAS,GAAG,CAAClE,UAAU,CAACoC,QAAQ,CAAC;MAEvC,IAAIb,KAAK,KAAK,EAAE,IAAIA,KAAK,IAAE,CAAC,EAC1BuB,SAAS,CAACqB,aAAa,CAACD,SAAS,CAAC,CAAC,KAEnCpB,SAAS,CAACsB,eAAe,EAAE;MAE7BtB,SAAS,CAACuB,sBAAsB,EAAE;IACpC,CAAC,CAAC;EAEJ;EAGF;EACET,aAAaA,CAACC,KAA0B;IAEtC,IAAIA,KAAK,IAAI,IAAI,EACjB;MACE,IAAI,CAAC1B,YAAY,CAACmC,UAAU,CAAC;QAC3BvD,IAAI,EAAE8C,KAAK,EAAEU,gBAAgB;QAC7BlC,YAAY,EAAE,IAAI,CAAChB,SAAS,CAACmD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKZ,KAAK,EAAEa,qBAAqB,EAAED,EAAE,CAAC;QACjFnC,YAAY,EAAEuB,KAAK,EAAEc,OAAO,EAAEC,YAAY,IAAE,EAAE;QAC9CrC,YAAY,EAAEsB,KAAK,EAAEc,OAAO,EAAEE,YAAY,IAAE,EAAE;QAC9CrC,OAAO,EAAG,IAAI,CAACnB,SAAS,CAACmD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKZ,KAAK,EAAEc,OAAO,EAAEnC,OAAO,EAAEiC,EAAE,CAAC;QACxEhC,GAAG,EAAEoB,KAAK,EAAEpB,GAAG;QACfC,UAAU,EAAE,IAAI,CAACrB,SAAS,CAACmD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKZ,KAAK,EAAEiB,eAAe,EAAEL,EAAE,CAAC;QACzE9B,SAAS,EAAEkB,KAAK,EAAEkB,mBAAmB;QACrCnC,aAAa,EAACiB,KAAK,EAAEjB,aAAa;QAClCC,YAAY,EAAEgB,KAAK,EAAEmB,kBAAkB;QACvClC,SAAS,EAAEe,KAAK,EAAEoB;OACnB,CAAC;MACF,IAAI,CAACjD,GAAG,CAACkD,aAAa,EAAE;IAC1B,CAAC,MACI;MACJ,IAAI,CAAC/C,YAAY,CAACmC,UAAU,CAAC;QAC1BvD,IAAI,EAAE,EAAE;QACRsB,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,IAAI;QACbC,GAAG,EAAE,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,EAAE;QACbC,aAAa,EAAC,EAAE;QACjBC,YAAY,EAAEzC,gBAAgB,CAAC+E,UAAU;QACxCrC,SAAS,EAAE;OACZ,CAAC;IACJ;EACF;EAEA;EACAsC,aAAaA,CAAA;IAEX,MAAMC,mBAAmB,GACvB;MACEZ,EAAE,EAAE,IAAI,CAACvC,IAAI,EAAE2B,KAAK,EAAEY,EAAE,IAAEpE,IAAI,CAACiF,EAAE,EAAE;MACnCf,gBAAgB,EAAE,IAAI,CAACpC,YAAY,CAACZ,KAAK,CAAC,MAAM,CAAC;MACjDoD,OAAO,EAAE;QACPC,YAAY,EAAE,IAAI,CAACzC,YAAY,CAACZ,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE;QAC3DsD,YAAY,EAAE,IAAI,CAAC1C,YAAY,CAACZ,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE;QAC3DiB,OAAO,EAAE,IAAI,CAACnB,SAAS,CAACmD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACsB,EAAE,IAAI,IAAI,CAACtC,YAAY,CAACZ,KAAK,CAAC,SAAS,CAAC,CAACkD,EAAE;OAChF;MAEFK,eAAe,EAAE,IAAI,CAAC3C,YAAY,CAACZ,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAACF,SAAS,CAACmD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACsB,EAAE,IAAI,IAAI,CAACtC,YAAY,CAACZ,KAAK,CAAC,YAAY,CAAC,CAACkD,EAAE,CAAC,GAAE,IAAI;MAClIhC,GAAG,EAAE,IAAI,CAACN,YAAY,CAACZ,KAAK,CAAC,KAAK,CAAC;MACnCwD,mBAAmB,EAAE,IAAI,CAAC5C,YAAY,CAACZ,KAAK,CAAC,WAAW,CAAC;MACzDyD,kBAAkB,EAAE,IAAI,CAAC7C,YAAY,CAACZ,KAAK,CAAC,cAAc,CAAC;MAC3DqB,aAAa,EAAE,IAAI,CAACT,YAAY,CAACZ,KAAK,CAAC,eAAe,CAAC;MACvD0D,OAAO,EAAE,IAAI,CAAC9C,YAAY,CAACZ,KAAK,CAAC,WAAW,CAAC,IAAE,IAAI,GAAG,IAAI,CAACY,YAAY,CAACZ,KAAK,CAAC,WAAW,CAAC,GAAC,IAAI;MAC9FmD,qBAAqB,EAAE,IAAI,CAACrD,SAAS,CAACmD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACsB,EAAE,IAAI,IAAI,CAACtC,YAAY,CAACZ,KAAK,CAAC,cAAc,CAAC,CAACkD,EAAE;KAC5E;IAE1B,OAAOY,mBAAmB;EAC5B;EAOAE,QAAQA,CAAA;IAEN,IAAIC,MAAM,GAAG,IAAI,CAACJ,aAAa,EAAE;IACjC,IAAI,CAACrD,SAAS,CAAC0D,KAAK,CAAC;MAAEC,MAAM;MAAwBxD,IAAI,EAAEsD;IAAM,CAAE,CAAC;EACtE;EAEAG,OAAOA,CAAA;IACL,IAAI,CAAC5D,SAAS,CAAC0D,KAAK,oCAAqB;EAC3C;;;uBAxJW5D,2BAA2B,EAAAtB,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAArF,EAAA,CAAAwF,iBAAA,GAAAxF,EAAA,CAAAqF,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA1F,EAAA,CAAAqF,iBAAA,CAQY3F,eAAe;IAAA;EAAA;;;YARtD4B,2BAA2B;MAAAqE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBxCjG,EAAA,CAAAC,cAAA,kBAAa;UACXD,EAAA,CAAAkB,SAAA,cAAsC;UACtClB,EAAA,CAAAC,cAAA,cAAgC;UAAAD,EAAA,CAAAE,MAAA,GAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAkB,SAAA,cAAsC;UACxClB,EAAA,CAAAG,YAAA,EAAc;UACdH,EAAA,CAAAC,cAAA,cAA2E;UAAxBD,EAAA,CAAAmG,UAAA,sBAAAC,8DAAA;YAAA,OAAYF,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UAK9DhF,EAJR,CAAAC,cAAA,uBAAkE,oBACjD,aACoB,wBACI,iBACtB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAkB,SAAA,gBAAwE;UAC1ElB,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAmC,iBACtB;UAAAD,EAAA,CAAAE,MAAA,qCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnDH,EAAA,CAAAC,cAAA,qBAA0E;UACvED,EAAA,CAAAW,UAAA,KAAA0F,kDAAA,wBAAgE;UAKtErG,EAFI,CAAAG,YAAA,EAAa,EAEA;UAGjBH,EADA,CAAAC,cAAA,yBAAmC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAkB,SAAA,gBAA4E;UAC7ElB,EAAA,CAAAG,YAAA,EAAiB;UAIhBH,EADF,CAAAC,cAAA,yBAAmC,iBACtB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAkB,SAAA,iBAA4E;UAC9ElB,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EADF,CAAAC,cAAA,yBAAmC,iBACtB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,sBAAsE;UACpED,EAAA,CAAAW,UAAA,KAAA2F,kDAAA,wBAAgE;UAItEtG,EADI,CAAAG,YAAA,EAAa,EACA;UAGfH,EADF,CAAAC,cAAA,yBAAmC,iBACtB;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC1BH,EAAA,CAAAkB,SAAA,iBAAwD;UAC1DlB,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAW,UAAA,KAAA4F,sDAAA,6BAA+E;UAW7EvG,EADF,CAAAC,cAAA,yBAAmC,iBACtB;UAAAD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAkB,SAAA,iBAAsE;UACxElB,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAmC,iBACtB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC1CH,EAAA,CAAAC,cAAA,sBAAgF;UAC9ED,EAAA,CAAAW,UAAA,KAAA6F,kDAAA,wBAAyE;UAI/ExG,EADI,CAAAG,YAAA,EAAa,EACA;UAOjBH,EALA,CAAAW,UAAA,KAAA8F,sDAAA,6BAAuI,KAAAC,sDAAA,6BAKA;UAYrI1G,EAFI,CAAAG,YAAA,EAAM,EACQ,EACF;UAGdH,EADF,CAAAC,cAAA,eAAwD,kBACoD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzHH,EAAA,CAAAC,cAAA,kBAAiF;UAAjCD,EAAA,CAAAmG,UAAA,mBAAAQ,8DAAA;YAAA,OAAST,GAAA,CAAAd,OAAA,EAAS;UAAA,EAAC;UAAcpF,EAAA,CAAAE,MAAA,aAAK;UAE5FF,EAF4F,CAAAG,YAAA,EAAS,EAC3F,EACH;;;UA/F2BH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAA4G,iBAAA,qBAAuB;UAGnD5G,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,cAAA8F,GAAA,CAAAtE,YAAA,CAA0B;UAYgB5B,EAAA,CAAAM,SAAA,IAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA8F,GAAA,CAAApF,SAAA,CAAY;UAsBpBd,EAAA,CAAAM,SAAA,IAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA8F,GAAA,CAAApF,SAAA,CAAY;UAWdd,EAAA,CAAAM,SAAA,GAAyC;UAAzCN,EAAA,CAAAI,UAAA,SAAA8F,GAAA,CAAAtE,YAAA,CAAA4B,GAAA,QAAAxC,KAAA,OAAyC;UAkBvChB,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,YAAA8F,GAAA,CAAA1D,YAAA,CAAe;UAMjBxC,EAAA,CAAAM,SAAA,EAAgG;UAAhGN,EAAA,CAAAI,UAAA,SAAA8F,GAAA,CAAAtE,YAAA,CAAA4B,GAAA,iBAAAxC,KAAA,WAAAkF,GAAA,CAAAtE,YAAA,CAAA4B,GAAA,iBAAAxC,KAAA,MAAgG;UAKhGhB,EAAA,CAAAM,SAAA,EAAgG;UAAhGN,EAAA,CAAAI,UAAA,SAAA8F,GAAA,CAAAtE,YAAA,CAAA4B,GAAA,iBAAAxC,KAAA,WAAAkF,GAAA,CAAAtE,YAAA,CAAA4B,GAAA,iBAAAxC,KAAA,MAAgG;UAexDhB,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,aAAA8F,GAAA,CAAAtE,YAAA,CAAAiF,OAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}