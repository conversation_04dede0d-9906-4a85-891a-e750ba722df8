{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class CtsPackageRequestService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'CtsIntegrationService';\n    this.batchUploadToCts = (fiscalYear, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/BatchUploadToCts',\n      params: {\n        fiscalYear\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.checkTransmissionStatus = (packageRequestId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/CheckTransmissionStatus',\n      params: {\n        packageRequestId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.createByInput = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.downloadDataPacketFile = (requestId, config) => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/DownloadDataPacketFile',\n      params: {\n        requestId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getAllCtsPackageRequestByInput = (input, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/GetAllCtsPackageRequest',\n      params: {\n        financialEndYear: input.financialEndYear,\n        exchangeReason: input.exchangeReason,\n        receivingCountry: input.receivingCountry,\n        ctsUploadStatus: input.ctsUploadStatus,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getSummaryByYearByYear = (year, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/GetSummaryByYear',\n      params: {\n        year\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.markAsDoNotUpload = (packageRequestId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/MarkAsDoNotUpload',\n      params: {\n        packageRequestId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.refreshTransmissionStatus = config => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/refresh-transmission-status'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.regeneratePackage = (packageRequestId, comments, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/RegeneratePackage',\n      params: {\n        packageRequestId,\n        comments\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.unMarkAsDoNotUpload = (packageRequestId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UnMarkAsDoNotUpload',\n      params: {\n        packageRequestId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.uploadToCts = (packageRequestId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UploadToCts',\n      params: {\n        packageRequestId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function CtsPackageRequestService_Factory(t) {\n      return new (t || CtsPackageRequestService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CtsPackageRequestService,\n      factory: CtsPackageRequestService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["CtsPackageRequestService", "constructor", "restService", "apiName", "batchUploadToCts", "fiscalYear", "config", "request", "method", "url", "params", "checkTransmissionStatus", "packageRequestId", "createByInput", "input", "body", "downloadDataPacketFile", "requestId", "responseType", "getAllCtsPackageRequestByInput", "financialEndYear", "exchangeReason", "receivingCountry", "ctsUploadStatus", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "getSummaryByYearByYear", "year", "markAsDoNotUpload", "refreshTransmissionStatus", "regeneratePackage", "comments", "unMarkAsDoNotUpload", "uploadToCts", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\proxies-ctsintegration-service\\lib\\proxy\\bdo\\ess\\cts-integration\\cts-package-request\\cts-package-request.service.ts"], "sourcesContent": ["import { RestService, Rest } from '@abp/ng.core';\r\nimport type { PagedResultDto } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\nimport type { CreateCtsPackageRequestDto, CtsPackageRequestDto, CtsPackageRequestSummaryDto, GetCtsPackageRequestDto } from '../application/contracts/cts-package-requests/models';\r\nimport type { CtsActionResultDto, CtsPackageRequestDataDto } from '../cts-package-requests/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CtsPackageRequestService {\r\n  apiName = 'CtsIntegrationService';\r\n  \r\n\r\n  batchUploadToCts = (fiscalYear: number, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/BatchUploadToCts',\r\n      params: { fiscalYear },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  checkTransmissionStatus = (packageRequestId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/CheckTransmissionStatus',\r\n      params: { packageRequestId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  createByInput = (input: CreateCtsPackageRequestDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsPackageRequestDataDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest',\r\n      body: input,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  downloadDataPacketFile = (requestId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string>({\r\n      method: 'GET',\r\n      responseType: 'text',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/DownloadDataPacketFile',\r\n      params: { requestId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getAllCtsPackageRequestByInput = (input: GetCtsPackageRequestDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, PagedResultDto<CtsPackageRequestDto>>({\r\n      method: 'GET',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/GetAllCtsPackageRequest',\r\n      params: { financialEndYear: input.financialEndYear, exchangeReason: input.exchangeReason, receivingCountry: input.receivingCountry, ctsUploadStatus: input.ctsUploadStatus, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getSummaryByYearByYear = (year: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsPackageRequestSummaryDto[]>({\r\n      method: 'GET',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/GetSummaryByYear',\r\n      params: { year },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  markAsDoNotUpload = (packageRequestId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/MarkAsDoNotUpload',\r\n      params: { packageRequestId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  refreshTransmissionStatus = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/refresh-transmission-status',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  regeneratePackage = (packageRequestId: string, comments: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/RegeneratePackage',\r\n      params: { packageRequestId, comments },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  unMarkAsDoNotUpload = (packageRequestId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UnMarkAsDoNotUpload',\r\n      params: { packageRequestId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  uploadToCts = (packageRequestId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, CtsActionResultDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UploadToCts',\r\n      params: { packageRequestId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AASA,OAAM,MAAOA,wBAAwB;EAsGnCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IArG/B,KAAAC,OAAO,GAAG,uBAAuB;IAGjC,KAAAC,gBAAgB,GAAG,CAACC,UAAkB,EAAEC,MAA6B,KACnE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,+DAA+D;MACpEC,MAAM,EAAE;QAAEL;MAAU;KACrB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAK,uBAAuB,GAAG,CAACC,gBAAwB,EAAEN,MAA6B,KAChF,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,sEAAsE;MAC3EC,MAAM,EAAE;QAAEE;MAAgB;KAC3B,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAO,aAAa,GAAG,CAACC,KAAiC,EAAER,MAA6B,KAC/E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAgC;MACtDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,8CAA8C;MACnDM,IAAI,EAAED;KACP,EACD;MAAEX,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAU,sBAAsB,GAAG,CAACC,SAAiB,EAAEX,MAA6B,KACxE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,KAAK;MACbU,YAAY,EAAE,MAAM;MACpBT,GAAG,EAAE,qEAAqE;MAC1EC,MAAM,EAAE;QAAEO;MAAS;KACpB,EACD;MAAEd,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAa,8BAA8B,GAAG,CAACL,KAA8B,EAAER,MAA6B,KAC7F,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA4C;MAClEC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,sEAAsE;MAC3EC,MAAM,EAAE;QAAEU,gBAAgB,EAAEN,KAAK,CAACM,gBAAgB;QAAEC,cAAc,EAAEP,KAAK,CAACO,cAAc;QAAEC,gBAAgB,EAAER,KAAK,CAACQ,gBAAgB;QAAEC,eAAe,EAAET,KAAK,CAACS,eAAe;QAAEC,OAAO,EAAEV,KAAK,CAACU,OAAO;QAAEC,SAAS,EAAEX,KAAK,CAACW,SAAS;QAAEC,cAAc,EAAEZ,KAAK,CAACY;MAAc;KACrQ,EACD;MAAEvB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAqB,sBAAsB,GAAG,CAACC,IAAY,EAAEtB,MAA6B,KACnE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAqC;MAC3DC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,+DAA+D;MACpEC,MAAM,EAAE;QAAEkB;MAAI;KACf,EACD;MAAEzB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAuB,iBAAiB,GAAG,CAACjB,gBAAwB,EAAEN,MAA6B,KAC1E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,gEAAgE;MACrEC,MAAM,EAAE;QAAEE;MAAgB;KAC3B,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAwB,yBAAyB,GAAIxB,MAA6B,IACxD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE;KACN,EACD;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAyB,iBAAiB,GAAG,CAACnB,gBAAwB,EAAEoB,QAAgB,EAAE1B,MAA6B,KAC5F,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,gEAAgE;MACrEC,MAAM,EAAE;QAAEE,gBAAgB;QAAEoB;MAAQ;KACrC,EACD;MAAE7B,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAA2B,mBAAmB,GAAG,CAACrB,gBAAwB,EAAEN,MAA6B,KAC5E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,kEAAkE;MACvEC,MAAM,EAAE;QAAEE;MAAgB;KAC3B,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAA4B,WAAW,GAAG,CAACtB,gBAAwB,EAAEN,MAA6B,KACpE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA0B;MAChDC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,0DAA0D;MAC/DC,MAAM,EAAE;QAAEE;MAAgB;KAC3B,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBAtGpCN,wBAAwB,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAxBtC,wBAAwB;MAAAuC,OAAA,EAAxBvC,wBAAwB,CAAAwC,IAAA;MAAAC,UAAA,EAFvB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}