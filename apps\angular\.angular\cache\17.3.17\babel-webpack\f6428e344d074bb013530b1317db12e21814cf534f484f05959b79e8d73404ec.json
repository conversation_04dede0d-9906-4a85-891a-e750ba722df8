{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    t = parseInt(val.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n  if (n === 1 || !(t === 0) && (i === 0 || i === 1)) return 1;\n  return 5;\n}\nexport default [\"da\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"S\", \"M\", \"T\", \"O\", \"T\", \"F\", \"L\"], [\"søn.\", \"man.\", \"tir.\", \"ons.\", \"tor.\", \"fre.\", \"lør.\"], [\"søndag\", \"mandag\", \"tirsdag\", \"onsdag\", \"torsdag\", \"fredag\", \"lørdag\"], [\"sø\", \"ma\", \"ti\", \"on\", \"to\", \"fr\", \"lø\"]], [[\"S\", \"M\", \"T\", \"O\", \"T\", \"F\", \"L\"], [\"søn\", \"man\", \"tir\", \"ons\", \"tor\", \"fre\", \"lør\"], [\"søndag\", \"mandag\", \"tirsdag\", \"onsdag\", \"torsdag\", \"fredag\", \"lørdag\"], [\"sø\", \"ma\", \"ti\", \"on\", \"to\", \"fr\", \"lø\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maj\", \"jun.\", \"jul.\", \"aug.\", \"sep.\", \"okt.\", \"nov.\", \"dec.\"], [\"januar\", \"februar\", \"marts\", \"april\", \"maj\", \"juni\", \"juli\", \"august\", \"september\", \"oktober\", \"november\", \"december\"]], u, [[\"fKr\", \"eKr\"], [\"f.Kr.\", \"e.Kr.\"], u], 1, [6, 0], [\"dd.MM.y\", \"d. MMM y\", \"d. MMMM y\", \"EEEE 'den' d. MMMM y\"], [\"HH.mm\", \"HH.mm.ss\", \"HH.mm.ss z\", \"HH.mm.ss zzzz\"], [\"{1} {0}\", u, \"{1} 'kl'. {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \".\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"DKK\", \"kr.\", \"dansk krone\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"Br.\"],\n  \"DKK\": [\"kr.\"],\n  \"ISK\": [u, \"kr.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"NOK\": [u, \"kr.\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"SEK\": [u, \"kr.\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "t", "parseInt", "toString", "replace"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/da.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), t = parseInt(val.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n    if (n === 1 || !(t === 0) && (i === 0 || i === 1))\n        return 1;\n    return 5;\n}\nexport default [\"da\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"S\", \"M\", \"T\", \"O\", \"T\", \"F\", \"L\"], [\"søn.\", \"man.\", \"tir.\", \"ons.\", \"tor.\", \"fre.\", \"lør.\"], [\"søndag\", \"mandag\", \"tirsdag\", \"onsdag\", \"torsdag\", \"fredag\", \"lørdag\"], [\"sø\", \"ma\", \"ti\", \"on\", \"to\", \"fr\", \"lø\"]], [[\"S\", \"M\", \"T\", \"O\", \"T\", \"F\", \"L\"], [\"søn\", \"man\", \"tir\", \"ons\", \"tor\", \"fre\", \"lør\"], [\"søndag\", \"mandag\", \"tirsdag\", \"onsdag\", \"torsdag\", \"fredag\", \"lørdag\"], [\"sø\", \"ma\", \"ti\", \"on\", \"to\", \"fr\", \"lø\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maj\", \"jun.\", \"jul.\", \"aug.\", \"sep.\", \"okt.\", \"nov.\", \"dec.\"], [\"januar\", \"februar\", \"marts\", \"april\", \"maj\", \"juni\", \"juli\", \"august\", \"september\", \"oktober\", \"november\", \"december\"]], u, [[\"fKr\", \"eKr\"], [\"f.Kr.\", \"e.Kr.\"], u], 1, [6, 0], [\"dd.MM.y\", \"d. MMM y\", \"d. MMMM y\", \"EEEE 'den' d. MMMM y\"], [\"HH.mm\", \"HH.mm.ss\", \"HH.mm.ss z\", \"HH.mm.ss zzzz\"], [\"{1} {0}\", u, \"{1} 'kl'. {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \".\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"DKK\", \"kr.\", \"dansk krone\", { \"AUD\": [\"AU$\", \"$\"], \"BYN\": [u, \"Br.\"], \"DKK\": [\"kr.\"], \"ISK\": [u, \"kr.\"], \"JPY\": [\"JP¥\", \"¥\"], \"NOK\": [u, \"kr.\"], \"PHP\": [u, \"₱\"], \"RON\": [u, \"L\"], \"SEK\": [u, \"kr.\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGC,QAAQ,CAACP,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;EACjH,IAAIR,CAAC,KAAK,CAAC,IAAI,EAAEK,CAAC,KAAK,CAAC,CAAC,KAAKJ,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC,EAC7C,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEL,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAE,eAAe,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}