{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['前', '公元'],\n  abbreviated: ['前', '公元'],\n  wide: ['公元前', '公元']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['第一季', '第二季', '第三季', '第四季'],\n  wide: ['第一季度', '第二季度', '第三季度', '第四季度']\n};\nvar monthValues = {\n  narrow: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'],\n  abbreviated: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n  wide: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']\n};\nvar dayValues = {\n  narrow: ['日', '一', '二', '三', '四', '五', '六'],\n  short: ['日', '一', '二', '三', '四', '五', '六'],\n  abbreviated: ['週日', '週一', '週二', '週三', '週四', '週五', '週六'],\n  wide: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '午夜',\n    noon: '晌',\n    morning: '早',\n    afternoon: '午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '午夜',\n    noon: '晌',\n    morning: '早',\n    afternoon: '午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  switch (options === null || options === void 0 ? void 0 : options.unit) {\n    case 'date':\n      return number + '日';\n    case 'hour':\n      return number + '時';\n    case 'minute':\n      return number + '分';\n    case 'second':\n      return number + '秒';\n    default:\n      return '第 ' + number;\n  }\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/zh-HK/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['前', '公元'],\n  abbreviated: ['前', '公元'],\n  wide: ['公元前', '公元']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['第一季', '第二季', '第三季', '第四季'],\n  wide: ['第一季度', '第二季度', '第三季度', '第四季度']\n};\nvar monthValues = {\n  narrow: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'],\n  abbreviated: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n  wide: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']\n};\nvar dayValues = {\n  narrow: ['日', '一', '二', '三', '四', '五', '六'],\n  short: ['日', '一', '二', '三', '四', '五', '六'],\n  abbreviated: ['週日', '週一', '週二', '週三', '週四', '週五', '週六'],\n  wide: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '午夜',\n    noon: '晌',\n    morning: '早',\n    afternoon: '午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '午夜',\n    noon: '晌',\n    morning: '早',\n    afternoon: '午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '午夜',\n    noon: '中午',\n    morning: '上午',\n    afternoon: '下午',\n    evening: '晚上',\n    night: '夜晚'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  switch (options === null || options === void 0 ? void 0 : options.unit) {\n    case 'date':\n      return number + '日';\n    case 'hour':\n      return number + '時';\n    case 'minute':\n      return number + '分';\n    case 'second':\n      return number + '秒';\n    default:\n      return '第 ' + number;\n  }\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACnBC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACxBC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI;AACpB,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzCC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AACvC,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;EACtEC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxFC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AACjF,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1CL,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvDC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACxD,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,QAAQC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI;IACpE,KAAK,MAAM;MACT,OAAOF,MAAM,GAAG,GAAG;IACrB,KAAK,MAAM;MACT,OAAOA,MAAM,GAAG,GAAG;IACrB,KAAK,QAAQ;MACX,OAAOA,MAAM,GAAG,GAAG;IACrB,KAAK,QAAQ;MACX,OAAOA,MAAM,GAAG,GAAG;IACrB;MACE,OAAO,IAAI,GAAGA,MAAM;EACxB;AACF,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbN,aAAa,EAAEA,aAAa;EAC5BO,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAEhC,eAAe,CAAC;IACnB2B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEjC,eAAe,CAAC;IACzB2B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}