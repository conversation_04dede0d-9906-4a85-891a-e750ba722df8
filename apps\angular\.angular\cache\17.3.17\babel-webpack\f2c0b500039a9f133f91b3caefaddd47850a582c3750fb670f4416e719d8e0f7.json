{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/es-PY\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['es-PY', [['a. m.', 'p. m.'], u, u], u, [['d', 'l', 'm', 'm', 'j', 'v', 's'], ['dom.', 'lun.', 'mar.', 'mié.', 'jue.', 'vie.', 'sáb.'], ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado'], ['do', 'lu', 'ma', 'mi', 'ju', 'vi', 'sa']], [['D', 'L', 'M', 'M', 'J', 'V', 'S'], ['dom.', 'lun.', 'mar.', 'mié.', 'jue.', 'vie.', 'sáb.'], ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado'], ['Do', 'Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sa']], [['E', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'], ['ene.', 'feb.', 'mar.', 'abr.', 'may.', 'jun.', 'jul.', 'ago.', 'sept.', 'oct.', 'nov.', 'dic.'], ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre']], u, [['a. C.', 'd. C.'], u, ['antes de Cristo', 'después de Cristo']], 0, [6, 0], ['d/M/yy', 'd MMM y', 'd \\'de\\' MMMM \\'de\\' y', 'EEEE, d \\'de\\' MMMM \\'de\\' y'], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1} {0}', u, '{1} \\'a\\' \\'las\\' {0}', u], [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0 %', '¤ #,##0.00;¤ -#,##0.00', '#E0'], 'PYG', 'Gs.', 'guaraní paraguayo', {\n    'AUD': [u, '$'],\n    'BRL': [u, 'R$'],\n    'CAD': [u, '$'],\n    'CNY': [u, '¥'],\n    'ESP': ['₧'],\n    'EUR': [u, '€'],\n    'FKP': [u, 'FK£'],\n    'GBP': [u, '£'],\n    'HKD': [u, '$'],\n    'ILS': [u, '₪'],\n    'INR': [u, '₹'],\n    'JPY': [u, '¥'],\n    'KRW': [u, '₩'],\n    'MXN': [u, '$'],\n    'NZD': [u, '$'],\n    'PYG': ['Gs.', '₲'],\n    'RON': [u, 'L'],\n    'SSP': [u, 'SD£'],\n    'SYP': [u, 'S£'],\n    'TWD': [u, 'NT$'],\n    'USD': [u, '$'],\n    'VEF': [u, 'BsF'],\n    'VND': [u, '₫'],\n    'XAF': [],\n    'XCD': [u, '$'],\n    'XOF': []\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/es-PY.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/es-PY\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'es-PY',\n        [['a. m.', 'p. m.'], u, u],\n        u,\n        [\n            ['d', 'l', 'm', 'm', 'j', 'v', 's'], ['dom.', 'lun.', 'mar.', 'mié.', 'jue.', 'vie.', 'sáb.'],\n            ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado'],\n            ['do', 'lu', 'ma', 'mi', 'ju', 'vi', 'sa']\n        ],\n        [\n            ['D', 'L', 'M', 'M', 'J', 'V', 'S'], ['dom.', 'lun.', 'mar.', 'mié.', 'jue.', 'vie.', 'sáb.'],\n            ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado'],\n            ['Do', 'Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sa']\n        ],\n        [\n            ['E', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n            [\n                'ene.', 'feb.', 'mar.', 'abr.', 'may.', 'jun.', 'jul.', 'ago.', 'sept.', 'oct.', 'nov.',\n                'dic.'\n            ],\n            [\n                'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre',\n                'octubre', 'noviembre', 'diciembre'\n            ]\n        ],\n        u,\n        [['a. C.', 'd. C.'], u, ['antes de Cristo', 'después de Cristo']],\n        0,\n        [6, 0],\n        ['d/M/yy', 'd MMM y', 'd \\'de\\' MMMM \\'de\\' y', 'EEEE, d \\'de\\' MMMM \\'de\\' y'],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1} {0}', u, '{1} \\'a\\' \\'las\\' {0}', u],\n        [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0 %', '¤ #,##0.00;¤ -#,##0.00', '#E0'],\n        'PYG',\n        'Gs.',\n        'guaraní paraguayo',\n        {\n            'AUD': [u, '$'],\n            'BRL': [u, 'R$'],\n            'CAD': [u, '$'],\n            'CNY': [u, '¥'],\n            'ESP': ['₧'],\n            'EUR': [u, '€'],\n            'FKP': [u, 'FK£'],\n            'GBP': [u, '£'],\n            'HKD': [u, '$'],\n            'ILS': [u, '₪'],\n            'INR': [u, '₹'],\n            'JPY': [u, '¥'],\n            'KRW': [u, '₩'],\n            'MXN': [u, '$'],\n            'NZD': [u, '$'],\n            'PYG': ['Gs.', '₲'],\n            'RON': [u, 'L'],\n            'SSP': [u, 'SD£'],\n            'SYP': [u, 'S£'],\n            'TWD': [u, 'NT$'],\n            'USD': [u, '$'],\n            'VEF': [u, 'BsF'],\n            'VND': [u, '₫'],\n            'XAF': [],\n            'XCD': [u, '$'],\n            'XOF': []\n        },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC5E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,OAAO,EACP,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAC1BA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAC7F,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAC1E,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAC7C,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAC7F,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAC1E,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAC7C,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EACvF,MAAM,CACT,EACD,CACI,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EACtF,SAAS,EAAE,WAAW,EAAE,WAAW,CACtC,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,EACjE,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,QAAQ,EAAE,SAAS,EAAE,wBAAwB,EAAE,8BAA8B,CAAC,EAC/E,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,SAAS,EAAEA,CAAC,EAAE,uBAAuB,EAAEA,CAAC,CAAC,EAC1C,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,SAAS,EAAE,wBAAwB,EAAE,KAAK,CAAC,EACzD,KAAK,EACL,KAAK,EACL,mBAAmB,EACnB;IACI,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE;EACX,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}