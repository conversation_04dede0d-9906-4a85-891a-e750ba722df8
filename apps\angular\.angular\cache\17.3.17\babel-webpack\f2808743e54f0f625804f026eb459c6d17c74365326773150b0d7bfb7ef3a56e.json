{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class IfReplaceableTemplateExistsDirective {\n  constructor(view, template, replaceableComponentsService) {\n    this.view = view;\n    this.template = template;\n    this.replaceableComponentsService = replaceableComponentsService;\n  }\n  ngAfterViewInit() {\n    const replaceableComponentInstance = this.replaceableComponentsService.get(this.abpIfReplaceableTemplateExists);\n    const isReplaceableComponentInstanceNotExits = !replaceableComponentInstance;\n    if (isReplaceableComponentInstanceNotExits) {\n      return;\n    }\n    this.view.createEmbeddedView(this.template);\n  }\n  static {\n    this.ɵfac = function IfReplaceableTemplateExistsDirective_Factory(t) {\n      return new (t || IfReplaceableTemplateExistsDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i1.ReplaceableComponentsService));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: IfReplaceableTemplateExistsDirective,\n      selectors: [[\"\", \"abpIfReplaceableTemplateExists\", \"\"]],\n      inputs: {\n        abpIfReplaceableTemplateExists: \"abpIfReplaceableTemplateExists\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["IfReplaceableTemplateExistsDirective", "constructor", "view", "template", "replaceableComponentsService", "ngAfterViewInit", "replaceableComponentInstance", "get", "abpIfReplaceableTemplateExists", "isReplaceableComponentInstanceNotExits", "createEmbeddedView", "i0", "ɵɵdirectiveInject", "ViewContainerRef", "TemplateRef", "i1", "ReplaceableComponentsService", "selectors", "inputs"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\directives\\if-replaceable-template-exists.directive.ts"], "sourcesContent": ["\r\nimport { AfterViewInit, Directive, Input, TemplateRef, ViewContainerRef, } from '@angular/core';\r\n\r\nimport { ReplaceableComponentsService } from '@abp/ng.core';\r\n\r\n\r\n\r\n@Directive({\r\n  selector: '[abpIfReplaceableTemplateExists]',\r\n})\r\nexport class IfReplaceableTemplateExistsDirective implements AfterViewInit {\r\n\r\n  @Input() abpIfReplaceableTemplateExists: string;\r\n\r\n\r\n\r\n  constructor(\r\n    private view: ViewContainerRef,\r\n    private template: TemplateRef<any>,\r\n    private replaceableComponentsService: ReplaceableComponentsService\r\n  ) { }\r\n\r\n\r\n\r\n  ngAfterViewInit(): void {\r\n\r\n    const replaceableComponentInstance = this.replaceableComponentsService.get(\r\n      this.abpIfReplaceableTemplateExists\r\n    );\r\n\r\n    const isReplaceableComponentInstanceNotExits = !replaceableComponentInstance;\r\n\r\n    if (isReplaceableComponentInstanceNotExits) {\r\n      return;\r\n    }\r\n\r\n    this.view.createEmbeddedView(this.template);\r\n  }\r\n}\r\n"], "mappings": ";;AAUA,OAAM,MAAOA,oCAAoC;EAM/CC,YACUC,IAAsB,EACtBC,QAA0B,EAC1BC,4BAA0D;IAF1D,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,4BAA4B,GAA5BA,4BAA4B;EAClC;EAIJC,eAAeA,CAAA;IAEb,MAAMC,4BAA4B,GAAG,IAAI,CAACF,4BAA4B,CAACG,GAAG,CACxE,IAAI,CAACC,8BAA8B,CACpC;IAED,MAAMC,sCAAsC,GAAG,CAACH,4BAA4B;IAE5E,IAAIG,sCAAsC,EAAE;MAC1C;IACF;IAEA,IAAI,CAACP,IAAI,CAACQ,kBAAkB,CAAC,IAAI,CAACP,QAAQ,CAAC;EAC7C;;;uBA3BWH,oCAAoC,EAAAW,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,gBAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,4BAAA;IAAA;EAAA;;;YAApChB,oCAAoC;MAAAiB,SAAA;MAAAC,MAAA;QAAAV,8BAAA;MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}