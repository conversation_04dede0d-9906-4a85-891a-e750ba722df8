{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousTuesday\n * @category Weekday Helpers\n * @summary When is the previous Tuesday?\n *\n * @description\n * When is the previous Tuesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Tuesday before Jun, 18, 2021?\n * const result = previousTuesday(new Date(2021, 5, 18))\n * //=> Tue June 15 2021 00:00:00\n */\nexport default function previousTuesday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 2);\n}", "map": {"version": 3, "names": ["requiredArgs", "previousDay", "previousTuesday", "date", "arguments"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/previousTuesday/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousTuesday\n * @category Weekday Helpers\n * @summary When is the previous Tuesday?\n *\n * @description\n * When is the previous Tuesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Tuesday before Jun, 18, 2021?\n * const result = previousTuesday(new Date(2021, 5, 18))\n * //=> Tue June 15 2021 00:00:00\n */\nexport default function previousTuesday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 2);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,WAAW,MAAM,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC5CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOH,WAAW,CAACE,IAAI,EAAE,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}