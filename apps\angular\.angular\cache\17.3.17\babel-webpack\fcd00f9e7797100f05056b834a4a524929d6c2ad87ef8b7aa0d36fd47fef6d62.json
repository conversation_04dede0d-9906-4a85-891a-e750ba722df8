{"ast": null, "code": "import { SharedModule } from '@app/shared/shared.module';\nimport { SurveyModule } from \"survey-angular-ui\";\nimport { CommonModule } from '@angular/common';\nimport { CoreModule } from '@abp/ng.core';\nimport { EsAssesmentListComponent } from './containers/es-assesment-list/es-assesment-list.component';\nimport { EsAssesmentListRoutingModule } from './es-assesment-list.routing.module';\nimport * as i0 from \"@angular/core\";\nexport class EsAssesmentListModule {\n  static {\n    this.ɵfac = function EsAssesmentListModule_Factory(t) {\n      return new (t || EsAssesmentListModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EsAssesmentListModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CommonModule, SurveyModule, CoreModule, EsAssesmentListRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EsAssesmentListModule, {\n    declarations: [EsAssesmentListComponent],\n    imports: [SharedModule, CommonModule, SurveyModule, CoreModule, EsAssesmentListRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "SurveyModule", "CommonModule", "CoreModule", "EsAssesmentListComponent", "EsAssesmentListRoutingModule", "EsAssesmentListModule", "declarations", "imports"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\es-assesment-list\\es-assesment-list.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { SharedModule } from '@app/shared/shared.module';\r\nimport { SurveyModule } from \"survey-angular-ui\";\r\nimport { CommonModule } from '@angular/common';\r\nimport { CoreModule } from '@abp/ng.core';\r\nimport { EsAssesmentListComponent } from './containers/es-assesment-list/es-assesment-list.component';\r\nimport { EsAssesmentListRoutingModule } from './es-assesment-list.routing.module';\r\n\r\n@NgModule({\r\n\r\n    imports: [\r\n\r\n        SharedModule,\r\n        CommonModule,\r\n        SurveyModule,\r\n        CoreModule,\r\n        EsAssesmentListRoutingModule\r\n\r\n    ],\r\n\r\n    declarations: [\r\n        EsAssesmentListComponent\r\n    ],\r\n})\r\nexport class EsAssesmentListModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,wBAAwB,QAAQ,4DAA4D;AACrG,SAASC,4BAA4B,QAAQ,oCAAoC;;AAkBjF,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAZ1BN,YAAY,EACZE,YAAY,EACZD,YAAY,EACZE,UAAU,EACVE,4BAA4B;IAAA;EAAA;;;2EAQvBC,qBAAqB;IAAAC,YAAA,GAH1BH,wBAAwB;IAAAI,OAAA,GATxBR,YAAY,EACZE,YAAY,EACZD,YAAY,EACZE,UAAU,EACVE,4BAA4B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}