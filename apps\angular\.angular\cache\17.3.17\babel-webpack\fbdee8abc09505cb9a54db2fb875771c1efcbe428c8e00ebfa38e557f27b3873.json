{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Channel, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\nimport { __extendsFn as __extends } from \"@microsoft/applicationinsights-shims\";\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\nimport { utlGetSessionStorage, utlSetSessionStorage } from \"@microsoft/applicationinsights-common\";\nimport { _throwInternal, arrForEach, arrIndexOf, dumpObj, getExceptionName, getJSON, isArray, isFunction, isString } from \"@microsoft/applicationinsights-core-js\";\nimport { _DYN_BATCH_PAYLOADS, _DYN_BUFFER_OVERRIDE, _DYN_CLEAR, _DYN_CLEAR_SENT, _DYN_COUNT, _DYN_EMIT_LINE_DELIMITED_0, _DYN_ENQUEUE, _DYN_EVENTS_LIMIT_IN_MEM, _DYN_LENGTH, _DYN_MARK_AS_SENT, _DYN_NAME_PREFIX, _DYN_PUSH, _DYN_STRINGIFY, _DYN__BUFFER__KEY, _DYN__MAX__BUFFER__SIZE, _DYN__SENT__BUFFER__KEY } from \"./__DynamicConstants\";\nvar BaseSendBuffer = /** @class */function () {\n  function BaseSendBuffer(logger, config) {\n    var _buffer = [];\n    var _bufferFullMessageSent = false;\n    this._get = function () {\n      return _buffer;\n    };\n    this._set = function (buffer) {\n      _buffer = buffer;\n      return _buffer;\n    };\n    dynamicProto(BaseSendBuffer, this, function (_self) {\n      _self[_DYN_ENQUEUE /* @min:%2eenqueue */] = function (payload) {\n        if (_self[_DYN_COUNT /* @min:%2ecount */]() >= config[_DYN_EVENTS_LIMIT_IN_MEM /* @min:%2eeventsLimitInMem */]()) {\n          // sent internal log only once per page view\n          if (!_bufferFullMessageSent) {\n            _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 105 /* _eInternalMessageId.InMemoryStorageBufferFull */, \"Maximum in-memory buffer size reached: \" + _self[_DYN_COUNT /* @min:%2ecount */](), true);\n            _bufferFullMessageSent = true;\n          }\n          return;\n        }\n        _buffer[_DYN_PUSH /* @min:%2epush */](payload);\n      };\n      _self[_DYN_COUNT /* @min:%2ecount */] = function () {\n        return _buffer[_DYN_LENGTH /* @min:%2elength */];\n      };\n      _self.size = function () {\n        var size = _buffer[_DYN_LENGTH /* @min:%2elength */];\n        for (var lp = 0; lp < _buffer[_DYN_LENGTH /* @min:%2elength */]; lp++) {\n          size += _buffer[lp][_DYN_LENGTH /* @min:%2elength */];\n        }\n        if (!config[_DYN_EMIT_LINE_DELIMITED_0 /* @min:%2eemitLineDelimitedJson */]()) {\n          size += 2;\n        }\n        return size;\n      };\n      _self[_DYN_CLEAR /* @min:%2eclear */] = function () {\n        _buffer = [];\n        _bufferFullMessageSent = false;\n      };\n      _self.getItems = function () {\n        return _buffer.slice(0);\n      };\n      _self[_DYN_BATCH_PAYLOADS /* @min:%2ebatchPayloads */] = function (payload) {\n        if (payload && payload[_DYN_LENGTH /* @min:%2elength */] > 0) {\n          var batch = config[_DYN_EMIT_LINE_DELIMITED_0 /* @min:%2eemitLineDelimitedJson */]() ? payload.join(\"\\n\") : \"[\" + payload.join(\",\") + \"]\";\n          return batch;\n        }\n        return null;\n      };\n    });\n  }\n  // Removed Stub for BaseSendBuffer.prototype.enqueue.\n  // Removed Stub for BaseSendBuffer.prototype.count.\n  // Removed Stub for BaseSendBuffer.prototype.size.\n  // Removed Stub for BaseSendBuffer.prototype.clear.\n  // Removed Stub for BaseSendBuffer.prototype.getItems.\n  // Removed Stub for BaseSendBuffer.prototype.batchPayloads.\n  // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n  // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n  // this will be removed when ES3 support is dropped.\n  BaseSendBuffer.__ieDyn = 1;\n  return BaseSendBuffer;\n}();\n/*\r\n * An array based send buffer.\r\n */\nvar ArraySendBuffer = /** @class */function (_super) {\n  __extends(ArraySendBuffer, _super);\n  function ArraySendBuffer(logger, config) {\n    var _this = _super.call(this, logger, config) || this;\n    dynamicProto(ArraySendBuffer, _this, function (_self, _base) {\n      _self[_DYN_MARK_AS_SENT /* @min:%2emarkAsSent */] = function (payload) {\n        _base[_DYN_CLEAR /* @min:%2eclear */]();\n      };\n      _self[_DYN_CLEAR_SENT /* @min:%2eclearSent */] = function (payload) {\n        // not supported\n      };\n    });\n    return _this;\n  }\n  // Removed Stub for ArraySendBuffer.prototype.markAsSent.\n  // Removed Stub for ArraySendBuffer.prototype.clearSent.\n  // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n  // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n  // this will be removed when ES3 support is dropped.\n  ArraySendBuffer.__ieDyn = 1;\n  return ArraySendBuffer;\n}(BaseSendBuffer);\nexport { ArraySendBuffer };\n/*\r\n * Session storage buffer holds a copy of all unsent items in the browser session storage.\r\n */\nvar SessionStorageSendBuffer = /** @class */function (_super) {\n  __extends(SessionStorageSendBuffer, _super);\n  function SessionStorageSendBuffer(logger, config) {\n    var _this = _super.call(this, logger, config) || this;\n    var _bufferFullMessageSent = false;\n    var _a = config[_DYN_BUFFER_OVERRIDE /* @min:%2ebufferOverride */]() || {\n        getItem: utlGetSessionStorage,\n        setItem: utlSetSessionStorage\n      },\n      getItem = _a.getItem,\n      setItem = _a.setItem;\n    dynamicProto(SessionStorageSendBuffer, _this, function (_self, _base) {\n      var bufferItems = _getBuffer(SessionStorageSendBuffer[_DYN__BUFFER__KEY /* @min:%2eBUFFER_KEY */]);\n      var notDeliveredItems = _getBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */]);\n      var buffer = _self._set(bufferItems.concat(notDeliveredItems));\n      // If the buffer has too many items, drop items from the end.\n      if (buffer[_DYN_LENGTH /* @min:%2elength */] > SessionStorageSendBuffer[_DYN__MAX__BUFFER__SIZE /* @min:%2eMAX_BUFFER_SIZE */]) {\n        buffer[_DYN_LENGTH /* @min:%2elength */] = SessionStorageSendBuffer[_DYN__MAX__BUFFER__SIZE /* @min:%2eMAX_BUFFER_SIZE */];\n      }\n      _setBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */], []);\n      _setBuffer(SessionStorageSendBuffer[_DYN__BUFFER__KEY /* @min:%2eBUFFER_KEY */], buffer);\n      _self[_DYN_ENQUEUE /* @min:%2eenqueue */] = function (payload) {\n        if (_self[_DYN_COUNT /* @min:%2ecount */]() >= SessionStorageSendBuffer[_DYN__MAX__BUFFER__SIZE /* @min:%2eMAX_BUFFER_SIZE */]) {\n          // sent internal log only once per page view\n          if (!_bufferFullMessageSent) {\n            _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 67 /* _eInternalMessageId.SessionStorageBufferFull */, \"Maximum buffer size reached: \" + _self[_DYN_COUNT /* @min:%2ecount */](), true);\n            _bufferFullMessageSent = true;\n          }\n          return;\n        }\n        _base[_DYN_ENQUEUE /* @min:%2eenqueue */](payload);\n        _setBuffer(SessionStorageSendBuffer[_DYN__BUFFER__KEY /* @min:%2eBUFFER_KEY */], _self._get());\n      };\n      _self[_DYN_CLEAR /* @min:%2eclear */] = function () {\n        _base[_DYN_CLEAR /* @min:%2eclear */]();\n        _setBuffer(SessionStorageSendBuffer[_DYN__BUFFER__KEY /* @min:%2eBUFFER_KEY */], _self._get());\n        _setBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */], []);\n        _bufferFullMessageSent = false;\n      };\n      _self[_DYN_MARK_AS_SENT /* @min:%2emarkAsSent */] = function (payload) {\n        _setBuffer(SessionStorageSendBuffer[_DYN__BUFFER__KEY /* @min:%2eBUFFER_KEY */], _self._set(_removePayloadsFromBuffer(payload, _self._get())));\n        var sentElements = _getBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */]);\n        if (sentElements instanceof Array && payload instanceof Array) {\n          sentElements = sentElements.concat(payload);\n          if (sentElements[_DYN_LENGTH /* @min:%2elength */] > SessionStorageSendBuffer[_DYN__MAX__BUFFER__SIZE /* @min:%2eMAX_BUFFER_SIZE */]) {\n            // We send telemetry normally. If the SENT_BUFFER is too big we don't add new elements\n            // until we receive a response from the backend and the buffer has free space again (see clearSent method)\n            _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 67 /* _eInternalMessageId.SessionStorageBufferFull */, \"Sent buffer reached its maximum size: \" + sentElements[_DYN_LENGTH /* @min:%2elength */], true);\n            sentElements[_DYN_LENGTH /* @min:%2elength */] = SessionStorageSendBuffer[_DYN__MAX__BUFFER__SIZE /* @min:%2eMAX_BUFFER_SIZE */];\n          }\n          _setBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */], sentElements);\n        }\n      };\n      _self[_DYN_CLEAR_SENT /* @min:%2eclearSent */] = function (payload) {\n        var sentElements = _getBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */]);\n        sentElements = _removePayloadsFromBuffer(payload, sentElements);\n        _setBuffer(SessionStorageSendBuffer[_DYN__SENT__BUFFER__KEY /* @min:%2eSENT_BUFFER_KEY */], sentElements);\n      };\n      function _removePayloadsFromBuffer(payloads, buffer) {\n        var remaining = [];\n        arrForEach(buffer, function (value) {\n          if (!isFunction(value) && arrIndexOf(payloads, value) === -1) {\n            remaining[_DYN_PUSH /* @min:%2epush */](value);\n          }\n        });\n        return remaining;\n      }\n      function _getBuffer(key) {\n        var prefixedKey = key;\n        try {\n          prefixedKey = config[_DYN_NAME_PREFIX /* @min:%2enamePrefix */] && config[_DYN_NAME_PREFIX /* @min:%2enamePrefix */]() ? config[_DYN_NAME_PREFIX /* @min:%2enamePrefix */]() + \"_\" + prefixedKey : prefixedKey;\n          var bufferJson = getItem(logger, prefixedKey);\n          if (bufferJson) {\n            var buffer_1 = getJSON().parse(bufferJson);\n            if (isString(buffer_1)) {\n              // When using some version prototype.js the stringify / parse cycle does not decode array's correctly\n              buffer_1 = getJSON().parse(buffer_1);\n            }\n            if (buffer_1 && isArray(buffer_1)) {\n              return buffer_1;\n            }\n          }\n        } catch (e) {\n          _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 42 /* _eInternalMessageId.FailedToRestoreStorageBuffer */, \" storage key: \" + prefixedKey + \", \" + getExceptionName(e), {\n            exception: dumpObj(e)\n          });\n        }\n        return [];\n      }\n      function _setBuffer(key, buffer) {\n        var prefixedKey = key;\n        try {\n          prefixedKey = config[_DYN_NAME_PREFIX /* @min:%2enamePrefix */] && config[_DYN_NAME_PREFIX /* @min:%2enamePrefix */]() ? config[_DYN_NAME_PREFIX /* @min:%2enamePrefix */]() + \"_\" + prefixedKey : prefixedKey;\n          var bufferJson = JSON[_DYN_STRINGIFY /* @min:%2estringify */](buffer);\n          setItem(logger, prefixedKey, bufferJson);\n        } catch (e) {\n          // if there was an error, clear the buffer\n          // telemetry is stored in the _buffer array so we won't loose any items\n          setItem(logger, prefixedKey, JSON[_DYN_STRINGIFY /* @min:%2estringify */]([]));\n          _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 41 /* _eInternalMessageId.FailedToSetStorageBuffer */, \" storage key: \" + prefixedKey + \", \" + getExceptionName(e) + \". Buffer cleared\", {\n            exception: dumpObj(e)\n          });\n        }\n      }\n    });\n    return _this;\n  }\n  // Removed Stub for SessionStorageSendBuffer.prototype.enqueue.\n  // Removed Stub for SessionStorageSendBuffer.prototype.clear.\n  // Removed Stub for SessionStorageSendBuffer.prototype.markAsSent.\n  // Removed Stub for SessionStorageSendBuffer.prototype.clearSent.\n  SessionStorageSendBuffer.BUFFER_KEY = \"AI_buffer\";\n  SessionStorageSendBuffer.SENT_BUFFER_KEY = \"AI_sentBuffer\";\n  // Maximum number of payloads stored in the buffer. If the buffer is full, new elements will be dropped.\n  SessionStorageSendBuffer.MAX_BUFFER_SIZE = 2000;\n  return SessionStorageSendBuffer;\n}(BaseSendBuffer);\nexport { SessionStorageSendBuffer };\n//# sourceMappingURL=SendBuffer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}