{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"vai\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"ꕞꕌꔵ\", \"ꗳꗡꘉ\", \"ꕚꕞꕚ\", \"ꕉꕞꕒ\", \"ꕉꔤꕆꕢ\", \"ꕉꔤꕀꕮ\", \"ꔻꔬꔳ\"], u, u], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ꖨꖕꔞ\", \"ꕒꕡ\", \"ꕾꖺ\", \"ꖢꖕ\", \"ꖑꕱ\", \"ꖱꘋ\", \"ꖱꕞ\", \"ꗛꔕ\", \"ꕢꕌ\", \"ꕭꖃ\", \"ꔞꘋ\", \"ꖨꖕꗏ\"], [\"ꖨꖕ ꕪꕴ ꔞꔀꕮꕊ\", \"ꕒꕡꖝꖕ\", \"ꕾꖺ\", \"ꖢꖕ\", \"ꖑꕱ\", \"ꖱꘋ\", \"ꖱꕞꔤ\", \"ꗛꔕ\", \"ꕢꕌ\", \"ꕭꖃ\", \"ꔞꘋꕔꕿ ꕸꖃꗏ\", \"ꖨꖕ ꕪꕴ ꗏꖺꕮꕊ\"]], u, [[\"BCE\", \"CE\"], u, u], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"LRD\", \"$\", \"ꕞꔤꔫꕩ ꕜꕞꕌ\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"LRD\": [\"$\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/vai.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"vai\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"ꕞꕌꔵ\", \"ꗳꗡꘉ\", \"ꕚꕞꕚ\", \"ꕉꕞꕒ\", \"ꕉꔤꕆꕢ\", \"ꕉꔤꕀꕮ\", \"ꔻꔬꔳ\"], u, u], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ꖨꖕꔞ\", \"ꕒꕡ\", \"ꕾꖺ\", \"ꖢꖕ\", \"ꖑꕱ\", \"ꖱꘋ\", \"ꖱꕞ\", \"ꗛꔕ\", \"ꕢꕌ\", \"ꕭꖃ\", \"ꔞꘋ\", \"ꖨꖕꗏ\"], [\"ꖨꖕ ꕪꕴ ꔞꔀꕮꕊ\", \"ꕒꕡꖝꖕ\", \"ꕾꖺ\", \"ꖢꖕ\", \"ꖑꕱ\", \"ꖱꘋ\", \"ꖱꕞꔤ\", \"ꗛꔕ\", \"ꕢꕌ\", \"ꕭꖃ\", \"ꔞꘋꕔꕿ ꕸꖃꗏ\", \"ꖨꖕ ꕪꕴ ꗏꖺꕮꕊ\"]], u, [[\"BCE\", \"CE\"], u, u], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"LRD\", \"$\", \"ꕞꔤꔫꕩ ꕜꕞꕌ\", { \"JPY\": [\"JP¥\", \"¥\"], \"LRD\": [\"$\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}