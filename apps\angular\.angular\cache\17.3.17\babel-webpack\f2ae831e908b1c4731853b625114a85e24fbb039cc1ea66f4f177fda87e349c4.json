{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { AppComponentBase } from '@app/app-component-base';\nimport { BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { RedFlagStatus } from 'proxies/economic-service/lib/proxy/bdo/ess/shared/constants/red-flags';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/red-flags\";\nimport * as i3 from \"@abp/ng.core\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ngx-validate/core\";\nimport * as i6 from \"../../../shared/components/bdo-table/bdo-table.component\";\nconst _c0 = () => [10, 15, 20, 30];\nconst _c1 = a0 => ({\n  \"bg-white\": a0\n});\nfunction RedFlagsComponent_div_0_div_7_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"label\", 29);\n    i0.ɵɵtext(2, \"Value\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 30);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RedFlagsComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\")(2, \"span\", 8);\n    i0.ɵɵtext(3, \"EDIT SETTINGS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 9);\n    i0.ɵɵtext(5, \"Select a row to edit settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"form\", 11);\n    i0.ɵɵlistener(\"ngSubmit\", function RedFlagsComponent_div_0_div_7_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(8, \"div\", 12)(9, \"label\", 13);\n    i0.ɵɵtext(10, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 12)(13, \"label\", 15);\n    i0.ɵɵtext(14, \"Event Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 12)(17, \"label\", 17);\n    i0.ɵɵtext(18, \"Display Priority\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 12)(21, \"label\", 19);\n    i0.ɵɵtext(22, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"textarea\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, RedFlagsComponent_div_0_div_7_div_24_Template, 4, 0, \"div\", 21);\n    i0.ɵɵelementStart(25, \"div\", 12)(26, \"div\", 22)(27, \"div\", 23)(28, \"label\", 24);\n    i0.ɵɵtext(29, \"Status\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 25);\n    i0.ɵɵelement(31, \"input\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 12)(33, \"button\", 27);\n    i0.ɵɵtext(34, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RedFlagsComponent_div_0_div_7_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(36, \"Cancel\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, ctx_r1.selectedRow));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.selectedRow));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isParamFlag);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.selectedRow || !ctx_r1.form.valid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.selectedRow);\n  }\n}\nfunction RedFlagsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\")(3, \"span\", 3);\n    i0.ɵɵtext(4, \"RED FLAGS SETTINGS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 4)(6, \"bdo-table\", 5);\n    i0.ɵɵlistener(\"onLazyLoad\", function RedFlagsComponent_div_0_Template_bdo_table_onLazyLoad_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLazyLoadEvent($event));\n    })(\"onRowClick\", function RedFlagsComponent_div_0_Template_bdo_table_onRowClick_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRowClicked($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, RedFlagsComponent_div_0_div_7_Template, 37, 10, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"id\", ctx_r1.tableId)(\"columns\", ctx_r1.redFlagsTableColumns)(\"pageIndex\", ctx_r1.currentPageIndex)(\"pageSize\", ctx_r1.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(10, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.editAllowed);\n  }\n}\nexport let RedFlagsComponent = /*#__PURE__*/(() => {\n  class RedFlagsComponent extends AppComponentBase {\n    constructor(injector, fb, redflagService, permissionService) {\n      super(injector);\n      this.fb = fb;\n      this.redflagService = redflagService;\n      this.permissionService = permissionService;\n      this.tableId = 'redflags-table';\n      this.request = {\n        isAscending: true,\n        sorting: '',\n        skipCount: 0,\n        maxResultCount: 10\n      };\n      this.records = [];\n      this.totalRecords = 0;\n      this.currentPageIndex = 0;\n      this.pageSize = 10;\n      this.isParamFlag = false;\n      this.editAllowed = false;\n      this.viewAllowed = false;\n      this.redFlagsTableColumns = [{\n        columnId: \"categoryDescription\" /* RedFlagsTableColumns.CATEGORY_DESCRIPTION */,\n        type: BdoTableColumnType.String,\n        maxWidth: 150,\n        frozenLeft: true,\n        isSortable: true,\n        columnName: 'Category'\n      }, {\n        columnId: \"eventTypeDescription\" /* RedFlagsTableColumns.EVENTTYPE_DESCRIPTION */,\n        type: BdoTableColumnType.String,\n        minWidth: 1,\n        isSortable: true,\n        columnName: 'Event Type'\n      }, {\n        columnId: \"priority\" /* RedFlagsTableColumns.PRIORITY */,\n        type: BdoTableColumnType.Number,\n        maxWidth: 60,\n        isSortable: true,\n        columnName: 'Display Priority'\n      }, {\n        columnId: \"description\" /* RedFlagsTableColumns.DESCRIPTION */,\n        type: BdoTableColumnType.String,\n        minWidth: 1,\n        isSortable: true,\n        columnName: 'Description'\n      }, {\n        columnId: \"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */,\n        type: BdoTableColumnType.Number,\n        maxWidth: 60,\n        isSortable: true,\n        columnName: 'Value'\n      }, {\n        columnId: \"status\" /* RedFlagsTableColumns.STATUS */,\n        type: BdoTableColumnType.String,\n        maxWidth: 60,\n        isSortable: true,\n        columnName: 'Status'\n      }];\n      this.createForm();\n    }\n    createForm() {\n      this.form = this.fb.group({\n        id: [{\n          value: ''\n        }],\n        categoryDescription: [{\n          value: '',\n          disabled: true\n        }],\n        eventTypeDescription: [{\n          value: '',\n          disabled: true\n        }],\n        priority: [{\n          value: ''\n        }, [Validators.required, Validators.min(1), Validators.pattern('^[0-9]+$')]],\n        description: [{\n          value: ''\n        }, [Validators.required, Validators.maxLength(500)]],\n        paramValue: [{\n          value: '',\n          disabled: true\n        }, [Validators.required, Validators.min(1), Validators.pattern('^[0-9]+(?:\\\\.[0-9]{1,2})?$')]],\n        status: [{\n          value: false\n        }]\n      });\n    }\n    ngOnInit() {\n      this.updateUI('INIT');\n      this.permissionService.getGrantedPolicy$(\"CAPortal.RedFlag.Edit\" /* Permissions.CAPORTAL_REDFLAG_EDIT */).subscribe(result => {\n        this.editAllowed = result;\n      });\n      this.permissionService.getGrantedPolicy$(\"CAPortal.RedFlag.View\" /* Permissions.CAPORTAL_REDFLAG_VIEW */).subscribe(result => {\n        this.viewAllowed = result;\n      });\n    }\n    fetchData() {\n      this.redflagService.getRedFlags(this.request).subscribe(result => {\n        this.records = result.data;\n        this.totalRecords = result.totalRecords;\n        this.setTableData();\n      });\n    }\n    onLazyLoadEvent(event) {\n      this.request.isAscending = event.isAscending;\n      this.request.sorting = event.sortField;\n      this.request.skipCount = event.pageNumber * event.pageSize;\n      this.request.maxResultCount = event.pageSize;\n      this.fetchData();\n    }\n    onRowClicked(event) {\n      this.selectedRow = event.rowData;\n      this.updateUI('ROWCLICKED');\n      if (event.rowData.cells.find(x => x.columnId === \"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */).value !== '') {\n        this.updateUI('PARAMFLAG');\n      } else {\n        this.updateUI('NONPARAMFLAG');\n      }\n      this.form.setValue({\n        id: event.rowData.id,\n        categoryDescription: event.rowData.cells.find(x => x.columnId === \"categoryDescription\" /* RedFlagsTableColumns.CATEGORY_DESCRIPTION */).value,\n        eventTypeDescription: event.rowData.cells.find(x => x.columnId === \"eventTypeDescription\" /* RedFlagsTableColumns.EVENTTYPE_DESCRIPTION */).value,\n        priority: event.rowData.cells.find(x => x.columnId === \"priority\" /* RedFlagsTableColumns.PRIORITY */).value,\n        description: event.rowData.cells.find(x => x.columnId === \"description\" /* RedFlagsTableColumns.DESCRIPTION */).value,\n        paramValue: event.rowData.cells.find(x => x.columnId === \"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */).value,\n        status: event.rowData.cells.find(x => x.columnId === \"status\" /* RedFlagsTableColumns.STATUS */).value === 'Active' ? true : false\n      });\n    }\n    setTableData() {\n      const tableData = new BdoTableData();\n      tableData.tableId = this.tableId;\n      tableData.totalRecords = this.totalRecords;\n      tableData.resetToFirstPage = false;\n      tableData.data = this.records.map(item => {\n        return {\n          id: item.id,\n          rawData: item,\n          cells: [{\n            columnId: \"categoryDescription\" /* RedFlagsTableColumns.CATEGORY_DESCRIPTION */,\n            value: item.categoryDescription\n          }, {\n            columnId: \"eventTypeDescription\" /* RedFlagsTableColumns.EVENTTYPE_DESCRIPTION */,\n            value: item.eventTypeDescription\n          }, {\n            columnId: \"priority\" /* RedFlagsTableColumns.PRIORITY */,\n            value: item.priority\n          }, {\n            columnId: \"description\" /* RedFlagsTableColumns.DESCRIPTION */,\n            value: item.description\n          }, {\n            columnId: \"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */,\n            value: item.paramValue === 0 ? '' : item.paramValue\n          }, {\n            columnId: \"status\" /* RedFlagsTableColumns.STATUS */,\n            value: item.status === RedFlagStatus.Active ? 'Active' : 'Inactive'\n          }]\n        };\n      });\n      setTimeout(() => {\n        this.tableService.setGridData(tableData);\n      }, 0);\n    }\n    onCancel() {\n      this.updateUI('CANCEL');\n    }\n    updateUI(action) {\n      switch (action) {\n        case 'INIT':\n          this.updateUI('CANCEL');\n          this.fetchData();\n          break;\n        case 'NONPARAMFLAG':\n          this.isParamFlag = false;\n          this.form.get(\"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */).disable();\n          break;\n        case 'PARAMFLAG':\n          this.isParamFlag = true;\n          this.form.get(\"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */).enable();\n          break;\n        case 'ROWCLICKED':\n          this.form.enable();\n          break;\n        case 'CANCEL':\n          this.form.reset();\n          this.form.disable();\n          this.isParamFlag = false;\n          this.selectedRow = null;\n          break;\n        default:\n          break;\n      }\n    }\n    onSubmit() {\n      if (this.form.valid && this.selectedRow) {\n        const modified = {\n          id: this.selectedRow.id,\n          category: this.selectedRow.rawData.category,\n          categoryDescription: this.selectedRow.rawData.categoryDescription,\n          eventType: this.selectedRow.rawData.eventType,\n          eventTypeDescription: this.selectedRow.rawData.eventTypeDescription,\n          priority: this.form.get(\"priority\" /* RedFlagsTableColumns.PRIORITY */).value,\n          description: this.form.get(\"description\" /* RedFlagsTableColumns.DESCRIPTION */).value,\n          type: this.selectedRow.rawData.type,\n          paramValue: this.form.get(\"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */).value === '' ? 0 : this.form.get(\"paramValue\" /* RedFlagsTableColumns.PARAM_VALUE */).value,\n          status: this.form.get(\"status\" /* RedFlagsTableColumns.STATUS */).value ? RedFlagStatus.Active : RedFlagStatus.Inactive\n        };\n        this.redflagService.updateRedFlag(modified).subscribe(() => {\n          this.fetchData();\n          this.updateUI('CANCEL');\n        });\n      }\n    }\n    static {\n      this.ɵfac = function RedFlagsComponent_Factory(t) {\n        return new (t || RedFlagsComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.RedFlagService), i0.ɵɵdirectiveInject(i3.PermissionService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RedFlagsComponent,\n        selectors: [[\"app-red-flags\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"display-flex\", 4, \"ngIf\"], [1, \"display-flex\"], [1, \"left-column\"], [1, \"title\"], [1, \"table-container\"], [\"scrollHeight\", \"70vh\", 3, \"onLazyLoad\", \"onRowClick\", \"id\", \"columns\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"class\", \"right-column\", 4, \"ngIf\"], [1, \"right-column\"], [1, \"edit-title\"], [1, \"edit-subtitle\"], [1, \"form-container\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [\"for\", \"categoryDescription\"], [\"type\", \"text\", \"id\", \"categoryDescription\", \"readonly\", \"\", \"formControlName\", \"categoryDescription\", 1, \"form-control\"], [\"for\", \"eventTypeDescription\"], [\"type\", \"text\", \"id\", \"eventTypeDescription\", \"readonly\", \"\", \"formControlName\", \"eventTypeDescription\", 1, \"form-control\"], [\"for\", \"priority\"], [\"type\", \"number\", \"id\", \"priority\", \"formControlName\", \"priority\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"description\"], [\"id\", \"description\", \"formControlName\", \"description\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"form-group\", 4, \"ngIf\"], [1, \"checkbox-container\"], [2, \"padding-right\", \"10px\"], [\"for\", \"status\"], [2, \"padding-top\", \"4px\"], [\"type\", \"checkbox\", \"id\", \"status\", \"formControlName\", \"status\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 2, \"margin-right\", \"5px\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\", \"disabled\"], [\"for\", \"paramValue\"], [\"type\", \"number\", \"id\", \"paramValue\", \"formControlName\", \"paramValue\", 1, \"form-control\", \"bg-white\"]],\n        template: function RedFlagsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, RedFlagsComponent_div_0_Template, 8, 11, \"div\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.viewAllowed);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.ValidationGroupDirective, i5.ValidationDirective, i6.BdoTableComponent],\n        styles: [\".bg-white[_ngcontent-%COMP%]{background-color:#fff!important}.table-container[_ngcontent-%COMP%]{z-index:0;position:relative;margin-right:.5em;min-height:100%!important}.title[_ngcontent-%COMP%]{font-size:1.5em;color:#00779b;display:block;margin:0 auto;padding:1.5rem}.edit-title[_ngcontent-%COMP%]{font-size:1.5em;color:#00779b;display:block;margin:0 auto;padding-top:1.5rem;padding-left:1.5rem}.edit-subtitle[_ngcontent-%COMP%]{padding-left:1.5rem;padding-bottom:1.5rem}.left-column[_ngcontent-%COMP%]{flex:1;margin-right:.5em;min-width:70%}.right-column[_ngcontent-%COMP%]{flex:1;background-color:#f3fafd;min-width:20%;overflow-y:auto;position:relative}.display-flex[_ngcontent-%COMP%]{display:flex}.form-container[_ngcontent-%COMP%]{width:100%;box-sizing:border-box}.form-group[_ngcontent-%COMP%]{margin:1rem}.form-control[_ngcontent-%COMP%]{width:100%}.checkbox-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;height:30px}\"]\n      });\n    }\n  }\n  return RedFlagsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}