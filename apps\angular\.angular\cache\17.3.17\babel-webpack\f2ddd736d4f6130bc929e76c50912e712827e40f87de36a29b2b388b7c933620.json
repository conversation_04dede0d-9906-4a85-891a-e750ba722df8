{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class StockExchangeService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'LookupService';\n    this.create = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/lookup-service/stockExchange',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.delete = (id, config) => this.restService.request({\n      method: 'DELETE',\n      url: `/api/lookup-service/stockExchange/${id}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.get = (id, config) => this.restService.request({\n      method: 'GET',\n      url: `/api/lookup-service/stockExchange/${id}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getList = (input, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/lookup-service/stockExchange',\n      params: {\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.update = (id, input, config) => this.restService.request({\n      method: 'PUT',\n      url: `/api/lookup-service/stockExchange/${id}`,\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function StockExchangeService_Factory(t) {\n      return new (t || StockExchangeService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: StockExchangeService,\n      factory: StockExchangeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["StockExchangeService", "constructor", "restService", "apiName", "create", "input", "config", "request", "method", "url", "body", "delete", "id", "get", "getList", "params", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "update", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\lookup-service\\lib\\proxy\\bdo\\ess\\lookup-service\\corporate-entity\\stock-exchange.service.ts"], "sourcesContent": ["import type { StockExchangeDto } from './models';\r\nimport { RestService, Rest } from '@abp/ng.core';\r\nimport type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StockExchangeService {\r\n  apiName = 'LookupService';\r\n  \r\n\r\n  create = (input: StockExchangeDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, StockExchangeDto>({\r\n      method: 'POST',\r\n      url: '/api/lookup-service/stockExchange',\r\n      body: input,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  delete = (id: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, void>({\r\n      method: 'DELETE',\r\n      url: `/api/lookup-service/stockExchange/${id}`,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  get = (id: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, StockExchangeDto>({\r\n      method: 'GET',\r\n      url: `/api/lookup-service/stockExchange/${id}`,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, PagedResultDto<StockExchangeDto>>({\r\n      method: 'GET',\r\n      url: '/api/lookup-service/stockExchange',\r\n      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  update = (id: string, input: StockExchangeDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, StockExchangeDto>({\r\n      method: 'PUT',\r\n      url: `/api/lookup-service/stockExchange/${id}`,\r\n      body: input,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAQA,OAAM,MAAOA,oBAAoB;EA8C/BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA7C/B,KAAAC,OAAO,GAAG,eAAe;IAGzB,KAAAC,MAAM,GAAG,CAACC,KAAuB,EAAEC,MAA6B,KAC9D,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwB;MAC9CC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,mCAAmC;MACxCC,IAAI,EAAEL;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAK,MAAM,GAAG,CAACC,EAAU,EAAEN,MAA6B,KACjD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAY;MAClCC,MAAM,EAAE,QAAQ;MAChBC,GAAG,EAAE,qCAAqCG,EAAE;KAC7C,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAO,GAAG,GAAG,CAACD,EAAU,EAAEN,MAA6B,KAC9C,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwB;MAC9CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,qCAAqCG,EAAE;KAC7C,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAQ,OAAO,GAAG,CAACT,KAAqC,EAAEC,MAA6B,KAC7E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwC;MAC9DC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,mCAAmC;MACxCM,MAAM,EAAE;QAAEC,OAAO,EAAEX,KAAK,CAACW,OAAO;QAAEC,SAAS,EAAEZ,KAAK,CAACY,SAAS;QAAEC,cAAc,EAAEb,KAAK,CAACa;MAAc;KACnG,EACD;MAAEf,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAa,MAAM,GAAG,CAACP,EAAU,EAAEP,KAAuB,EAAEC,MAA6B,KAC1E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwB;MAC9CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,qCAAqCG,EAAE,EAAE;MAC9CF,IAAI,EAAEL;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBA9CpCN,oBAAoB,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAApBvB,oBAAoB;MAAAwB,OAAA,EAApBxB,oBAAoB,CAAAyB,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}