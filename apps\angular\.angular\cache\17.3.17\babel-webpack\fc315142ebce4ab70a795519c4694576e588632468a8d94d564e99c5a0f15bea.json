{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/so-ET\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['so-ET', [['h', 'd'], ['GH', 'GD'], u], [['GH', 'GD'], u, u], [['A', 'I', 'T', 'A', 'Kh', 'J', 'S'], ['Axd', 'Isn', 'Tldo', 'Arbc', 'Khms', 'Jmc', 'Sbti'], ['Axad', 'Isniin', 'Talaado', 'Arbaco', 'Khamiis', 'Jimco', 'Sabti'], ['Axd', 'Isn', 'Tldo', 'Arbc', 'Khms', 'Jmc', 'Sbti']], u, [['J', 'F', 'M', 'A', 'M', 'J', 'L', 'O', 'S', 'O', 'N', 'D'], ['Jan', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Lul', 'Ogs', 'Seb', 'Okt', 'Nof', 'Dis'], ['Bisha Koobaad', 'Bisha Labaad', 'Bisha Saddexaad', 'Bisha Afraad', 'Bisha Shanaad', 'Bisha Lixaad', 'Bisha Todobaad', 'Bisha Sideedaad', 'Bisha Sagaalaad', 'Bisha Tobnaad', 'Bisha Kow iyo Tobnaad', 'Bisha Laba iyo Tobnaad']], [['J', 'F', 'M', 'A', 'M', 'J', 'L', 'O', 'S', 'O', 'N', 'D'], ['Jan', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Lul', 'Ogs', 'Seb', 'Okt', 'Nof', 'Dis'], ['Jannaayo', 'Febraayo', 'Maarso', 'Abriil', 'May', 'Juun', 'Luuliyo', 'Ogost', 'Sebtembar', 'Oktoobar', 'Nofembar', 'Desembar']], [['B', 'A'], ['CH', 'CD'], ['Ciise Hortii', 'Ciise Dabadii']], 0, [6, 0], ['dd/MM/yy', 'dd-MMM-y', 'dd MMMM y', 'EEEE, MMMM dd, y'], ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'], ['{1} {0}', u, u, u], ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'MaL', ':'], ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'], 'ETB', 'Br', 'Birta Itoobbiya', {\n    'BBD': ['DBB', '$'],\n    'ETB': ['Br'],\n    'JPY': ['JP¥', '¥'],\n    'SOS': ['S'],\n    'USD': ['US$', '$']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/so-ET.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/so-ET\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'so-ET',\n        [['h', 'd'], ['GH', 'GD'], u],\n        [['GH', 'GD'], u, u],\n        [\n            ['A', 'I', 'T', 'A', 'Kh', 'J', 'S'], ['Axd', 'Isn', 'Tldo', 'Arbc', 'Khms', 'Jmc', 'Sbti'],\n            ['Axad', 'Isniin', 'Talaado', 'Arbaco', 'Khamiis', 'Jimco', 'Sabti'],\n            ['Axd', 'Isn', 'Tldo', 'Arbc', 'Khms', 'Jmc', 'Sbti']\n        ],\n        u,\n        [\n            ['J', 'F', 'M', 'A', 'M', 'J', 'L', 'O', 'S', 'O', 'N', 'D'],\n            ['Jan', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Lul', 'Ogs', 'Seb', 'Okt', 'Nof', 'Dis'],\n            [\n                'Bisha Koobaad', 'Bisha Labaad', 'Bisha Saddexaad', 'Bisha Afraad', 'Bisha Shanaad',\n                'Bisha Lixaad', 'Bisha Todobaad', 'Bisha Sideedaad', 'Bisha Sagaalaad', 'Bisha Tobnaad',\n                'Bisha Kow iyo Tobnaad', 'Bisha Laba iyo Tobnaad'\n            ]\n        ],\n        [\n            ['J', 'F', 'M', 'A', 'M', 'J', 'L', 'O', 'S', 'O', 'N', 'D'],\n            ['Jan', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Lul', 'Ogs', 'Seb', 'Okt', 'Nof', 'Dis'],\n            [\n                'Jannaayo', 'Febraayo', 'Maarso', 'Abriil', 'May', 'Juun', 'Luuliyo', 'Ogost', 'Sebtembar',\n                'Oktoobar', 'Nofembar', 'Desembar'\n            ]\n        ],\n        [['B', 'A'], ['CH', 'CD'], ['Ciise Hortii', 'Ciise Dabadii']],\n        0,\n        [6, 0],\n        ['dd/MM/yy', 'dd-MMM-y', 'dd MMMM y', 'EEEE, MMMM dd, y'],\n        ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'],\n        ['{1} {0}', u, u, u],\n        ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'MaL', ':'],\n        ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'],\n        'ETB',\n        'Br',\n        'Birta Itoobbiya',\n        { 'BBD': ['DBB', '$'], 'ETB': ['Br'], 'JPY': ['JP¥', '¥'], 'SOS': ['S'], 'USD': ['US$', '$'] },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC5E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,OAAO,EACP,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,CAAC,EAC7B,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAC3F,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EACpE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CACxD,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACpF,CACI,eAAe,EAAE,cAAc,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,EACnF,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EACvF,uBAAuB,EAAE,wBAAwB,CACpD,CACJ,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACpF,CACI,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAC1F,UAAU,EAAE,UAAU,EAAE,UAAU,CACrC,CACJ,EACD,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,EAC7D,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,EACzD,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EACxD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAC3C,KAAK,EACL,IAAI,EACJ,iBAAiB,EACjB;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,IAAI,CAAC;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EAAE,CAAC,EAC9F,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}