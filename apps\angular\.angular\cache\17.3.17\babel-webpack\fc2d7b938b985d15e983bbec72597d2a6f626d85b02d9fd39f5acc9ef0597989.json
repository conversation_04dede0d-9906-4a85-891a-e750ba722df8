{"ast": null, "code": "import { HTTP_INTERCEPTORS, HttpResponse } from '@angular/common/http';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./audit-trail.service\";\nimport * as i2 from \"@abp/ng.core\";\n/**\n * Befoe any http request send out, application auto includes custom header, such as client ip address.\n */\nexport class AuditTrailInterceptor {\n  constructor(auditService, stateConfigService) {\n    this.auditService = auditService;\n    this.stateConfigService = stateConfigService;\n  }\n  intercept(request, next) {\n    const currentUser = this.stateConfigService.getOne(\"currentUser\");\n    request = request.clone({\n      setHeaders: {\n        'X-Forwarded-For': this.auditService.getIPAddress(),\n        'Ess-Audit-Uid': currentUser?.id ?? '' //Note: Header key has to Pascal Case.\n      }\n    });\n    return next.handle(request).pipe(tap(event => {\n      if (event instanceof HttpResponse) {\n        // Do custom stuff for response if you want.\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function AuditTrailInterceptor_Factory(t) {\n      return new (t || AuditTrailInterceptor)(i0.ɵɵinject(i1.AuditTrailService), i0.ɵɵinject(i2.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuditTrailInterceptor,\n      factory: AuditTrailInterceptor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n/**\n * Provider POJO for the  Audit Interceptor\n */\nexport const AuditInterceptorProvider = {\n  provide: HTTP_INTERCEPTORS,\n  useClass: AuditTrailInterceptor,\n  multi: true\n};", "map": {"version": 3, "names": ["HTTP_INTERCEPTORS", "HttpResponse", "tap", "AuditTrailInterceptor", "constructor", "auditService", "stateConfigService", "intercept", "request", "next", "currentUser", "getOne", "clone", "setHeaders", "getIPAddress", "id", "handle", "pipe", "event", "i0", "ɵɵinject", "i1", "AuditTrailService", "i2", "ConfigStateService", "factory", "ɵfac", "providedIn", "AuditInterceptorProvider", "provide", "useClass", "multi"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\audit\\services\\audit-trail.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  HttpRequest,\r\n  HttpHandler,\r\n  HttpEvent,\r\n  HttpInterceptor,\r\n  HTTP_INTERCEPTORS,\r\n  HttpResponse,\r\n} from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { tap } from 'rxjs/operators';\r\nimport { AuditTrailService } from './audit-trail.service';\r\nimport { AuthService, ConfigStateService, CurrentUserDto } from '@abp/ng.core';\r\n/**\r\n * Befoe any http request send out, application auto includes custom header, such as client ip address.\r\n */\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuditTrailInterceptor implements HttpInterceptor {\r\n  constructor(private auditService: AuditTrailService,  private stateConfigService: ConfigStateService) {}\r\n\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    const currentUser = this.stateConfigService.getOne(\"currentUser\") as CurrentUserDto;\r\n    request = request.clone({\r\n      setHeaders: {\r\n        'X-Forwarded-For': this.auditService.getIPAddress(),\r\n        'Ess-Audit-Uid': currentUser?.id??'', //Note: Header key has to Pascal Case.\r\n      },\r\n    });\r\n\r\n    return next.handle(request).pipe(\r\n      tap((event) => {\r\n        if (event instanceof HttpResponse) {\r\n          // Do custom stuff for response if you want.\r\n        }\r\n      })\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Provider POJO for the  Audit Interceptor\r\n */\r\nexport const AuditInterceptorProvider = {\r\n  provide: HTTP_INTERCEPTORS,\r\n  useClass: AuditTrailInterceptor,\r\n  multi: true,\r\n};\r\n"], "mappings": "AACA,SAKEA,iBAAiB,EACjBC,YAAY,QACP,sBAAsB;AAE7B,SAASC,GAAG,QAAQ,gBAAgB;;;;AAGpC;;;AAMA,OAAM,MAAOC,qBAAqB;EAChCC,YAAoBC,YAA+B,EAAWC,kBAAsC;IAAhF,KAAAD,YAAY,GAAZA,YAAY;IAA8B,KAAAC,kBAAkB,GAAlBA,kBAAkB;EAAuB;EAEvGC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAEjB,MAAMC,WAAW,GAAG,IAAI,CAACJ,kBAAkB,CAACK,MAAM,CAAC,aAAa,CAAmB;IACnFH,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC;MACtBC,UAAU,EAAE;QACV,iBAAiB,EAAE,IAAI,CAACR,YAAY,CAACS,YAAY,EAAE;QACnD,eAAe,EAAEJ,WAAW,EAAEK,EAAE,IAAE,EAAE,CAAE;;KAEzC,CAAC;IAEF,OAAON,IAAI,CAACO,MAAM,CAACR,OAAO,CAAC,CAACS,IAAI,CAC9Bf,GAAG,CAAEgB,KAAK,IAAI;MACZ,IAAIA,KAAK,YAAYjB,YAAY,EAAE;QACjC;MAAA;IAEJ,CAAC,CAAC,CACH;EACH;;;uBAtBWE,qBAAqB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;aAArBrB,qBAAqB;MAAAsB,OAAA,EAArBtB,qBAAqB,CAAAuB,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA;;AA2BpB;;;AAGA,OAAO,MAAMC,wBAAwB,GAAG;EACtCC,OAAO,EAAE7B,iBAAiB;EAC1B8B,QAAQ,EAAE3B,qBAAqB;EAC/B4B,KAAK,EAAE;CACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}