{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class FeatureRequestService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AdministrationService';\n    this.submitFeatureRequestByModel = (model, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/feature-request/SubmitFeatureRequest',\n      body: model\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function FeatureRequestService_Factory(t) {\n      return new (t || FeatureRequestService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FeatureRequestService,\n      factory: FeatureRequestService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["FeatureRequestService", "constructor", "restService", "apiName", "submitFeatureRequestByModel", "model", "config", "request", "method", "url", "body", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\admin-service\\lib\\proxy\\bdo\\ess\\administration-service\\controllers\\feature-request.service.ts"], "sourcesContent": ["import { RestService, Rest } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\nimport type { FeatureRequestModel } from '../../../../bdo/rf/net-middleware/application/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FeatureRequestService {\r\n  apiName = 'AdministrationService';\r\n  \r\n\r\n  submitFeatureRequestByModel = (model: FeatureRequestModel, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, boolean>({\r\n      method: 'POST',\r\n      url: '/api/feature-request/SubmitFeatureRequest',\r\n      body: model,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,qBAAqB;EAYhCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAX/B,KAAAC,OAAO,GAAG,uBAAuB;IAGjC,KAAAC,2BAA2B,GAAG,CAACC,KAA0B,EAAEC,MAA6B,KACtF,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAe;MACrCC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,2CAA2C;MAChDC,IAAI,EAAEL;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBAZpCN,qBAAqB,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAArBd,qBAAqB;MAAAe,OAAA,EAArBf,qBAAqB,CAAAgB,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}