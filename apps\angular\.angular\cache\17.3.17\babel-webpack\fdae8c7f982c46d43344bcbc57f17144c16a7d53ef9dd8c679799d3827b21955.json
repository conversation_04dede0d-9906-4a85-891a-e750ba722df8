{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Core, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { ObjAssign, ObjClass, ObjDefineProperty, ObjHasOwnProperty, ObjProto, strShimFunction, strShimObject, strShimPrototype, strShimUndefined, throwTypeError } from \"@microsoft/applicationinsights-shims\";\nimport { _DYN_APPLY, _DYN_CALL, _DYN_INDEX_OF, _DYN_LENGTH, _DYN_NAME, _DYN_PUSH, _DYN_REPLACE } from \"../__DynamicConstants\";\nimport { STR_EMPTY } from \"./InternalConstants\";\n// RESTRICT and AVOID circular dependencies you should not import other contained modules or export the contents of this file directly\n// Added to help with minfication\nvar strToISOString = \"toISOString\";\nvar cStrEndsWith = \"endsWith\";\nvar cStrStartsWith = \"startsWith\";\nvar strIndexOf = \"indexOf\";\nvar strMap = \"map\";\nvar strReduce = \"reduce\";\nvar cStrTrim = \"trim\";\nvar strToString = \"toString\";\n/**\r\n * Constant string defined to support minimization\r\n * @ignore\r\n */\nvar str__Proto = \"__proto__\";\n/**\r\n  * Constant string defined to support minimization\r\n  * @ignore\r\n  */\nvar strConstructor = \"constructor\";\nvar _objDefineProperty = ObjDefineProperty;\nvar _objFreeze = ObjClass.freeze;\nvar _objSeal = ObjClass.seal;\nvar _objKeys = ObjClass.keys;\nvar StringProto = String[strShimPrototype];\nvar _strTrim = StringProto[cStrTrim];\nvar _strEndsWith = StringProto[cStrEndsWith];\nvar _strStartsWith = StringProto[cStrStartsWith];\nvar DateProto = Date[strShimPrototype];\nvar _dataToISOString = DateProto[strToISOString];\nvar _isArray = Array.isArray;\nvar _objToString = ObjProto[strToString];\nvar _fnToString = ObjHasOwnProperty[strToString];\n// Cache what this browser reports as the object function constructor (as a string)\nvar _objFunctionString = _fnToString[_DYN_CALL /* @min:%2ecall */](ObjClass);\nvar rCamelCase = /-([a-z])/g;\nvar rNormalizeInvalid = /([^\\w\\d_$])/g;\nvar rLeadingNumeric = /^(\\d+[\\w\\d_$])/;\n/**\r\n * Pre-lookup to check if we are running on a modern browser (i.e. not IE8)\r\n * @ignore\r\n */\nvar _objGetPrototypeOf = Object[\"getPrototypeOf\"];\n/**\r\n  * Helper used to get the prototype of the target object as getPrototypeOf is not available in an ES3 environment.\r\n  * @ignore\r\n  */\nexport function _getObjProto(target) {\n  if (target) {\n    // This method doesn't existing in older browsers (e.g. IE8)\n    if (_objGetPrototypeOf) {\n      return _objGetPrototypeOf(target);\n    }\n    // target[Constructor] May break if the constructor has been changed or removed\n    var newProto = target[str__Proto] || target[strShimPrototype] || target[strConstructor];\n    if (newProto) {\n      return newProto;\n    }\n  }\n  return null;\n}\nexport function objToString(obj) {\n  return _objToString[_DYN_CALL /* @min:%2ecall */](obj);\n}\nexport function isTypeof(value, theType) {\n  return typeof value === theType;\n}\nexport function isUndefined(value) {\n  return value === undefined || typeof value === strShimUndefined;\n}\nexport function isNotUndefined(value) {\n  return !isUndefined(value);\n}\nexport function isNullOrUndefined(value) {\n  return value === null || isUndefined(value);\n}\nexport function isNotNullOrUndefined(value) {\n  return !isNullOrUndefined(value);\n}\nexport function hasOwnProperty(obj, prop) {\n  return !!(obj && ObjHasOwnProperty[_DYN_CALL /* @min:%2ecall */](obj, prop));\n}\nexport function isObject(value) {\n  // Changing to inline for performance\n  return !!(value && typeof value === strShimObject);\n}\nexport function isFunction(value) {\n  // Changing to inline for performance\n  return !!(value && typeof value === strShimFunction);\n}\nexport function isPromiseLike(value) {\n  return value && isFunction(value.then);\n}\n/**\r\n * Validates that the string name conforms to the JS IdentifierName specification and if not\r\n * normalizes the name so that it would. This method does not identify or change any keywords\r\n * meaning that if you pass in a known keyword the same value will be returned.\r\n * This is a simplified version\r\n * @param name The name to validate\r\n */\nexport function normalizeJsName(name) {\n  var value = name;\n  if (value && isString(value)) {\n    // CamelCase everything after the \"-\" and remove the dash\n    value = value[_DYN_REPLACE /* @min:%2ereplace */](rCamelCase, function (_all, letter) {\n      return letter.toUpperCase();\n    });\n    value = value[_DYN_REPLACE /* @min:%2ereplace */](rNormalizeInvalid, \"_\");\n    value = value[_DYN_REPLACE /* @min:%2ereplace */](rLeadingNumeric, function (_all, match) {\n      return \"_\" + match;\n    });\n  }\n  return value;\n}\n/**\r\n * This is a helper function for the equivalent of arForEach(objKeys(target), callbackFn), this is a\r\n * performance optimization to avoid the creation of a new array for large objects\r\n * @param target The target object to find and process the keys\r\n * @param callbackfn The function to call with the details\r\n */\nexport function objForEachKey(target, callbackfn) {\n  if (target) {\n    for (var prop in target) {\n      if (ObjHasOwnProperty[_DYN_CALL /* @min:%2ecall */](target, prop)) {\n        callbackfn[_DYN_CALL /* @min:%2ecall */](target, prop, target[prop]);\n      }\n    }\n  }\n}\n/**\r\n * The strEndsWith() method determines whether a string ends with the characters of a specified string, returning true or false as appropriate.\r\n * @param value - The value to check whether it ends with the search value.\r\n * @param search - The characters to be searched for at the end of the value.\r\n * @returns true if the given search value is found at the end of the string, otherwise false.\r\n */\nexport function strEndsWith(value, search) {\n  var result = false;\n  if (value && search && !(result = value === search)) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    result = _strEndsWith ? value[cStrEndsWith](search) : _strEndsWithPoly(value, search);\n  }\n  return result;\n}\n/**\r\n * The _strEndsWith() method determines whether a string ends with the characters of a specified string, returning true or false as appropriate.\r\n * @param value - The value to check whether it ends with the search value.\r\n * @param search - The characters to be searched for at the end of the value.\r\n * @returns true if the given search value is found at the end of the string, otherwise false.\r\n */\nexport function _strEndsWithPoly(value, search) {\n  var result = false;\n  var searchLen = search ? search[_DYN_LENGTH /* @min:%2elength */] : 0;\n  var valLen = value ? value[_DYN_LENGTH /* @min:%2elength */] : 0;\n  if (searchLen && valLen && valLen >= searchLen && !(result = value === search)) {\n    var pos = valLen - 1;\n    for (var lp = searchLen - 1; lp >= 0; lp--) {\n      if (value[pos] != search[lp]) {\n        return false;\n      }\n      pos--;\n    }\n    result = true;\n  }\n  return result;\n}\n/**\r\n * The strStartsWith() method determines whether a string starts with the characters of the specified string, returning true or false as appropriate.\r\n * @param value - The value to check whether it ends with the search value.\r\n * @param checkValue - The characters to be searched for at the start of the value.\r\n * @returns true if the given search value is found at the start of the string, otherwise false.\r\n */\nexport function strStartsWith(value, checkValue) {\n  var result = false;\n  if (value && checkValue && !(result = value === checkValue)) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    result = _strStartsWith ? value[cStrStartsWith](checkValue) : _strStartsWithPoly(value, checkValue);\n  }\n  return result;\n}\n/**\r\n * The strStartsWith() method determines whether a string starts with the characters of the specified string, returning true or false as appropriate.\r\n * @param value - The value to check whether it ends with the search value.\r\n * @param checkValue - The characters to be searched for at the start of the value.\r\n * @returns true if the given search value is found at the start of the string, otherwise false.\r\n */\nexport function _strStartsWithPoly(value, checkValue) {\n  // Using helper for performance and because string startsWith() is not available on IE\n  var result = false;\n  var chkLen = checkValue ? checkValue[_DYN_LENGTH /* @min:%2elength */] : 0;\n  if (value && chkLen && value[_DYN_LENGTH /* @min:%2elength */] >= chkLen && !(result = value === checkValue)) {\n    for (var lp = 0; lp < chkLen; lp++) {\n      if (value[lp] !== checkValue[lp]) {\n        return false;\n      }\n    }\n    result = true;\n  }\n  return result;\n}\n/**\r\n * A simple wrapper (for minification support) to check if the value contains the search string.\r\n * @param value - The string value to check for the existence of the search value\r\n * @param search - The value search within the value\r\n */\nexport function strContains(value, search) {\n  if (value && search) {\n    return value[_DYN_INDEX_OF /* @min:%2eindexOf */](search) !== -1;\n  }\n  return false;\n}\n/**\r\n * Check if an object is of type Date\r\n */\nexport function isDate(obj) {\n  return !!(obj && _objToString[_DYN_CALL /* @min:%2ecall */](obj) === \"[object Date]\");\n}\n/**\r\n * Check if an object is of type Array with optional generic T, the generic type is not validated\r\n * and exists to help with TypeScript validation only.\r\n */\nexport var isArray = _isArray || _isArrayPoly;\nfunction _isArrayPoly(obj) {\n  return !!(obj && _objToString[_DYN_CALL /* @min:%2ecall */](obj) === \"[object Array]\");\n}\n/**\r\n * Check if an object is of type Error\r\n */\nexport function isError(obj) {\n  return !!(obj && _objToString[_DYN_CALL /* @min:%2ecall */](obj) === \"[object Error]\");\n}\n/**\r\n * Checks if the type of value is a string.\r\n * @param {any} value - Value to be checked.\r\n * @return {boolean} True if the value is a string, false otherwise.\r\n */\nexport function isString(value) {\n  // Changing to inline for performance\n  return typeof value === \"string\";\n}\n/**\r\n * Checks if the type of value is a number.\r\n * @param {any} value - Value to be checked.\r\n * @return {boolean} True if the value is a number, false otherwise.\r\n */\nexport function isNumber(value) {\n  // Changing to inline for performance\n  return typeof value === \"number\";\n}\n/**\r\n * Checks if the type of value is a boolean.\r\n * @param {any} value - Value to be checked.\r\n * @return {boolean} True if the value is a boolean, false otherwise.\r\n */\nexport function isBoolean(value) {\n  // Changing to inline for performance\n  return typeof value === \"boolean\";\n}\n/**\r\n * Checks if the type of value is a Symbol.\r\n * This only returns a boolean as returning value is Symbol will cause issues for older TypeScript consumers\r\n * @param {any} value - Value to be checked.\r\n * @return {boolean} True if the value is a Symbol, false otherwise.\r\n */\nexport function isSymbol(value) {\n  return typeof value === \"symbol\";\n}\n/**\r\n * Checks if the type of the value is a normal plain object (not a null or data)\r\n * @param value\r\n */\nexport function isPlainObject(value) {\n  var result = false;\n  if (value && typeof value === \"object\") {\n    // Inlining _objGetPrototypeOf for performance to avoid an additional function call\n    var proto = _objGetPrototypeOf ? _objGetPrototypeOf(value) : _getObjProto(value);\n    if (!proto) {\n      // No prototype found so this is a plain Object eg. 'Object.create(null)'\n      result = true;\n    } else {\n      // Objects that have a prototype are plain only if they were created using the Object global (native) function\n      if (proto[strConstructor] && ObjHasOwnProperty[_DYN_CALL /* @min:%2ecall */](proto, strConstructor)) {\n        proto = proto[strConstructor];\n      }\n      result = typeof proto === strShimFunction && _fnToString[_DYN_CALL /* @min:%2ecall */](proto) === _objFunctionString;\n    }\n  }\n  return result;\n}\n/**\r\n * Convert a date to I.S.O. format in IE8\r\n */\nexport function toISOString(date) {\n  if (date) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    return _dataToISOString ? date[strToISOString]() : _toISOStringPoly(date);\n  }\n}\n/**\r\n * Convert a date to I.S.O. format in IE8\r\n */\nexport function _toISOStringPoly(date) {\n  if (date && date.getUTCFullYear) {\n    var pad = function (num) {\n      var r = String(num);\n      if (r[_DYN_LENGTH /* @min:%2elength */] === 1) {\n        r = \"0\" + r;\n      }\n      return r;\n    };\n    return date.getUTCFullYear() + \"-\" + pad(date.getUTCMonth() + 1) + \"-\" + pad(date.getUTCDate()) + \"T\" + pad(date.getUTCHours()) + \":\" + pad(date.getUTCMinutes()) + \":\" + pad(date.getUTCSeconds()) + \".\" + String((date.getUTCMilliseconds() / 1000).toFixed(3)).slice(2, 5) + \"Z\";\n  }\n}\n/**\r\n * Performs the specified action for each element in an array. This helper exists to avoid adding a polyfil for older browsers\r\n * that do not define Array.prototype.xxxx (eg. ES3 only, IE8) just in case any page checks for presence/absence of the prototype\r\n * implementation. Note: For consistency this will not use the Array.prototype.xxxx implementation if it exists as this would\r\n * cause a testing requirement to test with and without the implementations\r\n * @param callbackfn  A function that accepts up to three arguments. forEach calls the callbackfn function one time for each element in the array. It can return -1 to break out of the loop\r\n * @param thisArg  [Optional] An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.\r\n */\nexport function arrForEach(arr, callbackfn, thisArg) {\n  var len = arr[_DYN_LENGTH /* @min:%2elength */];\n  try {\n    for (var idx = 0; idx < len; idx++) {\n      if (idx in arr) {\n        if (callbackfn[_DYN_CALL /* @min:%2ecall */](thisArg || arr, arr[idx], idx, arr) === -1) {\n          break;\n        }\n      }\n    }\n  } catch (e) {\n    // This can happen with some native browser objects, but should not happen for the type we are checking for\n  }\n}\n/**\r\n * Returns the index of the first occurrence of a value in an array. This helper exists to avoid adding a polyfil for older browsers\r\n * that do not define Array.prototype.xxxx (eg. ES3 only, IE8) just in case any page checks for presence/absence of the prototype\r\n * implementation. Note: For consistency this will not use the Array.prototype.xxxx implementation if it exists as this would\r\n * cause a testing requirement to test with and without the implementations\r\n * @param searchElement The value to locate in the array.\r\n * @param fromIndex The array index at which to begin the search. If fromIndex is omitted, the search starts at index 0.\r\n */\nexport function arrIndexOf(arr, searchElement, fromIndex) {\n  if (arr) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    if (arr[strIndexOf]) {\n      return arr[strIndexOf](searchElement, fromIndex);\n    }\n    var len = arr[_DYN_LENGTH /* @min:%2elength */];\n    var from = fromIndex || 0;\n    try {\n      for (var lp = Math.max(from >= 0 ? from : len - Math.abs(from), 0); lp < len; lp++) {\n        if (lp in arr && arr[lp] === searchElement) {\n          return lp;\n        }\n      }\n    } catch (e) {\n      // This can happen with some native browser objects, but should not happen for the type we are checking for\n    }\n  }\n  return -1;\n}\n/**\r\n * Calls a defined callback function on each element of an array, and returns an array that contains the results. This helper exists\r\n * to avoid adding a polyfil for older browsers that do not define Array.prototype.xxxx (eg. ES3 only, IE8) just in case any page\r\n * checks for presence/absence of the prototype implementation. Note: For consistency this will not use the Array.prototype.xxxx\r\n * implementation if it exists as this would cause a testing requirement to test with and without the implementations\r\n * @param callbackfn A function that accepts up to three arguments. The map method calls the callbackfn function one time for each element in the array.\r\n * @param thisArg An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.\r\n */\nexport function arrMap(arr, callbackfn, thisArg) {\n  var results;\n  if (arr) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    if (arr[strMap]) {\n      return arr[strMap](callbackfn, thisArg);\n    }\n    var len = arr[_DYN_LENGTH /* @min:%2elength */];\n    var _this = thisArg || arr;\n    results = new Array(len);\n    try {\n      for (var lp = 0; lp < len; lp++) {\n        if (lp in arr) {\n          results[lp] = callbackfn[_DYN_CALL /* @min:%2ecall */](_this, arr[lp], arr);\n        }\n      }\n    } catch (e) {\n      // This can happen with some native browser objects, but should not happen for the type we are checking for\n    }\n  }\n  return results;\n}\n/**\r\n * Calls the specified callback function for all the elements in an array. The return value of the callback function is the accumulated result, and is\r\n * provided as an argument in the next call to the callback function. This helper exists to avoid adding a polyfil for older browsers that do not define\r\n * Array.prototype.xxxx (eg. ES3 only, IE8) just in case any page checks for presence/absence of the prototype implementation. Note: For consistency\r\n * this will not use the Array.prototype.xxxx implementation if it exists as this would cause a testing requirement to test with and without the implementations\r\n * @param callbackfn A function that accepts up to four arguments. The reduce method calls the callbackfn function one time for each element in the array.\r\n * @param initialValue If initialValue is specified, it is used as the initial value to start the accumulation. The first call to the callbackfn function provides this value as an argument instead of an array value.\r\n */\nexport function arrReduce(arr, callbackfn, initialValue) {\n  var value;\n  if (arr) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    if (arr[strReduce]) {\n      return arr[strReduce](callbackfn, initialValue);\n    }\n    var len = arr[_DYN_LENGTH /* @min:%2elength */];\n    var lp = 0;\n    // Specifically checking the number of passed arguments as the value could be anything\n    if (arguments[_DYN_LENGTH /* @min:%2elength */] >= 3) {\n      value = arguments[2];\n    } else {\n      while (lp < len && !(lp in arr)) {\n        lp++;\n      }\n      value = arr[lp++];\n    }\n    while (lp < len) {\n      if (lp in arr) {\n        value = callbackfn(value, arr[lp], lp, arr);\n      }\n      lp++;\n    }\n  }\n  return value;\n}\n/**\r\n * helper method to trim strings (IE8 does not implement String.prototype.trim)\r\n */\nexport function strTrim(str) {\n  if (str) {\n    // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n    str = _strTrim && str[cStrTrim] ? str[cStrTrim]() : str[_DYN_REPLACE /* @min:%2ereplace */] ? str[_DYN_REPLACE /* @min:%2ereplace */](/^\\s+|(?=\\s)\\s+$/g, STR_EMPTY) : str;\n  }\n  return str;\n}\nvar _objKeysHasDontEnumBug = !{\n  toString: null\n}.propertyIsEnumerable(\"toString\");\nvar _objKeysDontEnums = [\"toString\", \"toLocaleString\", \"valueOf\", \"hasOwnProperty\", \"isPrototypeOf\", \"propertyIsEnumerable\", \"constructor\"];\n/**\r\n * Returns the names of the enumerable string properties and methods of an object. This helper exists to avoid adding a polyfil for older browsers\r\n * that do not define Object.keys eg. ES3 only, IE8 just in case any page checks for presence/absence of the prototype implementation.\r\n * Note: For consistency this will not use the Object.keys implementation if it exists as this would cause a testing requirement to test with and without the implementations\r\n * @param obj Object that contains the properties and methods. This can be an object that you created or an existing Document Object Model (DOM) object.\r\n */\nexport function objKeys(obj) {\n  var objType = typeof obj;\n  if (objType !== strShimFunction && (objType !== strShimObject || obj === null)) {\n    throwTypeError(\"objKeys called on non-object\");\n  }\n  // For Performance try and use the native instance, using string lookup of the function to easily pass the ES3 build checks and minification\n  if (!_objKeysHasDontEnumBug && _objKeys) {\n    return _objKeys(obj);\n  }\n  var result = [];\n  for (var prop in obj) {\n    if (obj && ObjHasOwnProperty[_DYN_CALL /* @min:%2ecall */](obj, prop)) {\n      result[_DYN_PUSH /* @min:%2epush */](prop);\n    }\n  }\n  if (_objKeysHasDontEnumBug) {\n    var dontEnumsLength = _objKeysDontEnums[_DYN_LENGTH /* @min:%2elength */];\n    for (var lp = 0; lp < dontEnumsLength; lp++) {\n      if (obj && ObjHasOwnProperty[_DYN_CALL /* @min:%2ecall */](obj, _objKeysDontEnums[lp])) {\n        result[_DYN_PUSH /* @min:%2epush */](_objKeysDontEnums[lp]);\n      }\n    }\n  }\n  return result;\n}\n/**\r\n * Try to define get/set object property accessors for the target object/prototype, this will provide compatibility with\r\n * existing API definition when run within an ES5+ container that supports accessors but still enable the code to be loaded\r\n * and executed in an ES3 container, providing basic IE8 compatibility.\r\n * @param target The object on which to define the property.\r\n * @param prop The name of the property to be defined or modified.\r\n * @param getProp The getter function to wire against the getter.\r\n * @param setProp The setter function to wire against the setter.\r\n * @returns True if it was able to create the accessors otherwise false\r\n */\nexport function objDefineAccessors(target, prop, getProp, setProp) {\n  if (_objDefineProperty) {\n    try {\n      var descriptor = {\n        enumerable: true,\n        configurable: true\n      };\n      if (getProp) {\n        descriptor.get = getProp;\n      }\n      if (setProp) {\n        descriptor.set = setProp;\n      }\n      _objDefineProperty(target, prop, descriptor);\n      return true;\n    } catch (e) {\n      // IE8 Defines a defineProperty on Object but it's only supported for DOM elements so it will throw\n      // We will just ignore this here.\n    }\n  }\n  return false;\n}\nfunction _doNothing(value) {\n  return value;\n}\nexport function deepFreeze(obj) {\n  if (_objFreeze) {\n    objForEachKey(obj, function (name, value) {\n      if (isArray(value) || isObject(value)) {\n        _objFreeze(value);\n      }\n    });\n  }\n  return objFreeze(obj);\n}\nexport var objFreeze = _objFreeze || _doNothing;\nexport var objSeal = _objSeal || _doNothing;\n/**\r\n * Return the current time via the Date now() function (if available) and falls back to (new Date()).getTime() if now() is unavailable (IE8 or less)\r\n * https://caniuse.com/#search=Date.now\r\n */\nexport function dateNow() {\n  var dt = Date;\n  return dt.now ? dt.now() : new dt().getTime();\n}\n/**\r\n * Returns the name of object if it's an Error. Otherwise, returns empty string.\r\n */\nexport function getExceptionName(object) {\n  if (isError(object)) {\n    return object[_DYN_NAME /* @min:%2ename */];\n  }\n  return STR_EMPTY;\n}\n/**\r\n * Sets the provided value on the target instance using the field name when the provided chk function returns true, the chk\r\n * function will only be called if the new value is no equal to the original value.\r\n * @param target - The target object\r\n * @param field - The key of the target\r\n * @param value - The value to set\r\n * @param valChk - [Optional] Callback to check the value that if supplied will be called check if the new value can be set\r\n * @param srcChk - [Optional] Callback to check to original value that if supplied will be called if the new value should be set (if allowed)\r\n * @returns The existing or new value, depending what was set\r\n */\nexport function setValue(target, field, value, valChk, srcChk) {\n  var theValue = value;\n  if (target) {\n    theValue = target[field];\n    if (theValue !== value && (!srcChk || srcChk(theValue)) && (!valChk || valChk(value))) {\n      theValue = value;\n      target[field] = theValue;\n    }\n  }\n  return theValue;\n}\n/**\r\n * Returns the current value from the target object if not null or undefined otherwise sets the new value and returns it\r\n * @param target - The target object to return or set the default value\r\n * @param field - The key for the field to set on the target\r\n * @param defValue - [Optional] The value to set if not already present, when not provided a empty object will be added\r\n */\nexport function getSetValue(target, field, defValue) {\n  var theValue;\n  if (target) {\n    theValue = target[field];\n    if (!theValue && isNullOrUndefined(theValue)) {\n      // Supports having the default as null\n      theValue = !isUndefined(defValue) ? defValue : {};\n      target[field] = theValue;\n    }\n  } else {\n    // Expanded for performance so we only check defValue if required\n    theValue = !isUndefined(defValue) ? defValue : {};\n  }\n  return theValue;\n}\n/**\r\n * Get the mapped config value, if null or undefined any supplied defaultValue will be returned.\r\n * @param field - The name of the field as the named enum value (number) or the string name.\r\n * @param defaultValue - The default value to return if the config field is not present, null or undefined.\r\n */\nexport function getCfgValue(theValue, defaultValue) {\n  return !isNullOrUndefined(theValue) ? theValue : defaultValue;\n}\nexport function isNotTruthy(value) {\n  return !value;\n}\nexport function isTruthy(value) {\n  return !!value;\n}\nexport function throwError(message) {\n  throw new Error(message);\n}\nfunction _createProxyFunction(source, funcName) {\n  var srcFunc = null;\n  var src = null;\n  if (isFunction(source)) {\n    srcFunc = source;\n  } else {\n    src = source;\n  }\n  return function () {\n    // Capture the original arguments passed to the method\n    var originalArguments = arguments;\n    if (srcFunc) {\n      src = srcFunc();\n    }\n    if (src) {\n      return src[funcName][_DYN_APPLY /* @min:%2eapply */](src, originalArguments);\n    }\n  };\n}\n/**\r\n * Effectively assigns all enumerable properties (not just own properties) and functions (including inherited prototype) from\r\n * the source object to the target, it attempts to use proxy getters / setters (if possible) and proxy functions to avoid potential\r\n * implementation issues by assigning prototype functions as instance ones\r\n *\r\n * This method is the primary method used to \"update\" the snippet proxy with the ultimate implementations.\r\n *\r\n * Special ES3 Notes:\r\n * Updates (setting) of direct property values on the target or indirectly on the source object WILL NOT WORK PROPERLY, updates to the\r\n * properties of \"referenced\" object will work (target.context.newValue = 10 => will be reflected in the source.context as it's the\r\n * same object). ES3 Failures: assigning target.myProp = 3 -> Won't change source.myProp = 3, likewise the reverse would also fail.\r\n * @param target - The target object to be assigned with the source properties and functions\r\n * @param source - The source object which will be assigned / called by setting / calling the targets proxies\r\n * @param chkSet - An optional callback to determine whether a specific property/function should be proxied\r\n */\nexport function proxyAssign(target, source, chkSet) {\n  if (target && source && isObject(target) && isObject(source)) {\n    var _loop_1 = function (field) {\n      if (isString(field)) {\n        var value = source[field];\n        if (isFunction(value)) {\n          if (!chkSet || chkSet(field, true, source, target)) {\n            // Create a proxy function rather than just copying the (possible) prototype to the new object as an instance function\n            target[field] = _createProxyFunction(source, field);\n          }\n        } else if (!chkSet || chkSet(field, false, source, target)) {\n          if (hasOwnProperty(target, field)) {\n            // Remove any previous instance property\n            delete target[field];\n          }\n          if (!objDefineAccessors(target, field, function () {\n            return source[field];\n          }, function (theValue) {\n            source[field] = theValue;\n          })) {\n            // Unable to create an accessor, so just assign the values as a fallback\n            // -- this will (mostly) work for objects\n            // -- but will fail for accessing primitives (if the source changes it) and all types of \"setters\" as the source won't be modified\n            target[field] = value;\n          }\n        }\n      }\n    };\n    // effectively apply/proxy full source to the target instance\n    for (var field in source) {\n      _loop_1(field);\n    }\n  }\n  return target;\n}\n/**\r\n * Creates a proxy function on the target which internally will call the source version with all arguments passed to the target method.\r\n *\r\n * @param target - The target object to be assigned with the source properties and functions\r\n * @param name - The function name that will be added on the target\r\n * @param source - The source object which will be assigned / called by setting / calling the targets proxies\r\n * @param theFunc - The function name on the source that will be proxied on the target\r\n * @param overwriteTarget - If `false` this will not replace any pre-existing name otherwise (the default) it will overwrite any existing name\r\n */\nexport function proxyFunctionAs(target, name, source, theFunc, overwriteTarget) {\n  if (target && name && source) {\n    if (overwriteTarget !== false || isUndefined(target[name])) {\n      target[name] = _createProxyFunction(source, theFunc);\n    }\n  }\n}\n/**\r\n * Creates proxy functions on the target which internally will call the source version with all arguments passed to the target method.\r\n *\r\n * @param target - The target object to be assigned with the source properties and functions\r\n * @param source - The source object which will be assigned / called by setting / calling the targets proxies\r\n * @param functionsToProxy - An array of function names that will be proxied on the target\r\n * @param overwriteTarget - If false this will not replace any pre-existing name otherwise (the default) it will overwrite any existing name\r\n */\nexport function proxyFunctions(target, source, functionsToProxy, overwriteTarget) {\n  if (target && source && isObject(target) && isArray(functionsToProxy)) {\n    arrForEach(functionsToProxy, function (theFuncName) {\n      if (isString(theFuncName)) {\n        proxyFunctionAs(target, theFuncName, source, theFuncName, overwriteTarget);\n      }\n    });\n  }\n  return target;\n}\n/**\r\n * Simpler helper to create a dynamic class that implements the interface and populates the values with the defaults.\r\n * Only instance properties (hasOwnProperty) values are copied from the defaults to the new instance\r\n * @param defaults Simple helper\r\n */\nexport function createClassFromInterface(defaults) {\n  return /** @class */function () {\n    function class_1() {\n      var _this_1 = this;\n      if (defaults) {\n        objForEachKey(defaults, function (field, value) {\n          _this_1[field] = value;\n        });\n      }\n    }\n    return class_1;\n  }();\n}\n/**\r\n * A helper function to assist with JIT performance for objects that have properties added / removed dynamically\r\n * this is primarily for chromium based browsers and has limited effects on Firefox and none of IE. Only call this\r\n * function after you have finished \"updating\" the object, calling this within loops reduces or defeats the benefits.\r\n * This helps when iterating using for..in, objKeys() and objForEach()\r\n * @param theObject - The object to be optimized if possible\r\n */\nexport function optimizeObject(theObject) {\n  // V8 Optimization to cause the JIT compiler to create a new optimized object for looking up the own properties\n  // primarily for object with <= 19 properties for >= 20 the effect is reduced or non-existent\n  if (theObject && ObjAssign) {\n    theObject = ObjClass(ObjAssign({}, theObject));\n  }\n  return theObject;\n}\nexport function objExtend(obj1, obj2, obj3, obj4, obj5, obj6) {\n  // Variables\n  var theArgs = arguments;\n  var extended = theArgs[0] || {};\n  var argLen = theArgs[_DYN_LENGTH /* @min:%2elength */];\n  var deep = false;\n  var idx = 1;\n  // Check for \"Deep\" flag\n  if (argLen > 0 && isBoolean(extended)) {\n    deep = extended;\n    extended = theArgs[idx] || {};\n    idx++;\n  }\n  // Handle case when target is a string or something (possible in deep copy)\n  if (!isObject(extended)) {\n    extended = {};\n  }\n  // Loop through each remaining object and conduct a merge\n  for (; idx < argLen; idx++) {\n    var arg = theArgs[idx];\n    var isArgArray = isArray(arg);\n    var isArgObj = isObject(arg);\n    for (var prop in arg) {\n      var propOk = isArgArray && prop in arg || isArgObj && ObjHasOwnProperty[_DYN_CALL /* @min:%2ecall */](arg, prop);\n      if (!propOk) {\n        continue;\n      }\n      var newValue = arg[prop];\n      var isNewArray = void 0;\n      // If deep merge and property is an object, merge properties\n      if (deep && newValue && ((isNewArray = isArray(newValue)) || isPlainObject(newValue))) {\n        // Grab the current value of the extended object\n        var clone = extended[prop];\n        if (isNewArray) {\n          if (!isArray(clone)) {\n            // We can't \"merge\" an array with a non-array so overwrite the original\n            clone = [];\n          }\n        } else if (!isPlainObject(clone)) {\n          // We can't \"merge\" an object with a non-object\n          clone = {};\n        }\n        // Never move the original objects always clone them\n        newValue = objExtend(deep, clone, newValue);\n      }\n      // Assign the new (or previous) value (unless undefined)\n      if (newValue !== undefined) {\n        extended[prop] = newValue;\n      }\n    }\n  }\n  return extended;\n}\n//# sourceMappingURL=HelperFuncs.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}