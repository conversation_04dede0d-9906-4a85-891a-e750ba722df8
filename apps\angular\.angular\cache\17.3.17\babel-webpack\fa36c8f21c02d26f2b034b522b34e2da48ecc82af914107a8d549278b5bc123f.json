{"ast": null, "code": "import { EntityProp } from '@abp/ng.components/extensible';\nimport { of } from 'rxjs';\nimport { ClaimsComponent } from '../../components/claims/claims.component';\nexport const DEFAULT_CLAIMS_ENTITY_PROPS = EntityProp.createMany([{\n  type: \"string\" /* ePropType.String */,\n  name: 'name',\n  displayName: 'AbpIdentity::Name',\n  sortable: true,\n  columnWidth: 250\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'valueType',\n  displayName: 'AbpIdentity::ValueType',\n  sortable: true,\n  columnWidth: 200,\n  valueResolver: data => of(data.getInjected(ClaimsComponent).getTypeName(data.record.valueType))\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'description',\n  displayName: 'AbpIdentity::Description',\n  sortable: true,\n  columnWidth: 250\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'regex',\n  displayName: 'AbpIdentity::Regex',\n  sortable: true,\n  columnWidth: 200\n}, {\n  type: \"boolean\" /* ePropType.Boolean */,\n  name: 'required',\n  displayName: 'AbpIdentity::Required',\n  sortable: true,\n  columnWidth: 150\n}, {\n  type: \"boolean\" /* ePropType.Boolean */,\n  name: 'isStatic',\n  displayName: 'AbpIdentity::IsStatic',\n  sortable: true,\n  columnWidth: 150\n}]);", "map": {"version": 3, "names": ["EntityProp", "of", "ClaimsComponent", "DEFAULT_CLAIMS_ENTITY_PROPS", "createMany", "type", "name", "displayName", "sortable", "columnWidth", "valueResolver", "data", "getInjected", "getTypeName", "record", "valueType"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\defaults\\claims\\default-claims-entity-props.ts"], "sourcesContent": ["import { EntityProp, ePropType } from '@abp/ng.components/extensible';\r\nimport { ClaimTypeDto } from '@volo/abp.ng.identity/proxy';\r\nimport { of } from 'rxjs';\r\nimport { ClaimsComponent } from '../../components/claims/claims.component';\r\n\r\nexport const DEFAULT_CLAIMS_ENTITY_PROPS = EntityProp.createMany<ClaimTypeDto>([\r\n  {\r\n    type: ePropType.String,\r\n    name: 'name',\r\n    displayName: 'AbpIdentity::Name',\r\n    sortable: true,\r\n    columnWidth: 250,\r\n  },\r\n  {\r\n    type: ePropType.String,\r\n    name: 'valueType',\r\n    displayName: 'AbpIdentity::ValueType',\r\n    sortable: true,\r\n    columnWidth: 200,\r\n    valueResolver: data => of(data.getInjected(ClaimsComponent).getTypeName(data.record.valueType)),\r\n  },\r\n  {\r\n    type: ePropType.String,\r\n    name: 'description',\r\n    displayName: 'AbpIdentity::Description',\r\n    sortable: true,\r\n    columnWidth: 250,\r\n  },\r\n  {\r\n    type: ePropType.String,\r\n    name: 'regex',\r\n    displayName: 'AbpIdentity::Regex',\r\n    sortable: true,\r\n    columnWidth: 200,\r\n  },\r\n  {\r\n    type: ePropType.Boolean,\r\n    name: 'required',\r\n    displayName: 'AbpIdentity::Required',\r\n    sortable: true,\r\n    columnWidth: 150,\r\n  },\r\n  {\r\n    type: ePropType.Boolean,\r\n    name: 'isStatic',\r\n    displayName: 'AbpIdentity::IsStatic',\r\n    sortable: true,\r\n    columnWidth: 150,\r\n  },\r\n]);\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAmB,+BAA+B;AAErE,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,eAAe,QAAQ,0CAA0C;AAE1E,OAAO,MAAMC,2BAA2B,GAAGH,UAAU,CAACI,UAAU,CAAe,CAC7E;EACEC,IAAI;EACJC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE;CACd,EACD;EACEJ,IAAI;EACJC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,wBAAwB;EACrCC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,GAAG;EAChBC,aAAa,EAAEC,IAAI,IAAIV,EAAE,CAACU,IAAI,CAACC,WAAW,CAACV,eAAe,CAAC,CAACW,WAAW,CAACF,IAAI,CAACG,MAAM,CAACC,SAAS,CAAC;CAC/F,EACD;EACEV,IAAI;EACJC,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE;CACd,EACD;EACEJ,IAAI;EACJC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE;CACd,EACD;EACEJ,IAAI;EACJC,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE;CACd,EACD;EACEJ,IAAI;EACJC,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE;CACd,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}