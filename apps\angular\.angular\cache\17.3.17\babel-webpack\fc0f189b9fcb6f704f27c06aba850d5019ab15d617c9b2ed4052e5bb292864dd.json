{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\saas-service\\lib\\proxy\\bdo\\ess\\saas-service\\compliance-emails\\models.ts"], "sourcesContent": ["\r\nexport interface ComplianceEmailDto {\r\n  emailAddress?: string;\r\n  sequence: number;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}