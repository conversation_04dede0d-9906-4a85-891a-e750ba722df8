{"ast": null, "code": "import compare from 'just-compare';\n\n/* tslint:disable:no-non-null-assertion */\nclass ListNode {\n  constructor(value) {\n    this.value = value;\n  }\n}\nclass LinkedList {\n  constructor() {\n    this.size = 0;\n  }\n  get head() {\n    return this.first;\n  }\n  get tail() {\n    return this.last;\n  }\n  get length() {\n    return this.size;\n  }\n  attach(value, previousNode, nextNode) {\n    if (!previousNode) return this.addHead(value);\n    if (!nextNode) return this.addTail(value);\n    const node = new ListNode(value);\n    node.previous = previousNode;\n    previousNode.next = node;\n    node.next = nextNode;\n    nextNode.previous = node;\n    this.size++;\n    return node;\n  }\n  attachMany(values, previousNode, nextNode) {\n    if (!values.length) return [];\n    if (!previousNode) return this.addManyHead(values);\n    if (!nextNode) return this.addManyTail(values);\n    const list = new LinkedList();\n    list.addManyTail(values);\n    list.first.previous = previousNode;\n    previousNode.next = list.first;\n    list.last.next = nextNode;\n    nextNode.previous = list.last;\n    this.size += values.length;\n    return list.toNodeArray();\n  }\n  detach(node) {\n    if (!node.previous) return this.dropHead();\n    if (!node.next) return this.dropTail();\n    node.previous.next = node.next;\n    node.next.previous = node.previous;\n    this.size--;\n    return node;\n  }\n  add(value) {\n    return {\n      after: (...params) => this.addAfter.call(this, value, ...params),\n      before: (...params) => this.addBefore.call(this, value, ...params),\n      byIndex: position => this.addByIndex(value, position),\n      head: () => this.addHead(value),\n      tail: () => this.addTail(value)\n    };\n  }\n  addMany(values) {\n    return {\n      after: (...params) => this.addManyAfter.call(this, values, ...params),\n      before: (...params) => this.addManyBefore.call(this, values, ...params),\n      byIndex: position => this.addManyByIndex(values, position),\n      head: () => this.addManyHead(values),\n      tail: () => this.addManyTail(values)\n    };\n  }\n  addAfter(value, previousValue, compareFn = compare) {\n    const previous = this.find(node => compareFn(node.value, previousValue));\n    return previous ? this.attach(value, previous, previous.next) : this.addTail(value);\n  }\n  addBefore(value, nextValue, compareFn = compare) {\n    const next = this.find(node => compareFn(node.value, nextValue));\n    return next ? this.attach(value, next.previous, next) : this.addHead(value);\n  }\n  addByIndex(value, position) {\n    if (position < 0) position += this.size;else if (position >= this.size) return this.addTail(value);\n    if (position <= 0) return this.addHead(value);\n    const next = this.get(position);\n    return this.attach(value, next.previous, next);\n  }\n  addHead(value) {\n    const node = new ListNode(value);\n    node.next = this.first;\n    if (this.first) this.first.previous = node;else this.last = node;\n    this.first = node;\n    this.size++;\n    return node;\n  }\n  addTail(value) {\n    const node = new ListNode(value);\n    if (this.first) {\n      node.previous = this.last;\n      this.last.next = node;\n      this.last = node;\n    } else {\n      this.first = node;\n      this.last = node;\n    }\n    this.size++;\n    return node;\n  }\n  addManyAfter(values, previousValue, compareFn = compare) {\n    const previous = this.find(node => compareFn(node.value, previousValue));\n    return previous ? this.attachMany(values, previous, previous.next) : this.addManyTail(values);\n  }\n  addManyBefore(values, nextValue, compareFn = compare) {\n    const next = this.find(node => compareFn(node.value, nextValue));\n    return next ? this.attachMany(values, next.previous, next) : this.addManyHead(values);\n  }\n  addManyByIndex(values, position) {\n    if (position < 0) position += this.size;\n    if (position <= 0) return this.addManyHead(values);\n    if (position >= this.size) return this.addManyTail(values);\n    const next = this.get(position);\n    return this.attachMany(values, next.previous, next);\n  }\n  addManyHead(values) {\n    return values.reduceRight((nodes, value) => {\n      nodes.unshift(this.addHead(value));\n      return nodes;\n    }, []);\n  }\n  addManyTail(values) {\n    return values.map(value => this.addTail(value));\n  }\n  drop() {\n    return {\n      byIndex: position => this.dropByIndex(position),\n      byValue: (...params) => this.dropByValue.apply(this, params),\n      byValueAll: (...params) => this.dropByValueAll.apply(this, params),\n      head: () => this.dropHead(),\n      tail: () => this.dropTail()\n    };\n  }\n  dropMany(count) {\n    return {\n      byIndex: position => this.dropManyByIndex(count, position),\n      head: () => this.dropManyHead(count),\n      tail: () => this.dropManyTail(count)\n    };\n  }\n  dropByIndex(position) {\n    if (position < 0) position += this.size;\n    const current = this.get(position);\n    return current ? this.detach(current) : undefined;\n  }\n  dropByValue(value, compareFn = compare) {\n    const position = this.findIndex(node => compareFn(node.value, value));\n    return position < 0 ? undefined : this.dropByIndex(position);\n  }\n  dropByValueAll(value, compareFn = compare) {\n    const dropped = [];\n    for (let current = this.first, position = 0; current; position++, current = current.next) {\n      if (compareFn(current.value, value)) {\n        dropped.push(this.dropByIndex(position - dropped.length));\n      }\n    }\n    return dropped;\n  }\n  dropHead() {\n    const head = this.first;\n    if (head) {\n      this.first = head.next;\n      if (this.first) this.first.previous = undefined;else this.last = undefined;\n      this.size--;\n      return head;\n    }\n    return undefined;\n  }\n  dropTail() {\n    const tail = this.last;\n    if (tail) {\n      this.last = tail.previous;\n      if (this.last) this.last.next = undefined;else this.first = undefined;\n      this.size--;\n      return tail;\n    }\n    return undefined;\n  }\n  dropManyByIndex(count, position) {\n    if (count <= 0) return [];\n    if (position < 0) position = Math.max(position + this.size, 0);else if (position >= this.size) return [];\n    count = Math.min(count, this.size - position);\n    const dropped = [];\n    while (count--) {\n      const current = this.get(position);\n      dropped.push(this.detach(current));\n    }\n    return dropped;\n  }\n  dropManyHead(count) {\n    if (count <= 0) return [];\n    count = Math.min(count, this.size);\n    const dropped = [];\n    while (count--) dropped.unshift(this.dropHead());\n    return dropped;\n  }\n  dropManyTail(count) {\n    if (count <= 0) return [];\n    count = Math.min(count, this.size);\n    const dropped = [];\n    while (count--) dropped.push(this.dropTail());\n    return dropped;\n  }\n  find(predicate) {\n    for (let current = this.first, position = 0; current; position++, current = current.next) {\n      if (predicate(current, position, this)) return current;\n    }\n    return undefined;\n  }\n  findIndex(predicate) {\n    for (let current = this.first, position = 0; current; position++, current = current.next) {\n      if (predicate(current, position, this)) return position;\n    }\n    return -1;\n  }\n  forEach(iteratorFn) {\n    for (let node = this.first, position = 0; node; position++, node = node.next) {\n      iteratorFn(node, position, this);\n    }\n  }\n  get(position) {\n    return this.find((_, index) => position === index);\n  }\n  indexOf(value, compareFn = compare) {\n    return this.findIndex(node => compareFn(node.value, value));\n  }\n  toArray() {\n    const array = new Array(this.size);\n    this.forEach((node, index) => array[index] = node.value);\n    return array;\n  }\n  toNodeArray() {\n    const array = new Array(this.size);\n    this.forEach((node, index) => array[index] = node);\n    return array;\n  }\n  toString(mapperFn = JSON.stringify) {\n    return this.toArray().map(value => mapperFn(value)).join(' <-> ');\n  }\n  // Cannot use Generator type because of ng-packagr\n  *[Symbol.iterator]() {\n    for (let node = this.first, position = 0; node; position++, node = node.next) {\n      yield node.value;\n    }\n  }\n}\n\n/*\r\n * Public API Surface of utils\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { LinkedList, ListNode };\n//# sourceMappingURL=abp-utils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}