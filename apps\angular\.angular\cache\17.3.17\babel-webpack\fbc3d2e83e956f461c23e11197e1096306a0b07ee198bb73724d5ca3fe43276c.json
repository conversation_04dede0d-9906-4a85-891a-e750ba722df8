{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  return 5;\n}\nexport default [\"ca-FR\", [[\"a. m.\", \"p. m.\"], u, u], u, [[\"dg\", \"dl\", \"dt\", \"dc\", \"dj\", \"dv\", \"ds\"], [\"dg.\", \"dl.\", \"dt.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"], [\"diumenge\", \"dilluns\", \"dimarts\", \"dimecres\", \"dijous\", \"divendres\", \"dissabte\"], [\"dg.\", \"dl.\", \"dt.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"]], u, [[\"GN\", \"FB\", \"MÇ\", \"AB\", \"MG\", \"JN\", \"JL\", \"AG\", \"ST\", \"OC\", \"NV\", \"DS\"], [\"de gen.\", \"de febr.\", \"de març\", \"d’abr.\", \"de maig\", \"de juny\", \"de jul.\", \"d’ag.\", \"de set.\", \"d’oct.\", \"de nov.\", \"de des.\"], [\"de gener\", \"de febrer\", \"de març\", \"d’abril\", \"de maig\", \"de juny\", \"de juliol\", \"d’agost\", \"de setembre\", \"d’octubre\", \"de novembre\", \"de desembre\"]], [[\"GN\", \"FB\", \"MÇ\", \"AB\", \"MG\", \"JN\", \"JL\", \"AG\", \"ST\", \"OC\", \"NV\", \"DS\"], [\"gen.\", \"febr.\", \"març\", \"abr.\", \"maig\", \"juny\", \"jul.\", \"ag.\", \"set.\", \"oct.\", \"nov.\", \"des.\"], [\"gener\", \"febrer\", \"març\", \"abril\", \"maig\", \"juny\", \"juliol\", \"agost\", \"setembre\", \"octubre\", \"novembre\", \"desembre\"]], [[\"aC\", \"dC\"], u, [\"abans de Crist\", \"després de Crist\"]], 1, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM 'de' y\", \"EEEE, d MMMM 'de' y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss (zzzz)\"], [\"{1} {0}\", \"{1}, {0}\", \"{1}, 'a' 'les' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"ESP\": [\"₧\"],\n  \"FRF\": [\"F\"],\n  \"MXN\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"USD\": [u, \"$\"],\n  \"VEF\": [u, \"Bs F\"],\n  \"XCD\": [u, \"$\"],\n  \"XXX\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ca-FR.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    return 5;\n}\nexport default [\"ca-FR\", [[\"a. m.\", \"p. m.\"], u, u], u, [[\"dg\", \"dl\", \"dt\", \"dc\", \"dj\", \"dv\", \"ds\"], [\"dg.\", \"dl.\", \"dt.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"], [\"diumenge\", \"dilluns\", \"dimarts\", \"dimecres\", \"dijous\", \"divendres\", \"dissabte\"], [\"dg.\", \"dl.\", \"dt.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"]], u, [[\"GN\", \"FB\", \"MÇ\", \"AB\", \"MG\", \"JN\", \"JL\", \"AG\", \"ST\", \"OC\", \"NV\", \"DS\"], [\"de gen.\", \"de febr.\", \"de març\", \"d’abr.\", \"de maig\", \"de juny\", \"de jul.\", \"d’ag.\", \"de set.\", \"d’oct.\", \"de nov.\", \"de des.\"], [\"de gener\", \"de febrer\", \"de març\", \"d’abril\", \"de maig\", \"de juny\", \"de juliol\", \"d’agost\", \"de setembre\", \"d’octubre\", \"de novembre\", \"de desembre\"]], [[\"GN\", \"FB\", \"MÇ\", \"AB\", \"MG\", \"JN\", \"JL\", \"AG\", \"ST\", \"OC\", \"NV\", \"DS\"], [\"gen.\", \"febr.\", \"març\", \"abr.\", \"maig\", \"juny\", \"jul.\", \"ag.\", \"set.\", \"oct.\", \"nov.\", \"des.\"], [\"gener\", \"febrer\", \"març\", \"abril\", \"maig\", \"juny\", \"juliol\", \"agost\", \"setembre\", \"octubre\", \"novembre\", \"desembre\"]], [[\"aC\", \"dC\"], u, [\"abans de Crist\", \"després de Crist\"]], 1, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM 'de' y\", \"EEEE, d MMMM 'de' y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss (zzzz)\"], [\"{1} {0}\", \"{1}, {0}\", \"{1}, 'a' 'les' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"AUD\": [\"AU$\", \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"ESP\": [\"₧\"], \"FRF\": [\"F\"], \"MXN\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"USD\": [u, \"$\"], \"VEF\": [u, \"Bs F\"], \"XCD\": [u, \"$\"], \"XXX\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;EAChG,IAAIP,CAAC,KAAK,CAAC,IAAII,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAET,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,oBAAoB,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,MAAM,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}