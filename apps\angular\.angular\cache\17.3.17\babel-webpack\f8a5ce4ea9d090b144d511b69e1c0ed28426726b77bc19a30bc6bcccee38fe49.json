{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/card\";\n/** Rendering section \"THE NUMBER OF ENTITIES BY YEAR\" in the third tab \"Additional Statistics\" in Dashboard page.\n *  Requirement number: 2972; 2973; 2974; OECD requirements\n *\n */\nexport class JurisdictionsWithTaxResidenceComponent extends AppComponentBase {\n  /**\n   * @constructor\n   * @param {Injector} injector\n   */\n  constructor(injector) {\n    super(injector);\n    //\n    //Note: Work for OECD requirements\n    //\n    this.jurisdictions = '';\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.dashboardData) {\n      this.getData();\n    }\n  }\n  getData() {\n    if (this.dashboardData) this.jurisdictions = this.dashboardData.jurisdictions.map(item => item).join(',   ');\n  }\n  static {\n    this.ɵfac = function JurisdictionsWithTaxResidenceComponent_Factory(t) {\n      return new (t || JurisdictionsWithTaxResidenceComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: JurisdictionsWithTaxResidenceComponent,\n      selectors: [[\"app-jurisdictions-with-tax-residence\"]],\n      inputs: {\n        dashboardData: \"dashboardData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 11,\n      vars: 1,\n      consts: [[1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"]],\n      template: function JurisdictionsWithTaxResidenceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n          i0.ɵɵtext(3, \"LIST OF JURISDICTIONS IN WHICH TAX RESIDENCE WAS CLAIMED\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 1)(6, \"div\", 2);\n          i0.ɵɵtext(7, \"Name\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 1)(9, \"div\", 3);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\", ctx.jurisdictions, \" \");\n        }\n      },\n      dependencies: [i1.MatCard, i1.MatCardContent, i1.MatCardHeader, i1.MatCardTitle],\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJqdXJpc2RpY3Rpb25zLXdpdGgtdGF4LXJlc2lkZW5jZS5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvYWRkaXRpb25hbC1zdGF0aXN0aWNzL2p1cmlzZGljdGlvbnMtd2l0aC10YXgtcmVzaWRlbmNlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9NQUFvTSIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "JurisdictionsWithTaxResidenceComponent", "constructor", "injector", "jurisdictions", "ngOnInit", "ngOnChanges", "changes", "dashboardData", "getData", "map", "item", "join", "i0", "ɵɵdirectiveInject", "Injector", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "JurisdictionsWithTaxResidenceComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\additional-statistics\\jurisdictions-with-tax-residence.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\additional-statistics\\jurisdictions-with-tax-residence.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  Input,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { StatisticMainDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\n\r\n/** Rendering section \"THE NUMBER OF ENTITIES BY YEAR\" in the third tab \"Additional Statistics\" in Dashboard page.\r\n *  Requirement number: 2972; 2973; 2974; OECD requirements\r\n *\r\n */\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-jurisdictions-with-tax-residence',\r\n  templateUrl: './jurisdictions-with-tax-residence.component.html',\r\n  styleUrls: ['./jurisdictions-with-tax-residence.component.css'],\r\n})\r\nexport class JurisdictionsWithTaxResidenceComponent\r\n  extends AppComponentBase\r\n  implements OnInit, OnChanges\r\n{\r\n  @Input() dashboardData: StatisticMainDto;\r\n\r\n  //\r\n  //Note: Work for OECD requirements\r\n  //\r\n  jurisdictions = '';\r\n\r\n  /**\r\n   * @constructor\r\n   * @param {Injector} injector\r\n   */\r\n  constructor(injector: Injector) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit() {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.dashboardData) {\r\n      this.getData();\r\n    }\r\n  }\r\n\r\n  private getData() {\r\n    if (this.dashboardData)\r\n      this.jurisdictions = this.dashboardData.jurisdictions\r\n        .map((item) => item)\r\n        .join(',   ');\r\n  }\r\n}\r\n", "<mat-card>\r\n  <mat-card-header>\r\n    <mat-card-title class=\"dashboard-card-title\"\r\n      >LIST OF JURISDICTIONS IN WHICH TAX RESIDENCE WAS CLAIMED</mat-card-title\r\n    >\r\n  </mat-card-header>\r\n  <mat-card-content>\r\n    <div class=\"dashboard-table\">\r\n      <div class=\"col title\">Name</div>\r\n    </div>\r\n    <div class=\"dashboard-table\">\r\n      <div class=\"col item\">{{jurisdictions}} </div>\r\n    </div>\r\n  </mat-card-content>\r\n</mat-card>\r\n"], "mappings": "AASA,SAASA,gBAAgB,QAAQ,yBAAyB;;;AAG1D;;;;AAUA,OAAM,MAAOC,sCACX,SAAQD,gBAAgB;EAUxB;;;;EAIAE,YAAYC,QAAkB;IAC5B,KAAK,CAACA,QAAQ,CAAC;IAVjB;IACA;IACA;IACA,KAAAC,aAAa,GAAG,EAAE;EAQlB;EAEAC,QAAQA,CAAA,GAAI;EAEZC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACC,aAAa,EAAE;MACzB,IAAI,CAACC,OAAO,EAAE;IAChB;EACF;EAEQA,OAAOA,CAAA;IACb,IAAI,IAAI,CAACD,aAAa,EACpB,IAAI,CAACJ,aAAa,GAAG,IAAI,CAACI,aAAa,CAACJ,aAAa,CAClDM,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC,CACnBC,IAAI,CAAC,MAAM,CAAC;EACnB;;;uBAhCWX,sCAAsC,EAAAY,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA;IAAA;EAAA;;;YAAtCd,sCAAsC;MAAAe,SAAA;MAAAC,MAAA;QAAAT,aAAA;MAAA;MAAAU,QAAA,GAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB/Cb,EAFJ,CAAAe,cAAA,eAAU,sBACS,wBAEZ;UAAAf,EAAA,CAAAgB,MAAA,+DAAwD;UAE7DhB,EAF6D,CAAAiB,YAAA,EAC1D,EACe;UAGdjB,EAFJ,CAAAe,cAAA,uBAAkB,aACa,aACJ;UAAAf,EAAA,CAAAgB,MAAA,WAAI;UAC7BhB,EAD6B,CAAAiB,YAAA,EAAM,EAC7B;UAEJjB,EADF,CAAAe,cAAA,aAA6B,aACL;UAAAf,EAAA,CAAAgB,MAAA,IAAkB;UAG9ChB,EAH8C,CAAAiB,YAAA,EAAM,EAC1C,EACW,EACV;;;UAHiBjB,EAAA,CAAAkB,SAAA,IAAkB;UAAlBlB,EAAA,CAAAmB,kBAAA,KAAAL,GAAA,CAAAvB,aAAA,MAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}