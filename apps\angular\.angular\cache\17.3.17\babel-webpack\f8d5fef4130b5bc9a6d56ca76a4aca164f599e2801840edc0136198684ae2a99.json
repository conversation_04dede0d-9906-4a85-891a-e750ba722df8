{"ast": null, "code": "import tzTokenizeDate from '../tzTokenizeDate/index.js';\nimport newDateUTC from '../newDateUTC/index.js';\nvar MILLISECONDS_IN_HOUR = 3600000;\nvar MILLISECONDS_IN_MINUTE = 60000;\nvar patterns = {\n  timezone: /([Z+-].*)$/,\n  timezoneZ: /^(Z)$/,\n  timezoneHH: /^([+-]\\d{2})$/,\n  timezoneHHMM: /^([+-])(\\d{2}):?(\\d{2})$/\n};\n\n// Parse various time zone offset formats to an offset in milliseconds\nexport default function tzParseTimezone(timezoneString, date, isUtcDate) {\n  var token;\n  var absoluteOffset;\n\n  // Empty string\n  if (!timezoneString) {\n    return 0;\n  }\n\n  // Z\n  token = patterns.timezoneZ.exec(timezoneString);\n  if (token) {\n    return 0;\n  }\n  var hours;\n\n  // ±hh\n  token = patterns.timezoneHH.exec(timezoneString);\n  if (token) {\n    hours = parseInt(token[1], 10);\n    if (!validateTimezone(hours)) {\n      return NaN;\n    }\n    return -(hours * MILLISECONDS_IN_HOUR);\n  }\n\n  // ±hh:mm or ±hhmm\n  token = patterns.timezoneHHMM.exec(timezoneString);\n  if (token) {\n    hours = parseInt(token[2], 10);\n    var minutes = parseInt(token[3], 10);\n    if (!validateTimezone(hours, minutes)) {\n      return NaN;\n    }\n    absoluteOffset = Math.abs(hours) * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE;\n    return token[1] === '+' ? -absoluteOffset : absoluteOffset;\n  }\n\n  // IANA time zone\n  if (isValidTimezoneIANAString(timezoneString)) {\n    date = new Date(date || Date.now());\n    var utcDate = isUtcDate ? date : toUtcDate(date);\n    var offset = calcOffset(utcDate, timezoneString);\n    var fixedOffset = isUtcDate ? offset : fixOffset(date, offset, timezoneString);\n    return -fixedOffset;\n  }\n  return NaN;\n}\nfunction toUtcDate(date) {\n  return newDateUTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n}\nfunction calcOffset(date, timezoneString) {\n  var tokens = tzTokenizeDate(date, timezoneString);\n\n  // ms dropped because it's not provided by tzTokenizeDate\n  var asUTC = newDateUTC(tokens[0], tokens[1] - 1, tokens[2], tokens[3] % 24, tokens[4], tokens[5], 0).getTime();\n  var asTS = date.getTime();\n  var over = asTS % 1000;\n  asTS -= over >= 0 ? over : 1000 + over;\n  return asUTC - asTS;\n}\nfunction fixOffset(date, offset, timezoneString) {\n  var localTS = date.getTime();\n\n  // Our UTC time is just a guess because our offset is just a guess\n  var utcGuess = localTS - offset;\n\n  // Test whether the zone matches the offset for this ts\n  var o2 = calcOffset(new Date(utcGuess), timezoneString);\n\n  // If so, offset didn't change, and we're done\n  if (offset === o2) {\n    return offset;\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= o2 - offset;\n\n  // If that gives us the local time we want, we're done\n  var o3 = calcOffset(new Date(utcGuess), timezoneString);\n  if (o2 === o3) {\n    return o2;\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but we don't adjust the time\n  return Math.max(o2, o3);\n}\nfunction validateTimezone(hours, minutes) {\n  return -23 <= hours && hours <= 23 && (minutes == null || 0 <= minutes && minutes <= 59);\n}\nvar validIANATimezoneCache = {};\nfunction isValidTimezoneIANAString(timeZoneString) {\n  if (validIANATimezoneCache[timeZoneString]) return true;\n  try {\n    new Intl.DateTimeFormat(undefined, {\n      timeZone: timeZoneString\n    });\n    validIANATimezoneCache[timeZoneString] = true;\n    return true;\n  } catch (error) {\n    return false;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}