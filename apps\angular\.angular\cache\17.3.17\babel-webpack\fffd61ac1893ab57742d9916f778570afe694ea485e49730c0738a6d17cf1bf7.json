{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { strNotSpecified } from \"../Constants\";\nimport { _DYN_DURATION, _DYN_MEASUREMENTS, _DYN_NAME, _DYN_PROPERTIES, _DYN_RECEIVED_RESPONSE } from \"../__DynamicConstants\";\nimport { dataSanitizeMeasurements, dataSanitizeProperties, dataSanitizeString, dataSanitizeUrl } from \"./Common/DataSanitizer\";\nvar PageViewPerformance = /** @class */function () {\n  /**\r\n   * Constructs a new instance of the PageEventTelemetry object\r\n   */\n  function PageViewPerformance(logger, name, url, unused, properties, measurements, cs4BaseData) {\n    this.aiDataContract = {\n      ver: 1 /* FieldType.Required */,\n      name: 0 /* FieldType.Default */,\n      url: 0 /* FieldType.Default */,\n      duration: 0 /* FieldType.Default */,\n      perfTotal: 0 /* FieldType.Default */,\n      networkConnect: 0 /* FieldType.Default */,\n      sentRequest: 0 /* FieldType.Default */,\n      receivedResponse: 0 /* FieldType.Default */,\n      domProcessing: 0 /* FieldType.Default */,\n      properties: 0 /* FieldType.Default */,\n      measurements: 0 /* FieldType.Default */\n    };\n    var _self = this;\n    _self.ver = 2;\n    _self.url = dataSanitizeUrl(logger, url);\n    _self[_DYN_NAME /* @min:%2ename */] = dataSanitizeString(logger, name) || strNotSpecified;\n    _self[_DYN_PROPERTIES /* @min:%2eproperties */] = dataSanitizeProperties(logger, properties);\n    _self[_DYN_MEASUREMENTS /* @min:%2emeasurements */] = dataSanitizeMeasurements(logger, measurements);\n    if (cs4BaseData) {\n      _self.domProcessing = cs4BaseData.domProcessing;\n      _self[_DYN_DURATION /* @min:%2eduration */] = cs4BaseData[_DYN_DURATION /* @min:%2eduration */];\n      _self.networkConnect = cs4BaseData.networkConnect;\n      _self.perfTotal = cs4BaseData.perfTotal;\n      _self[_DYN_RECEIVED_RESPONSE /* @min:%2ereceivedResponse */] = cs4BaseData[_DYN_RECEIVED_RESPONSE /* @min:%2ereceivedResponse */];\n      _self.sentRequest = cs4BaseData.sentRequest;\n    }\n  }\n  PageViewPerformance.envelopeType = \"Microsoft.ApplicationInsights.{0}.PageviewPerformance\";\n  PageViewPerformance.dataType = \"PageviewPerformanceData\";\n  return PageViewPerformance;\n}();\nexport { PageViewPerformance };", "map": {"version": 3, "names": ["strNotSpecified", "_DYN_DURATION", "_DYN_MEASUREMENTS", "_DYN_NAME", "_DYN_PROPERTIES", "_DYN_RECEIVED_RESPONSE", "dataSanitizeMeasurements", "dataSanitizeProperties", "dataSanitizeString", "dataSanitizeUrl", "PageViewPerformance", "logger", "name", "url", "unused", "properties", "measurements", "cs4BaseData", "aiDataContract", "ver", "duration", "perfTotal", "networkConnect", "sentRequest", "receivedResponse", "domProcessing", "_self", "envelopeType", "dataType"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/Telemetry/PageViewPerformance.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { strNotSpecified } from \"../Constants\";\r\nimport { _DYN_DURATION, _DYN_MEASUREMENTS, _DYN_NAME, _DYN_PROPERTIES, _DYN_RECEIVED_RESPONSE } from \"../__DynamicConstants\";\r\nimport { dataSanitizeMeasurements, dataSanitizeProperties, dataSanitizeString, dataSanitizeUrl } from \"./Common/DataSanitizer\";\r\nvar PageViewPerformance = /** @class */ (function () {\r\n    /**\r\n     * Constructs a new instance of the PageEventTelemetry object\r\n     */\r\n    function PageViewPerformance(logger, name, url, unused, properties, measurements, cs4BaseData) {\r\n        this.aiDataContract = {\r\n            ver: 1 /* FieldType.Required */,\r\n            name: 0 /* FieldType.Default */,\r\n            url: 0 /* FieldType.Default */,\r\n            duration: 0 /* FieldType.Default */,\r\n            perfTotal: 0 /* FieldType.Default */,\r\n            networkConnect: 0 /* FieldType.Default */,\r\n            sentRequest: 0 /* FieldType.Default */,\r\n            receivedResponse: 0 /* FieldType.Default */,\r\n            domProcessing: 0 /* FieldType.Default */,\r\n            properties: 0 /* FieldType.Default */,\r\n            measurements: 0 /* FieldType.Default */\r\n        };\r\n        var _self = this;\r\n        _self.ver = 2;\r\n        _self.url = dataSanitizeUrl(logger, url);\r\n        _self[_DYN_NAME /* @min:%2ename */] = dataSanitizeString(logger, name) || strNotSpecified;\r\n        _self[_DYN_PROPERTIES /* @min:%2eproperties */] = dataSanitizeProperties(logger, properties);\r\n        _self[_DYN_MEASUREMENTS /* @min:%2emeasurements */] = dataSanitizeMeasurements(logger, measurements);\r\n        if (cs4BaseData) {\r\n            _self.domProcessing = cs4BaseData.domProcessing;\r\n            _self[_DYN_DURATION /* @min:%2eduration */] = cs4BaseData[_DYN_DURATION /* @min:%2eduration */];\r\n            _self.networkConnect = cs4BaseData.networkConnect;\r\n            _self.perfTotal = cs4BaseData.perfTotal;\r\n            _self[_DYN_RECEIVED_RESPONSE /* @min:%2ereceivedResponse */] = cs4BaseData[_DYN_RECEIVED_RESPONSE /* @min:%2ereceivedResponse */];\r\n            _self.sentRequest = cs4BaseData.sentRequest;\r\n        }\r\n    }\r\n    PageViewPerformance.envelopeType = \"Microsoft.ApplicationInsights.{0}.PageviewPerformance\";\r\n    PageViewPerformance.dataType = \"PageviewPerformanceData\";\r\n    return PageViewPerformance;\r\n}());\r\nexport { PageViewPerformance };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,eAAe,QAAQ,cAAc;AAC9C,SAASC,aAAa,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,eAAe,EAAEC,sBAAsB,QAAQ,uBAAuB;AAC5H,SAASC,wBAAwB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,wBAAwB;AAC9H,IAAIC,mBAAmB,GAAG,aAAe,YAAY;EACjD;AACJ;AACA;EACI,SAASA,mBAAmBA,CAACC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,WAAW,EAAE;IAC3F,IAAI,CAACC,cAAc,GAAG;MAClBC,GAAG,EAAE,CAAC,CAAC;MACPP,IAAI,EAAE,CAAC,CAAC;MACRC,GAAG,EAAE,CAAC,CAAC;MACPO,QAAQ,EAAE,CAAC,CAAC;MACZC,SAAS,EAAE,CAAC,CAAC;MACbC,cAAc,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC;MACfC,gBAAgB,EAAE,CAAC,CAAC;MACpBC,aAAa,EAAE,CAAC,CAAC;MACjBV,UAAU,EAAE,CAAC,CAAC;MACdC,YAAY,EAAE,CAAC,CAAC;IACpB,CAAC;IACD,IAAIU,KAAK,GAAG,IAAI;IAChBA,KAAK,CAACP,GAAG,GAAG,CAAC;IACbO,KAAK,CAACb,GAAG,GAAGJ,eAAe,CAACE,MAAM,EAAEE,GAAG,CAAC;IACxCa,KAAK,CAACvB,SAAS,CAAC,mBAAmB,GAAGK,kBAAkB,CAACG,MAAM,EAAEC,IAAI,CAAC,IAAIZ,eAAe;IACzF0B,KAAK,CAACtB,eAAe,CAAC,yBAAyB,GAAGG,sBAAsB,CAACI,MAAM,EAAEI,UAAU,CAAC;IAC5FW,KAAK,CAACxB,iBAAiB,CAAC,2BAA2B,GAAGI,wBAAwB,CAACK,MAAM,EAAEK,YAAY,CAAC;IACpG,IAAIC,WAAW,EAAE;MACbS,KAAK,CAACD,aAAa,GAAGR,WAAW,CAACQ,aAAa;MAC/CC,KAAK,CAACzB,aAAa,CAAC,uBAAuB,GAAGgB,WAAW,CAAChB,aAAa,CAAC,uBAAuB;MAC/FyB,KAAK,CAACJ,cAAc,GAAGL,WAAW,CAACK,cAAc;MACjDI,KAAK,CAACL,SAAS,GAAGJ,WAAW,CAACI,SAAS;MACvCK,KAAK,CAACrB,sBAAsB,CAAC,+BAA+B,GAAGY,WAAW,CAACZ,sBAAsB,CAAC,+BAA+B;MACjIqB,KAAK,CAACH,WAAW,GAAGN,WAAW,CAACM,WAAW;IAC/C;EACJ;EACAb,mBAAmB,CAACiB,YAAY,GAAG,uDAAuD;EAC1FjB,mBAAmB,CAACkB,QAAQ,GAAG,yBAAyB;EACxD,OAAOlB,mBAAmB;AAC9B,CAAC,CAAC,CAAE;AACJ,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}