{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ResourcePageComponent } from './containers/resource-page/resource-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ResourcePageComponent\n}];\nexport class ResourcePageRoutingModule {\n  static {\n    this.ɵfac = function ResourcePageRoutingModule_Factory(t) {\n      return new (t || ResourcePageRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResourcePageRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResourcePageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ResourcePageComponent", "routes", "path", "component", "ResourcePageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\resource-page\\resource-page.routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { ResourcePageComponent } from './containers/resource-page/resource-page.component';\r\n\r\n\r\n\r\nconst routes: Routes = [\r\n\r\n    { path: '', component: ResourcePageComponent }\r\n];\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild(routes)],\r\n    exports: [RouterModule]\r\n})\r\nexport class ResourcePageRoutingModule { }\r\n"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,qBAAqB,QAAQ,oDAAoD;;;AAI1F,MAAMC,MAAM,GAAW,CAEnB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAqB,CAAE,CACjD;AAMD,OAAM,MAAOI,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBAHxBL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEbK,yBAAyB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFxBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}