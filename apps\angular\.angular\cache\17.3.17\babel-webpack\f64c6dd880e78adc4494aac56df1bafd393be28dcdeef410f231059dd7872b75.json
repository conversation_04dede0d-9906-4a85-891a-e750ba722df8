{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 && !(i % 100 >= 12 && i % 100 <= 14)) return 3;\n  if (v === 0 && !(i === 1) && i % 10 === Math.floor(i % 10) && i % 10 >= 0 && i % 10 <= 1 || v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 || v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 12 && i % 100 <= 14) return 4;\n  return 5;\n}\nexport default [\"pl\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], u, [[\"n\", \"p\", \"w\", \"ś\", \"c\", \"p\", \"s\"], [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"], [\"niedziela\", \"poniedziałek\", \"wtorek\", \"środa\", \"czwartek\", \"piątek\", \"sobota\"], [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"]], [[\"N\", \"P\", \"W\", \"Ś\", \"C\", \"P\", \"S\"], [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"], [\"niedziela\", \"poniedziałek\", \"wtorek\", \"środa\", \"czwartek\", \"piątek\", \"sobota\"], [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"]], [[\"s\", \"l\", \"m\", \"k\", \"m\", \"c\", \"l\", \"s\", \"w\", \"p\", \"l\", \"g\"], [\"sty\", \"lut\", \"mar\", \"kwi\", \"maj\", \"cze\", \"lip\", \"sie\", \"wrz\", \"paź\", \"lis\", \"gru\"], [\"stycznia\", \"lutego\", \"marca\", \"kwietnia\", \"maja\", \"czerwca\", \"lipca\", \"sierpnia\", \"września\", \"października\", \"listopada\", \"grudnia\"]], [[\"S\", \"L\", \"M\", \"K\", \"M\", \"C\", \"L\", \"S\", \"W\", \"P\", \"L\", \"G\"], [\"sty\", \"lut\", \"mar\", \"kwi\", \"maj\", \"cze\", \"lip\", \"sie\", \"wrz\", \"paź\", \"lis\", \"gru\"], [\"styczeń\", \"luty\", \"marzec\", \"kwiecień\", \"maj\", \"czerwiec\", \"lipiec\", \"sierpień\", \"wrzesień\", \"październik\", \"listopad\", \"grudzień\"]], [[\"p.n.e.\", \"n.e.\"], u, [\"przed naszą erą\", \"naszej ery\"]], 1, [6, 0], [\"d.MM.y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"PLN\", \"zł\", \"złoty polski\", {\n  \"AUD\": [u, \"$\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [u, \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"PLN\": [\"zł\"],\n  \"RON\": [u, \"lej\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/pl.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    if (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)))\n        return 3;\n    if (v === 0 && (!(i === 1) && (i % 10 === Math.floor(i % 10) && (i % 10 >= 0 && i % 10 <= 1))) || (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 5 && i % 10 <= 9)) || v === 0 && (i % 100 === Math.floor(i % 100) && (i % 100 >= 12 && i % 100 <= 14))))\n        return 4;\n    return 5;\n}\nexport default [\"pl\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], u, [[\"n\", \"p\", \"w\", \"ś\", \"c\", \"p\", \"s\"], [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"], [\"niedziela\", \"poniedziałek\", \"wtorek\", \"środa\", \"czwartek\", \"piątek\", \"sobota\"], [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"]], [[\"N\", \"P\", \"W\", \"Ś\", \"C\", \"P\", \"S\"], [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"], [\"niedziela\", \"poniedziałek\", \"wtorek\", \"środa\", \"czwartek\", \"piątek\", \"sobota\"], [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"]], [[\"s\", \"l\", \"m\", \"k\", \"m\", \"c\", \"l\", \"s\", \"w\", \"p\", \"l\", \"g\"], [\"sty\", \"lut\", \"mar\", \"kwi\", \"maj\", \"cze\", \"lip\", \"sie\", \"wrz\", \"paź\", \"lis\", \"gru\"], [\"stycznia\", \"lutego\", \"marca\", \"kwietnia\", \"maja\", \"czerwca\", \"lipca\", \"sierpnia\", \"września\", \"października\", \"listopada\", \"grudnia\"]], [[\"S\", \"L\", \"M\", \"K\", \"M\", \"C\", \"L\", \"S\", \"W\", \"P\", \"L\", \"G\"], [\"sty\", \"lut\", \"mar\", \"kwi\", \"maj\", \"cze\", \"lip\", \"sie\", \"wrz\", \"paź\", \"lis\", \"gru\"], [\"styczeń\", \"luty\", \"marzec\", \"kwiecień\", \"maj\", \"czerwiec\", \"lipiec\", \"sierpień\", \"wrzesień\", \"październik\", \"listopad\", \"grudzień\"]], [[\"p.n.e.\", \"n.e.\"], u, [\"przed naszą erą\", \"naszej ery\"]], 1, [6, 0], [\"d.MM.y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"PLN\", \"zł\", \"złoty polski\", { \"AUD\": [u, \"$\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"PLN\": [\"zł\"], \"RON\": [u, \"lej\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;EAChG,IAAIP,CAAC,KAAK,CAAC,IAAII,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAE,EAC/G,OAAO,CAAC;EACZ,IAAII,CAAC,KAAK,CAAC,IAAK,EAAEJ,CAAC,KAAK,CAAC,CAAC,IAAKA,CAAC,GAAG,EAAE,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAI,IAAKI,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAG,IAAII,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAKA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAK,EAC9P,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEL,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,SAAS,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}