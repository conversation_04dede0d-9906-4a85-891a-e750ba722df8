{"ast": null, "code": "export * from './models';", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\information-exchanges\\historical-migration\\index.ts"], "sourcesContent": ["export * from './models';\r\n"], "mappings": "AAAA,cAAc,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}