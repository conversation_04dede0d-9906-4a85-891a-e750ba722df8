{"ast": null, "code": "import { BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { AppComponentBase } from '@app/app-component-base';\nimport { DialogStatus, SortDirection } from '@app/shared/constants';\nimport Swal from 'sweetalert2';\nimport { finalize } from 'rxjs';\nimport { DeclarationHistoryColumns, caActionButtons, caDeclarationHistoryColumns, raActionButtons } from './search-result-history-columns';\nimport { PreviousVersionsViewComponent } from '@app/features/es-declaration/containers/previous-versions-view/previous-versions-view.component';\nimport { PreviewFileComponent } from '@app/features/ca-action-page/containers/attachements-component/preview-file/preview-file.component';\nimport { EsLetterModalComponent } from '@app/features/ca-action-page/containers/models/es-letter-modal/es-letter-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"proxies/search-service/lib/proxy/bdo/ess/search-service/search-controller\";\nimport * as i3 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i4 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations\";\nimport * as i5 from \"@app/shared/services/sweetalert.service\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"@abp/ng.core\";\nimport * as i8 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/assessment\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"@angular/material/divider\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i14 from \"@angular/common\";\nfunction SearchResultHistoryStatusComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 4);\n    i0.ɵɵtext(2, \"ECONOMIC SUBSTANCE DECLARATIONS HISTORY AND STATUS\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchResultHistoryStatusComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 4);\n    i0.ɵɵtext(2, \"ECONOMIC SUBSTANCE ASSESSMENT HISTORY AND STATUS\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchResultHistoryStatusComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function SearchResultHistoryStatusComponent_div_2_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startNewDeclaration());\n    });\n    i0.ɵɵtext(2, \"Add New ES Declaration\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", ctx_r1.isDeleted);\n  }\n}\nfunction SearchResultHistoryStatusComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SearchResultHistoryStatusComponent_div_2_div_1_Template, 3, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpPermission\", \"EsService.Declaration.Submit\");\n  }\n}\nfunction SearchResultHistoryStatusComponent_mat_progress_spinner_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-progress-spinner\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"mode\", \"indeterminate\")(\"diameter\", 50);\n  }\n}\n// TODO: Refactor all the settimeouts\nexport let SearchResultHistoryStatusComponent = /*#__PURE__*/(() => {\n  class SearchResultHistoryStatusComponent extends AppComponentBase {\n    constructor(injector, router, basicSearch, declarationStatus, declarationService, sweetAlert, dialog, config, esLetterService, toasterService) {\n      super(injector);\n      this.router = router;\n      this.basicSearch = basicSearch;\n      this.declarationStatus = declarationStatus;\n      this.declarationService = declarationService;\n      this.sweetAlert = sweetAlert;\n      this.dialog = dialog;\n      this.config = config;\n      this.esLetterService = esLetterService;\n      this.toasterService = toasterService;\n      this.TableId = 'declaration-history';\n      this.PageSize = 100;\n      this.declarationList = [];\n      this.historyRequstDto = {\n        maxResultCount: 10,\n        sorting: \"periodEndYear desc\",\n        skipCount: 0\n      };\n      this.tempShowESLetterView = false;\n      this.isLoading = false;\n      this.isCA = false;\n      this.viewESletter = false;\n    }\n    ngOnChanges() {\n      // console.log('on init');\n      this.isCA = this.config.getFeature('SearchService.CASearch') == \"true\";\n      this.tableColumns = this.isCA ? caDeclarationHistoryColumns : DeclarationHistoryColumns;\n      this.isLoading = true;\n      this.getHistory();\n    }\n    getHistory() {\n      if (this.entityId) {\n        var getHistory;\n        if (this.isCA) {\n          getHistory = this.basicSearch.getCAEntityHistoryByEntityIdAndRequest(this.entityId, this.historyRequstDto);\n        } else {\n          getHistory = this.basicSearch.getEntityHistoryByEntityIdAndRequest(this.entityId, this.historyRequstDto);\n          // getDecData = this.declarationService.get(this.declarationList[0].declarationId);\n        }\n        getHistory.pipe(finalize(() => {\n          this.setTableData();\n          this.isLoading = false;\n        })).subscribe(result => {\n          this.declarationList = [];\n          result.forEach(element => {\n            var obj = {\n              declarationId: element.declarationId ? element.declarationId : \"\",\n              endYear: element.periodEndYear,\n              time: this.dateCompare(element.submissionTime, \"2000-01-01T00:00:00\") ? element.submissionTime : \"\"\n            };\n            if (this.isCA) {\n              obj.declarationStatus = this.declarationStatusList.find(x => x.id == element.declarationStatus)?.name;\n              obj.assessmentStatus = this.assessmentStatusList.find(x => x.id == element.assessmentStatusId)?.name;\n              if (obj.assessmentStatus == \"Pass\") {\n                this.declarationService.getCADeclarationByDeclarationId(element.declarationId).pipe(finalize(() => {\n                  this.isLoading = false;\n                  this.setTableData();\n                })).subscribe(result => {\n                  if (result.esLetterPath || this.tempShowESLetterView) {\n                    console.log('path published: ', element.isESLetterPublished);\n                    obj.esLetter = element.isESLetterPublished ? \"View\" : \"View / Publish\";\n                  } else {\n                    obj.esLetter = '';\n                  }\n                });\n              } else {\n                obj.esletter = '';\n              }\n            } else {\n              obj.submittedBy = element.submittedBy ? element.submittedBy : \"\";\n              obj.status = this.declarationStatusList.find(x => x.id == element.declarationStatus)?.name;\n              obj.esLetter = element.isESLetterPublished ? \"View\" : \"\";\n              obj.lastSavedDate = this.dateCompare(element.lastSavedTime, \"2000-01-01T00:00:00\") ? element.lastSavedTime : \"\";\n              this.setTableData();\n            }\n            //If Entity is deleted, hide Draft\n            if (!(obj.status && obj.status == \"Draft\" && this.isDeleted)) {\n              this.declarationList.push(obj);\n            }\n          });\n        });\n      }\n    }\n    dateCompare(date1, date2) {\n      const convertedDate1 = new Date(date1);\n      const convertedDate2 = new Date(date2);\n      return convertedDate1 >= convertedDate2;\n    }\n    startNewDeclaration() {\n      if (!this.entityId) {\n        Swal.fire({\n          icon: 'info',\n          title: 'No Entity Selected!',\n          text: 'Please select an entity before adding a declaration.',\n          allowOutsideClick: true\n        });\n        return;\n      }\n      this.sweetAlert.fireDialog({\n        action: \"create\",\n        title: \"Economic Substance Declaration Disclaimer\",\n        text: \"Pursuant to Section 19 of the Act (CESRA 2023), a person who intentionally provides false information relating to a corporate and legal entity commits an offense and is liable to enforcement action.\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          if (this.entityId) {\n            this.router.navigate(['/es-declaration'], {\n              queryParams: {\n                entityid: this.entityId,\n                source: \"manualEntry\"\n              }\n            });\n          }\n        }\n      });\n    }\n    actionButtonClicked(event) {\n      if (event.data.id) {\n        this.decId = event.data.id;\n        if (event.action === 'edit') {\n          this.goToDeclaration(event.data.id, event.data.rawData.status);\n        }\n        if (event.action === 'delete') {\n          this.deleteDeclaration(event.data.id, event.data.rawData.endYear);\n        }\n        if (event.action === 'view') {\n          this.viewDeclaration(event.data.id, event.data.rawData.status);\n        }\n        if (event.action === 'history') {\n          this.viewDeclarationHistory(event.data.id);\n        }\n        if (event.action === 'CaAction') {\n          if (this.fromDashboard) {\n            this.router.navigate(['/action-page'], {\n              queryParams: {\n                declarationid: event.data.id,\n                entityid: this.entityId,\n                from: \"Dashboard\"\n              }\n            });\n          } else if (this.isAdvancedSearch) {\n            this.router.navigate(['/action-page'], {\n              queryParams: {\n                declarationid: event.data.id,\n                entityid: this.entityId,\n                from: \"AdvancedSearch\"\n              }\n            });\n          } else {\n            this.router.navigate(['/action-page'], {\n              queryParams: {\n                declarationid: event.data.id,\n                entityid: this.entityId\n              }\n            });\n          }\n        }\n        if (event.action === 'CaHistory') {\n          this.viewDeclarationHistory(event.data.id);\n        }\n      }\n    }\n    goToDeclaration(declarationId, status) {\n      this.router.navigate(['/es-declaration'], {\n        queryParams: {\n          declarationid: declarationId,\n          entityid: this.entityId,\n          action: 'edit',\n          status: status,\n          source: \"manualEntry\"\n        }\n      });\n    }\n    deleteDeclaration(declarationId, year) {\n      this.sweetAlert.fireDialog({\n        action: \"delete\",\n        title: \"Are you sure you want to delete draft for period end year \" + year + '?',\n        text: \"You won't be able to revert this!\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.declarationService.delete(declarationId).subscribe(result => {\n            if (result === true) {\n              this.sweetAlert.fireDialog({\n                status: \"success\" /* DialogStatus.SUCCESS */,\n                action: \"delete\",\n                source: \"declaration-history\",\n                type: \"toaster\"\n              });\n              this.declarationList = this.declarationList.filter(item => item.declarationId !== declarationId);\n              this.setTableData();\n            }\n          });\n        }\n      });\n    }\n    viewDeclaration(declarationId, status) {\n      this.router.navigate(['/es-declaration'], {\n        queryParams: {\n          declarationid: declarationId,\n          entityid: this.entityId,\n          action: 'view',\n          status: status,\n          source: \"manualEntry\"\n        }\n      });\n    }\n    viewDeclarationHistory(declarationId) {\n      const dialogRef = this.dialog.open(PreviousVersionsViewComponent, {\n        data: {\n          declarationId: declarationId,\n          entityId: this.entityId\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        console.log('The dialog was closed');\n        console.log(result);\n      });\n    }\n    onLazyLoadEvent(event) {\n      // reset the search parameters based on event info\n      this.historyRequstDto.skipCount = event.pageNumber * event.pageSize;\n      if (event.isAscending === true) {\n        this.historyRequstDto.sorting = event.sortField + \" \" + \"asc\" /* SortDirection.ASCENDING */;\n      }\n      if (event.isAscending === false) {\n        this.historyRequstDto.sorting = event.sortField + \" \" + \"desc\" /* SortDirection.DESCENDING */;\n      }\n      // call api\n      this.getHistory();\n    }\n    esLetter(event) {\n      if (event.rawData.esLetter == 'View') {\n        this.esLetterService.getLetterByDeclarationid(event.rawData.declarationId).subscribe(result => {\n          const file = {\n            name: 'esLetter',\n            type: 'application/pdf',\n            content: 'data:application/pdf;base64,' + result\n          };\n          this.viewLetter(file);\n        });\n      } else {\n        const dialogRef = this.dialog.open(EsLetterModalComponent, {\n          data: {\n            decId: event.rawData.declarationId,\n            isPublish: true\n          }\n        });\n        dialogRef.afterClosed().subscribe(result => {\n          console.log('The dialog was closed');\n          console.log(result);\n          if (result) {\n            this.esLetterService.publishLetterByDeclarationId(event.rawData.declarationId).subscribe(result => {\n              console.log('result:', result);\n              this.declarationList.find(element => element.declarationId == event.rawData.declarationId).esLetter = \"View\";\n              this.setTableData();\n            });\n          }\n        });\n      }\n    }\n    viewLetter(file) {\n      const dialogRef = this.dialog.open(PreviewFileComponent, {\n        data: {\n          file\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        console.log('The dialog was closed');\n        console.log(result);\n      });\n    }\n    setTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = true;\n      tableData.tableId = this.TableId;\n      tableData.totalRecords = this.declarationList.length;\n      tableData.data = this.declarationList.map(x => {\n        var cells = [];\n        if (!this.isCA) {\n          //RA Search\n          let tempActions = raActionButtons;\n          if (x.status == 'Reopened') {\n            tempActions = raActionButtons.filter(({\n              actionType\n            }) => actionType != 'view' && actionType != 'delete' && actionType != 'history');\n          } else if (x.status != 'Resubmitted') {\n            tempActions = raActionButtons.filter(({\n              actionType\n            }) => actionType != 'history');\n          }\n          cells = [{\n            columnId: 'periodEndYear',\n            value: x.endYear\n          }, {\n            columnId: 'submittedBy',\n            value: x.submittedBy\n          }, {\n            columnId: 'submissionTime',\n            value: x.time\n          }, {\n            columnId: 'declarationStatus',\n            value: x.status\n          }, {\n            columnId: 'esLetter',\n            value: x.esLetter\n          }, {\n            columnId: 'lastSavedDate',\n            value: x.lastSavedDate\n          }, {\n            columnId: 'actions',\n            value: tempActions\n          }];\n        } else {\n          //CA Search TODO once backend code added filter CA action buttons to hide history \n          let tempActions = caActionButtons;\n          if (x.declarationStatus != 'Resubmitted') {\n            tempActions = caActionButtons.filter(({\n              actionType\n            }) => actionType != 'CaHistory');\n          }\n          cells = [{\n            columnId: 'periodEndYear',\n            value: x.endYear\n          }, {\n            columnId: 'submissionTime',\n            value: x.time\n          }, {\n            columnId: 'assessmentStatus',\n            value: x.assessmentStatus\n          }, {\n            columnId: 'esLetter',\n            value: x.esLetter\n          }, {\n            columnId: 'actions',\n            value: tempActions\n          }];\n        }\n        return {\n          id: x.declarationId,\n          rawData: x,\n          cells: cells\n        };\n      });\n      this.tableService.setGridData(tableData);\n    }\n    static {\n      this.ɵfac = function SearchResultHistoryStatusComponent_Factory(t) {\n        return new (t || SearchResultHistoryStatusComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.BasicSearchService), i0.ɵɵdirectiveInject(i3.DeclarationStatusService), i0.ɵɵdirectiveInject(i4.DeclarationService), i0.ɵɵdirectiveInject(i5.SweetAlertService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.ConfigStateService), i0.ɵɵdirectiveInject(i8.ESLetterService), i0.ɵɵdirectiveInject(i9.ToasterService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchResultHistoryStatusComponent,\n        selectors: [[\"app-search-result-history-status\"]],\n        inputs: {\n          entityId: \"entityId\",\n          declarationStatusList: \"declarationStatusList\",\n          assessmentStatusList: \"assessmentStatusList\",\n          isDeleted: \"isDeleted\",\n          fromDashboard: \"fromDashboard\",\n          isAdvancedSearch: \"isAdvancedSearch\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n        decls: 7,\n        vars: 12,\n        consts: [[4, \"ngIf\"], [1, \"divider-margin\"], [\"class\", \"mat-spinner-color history-spinner\", 3, \"mode\", \"diameter\", 4, \"ngIf\"], [\"scrollHeight\", \"24.3em\", \"defaultSortColumnId\", \"periodEndYear\", 3, \"onLazyLoad\", \"onActionClick\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [1, \"left-column-titles\"], [\"class\", \"flex-end\", 4, \"abpPermission\"], [1, \"flex-end\"], [\"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\", \"hidden\"], [1, \"mat-spinner-color\", \"history-spinner\", 3, \"mode\", \"diameter\"]],\n        template: function SearchResultHistoryStatusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, SearchResultHistoryStatusComponent_div_0_Template, 3, 0, \"div\", 0)(1, SearchResultHistoryStatusComponent_div_1_Template, 3, 0, \"div\", 0)(2, SearchResultHistoryStatusComponent_div_2_Template, 2, 1, \"div\", 0);\n            i0.ɵɵelement(3, \"mat-divider\", 1);\n            i0.ɵɵelementStart(4, \"div\");\n            i0.ɵɵtemplate(5, SearchResultHistoryStatusComponent_mat_progress_spinner_5_Template, 1, 2, \"mat-progress-spinner\", 2);\n            i0.ɵɵelementStart(6, \"bdo-table\", 3);\n            i0.ɵɵlistener(\"onLazyLoad\", function SearchResultHistoryStatusComponent_Template_bdo_table_onLazyLoad_6_listener($event) {\n              return ctx.onLazyLoadEvent($event);\n            })(\"onActionClick\", function SearchResultHistoryStatusComponent_Template_bdo_table_onActionClick_6_listener($event) {\n              return ctx.actionButtonClicked($event);\n            })(\"onLinkClick\", function SearchResultHistoryStatusComponent_Template_bdo_table_onLinkClick_6_listener($event) {\n              return ctx.esLetter($event);\n            });\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", !ctx.isCA);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCA);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isCA);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.tableColumns)(\"defaultSortOrder\", \"desc\")(\"pageSize\", ctx.PageSize)(\"isVirtualScroll\", true)(\"hidePagination\", false)(\"rowSelectable\", false)(\"lazyLoad\", true);\n          }\n        },\n        dependencies: [i10.MatDivider, i11.MatButton, i12.MatProgressSpinner, i13.BdoTableComponent, i14.NgIf, i7.PermissionDirective],\n        styles: [\".entity-history-container[_ngcontent-%COMP%]{display:flex;max-width:45em;margin:auto;min-width:100%;overflow:scroll}.divider-margin[_ngcontent-%COMP%]{margin:1em}.left-column-titles[_ngcontent-%COMP%]{font-size:1.5em;color:#00779b;display:block;margin:1em}.history-spinner[_ngcontent-%COMP%]{transform:translate(22em);position:fixed;z-index:101}\"]\n      });\n    }\n  }\n  return SearchResultHistoryStatusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}