{"ast": null, "code": "import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { CoreModule } from '@abp/ng.core';\nimport { CommercialUiModule } from '@volo/abp.commercial.ng.ui';\nimport { ThemeSharedModule } from '@abp/ng.theme.shared';\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\nimport { LpxSideMenuLayoutModule } from '@volosoft/ngx-lepton-x/layouts';\nimport { LpxResponsiveModule } from '@volo/ngx-lepton-x.core';\nimport { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { MatDateFnsModule, DateFnsAdapter, MAT_DATE_FNS_FORMATS } from '@angular/material-date-fns-adapter';\nimport { enCA } from 'date-fns/locale';\nimport { ToastrModule } from 'ngx-toastr';\nimport { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';\nimport { NgxIntlTelInputModule } from 'ngx-intl-tel-input';\nimport { AppMaterialModule } from './material.module';\nimport { BdoTableComponent, BdoTableFooterRowTemplateDirective, BdoTableRowDetailTemplateDirective, BdoToolbarModule, BdoLpxToolbarContainerModule, BdoTableService, BdoSettingsComponent } from './components';\nimport { DateFnsFormatDateTimePipe } from './pipes/date-fns-format-date-pipe.component';\nimport { IfReplaceableTemplateExistsDirective } from './directives';\nimport { LpxBreadcrumbModule } from './components/layout/breadcrumb/breadcrumb.module';\nimport { PhoneNumberComponent } from './components/phone-number/phone-number.component';\nimport { NgxMatDatetimePickerModule, NgxMatNativeDateModule, NgxMatTimepickerModule } from '@angular-material-components/datetime-picker';\nimport { SurveyFilePreviewComponent } from './components/survey-file-preview/survey-file-preview.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-toastr\";\nimport * as i2 from \"@sweetalert2/ngx-sweetalert2\";\nimport * as i3 from \"./components/layout/breadcrumb/breadcrumb.module\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [DatePipe, TitleCasePipe, BdoTableService, DateFnsFormatDateTimePipe, {\n        provide: DateAdapter,\n        useClass: DateFnsAdapter,\n        deps: [MAT_DATE_LOCALE]\n      }, {\n        provide: MAT_DATE_FORMATS,\n        useValue: MAT_DATE_FNS_FORMATS\n      }, {\n        provide: MAT_DATE_LOCALE,\n        useValue: enCA\n      }],\n      imports: [CommonModule, ReactiveFormsModule, RouterModule, AppMaterialModule, MatDateFnsModule, CoreModule, ThemeSharedModule, CommercialUiModule, NgxValidateCoreModule, ToastrModule.forRoot(), SweetAlert2Module.forRoot(), LpxSideMenuLayoutModule, LpxResponsiveModule, LpxBreadcrumbModule.forRoot(),\n      // Custom Right Side Bar  User\n      BdoToolbarModule, BdoLpxToolbarContainerModule, NgxIntlTelInputModule, ReactiveFormsModule, AppMaterialModule, ThemeSharedModule, CommercialUiModule, NgxValidateCoreModule,\n      // Custom Right Side Bar User\n      BdoToolbarModule, BdoLpxToolbarContainerModule, NgxMatDatetimePickerModule, NgxMatNativeDateModule, NgxMatTimepickerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [DateFnsFormatDateTimePipe, IfReplaceableTemplateExistsDirective,\n    // Dynamic table\n    BdoTableComponent, BdoTableRowDetailTemplateDirective, BdoTableFooterRowTemplateDirective, BdoSettingsComponent, PhoneNumberComponent, SurveyFilePreviewComponent],\n    imports: [CommonModule, ReactiveFormsModule, RouterModule, AppMaterialModule, MatDateFnsModule, CoreModule, ThemeSharedModule, CommercialUiModule, NgxValidateCoreModule, i1.ToastrModule, i2.SweetAlert2Module, LpxSideMenuLayoutModule, LpxResponsiveModule, i3.LpxBreadcrumbModule,\n    // Custom Right Side Bar  User\n    BdoToolbarModule, BdoLpxToolbarContainerModule, NgxIntlTelInputModule],\n    exports: [ReactiveFormsModule, AppMaterialModule, ThemeSharedModule, CommercialUiModule, NgxValidateCoreModule, DateFnsFormatDateTimePipe, IfReplaceableTemplateExistsDirective,\n    // Dynamic table\n    BdoTableComponent, BdoTableRowDetailTemplateDirective, BdoTableFooterRowTemplateDirective, BdoSettingsComponent,\n    // Custom Right Side Bar User\n    BdoToolbarModule, BdoLpxToolbarContainerModule, NgxMatDatetimePickerModule, NgxMatNativeDateModule, NgxMatTimepickerModule, SurveyFilePreviewComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "TitleCasePipe", "ReactiveFormsModule", "RouterModule", "CoreModule", "CommercialUiModule", "ThemeSharedModule", "NgxValidateCoreModule", "LpxSideMenuLayoutModule", "LpxResponsiveModule", "DateAdapter", "MAT_DATE_LOCALE", "MAT_DATE_FORMATS", "MatDateFnsModule", "DateFnsAdapter", "MAT_DATE_FNS_FORMATS", "enCA", "ToastrModule", "SweetAlert2Module", "NgxIntlTelInputModule", "AppMaterialModule", "BdoTableComponent", "BdoTableFooterRowTemplateDirective", "BdoTableRowDetailTemplateDirective", "BdoToolbarModule", "BdoLpxToolbarContainerModule", "BdoTableService", "BdoSettingsComponent", "DateFnsFormatDateTimePipe", "IfReplaceableTemplateExistsDirective", "LpxBreadcrumbModule", "PhoneNumberComponent", "NgxMatDatetimePickerModule", "NgxMatNativeDateModule", "NgxMatTimepickerModule", "SurveyFilePreviewComponent", "SharedModule", "provide", "useClass", "deps", "useValue", "imports", "forRoot", "declarations", "i1", "i2", "i3", "exports"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["\r\nimport { NgModule } from '@angular/core';\r\nimport { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { CoreModule } from '@abp/ng.core';\r\nimport { CommercialUiModule } from '@volo/abp.commercial.ng.ui';\r\nimport { ThemeSharedModule } from '@abp/ng.theme.shared';\r\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\r\nimport { LpxSideMenuLayoutModule } from '@volosoft/ngx-lepton-x/layouts';\r\nimport { LpxResponsiveModule, } from '@volo/ngx-lepton-x.core';\r\nimport { } from '@volosoft/abp.ng.theme.lepton-x';\r\n\r\nimport { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';\r\nimport { MatDateFnsModule, DateFnsAdapter, MAT_DATE_FNS_FORMATS } from '@angular/material-date-fns-adapter';\r\nimport { enCA } from 'date-fns/locale';\r\n\r\nimport { ToastrModule } from 'ngx-toastr';\r\nimport { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';\r\nimport { NgxIntlTelInputModule } from 'ngx-intl-tel-input';\r\n\r\nimport { AppMaterialModule } from './material.module';\r\nimport {\r\n    BdoTableComponent,\r\n    BdoTableFooterRowTemplateDirective, BdoTableRowDetailTemplateDirective,\r\n    BdoToolbarModule, BdoLpxToolbarContainerModule, BdoTableService, BdoSettingsComponent\r\n} from './components';\r\nimport { DateFnsFormatDateTimePipe } from './pipes/date-fns-format-date-pipe.component';\r\nimport { IfReplaceableTemplateExistsDirective } from './directives';\r\nimport { LpxBreadcrumbModule } from './components/layout/breadcrumb/breadcrumb.module';\r\nimport { PhoneNumberComponent } from './components/phone-number/phone-number.component';\r\nimport {NgxMatDatetimePickerModule, NgxMatNativeDateModule, NgxMatTimepickerModule} from '@angular-material-components/datetime-picker';\r\nimport { SurveyFilePreviewComponent } from './components/survey-file-preview/survey-file-preview.component';\r\n\r\n@NgModule({\r\n\r\n    imports: [\r\n\r\n        CommonModule,\r\n\r\n        ReactiveFormsModule,\r\n        RouterModule,\r\n\r\n        AppMaterialModule,\r\n        MatDateFnsModule,\r\n\r\n        CoreModule,\r\n        ThemeSharedModule,\r\n        CommercialUiModule,\r\n        NgxValidateCoreModule,\r\n        ToastrModule.forRoot(),\r\n        SweetAlert2Module.forRoot(),\r\n\r\n        LpxSideMenuLayoutModule,\r\n        LpxResponsiveModule,\r\n        LpxBreadcrumbModule.forRoot(),\r\n\r\n        // Custom Right Side Bar  User\r\n        BdoToolbarModule,\r\n        BdoLpxToolbarContainerModule,\r\n        NgxIntlTelInputModule\r\n    ],\r\n\r\n    declarations: [\r\n\r\n        DateFnsFormatDateTimePipe,\r\n\r\n        IfReplaceableTemplateExistsDirective,\r\n\r\n        // Dynamic table\r\n        BdoTableComponent,\r\n        BdoTableRowDetailTemplateDirective,\r\n        BdoTableFooterRowTemplateDirective,\r\n\r\n        BdoSettingsComponent,\r\n        PhoneNumberComponent,\r\n        SurveyFilePreviewComponent\r\n    ],\r\n\r\n    providers: [\r\n\r\n        DatePipe,\r\n        TitleCasePipe,\r\n        BdoTableService,\r\n        DateFnsFormatDateTimePipe,\r\n\r\n        { provide: DateAdapter, useClass: DateFnsAdapter, deps: [MAT_DATE_LOCALE] },\r\n        { provide: MAT_DATE_FORMATS, useValue: MAT_DATE_FNS_FORMATS },\r\n        { provide: MAT_DATE_LOCALE, useValue: enCA }\r\n    ],\r\n\r\n    exports: [\r\n\r\n        ReactiveFormsModule,\r\n\r\n        AppMaterialModule,\r\n\r\n        ThemeSharedModule,\r\n        CommercialUiModule,\r\n        NgxValidateCoreModule,\r\n\r\n        DateFnsFormatDateTimePipe,\r\n\r\n        IfReplaceableTemplateExistsDirective,\r\n\r\n        // Dynamic table\r\n        BdoTableComponent,\r\n        BdoTableRowDetailTemplateDirective,\r\n        BdoTableFooterRowTemplateDirective,\r\n\r\n        BdoSettingsComponent,\r\n\r\n        // Custom Right Side Bar User\r\n        BdoToolbarModule,\r\n        BdoLpxToolbarContainerModule,\r\n        NgxMatDatetimePickerModule, \r\n        NgxMatNativeDateModule, \r\n        NgxMatTimepickerModule,\r\n        SurveyFilePreviewComponent\r\n    ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,iBAAiB;AACvE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,mBAAmB,QAAS,yBAAyB;AAG9D,SAASC,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,wBAAwB;AACvF,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,oBAAoB,QAAQ,oCAAoC;AAC3G,SAASC,IAAI,QAAQ,iBAAiB;AAEtC,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,qBAAqB,QAAQ,oBAAoB;AAE1D,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SACIC,iBAAiB,EACjBC,kCAAkC,EAAEC,kCAAkC,EACtEC,gBAAgB,EAAEC,4BAA4B,EAAEC,eAAe,EAAEC,oBAAoB,QAClF,cAAc;AACrB,SAASC,yBAAyB,QAAQ,6CAA6C;AACvF,SAASC,oCAAoC,QAAQ,cAAc;AACnE,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAAQC,0BAA0B,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAO,8CAA8C;AACvI,SAASC,0BAA0B,QAAQ,gEAAgE;;;;;AAyF3G,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;iBA1CV,CAEPpC,QAAQ,EACRC,aAAa,EACbyB,eAAe,EACfE,yBAAyB,EAEzB;QAAES,OAAO,EAAE3B,WAAW;QAAE4B,QAAQ,EAAExB,cAAc;QAAEyB,IAAI,EAAE,CAAC5B,eAAe;MAAC,CAAE,EAC3E;QAAE0B,OAAO,EAAEzB,gBAAgB;QAAE4B,QAAQ,EAAEzB;MAAoB,CAAE,EAC7D;QAAEsB,OAAO,EAAE1B,eAAe;QAAE6B,QAAQ,EAAExB;MAAI,CAAE,CAC/C;MAAAyB,OAAA,GAnDG1C,YAAY,EAEZG,mBAAmB,EACnBC,YAAY,EAEZiB,iBAAiB,EACjBP,gBAAgB,EAEhBT,UAAU,EACVE,iBAAiB,EACjBD,kBAAkB,EAClBE,qBAAqB,EACrBU,YAAY,CAACyB,OAAO,EAAE,EACtBxB,iBAAiB,CAACwB,OAAO,EAAE,EAE3BlC,uBAAuB,EACvBC,mBAAmB,EACnBqB,mBAAmB,CAACY,OAAO,EAAE;MAE7B;MACAlB,gBAAgB,EAChBC,4BAA4B,EAC5BN,qBAAqB,EAiCrBjB,mBAAmB,EAEnBkB,iBAAiB,EAEjBd,iBAAiB,EACjBD,kBAAkB,EAClBE,qBAAqB;MAarB;MACAiB,gBAAgB,EAChBC,4BAA4B,EAC5BO,0BAA0B,EAC1BC,sBAAsB,EACtBC,sBAAsB;IAAA;EAAA;;;2EAIjBE,YAAY;IAAAO,YAAA,GAxDjBf,yBAAyB,EAEzBC,oCAAoC;IAEpC;IACAR,iBAAiB,EACjBE,kCAAkC,EAClCD,kCAAkC,EAElCK,oBAAoB,EACpBI,oBAAoB,EACpBI,0BAA0B;IAAAM,OAAA,GAtC1B1C,YAAY,EAEZG,mBAAmB,EACnBC,YAAY,EAEZiB,iBAAiB,EACjBP,gBAAgB,EAEhBT,UAAU,EACVE,iBAAiB,EACjBD,kBAAkB,EAClBE,qBAAqB,EAAAqC,EAAA,CAAA3B,YAAA,EAAA4B,EAAA,CAAA3B,iBAAA,EAIrBV,uBAAuB,EACvBC,mBAAmB,EAAAqC,EAAA,CAAAhB,mBAAA;IAGnB;IACAN,gBAAgB,EAChBC,4BAA4B,EAC5BN,qBAAqB;IAAA4B,OAAA,GAiCrB7C,mBAAmB,EAEnBkB,iBAAiB,EAEjBd,iBAAiB,EACjBD,kBAAkB,EAClBE,qBAAqB,EAErBqB,yBAAyB,EAEzBC,oCAAoC;IAEpC;IACAR,iBAAiB,EACjBE,kCAAkC,EAClCD,kCAAkC,EAElCK,oBAAoB;IAEpB;IACAH,gBAAgB,EAChBC,4BAA4B,EAC5BO,0BAA0B,EAC1BC,sBAAsB,EACtBC,sBAAsB,EACtBC,0BAA0B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}