{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"ki\", [[\"<PERSON>rok<PERSON>\", \"Hwaĩ-inĩ\"], u, u], u, [[\"K\", \"N\", \"N\", \"N\", \"A\", \"N\", \"N\"], [\"KMA\", \"NTT\", \"NMN\", \"NMT\", \"ART\", \"NMA\", \"NMM\"], [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>ju<PERSON><PERSON>\", \"Njuma<PERSON>hi\"], [\"KMA\", \"NTT\", \"NMN\", \"NMT\", \"ART\", \"NMA\", \"NMM\"]], u, [[\"J\", \"K\", \"G\", \"K\", \"G\", \"G\", \"M\", \"K\", \"K\", \"I\", \"I\", \"D\"], [\"JEN\", \"WKR\", \"WGT\", \"WKN\", \"WTN\", \"WTD\", \"WMJ\", \"WNN\", \"WKD\", \"WIK\", \"WMW\", \"DIT\"], [\"Njenuarĩ\", \"Mwere wa kerĩ\", \"Mwere wa gatatũ\", \"Mwere wa kana\", \"Mwere wa gatano\", \"Mwere wa gatandatũ\", \"Mwere wa mũgwanja\", \"Mwere wa kanana\", \"Mwere wa kenda\", \"Mwere wa ikũmi\", \"Mwere wa ikũmi na ũmwe\", \"Ndithemba\"]], u, [[\"MK\", \"TK\"], u, [\"Mbere ya Kristo\", \"Thutha wa Kristo\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"KES\", \"Ksh\", \"Ciringi ya Kenya\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KES\": [\"Ksh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ki.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"ki\", [[\"<PERSON>rok<PERSON>\", \"Hwaĩ-inĩ\"], u, u], u, [[\"K\", \"N\", \"N\", \"N\", \"A\", \"N\", \"N\"], [\"KMA\", \"NTT\", \"NMN\", \"NMT\", \"ART\", \"NMA\", \"NMM\"], [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>ju<PERSON><PERSON>\", \"Njuma<PERSON>hi\"], [\"KMA\", \"NTT\", \"NMN\", \"NMT\", \"ART\", \"NMA\", \"NMM\"]], u, [[\"J\", \"K\", \"G\", \"K\", \"G\", \"G\", \"M\", \"K\", \"K\", \"I\", \"I\", \"D\"], [\"JEN\", \"WKR\", \"WGT\", \"WKN\", \"WTN\", \"WTD\", \"WMJ\", \"WNN\", \"WKD\", \"WIK\", \"WMW\", \"DIT\"], [\"Njenuarĩ\", \"Mwere wa kerĩ\", \"Mwere wa gatatũ\", \"Mwere wa kana\", \"Mwere wa gatano\", \"Mwere wa gatandatũ\", \"Mwere wa mũgwanja\", \"Mwere wa kanana\", \"Mwere wa kenda\", \"Mwere wa ikũmi\", \"Mwere wa ikũmi na ũmwe\", \"Ndithemba\"]], u, [[\"MK\", \"TK\"], u, [\"Mbere ya Kristo\", \"Thutha wa Kristo\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"KES\", \"Ksh\", \"Ciringi ya Kenya\", { \"JPY\": [\"JP¥\", \"¥\"], \"KES\": [\"Ksh\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,WAAW,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}