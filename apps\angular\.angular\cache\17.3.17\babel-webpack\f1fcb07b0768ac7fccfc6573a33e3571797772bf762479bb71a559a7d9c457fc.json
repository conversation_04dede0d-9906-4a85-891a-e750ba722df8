{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport Chart from 'chart.js/auto';\nimport { DashboardListingType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service\";\nimport * as i2 from \"../../services/ca-dashboard-service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/divider\";\nimport * as i5 from \"@angular/material/card\";\n/**\n * Rendering \"ASSESSMENTS WITH INFORMATION REQUIRED/RECEIVED FROM RAs\" widget in\n * \"Statistics\" Tab in CA Dashboard page.\n *  */\nexport class AssessmentWithInfoFromRAsComponent extends AppComponentBase {\n  constructor(injector, CADashboardController, CADashBoardService, router) {\n    super(injector);\n    this.CADashboardController = CADashboardController;\n    this.CADashBoardService = CADashBoardService;\n    this.router = router;\n    this.informationRequested = [];\n    this.informationReceived = [];\n    this.dueDatePassed = [];\n    this.years = [];\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.dashboardData && this.dashboardData) {\n      this.selectedYear = this.dashboardData.fiscalYear;\n      this.getAssesmentData();\n    }\n  }\n  getAssesmentData() {\n    this.years = [];\n    this.informationRequested = [];\n    this.informationReceived = [];\n    this.dueDatePassed = [];\n    this.CADashboardController.getMainInformationRequestOverviewByYear(this.selectedYear).subscribe(result => {\n      result.forEach(element => {\n        if (element.fiscalYear != 0) {\n          this.years.push(element.fiscalYear);\n        } else {\n          this.years.push(\"Other\");\n        }\n        this.informationRequested.push({\n          year: element.fiscalYear,\n          count: element.numOfInformationRequired_4_4\n        });\n        this.informationReceived.push({\n          year: element.fiscalYear,\n          count: element.numOfInformationReceived_4_4\n        });\n        this.dueDatePassed.push({\n          year: element.fiscalYear,\n          count: element.numOfInformationRequestedOverDue_4_4\n        });\n      });\n      this.generateAssesmentChart();\n    });\n  }\n  generateAssesmentChart() {\n    if (this.chart) {\n      this.chart.data.labels = this.years;\n      this.chart.data.datasets.forEach(element => {\n        if (element.label === \"Information Requested\" /* DashboardConstants.INFO_REQESTED */) {\n          element.data = this.informationRequested.map(row => row.count);\n        }\n        if (element.label === \"Information Received\" /* DashboardConstants.INFO_RECEIVED */) {\n          element.data = this.informationReceived.map(row => row.count);\n        }\n        if (element.label === \"Due Date Passed\" /* DashboardConstants.DUE_DATE_PASSED */) {\n          element.data = this.dueDatePassed.map(row => row.count);\n        }\n      });\n      this.chart.update();\n    } else {\n      this.chart = new Chart(document.getElementById('assesmentsWithInfoReceivedChart'), {\n        type: 'bar',\n        data: {\n          labels: this.years,\n          datasets: [{\n            label: \"Information Requested\" /* DashboardConstants.INFO_REQESTED */,\n            data: this.informationRequested.map(row => row.count),\n            backgroundColor: [\"#B71C1C\"],\n            minBarLength: 2\n          }, {\n            label: \"Information Received\" /* DashboardConstants.INFO_RECEIVED */,\n            data: this.informationReceived.map(row => row.count),\n            backgroundColor: [\"#004D40\"],\n            minBarLength: 2\n          }, {\n            label: \"Due Date Passed\" /* DashboardConstants.DUE_DATE_PASSED */,\n            data: this.dueDatePassed.map(row => row.count),\n            backgroundColor: [\"#2525ff\"],\n            minBarLength: 2\n          }]\n        },\n        options: {\n          onClick: (event, element) => {\n            this.onClick(event, element);\n          },\n          onHover: (event, element) => {\n            if (element.length == 1) {\n              if (event.native.target instanceof HTMLElement) {\n                event.native.target.style.cursor = 'pointer';\n              }\n            }\n            if (element.length == 0) {\n              if (event.native.target instanceof HTMLElement) {\n                event.native.target.style.cursor = 'default';\n              }\n            }\n          },\n          responsive: true,\n          maintainAspectRatio: false,\n          indexAxis: 'y',\n          plugins: {\n            tooltip: {\n              mode: 'index'\n            }\n          },\n          layout: {\n            padding: {\n              right: 60\n            }\n          }\n        },\n        plugins: [{\n          id: \"barLabel\",\n          afterDatasetsDraw(chart, args, plugins) {\n            const {\n              ctx,\n              data\n            } = chart;\n            ctx.save();\n            for (let i = 0; i < data.datasets.length; i++) {\n              chart.getDatasetMeta(i).data.forEach((dataPoint, index) => {\n                if (!chart.getDatasetMeta(i).hidden) {\n                  let dataValue = data.datasets[i].data[index];\n                  ctx.font = \"bold 10px sans-serif\" /* DashboardBarLabelConstants.FONT */;\n                  ctx.fillStyle = \"grey\" /* DashboardBarLabelConstants.COLOUR */;\n                  ctx.textAlign = \"left\" /* DashboardBarLabelConstants.ALIGN_LEFT */;\n                  ctx.fillText(dataValue.toString(), dataPoint.x + 1, dataPoint.y);\n                }\n              });\n            }\n          }\n        }]\n      });\n    }\n  }\n  onClick(event, element) {\n    if (element[0]) {\n      let clickedElement = this.chart.data.datasets[element[0].datasetIndex].label;\n      let clickYear = this.years[element[0].index];\n      let dashboardDto = {\n        listingType: 6,\n        fiscalYear: clickYear,\n        maxResultCount: 10\n      };\n      if (clickedElement === \"Information Requested\" /* DashboardConstants.INFO_REQESTED */) {\n        dashboardDto.listingType = DashboardListingType.NumOfInformationRequired_4_4;\n      }\n      if (clickedElement === \"Information Received\" /* DashboardConstants.INFO_RECEIVED */) {\n        dashboardDto.listingType = DashboardListingType.NumOfInformationReceived_4_4;\n      }\n      if (clickedElement === \"Due Date Passed\" /* DashboardConstants.DUE_DATE_PASSED */) {\n        dashboardDto.listingType = DashboardListingType.NumOfInformationRequestedOverDue_4_4;\n      }\n      if (clickYear === 'Other') {\n        dashboardDto.fiscalYear = 0;\n        dashboardDto.selectedYear = this.selectedYear;\n      } else {\n        dashboardDto.fiscalYear = clickYear;\n      }\n      this.router.navigate(['/search-result'], {\n        queryParams: {\n          source: \"dashboard\" /* DashboardConstants.DASHBOARD */,\n          type: \"Information, Requested and Received\" /* DashboardConstants.INFO_REQUESTED_RECEIVED */,\n          year: dashboardDto.fiscalYear,\n          listingType: dashboardDto.listingType,\n          selectedYear: dashboardDto.selectedYear\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function AssessmentWithInfoFromRAsComponent_Factory(t) {\n      return new (t || AssessmentWithInfoFromRAsComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.CADashboardContorllerService), i0.ɵɵdirectiveInject(i2.CADashboardService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AssessmentWithInfoFromRAsComponent,\n      selectors: [[\"app-assessments-with-info-from-ras\"]],\n      inputs: {\n        dashboardData: \"dashboardData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"dashboard-card-title\"], [1, \"divider-margin\"], [1, \"chart-container\"], [\"id\", \"assesmentsWithInfoReceivedChart\"]],\n      template: function AssessmentWithInfoFromRAsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n          i0.ɵɵtext(3, \"ASSESSMENTS WITH INFORMATION REQUIRED/RECEIVED FROM RAs\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-content\");\n          i0.ɵɵelement(5, \"mat-divider\", 1);\n          i0.ɵɵelementStart(6, \"div\", 2);\n          i0.ɵɵelement(7, \"canvas\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"h5\");\n          i0.ɵɵtext(9, \"Excludes declaration data filed in OTAS\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i4.MatDivider, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle],\n      styles: [\".chart-container{\\n    position: relative;\\n    min-height: 20em;\\n    min-width: 30em;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2Vzc21lbnRzLXdpdGgtaW5mby1mcm9tLXJhcy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksa0JBQWtCO0lBQ2xCLGdCQUFnQjtJQUNoQixlQUFlO0FBQ25CIiwiZmlsZSI6ImFzc2Vzc21lbnRzLXdpdGgtaW5mby1mcm9tLXJhcy5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmNoYXJ0LWNvbnRhaW5lcntcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIG1pbi1oZWlnaHQ6IDIwZW07XHJcbiAgICBtaW4td2lkdGg6IDMwZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvc3RhdGlzdGljcy1jaGFydHMvYXNzZXNzbWVudHMtd2l0aC1pbmZvLWZyb20tcmFzLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLGVBQWU7QUFDbkI7QUFDQSw0ZEFBNGQiLCJzb3VyY2VzQ29udGVudCI6WyIuY2hhcnQtY29udGFpbmVye1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgbWluLWhlaWdodDogMjBlbTtcclxuICAgIG1pbi13aWR0aDogMzBlbTtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "Chart", "DashboardListingType", "AssessmentWithInfoFromRAsComponent", "constructor", "injector", "CADashboardController", "CADashBoardService", "router", "informationRequested", "informationReceived", "dueDatePassed", "years", "ngOnInit", "ngOnChanges", "changes", "dashboardData", "selected<PERSON>ear", "fiscalYear", "getAssesmentData", "getMainInformationRequestOverviewByYear", "subscribe", "result", "for<PERSON>ach", "element", "push", "year", "count", "numOfInformationRequired_4_4", "numOfInformationReceived_4_4", "numOfInformationRequestedOverDue_4_4", "generateAssesmentChart", "chart", "data", "labels", "datasets", "label", "map", "row", "update", "document", "getElementById", "type", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "onClick", "event", "onHover", "length", "native", "target", "HTMLElement", "style", "cursor", "responsive", "maintainAspectRatio", "indexAxis", "plugins", "tooltip", "mode", "layout", "padding", "right", "id", "afterDatasetsDraw", "args", "ctx", "save", "i", "getDatasetMeta", "dataPoint", "index", "hidden", "dataValue", "font", "fillStyle", "textAlign", "fillText", "toString", "x", "y", "clickedElement", "datasetIndex", "clickYear", "dashboardDto", "listingType", "maxResultCount", "NumOfInformationRequired_4_4", "NumOfInformationReceived_4_4", "NumOfInformationRequestedOverDue_4_4", "navigate", "queryParams", "source", "i0", "ɵɵdirectiveInject", "Injector", "i1", "CADashboardContorllerService", "i2", "CADashboardService", "i3", "Router", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "AssessmentWithInfoFromRAsComponent_Template", "rf", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\statistics-charts\\assessments-with-info-from-ras.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\statistics-charts\\assessments-with-info-from-ras.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  Input,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport Chart, { ChartItem, ChartType, Colors } from 'chart.js/auto';\r\nimport{CADashboardContorllerService} from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service'\r\nimport { CADashboardService } from '../../services/ca-dashboard-service';\r\nimport { GetDashboardListingByCountryDto, GetDashboardListingDto, StatisticMainDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { DashboardListingType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { Router } from '@angular/router';\r\nimport { DashboardBarLabelConstants, DashboardConstants } from '@app/shared/constants';\r\n/** \r\n * Rendering \"ASSESSMENTS WITH INFORMATION REQUIRED/RECEIVED FROM RAs\" widget in\r\n * \"Statistics\" Tab in CA Dashboard page.\r\n *  */\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-assessments-with-info-from-ras',\r\n  templateUrl: './assessments-with-info-from-ras.component.html',\r\n  styleUrls: ['./assessments-with-info-from-ras.component.css'],\r\n})\r\nexport class AssessmentWithInfoFromRAsComponent\r\n  extends AppComponentBase\r\n  implements OnInit\r\n{\r\n\r\n  constructor(injector: Injector, private CADashboardController: CADashboardContorllerService, private CADashBoardService: CADashboardService, private router: Router) {\r\n    super(injector);\r\n  }\r\n  @Input() dashboardData: StatisticMainDto;\r\n  selectedYear: number;\r\n  \r\n  informationRequested = [];\r\n  informationReceived = [];\r\n  dueDatePassed = [];\r\n  years = [];\r\n  chart: Chart;\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.dashboardData && this.dashboardData) {\r\n      this.selectedYear = this.dashboardData.fiscalYear;\r\n      this.getAssesmentData();\r\n    }\r\n  }\r\n\r\n  getAssesmentData(): void{\r\n      this.years = [];\r\n      this.informationRequested = [];\r\n      this.informationReceived = [];\r\n      this.dueDatePassed = [];\r\n      \r\n\r\n      this.CADashboardController.getMainInformationRequestOverviewByYear(this.selectedYear).subscribe(result =>{\r\n        result.forEach(element =>{\r\n          if (element.fiscalYear != 0){\r\n            this.years.push(element.fiscalYear);\r\n          }else{\r\n            this.years.push(\"Other\");\r\n          }\r\n          this.informationRequested.push({year: element.fiscalYear,count: element.numOfInformationRequired_4_4 });\r\n          this.informationReceived.push({year: element.fiscalYear,count: element.numOfInformationReceived_4_4});\r\n          this.dueDatePassed.push({year: element.fiscalYear,count: element.numOfInformationRequestedOverDue_4_4 });\r\n        });\r\n        this.generateAssesmentChart();\r\n      });\r\n  }\r\n\r\n  generateAssesmentChart(): void{\r\n    if(this.chart){\r\n      this.chart.data.labels = this.years;\r\n      this.chart.data.datasets.forEach(element =>{\r\n        if(element.label === DashboardConstants.INFO_REQESTED){\r\n          element.data = this.informationRequested.map(row => row.count);\r\n        }\r\n        if(element.label === DashboardConstants.INFO_RECEIVED){\r\n          element.data = this.informationReceived.map(row => row.count);\r\n        }\r\n        if(element.label === DashboardConstants.DUE_DATE_PASSED){\r\n          element.data = this.dueDatePassed.map(row => row.count);\r\n        }\r\n      });\r\n      this.chart.update();\r\n    }\r\n    \r\n    else{\r\n      this.chart = new Chart(\r\n        document.getElementById('assesmentsWithInfoReceivedChart') as ChartItem,\r\n        {\r\n          type: 'bar',\r\n          data: {\r\n            labels: this.years,\r\n            datasets: [\r\n              {\r\n                label: DashboardConstants.INFO_REQESTED,\r\n                data: this.informationRequested.map(row => row.count),\r\n                backgroundColor:[\"#B71C1C\"],\r\n                minBarLength: 2,\r\n              },\r\n              {\r\n                label: DashboardConstants.INFO_RECEIVED,\r\n                data: this.informationReceived.map(row => row.count),\r\n                backgroundColor:[\"#004D40\"],\r\n                minBarLength: 2,\r\n              }\r\n              ,\r\n              {\r\n                label: DashboardConstants.DUE_DATE_PASSED,\r\n                data: this.dueDatePassed.map(row => row.count),\r\n                backgroundColor:[\"#2525ff\"],\r\n                minBarLength: 2,\r\n              },\r\n            ]\r\n          },\r\n          options: {\r\n            onClick: (event, element) => {\r\n              this.onClick(event, element);\r\n            },\r\n            onHover:(event, element) =>{\r\n              if(element.length == 1){\r\n                if (event.native.target instanceof HTMLElement) {\r\n                  event.native.target.style.cursor = 'pointer'\r\n                }\r\n              }\r\n              if(element.length == 0){\r\n                if (event.native.target instanceof HTMLElement) {\r\n                  event.native.target.style.cursor = 'default'\r\n                }\r\n              }\r\n            },\r\n            responsive: true,\r\n            maintainAspectRatio: false,\r\n            indexAxis: 'y',\r\n            plugins:{\r\n              tooltip:{\r\n                mode:'index'\r\n              }\r\n            },\r\n            layout:{\r\n              padding:{\r\n                right:60\r\n              }\r\n            }\r\n          },\r\n          plugins: [{\r\n            id: \"barLabel\",\r\n            afterDatasetsDraw(chart,args,plugins){\r\n              const {ctx, data} = chart\r\n              ctx.save();\r\n              for(let i = 0; i < data.datasets.length; i++){\r\n                chart.getDatasetMeta(i).data.forEach((dataPoint, index) =>{\r\n                  if(!chart.getDatasetMeta(i).hidden){\r\n                    let dataValue = data.datasets[i].data[index]\r\n                    ctx.font = DashboardBarLabelConstants.FONT;\r\n                    ctx.fillStyle = DashboardBarLabelConstants.COLOUR;\r\n                    ctx.textAlign = DashboardBarLabelConstants.ALIGN_LEFT;\r\n                    ctx.fillText(dataValue.toString(), dataPoint.x +1, dataPoint.y)\r\n                  }\r\n                })\r\n              }\r\n            }\r\n          }],\r\n        }\r\n      );\r\n    }\r\n  }\r\n\r\n  onClick(event, element): void{\r\n    if (element[0]){\r\n      let clickedElement = this.chart.data.datasets[element[0].datasetIndex].label;\r\n      let clickYear = this.years[element[0].index];\r\n\r\n      let dashboardDto: GetDashboardListingDto = {listingType: 6, fiscalYear:clickYear, maxResultCount: 10}\r\n      if(clickedElement === DashboardConstants.INFO_REQESTED){\r\n        dashboardDto.listingType = DashboardListingType.NumOfInformationRequired_4_4\r\n      }\r\n      if(clickedElement === DashboardConstants.INFO_RECEIVED){\r\n        dashboardDto.listingType = DashboardListingType.NumOfInformationReceived_4_4\r\n      }\r\n      if(clickedElement === DashboardConstants.DUE_DATE_PASSED){\r\n        dashboardDto.listingType = DashboardListingType.NumOfInformationRequestedOverDue_4_4\r\n      }\r\n      if(clickYear === 'Other'){\r\n        dashboardDto.fiscalYear = 0;\r\n        dashboardDto.selectedYear = this.selectedYear;\r\n      }else{\r\n        dashboardDto.fiscalYear = clickYear;\r\n      }\r\n      this.router.navigate(['/search-result'], { queryParams: {source: DashboardConstants.DASHBOARD, type: DashboardConstants.INFO_REQUESTED_RECEIVED, year: dashboardDto.fiscalYear, listingType: dashboardDto.listingType, selectedYear: dashboardDto.selectedYear}});\r\n    }\r\n  }\r\n\r\n}\r\n", "<mat-card>\r\n  <mat-card-header>\r\n    <mat-card-title class=\"dashboard-card-title\">ASSESSMENTS WITH INFORMATION REQUIRED/RECEIVED FROM RAs</mat-card-title>\r\n  </mat-card-header>\r\n  <mat-card-content>\r\n    <mat-divider class=\"divider-margin\"></mat-divider>\r\n    <div class=\"chart-container\">\r\n      <canvas id=\"assesmentsWithInfoReceivedChart\"></canvas>\r\n    </div>\r\n    <h5>Excludes declaration data filed in OTAS</h5>\r\n  </mat-card-content>\r\n</mat-card>\r\n"], "mappings": "AAQA,SAASA,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAOC,KAAuC,MAAM,eAAe;AAInE,SAASC,oBAAoB,QAAQ,4FAA4F;;;;;;;AAGjI;;;;AAUA,OAAM,MAAOC,kCACX,SAAQH,gBAAgB;EAIxBI,YAAYC,QAAkB,EAAUC,qBAAmD,EAAUC,kBAAsC,EAAUC,MAAc;IACjK,KAAK,CAACH,QAAQ,CAAC;IADuB,KAAAC,qBAAqB,GAArBA,qBAAqB;IAAwC,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAM3J,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,mBAAmB,GAAG,EAAE;IACxB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,KAAK,GAAG,EAAE;EAPV;EAUAC,QAAQA,CAAA,GACR;EAGAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,EAAE;MAC/C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU;MACjD,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACZ,IAAI,CAACP,KAAK,GAAG,EAAE;IACf,IAAI,CAACH,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,aAAa,GAAG,EAAE;IAGvB,IAAI,CAACL,qBAAqB,CAACc,uCAAuC,CAAC,IAAI,CAACH,YAAY,CAAC,CAACI,SAAS,CAACC,MAAM,IAAG;MACvGA,MAAM,CAACC,OAAO,CAACC,OAAO,IAAG;QACvB,IAAIA,OAAO,CAACN,UAAU,IAAI,CAAC,EAAC;UAC1B,IAAI,CAACN,KAAK,CAACa,IAAI,CAACD,OAAO,CAACN,UAAU,CAAC;QACrC,CAAC,MAAI;UACH,IAAI,CAACN,KAAK,CAACa,IAAI,CAAC,OAAO,CAAC;QAC1B;QACA,IAAI,CAAChB,oBAAoB,CAACgB,IAAI,CAAC;UAACC,IAAI,EAAEF,OAAO,CAACN,UAAU;UAACS,KAAK,EAAEH,OAAO,CAACI;QAA4B,CAAE,CAAC;QACvG,IAAI,CAAClB,mBAAmB,CAACe,IAAI,CAAC;UAACC,IAAI,EAAEF,OAAO,CAACN,UAAU;UAACS,KAAK,EAAEH,OAAO,CAACK;QAA4B,CAAC,CAAC;QACrG,IAAI,CAAClB,aAAa,CAACc,IAAI,CAAC;UAACC,IAAI,EAAEF,OAAO,CAACN,UAAU;UAACS,KAAK,EAAEH,OAAO,CAACM;QAAoC,CAAE,CAAC;MAC1G,CAAC,CAAC;MACF,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,CAAC;EACN;EAEAA,sBAAsBA,CAAA;IACpB,IAAG,IAAI,CAACC,KAAK,EAAC;MACZ,IAAI,CAACA,KAAK,CAACC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACtB,KAAK;MACnC,IAAI,CAACoB,KAAK,CAACC,IAAI,CAACE,QAAQ,CAACZ,OAAO,CAACC,OAAO,IAAG;QACzC,IAAGA,OAAO,CAACY,KAAK,qEAAsC;UACpDZ,OAAO,CAACS,IAAI,GAAG,IAAI,CAACxB,oBAAoB,CAAC4B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,KAAK,CAAC;QAChE;QACA,IAAGH,OAAO,CAACY,KAAK,oEAAsC;UACpDZ,OAAO,CAACS,IAAI,GAAG,IAAI,CAACvB,mBAAmB,CAAC2B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,KAAK,CAAC;QAC/D;QACA,IAAGH,OAAO,CAACY,KAAK,iEAAwC;UACtDZ,OAAO,CAACS,IAAI,GAAG,IAAI,CAACtB,aAAa,CAAC0B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,KAAK,CAAC;QACzD;MACF,CAAC,CAAC;MACF,IAAI,CAACK,KAAK,CAACO,MAAM,EAAE;IACrB,CAAC,MAEG;MACF,IAAI,CAACP,KAAK,GAAG,IAAI/B,KAAK,CACpBuC,QAAQ,CAACC,cAAc,CAAC,iCAAiC,CAAc,EACvE;QACEC,IAAI,EAAE,KAAK;QACXT,IAAI,EAAE;UACJC,MAAM,EAAE,IAAI,CAACtB,KAAK;UAClBuB,QAAQ,EAAE,CACR;YACEC,KAAK;YACLH,IAAI,EAAE,IAAI,CAACxB,oBAAoB,CAAC4B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,KAAK,CAAC;YACrDgB,eAAe,EAAC,CAAC,SAAS,CAAC;YAC3BC,YAAY,EAAE;WACf,EACD;YACER,KAAK;YACLH,IAAI,EAAE,IAAI,CAACvB,mBAAmB,CAAC2B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,KAAK,CAAC;YACpDgB,eAAe,EAAC,CAAC,SAAS,CAAC;YAC3BC,YAAY,EAAE;WACf,EAED;YACER,KAAK;YACLH,IAAI,EAAE,IAAI,CAACtB,aAAa,CAAC0B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,KAAK,CAAC;YAC9CgB,eAAe,EAAC,CAAC,SAAS,CAAC;YAC3BC,YAAY,EAAE;WACf;SAEJ;QACDC,OAAO,EAAE;UACPC,OAAO,EAAEA,CAACC,KAAK,EAAEvB,OAAO,KAAI;YAC1B,IAAI,CAACsB,OAAO,CAACC,KAAK,EAAEvB,OAAO,CAAC;UAC9B,CAAC;UACDwB,OAAO,EAACA,CAACD,KAAK,EAAEvB,OAAO,KAAI;YACzB,IAAGA,OAAO,CAACyB,MAAM,IAAI,CAAC,EAAC;cACrB,IAAIF,KAAK,CAACG,MAAM,CAACC,MAAM,YAAYC,WAAW,EAAE;gBAC9CL,KAAK,CAACG,MAAM,CAACC,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,SAAS;cAC9C;YACF;YACA,IAAG9B,OAAO,CAACyB,MAAM,IAAI,CAAC,EAAC;cACrB,IAAIF,KAAK,CAACG,MAAM,CAACC,MAAM,YAAYC,WAAW,EAAE;gBAC9CL,KAAK,CAACG,MAAM,CAACC,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,SAAS;cAC9C;YACF;UACF,CAAC;UACDC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,SAAS,EAAE,GAAG;UACdC,OAAO,EAAC;YACNC,OAAO,EAAC;cACNC,IAAI,EAAC;;WAER;UACDC,MAAM,EAAC;YACLC,OAAO,EAAC;cACNC,KAAK,EAAC;;;SAGX;QACDL,OAAO,EAAE,CAAC;UACRM,EAAE,EAAE,UAAU;UACdC,iBAAiBA,CAACjC,KAAK,EAACkC,IAAI,EAACR,OAAO;YAClC,MAAM;cAACS,GAAG;cAAElC;YAAI,CAAC,GAAGD,KAAK;YACzBmC,GAAG,CAACC,IAAI,EAAE;YACV,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,IAAI,CAACE,QAAQ,CAACc,MAAM,EAAEoB,CAAC,EAAE,EAAC;cAC3CrC,KAAK,CAACsC,cAAc,CAACD,CAAC,CAAC,CAACpC,IAAI,CAACV,OAAO,CAAC,CAACgD,SAAS,EAAEC,KAAK,KAAI;gBACxD,IAAG,CAACxC,KAAK,CAACsC,cAAc,CAACD,CAAC,CAAC,CAACI,MAAM,EAAC;kBACjC,IAAIC,SAAS,GAAGzC,IAAI,CAACE,QAAQ,CAACkC,CAAC,CAAC,CAACpC,IAAI,CAACuC,KAAK,CAAC;kBAC5CL,GAAG,CAACQ,IAAI;kBACRR,GAAG,CAACS,SAAS;kBACbT,GAAG,CAACU,SAAS;kBACbV,GAAG,CAACW,QAAQ,CAACJ,SAAS,CAACK,QAAQ,EAAE,EAAER,SAAS,CAACS,CAAC,GAAE,CAAC,EAAET,SAAS,CAACU,CAAC,CAAC;gBACjE;cACF,CAAC,CAAC;YACJ;UACF;SACD;OACF,CACF;IACH;EACF;EAEAnC,OAAOA,CAACC,KAAK,EAAEvB,OAAO;IACpB,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAC;MACb,IAAI0D,cAAc,GAAG,IAAI,CAAClD,KAAK,CAACC,IAAI,CAACE,QAAQ,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC2D,YAAY,CAAC,CAAC/C,KAAK;MAC5E,IAAIgD,SAAS,GAAG,IAAI,CAACxE,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC,CAACgD,KAAK,CAAC;MAE5C,IAAIa,YAAY,GAA2B;QAACC,WAAW,EAAE,CAAC;QAAEpE,UAAU,EAACkE,SAAS;QAAEG,cAAc,EAAE;MAAE,CAAC;MACrG,IAAGL,cAAc,qEAAsC;QACrDG,YAAY,CAACC,WAAW,GAAGpF,oBAAoB,CAACsF,4BAA4B;MAC9E;MACA,IAAGN,cAAc,oEAAsC;QACrDG,YAAY,CAACC,WAAW,GAAGpF,oBAAoB,CAACuF,4BAA4B;MAC9E;MACA,IAAGP,cAAc,iEAAwC;QACvDG,YAAY,CAACC,WAAW,GAAGpF,oBAAoB,CAACwF,oCAAoC;MACtF;MACA,IAAGN,SAAS,KAAK,OAAO,EAAC;QACvBC,YAAY,CAACnE,UAAU,GAAG,CAAC;QAC3BmE,YAAY,CAACpE,YAAY,GAAG,IAAI,CAACA,YAAY;MAC/C,CAAC,MAAI;QACHoE,YAAY,CAACnE,UAAU,GAAGkE,SAAS;MACrC;MACA,IAAI,CAAC5E,MAAM,CAACmF,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;QAAEC,WAAW,EAAE;UAACC,MAAM;UAAgCnD,IAAI;UAA8ChB,IAAI,EAAE2D,YAAY,CAACnE,UAAU;UAAEoE,WAAW,EAAED,YAAY,CAACC,WAAW;UAAErE,YAAY,EAAEoE,YAAY,CAACpE;QAAY;MAAC,CAAC,CAAC;IACnQ;EACF;;;uBA5KWd,kCAAkC,EAAA2F,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,4BAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,kBAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlCnG,kCAAkC;MAAAoG,SAAA;MAAAC,MAAA;QAAAxF,aAAA;MAAA;MAAAyF,QAAA,GAAAX,EAAA,CAAAY,0BAAA,EAAAZ,EAAA,CAAAa,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAA9C,GAAA;QAAA,IAAA8C,EAAA;UCxB3CnB,EAFJ,CAAAoB,cAAA,eAAU,sBACS,wBAC8B;UAAApB,EAAA,CAAAqB,MAAA,8DAAuD;UACtGrB,EADsG,CAAAsB,YAAA,EAAiB,EACrG;UAClBtB,EAAA,CAAAoB,cAAA,uBAAkB;UAChBpB,EAAA,CAAAuB,SAAA,qBAAkD;UAClDvB,EAAA,CAAAoB,cAAA,aAA6B;UAC3BpB,EAAA,CAAAuB,SAAA,gBAAsD;UACxDvB,EAAA,CAAAsB,YAAA,EAAM;UACNtB,EAAA,CAAAoB,cAAA,SAAI;UAAApB,EAAA,CAAAqB,MAAA,8CAAuC;UAE/CrB,EAF+C,CAAAsB,YAAA,EAAK,EAC/B,EACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}