{"ast": null, "code": "import * as i1 from '@abp/ng.core';\nimport { LOADING_STRATEGY, SubscriptionService, CoreModule } from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ContentChild, Output, Input, NgModule } from '@angular/core';\nimport * as i4 from '@ng-bootstrap/ng-bootstrap';\nimport { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';\nimport * as i5 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationModule } from 'ng-zorro-antd/core/no-animation';\nimport * as i3 from 'ng-zorro-antd/tree';\nimport { NzTreeModule } from 'ng-zorro-antd/tree';\nimport { of } from 'rxjs';\nimport * as i2 from '@angular/common';\nconst _c0 = [\"menu\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction TreeComponent_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeComponent_ng_template_1_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeComponent_ng_template_1_ng_template_2_div_2_4_ng_template_0_Template(rf, ctx) {}\nfunction TreeComponent_ng_template_1_ng_template_2_div_2_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeComponent_ng_template_1_ng_template_2_div_2_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TreeComponent_ng_template_1_ng_template_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 6);\n    i0.ɵɵlistener(\"abpInit\", function TreeComponent_ng_template_1_ng_template_2_div_2_Template_div_abpInit_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const dropdown_r6 = i0.ɵɵreference(1);\n      const node_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.initDropdown(node_r7.key, dropdown_r6));\n    });\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementStart(3, \"div\", 14);\n    i0.ɵɵtemplate(4, TreeComponent_ng_template_1_ng_template_2_div_2_4_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"dropdown-toggle\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.menu)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c1, node_r7));\n  }\n}\nfunction TreeComponent_ng_template_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, TreeComponent_ng_template_1_ng_template_2_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, TreeComponent_ng_template_1_ng_template_2_div_2_Template, 5, 6, \"div\", 11);\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    i0.ɵɵnextContext();\n    const defaultNodeTemplate_r8 = i0.ɵɵreference(5);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.customNodeTemplate ? ctx_r3.customNodeTemplate == null ? null : ctx_r3.customNodeTemplate.template : defaultNodeTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c1, node_r7));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.menu);\n  }\n}\nfunction TreeComponent_ng_template_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(node_r9.title);\n  }\n}\nfunction TreeComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function TreeComponent_ng_template_1_Template_div_click_0_listener() {\n      const node_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSelectedNodeChange(node_r3));\n    });\n    i0.ɵɵtemplate(1, TreeComponent_ng_template_1_ng_container_1_Template, 1, 0, \"ng-container\", 9)(2, TreeComponent_ng_template_1_ng_template_2_Template, 3, 5, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(4, TreeComponent_ng_template_1_ng_template_4_Template, 2, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r3 = ctx.$implicit;\n    const nodeTemplate_r10 = i0.ɵɵreference(3);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r3.isNodeSelected(node_r3));\n    i0.ɵɵproperty(\"title\", node_r3.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", nodeTemplate_r10)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(5, _c1, node_r3));\n  }\n}\nfunction TreeComponent_ng_template_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"i\", 15);\n    i0.ɵɵtemplate(1, TreeComponent_ng_template_3_ng_container_1_Template, 1, 0, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = ctx.$implicit;\n    i0.ɵɵnextContext();\n    const minusIcon_r12 = i0.ɵɵreference(6);\n    const plusIcon_r13 = i0.ɵɵreference(8);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", node_r11.isExpanded ? minusIcon_r12 : plusIcon_r13);\n  }\n}\nfunction TreeComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 17);\n    i0.ɵɵelement(1, \"path\", 18);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TreeComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 17);\n    i0.ɵɵelement(1, \"path\", 19);\n    i0.ɵɵelementEnd();\n  }\n}\nlet TreeNodeTemplateDirective = /*#__PURE__*/(() => {\n  class TreeNodeTemplateDirective {\n    constructor(template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function TreeNodeTemplateDirective_Factory(t) {\n        return new (t || TreeNodeTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: TreeNodeTemplateDirective,\n        selectors: [[\"\", \"abpTreeNodeTemplate\", \"\"], [\"\", \"abp-tree-node-template\", \"\"]]\n      });\n    }\n  }\n  return TreeNodeTemplateDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ExpandedIconTemplateDirective = /*#__PURE__*/(() => {\n  class ExpandedIconTemplateDirective {\n    constructor(template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function ExpandedIconTemplateDirective_Factory(t) {\n        return new (t || ExpandedIconTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: ExpandedIconTemplateDirective,\n        selectors: [[\"\", \"abpTreeExpandedIconTemplate\", \"\"], [\"\", \"abp-tree-expanded-icon-template\", \"\"]]\n      });\n    }\n  }\n  return ExpandedIconTemplateDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst DISABLE_TREE_STYLE_LOADING_TOKEN = new InjectionToken('DISABLE_TREE_STYLE_LOADING_TOKEN');\nlet TreeComponent = /*#__PURE__*/(() => {\n  class TreeComponent {\n    constructor(lazyLoadService, subscriptionService, disableTreeStyleLoading, cdr) {\n      this.lazyLoadService = lazyLoadService;\n      this.subscriptionService = subscriptionService;\n      this.disableTreeStyleLoading = disableTreeStyleLoading;\n      this.cdr = cdr;\n      this.dropdowns = {};\n      this.checkedKeysChange = new EventEmitter();\n      this.expandedKeysChange = new EventEmitter();\n      this.selectedNodeChange = new EventEmitter();\n      this.dropOver = new EventEmitter();\n      this.nzExpandChange = new EventEmitter();\n      this.noAnimation = true;\n      this.checkedKeys = [];\n      this.nodes = [];\n      this.expandedKeys = [];\n      this.isNodeSelected = node => this.selectedNode?.id === node.key;\n      this.beforeDrop = event => {\n        this.dropPosition = event.pos;\n        return of(false);\n      };\n    }\n    ngOnInit() {\n      this.loadStyle();\n    }\n    loadStyle() {\n      if (this.disableTreeStyleLoading) {\n        return;\n      }\n      const loaded$ = this.lazyLoadService.load(LOADING_STRATEGY.AppendAnonymousStyleToHead('ng-zorro-antd-tree.css'));\n      this.subscriptionService.addOne(loaded$);\n    }\n    findNode(target, nodes) {\n      for (const node of nodes) {\n        if (node.key === target.id) {\n          return node;\n        }\n        if (node.children) {\n          let res = this.findNode(target, node.children);\n          if (res) {\n            return res;\n          }\n        }\n      }\n      return null;\n    }\n    onSelectedNodeChange(node) {\n      this.selectedNode = node.origin.entity;\n      if (this.changeCheckboxWithNode) {\n        let newVal;\n        if (node.isChecked) {\n          newVal = this.checkedKeys.filter(x => x !== node.key);\n        } else {\n          newVal = [...this.checkedKeys, node.key];\n        }\n        this.selectedNodeChange.emit(node);\n        this.checkedKeys = newVal;\n        this.checkedKeysChange.emit(newVal);\n      } else {\n        this.selectedNodeChange.emit(node.origin.entity);\n      }\n    }\n    onCheckboxChange(event) {\n      this.checkedKeys = [...event.keys];\n      this.checkedKeysChange.emit(event.keys);\n    }\n    onExpandedKeysChange(event) {\n      this.expandedKeys = [...event.keys];\n      this.expandedKeysChange.emit(event.keys);\n      this.nzExpandChange.emit(event);\n    }\n    onDrop(event) {\n      event.event.stopPropagation();\n      event.event.preventDefault();\n      event.pos = this.dropPosition;\n      this.dropOver.emit(event);\n    }\n    initDropdown(key, dropdown) {\n      this.dropdowns[key] = dropdown;\n    }\n    setSelectedNode(node) {\n      let newSelectedNode = this.findNode(node, this.nodes);\n      this.selectedNode = {\n        ...newSelectedNode\n      };\n      this.cdr.markForCheck();\n    }\n    static {\n      this.ɵfac = function TreeComponent_Factory(t) {\n        return new (t || TreeComponent)(i0.ɵɵdirectiveInject(i1.LazyLoadService), i0.ɵɵdirectiveInject(i1.SubscriptionService), i0.ɵɵdirectiveInject(DISABLE_TREE_STYLE_LOADING_TOKEN, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: TreeComponent,\n        selectors: [[\"abp-tree\"]],\n        contentQueries: function TreeComponent_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, _c0, 5);\n            i0.ɵɵcontentQuery(dirIndex, TreeNodeTemplateDirective, 5);\n            i0.ɵɵcontentQuery(dirIndex, ExpandedIconTemplateDirective, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customNodeTemplate = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.expandedIconTemplate = _t.first);\n          }\n        },\n        inputs: {\n          noAnimation: \"noAnimation\",\n          draggable: \"draggable\",\n          checkable: \"checkable\",\n          checkStrictly: \"checkStrictly\",\n          checkedKeys: \"checkedKeys\",\n          nodes: \"nodes\",\n          expandedKeys: \"expandedKeys\",\n          selectedNode: \"selectedNode\",\n          changeCheckboxWithNode: \"changeCheckboxWithNode\",\n          isNodeSelected: \"isNodeSelected\",\n          beforeDrop: \"beforeDrop\"\n        },\n        outputs: {\n          checkedKeysChange: \"checkedKeysChange\",\n          expandedKeysChange: \"expandedKeysChange\",\n          selectedNodeChange: \"selectedNodeChange\",\n          dropOver: \"dropOver\",\n          nzExpandChange: \"nzExpandChange\"\n        },\n        features: [i0.ɵɵProvidersFeature([SubscriptionService])],\n        decls: 9,\n        vars: 10,\n        consts: [[\"treeTemplate\", \"\"], [\"defaultIconTemplate\", \"\"], [\"minusIcon\", \"\"], [\"plusIcon\", \"\"], [\"nodeTemplate\", \"\"], [\"defaultNodeTemplate\", \"\"], [\"dropdown\", \"ngbDropdown\"], [3, \"nzExpandChange\", \"nzCheckBoxChange\", \"nzOnDrop\", \"nzContextMenu\", \"nzBeforeDrop\", \"nzDraggable\", \"nzCheckStrictly\", \"nzCheckable\", \"nzCheckedKeys\", \"nzData\", \"nzTreeTemplate\", \"nzExpandedKeys\", \"nzExpandedIcon\", \"nzNoAnimation\"], [3, \"click\", \"title\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"d-inline-flex\", \"align-items-center\", \"abp-ellipsis-inline\"], [\"class\", \"d-inline-block ms-1\", \"ngbDropdown\", \"\", \"placement\", \"bottom\", \"container\", \"body\", 3, \"abpInit\", 4, \"ngIf\"], [\"ngbDropdown\", \"\", \"placement\", \"bottom\", \"container\", \"body\", 1, \"d-inline-block\", \"ms-1\", 3, \"abpInit\"], [\"ngbDropdownToggle\", \"\", \"aria-hidden\", \"true\", 1, \"fas\", \"fa-caret-down\", \"text-muted\"], [\"ngbDropdownMenu\", \"\"], [\"aria-hidden\", \"true\"], [4, \"ngTemplateOutlet\"], [\"width\", \"15\", \"height\", \"15\", \"viewBox\", \"0 0 24 24\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill-rule\", \"evenodd\", \"clip-rule\", \"evenodd\"], [\"d\", \"M11.5 0c6.347 0 11.5 5.153 11.5 11.5s-5.153 11.5-11.5 11.5-11.5-5.153-11.5-11.5 5.153-11.5 11.5-11.5zm0 1c5.795 0 10.5 4.705 10.5 10.5s-4.705 10.5-10.5 10.5-10.5-4.705-10.5-10.5 4.705-10.5 10.5-10.5zm-6.5 10h13v1h-13v-1z\"], [\"d\", \"M11.5 0c6.347 0 11.5 5.153 11.5 11.5s-5.153 11.5-11.5 11.5-11.5-5.153-11.5-11.5 5.153-11.5 11.5-11.5zm0 1c5.795 0 10.5 4.705 10.5 10.5s-4.705 10.5-10.5 10.5-10.5-4.705-10.5-10.5 4.705-10.5 10.5-10.5zm.5 10h6v1h-6v6h-1v-6h-6v-1h6v-6h1v6z\"]],\n        template: function TreeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nz-tree\", 7);\n            i0.ɵɵlistener(\"nzExpandChange\", function TreeComponent_Template_nz_tree_nzExpandChange_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onExpandedKeysChange($event));\n            })(\"nzCheckBoxChange\", function TreeComponent_Template_nz_tree_nzCheckBoxChange_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCheckboxChange($event));\n            })(\"nzOnDrop\", function TreeComponent_Template_nz_tree_nzOnDrop_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onDrop($event));\n            })(\"nzContextMenu\", function TreeComponent_Template_nz_tree_nzContextMenu_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.dropdowns[$event.node == null ? null : $event.node.key] == null ? null : ctx.dropdowns[$event.node == null ? null : $event.node.key].toggle());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(1, TreeComponent_ng_template_1_Template, 6, 7, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, TreeComponent_ng_template_3_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, TreeComponent_ng_template_5_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(7, TreeComponent_ng_template_7_Template, 2, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const treeTemplate_r14 = i0.ɵɵreference(2);\n            const defaultIconTemplate_r15 = i0.ɵɵreference(4);\n            i0.ɵɵproperty(\"nzBeforeDrop\", ctx.beforeDrop)(\"nzDraggable\", ctx.draggable)(\"nzCheckStrictly\", ctx.checkStrictly)(\"nzCheckable\", ctx.checkable)(\"nzCheckedKeys\", ctx.checkedKeys)(\"nzData\", ctx.nodes)(\"nzTreeTemplate\", treeTemplate_r14)(\"nzExpandedKeys\", ctx.expandedKeys)(\"nzExpandedIcon\", (ctx.expandedIconTemplate == null ? null : ctx.expandedIconTemplate.template) || defaultIconTemplate_r15)(\"nzNoAnimation\", ctx.noAnimation);\n          }\n        },\n        dependencies: [i2.NgIf, i2.NgTemplateOutlet, i1.InitDirective, i3.NzTreeComponent, i4.NgbDropdown, i4.NgbDropdownToggle, i4.NgbDropdownMenu, i5.NzNoAnimationDirective],\n        styles: [\"abp-tree .ant-tree{color:inherit}abp-tree .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected{background-color:transparent}abp-tree .ant-tree .ant-tree-switcher{line-height:17px;align-items:center;justify-content:center;display:inline-flex}abp-tree .ant-tree .ant-tree-node-content-wrapper{width:100%;padding:0}abp-tree .ant-tree .ant-tree-node-content-wrapper>div{display:flex;padding-right:.5rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}abp-tree .ant-tree .node-wrapper{width:100%;position:relative;display:inline-block;margin:0;line-height:30px;text-decoration:none;vertical-align:top;border-radius:2px;cursor:pointer;padding:0 5px 0 8px;border:1px solid transparent}abp-tree .ant-tree .ellipsis{position:absolute;right:8px;top:1px;cursor:pointer}\\n\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return TreeComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst templates = [TreeNodeTemplateDirective, ExpandedIconTemplateDirective];\nconst exported = [...templates, TreeComponent];\nlet TreeModule = /*#__PURE__*/(() => {\n  class TreeModule {\n    static {\n      this.ɵfac = function TreeModule_Factory(t) {\n        return new (t || TreeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: TreeModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CoreModule, NzTreeModule, NgbDropdownModule, NzNoAnimationModule]\n      });\n    }\n  }\n  return TreeModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass BaseNode {\n  constructor(id, parentId) {\n    this.id = id;\n    this.parentId = parentId;\n  }\n}\nclass TreeNode extends BaseNode {\n  constructor(entity, nameResolver = ent => ent.displayName || ent.name) {\n    super(entity.id, entity.parentId);\n    this.entity = entity;\n    this.nameResolver = nameResolver;\n    this.icon = null;\n    this.children = [];\n    this.isLeaf = true;\n    this.checked = false;\n    this.selected = false;\n    this.expanded = false;\n    this.selectable = true;\n    this.disabled = false;\n    this.disableCheckbox = false;\n    this.key = entity.id;\n    this.title = nameResolver(entity);\n  }\n}\nclass TreeAdapter {\n  constructor(list = []) {\n    this.list = list;\n    this.tree = createTreeFromList(this.list);\n  }\n  getList() {\n    return this.list;\n  }\n  getTree() {\n    return this.tree;\n  }\n  handleDrop({\n    key,\n    parentNode\n  }) {\n    const index = this.list.findIndex(({\n      id\n    }) => id === key);\n    this.list[index].parentId = parentNode ? parentNode.key : null;\n    this.tree = createTreeFromList(this.list);\n  }\n  handleRemove({\n    key\n  }) {\n    this.updateTreeFromList(this.list.filter(item => item.id !== key));\n  }\n  handleUpdate({\n    key,\n    children\n  }) {\n    /**\n     * When we need to update a node with new children, first we need to remove any descendant nodes.\n     * If we remove immediate children and create a new tree, any other descendant nodes will be removed\n     * and we won't need to recursively remove sub children.\n     * Then, you simply add back the new children and create a new tree.\n     */\n    const listWithDescendantNodesRemoved = this.updateTreeFromList(this.list.filter(item => item.parentId !== key));\n    this.updateTreeFromList(listWithDescendantNodesRemoved.concat(children));\n  }\n  updateTreeFromList(list) {\n    this.tree = createTreeFromList(list);\n    this.list = createListFromTree(this.tree);\n    return this.list;\n  }\n}\n// UTILITY FUNCTIONS\nfunction createTreeFromList(list) {\n  const map = createMapFromList(list);\n  const tree = [];\n  list.forEach(row => {\n    const parentId = row.parentId;\n    const node = map.get(row.id);\n    if (parentId) {\n      const parent = map.get(parentId);\n      if (!parent) return;\n      parent.children.push(node);\n      parent.isLeaf = false;\n    } else {\n      tree.push(node);\n    }\n  });\n  return tree;\n}\nfunction createListFromTree(tree, list = []) {\n  tree.forEach(node => {\n    list.push({\n      ...node.entity,\n      parentId: node.parentId\n    });\n    if (node.children) createListFromTree(node.children, list);\n  });\n  return list;\n}\nfunction createMapFromList(list, map = new Map()) {\n  list.forEach(row => map.set(row.id, new TreeNode(row)));\n  return map;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseNode, DISABLE_TREE_STYLE_LOADING_TOKEN, ExpandedIconTemplateDirective, TreeAdapter, TreeComponent, TreeModule, TreeNode, TreeNodeTemplateDirective };\n//# sourceMappingURL=abp-ng.components-tree.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}