{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder dan een seconde',\n    other: 'minder dan {{count}} seconden'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} seconden'\n  },\n  halfAMinute: 'een halve minuut',\n  lessThanXMinutes: {\n    one: 'minder dan een minuut',\n    other: 'minder dan {{count}} minuten'\n  },\n  xMinutes: {\n    one: 'een minuut',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'ongeveer 1 uur',\n    other: 'ongeveer {{count}} uur'\n  },\n  xHours: {\n    one: '1 uur',\n    other: '{{count}} uur'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'ongeveer 1 week',\n    other: 'ongeveer {{count}} weken'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weken'\n  },\n  aboutXMonths: {\n    one: 'ongeveer 1 maand',\n    other: 'ongeveer {{count}} maanden'\n  },\n  xMonths: {\n    one: '1 maand',\n    other: '{{count}} maanden'\n  },\n  aboutXYears: {\n    one: 'ongeveer 1 jaar',\n    other: 'ongeveer {{count}} jaar'\n  },\n  xYears: {\n    one: '1 jaar',\n    other: '{{count}} jaar'\n  },\n  overXYears: {\n    one: 'meer dan 1 jaar',\n    other: 'meer dan {{count}} jaar'\n  },\n  almostXYears: {\n    one: 'bijna 1 jaar',\n    other: 'bijna {{count}} jaar'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'over ' + result;\n    } else {\n      return result + ' geleden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}