{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ca-AD\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    var i = Math.floor(Math.abs(n)),\n      v = n.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0) return 1;\n    return 5;\n  }\n  exports.default = ['ca-AD', [['a. m.', 'p. m.'], u, u], u, [['dg', 'dl', 'dt', 'dc', 'dj', 'dv', 'ds'], ['dg.', 'dl.', 'dt.', 'dc.', 'dj.', 'dv.', 'ds.'], ['diumenge', 'dilluns', 'dimarts', 'dimecres', 'dijous', 'divendres', 'dissabte'], ['dg.', 'dl.', 'dt.', 'dc.', 'dj.', 'dv.', 'ds.']], u, [['GN', 'FB', 'MÇ', 'AB', 'MG', 'JN', 'JL', 'AG', 'ST', 'OC', 'NV', 'DS'], ['de gen.', 'de febr.', 'de març', 'd’abr.', 'de maig', 'de juny', 'de jul.', 'd’ag.', 'de set.', 'd’oct.', 'de nov.', 'de des.'], ['de gener', 'de febrer', 'de març', 'd’abril', 'de maig', 'de juny', 'de juliol', 'd’agost', 'de setembre', 'd’octubre', 'de novembre', 'de desembre']], [['GN', 'FB', 'MÇ', 'AB', 'MG', 'JN', 'JL', 'AG', 'ST', 'OC', 'NV', 'DS'], ['gen.', 'febr.', 'març', 'abr.', 'maig', 'juny', 'jul.', 'ag.', 'set.', 'oct.', 'nov.', 'des.'], ['gener', 'febrer', 'març', 'abril', 'maig', 'juny', 'juliol', 'agost', 'setembre', 'octubre', 'novembre', 'desembre']], [['aC', 'dC'], u, ['abans de Crist', 'després de Crist']], 1, [6, 0], ['d/M/yy', 'd MMM y', 'd MMMM \\'de\\' y', 'EEEE, d MMMM \\'de\\' y'], ['H:mm', 'H:mm:ss', 'H:mm:ss z', 'H:mm:ss zzzz'], ['{1} {0}', '{1}, {0}', '{1} \\'a\\' \\'les\\' {0}', u], [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'], 'EUR', '€', 'euro', {\n    'AUD': ['AU$', '$'],\n    'BRL': [u, 'R$'],\n    'CAD': [u, '$'],\n    'CNY': [u, '¥'],\n    'ESP': ['₧'],\n    'MXN': [u, '$'],\n    'THB': ['฿'],\n    'USD': [u, '$'],\n    'VEF': [u, 'Bs F'],\n    'XCD': [u, '$'],\n    'XXX': []\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "i", "Math", "floor", "abs", "toString", "replace", "length", "default"], "sources": ["c:/Temp/node_modules/@angular/common/locales/ca-AD.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/ca-AD\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        var i = Math.floor(Math.abs(n)), v = n.toString().replace(/^[^.]*\\.?/, '').length;\n        if (i === 1 && v === 0)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'ca-AD',\n        [['a. m.', 'p. m.'], u, u],\n        u,\n        [\n            ['dg', 'dl', 'dt', 'dc', 'dj', 'dv', 'ds'], ['dg.', 'dl.', 'dt.', 'dc.', 'dj.', 'dv.', 'ds.'],\n            ['diumenge', 'dilluns', 'dimarts', 'dimecres', 'dijous', 'divendres', 'dissabte'],\n            ['dg.', 'dl.', 'dt.', 'dc.', 'dj.', 'dv.', 'ds.']\n        ],\n        u,\n        [\n            ['GN', 'FB', 'MÇ', 'AB', 'MG', 'JN', 'JL', 'AG', 'ST', 'OC', 'NV', 'DS'],\n            [\n                'de gen.', 'de febr.', 'de març', 'd’abr.', 'de maig', 'de juny', 'de jul.', 'd’ag.',\n                'de set.', 'd’oct.', 'de nov.', 'de des.'\n            ],\n            [\n                'de gener', 'de febrer', 'de març', 'd’abril', 'de maig', 'de juny', 'de juliol', 'd’agost',\n                'de setembre', 'd’octubre', 'de novembre', 'de desembre'\n            ]\n        ],\n        [\n            ['GN', 'FB', 'MÇ', 'AB', 'MG', 'JN', 'JL', 'AG', 'ST', 'OC', 'NV', 'DS'],\n            [\n                'gen.', 'febr.', 'març', 'abr.', 'maig', 'juny', 'jul.', 'ag.', 'set.', 'oct.', 'nov.', 'des.'\n            ],\n            [\n                'gener', 'febrer', 'març', 'abril', 'maig', 'juny', 'juliol', 'agost', 'setembre', 'octubre',\n                'novembre', 'desembre'\n            ]\n        ],\n        [['aC', 'dC'], u, ['abans de Crist', 'després de Crist']],\n        1,\n        [6, 0],\n        ['d/M/yy', 'd MMM y', 'd MMMM \\'de\\' y', 'EEEE, d MMMM \\'de\\' y'],\n        ['H:mm', 'H:mm:ss', 'H:mm:ss z', 'H:mm:ss zzzz'],\n        ['{1} {0}', '{1}, {0}', '{1} \\'a\\' \\'les\\' {0}', u],\n        [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'],\n        'EUR',\n        '€',\n        'euro',\n        {\n            'AUD': ['AU$', '$'],\n            'BRL': [u, 'R$'],\n            'CAD': [u, '$'],\n            'CNY': [u, '¥'],\n            'ESP': ['₧'],\n            'MXN': [u, '$'],\n            'THB': ['฿'],\n            'USD': [u, '$'],\n            'VEF': [u, 'Bs F'],\n            'XCD': [u, '$'],\n            'XXX': []\n        },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC5E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;MAAEV,CAAC,GAAGU,CAAC,CAACK,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;IACjF,IAAIN,CAAC,KAAK,CAAC,IAAIX,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAD,OAAO,CAACmB,OAAO,GAAG,CACd,OAAO,EACP,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEV,CAAC,EAAEA,CAAC,CAAC,EAC1BA,CAAC,EACD,CACI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAC7F,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,EACjF,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CACpD,EACDA,CAAC,EACD,CACI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxE,CACI,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EACpF,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAC5C,EACD,CACI,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAC3F,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAC3D,CACJ,EACD,CACI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxE,CACI,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CACjG,EACD,CACI,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAC5F,UAAU,EAAE,UAAU,CACzB,CACJ,EACD,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,EACzD,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,uBAAuB,CAAC,EACjE,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EAChD,CAAC,SAAS,EAAE,UAAU,EAAE,uBAAuB,EAAEA,CAAC,CAAC,EACnD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,GAAG,EACH,MAAM,EACN;IACI,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE;EACX,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}