{"ast": null, "code": "import { ListService } from '@abp/ng.core';\nimport { EXTENSIONS_IDENTIFIER } from '@abp/ng.components/extensible';\nimport { AbstractOrganizationUnitComponent, ORGANIZATION_UNIT_CONFIG } from '../abstract-organization-unit/abstract-organization-unit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.components/extensible\";\nimport * as i2 from \"@abp/ng.theme.shared\";\nimport * as i3 from \"./organization-members-modal-body.component\";\nimport * as i4 from \"../selected-organization-unit/selected-organization-unit.component\";\nimport * as i5 from \"@abp/ng.core\";\nfunction OrganizationMembersComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"AbpIdentity::SelectUsers\"));\n  }\n}\nfunction OrganizationMembersComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-organization-members-modal-body\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"checkedUnits\", ctx_r1.checkedUnits)(\"isCheckboxDisabled\", ctx_r1.isCheckboxDisabled);\n  }\n}\nfunction OrganizationMembersComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"abp-button\", 10);\n    i0.ɵɵlistener(\"click\", function OrganizationMembersComponent_ng_template_12_Template_abp_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addUnits());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpIdentity::Cancel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, \"AbpIdentity::Save\"));\n  }\n}\nexport let OrganizationMembersComponent = /*#__PURE__*/(() => {\n  class OrganizationMembersComponent extends AbstractOrganizationUnitComponent {\n    constructor() {\n      super(...arguments);\n      this.modalOption = {\n        size: 'xl'\n      };\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵOrganizationMembersComponent_BaseFactory;\n        return function OrganizationMembersComponent_Factory(t) {\n          return (ɵOrganizationMembersComponent_BaseFactory || (ɵOrganizationMembersComponent_BaseFactory = i0.ɵɵgetInheritedFactory(OrganizationMembersComponent)))(t || OrganizationMembersComponent);\n        };\n      })();\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OrganizationMembersComponent,\n        selectors: [[\"abp-organization-members\"]],\n        features: [i0.ɵɵProvidersFeature([ListService, {\n          provide: EXTENSIONS_IDENTIFIER,\n          useValue: \"Identity.OrganizationMembersComponent\" /* eIdentityComponents.OrganizationMembers */\n        }, {\n          provide: ORGANIZATION_UNIT_CONFIG,\n          useValue: {\n            getCurrentUnitsMethodName: 'getMembers',\n            addUnitsMethodName: 'addMembers',\n            addUnitsBodyPropName: 'userIds',\n            deleteMethodName: 'removeMember',\n            deletionLocalizationKey: 'AbpIdentity::RemoveUserFromOuWarningMessage'\n          }\n        }]), i0.ɵɵInheritDefinitionFeature],\n        decls: 14,\n        vars: 9,\n        consts: [[\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [1, \"py-2\", \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"mb-2\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-plus\", \"me-1\"], [3, \"data\", \"recordsTotal\", \"list\"], [3, \"visibleChange\", \"visible\", \"busy\", \"options\"], [3, \"checkedUnits\", \"isCheckboxDisabled\"], [\"type\", \"button\", \"abpClose\", \"\", 1, \"btn\", \"btn-secondary\"], [\"iconClass\", \"fa fa-check\", 3, \"click\"]],\n        template: function OrganizationMembersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 3);\n            i0.ɵɵelement(1, \"abp-selected-organization-unit\");\n            i0.ɵɵelementStart(2, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function OrganizationMembersComponent_Template_button_click_2_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.openModal());\n            });\n            i0.ɵɵelement(3, \"i\", 5);\n            i0.ɵɵtext(4);\n            i0.ɵɵpipe(5, \"abpLocalization\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(6, \"abp-extensible-table\", 6);\n            i0.ɵɵelementStart(7, \"abp-modal\", 7);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function OrganizationMembersComponent_Template_abp_modal_visibleChange_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.isModalVisible, $event) || (ctx.isModalVisible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(8, OrganizationMembersComponent_ng_template_8_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(10, OrganizationMembersComponent_ng_template_10_Template, 1, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(12, OrganizationMembersComponent_ng_template_12_Template, 6, 6, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 7, \"AbpIdentity::AddMember\"), \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"data\", ctx.currentOrganizationUnits.items)(\"recordsTotal\", ctx.currentOrganizationUnits.totalCount)(\"list\", ctx.list);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.isModalVisible);\n            i0.ɵɵproperty(\"busy\", ctx.isModalBusy)(\"options\", ctx.modalOption);\n          }\n        },\n        dependencies: [i1.ExtensibleTableComponent, i2.ButtonComponent, i2.ModalComponent, i2.ModalCloseDirective, i3.OrganizationMembersModalBodyComponent, i4.SelectedOrganizationUnitComponent, i5.LocalizationPipe],\n        encapsulation: 2\n      });\n    }\n  }\n  return OrganizationMembersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}