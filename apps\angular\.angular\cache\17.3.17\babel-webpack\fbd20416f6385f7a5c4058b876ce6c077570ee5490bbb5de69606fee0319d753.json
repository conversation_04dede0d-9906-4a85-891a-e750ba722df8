{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"rof\", [[\"kang’ama\", \"king<PERSON>\"], u, u], u, [[\"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"1\"], [\"Ijp\", \"Ijt\", \"Ijn\", \"Ijtn\", \"<PERSON>h\", \"<PERSON>ju\", \"Ijm\"], [\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"Ijumanne\", \"Iju<PERSON><PERSON>\", \"<PERSON>ham<PERSON>\", \"<PERSON>ju<PERSON><PERSON>\", \"Ijumamosi\"], [\"Ijp\", \"Ijt\", \"Ijn\", \"Ijtn\", \"<PERSON>h\", \"<PERSON><PERSON>\", \"Ijm\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"K\", \"K\", \"T\", \"S\", \"S\", \"N\", \"T\", \"I\", \"I\", \"I\"], [\"M1\", \"M2\", \"M3\", \"M4\", \"M5\", \"M6\", \"M7\", \"M8\", \"M9\", \"M10\", \"M11\", \"M12\"], [\"Mweri wa kwanza\", \"Mweri wa kaili\", \"Mweri wa katatu\", \"Mweri wa kaana\", \"Mweri wa tanu\", \"Mweri wa sita\", \"Mweri wa saba\", \"Mweri wa nane\", \"Mweri wa tisa\", \"Mweri wa ikumi\", \"Mweri wa ikumi na moja\", \"Mweri wa ikumi na mbili\"]], u, [[\"KM\", \"BM\"], u, [\"Kabla ya Mayesu\", \"Baada ya Mayesu\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"TZS\", \"TSh\", \"heleri sa Tanzania\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"TZS\": [\"TSh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/rof.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"rof\", [[\"kang’ama\", \"king<PERSON>\"], u, u], u, [[\"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"1\"], [\"Ijp\", \"Ijt\", \"Ijn\", \"Ijtn\", \"<PERSON>h\", \"<PERSON>ju\", \"Ijm\"], [\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"Ijumanne\", \"Iju<PERSON><PERSON>\", \"<PERSON>ham<PERSON>\", \"<PERSON>ju<PERSON><PERSON>\", \"Ijumamosi\"], [\"Ijp\", \"Ijt\", \"Ijn\", \"Ijtn\", \"<PERSON>h\", \"<PERSON><PERSON>\", \"Ijm\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"K\", \"K\", \"T\", \"S\", \"S\", \"N\", \"T\", \"I\", \"I\", \"I\"], [\"M1\", \"M2\", \"M3\", \"M4\", \"M5\", \"M6\", \"M7\", \"M8\", \"M9\", \"M10\", \"M11\", \"M12\"], [\"Mweri wa kwanza\", \"Mweri wa kaili\", \"Mweri wa katatu\", \"Mweri wa kaana\", \"Mweri wa tanu\", \"Mweri wa sita\", \"Mweri wa saba\", \"Mweri wa nane\", \"Mweri wa tisa\", \"Mweri wa ikumi\", \"Mweri wa ikumi na moja\", \"Mweri wa ikumi na mbili\"]], u, [[\"KM\", \"BM\"], u, [\"Kabla ya Mayesu\", \"Baada ya Mayesu\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"TZS\", \"TSh\", \"heleri sa Tanzania\", { \"JPY\": [\"JP¥\", \"¥\"], \"TZS\": [\"TSh\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,yBAAyB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}