{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport { DashboardListingType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\nimport { BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { OverviewByRaTableColumns } from './overview-by-ra-columns';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service\";\nimport * as i2 from \"../../services/ca-dashboard-service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/divider\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"../../../../shared/components/bdo-table/bdo-table.component\";\n/** Rendering \"Overview By RA\" widget in\n * \"Statistics\" Tab in CA Dashboard page.\n *  */\nexport class OverviewByRAComponent extends AppComponentBase {\n  constructor(injector, CADashboardController, CADashBoardService, router) {\n    super(injector);\n    this.CADashboardController = CADashboardController;\n    this.CADashBoardService = CADashBoardService;\n    this.router = router;\n    this.currentPageIndex = 0;\n    this.totalRecords = 10;\n    this.pageSizeOptions = [10, 20, 50, 100];\n    this.PageSize = 10;\n    this.tableCols = OverviewByRaTableColumns;\n    this.TableId = 'OverviewByRA';\n    this.tabledata = [];\n    this.sort = 'raName asc';\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.dashboardData && this.dashboardData) {\n      this.selectedYear = this.dashboardData.fiscalYear;\n      this.sort = 'raName asc';\n      this.RaDto = {\n        fiscalYear: this.selectedYear,\n        maxResultCount: this.PageSize\n      };\n      this.getData();\n    }\n  }\n  getData() {\n    this.CADashboardController.getStatisticRAsByDto(this.RaDto).subscribe(result => {\n      this.tabledata = [];\n      if (result) {\n        result.items.forEach(element => {\n          var obj = {\n            numberOfAssessmentNotStarted: element.numberOfAssessmentNotStarted,\n            numberOfESAssessmentClosed: element.numberOfESAssessmentClosed,\n            numberOfESAssessmentCompleted: element.numberOfESAssessmentCompleted,\n            numberOfESFilingsSubmitted: element.numberOfESFilingsSubmitted,\n            numberOfEntities: element.numberOfEntities,\n            numberOfEntitiesFilingsOverdue: element.numberOfEntitiesFilingsOverdue,\n            percentageOfBahamianOwned: element.percentageOfBahamianOwned + '%',\n            percentageOfESAssessmentFailed: element.percentageOfESAssessmentFailed + '%',\n            percentageOfESAssessmentPassed: element.percentageOfESAssessmentPassed + '%',\n            percentageOfESFilingsSubmitted: element.percentageOfESFilingsSubmitted + '%',\n            percentageOfESFilingsWithRedFlagEvents: element.percentageOfESFilingsWithRedFlagEvents + '%',\n            percentageOfNonResidentCompanies: element.percentageOfNonResidentCompanies + '%',\n            raCode: element.raCode ?? '',\n            raName: element.raName ?? '',\n            tenantId: element.tenantId\n          };\n          this.tabledata.push(obj);\n        });\n        this.totalRecords = result.totalCount;\n        this.setTableData();\n      }\n    });\n  }\n  onLazyLoadEvent(event) {\n    let sortDir = event.isAscending ? \"asc\" /* SortDirection.ASCENDING */ : \"desc\" /* SortDirection.DESCENDING */;\n    this.sort = event.sortField + ' ' + sortDir;\n    if (this.PageSize == (event.pageSize ?? 10)) {\n      this.currentPageIndex = event.pageNumber ?? 0;\n    } else {\n      this.PageSize = event.pageSize ?? 10;\n      this.currentPageIndex = 0;\n    }\n    var skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n    this.RaDto.maxResultCount = this.PageSize;\n    this.RaDto.skipCount = skipCount;\n    this.RaDto.sorting = this.sort;\n    this.getData();\n  }\n  onClick(event) {\n    let listingType = DashboardListingType.NumOfEntitiesFilingOverdue_4_7;\n    if (event.columnId === \"numberOfAssessmentNotStarted\" /* DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_NOT_STARTED */) {\n      listingType = DashboardListingType.NumOfAssessmentNotStart_4_7;\n    }\n    let dto = {\n      tenantId: event.id,\n      listingType: listingType,\n      fiscalYear: this.selectedYear,\n      maxResultCount: 10\n    };\n    this.router.navigate(['/search-result'], {\n      queryParams: {\n        source: \"dashboard\" /* DashboardConstants.DASHBOARD */,\n        type: \"Overview By RA\" /* DashboardConstants.OVERVIEW_BY_RA */,\n        year: this.selectedYear,\n        listingType: listingType,\n        tenantId: event.id\n      }\n    });\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableId;\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.tabledata.map(x => {\n      var cells = [];\n      cells = [{\n        columnId: \"raName\" /* DashboardOverviewByRaTableColumns.RA_NAME */,\n        value: x.raName\n      }, {\n        columnId: \"raCode\" /* DashboardOverviewByRaTableColumns.RA_CODE */,\n        value: x.raCode\n      }, {\n        columnId: \"numberOfEntities\" /* DashboardOverviewByRaTableColumns.TOTAL_NUM_ENTITIES */,\n        value: x.numberOfEntities\n      }, {\n        columnId: \"numberOfESFilingsSubmitted\" /* DashboardOverviewByRaTableColumns.NUM_ES_FILINGS_SUBMITTED */,\n        value: x.numberOfESFilingsSubmitted\n      }, {\n        columnId: \"percentageOfESFilingsSubmitted\" /* DashboardOverviewByRaTableColumns.PERC_ES_FILINGS_SUBMITTED */,\n        value: x.percentageOfESFilingsSubmitted\n      }, {\n        columnId: \"numberOfEntitiesFilingsOverdue\" /* DashboardOverviewByRaTableColumns.NUM_ES_FILINGS_OVERDUE */,\n        value: x.numberOfEntitiesFilingsOverdue\n      }, {\n        columnId: \"percentageOfNonResidentCompanies\" /* DashboardOverviewByRaTableColumns.PERC_NON_RESIDENT_COMPANIES */,\n        value: x.percentageOfNonResidentCompanies\n      }, {\n        columnId: \"percentageOfBahamianOwned\" /* DashboardOverviewByRaTableColumns.PERC_BAHAMIAN_OWNED */,\n        value: x.percentageOfBahamianOwned\n      }, {\n        columnId: \"percentageOfESFilingsWithRedFlagEvents\" /* DashboardOverviewByRaTableColumns.PERC_ES_FILINGS_RED_FLAGS */,\n        value: x.percentageOfESFilingsWithRedFlagEvents\n      }, {\n        columnId: \"numberOfAssessmentNotStarted\" /* DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_NOT_STARTED */,\n        value: x.numberOfAssessmentNotStarted\n      }, {\n        columnId: \"numberOfESAssessmentClosed\" /* DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_CLOSED */,\n        value: x.numberOfESAssessmentClosed\n      }, {\n        columnId: \"percentageOfESAssessmentPassed\" /* DashboardOverviewByRaTableColumns.PERC_ASSESSMENT_PASSED */,\n        value: x.percentageOfESAssessmentPassed\n      }, {\n        columnId: \"percentageOfESAssessmentFailed\" /* DashboardOverviewByRaTableColumns.PERC_ASSESSMENT_FAILED */,\n        value: x.percentageOfESAssessmentFailed\n      }, {\n        columnId: \"numberOfESAssessmentCompleted\" /* DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_COMPLETED */,\n        value: x.numberOfESAssessmentCompleted\n      }];\n      return {\n        id: x.tenantId,\n        rawData: x,\n        cells: cells\n      };\n    });\n    this.tableService.setGridData(tableData);\n  }\n  static {\n    this.ɵfac = function OverviewByRAComponent_Factory(t) {\n      return new (t || OverviewByRAComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.CADashboardContorllerService), i0.ɵɵdirectiveInject(i2.CADashboardService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OverviewByRAComponent,\n      selectors: [[\"app-overview-by-ra\"]],\n      inputs: {\n        dashboardData: \"dashboardData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 8,\n      vars: 10,\n      consts: [[1, \"dashboard-card-title\"], [1, \"divider-margin\"], [\"scrollHeight\", \"65vh\", \"defaultSortColumnId\", \"RAName\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSizeOptions\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"]],\n      template: function OverviewByRAComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n          i0.ɵɵtext(3, \"OVERVIEW BY RA\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-content\");\n          i0.ɵɵelement(5, \"mat-divider\", 1);\n          i0.ɵɵelementStart(6, \"div\")(7, \"bdo-table\", 2);\n          i0.ɵɵlistener(\"onLazyLoad\", function OverviewByRAComponent_Template_bdo_table_onLazyLoad_7_listener($event) {\n            return ctx.onLazyLoadEvent($event);\n          })(\"onLinkClick\", function OverviewByRAComponent_Template_bdo_table_onLinkClick_7_listener($event) {\n            return ctx.onClick($event);\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.tableCols)(\"defaultSortOrder\", \"asc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSizeOptions\", ctx.pageSizeOptions)(\"pageSize\", ctx.PageSize)(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", false)(\"lazyLoad\", true);\n        }\n      },\n      dependencies: [i4.MatDivider, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, i6.BdoTableComponent],\n      styles: [\"\\n.chart-container{\\n    position: relative;\\n    min-height: 20em;\\n    min-width: 30em;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm92ZXJ2aWV3LWJ5LXJhLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBO0lBQ0ksa0JBQWtCO0lBQ2xCLGdCQUFnQjtJQUNoQixlQUFlO0FBQ25CIiwiZmlsZSI6Im92ZXJ2aWV3LWJ5LXJhLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLmNoYXJ0LWNvbnRhaW5lcntcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIG1pbi1oZWlnaHQ6IDIwZW07XHJcbiAgICBtaW4td2lkdGg6IDMwZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvc3RhdGlzdGljcy1jaGFydHMvb3ZlcnZpZXctYnktcmEuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0E7SUFDSSxrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLGVBQWU7QUFDbkI7QUFDQSx3YkFBd2IiLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLmNoYXJ0LWNvbnRhaW5lcntcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIG1pbi1oZWlnaHQ6IDIwZW07XHJcbiAgICBtaW4td2lkdGg6IDMwZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "DashboardListingType", "BdoTableData", "OverviewByRaTableColumns", "OverviewByRAComponent", "constructor", "injector", "CADashboardController", "CADashBoardService", "router", "currentPageIndex", "totalRecords", "pageSizeOptions", "PageSize", "tableCols", "TableId", "tabledata", "sort", "ngOnInit", "ngOnChanges", "changes", "dashboardData", "selected<PERSON>ear", "fiscalYear", "Ra<PERSON><PERSON>", "maxResultCount", "getData", "getStatisticRAsByDto", "subscribe", "result", "items", "for<PERSON>ach", "element", "obj", "numberOfAssessmentNotStarted", "numberOfESAssessmentClosed", "numberOfESAssessmentCompleted", "numberOfESFilingsSubmitted", "numberOfEntities", "numberOfEntitiesFilingsOverdue", "percentageOfBahamianOwned", "percentageOfESAssessmentFailed", "percentageOfESAssessmentPassed", "percentageOfESFilingsSubmitted", "percentageOfESFilingsWithRedFlagEvents", "percentageOfNonResidentCompanies", "raCode", "raName", "tenantId", "push", "totalCount", "setTableData", "onLazyLoadEvent", "event", "sortDir", "isAscending", "sortField", "pageSize", "pageNumber", "skip<PERSON><PERSON>nt", "sorting", "onClick", "listingType", "NumOfEntitiesFilingOverdue_4_7", "columnId", "NumOfAssessmentNotStart_4_7", "dto", "id", "navigate", "queryParams", "source", "type", "year", "tableData", "resetToFirstPage", "tableId", "data", "map", "x", "cells", "value", "rawData", "tableService", "setGridData", "i0", "ɵɵdirectiveInject", "Injector", "i1", "CADashboardContorllerService", "i2", "CADashboardService", "i3", "Router", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "OverviewByRAComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "OverviewByRAComponent_Template_bdo_table_onLazyLoad_7_listener", "$event", "OverviewByRAComponent_Template_bdo_table_onLinkClick_7_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\statistics-charts\\overview-by-ra.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\statistics-charts\\overview-by-ra.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  Input,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport Chart, { ChartItem, ChartType, Colors } from 'chart.js/auto';\r\nimport{CADashboardContorllerService} from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service'\r\nimport { CADashboardService } from '../../services/ca-dashboard-service';\r\nimport { GetDashboardListingByActivityDto, GetDashboardListingByCountryDto, GetDashboardListingDto, GetStatisticRADto, StatisticMainDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { DashboardListingType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { Router } from '@angular/router';\r\nimport { BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\r\nimport { DashboardConstants,DashboardOverviewByRaTableColumns, SortDirection } from '@app/shared/constants';\r\nimport { OverviewByRaTableColumns } from './overview-by-ra-columns';\r\n/** Rendering \"Overview By RA\" widget in\r\n * \"Statistics\" Tab in CA Dashboard page.\r\n *  */\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-overview-by-ra',\r\n  templateUrl: './overview-by-ra.component.html',\r\n  styleUrls: ['./overview-by-ra.component.css'],\r\n})\r\nexport class OverviewByRAComponent extends AppComponentBase implements OnInit {\r\n  constructor(injector: Injector, private CADashboardController: CADashboardContorllerService, private CADashBoardService: CADashboardService, private router: Router) {\r\n    super(injector);\r\n  }\r\n\r\n  @Input() dashboardData: StatisticMainDto;\r\n  currentPageIndex = 0;\r\n  totalRecords = 10;\r\n  pageSizeOptions = [10, 20, 50, 100];\r\n  PageSize = 10;\r\n  tableCols = OverviewByRaTableColumns;\r\n  TableId = 'OverviewByRA';\r\n  selectedYear:number;\r\n  tabledata = [];\r\n  sort = 'raName asc'\r\n  RaDto: GetStatisticRADto\r\n  ngOnInit() {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.dashboardData && this.dashboardData) {\r\n      this.selectedYear = this.dashboardData.fiscalYear;\r\n      this.sort = 'raName asc';\r\n      this.RaDto = {fiscalYear: this.selectedYear, maxResultCount: this.PageSize}\r\n      this.getData(); \r\n    }\r\n  }\r\n  \r\n  getData(){\r\n    this.CADashboardController.getStatisticRAsByDto(this.RaDto).subscribe(result =>{\r\n      this.tabledata = [];\r\n      if(result){\r\n        result.items.forEach(element =>{\r\n          var obj = {\r\n            numberOfAssessmentNotStarted: element.numberOfAssessmentNotStarted,\r\n            numberOfESAssessmentClosed: element.numberOfESAssessmentClosed,\r\n            numberOfESAssessmentCompleted: element.numberOfESAssessmentCompleted,\r\n            numberOfESFilingsSubmitted: element.numberOfESFilingsSubmitted,\r\n            numberOfEntities: element.numberOfEntities,\r\n            numberOfEntitiesFilingsOverdue: element.numberOfEntitiesFilingsOverdue,\r\n            percentageOfBahamianOwned: element.percentageOfBahamianOwned + '%',\r\n            percentageOfESAssessmentFailed: element.percentageOfESAssessmentFailed + '%',\r\n            percentageOfESAssessmentPassed: element.percentageOfESAssessmentPassed + '%',\r\n            percentageOfESFilingsSubmitted: element.percentageOfESFilingsSubmitted + '%',\r\n            percentageOfESFilingsWithRedFlagEvents: element.percentageOfESFilingsWithRedFlagEvents + '%',\r\n            percentageOfNonResidentCompanies: element.percentageOfNonResidentCompanies + '%',\r\n            raCode: element.raCode ?? '',\r\n            raName: element.raName ?? '',\r\n            tenantId: element.tenantId\r\n          }\r\n          this.tabledata.push(obj);\r\n        });\r\n        this.totalRecords = result.totalCount;\r\n        this.setTableData();\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  onLazyLoadEvent(event){\r\n    let sortDir = event.isAscending ? SortDirection.ASCENDING : SortDirection.DESCENDING;\r\n    this.sort = event.sortField + ' ' + sortDir;\r\n\r\n    if (this.PageSize == (event.pageSize??10)) {\r\n      this.currentPageIndex = event.pageNumber??0;\r\n    } \r\n    else {\r\n      this.PageSize = event.pageSize ?? 10;\r\n      this.currentPageIndex = 0;\r\n    }\r\n    var skipCount =  (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\r\n\r\n    this.RaDto.maxResultCount = this.PageSize;\r\n    this.RaDto.skipCount = skipCount;\r\n    this.RaDto.sorting = this.sort;\r\n    this.getData();\r\n  }\r\n\r\n  onClick(event){\r\n    let listingType = DashboardListingType.NumOfEntitiesFilingOverdue_4_7\r\n    if(event.columnId === DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_NOT_STARTED){\r\n      listingType = DashboardListingType.NumOfAssessmentNotStart_4_7\r\n    }\r\n    let dto: GetDashboardListingDto = {tenantId: event.id, listingType: listingType, fiscalYear: this.selectedYear, maxResultCount: 10}\r\n    this.router.navigate(['/search-result'], { queryParams: {source: DashboardConstants.DASHBOARD, type: DashboardConstants.OVERVIEW_BY_RA, year: this.selectedYear, listingType: listingType, tenantId: event.id}});\r\n  }\r\n\r\n\r\n  private setTableData(): void {\r\n\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableId;\r\n    tableData.totalRecords = this.totalRecords;\r\n\r\n    tableData.data = this.tabledata.map(x => {\r\n      var cells = [];\r\n        cells = [\r\n          { columnId: DashboardOverviewByRaTableColumns.RA_NAME, value: x.raName },\r\n          { columnId: DashboardOverviewByRaTableColumns.RA_CODE, value: x.raCode },\r\n          { columnId: DashboardOverviewByRaTableColumns.TOTAL_NUM_ENTITIES, value: x.numberOfEntities },\r\n          { columnId: DashboardOverviewByRaTableColumns.NUM_ES_FILINGS_SUBMITTED, value: x.numberOfESFilingsSubmitted },\r\n          { columnId: DashboardOverviewByRaTableColumns.PERC_ES_FILINGS_SUBMITTED, value: x.percentageOfESFilingsSubmitted },\r\n          { columnId: DashboardOverviewByRaTableColumns.NUM_ES_FILINGS_OVERDUE, value: x.numberOfEntitiesFilingsOverdue },\r\n          { columnId: DashboardOverviewByRaTableColumns.PERC_NON_RESIDENT_COMPANIES, value: x.percentageOfNonResidentCompanies },\r\n          { columnId: DashboardOverviewByRaTableColumns.PERC_BAHAMIAN_OWNED, value: x.percentageOfBahamianOwned },\r\n          { columnId: DashboardOverviewByRaTableColumns.PERC_ES_FILINGS_RED_FLAGS, value: x.percentageOfESFilingsWithRedFlagEvents },\r\n          { columnId: DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_NOT_STARTED, value: x.numberOfAssessmentNotStarted },\r\n          { columnId: DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_CLOSED, value: x.numberOfESAssessmentClosed },\r\n          { columnId: DashboardOverviewByRaTableColumns.PERC_ASSESSMENT_PASSED, value: x.percentageOfESAssessmentPassed },\r\n          { columnId: DashboardOverviewByRaTableColumns.PERC_ASSESSMENT_FAILED, value: x.percentageOfESAssessmentFailed },\r\n          { columnId: DashboardOverviewByRaTableColumns.NUM_ASSESSMENT_COMPLETED, value: x.numberOfESAssessmentCompleted }\r\n        ];\r\n\r\n      return {\r\n        id: x.tenantId,\r\n        rawData: x,\r\n        cells: cells\r\n      };\r\n    });\r\n    this.tableService.setGridData(tableData);\r\n  }\r\n}\r\n", "<mat-card>\r\n  <mat-card-header>\r\n    <mat-card-title class=\"dashboard-card-title\">OVERVIEW BY RA</mat-card-title>\r\n  </mat-card-header>\r\n  <mat-card-content>\r\n    <mat-divider class=\"divider-margin\"></mat-divider>\r\n    <div>\r\n      <bdo-table [id]=\"TableId\" [columns]=\"tableCols\" scrollHeight=\"65vh\" defaultSortColumnId=\"RAName\" [defaultSortOrder]=\"'asc'\" [pageIndex]=\"currentPageIndex\"\r\n          [pageSizeOptions]=\"pageSizeOptions\" [pageSize]=\"PageSize\" [isVirtualScroll]=\"false\" [hidePagination]=\"false\" [rowSelectable]=\"false\" [lazyLoad]=\"true\"\r\n          (onLazyLoad)=\"onLazyLoadEvent($event)\" (onLinkClick) = \"onClick($event)\">\r\n      </bdo-table>\r\n    </div>\r\n\r\n\r\n  </mat-card-content>\r\n</mat-card>\r\n"], "mappings": "AAQA,SAASA,gBAAgB,QAAQ,yBAAyB;AAK1D,SAASC,oBAAoB,QAAQ,4FAA4F;AAEjI,SAASC,YAAY,QAAQ,kDAAkD;AAE/E,SAASC,wBAAwB,QAAQ,0BAA0B;;;;;;;;AACnE;;;AASA,OAAM,MAAOC,qBAAsB,SAAQJ,gBAAgB;EACzDK,YAAYC,QAAkB,EAAUC,qBAAmD,EAAUC,kBAAsC,EAAUC,MAAc;IACjK,KAAK,CAACH,QAAQ,CAAC;IADuB,KAAAC,qBAAqB,GAArBA,qBAAqB;IAAwC,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAK3J,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACnC,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAGX,wBAAwB;IACpC,KAAAY,OAAO,GAAG,cAAc;IAExB,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,IAAI,GAAG,YAAY;EAXnB;EAaAC,QAAQA,CAAA,GAAI;EAEZC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,EAAE;MAC/C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU;MACjD,IAAI,CAACN,IAAI,GAAG,YAAY;MACxB,IAAI,CAACO,KAAK,GAAG;QAACD,UAAU,EAAE,IAAI,CAACD,YAAY;QAAEG,cAAc,EAAE,IAAI,CAACZ;MAAQ,CAAC;MAC3E,IAAI,CAACa,OAAO,EAAE;IAChB;EACF;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACnB,qBAAqB,CAACoB,oBAAoB,CAAC,IAAI,CAACH,KAAK,CAAC,CAACI,SAAS,CAACC,MAAM,IAAG;MAC7E,IAAI,CAACb,SAAS,GAAG,EAAE;MACnB,IAAGa,MAAM,EAAC;QACRA,MAAM,CAACC,KAAK,CAACC,OAAO,CAACC,OAAO,IAAG;UAC7B,IAAIC,GAAG,GAAG;YACRC,4BAA4B,EAAEF,OAAO,CAACE,4BAA4B;YAClEC,0BAA0B,EAAEH,OAAO,CAACG,0BAA0B;YAC9DC,6BAA6B,EAAEJ,OAAO,CAACI,6BAA6B;YACpEC,0BAA0B,EAAEL,OAAO,CAACK,0BAA0B;YAC9DC,gBAAgB,EAAEN,OAAO,CAACM,gBAAgB;YAC1CC,8BAA8B,EAAEP,OAAO,CAACO,8BAA8B;YACtEC,yBAAyB,EAAER,OAAO,CAACQ,yBAAyB,GAAG,GAAG;YAClEC,8BAA8B,EAAET,OAAO,CAACS,8BAA8B,GAAG,GAAG;YAC5EC,8BAA8B,EAAEV,OAAO,CAACU,8BAA8B,GAAG,GAAG;YAC5EC,8BAA8B,EAAEX,OAAO,CAACW,8BAA8B,GAAG,GAAG;YAC5EC,sCAAsC,EAAEZ,OAAO,CAACY,sCAAsC,GAAG,GAAG;YAC5FC,gCAAgC,EAAEb,OAAO,CAACa,gCAAgC,GAAG,GAAG;YAChFC,MAAM,EAAEd,OAAO,CAACc,MAAM,IAAI,EAAE;YAC5BC,MAAM,EAAEf,OAAO,CAACe,MAAM,IAAI,EAAE;YAC5BC,QAAQ,EAAEhB,OAAO,CAACgB;WACnB;UACD,IAAI,CAAChC,SAAS,CAACiC,IAAI,CAAChB,GAAG,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAACtB,YAAY,GAAGkB,MAAM,CAACqB,UAAU;QACrC,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAACC,KAAK;IACnB,IAAIC,OAAO,GAAGD,KAAK,CAACE,WAAW,GAAE,sCAA0B;IAC3D,IAAI,CAACtC,IAAI,GAAGoC,KAAK,CAACG,SAAS,GAAG,GAAG,GAAGF,OAAO;IAE3C,IAAI,IAAI,CAACzC,QAAQ,KAAKwC,KAAK,CAACI,QAAQ,IAAE,EAAE,CAAC,EAAE;MACzC,IAAI,CAAC/C,gBAAgB,GAAG2C,KAAK,CAACK,UAAU,IAAE,CAAC;IAC7C,CAAC,MACI;MACH,IAAI,CAAC7C,QAAQ,GAAGwC,KAAK,CAACI,QAAQ,IAAI,EAAE;MACpC,IAAI,CAAC/C,gBAAgB,GAAG,CAAC;IAC3B;IACA,IAAIiD,SAAS,GAAI,CAAC,IAAI,CAACjD,gBAAgB,IAAI,CAAC,KAAK,IAAI,CAACG,QAAQ,IAAI,EAAE,CAAC;IAErE,IAAI,CAACW,KAAK,CAACC,cAAc,GAAG,IAAI,CAACZ,QAAQ;IACzC,IAAI,CAACW,KAAK,CAACmC,SAAS,GAAGA,SAAS;IAChC,IAAI,CAACnC,KAAK,CAACoC,OAAO,GAAG,IAAI,CAAC3C,IAAI;IAC9B,IAAI,CAACS,OAAO,EAAE;EAChB;EAEAmC,OAAOA,CAACR,KAAK;IACX,IAAIS,WAAW,GAAG7D,oBAAoB,CAAC8D,8BAA8B;IACrE,IAAGV,KAAK,CAACW,QAAQ,wGAAkE;MACjFF,WAAW,GAAG7D,oBAAoB,CAACgE,2BAA2B;IAChE;IACA,IAAIC,GAAG,GAA2B;MAAClB,QAAQ,EAAEK,KAAK,CAACc,EAAE;MAAEL,WAAW,EAAEA,WAAW;MAAEvC,UAAU,EAAE,IAAI,CAACD,YAAY;MAAEG,cAAc,EAAE;IAAE,CAAC;IACnI,IAAI,CAAChB,MAAM,CAAC2D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;MAAEC,WAAW,EAAE;QAACC,MAAM;QAAgCC,IAAI;QAAqCC,IAAI,EAAE,IAAI,CAAClD,YAAY;QAAEwC,WAAW,EAAEA,WAAW;QAAEd,QAAQ,EAAEK,KAAK,CAACc;MAAE;IAAC,CAAC,CAAC;EAClN;EAGQhB,YAAYA,CAAA;IAElB,MAAMsB,SAAS,GAAG,IAAIvE,YAAY,EAAE;IACpCuE,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC5D,OAAO;IAChC0D,SAAS,CAAC9D,YAAY,GAAG,IAAI,CAACA,YAAY;IAE1C8D,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC5D,SAAS,CAAC6D,GAAG,CAACC,CAAC,IAAG;MACtC,IAAIC,KAAK,GAAG,EAAE;MACZA,KAAK,GAAG,CACN;QAAEf,QAAQ;QAA6CgB,KAAK,EAAEF,CAAC,CAAC/B;MAAM,CAAE,EACxE;QAAEiB,QAAQ;QAA6CgB,KAAK,EAAEF,CAAC,CAAChC;MAAM,CAAE,EACxE;QAAEkB,QAAQ;QAAwDgB,KAAK,EAAEF,CAAC,CAACxC;MAAgB,CAAE,EAC7F;QAAE0B,QAAQ;QAA8DgB,KAAK,EAAEF,CAAC,CAACzC;MAA0B,CAAE,EAC7G;QAAE2B,QAAQ;QAA+DgB,KAAK,EAAEF,CAAC,CAACnC;MAA8B,CAAE,EAClH;QAAEqB,QAAQ;QAA4DgB,KAAK,EAAEF,CAAC,CAACvC;MAA8B,CAAE,EAC/G;QAAEyB,QAAQ;QAAiEgB,KAAK,EAAEF,CAAC,CAACjC;MAAgC,CAAE,EACtH;QAAEmB,QAAQ;QAAyDgB,KAAK,EAAEF,CAAC,CAACtC;MAAyB,CAAE,EACvG;QAAEwB,QAAQ;QAA+DgB,KAAK,EAAEF,CAAC,CAAClC;MAAsC,CAAE,EAC1H;QAAEoB,QAAQ;QAAgEgB,KAAK,EAAEF,CAAC,CAAC5C;MAA4B,CAAE,EACjH;QAAE8B,QAAQ;QAA2DgB,KAAK,EAAEF,CAAC,CAAC3C;MAA0B,CAAE,EAC1G;QAAE6B,QAAQ;QAA4DgB,KAAK,EAAEF,CAAC,CAACpC;MAA8B,CAAE,EAC/G;QAAEsB,QAAQ;QAA4DgB,KAAK,EAAEF,CAAC,CAACrC;MAA8B,CAAE,EAC/G;QAAEuB,QAAQ;QAA8DgB,KAAK,EAAEF,CAAC,CAAC1C;MAA6B,CAAE,CACjH;MAEH,OAAO;QACL+B,EAAE,EAAEW,CAAC,CAAC9B,QAAQ;QACdiC,OAAO,EAAEH,CAAC;QACVC,KAAK,EAAEA;OACR;IACH,CAAC,CAAC;IACF,IAAI,CAACG,YAAY,CAACC,WAAW,CAACV,SAAS,CAAC;EAC1C;;;uBAxHWrE,qBAAqB,EAAAgF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,4BAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,kBAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBxF,qBAAqB;MAAAyF,SAAA;MAAAC,MAAA;QAAAzE,aAAA;MAAA;MAAA0E,QAAA,GAAAX,EAAA,CAAAY,0BAAA,EAAAZ,EAAA,CAAAa,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzB9BnB,EAFJ,CAAAqB,cAAA,eAAU,sBACS,wBAC8B;UAAArB,EAAA,CAAAsB,MAAA,qBAAc;UAC7DtB,EAD6D,CAAAuB,YAAA,EAAiB,EAC5D;UAClBvB,EAAA,CAAAqB,cAAA,uBAAkB;UAChBrB,EAAA,CAAAwB,SAAA,qBAAkD;UAEhDxB,EADF,CAAAqB,cAAA,UAAK,mBAG0E;UAAlCrB,EAAvC,CAAAyB,UAAA,wBAAAC,+DAAAC,MAAA;YAAA,OAAcP,GAAA,CAAApD,eAAA,CAAA2D,MAAA,CAAuB;UAAA,EAAC,yBAAAC,gEAAAD,MAAA;YAAA,OAAkBP,GAAA,CAAA3C,OAAA,CAAAkD,MAAA,CAAe;UAAA,EAAC;UAMlF3B,EALM,CAAAuB,YAAA,EAAY,EACR,EAGW,EACV;;;UARMvB,EAAA,CAAA6B,SAAA,GAAc;UACgH7B,EAD9H,CAAA8B,UAAA,OAAAV,GAAA,CAAAzF,OAAA,CAAc,YAAAyF,GAAA,CAAA1F,SAAA,CAAsB,2BAA4E,cAAA0F,GAAA,CAAA9F,gBAAA,CAA+B,oBAAA8F,GAAA,CAAA5F,eAAA,CACnH,aAAA4F,GAAA,CAAA3F,QAAA,CAAsB,0BAA0B,yBAAyB,wBAAwB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}