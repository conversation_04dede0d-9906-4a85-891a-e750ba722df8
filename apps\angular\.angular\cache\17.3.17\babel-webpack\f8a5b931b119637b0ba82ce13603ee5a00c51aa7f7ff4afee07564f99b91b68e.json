{"ast": null, "code": "import toDate from \"../../../../toDate/index.js\";\nimport isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\n// Adapted from the `ru` translation\n\nvar weekdays = ['неделя', 'понеделник', 'вторник', 'сряда', 'четвъртък', 'петък', 'събота'];\nfunction lastWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'миналата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'миналия \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = weekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'във \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction nextWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следващата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следващия \" + weekday + \" в' p\";\n  }\n}\nvar lastWeekFormatToken = function lastWeekFormatToken(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nvar nextWeekFormatToken = function nextWeekFormatToken(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormatToken,\n  yesterday: \"'вчера в' p\",\n  today: \"'днес в' p\",\n  tomorrow: \"'утре в' p\",\n  nextWeek: nextWeekFormatToken,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}