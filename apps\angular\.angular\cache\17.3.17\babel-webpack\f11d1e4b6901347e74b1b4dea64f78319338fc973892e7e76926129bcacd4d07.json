{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Web Analytics, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n// @skip-file-minify\n// ##############################################################\n// AUTO GENERATED FILE: This file is Auto Generated during build.\n// ##############################################################\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n// Note: DON'T Export these const from the package as we are still targeting ES3 this will export a mutable variables that someone could change!!!\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\nexport var _DYN_TO_STRING = \"toString\"; // Count: 4\nexport var _DYN_DISABLE_EXCEPTION_TR0 = \"disableExceptionTracking\"; // Count: 3\nexport var _DYN_AUTO_TRACK_PAGE_VISI1 = \"autoTrackPageVisitTime\"; // Count: 4\nexport var _DYN_OVERRIDE_PAGE_VIEW_D2 = \"overridePageViewDuration\"; // Count: 3\nexport var _DYN_ENABLE_UNHANDLED_PRO3 = \"enableUnhandledPromiseRejectionTracking\"; // Count: 3\nexport var _DYN_SAMPLING_PERCENTAGE = \"samplingPercentage\"; // Count: 4\nexport var _DYN_IS_STORAGE_USE_DISAB4 = \"isStorageUseDisabled\"; // Count: 4\nexport var _DYN_IS_BROWSER_LINK_TRAC5 = \"isBrowserLinkTrackingEnabled\"; // Count: 4\nexport var _DYN_ENABLE_AUTO_ROUTE_TR6 = \"enableAutoRouteTracking\"; // Count: 3\nexport var _DYN_NAME_PREFIX = \"namePrefix\"; // Count: 3\nexport var _DYN_DISABLE_FLUSH_ON_BEF7 = \"disableFlushOnBeforeUnload\"; // Count: 3\nexport var _DYN_DISABLE_FLUSH_ON_UNL8 = \"disableFlushOnUnload\"; // Count: 2\nexport var _DYN_CORE = \"core\"; // Count: 7\nexport var _DYN_DATA_TYPE = \"dataType\"; // Count: 8\nexport var _DYN_ENVELOPE_TYPE = \"envelopeType\"; // Count: 7\nexport var _DYN_DIAG_LOG = \"diagLog\"; // Count: 13\nexport var _DYN_TRACK = \"track\"; // Count: 7\nexport var _DYN_TRACK_PAGE_VIEW = \"trackPageView\"; // Count: 4\nexport var _DYN_TRACK_PREVIOUS_PAGE_9 = \"trackPreviousPageVisit\"; // Count: 3\nexport var _DYN_SEND_PAGE_VIEW_INTER10 = \"sendPageViewInternal\"; // Count: 7\nexport var _DYN_SEND_PAGE_VIEW_PERFO11 = \"sendPageViewPerformanceInternal\"; // Count: 3\nexport var _DYN_POPULATE_PAGE_VIEW_P12 = \"populatePageViewPerformanceEvent\"; // Count: 3\nexport var _DYN_HREF = \"href\"; // Count: 6\nexport var _DYN_SEND_EXCEPTION_INTER13 = \"sendExceptionInternal\"; // Count: 2\nexport var _DYN_EXCEPTION = \"exception\"; // Count: 3\nexport var _DYN_ERROR = \"error\"; // Count: 5\nexport var _DYN__ONERROR = \"_onerror\"; // Count: 3\nexport var _DYN_ERROR_SRC = \"errorSrc\"; // Count: 3\nexport var _DYN_LINE_NUMBER = \"lineNumber\"; // Count: 5\nexport var _DYN_COLUMN_NUMBER = \"columnNumber\"; // Count: 5\nexport var _DYN_MESSAGE = \"message\"; // Count: 4\nexport var _DYN__CREATE_AUTO_EXCEPTI14 = \"CreateAutoException\"; // Count: 3\nexport var _DYN_ADD_TELEMETRY_INITIA15 = \"addTelemetryInitializer\"; // Count: 4\nexport var _DYN_DURATION = \"duration\"; // Count: 10\nexport var _DYN_LENGTH = \"length\"; // Count: 5\nexport var _DYN_IS_PERFORMANCE_TIMIN16 = \"isPerformanceTimingSupported\"; // Count: 2\nexport var _DYN_GET_PERFORMANCE_TIMI17 = \"getPerformanceTiming\"; // Count: 2\nexport var _DYN_NAVIGATION_START = \"navigationStart\"; // Count: 4\nexport var _DYN_SHOULD_COLLECT_DURAT18 = \"shouldCollectDuration\"; // Count: 3\nexport var _DYN_IS_PERFORMANCE_TIMIN19 = \"isPerformanceTimingDataReady\"; // Count: 2\nexport var _DYN_GET_ENTRIES_BY_TYPE = \"getEntriesByType\"; // Count: 3\nexport var _DYN_RESPONSE_START = \"responseStart\"; // Count: 5\nexport var _DYN_REQUEST_START = \"requestStart\"; // Count: 3\nexport var _DYN_LOAD_EVENT_END = \"loadEventEnd\"; // Count: 4\nexport var _DYN_RESPONSE_END = \"responseEnd\"; // Count: 5\nexport var _DYN_CONNECT_END = \"connectEnd\"; // Count: 4\nexport var _DYN_PAGE_VISIT_START_TIM20 = \"pageVisitStartTime\"; // Count: 2\n//# sourceMappingURL=__DynamicConstants.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}