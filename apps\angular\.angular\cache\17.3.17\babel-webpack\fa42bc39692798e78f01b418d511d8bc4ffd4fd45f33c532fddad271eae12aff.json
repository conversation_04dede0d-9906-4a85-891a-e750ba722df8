{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nvar formatRelativeLocale = {\n  lastWeek: \"'గత' eeee p\",\n  // CLDR #1384\n  yesterday: \"'నిన్న' p\",\n  // CLDR #1393\n  today: \"'ఈ రోజు' p\",\n  // CLDR #1394\n  tomorrow: \"'రేపు' p\",\n  // CLDR #1395\n  nextWeek: \"'తదుపరి' eeee p\",\n  // CLDR #1386\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/te/_lib/formatRelative/index.js"], "sourcesContent": ["// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nvar formatRelativeLocale = {\n  lastWeek: \"'గత' eeee p\",\n  // CLDR #1384\n  yesterday: \"'నిన్న' p\",\n  // CLDR #1393\n  today: \"'ఈ రోజు' p\",\n  // CLDR #1394\n  tomorrow: \"'రేపు' p\",\n  // CLDR #1395\n  nextWeek: \"'తదుపరి' eeee p\",\n  // CLDR #1386\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA;;AAEA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,aAAa;EACvB;EACAC,SAAS,EAAE,WAAW;EACtB;EACAC,KAAK,EAAE,YAAY;EACnB;EACAC,QAAQ,EAAE,UAAU;EACpB;EACAC,QAAQ,EAAE,iBAAiB;EAC3B;EACAC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}