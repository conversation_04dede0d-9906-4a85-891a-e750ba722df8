{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { arrReduce, objKeys, strEndsWith } from \"@microsoft/applicationinsights-core-js\";\nimport { DEFAULT_BREEZE_ENDPOINT } from \"./Constants\";\nimport { _DYN_INGESTIONENDPOINT, _DYN_LENGTH, _DYN_SPLIT, _DYN_TO_LOWER_CASE } from \"./__DynamicConstants\";\nvar _FIELDS_SEPARATOR = \";\";\nvar _FIELD_KEY_VALUE_SEPARATOR = \"=\";\nexport function parseConnectionString(connectionString) {\n  if (!connectionString) {\n    return {};\n  }\n  var kvPairs = connectionString[_DYN_SPLIT /* @min:%2esplit */](_FIELDS_SEPARATOR);\n  var result = arrReduce(kvPairs, function (fields, kv) {\n    var kvParts = kv[_DYN_SPLIT /* @min:%2esplit */](_FIELD_KEY_VALUE_SEPARATOR);\n    if (kvParts[_DYN_LENGTH /* @min:%2elength */] === 2) {\n      // only save fields with valid formats\n      var key = kvParts[0][_DYN_TO_LOWER_CASE /* @min:%2etoLowerCase */]();\n      var value = kvParts[1];\n      fields[key] = value;\n    }\n    return fields;\n  }, {});\n  if (objKeys(result)[_DYN_LENGTH /* @min:%2elength */] > 0) {\n    // this is a valid connection string, so parse the results\n    if (result.endpointsuffix) {\n      // use endpoint suffix where overrides are not provided\n      var locationPrefix = result.location ? result.location + \".\" : \"\";\n      result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */] = result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */] || \"https://\" + locationPrefix + \"dc.\" + result.endpointsuffix;\n      if (strEndsWith(result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */], \"/\")) {\n        result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */] = result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */].slice(0, -1);\n      }\n    }\n    // apply the default endpoints\n    result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */] = result[_DYN_INGESTIONENDPOINT /* @min:%2eingestionendpoint */] || DEFAULT_BREEZE_ENDPOINT;\n  }\n  return result;\n}\nexport var ConnectionStringParser = {\n  parse: parseConnectionString\n};\n//# sourceMappingURL=ConnectionStringParser.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}