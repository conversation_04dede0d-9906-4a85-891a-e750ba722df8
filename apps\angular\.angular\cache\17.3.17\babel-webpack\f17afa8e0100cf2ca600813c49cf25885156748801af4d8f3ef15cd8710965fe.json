{"ast": null, "code": "import { HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { GlobalHttpErrorInterceptor } from './global-http-error.interceptor';\n// NOTE: This contains a list of HTTP interceptors to handle various API error scenario's\n// The global error interceptor is the last in the chain and will catch anything not\n// specifically dealt with.\nexport const httpInterceptorProviders = [\n// NOTE: Keep the GlobalHttpErrorInterceptor first, interceptors run from bottom up\n{\n  provide: HTTP_INTERCEPTORS,\n  useClass: GlobalHttpErrorInterceptor,\n  multi: true\n}\n/** Add any other interceptors here */];", "map": {"version": 3, "names": ["HTTP_INTERCEPTORS", "GlobalHttpErrorInterceptor", "httpInterceptorProviders", "provide", "useClass", "multi"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\errors\\interceptors\\index.ts"], "sourcesContent": ["\r\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\r\n\r\nimport { GlobalHttpErrorInterceptor } from './global-http-error.interceptor';\r\n\r\n\r\n\r\n// NOTE: This contains a list of HTTP interceptors to handle various API error scenario's\r\n// The global error interceptor is the last in the chain and will catch anything not\r\n// specifically dealt with.\r\n\r\nexport const httpInterceptorProviders = [\r\n\r\n    // NOTE: Keep the GlobalHttpErrorInterceptor first, interceptors run from bottom up\r\n\r\n    { provide: HTTP_INTERCEPTORS, useClass: GlobalHttpErrorInterceptor, multi: true },\r\n\r\n    /** Add any other interceptors here */\r\n];\r\n"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,sBAAsB;AAExD,SAASC,0BAA0B,QAAQ,iCAAiC;AAI5E;AACA;AACA;AAEA,OAAO,MAAMC,wBAAwB,GAAG;AAEpC;AAEA;EAAEC,OAAO,EAAEH,iBAAiB;EAAEI,QAAQ,EAAEH,0BAA0B;EAAEI,KAAK,EAAE;AAAI;AAE/E,uCACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}