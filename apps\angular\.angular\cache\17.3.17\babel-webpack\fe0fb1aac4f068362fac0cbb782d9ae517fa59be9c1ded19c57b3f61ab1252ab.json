{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DeclarationImportDetailsComponent, DeclarationImportComponent } from './containers/';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DeclarationImportComponent\n}, {\n  path: 'importdetail',\n  component: DeclarationImportDetailsComponent\n}];\nexport let DeclarationImportRoutingModule = /*#__PURE__*/(() => {\n  class DeclarationImportRoutingModule {\n    static {\n      this.ɵfac = function DeclarationImportRoutingModule_Factory(t) {\n        return new (t || DeclarationImportRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: DeclarationImportRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return DeclarationImportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}