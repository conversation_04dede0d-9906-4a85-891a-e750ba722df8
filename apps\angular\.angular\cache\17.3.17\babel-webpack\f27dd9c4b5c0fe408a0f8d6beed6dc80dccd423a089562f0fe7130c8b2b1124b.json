{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  if (n === 2) return 2;\n  return 5;\n}\nexport default [\"se\", [[\"i.b.\", \"e.b.\"], u, [\"iđitbeaivet\", \"eahketbeaivet\"]], [[\"i.b.\", \"e.b.\"], u, [\"iđitbeaivi\", \"eahketbeaivi\"]], [[\"S\", \"V\", \"M\", \"G\", \"D\", \"B\", \"L\"], [\"sotn\", \"vuos\", \"maŋ\", \"gask\", \"duor\", \"bear\", \"láv\"], [\"sotnabeaivi\", \"vuossárga\", \"maŋŋeb<PERSON>rga\", \"gaska<PERSON><PERSON>k<PERSON>\", \"duorasdat\", \"bearjadat\", \"lávvardat\"], [\"sotn\", \"vuos\", \"maŋ\", \"gask\", \"duor\", \"bear\", \"láv\"]], u, [[\"O\", \"G\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"S\", \"B\", \"<PERSON>\", \"<PERSON>\", \"S\", \"J\"], [\"ođđj\", \"guov\", \"njuk\", \"cuo\", \"mies\", \"geas\", \"suoi\", \"borg\", \"čakč\", \"golg\", \"sk<PERSON>b\", \"juov\"], [\"ođđajagemánnu\", \"guovvamánnu\", \"njukčamánnu\", \"cuoŋománnu\", \"miessemánnu\", \"geassemánnu\", \"suoidnemánnu\", \"borgemánnu\", \"čakčamánnu\", \"golggotmánnu\", \"skábmamánnu\", \"juovlamánnu\"]], u, [[\"o.Kr.\", \"m.Kr.\"], u, [\"ovdal Kristtusa\", \"maŋŋel Kristtusa\"]], 1, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"−\", \"·10^\", \"·\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"NOK\", \"kr\", \"norgga kruvdno\", {\n  \"DKK\": [\"Dkr\", \"kr\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"NOK\": [\"kr\"],\n  \"SEK\": [\"Skr\", \"kr\"],\n  \"THB\": [\"฿\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/se.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    if (n === 2)\n        return 2;\n    return 5;\n}\nexport default [\"se\", [[\"i.b.\", \"e.b.\"], u, [\"iđitbeaivet\", \"eahketbeaivet\"]], [[\"i.b.\", \"e.b.\"], u, [\"iđitbeaivi\", \"eahketbeaivi\"]], [[\"S\", \"V\", \"M\", \"G\", \"D\", \"B\", \"L\"], [\"sotn\", \"vuos\", \"maŋ\", \"gask\", \"duor\", \"bear\", \"láv\"], [\"sotnabeaivi\", \"vuossárga\", \"maŋŋeb<PERSON>rga\", \"gaska<PERSON><PERSON>k<PERSON>\", \"duorasdat\", \"bearjadat\", \"lávvardat\"], [\"sotn\", \"vuos\", \"maŋ\", \"gask\", \"duor\", \"bear\", \"láv\"]], u, [[\"O\", \"G\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"S\", \"B\", \"<PERSON>\", \"<PERSON>\", \"S\", \"J\"], [\"ođđj\", \"guov\", \"njuk\", \"cuo\", \"mies\", \"geas\", \"suoi\", \"borg\", \"čakč\", \"golg\", \"sk<PERSON>b\", \"juov\"], [\"ođđajagemánnu\", \"guovvamánnu\", \"njukčamánnu\", \"cuoŋománnu\", \"miessemánnu\", \"geassemánnu\", \"suoidnemánnu\", \"borgemánnu\", \"čakčamánnu\", \"golggotmánnu\", \"skábmamánnu\", \"juovlamánnu\"]], u, [[\"o.Kr.\", \"m.Kr.\"], u, [\"ovdal Kristtusa\", \"maŋŋel Kristtusa\"]], 1, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"−\", \"·10^\", \"·\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"NOK\", \"kr\", \"norgga kruvdno\", { \"DKK\": [\"Dkr\", \"kr\"], \"JPY\": [\"JP¥\", \"¥\"], \"NOK\": [\"kr\"], \"SEK\": [\"Skr\", \"kr\"], \"THB\": [\"฿\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEJ,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,gBAAgB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}