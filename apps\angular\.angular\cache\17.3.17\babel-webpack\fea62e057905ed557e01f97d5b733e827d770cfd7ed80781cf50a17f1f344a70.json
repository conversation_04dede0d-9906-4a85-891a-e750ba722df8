{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"jv\", [[\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"], u, u], u, [[\"A\", \"S\", \"S\", \"R\", \"K\", \"J\", \"S\"], [\"Ahad\", \"<PERSON>\", \"Sel\", \"Rab\", \"<PERSON>m\", \"<PERSON><PERSON>\", \"Sab\"], [\"Ahad\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sabtu\"], [\"Ahad\", \"<PERSON>\", \"<PERSON>l\", \"Rab\", \"Kam\", \"Ju<PERSON>\", \"Sab\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"<PERSON>\", \"O\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"Agt\", \"<PERSON>\", \"Okt\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>uari\", \"<PERSON>ru<PERSON>\", \"<PERSON>t\", \"April\", \"<PERSON>\", \"<PERSON>i\", \"<PERSON>i\", \"Agus<PERSON>\", \"September\", \"<PERSON>tober\", \"November\", \"<PERSON>ember\"]], u, [[\"<PERSON>\", \"<PERSON>\"], u, [\"<PERSON>kdurunge <PERSON><PERSON><PERSON>\", \"Masehi\"]], 0, [6, 0], [\"dd-M<PERSON>-y\", \"d <PERSON><PERSON> y\", \"d M<PERSON><PERSON> y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"IDR\", \"Rp\", \"Rupiah Indonesia\", {\n  \"IDR\": [\"Rp\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/jv.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"jv\", [[\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"], u, u], u, [[\"A\", \"S\", \"S\", \"R\", \"K\", \"J\", \"S\"], [\"Ahad\", \"<PERSON>\", \"Sel\", \"Rab\", \"<PERSON>m\", \"<PERSON><PERSON>\", \"Sab\"], [\"Ahad\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sabtu\"], [\"Ahad\", \"<PERSON>\", \"<PERSON>l\", \"Rab\", \"Kam\", \"Ju<PERSON>\", \"Sab\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"<PERSON>\", \"O\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"Agt\", \"<PERSON>\", \"Okt\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>uari\", \"<PERSON>ru<PERSON>\", \"<PERSON>t\", \"April\", \"<PERSON>\", \"<PERSON>i\", \"<PERSON>i\", \"Agus<PERSON>\", \"September\", \"<PERSON>tober\", \"November\", \"<PERSON>ember\"]], u, [[\"<PERSON>\", \"<PERSON>\"], u, [\"<PERSON>kdurunge <PERSON><PERSON><PERSON>\", \"Masehi\"]], 0, [6, 0], [\"dd-M<PERSON>-y\", \"d <PERSON><PERSON> y\", \"d M<PERSON><PERSON> y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"IDR\", \"Rp\", \"Rupiah Indonesia\", { \"IDR\": [\"Rp\"], \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAEA,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,SAAS,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}