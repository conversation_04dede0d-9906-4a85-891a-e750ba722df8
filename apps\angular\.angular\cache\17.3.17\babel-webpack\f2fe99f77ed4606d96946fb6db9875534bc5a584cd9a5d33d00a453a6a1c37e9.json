{"ast": null, "code": "import * as i2 from '@abp/ng.core';\nimport { CoreModule } from '@abp/ng.core';\nimport * as i1 from '@abp/ng.permission-management/proxy';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, ViewChildren, NgModule } from '@angular/core';\nimport { concat, of } from 'rxjs';\nimport { take, switchMap, finalize, tap } from 'rxjs/operators';\nimport * as i3 from '@angular/common';\nimport * as i4 from '@angular/forms';\nimport * as i5 from '@abp/ng.theme.shared';\nimport { ThemeSharedModule } from '@abp/ng.theme.shared';\nconst _c0 = [\"selectAllInThisTabsRef\"];\nconst _c1 = [\"selectAllInAllTabsRef\"];\nconst _c2 = () => ({\n  size: \"lg\",\n  scrollable: false\n});\nconst _c3 = a0 => ({\n  assignedCount: a0\n});\nfunction PermissionManagementComponent_ng_container_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpPermissionManagement::Permissions\"), \" - \", ctx_r0.entityDisplayName || ctx_r0.data.entityDisplayName, \" \");\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r5 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", count_r5.assignedCount, \")\");\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const group_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onChangeGroup(group_r4));\n    })(\"select\", function PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template_a_select_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const group_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.setDisabled(group_r4.permissions));\n    });\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_span_3_Template, 2, 1, \"span\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const count_r5 = ctx.ngIf;\n    const group_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", (ctx_r0.selectedGroup == null ? null : ctx_r0.selectedGroup.name) === (group_r4 == null ? null : group_r4.name));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"font-weight-bold\", count_r5.assignedCount);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", group_r4 == null ? null : group_r4.displayName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", count_r5.assignedCount > 0);\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 23);\n    i0.ɵɵtemplate(1, PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template, 4, 6, \"a\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction1(1, _c3, ctx_r0.getAssignedCount(group_r4.name)));\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_div_21_ng_container_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", provider_r9.providerName, \": \", provider_r9.providerKey, \"\");\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_div_21_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermissionManagementComponent_ng_container_1_ng_template_3_div_21_ng_container_5_span_1_Template, 2, 2, \"span\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const permission_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", permission_r7.grantedProviders);\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"input\", 27, 5);\n    i0.ɵɵlistener(\"click\", function PermissionManagementComponent_ng_container_1_ng_template_3_div_21_Template_input_click_1_listener() {\n      const permission_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const permissionCheckbox_r8 = i0.ɵɵreference(2);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onClickCheckbox(permission_r7, permissionCheckbox_r8.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵtemplate(5, PermissionManagementComponent_ng_container_1_ng_template_3_div_21_ng_container_5_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const permission_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngStyle\", permission_r7.style);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.getChecked(permission_r7.name))(\"value\", ctx_r0.getChecked(permission_r7.name))(\"disabled\", ctx_r0.isGrantedByOtherProviderName(permission_r7.grantedProviders));\n    i0.ɵɵattribute(\"id\", permission_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"for\", permission_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", permission_r7.displayName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.hideBadges);\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"input\", 11, 3);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectAllTab, $event) || (ctx_r0.selectAllTab = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onClickSelectAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 12);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"hr\", 13);\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"ul\", 15);\n    i0.ɵɵtemplate(11, PermissionManagementComponent_ng_container_1_ng_template_3_li_11_Template, 2, 3, \"li\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 17)(13, \"div\", 18)(14, \"div\", 10)(15, \"input\", 19, 4);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectThisTab, $event) || (ctx_r0.selectThisTab = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onClickSelectThisTab());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 20);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(20, \"hr\", 21);\n    i0.ɵɵtemplate(21, PermissionManagementComponent_ng_container_1_ng_template_3_div_21_Template, 6, 8, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectAllTab);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabledSelectAllInAllTabs);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 10, \"AbpPermissionManagement::SelectAllInAllTabs\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.data.groups)(\"ngForTrackBy\", ctx_r0.trackByFn);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectThisTab);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disableSelectAllTab);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 12, \"AbpPermissionManagement::SelectAllInThisTab\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedGroupPermissions)(\"ngForTrackBy\", ctx_r0.trackByFn);\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"abp-button\", 32);\n    i0.ɵɵlistener(\"click\", function PermissionManagementComponent_ng_container_1_ng_template_5_Template_abp_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.submit());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpIdentity::Cancel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, \"AbpIdentity::Save\"));\n  }\n}\nfunction PermissionManagementComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermissionManagementComponent_ng_container_1_ng_template_1_Template, 3, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, PermissionManagementComponent_ng_container_1_ng_template_3_Template, 22, 14, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, PermissionManagementComponent_ng_container_1_ng_template_5_Template, 6, 6, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nclass PermissionManagementComponent {\n  get visible() {\n    return this._visible;\n  }\n  set visible(value) {\n    if (value === this._visible) return;\n    if (value) {\n      this.openModal().subscribe(() => {\n        this._visible = true;\n        this.visibleChange.emit(true);\n        concat(this.selectAllInAllTabsRef.changes, this.selectAllInThisTabsRef.changes).pipe(take(1)).subscribe(() => {\n          this.initModal();\n        });\n      });\n    } else {\n      this.setSelectedGroup(null);\n      this._visible = false;\n      this.visibleChange.emit(false);\n    }\n  }\n  constructor(service, configState) {\n    this.service = service;\n    this.configState = configState;\n    this.hideBadges = false;\n    this._visible = false;\n    this.visibleChange = new EventEmitter();\n    this.data = {\n      groups: [],\n      entityDisplayName: ''\n    };\n    this.permissions = [];\n    this.selectThisTab = false;\n    this.selectAllTab = false;\n    this.disableSelectAllTab = false;\n    this.disabledSelectAllInAllTabs = false;\n    this.modalBusy = false;\n    this.selectedGroupPermissions = [];\n    this.trackByFn = (_, item) => item.name;\n  }\n  getChecked(name) {\n    return (this.permissions.find(per => per.name === name) || {\n      isGranted: false\n    }).isGranted;\n  }\n  setSelectedGroup(group) {\n    this.selectedGroup = group;\n    if (!this.selectedGroup) {\n      this.selectedGroupPermissions = [];\n      return;\n    }\n    const margin = `margin-${document.body.dir === 'rtl' ? 'right' : 'left'}.px`;\n    const permissions = (this.data.groups.find(group => group.name === this.selectedGroup?.name) || {}).permissions || [];\n    this.selectedGroupPermissions = permissions.map(permission => ({\n      ...permission,\n      style: {\n        [margin]: findMargin(permissions, permission)\n      },\n      isGranted: (this.permissions.find(per => per.name === permission.name) || {}).isGranted\n    }));\n  }\n  setDisabled(permissions) {\n    if (permissions.length) {\n      this.disableSelectAllTab = permissions.every(permission => permission.isGranted && permission.grantedProviders?.every(p => p.providerName !== this.providerName));\n    } else {\n      this.disableSelectAllTab = false;\n    }\n  }\n  isGrantedByOtherProviderName(grantedProviders) {\n    if (grantedProviders.length) {\n      return grantedProviders.findIndex(p => p.providerName !== this.providerName) > -1;\n    }\n    return false;\n  }\n  onClickCheckbox(clickedPermission) {\n    if (clickedPermission.isGranted && this.isGrantedByOtherProviderName(clickedPermission.grantedProviders)) return;\n    this.setSelectedGroup(this.selectedGroup);\n    setTimeout(() => {\n      this.permissions = this.permissions.map(per => {\n        if (clickedPermission.name === per.name) {\n          return {\n            ...per,\n            isGranted: !per.isGranted\n          };\n        } else if (clickedPermission.name === per.parentName && clickedPermission.isGranted) {\n          return {\n            ...per,\n            isGranted: false\n          };\n        } else if (clickedPermission.parentName === per.name && !clickedPermission.isGranted) {\n          return {\n            ...per,\n            isGranted: true\n          };\n        }\n        return per;\n      });\n      this.updateSelectedGroupPermissions(clickedPermission);\n      this.setTabCheckboxState();\n      this.setGrantCheckboxState();\n      this.setParentClicked(clickedPermission);\n    }, 0);\n  }\n  setParentClicked(clickedPermissions) {\n    let childPermissionGrantedCount = 0;\n    let parentPermission;\n    if (clickedPermissions.parentName) {\n      this.permissions.forEach(per => {\n        if (per.name === clickedPermissions.parentName) {\n          parentPermission = per;\n        }\n      });\n      this.permissions.forEach(per => {\n        if (parentPermission.name === per.parentName) {\n          per.isGranted && childPermissionGrantedCount++;\n        }\n      });\n      if (childPermissionGrantedCount === 1 && !parentPermission.isGranted) {\n        this.permissions = this.permissions.map(per => {\n          if (per.name === parentPermission.name) {\n            per.isGranted = true;\n          }\n          return per;\n        });\n      }\n      return;\n    }\n    this.permissions = this.permissions.map(per => {\n      if (per.parentName === clickedPermissions.name) {\n        per.isGranted = false;\n      }\n      return per;\n    });\n  }\n  updateSelectedGroupPermissions(clickedPermissions) {\n    this.selectedGroupPermissions = this.selectedGroupPermissions.map(per => {\n      if (per.name === clickedPermissions.name) {\n        per.isGranted = !per.isGranted;\n      }\n      return per;\n    });\n  }\n  setTabCheckboxState() {\n    const selectableGroupPermissions = this.selectedGroupPermissions.filter(per => per.grantedProviders.every(p => p.providerName === this.providerName));\n    const selectedPermissions = selectableGroupPermissions.filter(per => per.isGranted);\n    const element = document.querySelector('#select-all-in-this-tabs');\n    if (selectedPermissions.length === selectableGroupPermissions.length) {\n      element.indeterminate = false;\n      this.selectThisTab = true;\n    } else if (selectedPermissions.length === 0) {\n      element.indeterminate = false;\n      this.selectThisTab = false;\n    } else {\n      element.indeterminate = true;\n    }\n  }\n  setGrantCheckboxState() {\n    const selectablePermissions = this.permissions.filter(per => per.grantedProviders.every(p => p.providerName === this.providerName));\n    const selectedAllPermissions = selectablePermissions.filter(per => per.isGranted);\n    const checkboxElement = document.querySelector('#select-all-in-all-tabs');\n    if (selectedAllPermissions.length === selectablePermissions.length) {\n      checkboxElement.indeterminate = false;\n      this.selectAllTab = true;\n    } else if (selectedAllPermissions.length === 0) {\n      checkboxElement.indeterminate = false;\n      this.selectAllTab = false;\n    } else {\n      checkboxElement.indeterminate = true;\n    }\n  }\n  onClickSelectThisTab() {\n    this.selectedGroupPermissions.forEach(permission => {\n      if (permission.isGranted && this.isGrantedByOtherProviderName(permission.grantedProviders)) return;\n      const index = this.permissions.findIndex(per => per.name === permission.name);\n      this.permissions = [...this.permissions.slice(0, index), {\n        ...this.permissions[index],\n        isGranted: !this.selectThisTab\n      }, ...this.permissions.slice(index + 1)];\n    });\n    this.setGrantCheckboxState();\n  }\n  onClickSelectAll() {\n    this.permissions = this.permissions.map(permission => ({\n      ...permission,\n      isGranted: this.isGrantedByOtherProviderName(permission.grantedProviders) || !this.selectAllTab\n    }));\n    if (!this.disableSelectAllTab) {\n      this.selectThisTab = !this.selectAllTab;\n      this.setTabCheckboxState();\n    }\n    this.onChangeGroup(this.selectedGroup);\n  }\n  onChangeGroup(group) {\n    this.setDisabled(group.permissions);\n    this.setSelectedGroup(group);\n    this.setTabCheckboxState();\n  }\n  submit() {\n    const unchangedPermissions = getPermissions(this.data.groups);\n    const changedPermissions = this.permissions.filter(per => (unchangedPermissions.find(unchanged => unchanged.name === per.name) || {}).isGranted === per.isGranted ? false : true).map(({\n      name,\n      isGranted\n    }) => ({\n      name,\n      isGranted\n    }));\n    if (!changedPermissions.length) {\n      this.visible = false;\n      return;\n    }\n    this.modalBusy = true;\n    this.service.update(this.providerName, this.providerKey, {\n      permissions: changedPermissions\n    }).pipe(switchMap(() => this.shouldFetchAppConfig() ? this.configState.refreshAppState() : of(null)), finalize(() => this.modalBusy = false)).subscribe(() => {\n      this.visible = false;\n    });\n  }\n  openModal() {\n    if (!this.providerKey || !this.providerName) {\n      throw new Error('Provider Key and Provider Name are required.');\n    }\n    return this.service.get(this.providerName, this.providerKey).pipe(tap(permissionRes => {\n      this.data = permissionRes;\n      this.permissions = getPermissions(permissionRes.groups);\n      this.setSelectedGroup(permissionRes.groups[0]);\n      this.disabledSelectAllInAllTabs = this.permissions.every(per => per.isGranted && per.grantedProviders.every(provider => provider.providerName !== this.providerName));\n    }));\n  }\n  initModal() {\n    // TODO: Refactor\n    setTimeout(() => {\n      this.setDisabled(this.selectedGroup?.permissions || []);\n      this.setTabCheckboxState();\n      this.setGrantCheckboxState();\n    });\n  }\n  getAssignedCount(groupName) {\n    return this.permissions.reduce((acc, val) => val.groupName === groupName && val.isGranted ? acc + 1 : acc, 0);\n  }\n  shouldFetchAppConfig() {\n    const currentUser = this.configState.getOne('currentUser');\n    if (this.providerName === 'R') return currentUser.roles.some(role => role === this.providerKey);\n    if (this.providerName === 'U') return currentUser.id === this.providerKey;\n    return false;\n  }\n  static {\n    this.ɵfac = function PermissionManagementComponent_Factory(t) {\n      return new (t || PermissionManagementComponent)(i0.ɵɵdirectiveInject(i1.PermissionsService), i0.ɵɵdirectiveInject(i2.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PermissionManagementComponent,\n      selectors: [[\"abp-permission-management\"]],\n      viewQuery: function PermissionManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.selectAllInThisTabsRef = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.selectAllInAllTabsRef = _t);\n        }\n      },\n      inputs: {\n        providerName: \"providerName\",\n        providerKey: \"providerKey\",\n        hideBadges: \"hideBadges\",\n        entityDisplayName: \"entityDisplayName\",\n        visible: \"visible\"\n      },\n      outputs: {\n        visibleChange: \"visibleChange\"\n      },\n      exportAs: [\"abpPermissionManagement\"],\n      decls: 2,\n      vars: 5,\n      consts: [[\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [\"selectAllInAllTabsRef\", \"\"], [\"selectAllInThisTabsRef\", \"\"], [\"permissionCheckbox\", \"\"], [3, \"visibleChange\", \"visible\", \"busy\", \"options\"], [4, \"ngIf\"], [1, \"row\"], [1, \"col-md-4\", \"scroll-in-modal\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"select-all-in-all-tabs\", \"name\", \"select-all-in-all-tabs\", 1, \"form-check-input\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"disabled\"], [\"for\", \"select-all-in-all-tabs\", 1, \"form-check-label\"], [1, \"mt-2\", \"mb-2\"], [1, \"overflow-auto\"], [1, \"nav\", \"nav-pills\", \"flex-column\"], [\"class\", \"nav-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"col-md-8\", \"scroll-in-modal\"], [1, \"ps-1\"], [\"type\", \"checkbox\", \"id\", \"select-all-in-this-tabs\", \"name\", \"select-all-in-this-tabs\", 1, \"form-check-input\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"disabled\"], [\"for\", \"select-all-in-this-tabs\", 1, \"form-check-label\"], [1, \"my-2\"], [\"class\", \"form-check mb-2\", 3, \"ngStyle\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"nav-item\"], [\"class\", \"nav-link pointer\", 3, \"active\", \"click\", \"select\", 4, \"ngIf\"], [1, \"nav-link\", \"pointer\", 3, \"click\", \"select\"], [1, \"form-check\", \"mb-2\", 3, \"ngStyle\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"click\", \"checked\", \"value\", \"disabled\"], [1, \"form-check-label\"], [\"class\", \"badge bg-light text-dark\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"bg-light\", \"text-dark\"], [\"type\", \"button\", \"abpClose\", \"\", 1, \"btn\", \"btn-outline-primary\"], [\"iconClass\", \"fa fa-check\", 3, \"click\"]],\n      template: function PermissionManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"abp-modal\", 6);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function PermissionManagementComponent_Template_abp_modal_visibleChange_0_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(1, PermissionManagementComponent_ng_container_1_Template, 7, 0, \"ng-container\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"busy\", ctx.modalBusy)(\"options\", i0.ɵɵpureFunction0(4, _c2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.data.entityDisplayName || ctx.entityDisplayName);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i3.NgStyle, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgModel, i5.ButtonComponent, i5.ModalComponent, i5.ModalCloseDirective, i2.LocalizationPipe],\n      styles: [\".overflow-scroll[_ngcontent-%COMP%]{max-height:70vh;overflow-y:scroll}.scroll-in-modal[_ngcontent-%COMP%]{overflow:auto;max-height:calc(100vh - 15rem)}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PermissionManagementComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-permission-management',\n      exportAs: 'abpPermissionManagement',\n      template: \"<abp-modal [(visible)]=\\\"visible\\\" [busy]=\\\"modalBusy\\\" [options]=\\\"{ size: 'lg' ,scrollable:false }\\\">\\r\\n  <ng-container *ngIf=\\\"data.entityDisplayName || entityDisplayName\\\">\\r\\n    <ng-template #abpHeader>\\r\\n      <h4>\\r\\n        {{ 'AbpPermissionManagement::Permissions' | abpLocalization }} -\\r\\n        {{ entityDisplayName || data.entityDisplayName }}\\r\\n      </h4>\\r\\n    </ng-template>\\r\\n    <ng-template #abpBody>\\r\\n      <div class=\\\"row\\\">\\r\\n        <div class=\\\"col-md-4 scroll-in-modal\\\">\\r\\n          <div class=\\\"form-check mb-2\\\">\\r\\n            <input\\r\\n              #selectAllInAllTabsRef\\r\\n              type=\\\"checkbox\\\"\\r\\n              id=\\\"select-all-in-all-tabs\\\"\\r\\n              name=\\\"select-all-in-all-tabs\\\"\\r\\n              class=\\\"form-check-input\\\"\\r\\n              [(ngModel)]=\\\"selectAllTab\\\"\\r\\n              (click)=\\\"onClickSelectAll()\\\"\\r\\n              [disabled]=\\\"disabledSelectAllInAllTabs\\\"\\r\\n            />\\r\\n            <label class=\\\"form-check-label\\\" for=\\\"select-all-in-all-tabs\\\">{{\\r\\n              'AbpPermissionManagement::SelectAllInAllTabs' | abpLocalization\\r\\n            }}</label>\\r\\n          </div>\\r\\n\\r\\n          <hr class=\\\"mt-2 mb-2\\\" />\\r\\n          <div class=\\\"overflow-auto\\\">\\r\\n            <ul class=\\\"nav nav-pills flex-column\\\">\\r\\n              <li *ngFor=\\\"let group of data.groups; trackBy: trackByFn\\\" class=\\\"nav-item\\\">\\r\\n                <a\\r\\n                  *ngIf=\\\"{ assignedCount: getAssignedCount(group.name) } as count\\\"\\r\\n                  class=\\\"nav-link pointer\\\"\\r\\n                  [class.active]=\\\"selectedGroup?.name === group?.name\\\"\\r\\n                  (click)=\\\"onChangeGroup(group)\\\"\\r\\n                  (select)=\\\"setDisabled(group.permissions)\\\"\\r\\n                >\\r\\n                  <div [class.font-weight-bold]=\\\"count.assignedCount\\\">\\r\\n                    {{ group?.displayName }}\\r\\n                    <span *ngIf=\\\"count.assignedCount > 0\\\">({{ count.assignedCount }})</span>\\r\\n                  </div>\\r\\n                </a>\\r\\n              </li>\\r\\n            </ul>\\r\\n          </div>\\r\\n         </div>\\r\\n\\r\\n        <div class=\\\"col-md-8 scroll-in-modal\\\">\\r\\n          <div class=\\\"ps-1\\\">\\r\\n            <div class=\\\"form-check mb-2\\\">\\r\\n              <input\\r\\n                #selectAllInThisTabsRef\\r\\n                type=\\\"checkbox\\\"\\r\\n                id=\\\"select-all-in-this-tabs\\\"\\r\\n                name=\\\"select-all-in-this-tabs\\\"\\r\\n                class=\\\"form-check-input\\\"\\r\\n                [(ngModel)]=\\\"selectThisTab\\\"\\r\\n                [disabled]=\\\"disableSelectAllTab\\\"\\r\\n                (click)=\\\"onClickSelectThisTab()\\\"\\r\\n              />\\r\\n              <label class=\\\"form-check-label\\\" for=\\\"select-all-in-this-tabs\\\">{{\\r\\n                'AbpPermissionManagement::SelectAllInThisTab' | abpLocalization\\r\\n              }}</label>\\r\\n            </div>\\r\\n            <hr class=\\\"my-2\\\" />\\r\\n            <div\\r\\n              *ngFor=\\\"let permission of selectedGroupPermissions; let i = index; trackBy: trackByFn\\\"\\r\\n              [ngStyle]=\\\"permission.style\\\"\\r\\n              class=\\\"form-check mb-2\\\"\\r\\n            >\\r\\n              <input\\r\\n                #permissionCheckbox\\r\\n                type=\\\"checkbox\\\"\\r\\n                [checked]=\\\"getChecked(permission.name)\\\"\\r\\n                [value]=\\\"getChecked(permission.name)\\\"\\r\\n                [attr.id]=\\\"permission.name\\\"\\r\\n                class=\\\"form-check-input\\\"\\r\\n                [disabled]=\\\"isGrantedByOtherProviderName(permission.grantedProviders)\\\"\\r\\n                (click)=\\\"onClickCheckbox(permission, permissionCheckbox.value)\\\"\\r\\n              />\\r\\n              <label class=\\\"form-check-label\\\" [attr.for]=\\\"permission.name\\\"\\r\\n                >{{ permission.displayName }}\\r\\n                <ng-container *ngIf=\\\"!hideBadges\\\">\\r\\n                  <span\\r\\n                    *ngFor=\\\"let provider of permission.grantedProviders\\\"\\r\\n                    class=\\\"badge bg-light text-dark\\\"\\r\\n                    >{{ provider.providerName }}: {{ provider.providerKey }}</span\\r\\n                  >\\r\\n                </ng-container>\\r\\n              </label>\\r\\n            </div>\\r\\n          </div>\\r\\n        </div>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n    <ng-template #abpFooter>\\r\\n      <button type=\\\"button\\\" class=\\\"btn btn-outline-primary\\\" abpClose>\\r\\n        {{ 'AbpIdentity::Cancel' | abpLocalization }}\\r\\n      </button>\\r\\n      <abp-button iconClass=\\\"fa fa-check\\\" (click)=\\\"submit()\\\">{{\\r\\n        'AbpIdentity::Save' | abpLocalization\\r\\n      }}</abp-button>\\r\\n    </ng-template>\\r\\n  </ng-container>\\r\\n</abp-modal>\\r\\n\",\n      styles: [\".overflow-scroll{max-height:70vh;overflow-y:scroll}.scroll-in-modal{overflow:auto;max-height:calc(100vh - 15rem)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.PermissionsService\n  }, {\n    type: i2.ConfigStateService\n  }], {\n    providerName: [{\n      type: Input\n    }],\n    providerKey: [{\n      type: Input\n    }],\n    hideBadges: [{\n      type: Input\n    }],\n    entityDisplayName: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    selectAllInThisTabsRef: [{\n      type: ViewChildren,\n      args: ['selectAllInThisTabsRef']\n    }],\n    selectAllInAllTabsRef: [{\n      type: ViewChildren,\n      args: ['selectAllInAllTabsRef']\n    }]\n  });\n})();\nfunction findMargin(permissions, permission) {\n  const parentPermission = permissions.find(per => per.name === permission.parentName);\n  if (parentPermission && parentPermission.parentName) {\n    let margin = 20;\n    return margin += findMargin(permissions, parentPermission);\n  }\n  return parentPermission ? 20 : 0;\n}\nfunction getPermissions(groups) {\n  return groups.reduce((acc, val) => [...acc, ...val.permissions.map(p => ({\n    ...p,\n    groupName: val.name || ''\n  }))], []);\n}\nclass PermissionManagementModule {\n  static {\n    this.ɵfac = function PermissionManagementModule_Factory(t) {\n      return new (t || PermissionManagementModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PermissionManagementModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule, ThemeSharedModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PermissionManagementModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [PermissionManagementComponent],\n      imports: [CoreModule, ThemeSharedModule],\n      exports: [PermissionManagementComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PermissionManagementComponent, PermissionManagementModule };", "map": {"version": 3, "names": ["i2", "CoreModule", "i1", "i0", "EventEmitter", "Component", "Input", "Output", "ViewChildren", "NgModule", "concat", "of", "take", "switchMap", "finalize", "tap", "i3", "i4", "i5", "ThemeSharedModule", "_c0", "_c1", "_c2", "size", "scrollable", "_c3", "a0", "assignedCount", "PermissionManagementComponent_ng_container_1_ng_template_1_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵpipe", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "entityDisplayName", "data", "PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_span_3_Template", "count_r5", "ngIf", "ɵɵtextInterpolate1", "PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template_a_click_0_listener", "ɵɵrestoreView", "group_r4", "$implicit", "ɵɵresetView", "onChangeGroup", "PermissionManagementComponent_ng_container_1_ng_template_3_li_11_a_1_Template_a_select_0_listener", "setDisabled", "permissions", "ɵɵtemplate", "ɵɵclassProp", "selectedGroup", "name", "displayName", "ɵɵproperty", "PermissionManagementComponent_ng_container_1_ng_template_3_li_11_Template", "ɵɵpureFunction1", "getAssignedCount", "PermissionManagementComponent_ng_container_1_ng_template_3_div_21_ng_container_5_span_1_Template", "provider_r9", "providerName", "providerKey", "PermissionManagementComponent_ng_container_1_ng_template_3_div_21_ng_container_5_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "permission_r7", "grantedProviders", "PermissionManagementComponent_ng_container_1_ng_template_3_div_21_Template", "_r6", "PermissionManagementComponent_ng_container_1_ng_template_3_div_21_Template_input_click_1_listener", "permissionCheckbox_r8", "ɵɵreference", "onClickCheckbox", "value", "style", "getChecked", "isGrantedByOtherProviderName", "ɵɵattribute", "hideBadges", "PermissionManagementComponent_ng_container_1_ng_template_3_Template", "_r2", "ɵɵtwoWayListener", "PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_ngModelChange_3_listener", "$event", "ɵɵtwoWayBindingSet", "selectAllTab", "PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_click_3_listener", "onClickSelectAll", "ɵɵelement", "PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_ngModelChange_15_listener", "selectThisTab", "PermissionManagementComponent_ng_container_1_ng_template_3_Template_input_click_15_listener", "onClickSelectThisTab", "ɵɵtwoWayProperty", "disabledSelectAllInAllTabs", "ɵɵtextInterpolate", "groups", "trackByFn", "disableSelectAllTab", "selectedGroupPermissions", "PermissionManagementComponent_ng_container_1_ng_template_5_Template", "_r10", "PermissionManagementComponent_ng_container_1_ng_template_5_Template_abp_button_click_3_listener", "submit", "PermissionManagementComponent_ng_container_1_Template", "ɵɵtemplateRefExtractor", "PermissionManagementComponent", "visible", "_visible", "openModal", "subscribe", "visibleChange", "emit", "selectAllInAllTabsRef", "changes", "selectAllInThisTabsRef", "pipe", "initModal", "setSelectedGroup", "constructor", "service", "configState", "modalBusy", "_", "item", "find", "per", "isGranted", "group", "margin", "document", "body", "dir", "map", "permission", "<PERSON><PERSON><PERSON><PERSON>", "length", "every", "p", "findIndex", "clickedPermission", "setTimeout", "parentName", "updateSelectedGroupPermissions", "setTabCheckboxState", "setGrantCheckboxState", "set<PERSON><PERSON>ntClicked", "clickedPermissions", "childPermissionGrantedCount", "parentPermission", "for<PERSON>ach", "selectableGroupPermissions", "filter", "selectedPermissions", "element", "querySelector", "indeterminate", "selectablePermissions", "selectedAllPermissions", "checkboxElement", "index", "slice", "unchangedPermissions", "getPermissions", "changedPermissions", "unchanged", "update", "shouldFetchAppConfig", "refreshAppState", "Error", "get", "permissionRes", "provider", "groupName", "reduce", "acc", "val", "currentUser", "getOne", "roles", "some", "role", "id", "ɵfac", "PermissionManagementComponent_Factory", "t", "ɵɵdirectiveInject", "PermissionsService", "ConfigStateService", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "PermissionManagementComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "inputs", "outputs", "exportAs", "decls", "vars", "consts", "template", "PermissionManagementComponent_Template", "PermissionManagementComponent_Template_abp_modal_visibleChange_0_listener", "ɵɵpureFunction0", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "CheckboxControlValueAccessor", "NgControlStatus", "NgModel", "ButtonComponent", "ModalComponent", "ModalCloseDirective", "LocalizationPipe", "styles", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "PermissionManagementModule", "PermissionManagementModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@abp/ng.permission-management/fesm2022/abp-ng.permission-management.mjs"], "sourcesContent": ["import * as i2 from '@abp/ng.core';\nimport { CoreModule } from '@abp/ng.core';\nimport * as i1 from '@abp/ng.permission-management/proxy';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, ViewChildren, NgModule } from '@angular/core';\nimport { concat, of } from 'rxjs';\nimport { take, switchMap, finalize, tap } from 'rxjs/operators';\nimport * as i3 from '@angular/common';\nimport * as i4 from '@angular/forms';\nimport * as i5 from '@abp/ng.theme.shared';\nimport { ThemeSharedModule } from '@abp/ng.theme.shared';\n\nclass PermissionManagementComponent {\n    get visible() {\n        return this._visible;\n    }\n    set visible(value) {\n        if (value === this._visible)\n            return;\n        if (value) {\n            this.openModal().subscribe(() => {\n                this._visible = true;\n                this.visibleChange.emit(true);\n                concat(this.selectAllInAllTabsRef.changes, this.selectAllInThisTabsRef.changes)\n                    .pipe(take(1))\n                    .subscribe(() => {\n                    this.initModal();\n                });\n            });\n        }\n        else {\n            this.setSelectedGroup(null);\n            this._visible = false;\n            this.visibleChange.emit(false);\n        }\n    }\n    constructor(service, configState) {\n        this.service = service;\n        this.configState = configState;\n        this.hideBadges = false;\n        this._visible = false;\n        this.visibleChange = new EventEmitter();\n        this.data = { groups: [], entityDisplayName: '' };\n        this.permissions = [];\n        this.selectThisTab = false;\n        this.selectAllTab = false;\n        this.disableSelectAllTab = false;\n        this.disabledSelectAllInAllTabs = false;\n        this.modalBusy = false;\n        this.selectedGroupPermissions = [];\n        this.trackByFn = (_, item) => item.name;\n    }\n    getChecked(name) {\n        return (this.permissions.find(per => per.name === name) || { isGranted: false }).isGranted;\n    }\n    setSelectedGroup(group) {\n        this.selectedGroup = group;\n        if (!this.selectedGroup) {\n            this.selectedGroupPermissions = [];\n            return;\n        }\n        const margin = `margin-${document.body.dir === 'rtl' ? 'right' : 'left'}.px`;\n        const permissions = (this.data.groups.find(group => group.name === this.selectedGroup?.name) || {}).permissions ||\n            [];\n        this.selectedGroupPermissions = permissions.map(permission => ({\n            ...permission,\n            style: { [margin]: findMargin(permissions, permission) },\n            isGranted: (this.permissions.find(per => per.name === permission.name) || {}).isGranted,\n        }));\n    }\n    setDisabled(permissions) {\n        if (permissions.length) {\n            this.disableSelectAllTab = permissions.every(permission => permission.isGranted &&\n                permission.grantedProviders?.every(p => p.providerName !== this.providerName));\n        }\n        else {\n            this.disableSelectAllTab = false;\n        }\n    }\n    isGrantedByOtherProviderName(grantedProviders) {\n        if (grantedProviders.length) {\n            return grantedProviders.findIndex(p => p.providerName !== this.providerName) > -1;\n        }\n        return false;\n    }\n    onClickCheckbox(clickedPermission) {\n        if (clickedPermission.isGranted &&\n            this.isGrantedByOtherProviderName(clickedPermission.grantedProviders))\n            return;\n        this.setSelectedGroup(this.selectedGroup);\n        setTimeout(() => {\n            this.permissions = this.permissions.map(per => {\n                if (clickedPermission.name === per.name) {\n                    return { ...per, isGranted: !per.isGranted };\n                }\n                else if (clickedPermission.name === per.parentName && clickedPermission.isGranted) {\n                    return { ...per, isGranted: false };\n                }\n                else if (clickedPermission.parentName === per.name && !clickedPermission.isGranted) {\n                    return { ...per, isGranted: true };\n                }\n                return per;\n            });\n            this.updateSelectedGroupPermissions(clickedPermission);\n            this.setTabCheckboxState();\n            this.setGrantCheckboxState();\n            this.setParentClicked(clickedPermission);\n        }, 0);\n    }\n    setParentClicked(clickedPermissions) {\n        let childPermissionGrantedCount = 0;\n        let parentPermission;\n        if (clickedPermissions.parentName) {\n            this.permissions.forEach(per => {\n                if (per.name === clickedPermissions.parentName) {\n                    parentPermission = per;\n                }\n            });\n            this.permissions.forEach(per => {\n                if (parentPermission.name === per.parentName) {\n                    per.isGranted && childPermissionGrantedCount++;\n                }\n            });\n            if (childPermissionGrantedCount === 1 && !parentPermission.isGranted) {\n                this.permissions = this.permissions.map(per => {\n                    if (per.name === parentPermission.name) {\n                        per.isGranted = true;\n                    }\n                    return per;\n                });\n            }\n            return;\n        }\n        this.permissions = this.permissions.map(per => {\n            if (per.parentName === clickedPermissions.name) {\n                per.isGranted = false;\n            }\n            return per;\n        });\n    }\n    updateSelectedGroupPermissions(clickedPermissions) {\n        this.selectedGroupPermissions = this.selectedGroupPermissions.map(per => {\n            if (per.name === clickedPermissions.name) {\n                per.isGranted = !per.isGranted;\n            }\n            return per;\n        });\n    }\n    setTabCheckboxState() {\n        const selectableGroupPermissions = this.selectedGroupPermissions.filter(per => per.grantedProviders.every(p => p.providerName === this.providerName));\n        const selectedPermissions = selectableGroupPermissions.filter(per => per.isGranted);\n        const element = document.querySelector('#select-all-in-this-tabs');\n        if (selectedPermissions.length === selectableGroupPermissions.length) {\n            element.indeterminate = false;\n            this.selectThisTab = true;\n        }\n        else if (selectedPermissions.length === 0) {\n            element.indeterminate = false;\n            this.selectThisTab = false;\n        }\n        else {\n            element.indeterminate = true;\n        }\n    }\n    setGrantCheckboxState() {\n        const selectablePermissions = this.permissions.filter(per => per.grantedProviders.every(p => p.providerName === this.providerName));\n        const selectedAllPermissions = selectablePermissions.filter(per => per.isGranted);\n        const checkboxElement = document.querySelector('#select-all-in-all-tabs');\n        if (selectedAllPermissions.length === selectablePermissions.length) {\n            checkboxElement.indeterminate = false;\n            this.selectAllTab = true;\n        }\n        else if (selectedAllPermissions.length === 0) {\n            checkboxElement.indeterminate = false;\n            this.selectAllTab = false;\n        }\n        else {\n            checkboxElement.indeterminate = true;\n        }\n    }\n    onClickSelectThisTab() {\n        this.selectedGroupPermissions.forEach(permission => {\n            if (permission.isGranted && this.isGrantedByOtherProviderName(permission.grantedProviders))\n                return;\n            const index = this.permissions.findIndex(per => per.name === permission.name);\n            this.permissions = [\n                ...this.permissions.slice(0, index),\n                { ...this.permissions[index], isGranted: !this.selectThisTab },\n                ...this.permissions.slice(index + 1),\n            ];\n        });\n        this.setGrantCheckboxState();\n    }\n    onClickSelectAll() {\n        this.permissions = this.permissions.map(permission => ({\n            ...permission,\n            isGranted: this.isGrantedByOtherProviderName(permission.grantedProviders) || !this.selectAllTab,\n        }));\n        if (!this.disableSelectAllTab) {\n            this.selectThisTab = !this.selectAllTab;\n            this.setTabCheckboxState();\n        }\n        this.onChangeGroup(this.selectedGroup);\n    }\n    onChangeGroup(group) {\n        this.setDisabled(group.permissions);\n        this.setSelectedGroup(group);\n        this.setTabCheckboxState();\n    }\n    submit() {\n        const unchangedPermissions = getPermissions(this.data.groups);\n        const changedPermissions = this.permissions\n            .filter(per => (unchangedPermissions.find(unchanged => unchanged.name === per.name) || {}).isGranted ===\n            per.isGranted\n            ? false\n            : true)\n            .map(({ name, isGranted }) => ({ name, isGranted }));\n        if (!changedPermissions.length) {\n            this.visible = false;\n            return;\n        }\n        this.modalBusy = true;\n        this.service\n            .update(this.providerName, this.providerKey, { permissions: changedPermissions })\n            .pipe(switchMap(() => this.shouldFetchAppConfig() ? this.configState.refreshAppState() : of(null)), finalize(() => (this.modalBusy = false)))\n            .subscribe(() => {\n            this.visible = false;\n        });\n    }\n    openModal() {\n        if (!this.providerKey || !this.providerName) {\n            throw new Error('Provider Key and Provider Name are required.');\n        }\n        return this.service.get(this.providerName, this.providerKey).pipe(tap((permissionRes) => {\n            this.data = permissionRes;\n            this.permissions = getPermissions(permissionRes.groups);\n            this.setSelectedGroup(permissionRes.groups[0]);\n            this.disabledSelectAllInAllTabs = this.permissions.every(per => per.isGranted &&\n                per.grantedProviders.every(provider => provider.providerName !== this.providerName));\n        }));\n    }\n    initModal() {\n        // TODO: Refactor\n        setTimeout(() => {\n            this.setDisabled(this.selectedGroup?.permissions || []);\n            this.setTabCheckboxState();\n            this.setGrantCheckboxState();\n        });\n    }\n    getAssignedCount(groupName) {\n        return this.permissions.reduce((acc, val) => (val.groupName === groupName && val.isGranted ? acc + 1 : acc), 0);\n    }\n    shouldFetchAppConfig() {\n        const currentUser = this.configState.getOne('currentUser');\n        if (this.providerName === 'R')\n            return currentUser.roles.some(role => role === this.providerKey);\n        if (this.providerName === 'U')\n            return currentUser.id === this.providerKey;\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PermissionManagementComponent, deps: [{ token: i1.PermissionsService }, { token: i2.ConfigStateService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: PermissionManagementComponent, selector: \"abp-permission-management\", inputs: { providerName: \"providerName\", providerKey: \"providerKey\", hideBadges: \"hideBadges\", entityDisplayName: \"entityDisplayName\", visible: \"visible\" }, outputs: { visibleChange: \"visibleChange\" }, viewQueries: [{ propertyName: \"selectAllInThisTabsRef\", predicate: [\"selectAllInThisTabsRef\"], descendants: true }, { propertyName: \"selectAllInAllTabsRef\", predicate: [\"selectAllInAllTabsRef\"], descendants: true }], exportAs: [\"abpPermissionManagement\"], ngImport: i0, template: \"<abp-modal [(visible)]=\\\"visible\\\" [busy]=\\\"modalBusy\\\" [options]=\\\"{ size: 'lg' ,scrollable:false }\\\">\\r\\n  <ng-container *ngIf=\\\"data.entityDisplayName || entityDisplayName\\\">\\r\\n    <ng-template #abpHeader>\\r\\n      <h4>\\r\\n        {{ 'AbpPermissionManagement::Permissions' | abpLocalization }} -\\r\\n        {{ entityDisplayName || data.entityDisplayName }}\\r\\n      </h4>\\r\\n    </ng-template>\\r\\n    <ng-template #abpBody>\\r\\n      <div class=\\\"row\\\">\\r\\n        <div class=\\\"col-md-4 scroll-in-modal\\\">\\r\\n          <div class=\\\"form-check mb-2\\\">\\r\\n            <input\\r\\n              #selectAllInAllTabsRef\\r\\n              type=\\\"checkbox\\\"\\r\\n              id=\\\"select-all-in-all-tabs\\\"\\r\\n              name=\\\"select-all-in-all-tabs\\\"\\r\\n              class=\\\"form-check-input\\\"\\r\\n              [(ngModel)]=\\\"selectAllTab\\\"\\r\\n              (click)=\\\"onClickSelectAll()\\\"\\r\\n              [disabled]=\\\"disabledSelectAllInAllTabs\\\"\\r\\n            />\\r\\n            <label class=\\\"form-check-label\\\" for=\\\"select-all-in-all-tabs\\\">{{\\r\\n              'AbpPermissionManagement::SelectAllInAllTabs' | abpLocalization\\r\\n            }}</label>\\r\\n          </div>\\r\\n\\r\\n          <hr class=\\\"mt-2 mb-2\\\" />\\r\\n          <div class=\\\"overflow-auto\\\">\\r\\n            <ul class=\\\"nav nav-pills flex-column\\\">\\r\\n              <li *ngFor=\\\"let group of data.groups; trackBy: trackByFn\\\" class=\\\"nav-item\\\">\\r\\n                <a\\r\\n                  *ngIf=\\\"{ assignedCount: getAssignedCount(group.name) } as count\\\"\\r\\n                  class=\\\"nav-link pointer\\\"\\r\\n                  [class.active]=\\\"selectedGroup?.name === group?.name\\\"\\r\\n                  (click)=\\\"onChangeGroup(group)\\\"\\r\\n                  (select)=\\\"setDisabled(group.permissions)\\\"\\r\\n                >\\r\\n                  <div [class.font-weight-bold]=\\\"count.assignedCount\\\">\\r\\n                    {{ group?.displayName }}\\r\\n                    <span *ngIf=\\\"count.assignedCount > 0\\\">({{ count.assignedCount }})</span>\\r\\n                  </div>\\r\\n                </a>\\r\\n              </li>\\r\\n            </ul>\\r\\n          </div>\\r\\n         </div>\\r\\n\\r\\n        <div class=\\\"col-md-8 scroll-in-modal\\\">\\r\\n          <div class=\\\"ps-1\\\">\\r\\n            <div class=\\\"form-check mb-2\\\">\\r\\n              <input\\r\\n                #selectAllInThisTabsRef\\r\\n                type=\\\"checkbox\\\"\\r\\n                id=\\\"select-all-in-this-tabs\\\"\\r\\n                name=\\\"select-all-in-this-tabs\\\"\\r\\n                class=\\\"form-check-input\\\"\\r\\n                [(ngModel)]=\\\"selectThisTab\\\"\\r\\n                [disabled]=\\\"disableSelectAllTab\\\"\\r\\n                (click)=\\\"onClickSelectThisTab()\\\"\\r\\n              />\\r\\n              <label class=\\\"form-check-label\\\" for=\\\"select-all-in-this-tabs\\\">{{\\r\\n                'AbpPermissionManagement::SelectAllInThisTab' | abpLocalization\\r\\n              }}</label>\\r\\n            </div>\\r\\n            <hr class=\\\"my-2\\\" />\\r\\n            <div\\r\\n              *ngFor=\\\"let permission of selectedGroupPermissions; let i = index; trackBy: trackByFn\\\"\\r\\n              [ngStyle]=\\\"permission.style\\\"\\r\\n              class=\\\"form-check mb-2\\\"\\r\\n            >\\r\\n              <input\\r\\n                #permissionCheckbox\\r\\n                type=\\\"checkbox\\\"\\r\\n                [checked]=\\\"getChecked(permission.name)\\\"\\r\\n                [value]=\\\"getChecked(permission.name)\\\"\\r\\n                [attr.id]=\\\"permission.name\\\"\\r\\n                class=\\\"form-check-input\\\"\\r\\n                [disabled]=\\\"isGrantedByOtherProviderName(permission.grantedProviders)\\\"\\r\\n                (click)=\\\"onClickCheckbox(permission, permissionCheckbox.value)\\\"\\r\\n              />\\r\\n              <label class=\\\"form-check-label\\\" [attr.for]=\\\"permission.name\\\"\\r\\n                >{{ permission.displayName }}\\r\\n                <ng-container *ngIf=\\\"!hideBadges\\\">\\r\\n                  <span\\r\\n                    *ngFor=\\\"let provider of permission.grantedProviders\\\"\\r\\n                    class=\\\"badge bg-light text-dark\\\"\\r\\n                    >{{ provider.providerName }}: {{ provider.providerKey }}</span\\r\\n                  >\\r\\n                </ng-container>\\r\\n              </label>\\r\\n            </div>\\r\\n          </div>\\r\\n        </div>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n    <ng-template #abpFooter>\\r\\n      <button type=\\\"button\\\" class=\\\"btn btn-outline-primary\\\" abpClose>\\r\\n        {{ 'AbpIdentity::Cancel' | abpLocalization }}\\r\\n      </button>\\r\\n      <abp-button iconClass=\\\"fa fa-check\\\" (click)=\\\"submit()\\\">{{\\r\\n        'AbpIdentity::Save' | abpLocalization\\r\\n      }}</abp-button>\\r\\n    </ng-template>\\r\\n  </ng-container>\\r\\n</abp-modal>\\r\\n\", styles: [\".overflow-scroll{max-height:70vh;overflow-y:scroll}.scroll-in-modal{overflow:auto;max-height:calc(100vh - 15rem)}\\n\"], dependencies: [{ kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i4.CheckboxControlValueAccessor, selector: \"input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]\" }, { kind: \"directive\", type: i4.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i4.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"component\", type: i5.ButtonComponent, selector: \"abp-button\", inputs: [\"buttonId\", \"buttonClass\", \"buttonType\", \"formName\", \"iconClass\", \"loading\", \"disabled\", \"attributes\"], outputs: [\"click\", \"focus\", \"blur\", \"abpClick\", \"abpFocus\", \"abpBlur\"] }, { kind: \"component\", type: i5.ModalComponent, selector: \"abp-modal\", inputs: [\"visible\", \"busy\", \"options\", \"suppressUnsavedChangesWarning\"], outputs: [\"visibleChange\", \"init\", \"appear\", \"disappear\"] }, { kind: \"directive\", type: i5.ModalCloseDirective, selector: \"[abpClose]\" }, { kind: \"pipe\", type: i2.LocalizationPipe, name: \"abpLocalization\" }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PermissionManagementComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'abp-permission-management', exportAs: 'abpPermissionManagement', template: \"<abp-modal [(visible)]=\\\"visible\\\" [busy]=\\\"modalBusy\\\" [options]=\\\"{ size: 'lg' ,scrollable:false }\\\">\\r\\n  <ng-container *ngIf=\\\"data.entityDisplayName || entityDisplayName\\\">\\r\\n    <ng-template #abpHeader>\\r\\n      <h4>\\r\\n        {{ 'AbpPermissionManagement::Permissions' | abpLocalization }} -\\r\\n        {{ entityDisplayName || data.entityDisplayName }}\\r\\n      </h4>\\r\\n    </ng-template>\\r\\n    <ng-template #abpBody>\\r\\n      <div class=\\\"row\\\">\\r\\n        <div class=\\\"col-md-4 scroll-in-modal\\\">\\r\\n          <div class=\\\"form-check mb-2\\\">\\r\\n            <input\\r\\n              #selectAllInAllTabsRef\\r\\n              type=\\\"checkbox\\\"\\r\\n              id=\\\"select-all-in-all-tabs\\\"\\r\\n              name=\\\"select-all-in-all-tabs\\\"\\r\\n              class=\\\"form-check-input\\\"\\r\\n              [(ngModel)]=\\\"selectAllTab\\\"\\r\\n              (click)=\\\"onClickSelectAll()\\\"\\r\\n              [disabled]=\\\"disabledSelectAllInAllTabs\\\"\\r\\n            />\\r\\n            <label class=\\\"form-check-label\\\" for=\\\"select-all-in-all-tabs\\\">{{\\r\\n              'AbpPermissionManagement::SelectAllInAllTabs' | abpLocalization\\r\\n            }}</label>\\r\\n          </div>\\r\\n\\r\\n          <hr class=\\\"mt-2 mb-2\\\" />\\r\\n          <div class=\\\"overflow-auto\\\">\\r\\n            <ul class=\\\"nav nav-pills flex-column\\\">\\r\\n              <li *ngFor=\\\"let group of data.groups; trackBy: trackByFn\\\" class=\\\"nav-item\\\">\\r\\n                <a\\r\\n                  *ngIf=\\\"{ assignedCount: getAssignedCount(group.name) } as count\\\"\\r\\n                  class=\\\"nav-link pointer\\\"\\r\\n                  [class.active]=\\\"selectedGroup?.name === group?.name\\\"\\r\\n                  (click)=\\\"onChangeGroup(group)\\\"\\r\\n                  (select)=\\\"setDisabled(group.permissions)\\\"\\r\\n                >\\r\\n                  <div [class.font-weight-bold]=\\\"count.assignedCount\\\">\\r\\n                    {{ group?.displayName }}\\r\\n                    <span *ngIf=\\\"count.assignedCount > 0\\\">({{ count.assignedCount }})</span>\\r\\n                  </div>\\r\\n                </a>\\r\\n              </li>\\r\\n            </ul>\\r\\n          </div>\\r\\n         </div>\\r\\n\\r\\n        <div class=\\\"col-md-8 scroll-in-modal\\\">\\r\\n          <div class=\\\"ps-1\\\">\\r\\n            <div class=\\\"form-check mb-2\\\">\\r\\n              <input\\r\\n                #selectAllInThisTabsRef\\r\\n                type=\\\"checkbox\\\"\\r\\n                id=\\\"select-all-in-this-tabs\\\"\\r\\n                name=\\\"select-all-in-this-tabs\\\"\\r\\n                class=\\\"form-check-input\\\"\\r\\n                [(ngModel)]=\\\"selectThisTab\\\"\\r\\n                [disabled]=\\\"disableSelectAllTab\\\"\\r\\n                (click)=\\\"onClickSelectThisTab()\\\"\\r\\n              />\\r\\n              <label class=\\\"form-check-label\\\" for=\\\"select-all-in-this-tabs\\\">{{\\r\\n                'AbpPermissionManagement::SelectAllInThisTab' | abpLocalization\\r\\n              }}</label>\\r\\n            </div>\\r\\n            <hr class=\\\"my-2\\\" />\\r\\n            <div\\r\\n              *ngFor=\\\"let permission of selectedGroupPermissions; let i = index; trackBy: trackByFn\\\"\\r\\n              [ngStyle]=\\\"permission.style\\\"\\r\\n              class=\\\"form-check mb-2\\\"\\r\\n            >\\r\\n              <input\\r\\n                #permissionCheckbox\\r\\n                type=\\\"checkbox\\\"\\r\\n                [checked]=\\\"getChecked(permission.name)\\\"\\r\\n                [value]=\\\"getChecked(permission.name)\\\"\\r\\n                [attr.id]=\\\"permission.name\\\"\\r\\n                class=\\\"form-check-input\\\"\\r\\n                [disabled]=\\\"isGrantedByOtherProviderName(permission.grantedProviders)\\\"\\r\\n                (click)=\\\"onClickCheckbox(permission, permissionCheckbox.value)\\\"\\r\\n              />\\r\\n              <label class=\\\"form-check-label\\\" [attr.for]=\\\"permission.name\\\"\\r\\n                >{{ permission.displayName }}\\r\\n                <ng-container *ngIf=\\\"!hideBadges\\\">\\r\\n                  <span\\r\\n                    *ngFor=\\\"let provider of permission.grantedProviders\\\"\\r\\n                    class=\\\"badge bg-light text-dark\\\"\\r\\n                    >{{ provider.providerName }}: {{ provider.providerKey }}</span\\r\\n                  >\\r\\n                </ng-container>\\r\\n              </label>\\r\\n            </div>\\r\\n          </div>\\r\\n        </div>\\r\\n      </div>\\r\\n    </ng-template>\\r\\n    <ng-template #abpFooter>\\r\\n      <button type=\\\"button\\\" class=\\\"btn btn-outline-primary\\\" abpClose>\\r\\n        {{ 'AbpIdentity::Cancel' | abpLocalization }}\\r\\n      </button>\\r\\n      <abp-button iconClass=\\\"fa fa-check\\\" (click)=\\\"submit()\\\">{{\\r\\n        'AbpIdentity::Save' | abpLocalization\\r\\n      }}</abp-button>\\r\\n    </ng-template>\\r\\n  </ng-container>\\r\\n</abp-modal>\\r\\n\", styles: [\".overflow-scroll{max-height:70vh;overflow-y:scroll}.scroll-in-modal{overflow:auto;max-height:calc(100vh - 15rem)}\\n\"] }]\n        }], ctorParameters: () => [{ type: i1.PermissionsService }, { type: i2.ConfigStateService }], propDecorators: { providerName: [{\n                type: Input\n            }], providerKey: [{\n                type: Input\n            }], hideBadges: [{\n                type: Input\n            }], entityDisplayName: [{\n                type: Input\n            }], visible: [{\n                type: Input\n            }], visibleChange: [{\n                type: Output\n            }], selectAllInThisTabsRef: [{\n                type: ViewChildren,\n                args: ['selectAllInThisTabsRef']\n            }], selectAllInAllTabsRef: [{\n                type: ViewChildren,\n                args: ['selectAllInAllTabsRef']\n            }] } });\nfunction findMargin(permissions, permission) {\n    const parentPermission = permissions.find(per => per.name === permission.parentName);\n    if (parentPermission && parentPermission.parentName) {\n        let margin = 20;\n        return (margin += findMargin(permissions, parentPermission));\n    }\n    return parentPermission ? 20 : 0;\n}\nfunction getPermissions(groups) {\n    return groups.reduce((acc, val) => [\n        ...acc,\n        ...val.permissions.map(p => ({ ...p, groupName: val.name || '' })),\n    ], []);\n}\n\nclass PermissionManagementModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PermissionManagementModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: PermissionManagementModule, declarations: [PermissionManagementComponent], imports: [CoreModule, ThemeSharedModule], exports: [PermissionManagementComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PermissionManagementModule, imports: [CoreModule, ThemeSharedModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PermissionManagementModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [PermissionManagementComponent],\n                    imports: [CoreModule, ThemeSharedModule],\n                    exports: [PermissionManagementComponent],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PermissionManagementComponent, PermissionManagementModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,cAAc;AAClC,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC9F,SAASC,MAAM,EAAEC,EAAE,QAAQ,MAAM;AACjC,SAASC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,gBAAgB;AAC/D,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,iBAAiB,QAAQ,sBAAsB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAAC,IAAA;EAAAC,UAAA;AAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,aAAA,EAAAD;AAAA;AAAA,SAAAE,oEAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0P2C1B,EAAE,CAAA4B,cAAA,QACkwB,CAAC;IADrwB5B,EAAE,CAAA6B,MAAA,EACq5B,CAAC;IADx5B7B,EAAE,CAAA8B,MAAA;IAAF9B,EAAE,CAAA+B,YAAA,CAC05B,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,MAAA,GAD75BhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAkC,SAAA,CACq5B,CAAC;IADx5BlC,EAAE,CAAAmC,kBAAA,MAAFnC,EAAE,CAAAoC,WAAA,uDAAAJ,MAAA,CAAAK,iBAAA,IAAAL,MAAA,CAAAM,IAAA,CAAAD,iBAAA,KACq5B,CAAC;EAAA;AAAA;AAAA,SAAAE,qFAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADx5B1B,EAAE,CAAA4B,cAAA,UACw9E,CAAC;IAD39E5B,EAAE,CAAA6B,MAAA,EACm/E,CAAC;IADt/E7B,EAAE,CAAA+B,YAAA,CAC0/E,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAc,QAAA,GAD7/ExC,EAAE,CAAAiC,aAAA,GAAAQ,IAAA;IAAFzC,EAAE,CAAAkC,SAAA,CACm/E,CAAC;IADt/ElC,EAAE,CAAA0C,kBAAA,MAAAF,QAAA,CAAAhB,aAAA,KACm/E,CAAC;EAAA;AAAA;AAAA,SAAAmB,8EAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkB,GAAA,GADt/E5C,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAA4B,cAAA,WAC4xE,CAAC;IAD/xE5B,EAAE,CAAA8C,UAAA,mBAAAC,iGAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAJ,GAAA;MAAA,MAAAK,QAAA,GAAFjD,EAAE,CAAAiC,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAA,OAAFjC,EAAE,CAAAmD,WAAA,CACirEnB,MAAA,CAAAoB,aAAA,CAAAH,QAAmB,CAAC;IAAA,CAAC,CAAC,oBAAAI,kGAAA;MADzsErD,EAAE,CAAAgD,aAAA,CAAAJ,GAAA;MAAA,MAAAK,QAAA,GAAFjD,EAAE,CAAAiC,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAA,OAAFjC,EAAE,CAAAmD,WAAA,CACwuEnB,MAAA,CAAAsB,WAAA,CAAAL,QAAA,CAAAM,WAA6B,CAAC;IAAA,CAAC,CAAC;IAD1wEvD,EAAE,CAAA4B,cAAA,SACw2E,CAAC;IAD32E5B,EAAE,CAAA6B,MAAA,EACg7E,CAAC;IADn7E7B,EAAE,CAAAwD,UAAA,IAAAjB,oFAAA,iBACw9E,CAAC;IAD39EvC,EAAE,CAAA+B,YAAA,CACshF,CAAC,CAAuB,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAc,QAAA,GAAAb,GAAA,CAAAc,IAAA;IAAA,MAAAQ,QAAA,GADjjFjD,EAAE,CAAAiC,aAAA,GAAAiB,SAAA;IAAA,MAAAlB,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAyD,WAAA,YAAAzB,MAAA,CAAA0B,aAAA,kBAAA1B,MAAA,CAAA0B,aAAA,CAAAC,IAAA,OAAAV,QAAA,kBAAAA,QAAA,CAAAU,IAAA,CACgpE,CAAC;IADnpE3D,EAAE,CAAAkC,SAAA,CACu2E,CAAC;IAD12ElC,EAAE,CAAAyD,WAAA,qBAAAjB,QAAA,CAAAhB,aACu2E,CAAC;IAD12ExB,EAAE,CAAAkC,SAAA,CACg7E,CAAC;IADn7ElC,EAAE,CAAA0C,kBAAA,MAAAO,QAAA,kBAAAA,QAAA,CAAAW,WAAA,KACg7E,CAAC;IADn7E5D,EAAE,CAAAkC,SAAA,CACq9E,CAAC;IADx9ElC,EAAE,CAAA6D,UAAA,SAAArB,QAAA,CAAAhB,aAAA,IACq9E,CAAC;EAAA;AAAA;AAAA,SAAAsC,0EAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADx9E1B,EAAE,CAAA4B,cAAA,YACs6D,CAAC;IADz6D5B,EAAE,CAAAwD,UAAA,IAAAb,6EAAA,eAC4xE,CAAC;IAD/xE3C,EAAE,CAAA+B,YAAA,CACqkF,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAuB,QAAA,GAAAtB,GAAA,CAAAuB,SAAA;IAAA,MAAAlB,MAAA,GADxkFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAkC,SAAA,CAC0gE,CAAC;IAD7gElC,EAAE,CAAA6D,UAAA,SAAF7D,EAAE,CAAA+D,eAAA,IAAAzC,GAAA,EAAAU,MAAA,CAAAgC,gBAAA,CAAAf,QAAA,CAAAU,IAAA,EAC0gE,CAAC;EAAA;AAAA;AAAA,SAAAM,iGAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD7gE1B,EAAE,CAAA4B,cAAA,cACwhJ,CAAC;IAD3hJ5B,EAAE,CAAA6B,MAAA,EAC+kJ,CAAC;IADllJ7B,EAAE,CAAA+B,YAAA,CAC4mJ,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAwC,WAAA,GAAAvC,GAAA,CAAAuB,SAAA;IAD/mJlD,EAAE,CAAAkC,SAAA,CAC+kJ,CAAC;IADllJlC,EAAE,CAAAmC,kBAAA,KAAA+B,WAAA,CAAAC,YAAA,QAAAD,WAAA,CAAAE,WAAA,IAC+kJ,CAAC;EAAA;AAAA;AAAA,SAAAC,0FAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADllJ1B,EAAE,CAAAsE,uBAAA,EAC41I,CAAC;IAD/1ItE,EAAE,CAAAwD,UAAA,IAAAS,gGAAA,kBACwhJ,CAAC;IAD3hJjE,EAAE,CAAAuE,qBAAA;EAAA;EAAA,IAAA7C,EAAA;IAAA,MAAA8C,aAAA,GAAFxE,EAAE,CAAAiC,aAAA,GAAAiB,SAAA;IAAFlD,EAAE,CAAAkC,SAAA,CACm8I,CAAC;IADt8IlC,EAAE,CAAA6D,UAAA,YAAAW,aAAA,CAAAC,gBACm8I,CAAC;EAAA;AAAA;AAAA,SAAAC,2EAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiD,GAAA,GADt8I3E,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAA4B,cAAA,aACiqH,CAAC,kBAA+f,CAAC;IADpqI5B,EAAE,CAAA8C,UAAA,mBAAA8B,kGAAA;MAAA,MAAAJ,aAAA,GAAFxE,EAAE,CAAAgD,aAAA,CAAA2B,GAAA,EAAAzB,SAAA;MAAA,MAAA2B,qBAAA,GAAF7E,EAAE,CAAA8E,WAAA;MAAA,MAAA9C,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAA,OAAFjC,EAAE,CAAAmD,WAAA,CACulInB,MAAA,CAAA+C,eAAA,CAAAP,aAAA,EAAAK,qBAAA,CAAAG,KAAoD,CAAC;IAAA,CAAC,CAAC;IADhpIhF,EAAE,CAAA+B,YAAA,CACiqI,CAAC;IADpqI/B,EAAE,CAAA4B,cAAA,eACwwI,CAAC;IAD3wI5B,EAAE,CAAA6B,MAAA,EACwzI,CAAC;IAD3zI7B,EAAE,CAAAwD,UAAA,IAAAa,yFAAA,yBAC41I,CAAC;IAD/1IrE,EAAE,CAAA+B,YAAA,CACyqJ,CAAC,CAAqB,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAA8C,aAAA,GAAA7C,GAAA,CAAAuB,SAAA;IAAA,MAAAlB,MAAA,GADlsJhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAA6D,UAAA,YAAAW,aAAA,CAAAS,KACqmH,CAAC;IADxmHjF,EAAE,CAAAkC,SAAA,CACk0H,CAAC;IADr0HlC,EAAE,CAAA6D,UAAA,YAAA7B,MAAA,CAAAkD,UAAA,CAAAV,aAAA,CAAAb,IAAA,CACk0H,CAAC,UAAA3B,MAAA,CAAAkD,UAAA,CAAAV,aAAA,CAAAb,IAAA,CAA0D,CAAC,aAAA3B,MAAA,CAAAmD,4BAAA,CAAAX,aAAA,CAAAC,gBAAA,CAA0L,CAAC;IAD3jIzE,EAAE,CAAAoF,WAAA,OAAAZ,aAAA,CAAAb,IAAA;IAAF3D,EAAE,CAAAkC,SAAA,EACmvI,CAAC;IADtvIlC,EAAE,CAAAoF,WAAA,QAAAZ,aAAA,CAAAb,IAAA;IAAF3D,EAAE,CAAAkC,SAAA,CACwzI,CAAC;IAD3zIlC,EAAE,CAAA0C,kBAAA,KAAA8B,aAAA,CAAAZ,WAAA,KACwzI,CAAC;IAD3zI5D,EAAE,CAAAkC,SAAA,CACy1I,CAAC;IAD51IlC,EAAE,CAAA6D,UAAA,UAAA7B,MAAA,CAAAqD,UACy1I,CAAC;EAAA;AAAA;AAAA,SAAAC,oEAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6D,GAAA,GAD51IvF,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAA4B,cAAA,YAC2+B,CAAC,YAAmD,CAAC,aAA4C,CAAC,kBAAuZ,CAAC;IADv+C5B,EAAE,CAAAwF,gBAAA,2BAAAC,mGAAAC,MAAA;MAAF1F,EAAE,CAAAgD,aAAA,CAAAuC,GAAA;MAAA,MAAAvD,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAFjC,EAAE,CAAA2F,kBAAA,CAAA3D,MAAA,CAAA4D,YAAA,EAAAF,MAAA,MAAA1D,MAAA,CAAA4D,YAAA,GAAAF,MAAA;MAAA,OAAF1F,EAAE,CAAAmD,WAAA,CAAAuC,MAAA;IAAA,CACu2C,CAAC;IAD12C1F,EAAE,CAAA8C,UAAA,mBAAA+C,2FAAA;MAAF7F,EAAE,CAAAgD,aAAA,CAAAuC,GAAA;MAAA,MAAAvD,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAA,OAAFjC,EAAE,CAAAmD,WAAA,CACo4CnB,MAAA,CAAA8D,gBAAA,CAAiB,CAAC;IAAA,CAAC,CAAC;IAD15C9F,EAAE,CAAA+B,YAAA,CACo+C,CAAC;IADv+C/B,EAAE,CAAA4B,cAAA,eACqjD,CAAC;IADxjD5B,EAAE,CAAA6B,MAAA,EAC0pD,CAAC;IAD7pD7B,EAAE,CAAA8B,MAAA;IAAF9B,EAAE,CAAA+B,YAAA,CACkqD,CAAC,CAAmB,CAAC;IADzrD/B,EAAE,CAAA+F,SAAA,YACkuD,CAAC;IADruD/F,EAAE,CAAA4B,cAAA,aAC6wD,CAAC,aAAuD,CAAC;IADx0D5B,EAAE,CAAAwD,UAAA,KAAAM,yEAAA,gBACs6D,CAAC;IADz6D9D,EAAE,CAAA+B,YAAA,CAC0lF,CAAC,CAAmB,CAAC,CAAkB,CAAC;IADpoF/B,EAAE,CAAA4B,cAAA,cACyrF,CAAC,cAAiC,CAAC,cAA8C,CAAC,mBAA4a,CAAC;IAD1rG5B,EAAE,CAAAwF,gBAAA,2BAAAQ,oGAAAN,MAAA;MAAF1F,EAAE,CAAAgD,aAAA,CAAAuC,GAAA;MAAA,MAAAvD,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAFjC,EAAE,CAAA2F,kBAAA,CAAA3D,MAAA,CAAAiE,aAAA,EAAAP,MAAA,MAAA1D,MAAA,CAAAiE,aAAA,GAAAP,MAAA;MAAA,OAAF1F,EAAE,CAAAmD,WAAA,CAAAuC,MAAA;IAAA,CACujG,CAAC;IAD1jG1F,EAAE,CAAA8C,UAAA,mBAAAoD,4FAAA;MAAFlG,EAAE,CAAAgD,aAAA,CAAAuC,GAAA;MAAA,MAAAvD,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAA,OAAFjC,EAAE,CAAAmD,WAAA,CAC4oGnB,MAAA,CAAAmE,oBAAA,CAAqB,CAAC;IAAA,CAAC,CAAC;IADtqGnG,EAAE,CAAA+B,YAAA,CACurG,CAAC;IAD1rG/B,EAAE,CAAA4B,cAAA,gBAC2wG,CAAC;IAD9wG5B,EAAE,CAAA6B,MAAA,GACo3G,CAAC;IADv3G7B,EAAE,CAAA8B,MAAA;IAAF9B,EAAE,CAAA+B,YAAA,CAC43G,CAAC,CAAqB,CAAC;IADr5G/B,EAAE,CAAA+F,SAAA,aACu7G,CAAC;IAD17G/F,EAAE,CAAAwD,UAAA,KAAAkB,0EAAA,iBACiqH,CAAC;IADpqH1E,EAAE,CAAA+B,YAAA,CACmtJ,CAAC,CAAiB,CAAC,CAAe,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,MAAA,GADxvJhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAkC,SAAA,EACu2C,CAAC;IAD12ClC,EAAE,CAAAoG,gBAAA,YAAApE,MAAA,CAAA4D,YACu2C,CAAC;IAD12C5F,EAAE,CAAA6D,UAAA,aAAA7B,MAAA,CAAAqE,0BACk9C,CAAC;IADr9CrG,EAAE,CAAAkC,SAAA,EAC0pD,CAAC;IAD7pDlC,EAAE,CAAAsG,iBAAA,CAAFtG,EAAE,CAAAoC,WAAA,sDAC0pD,CAAC;IAD7pDpC,EAAE,CAAAkC,SAAA,EAC83D,CAAC;IADj4DlC,EAAE,CAAA6D,UAAA,YAAA7B,MAAA,CAAAM,IAAA,CAAAiE,MAC83D,CAAC,iBAAAvE,MAAA,CAAAwE,SAAiB,CAAC;IADn5DxG,EAAE,CAAAkC,SAAA,EACujG,CAAC;IAD1jGlC,EAAE,CAAAoG,gBAAA,YAAApE,MAAA,CAAAiE,aACujG,CAAC;IAD1jGjG,EAAE,CAAA6D,UAAA,aAAA7B,MAAA,CAAAyE,mBAC6mG,CAAC;IADhnGzG,EAAE,CAAAkC,SAAA,EACo3G,CAAC;IADv3GlC,EAAE,CAAAsG,iBAAA,CAAFtG,EAAE,CAAAoC,WAAA,uDACo3G,CAAC;IADv3GpC,EAAE,CAAAkC,SAAA,EACkhH,CAAC;IADrhHlC,EAAE,CAAA6D,UAAA,YAAA7B,MAAA,CAAA0E,wBACkhH,CAAC,iBAAA1E,MAAA,CAAAwE,SAAgC,CAAC;EAAA;AAAA;AAAA,SAAAG,oEAAAjF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkF,IAAA,GADtjH5G,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAA4B,cAAA,gBACw3J,CAAC;IAD33J5B,EAAE,CAAA6B,MAAA,EAC27J,CAAC;IAD97J7B,EAAE,CAAA8B,MAAA;IAAF9B,EAAE,CAAA+B,YAAA,CACo8J,CAAC;IADv8J/B,EAAE,CAAA4B,cAAA,oBACygK,CAAC;IAD5gK5B,EAAE,CAAA8C,UAAA,mBAAA+D,gGAAA;MAAF7G,EAAE,CAAAgD,aAAA,CAAA4D,IAAA;MAAA,MAAA5E,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;MAAA,OAAFjC,EAAE,CAAAmD,WAAA,CAC+/JnB,MAAA,CAAA8E,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAD3gK9G,EAAE,CAAA6B,MAAA,EACwkK,CAAC;IAD3kK7B,EAAE,CAAA8B,MAAA;IAAF9B,EAAE,CAAA+B,YAAA,CACqlK,CAAC;EAAA;EAAA,IAAAL,EAAA;IADxlK1B,EAAE,CAAAkC,SAAA,CAC27J,CAAC;IAD97JlC,EAAE,CAAA0C,kBAAA,MAAF1C,EAAE,CAAAoC,WAAA,kCAC27J,CAAC;IAD97JpC,EAAE,CAAAkC,SAAA,EACwkK,CAAC;IAD3kKlC,EAAE,CAAAsG,iBAAA,CAAFtG,EAAE,CAAAoC,WAAA,2BACwkK,CAAC;EAAA;AAAA;AAAA,SAAA2E,sDAAArF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD3kK1B,EAAE,CAAAsE,uBAAA,EACotB,CAAC;IADvtBtE,EAAE,CAAAwD,UAAA,IAAA/B,mEAAA,gCAAFzB,EAAE,CAAAgH,sBACovB,CAAC,IAAA1B,mEAAA,kCADvvBtF,EAAE,CAAAgH,sBAC88B,CAAC,IAAAL,mEAAA,gCADj9B3G,EAAE,CAAAgH,sBAC2yJ,CAAC;IAD9yJhH,EAAE,CAAAuE,qBAAA;EAAA;AAAA;AAxPtG,MAAM0C,6BAA6B,CAAC;EAChC,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAAClC,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAACmC,QAAQ,EACvB;IACJ,IAAInC,KAAK,EAAE;MACP,IAAI,CAACoC,SAAS,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;QAC7B,IAAI,CAACF,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACG,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;QAC7BhH,MAAM,CAAC,IAAI,CAACiH,qBAAqB,CAACC,OAAO,EAAE,IAAI,CAACC,sBAAsB,CAACD,OAAO,CAAC,CAC1EE,IAAI,CAAClH,IAAI,CAAC,CAAC,CAAC,CAAC,CACb4G,SAAS,CAAC,MAAM;UACjB,IAAI,CAACO,SAAS,CAAC,CAAC;QACpB,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACV,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACG,aAAa,CAACC,IAAI,CAAC,KAAK,CAAC;IAClC;EACJ;EACAO,WAAWA,CAACC,OAAO,EAAEC,WAAW,EAAE;IAC9B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC3C,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC8B,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACG,aAAa,GAAG,IAAIrH,YAAY,CAAC,CAAC;IACvC,IAAI,CAACqC,IAAI,GAAG;MAAEiE,MAAM,EAAE,EAAE;MAAElE,iBAAiB,EAAE;IAAG,CAAC;IACjD,IAAI,CAACkB,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC0C,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACL,YAAY,GAAG,KAAK;IACzB,IAAI,CAACa,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACJ,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAAC4B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACvB,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACF,SAAS,GAAG,CAAC0B,CAAC,EAAEC,IAAI,KAAKA,IAAI,CAACxE,IAAI;EAC3C;EACAuB,UAAUA,CAACvB,IAAI,EAAE;IACb,OAAO,CAAC,IAAI,CAACJ,WAAW,CAAC6E,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1E,IAAI,KAAKA,IAAI,CAAC,IAAI;MAAE2E,SAAS,EAAE;IAAM,CAAC,EAAEA,SAAS;EAC9F;EACAT,gBAAgBA,CAACU,KAAK,EAAE;IACpB,IAAI,CAAC7E,aAAa,GAAG6E,KAAK;IAC1B,IAAI,CAAC,IAAI,CAAC7E,aAAa,EAAE;MACrB,IAAI,CAACgD,wBAAwB,GAAG,EAAE;MAClC;IACJ;IACA,MAAM8B,MAAM,GAAG,UAAUC,QAAQ,CAACC,IAAI,CAACC,GAAG,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM,KAAK;IAC5E,MAAMpF,WAAW,GAAG,CAAC,IAAI,CAACjB,IAAI,CAACiE,MAAM,CAAC6B,IAAI,CAACG,KAAK,IAAIA,KAAK,CAAC5E,IAAI,KAAK,IAAI,CAACD,aAAa,EAAEC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEJ,WAAW,IAC3G,EAAE;IACN,IAAI,CAACmD,wBAAwB,GAAGnD,WAAW,CAACqF,GAAG,CAACC,UAAU,KAAK;MAC3D,GAAGA,UAAU;MACb5D,KAAK,EAAE;QAAE,CAACuD,MAAM,GAAGM,UAAU,CAACvF,WAAW,EAAEsF,UAAU;MAAE,CAAC;MACxDP,SAAS,EAAE,CAAC,IAAI,CAAC/E,WAAW,CAAC6E,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1E,IAAI,KAAKkF,UAAU,CAAClF,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE2E;IAClF,CAAC,CAAC,CAAC;EACP;EACAhF,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAIA,WAAW,CAACwF,MAAM,EAAE;MACpB,IAAI,CAACtC,mBAAmB,GAAGlD,WAAW,CAACyF,KAAK,CAACH,UAAU,IAAIA,UAAU,CAACP,SAAS,IAC3EO,UAAU,CAACpE,gBAAgB,EAAEuE,KAAK,CAACC,CAAC,IAAIA,CAAC,CAAC9E,YAAY,KAAK,IAAI,CAACA,YAAY,CAAC,CAAC;IACtF,CAAC,MACI;MACD,IAAI,CAACsC,mBAAmB,GAAG,KAAK;IACpC;EACJ;EACAtB,4BAA4BA,CAACV,gBAAgB,EAAE;IAC3C,IAAIA,gBAAgB,CAACsE,MAAM,EAAE;MACzB,OAAOtE,gBAAgB,CAACyE,SAAS,CAACD,CAAC,IAAIA,CAAC,CAAC9E,YAAY,KAAK,IAAI,CAACA,YAAY,CAAC,GAAG,CAAC,CAAC;IACrF;IACA,OAAO,KAAK;EAChB;EACAY,eAAeA,CAACoE,iBAAiB,EAAE;IAC/B,IAAIA,iBAAiB,CAACb,SAAS,IAC3B,IAAI,CAACnD,4BAA4B,CAACgE,iBAAiB,CAAC1E,gBAAgB,CAAC,EACrE;IACJ,IAAI,CAACoD,gBAAgB,CAAC,IAAI,CAACnE,aAAa,CAAC;IACzC0F,UAAU,CAAC,MAAM;MACb,IAAI,CAAC7F,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqF,GAAG,CAACP,GAAG,IAAI;QAC3C,IAAIc,iBAAiB,CAACxF,IAAI,KAAK0E,GAAG,CAAC1E,IAAI,EAAE;UACrC,OAAO;YAAE,GAAG0E,GAAG;YAAEC,SAAS,EAAE,CAACD,GAAG,CAACC;UAAU,CAAC;QAChD,CAAC,MACI,IAAIa,iBAAiB,CAACxF,IAAI,KAAK0E,GAAG,CAACgB,UAAU,IAAIF,iBAAiB,CAACb,SAAS,EAAE;UAC/E,OAAO;YAAE,GAAGD,GAAG;YAAEC,SAAS,EAAE;UAAM,CAAC;QACvC,CAAC,MACI,IAAIa,iBAAiB,CAACE,UAAU,KAAKhB,GAAG,CAAC1E,IAAI,IAAI,CAACwF,iBAAiB,CAACb,SAAS,EAAE;UAChF,OAAO;YAAE,GAAGD,GAAG;YAAEC,SAAS,EAAE;UAAK,CAAC;QACtC;QACA,OAAOD,GAAG;MACd,CAAC,CAAC;MACF,IAAI,CAACiB,8BAA8B,CAACH,iBAAiB,CAAC;MACtD,IAAI,CAACI,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,gBAAgB,CAACN,iBAAiB,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT;EACAM,gBAAgBA,CAACC,kBAAkB,EAAE;IACjC,IAAIC,2BAA2B,GAAG,CAAC;IACnC,IAAIC,gBAAgB;IACpB,IAAIF,kBAAkB,CAACL,UAAU,EAAE;MAC/B,IAAI,CAAC9F,WAAW,CAACsG,OAAO,CAACxB,GAAG,IAAI;QAC5B,IAAIA,GAAG,CAAC1E,IAAI,KAAK+F,kBAAkB,CAACL,UAAU,EAAE;UAC5CO,gBAAgB,GAAGvB,GAAG;QAC1B;MACJ,CAAC,CAAC;MACF,IAAI,CAAC9E,WAAW,CAACsG,OAAO,CAACxB,GAAG,IAAI;QAC5B,IAAIuB,gBAAgB,CAACjG,IAAI,KAAK0E,GAAG,CAACgB,UAAU,EAAE;UAC1ChB,GAAG,CAACC,SAAS,IAAIqB,2BAA2B,EAAE;QAClD;MACJ,CAAC,CAAC;MACF,IAAIA,2BAA2B,KAAK,CAAC,IAAI,CAACC,gBAAgB,CAACtB,SAAS,EAAE;QAClE,IAAI,CAAC/E,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqF,GAAG,CAACP,GAAG,IAAI;UAC3C,IAAIA,GAAG,CAAC1E,IAAI,KAAKiG,gBAAgB,CAACjG,IAAI,EAAE;YACpC0E,GAAG,CAACC,SAAS,GAAG,IAAI;UACxB;UACA,OAAOD,GAAG;QACd,CAAC,CAAC;MACN;MACA;IACJ;IACA,IAAI,CAAC9E,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqF,GAAG,CAACP,GAAG,IAAI;MAC3C,IAAIA,GAAG,CAACgB,UAAU,KAAKK,kBAAkB,CAAC/F,IAAI,EAAE;QAC5C0E,GAAG,CAACC,SAAS,GAAG,KAAK;MACzB;MACA,OAAOD,GAAG;IACd,CAAC,CAAC;EACN;EACAiB,8BAA8BA,CAACI,kBAAkB,EAAE;IAC/C,IAAI,CAAChD,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACkC,GAAG,CAACP,GAAG,IAAI;MACrE,IAAIA,GAAG,CAAC1E,IAAI,KAAK+F,kBAAkB,CAAC/F,IAAI,EAAE;QACtC0E,GAAG,CAACC,SAAS,GAAG,CAACD,GAAG,CAACC,SAAS;MAClC;MACA,OAAOD,GAAG;IACd,CAAC,CAAC;EACN;EACAkB,mBAAmBA,CAAA,EAAG;IAClB,MAAMO,0BAA0B,GAAG,IAAI,CAACpD,wBAAwB,CAACqD,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAAC5D,gBAAgB,CAACuE,KAAK,CAACC,CAAC,IAAIA,CAAC,CAAC9E,YAAY,KAAK,IAAI,CAACA,YAAY,CAAC,CAAC;IACrJ,MAAM6F,mBAAmB,GAAGF,0BAA0B,CAACC,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC;IACnF,MAAM2B,OAAO,GAAGxB,QAAQ,CAACyB,aAAa,CAAC,0BAA0B,CAAC;IAClE,IAAIF,mBAAmB,CAACjB,MAAM,KAAKe,0BAA0B,CAACf,MAAM,EAAE;MAClEkB,OAAO,CAACE,aAAa,GAAG,KAAK;MAC7B,IAAI,CAAClE,aAAa,GAAG,IAAI;IAC7B,CAAC,MACI,IAAI+D,mBAAmB,CAACjB,MAAM,KAAK,CAAC,EAAE;MACvCkB,OAAO,CAACE,aAAa,GAAG,KAAK;MAC7B,IAAI,CAAClE,aAAa,GAAG,KAAK;IAC9B,CAAC,MACI;MACDgE,OAAO,CAACE,aAAa,GAAG,IAAI;IAChC;EACJ;EACAX,qBAAqBA,CAAA,EAAG;IACpB,MAAMY,qBAAqB,GAAG,IAAI,CAAC7G,WAAW,CAACwG,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAAC5D,gBAAgB,CAACuE,KAAK,CAACC,CAAC,IAAIA,CAAC,CAAC9E,YAAY,KAAK,IAAI,CAACA,YAAY,CAAC,CAAC;IACnI,MAAMkG,sBAAsB,GAAGD,qBAAqB,CAACL,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC;IACjF,MAAMgC,eAAe,GAAG7B,QAAQ,CAACyB,aAAa,CAAC,yBAAyB,CAAC;IACzE,IAAIG,sBAAsB,CAACtB,MAAM,KAAKqB,qBAAqB,CAACrB,MAAM,EAAE;MAChEuB,eAAe,CAACH,aAAa,GAAG,KAAK;MACrC,IAAI,CAACvE,YAAY,GAAG,IAAI;IAC5B,CAAC,MACI,IAAIyE,sBAAsB,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC1CuB,eAAe,CAACH,aAAa,GAAG,KAAK;MACrC,IAAI,CAACvE,YAAY,GAAG,KAAK;IAC7B,CAAC,MACI;MACD0E,eAAe,CAACH,aAAa,GAAG,IAAI;IACxC;EACJ;EACAhE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACO,wBAAwB,CAACmD,OAAO,CAAChB,UAAU,IAAI;MAChD,IAAIA,UAAU,CAACP,SAAS,IAAI,IAAI,CAACnD,4BAA4B,CAAC0D,UAAU,CAACpE,gBAAgB,CAAC,EACtF;MACJ,MAAM8F,KAAK,GAAG,IAAI,CAAChH,WAAW,CAAC2F,SAAS,CAACb,GAAG,IAAIA,GAAG,CAAC1E,IAAI,KAAKkF,UAAU,CAAClF,IAAI,CAAC;MAC7E,IAAI,CAACJ,WAAW,GAAG,CACf,GAAG,IAAI,CAACA,WAAW,CAACiH,KAAK,CAAC,CAAC,EAAED,KAAK,CAAC,EACnC;QAAE,GAAG,IAAI,CAAChH,WAAW,CAACgH,KAAK,CAAC;QAAEjC,SAAS,EAAE,CAAC,IAAI,CAACrC;MAAc,CAAC,EAC9D,GAAG,IAAI,CAAC1C,WAAW,CAACiH,KAAK,CAACD,KAAK,GAAG,CAAC,CAAC,CACvC;IACL,CAAC,CAAC;IACF,IAAI,CAACf,qBAAqB,CAAC,CAAC;EAChC;EACA1D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACvC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqF,GAAG,CAACC,UAAU,KAAK;MACnD,GAAGA,UAAU;MACbP,SAAS,EAAE,IAAI,CAACnD,4BAA4B,CAAC0D,UAAU,CAACpE,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAACmB;IACvF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,IAAI,CAACa,mBAAmB,EAAE;MAC3B,IAAI,CAACR,aAAa,GAAG,CAAC,IAAI,CAACL,YAAY;MACvC,IAAI,CAAC2D,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACnG,aAAa,CAAC,IAAI,CAACM,aAAa,CAAC;EAC1C;EACAN,aAAaA,CAACmF,KAAK,EAAE;IACjB,IAAI,CAACjF,WAAW,CAACiF,KAAK,CAAChF,WAAW,CAAC;IACnC,IAAI,CAACsE,gBAAgB,CAACU,KAAK,CAAC;IAC5B,IAAI,CAACgB,mBAAmB,CAAC,CAAC;EAC9B;EACAzC,MAAMA,CAAA,EAAG;IACL,MAAM2D,oBAAoB,GAAGC,cAAc,CAAC,IAAI,CAACpI,IAAI,CAACiE,MAAM,CAAC;IAC7D,MAAMoE,kBAAkB,GAAG,IAAI,CAACpH,WAAW,CACtCwG,MAAM,CAAC1B,GAAG,IAAI,CAACoC,oBAAoB,CAACrC,IAAI,CAACwC,SAAS,IAAIA,SAAS,CAACjH,IAAI,KAAK0E,GAAG,CAAC1E,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE2E,SAAS,KACpGD,GAAG,CAACC,SAAS,GACX,KAAK,GACL,IAAI,CAAC,CACNM,GAAG,CAAC,CAAC;MAAEjF,IAAI;MAAE2E;IAAU,CAAC,MAAM;MAAE3E,IAAI;MAAE2E;IAAU,CAAC,CAAC,CAAC;IACxD,IAAI,CAACqC,kBAAkB,CAAC5B,MAAM,EAAE;MAC5B,IAAI,CAAC7B,OAAO,GAAG,KAAK;MACpB;IACJ;IACA,IAAI,CAACe,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,OAAO,CACP8C,MAAM,CAAC,IAAI,CAAC1G,YAAY,EAAE,IAAI,CAACC,WAAW,EAAE;MAAEb,WAAW,EAAEoH;IAAmB,CAAC,CAAC,CAChFhD,IAAI,CAACjH,SAAS,CAAC,MAAM,IAAI,CAACoK,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAAC9C,WAAW,CAAC+C,eAAe,CAAC,CAAC,GAAGvK,EAAE,CAAC,IAAI,CAAC,CAAC,EAAEG,QAAQ,CAAC,MAAO,IAAI,CAACsH,SAAS,GAAG,KAAM,CAAC,CAAC,CAC5IZ,SAAS,CAAC,MAAM;MACjB,IAAI,CAACH,OAAO,GAAG,KAAK;IACxB,CAAC,CAAC;EACN;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAAChD,WAAW,IAAI,CAAC,IAAI,CAACD,YAAY,EAAE;MACzC,MAAM,IAAI6G,KAAK,CAAC,8CAA8C,CAAC;IACnE;IACA,OAAO,IAAI,CAACjD,OAAO,CAACkD,GAAG,CAAC,IAAI,CAAC9G,YAAY,EAAE,IAAI,CAACC,WAAW,CAAC,CAACuD,IAAI,CAAC/G,GAAG,CAAEsK,aAAa,IAAK;MACrF,IAAI,CAAC5I,IAAI,GAAG4I,aAAa;MACzB,IAAI,CAAC3H,WAAW,GAAGmH,cAAc,CAACQ,aAAa,CAAC3E,MAAM,CAAC;MACvD,IAAI,CAACsB,gBAAgB,CAACqD,aAAa,CAAC3E,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,IAAI,CAACF,0BAA0B,GAAG,IAAI,CAAC9C,WAAW,CAACyF,KAAK,CAACX,GAAG,IAAIA,GAAG,CAACC,SAAS,IACzED,GAAG,CAAC5D,gBAAgB,CAACuE,KAAK,CAACmC,QAAQ,IAAIA,QAAQ,CAAChH,YAAY,KAAK,IAAI,CAACA,YAAY,CAAC,CAAC;IAC5F,CAAC,CAAC,CAAC;EACP;EACAyD,SAASA,CAAA,EAAG;IACR;IACAwB,UAAU,CAAC,MAAM;MACb,IAAI,CAAC9F,WAAW,CAAC,IAAI,CAACI,aAAa,EAAEH,WAAW,IAAI,EAAE,CAAC;MACvD,IAAI,CAACgG,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACAxF,gBAAgBA,CAACoH,SAAS,EAAE;IACxB,OAAO,IAAI,CAAC7H,WAAW,CAAC8H,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAMA,GAAG,CAACH,SAAS,KAAKA,SAAS,IAAIG,GAAG,CAACjD,SAAS,GAAGgD,GAAG,GAAG,CAAC,GAAGA,GAAI,EAAE,CAAC,CAAC;EACnH;EACAR,oBAAoBA,CAAA,EAAG;IACnB,MAAMU,WAAW,GAAG,IAAI,CAACxD,WAAW,CAACyD,MAAM,CAAC,aAAa,CAAC;IAC1D,IAAI,IAAI,CAACtH,YAAY,KAAK,GAAG,EACzB,OAAOqH,WAAW,CAACE,KAAK,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAK,IAAI,CAACxH,WAAW,CAAC;IACpE,IAAI,IAAI,CAACD,YAAY,KAAK,GAAG,EACzB,OAAOqH,WAAW,CAACK,EAAE,KAAK,IAAI,CAACzH,WAAW;IAC9C,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAAC0H,IAAI,YAAAC,sCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF/E,6BAA6B,EAAvCjH,EAAE,CAAAiM,iBAAA,CAAuDlM,EAAE,CAACmM,kBAAkB,GAA9ElM,EAAE,CAAAiM,iBAAA,CAAyFpM,EAAE,CAACsM,kBAAkB;IAAA,CAA4C;EAAE;EAC9P;IAAS,IAAI,CAACC,IAAI,kBAD8EpM,EAAE,CAAAqM,iBAAA;MAAAC,IAAA,EACJrF,6BAA6B;MAAAsF,SAAA;MAAAC,SAAA,WAAAC,oCAAA/K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD3B1B,EAAE,CAAA0M,WAAA,CAAAzL,GAAA;UAAFjB,EAAE,CAAA0M,WAAA,CAAAxL,GAAA;QAAA;QAAA,IAAAQ,EAAA;UAAA,IAAAiL,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlL,GAAA,CAAA+F,sBAAA,GAAAiF,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlL,GAAA,CAAA6F,qBAAA,GAAAmF,EAAA;QAAA;MAAA;MAAAG,MAAA;QAAA3I,YAAA;QAAAC,WAAA;QAAAiB,UAAA;QAAAhD,iBAAA;QAAA6E,OAAA;MAAA;MAAA6F,OAAA;QAAAzF,aAAA;MAAA;MAAA0F,QAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAA3L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAA4B,cAAA,kBAC0oB,CAAC;UAD7oB5B,EAAE,CAAAwF,gBAAA,2BAAA8H,0EAAA5H,MAAA;YAAF1F,EAAE,CAAA2F,kBAAA,CAAAhE,GAAA,CAAAuF,OAAA,EAAAxB,MAAA,MAAA/D,GAAA,CAAAuF,OAAA,GAAAxB,MAAA;YAAA,OAAAA,MAAA;UAAA,CACqkB,CAAC;UADxkB1F,EAAE,CAAAwD,UAAA,IAAAuD,qDAAA,yBACotB,CAAC;UADvtB/G,EAAE,CAAA+B,YAAA,CACgpK,CAAC;QAAA;QAAA,IAAAL,EAAA;UADnpK1B,EAAE,CAAAoG,gBAAA,YAAAzE,GAAA,CAAAuF,OACqkB,CAAC;UADxkBlH,EAAE,CAAA6D,UAAA,SAAAlC,GAAA,CAAAsG,SAC0lB,CAAC,YAD7lBjI,EAAE,CAAAuN,eAAA,IAAApM,GAAA,CACyoB,CAAC;UAD5oBnB,EAAE,CAAAkC,SAAA,CACitB,CAAC;UADptBlC,EAAE,CAAA6D,UAAA,SAAAlC,GAAA,CAAAW,IAAA,CAAAD,iBAAA,IAAAV,GAAA,CAAAU,iBACitB,CAAC;QAAA;MAAA;MAAAmL,YAAA,GAAinJ3M,EAAE,CAAC4M,OAAO,EAAmH5M,EAAE,CAAC6M,IAAI,EAA6F7M,EAAE,CAAC8M,OAAO,EAA2E7M,EAAE,CAAC8M,4BAA4B,EAAkJ9M,EAAE,CAAC+M,eAAe,EAAsF/M,EAAE,CAACgN,OAAO,EAA8M/M,EAAE,CAACgN,eAAe,EAAgPhN,EAAE,CAACiN,cAAc,EAA0LjN,EAAE,CAACkN,mBAAmB,EAAkDpO,EAAE,CAACqO,gBAAgB;MAAAC,MAAA;IAAA,EAA+B;EAAE;AACzxN;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGpO,EAAE,CAAAqO,iBAAA,CAGXpH,6BAA6B,EAAc,CAAC;IAC3HqF,IAAI,EAAEpM,SAAS;IACfoO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,2BAA2B;MAAEvB,QAAQ,EAAE,yBAAyB;MAAEI,QAAQ,EAAE,mnJAAmnJ;MAAEe,MAAM,EAAE,CAAC,qHAAqH;IAAE,CAAC;EACz1J,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7B,IAAI,EAAEvM,EAAE,CAACmM;EAAmB,CAAC,EAAE;IAAEI,IAAI,EAAEzM,EAAE,CAACsM;EAAmB,CAAC,CAAC,EAAkB;IAAEhI,YAAY,EAAE,CAAC;MACvHmI,IAAI,EAAEnM;IACV,CAAC,CAAC;IAAEiE,WAAW,EAAE,CAAC;MACdkI,IAAI,EAAEnM;IACV,CAAC,CAAC;IAAEkF,UAAU,EAAE,CAAC;MACbiH,IAAI,EAAEnM;IACV,CAAC,CAAC;IAAEkC,iBAAiB,EAAE,CAAC;MACpBiK,IAAI,EAAEnM;IACV,CAAC,CAAC;IAAE+G,OAAO,EAAE,CAAC;MACVoF,IAAI,EAAEnM;IACV,CAAC,CAAC;IAAEmH,aAAa,EAAE,CAAC;MAChBgF,IAAI,EAAElM;IACV,CAAC,CAAC;IAAEsH,sBAAsB,EAAE,CAAC;MACzB4E,IAAI,EAAEjM,YAAY;MAClBiO,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAE9G,qBAAqB,EAAE,CAAC;MACxB8E,IAAI,EAAEjM,YAAY;MAClBiO,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC;EAAE,CAAC;AAAA;AAChB,SAASxF,UAAUA,CAACvF,WAAW,EAAEsF,UAAU,EAAE;EACzC,MAAMe,gBAAgB,GAAGrG,WAAW,CAAC6E,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1E,IAAI,KAAKkF,UAAU,CAACQ,UAAU,CAAC;EACpF,IAAIO,gBAAgB,IAAIA,gBAAgB,CAACP,UAAU,EAAE;IACjD,IAAIb,MAAM,GAAG,EAAE;IACf,OAAQA,MAAM,IAAIM,UAAU,CAACvF,WAAW,EAAEqG,gBAAgB,CAAC;EAC/D;EACA,OAAOA,gBAAgB,GAAG,EAAE,GAAG,CAAC;AACpC;AACA,SAASc,cAAcA,CAACnE,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAAC8E,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK,CAC/B,GAAGD,GAAG,EACN,GAAGC,GAAG,CAAChI,WAAW,CAACqF,GAAG,CAACK,CAAC,KAAK;IAAE,GAAGA,CAAC;IAAEmC,SAAS,EAAEG,GAAG,CAAC5H,IAAI,IAAI;EAAG,CAAC,CAAC,CAAC,CACrE,EAAE,EAAE,CAAC;AACV;AAEA,MAAM6K,0BAA0B,CAAC;EAC7B;IAAS,IAAI,CAAC1C,IAAI,YAAA2C,mCAAAzC,CAAA;MAAA,YAAAA,CAAA,IAAwFwC,0BAA0B;IAAA,CAAkD;EAAE;EACxL;IAAS,IAAI,CAACE,IAAI,kBA1C8E1O,EAAE,CAAA2O,gBAAA;MAAArC,IAAA,EA0CSkC;IAA0B,EAAsI;EAAE;EAC7Q;IAAS,IAAI,CAACI,IAAI,kBA3C8E5O,EAAE,CAAA6O,gBAAA;MAAAC,OAAA,GA2C+ChP,UAAU,EAAEkB,iBAAiB;IAAA,EAAI;EAAE;AACxL;AACA;EAAA,QAAAoN,SAAA,oBAAAA,SAAA,KA7CoGpO,EAAE,CAAAqO,iBAAA,CA6CXG,0BAA0B,EAAc,CAAC;IACxHlC,IAAI,EAAEhM,QAAQ;IACdgO,IAAI,EAAE,CAAC;MACCS,YAAY,EAAE,CAAC9H,6BAA6B,CAAC;MAC7C6H,OAAO,EAAE,CAAChP,UAAU,EAAEkB,iBAAiB,CAAC;MACxCgO,OAAO,EAAE,CAAC/H,6BAA6B;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,6BAA6B,EAAEuH,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}