{"ast": null, "code": "import { SurveyCreatorModel } from \"survey-creator-core\";\nimport \"survey-core/survey.i18n.js\";\nimport \"survey-creator-core/survey-creator-core.i18n.js\";\nimport { AppComponentBase } from '@app/app-component-base';\nimport * as SurveyCore from \"survey-core\";\nimport * as widgets from \"surveyjs-widgets\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../service/declaration-template-service.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates\";\nimport * as i4 from \"@app/shared/services/composite-question.service\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"survey-creator-angular\";\nwidgets.inputmask(SurveyCore);\nexport class DeclarationEditorComponent extends AppComponentBase {\n  constructor(injector, DeclarationTemplateservice, router, TemplateService, compositeQuestionService) {\n    super(injector);\n    this.DeclarationTemplateservice = DeclarationTemplateservice;\n    this.router = router;\n    this.TemplateService = TemplateService;\n    this.compositeQuestionService = compositeQuestionService;\n    this.id = this.DeclarationTemplateservice.getId();\n    this.json = '';\n    this.options = {\n      showLogicTab: true\n    };\n    this.creator = new SurveyCreatorModel(this.options);\n    this.model = this.creator;\n    //this.compositeQuestionService.setCompositeQuestions();\n  }\n  ngOnInit() {\n    this.editorSetup();\n  }\n  editorSetup() {\n    this.TemplateService.get(this.id).subscribe(response => {\n      this.json = JSON.stringify(response.survey, (key, value) => {\n        if (value !== null) return value;\n      });\n      this.creator.JSON = JSON.parse(this.json);\n      this.creator.text = this.json;\n      this.model = this.creator;\n      this.currentTemplate = response;\n      this.compositeQuestionService.setCompositeQuestions(this.currentTemplate);\n    });\n  }\n  saveSurvey() {\n    const survey = this.creator.JSON;\n    const template = {\n      name: this.currentTemplate.name,\n      description: this.currentTemplate.description,\n      effectiveDate: this.currentTemplate.effectiveDate,\n      expiryDate: this.currentTemplate.expiryDate,\n      survey: survey,\n      isActive: this.currentTemplate.isActive\n    };\n    this.TemplateService.update(this.id, template).subscribe();\n  }\n  closeEditor() {\n    // Add save logic before navigating back to the templates list \n    this.saveSurvey();\n    this.router.navigateByUrl('declaration-templates');\n  }\n  static {\n    this.ɵfac = function DeclarationEditorComponent_Factory(t) {\n      return new (t || DeclarationEditorComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.DeclarationTemplateServiceService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i4.CompositeQuestionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeclarationEditorComponent,\n      selectors: [[\"app-declaration-editor\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 4,\n      vars: 1,\n      consts: [[\"mat-raised-button\", \"\", 1, \"button-margin\", 3, \"click\"], [2, \"height\", \"100vh\"], [3, \"model\"]],\n      template: function DeclarationEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function DeclarationEditorComponent_Template_button_click_0_listener() {\n            return ctx.closeEditor();\n          });\n          i0.ɵɵtext(1, \"Save and Close Editor\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"survey-creator\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.model);\n        }\n      },\n      dependencies: [i5.MatButton, i6.CreatorComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZWNsYXJhdGlvbi1lZGl0b3IuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGVjbGFyYXRpb24tdGVtcGxhdGVzL2NvbnRhaW5lcnMvZGVjbGFyYXRpb24tZWRpdG9yL2RlY2xhcmF0aW9uLWVkaXRvci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esb0xBQW9MIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SurveyCreatorModel", "AppComponentBase", "SurveyCore", "widgets", "inputmask", "DeclarationEditorComponent", "constructor", "injector", "DeclarationTemplateservice", "router", "TemplateService", "compositeQuestionService", "id", "getId", "json", "options", "showLogicTab", "creator", "model", "ngOnInit", "editorSetup", "get", "subscribe", "response", "JSON", "stringify", "survey", "key", "value", "parse", "text", "currentTemplate", "setCompositeQuestions", "save<PERSON>urvey", "template", "name", "description", "effectiveDate", "expiryDate", "isActive", "update", "closeEditor", "navigateByUrl", "i0", "ɵɵdirectiveInject", "Injector", "i1", "DeclarationTemplateServiceService", "i2", "Router", "i3", "i4", "CompositeQuestionService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "DeclarationEditorComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "DeclarationEditorComponent_Template_button_click_0_listener", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\declaration-templates\\containers\\declaration-editor\\declaration-editor.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\declaration-templates\\containers\\declaration-editor\\declaration-editor.component.html"], "sourcesContent": ["import { Component, Injector, OnInit } from '@angular/core';\r\nimport { DeclarationTemplateServiceService } from '../service/declaration-template-service.service';\r\nimport { SurveyCreatorModel } from \"survey-creator-core\";\r\nimport \"survey-core/survey.i18n.js\";\r\nimport \"survey-creator-core/survey-creator-core.i18n.js\";\r\nimport { Router } from '@angular/router';\r\nimport { TemplateDto,SurveyDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates';\r\nimport { TemplateService } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { CompositeQuestionService } from '@app/shared/services/composite-question.service';\r\nimport * as SurveyCore from \"survey-core\";\r\nimport * as widgets from \"surveyjs-widgets\";\r\nimport Inputmask from \"inputmask\";\r\nwidgets.inputmask(SurveyCore);\r\n@Component({\r\n  selector: 'app-declaration-editor',\r\n  templateUrl: './declaration-editor.component.html',\r\n  styleUrls: ['./declaration-editor.component.scss']\r\n})\r\nexport class DeclarationEditorComponent extends AppComponentBase implements OnInit {\r\n  \r\n  constructor(injector: Injector ,private DeclarationTemplateservice: DeclarationTemplateServiceService, private router: Router,private TemplateService: TemplateService, private compositeQuestionService: CompositeQuestionService){\r\n    super(injector);\r\n    //this.compositeQuestionService.setCompositeQuestions();\r\n  }\r\n\r\n  id:string = this.DeclarationTemplateservice.getId();\r\n  json = '' ;\r\n  currentTemplate:TemplateDto;\r\n  options= {\r\n        showLogicTab: true\r\n  };\r\n  creator= new SurveyCreatorModel(this.options);\r\n  model: SurveyCreatorModel = this.creator;\r\n  \r\n  ngOnInit(): void {\r\n    this.editorSetup();\r\n  }\r\n\r\n  editorSetup():void{\r\n    \r\n    this.TemplateService.get(this.id).subscribe((response=>{\r\n\r\n      this.json =(JSON.stringify(response.survey, (key, value) => {\r\n        if (value !== null) return value\r\n      }));\r\n      this.creator.JSON = JSON.parse(this.json);\r\n      this.creator.text = this.json;\r\n      this.model = this.creator;\r\n      this.currentTemplate = response;\r\n      this.compositeQuestionService.setCompositeQuestions(this.currentTemplate);\r\n    }));\r\n  }\r\n\r\n  saveSurvey():void{\r\n    const survey: SurveyDto = this.creator.JSON;\r\n    const template: TemplateDto = {\r\n      name: this.currentTemplate.name,\r\n      description: this.currentTemplate.description,\r\n      effectiveDate: this.currentTemplate.effectiveDate,\r\n      expiryDate: this.currentTemplate.expiryDate,\r\n      survey:survey,\r\n      isActive: this.currentTemplate.isActive\r\n    } as TemplateDto;\r\n    this.TemplateService.update(this.id,template).subscribe();\r\n  }\r\n\r\n  closeEditor():void{\r\n    // Add save logic before navigating back to the templates list \r\n    this.saveSurvey();\r\n    this.router.navigateByUrl('declaration-templates');\r\n  }\r\n\r\n}\r\n", "\r\n<button mat-raised-button  class=\"button-margin\" (click)=\"closeEditor()\">Save and Close Editor</button>\r\n\r\n<div style=\"height: 100vh;\">\r\n    <survey-creator [model]=\"model\"></survey-creator>\r\n</div>\r\n\r\n"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,qBAAqB;AACxD,OAAO,4BAA4B;AACnC,OAAO,iDAAiD;AAIxD,SAASC,gBAAgB,QAAQ,yBAAyB;AAE1D,OAAO,KAAKC,UAAU,MAAM,aAAa;AACzC,OAAO,KAAKC,OAAO,MAAM,kBAAkB;;;;;;;;AAE3CA,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC;AAM7B,OAAM,MAAOG,0BAA2B,SAAQJ,gBAAgB;EAE9DK,YAAYC,QAAkB,EAAUC,0BAA6D,EAAUC,MAAc,EAASC,eAAgC,EAAUC,wBAAkD;IAChO,KAAK,CAACJ,QAAQ,CAAC;IADuB,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAA6C,KAAAC,MAAM,GAANA,MAAM;IAAiB,KAAAC,eAAe,GAAfA,eAAe;IAA2B,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAKxM,KAAAC,EAAE,GAAU,IAAI,CAACJ,0BAA0B,CAACK,KAAK,EAAE;IACnD,KAAAC,IAAI,GAAG,EAAE;IAET,KAAAC,OAAO,GAAE;MACHC,YAAY,EAAE;KACnB;IACD,KAAAC,OAAO,GAAE,IAAIjB,kBAAkB,CAAC,IAAI,CAACe,OAAO,CAAC;IAC7C,KAAAG,KAAK,GAAuB,IAAI,CAACD,OAAO;IAVtC;EACF;EAWAE,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IAET,IAAI,CAACV,eAAe,CAACW,GAAG,CAAC,IAAI,CAACT,EAAE,CAAC,CAACU,SAAS,CAAEC,QAAQ,IAAE;MAErD,IAAI,CAACT,IAAI,GAAGU,IAAI,CAACC,SAAS,CAACF,QAAQ,CAACG,MAAM,EAAE,CAACC,GAAG,EAAEC,KAAK,KAAI;QACzD,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;MAClC,CAAC,CAAE;MACH,IAAI,CAACX,OAAO,CAACO,IAAI,GAAGA,IAAI,CAACK,KAAK,CAAC,IAAI,CAACf,IAAI,CAAC;MACzC,IAAI,CAACG,OAAO,CAACa,IAAI,GAAG,IAAI,CAAChB,IAAI;MAC7B,IAAI,CAACI,KAAK,GAAG,IAAI,CAACD,OAAO;MACzB,IAAI,CAACc,eAAe,GAAGR,QAAQ;MAC/B,IAAI,CAACZ,wBAAwB,CAACqB,qBAAqB,CAAC,IAAI,CAACD,eAAe,CAAC;IAC3E,CAAE,CAAC;EACL;EAEAE,UAAUA,CAAA;IACR,MAAMP,MAAM,GAAc,IAAI,CAACT,OAAO,CAACO,IAAI;IAC3C,MAAMU,QAAQ,GAAgB;MAC5BC,IAAI,EAAE,IAAI,CAACJ,eAAe,CAACI,IAAI;MAC/BC,WAAW,EAAE,IAAI,CAACL,eAAe,CAACK,WAAW;MAC7CC,aAAa,EAAE,IAAI,CAACN,eAAe,CAACM,aAAa;MACjDC,UAAU,EAAE,IAAI,CAACP,eAAe,CAACO,UAAU;MAC3CZ,MAAM,EAACA,MAAM;MACba,QAAQ,EAAE,IAAI,CAACR,eAAe,CAACQ;KACjB;IAChB,IAAI,CAAC7B,eAAe,CAAC8B,MAAM,CAAC,IAAI,CAAC5B,EAAE,EAACsB,QAAQ,CAAC,CAACZ,SAAS,EAAE;EAC3D;EAEAmB,WAAWA,CAAA;IACT;IACA,IAAI,CAACR,UAAU,EAAE;IACjB,IAAI,CAACxB,MAAM,CAACiC,aAAa,CAAC,uBAAuB,CAAC;EACpD;;;uBApDWrC,0BAA0B,EAAAsC,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,iCAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAxC,eAAA,GAAAiC,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAA1B/C,0BAA0B;MAAAgD,SAAA;MAAAC,QAAA,GAAAX,EAAA,CAAAY,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxB,QAAA,WAAAyB,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBvCjB,EAAA,CAAAmB,cAAA,gBAAyE;UAAxBnB,EAAA,CAAAoB,UAAA,mBAAAC,4DAAA;YAAA,OAASH,GAAA,CAAApB,WAAA,EAAa;UAAA,EAAC;UAACE,EAAA,CAAAsB,MAAA,4BAAqB;UAAAtB,EAAA,CAAAuB,YAAA,EAAS;UAEvGvB,EAAA,CAAAmB,cAAA,aAA4B;UACxBnB,EAAA,CAAAwB,SAAA,wBAAiD;UACrDxB,EAAA,CAAAuB,YAAA,EAAM;;;UADcvB,EAAA,CAAAyB,SAAA,GAAe;UAAfzB,EAAA,CAAA0B,UAAA,UAAAR,GAAA,CAAA3C,KAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}