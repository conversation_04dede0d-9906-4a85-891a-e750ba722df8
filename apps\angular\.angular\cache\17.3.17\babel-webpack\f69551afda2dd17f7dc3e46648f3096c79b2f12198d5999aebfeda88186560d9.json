{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { strNotSpecified } from \"../Constants\";\nimport { _DYN_MEASUREMENTS, _DYN_MESSAGE, _DYN_PROPERTIES, _DYN_SEVERITY_LEVEL } from \"../__DynamicConstants\";\nimport { dataSanitizeMeasurements, dataSanitizeMessage, dataSanitizeProperties } from \"./Common/DataSanitizer\";\nvar Trace = /** @class */function () {\n  /**\r\n   * Constructs a new instance of the TraceTelemetry object\r\n   */\n  function Trace(logger, message, severityLevel, properties, measurements) {\n    this.aiDataContract = {\n      ver: 1 /* FieldType.Required */,\n      message: 1 /* FieldType.Required */,\n      severityLevel: 0 /* FieldType.Default */,\n      properties: 0 /* FieldType.Default */\n    };\n    var _self = this;\n    _self.ver = 2;\n    message = message || strNotSpecified;\n    _self[_DYN_MESSAGE /* @min:%2emessage */] = dataSanitizeMessage(logger, message);\n    _self[_DYN_PROPERTIES /* @min:%2eproperties */] = dataSanitizeProperties(logger, properties);\n    _self[_DYN_MEASUREMENTS /* @min:%2emeasurements */] = dataSanitizeMeasurements(logger, measurements);\n    if (severityLevel) {\n      _self[_DYN_SEVERITY_LEVEL /* @min:%2eseverityLevel */] = severityLevel;\n    }\n  }\n  Trace.envelopeType = \"Microsoft.ApplicationInsights.{0}.Message\";\n  Trace.dataType = \"MessageData\";\n  return Trace;\n}();\nexport { Trace };", "map": {"version": 3, "names": ["strNotSpecified", "_DYN_MEASUREMENTS", "_DYN_MESSAGE", "_DYN_PROPERTIES", "_DYN_SEVERITY_LEVEL", "dataSanitizeMeasurements", "dataSanitizeMessage", "dataSanitizeProperties", "Trace", "logger", "message", "severityLevel", "properties", "measurements", "aiDataContract", "ver", "_self", "envelopeType", "dataType"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/Telemetry/Trace.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { strNotSpecified } from \"../Constants\";\r\nimport { _DYN_MEASUREMENTS, _DYN_MESSAGE, _DYN_PROPERTIES, _DYN_SEVERITY_LEVEL } from \"../__DynamicConstants\";\r\nimport { dataSanitizeMeasurements, dataSanitizeMessage, dataSanitizeProperties } from \"./Common/DataSanitizer\";\r\nvar Trace = /** @class */ (function () {\r\n    /**\r\n     * Constructs a new instance of the TraceTelemetry object\r\n     */\r\n    function Trace(logger, message, severityLevel, properties, measurements) {\r\n        this.aiDataContract = {\r\n            ver: 1 /* FieldType.Required */,\r\n            message: 1 /* FieldType.Required */,\r\n            severityLevel: 0 /* FieldType.Default */,\r\n            properties: 0 /* FieldType.Default */\r\n        };\r\n        var _self = this;\r\n        _self.ver = 2;\r\n        message = message || strNotSpecified;\r\n        _self[_DYN_MESSAGE /* @min:%2emessage */] = dataSanitizeMessage(logger, message);\r\n        _self[_DYN_PROPERTIES /* @min:%2eproperties */] = dataSanitizeProperties(logger, properties);\r\n        _self[_DYN_MEASUREMENTS /* @min:%2emeasurements */] = dataSanitizeMeasurements(logger, measurements);\r\n        if (severityLevel) {\r\n            _self[_DYN_SEVERITY_LEVEL /* @min:%2eseverityLevel */] = severityLevel;\r\n        }\r\n    }\r\n    Trace.envelopeType = \"Microsoft.ApplicationInsights.{0}.Message\";\r\n    Trace.dataType = \"MessageData\";\r\n    return Trace;\r\n}());\r\nexport { Trace };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,eAAe,QAAQ,cAAc;AAC9C,SAASC,iBAAiB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,mBAAmB,QAAQ,uBAAuB;AAC7G,SAASC,wBAAwB,EAAEC,mBAAmB,EAAEC,sBAAsB,QAAQ,wBAAwB;AAC9G,IAAIC,KAAK,GAAG,aAAe,YAAY;EACnC;AACJ;AACA;EACI,SAASA,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAE;IACrE,IAAI,CAACC,cAAc,GAAG;MAClBC,GAAG,EAAE,CAAC,CAAC;MACPL,OAAO,EAAE,CAAC,CAAC;MACXC,aAAa,EAAE,CAAC,CAAC;MACjBC,UAAU,EAAE,CAAC,CAAC;IAClB,CAAC;IACD,IAAII,KAAK,GAAG,IAAI;IAChBA,KAAK,CAACD,GAAG,GAAG,CAAC;IACbL,OAAO,GAAGA,OAAO,IAAIV,eAAe;IACpCgB,KAAK,CAACd,YAAY,CAAC,sBAAsB,GAAGI,mBAAmB,CAACG,MAAM,EAAEC,OAAO,CAAC;IAChFM,KAAK,CAACb,eAAe,CAAC,yBAAyB,GAAGI,sBAAsB,CAACE,MAAM,EAAEG,UAAU,CAAC;IAC5FI,KAAK,CAACf,iBAAiB,CAAC,2BAA2B,GAAGI,wBAAwB,CAACI,MAAM,EAAEI,YAAY,CAAC;IACpG,IAAIF,aAAa,EAAE;MACfK,KAAK,CAACZ,mBAAmB,CAAC,4BAA4B,GAAGO,aAAa;IAC1E;EACJ;EACAH,KAAK,CAACS,YAAY,GAAG,2CAA2C;EAChET,KAAK,CAACU,QAAQ,GAAG,aAAa;EAC9B,OAAOV,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}