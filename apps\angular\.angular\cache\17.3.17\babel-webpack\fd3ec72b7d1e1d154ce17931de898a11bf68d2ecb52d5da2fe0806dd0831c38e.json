{"ast": null, "code": "/** Work for rendering Relevant Acvitities in UI.\n *  Properties definitions are following RelevantActivityEnum.\n *  Work for \"Additional Statistics\" tab only.\n */\nexport class ActivityCategoryRow {\n  constructor() {\n    // Banking business\n    this.bankBATitle = 'Banking Business';\n    // Insurance business\n    this.insuBATitle = 'Insurance business';\n    // Fund management business\n    this.fundBATitle = 'Fund management business';\n    // Finance and leasing business\n    this.finaBATitle = 'Finance and leasing business';\n    // Headquarters business\n    this.headBATitle = 'Headquarters business';\n    // Shipping business\n    this.shipBATitle = 'Shipping business';\n    // Holding business\n    this.holdBATitle = 'Holding business';\n    // Intellectual property business\n    this.intelBATitle = 'Intellectual property business';\n    // Distribution and service centre business\n    this.disrBATitle = 'Distribution and service centre business';\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}