{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"dz\", [[\"སྔ་ཆ་\", \"ཕྱི་ཆ་\"], u, u], u, [[\"ཟླ\", \"མིར\", \"ལྷག\", \"ཕུར\", \"སངྶ\", \"སྤེན\", \"ཉི\"], [\"ཟླ་\", \"མིར་\", \"ལྷག་\", \"ཕུར་\", \"སངས་\", \"སྤེན་\", \"ཉི་\"], [\"གཟའ་ཟླ་བ་\", \"གཟའ་མིག་དམར་\", \"གཟའ་ལྷག་པ་\", \"གཟའ་ཕུར་བུ་\", \"གཟའ་པ་སངས་\", \"གཟའ་སྤེན་པ་\", \"གཟའ་ཉི་མ་\"], [\"ཟླ་\", \"མིར་\", \"ལྷག་\", \"ཕུར་\", \"སངས་\", \"སྤེན་\", \"ཉི་\"]], u, [[\"༡\", \"༢\", \"༣\", \"4\", \"༥\", \"༦\", \"༧\", \"༨\", \"9\", \"༡༠\", \"༡༡\", \"༡༢\"], [\"༡\", \"༢\", \"༣\", \"༤\", \"༥\", \"༦\", \"༧\", \"༨\", \"༩\", \"༡༠\", \"༡༡\", \"12\"], [\"ཟླ་དངཔ་\", \"ཟླ་གཉིས་པ་\", \"ཟླ་གསུམ་པ་\", \"ཟླ་བཞི་པ་\", \"ཟླ་ལྔ་པ་\", \"ཟླ་དྲུག་པ\", \"ཟླ་བདུན་པ་\", \"ཟླ་བརྒྱད་པ་\", \"ཟླ་དགུ་པ་\", \"ཟླ་བཅུ་པ་\", \"ཟླ་བཅུ་གཅིག་པ་\", \"ཟླ་བཅུ་གཉིས་པ་\"]], [[\"༡\", \"༢\", \"༣\", \"༤\", \"༥\", \"༦\", \"༧\", \"༨\", \"༩\", \"༡༠\", \"༡༡\", \"༡༢\"], [\"ཟླ་༡\", \"ཟླ་༢\", \"ཟླ་༣\", \"ཟླ་༤\", \"ཟླ་༥\", \"ཟླ་༦\", \"ཟླ་༧\", \"ཟླ་༨\", \"ཟླ་༩\", \"ཟླ་༡༠\", \"ཟླ་༡༡\", \"ཟླ་༡༢\"], [\"སྤྱི་ཟླ་དངཔ་\", \"སྤྱི་ཟླ་གཉིས་པ་\", \"སྤྱི་ཟླ་གསུམ་པ་\", \"སྤྱི་ཟླ་བཞི་པ\", \"སྤྱི་ཟླ་ལྔ་པ་\", \"སྤྱི་ཟླ་དྲུག་པ\", \"སྤྱི་ཟླ་བདུན་པ་\", \"སྤྱི་ཟླ་བརྒྱད་པ་\", \"སྤྱི་ཟླ་དགུ་པ་\", \"སྤྱི་ཟླ་བཅུ་པ་\", \"སྤྱི་ཟླ་བཅུ་གཅིག་པ་\", \"སྤྱི་ཟླ་བཅུ་གཉིས་པ་\"]], [[\"BCE\", \"CE\"], u, u], 0, [6, 0], [\"y-MM-dd\", \"སྤྱི་ལོ་y ཟླ་MMM ཚེས་dd\", \"སྤྱི་ལོ་y MMMM ཚེས་ dd\", \"EEEE, སྤྱི་ལོ་y MMMM ཚེས་dd\"], [\"ཆུ་ཚོད་ h སྐར་མ་ mm a\", \"ཆུ་ཚོད་h:mm:ss a\", \"ཆུ་ཚོད་ h སྐར་མ་ mm:ss a z\", \"ཆུ་ཚོད་ h སྐར་མ་ mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0 %\", \"¤#,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"རྒྱ་གར་གྱི་དངུལ་ རུ་པི\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BTN\": [\"Nu.\"],\n  \"ILS\": [u, \"₪\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KRW\": [\"KR₩\", \"₩\"],\n  \"THB\": [\"TH฿\", \"฿\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"XAF\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/dz.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"dz\", [[\"སྔ་ཆ་\", \"ཕྱི་ཆ་\"], u, u], u, [[\"ཟླ\", \"མིར\", \"ལྷག\", \"ཕུར\", \"སངྶ\", \"སྤེན\", \"ཉི\"], [\"ཟླ་\", \"མིར་\", \"ལྷག་\", \"ཕུར་\", \"སངས་\", \"སྤེན་\", \"ཉི་\"], [\"གཟའ་ཟླ་བ་\", \"གཟའ་མིག་དམར་\", \"གཟའ་ལྷག་པ་\", \"གཟའ་ཕུར་བུ་\", \"གཟའ་པ་སངས་\", \"གཟའ་སྤེན་པ་\", \"གཟའ་ཉི་མ་\"], [\"ཟླ་\", \"མིར་\", \"ལྷག་\", \"ཕུར་\", \"སངས་\", \"སྤེན་\", \"ཉི་\"]], u, [[\"༡\", \"༢\", \"༣\", \"4\", \"༥\", \"༦\", \"༧\", \"༨\", \"9\", \"༡༠\", \"༡༡\", \"༡༢\"], [\"༡\", \"༢\", \"༣\", \"༤\", \"༥\", \"༦\", \"༧\", \"༨\", \"༩\", \"༡༠\", \"༡༡\", \"12\"], [\"ཟླ་དངཔ་\", \"ཟླ་གཉིས་པ་\", \"ཟླ་གསུམ་པ་\", \"ཟླ་བཞི་པ་\", \"ཟླ་ལྔ་པ་\", \"ཟླ་དྲུག་པ\", \"ཟླ་བདུན་པ་\", \"ཟླ་བརྒྱད་པ་\", \"ཟླ་དགུ་པ་\", \"ཟླ་བཅུ་པ་\", \"ཟླ་བཅུ་གཅིག་པ་\", \"ཟླ་བཅུ་གཉིས་པ་\"]], [[\"༡\", \"༢\", \"༣\", \"༤\", \"༥\", \"༦\", \"༧\", \"༨\", \"༩\", \"༡༠\", \"༡༡\", \"༡༢\"], [\"ཟླ་༡\", \"ཟླ་༢\", \"ཟླ་༣\", \"ཟླ་༤\", \"ཟླ་༥\", \"ཟླ་༦\", \"ཟླ་༧\", \"ཟླ་༨\", \"ཟླ་༩\", \"ཟླ་༡༠\", \"ཟླ་༡༡\", \"ཟླ་༡༢\"], [\"སྤྱི་ཟླ་དངཔ་\", \"སྤྱི་ཟླ་གཉིས་པ་\", \"སྤྱི་ཟླ་གསུམ་པ་\", \"སྤྱི་ཟླ་བཞི་པ\", \"སྤྱི་ཟླ་ལྔ་པ་\", \"སྤྱི་ཟླ་དྲུག་པ\", \"སྤྱི་ཟླ་བདུན་པ་\", \"སྤྱི་ཟླ་བརྒྱད་པ་\", \"སྤྱི་ཟླ་དགུ་པ་\", \"སྤྱི་ཟླ་བཅུ་པ་\", \"སྤྱི་ཟླ་བཅུ་གཅིག་པ་\", \"སྤྱི་ཟླ་བཅུ་གཉིས་པ་\"]], [[\"BCE\", \"CE\"], u, u], 0, [6, 0], [\"y-MM-dd\", \"སྤྱི་ལོ་y ཟླ་MMM ཚེས་dd\", \"སྤྱི་ལོ་y MMMM ཚེས་ dd\", \"EEEE, སྤྱི་ལོ་y MMMM ཚེས་dd\"], [\"ཆུ་ཚོད་ h སྐར་མ་ mm a\", \"ཆུ་ཚོད་h:mm:ss a\", \"ཆུ་ཚོད་ h སྐར་མ་ mm:ss a z\", \"ཆུ་ཚོད་ h སྐར་མ་ mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0 %\", \"¤#,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"རྒྱ་གར་གྱི་དངུལ་ རུ་པི\", { \"AUD\": [\"AU$\", \"$\"], \"BTN\": [\"Nu.\"], \"ILS\": [u, \"₪\"], \"JPY\": [\"JP¥\", \"¥\"], \"KRW\": [\"KR₩\", \"₩\"], \"THB\": [\"TH฿\", \"฿\"], \"USD\": [\"US$\", \"$\"], \"XAF\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,yBAAyB,EAAE,wBAAwB,EAAE,6BAA6B,CAAC,EAAE,CAAC,uBAAuB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,+BAA+B,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,wBAAwB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}