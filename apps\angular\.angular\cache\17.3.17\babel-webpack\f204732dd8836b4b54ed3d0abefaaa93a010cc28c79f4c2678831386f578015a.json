{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class ESLetterService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.generateESLetterByDeclarationId = (declarationId, config) => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/ESService/ESLetter/GenerateESLetter',\n      params: {\n        declarationId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getLetterByDeclarationid = (declarationid, config) => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/ESService/ESLetter/GetLetter',\n      params: {\n        declarationid\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.previewLetterByDeclarationid = (declarationid, config) => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/ESService/ESLetter/PreviewLetter',\n      params: {\n        declarationid\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.publishAllPendingLettersByYear = (year, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/ESLetter/PublishAllPendingLetters',\n      params: {\n        year\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.publishLetterByDeclarationId = (declarationId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/ESLetter/PublishLetter',\n      params: {\n        declarationId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function ESLetterService_Factory(t) {\n      return new (t || ESLetterService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ESLetterService,\n      factory: ESLetterService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ESLetterService", "constructor", "restService", "apiName", "generateESLetterByDeclarationId", "declarationId", "config", "request", "method", "responseType", "url", "params", "getLetterByDeclarationid", "declarationid", "previewLetterByDeclarationid", "publishAllPendingLettersByYear", "year", "publishLetterByDeclarationId", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\assessment\\esletter.service.ts"], "sourcesContent": ["import { RestService, Rest } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ESLetterService {\r\n  apiName = 'EconomicSubstanceService';\r\n  \r\n\r\n  generateESLetterByDeclarationId = (declarationId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string>({\r\n      method: 'GET',\r\n      responseType: 'text',\r\n      url: '/api/ESService/ESLetter/GenerateESLetter',\r\n      params: { declarationId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getLetterByDeclarationid = (declarationid: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string>({\r\n      method: 'GET',\r\n      responseType: 'text',\r\n      url: '/api/ESService/ESLetter/GetLetter',\r\n      params: { declarationid },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  previewLetterByDeclarationid = (declarationid: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string>({\r\n      method: 'GET',\r\n      responseType: 'text',\r\n      url: '/api/ESService/ESLetter/PreviewLetter',\r\n      params: { declarationid },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  publishAllPendingLettersByYear = (year?: number, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, number>({\r\n      method: 'POST',\r\n      url: '/api/ESService/ESLetter/PublishAllPendingLetters',\r\n      params: { year },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  publishLetterByDeclarationId = (declarationId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, void>({\r\n      method: 'POST',\r\n      url: '/api/ESService/ESLetter/PublishLetter',\r\n      params: { declarationId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAMA,OAAM,MAAOA,eAAe;EAmD1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlD/B,KAAAC,OAAO,GAAG,0BAA0B;IAGpC,KAAAC,+BAA+B,GAAG,CAACC,aAAqB,EAAEC,MAA6B,KACrF,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,MAAM;MACpBC,GAAG,EAAE,0CAA0C;MAC/CC,MAAM,EAAE;QAAEN;MAAa;KACxB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAM,wBAAwB,GAAG,CAACC,aAAqB,EAAEP,MAA6B,KAC9E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,MAAM;MACpBC,GAAG,EAAE,mCAAmC;MACxCC,MAAM,EAAE;QAAEE;MAAa;KACxB,EACD;MAAEV,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAQ,4BAA4B,GAAG,CAACD,aAAqB,EAAEP,MAA6B,KAClF,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,MAAM;MACpBC,GAAG,EAAE,uCAAuC;MAC5CC,MAAM,EAAE;QAAEE;MAAa;KACxB,EACD;MAAEV,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAS,8BAA8B,GAAG,CAACC,IAAa,EAAEV,MAA6B,KAC5E,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,MAAM;MACdE,GAAG,EAAE,kDAAkD;MACvDC,MAAM,EAAE;QAAEK;MAAI;KACf,EACD;MAAEb,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAW,4BAA4B,GAAG,CAACZ,aAAqB,EAAEC,MAA6B,KAClF,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAY;MAClCC,MAAM,EAAE,MAAM;MACdE,GAAG,EAAE,uCAAuC;MAC5CC,MAAM,EAAE;QAAEN;MAAa;KACxB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBAnDpCN,eAAe,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAfrB,eAAe;MAAAsB,OAAA,EAAftB,eAAe,CAAAuB,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}