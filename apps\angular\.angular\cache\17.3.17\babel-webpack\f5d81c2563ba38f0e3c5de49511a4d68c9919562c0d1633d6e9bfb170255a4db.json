{"ast": null, "code": "import _asyncToGenerator from \"c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Model } from \"survey-core\";\nimport { AppComponentBase } from '@app/app-component-base';\n//import { DatePipe } from '@angular/common';\nimport { DateHelper } from '@app/shared/utils/date-helper';\nimport { viewDeclarationEmpty } from '@app/shared/declaration-jsons/view-declaration-json';\nimport { ComponentCollection } from \"survey-core\";\nimport { environment } from '@environments/environment';\nimport * as SurveyCore from \"survey-core\";\nimport { FunctionFactory } from 'survey-core';\nimport { isAcceptedFileType, relevantActivityDateValid } from './validationFns';\nimport { themeJson } from './survey-theme-json';\nimport { finalize, forkJoin } from 'rxjs';\nimport { Converter } from \"showdown\";\nimport { init } from './surveyjs-inputmask-override';\nimport html2pdf from 'html2pdf.js'; //widgets.inputmask(SurveyCore);\nimport { AngularComponentFactory } from 'survey-angular-ui';\nimport { Serializer } from \"survey-core\";\nimport { SurveyFilePreviewComponent } from '@app/shared/components/survey-file-preview/survey-file-preview.component';\nimport { zonedTimeToUtc } from 'date-fns-tz';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates\";\nimport * as i2 from \"@app/shared/services/sweetalert.service\";\nimport * as i3 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations\";\nimport * as i6 from \"@app/shared/services/composite-question.service\";\nimport * as i7 from \"proxies/corporate-service/lib/proxy/bdo/ess/corporate-entity-service/corporate-entities\";\nimport * as i8 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/imports\";\nimport * as i9 from \"@angular/material/dialog\";\nimport * as i10 from \"@abp/ng.core\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/divider\";\nimport * as i13 from \"@angular/material/button\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"@angular/material/card\";\nimport * as i16 from \"@angular/material/tooltip\";\nimport * as i17 from \"survey-angular-ui\";\nimport * as i18 from \"@angular/common\";\nimport * as i19 from \"../declaration-entity-details/declaration-entity-details.component\";\nimport * as i20 from \"../assessment-action-view/assessment-action-view.component\";\nfunction EsDeclarationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"mode\", \"indeterminate\")(\"diameter\", 100);\n  }\n}\nfunction EsDeclarationComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSurvey());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 20);\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EsDeclarationComponent_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_div_7_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.switchToEdit());\n    });\n    i0.ɵɵtext(1, \"Edit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, EsDeclarationComponent_div_7_button_1_Template, 2, 0, \"button\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.surveyModel && ctx_r1.isEditVisible);\n  }\n}\nfunction EsDeclarationComponent_button_8_mat_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"print\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_button_8_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"mat-spinner\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.print());\n    });\n    i0.ɵɵtemplate(1, EsDeclarationComponent_button_8_mat_icon_1_Template, 2, 0, \"mat-icon\", 23)(2, EsDeclarationComponent_button_8_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isPrinting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isPrinting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPrinting);\n  }\n}\nfunction EsDeclarationComponent_div_9_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_div_9_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.preSaveVersionWarningCheck());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 20);\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EsDeclarationComponent_div_9_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_div_9_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.preSaveVersionWarningCheck());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 20);\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EsDeclarationComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, EsDeclarationComponent_div_9_button_1_Template, 3, 0, \"button\", 26)(2, EsDeclarationComponent_div_9_button_2_Template, 3, 0, \"button\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.surveyModel && ctx_r1.action !== \"view\" && ctx_r1.source !== \"Import\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.surveyModel && (ctx_r1.surveyModel == null ? null : ctx_r1.surveyModel.isLastPage) && !(ctx_r1.surveyModel == null ? null : ctx_r1.surveyModel.isPreviewButtonVisible) && (ctx_r1.surveyModel == null ? null : ctx_r1.surveyModel.isCompleteButtonVisible) && ctx_r1.source === \"Import\");\n  }\n}\nfunction EsDeclarationComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.surveyModel.prevPage());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Previous\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.surveyModel.nextPage());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Next\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.surveyModel.showPreview());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Preview\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_div_13_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.surveyModel.doComplete());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"done\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Complete\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, EsDeclarationComponent_div_13_button_1_Template, 4, 0, \"button\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.surveyModel && (ctx_r1.surveyModel == null ? null : ctx_r1.surveyModel.isLastPage) && !(ctx_r1.surveyModel == null ? null : ctx_r1.surveyModel.isPreviewButtonVisible) && (ctx_r1.surveyModel == null ? null : ctx_r1.surveyModel.isCompleteButtonVisible) && ctx_r1.source !== \"Import\");\n  }\n}\nfunction EsDeclarationComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submitImport());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"done\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Submit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function EsDeclarationComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.upgradeTemplateButtonClicked());\n    });\n    i0.ɵɵtext(1, \"Switch to latest Template\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EsDeclarationComponent_app_assessment_action_view_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-assessment-action-view\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"declarationData\", ctx_r1.declarationData)(\"declarationId\", ctx_r1.declarationId)(\"type\", ctx_r1.entityType)(\"historyId\", ctx_r1.historyId);\n  }\n}\nfunction EsDeclarationComponent_div_22_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r13);\n  }\n}\nfunction EsDeclarationComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"h4\", 30);\n    i0.ɵɵelement(2, \"em\", 31);\n    i0.ɵɵtext(3, \" Validation Errors:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 32);\n    i0.ɵɵtemplate(5, EsDeclarationComponent_div_22_li_5_Template, 2, 1, \"li\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.validationErrors);\n  }\n}\nfunction EsDeclarationComponent_div_23_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r14);\n  }\n}\nfunction EsDeclarationComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h4\", 30);\n    i0.ɵɵelement(2, \"em\", 31);\n    i0.ɵɵtext(3, \" Warnings:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 32);\n    i0.ɵɵtemplate(5, EsDeclarationComponent_div_23_li_5_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.validationWarnings);\n  }\n}\ninit(SurveyCore);\n// TODO: Refactor all the settimeouts\nAngularComponentFactory.Instance.registerComponent(\"sv-file-preview\", SurveyFilePreviewComponent);\nFunctionFactory.Instance.register(\"isAcceptedFileType\", isAcceptedFileType);\nFunctionFactory.Instance.register(\"relevantActivityDateValid\", relevantActivityDateValid);\nexport class EsDeclarationComponent extends AppComponentBase {\n  constructor(injector, service, sweetAlert, activityLookup, route, declarationService, statusLookup,\n  //private datePipe: DatePipe,\n  compositeQuestionService, cigaLookup, router, corporateEntityService, countryService, declarationImportService, dialog, historyService, config) {\n    super(injector);\n    this.service = service;\n    this.sweetAlert = sweetAlert;\n    this.activityLookup = activityLookup;\n    this.route = route;\n    this.declarationService = declarationService;\n    this.statusLookup = statusLookup;\n    this.compositeQuestionService = compositeQuestionService;\n    this.cigaLookup = cigaLookup;\n    this.router = router;\n    this.corporateEntityService = corporateEntityService;\n    this.countryService = countryService;\n    this.declarationImportService = declarationImportService;\n    this.dialog = dialog;\n    this.historyService = historyService;\n    this.config = config;\n    this.templateListDto = {\n      sorting: \"id\",\n      skipCount: 0,\n      maxResultCount: 100\n    }; // TODO: Get all results not just first 10\n    this.address = [];\n    this.hasValidationErrors = false;\n    this.isPrintVisible = false;\n    this.isEditVisible = false;\n    this.firstClick = true;\n    this.validationWarnings = [];\n    this.haswarnings = false;\n    this.isPrinting = false;\n    this.statusMappings = {\n      submitted: \"\",\n      Resubmitted: \"\",\n      Draft: \"\",\n      Reopened: \"\"\n    };\n    this.activityMappings = {\n      holding: \"\",\n      distribution: \"\",\n      ip: \"\",\n      shipping: \"\",\n      headquarters: \"\",\n      finance: \"\",\n      funds: \"\",\n      insurance: \"\",\n      banking: \"\"\n    };\n    this.cigaOtherId = {\n      holding: \"\",\n      distribution: \"\",\n      ip: \"\",\n      shipping: \"\",\n      headquarters: \"\",\n      finance: \"\",\n      funds: \"\",\n      insurance: \"\",\n      banking: \"\"\n    };\n    this.isViewMode = false;\n    this.currentPageIndex = 0;\n    this.isBackendValidationComplete = false;\n    this.compositeKeys = [\"intellectualPropertyBusiness\", \"bankingQuestions\", \"distributionQuestions\", \"financeQuestions\", \"fundManagmentQuestions\", \"headquartersQuestions\", \"holdingBusinessQuestions\", \"insuranceQuestions\", \"outsourcingIntellectualPropertyBusiness\", \"shippingQuestions\"];\n    this.isLoading = true;\n    this.entityType = 'RA';\n    this.displayActions = true;\n    this.switchTemplateVisible = false;\n    this.templateVersion = 2; //Default template to 2 for new declaration\n    this.assumedLocalZone = 'America/New_York'; //local\n    this.declarationStatus = null;\n    this.route.queryParams.subscribe(params => {\n      this.declarationId = params['declarationid'] ? params['declarationid'] : null;\n      this.entityId = params['entityid'];\n      this.action = params['action'];\n      this.status = params['status'];\n      this.source = params['source'];\n      this.importfileid = params['importfileid'];\n      this.importdeclarationid = params['importdeclarationid'];\n      this.historyId = params['historyid'];\n      console.log(this.importdeclarationid);\n      console.log(this.declarationId);\n    });\n    //this.compositeQuestionService.setCompositeQuestions();\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isLoading = true;\n      var isCa = _this.config.getFeature('SearchService.CASearch') == 'true';\n      _this.entityType = isCa ? 'CA' : 'RA';\n      yield _this.intialConfigAndSetup();\n      //this.isLoading = false;\n    })();\n  }\n  setSurvey() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.historyId) {\n        yield _this2.historyService.get(_this2.historyId).subscribe(data => {\n          _this2.declarationData = data.declarationContents;\n          console.log('dec service: ', _this2.declarationData);\n        });\n      } else if (_this2.declarationId || _this2.status === \"Imported\") {\n        yield _this2.declarationService.get(_this2.declarationId).subscribe(data => {\n          _this2.declarationData = data.declarationDto;\n          console.log('dec service: ', _this2.declarationData);\n        });\n      }\n      if (_this2.action === \"view\") {\n        _this2.displayActions = true;\n        console.log('historyId: ', _this2.historyId);\n        if (_this2.status === 'Submitted' || _this2.historyId || _this2.status === 'Resubmitted') _this2.isEditVisible = false;else _this2.isEditVisible = true;\n        _this2.isPrintVisible = true;\n        _this2.isViewMode = true;\n        _this2.viewTemplate = viewDeclarationEmpty;\n        _this2.mapSurveyJson();\n        _this2.setSurveyData();\n        _this2.hasValidationErrors = false;\n        _this2.haswarnings = false; // no need to show this stuff in view mode\n      } else if (_this2.source === 'Import' && _this2.status !== \"Imported\") {\n        // Only go here if its still in the staging table\n        _this2.displayActions = true;\n        _this2.setSurveyImport();\n      } else {\n        _this2.isEditVisible = false;\n        _this2.isPrintVisible = false;\n        _this2.isViewMode = false;\n        if (_this2.action == 'edit') {\n          _this2.displayActions = false;\n        }\n        Serializer.addProperty(\"question\", {\n          name: \"inputWidth\",\n          type: \"string\"\n        });\n        _this2.survey = new Model(_this2.activeTemplate);\n        _this2.survey.applyTheme(themeJson);\n        //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\n        _this2.survey.showNavigationButtons = false;\n        const converter = new Converter();\n        _this2.survey.onTextMarkdown.add(function (survey, options) {\n          // Convert Markdown to HTML\n          let str = converter.makeHtml(options.text);\n          // Remove root paragraphs <p></p>\n          str = str.substring(3);\n          str = str.substring(0, str.length - 4);\n          // Set HTML markup to render\n          options.html = str;\n        });\n        _this2.survey.completedHtml = \"Thank you for Submitting the Declaration\";\n        _this2.survey.onComplete.add(sender => {\n          _this2.surveyresults = JSON.parse(JSON.stringify(sender.data));\n          sender.isCompleted = false;\n          _this2.surveyComplete(sender);\n        });\n        _this2.survey.onValueChanged.add((sender, options) => {\n          _this2.valueChanged(sender, options);\n        });\n        _this2.survey.onCurrentPageChanging.add((sender, options) => {\n          if (options?.oldCurrentPage?.propertyHash?.name !== 'all' && options?.newCurrentPage?.propertyHash?.name !== 'all') {\n            // all is the name of the preview page before submission\n            if (options.isGoingForward) {\n              if (!_this2.isBackendValidationComplete || _this2.firstClick && _this2.haswarnings) {\n                options.allow = false;\n              }\n              if (!_this2.isBackendValidationComplete || _this2.hasValidationErrors) {\n                _this2.surveyresults = JSON.parse(JSON.stringify(_this2.survey.data));\n                _this2.setErrorsOnPageChange(sender, options);\n              }\n            }\n          }\n        });\n        _this2.survey.onCurrentPageChanged.add((sender, options) => {\n          _this2.isBackendValidationComplete = false;\n          _this2.firstClick = true;\n          _this2.hasValidationErrors = false;\n          _this2.haswarnings = false;\n          _this2.validationErrors = [];\n          _this2.validationWarnings = [];\n          _this2.isPrintVisible = false; // hide the print when not in the preview at end of survey. This event doesn't fire when going to preview only going away from it\n          _this2.currentPageIndex = options?.newCurrentPage?.propertyHash?.num - 1;\n          if (options?.newCurrentPage?.propertyHash?.name === 'financialPeriod') _this2.currentPageIndex = 0;\n          if (options?.newCurrentPage?.propertyHash?.name === 'entityDetails') _this2.currentPageIndex = 1;\n          if (options?.newCurrentPage?.propertyHash?.name === 'relevantActivity') _this2.currentPageIndex = 2;\n          if (options?.newCurrentPage?.propertyHash?.name === 'taxResidency') _this2.currentPageIndex = 3;\n          if (options?.newCurrentPage?.propertyHash?.name === 'activityDetail') _this2.currentPageIndex = 4;\n          if (options?.newCurrentPage?.propertyHash?.name === 'Supporting_Details') _this2.currentPageIndex = 5;\n          _this2.scrollToTop();\n        });\n        _this2.survey.onShowingPreview.add((sender, options) => {\n          _this2.isPrintVisible = true;\n        });\n        _this2.survey.onUploadFiles.add((_, options) => {\n          if (_this2.declarationId == null || _this2.declarationId == '00000000-0000-0000-0000-000000000000') {\n            //Need to save as draft first, to save the declarationId\n            _this2.saveAsDraft(false).then(() => {\n              _this2.uploadDeclarationFile(options);\n            }).catch(error => {\n              console.log(error);\n              options.callback(\"error\");\n            });\n          } else {\n            _this2.uploadDeclarationFile(options);\n          }\n        });\n        // this.survey.onDownloadFile.add((_, options) => {\n        //   this.onDownloadFile(options);\n        // });\n        _this2.survey.onClearFiles.add((_, options) => {\n          _this2.onClearFiles(options);\n        });\n        _this2.setSurveyData();\n      }\n    })();\n  }\n  setSurveyImport() {\n    this.isEditVisible = false;\n    this.isPrintVisible = false;\n    this.isViewMode = false;\n    Serializer.addProperty(\"question\", {\n      name: \"inputWidth\",\n      type: \"string\"\n    });\n    this.survey = new Model(this.activeTemplate);\n    this.survey.applyTheme(themeJson);\n    //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\n    this.survey.showNavigationButtons = false;\n    const converter = new Converter();\n    this.survey.onTextMarkdown.add(function (survey, options) {\n      // Convert Markdown to HTML\n      let str = converter.makeHtml(options.text);\n      // Remove root paragraphs <p></p>\n      str = str.substring(3);\n      str = str.substring(0, str.length - 4);\n      // Set HTML markup to render\n      options.html = str;\n    });\n    this.survey.completedHtml = \"Thank you for Submitting the Declaration\";\n    this.survey.onComplete.add(sender => {\n      this.surveyresults = JSON.parse(JSON.stringify(sender.data));\n      sender.isCompleted = false;\n      this.surveyComplete(sender);\n    });\n    this.survey.onValueChanged.add((sender, options) => {\n      this.valueChanged(sender, options);\n    });\n    this.survey.onCurrentPageChanging.add((sender, options) => {\n      if (options?.oldCurrentPage?.propertyHash?.name !== 'all' && options?.newCurrentPage?.propertyHash?.name !== 'all') {\n        // all is the name of the preview page before submission\n        if (options.isGoingForward) {\n          if (!this.isBackendValidationComplete || this.firstClick && this.haswarnings) {\n            options.allow = false;\n          }\n          if (!this.isBackendValidationComplete || this.hasValidationErrors) {\n            this.surveyresults = JSON.parse(JSON.stringify(this.survey.data));\n            this.setErrorsOnPageChangeImport(sender, options);\n          }\n        }\n      }\n    });\n    this.survey.onCurrentPageChanged.add((sender, options) => {\n      this.isBackendValidationComplete = false;\n      this.firstClick = true;\n      this.hasValidationErrors = false;\n      this.haswarnings = false;\n      this.validationErrors = [];\n      this.validationWarnings = [];\n      this.isPrintVisible = false; // hide the print when not in the preview at end of survey. This event doesn't fire when going to preview only going away from it\n      this.currentPageIndex = options?.newCurrentPage?.propertyHash?.num - 1;\n      if (options?.newCurrentPage?.propertyHash?.name === 'financialPeriod') this.currentPageIndex = 0;\n      if (options?.newCurrentPage?.propertyHash?.name === 'entityDetails') this.currentPageIndex = 1;\n      if (options?.newCurrentPage?.propertyHash?.name === 'relevantActivity') this.currentPageIndex = 2;\n      if (options?.newCurrentPage?.propertyHash?.name === 'taxResidency') this.currentPageIndex = 3;\n      if (options?.newCurrentPage?.propertyHash?.name === 'activityDetail') this.currentPageIndex = 4;\n      if (options?.newCurrentPage?.propertyHash?.name === 'Supporting_Details') this.currentPageIndex = 5;\n    });\n    this.survey.onShowingPreview.add((sender, options) => {\n      this.isPrintVisible = true;\n    });\n    this.survey.onUploadFiles.add((_, options) => {\n      this.uploadDeclarationFile(options);\n    });\n    // this.survey.onDownloadFile.add((_, options) => {\n    //   this.onDownloadFile(options);\n    // });\n    this.survey.onClearFiles.add((_, options) => {\n      this.onClearFiles(options);\n    });\n    this.setImportSurveyData();\n    // });\n  }\n  isValidFileType(fileType) {\n    switch (fileType.toLowerCase()) {\n      case \"png\":\n        {\n          return true;\n        }\n      case \"bmp\":\n        {\n          return true;\n        }\n      case \"jpg\":\n        {\n          return true;\n        }\n      case \"jpeg\":\n        {\n          return true;\n        }\n      case \"pdf\":\n        {\n          return true;\n        }\n    }\n    return false;\n  }\n  uploadDeclarationFile(options) {\n    try {\n      if (options != null && options.files) {\n        if (this.surveyModel) {}\n        var count = 1;\n        const fileUploadLocation = options?.name;\n        const spinnerName = fileUploadLocation + \"Spinner\";\n        this.surveyModel.setVariable(spinnerName, true);\n        for (const f of options.files) {\n          var docType = options.question.name;\n          if (docType) {\n            const fileType = f?.name.split(\".\").pop();\n            if (!this.isValidFileType(fileType)) {\n              try {\n                this.sweetAlert.fireDialog({\n                  status: \"fail-type\",\n                  action: 'upload',\n                  source: \"es-declaration-file\",\n                  type: \"toaster\"\n                });\n                this.surveyModel.setVariable(spinnerName, false);\n                options.callback(\"error\");\n                return;\n              } catch (e) {\n                options.callback(\"error\");\n                return;\n              }\n            }\n            const currentFiles = this.survey.getValue(options?.name);\n            for (const currentFile in currentFiles) {\n              for (const fileInfo in currentFiles[currentFile]) {\n                if (fileInfo === \"name\" && currentFiles[currentFile][fileInfo] === f.name) {\n                  this.sweetAlert.fireDialog({\n                    status: \"fail-name\",\n                    action: 'upload',\n                    source: \"es-declaration-file\",\n                    type: \"toaster\"\n                  });\n                  this.surveyModel.setVariable(spinnerName, false);\n                  options.callback(\"error\");\n                  return;\n                }\n              }\n            }\n            const reader = new FileReader();\n            reader.readAsDataURL(f);\n            reader.onload = () => {\n              var data = reader.result;\n              var upload = {\n                fileName: f.name,\n                fileContents: data,\n                uploadedDeclarationId: this.declarationId ? this.declarationId : this.importdeclarationid,\n                docType: docType,\n                isImport: this.importdeclarationid ? true : false\n              };\n              this.declarationService.uploadDeclarationDocument(upload).subscribe(result => {\n                console.log('file: ' + f.name + ' was uploaded successfully');\n                if (count == options.files.length) {\n                  options.callback(\"success\", options.files.map(f => {\n                    const fileUploadLocation = options?.name;\n                    const spinnerName = fileUploadLocation + \"Spinner\";\n                    this.surveyModel.setVariable(spinnerName, false);\n                    return {\n                      file: f\n                    };\n                  }));\n                  this.sweetAlert.fireDialog({\n                    status: \"success\",\n                    action: 'upload',\n                    source: \"es-declaration-file\",\n                    type: \"toaster\"\n                  });\n                }\n                count++;\n              });\n            };\n            reader.onerror = err => {\n              console.log(err);\n            };\n          }\n        }\n      }\n    } catch (e) {\n      console.log(e);\n      const fileUploadLocation = options?.name;\n      const spinnerName = fileUploadLocation + \"Spinner\";\n      this.surveyModel.setVariable(spinnerName, false);\n      options.callback(\"error\");\n    }\n  }\n  onDownloadFile(options) {\n    if (options != null && options.fileValue) {\n      var count = 1;\n      var fileList = [];\n      // for(const f of options.fileValue) {\n      var f = options.fileValue;\n      var docType = options.question.name;\n      var header = this.GetHeader(options.fileValue);\n      let isImport = false;\n      let idToUse = \"\";\n      if (this.importdeclarationid && !this.declarationId) {\n        idToUse = this.importdeclarationid;\n        isImport = true;\n      } else {\n        idToUse = this.declarationId;\n        isImport = false;\n      }\n      if (docType) {\n        const fileUploadLocation = options?.name;\n        const spinnerName = fileUploadLocation + \"Spinner\";\n        if (this.surveyModel) {\n          this.surveyModel.setVariable(spinnerName, true);\n        }\n        if (this.entityType == 'CA') {\n          this.declarationService.downloadCADeclarationDocument(idToUse, f.name, docType, isImport).pipe(finalize(() => {\n            if (this.surveyModel) {\n              this.surveyModel.setVariable(spinnerName, false);\n            }\n          })).subscribe(result => {\n            var t = result;\n            options.callback(\"success\", header + result);\n          });\n        } else {\n          this.declarationService.downloadDeclarationDocument(idToUse, f.name, docType, isImport).pipe(finalize(() => {\n            if (this.surveyModel) {\n              this.surveyModel.setVariable(spinnerName, false);\n            }\n          })).subscribe(result => {\n            var t = result;\n            options.callback(\"success\", header + result);\n          });\n        }\n      }\n      // }\n    }\n  }\n  GetHeader(fileValue) {\n    var fileName = fileValue.name;\n    var split = fileName.split('.');\n    var extension = split[split.length - 1];\n    switch (extension.toLowerCase()) {\n      case \"png\":\n        {\n          return \"data:image/png;base64,\";\n        }\n      case \"bmp\":\n        {\n          return \"data:image/bmp;base64,\";\n        }\n      case \"jpg\":\n        {\n          return \"data:image/jpeg;base64,\";\n        }\n      case \"jpeg\":\n        {\n          return \"data:image/jpeg;base64,\";\n        }\n      case \"pdf\":\n        {\n          return \"data:application/pdf;base64,\";\n        }\n    }\n    return \"\";\n  }\n  valueChanged(sender, options) {\n    if (options.name === \"financialPeriodChange\" && options.value === false) {\n      this.survey.setValue('financialPeriodStartDate', this.validStartDate);\n      this.survey.setValue('financialPeriodEndDate', this.validEndDate);\n    }\n    if (options.name === \"financialPeriodChange\" && options.value === true) {\n      this.survey.setValue('financialPeriodStartDate', '');\n      this.survey.setValue('financialPeriodEndDate', '');\n    }\n    if (options.name === \"entityDetailsBusinessSameAsRegisteredAddress\" && options.value === true) {\n      if (this.entityId) {\n        this.corporateEntityService.get(this.entityId, true).subscribe(result => {\n          this.address = [];\n          this.countryService.getList({\n            sorting: \"name desc\",\n            maxResultCount: 1000\n          }).subscribe(response => {\n            response.items.forEach(country => {\n              if (country.name?.toLowerCase() === result.registeredOfficeAddress.country?.toLowerCase()) {\n                result.registeredOfficeAddress.country = country.id;\n                this.address.push(result.registeredOfficeAddress);\n                this.survey.setValue(\"entityDetailsEnterDifferntBusinessAddress\", this.address);\n              }\n            });\n          });\n          this.survey.setValue(\"entityDetailsEnterDifferntBusinessAddress\", this.address);\n        });\n      }\n    }\n  }\n  setErrorsOnPageChange(sender, options) {\n    this.validationErrors = [];\n    const oldPage = options?.oldCurrentPage?.propertyHash?.name;\n    const newPage = options?.newCurrentPage?.propertyHash?.name;\n    this.hasValidationErrors = false;\n    // Setting up dto\n    let declaratioDto = {\n      informationRequestedArray: [],\n      surveyData: JSON.parse(JSON.stringify(this.surveyresults)),\n      actionEnforcedFileNames: [],\n      declarationTemplateId: this.templateId ?? null\n    };\n    this.convertAllDatesToUTC(declaratioDto);\n    if (declaratioDto.surveyData.relevantActRelevantActivities) {\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\n    }\n    if (this.declarationId) declaratioDto.id = this.declarationId;\n    declaratioDto.surveyData.entityId = this.entityId;\n    if (options.isGoingForward) {\n      this.declarationService.validatePageByPageNameAndInput(oldPage, declaratioDto).subscribe(result => {\n        const errors = result.validationResult.errors.filter(item => item.severity === 0);\n        const warnings = result.validationResult.errors.filter(item => item.severity !== 0);\n        this.setValidations(errors);\n        this.setWarnings(warnings);\n        if (this.validationErrors.length === 0) {\n          this.isBackendValidationComplete = true;\n          this.hasValidationErrors = false;\n        }\n        if (!this.hasValidationErrors && this.isBackendValidationComplete) {\n          if (!this.haswarnings) {\n            //next page\n            this.survey.nextPage();\n          } else {\n            //we have warnings but no errors\n            //is first click\n            if (this.firstClick) {\n              this.firstClick = false;\n            } else {\n              // go next\n              this.survey.nextPage();\n            }\n          }\n        }\n        if (this.hasValidationErrors || this.haswarnings) {\n          this.scrollToTop();\n        }\n      });\n    }\n  }\n  clearIncorrectDataImport(data) {\n    const endDateYear = new Date(data.declarationDto.surveyData['financialPeriodEndDate']).getFullYear();\n    let skipToEnd = false;\n    let skipFrom = \"\"; // if skip from = financialPeriod keep this pages data clear everything until the last page\n    //financial Period\n    const subjectToReclassification = data.declarationDto.surveyData['subjectToReclassification'];\n    const esFiledOnOTAS = data.declarationDto.surveyData['esFiledOnOTAS'];\n    const otasReceipt = data.declarationDto.surveyData['otasReceipt'];\n    const otasReceiptUpload = data.declarationDto.surveyData['otasReceiptUpload'];\n    if (!(endDateYear === 2022 || endDateYear === 2023)) {\n      data.declarationDto.surveyData['subjectToReclassification'] = null;\n      data.declarationDto.surveyData['esFiledOnOTAS'] = null;\n      data.declarationDto.surveyData['otasReceipt'] = null;\n      data.declarationDto.surveyData['otasReceiptUpload'] = null;\n    }\n    if ((endDateYear === 2022 || endDateYear === 2023) && esFiledOnOTAS === true) {\n      skipToEnd = true;\n      skipFrom = 'financialPeriod';\n      this.skipAndClearSectionsImport(data, ['entityDetails', 'relevantActivity', 'taxResidency', 'activityDetail']);\n    }\n    if (esFiledOnOTAS === false) {\n      data.declarationDto.surveyData['otasReceipt'] = null;\n      data.declarationDto.surveyData['otasReceiptUpload'] = null;\n    }\n    if (otasReceipt === false) {\n      data.declarationDto.surveyData['otasReceiptUpload'] = null;\n    }\n    // Relevant activities\n    const relevantActRelevantActivities = data.declarationDto.surveyData['relevantActRelevantActivities'];\n    if (relevantActRelevantActivities) {\n      if (relevantActRelevantActivities.includes('none') || relevantActRelevantActivities.length === 0) {\n        skipToEnd = true;\n        skipFrom = 'relevantActivity';\n        this.skipAndClearSectionsImport(data, ['taxResidency', 'activityDetail']);\n      }\n    }\n    // tax residency\n    const taxResidency100PercentBahamian = data.declarationDto.surveyData['taxResidency100PercentBahamian'];\n    const taxResidencyIsInvestmentFund = data.declarationDto.surveyData['taxResidencyIsInvestmentFund'];\n    const taxResidencyOutsideBahamas = data.declarationDto.surveyData['taxResidencyOutsideBahamas'];\n    const hasImmediateParent = data.declarationDto.surveyData['hasImmediateParent'];\n    const taxResidencyHasParent = data.declarationDto.surveyData['taxResidencyHasParent'];\n    if (taxResidency100PercentBahamian) {\n      skipToEnd = true;\n      skipFrom = 'taxResidency';\n      data.declarationDto.surveyData['taxResidencyIsInvestmentFund'] = null;\n      data.declarationDto.surveyData['taxResidencyOutsideBahamas'] = null;\n      data.declarationDto.surveyData['hasImmediateParent'] = null;\n      data.declarationDto.surveyData['taxResidencyHasParent'] = null;\n      data.declarationDto.surveyData['taxResidencyImmediateParentEntity'] = null;\n      data.declarationDto.surveyData['taxResidencyUltimateParentEntityInfo'] = null;\n      data.declarationDto.surveyData['taxResidencyJurisdictionEntityIsTaxResident'] = null;\n      data.declarationDto.surveyData['taxResidencyTaxpayerIDNumber'] = null;\n      data.declarationDto.surveyData['taxResidencyEvidenceOfTaxResidency'] = null;\n    }\n    if (taxResidencyIsInvestmentFund) {\n      skipToEnd = true;\n      skipFrom = 'taxResidency';\n      data.declarationDto.surveyData['taxResidencyOutsideBahamas'] = null;\n      data.declarationDto.surveyData['hasImmediateParent'] = null;\n      data.declarationDto.surveyData['taxResidencyHasParent'] = null;\n      data.declarationDto.surveyData['taxResidencyImmediateParentEntity'] = null;\n      data.declarationDto.surveyData['taxResidencyUltimateParentEntityInfo'] = null;\n      data.declarationDto.surveyData['taxResidencyJurisdictionEntityIsTaxResident'] = null;\n      data.declarationDto.surveyData['taxResidencyTaxpayerIDNumber'] = null;\n      data.declarationDto.surveyData['taxResidencyEvidenceOfTaxResidency'] = null;\n    }\n    if (taxResidencyIsInvestmentFund === false) {\n      if (hasImmediateParent === false) {\n        data.declarationDto.surveyData['taxResidencyImmediateParentEntity'] = null;\n      }\n      if (taxResidencyHasParent === false) {\n        data.declarationDto.surveyData['taxResidencyUltimateParentEntityInfo'] = null;\n      }\n    }\n    if (taxResidencyOutsideBahamas === false) {\n      data.declarationDto.surveyData['taxResidencyJurisdictionEntityIsTaxResident'] = null;\n      data.declarationDto.surveyData['taxResidencyTaxpayerIDNumber'] = null;\n      data.declarationDto.surveyData['taxResidencyEvidenceOfTaxResidency'] = null;\n    }\n    if (taxResidencyOutsideBahamas) {\n      skipToEnd = true;\n      skipFrom = 'taxResidency';\n    }\n    if (skipToEnd === true && skipFrom === 'taxResidency') {\n      this.skipAndClearSectionsImport(data, ['activityDetail']);\n    }\n  }\n  skipAndClearSectionsImport(data, sectionsToClear) {\n    for (const clearKey in data.declarationDto.surveyData) {\n      //entity Details\n      if (sectionsToClear.includes('entityDetails')) {\n        if (clearKey === 'entityDetailsTIN') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'entityDetailsAnnualIncome' && data.declarationDto.surveyData[clearKey] && data.declarationDto.surveyData[clearKey][0]?.currencyValue != null) data.declarationDto.surveyData[clearKey][0].currencyValue = null;\n        if (clearKey === 'entityDetailsBusinessSameAsRegisteredAddress') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'entityDetailsEnterDifferntBusinessAddress') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'entityDetailsNameMNE') data.declarationDto.surveyData[clearKey] = null;\n      }\n      //relevantActivity\n      if (sectionsToClear.includes('relevantActivity')) {\n        if (clearKey === 'relevantActRelevantActivities') data.declarationDto.surveyData[clearKey] = null;\n      }\n      //tax residency\n      if (sectionsToClear.includes('taxResidency')) {\n        if (clearKey === 'taxResidency100PercentBahamian') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyIsInvestmentFund') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyOutsideBahamas') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyJurisdictionEntityIsTaxResident') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyTaxpayerIDNumber') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyEvidenceOfTaxResidency') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyHasParent') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyUltimateParentEntityInfo') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'hasImmediateParent') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'taxResidencyImmediateParentEntity') data.declarationDto.surveyData[clearKey] = null;\n      }\n      //activity detail\n      if (sectionsToClear.includes('activityDetail')) {\n        if (clearKey === 'intellectualPropertyBusiness') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'holdingBusinessQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'bankingQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'insuranceQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'fundManagmentQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'financeQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'headquartersQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'shippingQuestions') data.declarationDto.surveyData[clearKey] = null;\n        if (clearKey === 'distributionQuestions') data.declarationDto.surveyData[clearKey] = null;\n      }\n    }\n  }\n  setErrorsOnPageChangeImport(sender, options) {\n    this.validationErrors = [];\n    const oldPage = options?.oldCurrentPage?.propertyHash?.name;\n    const newPage = options?.newCurrentPage?.propertyHash?.name;\n    this.hasValidationErrors = false;\n    // Setting up dto\n    let declaratioDto = {\n      informationRequestedArray: [],\n      surveyData: JSON.parse(JSON.stringify(this.surveyresults)),\n      actionEnforcedFileNames: [],\n      declarationTemplateId: this.templateId ?? null\n    };\n    this.convertAllDatesToUTC(declaratioDto);\n    if (declaratioDto.surveyData.relevantActRelevantActivities) {\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\n    }\n    if (this.importdeclarationid) declaratioDto.id = this.importdeclarationid;\n    declaratioDto.surveyData.entityId = this.entityId;\n    if (options.isGoingForward) {\n      this.declarationImportService.validatePageByPageNameAndInput(oldPage, declaratioDto).subscribe(result => {\n        const errors = result.validationResult.errors.filter(item => item.severity === 0);\n        const warnings = result.validationResult.errors.filter(item => item.severity !== 0);\n        this.setValidations(errors);\n        this.setWarnings(warnings);\n        if (this.validationErrors.length === 0) {\n          this.isBackendValidationComplete = true;\n          this.hasValidationErrors = false;\n        }\n        if (!this.hasValidationErrors && this.isBackendValidationComplete) {\n          if (!this.haswarnings) {\n            //next page\n            this.survey.nextPage();\n          } else {\n            //we have warnings but no errors\n            //is first click\n            if (this.firstClick) {\n              this.firstClick = false;\n            } else {\n              // go next\n              this.survey.nextPage();\n            }\n          }\n        }\n        if (this.hasValidationErrors || this.haswarnings) {\n          this.scrollToTop();\n        }\n      });\n    }\n  }\n  mapSurveyJson() {\n    // Taking everything from each page of the active survey and putting it into the correct panel on the view survey\n    // If page names are changed in active survey must be reflected here\n    this.viewTemplate.pages[0].elements.forEach(element => {\n      this.activeTemplate.pages.forEach(page => {\n        if (element.name === page.name) element.elements = page.elements;\n      });\n    });\n    Serializer.addProperty(\"question\", {\n      name: \"inputWidth\",\n      type: \"string\"\n    });\n    this.survey = new Model(this.viewTemplate);\n    this.survey.applyTheme(themeJson);\n    //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\n    const converter = new Converter();\n    this.survey.onTextMarkdown.add(function (survey, options) {\n      // Convert Markdown to HTML\n      let str = converter.makeHtml(options.text);\n      // Remove root paragraphs <p></p>\n      str = str.substring(3);\n      str = str.substring(0, str.length - 4);\n      // Set HTML markup to render\n      options.html = str;\n    });\n    // this.survey.onDownloadFile.add((_, options) => {\n    //   this.onDownloadFile(options);\n    // });\n    this.survey.onClearFiles.add((_, options) => {\n      this.onClearFiles(options);\n    });\n  }\n  onClearFiles(options) {\n    //call declaration service delete declaration file\n    var declarationId = this.declarationId;\n    var fileName = options.fileName;\n    var docType = options.name;\n    const fileUploadLocation = options?.name;\n    const spinnerName = fileUploadLocation + \"Spinner\";\n    let idToUse = \"\";\n    let isImport = false;\n    if (this.importdeclarationid && !this.declarationId) {\n      idToUse = this.importdeclarationid;\n      isImport = true;\n    } else {\n      idToUse = this.declarationId;\n      isImport = false;\n    }\n    if (options.fileName != null) {\n      // only one file at a time deleted\n      if (docType && idToUse && fileName) {\n        if (this.surveyModel) {\n          this.surveyModel.setVariable(spinnerName, true);\n        }\n        this.declarationService.deleteDeclarationAttachmentFile(idToUse, fileName, docType, isImport).pipe(finalize(() => {\n          if (this.surveyModel) {\n            this.surveyModel.setVariable(spinnerName, false);\n          }\n        })).subscribe(result => {\n          console.log('File delete');\n          options.callback(\"success\");\n          if (this.declarationId) {\n            this.saveAsDraft().catch(error => {\n              console.log(error);\n            });\n          }\n        });\n      }\n    } else {\n      // when the clear all button is used\n      let listOfFiles = [];\n      if (options?.value) {\n        for (const files in options.value) {\n          for (const fileInfo in options.value[files]) {\n            if (fileInfo === 'name') {\n              fileName = options.value[files][fileInfo];\n              listOfFiles.push(fileName);\n            }\n          }\n        }\n        if (listOfFiles.length > 0) {\n          this.declarationService.deleteMultipleDeclarationAttachmentsFile(idToUse, listOfFiles, docType, isImport).subscribe(result => {\n            options.callback(\"success\");\n            if (this.declarationId) {\n              this.saveAsDraft().catch(error => {\n                console.log(error);\n              });\n            }\n          });\n        }\n      }\n    }\n  }\n  formatDate(dateStr) {\n    return DateHelper.formatEstUtcDate(dateStr, 'yyyy-MM-dd');\n  }\n  setImportSurveyData() {\n    let activities = [];\n    this.declarationImportService.getImport(this.importdeclarationid).subscribe(result => {\n      this.survey.data = result.declarationDto.surveyData;\n      for (const key in result.declarationDto.surveyData) {\n        if (result.declarationDto.surveyData[key]) {\n          // dates need to be set up by trimming the extra stuff off\n          if (key === \"financialPeriodStartDate\") {\n            this.validStartDate = this.formatDate(result.declarationDto.surveyData[key]);\n            result.declarationDto.surveyData[key] = this.formatDate(result.declarationDto.surveyData[key]);\n          }\n          if (key === \"financialPeriodEndDate\") {\n            this.validEndDate = this.formatDate(result.declarationDto.surveyData[key]);\n            result.declarationDto.surveyData[key] = this.formatDate(result.declarationDto.surveyData[key]);\n          }\n          if (key === \"relevantActRelevantActivities\") {\n            activities = result.declarationDto.surveyData[key];\n          }\n          if (this.compositeKeys.includes(key)) {\n            if (key === \"holdingBusinessQuestions\") {\n              result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\n              result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\n            }\n            if (key === \"intellectualPropertyBusiness\") {\n              result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\n              result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\n            }\n            if (key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\") {\n              // Other activities\n              result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\n              result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\n            }\n          }\n        }\n      }\n      this.clearIncorrectDataImport(result);\n      this.survey.data = JSON.parse(JSON.stringify(result.declarationDto.surveyData));\n      //console.log(this.survey.data)\n      this.mapData(activities);\n      this.surveyModel = this.survey;\n      this.isLoading = false;\n    });\n  }\n  setSurveyData() {\n    let activities = [];\n    if (this.declarationId || this.historyId) {\n      // if declaration ID then go get that and set survey\n      var observable;\n      var contents = '';\n      if (this.historyId) {\n        observable = this.historyService.get(this.historyId);\n        contents = 'declarationContents';\n      } else {\n        observable = this.declarationService.get(this.declarationId);\n        contents = 'declarationDto';\n      }\n      observable.subscribe(result => {\n        this.survey.data = result[contents].surveyData;\n        for (const key in result[contents].surveyData) {\n          if (result[contents].surveyData[key]) {\n            // dates need to be set up by trimming the extra stuff off\n            if (key === \"financialPeriodStartDate\") {\n              this.validStartDate = this.formatDate(result[contents].surveyData[key]);\n              result[contents].surveyData[key] = this.formatDate(result[contents].surveyData[key]);\n            }\n            if (key === \"financialPeriodEndDate\") {\n              this.validEndDate = this.formatDate(result[contents].surveyData[key]);\n              console.log(this.validEndDate);\n              result[contents].surveyData[key] = this.formatDate(result[contents].surveyData[key]);\n            }\n            if (key === \"relevantActRelevantActivities\") {\n              activities = result[contents].surveyData[key];\n            }\n            if (this.compositeKeys.includes(key)) {\n              if (key === \"holdingBusinessQuestions\") {\n                result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\n                result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\n              }\n              if (key === \"intellectualPropertyBusiness\") {\n                result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\n                result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\n              }\n              if (key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\") {\n                // Other activities\n                result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\n                result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\n              }\n            }\n          }\n        }\n        this.survey.data = JSON.parse(JSON.stringify(result[contents].surveyData));\n        this.mapData(activities);\n        // is in view mode ?\n        if (this.action === 'view') {\n          this.survey.mode = \"display\";\n          this.hasValidationErrors = false;\n          this.haswarnings = false; // no need to show this stuff in view mode\n        }\n        this.survey.setVariable(\"otasReceiptUploadSpinner\", false);\n        this.survey.setVariable(\"taxResidencyEvidenceOfTaxResidencySpinner\", false);\n        this.survey.setVariable(\"intelPropBusEmployeeResponsibleGenerationIncomeAttachmentSpinner\", false);\n        this.survey.setVariable(\"intelPropBusStrategicDecisionsAttachmentSpinner\", false);\n        this.survey.setVariable(\"intelPropBusNatureOfTradingActivityAttachmentSpinner\", false);\n        this.survey.setVariable(\"highRiskBusinessPlanDetailsAttachmentSpinner\", false);\n        this.survey.setVariable(\"highRiskEvidenceDecisionMakingInBahamasAttachmentSpinner\", false);\n        this.survey.setVariable(\"highRiskOtherFactsAttachmentSpinner\", false);\n        this.survey.setVariable(\"supportingAttachmentSpinner\", false);\n        this.surveyModel = this.survey;\n        this.isLoading = false;\n      });\n    } else {\n      // new survey\n      this.setNewSurveyData();\n    }\n  }\n  setNewSurveyData() {\n    // call the valid dates api, get the dates and set them\n    this.declarationService.getLatestValidDatesByEntityId(this.entityId).subscribe(result => {\n      this.survey.setValue('financialPeriodStartDate', this.formatDate(result[0]));\n      this.survey.setValue('financialPeriodEndDate', this.formatDate(result[1]));\n      this.validEndDate = this.formatDate(result[1]);\n      this.validStartDate = this.formatDate(result[0]);\n      this.survey.setVariable(\"otasReceiptUploadSpinner\", false);\n      this.survey.setVariable(\"taxResidencyEvidenceOfTaxResidencySpinner\", false);\n      this.survey.setVariable(\"intelPropBusEmployeeResponsibleGenerationIncomeAttachmentSpinner\", false);\n      this.survey.setVariable(\"intelPropBusStrategicDecisionsAttachmentSpinner\", false);\n      this.survey.setVariable(\"intelPropBusNatureOfTradingActivityAttachmentSpinner\", false);\n      this.survey.setVariable(\"highRiskBusinessPlanDetailsAttachmentSpinner\", false);\n      this.survey.setVariable(\"highRiskEvidenceDecisionMakingInBahamasAttachmentSpinner\", false);\n      this.survey.setVariable(\"highRiskOtherFactsAttachmentSpinner\", false);\n      this.survey.setVariable(\"supportingAttachmentSpinner\", false);\n      this.surveyModel = this.survey;\n      this.isLoading = false;\n    });\n  }\n  mapData(activities) {\n    // mapping from id to name\n    if (activities) {\n      if (this.survey.data['relevantActRelevantActivities']) {\n        let convertedActivities = [];\n        this.activityLookup.getList({\n          maxResultCount: 100\n        }).subscribe(result => {\n          result.items.forEach(element => {\n            activities.forEach(activity => {\n              if (element.id === activity) {\n                convertedActivities.push(element.name);\n              }\n            });\n          });\n          if (convertedActivities.length > 0) this.survey.setValue('relevantActRelevantActivities', convertedActivities);else this.survey.setValue('relevantActRelevantActivities', activities); // if name was returned by api then we dont have any ids to convert\n        });\n      }\n    }\n  }\n  setValidations(errors) {\n    this.validationErrors = [];\n    for (const error in errors) {\n      for (const errorInfo in errors[error]) {\n        if (errorInfo === 'errorMessage' && errors[error]['severity'] === 0) {\n          // only add errors not warnings\n          this.validationErrors.push(errors[error][errorInfo]);\n        }\n      }\n    }\n    if (this.validationErrors.length > 0) this.hasValidationErrors = true;else this.hasValidationErrors = false;\n  }\n  setWarnings(warnings) {\n    this.validationWarnings = [];\n    for (const warning in warnings) {\n      for (const warningInfo in warnings[warning]) {\n        if (warningInfo === 'errorMessage') {\n          this.validationWarnings.push(warnings[warning][warningInfo]);\n        }\n      }\n    }\n    if (this.validationWarnings.length > 0) this.haswarnings = true;else this.haswarnings = false;\n  }\n  surveyComplete(sender) {\n    // Setting up dto\n    if (this.templateVersion != 1) {\n      let declaratioDto = {\n        statusLookupId: this.statusMappings.submitted,\n        status: \"Submitted\",\n        informationRequestedArray: this.declarationData?.informationRequestedArray ?? [],\n        surveyData: JSON.parse(JSON.stringify(this.surveyresults)),\n        actionEnforcedFileNames: [],\n        isReopened: this.declarationData?.isReopened,\n        declarationTemplateId: this.templateId ?? null\n      };\n      this.convertAllDatesToUTC(declaratioDto);\n      if (declaratioDto.surveyData.relevantActRelevantActivities) {\n        const convertedResult = this.convertActivitiesNameToId(declaratioDto);\n        declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\n      }\n      if (this.declarationId) declaratioDto.id = this.declarationId;\n      declaratioDto.surveyData.entityId = this.entityId;\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: 'Are you sure you want to submit',\n        text: \"Please note that once submitted, the form will be locked and received by the Ministry of Finance and will be subject to further review and/or onsite inspection and audit\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.declarationService.submit(declaratioDto).subscribe(result => {\n            this.setValidations(result.validationResult.errors);\n            if (this.validationErrors.length === 0) {\n              // if no validation error display success and allow it to go to complete screen\n              sender.isCompleted = false; // if true it will display the thank you for completing the survey for a split second before going to the view mode\n              if (!this.declarationId) this.declarationId = result.declarationId;\n              this.router.navigate(['/es-declaration'], {\n                queryParams: {\n                  declarationid: this.declarationId,\n                  entityid: this.entityId,\n                  action: 'view',\n                  status: \"Submitted\",\n                  source: this.source\n                }\n              });\n              this.action = 'view';\n              this.status = 'Submitted';\n              this.setSurvey();\n              this.hasValidationErrors = false;\n              this.sweetAlert.fireDialog({\n                status: \"success\" /* DialogStatus.SUCCESS */,\n                source: \"es-declaration-submit\",\n                type: \"toaster\"\n              });\n            } else {\n              // validation errors do not go to complete screen\n              sender.isCompleted = false;\n              this.hasValidationErrors = true;\n              this.sweetAlert.fireDialog({\n                status: \"fail\" /* DialogStatus.FAILED */,\n                source: \"es-declaration-submit\",\n                type: \"toaster\"\n              });\n            }\n          });\n        }\n      });\n    } else {\n      sender.isCompleted = false;\n      this.sweetAlert.fireDialog({\n        type: 'DeclarationSubmitVersion1Error'\n      });\n    }\n  }\n  convertLocalToUtcDate(date) {\n    if (date) {\n      //const convertedDate = new Date(date + \"T00:00\"); // set the time stamp in the survey to midnight local time\n      //Always treat local time is EST\n      const convertedDate = zonedTimeToUtc(date, this.assumedLocalZone);\n      return convertedDate.toISOString(); //convert it to utc time\n    } else return date;\n  }\n  convertAllDatesToUTC(declarationDto) {\n    if (declarationDto.surveyData.relevantActRelevantActivities) {\n      const listOfActivities = declarationDto.surveyData.relevantActRelevantActivities;\n      listOfActivities.forEach(activity => {\n        if (activity === \"Holding business\" && 'holdingBusinessQuestions' in declarationDto.surveyData && declarationDto.surveyData.holdingBusinessQuestions !== null) {\n          declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Distribution and service centre business\" && 'distributionQuestions' in declarationDto.surveyData && declarationDto.surveyData.distributionQuestions !== null) {\n          declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Intellectual property business\" && 'intellectualPropertyBusiness' in declarationDto.surveyData && declarationDto.surveyData.intellectualPropertyBusiness !== null) {\n          declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Shipping business\" && 'shippingQuestions' in declarationDto.surveyData && declarationDto.surveyData.shippingQuestions !== null) {\n          declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Headquarters business\" && 'headquartersQuestions' in declarationDto.surveyData && declarationDto.surveyData.headquartersQuestions !== null) {\n          declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Finance and leasing business\" && 'financeQuestions' in declarationDto.surveyData && declarationDto.surveyData.financeQuestions !== null) {\n          declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Fund management business\" && 'fundManagmentQuestions' in declarationDto.surveyData && declarationDto.surveyData.fundManagmentQuestions !== null) {\n          declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Insurance business\" && 'insuranceQuestions' in declarationDto.surveyData && declarationDto.surveyData.insuranceQuestions !== null) {\n          declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n        if (activity === \"Banking business\" && 'bankingQuestions' in declarationDto.surveyData && declarationDto.surveyData.bankingQuestions !== null) {\n          declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\n          declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\n        }\n      });\n    }\n    declarationDto.surveyData.financialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.financialPeriodEndDate);\n    declarationDto.surveyData.financialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.financialPeriodStartDate);\n  }\n  saveAsDraftImport() {\n    // Setting up dto\n    let declaratioDto = {\n      statusLookupId: this.statusMappings.Draft,\n      status: \"Draft\",\n      informationRequestedArray: [],\n      surveyData: JSON.parse(JSON.stringify(this.surveyresults)),\n      declarationTemplateId: this.templateId ?? null,\n      actionEnforcedFileNames: []\n    };\n    this.convertAllDatesToUTC(declaratioDto);\n    if (declaratioDto.surveyData.relevantActRelevantActivities) {\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\n    }\n    if (this.importdeclarationid) declaratioDto.id = this.importdeclarationid;\n    declaratioDto.surveyData.entityId = this.entityId;\n    this.declarationImportService.saveDraft(declaratioDto).subscribe(result => {\n      this.setValidations(result.validationResult.errors);\n      if (this.validationErrors.length === 0) {\n        // if no validation error display success and allow it to go to complete screen\n        this.router.navigate(['/es-import/importdetail'], {\n          queryParams: {\n            id: this.importfileid\n          }\n        });\n        this.hasValidationErrors = false;\n        this.sweetAlert.fireDialog({\n          status: \"success\" /* DialogStatus.SUCCESS */,\n          source: \"es-declaration-import-save\",\n          type: \"toaster\"\n        });\n      } else {\n        // validation errors do not go to complete screen\n        this.hasValidationErrors = true;\n        this.sweetAlert.fireDialog({\n          status: \"fail\" /* DialogStatus.FAILED */,\n          source: \"es-declaration-import-save\",\n          type: \"toaster\"\n        });\n      }\n    });\n  }\n  preSaveVersionWarningCheck() {\n    var _this3 = this;\n    return _asyncToGenerator(function* (showToaster = true) {\n      //only need to call this when using the save buttons \n      // uncomment 3 below lines to show upgrade popup before save.\n      let completed = yield _this3.upgradeToLatestTemplatePopup();\n      if (!completed) {\n        // if the user selected yes then BE will save the current data and update template otherwise just do the save normally. \n        _this3.saveAsDraft().catch(error => {\n          console.log(error);\n        });\n      }\n    }).apply(this, arguments);\n  }\n  saveAsDraft() {\n    var _this4 = this;\n    return _asyncToGenerator(function* (showToaster = true) {\n      var current = _this4;\n      return new Promise((resolve, reject) => {\n        current.surveyresults = JSON.parse(JSON.stringify(current.survey.data));\n        if (current.source === 'Import') {\n          current.saveAsDraftImport();\n          resolve();\n        } else {\n          _this4.validationErrors = [];\n          _this4.hasValidationErrors = false;\n          if ('financialPeriodEndDate' in current.surveyresults && 'financialPeriodStartDate' in current.surveyresults) {\n            let saveCheck = {\n              informationRequestedArray: [],\n              surveyData: JSON.parse(JSON.stringify(current.surveyresults)),\n              declarationTemplateId: current.templateId ?? null,\n              actionEnforcedFileNames: []\n            };\n            if (saveCheck.surveyData.relevantActRelevantActivities) {\n              const convertedResult = current.convertActivitiesNameToId(saveCheck);\n              saveCheck.surveyData.relevantActRelevantActivities = convertedResult;\n            }\n            if (current.declarationId) saveCheck.id = current.declarationId;\n            saveCheck.surveyData.entityId = current.entityId;\n            saveCheck.surveyData.financialPeriodEndDate = current.convertLocalToUtcDate(saveCheck.surveyData.financialPeriodEndDate);\n            saveCheck.surveyData.financialPeriodStartDate = current.convertLocalToUtcDate(saveCheck.surveyData.financialPeriodStartDate);\n            let canSave = true;\n            current.declarationService.validatePageByPageNameAndInput('financialPeriod', saveCheck).subscribe(response => {\n              let errors = response.validationResult.errors;\n              response.validationResult.isValid;\n              for (const error in errors) {\n                for (const errorInfo in errors[error]) {\n                  if (errorInfo === 'propertyName' && (errors[error][errorInfo].includes(\"FinancialPeriodStartDate\") || errors[error][errorInfo].includes(\"FinancialPeriodEndDate\"))) {\n                    _this4.validationErrors.push(errors[error][\"errorMessage\"]);\n                    _this4.hasValidationErrors = true;\n                    canSave = false;\n                  }\n                }\n              }\n              if (canSave) {\n                let declaratioDto = {\n                  statusLookupId: current.statusMappings.Draft,\n                  status: \"Draft\",\n                  informationRequestedArray: _this4.declarationData?.informationRequestedArray ?? [],\n                  surveyData: JSON.parse(JSON.stringify(current.surveyresults)),\n                  declarationTemplateId: current.templateId ?? null,\n                  actionEnforcedFileNames: []\n                };\n                current.convertAllDatesToUTC(declaratioDto);\n                if (declaratioDto.surveyData.relevantActRelevantActivities) {\n                  const convertedResult = current.convertActivitiesNameToId(declaratioDto);\n                  declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\n                }\n                if (current.declarationId) declaratioDto.id = current.declarationId;\n                declaratioDto.surveyData.entityId = current.entityId;\n                current.declarationService.saveDraft(declaratioDto).subscribe(result => {\n                  if (showToaster) {\n                    current.sweetAlert.fireDialog({\n                      status: \"success\" /* DialogStatus.SUCCESS */,\n                      source: \"es-declaration\",\n                      type: \"toaster\"\n                    });\n                  }\n                  if (!current.declarationId) {\n                    current.declarationId = result.declarationId;\n                    current.router.navigate(['/es-declaration'], {\n                      queryParams: {\n                        declarationid: current.declarationId,\n                        entityid: current.entityId,\n                        action: 'edit',\n                        status: current.status,\n                        source: current.source\n                      }\n                    });\n                  }\n                  resolve();\n                });\n              } else {\n                current.sweetAlert.fireDialog({\n                  status: \"fail\" /* DialogStatus.FAILED */,\n                  source: \"es-declaration\",\n                  type: \"toaster\"\n                });\n                reject();\n              }\n            });\n          } else {\n            current.sweetAlert.fireDialog({\n              status: \"fail\" /* DialogStatus.FAILED */,\n              source: \"es-declaration\",\n              type: \"toaster\"\n            });\n            reject();\n          }\n        }\n      });\n    }).apply(this, arguments);\n  }\n  convertActivitiesNameToId(declaratioDto) {\n    let convertedActivities = [];\n    if (declaratioDto.surveyData.relevantActRelevantActivities) {\n      declaratioDto.surveyData.relevantActRelevantActivities.forEach(element => {\n        if (element === \"Holding business\") convertedActivities.push(this.activityMappings.holding);\n        if (element === \"Distribution and service centre business\") convertedActivities.push(this.activityMappings.distribution);\n        if (element === \"Intellectual property business\") convertedActivities.push(this.activityMappings.ip);\n        if (element === \"Shipping business\") convertedActivities.push(this.activityMappings.shipping);\n        if (element === \"Headquarters business\") convertedActivities.push(this.activityMappings.headquarters);\n        if (element === \"Finance and leasing business\") convertedActivities.push(this.activityMappings.finance);\n        if (element === \"Fund management business\") convertedActivities.push(this.activityMappings.funds);\n        if (element === \"Insurance business\") convertedActivities.push(this.activityMappings.insurance);\n        if (element === \"Banking business\") convertedActivities.push(this.activityMappings.banking);\n        if (element === \"none\") convertedActivities.push('none');\n      });\n      return convertedActivities;\n    }\n    return convertedActivities;\n  }\n  preProcessCigaDropdown() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      ComponentCollection.Instance.items.forEach(Component => {\n        if (Component['json']['name'] === \"IntellectualPropertyBusiness\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivity') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.ip;\n              }\n              if (element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivityComment') {\n                elements['visibleIf'] = \"{IntellectualPropertyBusiness.intelPropBusCIGAInBahamasForRelevantActivity} anyof ['\" + _this5.cigaOtherId.ip + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"bankingOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.banking;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{bankingQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.banking + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"distributionOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.distribution;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{distributionQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.distribution + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"financeOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.finance;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{financeQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.finance + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"fundManagmentOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.funds;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{fundManagmentQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.funds + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"headquartersOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.headquarters;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{headquartersQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.headquarters + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"insuranceOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.insurance;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{insuranceQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.insurance + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"shippingOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this5.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this5.activityMappings.shipping;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{shippingQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this5.cigaOtherId.shipping + \"']\";\n              }\n            }\n          });\n        }\n      });\n    })();\n  }\n  setUrls(SurveyTemplate) {\n    for (const key in SurveyTemplate) {\n      for (const key2 in SurveyTemplate[key]) {\n        for (const key3 in SurveyTemplate[key][key2]) {\n          for (const key4 in SurveyTemplate[key][key2][key3]) {\n            for (const key5 in SurveyTemplate[key][key2][key3][key4]) {\n              for (const key6 in SurveyTemplate[key][key2][key3][key4][key5]) {\n                for (const key7 in SurveyTemplate[key][key2][key3][key4][key5][key6]) {\n                  if (key7 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7] !== null) {\n                    // choices by url is on the question level example is relevant activity drop down\n                    SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url.replace(\"{0}\", this.tenantName);\n                    console.log(key7, SurveyTemplate[key][key2][key3][key4][key5][key6][key7]);\n                  }\n                  for (const key8 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7]) {\n                    for (const key9 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8]) {\n                      if (key9 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9] !== null) {\n                        // choices by url is on a column level example entity details differnt business address country drop down\n                        SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url.replace(\"{0}\", this.tenantName);\n                        console.log(key9, SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9]);\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  intialConfigAndSetup() {\n    const configData = this.configState.getAll();\n    if (configData.currentTenant.isAvailable) {\n      this.tenantName = configData.currentTenant.name;\n    }\n    let lookupObservable = this.statusLookup.getList({\n      maxResultCount: 10\n    });\n    let activityObservable = this.activityLookup.getList({\n      maxResultCount: 100\n    });\n    let cigaObservable = this.cigaLookup.getList({\n      maxResultCount: 100\n    });\n    let templateObservable = this.service.getList(this.templateListDto);\n    let observableList = [lookupObservable, activityObservable, cigaObservable, templateObservable];\n    if (this.declarationId || this.status === \"Imported\") {\n      let declarationData = this.declarationService.get(this.declarationId);\n      observableList.push(declarationData);\n    }\n    forkJoin(observableList).subscribe(result => {\n      const lookupData = result[0].items;\n      const activityData = result[1].items;\n      const cigaData = result[2].items;\n      const templateData = result[3].items;\n      let declarationData;\n      if (result.length > 4) {\n        declarationData = result[4].declarationDto;\n      }\n      lookupData.forEach(element => {\n        if (element.name === \"Submitted\") this.statusMappings.submitted = element.id;\n        if (element.name === \"Resubmitted\") this.statusMappings.Resubmitted = element.id;\n        if (element.name === \"Draft\") this.statusMappings.Draft = element.id;\n        if (element.name === \"Reopened\") this.statusMappings.Reopened = element.id;\n      });\n      activityData.forEach(element => {\n        if (element.name === \"Holding business\") this.activityMappings.holding = element.id;\n        if (element.name === \"Distribution and service centre business\") this.activityMappings.distribution = element.id;\n        if (element.name === \"Intellectual property business\") this.activityMappings.ip = element.id;\n        if (element.name === \"Shipping business\") this.activityMappings.shipping = element.id;\n        if (element.name === \"Headquarters business\") this.activityMappings.headquarters = element.id;\n        if (element.name === \"Finance and leasing business\") this.activityMappings.finance = element.id;\n        if (element.name === \"Fund management business\") this.activityMappings.funds = element.id;\n        if (element.name === \"Insurance business\") this.activityMappings.insurance = element.id;\n        if (element.name === \"Banking business\") this.activityMappings.banking = element.id;\n      });\n      cigaData.forEach(item => {\n        if (item.name.includes('(please specify)')) {\n          if (item.relevantActivityId === this.activityMappings.holding) this.cigaOtherId.holding = item.id;\n          if (item.relevantActivityId === this.activityMappings.distribution) this.cigaOtherId.distribution = item.id;\n          if (item.relevantActivityId === this.activityMappings.ip) this.cigaOtherId.ip = item.id;\n          if (item.relevantActivityId === this.activityMappings.shipping) this.cigaOtherId.shipping = item.id;\n          if (item.relevantActivityId === this.activityMappings.headquarters) this.cigaOtherId.headquarters = item.id;\n          if (item.relevantActivityId === this.activityMappings.finance) this.cigaOtherId.finance = item.id;\n          if (item.relevantActivityId === this.activityMappings.funds) this.cigaOtherId.funds = item.id;\n          if (item.relevantActivityId === this.activityMappings.insurance) this.cigaOtherId.insurance = item.id;\n          if (item.relevantActivityId === this.activityMappings.banking) this.cigaOtherId.banking = item.id;\n        }\n      });\n      var activeTemplate;\n      if (declarationData && declarationData.declarationTemplateId) {\n        activeTemplate = templateData.find(x => x.id == declarationData.declarationTemplateId);\n      } else {\n        activeTemplate = templateData.find(element => element.isActive);\n      }\n      if (activeTemplate) {\n        this.activeTemplate = activeTemplate.survey;\n        this.templateId = activeTemplate.id;\n      }\n      this.compositeQuestionService.setCompositeQuestions(activeTemplate);\n      if (this.templateId && declarationData && declarationData.status) {\n        this.declarationStatus = declarationData.status;\n        this.service.getTemplateVersionById(this.templateId).subscribe(result => {\n          this.templateVersion = result;\n          if (this.templateVersion != 2 && (this.declarationStatus == 'Draft' || this.declarationStatus == 'Reopened')) {\n            this.switchTemplateVisible = true;\n          }\n        });\n      }\n      this.setUrls(this.activeTemplate);\n      this.preProcessCigaDropdown();\n      this.setSurvey();\n    });\n  }\n  switchToEdit() {\n    this.router.navigate(['/es-declaration'], {\n      queryParams: {\n        declarationid: this.declarationId,\n        entityid: this.entityId,\n        action: 'edit',\n        status: this.status,\n        source: this.source\n      }\n    });\n    this.action = 'edit';\n    this.isLoading = true;\n    this.setSurvey();\n  }\n  closeSurvey() {\n    if (this.action !== \"view\") {\n      this.sweetAlert.fireDialog({\n        action: \"delete\",\n        title: \"Are you sure you want to close the Declaration ?\",\n        text: \"Any unsaved changes may be lost\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          if (this.source === 'Import') this.router.navigate(['/es-import/importdetail'], {\n            queryParams: {\n              id: this.importfileid\n            }\n          });else this.router.navigateByUrl('/search-result');\n        }\n      });\n    } else {\n      this.router.navigateByUrl('/search-result');\n    }\n  }\n  scrollToTop() {\n    const element = document.querySelector('.ps'); // of the lepton x content area\n    element.scroll({\n      top: 0,\n      left: 0,\n      behavior: 'smooth'\n    });\n  }\n  deleteImport() {\n    if (this.importdeclarationid) {\n      this.sweetAlert.fireDialog({\n        action: \"delete\",\n        title: \"Are you sure you want to delete the imported Decleration?\",\n        text: \"Action is unreversable\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.declarationImportService.delete(this.importdeclarationid).subscribe(result => {\n            console.log(\"dec Result: \", result);\n            this.sweetAlert.fireDialog({\n              status: \"success\" /* DialogStatus.SUCCESS */,\n              source: \"es-declaration-import-delete\",\n              type: \"toaster\"\n            });\n            this.router.navigate(['/es-import/importdetail'], {\n              queryParams: {\n                id: this.importfileid\n              }\n            });\n          });\n        }\n      });\n    } else {\n      this.sweetAlert.fireDialog({\n        status: \"fail\" /* DialogStatus.FAILED */,\n        source: \"es-declaration-import-delete\",\n        type: \"toaster\"\n      });\n    }\n  }\n  print() {\n    this.isPrinting = true;\n    var element = document.getElementById('survey');\n    var opt = {\n      margin: [2.6, 0, 2.6, 0],\n      filename: 'Declaration-Export.pdf',\n      image: {\n        type: 'jpeg'\n      },\n      html2canvas: {\n        scale: 2,\n        letterRendering: true,\n        onclone: clonedDocument => {\n          Array.from(clonedDocument.querySelectorAll('textarea')).forEach(textAreaEle => {\n            const textArea = textAreaEle;\n            const div = clonedDocument.createElement('div');\n            div.innerText = textArea.value;\n            div.style.width = '100%';\n            div.style.whiteSpace = 'pre-wrap';\n            div.style.overflowWrap = 'break-word';\n            div.style.boxSizing = 'border-box';\n            div.style.padding = 'calc(1.5 * (var(--sjs-base-unit, 8px))) calc(2 * (var(--sjs-base-unit, 8px)))';\n            div.style.lineHeight = 'calc(1.5 * (var(--sjs-internal-font-editorfont-size)))';\n            div.style.fontFamily = 'var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))))';\n            div.style.fontWeight = 'var(--sjs-font-editorfont-weight, 500)';\n            div.style.color = 'var(--sjs-font-editorfont-color, rgba(0, 0, 0, 0.91))';\n            div.style.fontSize = '16px';\n            div.style.backgroundColor = 'var(--sjs-editorpanel-backcolor, #f9f9f9)';\n            div.style.border = 'none';\n            div.style.borderRadius = 'var(--sjs-editorpanel-cornerRadius, 4px)';\n            div.style.textAlign = 'start';\n            div.style.verticalAlign = 'middle';\n            if (!textArea.value) {\n              div.style.minHeight = '3em';\n            }\n            div.style.boxShadow = 'var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, 0.15)), 0 0 0 0px var(--sjs-primary-backcolor, #19b394)';\n            textArea.style.display = 'none';\n            textArea.parentElement.append(div);\n          });\n          Array.from(clonedDocument.querySelectorAll('input')).forEach(inputEle => {\n            const input = inputEle;\n            const div = clonedDocument.createElement('div');\n            if (input.type === 'date' || input.type === 'text') {\n              div.innerText = input.value;\n              div.style.width = '100%';\n              div.style.whiteSpace = 'pre-wrap';\n              div.style.overflowWrap = 'break-word';\n              div.style.boxSizing = 'border-box';\n              div.style.padding = 'calc(1.5 * (var(--sjs-base-unit, 8px))) calc(2 * (var(--sjs-base-unit, 8px)))';\n              div.style.lineHeight = 'calc(1.5 * (var(--sjs-internal-font-editorfont-size)))';\n              div.style.fontFamily = 'var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))))';\n              div.style.fontWeight = 'var(--sjs-font-editorfont-weight, 500)';\n              div.style.color = 'var(--sjs-font-editorfont-color, rgba(0, 0, 0, 0.91))';\n              div.style.fontSize = '16px';\n              div.style.backgroundColor = 'var(--sjs-editorpanel-backcolor, #f9f9f9)';\n              div.style.border = 'none';\n              div.style.borderRadius = 'var(--sjs-editorpanel-cornerRadius, 4px)';\n              div.style.textAlign = 'start';\n              div.style.verticalAlign = 'middle';\n              div.style.boxShadow = 'var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, 0.15)), 0 0 0 0px var(--sjs-primary-backcolor, #19b394)';\n              input.style.display = 'none';\n              if (!input.value) {\n                div.style.minHeight = '3em';\n              }\n              input.parentElement.append(div);\n            } else {\n              input.style.display = 'none';\n            }\n          });\n        }\n      },\n      jsPDF: {\n        unit: 'mm',\n        format: [element.offsetWidth / 3, 450],\n        orientation: 'p'\n      },\n      pagebreak: {\n        avoid: ['.sd-question', '.sd-question__content', '.sd-text__content', '.sd-element__title']\n      },\n      removeContainer: false\n    };\n    html2pdf().set(opt).from(element).save().then(() => {\n      this.isPrinting = false;\n      this.sweetAlert.fireDialog({\n        status: \"success\" /* DialogStatus.SUCCESS */,\n        source: \"es-declaration-download\",\n        type: \"toaster\"\n      });\n    }).catch(error => {\n      console.error('Error saving PDF:', error);\n      this.sweetAlert.fireDialog({\n        status: \"fail\" /* DialogStatus.FAILED */,\n        source: \"es-declaration-download\",\n        type: \"toaster\"\n      });\n      this.isPrinting = false;\n    });\n  }\n  submitImport() {\n    if (this.importdeclarationid) {\n      let declerationDto = {\n        informationRequestedArray: [],\n        surveyData: JSON.parse(JSON.stringify(this.survey.data)),\n        declarationTemplateId: this.templateId,\n        actionEnforcedFileNames: []\n      };\n      this.convertAllDatesToUTC(declerationDto);\n      if (declerationDto.surveyData.relevantActRelevantActivities) {\n        const convertedResult = this.convertActivitiesNameToId(declerationDto);\n        declerationDto.surveyData.relevantActRelevantActivities = convertedResult;\n      }\n      if (this.importdeclarationid) declerationDto.id = this.importdeclarationid;\n      declerationDto.surveyData.entityId = this.entityId;\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: 'Are you sure you want to submit',\n        text: \"\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.declarationImportService.saveAndSubmit(declerationDto).subscribe(result => {\n            this.setValidations(result.validationResult.errors);\n            if (this.validationErrors.length === 0) {\n              // if no validation error display success and allow it to go to complete screen\n              this.router.navigate(['/es-import/importdetail'], {\n                queryParams: {\n                  id: this.importfileid\n                }\n              });\n              this.hasValidationErrors = false;\n              this.sweetAlert.fireDialog({\n                status: \"success\" /* DialogStatus.SUCCESS */,\n                source: \"es-declaration-import-submitted\",\n                type: \"toaster\"\n              });\n            } else {\n              // validation errors do not go to complete screen\n              this.hasValidationErrors = true;\n              this.sweetAlert.fireDialog({\n                status: \"fail\" /* DialogStatus.FAILED */,\n                source: \"es-declaration-import-submitted\",\n                type: \"toaster\"\n              });\n            }\n          });\n        }\n      });\n    } else {\n      this.sweetAlert.fireDialog({\n        status: \"fail\" /* DialogStatus.FAILED */,\n        source: \"es-declaration-import-submit\",\n        type: \"toaster\"\n      });\n    }\n  }\n  upgradeToLatestTemplatePopup() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      // call this to trigger upgrade pop up, will return true if user upgrades else false: use for pre save popup\n      // check template version if old and draft or reopened ask user to upgrade \n      if (_this6.templateVersion === 1 && (_this6.declarationStatus === 'Draft' || _this6.declarationStatus === 'Reopened')) {\n        return new Promise(resolve => {\n          _this6.sweetAlert.fireDialog({\n            action: \"declarationVersionUpgrade\",\n            title: 'Declaration Version Warning',\n            text: \"Please update to the latest declaration version to enable submission. Verify all entered data before submitting, as upgrading to the new template may result in some minor data loss.\",\n            type: \"confirm\"\n          }, /*#__PURE__*/function () {\n            var _ref = _asyncToGenerator(function* (confirm) {\n              if (confirm) {\n                yield _this6.switchDeclarationToLatestTemplate();\n                resolve(true);\n              } else {\n                resolve(false);\n              }\n            });\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }());\n        });\n      } else {\n        return false;\n      }\n    })();\n  }\n  upgradeTemplateButtonClicked() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7.templateVersion === 1 && (_this7.declarationStatus === 'Draft' || _this7.declarationStatus === 'Reopened')) {\n        _this7.sweetAlert.fireDialog({\n          action: \"declarationVersionUpgrade\",\n          title: 'Declaration Version Upgrade Confirmation',\n          text: \"Please be aware that minor data loss may occur after upgrading. Ensure you check all entered data prior to submission.\",\n          type: \"confirm\"\n        }, /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (confirm) {\n            if (confirm) {\n              yield _this7.switchDeclarationToLatestTemplate();\n            }\n          });\n          return function (_x2) {\n            return _ref2.apply(this, arguments);\n          };\n        }());\n      }\n    })();\n  }\n  switchDeclarationToLatestTemplate() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      _this8.surveyresults = JSON.parse(JSON.stringify(_this8.survey.data));\n      let declaratioDto = {\n        informationRequestedArray: _this8.declarationData?.informationRequestedArray ?? [],\n        surveyData: JSON.parse(JSON.stringify(_this8.surveyresults)),\n        declarationTemplateId: _this8.templateId ?? null,\n        actionEnforcedFileNames: []\n      };\n      if (_this8.declarationStatus == 'Draft') {\n        declaratioDto.status = \"Draft\";\n        declaratioDto.statusLookupId = _this8.statusMappings.Draft;\n      } else if (_this8.declarationStatus == 'Reopened') {\n        declaratioDto.status = \"Reopened\";\n        declaratioDto.statusLookupId = _this8.statusMappings.Reopened;\n      }\n      _this8.convertAllDatesToUTC(declaratioDto);\n      if (declaratioDto.surveyData.relevantActRelevantActivities) {\n        const convertedResult = _this8.convertActivitiesNameToId(declaratioDto);\n        declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\n      }\n      if (_this8.declarationId) declaratioDto.id = _this8.declarationId;\n      declaratioDto.surveyData.entityId = _this8.entityId;\n      _this8.declarationService.upgradeTemplateVersionByDto(declaratioDto).subscribe(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (result) {\n          _this8.validationErrors = [];\n          if (!result.validationResult.isValid) {\n            let errors = result.validationResult.errors;\n            for (const error in errors) {\n              for (const errorInfo in errors[error]) {\n                if (errorInfo === 'propertyName' && (errors[error][errorInfo].includes(\"FinancialPeriodStartDate\") || errors[error][errorInfo].includes(\"FinancialPeriodEndDate\"))) {\n                  _this8.validationErrors.push(errors[error][\"errorMessage\"]);\n                  _this8.hasValidationErrors = true;\n                }\n              }\n            }\n            _this8.sweetAlert.fireDialog({\n              status: \"fail\" /* DialogStatus.FAILED */,\n              source: \"upgrade-template-version\",\n              type: \"toaster\"\n            });\n          } else {\n            _this8.isLoading = true;\n            _this8.switchTemplateVisible = false;\n            yield _this8.intialConfigAndSetup();\n            _this8.sweetAlert.fireDialog({\n              status: \"success\" /* DialogStatus.SUCCESS */,\n              source: \"upgrade-template-version\",\n              type: \"toaster\"\n            });\n          }\n        });\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  static {\n    this.ɵfac = function EsDeclarationComponent_Factory(t) {\n      return new (t || EsDeclarationComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.SweetAlertService), i0.ɵɵdirectiveInject(i3.RelevantActivityService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.DeclarationService), i0.ɵɵdirectiveInject(i3.DeclarationStatusService), i0.ɵɵdirectiveInject(i6.CompositeQuestionService), i0.ɵɵdirectiveInject(i3.CigaService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i7.CorporateEntityService), i0.ɵɵdirectiveInject(i3.CountryService), i0.ɵɵdirectiveInject(i8.DeclarationImportService), i0.ɵɵdirectiveInject(i9.MatDialog), i0.ɵɵdirectiveInject(i5.DeclarationHistoryService), i0.ɵɵdirectiveInject(i10.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EsDeclarationComponent,\n      selectors: [[\"app-es-declaration\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 25,\n      vars: 20,\n      consts: [[\"class\", \"mat-spinner-center\", 4, \"ngIf\"], [1, \"card-background\", 3, \"hidden\"], [1, \"display-flex-space\", \"sticky-survey-header\"], [1, \"title-color-font\"], [1, \"button-container\"], [\"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", \"matTooltip\", \"Close\", 3, \"click\", 4, \"ngIf\"], [4, \"abpPermission\"], [\"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", \"id\", \"printButton\", \"matTooltip\", \"Print\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5 \", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"survey\", 1, \"card-background\"], [1, \"divider-margin\"], [3, \"entityId\", \"currentPageIndex\", \"isViewMode\", \"type\"], [3, \"declarationData\", \"declarationId\", \"type\", \"historyId\", 4, \"ngIf\"], [\"class\", \"validation-errors-box spacing-margin\", 4, \"ngIf\"], [\"class\", \"warning-box\", 4, \"ngIf\"], [\"id\", \"survey\", 3, \"model\"], [1, \"mat-spinner-center\"], [1, \"mat-spinner-color\", 3, \"mode\", \"diameter\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"Close\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [1, \"icon\"], [\"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [\"mat-raised-button\", \"\", \"id\", \"printButton\", \"matTooltip\", \"Print\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\", \"disabled\"], [\"class\", \"icon\", 4, \"ngIf\"], [4, \"ngIf\"], [\"diameter\", \"20\", \"color\", \"accent\"], [\"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5 mat-button\", \"matTooltip\", \"Save As Draft\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"Save As Draft\", 1, \"ui-button\", \"margin-l-5\", \"mat-button\", 3, \"click\"], [3, \"declarationData\", \"declarationId\", \"type\", \"historyId\"], [1, \"validation-errors-box\", \"spacing-margin\"], [1, \"validation-error-title\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"validation-error-list\"], [\"class\", \"validation-error-text\", 4, \"ngFor\", \"ngForOf\"], [1, \"validation-error-text\"], [1, \"warning-box\"], [\"class\", \"validation-warning-text\", 4, \"ngFor\", \"ngForOf\"], [1, \"validation-warning-text\"]],\n      template: function EsDeclarationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, EsDeclarationComponent_div_0_Template, 2, 2, \"div\", 0);\n          i0.ɵɵelementStart(1, \"mat-card\", 1)(2, \"mat-card-header\", 2)(3, \"mat-card-title\", 3);\n          i0.ɵɵtext(4, \"Economic Substance Declaration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, EsDeclarationComponent_button_6_Template, 3, 0, \"button\", 5)(7, EsDeclarationComponent_div_7_Template, 2, 1, \"div\", 6)(8, EsDeclarationComponent_button_8_Template, 3, 3, \"button\", 7)(9, EsDeclarationComponent_div_9_Template, 3, 2, \"div\", 6)(10, EsDeclarationComponent_button_10_Template, 4, 0, \"button\", 8)(11, EsDeclarationComponent_button_11_Template, 4, 0, \"button\", 9)(12, EsDeclarationComponent_button_12_Template, 4, 0, \"button\", 9)(13, EsDeclarationComponent_div_13_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementStart(14, \"div\");\n          i0.ɵɵtemplate(15, EsDeclarationComponent_button_15_Template, 4, 0, \"button\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, EsDeclarationComponent_button_16_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-card-content\", 10)(18, \"div\");\n          i0.ɵɵelement(19, \"mat-divider\", 11)(20, \"app-declaration-entity-details\", 12);\n          i0.ɵɵtemplate(21, EsDeclarationComponent_app_assessment_action_view_21_Template, 1, 4, \"app-assessment-action-view\", 13)(22, EsDeclarationComponent_div_22_Template, 6, 1, \"div\", 14)(23, EsDeclarationComponent_div_23_Template, 6, 1, \"div\", 15);\n          i0.ɵɵelement(24, \"survey\", 16);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"hidden\", ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"abpPermission\", \"EsService.Declaration.Submit\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel && ctx.isPrintVisible);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"abpPermission\", \"EsService.Declaration.Submit\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel && !(ctx.surveyModel == null ? null : ctx.surveyModel.isFirstPage));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel && !(ctx.surveyModel == null ? null : ctx.surveyModel.isLastPage));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel && (ctx.surveyModel == null ? null : ctx.surveyModel.isPreviewButtonVisible));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"abpPermission\", \"EsService.Declaration.Submit\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel && (ctx.surveyModel == null ? null : ctx.surveyModel.isLastPage) && !(ctx.surveyModel == null ? null : ctx.surveyModel.isPreviewButtonVisible) && (ctx.surveyModel == null ? null : ctx.surveyModel.isCompleteButtonVisible) && ctx.source === \"Import\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.surveyModel && ctx.switchTemplateVisible);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"entityId\", ctx.entityId)(\"currentPageIndex\", ctx.currentPageIndex)(\"isViewMode\", ctx.isViewMode)(\"type\", ctx.entityType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.displayActions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasValidationErrors && !ctx.isPrinting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.haswarnings && !ctx.isPrinting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.surveyModel);\n        }\n      },\n      dependencies: [i11.MatIcon, i12.MatDivider, i13.MatButton, i14.MatProgressSpinner, i15.MatCard, i15.MatCardContent, i15.MatCardHeader, i15.MatCardTitle, i16.MatTooltip, i17.SurveyComponent, i18.NgForOf, i18.NgIf, i10.PermissionDirective, i19.DeclarationEntityDetailsComponent, i20.AssessmentActionViewComponent],\n      styles: [\".validation-errors-box[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n  min-height: 100px;\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  padding: 10px;\\n  border: 0.2em solid #a40c0c;\\n  border-radius: 4px;\\n  margin-bottom: 1em;\\n}\\n\\n.validation-error-list[_ngcontent-%COMP%] {\\n  list-style: circle;\\n  padding: 0;\\n  margin: 0;\\n  margin-left: 1em;\\n}\\n\\n.validation-error-text[_ngcontent-%COMP%] {\\n  color: red;\\n  font-size: 1em;\\n  margin-bottom: 5px;\\n}\\n\\n.validation-warning-text[_ngcontent-%COMP%] {\\n  color: rgb(255, 166, 0);\\n  font-size: 1em;\\n  margin-bottom: 5px;\\n}\\n\\n.validation-error-title[_ngcontent-%COMP%] {\\n  color: #0095dc;\\n}\\n\\n.top-right-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4em;\\n  right: 8em;\\n}\\n\\n.card-background[_ngcontent-%COMP%] {\\n  background-color: #f4f4f4;\\n}\\n\\n.spacing-margin-button[_ngcontent-%COMP%] {\\n  margin-top: 4em;\\n  margin-bottom: 4em;\\n}\\n\\n.spacing-margin[_ngcontent-%COMP%] {\\n  margin-top: 2em;\\n}\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: flex-end !important;\\n}\\n\\n.warning-box[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n  min-height: 100px;\\n  background-color: #fffce4;\\n  color: #333; \\n\\n  padding: 10px;\\n  border: 0.2em solid #fcc424;\\n  border-radius: 4px;\\n  margin-bottom: 1em;\\n}\\n\\n.divider-margin[_ngcontent-%COMP%] {\\n  margin: 1em;\\n}\\n\\n.sticky-survey-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 5.5em;\\n  z-index: 100;\\n  background-color: #f4f4f4;\\n  flex-wrap: wrap;\\n  height: auto;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Model", "AppComponentBase", "Date<PERSON>elper", "viewDeclarationEmpty", "ComponentCollection", "environment", "SurveyCore", "FunctionFactory", "isAcceptedFileType", "relevantActivityDateValid", "themeJson", "finalize", "fork<PERSON><PERSON>n", "Converter", "init", "html2pdf", "AngularComponentFactory", "Serializer", "SurveyFilePreviewComponent", "zonedTimeToUtc", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵlistener", "EsDeclarationComponent_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "close<PERSON><PERSON>vey", "ɵɵtext", "EsDeclarationComponent_div_7_button_1_Template_button_click_0_listener", "_r3", "switchToEdit", "ɵɵtemplate", "EsDeclarationComponent_div_7_button_1_Template", "surveyModel", "isEditVisible", "EsDeclarationComponent_button_8_Template_button_click_0_listener", "_r4", "print", "EsDeclarationComponent_button_8_mat_icon_1_Template", "EsDeclarationComponent_button_8_span_2_Template", "isPrinting", "EsDeclarationComponent_div_9_button_1_Template_button_click_0_listener", "_r5", "preSaveVersionWarningCheck", "EsDeclarationComponent_div_9_button_2_Template_button_click_0_listener", "_r6", "EsDeclarationComponent_div_9_button_1_Template", "EsDeclarationComponent_div_9_button_2_Template", "action", "source", "isLastPage", "isPreviewButtonVisible", "isCompleteButtonVisible", "EsDeclarationComponent_button_10_Template_button_click_0_listener", "_r7", "prevPage", "EsDeclarationComponent_button_11_Template_button_click_0_listener", "_r8", "nextPage", "EsDeclarationComponent_button_12_Template_button_click_0_listener", "_r9", "showPreview", "EsDeclarationComponent_div_13_button_1_Template_button_click_0_listener", "_r10", "doComplete", "EsDeclarationComponent_div_13_button_1_Template", "EsDeclarationComponent_button_15_Template_button_click_0_listener", "_r11", "submitImport", "EsDeclarationComponent_button_16_Template_button_click_0_listener", "_r12", "upgradeTemplateButtonClicked", "declarationData", "declarationId", "entityType", "historyId", "ɵɵtextInterpolate", "error_r13", "EsDeclarationComponent_div_22_li_5_Template", "validationErrors", "error_r14", "EsDeclarationComponent_div_23_li_5_Template", "validationWarnings", "Instance", "registerComponent", "register", "EsDeclarationComponent", "constructor", "injector", "service", "<PERSON><PERSON><PERSON><PERSON>", "activityLookup", "route", "declarationService", "statusLookup", "compositeQuestionService", "cigaLookup", "router", "corporateEntityService", "countryService", "declarationImportService", "dialog", "historyService", "config", "templateListDto", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "address", "hasValidationErrors", "isPrintVisible", "firstClick", "haswarnings", "statusMappings", "submitted", "Resubmitted", "Draft", "Reopened", "activityMappings", "holding", "distribution", "ip", "shipping", "headquarters", "finance", "funds", "insurance", "banking", "cigaOtherId", "isViewMode", "currentPageIndex", "isBackendValidationComplete", "compositeKeys", "isLoading", "displayActions", "switchTemplateVisible", "templateVersion", "assumedLocalZone", "declarationStatus", "queryParams", "subscribe", "params", "entityId", "status", "importfileid", "importdeclarationid", "console", "log", "ngOnInit", "_this", "_asyncToGenerator", "isCa", "getFeature", "intialConfigAndSetup", "<PERSON><PERSON><PERSON><PERSON>", "_this2", "get", "data", "declarationContents", "declarationDto", "viewTemplate", "mapSurvey<PERSON>son", "setSurveyData", "setSurveyImport", "addProperty", "name", "type", "survey", "activeTemplate", "applyTheme", "showNavigationButtons", "converter", "onTextMarkdown", "add", "options", "str", "makeHtml", "text", "substring", "length", "html", "completedHtml", "onComplete", "sender", "surveyresults", "JSON", "parse", "stringify", "isCompleted", "surveyComplete", "onValueChanged", "valueChanged", "onCurrentPageChanging", "oldCurrentPage", "propertyHash", "newCurrentPage", "isGoingForward", "allow", "setErrorsOnPageChange", "onCurrentPageChanged", "num", "scrollToTop", "onShowingPreview", "onUploadFiles", "_", "saveAsDraft", "then", "uploadDeclarationFile", "catch", "error", "callback", "onClearFiles", "setErrorsOnPageChangeImport", "setImportSurveyData", "isValidFileType", "fileType", "toLowerCase", "files", "count", "fileUploadLocation", "spinnerName", "setVariable", "f", "docType", "question", "split", "pop", "fireDialog", "e", "currentFiles", "getValue", "currentFile", "fileInfo", "reader", "FileReader", "readAsDataURL", "onload", "result", "upload", "fileName", "fileContents", "uploadedDeclarationId", "isImport", "uploadDeclarationDocument", "map", "file", "onerror", "err", "onDownloadFile", "fileValue", "fileList", "header", "GetH<PERSON>er", "idToUse", "downloadCADeclarationDocument", "pipe", "t", "downloadDeclarationDocument", "extension", "value", "setValue", "validStartDate", "validEndDate", "getList", "response", "items", "for<PERSON>ach", "country", "registeredOfficeAddress", "id", "push", "oldPage", "newPage", "declaratioDto", "informationRequestedArray", "surveyData", "actionEnforcedFileNames", "declarationTemplateId", "templateId", "convertAllDatesToUTC", "relevantActRelevantActivities", "convertedResult", "convertActivitiesNameToId", "validatePageByPageNameAndInput", "errors", "validationResult", "filter", "item", "severity", "warnings", "setValidations", "setWarnings", "clearIncorrectDataImport", "endDateYear", "Date", "getFullYear", "skipToEnd", "skip<PERSON>rom", "subjectToReclassification", "esFiledOnOTAS", "otasReceipt", "otasReceiptUpload", "skipAndClearSectionsImport", "includes", "taxResidency100PercentBahamian", "taxResidencyIsInvestmentFund", "taxResidencyOutsideBahamas", "hasImmediateParent", "taxResidencyHasParent", "sectionsToClear", "<PERSON><PERSON><PERSON>", "currencyValue", "pages", "elements", "element", "page", "deleteDeclarationAttachmentFile", "listOfFiles", "deleteMultipleDeclarationAttachmentsFile", "formatDate", "dateStr", "formatEstUtcDate", "activities", "getImport", "key", "mapData", "observable", "contents", "mode", "setNewSurveyData", "getLatestValidDatesByEntityId", "convertedActivities", "activity", "errorInfo", "warning", "warningInfo", "statusLookupId", "isReopened", "title", "confirm", "submit", "navigate", "declarationid", "entityid", "convertLocalToUtcDate", "date", "convertedDate", "toISOString", "listOfActivities", "holdingBusinessQuestions", "holdingBussinessPartOfFinancialPeriodEndDate", "holdingBussinessPartOfFinancialPeriodStartDate", "distributionQuestions", "otherRelevantActivitiesPartOfFinancialPeriodEndDate", "otherRelevantActivitiesPartOfFinancialPeriodStartDate", "intellectualPropertyBusiness", "intelPropPartOfFinancialPeriodEndDate", "intelPropPartOfFinancialPeriodStartDate", "shippingQuestions", "headquartersQuestions", "financeQuestions", "fundManagmentQuestions", "insuranceQuestions", "bankingQuestions", "financialPeriodEndDate", "financialPeriodStartDate", "saveAsDraftImport", "saveDraft", "_this3", "showToaster", "completed", "upgradeToLatestTemplatePopup", "apply", "arguments", "_this4", "current", "Promise", "resolve", "reject", "saveCheck", "canSave", "<PERSON><PERSON><PERSON><PERSON>", "preProcessCigaDropdown", "_this5", "Component", "replace", "tenantName", "setUrls", "SurveyTemplate", "key2", "key3", "key4", "key5", "key6", "key7", "url", "key8", "key9", "configData", "configState", "getAll", "currentTenant", "isAvailable", "lookupObservable", "activityObservable", "cigaObservable", "templateObservable", "observableList", "lookupData", "activityData", "cigaData", "templateData", "relevantActivityId", "find", "x", "isActive", "setCompositeQuestions", "getTemplateVersionById", "navigateByUrl", "document", "querySelector", "scroll", "top", "left", "behavior", "deleteImport", "delete", "getElementById", "opt", "margin", "filename", "image", "html2canvas", "scale", "letterRendering", "onclone", "clonedDocument", "Array", "from", "querySelectorAll", "textArea<PERSON>le", "textArea", "div", "createElement", "innerText", "style", "width", "whiteSpace", "overflowWrap", "boxSizing", "padding", "lineHeight", "fontFamily", "fontWeight", "color", "fontSize", "backgroundColor", "border", "borderRadius", "textAlign", "verticalAlign", "minHeight", "boxShadow", "display", "parentElement", "append", "inputEle", "input", "jsPDF", "unit", "format", "offsetWidth", "orientation", "pagebreak", "avoid", "remove<PERSON><PERSON><PERSON>", "set", "save", "declerationDto", "saveAndSubmit", "_this6", "_ref", "switchDeclarationToLatestTemplate", "_x", "_this7", "_ref2", "_x2", "_this8", "upgradeTemplateVersionByDto", "_ref3", "_x3", "ɵɵdirectiveInject", "Injector", "i1", "TemplateService", "i2", "SweetAlertService", "i3", "RelevantActivityService", "i4", "ActivatedRoute", "i5", "DeclarationService", "DeclarationStatusService", "i6", "CompositeQuestionService", "CigaService", "Router", "i7", "CorporateEntityService", "CountryService", "i8", "DeclarationImportService", "i9", "MatDialog", "DeclarationHistoryService", "i10", "ConfigStateService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "EsDeclarationComponent_Template", "rf", "ctx", "EsDeclarationComponent_div_0_Template", "EsDeclarationComponent_button_6_Template", "EsDeclarationComponent_div_7_Template", "EsDeclarationComponent_button_8_Template", "EsDeclarationComponent_div_9_Template", "EsDeclarationComponent_button_10_Template", "EsDeclarationComponent_button_11_Template", "EsDeclarationComponent_button_12_Template", "EsDeclarationComponent_div_13_Template", "EsDeclarationComponent_button_15_Template", "EsDeclarationComponent_button_16_Template", "EsDeclarationComponent_app_assessment_action_view_21_Template", "EsDeclarationComponent_div_22_Template", "EsDeclarationComponent_div_23_Template", "isFirstPage"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\es-declaration\\containers\\es-declaration\\es-declaration.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\es-declaration\\containers\\es-declaration\\es-declaration.component.html"], "sourcesContent": ["import { Component, Injector } from '@angular/core';\r\nimport { Model } from \"survey-core\";\r\nimport {json} from \"./survey-json\"\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\nimport { SurveyDto, TemplateDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates';\r\nimport { TemplateService, GetTemplateListDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates';\r\nimport { RelevantActivityService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { ActivatedRoute, ParamMap } from '@angular/router';\r\nimport { DeclarationGetResult, DeclarationHistoryService, DeclarationService, SurveyData, UploadDeclarationDocumentDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations';\r\nimport { DeclarationDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations';\r\nimport { DeclarationStatusService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { CorporateEntityDto, CorporateEntityService } from 'proxies/corporate-service/lib/proxy/bdo/ess/corporate-entity-service/corporate-entities';\r\n//import { DatePipe } from '@angular/common';\r\nimport { DateHelper } from '@app/shared/utils/date-helper';\r\nimport { DialogStatus } from '@app/shared/constants/general.constants';\r\nimport { ValidationFailure } from 'proxies/economic-service/lib/proxy/fluent-validation/results';\r\nimport {viewDeclarationEmpty } from '@app/shared/declaration-jsons/view-declaration-json';\r\nimport { ComponentCollection } from \"survey-core\";\r\nimport { CompositeQuestionService } from '@app/shared/services/composite-question.service';\r\nimport { CigaService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { CountryService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { environment } from '@environments/environment';\r\nimport { Router } from '@angular/router';\r\nimport * as SurveyCore from \"survey-core\";\r\nimport * as widgets from \"surveyjs-widgets\";\r\nimport Inputmask from \"inputmask\";\r\nimport { FunctionFactory } from 'survey-core';\r\nimport { isAcceptedFileType, relevantActivityDateValid } from './validationFns';\r\nimport { themeJson } from './survey-theme-json';\r\nimport { DeclarationImportService } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/imports';\r\nimport { GetDeclarationImportDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports/dtos';\r\nimport { Observable, finalize, forkJoin } from 'rxjs';\r\nimport { DeclartionDocumentType } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports/declartion-document-type.enum';\r\nimport { BdoTableActionsRowData, BdoTableColumnDefinition, BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\r\n\r\nimport { PreviewFileComponent } from '@app/features/ca-action-page/containers/attachements-component/preview-file/preview-file.component';\r\nimport { MatDialog } from '@angular/material/dialog';\r\n\r\nimport { getBase64Header } from '@app/shared/utils/base64-header';\r\nimport { ConfigStateService } from '@abp/ng.core';\r\nimport { Converter } from \"showdown\";\r\nimport { init } from './surveyjs-inputmask-override';\r\n\r\nimport html2pdf from 'html2pdf.js'//widgets.inputmask(SurveyCore);\r\nimport { AngularComponentFactory } from 'survey-angular-ui';\r\nimport { Serializer } from \"survey-core\";\r\nimport { SurveyFilePreviewComponent } from '@app/shared/components/survey-file-preview/survey-file-preview.component';\r\nimport { format, parseISO } from 'date-fns';\r\nimport { zonedTimeToUtc } from 'date-fns-tz';\r\n\r\ninit(SurveyCore);\r\n// TODO: Refactor all the settimeouts\r\nAngularComponentFactory.Instance.registerComponent(\"sv-file-preview\", SurveyFilePreviewComponent)\r\n\r\nFunctionFactory.Instance.register(\"isAcceptedFileType\", isAcceptedFileType);\r\nFunctionFactory.Instance.register(\"relevantActivityDateValid\", relevantActivityDateValid);\r\n@Component({\r\n  selector: 'app-es-declaration',\r\n  templateUrl: './es-declaration.component.html',\r\n  styleUrls: ['./es-declaration.component.scss']\r\n})\r\n\r\n\r\nexport class EsDeclarationComponent extends AppComponentBase {\r\n  templateListDto: GetTemplateListDto = {sorting: \"id\", skipCount:0, maxResultCount: 100} // TODO: Get all results not just first 10\r\n\r\n  constructor(injector: Injector,\r\n    private service: TemplateService,\r\n    private sweetAlert: SweetAlertService,\r\n    private activityLookup: RelevantActivityService,\r\n    private route: ActivatedRoute,\r\n    private declarationService: DeclarationService,\r\n    private statusLookup: DeclarationStatusService,\r\n    //private datePipe: DatePipe,\r\n    private compositeQuestionService: CompositeQuestionService,\r\n    private cigaLookup: CigaService,\r\n    private router: Router,\r\n    private corporateEntityService: CorporateEntityService,\r\n    private countryService: CountryService,\r\n    private declarationImportService: DeclarationImportService,\r\n    public dialog: MatDialog,\r\n    public historyService: DeclarationHistoryService,\r\n    private config: ConfigStateService,\r\n   ){\r\n    super(injector);\r\n    this.route.queryParams.subscribe(params => {\r\n      this.declarationId = params['declarationid'] ? params['declarationid'] : null;\r\n      this.entityId =  params['entityid'];\r\n      this.action = params['action'];\r\n      this.status = params['status'];\r\n      this.source = params['source'];\r\n      this.importfileid = params['importfileid'];\r\n      this.importdeclarationid = params['importdeclarationid'];\r\n      this.historyId = params['historyid']\r\n      console.log(this.importdeclarationid)\r\n      console.log(this.declarationId)\r\n    });\r\n    //this.compositeQuestionService.setCompositeQuestions();\r\n  }\r\n  templateId:string;\r\n  historyId:string\r\n  declarationData:DeclarationDto;\r\n  surveyModel: Model;\r\n  address = [];\r\n  survey;\r\n  surveyresults;\r\n  declarationId: string;\r\n  entityId: string;\r\n  action: string;\r\n  status:string;\r\n  source:string;\r\n  importfileid: string;\r\n  importdeclarationid: string\r\n  hasValidationErrors = false;\r\n  validationErrors: string[];\r\n  isPrintVisible: boolean = false;\r\n  isEditVisible: boolean = false;\r\n  firstClick = true;\r\n  validationWarnings: string[] = [];\r\n  haswarnings = false;\r\n  isPrinting = false;\r\n  statusMappings = {submitted: \"\", Resubmitted: \"\", Draft:\"\", Reopened: \"\" };\r\n  activityMappings = {holding: \"\", distribution: \"\", ip:\"\", shipping: \"\",  headquarters:\"\", finance: \"\",  funds:\"\", insurance: \"\", banking:\"\"  };\r\n  cigaOtherId = {holding: \"\", distribution: \"\", ip:\"\", shipping: \"\",  headquarters:\"\", finance: \"\",  funds:\"\", insurance: \"\", banking:\"\"  };\r\n  validStartDate;\r\n  validEndDate;\r\n  isViewMode = false;\r\n  currentPageIndex = 0;\r\n  isBackendValidationComplete = false;\r\n  compositeKeys: string[] = [\"intellectualPropertyBusiness\",\"bankingQuestions\", \"distributionQuestions\", \"financeQuestions\", \"fundManagmentQuestions\",\r\n  \"headquartersQuestions\", \"holdingBusinessQuestions\", \"insuranceQuestions\",\"outsourcingIntellectualPropertyBusiness\", \"shippingQuestions\"]\r\n  activeTemplate: SurveyDto;\r\n  viewTemplate: SurveyDto;\r\n  isLoading = true;\r\n  tenantName: string;\r\n  entityType = 'RA'\r\n  displayActions = true\r\n  switchTemplateVisible = false;\r\n  templateVersion = 2;   //Default template to 2 for new declaration\r\n  assumedLocalZone = 'America/New_York'; //local\r\n  templateDto: TemplateDto;\r\n  declarationStatus = null;\r\n  async ngOnInit(): Promise<void> {\r\n    this.isLoading = true;\r\n    var isCa = this.config.getFeature('SearchService.CASearch') == 'true';\r\n    this.entityType = isCa? 'CA':'RA'\r\n    await this.intialConfigAndSetup();\r\n    //this.isLoading = false;\r\n  }\r\n\r\n  async setSurvey(): Promise<void>{\r\n\r\n    if (this.historyId){\r\n      await this.historyService.get(this.historyId).subscribe((data)=> {\r\n        this.declarationData = data.declarationContents;\r\n        console.log('dec service: ', this.declarationData)\r\n      })\r\n    }\r\n    else if(this.declarationId || this.status === \"Imported\")\r\n    {\r\n      await this.declarationService.get(this.declarationId).subscribe((data)=> {\r\n        this.declarationData = data.declarationDto;\r\n        console.log('dec service: ', this.declarationData)\r\n      })\r\n    }\r\n    if(this.action === \"view\"){\r\n      this.displayActions = true\r\n      console.log('historyId: ', this.historyId)\r\n      if(this.status === 'Submitted' || this.historyId || this.status === 'Resubmitted') this.isEditVisible = false;\r\n      else this.isEditVisible = true;\r\n      this.isPrintVisible = true;\r\n      this.isViewMode = true;\r\n      this.viewTemplate = viewDeclarationEmpty;\r\n      this.mapSurveyJson();\r\n      this.setSurveyData();\r\n      this.hasValidationErrors = false;\r\n      this.haswarnings = false; // no need to show this stuff in view mode\r\n    }\r\n    else if(this.source === 'Import' && this.status !== \"Imported\"){ // Only go here if its still in the staging table\r\n      this.displayActions = true\r\n      this.setSurveyImport()\r\n    }\r\n    else{\r\n      this.isEditVisible = false;\r\n      this.isPrintVisible = false;\r\n      this.isViewMode = false;\r\n\r\n      if (this.action == 'edit'){\r\n        this.displayActions = false\r\n      }\r\n      Serializer.addProperty(\"question\", {\r\n        name: \"inputWidth\",\r\n        type: \"string\"         \r\n      });\r\n        this.survey = new Model(this.activeTemplate);\r\n        this.survey.applyTheme(themeJson);\r\n        //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\r\n\r\n        this.survey.showNavigationButtons = false;\r\n        const converter = new Converter();\r\n        this.survey.onTextMarkdown.add(function (survey, options) {\r\n          // Convert Markdown to HTML\r\n          let str = converter.makeHtml(options.text);\r\n          // Remove root paragraphs <p></p>\r\n          str = str.substring(3);\r\n          str = str.substring(0, str.length - 4);\r\n          // Set HTML markup to render\r\n          options.html = str;\r\n        });\r\n        this.survey.completedHtml = \"Thank you for Submitting the Declaration\"\r\n\r\n        this.survey.onComplete.add((sender)=>{// calling complete function when completed\r\n          this.surveyresults =  JSON.parse(JSON.stringify(sender.data));\r\n          sender.isCompleted = false\r\n          this.surveyComplete(sender);\r\n        });\r\n\r\n        this.survey.onValueChanged.add((sender,options)=>{\r\n          this.valueChanged(sender,options);\r\n        });\r\n\r\n        this.survey.onCurrentPageChanging.add((sender, options)=>{\r\n          if(options?.oldCurrentPage?.propertyHash?.name !== 'all' && options?.newCurrentPage?.propertyHash?.name !== 'all' ){ // all is the name of the preview page before submission\r\n            if(options.isGoingForward){\r\n              if(!this.isBackendValidationComplete || (this.firstClick && this.haswarnings)){\r\n                options.allow = false;\r\n              }\r\n              if(!this.isBackendValidationComplete || this.hasValidationErrors){\r\n                  this.surveyresults = JSON.parse(JSON.stringify(this.survey.data));\r\n                  this.setErrorsOnPageChange(sender,options);\r\n              }\r\n            }\r\n          }\r\n\r\n        });\r\n\r\n        this.survey.onCurrentPageChanged.add((sender, options)=>{\r\n          this.isBackendValidationComplete = false;\r\n          this.firstClick = true;\r\n          this.hasValidationErrors = false;\r\n          this.haswarnings = false;\r\n          this.validationErrors = [];\r\n          this.validationWarnings = [];\r\n          this.isPrintVisible = false; // hide the print when not in the preview at end of survey. This event doesn't fire when going to preview only going away from it\r\n\r\n          this.currentPageIndex = options?.newCurrentPage?.propertyHash?.num - 1;\r\n          if(options?.newCurrentPage?.propertyHash?.name === 'financialPeriod') this.currentPageIndex = 0;\r\n          if(options?.newCurrentPage?.propertyHash?.name === 'entityDetails') this.currentPageIndex = 1;\r\n          if(options?.newCurrentPage?.propertyHash?.name === 'relevantActivity') this.currentPageIndex = 2;\r\n          if(options?.newCurrentPage?.propertyHash?.name === 'taxResidency') this.currentPageIndex = 3;\r\n          if(options?.newCurrentPage?.propertyHash?.name === 'activityDetail') this.currentPageIndex = 4;\r\n          if(options?.newCurrentPage?.propertyHash?.name === 'Supporting_Details') this.currentPageIndex = 5;\r\n          this.scrollToTop();\r\n        });\r\n\r\n        this.survey.onShowingPreview.add((sender,options)=>{\r\n          this.isPrintVisible = true;\r\n        });\r\n\r\n         this.survey.onUploadFiles.add((_, options) => {\r\n          if(this.declarationId == null || this.declarationId == '00000000-0000-0000-0000-000000000000')\r\n          {\r\n            //Need to save as draft first, to save the declarationId\r\n            this.saveAsDraft(false).then(()=> {\r\n              this.uploadDeclarationFile(options);\r\n            }).catch((error=>{\r\n              console.log(error);\r\n              options.callback(\"error\");\r\n            }));\r\n\r\n          } else {\r\n            this.uploadDeclarationFile(options);\r\n          }\r\n        });\r\n\r\n        // this.survey.onDownloadFile.add((_, options) => {\r\n        //   this.onDownloadFile(options);\r\n        // });\r\n\r\n        this.survey.onClearFiles.add((_,options)=> {\r\n          this.onClearFiles(options);\r\n        })\r\n\r\n        this.setSurveyData();\r\n    }\r\n  }\r\n\r\n  setSurveyImport():void{\r\n    this.isEditVisible = false;\r\n    this.isPrintVisible = false;\r\n    this.isViewMode = false;\r\n\r\n    Serializer.addProperty(\"question\", {\r\n      name: \"inputWidth\",\r\n      type: \"string\"         \r\n    });\r\n      this.survey = new Model(this.activeTemplate);\r\n      this.survey.applyTheme(themeJson);\r\n      //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\r\n      this.survey.showNavigationButtons = false;\r\n      const converter = new Converter();\r\n      \r\n      this.survey.onTextMarkdown.add(function (survey, options) {\r\n        // Convert Markdown to HTML\r\n        let str = converter.makeHtml(options.text);\r\n        // Remove root paragraphs <p></p>\r\n        str = str.substring(3);\r\n        str = str.substring(0, str.length - 4);\r\n        // Set HTML markup to render\r\n        options.html = str;\r\n      });\r\n      this.survey.completedHtml = \"Thank you for Submitting the Declaration\"\r\n\r\n      this.survey.onComplete.add((sender)=>{// calling complete function when completed\r\n        this.surveyresults =  JSON.parse(JSON.stringify(sender.data));\r\n        sender.isCompleted = false\r\n        this.surveyComplete(sender);\r\n      });\r\n\r\n      this.survey.onValueChanged.add((sender,options)=>{\r\n        this.valueChanged(sender,options);\r\n      });\r\n\r\n      this.survey.onCurrentPageChanging.add((sender, options)=>{\r\n        if(options?.oldCurrentPage?.propertyHash?.name !== 'all' && options?.newCurrentPage?.propertyHash?.name !== 'all' ){ // all is the name of the preview page before submission\r\n          if(options.isGoingForward){\r\n            if(!this.isBackendValidationComplete || (this.firstClick && this.haswarnings)){\r\n              options.allow = false;\r\n            }\r\n            if(!this.isBackendValidationComplete || this.hasValidationErrors){\r\n                this.surveyresults = JSON.parse(JSON.stringify(this.survey.data));\r\n                this.setErrorsOnPageChangeImport(sender,options);\r\n            }\r\n          }\r\n        }\r\n\r\n      });\r\n\r\n      this.survey.onCurrentPageChanged.add((sender, options)=>{\r\n        this.isBackendValidationComplete = false;\r\n        this.firstClick = true;\r\n        this.hasValidationErrors = false;\r\n        this.haswarnings = false;\r\n        this.validationErrors = [];\r\n        this.validationWarnings = [];\r\n        this.isPrintVisible = false; // hide the print when not in the preview at end of survey. This event doesn't fire when going to preview only going away from it\r\n\r\n        this.currentPageIndex = options?.newCurrentPage?.propertyHash?.num - 1;\r\n        if(options?.newCurrentPage?.propertyHash?.name === 'financialPeriod') this.currentPageIndex = 0;\r\n        if(options?.newCurrentPage?.propertyHash?.name === 'entityDetails') this.currentPageIndex = 1;\r\n        if(options?.newCurrentPage?.propertyHash?.name === 'relevantActivity') this.currentPageIndex = 2;\r\n        if(options?.newCurrentPage?.propertyHash?.name === 'taxResidency') this.currentPageIndex = 3;\r\n        if(options?.newCurrentPage?.propertyHash?.name === 'activityDetail') this.currentPageIndex = 4;\r\n        if(options?.newCurrentPage?.propertyHash?.name === 'Supporting_Details') this.currentPageIndex = 5;\r\n      });\r\n\r\n      this.survey.onShowingPreview.add((sender,options)=>{\r\n        this.isPrintVisible = true;\r\n      });\r\n        this.survey.onUploadFiles.add((_, options) => {\r\n          this.uploadDeclarationFile(options);\r\n        });\r\n\r\n        // this.survey.onDownloadFile.add((_, options) => {\r\n        //   this.onDownloadFile(options);\r\n        // });\r\n\r\n        this.survey.onClearFiles.add((_,options)=> {\r\n          this.onClearFiles(options);\r\n        })\r\n\r\n        this.setImportSurveyData();\r\n      // });\r\n  }\r\n\r\n  isValidFileType(fileType: string):boolean{\r\n    switch(fileType.toLowerCase())\r\n    {\r\n      case \"png\": {\r\n        return true;\r\n      }\r\n      case \"bmp\": {\r\n        return true;\r\n      }\r\n      case \"jpg\": {\r\n        return true;\r\n      }\r\n      case \"jpeg\": {\r\n        return true;\r\n      }\r\n      case \"pdf\": {\r\n        return true;\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n\r\n  uploadDeclarationFile(options: any)\r\n  {\r\n    try{\r\n      if(options != null && options.files)\r\n      {\r\n        if(this.surveyModel){\r\n        }\r\n          var count = 1;\r\n          const fileUploadLocation = options?.name;\r\n          const spinnerName = fileUploadLocation + \"Spinner\";\r\n          this.surveyModel.setVariable(spinnerName, true);\r\n          for(const f of options.files) {\r\n            var docType = options.question.name as DeclartionDocumentType;\r\n            if(docType)\r\n            {\r\n              const fileType = f?.name.split(\".\").pop();\r\n              if(!this.isValidFileType(fileType))\r\n              {\r\n                try{\r\n                  this.sweetAlert.fireDialog({status: \"fail-type\", action: 'upload', source:\"es-declaration-file\", type:\"toaster\" });\r\n                  this.surveyModel.setVariable(spinnerName, false);\r\n                  options.callback(\"error\");\r\n                  return;\r\n                } catch(e)\r\n                {\r\n                  options.callback(\"error\");\r\n                  return;\r\n                }\r\n              }\r\n\r\n              const currentFiles = this.survey.getValue(options?.name);\r\n              for (const currentFile in currentFiles){\r\n                for (const fileInfo in currentFiles[currentFile]){\r\n                  if(fileInfo === \"name\" && currentFiles[currentFile][fileInfo] === f.name ){\r\n                    this.sweetAlert.fireDialog({status: \"fail-name\", action: 'upload', source:\"es-declaration-file\", type:\"toaster\" });\r\n                    this.surveyModel.setVariable(spinnerName, false);\r\n                    options.callback(\"error\");\r\n                    return;\r\n                  }\r\n                }\r\n              }\r\n\r\n              const reader = new FileReader();\r\n              reader.readAsDataURL(f);\r\n              reader.onload = () => {\r\n                var data = reader.result as string;\r\n\r\n                var upload = {\r\n                  fileName: f.name,\r\n                  fileContents: data,\r\n                  uploadedDeclarationId: this.declarationId ? this.declarationId : this.importdeclarationid,\r\n                  docType: docType,\r\n                  isImport: this.importdeclarationid ? true : false\r\n                } as UploadDeclarationDocumentDto;\r\n\r\n                this.declarationService.uploadDeclarationDocument(upload)\r\n                .subscribe(result=> {\r\n                  console.log('file: ' + f.name + ' was uploaded successfully');\r\n                  if(count == options.files.length)\r\n                  {\r\n                    options.callback(\"success\", options.files.map(f => {\r\n                      const fileUploadLocation = options?.name;\r\n                      const spinnerName = fileUploadLocation + \"Spinner\";\r\n                      this.surveyModel.setVariable(spinnerName, false);\r\n                      return {\r\n                        file: f\r\n                      }\r\n                    }));\r\n                    this.sweetAlert.fireDialog({status: \"success\", action: 'upload', source:\"es-declaration-file\", type:\"toaster\" });\r\n                  }\r\n                  count++;\r\n                });\r\n              };\r\n              reader.onerror = (err) => {\r\n                console.log(err);\r\n              }\r\n            }\r\n          }\r\n      }\r\n    } catch(e)\r\n    {\r\n      console.log(e);\r\n      const fileUploadLocation = options?.name;\r\n      const spinnerName = fileUploadLocation + \"Spinner\";\r\n\r\n      this.surveyModel.setVariable(spinnerName, false);\r\n      options.callback(\"error\");\r\n    }\r\n  }\r\n\r\n  onDownloadFile(options:any) {\r\n    if(options != null && options.fileValue)\r\n    {\r\n      var count = 1;\r\n      var fileList = [];\r\n      // for(const f of options.fileValue) {\r\n        var f = options.fileValue;\r\n        var docType = options.question.name as DeclartionDocumentType;\r\n        var header = this.GetHeader(options.fileValue);\r\n        let isImport = false;\r\n        let idToUse = \"\";\r\n        if(this.importdeclarationid && !this.declarationId){\r\n          idToUse = this.importdeclarationid;\r\n          isImport = true;\r\n        }else{\r\n          idToUse = this.declarationId;\r\n          isImport = false;\r\n        }\r\n        if(docType){\r\n          const fileUploadLocation = options?.name;\r\n          const spinnerName = fileUploadLocation + \"Spinner\";\r\n          if(this.surveyModel){\r\n            this.surveyModel.setVariable(spinnerName, true);\r\n          }\r\n          if (this.entityType == 'CA'){\r\n            this.declarationService.downloadCADeclarationDocument(idToUse,f.name, docType,isImport).pipe(\r\n              finalize(()=>{\r\n                if(this.surveyModel){\r\n                  this.surveyModel.setVariable(spinnerName, false);\r\n                }\r\n              })\r\n            )\r\n            .subscribe((result)=> {\r\n              var t = result;\r\n              options.callback(\"success\", header + result);\r\n            });\r\n          }else{\r\n            this.declarationService.downloadDeclarationDocument(idToUse, f.name, docType, isImport).pipe(\r\n              finalize(()=>{\r\n                if(this.surveyModel){\r\n                  this.surveyModel.setVariable(spinnerName, false);\r\n                }\r\n              })\r\n            )\r\n            .subscribe((result)=> {\r\n              var t = result;\r\n              options.callback(\"success\", header + result);\r\n            });\r\n          }\r\n\r\n        }\r\n      // }\r\n    }\r\n  }\r\n  GetHeader(fileValue: any) {\r\n    var fileName = fileValue.name;\r\n    var split = fileName.split('.');\r\n    var extension = split[split.length-1];\r\n    switch(extension.toLowerCase())\r\n    {\r\n      case \"png\": {\r\n        return \"data:image/png;base64,\";\r\n      }\r\n      case \"bmp\": {\r\n        return \"data:image/bmp;base64,\";\r\n      }\r\n      case \"jpg\": {\r\n        return \"data:image/jpeg;base64,\";\r\n      }\r\n      case \"jpeg\": {\r\n        return \"data:image/jpeg;base64,\";\r\n      }\r\n      case \"pdf\": {\r\n        return \"data:application/pdf;base64,\";\r\n      }\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n\r\n  valueChanged(sender,options):void {\r\n    if (options.name === \"financialPeriodChange\" && options.value === false){\r\n      this.survey.setValue('financialPeriodStartDate', this.validStartDate);\r\n      this.survey.setValue('financialPeriodEndDate',this.validEndDate);\r\n    }\r\n\r\n    if (options.name === \"financialPeriodChange\" && options.value === true){\r\n      this.survey.setValue('financialPeriodStartDate', '');\r\n      this.survey.setValue('financialPeriodEndDate','');\r\n    }\r\n    if (options.name === \"entityDetailsBusinessSameAsRegisteredAddress\" && options.value === true){\r\n\r\n\r\n      if(this.entityId){\r\n        this.corporateEntityService.get(this.entityId, true).subscribe(result=>{\r\n          this.address = [];\r\n          this.countryService.getList({sorting:\"name desc\", maxResultCount: 1000}).subscribe(response=>{\r\n            response.items.forEach(country => {\r\n              if(country.name?.toLowerCase() === result.registeredOfficeAddress.country?.toLowerCase()){\r\n                result.registeredOfficeAddress.country = country.id;\r\n                this.address.push(result.registeredOfficeAddress);\r\n                this.survey.setValue(\"entityDetailsEnterDifferntBusinessAddress\",this.address);\r\n              }\r\n            });\r\n          });\r\n          this.survey.setValue(\"entityDetailsEnterDifferntBusinessAddress\",this.address);\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  setErrorsOnPageChange (sender, options): void{\r\n    this.validationErrors = [];\r\n    const oldPage = options?.oldCurrentPage?.propertyHash?.name;\r\n    const newPage = options?.newCurrentPage?.propertyHash?.name;\r\n    this.hasValidationErrors = false;\r\n    // Setting up dto\r\n    let declaratioDto: DeclarationDto = {\r\n      informationRequestedArray: [],\r\n        surveyData:  JSON.parse(JSON.stringify(this.surveyresults)) as SurveyData,\r\n        actionEnforcedFileNames:[],\r\n        declarationTemplateId: this.templateId ?? null\r\n      }\r\n    this.convertAllDatesToUTC(declaratioDto);\r\n    if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\r\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n    }\r\n    if (this.declarationId) declaratioDto.id = this.declarationId;\r\n    declaratioDto.surveyData.entityId = this.entityId;\r\n\r\n    if(options.isGoingForward){\r\n      this.declarationService.validatePageByPageNameAndInput(oldPage, declaratioDto).subscribe(result =>{\r\n        const errors = result.validationResult.errors.filter(item => item.severity === 0);\r\n        const warnings = result.validationResult.errors.filter(item => item.severity !== 0);\r\n        this.setValidations(errors);\r\n        this.setWarnings(warnings);\r\n        if(this.validationErrors.length === 0 ) {\r\n          this.isBackendValidationComplete = true;\r\n          this.hasValidationErrors = false;\r\n        }\r\n        if(!this.hasValidationErrors && this.isBackendValidationComplete){\r\n          if(!this.haswarnings){\r\n            //next page\r\n            this.survey.nextPage();\r\n          }\r\n          else{\r\n            //we have warnings but no errors\r\n            //is first click\r\n            if(this.firstClick){\r\n              this.firstClick = false;\r\n            }\r\n            else{\r\n              // go next\r\n              this.survey.nextPage()\r\n            }\r\n          }\r\n        }\r\n        if(this.hasValidationErrors || this.haswarnings){\r\n          this.scrollToTop();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  clearIncorrectDataImport(data: DeclarationGetResult){\r\n    const endDateYear = new Date(data.declarationDto.surveyData['financialPeriodEndDate']).getFullYear();\r\n    let skipToEnd = false;\r\n    let skipFrom = \"\"; // if skip from = financialPeriod keep this pages data clear everything until the last page\r\n\r\n    //financial Period\r\n      const subjectToReclassification = data.declarationDto.surveyData['subjectToReclassification'];\r\n      const esFiledOnOTAS = data.declarationDto.surveyData['esFiledOnOTAS'];\r\n      const otasReceipt = data.declarationDto.surveyData['otasReceipt'];\r\n      const otasReceiptUpload = data.declarationDto.surveyData['otasReceiptUpload'];\r\n\r\n      if(!(endDateYear === 2022 || endDateYear === 2023)){\r\n        data.declarationDto.surveyData['subjectToReclassification'] = null;\r\n        data.declarationDto.surveyData['esFiledOnOTAS'] = null;\r\n        data.declarationDto.surveyData['otasReceipt'] = null;\r\n        data.declarationDto.surveyData['otasReceiptUpload'] = null;\r\n      }\r\n      if((endDateYear === 2022 || endDateYear === 2023) && esFiledOnOTAS === true){\r\n        skipToEnd = true;\r\n        skipFrom = 'financialPeriod';\r\n        this.skipAndClearSectionsImport(data, ['entityDetails', 'relevantActivity', 'taxResidency', 'activityDetail']);\r\n      }\r\n      if(esFiledOnOTAS === false){\r\n        data.declarationDto.surveyData['otasReceipt'] = null;\r\n        data.declarationDto.surveyData['otasReceiptUpload'] = null;\r\n      }\r\n      if(otasReceipt === false){\r\n        data.declarationDto.surveyData['otasReceiptUpload'] = null;\r\n      }\r\n\r\n      // Relevant activities\r\n      const relevantActRelevantActivities = data.declarationDto.surveyData['relevantActRelevantActivities'];\r\n      if(relevantActRelevantActivities){\r\n        if(relevantActRelevantActivities.includes('none') || relevantActRelevantActivities.length === 0){\r\n          skipToEnd = true;\r\n          skipFrom = 'relevantActivity';\r\n          this.skipAndClearSectionsImport(data, ['taxResidency', 'activityDetail']);\r\n        }\r\n      }\r\n\r\n      // tax residency\r\n      const taxResidency100PercentBahamian = data.declarationDto.surveyData['taxResidency100PercentBahamian'];\r\n      const taxResidencyIsInvestmentFund = data.declarationDto.surveyData['taxResidencyIsInvestmentFund'];\r\n      const taxResidencyOutsideBahamas = data.declarationDto.surveyData['taxResidencyOutsideBahamas'];\r\n      const hasImmediateParent = data.declarationDto.surveyData['hasImmediateParent'];\r\n      const taxResidencyHasParent = data.declarationDto.surveyData['taxResidencyHasParent'];\r\n\r\n      if(taxResidency100PercentBahamian){\r\n        skipToEnd = true;\r\n        skipFrom = 'taxResidency';\r\n        data.declarationDto.surveyData['taxResidencyIsInvestmentFund'] = null;\r\n        data.declarationDto.surveyData['taxResidencyOutsideBahamas'] = null;\r\n        data.declarationDto.surveyData['hasImmediateParent'] = null;\r\n        data.declarationDto.surveyData['taxResidencyHasParent'] = null;\r\n        data.declarationDto.surveyData['taxResidencyImmediateParentEntity'] = null;\r\n        data.declarationDto.surveyData['taxResidencyUltimateParentEntityInfo'] = null;\r\n        data.declarationDto.surveyData['taxResidencyJurisdictionEntityIsTaxResident'] = null;\r\n        data.declarationDto.surveyData['taxResidencyTaxpayerIDNumber'] = null;\r\n        data.declarationDto.surveyData['taxResidencyEvidenceOfTaxResidency'] = null;\r\n      }\r\n      if(taxResidencyIsInvestmentFund){\r\n        skipToEnd = true;\r\n        skipFrom = 'taxResidency';\r\n        data.declarationDto.surveyData['taxResidencyOutsideBahamas'] = null;\r\n        data.declarationDto.surveyData['hasImmediateParent'] = null;\r\n        data.declarationDto.surveyData['taxResidencyHasParent'] = null;\r\n        data.declarationDto.surveyData['taxResidencyImmediateParentEntity'] = null;\r\n        data.declarationDto.surveyData['taxResidencyUltimateParentEntityInfo'] = null;\r\n        data.declarationDto.surveyData['taxResidencyJurisdictionEntityIsTaxResident'] = null;\r\n        data.declarationDto.surveyData['taxResidencyTaxpayerIDNumber'] = null;\r\n        data.declarationDto.surveyData['taxResidencyEvidenceOfTaxResidency'] = null;\r\n      }\r\n      if(taxResidencyIsInvestmentFund === false){\r\n       if(hasImmediateParent === false){\r\n        data.declarationDto.surveyData['taxResidencyImmediateParentEntity'] = null;\r\n       }\r\n       if(taxResidencyHasParent === false){\r\n        data.declarationDto.surveyData['taxResidencyUltimateParentEntityInfo'] = null;\r\n       }\r\n      }\r\n      if(taxResidencyOutsideBahamas === false){\r\n        data.declarationDto.surveyData['taxResidencyJurisdictionEntityIsTaxResident'] = null;\r\n        data.declarationDto.surveyData['taxResidencyTaxpayerIDNumber'] = null;\r\n        data.declarationDto.surveyData['taxResidencyEvidenceOfTaxResidency'] = null;\r\n      }\r\n      if(taxResidencyOutsideBahamas){\r\n        skipToEnd = true;\r\n        skipFrom = 'taxResidency';\r\n      }\r\n\r\n      if (skipToEnd === true && skipFrom === 'taxResidency'){\r\n        this.skipAndClearSectionsImport(data, ['activityDetail']);\r\n      }\r\n\r\n  }\r\n\r\n  skipAndClearSectionsImport(data: DeclarationGetResult, sectionsToClear: string[]){\r\n    for (const clearKey in data.declarationDto.surveyData){\r\n\r\n      //entity Details\r\n      if(sectionsToClear.includes('entityDetails')){\r\n        if(clearKey === 'entityDetailsTIN') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'entityDetailsAnnualIncome' && data.declarationDto.surveyData[clearKey] && data.declarationDto.surveyData[clearKey][0]?.currencyValue != null) data.declarationDto.surveyData[clearKey][0].currencyValue = null;\r\n        if(clearKey === 'entityDetailsBusinessSameAsRegisteredAddress') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'entityDetailsEnterDifferntBusinessAddress') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'entityDetailsNameMNE') data.declarationDto.surveyData[clearKey] = null;\r\n      }\r\n\r\n      //relevantActivity\r\n      if(sectionsToClear.includes('relevantActivity')){\r\n        if(clearKey === 'relevantActRelevantActivities') data.declarationDto.surveyData[clearKey] = null;\r\n      }\r\n\r\n      //tax residency\r\n      if(sectionsToClear.includes('taxResidency')){\r\n        if(clearKey === 'taxResidency100PercentBahamian') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyIsInvestmentFund') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyOutsideBahamas') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyJurisdictionEntityIsTaxResident') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyTaxpayerIDNumber') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyEvidenceOfTaxResidency') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyHasParent') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyUltimateParentEntityInfo') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'hasImmediateParent') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'taxResidencyImmediateParentEntity') data.declarationDto.surveyData[clearKey] = null;\r\n      }\r\n\r\n      //activity detail\r\n      if(sectionsToClear.includes('activityDetail')){\r\n        if(clearKey === 'intellectualPropertyBusiness') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'holdingBusinessQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'bankingQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'insuranceQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'fundManagmentQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'financeQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'headquartersQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'shippingQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n        if(clearKey === 'distributionQuestions') data.declarationDto.surveyData[clearKey] = null;\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n  setErrorsOnPageChangeImport(sender, options):void{\r\n    this.validationErrors = [];\r\n    const oldPage = options?.oldCurrentPage?.propertyHash?.name;\r\n    const newPage = options?.newCurrentPage?.propertyHash?.name;\r\n    this.hasValidationErrors = false;\r\n    // Setting up dto\r\n    let declaratioDto: DeclarationDto = {\r\n      informationRequestedArray: [],\r\n        surveyData:  JSON.parse(JSON.stringify(this.surveyresults)) as SurveyData,\r\n        actionEnforcedFileNames:[],\r\n        declarationTemplateId: this.templateId ?? null\r\n      }\r\n    this.convertAllDatesToUTC(declaratioDto);\r\n    if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\r\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n    }\r\n    if (this.importdeclarationid) declaratioDto.id = this.importdeclarationid;\r\n    declaratioDto.surveyData.entityId = this.entityId;\r\n\r\n    if(options.isGoingForward){\r\n      this.declarationImportService.validatePageByPageNameAndInput(oldPage, declaratioDto).subscribe(result =>{\r\n        const errors = result.validationResult.errors.filter(item => item.severity === 0);\r\n        const warnings = result.validationResult.errors.filter(item => item.severity !== 0);\r\n        this.setValidations(errors);\r\n        this.setWarnings(warnings);\r\n        if(this.validationErrors.length === 0 ) {\r\n          this.isBackendValidationComplete = true;\r\n          this.hasValidationErrors = false;\r\n        }\r\n        if(!this.hasValidationErrors && this.isBackendValidationComplete){\r\n          if(!this.haswarnings){\r\n            //next page\r\n            this.survey.nextPage();\r\n          }\r\n          else{\r\n            //we have warnings but no errors\r\n            //is first click\r\n            if(this.firstClick){\r\n              this.firstClick = false;\r\n            }\r\n            else{\r\n              // go next\r\n              this.survey.nextPage()\r\n            }\r\n          }\r\n        }\r\n        if(this.hasValidationErrors || this.haswarnings){\r\n          this.scrollToTop();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  mapSurveyJson(){\r\n    // Taking everything from each page of the active survey and putting it into the correct panel on the view survey\r\n    // If page names are changed in active survey must be reflected here\r\n    this.viewTemplate.pages[0].elements.forEach(element => {\r\n      this.activeTemplate.pages.forEach(page=>{\r\n        if(element.name === page.name) element.elements = page.elements;\r\n      });\r\n    });\r\n\r\n    Serializer.addProperty(\"question\", {\r\n      name: \"inputWidth\",\r\n      type: \"string\"         \r\n    });\r\n    this.survey = new Model(this.viewTemplate);\r\n    this.survey.applyTheme(themeJson);\r\n    //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\r\n    const converter = new Converter();\r\n        this.survey.onTextMarkdown.add(function (survey, options) {\r\n          // Convert Markdown to HTML\r\n          let str = converter.makeHtml(options.text);\r\n          // Remove root paragraphs <p></p>\r\n          str = str.substring(3);\r\n          str = str.substring(0, str.length - 4);\r\n          // Set HTML markup to render\r\n          options.html = str;\r\n    });\r\n\r\n    // this.survey.onDownloadFile.add((_, options) => {\r\n    //   this.onDownloadFile(options);\r\n    // });\r\n    this.survey.onClearFiles.add((_,options)=> {\r\n      this.onClearFiles(options);\r\n    })\r\n  }\r\n  onClearFiles(options: any) {\r\n    //call declaration service delete declaration file\r\n    var declarationId = this.declarationId;\r\n    var fileName = options.fileName;\r\n    var docType  = options.name as DeclartionDocumentType;\r\n    const fileUploadLocation = options?.name;\r\n    const spinnerName = fileUploadLocation + \"Spinner\";\r\n    let idToUse = \"\";\r\n    let isImport = false;\r\n    if(this.importdeclarationid && !this.declarationId){\r\n      idToUse = this.importdeclarationid;\r\n      isImport = true;\r\n    }else{\r\n      idToUse = this.declarationId;\r\n      isImport = false;\r\n    }\r\n    if(options.fileName != null){ // only one file at a time deleted\r\n      if(docType && idToUse && fileName)\r\n      {\r\n        if(this.surveyModel){\r\n          this.surveyModel.setVariable(spinnerName, true);\r\n        }\r\n        this.declarationService.deleteDeclarationAttachmentFile(idToUse, fileName, docType, isImport).pipe(\r\n          finalize(()=>{\r\n            if(this.surveyModel){\r\n              this.surveyModel.setVariable(spinnerName, false);\r\n            }\r\n          })\r\n        )\r\n        .subscribe((result => {\r\n          console.log('File delete');\r\n          options.callback(\"success\");\r\n          if(this.declarationId){\r\n            this.saveAsDraft().catch((error=>{\r\n              console.log(error);\r\n            }));\r\n          }\r\n        }))\r\n      }\r\n    }\r\n    else{ // when the clear all button is used\r\n      let listOfFiles = [];\r\n      if(options?.value){\r\n        for (const files in options.value){\r\n          for (const fileInfo in options.value[files]){\r\n            if(fileInfo === 'name'){\r\n              fileName = options.value[files][fileInfo];\r\n              listOfFiles.push(fileName);\r\n            }\r\n          }\r\n        }\r\n\r\n        if (listOfFiles.length > 0) {\r\n          this.declarationService.deleteMultipleDeclarationAttachmentsFile(idToUse, listOfFiles, docType, isImport).subscribe(result=>{\r\n            options.callback(\"success\");\r\n            if(this.declarationId){\r\n              this.saveAsDraft().catch((error=>{\r\n                console.log(error);\r\n              }));\r\n            } \r\n          });\r\n        }\r\n      }\r\n    }\r\n  }\r\n  formatDate(dateStr: string): string {\r\n    return DateHelper.formatEstUtcDate(dateStr,'yyyy-MM-dd');\r\n  }\r\n  setImportSurveyData():void{\r\n    let activities = [];\r\n    this.declarationImportService.getImport(this.importdeclarationid).subscribe(result=>{\r\n      this.survey.data = result.declarationDto.surveyData\r\n      for(const key in result.declarationDto.surveyData){\r\n        if(result.declarationDto.surveyData[key]){ // dates need to be set up by trimming the extra stuff off\r\n          if(key === \"financialPeriodStartDate\"){\r\n            this.validStartDate = this.formatDate(result.declarationDto.surveyData[key]);\r\n            result.declarationDto.surveyData[key] = this.formatDate(result.declarationDto.surveyData[key]);\r\n          }\r\n          if(key === \"financialPeriodEndDate\") {\r\n            this.validEndDate = this.formatDate(result.declarationDto.surveyData[key]);\r\n            result.declarationDto.surveyData[key] = this.formatDate(result.declarationDto.surveyData[key]);\r\n          }\r\n\r\n          if(key === \"relevantActRelevantActivities\"){\r\n            activities  = result.declarationDto.surveyData[key]\r\n          }\r\n\r\n          if(this.compositeKeys.includes(key)){\r\n            if(key === \"holdingBusinessQuestions\") {\r\n              result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\r\n              result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\r\n            }\r\n            if(key === \"intellectualPropertyBusiness\"){\r\n              result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\r\n              result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\r\n            }\r\n            if(key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\"){ // Other activities\r\n              result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\r\n              result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(result.declarationDto.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      this.clearIncorrectDataImport(result);\r\n      this.survey.data = JSON.parse(JSON.stringify(result.declarationDto.surveyData));\r\n      //console.log(this.survey.data)\r\n      this.mapData(activities);\r\n      this.surveyModel = this.survey;\r\n      this.isLoading = false;\r\n\r\n\r\n    });\r\n  }\r\n\r\n  setSurveyData(): void{\r\n    let activities = [];\r\n\r\n    if(this.declarationId || this.historyId){// if declaration ID then go get that and set survey\r\n      var observable\r\n      var contents = ''\r\n      if (this.historyId){\r\n        observable = this.historyService.get(this.historyId)\r\n        contents = 'declarationContents'\r\n      }else{\r\n        observable = this.declarationService.get(this.declarationId)\r\n        contents = 'declarationDto'\r\n      }\r\n      observable.subscribe(result=>{\r\n        this.survey.data = result[contents].surveyData\r\n        for(const key in result[contents].surveyData){\r\n          if(result[contents].surveyData[key]){ // dates need to be set up by trimming the extra stuff off\r\n            if(key === \"financialPeriodStartDate\"){\r\n              this.validStartDate = this.formatDate(result[contents].surveyData[key]);\r\n              result[contents].surveyData[key] = this.formatDate(result[contents].surveyData[key]);\r\n            }\r\n            if(key === \"financialPeriodEndDate\") {\r\n              this.validEndDate = this.formatDate(result[contents].surveyData[key]);\r\n              console.log(this.validEndDate)\r\n              result[contents].surveyData[key] = this.formatDate(result[contents].surveyData[key]);\r\n            }\r\n\r\n            if(key === \"relevantActRelevantActivities\"){\r\n              activities  = result[contents].surveyData[key]\r\n            }\r\n\r\n            if(this.compositeKeys.includes(key)){\r\n              if(key === \"holdingBusinessQuestions\") {\r\n                result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\r\n                result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(result[contents].surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\r\n              }\r\n              if(key === \"intellectualPropertyBusiness\"){\r\n                result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\r\n                result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(result[contents].surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\r\n              }\r\n              if(key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\"){ // Other activities\r\n                result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\r\n                result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(result[contents].surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.survey.data = JSON.parse(JSON.stringify(result[contents].surveyData));\r\n        this.mapData(activities);\r\n\r\n        // is in view mode ?\r\n        if (this.action === 'view') {\r\n          this.survey.mode = \"display\";\r\n          this.hasValidationErrors = false;\r\n          this.haswarnings = false; // no need to show this stuff in view mode\r\n        }\r\n        this.survey.setVariable(\"otasReceiptUploadSpinner\", false);\r\n        this.survey.setVariable(\"taxResidencyEvidenceOfTaxResidencySpinner\", false);\r\n        this.survey.setVariable(\"intelPropBusEmployeeResponsibleGenerationIncomeAttachmentSpinner\", false);\r\n        this.survey.setVariable(\"intelPropBusStrategicDecisionsAttachmentSpinner\", false);\r\n        this.survey.setVariable(\"intelPropBusNatureOfTradingActivityAttachmentSpinner\", false);\r\n        this.survey.setVariable(\"highRiskBusinessPlanDetailsAttachmentSpinner\", false);\r\n        this.survey.setVariable(\"highRiskEvidenceDecisionMakingInBahamasAttachmentSpinner\", false);\r\n        this.survey.setVariable(\"highRiskOtherFactsAttachmentSpinner\", false);\r\n        this.survey.setVariable(\"supportingAttachmentSpinner\", false);\r\n\r\n        this.surveyModel = this.survey;\r\n        this.isLoading = false;\r\n      });\r\n    }\r\n    else{// new survey\r\n      this.setNewSurveyData();\r\n    }\r\n  }\r\n\r\n  setNewSurveyData(): void{\r\n    // call the valid dates api, get the dates and set them\r\n    this.declarationService.getLatestValidDatesByEntityId(this.entityId).subscribe(result=>{\r\n      this.survey.setValue('financialPeriodStartDate', this.formatDate(result[0]));\r\n      this.survey.setValue('financialPeriodEndDate',this.formatDate(result[1]));\r\n      this.validEndDate = this.formatDate(result[1]);\r\n      this.validStartDate = this.formatDate(result[0]);\r\n\r\n      this.survey.setVariable(\"otasReceiptUploadSpinner\", false);\r\n      this.survey.setVariable(\"taxResidencyEvidenceOfTaxResidencySpinner\", false);\r\n      this.survey.setVariable(\"intelPropBusEmployeeResponsibleGenerationIncomeAttachmentSpinner\", false);\r\n      this.survey.setVariable(\"intelPropBusStrategicDecisionsAttachmentSpinner\", false);\r\n      this.survey.setVariable(\"intelPropBusNatureOfTradingActivityAttachmentSpinner\", false);\r\n      this.survey.setVariable(\"highRiskBusinessPlanDetailsAttachmentSpinner\", false);\r\n      this.survey.setVariable(\"highRiskEvidenceDecisionMakingInBahamasAttachmentSpinner\", false);\r\n      this.survey.setVariable(\"highRiskOtherFactsAttachmentSpinner\", false);\r\n      this.survey.setVariable(\"supportingAttachmentSpinner\", false);\r\n\r\n      this.surveyModel = this.survey;\r\n      this.isLoading = false;\r\n    });\r\n  }\r\n\r\n  mapData(activities?):void{\r\n    // mapping from id to name\r\n    if(activities){\r\n      if(this.survey.data['relevantActRelevantActivities']){\r\n        let convertedActivities = [];\r\n        this.activityLookup.getList({maxResultCount: 100}).subscribe(result =>{\r\n          result.items.forEach(element => {\r\n            activities.forEach(activity => {\r\n              if(element.id === activity){\r\n                convertedActivities.push(element.name);\r\n              }\r\n            });\r\n          });\r\n          if(convertedActivities.length >0) this.survey.setValue('relevantActRelevantActivities', convertedActivities);\r\n          else this.survey.setValue('relevantActRelevantActivities', activities); // if name was returned by api then we dont have any ids to convert\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  setValidations(errors: ValidationFailure[]):void{\r\n    this.validationErrors = [];\r\n    for (const error in errors){\r\n      for(const errorInfo in errors[error]){\r\n        if(errorInfo === 'errorMessage' && errors[error]['severity'] === 0){ // only add errors not warnings\r\n          this.validationErrors.push(errors[error][errorInfo])\r\n        }\r\n      }\r\n    }\r\n    if (this.validationErrors.length > 0) this.hasValidationErrors = true\r\n    else this.hasValidationErrors = false;\r\n  }\r\n\r\n  setWarnings(warnings: ValidationFailure[]): void{\r\n    this.validationWarnings = [];\r\n    for (const warning in warnings){\r\n      for(const warningInfo in warnings[warning]){\r\n        if(warningInfo === 'errorMessage'){\r\n          this.validationWarnings.push(warnings[warning][warningInfo]);\r\n        }\r\n      }\r\n    }\r\n    if (this.validationWarnings.length > 0) this.haswarnings = true\r\n    else this.haswarnings = false;\r\n  }\r\n\r\n  surveyComplete(sender):void{\r\n    // Setting up dto\r\n    if(this.templateVersion != 1){\r\n      let declaratioDto: DeclarationDto = {\r\n        statusLookupId: this.statusMappings.submitted,\r\n        status: \"Submitted\",\r\n        informationRequestedArray: this.declarationData?.informationRequestedArray??[],\r\n        surveyData: JSON.parse(JSON.stringify(this.surveyresults)) as SurveyData,\r\n        actionEnforcedFileNames:[],\r\n        isReopened: this.declarationData?.isReopened,\r\n        declarationTemplateId: this.templateId ?? null\r\n      }\r\n      this.convertAllDatesToUTC(declaratioDto);\r\n      if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n        const convertedResult = this.convertActivitiesNameToId(declaratioDto);\r\n        declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n      }\r\n      if (this.declarationId) declaratioDto.id = this.declarationId;\r\n      declaratioDto.surveyData.entityId = this.entityId;\r\n      this.sweetAlert.fireDialog({action: \"submit\",title: 'Are you sure you want to submit',\r\n      text: \"Please note that once submitted, the form will be locked and received by the Ministry of Finance and will be subject to further review and/or onsite inspection and audit\",\r\n      type:\"confirm\"},\r\n      (confirm) =>{ // call back function checking if yes or no was selected in confirmaion\r\n        if(confirm) {\r\n          this.declarationService.submit(declaratioDto).subscribe(result=>{\r\n            this.setValidations(result.validationResult.errors);\r\n            if(this.validationErrors.length === 0){ // if no validation error display success and allow it to go to complete screen\r\n              sender.isCompleted = false; // if true it will display the thank you for completing the survey for a split second before going to the view mode\r\n              if(!this.declarationId) this.declarationId = result.declarationId;\r\n              this.router.navigate(['/es-declaration'], { queryParams: {declarationid: this.declarationId, entityid: this.entityId, action: 'view', status:\"Submitted\", source: this.source}});\r\n              this.action = 'view';\r\n              this.status = 'Submitted';\r\n              this.setSurvey();\r\n              this.hasValidationErrors = false;\r\n              this.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"es-declaration-submit\", type:\"toaster\"});\r\n            }\r\n            else{ // validation errors do not go to complete screen\r\n              sender.isCompleted = false;\r\n              this.hasValidationErrors = true;\r\n              this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration-submit\", type:\"toaster\"});\r\n            }\r\n          });\r\n        }\r\n        });\r\n    }\r\n    else{\r\n      sender.isCompleted = false;\r\n      this.sweetAlert.fireDialog({type:'DeclarationSubmitVersion1Error'});  \r\n    }\r\n    \r\n  }\r\n\r\n  convertLocalToUtcDate(date:string): string{\r\n    if(date){\r\n      //const convertedDate = new Date(date + \"T00:00\"); // set the time stamp in the survey to midnight local time\r\n      //Always treat local time is EST\r\n      const convertedDate = zonedTimeToUtc(date, this.assumedLocalZone);\r\n      return convertedDate.toISOString(); //convert it to utc time\r\n    }\r\n    else return date;\r\n  }\r\n\r\n  convertAllDatesToUTC(declarationDto: DeclarationDto): void{\r\n    if(declarationDto.surveyData.relevantActRelevantActivities){\r\n      const listOfActivities = declarationDto.surveyData.relevantActRelevantActivities;\r\n      listOfActivities.forEach(activity => {\r\n        if(activity ===\"Holding business\" && 'holdingBusinessQuestions' in declarationDto.surveyData && declarationDto.surveyData.holdingBusinessQuestions !== null) {\r\n          declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.holdingBusinessQuestions.holdingBussinessPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Distribution and service centre business\" && 'distributionQuestions' in declarationDto.surveyData && declarationDto.surveyData.distributionQuestions !== null ) {\r\n          declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.distributionQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Intellectual property business\" && 'intellectualPropertyBusiness' in declarationDto.surveyData && declarationDto.surveyData.intellectualPropertyBusiness !== null) {\r\n          declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.intellectualPropertyBusiness.intelPropPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Shipping business\" && 'shippingQuestions' in declarationDto.surveyData && declarationDto.surveyData.shippingQuestions !== null){\r\n          declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.shippingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Headquarters business\" && 'headquartersQuestions' in declarationDto.surveyData && declarationDto.surveyData.headquartersQuestions !== null) {\r\n          declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.headquartersQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Finance and leasing business\" && 'financeQuestions' in declarationDto.surveyData && declarationDto.surveyData.financeQuestions !== null) {\r\n          declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.financeQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Fund management business\" && 'fundManagmentQuestions' in declarationDto.surveyData && declarationDto.surveyData.fundManagmentQuestions !== null){\r\n          declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.fundManagmentQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Insurance business\" && 'insuranceQuestions' in declarationDto.surveyData && declarationDto.surveyData.insuranceQuestions !== null) {\r\n          declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.insuranceQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n        if(activity ===\"Banking business\" && 'bankingQuestions' in declarationDto.surveyData && declarationDto.surveyData.bankingQuestions !== null) {\r\n          declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodEndDate);\r\n          declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.bankingQuestions.otherRelevantActivitiesPartOfFinancialPeriodStartDate);\r\n        }\r\n      });\r\n    }\r\n    declarationDto.surveyData.financialPeriodEndDate = this.convertLocalToUtcDate(declarationDto.surveyData.financialPeriodEndDate);\r\n    declarationDto.surveyData.financialPeriodStartDate = this.convertLocalToUtcDate(declarationDto.surveyData.financialPeriodStartDate);\r\n  }\r\n\r\n  saveAsDraftImport(){\r\n    // Setting up dto\r\n    let declaratioDto: DeclarationDto = {\r\n      statusLookupId: this.statusMappings.Draft,\r\n      status: \"Draft\",\r\n      informationRequestedArray: [],\r\n      surveyData: JSON.parse(JSON.stringify(this.surveyresults)) as SurveyData,\r\n      declarationTemplateId: this.templateId ?? null,\r\n      actionEnforcedFileNames:[]\r\n    }\r\n    this.convertAllDatesToUTC(declaratioDto);\r\n    if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\r\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n    }\r\n    if (this.importdeclarationid) declaratioDto.id = this.importdeclarationid;\r\n    declaratioDto.surveyData.entityId = this.entityId;\r\n    this.declarationImportService.saveDraft(declaratioDto).subscribe(result=>{\r\n      this.setValidations(result.validationResult.errors);\r\n      if(this.validationErrors.length === 0){ // if no validation error display success and allow it to go to complete screen\r\n        this.router.navigate(['/es-import/importdetail'], { queryParams: { id: this.importfileid} });\r\n        this.hasValidationErrors = false;\r\n        this.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"es-declaration-import-save\", type:\"toaster\"});\r\n      }\r\n      else{ // validation errors do not go to complete screen\r\n        this.hasValidationErrors = true;\r\n        this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration-import-save\", type:\"toaster\"});\r\n      }\r\n    });\r\n  }\r\n\r\n async preSaveVersionWarningCheck(showToaster: boolean = true) { \r\n  //only need to call this when using the save buttons \r\n  // uncomment 3 below lines to show upgrade popup before save.\r\n\r\n    let completed = await this.upgradeToLatestTemplatePopup();\r\n\r\n    if(!completed){ // if the user selected yes then BE will save the current data and update template otherwise just do the save normally. \r\n      this.saveAsDraft().catch((error=>{\r\n        console.log(error);\r\n      }));\r\n    }\r\n  }\r\n\r\n  async saveAsDraft(showToaster: boolean = true): Promise<void>{\r\n    var current = this;\r\n    return new Promise((resolve, reject) => {\r\n      current.surveyresults = JSON.parse(JSON.stringify(current.survey.data));\r\n        if(current.source === 'Import'){\r\n          current.saveAsDraftImport();\r\n          resolve();\r\n        }\r\n        else {\r\n            this.validationErrors = [];\r\n            this.hasValidationErrors = false;\r\n          if('financialPeriodEndDate' in current.surveyresults && 'financialPeriodStartDate' in current.surveyresults){\r\n            let saveCheck: DeclarationDto = {\r\n              informationRequestedArray: [],\r\n              surveyData:  JSON.parse(JSON.stringify(current.surveyresults)) as SurveyData,\r\n              declarationTemplateId: current.templateId ?? null,\r\n              actionEnforcedFileNames:[]\r\n            }\r\n            if(saveCheck.surveyData.relevantActRelevantActivities){\r\n              const convertedResult = current.convertActivitiesNameToId(saveCheck);\r\n              saveCheck.surveyData.relevantActRelevantActivities = convertedResult;\r\n            }\r\n            if (current.declarationId) saveCheck.id = current.declarationId;\r\n            saveCheck.surveyData.entityId = current.entityId;\r\n            saveCheck.surveyData.financialPeriodEndDate = current.convertLocalToUtcDate(saveCheck.surveyData.financialPeriodEndDate);\r\n            saveCheck.surveyData.financialPeriodStartDate = current.convertLocalToUtcDate(saveCheck.surveyData.financialPeriodStartDate);\r\n            let canSave = true;\r\n            current.declarationService.validatePageByPageNameAndInput('financialPeriod', saveCheck).subscribe(response =>\r\n            {\r\n              let errors = response.validationResult.errors\r\n              response.validationResult.isValid\r\n              for (const error in errors){\r\n                for(const errorInfo in errors[error]){\r\n                  if(errorInfo === 'propertyName' && (errors[error][errorInfo].includes(\"FinancialPeriodStartDate\")|| errors[error][errorInfo].includes(\"FinancialPeriodEndDate\"))){\r\n                    this.validationErrors.push(errors[error][\"errorMessage\"]);\r\n                    this.hasValidationErrors = true;\r\n                    canSave = false;\r\n                  }\r\n                }\r\n              }\r\n\r\n              if(canSave){\r\n                  let declaratioDto: DeclarationDto = {\r\n                  statusLookupId: current.statusMappings.Draft,\r\n                  status: \"Draft\",\r\n                  informationRequestedArray:this.declarationData?.informationRequestedArray??[],\r\n                  surveyData:  JSON.parse(JSON.stringify(current.surveyresults)) as SurveyData,\r\n                  declarationTemplateId: current.templateId ?? null,\r\n                  actionEnforcedFileNames:[]\r\n                }\r\n                current.convertAllDatesToUTC(declaratioDto);\r\n                if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n                  const convertedResult = current.convertActivitiesNameToId(declaratioDto);\r\n                  declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n                }\r\n                if (current.declarationId) declaratioDto.id = current.declarationId;\r\n                declaratioDto.surveyData.entityId = current.entityId;\r\n                current.declarationService.saveDraft(declaratioDto).subscribe(result=>{\r\n                  if(showToaster) {current.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"es-declaration\", type:\"toaster\"});}\r\n                  if (!current.declarationId){\r\n\r\n                    current.declarationId = result.declarationId;\r\n                    current.router.navigate(['/es-declaration'], { queryParams: {declarationid: current.declarationId, entityid: current.entityId, action: 'edit', status:current.status, source: current.source}});\r\n                  }\r\n                  resolve();\r\n                });\r\n                }\r\n              else {\r\n                current.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration\", type:\"toaster\"});\r\n                reject();\r\n              }\r\n            });\r\n          }\r\n          else{\r\n            current.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration\", type:\"toaster\"});\r\n            reject();\r\n          }\r\n        }\r\n    });\r\n  }\r\n\r\n  convertActivitiesNameToId(declaratioDto): string[]{\r\n    let convertedActivities = [];\r\n    if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n      declaratioDto.surveyData.relevantActRelevantActivities.forEach(element =>{\r\n        if(element ===\"Holding business\") convertedActivities.push(this.activityMappings.holding) ;\r\n        if(element ===\"Distribution and service centre business\") convertedActivities.push(this.activityMappings.distribution) ;\r\n        if(element ===\"Intellectual property business\") convertedActivities.push(this.activityMappings.ip) ;\r\n        if(element ===\"Shipping business\") convertedActivities.push(this.activityMappings.shipping) ;\r\n        if(element ===\"Headquarters business\") convertedActivities.push(this.activityMappings.headquarters) ;\r\n        if(element ===\"Finance and leasing business\") convertedActivities.push(this.activityMappings.finance );\r\n        if(element ===\"Fund management business\")convertedActivities.push( this.activityMappings.funds) ;\r\n        if(element ===\"Insurance business\") convertedActivities.push(this.activityMappings.insurance) ;\r\n        if(element ===\"Banking business\") convertedActivities.push(this.activityMappings.banking) ;\r\n        if(element ===\"none\") convertedActivities.push('none');\r\n      });\r\n      return convertedActivities;\r\n    }\r\n    return convertedActivities\r\n  }\r\n\r\n  async preProcessCigaDropdown():Promise<void>{\r\n    ComponentCollection.Instance.items.forEach(Component => {\r\n      if(Component['json']['name'] ===\"IntellectualPropertyBusiness\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivity'){\r\n              elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.ip;\r\n            }\r\n            if(element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivityComment'){\r\n              elements['visibleIf'] = \"{IntellectualPropertyBusiness.intelPropBusCIGAInBahamasForRelevantActivity} anyof ['\" +this.cigaOtherId.ip + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"bankingOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n             elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.banking;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{bankingQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.banking + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"distributionOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.distribution;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{distributionQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.distribution + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"financeOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.finance;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{financeQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.finance + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"fundManagmentOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n              elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.funds;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{fundManagmentQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.funds + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"headquartersOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.headquarters;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{headquartersQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.headquarters + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"insuranceOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + this.activityMappings.insurance;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{insuranceQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.insurance + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"shippingOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n              elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + this.activityMappings.shipping;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{shippingQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.shipping + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  setUrls(SurveyTemplate:SurveyDto):void{ // only for non composite questions\r\n    for(const key in SurveyTemplate){\r\n      for(const key2 in SurveyTemplate[key]){\r\n        for(const key3 in SurveyTemplate[key][key2]){\r\n          for(const key4 in SurveyTemplate[key][key2][key3]){\r\n            for(const key5 in SurveyTemplate[key][key2][key3][key4]){\r\n              for(const key6 in SurveyTemplate[key][key2][key3][key4][key5]){\r\n                for(const key7 in SurveyTemplate[key][key2][key3][key4][key5][key6]){\r\n                  if(key7 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7] !== null){ // choices by url is on the question level example is relevant activity drop down\r\n                    SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url.replace(\"{0}\", this.tenantName);\r\n                    console.log(key7,SurveyTemplate[key][key2][key3][key4][key5][key6][key7] )\r\n                  }\r\n                  for(const key8 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7]){\r\n                    for(const key9 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8]){\r\n                      if(key9 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9] !== null ){ // choices by url is on a column level example entity details differnt business address country drop down\r\n                        SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url.replace(\"{0}\", this.tenantName);\r\n                        console.log(key9,SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9])\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  intialConfigAndSetup():void{\r\n    const configData = this.configState.getAll();\r\n    if(configData.currentTenant.isAvailable){\r\n      this.tenantName = configData.currentTenant.name;\r\n    }\r\n\r\n    let lookupObservable = this.statusLookup.getList({maxResultCount: 10});\r\n    let activityObservable = this.activityLookup.getList({maxResultCount: 100});\r\n    let cigaObservable = this.cigaLookup.getList({maxResultCount:100});\r\n    let templateObservable = this.service.getList(this.templateListDto);\r\n    let observableList : Observable<any>[] = [lookupObservable, activityObservable, cigaObservable,templateObservable];\r\n    if(this.declarationId || this.status === \"Imported\")\r\n    {\r\n      let declarationData = this.declarationService.get(this.declarationId);\r\n      observableList.push(declarationData);\r\n    }\r\n    forkJoin(observableList).subscribe(result =>{\r\n      const lookupData = result[0].items;\r\n      const activityData = result[1].items;\r\n      const cigaData = result[2].items;\r\n      const templateData = result[3].items;\r\n      let declarationData : DeclarationDto;\r\n      if(result.length > 4)\r\n      {\r\n        declarationData = result[4].declarationDto;\r\n      }\r\n\r\n      lookupData.forEach(element => {\r\n        if(element.name === \"Submitted\") this.statusMappings.submitted = element.id;\r\n        if(element.name === \"Resubmitted\") this.statusMappings.Resubmitted = element.id;\r\n        if(element.name === \"Draft\") this.statusMappings.Draft = element.id;\r\n        if(element.name === \"Reopened\") this.statusMappings.Reopened = element.id;\r\n      });\r\n\r\n      activityData.forEach(element => {\r\n        if(element.name ===\"Holding business\") this.activityMappings.holding = element.id;\r\n        if(element.name ===\"Distribution and service centre business\") this.activityMappings.distribution = element.id;\r\n        if(element.name ===\"Intellectual property business\") this.activityMappings.ip = element.id;\r\n        if(element.name ===\"Shipping business\") this.activityMappings.shipping = element.id;\r\n        if(element.name ===\"Headquarters business\") this.activityMappings.headquarters = element.id;\r\n        if(element.name ===\"Finance and leasing business\") this.activityMappings.finance = element.id;\r\n        if(element.name ===\"Fund management business\") this.activityMappings.funds = element.id;\r\n        if(element.name ===\"Insurance business\") this.activityMappings.insurance = element.id;\r\n        if(element.name ===\"Banking business\") this.activityMappings.banking = element.id;\r\n      });\r\n\r\n      cigaData.forEach(item=>{\r\n        if(item.name.includes('(please specify)')){\r\n          if(item.relevantActivityId === this.activityMappings.holding) this.cigaOtherId.holding = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.distribution) this.cigaOtherId.distribution = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.ip) this.cigaOtherId.ip = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.shipping) this.cigaOtherId.shipping = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.headquarters) this.cigaOtherId.headquarters = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.finance) this.cigaOtherId.finance = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.funds) this.cigaOtherId.funds = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.insurance) this.cigaOtherId.insurance = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.banking) this.cigaOtherId.banking = item.id;\r\n        }\r\n      });\r\n\r\n      var activeTemplate;\r\n\r\n      if(declarationData && declarationData.declarationTemplateId)\r\n      {\r\n        activeTemplate = templateData.find(x => x.id == declarationData.declarationTemplateId);\r\n      } else {\r\n        activeTemplate = templateData.find(element => element.isActive);\r\n      }\r\n      if(activeTemplate)\r\n      {\r\n        this.activeTemplate = activeTemplate.survey;\r\n        this.templateId = activeTemplate.id;\r\n      }\r\n      this.compositeQuestionService.setCompositeQuestions(activeTemplate);\r\n      \r\n      if(this.templateId && declarationData && declarationData.status){\r\n        this.declarationStatus = declarationData.status;\r\n        this.service.getTemplateVersionById(this.templateId).subscribe(result=>{\r\n          this.templateVersion = result;\r\n          if(this.templateVersion != 2 && (this.declarationStatus == 'Draft' || this.declarationStatus == 'Reopened')){\r\n            this.switchTemplateVisible = true\r\n          }\r\n        });\r\n      }\r\n\r\n      this.setUrls(this.activeTemplate);\r\n      this.preProcessCigaDropdown();\r\n      this.setSurvey();\r\n    });\r\n  }\r\n\r\n\r\n  switchToEdit():void{\r\n    this.router.navigate(['/es-declaration'], { queryParams: {declarationid: this.declarationId, entityid: this.entityId, action: 'edit', status:this.status, source: this.source}});\r\n    this.action = 'edit';\r\n    this.isLoading = true;\r\n    this.setSurvey();\r\n\r\n  }\r\n\r\n  closeSurvey(): void{\r\n    if(this.action !== \"view\"){\r\n      this.sweetAlert.fireDialog({action: \"delete\", title: \"Are you sure you want to close the Declaration ?\" ,\r\n      text: \"Any unsaved changes may be lost\", type:\"confirm\"}, (confirm) =>{ // call back function checking if yes or no was selected in confirmaion\r\n        if(confirm) {\r\n          if(this.source === 'Import') this.router.navigate(['/es-import/importdetail'], { queryParams: { id: this.importfileid} });\r\n          else\r\n            this.router.navigateByUrl('/search-result');\r\n        }\r\n      });\r\n    }\r\n    else{\r\n      this.router.navigateByUrl('/search-result');\r\n    }\r\n  }\r\n\r\n  scrollToTop(){\r\n    const element = document.querySelector('.ps'); // of the lepton x content area\r\n    element.scroll({\r\n      top: 0,\r\n      left: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  }\r\n\r\n  deleteImport():void {\r\n    if (this.importdeclarationid) {\r\n\r\n      this.sweetAlert.fireDialog({action: \"delete\", title: \"Are you sure you want to delete the imported Decleration?\" ,\r\n      text: \"Action is unreversable\", type:\"confirm\"}, (confirm) =>{ // call back function checking if yes or no was selected in confirmaion\r\n        if (confirm) {\r\n          this.declarationImportService.delete(this.importdeclarationid).subscribe(result => {\r\n            console.log(\"dec Result: \", result);\r\n            this.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"es-declaration-import-delete\", type:\"toaster\"});\r\n            this.router.navigate(['/es-import/importdetail'], { queryParams: { id: this.importfileid} });\r\n          });\r\n        }\r\n      });\r\n    }else{\r\n      this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration-import-delete\", type:\"toaster\"});\r\n    }\r\n  }\r\n\r\n  print(){\r\n    this.isPrinting = true;\r\n\r\n    var element = document.getElementById('survey');\r\n    var opt = {\r\n      margin: [2.6, 0, 2.6 ,0],\r\n      filename:     'Declaration-Export.pdf',\r\n      image:        { type: 'jpeg'},\r\n      html2canvas:  { scale: 2,  letterRendering: true,\r\n        onclone: (clonedDocument) => { // Added becuase the inner package html2canvas has a known issue for textarea print -> workaround is to swap in a div styled as surveyjs input\r\n          Array.from(clonedDocument.querySelectorAll('textarea')).forEach((textAreaEle) => {\r\n            const textArea = textAreaEle as HTMLTextAreaElement;\r\n            const div = clonedDocument.createElement('div')\r\n            div.innerText = textArea.value\r\n            div.style.width = '100%';\r\n            div.style.whiteSpace = 'pre-wrap';\r\n            div.style.overflowWrap = 'break-word';\r\n            div.style.boxSizing = 'border-box';\r\n            div.style.padding = 'calc(1.5 * (var(--sjs-base-unit, 8px))) calc(2 * (var(--sjs-base-unit, 8px)))';\r\n            div.style.lineHeight = 'calc(1.5 * (var(--sjs-internal-font-editorfont-size)))';\r\n            div.style.fontFamily = 'var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))))';\r\n            div.style.fontWeight = 'var(--sjs-font-editorfont-weight, 500)';\r\n            div.style.color = 'var(--sjs-font-editorfont-color, rgba(0, 0, 0, 0.91))';\r\n            div.style.fontSize = '16px';\r\n            div.style.backgroundColor = 'var(--sjs-editorpanel-backcolor, #f9f9f9)';\r\n            div.style.border = 'none';\r\n            div.style.borderRadius = 'var(--sjs-editorpanel-cornerRadius, 4px)';\r\n            div.style.textAlign = 'start';\r\n            div.style.verticalAlign = 'middle'\r\n            if(!textArea.value){\r\n              div.style.minHeight = '3em'\r\n            }            \r\n            div.style.boxShadow = 'var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, 0.15)), 0 0 0 0px var(--sjs-primary-backcolor, #19b394)';\r\n            textArea.style.display = 'none'\r\n            textArea.parentElement.append(div)\r\n          })\r\n          Array.from(clonedDocument.querySelectorAll('input')).forEach((inputEle) => {\r\n            const input = inputEle as HTMLInputElement;\r\n            const div = clonedDocument.createElement('div')\r\n            if(input.type === 'date' || input.type === 'text' ){\r\n              div.innerText = input.value\r\n              div.style.width = '100%';\r\n              div.style.whiteSpace = 'pre-wrap';\r\n              div.style.overflowWrap = 'break-word';\r\n              div.style.boxSizing = 'border-box';\r\n              div.style.padding = 'calc(1.5 * (var(--sjs-base-unit, 8px))) calc(2 * (var(--sjs-base-unit, 8px)))';\r\n              div.style.lineHeight = 'calc(1.5 * (var(--sjs-internal-font-editorfont-size)))';\r\n              div.style.fontFamily = 'var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))))';\r\n              div.style.fontWeight = 'var(--sjs-font-editorfont-weight, 500)';\r\n              div.style.color = 'var(--sjs-font-editorfont-color, rgba(0, 0, 0, 0.91))';\r\n              div.style.fontSize = '16px';\r\n              div.style.backgroundColor = 'var(--sjs-editorpanel-backcolor, #f9f9f9)';\r\n              div.style.border = 'none';\r\n              div.style.borderRadius = 'var(--sjs-editorpanel-cornerRadius, 4px)';\r\n              div.style.textAlign = 'start';\r\n              div.style.verticalAlign = 'middle'\r\n              div.style.boxShadow = 'var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, 0.15)), 0 0 0 0px var(--sjs-primary-backcolor, #19b394)';\r\n              input.style.display = 'none'\r\n              if(!input.value){\r\n                div.style.minHeight = '3em';\r\n              }\r\n              input.parentElement.append(div)\r\n            }else{\r\n              \r\n              input.style.display = 'none'\r\n            }\r\n            \r\n          })\r\n\r\n        }},\r\n      jsPDF: { unit: 'mm', format: [element.offsetWidth/3, 450], orientation: 'p' },\r\n      pagebreak: { avoid: ['.sd-question', '.sd-question__content','.sd-text__content', '.sd-element__title']},\r\n      removeContainer: false,\r\n    };\r\n    html2pdf().set(opt).from(element).save().then(()=>{\r\n        this.isPrinting = false;\r\n        this.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"es-declaration-download\", type:\"toaster\"});\r\n    }).catch((error)=>{\r\n      console.error('Error saving PDF:', error);\r\n      this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration-download\", type:\"toaster\"});\r\n      this.isPrinting = false;\r\n    });\r\n  }\r\n\r\n  submitImport():void{\r\n    if (this.importdeclarationid){\r\n\r\n      let declerationDto: DeclarationDto = {\r\n        informationRequestedArray: [],\r\n        surveyData: JSON.parse(JSON.stringify(this.survey.data)) as SurveyData,\r\n        declarationTemplateId: this.templateId,\r\n        actionEnforcedFileNames:[]\r\n      }\r\n\r\n      this.convertAllDatesToUTC(declerationDto);\r\n\r\n      if(declerationDto.surveyData.relevantActRelevantActivities){\r\n        const convertedResult = this.convertActivitiesNameToId(declerationDto);\r\n        declerationDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n      }\r\n\r\n      if (this.importdeclarationid) declerationDto.id = this.importdeclarationid;\r\n      declerationDto.surveyData.entityId = this.entityId;\r\n\r\n      this.sweetAlert.fireDialog({action: \"submit\",title: 'Are you sure you want to submit',\r\n      text: \"\",\r\n      type:\"confirm\"},\r\n      (confirm) =>{ // call back function checking if yes or no was selected in confirmaion\r\n        if(confirm) {\r\n         this.declarationImportService.saveAndSubmit(declerationDto).subscribe(result => {\r\n          this.setValidations(result.validationResult.errors);\r\n          if(this.validationErrors.length === 0){ // if no validation error display success and allow it to go to complete screen\r\n            this.router.navigate(['/es-import/importdetail'], { queryParams: { id: this.importfileid} });\r\n            this.hasValidationErrors = false;\r\n            this.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"es-declaration-import-submitted\", type:\"toaster\"});\r\n          }\r\n          else{ // validation errors do not go to complete screen\r\n            this.hasValidationErrors = true;\r\n            this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration-import-submitted\", type:\"toaster\"});\r\n          }\r\n\r\n         });\r\n        }\r\n        });\r\n    }else{\r\n      this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"es-declaration-import-submit\", type:\"toaster\"});\r\n    }\r\n  }\r\n\r\n  async upgradeToLatestTemplatePopup(){\r\n    // call this to trigger upgrade pop up, will return true if user upgrades else false: use for pre save popup\r\n\r\n    // check template version if old and draft or reopened ask user to upgrade \r\n    if (this.templateVersion === 1 && (this.declarationStatus === 'Draft' || this.declarationStatus === 'Reopened')) {\r\n      return new Promise((resolve) => {\r\n          this.sweetAlert.fireDialog({\r\n              action: \"declarationVersionUpgrade\",\r\n              title: 'Declaration Version Warning',\r\n              text: \"Please update to the latest declaration version to enable submission. Verify all entered data before submitting, as upgrading to the new template may result in some minor data loss.\",\r\n              type: \"confirm\"\r\n          }, async (confirm) => {\r\n              if (confirm) {\r\n                  await this.switchDeclarationToLatestTemplate();\r\n                  resolve(true);\r\n              } else {\r\n                  resolve(false);\r\n              }\r\n          });\r\n      });\r\n    } \r\n    else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async upgradeTemplateButtonClicked(){ // only used when user clicks upgrade button at top of declaration\r\n    if (this.templateVersion === 1 && (this.declarationStatus === 'Draft' || this.declarationStatus === 'Reopened')) {\r\n      this.sweetAlert.fireDialog({\r\n        action: \"declarationVersionUpgrade\",\r\n        title: 'Declaration Version Upgrade Confirmation',\r\n        text: \"Please be aware that minor data loss may occur after upgrading. Ensure you check all entered data prior to submission.\",\r\n        type:\"confirm\"\r\n      }, async (confirm) =>{\r\n          if(confirm) {\r\n            await this.switchDeclarationToLatestTemplate();\r\n          }\r\n      });\r\n    } \r\n  }\r\n\r\n  async switchDeclarationToLatestTemplate(): Promise<void>{\r\n    this.surveyresults = JSON.parse(JSON.stringify(this.survey.data));\r\n    let declaratioDto: DeclarationDto = {\r\n      informationRequestedArray:this.declarationData?.informationRequestedArray??[],\r\n      surveyData:  JSON.parse(JSON.stringify(this.surveyresults)) as SurveyData,\r\n      declarationTemplateId: this.templateId ?? null,\r\n      actionEnforcedFileNames:[]\r\n    }\r\n    if(this.declarationStatus == 'Draft'){\r\n      declaratioDto.status = \"Draft\";\r\n      declaratioDto.statusLookupId = this.statusMappings.Draft;\r\n    }\r\n    else if( this.declarationStatus == 'Reopened'){\r\n      declaratioDto.status = \"Reopened\";\r\n      declaratioDto.statusLookupId = this.statusMappings.Reopened;\r\n    }\r\n    this.convertAllDatesToUTC(declaratioDto);\r\n    if(declaratioDto.surveyData.relevantActRelevantActivities){\r\n      const convertedResult = this.convertActivitiesNameToId(declaratioDto);\r\n      declaratioDto.surveyData.relevantActRelevantActivities = convertedResult;\r\n    }\r\n    if (this.declarationId) declaratioDto.id = this.declarationId;\r\n    declaratioDto.surveyData.entityId = this.entityId;\r\n    this.declarationService.upgradeTemplateVersionByDto(declaratioDto).subscribe(async result=>{\r\n      this.validationErrors = [];\r\n      if(!result.validationResult.isValid){\r\n        let errors = result.validationResult.errors\r\n        for (const error in errors){\r\n          for(const errorInfo in errors[error]){\r\n            if(errorInfo === 'propertyName' && (errors[error][errorInfo].includes(\"FinancialPeriodStartDate\")|| errors[error][errorInfo].includes(\"FinancialPeriodEndDate\"))){\r\n              this.validationErrors.push(errors[error][\"errorMessage\"]);\r\n              this.hasValidationErrors = true;\r\n            }\r\n          }\r\n        }\r\n        this.sweetAlert.fireDialog({status: DialogStatus.FAILED, source:\"upgrade-template-version\", type:\"toaster\"});\r\n      }\r\n      else{\r\n        this.isLoading = true;\r\n        this.switchTemplateVisible = false;\r\n        await this.intialConfigAndSetup();\r\n        this.sweetAlert.fireDialog({status: DialogStatus.SUCCESS, source:\"upgrade-template-version\", type:\"toaster\"});\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n", "    <div class=\"mat-spinner-center\" *ngIf= \"isLoading\">\r\n        <mat-progress-spinner [mode] = \"'indeterminate'\" [diameter]=\"100\" class=\"mat-spinner-color\"></mat-progress-spinner>\r\n    </div>\r\n    <mat-card class=\"card-background\" [hidden] = isLoading>\r\n\r\n        <mat-card-header class=\"display-flex-space sticky-survey-header\">\r\n            <mat-card-title class=\"title-color-font\">Economic Substance Declaration</mat-card-title>\r\n            <div class=\"button-container\">\r\n                <button mat-raised-button class=\"ui-button margin-l-5\"  (click) = \"closeSurvey()\" *ngIf= \"surveyModel\" matTooltip=\"Close\"><mat-icon class=\"icon\">close</mat-icon></button>\r\n                <!-- <div>\r\n                  <button mat-raised-button class=\"ui-button margin-l-5\" matTooltip=\"Delete\" (click) = \"deleteImport()\" *ngIf= \"surveyModel && source === 'Import'\"><mat-icon class=\"icon\">delete</mat-icon></button>\r\n                </div> -->\r\n                <div *abpPermission=\"'EsService.Declaration.Submit'\">\r\n                    <button mat-raised-button class=\"ui-button margin-l-5\"  (click) = \"switchToEdit()\" *ngIf= \"surveyModel && isEditVisible\">Edit</button>\r\n                </div>\r\n\r\n                <button mat-raised-button class=\"ui-button margin-l-5\" id=\"printButton\"\r\n                    [disabled]=\"isPrinting\" *ngIf=\"surveyModel && isPrintVisible\"\r\n                    matTooltip=\"Print\" (click)=\"print()\">\r\n                        <mat-icon class=\"icon\" *ngIf=\"!isPrinting\">print</mat-icon>\r\n                    <span *ngIf=\"isPrinting\">\r\n                        <mat-spinner diameter=\"20\" color=\"accent\"></mat-spinner>\r\n                    </span>\r\n                </button>\r\n                <div *abpPermission=\"'EsService.Declaration.Submit'\">\r\n                    <button mat-raised-button class=\"ui-button margin-l-5 mat-button\" *ngIf = \"surveyModel && action !== 'view' && source !== 'Import'\" (click) =\"preSaveVersionWarningCheck()\" matTooltip=\"Save As Draft\"><mat-icon class=\"icon\">save</mat-icon></button>\r\n                    <button mat-raised-button class=\"ui-button margin-l-5 mat-button\" *ngIf = \"surveyModel && surveyModel?.isLastPage && !surveyModel?.isPreviewButtonVisible && surveyModel?.isCompleteButtonVisible && source === 'Import'\" (click) =\"preSaveVersionWarningCheck()\" matTooltip=\"Save As Draft\"><mat-icon class=\"icon\">save</mat-icon></button>\r\n                </div>\r\n                <button mat-raised-button class=\"ui-button margin-l-5 \"  *ngIf=\"surveyModel && !surveyModel?.isFirstPage\" (click)=\"surveyModel.prevPage()\"> <mat-icon>arrow_back</mat-icon>Previous</button>\r\n                <button mat-raised-button class=\"ui-button margin-l-5\"  *ngIf=\"surveyModel && !surveyModel?.isLastPage\" (click)=\"surveyModel.nextPage()\"> <mat-icon>arrow_forward</mat-icon>Next</button>\r\n                <button mat-raised-button class=\"ui-button margin-l-5\" *ngIf=\"surveyModel && surveyModel?.isPreviewButtonVisible\" (click) = \"surveyModel.showPreview()\"><mat-icon>visibility</mat-icon>Preview</button>\r\n                <div *abpPermission=\"'EsService.Declaration.Submit'\">\r\n                    <button mat-raised-button class=\"ui-button margin-l-5\" *ngIf=\"surveyModel && surveyModel?.isLastPage && !surveyModel?.isPreviewButtonVisible && surveyModel?.isCompleteButtonVisible && source !== 'Import'\" (click)=\"surveyModel.doComplete()\"> <mat-icon>done</mat-icon>Complete</button>\r\n                </div>\r\n\r\n                <div>\r\n                  <button mat-raised-button class=\"ui-button margin-l-5\" (click) = \"submitImport()\" *ngIf = \"surveyModel && surveyModel?.isLastPage && !surveyModel?.isPreviewButtonVisible && surveyModel?.isCompleteButtonVisible && source === 'Import'\"> <mat-icon>done</mat-icon>Submit</button>\r\n                </div>\r\n                <button mat-raised-button class=\"ui-button margin-l-5 \" *ngIf=\"surveyModel && switchTemplateVisible\" (click)=\"upgradeTemplateButtonClicked()\">Switch to latest Template</button>\r\n\r\n            </div>\r\n        </mat-card-header>\r\n        <mat-card-content id=\"survey\" class=\"card-background\">\r\n            <div>\r\n                <mat-divider class=\"divider-margin\"></mat-divider>\r\n\r\n                <app-declaration-entity-details [entityId] = \"entityId\" [currentPageIndex] = \"currentPageIndex\" [isViewMode] = \"isViewMode\" [type]=\"entityType\"></app-declaration-entity-details>\r\n\r\n                <app-assessment-action-view *ngIf=\"displayActions\" [declarationData]=\"declarationData\" [declarationId]=\"declarationId\" [type]=\"entityType\" [historyId]=\"historyId\"></app-assessment-action-view>\r\n\r\n                <div class=\"validation-errors-box spacing-margin\" *ngIf  = \"hasValidationErrors && !isPrinting\">\r\n                    <h4 class=\"validation-error-title\">  <em class=\"fas fa-exclamation-triangle\"></em> Validation Errors:</h4>\r\n                    <ul class=\"validation-error-list\">\r\n                        <li *ngFor=\"let error of validationErrors\" class=\"validation-error-text\">{{error}}</li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <div class=\"warning-box\" *ngIf  = \"haswarnings && !isPrinting\">\r\n                    <h4 class=\"validation-error-title\">  <em class=\"fas fa-exclamation-triangle\"></em> Warnings:</h4>\r\n                    <ul class=\"validation-error-list\">\r\n                        <li *ngFor=\"let error of validationWarnings\" class=\"validation-warning-text\">{{error}}</li>\r\n                    </ul>\r\n                </div>\r\n                <survey [model]=\"surveyModel\" id=\"survey\"></survey>\r\n            </div>\r\n        </mat-card-content>\r\n\r\n\r\n\r\n\r\n\r\n    </mat-card>\r\n"], "mappings": ";AACA,SAASA,KAAK,QAAQ,aAAa;AAMnC,SAASC,gBAAgB,QAAQ,yBAAyB;AAM1D;AACA,SAASC,UAAU,QAAQ,+BAA+B;AAG1D,SAAQC,oBAAoB,QAAQ,qDAAqD;AACzF,SAASC,mBAAmB,QAAQ,aAAa;AAIjD,SAASC,WAAW,QAAQ,2BAA2B;AAEvD,OAAO,KAAKC,UAAU,MAAM,aAAa;AAGzC,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,kBAAkB,EAAEC,yBAAyB,QAAQ,iBAAiB;AAC/E,SAASC,SAAS,QAAQ,qBAAqB;AAG/C,SAAqBC,QAAQ,EAAEC,QAAQ,QAAQ,MAAM;AASrD,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,IAAI,QAAQ,+BAA+B;AAEpD,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,0BAA0B,QAAQ,0EAA0E;AAErH,SAASC,cAAc,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;ICjDxCC,EAAA,CAAAC,cAAA,cAAmD;IAC/CD,EAAA,CAAAE,SAAA,+BAAmH;IACvHF,EAAA,CAAAG,YAAA,EAAM;;;IADoBH,EAAA,CAAAI,SAAA,EAA0B;IAACJ,EAA3B,CAAAK,UAAA,yBAA0B,iBAAiB;;;;;;IAOzDL,EAAA,CAAAC,cAAA,iBAA0H;IAAlED,EAAA,CAAAM,UAAA,mBAAAC,iEAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAWF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAAyCb,EAAA,CAAAC,cAAA,mBAAuB;IAAAD,EAAA,CAAAc,MAAA,YAAK;IAAWd,EAAX,CAAAG,YAAA,EAAW,EAAS;;;;;;IAKtKH,EAAA,CAAAC,cAAA,iBAAyH;IAAjED,EAAA,CAAAM,UAAA,mBAAAS,uEAAA;MAAAf,EAAA,CAAAQ,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAWF,MAAA,CAAAO,YAAA,EAAc;IAAA,EAAC;IAAuCjB,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;IAD1IH,EAAA,CAAAC,cAAA,UAAqD;IACjDD,EAAA,CAAAkB,UAAA,IAAAC,8CAAA,oBAAyH;IAC7HnB,EAAA,CAAAG,YAAA,EAAM;;;;IADkFH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAK,UAAA,SAAAK,MAAA,CAAAU,WAAA,IAAAV,MAAA,CAAAW,aAAA,CAAmC;;;;;IAMnHrB,EAAA,CAAAC,cAAA,mBAA2C;IAAAD,EAAA,CAAAc,MAAA,YAAK;IAAAd,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAC,cAAA,WAAyB;IACrBD,EAAA,CAAAE,SAAA,sBAAwD;IAC5DF,EAAA,CAAAG,YAAA,EAAO;;;;;;IANXH,EAAA,CAAAC,cAAA,iBAEyC;IAAlBD,EAAA,CAAAM,UAAA,mBAAAgB,iEAAA;MAAAtB,EAAA,CAAAQ,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAc,KAAA,EAAO;IAAA,EAAC;IAEpCxB,EADI,CAAAkB,UAAA,IAAAO,mDAAA,uBAA2C,IAAAC,+CAAA,mBACtB;IAG7B1B,EAAA,CAAAG,YAAA,EAAS;;;;IANLH,EAAA,CAAAK,UAAA,aAAAK,MAAA,CAAAiB,UAAA,CAAuB;IAEK3B,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,UAAA,UAAAK,MAAA,CAAAiB,UAAA,CAAiB;IACtC3B,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,UAAA,SAAAK,MAAA,CAAAiB,UAAA,CAAgB;;;;;;IAKvB3B,EAAA,CAAAC,cAAA,iBAAuM;IAAnED,EAAA,CAAAM,UAAA,mBAAAsB,uEAAA;MAAA5B,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAoB,0BAAA,EAA4B;IAAA,EAAC;IAA4B9B,EAAA,CAAAC,cAAA,mBAAuB;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAWd,EAAX,CAAAG,YAAA,EAAW,EAAS;;;;;;IACtPH,EAAA,CAAAC,cAAA,iBAA6R;IAAnED,EAAA,CAAAM,UAAA,mBAAAyB,uEAAA;MAAA/B,EAAA,CAAAQ,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAoB,0BAAA,EAA4B;IAAA,EAAC;IAA4B9B,EAAA,CAAAC,cAAA,mBAAuB;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAWd,EAAX,CAAAG,YAAA,EAAW,EAAS;;;;;IAFhVH,EAAA,CAAAC,cAAA,UAAqD;IAEjDD,EADA,CAAAkB,UAAA,IAAAe,8CAAA,qBAAuM,IAAAC,8CAAA,qBACsF;IACjSlC,EAAA,CAAAG,YAAA,EAAM;;;;IAFiEH,EAAA,CAAAI,SAAA,EAA+D;IAA/DJ,EAAA,CAAAK,UAAA,SAAAK,MAAA,CAAAU,WAAA,IAAAV,MAAA,CAAAyB,MAAA,eAAAzB,MAAA,CAAA0B,MAAA,cAA+D;IAC/DpC,EAAA,CAAAI,SAAA,EAAqJ;IAArJJ,EAAA,CAAAK,UAAA,SAAAK,MAAA,CAAAU,WAAA,KAAAV,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAiB,UAAA,OAAA3B,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAkB,sBAAA,MAAA5B,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAmB,uBAAA,KAAA7B,MAAA,CAAA0B,MAAA,cAAqJ;;;;;;IAE5NpC,EAAA,CAAAC,cAAA,iBAA2I;IAAjCD,EAAA,CAAAM,UAAA,mBAAAkC,kEAAA;MAAAxC,EAAA,CAAAQ,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAU,WAAA,CAAAsB,QAAA,EAAsB;IAAA,EAAC;IAAE1C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,iBAAU;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAAAH,EAAA,CAAAc,MAAA,eAAQ;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5LH,EAAA,CAAAC,cAAA,iBAAyI;IAAjCD,EAAA,CAAAM,UAAA,mBAAAqC,kEAAA;MAAA3C,EAAA,CAAAQ,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAU,WAAA,CAAAyB,QAAA,EAAsB;IAAA,EAAC;IAAE7C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,oBAAa;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAAAH,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;;IACzLH,EAAA,CAAAC,cAAA,iBAAwJ;IAAtCD,EAAA,CAAAM,UAAA,mBAAAwC,kEAAA;MAAA9C,EAAA,CAAAQ,aAAA,CAAAuC,GAAA;MAAA,MAAArC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAWF,MAAA,CAAAU,WAAA,CAAA4B,WAAA,EAAyB;IAAA,EAAC;IAAChD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,iBAAU;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAAAH,EAAA,CAAAc,MAAA,cAAO;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;;IAEnMH,EAAA,CAAAC,cAAA,iBAAgP;IAAnCD,EAAA,CAAAM,UAAA,mBAAA2C,wEAAA;MAAAjD,EAAA,CAAAQ,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAU,WAAA,CAAA+B,UAAA,EAAwB;IAAA,EAAC;IAAEnD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAAAH,EAAA,CAAAc,MAAA,eAAQ;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;IAD/RH,EAAA,CAAAC,cAAA,UAAqD;IACjDD,EAAA,CAAAkB,UAAA,IAAAkC,+CAAA,oBAAgP;IACpPpD,EAAA,CAAAG,YAAA,EAAM;;;;IADsDH,EAAA,CAAAI,SAAA,EAAmJ;IAAnJJ,EAAA,CAAAK,UAAA,SAAAK,MAAA,CAAAU,WAAA,KAAAV,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAiB,UAAA,OAAA3B,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAkB,sBAAA,MAAA5B,MAAA,CAAAU,WAAA,kBAAAV,MAAA,CAAAU,WAAA,CAAAmB,uBAAA,KAAA7B,MAAA,CAAA0B,MAAA,cAAmJ;;;;;;IAI7MpC,EAAA,CAAAC,cAAA,iBAA0O;IAAnLD,EAAA,CAAAM,UAAA,mBAAA+C,kEAAA;MAAArD,EAAA,CAAAQ,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAWF,MAAA,CAAA6C,YAAA,EAAc;IAAA,EAAC;IAA0JvD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAAAH,EAAA,CAAAc,MAAA,aAAM;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;;IAErRH,EAAA,CAAAC,cAAA,iBAA8I;IAAzCD,EAAA,CAAAM,UAAA,mBAAAkD,kEAAA;MAAAxD,EAAA,CAAAQ,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAgD,4BAAA,EAA8B;IAAA,EAAC;IAAC1D,EAAA,CAAAc,MAAA,gCAAyB;IAAAd,EAAA,CAAAG,YAAA,EAAS;;;;;IAUhLH,EAAA,CAAAE,SAAA,qCAAgM;;;;IAArDF,EAAxF,CAAAK,UAAA,oBAAAK,MAAA,CAAAiD,eAAA,CAAmC,kBAAAjD,MAAA,CAAAkD,aAAA,CAAgC,SAAAlD,MAAA,CAAAmD,UAAA,CAAoB,cAAAnD,MAAA,CAAAoD,SAAA,CAAwB;;;;;IAK1J9D,EAAA,CAAAC,cAAA,aAAyE;IAAAD,EAAA,CAAAc,MAAA,GAAS;IAAAd,EAAA,CAAAG,YAAA,EAAK;;;;IAAdH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAA+D,iBAAA,CAAAC,SAAA,CAAS;;;;;IAFtFhE,EADJ,CAAAC,cAAA,cAAgG,aACzD;IAAED,EAAA,CAAAE,SAAA,aAA6C;IAACF,EAAA,CAAAc,MAAA,0BAAkB;IAAAd,EAAA,CAAAG,YAAA,EAAK;IAC1GH,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAAkB,UAAA,IAAA+C,2CAAA,iBAAyE;IAEjFjE,EADI,CAAAG,YAAA,EAAK,EACH;;;;IAFwBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,YAAAK,MAAA,CAAAwD,gBAAA,CAAmB;;;;;IAOzClE,EAAA,CAAAC,cAAA,aAA6E;IAAAD,EAAA,CAAAc,MAAA,GAAS;IAAAd,EAAA,CAAAG,YAAA,EAAK;;;;IAAdH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAA+D,iBAAA,CAAAI,SAAA,CAAS;;;;;IAF1FnE,EADJ,CAAAC,cAAA,cAA+D,aACxB;IAAED,EAAA,CAAAE,SAAA,aAA6C;IAACF,EAAA,CAAAc,MAAA,iBAAS;IAAAd,EAAA,CAAAG,YAAA,EAAK;IACjGH,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAAkB,UAAA,IAAAkD,2CAAA,iBAA6E;IAErFpE,EADI,CAAAG,YAAA,EAAK,EACH;;;;IAFwBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,YAAAK,MAAA,CAAA2D,kBAAA,CAAqB;;;ADTnE3E,IAAI,CAACR,UAAU,CAAC;AAChB;AACAU,uBAAuB,CAAC0E,QAAQ,CAACC,iBAAiB,CAAC,iBAAiB,EAAEzE,0BAA0B,CAAC;AAEjGX,eAAe,CAACmF,QAAQ,CAACE,QAAQ,CAAC,oBAAoB,EAAEpF,kBAAkB,CAAC;AAC3ED,eAAe,CAACmF,QAAQ,CAACE,QAAQ,CAAC,2BAA2B,EAAEnF,yBAAyB,CAAC;AAQzF,OAAM,MAAOoF,sBAAuB,SAAQ5F,gBAAgB;EAG1D6F,YAAYC,QAAkB,EACpBC,OAAwB,EACxBC,UAA6B,EAC7BC,cAAuC,EACvCC,KAAqB,EACrBC,kBAAsC,EACtCC,YAAsC;EAC9C;EACQC,wBAAkD,EAClDC,UAAuB,EACvBC,MAAc,EACdC,sBAA8C,EAC9CC,cAA8B,EAC9BC,wBAAkD,EACnDC,MAAiB,EACjBC,cAAyC,EACxCC,MAA0B;IAElC,KAAK,CAACf,QAAQ,CAAC;IAjBP,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IAEZ,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACzB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAAC,eAAe,GAAuB;MAACC,OAAO,EAAE,IAAI;MAAEC,SAAS,EAAC,CAAC;MAAEC,cAAc,EAAE;IAAG,CAAC,EAAC;IAuCxF,KAAAC,OAAO,GAAG,EAAE;IAUZ,KAAAC,mBAAmB,GAAG,KAAK;IAE3B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA5E,aAAa,GAAY,KAAK;IAC9B,KAAA6E,UAAU,GAAG,IAAI;IACjB,KAAA7B,kBAAkB,GAAa,EAAE;IACjC,KAAA8B,WAAW,GAAG,KAAK;IACnB,KAAAxE,UAAU,GAAG,KAAK;IAClB,KAAAyE,cAAc,GAAG;MAACC,SAAS,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,KAAK,EAAC,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE;IAC1E,KAAAC,gBAAgB,GAAG;MAACC,OAAO,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,EAAE,EAAC,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAGC,YAAY,EAAC,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAGC,KAAK,EAAC,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,OAAO,EAAC;IAAE,CAAG;IAC9I,KAAAC,WAAW,GAAG;MAACT,OAAO,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,EAAE,EAAC,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAGC,YAAY,EAAC,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAGC,KAAK,EAAC,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,OAAO,EAAC;IAAE,CAAG;IAGzI,KAAAE,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,2BAA2B,GAAG,KAAK;IACnC,KAAAC,aAAa,GAAa,CAAC,8BAA8B,EAAC,kBAAkB,EAAE,uBAAuB,EAAE,kBAAkB,EAAE,wBAAwB,EACnJ,uBAAuB,EAAE,0BAA0B,EAAE,oBAAoB,EAAC,yCAAyC,EAAE,mBAAmB,CAAC;IAGzI,KAAAC,SAAS,GAAG,IAAI;IAEhB,KAAA3D,UAAU,GAAG,IAAI;IACjB,KAAA4D,cAAc,GAAG,IAAI;IACrB,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,eAAe,GAAG,CAAC,CAAC,CAAG;IACvB,KAAAC,gBAAgB,GAAG,kBAAkB,CAAC,CAAC;IAEvC,KAAAC,iBAAiB,GAAG,IAAI;IAxDtB,IAAI,CAAC9C,KAAK,CAAC+C,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAACpE,aAAa,GAAGoE,MAAM,CAAC,eAAe,CAAC,GAAGA,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI;MAC7E,IAAI,CAACC,QAAQ,GAAID,MAAM,CAAC,UAAU,CAAC;MACnC,IAAI,CAAC7F,MAAM,GAAG6F,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,CAACE,MAAM,GAAGF,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,CAAC5F,MAAM,GAAG4F,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,CAACG,YAAY,GAAGH,MAAM,CAAC,cAAc,CAAC;MAC1C,IAAI,CAACI,mBAAmB,GAAGJ,MAAM,CAAC,qBAAqB,CAAC;MACxD,IAAI,CAAClE,SAAS,GAAGkE,MAAM,CAAC,WAAW,CAAC;MACpCK,OAAO,CAACC,GAAG,CAAC,IAAI,CAACF,mBAAmB,CAAC;MACrCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1E,aAAa,CAAC;IACjC,CAAC,CAAC;IACF;EACF;EA4CM2E,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAChB,SAAS,GAAG,IAAI;MACrB,IAAIkB,IAAI,GAAGF,KAAI,CAAC9C,MAAM,CAACiD,UAAU,CAAC,wBAAwB,CAAC,IAAI,MAAM;MACrEH,KAAI,CAAC3E,UAAU,GAAG6E,IAAI,GAAE,IAAI,GAAC,IAAI;MACjC,MAAMF,KAAI,CAACI,oBAAoB,EAAE;MACjC;IAAA;EACF;EAEMC,SAASA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAEb,IAAIK,MAAI,CAAChF,SAAS,EAAC;QACjB,MAAMgF,MAAI,CAACrD,cAAc,CAACsD,GAAG,CAACD,MAAI,CAAChF,SAAS,CAAC,CAACiE,SAAS,CAAEiB,IAAI,IAAG;UAC9DF,MAAI,CAACnF,eAAe,GAAGqF,IAAI,CAACC,mBAAmB;UAC/CZ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,MAAI,CAACnF,eAAe,CAAC;QACpD,CAAC,CAAC;MACJ,CAAC,MACI,IAAGmF,MAAI,CAAClF,aAAa,IAAIkF,MAAI,CAACZ,MAAM,KAAK,UAAU,EACxD;QACE,MAAMY,MAAI,CAAC9D,kBAAkB,CAAC+D,GAAG,CAACD,MAAI,CAAClF,aAAa,CAAC,CAACmE,SAAS,CAAEiB,IAAI,IAAG;UACtEF,MAAI,CAACnF,eAAe,GAAGqF,IAAI,CAACE,cAAc;UAC1Cb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,MAAI,CAACnF,eAAe,CAAC;QACpD,CAAC,CAAC;MACJ;MACA,IAAGmF,MAAI,CAAC3G,MAAM,KAAK,MAAM,EAAC;QACxB2G,MAAI,CAACrB,cAAc,GAAG,IAAI;QAC1BY,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEQ,MAAI,CAAChF,SAAS,CAAC;QAC1C,IAAGgF,MAAI,CAACZ,MAAM,KAAK,WAAW,IAAIY,MAAI,CAAChF,SAAS,IAAIgF,MAAI,CAACZ,MAAM,KAAK,aAAa,EAAEY,MAAI,CAACzH,aAAa,GAAG,KAAK,CAAC,KACzGyH,MAAI,CAACzH,aAAa,GAAG,IAAI;QAC9ByH,MAAI,CAAC7C,cAAc,GAAG,IAAI;QAC1B6C,MAAI,CAAC1B,UAAU,GAAG,IAAI;QACtB0B,MAAI,CAACK,YAAY,GAAGpK,oBAAoB;QACxC+J,MAAI,CAACM,aAAa,EAAE;QACpBN,MAAI,CAACO,aAAa,EAAE;QACpBP,MAAI,CAAC9C,mBAAmB,GAAG,KAAK;QAChC8C,MAAI,CAAC3C,WAAW,GAAG,KAAK,CAAC,CAAC;MAC5B,CAAC,MACI,IAAG2C,MAAI,CAAC1G,MAAM,KAAK,QAAQ,IAAI0G,MAAI,CAACZ,MAAM,KAAK,UAAU,EAAC;QAAE;QAC/DY,MAAI,CAACrB,cAAc,GAAG,IAAI;QAC1BqB,MAAI,CAACQ,eAAe,EAAE;MACxB,CAAC,MACG;QACFR,MAAI,CAACzH,aAAa,GAAG,KAAK;QAC1ByH,MAAI,CAAC7C,cAAc,GAAG,KAAK;QAC3B6C,MAAI,CAAC1B,UAAU,GAAG,KAAK;QAEvB,IAAI0B,MAAI,CAAC3G,MAAM,IAAI,MAAM,EAAC;UACxB2G,MAAI,CAACrB,cAAc,GAAG,KAAK;QAC7B;QACA5H,UAAU,CAAC0J,WAAW,CAAC,UAAU,EAAE;UACjCC,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE;SACP,CAAC;QACAX,MAAI,CAACY,MAAM,GAAG,IAAI9K,KAAK,CAACkK,MAAI,CAACa,cAAc,CAAC;QAC5Cb,MAAI,CAACY,MAAM,CAACE,UAAU,CAACtK,SAAS,CAAC;QACjC;QAEAwJ,MAAI,CAACY,MAAM,CAACG,qBAAqB,GAAG,KAAK;QACzC,MAAMC,SAAS,GAAG,IAAIrK,SAAS,EAAE;QACjCqJ,MAAI,CAACY,MAAM,CAACK,cAAc,CAACC,GAAG,CAAC,UAAUN,MAAM,EAAEO,OAAO;UACtD;UACA,IAAIC,GAAG,GAAGJ,SAAS,CAACK,QAAQ,CAACF,OAAO,CAACG,IAAI,CAAC;UAC1C;UACAF,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC;UACtBH,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,EAAEH,GAAG,CAACI,MAAM,GAAG,CAAC,CAAC;UACtC;UACAL,OAAO,CAACM,IAAI,GAAGL,GAAG;QACpB,CAAC,CAAC;QACFpB,MAAI,CAACY,MAAM,CAACc,aAAa,GAAG,0CAA0C;QAEtE1B,MAAI,CAACY,MAAM,CAACe,UAAU,CAACT,GAAG,CAAEU,MAAM,IAAG;UACnC5B,MAAI,CAAC6B,aAAa,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,MAAM,CAAC1B,IAAI,CAAC,CAAC;UAC7D0B,MAAM,CAACK,WAAW,GAAG,KAAK;UAC1BjC,MAAI,CAACkC,cAAc,CAACN,MAAM,CAAC;QAC7B,CAAC,CAAC;QAEF5B,MAAI,CAACY,MAAM,CAACuB,cAAc,CAACjB,GAAG,CAAC,CAACU,MAAM,EAACT,OAAO,KAAG;UAC/CnB,MAAI,CAACoC,YAAY,CAACR,MAAM,EAACT,OAAO,CAAC;QACnC,CAAC,CAAC;QAEFnB,MAAI,CAACY,MAAM,CAACyB,qBAAqB,CAACnB,GAAG,CAAC,CAACU,MAAM,EAAET,OAAO,KAAG;UACvD,IAAGA,OAAO,EAAEmB,cAAc,EAAEC,YAAY,EAAE7B,IAAI,KAAK,KAAK,IAAIS,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,KAAK,EAAE;YAAE;YACnH,IAAGS,OAAO,CAACsB,cAAc,EAAC;cACxB,IAAG,CAACzC,MAAI,CAACxB,2BAA2B,IAAKwB,MAAI,CAAC5C,UAAU,IAAI4C,MAAI,CAAC3C,WAAY,EAAC;gBAC5E8D,OAAO,CAACuB,KAAK,GAAG,KAAK;cACvB;cACA,IAAG,CAAC1C,MAAI,CAACxB,2BAA2B,IAAIwB,MAAI,CAAC9C,mBAAmB,EAAC;gBAC7D8C,MAAI,CAAC6B,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAChC,MAAI,CAACY,MAAM,CAACV,IAAI,CAAC,CAAC;gBACjEF,MAAI,CAAC2C,qBAAqB,CAACf,MAAM,EAACT,OAAO,CAAC;cAC9C;YACF;UACF;QAEF,CAAC,CAAC;QAEFnB,MAAI,CAACY,MAAM,CAACgC,oBAAoB,CAAC1B,GAAG,CAAC,CAACU,MAAM,EAAET,OAAO,KAAG;UACtDnB,MAAI,CAACxB,2BAA2B,GAAG,KAAK;UACxCwB,MAAI,CAAC5C,UAAU,GAAG,IAAI;UACtB4C,MAAI,CAAC9C,mBAAmB,GAAG,KAAK;UAChC8C,MAAI,CAAC3C,WAAW,GAAG,KAAK;UACxB2C,MAAI,CAAC5E,gBAAgB,GAAG,EAAE;UAC1B4E,MAAI,CAACzE,kBAAkB,GAAG,EAAE;UAC5ByE,MAAI,CAAC7C,cAAc,GAAG,KAAK,CAAC,CAAC;UAE7B6C,MAAI,CAACzB,gBAAgB,GAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAEM,GAAG,GAAG,CAAC;UACtE,IAAG1B,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,iBAAiB,EAAEV,MAAI,CAACzB,gBAAgB,GAAG,CAAC;UAC/F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,eAAe,EAAEV,MAAI,CAACzB,gBAAgB,GAAG,CAAC;UAC7F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,kBAAkB,EAAEV,MAAI,CAACzB,gBAAgB,GAAG,CAAC;UAChG,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,cAAc,EAAEV,MAAI,CAACzB,gBAAgB,GAAG,CAAC;UAC5F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,gBAAgB,EAAEV,MAAI,CAACzB,gBAAgB,GAAG,CAAC;UAC9F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,oBAAoB,EAAEV,MAAI,CAACzB,gBAAgB,GAAG,CAAC;UAClGyB,MAAI,CAAC8C,WAAW,EAAE;QACpB,CAAC,CAAC;QAEF9C,MAAI,CAACY,MAAM,CAACmC,gBAAgB,CAAC7B,GAAG,CAAC,CAACU,MAAM,EAACT,OAAO,KAAG;UACjDnB,MAAI,CAAC7C,cAAc,GAAG,IAAI;QAC5B,CAAC,CAAC;QAED6C,MAAI,CAACY,MAAM,CAACoC,aAAa,CAAC9B,GAAG,CAAC,CAAC+B,CAAC,EAAE9B,OAAO,KAAI;UAC5C,IAAGnB,MAAI,CAAClF,aAAa,IAAI,IAAI,IAAIkF,MAAI,CAAClF,aAAa,IAAI,sCAAsC,EAC7F;YACE;YACAkF,MAAI,CAACkD,WAAW,CAAC,KAAK,CAAC,CAACC,IAAI,CAAC,MAAI;cAC/BnD,MAAI,CAACoD,qBAAqB,CAACjC,OAAO,CAAC;YACrC,CAAC,CAAC,CAACkC,KAAK,CAAEC,KAAK,IAAE;cACf/D,OAAO,CAACC,GAAG,CAAC8D,KAAK,CAAC;cAClBnC,OAAO,CAACoC,QAAQ,CAAC,OAAO,CAAC;YAC3B,CAAE,CAAC;UAEL,CAAC,MAAM;YACLvD,MAAI,CAACoD,qBAAqB,CAACjC,OAAO,CAAC;UACrC;QACF,CAAC,CAAC;QAEF;QACA;QACA;QAEAnB,MAAI,CAACY,MAAM,CAAC4C,YAAY,CAACtC,GAAG,CAAC,CAAC+B,CAAC,EAAC9B,OAAO,KAAG;UACxCnB,MAAI,CAACwD,YAAY,CAACrC,OAAO,CAAC;QAC5B,CAAC,CAAC;QAEFnB,MAAI,CAACO,aAAa,EAAE;MACxB;IAAC;EACH;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACjI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC4E,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACmB,UAAU,GAAG,KAAK;IAEvBvH,UAAU,CAAC0J,WAAW,CAAC,UAAU,EAAE;MACjCC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE;KACP,CAAC;IACA,IAAI,CAACC,MAAM,GAAG,IAAI9K,KAAK,CAAC,IAAI,CAAC+K,cAAc,CAAC;IAC5C,IAAI,CAACD,MAAM,CAACE,UAAU,CAACtK,SAAS,CAAC;IACjC;IACA,IAAI,CAACoK,MAAM,CAACG,qBAAqB,GAAG,KAAK;IACzC,MAAMC,SAAS,GAAG,IAAIrK,SAAS,EAAE;IAEjC,IAAI,CAACiK,MAAM,CAACK,cAAc,CAACC,GAAG,CAAC,UAAUN,MAAM,EAAEO,OAAO;MACtD;MACA,IAAIC,GAAG,GAAGJ,SAAS,CAACK,QAAQ,CAACF,OAAO,CAACG,IAAI,CAAC;MAC1C;MACAF,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC;MACtBH,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,EAAEH,GAAG,CAACI,MAAM,GAAG,CAAC,CAAC;MACtC;MACAL,OAAO,CAACM,IAAI,GAAGL,GAAG;IACpB,CAAC,CAAC;IACF,IAAI,CAACR,MAAM,CAACc,aAAa,GAAG,0CAA0C;IAEtE,IAAI,CAACd,MAAM,CAACe,UAAU,CAACT,GAAG,CAAEU,MAAM,IAAG;MACnC,IAAI,CAACC,aAAa,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,MAAM,CAAC1B,IAAI,CAAC,CAAC;MAC7D0B,MAAM,CAACK,WAAW,GAAG,KAAK;MAC1B,IAAI,CAACC,cAAc,CAACN,MAAM,CAAC;IAC7B,CAAC,CAAC;IAEF,IAAI,CAAChB,MAAM,CAACuB,cAAc,CAACjB,GAAG,CAAC,CAACU,MAAM,EAACT,OAAO,KAAG;MAC/C,IAAI,CAACiB,YAAY,CAACR,MAAM,EAACT,OAAO,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAACP,MAAM,CAACyB,qBAAqB,CAACnB,GAAG,CAAC,CAACU,MAAM,EAAET,OAAO,KAAG;MACvD,IAAGA,OAAO,EAAEmB,cAAc,EAAEC,YAAY,EAAE7B,IAAI,KAAK,KAAK,IAAIS,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,KAAK,EAAE;QAAE;QACnH,IAAGS,OAAO,CAACsB,cAAc,EAAC;UACxB,IAAG,CAAC,IAAI,CAACjE,2BAA2B,IAAK,IAAI,CAACpB,UAAU,IAAI,IAAI,CAACC,WAAY,EAAC;YAC5E8D,OAAO,CAACuB,KAAK,GAAG,KAAK;UACvB;UACA,IAAG,CAAC,IAAI,CAAClE,2BAA2B,IAAI,IAAI,CAACtB,mBAAmB,EAAC;YAC7D,IAAI,CAAC2E,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACpB,MAAM,CAACV,IAAI,CAAC,CAAC;YACjE,IAAI,CAACuD,2BAA2B,CAAC7B,MAAM,EAACT,OAAO,CAAC;UACpD;QACF;MACF;IAEF,CAAC,CAAC;IAEF,IAAI,CAACP,MAAM,CAACgC,oBAAoB,CAAC1B,GAAG,CAAC,CAACU,MAAM,EAAET,OAAO,KAAG;MACtD,IAAI,CAAC3C,2BAA2B,GAAG,KAAK;MACxC,IAAI,CAACpB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACF,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACjC,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACG,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAAC4B,cAAc,GAAG,KAAK,CAAC,CAAC;MAE7B,IAAI,CAACoB,gBAAgB,GAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAEM,GAAG,GAAG,CAAC;MACtE,IAAG1B,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,iBAAiB,EAAE,IAAI,CAACnC,gBAAgB,GAAG,CAAC;MAC/F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,eAAe,EAAE,IAAI,CAACnC,gBAAgB,GAAG,CAAC;MAC7F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,kBAAkB,EAAE,IAAI,CAACnC,gBAAgB,GAAG,CAAC;MAChG,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,cAAc,EAAE,IAAI,CAACnC,gBAAgB,GAAG,CAAC;MAC5F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,gBAAgB,EAAE,IAAI,CAACnC,gBAAgB,GAAG,CAAC;MAC9F,IAAG4C,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI,KAAK,oBAAoB,EAAE,IAAI,CAACnC,gBAAgB,GAAG,CAAC;IACpG,CAAC,CAAC;IAEF,IAAI,CAACqC,MAAM,CAACmC,gBAAgB,CAAC7B,GAAG,CAAC,CAACU,MAAM,EAACT,OAAO,KAAG;MACjD,IAAI,CAAChE,cAAc,GAAG,IAAI;IAC5B,CAAC,CAAC;IACA,IAAI,CAACyD,MAAM,CAACoC,aAAa,CAAC9B,GAAG,CAAC,CAAC+B,CAAC,EAAE9B,OAAO,KAAI;MAC3C,IAAI,CAACiC,qBAAqB,CAACjC,OAAO,CAAC;IACrC,CAAC,CAAC;IAEF;IACA;IACA;IAEA,IAAI,CAACP,MAAM,CAAC4C,YAAY,CAACtC,GAAG,CAAC,CAAC+B,CAAC,EAAC9B,OAAO,KAAG;MACxC,IAAI,CAACqC,YAAY,CAACrC,OAAO,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACuC,mBAAmB,EAAE;IAC5B;EACJ;EAEAC,eAAeA,CAACC,QAAgB;IAC9B,QAAOA,QAAQ,CAACC,WAAW,EAAE;MAE3B,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;MACA,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;MACA,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;MACA,KAAK,MAAM;QAAE;UACX,OAAO,IAAI;QACb;MACA,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;IACF;IACA,OAAO,KAAK;EACd;EAEAT,qBAAqBA,CAACjC,OAAY;IAEhC,IAAG;MACD,IAAGA,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC2C,KAAK,EACnC;QACE,IAAG,IAAI,CAACxL,WAAW,EAAC,CACpB;QACE,IAAIyL,KAAK,GAAG,CAAC;QACb,MAAMC,kBAAkB,GAAG7C,OAAO,EAAET,IAAI;QACxC,MAAMuD,WAAW,GAAGD,kBAAkB,GAAG,SAAS;QAClD,IAAI,CAAC1L,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,IAAI,CAAC;QAC/C,KAAI,MAAME,CAAC,IAAIhD,OAAO,CAAC2C,KAAK,EAAE;UAC5B,IAAIM,OAAO,GAAGjD,OAAO,CAACkD,QAAQ,CAAC3D,IAA8B;UAC7D,IAAG0D,OAAO,EACV;YACE,MAAMR,QAAQ,GAAGO,CAAC,EAAEzD,IAAI,CAAC4D,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;YACzC,IAAG,CAAC,IAAI,CAACZ,eAAe,CAACC,QAAQ,CAAC,EAClC;cACE,IAAG;gBACD,IAAI,CAAC7H,UAAU,CAACyI,UAAU,CAAC;kBAACpF,MAAM,EAAE,WAAW;kBAAE/F,MAAM,EAAE,QAAQ;kBAAEC,MAAM,EAAC,qBAAqB;kBAAEqH,IAAI,EAAC;gBAAS,CAAE,CAAC;gBAClH,IAAI,CAACrI,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;gBAChD9C,OAAO,CAACoC,QAAQ,CAAC,OAAO,CAAC;gBACzB;cACF,CAAC,CAAC,OAAMkB,CAAC,EACT;gBACEtD,OAAO,CAACoC,QAAQ,CAAC,OAAO,CAAC;gBACzB;cACF;YACF;YAEA,MAAMmB,YAAY,GAAG,IAAI,CAAC9D,MAAM,CAAC+D,QAAQ,CAACxD,OAAO,EAAET,IAAI,CAAC;YACxD,KAAK,MAAMkE,WAAW,IAAIF,YAAY,EAAC;cACrC,KAAK,MAAMG,QAAQ,IAAIH,YAAY,CAACE,WAAW,CAAC,EAAC;gBAC/C,IAAGC,QAAQ,KAAK,MAAM,IAAIH,YAAY,CAACE,WAAW,CAAC,CAACC,QAAQ,CAAC,KAAKV,CAAC,CAACzD,IAAI,EAAE;kBACxE,IAAI,CAAC3E,UAAU,CAACyI,UAAU,CAAC;oBAACpF,MAAM,EAAE,WAAW;oBAAE/F,MAAM,EAAE,QAAQ;oBAAEC,MAAM,EAAC,qBAAqB;oBAAEqH,IAAI,EAAC;kBAAS,CAAE,CAAC;kBAClH,IAAI,CAACrI,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;kBAChD9C,OAAO,CAACoC,QAAQ,CAAC,OAAO,CAAC;kBACzB;gBACF;cACF;YACF;YAEA,MAAMuB,MAAM,GAAG,IAAIC,UAAU,EAAE;YAC/BD,MAAM,CAACE,aAAa,CAACb,CAAC,CAAC;YACvBW,MAAM,CAACG,MAAM,GAAG,MAAK;cACnB,IAAI/E,IAAI,GAAG4E,MAAM,CAACI,MAAgB;cAElC,IAAIC,MAAM,GAAG;gBACXC,QAAQ,EAAEjB,CAAC,CAACzD,IAAI;gBAChB2E,YAAY,EAAEnF,IAAI;gBAClBoF,qBAAqB,EAAE,IAAI,CAACxK,aAAa,GAAG,IAAI,CAACA,aAAa,GAAG,IAAI,CAACwE,mBAAmB;gBACzF8E,OAAO,EAAEA,OAAO;gBAChBmB,QAAQ,EAAE,IAAI,CAACjG,mBAAmB,GAAG,IAAI,GAAG;eACb;cAEjC,IAAI,CAACpD,kBAAkB,CAACsJ,yBAAyB,CAACL,MAAM,CAAC,CACxDlG,SAAS,CAACiG,MAAM,IAAE;gBACjB3F,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAG2E,CAAC,CAACzD,IAAI,GAAG,4BAA4B,CAAC;gBAC7D,IAAGqD,KAAK,IAAI5C,OAAO,CAAC2C,KAAK,CAACtC,MAAM,EAChC;kBACEL,OAAO,CAACoC,QAAQ,CAAC,SAAS,EAAEpC,OAAO,CAAC2C,KAAK,CAAC2B,GAAG,CAACtB,CAAC,IAAG;oBAChD,MAAMH,kBAAkB,GAAG7C,OAAO,EAAET,IAAI;oBACxC,MAAMuD,WAAW,GAAGD,kBAAkB,GAAG,SAAS;oBAClD,IAAI,CAAC1L,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;oBAChD,OAAO;sBACLyB,IAAI,EAAEvB;qBACP;kBACH,CAAC,CAAC,CAAC;kBACH,IAAI,CAACpI,UAAU,CAACyI,UAAU,CAAC;oBAACpF,MAAM,EAAE,SAAS;oBAAE/F,MAAM,EAAE,QAAQ;oBAAEC,MAAM,EAAC,qBAAqB;oBAAEqH,IAAI,EAAC;kBAAS,CAAE,CAAC;gBAClH;gBACAoD,KAAK,EAAE;cACT,CAAC,CAAC;YACJ,CAAC;YACDe,MAAM,CAACa,OAAO,GAAIC,GAAG,IAAI;cACvBrG,OAAO,CAACC,GAAG,CAACoG,GAAG,CAAC;YAClB,CAAC;UACH;QACF;MACJ;IACF,CAAC,CAAC,OAAMnB,CAAC,EACT;MACElF,OAAO,CAACC,GAAG,CAACiF,CAAC,CAAC;MACd,MAAMT,kBAAkB,GAAG7C,OAAO,EAAET,IAAI;MACxC,MAAMuD,WAAW,GAAGD,kBAAkB,GAAG,SAAS;MAElD,IAAI,CAAC1L,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;MAChD9C,OAAO,CAACoC,QAAQ,CAAC,OAAO,CAAC;IAC3B;EACF;EAEAsC,cAAcA,CAAC1E,OAAW;IACxB,IAAGA,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC2E,SAAS,EACvC;MACE,IAAI/B,KAAK,GAAG,CAAC;MACb,IAAIgC,QAAQ,GAAG,EAAE;MACjB;MACE,IAAI5B,CAAC,GAAGhD,OAAO,CAAC2E,SAAS;MACzB,IAAI1B,OAAO,GAAGjD,OAAO,CAACkD,QAAQ,CAAC3D,IAA8B;MAC7D,IAAIsF,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC9E,OAAO,CAAC2E,SAAS,CAAC;MAC9C,IAAIP,QAAQ,GAAG,KAAK;MACpB,IAAIW,OAAO,GAAG,EAAE;MAChB,IAAG,IAAI,CAAC5G,mBAAmB,IAAI,CAAC,IAAI,CAACxE,aAAa,EAAC;QACjDoL,OAAO,GAAG,IAAI,CAAC5G,mBAAmB;QAClCiG,QAAQ,GAAG,IAAI;MACjB,CAAC,MAAI;QACHW,OAAO,GAAG,IAAI,CAACpL,aAAa;QAC5ByK,QAAQ,GAAG,KAAK;MAClB;MACA,IAAGnB,OAAO,EAAC;QACT,MAAMJ,kBAAkB,GAAG7C,OAAO,EAAET,IAAI;QACxC,MAAMuD,WAAW,GAAGD,kBAAkB,GAAG,SAAS;QAClD,IAAG,IAAI,CAAC1L,WAAW,EAAC;UAClB,IAAI,CAACA,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,IAAI,CAAC;QACjD;QACA,IAAI,IAAI,CAAClJ,UAAU,IAAI,IAAI,EAAC;UAC1B,IAAI,CAACmB,kBAAkB,CAACiK,6BAA6B,CAACD,OAAO,EAAC/B,CAAC,CAACzD,IAAI,EAAE0D,OAAO,EAACmB,QAAQ,CAAC,CAACa,IAAI,CAC1F3P,QAAQ,CAAC,MAAI;YACX,IAAG,IAAI,CAAC6B,WAAW,EAAC;cAClB,IAAI,CAACA,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;YAClD;UACF,CAAC,CAAC,CACH,CACAhF,SAAS,CAAEiG,MAAM,IAAG;YACnB,IAAImB,CAAC,GAAGnB,MAAM;YACd/D,OAAO,CAACoC,QAAQ,CAAC,SAAS,EAAEyC,MAAM,GAAGd,MAAM,CAAC;UAC9C,CAAC,CAAC;QACJ,CAAC,MAAI;UACH,IAAI,CAAChJ,kBAAkB,CAACoK,2BAA2B,CAACJ,OAAO,EAAE/B,CAAC,CAACzD,IAAI,EAAE0D,OAAO,EAAEmB,QAAQ,CAAC,CAACa,IAAI,CAC1F3P,QAAQ,CAAC,MAAI;YACX,IAAG,IAAI,CAAC6B,WAAW,EAAC;cAClB,IAAI,CAACA,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;YAClD;UACF,CAAC,CAAC,CACH,CACAhF,SAAS,CAAEiG,MAAM,IAAG;YACnB,IAAImB,CAAC,GAAGnB,MAAM;YACd/D,OAAO,CAACoC,QAAQ,CAAC,SAAS,EAAEyC,MAAM,GAAGd,MAAM,CAAC;UAC9C,CAAC,CAAC;QACJ;MAEF;MACF;IACF;EACF;EACAe,SAASA,CAACH,SAAc;IACtB,IAAIV,QAAQ,GAAGU,SAAS,CAACpF,IAAI;IAC7B,IAAI4D,KAAK,GAAGc,QAAQ,CAACd,KAAK,CAAC,GAAG,CAAC;IAC/B,IAAIiC,SAAS,GAAGjC,KAAK,CAACA,KAAK,CAAC9C,MAAM,GAAC,CAAC,CAAC;IACrC,QAAO+E,SAAS,CAAC1C,WAAW,EAAE;MAE5B,KAAK,KAAK;QAAE;UACV,OAAO,wBAAwB;QACjC;MACA,KAAK,KAAK;QAAE;UACV,OAAO,wBAAwB;QACjC;MACA,KAAK,KAAK;QAAE;UACV,OAAO,yBAAyB;QAClC;MACA,KAAK,MAAM;QAAE;UACX,OAAO,yBAAyB;QAClC;MACA,KAAK,KAAK;QAAE;UACV,OAAO,8BAA8B;QACvC;IACF;IACA,OAAO,EAAE;EACX;EAGAzB,YAAYA,CAACR,MAAM,EAACT,OAAO;IACzB,IAAIA,OAAO,CAACT,IAAI,KAAK,uBAAuB,IAAIS,OAAO,CAACqF,KAAK,KAAK,KAAK,EAAC;MACtE,IAAI,CAAC5F,MAAM,CAAC6F,QAAQ,CAAC,0BAA0B,EAAE,IAAI,CAACC,cAAc,CAAC;MACrE,IAAI,CAAC9F,MAAM,CAAC6F,QAAQ,CAAC,wBAAwB,EAAC,IAAI,CAACE,YAAY,CAAC;IAClE;IAEA,IAAIxF,OAAO,CAACT,IAAI,KAAK,uBAAuB,IAAIS,OAAO,CAACqF,KAAK,KAAK,IAAI,EAAC;MACrE,IAAI,CAAC5F,MAAM,CAAC6F,QAAQ,CAAC,0BAA0B,EAAE,EAAE,CAAC;MACpD,IAAI,CAAC7F,MAAM,CAAC6F,QAAQ,CAAC,wBAAwB,EAAC,EAAE,CAAC;IACnD;IACA,IAAItF,OAAO,CAACT,IAAI,KAAK,8CAA8C,IAAIS,OAAO,CAACqF,KAAK,KAAK,IAAI,EAAC;MAG5F,IAAG,IAAI,CAACrH,QAAQ,EAAC;QACf,IAAI,CAAC5C,sBAAsB,CAAC0D,GAAG,CAAC,IAAI,CAACd,QAAQ,EAAE,IAAI,CAAC,CAACF,SAAS,CAACiG,MAAM,IAAE;UACrE,IAAI,CAACjI,OAAO,GAAG,EAAE;UACjB,IAAI,CAACT,cAAc,CAACoK,OAAO,CAAC;YAAC9J,OAAO,EAAC,WAAW;YAAEE,cAAc,EAAE;UAAI,CAAC,CAAC,CAACiC,SAAS,CAAC4H,QAAQ,IAAE;YAC3FA,QAAQ,CAACC,KAAK,CAACC,OAAO,CAACC,OAAO,IAAG;cAC/B,IAAGA,OAAO,CAACtG,IAAI,EAAEmD,WAAW,EAAE,KAAKqB,MAAM,CAAC+B,uBAAuB,CAACD,OAAO,EAAEnD,WAAW,EAAE,EAAC;gBACvFqB,MAAM,CAAC+B,uBAAuB,CAACD,OAAO,GAAGA,OAAO,CAACE,EAAE;gBACnD,IAAI,CAACjK,OAAO,CAACkK,IAAI,CAACjC,MAAM,CAAC+B,uBAAuB,CAAC;gBACjD,IAAI,CAACrG,MAAM,CAAC6F,QAAQ,CAAC,2CAA2C,EAAC,IAAI,CAACxJ,OAAO,CAAC;cAChF;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;UACF,IAAI,CAAC2D,MAAM,CAAC6F,QAAQ,CAAC,2CAA2C,EAAC,IAAI,CAACxJ,OAAO,CAAC;QAChF,CAAC,CAAC;MACJ;IACF;EACF;EAEA0F,qBAAqBA,CAAEf,MAAM,EAAET,OAAO;IACpC,IAAI,CAAC/F,gBAAgB,GAAG,EAAE;IAC1B,MAAMgM,OAAO,GAAGjG,OAAO,EAAEmB,cAAc,EAAEC,YAAY,EAAE7B,IAAI;IAC3D,MAAM2G,OAAO,GAAGlG,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI;IAC3D,IAAI,CAACxD,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAIoK,aAAa,GAAmB;MAClCC,yBAAyB,EAAE,EAAE;MAC3BC,UAAU,EAAG1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACH,aAAa,CAAC,CAAe;MACzE4F,uBAAuB,EAAC,EAAE;MAC1BC,qBAAqB,EAAE,IAAI,CAACC,UAAU,IAAI;KAC3C;IACH,IAAI,CAACC,oBAAoB,CAACN,aAAa,CAAC;IACxC,IAAGA,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;MACxD,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACT,aAAa,CAAC;MACrEA,aAAa,CAACE,UAAU,CAACK,6BAA6B,GAAGC,eAAe;IAC1E;IACA,IAAI,IAAI,CAAChN,aAAa,EAAEwM,aAAa,CAACJ,EAAE,GAAG,IAAI,CAACpM,aAAa;IAC7DwM,aAAa,CAACE,UAAU,CAACrI,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAEjD,IAAGgC,OAAO,CAACsB,cAAc,EAAC;MACxB,IAAI,CAACvG,kBAAkB,CAAC8L,8BAA8B,CAACZ,OAAO,EAAEE,aAAa,CAAC,CAACrI,SAAS,CAACiG,MAAM,IAAG;QAChG,MAAM+C,MAAM,GAAG/C,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAK,CAAC,CAAC;QACjF,MAAMC,QAAQ,GAAGpD,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAK,CAAC,CAAC;QACnF,IAAI,CAACE,cAAc,CAACN,MAAM,CAAC;QAC3B,IAAI,CAACO,WAAW,CAACF,QAAQ,CAAC;QAC1B,IAAG,IAAI,CAAClN,gBAAgB,CAACoG,MAAM,KAAK,CAAC,EAAG;UACtC,IAAI,CAAChD,2BAA2B,GAAG,IAAI;UACvC,IAAI,CAACtB,mBAAmB,GAAG,KAAK;QAClC;QACA,IAAG,CAAC,IAAI,CAACA,mBAAmB,IAAI,IAAI,CAACsB,2BAA2B,EAAC;UAC/D,IAAG,CAAC,IAAI,CAACnB,WAAW,EAAC;YACnB;YACA,IAAI,CAACuD,MAAM,CAAC7G,QAAQ,EAAE;UACxB,CAAC,MACG;YACF;YACA;YACA,IAAG,IAAI,CAACqD,UAAU,EAAC;cACjB,IAAI,CAACA,UAAU,GAAG,KAAK;YACzB,CAAC,MACG;cACF;cACA,IAAI,CAACwD,MAAM,CAAC7G,QAAQ,EAAE;YACxB;UACF;QACF;QACA,IAAG,IAAI,CAACmD,mBAAmB,IAAI,IAAI,CAACG,WAAW,EAAC;UAC9C,IAAI,CAACyF,WAAW,EAAE;QACpB;MACF,CAAC,CAAC;IACJ;EACF;EAEA2F,wBAAwBA,CAACvI,IAA0B;IACjD,MAAMwI,WAAW,GAAG,IAAIC,IAAI,CAACzI,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,wBAAwB,CAAC,CAAC,CAACoB,WAAW,EAAE;IACpG,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,QAAQ,GAAG,EAAE,CAAC,CAAC;IAEnB;IACE,MAAMC,yBAAyB,GAAG7I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,2BAA2B,CAAC;IAC7F,MAAMwB,aAAa,GAAG9I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,eAAe,CAAC;IACrE,MAAMyB,WAAW,GAAG/I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,aAAa,CAAC;IACjE,MAAM0B,iBAAiB,GAAGhJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mBAAmB,CAAC;IAE7E,IAAG,EAAEkB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,IAAI,CAAC,EAAC;MACjDxI,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,2BAA2B,CAAC,GAAG,IAAI;MAClEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,eAAe,CAAC,GAAG,IAAI;MACtDtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,aAAa,CAAC,GAAG,IAAI;MACpDtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAC5D;IACA,IAAG,CAACkB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,IAAI,KAAKM,aAAa,KAAK,IAAI,EAAC;MAC1EH,SAAS,GAAG,IAAI;MAChBC,QAAQ,GAAG,iBAAiB;MAC5B,IAAI,CAACK,0BAA0B,CAACjJ,IAAI,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAChH;IACA,IAAG8I,aAAa,KAAK,KAAK,EAAC;MACzB9I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,aAAa,CAAC,GAAG,IAAI;MACpDtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAC5D;IACA,IAAGyB,WAAW,KAAK,KAAK,EAAC;MACvB/I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAC5D;IAEA;IACA,MAAMK,6BAA6B,GAAG3H,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,+BAA+B,CAAC;IACrG,IAAGK,6BAA6B,EAAC;MAC/B,IAAGA,6BAA6B,CAACuB,QAAQ,CAAC,MAAM,CAAC,IAAIvB,6BAA6B,CAACrG,MAAM,KAAK,CAAC,EAAC;QAC9FqH,SAAS,GAAG,IAAI;QAChBC,QAAQ,GAAG,kBAAkB;QAC7B,IAAI,CAACK,0BAA0B,CAACjJ,IAAI,EAAE,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;MAC3E;IACF;IAEA;IACA,MAAMmJ,8BAA8B,GAAGnJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,gCAAgC,CAAC;IACvG,MAAM8B,4BAA4B,GAAGpJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,8BAA8B,CAAC;IACnG,MAAM+B,0BAA0B,GAAGrJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,4BAA4B,CAAC;IAC/F,MAAMgC,kBAAkB,GAAGtJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,oBAAoB,CAAC;IAC/E,MAAMiC,qBAAqB,GAAGvJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,uBAAuB,CAAC;IAErF,IAAG6B,8BAA8B,EAAC;MAChCR,SAAS,GAAG,IAAI;MAChBC,QAAQ,GAAG,cAAc;MACzB5I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,8BAA8B,CAAC,GAAG,IAAI;MACrEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,4BAA4B,CAAC,GAAG,IAAI;MACnEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,oBAAoB,CAAC,GAAG,IAAI;MAC3DtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,uBAAuB,CAAC,GAAG,IAAI;MAC9DtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mCAAmC,CAAC,GAAG,IAAI;MAC1EtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,sCAAsC,CAAC,GAAG,IAAI;MAC7EtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,6CAA6C,CAAC,GAAG,IAAI;MACpFtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,8BAA8B,CAAC,GAAG,IAAI;MACrEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,oCAAoC,CAAC,GAAG,IAAI;IAC7E;IACA,IAAG8B,4BAA4B,EAAC;MAC9BT,SAAS,GAAG,IAAI;MAChBC,QAAQ,GAAG,cAAc;MACzB5I,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,4BAA4B,CAAC,GAAG,IAAI;MACnEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,oBAAoB,CAAC,GAAG,IAAI;MAC3DtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,uBAAuB,CAAC,GAAG,IAAI;MAC9DtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mCAAmC,CAAC,GAAG,IAAI;MAC1EtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,sCAAsC,CAAC,GAAG,IAAI;MAC7EtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,6CAA6C,CAAC,GAAG,IAAI;MACpFtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,8BAA8B,CAAC,GAAG,IAAI;MACrEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,oCAAoC,CAAC,GAAG,IAAI;IAC7E;IACA,IAAG8B,4BAA4B,KAAK,KAAK,EAAC;MACzC,IAAGE,kBAAkB,KAAK,KAAK,EAAC;QAC/BtJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,mCAAmC,CAAC,GAAG,IAAI;MAC3E;MACA,IAAGiC,qBAAqB,KAAK,KAAK,EAAC;QAClCvJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,sCAAsC,CAAC,GAAG,IAAI;MAC9E;IACD;IACA,IAAG+B,0BAA0B,KAAK,KAAK,EAAC;MACtCrJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,6CAA6C,CAAC,GAAG,IAAI;MACpFtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,8BAA8B,CAAC,GAAG,IAAI;MACrEtH,IAAI,CAACE,cAAc,CAACoH,UAAU,CAAC,oCAAoC,CAAC,GAAG,IAAI;IAC7E;IACA,IAAG+B,0BAA0B,EAAC;MAC5BV,SAAS,GAAG,IAAI;MAChBC,QAAQ,GAAG,cAAc;IAC3B;IAEA,IAAID,SAAS,KAAK,IAAI,IAAIC,QAAQ,KAAK,cAAc,EAAC;MACpD,IAAI,CAACK,0BAA0B,CAACjJ,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC3D;EAEJ;EAEAiJ,0BAA0BA,CAACjJ,IAA0B,EAAEwJ,eAAyB;IAC9E,KAAK,MAAMC,QAAQ,IAAIzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,EAAC;MAEpD;MACA,IAAGkC,eAAe,CAACN,QAAQ,CAAC,eAAe,CAAC,EAAC;QAC3C,IAAGO,QAAQ,KAAK,kBAAkB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACnF,IAAGA,QAAQ,KAAK,2BAA2B,IAAIzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,IAAIzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEC,aAAa,IAAI,IAAI,EAAE1J,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACC,aAAa,GAAG,IAAI;QAC/N,IAAGD,QAAQ,KAAK,8CAA8C,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC/G,IAAGA,QAAQ,KAAK,2CAA2C,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC5G,IAAGA,QAAQ,KAAK,sBAAsB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;MACzF;MAEA;MACA,IAAGD,eAAe,CAACN,QAAQ,CAAC,kBAAkB,CAAC,EAAC;QAC9C,IAAGO,QAAQ,KAAK,+BAA+B,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;MAClG;MAEA;MACA,IAAGD,eAAe,CAACN,QAAQ,CAAC,cAAc,CAAC,EAAC;QAC1C,IAAGO,QAAQ,KAAK,gCAAgC,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACjG,IAAGA,QAAQ,KAAK,8BAA8B,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC/F,IAAGA,QAAQ,KAAK,4BAA4B,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC7F,IAAGA,QAAQ,KAAK,6CAA6C,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC9G,IAAGA,QAAQ,KAAK,8BAA8B,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC/F,IAAGA,QAAQ,KAAK,oCAAoC,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACrG,IAAGA,QAAQ,KAAK,uBAAuB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACxF,IAAGA,QAAQ,KAAK,sCAAsC,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACvG,IAAGA,QAAQ,KAAK,oBAAoB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACrF,IAAGA,QAAQ,KAAK,mCAAmC,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;MACtG;MAEA;MACA,IAAGD,eAAe,CAACN,QAAQ,CAAC,gBAAgB,CAAC,EAAC;QAC5C,IAAGO,QAAQ,KAAK,8BAA8B,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC/F,IAAGA,QAAQ,KAAK,0BAA0B,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QAC3F,IAAGA,QAAQ,KAAK,kBAAkB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACnF,IAAGA,QAAQ,KAAK,oBAAoB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACrF,IAAGA,QAAQ,KAAK,wBAAwB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACzF,IAAGA,QAAQ,KAAK,kBAAkB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACnF,IAAGA,QAAQ,KAAK,uBAAuB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACxF,IAAGA,QAAQ,KAAK,mBAAmB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;QACpF,IAAGA,QAAQ,KAAK,uBAAuB,EAAEzJ,IAAI,CAACE,cAAc,CAACoH,UAAU,CAACmC,QAAQ,CAAC,GAAG,IAAI;MAC1F;IAEF;EACF;EAEAlG,2BAA2BA,CAAC7B,MAAM,EAAET,OAAO;IACzC,IAAI,CAAC/F,gBAAgB,GAAG,EAAE;IAC1B,MAAMgM,OAAO,GAAGjG,OAAO,EAAEmB,cAAc,EAAEC,YAAY,EAAE7B,IAAI;IAC3D,MAAM2G,OAAO,GAAGlG,OAAO,EAAEqB,cAAc,EAAED,YAAY,EAAE7B,IAAI;IAC3D,IAAI,CAACxD,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAIoK,aAAa,GAAmB;MAClCC,yBAAyB,EAAE,EAAE;MAC3BC,UAAU,EAAG1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACH,aAAa,CAAC,CAAe;MACzE4F,uBAAuB,EAAC,EAAE;MAC1BC,qBAAqB,EAAE,IAAI,CAACC,UAAU,IAAI;KAC3C;IACH,IAAI,CAACC,oBAAoB,CAACN,aAAa,CAAC;IACxC,IAAGA,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;MACxD,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACT,aAAa,CAAC;MACrEA,aAAa,CAACE,UAAU,CAACK,6BAA6B,GAAGC,eAAe;IAC1E;IACA,IAAI,IAAI,CAACxI,mBAAmB,EAAEgI,aAAa,CAACJ,EAAE,GAAG,IAAI,CAAC5H,mBAAmB;IACzEgI,aAAa,CAACE,UAAU,CAACrI,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAEjD,IAAGgC,OAAO,CAACsB,cAAc,EAAC;MACxB,IAAI,CAAChG,wBAAwB,CAACuL,8BAA8B,CAACZ,OAAO,EAAEE,aAAa,CAAC,CAACrI,SAAS,CAACiG,MAAM,IAAG;QACtG,MAAM+C,MAAM,GAAG/C,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAK,CAAC,CAAC;QACjF,MAAMC,QAAQ,GAAGpD,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAK,CAAC,CAAC;QACnF,IAAI,CAACE,cAAc,CAACN,MAAM,CAAC;QAC3B,IAAI,CAACO,WAAW,CAACF,QAAQ,CAAC;QAC1B,IAAG,IAAI,CAAClN,gBAAgB,CAACoG,MAAM,KAAK,CAAC,EAAG;UACtC,IAAI,CAAChD,2BAA2B,GAAG,IAAI;UACvC,IAAI,CAACtB,mBAAmB,GAAG,KAAK;QAClC;QACA,IAAG,CAAC,IAAI,CAACA,mBAAmB,IAAI,IAAI,CAACsB,2BAA2B,EAAC;UAC/D,IAAG,CAAC,IAAI,CAACnB,WAAW,EAAC;YACnB;YACA,IAAI,CAACuD,MAAM,CAAC7G,QAAQ,EAAE;UACxB,CAAC,MACG;YACF;YACA;YACA,IAAG,IAAI,CAACqD,UAAU,EAAC;cACjB,IAAI,CAACA,UAAU,GAAG,KAAK;YACzB,CAAC,MACG;cACF;cACA,IAAI,CAACwD,MAAM,CAAC7G,QAAQ,EAAE;YACxB;UACF;QACF;QACA,IAAG,IAAI,CAACmD,mBAAmB,IAAI,IAAI,CAACG,WAAW,EAAC;UAC9C,IAAI,CAACyF,WAAW,EAAE;QACpB;MACF,CAAC,CAAC;IACJ;EACF;EAEAxC,aAAaA,CAAA;IACX;IACA;IACA,IAAI,CAACD,YAAY,CAACwJ,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC/C,OAAO,CAACgD,OAAO,IAAG;MACpD,IAAI,CAAClJ,cAAc,CAACgJ,KAAK,CAAC9C,OAAO,CAACiD,IAAI,IAAE;QACtC,IAAGD,OAAO,CAACrJ,IAAI,KAAKsJ,IAAI,CAACtJ,IAAI,EAAEqJ,OAAO,CAACD,QAAQ,GAAGE,IAAI,CAACF,QAAQ;MACjE,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF/S,UAAU,CAAC0J,WAAW,CAAC,UAAU,EAAE;MACjCC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE;KACP,CAAC;IACF,IAAI,CAACC,MAAM,GAAG,IAAI9K,KAAK,CAAC,IAAI,CAACuK,YAAY,CAAC;IAC1C,IAAI,CAACO,MAAM,CAACE,UAAU,CAACtK,SAAS,CAAC;IACjC;IACA,MAAMwK,SAAS,GAAG,IAAIrK,SAAS,EAAE;IAC7B,IAAI,CAACiK,MAAM,CAACK,cAAc,CAACC,GAAG,CAAC,UAAUN,MAAM,EAAEO,OAAO;MACtD;MACA,IAAIC,GAAG,GAAGJ,SAAS,CAACK,QAAQ,CAACF,OAAO,CAACG,IAAI,CAAC;MAC1C;MACAF,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC;MACtBH,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,EAAEH,GAAG,CAACI,MAAM,GAAG,CAAC,CAAC;MACtC;MACAL,OAAO,CAACM,IAAI,GAAGL,GAAG;IACxB,CAAC,CAAC;IAEF;IACA;IACA;IACA,IAAI,CAACR,MAAM,CAAC4C,YAAY,CAACtC,GAAG,CAAC,CAAC+B,CAAC,EAAC9B,OAAO,KAAG;MACxC,IAAI,CAACqC,YAAY,CAACrC,OAAO,CAAC;IAC5B,CAAC,CAAC;EACJ;EACAqC,YAAYA,CAACrC,OAAY;IACvB;IACA,IAAIrG,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIsK,QAAQ,GAAGjE,OAAO,CAACiE,QAAQ;IAC/B,IAAIhB,OAAO,GAAIjD,OAAO,CAACT,IAA8B;IACrD,MAAMsD,kBAAkB,GAAG7C,OAAO,EAAET,IAAI;IACxC,MAAMuD,WAAW,GAAGD,kBAAkB,GAAG,SAAS;IAClD,IAAIkC,OAAO,GAAG,EAAE;IAChB,IAAIX,QAAQ,GAAG,KAAK;IACpB,IAAG,IAAI,CAACjG,mBAAmB,IAAI,CAAC,IAAI,CAACxE,aAAa,EAAC;MACjDoL,OAAO,GAAG,IAAI,CAAC5G,mBAAmB;MAClCiG,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAI;MACHW,OAAO,GAAG,IAAI,CAACpL,aAAa;MAC5ByK,QAAQ,GAAG,KAAK;IAClB;IACA,IAAGpE,OAAO,CAACiE,QAAQ,IAAI,IAAI,EAAC;MAAE;MAC5B,IAAGhB,OAAO,IAAI8B,OAAO,IAAId,QAAQ,EACjC;QACE,IAAG,IAAI,CAAC9M,WAAW,EAAC;UAClB,IAAI,CAACA,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,IAAI,CAAC;QACjD;QACA,IAAI,CAAC/H,kBAAkB,CAAC+N,+BAA+B,CAAC/D,OAAO,EAAEd,QAAQ,EAAEhB,OAAO,EAAEmB,QAAQ,CAAC,CAACa,IAAI,CAChG3P,QAAQ,CAAC,MAAI;UACX,IAAG,IAAI,CAAC6B,WAAW,EAAC;YAClB,IAAI,CAACA,WAAW,CAAC4L,WAAW,CAACD,WAAW,EAAE,KAAK,CAAC;UAClD;QACF,CAAC,CAAC,CACH,CACAhF,SAAS,CAAEiG,MAAM,IAAG;UACnB3F,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;UAC1B2B,OAAO,CAACoC,QAAQ,CAAC,SAAS,CAAC;UAC3B,IAAG,IAAI,CAACzI,aAAa,EAAC;YACpB,IAAI,CAACoI,WAAW,EAAE,CAACG,KAAK,CAAEC,KAAK,IAAE;cAC/B/D,OAAO,CAACC,GAAG,CAAC8D,KAAK,CAAC;YACpB,CAAE,CAAC;UACL;QACF,CAAE,CAAC;MACL;IACF,CAAC,MACG;MAAE;MACJ,IAAI4G,WAAW,GAAG,EAAE;MACpB,IAAG/I,OAAO,EAAEqF,KAAK,EAAC;QAChB,KAAK,MAAM1C,KAAK,IAAI3C,OAAO,CAACqF,KAAK,EAAC;UAChC,KAAK,MAAM3B,QAAQ,IAAI1D,OAAO,CAACqF,KAAK,CAAC1C,KAAK,CAAC,EAAC;YAC1C,IAAGe,QAAQ,KAAK,MAAM,EAAC;cACrBO,QAAQ,GAAGjE,OAAO,CAACqF,KAAK,CAAC1C,KAAK,CAAC,CAACe,QAAQ,CAAC;cACzCqF,WAAW,CAAC/C,IAAI,CAAC/B,QAAQ,CAAC;YAC5B;UACF;QACF;QAEA,IAAI8E,WAAW,CAAC1I,MAAM,GAAG,CAAC,EAAE;UAC1B,IAAI,CAACtF,kBAAkB,CAACiO,wCAAwC,CAACjE,OAAO,EAAEgE,WAAW,EAAE9F,OAAO,EAAEmB,QAAQ,CAAC,CAACtG,SAAS,CAACiG,MAAM,IAAE;YAC1H/D,OAAO,CAACoC,QAAQ,CAAC,SAAS,CAAC;YAC3B,IAAG,IAAI,CAACzI,aAAa,EAAC;cACpB,IAAI,CAACoI,WAAW,EAAE,CAACG,KAAK,CAAEC,KAAK,IAAE;gBAC/B/D,OAAO,CAACC,GAAG,CAAC8D,KAAK,CAAC;cACpB,CAAE,CAAC;YACL;UACF,CAAC,CAAC;QACJ;MACF;IACF;EACF;EACA8G,UAAUA,CAACC,OAAe;IACxB,OAAOrU,UAAU,CAACsU,gBAAgB,CAACD,OAAO,EAAC,YAAY,CAAC;EAC1D;EACA3G,mBAAmBA,CAAA;IACjB,IAAI6G,UAAU,GAAG,EAAE;IACnB,IAAI,CAAC9N,wBAAwB,CAAC+N,SAAS,CAAC,IAAI,CAAClL,mBAAmB,CAAC,CAACL,SAAS,CAACiG,MAAM,IAAE;MAClF,IAAI,CAACtE,MAAM,CAACV,IAAI,GAAGgF,MAAM,CAAC9E,cAAc,CAACoH,UAAU;MACnD,KAAI,MAAMiD,GAAG,IAAIvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,EAAC;QAChD,IAAGtC,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,EAAC;UAAE;UACzC,IAAGA,GAAG,KAAK,0BAA0B,EAAC;YACpC,IAAI,CAAC/D,cAAc,GAAG,IAAI,CAAC0D,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC;YAC5EvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC;UAChG;UACA,IAAGA,GAAG,KAAK,wBAAwB,EAAE;YACnC,IAAI,CAAC9D,YAAY,GAAG,IAAI,CAACyD,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC;YAC1EvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC;UAChG;UAEA,IAAGA,GAAG,KAAK,+BAA+B,EAAC;YACzCF,UAAU,GAAIrF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC;UACrD;UAEA,IAAG,IAAI,CAAChM,aAAa,CAAC2K,QAAQ,CAACqB,GAAG,CAAC,EAAC;YAClC,IAAGA,GAAG,KAAK,0BAA0B,EAAE;cACrCvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,gDAAgD,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,gDAAgD,CAAC,CAAC;cAClMvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,8CAA8C,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,8CAA8C,CAAC,CAAC;YAChM;YACA,IAAGA,GAAG,KAAK,8BAA8B,EAAC;cACxCvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,yCAAyC,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,yCAAyC,CAAC,CAAC;cACpLvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,uCAAuC,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;YAClL;YACA,IAAGA,GAAG,KAAK,8BAA8B,IAAIA,GAAG,KAAK,0BAA0B,EAAC;cAAE;cAChFvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,uDAAuD,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,uDAAuD,CAAC,CAAC;cAChNvF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,qDAAqD,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAACiD,GAAG,CAAC,CAAC,qDAAqD,CAAC,CAAC;YAC9M;UACF;QACF;MACF;MAEA,IAAI,CAAChC,wBAAwB,CAACvD,MAAM,CAAC;MACrC,IAAI,CAACtE,MAAM,CAACV,IAAI,GAAG4B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACkD,MAAM,CAAC9E,cAAc,CAACoH,UAAU,CAAC,CAAC;MAC/E;MACA,IAAI,CAACkD,OAAO,CAACH,UAAU,CAAC;MACxB,IAAI,CAACjS,WAAW,GAAG,IAAI,CAACsI,MAAM;MAC9B,IAAI,CAAClC,SAAS,GAAG,KAAK;IAGxB,CAAC,CAAC;EACJ;EAEA6B,aAAaA,CAAA;IACX,IAAIgK,UAAU,GAAG,EAAE;IAEnB,IAAG,IAAI,CAACzP,aAAa,IAAI,IAAI,CAACE,SAAS,EAAC;MAAC;MACvC,IAAI2P,UAAU;MACd,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAI,IAAI,CAAC5P,SAAS,EAAC;QACjB2P,UAAU,GAAG,IAAI,CAAChO,cAAc,CAACsD,GAAG,CAAC,IAAI,CAACjF,SAAS,CAAC;QACpD4P,QAAQ,GAAG,qBAAqB;MAClC,CAAC,MAAI;QACHD,UAAU,GAAG,IAAI,CAACzO,kBAAkB,CAAC+D,GAAG,CAAC,IAAI,CAACnF,aAAa,CAAC;QAC5D8P,QAAQ,GAAG,gBAAgB;MAC7B;MACAD,UAAU,CAAC1L,SAAS,CAACiG,MAAM,IAAE;QAC3B,IAAI,CAACtE,MAAM,CAACV,IAAI,GAAGgF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU;QAC9C,KAAI,MAAMiD,GAAG,IAAIvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,EAAC;UAC3C,IAAGtC,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,EAAC;YAAE;YACpC,IAAGA,GAAG,KAAK,0BAA0B,EAAC;cACpC,IAAI,CAAC/D,cAAc,GAAG,IAAI,CAAC0D,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC;cACvEvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC;YACtF;YACA,IAAGA,GAAG,KAAK,wBAAwB,EAAE;cACnC,IAAI,CAAC9D,YAAY,GAAG,IAAI,CAACyD,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC;cACrElL,OAAO,CAACC,GAAG,CAAC,IAAI,CAACmH,YAAY,CAAC;cAC9BzB,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC;YACtF;YAEA,IAAGA,GAAG,KAAK,+BAA+B,EAAC;cACzCF,UAAU,GAAIrF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC;YAChD;YAEA,IAAG,IAAI,CAAChM,aAAa,CAAC2K,QAAQ,CAACqB,GAAG,CAAC,EAAC;cAClC,IAAGA,GAAG,KAAK,0BAA0B,EAAE;gBACrCvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,gDAAgD,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,gDAAgD,CAAC,CAAC;gBACxLvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,8CAA8C,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,8CAA8C,CAAC,CAAC;cACtL;cACA,IAAGA,GAAG,KAAK,8BAA8B,EAAC;gBACxCvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,yCAAyC,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,yCAAyC,CAAC,CAAC;gBAC1KvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,uCAAuC,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;cACxK;cACA,IAAGA,GAAG,KAAK,8BAA8B,IAAIA,GAAG,KAAK,0BAA0B,EAAC;gBAAE;gBAChFvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,uDAAuD,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,uDAAuD,CAAC,CAAC;gBACtMvF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,qDAAqD,CAAC,GAAG,IAAI,CAACL,UAAU,CAAClF,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAACiD,GAAG,CAAC,CAAC,qDAAqD,CAAC,CAAC;cACpM;YACF;UACF;QACF;QACA,IAAI,CAAC7J,MAAM,CAACV,IAAI,GAAG4B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACkD,MAAM,CAAC0F,QAAQ,CAAC,CAACpD,UAAU,CAAC,CAAC;QAC1E,IAAI,CAACkD,OAAO,CAACH,UAAU,CAAC;QAExB;QACA,IAAI,IAAI,CAAClR,MAAM,KAAK,MAAM,EAAE;UAC1B,IAAI,CAACuH,MAAM,CAACiK,IAAI,GAAG,SAAS;UAC5B,IAAI,CAAC3N,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAACG,WAAW,GAAG,KAAK,CAAC,CAAC;QAC5B;QACA,IAAI,CAACuD,MAAM,CAACsD,WAAW,CAAC,0BAA0B,EAAE,KAAK,CAAC;QAC1D,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,2CAA2C,EAAE,KAAK,CAAC;QAC3E,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,kEAAkE,EAAE,KAAK,CAAC;QAClG,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,iDAAiD,EAAE,KAAK,CAAC;QACjF,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,sDAAsD,EAAE,KAAK,CAAC;QACtF,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,8CAA8C,EAAE,KAAK,CAAC;QAC9E,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,0DAA0D,EAAE,KAAK,CAAC;QAC1F,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,qCAAqC,EAAE,KAAK,CAAC;QACrE,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,6BAA6B,EAAE,KAAK,CAAC;QAE7D,IAAI,CAAC5L,WAAW,GAAG,IAAI,CAACsI,MAAM;QAC9B,IAAI,CAAClC,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ,CAAC,MACG;MAAC;MACH,IAAI,CAACoM,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAC5O,kBAAkB,CAAC6O,6BAA6B,CAAC,IAAI,CAAC5L,QAAQ,CAAC,CAACF,SAAS,CAACiG,MAAM,IAAE;MACrF,IAAI,CAACtE,MAAM,CAAC6F,QAAQ,CAAC,0BAA0B,EAAE,IAAI,CAAC2D,UAAU,CAAClF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5E,IAAI,CAACtE,MAAM,CAAC6F,QAAQ,CAAC,wBAAwB,EAAC,IAAI,CAAC2D,UAAU,CAAClF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACzE,IAAI,CAACyB,YAAY,GAAG,IAAI,CAACyD,UAAU,CAAClF,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,IAAI,CAACwB,cAAc,GAAG,IAAI,CAAC0D,UAAU,CAAClF,MAAM,CAAC,CAAC,CAAC,CAAC;MAEhD,IAAI,CAACtE,MAAM,CAACsD,WAAW,CAAC,0BAA0B,EAAE,KAAK,CAAC;MAC1D,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,2CAA2C,EAAE,KAAK,CAAC;MAC3E,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,kEAAkE,EAAE,KAAK,CAAC;MAClG,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,iDAAiD,EAAE,KAAK,CAAC;MACjF,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,sDAAsD,EAAE,KAAK,CAAC;MACtF,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,8CAA8C,EAAE,KAAK,CAAC;MAC9E,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,0DAA0D,EAAE,KAAK,CAAC;MAC1F,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,qCAAqC,EAAE,KAAK,CAAC;MACrE,IAAI,CAACtD,MAAM,CAACsD,WAAW,CAAC,6BAA6B,EAAE,KAAK,CAAC;MAE7D,IAAI,CAAC5L,WAAW,GAAG,IAAI,CAACsI,MAAM;MAC9B,IAAI,CAAClC,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC;EACJ;EAEAgM,OAAOA,CAACH,UAAW;IACjB;IACA,IAAGA,UAAU,EAAC;MACZ,IAAG,IAAI,CAAC3J,MAAM,CAACV,IAAI,CAAC,+BAA+B,CAAC,EAAC;QACnD,IAAI8K,mBAAmB,GAAG,EAAE;QAC5B,IAAI,CAAChP,cAAc,CAAC4K,OAAO,CAAC;UAAC5J,cAAc,EAAE;QAAG,CAAC,CAAC,CAACiC,SAAS,CAACiG,MAAM,IAAG;UACpEA,MAAM,CAAC4B,KAAK,CAACC,OAAO,CAACgD,OAAO,IAAG;YAC7BQ,UAAU,CAACxD,OAAO,CAACkE,QAAQ,IAAG;cAC5B,IAAGlB,OAAO,CAAC7C,EAAE,KAAK+D,QAAQ,EAAC;gBACzBD,mBAAmB,CAAC7D,IAAI,CAAC4C,OAAO,CAACrJ,IAAI,CAAC;cACxC;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;UACF,IAAGsK,mBAAmB,CAACxJ,MAAM,GAAE,CAAC,EAAE,IAAI,CAACZ,MAAM,CAAC6F,QAAQ,CAAC,+BAA+B,EAAEuE,mBAAmB,CAAC,CAAC,KACxG,IAAI,CAACpK,MAAM,CAAC6F,QAAQ,CAAC,+BAA+B,EAAE8D,UAAU,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC;MACJ;IACF;EACF;EAEAhC,cAAcA,CAACN,MAA2B;IACxC,IAAI,CAAC7M,gBAAgB,GAAG,EAAE;IAC1B,KAAK,MAAMkI,KAAK,IAAI2E,MAAM,EAAC;MACzB,KAAI,MAAMiD,SAAS,IAAIjD,MAAM,CAAC3E,KAAK,CAAC,EAAC;QACnC,IAAG4H,SAAS,KAAK,cAAc,IAAIjD,MAAM,CAAC3E,KAAK,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAC;UAAE;UACnE,IAAI,CAAClI,gBAAgB,CAAC+L,IAAI,CAACc,MAAM,CAAC3E,KAAK,CAAC,CAAC4H,SAAS,CAAC,CAAC;QACtD;MACF;IACF;IACA,IAAI,IAAI,CAAC9P,gBAAgB,CAACoG,MAAM,GAAG,CAAC,EAAE,IAAI,CAACtE,mBAAmB,GAAG,IAAI,MAChE,IAAI,CAACA,mBAAmB,GAAG,KAAK;EACvC;EAEAsL,WAAWA,CAACF,QAA6B;IACvC,IAAI,CAAC/M,kBAAkB,GAAG,EAAE;IAC5B,KAAK,MAAM4P,OAAO,IAAI7C,QAAQ,EAAC;MAC7B,KAAI,MAAM8C,WAAW,IAAI9C,QAAQ,CAAC6C,OAAO,CAAC,EAAC;QACzC,IAAGC,WAAW,KAAK,cAAc,EAAC;UAChC,IAAI,CAAC7P,kBAAkB,CAAC4L,IAAI,CAACmB,QAAQ,CAAC6C,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;QAC9D;MACF;IACF;IACA,IAAI,IAAI,CAAC7P,kBAAkB,CAACiG,MAAM,GAAG,CAAC,EAAE,IAAI,CAACnE,WAAW,GAAG,IAAI,MAC1D,IAAI,CAACA,WAAW,GAAG,KAAK;EAC/B;EAEA6E,cAAcA,CAACN,MAAM;IACnB;IACA,IAAG,IAAI,CAAC/C,eAAe,IAAI,CAAC,EAAC;MAC3B,IAAIyI,aAAa,GAAmB;QAClC+D,cAAc,EAAE,IAAI,CAAC/N,cAAc,CAACC,SAAS;QAC7C6B,MAAM,EAAE,WAAW;QACnBmI,yBAAyB,EAAE,IAAI,CAAC1M,eAAe,EAAE0M,yBAAyB,IAAE,EAAE;QAC9EC,UAAU,EAAE1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACH,aAAa,CAAC,CAAe;QACxE4F,uBAAuB,EAAC,EAAE;QAC1B6D,UAAU,EAAE,IAAI,CAACzQ,eAAe,EAAEyQ,UAAU;QAC5C5D,qBAAqB,EAAE,IAAI,CAACC,UAAU,IAAI;OAC3C;MACD,IAAI,CAACC,oBAAoB,CAACN,aAAa,CAAC;MACxC,IAAGA,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;QACxD,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACT,aAAa,CAAC;QACrEA,aAAa,CAACE,UAAU,CAACK,6BAA6B,GAAGC,eAAe;MAC1E;MACA,IAAI,IAAI,CAAChN,aAAa,EAAEwM,aAAa,CAACJ,EAAE,GAAG,IAAI,CAACpM,aAAa;MAC7DwM,aAAa,CAACE,UAAU,CAACrI,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACjD,IAAI,CAACpD,UAAU,CAACyI,UAAU,CAAC;QAACnL,MAAM,EAAE,QAAQ;QAACkS,KAAK,EAAE,iCAAiC;QACrFjK,IAAI,EAAE,2KAA2K;QACjLX,IAAI,EAAC;MAAS,CAAC,EACd6K,OAAO,IAAI;QACV,IAAGA,OAAO,EAAE;UACV,IAAI,CAACtP,kBAAkB,CAACuP,MAAM,CAACnE,aAAa,CAAC,CAACrI,SAAS,CAACiG,MAAM,IAAE;YAC9D,IAAI,CAACqD,cAAc,CAACrD,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAAC;YACnD,IAAG,IAAI,CAAC7M,gBAAgB,CAACoG,MAAM,KAAK,CAAC,EAAC;cAAE;cACtCI,MAAM,CAACK,WAAW,GAAG,KAAK,CAAC,CAAC;cAC5B,IAAG,CAAC,IAAI,CAACnH,aAAa,EAAE,IAAI,CAACA,aAAa,GAAGoK,MAAM,CAACpK,aAAa;cACjE,IAAI,CAACwB,MAAM,CAACoP,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;gBAAE1M,WAAW,EAAE;kBAAC2M,aAAa,EAAE,IAAI,CAAC7Q,aAAa;kBAAE8Q,QAAQ,EAAE,IAAI,CAACzM,QAAQ;kBAAE9F,MAAM,EAAE,MAAM;kBAAE+F,MAAM,EAAC,WAAW;kBAAE9F,MAAM,EAAE,IAAI,CAACA;gBAAM;cAAC,CAAC,CAAC;cAChL,IAAI,CAACD,MAAM,GAAG,MAAM;cACpB,IAAI,CAAC+F,MAAM,GAAG,WAAW;cACzB,IAAI,CAACW,SAAS,EAAE;cAChB,IAAI,CAAC7C,mBAAmB,GAAG,KAAK;cAChC,IAAI,CAACnB,UAAU,CAACyI,UAAU,CAAC;gBAACpF,MAAM;gBAAwB9F,MAAM,EAAC,uBAAuB;gBAAEqH,IAAI,EAAC;cAAS,CAAC,CAAC;YAC5G,CAAC,MACG;cAAE;cACJiB,MAAM,CAACK,WAAW,GAAG,KAAK;cAC1B,IAAI,CAAC/E,mBAAmB,GAAG,IAAI;cAC/B,IAAI,CAACnB,UAAU,CAACyI,UAAU,CAAC;gBAACpF,MAAM;gBAAuB9F,MAAM,EAAC,uBAAuB;gBAAEqH,IAAI,EAAC;cAAS,CAAC,CAAC;YAC3G;UACF,CAAC,CAAC;QACJ;MACA,CAAC,CAAC;IACN,CAAC,MACG;MACFiB,MAAM,CAACK,WAAW,GAAG,KAAK;MAC1B,IAAI,CAAClG,UAAU,CAACyI,UAAU,CAAC;QAAC7D,IAAI,EAAC;MAAgC,CAAC,CAAC;IACrE;EAEF;EAEAkL,qBAAqBA,CAACC,IAAW;IAC/B,IAAGA,IAAI,EAAC;MACN;MACA;MACA,MAAMC,aAAa,GAAG9U,cAAc,CAAC6U,IAAI,EAAE,IAAI,CAAChN,gBAAgB,CAAC;MACjE,OAAOiN,aAAa,CAACC,WAAW,EAAE,CAAC,CAAC;IACtC,CAAC,MACI,OAAOF,IAAI;EAClB;EAEAlE,oBAAoBA,CAACxH,cAA8B;IACjD,IAAGA,cAAc,CAACoH,UAAU,CAACK,6BAA6B,EAAC;MACzD,MAAMoE,gBAAgB,GAAG7L,cAAc,CAACoH,UAAU,CAACK,6BAA6B;MAChFoE,gBAAgB,CAAClF,OAAO,CAACkE,QAAQ,IAAG;QAClC,IAAGA,QAAQ,KAAI,kBAAkB,IAAI,0BAA0B,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAAC0E,wBAAwB,KAAK,IAAI,EAAE;UAC3J9L,cAAc,CAACoH,UAAU,CAAC0E,wBAAwB,CAACC,4CAA4C,GAAG,IAAI,CAACN,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAAC0E,wBAAwB,CAACC,4CAA4C,CAAC;UAC7N/L,cAAc,CAACoH,UAAU,CAAC0E,wBAAwB,CAACE,8CAA8C,GAAG,IAAI,CAACP,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAAC0E,wBAAwB,CAACE,8CAA8C,CAAC;QACnO;QACA,IAAGnB,QAAQ,KAAI,0CAA0C,IAAI,uBAAuB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAAC6E,qBAAqB,KAAK,IAAI,EAAG;UAC9KjM,cAAc,CAACoH,UAAU,CAAC6E,qBAAqB,CAACC,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAAC6E,qBAAqB,CAACC,mDAAmD,CAAC;UACrOlM,cAAc,CAACoH,UAAU,CAAC6E,qBAAqB,CAACE,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAAC6E,qBAAqB,CAACE,qDAAqD,CAAC;QAC3O;QACA,IAAGtB,QAAQ,KAAI,gCAAgC,IAAI,8BAA8B,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACgF,4BAA4B,KAAK,IAAI,EAAE;UACjLpM,cAAc,CAACoH,UAAU,CAACgF,4BAA4B,CAACC,qCAAqC,GAAG,IAAI,CAACZ,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACgF,4BAA4B,CAACC,qCAAqC,CAAC;UACvNrM,cAAc,CAACoH,UAAU,CAACgF,4BAA4B,CAACE,uCAAuC,GAAG,IAAI,CAACb,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACgF,4BAA4B,CAACE,uCAAuC,CAAC;QAC7N;QACA,IAAGzB,QAAQ,KAAI,mBAAmB,IAAI,mBAAmB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACmF,iBAAiB,KAAK,IAAI,EAAC;UAC7IvM,cAAc,CAACoH,UAAU,CAACmF,iBAAiB,CAACL,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACmF,iBAAiB,CAACL,mDAAmD,CAAC;UAC7NlM,cAAc,CAACoH,UAAU,CAACmF,iBAAiB,CAACJ,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACmF,iBAAiB,CAACJ,qDAAqD,CAAC;QACnO;QACA,IAAGtB,QAAQ,KAAI,uBAAuB,IAAI,uBAAuB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACoF,qBAAqB,KAAK,IAAI,EAAE;UAC1JxM,cAAc,CAACoH,UAAU,CAACoF,qBAAqB,CAACN,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACoF,qBAAqB,CAACN,mDAAmD,CAAC;UACrOlM,cAAc,CAACoH,UAAU,CAACoF,qBAAqB,CAACL,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACoF,qBAAqB,CAACL,qDAAqD,CAAC;QAC3O;QACA,IAAGtB,QAAQ,KAAI,8BAA8B,IAAI,kBAAkB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACqF,gBAAgB,KAAK,IAAI,EAAE;UACvJzM,cAAc,CAACoH,UAAU,CAACqF,gBAAgB,CAACP,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACqF,gBAAgB,CAACP,mDAAmD,CAAC;UAC3NlM,cAAc,CAACoH,UAAU,CAACqF,gBAAgB,CAACN,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACqF,gBAAgB,CAACN,qDAAqD,CAAC;QACjO;QACA,IAAGtB,QAAQ,KAAI,0BAA0B,IAAI,wBAAwB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACsF,sBAAsB,KAAK,IAAI,EAAC;UAC9J1M,cAAc,CAACoH,UAAU,CAACsF,sBAAsB,CAACR,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACsF,sBAAsB,CAACR,mDAAmD,CAAC;UACvOlM,cAAc,CAACoH,UAAU,CAACsF,sBAAsB,CAACP,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACsF,sBAAsB,CAACP,qDAAqD,CAAC;QAC7O;QACA,IAAGtB,QAAQ,KAAI,oBAAoB,IAAI,oBAAoB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACuF,kBAAkB,KAAK,IAAI,EAAE;UACjJ3M,cAAc,CAACoH,UAAU,CAACuF,kBAAkB,CAACT,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACuF,kBAAkB,CAACT,mDAAmD,CAAC;UAC/NlM,cAAc,CAACoH,UAAU,CAACuF,kBAAkB,CAACR,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACuF,kBAAkB,CAACR,qDAAqD,CAAC;QACrO;QACA,IAAGtB,QAAQ,KAAI,kBAAkB,IAAI,kBAAkB,IAAI7K,cAAc,CAACoH,UAAU,IAAIpH,cAAc,CAACoH,UAAU,CAACwF,gBAAgB,KAAK,IAAI,EAAE;UAC3I5M,cAAc,CAACoH,UAAU,CAACwF,gBAAgB,CAACV,mDAAmD,GAAG,IAAI,CAACT,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACwF,gBAAgB,CAACV,mDAAmD,CAAC;UAC3NlM,cAAc,CAACoH,UAAU,CAACwF,gBAAgB,CAACT,qDAAqD,GAAG,IAAI,CAACV,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACwF,gBAAgB,CAACT,qDAAqD,CAAC;QACjO;MACF,CAAC,CAAC;IACJ;IACAnM,cAAc,CAACoH,UAAU,CAACyF,sBAAsB,GAAG,IAAI,CAACpB,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAACyF,sBAAsB,CAAC;IAC/H7M,cAAc,CAACoH,UAAU,CAAC0F,wBAAwB,GAAG,IAAI,CAACrB,qBAAqB,CAACzL,cAAc,CAACoH,UAAU,CAAC0F,wBAAwB,CAAC;EACrI;EAEAC,iBAAiBA,CAAA;IACf;IACA,IAAI7F,aAAa,GAAmB;MAClC+D,cAAc,EAAE,IAAI,CAAC/N,cAAc,CAACG,KAAK;MACzC2B,MAAM,EAAE,OAAO;MACfmI,yBAAyB,EAAE,EAAE;MAC7BC,UAAU,EAAE1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACH,aAAa,CAAC,CAAe;MACxE6F,qBAAqB,EAAE,IAAI,CAACC,UAAU,IAAI,IAAI;MAC9CF,uBAAuB,EAAC;KACzB;IACD,IAAI,CAACG,oBAAoB,CAACN,aAAa,CAAC;IACxC,IAAGA,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;MACxD,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACT,aAAa,CAAC;MACrEA,aAAa,CAACE,UAAU,CAACK,6BAA6B,GAAGC,eAAe;IAC1E;IACA,IAAI,IAAI,CAACxI,mBAAmB,EAAEgI,aAAa,CAACJ,EAAE,GAAG,IAAI,CAAC5H,mBAAmB;IACzEgI,aAAa,CAACE,UAAU,CAACrI,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACjD,IAAI,CAAC1C,wBAAwB,CAAC2Q,SAAS,CAAC9F,aAAa,CAAC,CAACrI,SAAS,CAACiG,MAAM,IAAE;MACvE,IAAI,CAACqD,cAAc,CAACrD,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAAC;MACnD,IAAG,IAAI,CAAC7M,gBAAgB,CAACoG,MAAM,KAAK,CAAC,EAAC;QAAE;QACtC,IAAI,CAAClF,MAAM,CAACoP,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;UAAE1M,WAAW,EAAE;YAAEkI,EAAE,EAAE,IAAI,CAAC7H;UAAY;QAAC,CAAE,CAAC;QAC5F,IAAI,CAACnC,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAACnB,UAAU,CAACyI,UAAU,CAAC;UAACpF,MAAM;UAAwB9F,MAAM,EAAC,4BAA4B;UAAEqH,IAAI,EAAC;QAAS,CAAC,CAAC;MACjH,CAAC,MACG;QAAE;QACJ,IAAI,CAACzD,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAACnB,UAAU,CAACyI,UAAU,CAAC;UAACpF,MAAM;UAAuB9F,MAAM,EAAC,4BAA4B;UAAEqH,IAAI,EAAC;QAAS,CAAC,CAAC;MAChH;IACF,CAAC,CAAC;EACJ;EAEK3H,0BAA0BA,CAAA,EAA4B;IAAA,IAAAqU,MAAA;IAAA,OAAA1N,iBAAA,YAA3B2N,WAAA,GAAuB,IAAI;MAC3D;MACA;MAEE,IAAIC,SAAS,SAASF,MAAI,CAACG,4BAA4B,EAAE;MAEzD,IAAG,CAACD,SAAS,EAAC;QAAE;QACdF,MAAI,CAACnK,WAAW,EAAE,CAACG,KAAK,CAAEC,KAAK,IAAE;UAC/B/D,OAAO,CAACC,GAAG,CAAC8D,KAAK,CAAC;QACpB,CAAE,CAAC;MACL;IAAC,GAAAmK,KAAA,OAAAC,SAAA;EACH;EAEMxK,WAAWA,CAAA,EAA4B;IAAA,IAAAyK,MAAA;IAAA,OAAAhO,iBAAA,YAA3B2N,WAAA,GAAuB,IAAI;MAC3C,IAAIM,OAAO,GAAGD,MAAI;MAClB,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,OAAO,CAAC/L,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC4L,OAAO,CAAChN,MAAM,CAACV,IAAI,CAAC,CAAC;QACrE,IAAG0N,OAAO,CAACtU,MAAM,KAAK,QAAQ,EAAC;UAC7BsU,OAAO,CAACT,iBAAiB,EAAE;UAC3BW,OAAO,EAAE;QACX,CAAC,MACI;UACDH,MAAI,CAACvS,gBAAgB,GAAG,EAAE;UAC1BuS,MAAI,CAACzQ,mBAAmB,GAAG,KAAK;UAClC,IAAG,wBAAwB,IAAI0Q,OAAO,CAAC/L,aAAa,IAAI,0BAA0B,IAAI+L,OAAO,CAAC/L,aAAa,EAAC;YAC1G,IAAImM,SAAS,GAAmB;cAC9BzG,yBAAyB,EAAE,EAAE;cAC7BC,UAAU,EAAG1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC4L,OAAO,CAAC/L,aAAa,CAAC,CAAe;cAC5E6F,qBAAqB,EAAEkG,OAAO,CAACjG,UAAU,IAAI,IAAI;cACjDF,uBAAuB,EAAC;aACzB;YACD,IAAGuG,SAAS,CAACxG,UAAU,CAACK,6BAA6B,EAAC;cACpD,MAAMC,eAAe,GAAG8F,OAAO,CAAC7F,yBAAyB,CAACiG,SAAS,CAAC;cACpEA,SAAS,CAACxG,UAAU,CAACK,6BAA6B,GAAGC,eAAe;YACtE;YACA,IAAI8F,OAAO,CAAC9S,aAAa,EAAEkT,SAAS,CAAC9G,EAAE,GAAG0G,OAAO,CAAC9S,aAAa;YAC/DkT,SAAS,CAACxG,UAAU,CAACrI,QAAQ,GAAGyO,OAAO,CAACzO,QAAQ;YAChD6O,SAAS,CAACxG,UAAU,CAACyF,sBAAsB,GAAGW,OAAO,CAAC/B,qBAAqB,CAACmC,SAAS,CAACxG,UAAU,CAACyF,sBAAsB,CAAC;YACxHe,SAAS,CAACxG,UAAU,CAAC0F,wBAAwB,GAAGU,OAAO,CAAC/B,qBAAqB,CAACmC,SAAS,CAACxG,UAAU,CAAC0F,wBAAwB,CAAC;YAC5H,IAAIe,OAAO,GAAG,IAAI;YAClBL,OAAO,CAAC1R,kBAAkB,CAAC8L,8BAA8B,CAAC,iBAAiB,EAAEgG,SAAS,CAAC,CAAC/O,SAAS,CAAC4H,QAAQ,IAAG;cAE3G,IAAIoB,MAAM,GAAGpB,QAAQ,CAACqB,gBAAgB,CAACD,MAAM;cAC7CpB,QAAQ,CAACqB,gBAAgB,CAACgG,OAAO;cACjC,KAAK,MAAM5K,KAAK,IAAI2E,MAAM,EAAC;gBACzB,KAAI,MAAMiD,SAAS,IAAIjD,MAAM,CAAC3E,KAAK,CAAC,EAAC;kBACnC,IAAG4H,SAAS,KAAK,cAAc,KAAKjD,MAAM,CAAC3E,KAAK,CAAC,CAAC4H,SAAS,CAAC,CAAC9B,QAAQ,CAAC,0BAA0B,CAAC,IAAGnB,MAAM,CAAC3E,KAAK,CAAC,CAAC4H,SAAS,CAAC,CAAC9B,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EAAC;oBAC/JuE,MAAI,CAACvS,gBAAgB,CAAC+L,IAAI,CAACc,MAAM,CAAC3E,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC;oBACzDqK,MAAI,CAACzQ,mBAAmB,GAAG,IAAI;oBAC/B+Q,OAAO,GAAG,KAAK;kBACjB;gBACF;cACF;cAEA,IAAGA,OAAO,EAAC;gBACP,IAAI3G,aAAa,GAAmB;kBACpC+D,cAAc,EAAEuC,OAAO,CAACtQ,cAAc,CAACG,KAAK;kBAC5C2B,MAAM,EAAE,OAAO;kBACfmI,yBAAyB,EAACoG,MAAI,CAAC9S,eAAe,EAAE0M,yBAAyB,IAAE,EAAE;kBAC7EC,UAAU,EAAG1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC4L,OAAO,CAAC/L,aAAa,CAAC,CAAe;kBAC5E6F,qBAAqB,EAAEkG,OAAO,CAACjG,UAAU,IAAI,IAAI;kBACjDF,uBAAuB,EAAC;iBACzB;gBACDmG,OAAO,CAAChG,oBAAoB,CAACN,aAAa,CAAC;gBAC3C,IAAGA,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;kBACxD,MAAMC,eAAe,GAAG8F,OAAO,CAAC7F,yBAAyB,CAACT,aAAa,CAAC;kBACxEA,aAAa,CAACE,UAAU,CAACK,6BAA6B,GAAGC,eAAe;gBAC1E;gBACA,IAAI8F,OAAO,CAAC9S,aAAa,EAAEwM,aAAa,CAACJ,EAAE,GAAG0G,OAAO,CAAC9S,aAAa;gBACnEwM,aAAa,CAACE,UAAU,CAACrI,QAAQ,GAAGyO,OAAO,CAACzO,QAAQ;gBACpDyO,OAAO,CAAC1R,kBAAkB,CAACkR,SAAS,CAAC9F,aAAa,CAAC,CAACrI,SAAS,CAACiG,MAAM,IAAE;kBACpE,IAAGoI,WAAW,EAAE;oBAACM,OAAO,CAAC7R,UAAU,CAACyI,UAAU,CAAC;sBAACpF,MAAM;sBAAwB9F,MAAM,EAAC,gBAAgB;sBAAEqH,IAAI,EAAC;oBAAS,CAAC,CAAC;kBAAC;kBACxH,IAAI,CAACiN,OAAO,CAAC9S,aAAa,EAAC;oBAEzB8S,OAAO,CAAC9S,aAAa,GAAGoK,MAAM,CAACpK,aAAa;oBAC5C8S,OAAO,CAACtR,MAAM,CAACoP,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;sBAAE1M,WAAW,EAAE;wBAAC2M,aAAa,EAAEiC,OAAO,CAAC9S,aAAa;wBAAE8Q,QAAQ,EAAEgC,OAAO,CAACzO,QAAQ;wBAAE9F,MAAM,EAAE,MAAM;wBAAE+F,MAAM,EAACwO,OAAO,CAACxO,MAAM;wBAAE9F,MAAM,EAAEsU,OAAO,CAACtU;sBAAM;oBAAC,CAAC,CAAC;kBACjM;kBACAwU,OAAO,EAAE;gBACX,CAAC,CAAC;cACF,CAAC,MACE;gBACHF,OAAO,CAAC7R,UAAU,CAACyI,UAAU,CAAC;kBAACpF,MAAM;kBAAuB9F,MAAM,EAAC,gBAAgB;kBAAEqH,IAAI,EAAC;gBAAS,CAAC,CAAC;gBACrGoN,MAAM,EAAE;cACV;YACF,CAAC,CAAC;UACJ,CAAC,MACG;YACFH,OAAO,CAAC7R,UAAU,CAACyI,UAAU,CAAC;cAACpF,MAAM;cAAuB9F,MAAM,EAAC,gBAAgB;cAAEqH,IAAI,EAAC;YAAS,CAAC,CAAC;YACrGoN,MAAM,EAAE;UACV;QACF;MACJ,CAAC,CAAC;IAAC,GAAAN,KAAA,OAAAC,SAAA;EACL;EAEA3F,yBAAyBA,CAACT,aAAa;IACrC,IAAI0D,mBAAmB,GAAG,EAAE;IAC5B,IAAG1D,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;MACxDP,aAAa,CAACE,UAAU,CAACK,6BAA6B,CAACd,OAAO,CAACgD,OAAO,IAAG;QACvE,IAAGA,OAAO,KAAI,kBAAkB,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACC,OAAO,CAAC;QACzF,IAAGmM,OAAO,KAAI,0CAA0C,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACE,YAAY,CAAC;QACtH,IAAGkM,OAAO,KAAI,gCAAgC,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACG,EAAE,CAAC;QAClG,IAAGiM,OAAO,KAAI,mBAAmB,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACI,QAAQ,CAAC;QAC3F,IAAGgM,OAAO,KAAI,uBAAuB,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACK,YAAY,CAAC;QACnG,IAAG+L,OAAO,KAAI,8BAA8B,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACM,OAAO,CAAE;QACtG,IAAG8L,OAAO,KAAI,0BAA0B,EAACiB,mBAAmB,CAAC7D,IAAI,CAAE,IAAI,CAACxJ,gBAAgB,CAACO,KAAK,CAAC;QAC/F,IAAG6L,OAAO,KAAI,oBAAoB,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACQ,SAAS,CAAC;QAC7F,IAAG4L,OAAO,KAAI,kBAAkB,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,IAAI,CAACxJ,gBAAgB,CAACS,OAAO,CAAC;QACzF,IAAG2L,OAAO,KAAI,MAAM,EAAEiB,mBAAmB,CAAC7D,IAAI,CAAC,MAAM,CAAC;MACxD,CAAC,CAAC;MACF,OAAO6D,mBAAmB;IAC5B;IACA,OAAOA,mBAAmB;EAC5B;EAEMmD,sBAAsBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzO,iBAAA;MAC1BzJ,mBAAmB,CAACsF,QAAQ,CAACsL,KAAK,CAACC,OAAO,CAACsH,SAAS,IAAG;QACrD,IAAGA,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,8BAA8B,EAAC;UAC7DA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,8CAA8C,EAAC;gBAC5FD,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACG,EAAE;cAChM;cACA,IAAGiM,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,qDAAqD,EAAC;gBACnGD,QAAQ,CAAC,WAAW,CAAC,GAAG,sFAAsF,GAAEsE,MAAI,CAAC/P,WAAW,CAACP,EAAE,GAAG,IAAI;cAC5I;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGuQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,gCAAgC,EAAC;UAC/DA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC5ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACS,OAAO;cACpM;cACA,IAAG2L,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,yDAAyD,GAAEsE,MAAI,CAAC/P,WAAW,CAACD,OAAO,GAAG,IAAI;cACpH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGiQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,qCAAqC,EAAC;UACpEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACE,YAAY;cAC3M;cACA,IAAGkM,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,8DAA8D,GAAEsE,MAAI,CAAC/P,WAAW,CAACR,YAAY,GAAG,IAAI;cAC9H;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGwQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,gCAAgC,EAAC;UAC/DA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACM,OAAO;cACtM;cACA,IAAG8L,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,yDAAyD,GAAEsE,MAAI,CAAC/P,WAAW,CAACJ,OAAO,GAAG,IAAI;cACpH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGoQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,sCAAsC,EAAC;UACrEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC3ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACO,KAAK;cACnM;cACA,IAAG6L,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,+DAA+D,GAAEsE,MAAI,CAAC/P,WAAW,CAACH,KAAK,GAAG,IAAI;cACxH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGmQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,qCAAqC,EAAC;UACpEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACK,YAAY;cAC3M;cACA,IAAG+L,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,8DAA8D,GAAEsE,MAAI,CAAC/P,WAAW,CAACL,YAAY,GAAG,IAAI;cAC9H;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGqQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,kCAAkC,EAAC;UACjEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAG,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACQ,SAAS;cACzM;cACA,IAAG4L,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,2DAA2D,GAAEsE,MAAI,CAAC/P,WAAW,CAACF,SAAS,GAAG,IAAI;cACxH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGkQ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,iCAAiC,EAAC;UAChEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAACtH,OAAO,CAAC+C,QAAQ,IAAG;YACnD,KAAI,MAAMC,OAAO,IAAID,QAAQ,EAAE;cAAE;cAC/B,IAAGC,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC3ED,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG3T,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACmY,OAAO,CAAC,KAAK,EAAEF,MAAI,CAACG,UAAU,CAAC,GAAG,yBAAyB,GAAG,8BAA8B,GAAGH,MAAI,CAACzQ,gBAAgB,CAACI,QAAQ;cACvM;cACA,IAAGgM,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACC,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFD,QAAQ,CAAC,WAAW,CAAC,GAAG,0DAA0D,GAAEsE,MAAI,CAAC/P,WAAW,CAACN,QAAQ,GAAG,IAAI;cACtH;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IAAC;EACL;EAEAyQ,OAAOA,CAACC,cAAwB;IAC9B,KAAI,MAAMhE,GAAG,IAAIgE,cAAc,EAAC;MAC9B,KAAI,MAAMC,IAAI,IAAID,cAAc,CAAChE,GAAG,CAAC,EAAC;QACpC,KAAI,MAAMkE,IAAI,IAAIF,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,EAAC;UAC1C,KAAI,MAAME,IAAI,IAAIH,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;YAChD,KAAI,MAAME,IAAI,IAAIJ,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;cACtD,KAAI,MAAME,IAAI,IAAIL,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;gBAC5D,KAAI,MAAME,IAAI,IAAIN,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;kBAClE,IAAGC,IAAI,KAAK,cAAc,IAAIN,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,KAAK,IAAI,EAAC;oBAAE;oBAC/FN,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,GAAGP,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,CAACV,OAAO,CAAC,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;oBACzJhP,OAAO,CAACC,GAAG,CAACuP,IAAI,EAACN,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAAE;kBAC5E;kBACA,KAAI,MAAME,IAAI,IAAIR,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;oBACxE,KAAI,MAAMG,IAAI,IAAIT,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,EAAC;sBAC9E,IAAGC,IAAI,KAAK,cAAc,IAAIT,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,KAAK,IAAI,EAAE;wBAAE;wBAC5GT,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACF,GAAG,GAAGP,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACF,GAAG,CAACV,OAAO,CAAC,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;wBACjLhP,OAAO,CAACC,GAAG,CAAC0P,IAAI,EAACT,cAAc,CAAChE,GAAG,CAAC,CAACiE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,CAAC;sBACvF;oBACF;kBACF;gBACF;cACF;YACF;UACF;QACF;MACF;IACF;EACF;EAEApP,oBAAoBA,CAAA;IAClB,MAAMqP,UAAU,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,EAAE;IAC5C,IAAGF,UAAU,CAACG,aAAa,CAACC,WAAW,EAAC;MACtC,IAAI,CAAChB,UAAU,GAAGY,UAAU,CAACG,aAAa,CAAC5O,IAAI;IACjD;IAEA,IAAI8O,gBAAgB,GAAG,IAAI,CAACrT,YAAY,CAACyK,OAAO,CAAC;MAAC5J,cAAc,EAAE;IAAE,CAAC,CAAC;IACtE,IAAIyS,kBAAkB,GAAG,IAAI,CAACzT,cAAc,CAAC4K,OAAO,CAAC;MAAC5J,cAAc,EAAE;IAAG,CAAC,CAAC;IAC3E,IAAI0S,cAAc,GAAG,IAAI,CAACrT,UAAU,CAACuK,OAAO,CAAC;MAAC5J,cAAc,EAAC;IAAG,CAAC,CAAC;IAClE,IAAI2S,kBAAkB,GAAG,IAAI,CAAC7T,OAAO,CAAC8K,OAAO,CAAC,IAAI,CAAC/J,eAAe,CAAC;IACnE,IAAI+S,cAAc,GAAuB,CAACJ,gBAAgB,EAAEC,kBAAkB,EAAEC,cAAc,EAACC,kBAAkB,CAAC;IAClH,IAAG,IAAI,CAAC7U,aAAa,IAAI,IAAI,CAACsE,MAAM,KAAK,UAAU,EACnD;MACE,IAAIvE,eAAe,GAAG,IAAI,CAACqB,kBAAkB,CAAC+D,GAAG,CAAC,IAAI,CAACnF,aAAa,CAAC;MACrE8U,cAAc,CAACzI,IAAI,CAACtM,eAAe,CAAC;IACtC;IACAnE,QAAQ,CAACkZ,cAAc,CAAC,CAAC3Q,SAAS,CAACiG,MAAM,IAAG;MAC1C,MAAM2K,UAAU,GAAG3K,MAAM,CAAC,CAAC,CAAC,CAAC4B,KAAK;MAClC,MAAMgJ,YAAY,GAAG5K,MAAM,CAAC,CAAC,CAAC,CAAC4B,KAAK;MACpC,MAAMiJ,QAAQ,GAAG7K,MAAM,CAAC,CAAC,CAAC,CAAC4B,KAAK;MAChC,MAAMkJ,YAAY,GAAG9K,MAAM,CAAC,CAAC,CAAC,CAAC4B,KAAK;MACpC,IAAIjM,eAAgC;MACpC,IAAGqK,MAAM,CAAC1D,MAAM,GAAG,CAAC,EACpB;QACE3G,eAAe,GAAGqK,MAAM,CAAC,CAAC,CAAC,CAAC9E,cAAc;MAC5C;MAEAyP,UAAU,CAAC9I,OAAO,CAACgD,OAAO,IAAG;QAC3B,IAAGA,OAAO,CAACrJ,IAAI,KAAK,WAAW,EAAE,IAAI,CAACpD,cAAc,CAACC,SAAS,GAAGwM,OAAO,CAAC7C,EAAE;QAC3E,IAAG6C,OAAO,CAACrJ,IAAI,KAAK,aAAa,EAAE,IAAI,CAACpD,cAAc,CAACE,WAAW,GAAGuM,OAAO,CAAC7C,EAAE;QAC/E,IAAG6C,OAAO,CAACrJ,IAAI,KAAK,OAAO,EAAE,IAAI,CAACpD,cAAc,CAACG,KAAK,GAAGsM,OAAO,CAAC7C,EAAE;QACnE,IAAG6C,OAAO,CAACrJ,IAAI,KAAK,UAAU,EAAE,IAAI,CAACpD,cAAc,CAACI,QAAQ,GAAGqM,OAAO,CAAC7C,EAAE;MAC3E,CAAC,CAAC;MAEF4I,YAAY,CAAC/I,OAAO,CAACgD,OAAO,IAAG;QAC7B,IAAGA,OAAO,CAACrJ,IAAI,KAAI,kBAAkB,EAAE,IAAI,CAAC/C,gBAAgB,CAACC,OAAO,GAAGmM,OAAO,CAAC7C,EAAE;QACjF,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,0CAA0C,EAAE,IAAI,CAAC/C,gBAAgB,CAACE,YAAY,GAAGkM,OAAO,CAAC7C,EAAE;QAC9G,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,gCAAgC,EAAE,IAAI,CAAC/C,gBAAgB,CAACG,EAAE,GAAGiM,OAAO,CAAC7C,EAAE;QAC1F,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,mBAAmB,EAAE,IAAI,CAAC/C,gBAAgB,CAACI,QAAQ,GAAGgM,OAAO,CAAC7C,EAAE;QACnF,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,uBAAuB,EAAE,IAAI,CAAC/C,gBAAgB,CAACK,YAAY,GAAG+L,OAAO,CAAC7C,EAAE;QAC3F,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,8BAA8B,EAAE,IAAI,CAAC/C,gBAAgB,CAACM,OAAO,GAAG8L,OAAO,CAAC7C,EAAE;QAC7F,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,0BAA0B,EAAE,IAAI,CAAC/C,gBAAgB,CAACO,KAAK,GAAG6L,OAAO,CAAC7C,EAAE;QACvF,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,oBAAoB,EAAE,IAAI,CAAC/C,gBAAgB,CAACQ,SAAS,GAAG4L,OAAO,CAAC7C,EAAE;QACrF,IAAG6C,OAAO,CAACrJ,IAAI,KAAI,kBAAkB,EAAE,IAAI,CAAC/C,gBAAgB,CAACS,OAAO,GAAG2L,OAAO,CAAC7C,EAAE;MACnF,CAAC,CAAC;MAEF6I,QAAQ,CAAChJ,OAAO,CAACqB,IAAI,IAAE;QACrB,IAAGA,IAAI,CAAC1H,IAAI,CAAC0I,QAAQ,CAAC,kBAAkB,CAAC,EAAC;UACxC,IAAGhB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAACS,WAAW,CAACT,OAAO,GAAGwK,IAAI,CAAClB,EAAE;UAChG,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACE,YAAY,EAAE,IAAI,CAACQ,WAAW,CAACR,YAAY,GAAGuK,IAAI,CAAClB,EAAE;UAC1G,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACG,EAAE,EAAE,IAAI,CAACO,WAAW,CAACP,EAAE,GAAGsK,IAAI,CAAClB,EAAE;UACtF,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACI,QAAQ,EAAE,IAAI,CAACM,WAAW,CAACN,QAAQ,GAAGqK,IAAI,CAAClB,EAAE;UAClG,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACK,YAAY,EAAE,IAAI,CAACK,WAAW,CAACL,YAAY,GAAGoK,IAAI,CAAClB,EAAE;UAC1G,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACM,OAAO,EAAE,IAAI,CAACI,WAAW,CAACJ,OAAO,GAAGmK,IAAI,CAAClB,EAAE;UAChG,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACO,KAAK,EAAE,IAAI,CAACG,WAAW,CAACH,KAAK,GAAGkK,IAAI,CAAClB,EAAE;UAC5F,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACQ,SAAS,EAAE,IAAI,CAACE,WAAW,CAACF,SAAS,GAAGiK,IAAI,CAAClB,EAAE;UACpG,IAAGkB,IAAI,CAAC6H,kBAAkB,KAAK,IAAI,CAACtS,gBAAgB,CAACS,OAAO,EAAE,IAAI,CAACC,WAAW,CAACD,OAAO,GAAGgK,IAAI,CAAClB,EAAE;QAClG;MACF,CAAC,CAAC;MAEF,IAAIrG,cAAc;MAElB,IAAGhG,eAAe,IAAIA,eAAe,CAAC6M,qBAAqB,EAC3D;QACE7G,cAAc,GAAGmP,YAAY,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjJ,EAAE,IAAIrM,eAAe,CAAC6M,qBAAqB,CAAC;MACxF,CAAC,MAAM;QACL7G,cAAc,GAAGmP,YAAY,CAACE,IAAI,CAACnG,OAAO,IAAIA,OAAO,CAACqG,QAAQ,CAAC;MACjE;MACA,IAAGvP,cAAc,EACjB;QACE,IAAI,CAACA,cAAc,GAAGA,cAAc,CAACD,MAAM;QAC3C,IAAI,CAAC+G,UAAU,GAAG9G,cAAc,CAACqG,EAAE;MACrC;MACA,IAAI,CAAC9K,wBAAwB,CAACiU,qBAAqB,CAACxP,cAAc,CAAC;MAEnE,IAAG,IAAI,CAAC8G,UAAU,IAAI9M,eAAe,IAAIA,eAAe,CAACuE,MAAM,EAAC;QAC9D,IAAI,CAACL,iBAAiB,GAAGlE,eAAe,CAACuE,MAAM;QAC/C,IAAI,CAACtD,OAAO,CAACwU,sBAAsB,CAAC,IAAI,CAAC3I,UAAU,CAAC,CAAC1I,SAAS,CAACiG,MAAM,IAAE;UACrE,IAAI,CAACrG,eAAe,GAAGqG,MAAM;UAC7B,IAAG,IAAI,CAACrG,eAAe,IAAI,CAAC,KAAK,IAAI,CAACE,iBAAiB,IAAI,OAAO,IAAI,IAAI,CAACA,iBAAiB,IAAI,UAAU,CAAC,EAAC;YAC1G,IAAI,CAACH,qBAAqB,GAAG,IAAI;UACnC;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC4P,OAAO,CAAC,IAAI,CAAC3N,cAAc,CAAC;MACjC,IAAI,CAACsN,sBAAsB,EAAE;MAC7B,IAAI,CAACpO,SAAS,EAAE;IAClB,CAAC,CAAC;EACJ;EAGA5H,YAAYA,CAAA;IACV,IAAI,CAACmE,MAAM,CAACoP,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;MAAE1M,WAAW,EAAE;QAAC2M,aAAa,EAAE,IAAI,CAAC7Q,aAAa;QAAE8Q,QAAQ,EAAE,IAAI,CAACzM,QAAQ;QAAE9F,MAAM,EAAE,MAAM;QAAE+F,MAAM,EAAC,IAAI,CAACA,MAAM;QAAE9F,MAAM,EAAE,IAAI,CAACA;MAAM;IAAC,CAAC,CAAC;IAChL,IAAI,CAACD,MAAM,GAAG,MAAM;IACpB,IAAI,CAACqF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACqB,SAAS,EAAE;EAElB;EAEAhI,WAAWA,CAAA;IACT,IAAG,IAAI,CAACsB,MAAM,KAAK,MAAM,EAAC;MACxB,IAAI,CAAC0C,UAAU,CAACyI,UAAU,CAAC;QAACnL,MAAM,EAAE,QAAQ;QAAEkS,KAAK,EAAE,kDAAkD;QACvGjK,IAAI,EAAE,iCAAiC;QAAEX,IAAI,EAAC;MAAS,CAAC,EAAG6K,OAAO,IAAI;QACpE,IAAGA,OAAO,EAAE;UACV,IAAG,IAAI,CAAClS,MAAM,KAAK,QAAQ,EAAE,IAAI,CAACgD,MAAM,CAACoP,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAAE1M,WAAW,EAAE;cAAEkI,EAAE,EAAE,IAAI,CAAC7H;YAAY;UAAC,CAAE,CAAC,CAAC,KAExH,IAAI,CAAC/C,MAAM,CAACiU,aAAa,CAAC,gBAAgB,CAAC;QAC/C;MACF,CAAC,CAAC;IACJ,CAAC,MACG;MACF,IAAI,CAACjU,MAAM,CAACiU,aAAa,CAAC,gBAAgB,CAAC;IAC7C;EACF;EAEAzN,WAAWA,CAAA;IACT,MAAMiH,OAAO,GAAGyG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C1G,OAAO,CAAC2G,MAAM,CAAC;MACbC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxR,mBAAmB,EAAE;MAE5B,IAAI,CAACvD,UAAU,CAACyI,UAAU,CAAC;QAACnL,MAAM,EAAE,QAAQ;QAAEkS,KAAK,EAAE,2DAA2D;QAChHjK,IAAI,EAAE,wBAAwB;QAAEX,IAAI,EAAC;MAAS,CAAC,EAAG6K,OAAO,IAAI;QAC3D,IAAIA,OAAO,EAAE;UACX,IAAI,CAAC/O,wBAAwB,CAACsU,MAAM,CAAC,IAAI,CAACzR,mBAAmB,CAAC,CAACL,SAAS,CAACiG,MAAM,IAAG;YAChF3F,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE0F,MAAM,CAAC;YACnC,IAAI,CAACnJ,UAAU,CAACyI,UAAU,CAAC;cAACpF,MAAM;cAAwB9F,MAAM,EAAC,8BAA8B;cAAEqH,IAAI,EAAC;YAAS,CAAC,CAAC;YACjH,IAAI,CAACrE,MAAM,CAACoP,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;cAAE1M,WAAW,EAAE;gBAAEkI,EAAE,EAAE,IAAI,CAAC7H;cAAY;YAAC,CAAE,CAAC;UAC9F,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAI;MACH,IAAI,CAACtD,UAAU,CAACyI,UAAU,CAAC;QAACpF,MAAM;QAAuB9F,MAAM,EAAC,8BAA8B;QAAEqH,IAAI,EAAC;MAAS,CAAC,CAAC;IAClH;EACF;EAEAjI,KAAKA,CAAA;IACH,IAAI,CAACG,UAAU,GAAG,IAAI;IAEtB,IAAIkR,OAAO,GAAGyG,QAAQ,CAACQ,cAAc,CAAC,QAAQ,CAAC;IAC/C,IAAIC,GAAG,GAAG;MACRC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MACxBC,QAAQ,EAAM,wBAAwB;MACtCC,KAAK,EAAS;QAAEzQ,IAAI,EAAE;MAAM,CAAC;MAC7B0Q,WAAW,EAAG;QAAEC,KAAK,EAAE,CAAC;QAAGC,eAAe,EAAE,IAAI;QAC9CC,OAAO,EAAGC,cAAc,IAAI;UAC1BC,KAAK,CAACC,IAAI,CAACF,cAAc,CAACG,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC7K,OAAO,CAAE8K,WAAW,IAAI;YAC9E,MAAMC,QAAQ,GAAGD,WAAkC;YACnD,MAAME,GAAG,GAAGN,cAAc,CAACO,aAAa,CAAC,KAAK,CAAC;YAC/CD,GAAG,CAACE,SAAS,GAAGH,QAAQ,CAACtL,KAAK;YAC9BuL,GAAG,CAACG,KAAK,CAACC,KAAK,GAAG,MAAM;YACxBJ,GAAG,CAACG,KAAK,CAACE,UAAU,GAAG,UAAU;YACjCL,GAAG,CAACG,KAAK,CAACG,YAAY,GAAG,YAAY;YACrCN,GAAG,CAACG,KAAK,CAACI,SAAS,GAAG,YAAY;YAClCP,GAAG,CAACG,KAAK,CAACK,OAAO,GAAG,+EAA+E;YACnGR,GAAG,CAACG,KAAK,CAACM,UAAU,GAAG,wDAAwD;YAC/ET,GAAG,CAACG,KAAK,CAACO,UAAU,GAAG,+GAA+G;YACtIV,GAAG,CAACG,KAAK,CAACQ,UAAU,GAAG,wCAAwC;YAC/DX,GAAG,CAACG,KAAK,CAACS,KAAK,GAAG,uDAAuD;YACzEZ,GAAG,CAACG,KAAK,CAACU,QAAQ,GAAG,MAAM;YAC3Bb,GAAG,CAACG,KAAK,CAACW,eAAe,GAAG,2CAA2C;YACvEd,GAAG,CAACG,KAAK,CAACY,MAAM,GAAG,MAAM;YACzBf,GAAG,CAACG,KAAK,CAACa,YAAY,GAAG,0CAA0C;YACnEhB,GAAG,CAACG,KAAK,CAACc,SAAS,GAAG,OAAO;YAC7BjB,GAAG,CAACG,KAAK,CAACe,aAAa,GAAG,QAAQ;YAClC,IAAG,CAACnB,QAAQ,CAACtL,KAAK,EAAC;cACjBuL,GAAG,CAACG,KAAK,CAACgB,SAAS,GAAG,KAAK;YAC7B;YACAnB,GAAG,CAACG,KAAK,CAACiB,SAAS,GAAG,qHAAqH;YAC3IrB,QAAQ,CAACI,KAAK,CAACkB,OAAO,GAAG,MAAM;YAC/BtB,QAAQ,CAACuB,aAAa,CAACC,MAAM,CAACvB,GAAG,CAAC;UACpC,CAAC,CAAC;UACFL,KAAK,CAACC,IAAI,CAACF,cAAc,CAACG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC7K,OAAO,CAAEwM,QAAQ,IAAI;YACxE,MAAMC,KAAK,GAAGD,QAA4B;YAC1C,MAAMxB,GAAG,GAAGN,cAAc,CAACO,aAAa,CAAC,KAAK,CAAC;YAC/C,IAAGwB,KAAK,CAAC7S,IAAI,KAAK,MAAM,IAAI6S,KAAK,CAAC7S,IAAI,KAAK,MAAM,EAAE;cACjDoR,GAAG,CAACE,SAAS,GAAGuB,KAAK,CAAChN,KAAK;cAC3BuL,GAAG,CAACG,KAAK,CAACC,KAAK,GAAG,MAAM;cACxBJ,GAAG,CAACG,KAAK,CAACE,UAAU,GAAG,UAAU;cACjCL,GAAG,CAACG,KAAK,CAACG,YAAY,GAAG,YAAY;cACrCN,GAAG,CAACG,KAAK,CAACI,SAAS,GAAG,YAAY;cAClCP,GAAG,CAACG,KAAK,CAACK,OAAO,GAAG,+EAA+E;cACnGR,GAAG,CAACG,KAAK,CAACM,UAAU,GAAG,wDAAwD;cAC/ET,GAAG,CAACG,KAAK,CAACO,UAAU,GAAG,+GAA+G;cACtIV,GAAG,CAACG,KAAK,CAACQ,UAAU,GAAG,wCAAwC;cAC/DX,GAAG,CAACG,KAAK,CAACS,KAAK,GAAG,uDAAuD;cACzEZ,GAAG,CAACG,KAAK,CAACU,QAAQ,GAAG,MAAM;cAC3Bb,GAAG,CAACG,KAAK,CAACW,eAAe,GAAG,2CAA2C;cACvEd,GAAG,CAACG,KAAK,CAACY,MAAM,GAAG,MAAM;cACzBf,GAAG,CAACG,KAAK,CAACa,YAAY,GAAG,0CAA0C;cACnEhB,GAAG,CAACG,KAAK,CAACc,SAAS,GAAG,OAAO;cAC7BjB,GAAG,CAACG,KAAK,CAACe,aAAa,GAAG,QAAQ;cAClClB,GAAG,CAACG,KAAK,CAACiB,SAAS,GAAG,qHAAqH;cAC3IK,KAAK,CAACtB,KAAK,CAACkB,OAAO,GAAG,MAAM;cAC5B,IAAG,CAACI,KAAK,CAAChN,KAAK,EAAC;gBACduL,GAAG,CAACG,KAAK,CAACgB,SAAS,GAAG,KAAK;cAC7B;cACAM,KAAK,CAACH,aAAa,CAACC,MAAM,CAACvB,GAAG,CAAC;YACjC,CAAC,MAAI;cAEHyB,KAAK,CAACtB,KAAK,CAACkB,OAAO,GAAG,MAAM;YAC9B;UAEF,CAAC,CAAC;QAEJ;MAAC,CAAC;MACJK,KAAK,EAAE;QAAEC,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE,CAAC5J,OAAO,CAAC6J,WAAW,GAAC,CAAC,EAAE,GAAG,CAAC;QAAEC,WAAW,EAAE;MAAG,CAAE;MAC7EC,SAAS,EAAE;QAAEC,KAAK,EAAE,CAAC,cAAc,EAAE,uBAAuB,EAAC,mBAAmB,EAAE,oBAAoB;MAAC,CAAC;MACxGC,eAAe,EAAE;KAClB;IACDnd,QAAQ,EAAE,CAACod,GAAG,CAAChD,GAAG,CAAC,CAACU,IAAI,CAAC5H,OAAO,CAAC,CAACmK,IAAI,EAAE,CAAC/Q,IAAI,CAAC,MAAI;MAC9C,IAAI,CAACtK,UAAU,GAAG,KAAK;MACvB,IAAI,CAACkD,UAAU,CAACyI,UAAU,CAAC;QAACpF,MAAM;QAAwB9F,MAAM,EAAC,yBAAyB;QAAEqH,IAAI,EAAC;MAAS,CAAC,CAAC;IAChH,CAAC,CAAC,CAAC0C,KAAK,CAAEC,KAAK,IAAG;MAChB/D,OAAO,CAAC+D,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,IAAI,CAACvH,UAAU,CAACyI,UAAU,CAAC;QAACpF,MAAM;QAAuB9F,MAAM,EAAC,yBAAyB;QAAEqH,IAAI,EAAC;MAAS,CAAC,CAAC;MAC3G,IAAI,CAAC9H,UAAU,GAAG,KAAK;IACzB,CAAC,CAAC;EACJ;EAEA4B,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC6E,mBAAmB,EAAC;MAE3B,IAAI6U,cAAc,GAAmB;QACnC5M,yBAAyB,EAAE,EAAE;QAC7BC,UAAU,EAAE1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACpB,MAAM,CAACV,IAAI,CAAC,CAAe;QACtEwH,qBAAqB,EAAE,IAAI,CAACC,UAAU;QACtCF,uBAAuB,EAAC;OACzB;MAED,IAAI,CAACG,oBAAoB,CAACuM,cAAc,CAAC;MAEzC,IAAGA,cAAc,CAAC3M,UAAU,CAACK,6BAA6B,EAAC;QACzD,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACoM,cAAc,CAAC;QACtEA,cAAc,CAAC3M,UAAU,CAACK,6BAA6B,GAAGC,eAAe;MAC3E;MAEA,IAAI,IAAI,CAACxI,mBAAmB,EAAE6U,cAAc,CAACjN,EAAE,GAAG,IAAI,CAAC5H,mBAAmB;MAC1E6U,cAAc,CAAC3M,UAAU,CAACrI,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAElD,IAAI,CAACpD,UAAU,CAACyI,UAAU,CAAC;QAACnL,MAAM,EAAE,QAAQ;QAACkS,KAAK,EAAE,iCAAiC;QACrFjK,IAAI,EAAE,EAAE;QACRX,IAAI,EAAC;MAAS,CAAC,EACd6K,OAAO,IAAI;QACV,IAAGA,OAAO,EAAE;UACX,IAAI,CAAC/O,wBAAwB,CAAC2X,aAAa,CAACD,cAAc,CAAC,CAAClV,SAAS,CAACiG,MAAM,IAAG;YAC9E,IAAI,CAACqD,cAAc,CAACrD,MAAM,CAACgD,gBAAgB,CAACD,MAAM,CAAC;YACnD,IAAG,IAAI,CAAC7M,gBAAgB,CAACoG,MAAM,KAAK,CAAC,EAAC;cAAE;cACtC,IAAI,CAAClF,MAAM,CAACoP,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;gBAAE1M,WAAW,EAAE;kBAAEkI,EAAE,EAAE,IAAI,CAAC7H;gBAAY;cAAC,CAAE,CAAC;cAC5F,IAAI,CAACnC,mBAAmB,GAAG,KAAK;cAChC,IAAI,CAACnB,UAAU,CAACyI,UAAU,CAAC;gBAACpF,MAAM;gBAAwB9F,MAAM,EAAC,iCAAiC;gBAAEqH,IAAI,EAAC;cAAS,CAAC,CAAC;YACtH,CAAC,MACG;cAAE;cACJ,IAAI,CAACzD,mBAAmB,GAAG,IAAI;cAC/B,IAAI,CAACnB,UAAU,CAACyI,UAAU,CAAC;gBAACpF,MAAM;gBAAuB9F,MAAM,EAAC,iCAAiC;gBAAEqH,IAAI,EAAC;cAAS,CAAC,CAAC;YACrH;UAED,CAAC,CAAC;QACH;MACA,CAAC,CAAC;IACN,CAAC,MAAI;MACH,IAAI,CAAC5E,UAAU,CAACyI,UAAU,CAAC;QAACpF,MAAM;QAAuB9F,MAAM,EAAC,8BAA8B;QAAEqH,IAAI,EAAC;MAAS,CAAC,CAAC;IAClH;EACF;EAEM6M,4BAA4BA,CAAA;IAAA,IAAA6G,MAAA;IAAA,OAAA1U,iBAAA;MAChC;MAEA;MACA,IAAI0U,MAAI,CAACxV,eAAe,KAAK,CAAC,KAAKwV,MAAI,CAACtV,iBAAiB,KAAK,OAAO,IAAIsV,MAAI,CAACtV,iBAAiB,KAAK,UAAU,CAAC,EAAE;QAC/G,OAAO,IAAI8O,OAAO,CAAEC,OAAO,IAAI;UAC3BuG,MAAI,CAACtY,UAAU,CAACyI,UAAU,CAAC;YACvBnL,MAAM,EAAE,2BAA2B;YACnCkS,KAAK,EAAE,6BAA6B;YACpCjK,IAAI,EAAE,uLAAuL;YAC7LX,IAAI,EAAE;WACT;YAAA,IAAA2T,IAAA,GAAA3U,iBAAA,CAAE,WAAO6L,OAAO,EAAI;cACjB,IAAIA,OAAO,EAAE;gBACT,MAAM6I,MAAI,CAACE,iCAAiC,EAAE;gBAC9CzG,OAAO,CAAC,IAAI,CAAC;cACjB,CAAC,MAAM;gBACHA,OAAO,CAAC,KAAK,CAAC;cAClB;YACJ,CAAC;YAAA,iBAAA0G,EAAA;cAAA,OAAAF,IAAA,CAAA7G,KAAA,OAAAC,SAAA;YAAA;UAAA,IAAC;QACN,CAAC,CAAC;MACJ,CAAC,MACI;QACH,OAAO,KAAK;MACd;IAAC;EACH;EAEM9S,4BAA4BA,CAAA;IAAA,IAAA6Z,MAAA;IAAA,OAAA9U,iBAAA;MAChC,IAAI8U,MAAI,CAAC5V,eAAe,KAAK,CAAC,KAAK4V,MAAI,CAAC1V,iBAAiB,KAAK,OAAO,IAAI0V,MAAI,CAAC1V,iBAAiB,KAAK,UAAU,CAAC,EAAE;QAC/G0V,MAAI,CAAC1Y,UAAU,CAACyI,UAAU,CAAC;UACzBnL,MAAM,EAAE,2BAA2B;UACnCkS,KAAK,EAAE,0CAA0C;UACjDjK,IAAI,EAAE,wHAAwH;UAC9HX,IAAI,EAAC;SACN;UAAA,IAAA+T,KAAA,GAAA/U,iBAAA,CAAE,WAAO6L,OAAO,EAAI;YACjB,IAAGA,OAAO,EAAE;cACV,MAAMiJ,MAAI,CAACF,iCAAiC,EAAE;YAChD;UACJ,CAAC;UAAA,iBAAAI,GAAA;YAAA,OAAAD,KAAA,CAAAjH,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACJ;IAAC;EACH;EAEM6G,iCAAiCA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAAjV,iBAAA;MACrCiV,MAAI,CAAC/S,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC4S,MAAI,CAAChU,MAAM,CAACV,IAAI,CAAC,CAAC;MACjE,IAAIoH,aAAa,GAAmB;QAClCC,yBAAyB,EAACqN,MAAI,CAAC/Z,eAAe,EAAE0M,yBAAyB,IAAE,EAAE;QAC7EC,UAAU,EAAG1F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC4S,MAAI,CAAC/S,aAAa,CAAC,CAAe;QACzE6F,qBAAqB,EAAEkN,MAAI,CAACjN,UAAU,IAAI,IAAI;QAC9CF,uBAAuB,EAAC;OACzB;MACD,IAAGmN,MAAI,CAAC7V,iBAAiB,IAAI,OAAO,EAAC;QACnCuI,aAAa,CAAClI,MAAM,GAAG,OAAO;QAC9BkI,aAAa,CAAC+D,cAAc,GAAGuJ,MAAI,CAACtX,cAAc,CAACG,KAAK;MAC1D,CAAC,MACI,IAAImX,MAAI,CAAC7V,iBAAiB,IAAI,UAAU,EAAC;QAC5CuI,aAAa,CAAClI,MAAM,GAAG,UAAU;QACjCkI,aAAa,CAAC+D,cAAc,GAAGuJ,MAAI,CAACtX,cAAc,CAACI,QAAQ;MAC7D;MACAkX,MAAI,CAAChN,oBAAoB,CAACN,aAAa,CAAC;MACxC,IAAGA,aAAa,CAACE,UAAU,CAACK,6BAA6B,EAAC;QACxD,MAAMC,eAAe,GAAG8M,MAAI,CAAC7M,yBAAyB,CAACT,aAAa,CAAC;QACrEA,aAAa,CAACE,UAAU,CAACK,6BAA6B,GAAGC,eAAe;MAC1E;MACA,IAAI8M,MAAI,CAAC9Z,aAAa,EAAEwM,aAAa,CAACJ,EAAE,GAAG0N,MAAI,CAAC9Z,aAAa;MAC7DwM,aAAa,CAACE,UAAU,CAACrI,QAAQ,GAAGyV,MAAI,CAACzV,QAAQ;MACjDyV,MAAI,CAAC1Y,kBAAkB,CAAC2Y,2BAA2B,CAACvN,aAAa,CAAC,CAACrI,SAAS;QAAA,IAAA6V,KAAA,GAAAnV,iBAAA,CAAC,WAAMuF,MAAM,EAAE;UACzF0P,MAAI,CAACxZ,gBAAgB,GAAG,EAAE;UAC1B,IAAG,CAAC8J,MAAM,CAACgD,gBAAgB,CAACgG,OAAO,EAAC;YAClC,IAAIjG,MAAM,GAAG/C,MAAM,CAACgD,gBAAgB,CAACD,MAAM;YAC3C,KAAK,MAAM3E,KAAK,IAAI2E,MAAM,EAAC;cACzB,KAAI,MAAMiD,SAAS,IAAIjD,MAAM,CAAC3E,KAAK,CAAC,EAAC;gBACnC,IAAG4H,SAAS,KAAK,cAAc,KAAKjD,MAAM,CAAC3E,KAAK,CAAC,CAAC4H,SAAS,CAAC,CAAC9B,QAAQ,CAAC,0BAA0B,CAAC,IAAGnB,MAAM,CAAC3E,KAAK,CAAC,CAAC4H,SAAS,CAAC,CAAC9B,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EAAC;kBAC/JwL,MAAI,CAACxZ,gBAAgB,CAAC+L,IAAI,CAACc,MAAM,CAAC3E,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC;kBACzDsR,MAAI,CAAC1X,mBAAmB,GAAG,IAAI;gBACjC;cACF;YACF;YACA0X,MAAI,CAAC7Y,UAAU,CAACyI,UAAU,CAAC;cAACpF,MAAM;cAAuB9F,MAAM,EAAC,0BAA0B;cAAEqH,IAAI,EAAC;YAAS,CAAC,CAAC;UAC9G,CAAC,MACG;YACFiU,MAAI,CAAClW,SAAS,GAAG,IAAI;YACrBkW,MAAI,CAAChW,qBAAqB,GAAG,KAAK;YAClC,MAAMgW,MAAI,CAAC9U,oBAAoB,EAAE;YACjC8U,MAAI,CAAC7Y,UAAU,CAACyI,UAAU,CAAC;cAACpF,MAAM;cAAwB9F,MAAM,EAAC,0BAA0B;cAAEqH,IAAI,EAAC;YAAS,CAAC,CAAC;UAC/G;QACF,CAAC;QAAA,iBAAAoU,GAAA;UAAA,OAAAD,KAAA,CAAArH,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACL;;;uBA3xDW/R,sBAAsB,EAAAzE,EAAA,CAAA8d,iBAAA,CAAA9d,EAAA,CAAA+d,QAAA,GAAA/d,EAAA,CAAA8d,iBAAA,CAAAE,EAAA,CAAAC,eAAA,GAAAje,EAAA,CAAA8d,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAne,EAAA,CAAA8d,iBAAA,CAAAM,EAAA,CAAAC,uBAAA,GAAAre,EAAA,CAAA8d,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAve,EAAA,CAAA8d,iBAAA,CAAAU,EAAA,CAAAC,kBAAA,GAAAze,EAAA,CAAA8d,iBAAA,CAAAM,EAAA,CAAAM,wBAAA,GAAA1e,EAAA,CAAA8d,iBAAA,CAAAa,EAAA,CAAAC,wBAAA,GAAA5e,EAAA,CAAA8d,iBAAA,CAAAM,EAAA,CAAAS,WAAA,GAAA7e,EAAA,CAAA8d,iBAAA,CAAAQ,EAAA,CAAAQ,MAAA,GAAA9e,EAAA,CAAA8d,iBAAA,CAAAiB,EAAA,CAAAC,sBAAA,GAAAhf,EAAA,CAAA8d,iBAAA,CAAAM,EAAA,CAAAa,cAAA,GAAAjf,EAAA,CAAA8d,iBAAA,CAAAoB,EAAA,CAAAC,wBAAA,GAAAnf,EAAA,CAAA8d,iBAAA,CAAAsB,EAAA,CAAAC,SAAA,GAAArf,EAAA,CAAA8d,iBAAA,CAAAU,EAAA,CAAAc,yBAAA,GAAAtf,EAAA,CAAA8d,iBAAA,CAAAyB,GAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAtB/a,sBAAsB;MAAAgb,SAAA;MAAAC,QAAA,GAAA1f,EAAA,CAAA2f,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChE/BjgB,EAAA,CAAAkB,UAAA,IAAAif,qCAAA,iBAAmD;UAM3CngB,EAHR,CAAAC,cAAA,kBAAuD,yBAEc,wBACpB;UAAAD,EAAA,CAAAc,MAAA,qCAA8B;UAAAd,EAAA,CAAAG,YAAA,EAAiB;UACxFH,EAAA,CAAAC,cAAA,aAA8B;UAwB1BD,EAvBA,CAAAkB,UAAA,IAAAkf,wCAAA,oBAA0H,IAAAC,qCAAA,iBAIrE,IAAAC,wCAAA,oBAMZ,IAAAC,qCAAA,iBAMY,KAAAC,yCAAA,oBAIsF,KAAAC,yCAAA,oBACF,KAAAC,yCAAA,oBACe,KAAAC,sCAAA,iBACnG;UAIrD3gB,EAAA,CAAAC,cAAA,WAAK;UACHD,EAAA,CAAAkB,UAAA,KAAA0f,yCAAA,oBAA0O;UAC5O5gB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAkB,UAAA,KAAA2f,yCAAA,oBAA8I;UAGtJ7gB,EADI,CAAAG,YAAA,EAAM,EACQ;UAEdH,EADJ,CAAAC,cAAA,4BAAsD,WAC7C;UAGDD,EAFA,CAAAE,SAAA,uBAAkD,0CAE+H;UAWjLF,EATA,CAAAkB,UAAA,KAAA4f,6DAAA,yCAAmK,KAAAC,sCAAA,kBAEnE,KAAAC,sCAAA,kBAOjC;UAM/DhhB,EAAA,CAAAE,SAAA,kBAAmD;UAQ/DF,EAPQ,CAAAG,YAAA,EAAM,EACS,EAMZ;;;UAvEsBH,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA1Y,SAAA,CAAgB;UAGfxH,EAAA,CAAAI,SAAA,EAAoB;UAApBJ,EAAA,CAAAK,UAAA,WAAA6f,GAAA,CAAA1Y,SAAA,CAAoB;UAKyCxH,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,CAAkB;UAI/FpB,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAK,UAAA,iDAA6C;UAKtBL,EAAA,CAAAI,SAAA,EAAmC;UAAnCJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,IAAA8e,GAAA,CAAAja,cAAA,CAAmC;UAO1DjG,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAK,UAAA,iDAA6C;UAIOL,EAAA,CAAAI,SAAA,EAA8C;UAA9CJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,MAAA8e,GAAA,CAAA9e,WAAA,kBAAA8e,GAAA,CAAA9e,WAAA,CAAA6f,WAAA,EAA8C;UAC/CjhB,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,MAAA8e,GAAA,CAAA9e,WAAA,kBAAA8e,GAAA,CAAA9e,WAAA,CAAAiB,UAAA,EAA6C;UAC9CrC,EAAA,CAAAI,SAAA,EAAwD;UAAxDJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,KAAA8e,GAAA,CAAA9e,WAAA,kBAAA8e,GAAA,CAAA9e,WAAA,CAAAkB,sBAAA,EAAwD;UAC1GtC,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAK,UAAA,iDAA6C;UAKkCL,EAAA,CAAAI,SAAA,GAAqJ;UAArJJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,KAAA8e,GAAA,CAAA9e,WAAA,kBAAA8e,GAAA,CAAA9e,WAAA,CAAAiB,UAAA,OAAA6d,GAAA,CAAA9e,WAAA,kBAAA8e,GAAA,CAAA9e,WAAA,CAAAkB,sBAAA,MAAA4d,GAAA,CAAA9e,WAAA,kBAAA8e,GAAA,CAAA9e,WAAA,CAAAmB,uBAAA,KAAA2d,GAAA,CAAA9d,MAAA,cAAqJ;UAEjLpC,EAAA,CAAAI,SAAA,EAA0C;UAA1CJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA9e,WAAA,IAAA8e,GAAA,CAAAxY,qBAAA,CAA0C;UAQnE1H,EAAA,CAAAI,SAAA,GAAuB;UAAqEJ,EAA5F,CAAAK,UAAA,aAAA6f,GAAA,CAAAjY,QAAA,CAAuB,qBAAAiY,GAAA,CAAA7Y,gBAAA,CAAwC,eAAA6Y,GAAA,CAAA9Y,UAAA,CAA4B,SAAA8Y,GAAA,CAAArc,UAAA,CAAoB;UAElH7D,EAAA,CAAAI,SAAA,EAAoB;UAApBJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAAzY,cAAA,CAAoB;UAEEzH,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAAla,mBAAA,KAAAka,GAAA,CAAAve,UAAA,CAA2C;UAOpE3B,EAAA,CAAAI,SAAA,EAAmC;UAAnCJ,EAAA,CAAAK,UAAA,SAAA6f,GAAA,CAAA/Z,WAAA,KAAA+Z,GAAA,CAAAve,UAAA,CAAmC;UAMrD3B,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAK,UAAA,UAAA6f,GAAA,CAAA9e,WAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}