{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"mgh\", [[\"wichi<PERSON>\", \"mchochil’l\"], u, u], u, [[\"S\", \"J\", \"J\", \"J\", \"A\", \"I\", \"J\"], [\"Sab\", \"Jtt\", \"Jnn\", \"Jtn\", \"Ara\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"], [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>mann<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>ju<PERSON><PERSON>\", \"Ju<PERSON><PERSON><PERSON>\"], [\"Sab\", \"Jtt\", \"Jnn\", \"Jtn\", \"Ara\", \"Iju\", \"J<PERSON>\"]], u, [[\"K\", \"U\", \"R\", \"C\", \"T\", \"M\", \"S\", \"N\", \"T\", \"<PERSON>\", \"<PERSON>\", \"Y\"], [\"<PERSON>wa\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sab\", \"<PERSON>\", \"<PERSON>is\", \"Kum\", \"Moj\", \"Yel\"], [\"Mweri wo kwanza\", \"Mweri wo unayeli\", \"Mweri wo uneraru\", \"Mweri wo unecheshe\", \"Mweri wo unethanu\", \"Mweri wo thanu na mocha\", \"Mweri wo saba\", \"Mweri wo nane\", \"Mweri wo tisa\", \"Mweri wo kumi\", \"Mweri wo kumi na moja\", \"Mweri wo kumi na yel’li\"]], u, [[\"HY\", \"YY\"], u, [\"Hinapiya yesu\", \"Yopia yesu\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"MZN\", \"MTn\", \"MZN\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"MZN\": [\"MTn\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/mgh.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"mgh\", [[\"wichi<PERSON>\", \"mchochil’l\"], u, u], u, [[\"S\", \"J\", \"J\", \"J\", \"A\", \"I\", \"J\"], [\"Sab\", \"Jtt\", \"Jnn\", \"Jtn\", \"Ara\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"], [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>mann<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>ju<PERSON><PERSON>\", \"Ju<PERSON><PERSON><PERSON>\"], [\"Sab\", \"Jtt\", \"Jnn\", \"Jtn\", \"Ara\", \"Iju\", \"J<PERSON>\"]], u, [[\"K\", \"U\", \"R\", \"C\", \"T\", \"M\", \"S\", \"N\", \"T\", \"<PERSON>\", \"<PERSON>\", \"Y\"], [\"<PERSON>wa\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sab\", \"<PERSON>\", \"<PERSON>is\", \"Kum\", \"Moj\", \"Yel\"], [\"Mweri wo kwanza\", \"Mweri wo unayeli\", \"Mweri wo uneraru\", \"Mweri wo unecheshe\", \"Mweri wo unethanu\", \"Mweri wo thanu na mocha\", \"Mweri wo saba\", \"Mweri wo nane\", \"Mweri wo tisa\", \"Mweri wo kumi\", \"Mweri wo kumi na moja\", \"Mweri wo kumi na yel’li\"]], u, [[\"HY\", \"YY\"], u, [\"Hinapiya yesu\", \"Yopia yesu\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"MZN\", \"MTn\", \"MZN\", { \"JPY\": [\"JP¥\", \"¥\"], \"MZN\": [\"MTn\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,yBAAyB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}