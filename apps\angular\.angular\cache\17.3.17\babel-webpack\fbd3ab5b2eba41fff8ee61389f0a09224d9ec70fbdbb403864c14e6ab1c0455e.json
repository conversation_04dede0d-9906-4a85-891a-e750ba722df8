{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['aC', 'dC'],\n  abbreviated: ['a.C.', 'd.C.'],\n  wide: ['avanti Cristo', 'dopo Cristo']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['T1', 'T2', 'T3', 'T4'],\n  wide: ['1º trimestre', '2º trimestre', '3º trimestre', '4º trimestre']\n};\nvar monthValues = {\n  narrow: ['G', 'F', 'M', 'A', 'M', 'G', 'L', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['gen', 'feb', 'mar', 'apr', 'mag', 'giu', 'lug', 'ago', 'set', 'ott', 'nov', 'dic'],\n  wide: ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno', 'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'M', 'G', 'V', 'S'],\n  short: ['dom', 'lun', 'mar', 'mer', 'gio', 'ven', 'sab'],\n  abbreviated: ['dom', 'lun', 'mar', 'mer', 'gio', 'ven', 'sab'],\n  wide: ['domenica', 'lunedì', 'martedì', 'mercoledì', 'giovedì', 'venerdì', 'sabato']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'm.',\n    pm: 'p.',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'mattina',\n    afternoon: 'pomeriggio',\n    evening: 'sera',\n    night: 'notte'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'mattina',\n    afternoon: 'pomeriggio',\n    evening: 'sera',\n    night: 'notte'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'mattina',\n    afternoon: 'pomeriggio',\n    evening: 'sera',\n    night: 'notte'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'm.',\n    pm: 'p.',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'di mattina',\n    afternoon: 'del pomeriggio',\n    evening: 'di sera',\n    night: 'di notte'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'di mattina',\n    afternoon: 'del pomeriggio',\n    evening: 'di sera',\n    night: 'di notte'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'di mattina',\n    afternoon: 'del pomeriggio',\n    evening: 'di sera',\n    night: 'di notte'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return String(number);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}