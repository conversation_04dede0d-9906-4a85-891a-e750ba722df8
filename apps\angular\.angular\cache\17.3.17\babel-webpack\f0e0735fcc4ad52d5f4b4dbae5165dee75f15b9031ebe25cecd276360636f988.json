{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AdvancedSearchComponent } from './container/advanced-search.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AdvancedSearchComponent\n}];\nexport class AdvancedSearchRoutingModule {\n  static {\n    this.ɵfac = function AdvancedSearchRoutingModule_Factory(t) {\n      return new (t || AdvancedSearchRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdvancedSearchRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdvancedSearchRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AdvancedSearchComponent", "routes", "path", "component", "AdvancedSearchRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\advanced-search\\advanced-search.routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { AdvancedSearchComponent } from './container/advanced-search.component';\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst routes: Routes = [\r\n\r\n    { path: '', component: AdvancedSearchComponent }\r\n];\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild(routes)],\r\n    exports: [RouterModule]\r\n})\r\nexport class AdvancedSearchRoutingModule { }\r\n"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,uCAAuC;;;AAO/E,MAAMC,MAAM,GAAW,CAEnB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAuB,CAAE,CACnD;AAMD,OAAM,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH1BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEbK,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF1BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}