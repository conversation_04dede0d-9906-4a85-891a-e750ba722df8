{"ast": null, "code": "import { inject } from '@angular/core';\nimport { tap, map } from 'rxjs/operators';\nimport { ConfigStateService } from '@abp/ng.core';\nimport { ExtensionsService, getObjectExtensionEntitiesFromStore, mapEntitiesToContributors, mergeWithDefaultProps } from '@abp/ng.components/extensible';\nimport { ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS, DEFAULT_ACCOUNT_FORM_PROPS } from '../tokens/extensions.token';\nimport * as i0 from \"@angular/core\";\n/**\n * @deprecated Use `accountExtensionsResolver` *function* instead.\n */\nexport class AccountExtensionsGuard {\n  constructor() {\n    this.configState = inject(ConfigStateService);\n    this.extensions = inject(ExtensionsService);\n  }\n  canActivate() {\n    const config = {\n      optional: true\n    };\n    const editFormContributors = inject(ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS, config) || {};\n    return getObjectExtensionEntitiesFromStore(this.configState, 'Identity').pipe(map(entities => ({\n      [\"Account.PersonalSettingsComponent\" /* eAccountComponents.PersonalSettings */]: entities.User\n    })), mapEntitiesToContributors(this.configState, 'AbpIdentity'), tap(objectExtensionContributors => {\n      mergeWithDefaultProps(this.extensions.editFormProps, DEFAULT_ACCOUNT_FORM_PROPS, objectExtensionContributors.editForm, editFormContributors);\n    }), map(() => true));\n  }\n  static {\n    this.ɵfac = function AccountExtensionsGuard_Factory(t) {\n      return new (t || AccountExtensionsGuard)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AccountExtensionsGuard,\n      factory: AccountExtensionsGuard.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "tap", "map", "ConfigStateService", "ExtensionsService", "getObjectExtensionEntitiesFromStore", "mapEntitiesToContributors", "mergeWithDefaultProps", "ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS", "DEFAULT_ACCOUNT_FORM_PROPS", "AccountExtensionsGuard", "constructor", "configState", "extensions", "canActivate", "config", "optional", "editFormContributors", "pipe", "entities", "User", "objectExtensionContributors", "editFormProps", "editForm", "factory", "ɵfac"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\guards\\extensions.guard.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\n\r\nimport { Observable } from 'rxjs';\r\nimport { tap, map } from 'rxjs/operators';\r\n\r\nimport { ConfigStateService, IAbpGuard } from '@abp/ng.core';\r\nimport {\r\n  ExtensionsService,\r\n  getObjectExtensionEntitiesFromStore,\r\n  mapEntitiesToContributors,\r\n  mergeWithDefaultProps,\r\n} from '@abp/ng.components/extensible';\r\n\r\nimport {\r\n  ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS,\r\n  DEFAULT_ACCOUNT_FORM_PROPS,\r\n} from '../tokens/extensions.token';\r\nimport { eAccountComponents } from '../enums/components';\r\n\r\n/**\r\n * @deprecated Use `accountExtensionsResolver` *function* instead.\r\n */\r\n@Injectable()\r\nexport class AccountExtensionsGuard implements IAbpGuard {\r\n  protected readonly configState = inject(ConfigStateService);\r\n  protected readonly extensions = inject(ExtensionsService);\r\n\r\n  canActivate(): Observable<boolean> {\r\n    const config = { optional: true };\r\n\r\n    const editFormContributors = inject(ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS, config) || {};\r\n\r\n    return getObjectExtensionEntitiesFromStore(this.configState, 'Identity').pipe(\r\n      map(entities => ({\r\n        [eAccountComponents.PersonalSettings]: entities.User,\r\n      })),\r\n      mapEntitiesToContributors(this.configState, 'AbpIdentity'),\r\n      tap(objectExtensionContributors => {\r\n        mergeWithDefaultProps(\r\n          this.extensions.editFormProps,\r\n          DEFAULT_ACCOUNT_FORM_PROPS,\r\n          objectExtensionContributors.editForm,\r\n          editFormContributors,\r\n        );\r\n      }),\r\n      map(() => true),\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAGlD,SAASC,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AAEzC,SAASC,kBAAkB,QAAmB,cAAc;AAC5D,SACEC,iBAAiB,EACjBC,mCAAmC,EACnCC,yBAAyB,EACzBC,qBAAqB,QAChB,+BAA+B;AAEtC,SACEC,mCAAmC,EACnCC,0BAA0B,QACrB,4BAA4B;;AAGnC;;;AAIA,OAAM,MAAOC,sBAAsB;EADnCC,YAAA;IAEqB,KAAAC,WAAW,GAAGZ,MAAM,CAACG,kBAAkB,CAAC;IACxC,KAAAU,UAAU,GAAGb,MAAM,CAACI,iBAAiB,CAAC;;EAEzDU,WAAWA,CAAA;IACT,MAAMC,MAAM,GAAG;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAEjC,MAAMC,oBAAoB,GAAGjB,MAAM,CAACQ,mCAAmC,EAAEO,MAAM,CAAC,IAAI,EAAE;IAEtF,OAAOV,mCAAmC,CAAC,IAAI,CAACO,WAAW,EAAE,UAAU,CAAC,CAACM,IAAI,CAC3EhB,GAAG,CAACiB,QAAQ,KAAK;MACf,iFAAuCA,QAAQ,CAACC;KACjD,CAAC,CAAC,EACHd,yBAAyB,CAAC,IAAI,CAACM,WAAW,EAAE,aAAa,CAAC,EAC1DX,GAAG,CAACoB,2BAA2B,IAAG;MAChCd,qBAAqB,CACnB,IAAI,CAACM,UAAU,CAACS,aAAa,EAC7Bb,0BAA0B,EAC1BY,2BAA2B,CAACE,QAAQ,EACpCN,oBAAoB,CACrB;IACH,CAAC,CAAC,EACFf,GAAG,CAAC,MAAM,IAAI,CAAC,CAChB;EACH;;;uBAxBWQ,sBAAsB;IAAA;EAAA;;;aAAtBA,sBAAsB;MAAAc,OAAA,EAAtBd,sBAAsB,CAAAe;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}