{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { HeaderNames } from \"./HeaderNames\";\nimport { HttpClient } from \"./HttpClient\";\n/** @private */\nexport class AccessTokenHttpClient extends HttpClient {\n  constructor(innerClient, accessTokenFactory) {\n    super();\n    this._innerClient = innerClient;\n    this._accessTokenFactory = accessTokenFactory;\n  }\n  send(request) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let allowRetry = true;\n      if (_this._accessTokenFactory && (!_this._accessToken || request.url && request.url.indexOf(\"/negotiate?\") > 0)) {\n        // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\n        allowRetry = false;\n        _this._accessToken = yield _this._accessTokenFactory();\n      }\n      _this._setAuthorizationHeader(request);\n      const response = yield _this._innerClient.send(request);\n      if (allowRetry && response.statusCode === 401 && _this._accessTokenFactory) {\n        _this._accessToken = yield _this._accessTokenFactory();\n        _this._setAuthorizationHeader(request);\n        return yield _this._innerClient.send(request);\n      }\n      return response;\n    })();\n  }\n  _setAuthorizationHeader(request) {\n    if (!request.headers) {\n      request.headers = {};\n    }\n    if (this._accessToken) {\n      request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`;\n    }\n    // don't remove the header if there isn't an access token factory, the user manually added the header in this case\n    else if (this._accessTokenFactory) {\n      if (request.headers[HeaderNames.Authorization]) {\n        delete request.headers[HeaderNames.Authorization];\n      }\n    }\n  }\n  getCookieString(url) {\n    return this._innerClient.getCookieString(url);\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "HttpClient", "AccessTokenHttpClient", "constructor", "innerClient", "accessTokenFactory", "_innerClient", "_accessTokenFactory", "send", "request", "_this", "_asyncToGenerator", "allowRetry", "_accessToken", "url", "indexOf", "_setAuthorizationHeader", "response", "statusCode", "headers", "Authorization", "getCookieString"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\n/** @private */\r\nexport class AccessTokenHttpClient extends HttpClient {\r\n    constructor(innerClient, accessTokenFactory) {\r\n        super();\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n    async send(request) {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n    _setAuthorizationHeader(request) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`;\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[HeaderNames.Authorization]) {\r\n                delete request.headers[HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n    getCookieString(url) {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC;AACA,OAAO,MAAMC,qBAAqB,SAASD,UAAU,CAAC;EAClDE,WAAWA,CAACC,WAAW,EAAEC,kBAAkB,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAGF,WAAW;IAC/B,IAAI,CAACG,mBAAmB,GAAGF,kBAAkB;EACjD;EACMG,IAAIA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAIC,UAAU,GAAG,IAAI;MACrB,IAAIF,KAAI,CAACH,mBAAmB,KAAK,CAACG,KAAI,CAACG,YAAY,IAAKJ,OAAO,CAACK,GAAG,IAAIL,OAAO,CAACK,GAAG,CAACC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAE,CAAC,EAAE;QAC7G;QACAH,UAAU,GAAG,KAAK;QAClBF,KAAI,CAACG,YAAY,SAASH,KAAI,CAACH,mBAAmB,CAAC,CAAC;MACxD;MACAG,KAAI,CAACM,uBAAuB,CAACP,OAAO,CAAC;MACrC,MAAMQ,QAAQ,SAASP,KAAI,CAACJ,YAAY,CAACE,IAAI,CAACC,OAAO,CAAC;MACtD,IAAIG,UAAU,IAAIK,QAAQ,CAACC,UAAU,KAAK,GAAG,IAAIR,KAAI,CAACH,mBAAmB,EAAE;QACvEG,KAAI,CAACG,YAAY,SAASH,KAAI,CAACH,mBAAmB,CAAC,CAAC;QACpDG,KAAI,CAACM,uBAAuB,CAACP,OAAO,CAAC;QACrC,aAAaC,KAAI,CAACJ,YAAY,CAACE,IAAI,CAACC,OAAO,CAAC;MAChD;MACA,OAAOQ,QAAQ;IAAC;EACpB;EACAD,uBAAuBA,CAACP,OAAO,EAAE;IAC7B,IAAI,CAACA,OAAO,CAACU,OAAO,EAAE;MAClBV,OAAO,CAACU,OAAO,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAACN,YAAY,EAAE;MACnBJ,OAAO,CAACU,OAAO,CAACnB,WAAW,CAACoB,aAAa,CAAC,GAAG,UAAU,IAAI,CAACP,YAAY,EAAE;IAC9E;IACA;IAAA,KACK,IAAI,IAAI,CAACN,mBAAmB,EAAE;MAC/B,IAAIE,OAAO,CAACU,OAAO,CAACnB,WAAW,CAACoB,aAAa,CAAC,EAAE;QAC5C,OAAOX,OAAO,CAACU,OAAO,CAACnB,WAAW,CAACoB,aAAa,CAAC;MACrD;IACJ;EACJ;EACAC,eAAeA,CAACP,GAAG,EAAE;IACjB,OAAO,IAAI,CAACR,YAAY,CAACe,eAAe,CAACP,GAAG,CAAC;EACjD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}