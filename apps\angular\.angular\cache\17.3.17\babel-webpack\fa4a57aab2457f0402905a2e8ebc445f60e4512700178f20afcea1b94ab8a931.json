{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['vC', 'nC'],\n  abbreviated: ['vC', 'nC'],\n  wide: ['voor <PERSON>', 'na <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1ste kwartaal', '2de kwartaal', '3de kwartaal', '4de kwartaal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mrt', 'Apr', 'Mei', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des'],\n  wide: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'September', '<PERSON><PERSON><PERSON>', 'November', '<PERSON><PERSON><PERSON>']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'W', 'D', 'V', 'S'],\n  short: ['So', 'Ma', 'Di', 'Wo', 'Do', 'Vr', 'Sa'],\n  abbreviated: ['Son', 'Maa', 'Din', 'Woe', 'Don', 'Vry', 'Sat'],\n  wide: ['Sondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrydag', 'Saterdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  },\n  abbreviated: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  },\n  wide: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  },\n  abbreviated: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  },\n  wide: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 < 20) {\n    switch (rem100) {\n      case 1:\n      case 8:\n        return number + 'ste';\n      default:\n        return number + 'de';\n    }\n  }\n  return number + 'ste';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}