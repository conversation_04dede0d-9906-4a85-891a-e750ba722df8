{"ast": null, "code": "/** Work for rendering Relevant Acvitities in UI.\n *  Properties definitions are following RelevantActivityEnum.\n *  Work for \"Additional Statistics\" tab only.\n */\nexport class ActivityCategoryRow {\n  constructor() {\n    // Banking business\n    this.bankBATitle = 'Banking Business';\n    // Insurance business\n    this.insuBATitle = 'Insurance business';\n    // Fund management business\n    this.fundBATitle = 'Fund management business';\n    // Finance and leasing business\n    this.finaBATitle = 'Finance and leasing business';\n    // Headquarters business\n    this.headBATitle = 'Headquarters business';\n    // Shipping business\n    this.shipBATitle = 'Shipping business';\n    // Holding business\n    this.holdBATitle = 'Holding business';\n    // Intellectual property business\n    this.intelBATitle = 'Intellectual property business';\n    // Distribution and service centre business\n    this.disrBATitle = 'Distribution and service centre business';\n  }\n}", "map": {"version": 3, "names": ["ActivityCategoryRow", "constructor", "bankBATitle", "insuBATitle", "fundBATitle", "finaBATitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shipBATitle", "holdBATitle", "intelBATitle", "disrBATitle"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\services\\activity-category-row.ts"], "sourcesContent": ["/** Work for rendering Relevant Acvitities in UI.\r\n *  Properties definitions are following RelevantActivityEnum.\r\n *  Work for \"Additional Statistics\" tab only.\r\n */\r\nexport class ActivityCategoryRow {\r\n  title: string;\r\n\r\n  /** Current processing year. */\r\n  year: number;\r\n\r\n  // Banking business\r\n  bankBATitle: string = 'Banking Business';\r\n  bankBAValue: number;\r\n\r\n  // Insurance business\r\n  insuBATitle: string = 'Insurance business';\r\n  insuBAValue: number;\r\n\r\n  // Fund management business\r\n  fundBATitle: string = 'Fund management business';\r\n  fundBAValue: number;\r\n\r\n  // Finance and leasing business\r\n  finaBATitle: string = 'Finance and leasing business';\r\n  finaBAValue: number;\r\n\r\n  // Headquarters business\r\n  headBATitle: string = 'Headquarters business';\r\n  headBAValue: number;\r\n\r\n  // Shipping business\r\n  shipBATitle: string = 'Shipping business';\r\n  shipBAValue: number;\r\n\r\n  // Holding business\r\n  holdBATitle: string = 'Holding business';\r\n  holdBAValue: number;\r\n\r\n  // Intellectual property business\r\n  intelBATitle: string = 'Intellectual property business';\r\n  intelBAValue: number;\r\n\r\n  // Distribution and service centre business\r\n  disrBATitle: string = 'Distribution and service centre business';\r\n  disrBAValue: number;\r\n\r\n  total: number;\r\n  /* Work for CARRYING ON ACTIVITY UNDER EACH CATEGORY section only.  */\r\n  totalNumberOfEntitiesWithRelevantActivity: number;\r\n  /* Work for CARRYING ON ACTIVITY UNDER EACH CATEGORY section only.  */\r\n  totalNumberOfEntitiesNoRelevantActivity: number;\r\n\r\n  constructor() {}\r\n}\r\n"], "mappings": "AAAA;;;;AAIA,OAAM,MAAOA,mBAAmB;EAgD9BC,YAAA;IA1CA;IACA,KAAAC,WAAW,GAAW,kBAAkB;IAGxC;IACA,KAAAC,WAAW,GAAW,oBAAoB;IAG1C;IACA,KAAAC,WAAW,GAAW,0BAA0B;IAGhD;IACA,KAAAC,WAAW,GAAW,8BAA8B;IAGpD;IACA,KAAAC,WAAW,GAAW,uBAAuB;IAG7C;IACA,KAAAC,WAAW,GAAW,mBAAmB;IAGzC;IACA,KAAAC,WAAW,GAAW,kBAAkB;IAGxC;IACA,KAAAC,YAAY,GAAW,gCAAgC;IAGvD;IACA,KAAAC,WAAW,GAAW,0CAA0C;EASjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}