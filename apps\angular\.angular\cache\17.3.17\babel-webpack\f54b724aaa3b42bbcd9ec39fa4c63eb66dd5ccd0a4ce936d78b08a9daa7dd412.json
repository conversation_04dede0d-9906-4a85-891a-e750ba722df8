{"ast": null, "code": "import tzParseTimezone from '../_lib/tzParseTimezone/index.js';\n\n/**\n * @name getTimezoneOffset\n * @category Time Zone Helpers\n * @summary Gets the offset in milliseconds between the time zone and Universal Coordinated Time (UTC)\n *\n * @description\n * Returns the time zone offset from UTC time in milliseconds for IANA time zones as well\n * as other time zone offset string formats.\n *\n * For time zones where daylight savings time is applicable a `Date` should be passed on\n * the second parameter to ensure the offset correctly accounts for DST at that time of\n * year. When omitted, the current date is used.\n *\n * @param {String} timeZone - the time zone of this local time, can be an offset or IANA time zone\n * @param {Date|Number} [date] - the date with values representing the local time\n * @returns {Number} the time zone offset in milliseconds\n *\n * @example\n * const result = getTimezoneOffset('-07:00')\n *   //=> -******** (-7 * 60 * 60 * 1000)\n * const result = getTimezoneOffset('Africa/Johannesburg')\n *   //=> 7200000 (2 * 60 * 60 * 1000)\n * const result = getTimezoneOffset('America/New_York', new Date(2016, 0, 1))\n *   //=> -******** (-5 * 60 * 60 * 1000)\n * const result = getTimezoneOffset('America/New_York', new Date(2016, 6, 1))\n *   //=> -******** (-4 * 60 * 60 * 1000)\n */\nexport default function getTimezoneOffset(timeZone, date) {\n  return -tzParseTimezone(timeZone, date);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}