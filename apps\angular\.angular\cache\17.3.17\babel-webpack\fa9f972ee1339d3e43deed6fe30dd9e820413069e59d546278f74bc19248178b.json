{"ast": null, "code": "/*!\n * html2pdf.js v0.10.3\n * Copyright (c) 2025 <PERSON>\n * Released under the MIT License.\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory(require(\"jspdf\"), require(\"html2canvas\"));else if (typeof define === 'function' && define.amd) define(\"html2pdf\", [\"jspdf\", \"html2canvas\"], factory);else if (typeof exports === 'object') exports[\"html2pdf\"] = factory(require(\"jspdf\"), require(\"html2canvas\"));else root[\"html2pdf\"] = factory(root[\"jspdf\"], root[\"html2canvas\"]);\n})(self, function (__WEBPACK_EXTERNAL_MODULE_jspdf__, __WEBPACK_EXTERNAL_MODULE_html2canvas__) {\n  return /******/function () {\n    // webpackBootstrap\n    /******/\n    var __webpack_modules__ = {\n      /***/\"./src/plugin/hyperlinks.js\": (\n      /*!**********************************!*\\\n        !*** ./src/plugin/hyperlinks.js ***!\n        \\**********************************/\n      /***/\n      function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n        \"use strict\";\n\n        __webpack_require__.r(__webpack_exports__);\n        /* harmony import */\n        var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */\"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n        /* harmony import */\n        var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_0__);\n        /* harmony import */\n        var core_js_modules_es_string_link_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.string.link.js */\"./node_modules/core-js/modules/es.string.link.js\");\n        /* harmony import */\n        var core_js_modules_es_string_link_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_link_js__WEBPACK_IMPORTED_MODULE_1__);\n        /* harmony import */\n        var _worker_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../worker.js */\"./src/worker.js\");\n        /* harmony import */\n        var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.js */\"./src/utils.js\");\n\n        // Add hyperlink functionality to the PDF creation.\n        // Main link array, and refs to original functions.\n\n        var linkInfo = [];\n        var orig = {\n          toContainer: _worker_js__WEBPACK_IMPORTED_MODULE_2__.default.prototype.toContainer,\n          toPdf: _worker_js__WEBPACK_IMPORTED_MODULE_2__.default.prototype.toPdf\n        };\n        _worker_js__WEBPACK_IMPORTED_MODULE_2__.default.prototype.toContainer = function toContainer() {\n          return orig.toContainer.call(this).then(function toContainer_hyperlink() {\n            // Retrieve hyperlink info if the option is enabled.\n            if (this.opt.enableLinks) {\n              // Find all anchor tags and get the container's bounds for reference.\n              var container = this.prop.container;\n              var links = container.querySelectorAll('a');\n              var containerRect = (0, _utils_js__WEBPACK_IMPORTED_MODULE_3__.unitConvert)(container.getBoundingClientRect(), this.prop.pageSize.k);\n              linkInfo = []; // Loop through each anchor tag.\n\n              Array.prototype.forEach.call(links, function (link) {\n                // Treat each client rect as a separate link (for text-wrapping).\n                var clientRects = link.getClientRects();\n                for (var i = 0; i < clientRects.length; i++) {\n                  var clientRect = (0, _utils_js__WEBPACK_IMPORTED_MODULE_3__.unitConvert)(clientRects[i], this.prop.pageSize.k);\n                  clientRect.left -= containerRect.left;\n                  clientRect.top -= containerRect.top;\n                  var page = Math.floor(clientRect.top / this.prop.pageSize.inner.height) + 1;\n                  var top = this.opt.margin[0] + clientRect.top % this.prop.pageSize.inner.height;\n                  var left = this.opt.margin[1] + clientRect.left;\n                  linkInfo.push({\n                    page: page,\n                    top: top,\n                    left: left,\n                    clientRect: clientRect,\n                    link: link\n                  });\n                }\n              }, this);\n            }\n          });\n        };\n        _worker_js__WEBPACK_IMPORTED_MODULE_2__.default.prototype.toPdf = function toPdf() {\n          return orig.toPdf.call(this).then(function toPdf_hyperlink() {\n            // Add hyperlinks if the option is enabled.\n            if (this.opt.enableLinks) {\n              // Attach each anchor tag based on info from toContainer().\n              linkInfo.forEach(function (l) {\n                this.prop.pdf.setPage(l.page);\n                this.prop.pdf.link(l.left, l.top, l.clientRect.width, l.clientRect.height, {\n                  url: l.link.href\n                });\n              }, this); // Reset the active page of the PDF to the final page.\n\n              var nPages = this.prop.pdf.internal.getNumberOfPages();\n              this.prop.pdf.setPage(nPages);\n            }\n          });\n        };\n\n        /***/\n      }),\n      /***/\"./src/plugin/jspdf-plugin.js\": (\n      /*!************************************!*\\\n        !*** ./src/plugin/jspdf-plugin.js ***!\n        \\************************************/\n      /***/\n      function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n        \"use strict\";\n\n        __webpack_require__.r(__webpack_exports__);\n        /* harmony import */\n        var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */\"./node_modules/core-js/modules/es.symbol.js\");\n        /* harmony import */\n        var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n        /* harmony import */\n        var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */\"./node_modules/core-js/modules/es.symbol.description.js\");\n        /* harmony import */\n        var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n        /* harmony import */\n        var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */\"./node_modules/core-js/modules/es.object.to-string.js\");\n        /* harmony import */\n        var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__);\n        /* harmony import */\n        var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */\"./node_modules/core-js/modules/es.symbol.iterator.js\");\n        /* harmony import */\n        var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_3__);\n        /* harmony import */\n        var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */\"./node_modules/core-js/modules/es.array.iterator.js\");\n        /* harmony import */\n        var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n        /* harmony import */\n        var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */\"./node_modules/core-js/modules/es.string.iterator.js\");\n        /* harmony import */\n        var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_5__);\n        /* harmony import */\n        var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */\"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n        /* harmony import */\n        var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_6__);\n        /* harmony import */\n        var jspdf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jspdf */\"jspdf\");\n        /* harmony import */\n        var jspdf__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(jspdf__WEBPACK_IMPORTED_MODULE_7__);\n        function _typeof(obj) {\n          \"@babel/helpers - typeof\";\n\n          if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n            _typeof = function _typeof(obj) {\n              return typeof obj;\n            };\n          } else {\n            _typeof = function _typeof(obj) {\n              return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n            };\n          }\n          return _typeof(obj);\n        }\n\n        // Import dependencies.\n        // Get dimensions of a PDF page, as determined by jsPDF.\n\n        jspdf__WEBPACK_IMPORTED_MODULE_7__.jsPDF.getPageSize = function (orientation, unit, format) {\n          // Decode options object\n          if (_typeof(orientation) === 'object') {\n            var options = orientation;\n            orientation = options.orientation;\n            unit = options.unit || unit;\n            format = options.format || format;\n          } // Default options\n\n          unit = unit || 'mm';\n          format = format || 'a4';\n          orientation = ('' + (orientation || 'P')).toLowerCase();\n          var format_as_string = ('' + format).toLowerCase(); // Size in pt of various paper formats\n\n          var pageFormats = {\n            'a0': [2383.94, 3370.39],\n            'a1': [1683.78, 2383.94],\n            'a2': [1190.55, 1683.78],\n            'a3': [841.89, 1190.55],\n            'a4': [595.28, 841.89],\n            'a5': [419.53, 595.28],\n            'a6': [297.64, 419.53],\n            'a7': [209.76, 297.64],\n            'a8': [147.40, 209.76],\n            'a9': [104.88, 147.40],\n            'a10': [73.70, 104.88],\n            'b0': [2834.65, 4008.19],\n            'b1': [2004.09, 2834.65],\n            'b2': [1417.32, 2004.09],\n            'b3': [1000.63, 1417.32],\n            'b4': [708.66, 1000.63],\n            'b5': [498.90, 708.66],\n            'b6': [354.33, 498.90],\n            'b7': [249.45, 354.33],\n            'b8': [175.75, 249.45],\n            'b9': [124.72, 175.75],\n            'b10': [87.87, 124.72],\n            'c0': [2599.37, 3676.54],\n            'c1': [1836.85, 2599.37],\n            'c2': [1298.27, 1836.85],\n            'c3': [918.43, 1298.27],\n            'c4': [649.13, 918.43],\n            'c5': [459.21, 649.13],\n            'c6': [323.15, 459.21],\n            'c7': [229.61, 323.15],\n            'c8': [161.57, 229.61],\n            'c9': [113.39, 161.57],\n            'c10': [79.37, 113.39],\n            'dl': [311.81, 623.62],\n            'letter': [612, 792],\n            'government-letter': [576, 756],\n            'legal': [612, 1008],\n            'junior-legal': [576, 360],\n            'ledger': [1224, 792],\n            'tabloid': [792, 1224],\n            'credit-card': [153, 243]\n          }; // Unit conversion\n\n          switch (unit) {\n            case 'pt':\n              var k = 1;\n              break;\n            case 'mm':\n              var k = 72 / 25.4;\n              break;\n            case 'cm':\n              var k = 72 / 2.54;\n              break;\n            case 'in':\n              var k = 72;\n              break;\n            case 'px':\n              var k = 72 / 96;\n              break;\n            case 'pc':\n              var k = 12;\n              break;\n            case 'em':\n              var k = 12;\n              break;\n            case 'ex':\n              var k = 6;\n              break;\n            default:\n              throw 'Invalid unit: ' + unit;\n          } // Dimensions are stored as user units and converted to points on output\n\n          if (pageFormats.hasOwnProperty(format_as_string)) {\n            var pageHeight = pageFormats[format_as_string][1] / k;\n            var pageWidth = pageFormats[format_as_string][0] / k;\n          } else {\n            try {\n              var pageHeight = format[1];\n              var pageWidth = format[0];\n            } catch (err) {\n              throw new Error('Invalid format: ' + format);\n            }\n          } // Handle page orientation\n\n          if (orientation === 'p' || orientation === 'portrait') {\n            orientation = 'p';\n            if (pageWidth > pageHeight) {\n              var tmp = pageWidth;\n              pageWidth = pageHeight;\n              pageHeight = tmp;\n            }\n          } else if (orientation === 'l' || orientation === 'landscape') {\n            orientation = 'l';\n            if (pageHeight > pageWidth) {\n              var tmp = pageWidth;\n              pageWidth = pageHeight;\n              pageHeight = tmp;\n            }\n          } else {\n            throw 'Invalid orientation: ' + orientation;\n          } // Return information (k is the unit conversion ratio from pts)\n\n          var info = {\n            'width': pageWidth,\n            'height': pageHeight,\n            'unit': unit,\n            'k': k\n          };\n          return info;\n        };\n\n        /* harmony default export */\n        __webpack_exports__[\"default\"] = jspdf__WEBPACK_IMPORTED_MODULE_7__.jsPDF;\n\n        /***/\n      }),\n      /***/\"./src/plugin/pagebreaks.js\": (\n      /*!**********************************!*\\\n        !*** ./src/plugin/pagebreaks.js ***!\n        \\**********************************/\n      /***/\n      function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n        \"use strict\";\n\n        __webpack_require__.r(__webpack_exports__);\n        /* harmony import */\n        var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */\"./node_modules/core-js/modules/es.array.concat.js\");\n        /* harmony import */\n        var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);\n        /* harmony import */\n        var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.slice.js */\"./node_modules/core-js/modules/es.array.slice.js\");\n        /* harmony import */\n        var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_1__);\n        /* harmony import */\n        var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.join.js */\"./node_modules/core-js/modules/es.array.join.js\");\n        /* harmony import */\n        var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_2__);\n        /* harmony import */\n        var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */\"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n        /* harmony import */\n        var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_3__);\n        /* harmony import */\n        var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */\"./node_modules/core-js/modules/es.object.keys.js\");\n        /* harmony import */\n        var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);\n        /* harmony import */\n        var _worker_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../worker.js */\"./src/worker.js\");\n        /* harmony import */\n        var _utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils.js */\"./src/utils.js\");\n\n        /* Pagebreak plugin:\n        \n            Adds page-break functionality to the html2pdf library. Page-breaks can be\n            enabled by CSS styles, set on individual elements using selectors, or\n            avoided from breaking inside all elements.\n        \n            Options on the `opt.pagebreak` object:\n        \n            mode:   String or array of strings: 'avoid-all', 'css', and/or 'legacy'\n                    Default: ['css', 'legacy']\n        \n            before: String or array of CSS selectors for which to add page-breaks\n                    before each element. Can be a specific element with an ID\n                    ('#myID'), all elements of a type (e.g. 'img'), all of a class\n                    ('.myClass'), or even '*' to match every element.\n        \n            after:  Like 'before', but adds a page-break immediately after the element.\n        \n            avoid:  Like 'before', but avoids page-breaks on these elements. You can\n                    enable this feature on every element using the 'avoid-all' mode.\n        */\n        // Refs to original functions.\n\n        var orig = {\n          toContainer: _worker_js__WEBPACK_IMPORTED_MODULE_5__.default.prototype.toContainer\n        }; // Add pagebreak default options to the Worker template.\n\n        _worker_js__WEBPACK_IMPORTED_MODULE_5__.default.template.opt.pagebreak = {\n          mode: ['css', 'legacy'],\n          before: [],\n          after: [],\n          avoid: []\n        };\n        _worker_js__WEBPACK_IMPORTED_MODULE_5__.default.prototype.toContainer = function toContainer() {\n          return orig.toContainer.call(this).then(function toContainer_pagebreak() {\n            // Setup root element and inner page height.\n            var root = this.prop.container;\n            var pxPageHeight = this.prop.pageSize.inner.px.height; // Check all requested modes.\n\n            var modeSrc = [].concat(this.opt.pagebreak.mode);\n            var mode = {\n              avoidAll: modeSrc.indexOf('avoid-all') !== -1,\n              css: modeSrc.indexOf('css') !== -1,\n              legacy: modeSrc.indexOf('legacy') !== -1\n            }; // Get arrays of all explicitly requested elements.\n\n            var select = {};\n            var self = this;\n            ['before', 'after', 'avoid'].forEach(function (key) {\n              var all = mode.avoidAll && key === 'avoid';\n              select[key] = all ? [] : [].concat(self.opt.pagebreak[key] || []);\n              if (select[key].length > 0) {\n                select[key] = Array.prototype.slice.call(root.querySelectorAll(select[key].join(', ')));\n              }\n            }); // Get all legacy page-break elements.\n\n            var legacyEls = root.querySelectorAll('.html2pdf__page-break');\n            legacyEls = Array.prototype.slice.call(legacyEls); // Loop through all elements.\n\n            var els = root.querySelectorAll('*');\n            Array.prototype.forEach.call(els, function pagebreak_loop(el) {\n              // Setup pagebreak rules based on legacy and avoidAll modes.\n              var rules = {\n                before: false,\n                after: mode.legacy && legacyEls.indexOf(el) !== -1,\n                avoid: mode.avoidAll\n              }; // Add rules for css mode.\n\n              if (mode.css) {\n                // TODO: Check if this is valid with iFrames.\n                var style = window.getComputedStyle(el); // TODO: Handle 'left' and 'right' correctly.\n                // TODO: Add support for 'avoid' on breakBefore/After.\n\n                var breakOpt = ['always', 'page', 'left', 'right'];\n                var avoidOpt = ['avoid', 'avoid-page'];\n                rules = {\n                  before: rules.before || breakOpt.indexOf(style.breakBefore || style.pageBreakBefore) !== -1,\n                  after: rules.after || breakOpt.indexOf(style.breakAfter || style.pageBreakAfter) !== -1,\n                  avoid: rules.avoid || avoidOpt.indexOf(style.breakInside || style.pageBreakInside) !== -1\n                };\n              } // Add rules for explicit requests.\n\n              Object.keys(rules).forEach(function (key) {\n                rules[key] = rules[key] || select[key].indexOf(el) !== -1;\n              }); // Get element position on the screen.\n              // TODO: Subtract the top of the container from clientRect.top/bottom?\n\n              var clientRect = el.getBoundingClientRect(); // Avoid: Check if a break happens mid-element.\n\n              if (rules.avoid && !rules.before) {\n                var startPage = Math.floor(clientRect.top / pxPageHeight);\n                var endPage = Math.floor(clientRect.bottom / pxPageHeight);\n                var nPages = Math.abs(clientRect.bottom - clientRect.top) / pxPageHeight; // Turn on rules.before if the el is broken and is at most one page long.\n\n                if (endPage !== startPage && nPages <= 1) {\n                  rules.before = true;\n                }\n              } // Before: Create a padding div to push the element to the next page.\n\n              if (rules.before) {\n                var pad = (0, _utils_js__WEBPACK_IMPORTED_MODULE_6__.createElement)('div', {\n                  style: {\n                    display: 'block',\n                    height: pxPageHeight - clientRect.top % pxPageHeight + 'px'\n                  }\n                });\n                el.parentNode.insertBefore(pad, el);\n              } // After: Create a padding div to fill the remaining page.\n\n              if (rules.after) {\n                var pad = (0, _utils_js__WEBPACK_IMPORTED_MODULE_6__.createElement)('div', {\n                  style: {\n                    display: 'block',\n                    height: pxPageHeight - clientRect.bottom % pxPageHeight + 'px'\n                  }\n                });\n                el.parentNode.insertBefore(pad, el.nextSibling);\n              }\n            });\n          });\n        };\n\n        /***/\n      }),\n      /***/\"./src/utils.js\": (\n      /*!**********************!*\\\n        !*** ./src/utils.js ***!\n        \\**********************/\n      /***/\n      function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n        \"use strict\";\n\n        __webpack_require__.r(__webpack_exports__);\n        /* harmony export */\n        __webpack_require__.d(__webpack_exports__, {\n          /* harmony export */\"objType\": function () {\n            return /* binding */objType;\n          },\n          /* harmony export */\"createElement\": function () {\n            return /* binding */createElement;\n          },\n          /* harmony export */\"cloneNode\": function () {\n            return /* binding */cloneNode;\n          },\n          /* harmony export */\"unitConvert\": function () {\n            return /* binding */unitConvert;\n          },\n          /* harmony export */\"toPx\": function () {\n            return /* binding */toPx;\n          }\n          /* harmony export */\n        });\n        /* harmony import */\n        var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */\"./node_modules/core-js/modules/es.number.constructor.js\");\n        /* harmony import */\n        var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_0__);\n        /* harmony import */\n        var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.js */\"./node_modules/core-js/modules/es.symbol.js\");\n        /* harmony import */\n        var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_1__);\n        /* harmony import */\n        var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */\"./node_modules/core-js/modules/es.symbol.description.js\");\n        /* harmony import */\n        var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_2__);\n        /* harmony import */\n        var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */\"./node_modules/core-js/modules/es.object.to-string.js\");\n        /* harmony import */\n        var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_3__);\n        /* harmony import */\n        var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */\"./node_modules/core-js/modules/es.symbol.iterator.js\");\n        /* harmony import */\n        var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n        /* harmony import */\n        var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */\"./node_modules/core-js/modules/es.array.iterator.js\");\n        /* harmony import */\n        var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_5__);\n        /* harmony import */\n        var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */\"./node_modules/core-js/modules/es.string.iterator.js\");\n        /* harmony import */\n        var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__);\n        /* harmony import */\n        var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */\"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n        /* harmony import */\n        var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_7__);\n        function _typeof(obj) {\n          \"@babel/helpers - typeof\";\n\n          if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n            _typeof = function _typeof(obj) {\n              return typeof obj;\n            };\n          } else {\n            _typeof = function _typeof(obj) {\n              return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n            };\n          }\n          return _typeof(obj);\n        }\n\n        // Determine the type of a variable/object.\n        var objType = function objType(obj) {\n          var type = _typeof(obj);\n          if (type === 'undefined') return 'undefined';else if (type === 'string' || obj instanceof String) return 'string';else if (type === 'number' || obj instanceof Number) return 'number';else if (type === 'function' || obj instanceof Function) return 'function';else if (!!obj && obj.constructor === Array) return 'array';else if (obj && obj.nodeType === 1) return 'element';else if (type === 'object') return 'object';else return 'unknown';\n        }; // Create an HTML element with optional className, innerHTML, and style.\n\n        var createElement = function createElement(tagName, opt) {\n          var el = document.createElement(tagName);\n          if (opt.className) el.className = opt.className;\n          if (opt.innerHTML) {\n            el.innerHTML = opt.innerHTML;\n            var scripts = el.getElementsByTagName('script');\n            for (var i = scripts.length; i-- > 0; null) {\n              scripts[i].parentNode.removeChild(scripts[i]);\n            }\n          }\n          for (var key in opt.style) {\n            el.style[key] = opt.style[key];\n          }\n          return el;\n        }; // Deep-clone a node and preserve contents/properties.\n\n        var cloneNode = function cloneNode(node, javascriptEnabled) {\n          // Recursively clone the node.\n          var clone = node.nodeType === 3 ? document.createTextNode(node.nodeValue) : node.cloneNode(false);\n          for (var child = node.firstChild; child; child = child.nextSibling) {\n            if (javascriptEnabled === true || child.nodeType !== 1 || child.nodeName !== 'SCRIPT') {\n              clone.appendChild(cloneNode(child, javascriptEnabled));\n            }\n          }\n          if (node.nodeType === 1) {\n            // Preserve contents/properties of special nodes.\n            if (node.nodeName === 'CANVAS') {\n              clone.width = node.width;\n              clone.height = node.height;\n              clone.getContext('2d').drawImage(node, 0, 0);\n            } else if (node.nodeName === 'TEXTAREA' || node.nodeName === 'SELECT') {\n              clone.value = node.value;\n            } // Preserve the node's scroll position when it loads.\n\n            clone.addEventListener('load', function () {\n              clone.scrollTop = node.scrollTop;\n              clone.scrollLeft = node.scrollLeft;\n            }, true);\n          } // Return the cloned node.\n\n          return clone;\n        }; // Convert units from px using the conversion value 'k' from jsPDF.\n\n        var unitConvert = function unitConvert(obj, k) {\n          if (objType(obj) === 'number') {\n            return obj * 72 / 96 / k;\n          } else {\n            var newObj = {};\n            for (var key in obj) {\n              newObj[key] = obj[key] * 72 / 96 / k;\n            }\n            return newObj;\n          }\n        }; // Convert units to px using the conversion value 'k' from jsPDF.\n\n        var toPx = function toPx(val, k) {\n          return Math.floor(val * k / 72 * 96);\n        };\n\n        /***/\n      }),\n      /***/\"./src/worker.js\": (\n      /*!***********************!*\\\n        !*** ./src/worker.js ***!\n        \\***********************/\n      /***/\n      function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n        \"use strict\";\n\n        __webpack_require__.r(__webpack_exports__);\n        /* harmony import */\n        var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.assign.js */\"./node_modules/core-js/modules/es.object.assign.js\");\n        /* harmony import */\n        var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_0__);\n        /* harmony import */\n        var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.map.js */\"./node_modules/core-js/modules/es.array.map.js\");\n        /* harmony import */\n        var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_1__);\n        /* harmony import */\n        var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */\"./node_modules/core-js/modules/es.object.keys.js\");\n        /* harmony import */\n        var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_2__);\n        /* harmony import */\n        var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */\"./node_modules/core-js/modules/es.array.concat.js\");\n        /* harmony import */\n        var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__);\n        /* harmony import */\n        var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */\"./node_modules/core-js/modules/es.object.to-string.js\");\n        /* harmony import */\n        var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_4__);\n        /* harmony import */\n        var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */\"./node_modules/core-js/modules/es.regexp.to-string.js\");\n        /* harmony import */\n        var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5__);\n        /* harmony import */\n        var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.function.name.js */\"./node_modules/core-js/modules/es.function.name.js\");\n        /* harmony import */\n        var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_6__);\n        /* harmony import */\n        var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */\"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n        /* harmony import */\n        var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_7__);\n        /* harmony import */\n        var jspdf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! jspdf */\"jspdf\");\n        /* harmony import */\n        var jspdf__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(jspdf__WEBPACK_IMPORTED_MODULE_8__);\n        /* harmony import */\n        var html2canvas__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html2canvas */\"html2canvas\");\n        /* harmony import */\n        var html2canvas__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_9__);\n        /* harmony import */\n        var _utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils.js */\"./src/utils.js\");\n        /* harmony import */\n        var es6_promise__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! es6-promise */\"./node_modules/es6-promise/dist/es6-promise.js\");\n        /* harmony import */\n        var es6_promise__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(es6_promise__WEBPACK_IMPORTED_MODULE_11__);\n        var Promise = es6_promise__WEBPACK_IMPORTED_MODULE_11___default().Promise;\n        /* ----- CONSTRUCTOR ----- */\n\n        var Worker = function Worker(opt) {\n          // Create the root parent for the proto chain, and the starting Worker.\n          var root = Object.assign(Worker.convert(Promise.resolve()), JSON.parse(JSON.stringify(Worker.template)));\n          var self = Worker.convert(Promise.resolve(), root); // Set progress, optional settings, and return.\n\n          self = self.setProgress(1, Worker, 1, [Worker]);\n          self = self.set(opt);\n          return self;\n        }; // Boilerplate for subclassing Promise.\n\n        Worker.prototype = Object.create(Promise.prototype);\n        Worker.prototype.constructor = Worker; // Converts/casts promises into Workers.\n\n        Worker.convert = function convert(promise, inherit) {\n          // Uses prototypal inheritance to receive changes made to ancestors' properties.\n          promise.__proto__ = inherit || Worker.prototype;\n          return promise;\n        };\n        Worker.template = {\n          prop: {\n            src: null,\n            container: null,\n            overlay: null,\n            canvas: null,\n            img: null,\n            pdf: null,\n            pageSize: null\n          },\n          progress: {\n            val: 0,\n            state: null,\n            n: 0,\n            stack: []\n          },\n          opt: {\n            filename: 'file.pdf',\n            margin: [0, 0, 0, 0],\n            image: {\n              type: 'jpeg',\n              quality: 0.95\n            },\n            enableLinks: true,\n            html2canvas: {},\n            jsPDF: {}\n          }\n        };\n        /* ----- FROM / TO ----- */\n\n        Worker.prototype.from = function from(src, type) {\n          function getType(src) {\n            switch ((0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.objType)(src)) {\n              case 'string':\n                return 'string';\n              case 'element':\n                return src.nodeName.toLowerCase && src.nodeName.toLowerCase() === 'canvas' ? 'canvas' : 'element';\n              default:\n                return 'unknown';\n            }\n          }\n          return this.then(function from_main() {\n            type = type || getType(src);\n            switch (type) {\n              case 'string':\n                return this.set({\n                  src: (0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.createElement)('div', {\n                    innerHTML: src\n                  })\n                });\n              case 'element':\n                return this.set({\n                  src: src\n                });\n              case 'canvas':\n                return this.set({\n                  canvas: src\n                });\n              case 'img':\n                return this.set({\n                  img: src\n                });\n              default:\n                return this.error('Unknown source type.');\n            }\n          });\n        };\n        Worker.prototype.to = function to(target) {\n          // Route the 'to' request to the appropriate method.\n          switch (target) {\n            case 'container':\n              return this.toContainer();\n            case 'canvas':\n              return this.toCanvas();\n            case 'img':\n              return this.toImg();\n            case 'pdf':\n              return this.toPdf();\n            default:\n              return this.error('Invalid target.');\n          }\n        };\n        Worker.prototype.toContainer = function toContainer() {\n          // Set up function prerequisites.\n          var prereqs = [function checkSrc() {\n            return this.prop.src || this.error('Cannot duplicate - no source HTML.');\n          }, function checkPageSize() {\n            return this.prop.pageSize || this.setPageSize();\n          }];\n          return this.thenList(prereqs).then(function toContainer_main() {\n            // Define the CSS styles for the container and its overlay parent.\n            var overlayCSS = {\n              position: 'fixed',\n              overflow: 'hidden',\n              zIndex: 1000,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              top: 0,\n              backgroundColor: 'rgba(0,0,0,0.8)'\n            };\n            var containerCSS = {\n              position: 'absolute',\n              width: this.prop.pageSize.inner.width + this.prop.pageSize.unit,\n              left: 0,\n              right: 0,\n              top: 0,\n              height: 'auto',\n              margin: 'auto',\n              backgroundColor: 'white'\n            }; // Set the overlay to hidden (could be changed in the future to provide a print preview).\n\n            overlayCSS.opacity = 0; // Create and attach the elements.\n\n            var source = (0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.cloneNode)(this.prop.src, this.opt.html2canvas.javascriptEnabled);\n            this.prop.overlay = (0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.createElement)('div', {\n              className: 'html2pdf__overlay',\n              style: overlayCSS\n            });\n            this.prop.container = (0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.createElement)('div', {\n              className: 'html2pdf__container',\n              style: containerCSS\n            });\n            this.prop.container.appendChild(source);\n            this.prop.overlay.appendChild(this.prop.container);\n            document.body.appendChild(this.prop.overlay);\n          });\n        };\n        Worker.prototype.toCanvas = function toCanvas() {\n          // Set up function prerequisites.\n          var prereqs = [function checkContainer() {\n            return document.body.contains(this.prop.container) || this.toContainer();\n          }]; // Fulfill prereqs then create the canvas.\n\n          return this.thenList(prereqs).then(function toCanvas_main() {\n            // Handle old-fashioned 'onrendered' argument.\n            var options = Object.assign({}, this.opt.html2canvas);\n            delete options.onrendered;\n            return html2canvas__WEBPACK_IMPORTED_MODULE_9__(this.prop.container, options);\n          }).then(function toCanvas_post(canvas) {\n            // Handle old-fashioned 'onrendered' argument.\n            var onRendered = this.opt.html2canvas.onrendered || function () {};\n            onRendered(canvas);\n            this.prop.canvas = canvas;\n            document.body.removeChild(this.prop.overlay);\n          });\n        };\n        Worker.prototype.toImg = function toImg() {\n          // Set up function prerequisites.\n          var prereqs = [function checkCanvas() {\n            return this.prop.canvas || this.toCanvas();\n          }]; // Fulfill prereqs then create the image.\n\n          return this.thenList(prereqs).then(function toImg_main() {\n            var imgData = this.prop.canvas.toDataURL('image/' + this.opt.image.type, this.opt.image.quality);\n            this.prop.img = document.createElement('img');\n            this.prop.img.src = imgData;\n          });\n        };\n        Worker.prototype.toPdf = function toPdf() {\n          // Set up function prerequisites.\n          var prereqs = [function checkCanvas() {\n            return this.prop.canvas || this.toCanvas();\n          }, function checkPageSize() {\n            return this.prop.pageSize || this.setPageSize();\n          }]; // Fulfill prereqs then create the image.\n\n          return this.thenList(prereqs).then(function toPdf_main() {\n            // Create local copies of frequently used properties.\n            var canvas = this.prop.canvas;\n            var opt = this.opt; // Calculate the number of pages.\n\n            var pxFullHeight = canvas.height;\n            var pxPageHeight = Math.floor(canvas.width * this.prop.pageSize.inner.ratio);\n            var nPages = Math.ceil(pxFullHeight / pxPageHeight); // Define pageHeight separately so it can be trimmed on the final page.\n\n            var pageHeight = this.prop.pageSize.inner.height; // Create a one-page canvas to split up the full image.\n\n            var pageCanvas = document.createElement('canvas');\n            var pageCtx = pageCanvas.getContext('2d');\n            pageCanvas.width = canvas.width;\n            pageCanvas.height = pxPageHeight; // Initialize the PDF.\n\n            this.prop.pdf = this.prop.pdf || new jspdf__WEBPACK_IMPORTED_MODULE_8__.jsPDF(opt.jsPDF);\n            for (var page = 0; page < nPages; page++) {\n              // Trim the final page to reduce file size.\n              if (page === nPages - 1 && pxFullHeight % pxPageHeight !== 0) {\n                pageCanvas.height = pxFullHeight % pxPageHeight;\n                pageHeight = pageCanvas.height * this.prop.pageSize.inner.width / pageCanvas.width;\n              } // Display the page.\n\n              var w = pageCanvas.width;\n              var h = pageCanvas.height;\n              pageCtx.fillStyle = 'white';\n              pageCtx.fillRect(0, 0, w, h);\n              pageCtx.drawImage(canvas, 0, page * pxPageHeight, w, h, 0, 0, w, h); // Add the page to the PDF.\n\n              if (page) this.prop.pdf.addPage();\n              var imgData = pageCanvas.toDataURL('image/' + opt.image.type, opt.image.quality);\n              this.prop.pdf.addImage(imgData, opt.image.type, opt.margin[1], opt.margin[0], this.prop.pageSize.inner.width, pageHeight);\n            }\n          });\n        };\n        /* ----- OUTPUT / SAVE ----- */\n\n        Worker.prototype.output = function output(type, options, src) {\n          // Redirect requests to the correct function (outputPdf / outputImg).\n          src = src || 'pdf';\n          if (src.toLowerCase() === 'img' || src.toLowerCase() === 'image') {\n            return this.outputImg(type, options);\n          } else {\n            return this.outputPdf(type, options);\n          }\n        };\n        Worker.prototype.outputPdf = function outputPdf(type, options) {\n          // Set up function prerequisites.\n          var prereqs = [function checkPdf() {\n            return this.prop.pdf || this.toPdf();\n          }]; // Fulfill prereqs then perform the appropriate output.\n\n          return this.thenList(prereqs).then(function outputPdf_main() {\n            /* Currently implemented output types:\n             *    https://rawgit.com/MrRio/jsPDF/master/docs/jspdf.js.html#line992\n             *  save(options), arraybuffer, blob, bloburi/bloburl,\n             *  datauristring/dataurlstring, dataurlnewwindow, datauri/dataurl\n             */\n            return this.prop.pdf.output(type, options);\n          });\n        };\n        Worker.prototype.outputImg = function outputImg(type, options) {\n          // Set up function prerequisites.\n          var prereqs = [function checkImg() {\n            return this.prop.img || this.toImg();\n          }]; // Fulfill prereqs then perform the appropriate output.\n\n          return this.thenList(prereqs).then(function outputImg_main() {\n            switch (type) {\n              case undefined:\n              case 'img':\n                return this.prop.img;\n              case 'datauristring':\n              case 'dataurlstring':\n                return this.prop.img.src;\n              case 'datauri':\n              case 'dataurl':\n                return document.location.href = this.prop.img.src;\n              default:\n                throw 'Image output type \"' + type + '\" is not supported.';\n            }\n          });\n        };\n        Worker.prototype.save = function save(filename) {\n          // Set up function prerequisites.\n          var prereqs = [function checkPdf() {\n            return this.prop.pdf || this.toPdf();\n          }]; // Fulfill prereqs, update the filename (if provided), and save the PDF.\n\n          return this.thenList(prereqs).set(filename ? {\n            filename: filename\n          } : null).then(function save_main() {\n            this.prop.pdf.save(this.opt.filename);\n          });\n        };\n        /* ----- SET / GET ----- */\n\n        Worker.prototype.set = function set(opt) {\n          // TODO: Implement ordered pairs?\n          // Silently ignore invalid or empty input.\n          if ((0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.objType)(opt) !== 'object') {\n            return this;\n          } // Build an array of setter functions to queue.\n\n          var fns = Object.keys(opt || {}).map(function (key) {\n            switch (key) {\n              case 'margin':\n                return this.setMargin.bind(this, opt.margin);\n              case 'jsPDF':\n                return function set_jsPDF() {\n                  this.opt.jsPDF = opt.jsPDF;\n                  return this.setPageSize();\n                };\n              case 'pageSize':\n                return this.setPageSize.bind(this, opt.pageSize);\n              default:\n                if (key in Worker.template.prop) {\n                  // Set pre-defined properties in prop.\n                  return function set_prop() {\n                    this.prop[key] = opt[key];\n                  };\n                } else {\n                  // Set any other properties in opt.\n                  return function set_opt() {\n                    this.opt[key] = opt[key];\n                  };\n                }\n            }\n          }, this); // Set properties within the promise chain.\n\n          return this.then(function set_main() {\n            return this.thenList(fns);\n          });\n        };\n        Worker.prototype.get = function get(key, cbk) {\n          return this.then(function get_main() {\n            // Fetch the requested property, either as a predefined prop or in opt.\n            var val = key in Worker.template.prop ? this.prop[key] : this.opt[key];\n            return cbk ? cbk(val) : val;\n          });\n        };\n        Worker.prototype.setMargin = function setMargin(margin) {\n          return this.then(function setMargin_main() {\n            // Parse the margin property: [top, left, bottom, right].\n            switch ((0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.objType)(margin)) {\n              case 'number':\n                margin = [margin, margin, margin, margin];\n              case 'array':\n                if (margin.length === 2) {\n                  margin = [margin[0], margin[1], margin[0], margin[1]];\n                }\n                if (margin.length === 4) {\n                  break;\n                }\n              default:\n                return this.error('Invalid margin array.');\n            } // Set the margin property, then update pageSize.\n\n            this.opt.margin = margin;\n          }).then(this.setPageSize);\n        };\n        Worker.prototype.setPageSize = function setPageSize(pageSize) {\n          return this.then(function setPageSize_main() {\n            // Retrieve page-size based on jsPDF settings, if not explicitly provided.\n            pageSize = pageSize || jspdf__WEBPACK_IMPORTED_MODULE_8__.jsPDF.getPageSize(this.opt.jsPDF); // Add 'inner' field if not present.\n\n            if (!pageSize.hasOwnProperty('inner')) {\n              pageSize.inner = {\n                width: pageSize.width - this.opt.margin[1] - this.opt.margin[3],\n                height: pageSize.height - this.opt.margin[0] - this.opt.margin[2]\n              };\n              pageSize.inner.px = {\n                width: (0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.toPx)(pageSize.inner.width, pageSize.k),\n                height: (0, _utils_js__WEBPACK_IMPORTED_MODULE_10__.toPx)(pageSize.inner.height, pageSize.k)\n              };\n              pageSize.inner.ratio = pageSize.inner.height / pageSize.inner.width;\n            } // Attach pageSize to this.\n\n            this.prop.pageSize = pageSize;\n          });\n        };\n        Worker.prototype.setProgress = function setProgress(val, state, n, stack) {\n          // Immediately update all progress values.\n          if (val != null) this.progress.val = val;\n          if (state != null) this.progress.state = state;\n          if (n != null) this.progress.n = n;\n          if (stack != null) this.progress.stack = stack;\n          this.progress.ratio = this.progress.val / this.progress.state; // Return this for command chaining.\n\n          return this;\n        };\n        Worker.prototype.updateProgress = function updateProgress(val, state, n, stack) {\n          // Immediately update all progress values, using setProgress.\n          return this.setProgress(val ? this.progress.val + val : null, state ? state : null, n ? this.progress.n + n : null, stack ? this.progress.stack.concat(stack) : null);\n        };\n        /* ----- PROMISE MAPPING ----- */\n\n        Worker.prototype.then = function then(onFulfilled, onRejected) {\n          // Wrap `this` for encapsulation.\n          var self = this;\n          return this.thenCore(onFulfilled, onRejected, function then_main(onFulfilled, onRejected) {\n            // Update progress while queuing, calling, and resolving `then`.\n            self.updateProgress(null, null, 1, [onFulfilled]);\n            return Promise.prototype.then.call(this, function then_pre(val) {\n              self.updateProgress(null, onFulfilled);\n              return val;\n            }).then(onFulfilled, onRejected).then(function then_post(val) {\n              self.updateProgress(1);\n              return val;\n            });\n          });\n        };\n        Worker.prototype.thenCore = function thenCore(onFulfilled, onRejected, thenBase) {\n          // Handle optional thenBase parameter.\n          thenBase = thenBase || Promise.prototype.then; // Wrap `this` for encapsulation and bind it to the promise handlers.\n\n          var self = this;\n          if (onFulfilled) {\n            onFulfilled = onFulfilled.bind(self);\n          }\n          if (onRejected) {\n            onRejected = onRejected.bind(self);\n          } // Cast self into a Promise to avoid polyfills recursively defining `then`.\n\n          var isNative = Promise.toString().indexOf('[native code]') !== -1 && Promise.name === 'Promise';\n          var selfPromise = isNative ? self : Worker.convert(Object.assign({}, self), Promise.prototype); // Return the promise, after casting it into a Worker and preserving props.\n\n          var returnVal = thenBase.call(selfPromise, onFulfilled, onRejected);\n          return Worker.convert(returnVal, self.__proto__);\n        };\n        Worker.prototype.thenExternal = function thenExternal(onFulfilled, onRejected) {\n          // Call `then` and return a standard promise (exits the Worker chain).\n          return Promise.prototype.then.call(this, onFulfilled, onRejected);\n        };\n        Worker.prototype.thenList = function thenList(fns) {\n          // Queue a series of promise 'factories' into the promise chain.\n          var self = this;\n          fns.forEach(function thenList_forEach(fn) {\n            self = self.thenCore(fn);\n          });\n          return self;\n        };\n        Worker.prototype['catch'] = function (onRejected) {\n          // Bind `this` to the promise handler, call `catch`, and return a Worker.\n          if (onRejected) {\n            onRejected = onRejected.bind(this);\n          }\n          var returnVal = Promise.prototype['catch'].call(this, onRejected);\n          return Worker.convert(returnVal, this);\n        };\n        Worker.prototype.catchExternal = function catchExternal(onRejected) {\n          // Call `catch` and return a standard promise (exits the Worker chain).\n          return Promise.prototype['catch'].call(this, onRejected);\n        };\n        Worker.prototype.error = function error(msg) {\n          // Throw the error in the Promise chain.\n          return this.then(function error_main() {\n            throw new Error(msg);\n          });\n        };\n        /* ----- ALIASES ----- */\n\n        Worker.prototype.using = Worker.prototype.set;\n        Worker.prototype.saveAs = Worker.prototype.save;\n        Worker.prototype.export = Worker.prototype.output;\n        Worker.prototype.run = Worker.prototype.then;\n        /* ----- FINISHING ----- */\n        // Expose the Worker class.\n\n        /* harmony default export */\n        __webpack_exports__[\"default\"] = Worker;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/a-function.js\": (\n      /*!******************************************************!*\\\n        !*** ./node_modules/core-js/internals/a-function.js ***!\n        \\******************************************************/\n      /***/\n      function (module) {\n        module.exports = function (it) {\n          if (typeof it != 'function') {\n            throw TypeError(String(it) + ' is not a function');\n          }\n          return it;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/a-possible-prototype.js\": (\n      /*!****************************************************************!*\\\n        !*** ./node_modules/core-js/internals/a-possible-prototype.js ***!\n        \\****************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        module.exports = function (it) {\n          if (!isObject(it) && it !== null) {\n            throw TypeError(\"Can't set \" + String(it) + ' as a prototype');\n          }\n          return it;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/add-to-unscopables.js\": (\n      /*!**************************************************************!*\\\n        !*** ./node_modules/core-js/internals/add-to-unscopables.js ***!\n        \\**************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var create = __webpack_require__(/*! ../internals/object-create */\"./node_modules/core-js/internals/object-create.js\");\n        var definePropertyModule = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\");\n        var UNSCOPABLES = wellKnownSymbol('unscopables');\n        var ArrayPrototype = Array.prototype;\n\n        // Array.prototype[@@unscopables]\n        // https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\n        if (ArrayPrototype[UNSCOPABLES] == undefined) {\n          definePropertyModule.f(ArrayPrototype, UNSCOPABLES, {\n            configurable: true,\n            value: create(null)\n          });\n        }\n\n        // add a key to Array.prototype[@@unscopables]\n        module.exports = function (key) {\n          ArrayPrototype[UNSCOPABLES][key] = true;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/an-object.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/an-object.js ***!\n        \\*****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        module.exports = function (it) {\n          if (!isObject(it)) {\n            throw TypeError(String(it) + ' is not an object');\n          }\n          return it;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-for-each.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-for-each.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $forEach = __webpack_require__(/*! ../internals/array-iteration */\"./node_modules/core-js/internals/array-iteration.js\").forEach;\n        var arrayMethodIsStrict = __webpack_require__(/*! ../internals/array-method-is-strict */\"./node_modules/core-js/internals/array-method-is-strict.js\");\n        var STRICT_METHOD = arrayMethodIsStrict('forEach');\n\n        // `Array.prototype.forEach` method implementation\n        // https://tc39.es/ecma262/#sec-array.prototype.foreach\n        module.exports = !STRICT_METHOD ? function forEach(callbackfn /* , thisArg */) {\n          return $forEach(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n          // eslint-disable-next-line es/no-array-prototype-foreach -- safe\n        } : [].forEach;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-includes.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-includes.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var toLength = __webpack_require__(/*! ../internals/to-length */\"./node_modules/core-js/internals/to-length.js\");\n        var toAbsoluteIndex = __webpack_require__(/*! ../internals/to-absolute-index */\"./node_modules/core-js/internals/to-absolute-index.js\");\n\n        // `Array.prototype.{ indexOf, includes }` methods implementation\n        var createMethod = function (IS_INCLUDES) {\n          return function ($this, el, fromIndex) {\n            var O = toIndexedObject($this);\n            var length = toLength(O.length);\n            var index = toAbsoluteIndex(fromIndex, length);\n            var value;\n            // Array#includes uses SameValueZero equality algorithm\n            // eslint-disable-next-line no-self-compare -- NaN check\n            if (IS_INCLUDES && el != el) while (length > index) {\n              value = O[index++];\n              // eslint-disable-next-line no-self-compare -- NaN check\n              if (value != value) return true;\n              // Array#indexOf ignores holes, Array#includes - not\n            } else for (; length > index; index++) {\n              if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n            }\n            return !IS_INCLUDES && -1;\n          };\n        };\n        module.exports = {\n          // `Array.prototype.includes` method\n          // https://tc39.es/ecma262/#sec-array.prototype.includes\n          includes: createMethod(true),\n          // `Array.prototype.indexOf` method\n          // https://tc39.es/ecma262/#sec-array.prototype.indexof\n          indexOf: createMethod(false)\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-iteration.js\": (\n      /*!***********************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-iteration.js ***!\n        \\***********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var bind = __webpack_require__(/*! ../internals/function-bind-context */\"./node_modules/core-js/internals/function-bind-context.js\");\n        var IndexedObject = __webpack_require__(/*! ../internals/indexed-object */\"./node_modules/core-js/internals/indexed-object.js\");\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var toLength = __webpack_require__(/*! ../internals/to-length */\"./node_modules/core-js/internals/to-length.js\");\n        var arraySpeciesCreate = __webpack_require__(/*! ../internals/array-species-create */\"./node_modules/core-js/internals/array-species-create.js\");\n        var push = [].push;\n\n        // `Array.prototype.{ forEach, map, filter, some, every, find, findIndex, filterReject }` methods implementation\n        var createMethod = function (TYPE) {\n          var IS_MAP = TYPE == 1;\n          var IS_FILTER = TYPE == 2;\n          var IS_SOME = TYPE == 3;\n          var IS_EVERY = TYPE == 4;\n          var IS_FIND_INDEX = TYPE == 6;\n          var IS_FILTER_REJECT = TYPE == 7;\n          var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;\n          return function ($this, callbackfn, that, specificCreate) {\n            var O = toObject($this);\n            var self = IndexedObject(O);\n            var boundFunction = bind(callbackfn, that, 3);\n            var length = toLength(self.length);\n            var index = 0;\n            var create = specificCreate || arraySpeciesCreate;\n            var target = IS_MAP ? create($this, length) : IS_FILTER || IS_FILTER_REJECT ? create($this, 0) : undefined;\n            var value, result;\n            for (; length > index; index++) if (NO_HOLES || index in self) {\n              value = self[index];\n              result = boundFunction(value, index, O);\n              if (TYPE) {\n                if (IS_MAP) target[index] = result; // map\n                else if (result) switch (TYPE) {\n                  case 3:\n                    return true;\n                  // some\n                  case 5:\n                    return value;\n                  // find\n                  case 6:\n                    return index;\n                  // findIndex\n                  case 2:\n                    push.call(target, value);\n                  // filter\n                } else switch (TYPE) {\n                  case 4:\n                    return false;\n                  // every\n                  case 7:\n                    push.call(target, value);\n                  // filterReject\n                }\n              }\n            }\n            return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;\n          };\n        };\n        module.exports = {\n          // `Array.prototype.forEach` method\n          // https://tc39.es/ecma262/#sec-array.prototype.foreach\n          forEach: createMethod(0),\n          // `Array.prototype.map` method\n          // https://tc39.es/ecma262/#sec-array.prototype.map\n          map: createMethod(1),\n          // `Array.prototype.filter` method\n          // https://tc39.es/ecma262/#sec-array.prototype.filter\n          filter: createMethod(2),\n          // `Array.prototype.some` method\n          // https://tc39.es/ecma262/#sec-array.prototype.some\n          some: createMethod(3),\n          // `Array.prototype.every` method\n          // https://tc39.es/ecma262/#sec-array.prototype.every\n          every: createMethod(4),\n          // `Array.prototype.find` method\n          // https://tc39.es/ecma262/#sec-array.prototype.find\n          find: createMethod(5),\n          // `Array.prototype.findIndex` method\n          // https://tc39.es/ecma262/#sec-array.prototype.findIndex\n          findIndex: createMethod(6),\n          // `Array.prototype.filterReject` method\n          // https://github.com/tc39/proposal-array-filtering\n          filterReject: createMethod(7)\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-method-has-species-support.js\": (\n      /*!****************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-method-has-species-support.js ***!\n        \\****************************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var V8_VERSION = __webpack_require__(/*! ../internals/engine-v8-version */\"./node_modules/core-js/internals/engine-v8-version.js\");\n        var SPECIES = wellKnownSymbol('species');\n        module.exports = function (METHOD_NAME) {\n          // We can't use this feature detection in V8 since it causes\n          // deoptimization and serious performance degradation\n          // https://github.com/zloirock/core-js/issues/677\n          return V8_VERSION >= 51 || !fails(function () {\n            var array = [];\n            var constructor = array.constructor = {};\n            constructor[SPECIES] = function () {\n              return {\n                foo: 1\n              };\n            };\n            return array[METHOD_NAME](Boolean).foo !== 1;\n          });\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-method-is-strict.js\": (\n      /*!******************************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-method-is-strict.js ***!\n        \\******************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        module.exports = function (METHOD_NAME, argument) {\n          var method = [][METHOD_NAME];\n          return !!method && fails(function () {\n            // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n            method.call(null, argument || function () {\n              throw 1;\n            }, 1);\n          });\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-species-constructor.js\": (\n      /*!*********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-species-constructor.js ***!\n        \\*********************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var isArray = __webpack_require__(/*! ../internals/is-array */\"./node_modules/core-js/internals/is-array.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var SPECIES = wellKnownSymbol('species');\n\n        // a part of `ArraySpeciesCreate` abstract operation\n        // https://tc39.es/ecma262/#sec-arrayspeciescreate\n        module.exports = function (originalArray) {\n          var C;\n          if (isArray(originalArray)) {\n            C = originalArray.constructor;\n            // cross-realm fallback\n            if (typeof C == 'function' && (C === Array || isArray(C.prototype))) C = undefined;else if (isObject(C)) {\n              C = C[SPECIES];\n              if (C === null) C = undefined;\n            }\n          }\n          return C === undefined ? Array : C;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/array-species-create.js\": (\n      /*!****************************************************************!*\\\n        !*** ./node_modules/core-js/internals/array-species-create.js ***!\n        \\****************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var arraySpeciesConstructor = __webpack_require__(/*! ../internals/array-species-constructor */\"./node_modules/core-js/internals/array-species-constructor.js\");\n\n        // `ArraySpeciesCreate` abstract operation\n        // https://tc39.es/ecma262/#sec-arrayspeciescreate\n        module.exports = function (originalArray, length) {\n          return new (arraySpeciesConstructor(originalArray))(length === 0 ? 0 : length);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/classof-raw.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/classof-raw.js ***!\n        \\*******************************************************/\n      /***/\n      function (module) {\n        var toString = {}.toString;\n        module.exports = function (it) {\n          return toString.call(it).slice(8, -1);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/classof.js\": (\n      /*!***************************************************!*\\\n        !*** ./node_modules/core-js/internals/classof.js ***!\n        \\***************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var TO_STRING_TAG_SUPPORT = __webpack_require__(/*! ../internals/to-string-tag-support */\"./node_modules/core-js/internals/to-string-tag-support.js\");\n        var classofRaw = __webpack_require__(/*! ../internals/classof-raw */\"./node_modules/core-js/internals/classof-raw.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var TO_STRING_TAG = wellKnownSymbol('toStringTag');\n        // ES3 wrong here\n        var CORRECT_ARGUMENTS = classofRaw(function () {\n          return arguments;\n        }()) == 'Arguments';\n\n        // fallback for IE11 Script Access Denied error\n        var tryGet = function (it, key) {\n          try {\n            return it[key];\n          } catch (error) {/* empty */}\n        };\n\n        // getting tag from ES6+ `Object.prototype.toString`\n        module.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n          var O, tag, result;\n          return it === undefined ? 'Undefined' : it === null ? 'Null'\n          // @@toStringTag case\n          : typeof (tag = tryGet(O = Object(it), TO_STRING_TAG)) == 'string' ? tag\n          // builtinTag case\n          : CORRECT_ARGUMENTS ? classofRaw(O)\n          // ES3 arguments fallback\n          : (result = classofRaw(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : result;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/copy-constructor-properties.js\": (\n      /*!***********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/copy-constructor-properties.js ***!\n        \\***********************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var ownKeys = __webpack_require__(/*! ../internals/own-keys */\"./node_modules/core-js/internals/own-keys.js\");\n        var getOwnPropertyDescriptorModule = __webpack_require__(/*! ../internals/object-get-own-property-descriptor */\"./node_modules/core-js/internals/object-get-own-property-descriptor.js\");\n        var definePropertyModule = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\");\n        module.exports = function (target, source) {\n          var keys = ownKeys(source);\n          var defineProperty = definePropertyModule.f;\n          var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n          for (var i = 0; i < keys.length; i++) {\n            var key = keys[i];\n            if (!has(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n          }\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/correct-prototype-getter.js\": (\n      /*!********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/correct-prototype-getter.js ***!\n        \\********************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        module.exports = !fails(function () {\n          function F() {/* empty */}\n          F.prototype.constructor = null;\n          // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n          return Object.getPrototypeOf(new F()) !== F.prototype;\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/create-html.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/create-html.js ***!\n        \\*******************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var requireObjectCoercible = __webpack_require__(/*! ../internals/require-object-coercible */\"./node_modules/core-js/internals/require-object-coercible.js\");\n        var toString = __webpack_require__(/*! ../internals/to-string */\"./node_modules/core-js/internals/to-string.js\");\n        var quot = /\"/g;\n\n        // `CreateHTML` abstract operation\n        // https://tc39.es/ecma262/#sec-createhtml\n        module.exports = function (string, tag, attribute, value) {\n          var S = toString(requireObjectCoercible(string));\n          var p1 = '<' + tag;\n          if (attribute !== '') p1 += ' ' + attribute + '=\"' + toString(value).replace(quot, '&quot;') + '\"';\n          return p1 + '>' + S + '</' + tag + '>';\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/create-iterator-constructor.js\": (\n      /*!***********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/create-iterator-constructor.js ***!\n        \\***********************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var IteratorPrototype = __webpack_require__(/*! ../internals/iterators-core */\"./node_modules/core-js/internals/iterators-core.js\").IteratorPrototype;\n        var create = __webpack_require__(/*! ../internals/object-create */\"./node_modules/core-js/internals/object-create.js\");\n        var createPropertyDescriptor = __webpack_require__(/*! ../internals/create-property-descriptor */\"./node_modules/core-js/internals/create-property-descriptor.js\");\n        var setToStringTag = __webpack_require__(/*! ../internals/set-to-string-tag */\"./node_modules/core-js/internals/set-to-string-tag.js\");\n        var Iterators = __webpack_require__(/*! ../internals/iterators */\"./node_modules/core-js/internals/iterators.js\");\n        var returnThis = function () {\n          return this;\n        };\n        module.exports = function (IteratorConstructor, NAME, next) {\n          var TO_STRING_TAG = NAME + ' Iterator';\n          IteratorConstructor.prototype = create(IteratorPrototype, {\n            next: createPropertyDescriptor(1, next)\n          });\n          setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n          Iterators[TO_STRING_TAG] = returnThis;\n          return IteratorConstructor;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/create-non-enumerable-property.js\": (\n      /*!**************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/create-non-enumerable-property.js ***!\n        \\**************************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var definePropertyModule = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\");\n        var createPropertyDescriptor = __webpack_require__(/*! ../internals/create-property-descriptor */\"./node_modules/core-js/internals/create-property-descriptor.js\");\n        module.exports = DESCRIPTORS ? function (object, key, value) {\n          return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n        } : function (object, key, value) {\n          object[key] = value;\n          return object;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/create-property-descriptor.js\": (\n      /*!**********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/create-property-descriptor.js ***!\n        \\**********************************************************************/\n      /***/\n      function (module) {\n        module.exports = function (bitmap, value) {\n          return {\n            enumerable: !(bitmap & 1),\n            configurable: !(bitmap & 2),\n            writable: !(bitmap & 4),\n            value: value\n          };\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/create-property.js\": (\n      /*!***********************************************************!*\\\n        !*** ./node_modules/core-js/internals/create-property.js ***!\n        \\***********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var toPropertyKey = __webpack_require__(/*! ../internals/to-property-key */\"./node_modules/core-js/internals/to-property-key.js\");\n        var definePropertyModule = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\");\n        var createPropertyDescriptor = __webpack_require__(/*! ../internals/create-property-descriptor */\"./node_modules/core-js/internals/create-property-descriptor.js\");\n        module.exports = function (object, key, value) {\n          var propertyKey = toPropertyKey(key);\n          if (propertyKey in object) definePropertyModule.f(object, propertyKey, createPropertyDescriptor(0, value));else object[propertyKey] = value;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/define-iterator.js\": (\n      /*!***********************************************************!*\\\n        !*** ./node_modules/core-js/internals/define-iterator.js ***!\n        \\***********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var createIteratorConstructor = __webpack_require__(/*! ../internals/create-iterator-constructor */\"./node_modules/core-js/internals/create-iterator-constructor.js\");\n        var getPrototypeOf = __webpack_require__(/*! ../internals/object-get-prototype-of */\"./node_modules/core-js/internals/object-get-prototype-of.js\");\n        var setPrototypeOf = __webpack_require__(/*! ../internals/object-set-prototype-of */\"./node_modules/core-js/internals/object-set-prototype-of.js\");\n        var setToStringTag = __webpack_require__(/*! ../internals/set-to-string-tag */\"./node_modules/core-js/internals/set-to-string-tag.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var redefine = __webpack_require__(/*! ../internals/redefine */\"./node_modules/core-js/internals/redefine.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var IS_PURE = __webpack_require__(/*! ../internals/is-pure */\"./node_modules/core-js/internals/is-pure.js\");\n        var Iterators = __webpack_require__(/*! ../internals/iterators */\"./node_modules/core-js/internals/iterators.js\");\n        var IteratorsCore = __webpack_require__(/*! ../internals/iterators-core */\"./node_modules/core-js/internals/iterators-core.js\");\n        var IteratorPrototype = IteratorsCore.IteratorPrototype;\n        var BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\n        var ITERATOR = wellKnownSymbol('iterator');\n        var KEYS = 'keys';\n        var VALUES = 'values';\n        var ENTRIES = 'entries';\n        var returnThis = function () {\n          return this;\n        };\n        module.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n          createIteratorConstructor(IteratorConstructor, NAME, next);\n          var getIterationMethod = function (KIND) {\n            if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n            if (!BUGGY_SAFARI_ITERATORS && KIND in IterablePrototype) return IterablePrototype[KIND];\n            switch (KIND) {\n              case KEYS:\n                return function keys() {\n                  return new IteratorConstructor(this, KIND);\n                };\n              case VALUES:\n                return function values() {\n                  return new IteratorConstructor(this, KIND);\n                };\n              case ENTRIES:\n                return function entries() {\n                  return new IteratorConstructor(this, KIND);\n                };\n            }\n            return function () {\n              return new IteratorConstructor(this);\n            };\n          };\n          var TO_STRING_TAG = NAME + ' Iterator';\n          var INCORRECT_VALUES_NAME = false;\n          var IterablePrototype = Iterable.prototype;\n          var nativeIterator = IterablePrototype[ITERATOR] || IterablePrototype['@@iterator'] || DEFAULT && IterablePrototype[DEFAULT];\n          var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n          var anyNativeIterator = NAME == 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n          var CurrentIteratorPrototype, methods, KEY;\n\n          // fix native\n          if (anyNativeIterator) {\n            CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n            if (IteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n              if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n                if (setPrototypeOf) {\n                  setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n                } else if (typeof CurrentIteratorPrototype[ITERATOR] != 'function') {\n                  createNonEnumerableProperty(CurrentIteratorPrototype, ITERATOR, returnThis);\n                }\n              }\n              // Set @@toStringTag to native iterators\n              setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n              if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n            }\n          }\n\n          // fix Array.prototype.{ values, @@iterator }.name in V8 / FF\n          if (DEFAULT == VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n            INCORRECT_VALUES_NAME = true;\n            defaultIterator = function values() {\n              return nativeIterator.call(this);\n            };\n          }\n\n          // define iterator\n          if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n            createNonEnumerableProperty(IterablePrototype, ITERATOR, defaultIterator);\n          }\n          Iterators[NAME] = defaultIterator;\n\n          // export additional methods\n          if (DEFAULT) {\n            methods = {\n              values: getIterationMethod(VALUES),\n              keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n              entries: getIterationMethod(ENTRIES)\n            };\n            if (FORCED) for (KEY in methods) {\n              if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n                redefine(IterablePrototype, KEY, methods[KEY]);\n              }\n            } else $({\n              target: NAME,\n              proto: true,\n              forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME\n            }, methods);\n          }\n          return methods;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/define-well-known-symbol.js\": (\n      /*!********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/define-well-known-symbol.js ***!\n        \\********************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var path = __webpack_require__(/*! ../internals/path */\"./node_modules/core-js/internals/path.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var wrappedWellKnownSymbolModule = __webpack_require__(/*! ../internals/well-known-symbol-wrapped */\"./node_modules/core-js/internals/well-known-symbol-wrapped.js\");\n        var defineProperty = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\").f;\n        module.exports = function (NAME) {\n          var Symbol = path.Symbol || (path.Symbol = {});\n          if (!has(Symbol, NAME)) defineProperty(Symbol, NAME, {\n            value: wrappedWellKnownSymbolModule.f(NAME)\n          });\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/descriptors.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/descriptors.js ***!\n        \\*******************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n\n        // Detect IE8's incomplete defineProperty implementation\n        module.exports = !fails(function () {\n          // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n          return Object.defineProperty({}, 1, {\n            get: function () {\n              return 7;\n            }\n          })[1] != 7;\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/document-create-element.js\": (\n      /*!*******************************************************************!*\\\n        !*** ./node_modules/core-js/internals/document-create-element.js ***!\n        \\*******************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var document = global.document;\n        // typeof document.createElement is 'object' in old IE\n        var EXISTS = isObject(document) && isObject(document.createElement);\n        module.exports = function (it) {\n          return EXISTS ? document.createElement(it) : {};\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/dom-iterables.js\": (\n      /*!*********************************************************!*\\\n        !*** ./node_modules/core-js/internals/dom-iterables.js ***!\n        \\*********************************************************/\n      /***/\n      function (module) {\n        // iterable DOM collections\n        // flag - `iterable` interface - 'entries', 'keys', 'values', 'forEach' methods\n        module.exports = {\n          CSSRuleList: 0,\n          CSSStyleDeclaration: 0,\n          CSSValueList: 0,\n          ClientRectList: 0,\n          DOMRectList: 0,\n          DOMStringList: 0,\n          DOMTokenList: 1,\n          DataTransferItemList: 0,\n          FileList: 0,\n          HTMLAllCollection: 0,\n          HTMLCollection: 0,\n          HTMLFormElement: 0,\n          HTMLSelectElement: 0,\n          MediaList: 0,\n          MimeTypeArray: 0,\n          NamedNodeMap: 0,\n          NodeList: 1,\n          PaintRequestList: 0,\n          Plugin: 0,\n          PluginArray: 0,\n          SVGLengthList: 0,\n          SVGNumberList: 0,\n          SVGPathSegList: 0,\n          SVGPointList: 0,\n          SVGStringList: 0,\n          SVGTransformList: 0,\n          SourceBufferList: 0,\n          StyleSheetList: 0,\n          TextTrackCueList: 0,\n          TextTrackList: 0,\n          TouchList: 0\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/engine-user-agent.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/engine-user-agent.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var getBuiltIn = __webpack_require__(/*! ../internals/get-built-in */\"./node_modules/core-js/internals/get-built-in.js\");\n        module.exports = getBuiltIn('navigator', 'userAgent') || '';\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/engine-v8-version.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/engine-v8-version.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var userAgent = __webpack_require__(/*! ../internals/engine-user-agent */\"./node_modules/core-js/internals/engine-user-agent.js\");\n        var process = global.process;\n        var Deno = global.Deno;\n        var versions = process && process.versions || Deno && Deno.version;\n        var v8 = versions && versions.v8;\n        var match, version;\n        if (v8) {\n          match = v8.split('.');\n          version = match[0] < 4 ? 1 : match[0] + match[1];\n        } else if (userAgent) {\n          match = userAgent.match(/Edge\\/(\\d+)/);\n          if (!match || match[1] >= 74) {\n            match = userAgent.match(/Chrome\\/(\\d+)/);\n            if (match) version = match[1];\n          }\n        }\n        module.exports = version && +version;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/enum-bug-keys.js\": (\n      /*!*********************************************************!*\\\n        !*** ./node_modules/core-js/internals/enum-bug-keys.js ***!\n        \\*********************************************************/\n      /***/\n      function (module) {\n        // IE8- don't enum bug keys\n        module.exports = ['constructor', 'hasOwnProperty', 'isPrototypeOf', 'propertyIsEnumerable', 'toLocaleString', 'toString', 'valueOf'];\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/export.js\": (\n      /*!**************************************************!*\\\n        !*** ./node_modules/core-js/internals/export.js ***!\n        \\**************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var getOwnPropertyDescriptor = __webpack_require__(/*! ../internals/object-get-own-property-descriptor */\"./node_modules/core-js/internals/object-get-own-property-descriptor.js\").f;\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var redefine = __webpack_require__(/*! ../internals/redefine */\"./node_modules/core-js/internals/redefine.js\");\n        var setGlobal = __webpack_require__(/*! ../internals/set-global */\"./node_modules/core-js/internals/set-global.js\");\n        var copyConstructorProperties = __webpack_require__(/*! ../internals/copy-constructor-properties */\"./node_modules/core-js/internals/copy-constructor-properties.js\");\n        var isForced = __webpack_require__(/*! ../internals/is-forced */\"./node_modules/core-js/internals/is-forced.js\");\n\n        /*\n          options.target      - name of the target object\n          options.global      - target is the global object\n          options.stat        - export as static methods of target\n          options.proto       - export as prototype methods of target\n          options.real        - real prototype method for the `pure` version\n          options.forced      - export even if the native feature is available\n          options.bind        - bind methods to the target, required for the `pure` version\n          options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version\n          options.unsafe      - use the simple assignment of property instead of delete + defineProperty\n          options.sham        - add a flag to not completely full polyfills\n          options.enumerable  - export as enumerable property\n          options.noTargetGet - prevent calling a getter on target\n        */\n        module.exports = function (options, source) {\n          var TARGET = options.target;\n          var GLOBAL = options.global;\n          var STATIC = options.stat;\n          var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n          if (GLOBAL) {\n            target = global;\n          } else if (STATIC) {\n            target = global[TARGET] || setGlobal(TARGET, {});\n          } else {\n            target = (global[TARGET] || {}).prototype;\n          }\n          if (target) for (key in source) {\n            sourceProperty = source[key];\n            if (options.noTargetGet) {\n              descriptor = getOwnPropertyDescriptor(target, key);\n              targetProperty = descriptor && descriptor.value;\n            } else targetProperty = target[key];\n            FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n            // contained in target\n            if (!FORCED && targetProperty !== undefined) {\n              if (typeof sourceProperty === typeof targetProperty) continue;\n              copyConstructorProperties(sourceProperty, targetProperty);\n            }\n            // add a flag to not completely full polyfills\n            if (options.sham || targetProperty && targetProperty.sham) {\n              createNonEnumerableProperty(sourceProperty, 'sham', true);\n            }\n            // extend global\n            redefine(target, key, sourceProperty, options);\n          }\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/fails.js\": (\n      /*!*************************************************!*\\\n        !*** ./node_modules/core-js/internals/fails.js ***!\n        \\*************************************************/\n      /***/\n      function (module) {\n        module.exports = function (exec) {\n          try {\n            return !!exec();\n          } catch (error) {\n            return true;\n          }\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/function-bind-context.js\": (\n      /*!*****************************************************************!*\\\n        !*** ./node_modules/core-js/internals/function-bind-context.js ***!\n        \\*****************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var aFunction = __webpack_require__(/*! ../internals/a-function */\"./node_modules/core-js/internals/a-function.js\");\n\n        // optional / simple context binding\n        module.exports = function (fn, that, length) {\n          aFunction(fn);\n          if (that === undefined) return fn;\n          switch (length) {\n            case 0:\n              return function () {\n                return fn.call(that);\n              };\n            case 1:\n              return function (a) {\n                return fn.call(that, a);\n              };\n            case 2:\n              return function (a, b) {\n                return fn.call(that, a, b);\n              };\n            case 3:\n              return function (a, b, c) {\n                return fn.call(that, a, b, c);\n              };\n          }\n          return function /* ...args */\n          () {\n            return fn.apply(that, arguments);\n          };\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/get-built-in.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/internals/get-built-in.js ***!\n        \\********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var aFunction = function (variable) {\n          return typeof variable == 'function' ? variable : undefined;\n        };\n        module.exports = function (namespace, method) {\n          return arguments.length < 2 ? aFunction(global[namespace]) : global[namespace] && global[namespace][method];\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/global.js\": (\n      /*!**************************************************!*\\\n        !*** ./node_modules/core-js/internals/global.js ***!\n        \\**************************************************/\n      /***/\n      function (module) {\n        var check = function (it) {\n          return it && it.Math == Math && it;\n        };\n\n        // https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\n        module.exports =\n        // eslint-disable-next-line es/no-global-this -- safe\n        check(typeof globalThis == 'object' && globalThis) || check(typeof window == 'object' && window) ||\n        // eslint-disable-next-line no-restricted-globals -- safe\n        check(typeof self == 'object' && self) || check(typeof global == 'object' && global) ||\n        // eslint-disable-next-line no-new-func -- fallback\n        function () {\n          return this;\n        }() || Function('return this')();\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/has.js\": (\n      /*!***********************************************!*\\\n        !*** ./node_modules/core-js/internals/has.js ***!\n        \\***********************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var hasOwnProperty = {}.hasOwnProperty;\n        module.exports = Object.hasOwn || function hasOwn(it, key) {\n          return hasOwnProperty.call(toObject(it), key);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/hidden-keys.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/hidden-keys.js ***!\n        \\*******************************************************/\n      /***/\n      function (module) {\n        module.exports = {};\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/html.js\": (\n      /*!************************************************!*\\\n        !*** ./node_modules/core-js/internals/html.js ***!\n        \\************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var getBuiltIn = __webpack_require__(/*! ../internals/get-built-in */\"./node_modules/core-js/internals/get-built-in.js\");\n        module.exports = getBuiltIn('document', 'documentElement');\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/ie8-dom-define.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/ie8-dom-define.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var createElement = __webpack_require__(/*! ../internals/document-create-element */\"./node_modules/core-js/internals/document-create-element.js\");\n\n        // Thank's IE8 for his funny defineProperty\n        module.exports = !DESCRIPTORS && !fails(function () {\n          // eslint-disable-next-line es/no-object-defineproperty -- requied for testing\n          return Object.defineProperty(createElement('div'), 'a', {\n            get: function () {\n              return 7;\n            }\n          }).a != 7;\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/indexed-object.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/indexed-object.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var classof = __webpack_require__(/*! ../internals/classof-raw */\"./node_modules/core-js/internals/classof-raw.js\");\n        var split = ''.split;\n\n        // fallback for non-array-like ES3 and non-enumerable old V8 strings\n        module.exports = fails(function () {\n          // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n          // eslint-disable-next-line no-prototype-builtins -- safe\n          return !Object('z').propertyIsEnumerable(0);\n        }) ? function (it) {\n          return classof(it) == 'String' ? split.call(it, '') : Object(it);\n        } : Object;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/inherit-if-required.js\": (\n      /*!***************************************************************!*\\\n        !*** ./node_modules/core-js/internals/inherit-if-required.js ***!\n        \\***************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var setPrototypeOf = __webpack_require__(/*! ../internals/object-set-prototype-of */\"./node_modules/core-js/internals/object-set-prototype-of.js\");\n\n        // makes subclassing work correct for wrapped built-ins\n        module.exports = function ($this, dummy, Wrapper) {\n          var NewTarget, NewTargetPrototype;\n          if (\n          // it can work only with native `setPrototypeOf`\n          setPrototypeOf &&\n          // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n          typeof (NewTarget = dummy.constructor) == 'function' && NewTarget !== Wrapper && isObject(NewTargetPrototype = NewTarget.prototype) && NewTargetPrototype !== Wrapper.prototype) setPrototypeOf($this, NewTargetPrototype);\n          return $this;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/inspect-source.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/inspect-source.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var store = __webpack_require__(/*! ../internals/shared-store */\"./node_modules/core-js/internals/shared-store.js\");\n        var functionToString = Function.toString;\n\n        // this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\n        if (typeof store.inspectSource != 'function') {\n          store.inspectSource = function (it) {\n            return functionToString.call(it);\n          };\n        }\n        module.exports = store.inspectSource;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/internal-state.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/internal-state.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var NATIVE_WEAK_MAP = __webpack_require__(/*! ../internals/native-weak-map */\"./node_modules/core-js/internals/native-weak-map.js\");\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var objectHas = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var shared = __webpack_require__(/*! ../internals/shared-store */\"./node_modules/core-js/internals/shared-store.js\");\n        var sharedKey = __webpack_require__(/*! ../internals/shared-key */\"./node_modules/core-js/internals/shared-key.js\");\n        var hiddenKeys = __webpack_require__(/*! ../internals/hidden-keys */\"./node_modules/core-js/internals/hidden-keys.js\");\n        var OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\n        var WeakMap = global.WeakMap;\n        var set, get, has;\n        var enforce = function (it) {\n          return has(it) ? get(it) : set(it, {});\n        };\n        var getterFor = function (TYPE) {\n          return function (it) {\n            var state;\n            if (!isObject(it) || (state = get(it)).type !== TYPE) {\n              throw TypeError('Incompatible receiver, ' + TYPE + ' required');\n            }\n            return state;\n          };\n        };\n        if (NATIVE_WEAK_MAP || shared.state) {\n          var store = shared.state || (shared.state = new WeakMap());\n          var wmget = store.get;\n          var wmhas = store.has;\n          var wmset = store.set;\n          set = function (it, metadata) {\n            if (wmhas.call(store, it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n            metadata.facade = it;\n            wmset.call(store, it, metadata);\n            return metadata;\n          };\n          get = function (it) {\n            return wmget.call(store, it) || {};\n          };\n          has = function (it) {\n            return wmhas.call(store, it);\n          };\n        } else {\n          var STATE = sharedKey('state');\n          hiddenKeys[STATE] = true;\n          set = function (it, metadata) {\n            if (objectHas(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n            metadata.facade = it;\n            createNonEnumerableProperty(it, STATE, metadata);\n            return metadata;\n          };\n          get = function (it) {\n            return objectHas(it, STATE) ? it[STATE] : {};\n          };\n          has = function (it) {\n            return objectHas(it, STATE);\n          };\n        }\n        module.exports = {\n          set: set,\n          get: get,\n          has: has,\n          enforce: enforce,\n          getterFor: getterFor\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/is-array.js\": (\n      /*!****************************************************!*\\\n        !*** ./node_modules/core-js/internals/is-array.js ***!\n        \\****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var classof = __webpack_require__(/*! ../internals/classof-raw */\"./node_modules/core-js/internals/classof-raw.js\");\n\n        // `IsArray` abstract operation\n        // https://tc39.es/ecma262/#sec-isarray\n        // eslint-disable-next-line es/no-array-isarray -- safe\n        module.exports = Array.isArray || function isArray(arg) {\n          return classof(arg) == 'Array';\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/is-forced.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/is-forced.js ***!\n        \\*****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var replacement = /#|\\.prototype\\./;\n        var isForced = function (feature, detection) {\n          var value = data[normalize(feature)];\n          return value == POLYFILL ? true : value == NATIVE ? false : typeof detection == 'function' ? fails(detection) : !!detection;\n        };\n        var normalize = isForced.normalize = function (string) {\n          return String(string).replace(replacement, '.').toLowerCase();\n        };\n        var data = isForced.data = {};\n        var NATIVE = isForced.NATIVE = 'N';\n        var POLYFILL = isForced.POLYFILL = 'P';\n        module.exports = isForced;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/is-object.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/is-object.js ***!\n        \\*****************************************************/\n      /***/\n      function (module) {\n        module.exports = function (it) {\n          return typeof it === 'object' ? it !== null : typeof it === 'function';\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/is-pure.js\": (\n      /*!***************************************************!*\\\n        !*** ./node_modules/core-js/internals/is-pure.js ***!\n        \\***************************************************/\n      /***/\n      function (module) {\n        module.exports = false;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/is-symbol.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/is-symbol.js ***!\n        \\*****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var getBuiltIn = __webpack_require__(/*! ../internals/get-built-in */\"./node_modules/core-js/internals/get-built-in.js\");\n        var USE_SYMBOL_AS_UID = __webpack_require__(/*! ../internals/use-symbol-as-uid */\"./node_modules/core-js/internals/use-symbol-as-uid.js\");\n        module.exports = USE_SYMBOL_AS_UID ? function (it) {\n          return typeof it == 'symbol';\n        } : function (it) {\n          var $Symbol = getBuiltIn('Symbol');\n          return typeof $Symbol == 'function' && Object(it) instanceof $Symbol;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/iterators-core.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/internals/iterators-core.js ***!\n        \\**********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var getPrototypeOf = __webpack_require__(/*! ../internals/object-get-prototype-of */\"./node_modules/core-js/internals/object-get-prototype-of.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var IS_PURE = __webpack_require__(/*! ../internals/is-pure */\"./node_modules/core-js/internals/is-pure.js\");\n        var ITERATOR = wellKnownSymbol('iterator');\n        var BUGGY_SAFARI_ITERATORS = false;\n        var returnThis = function () {\n          return this;\n        };\n\n        // `%IteratorPrototype%` object\n        // https://tc39.es/ecma262/#sec-%iteratorprototype%-object\n        var IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\n        /* eslint-disable es/no-array-prototype-keys -- safe */\n        if ([].keys) {\n          arrayIterator = [].keys();\n          // Safari 8 has buggy iterators w/o `next`\n          if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;else {\n            PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n            if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n          }\n        }\n        var NEW_ITERATOR_PROTOTYPE = IteratorPrototype == undefined || fails(function () {\n          var test = {};\n          // FF44- legacy iterators case\n          return IteratorPrototype[ITERATOR].call(test) !== test;\n        });\n        if (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};\n\n        // `%IteratorPrototype%[@@iterator]()` method\n        // https://tc39.es/ecma262/#sec-%iteratorprototype%-@@iterator\n        if ((!IS_PURE || NEW_ITERATOR_PROTOTYPE) && !has(IteratorPrototype, ITERATOR)) {\n          createNonEnumerableProperty(IteratorPrototype, ITERATOR, returnThis);\n        }\n        module.exports = {\n          IteratorPrototype: IteratorPrototype,\n          BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/iterators.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/iterators.js ***!\n        \\*****************************************************/\n      /***/\n      function (module) {\n        module.exports = {};\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/native-symbol.js\": (\n      /*!*********************************************************!*\\\n        !*** ./node_modules/core-js/internals/native-symbol.js ***!\n        \\*********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        /* eslint-disable es/no-symbol -- required for testing */\n        var V8_VERSION = __webpack_require__(/*! ../internals/engine-v8-version */\"./node_modules/core-js/internals/engine-v8-version.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n\n        // eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\n        module.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n          var symbol = Symbol();\n          // Chrome 38 Symbol has incorrect toString conversion\n          // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n          return !String(symbol) || !(Object(symbol) instanceof Symbol) ||\n          // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n          !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/native-weak-map.js\": (\n      /*!***********************************************************!*\\\n        !*** ./node_modules/core-js/internals/native-weak-map.js ***!\n        \\***********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var inspectSource = __webpack_require__(/*! ../internals/inspect-source */\"./node_modules/core-js/internals/inspect-source.js\");\n        var WeakMap = global.WeakMap;\n        module.exports = typeof WeakMap === 'function' && /native code/.test(inspectSource(WeakMap));\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-assign.js\": (\n      /*!*********************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-assign.js ***!\n        \\*********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var objectKeys = __webpack_require__(/*! ../internals/object-keys */\"./node_modules/core-js/internals/object-keys.js\");\n        var getOwnPropertySymbolsModule = __webpack_require__(/*! ../internals/object-get-own-property-symbols */\"./node_modules/core-js/internals/object-get-own-property-symbols.js\");\n        var propertyIsEnumerableModule = __webpack_require__(/*! ../internals/object-property-is-enumerable */\"./node_modules/core-js/internals/object-property-is-enumerable.js\");\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var IndexedObject = __webpack_require__(/*! ../internals/indexed-object */\"./node_modules/core-js/internals/indexed-object.js\");\n\n        // eslint-disable-next-line es/no-object-assign -- safe\n        var $assign = Object.assign;\n        // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n        var defineProperty = Object.defineProperty;\n\n        // `Object.assign` method\n        // https://tc39.es/ecma262/#sec-object.assign\n        module.exports = !$assign || fails(function () {\n          // should have correct order of operations (Edge bug)\n          if (DESCRIPTORS && $assign({\n            b: 1\n          }, $assign(defineProperty({}, 'a', {\n            enumerable: true,\n            get: function () {\n              defineProperty(this, 'b', {\n                value: 3,\n                enumerable: false\n              });\n            }\n          }), {\n            b: 2\n          })).b !== 1) return true;\n          // should work with symbols and should have deterministic property order (V8 bug)\n          var A = {};\n          var B = {};\n          // eslint-disable-next-line es/no-symbol -- safe\n          var symbol = Symbol();\n          var alphabet = 'abcdefghijklmnopqrst';\n          A[symbol] = 7;\n          alphabet.split('').forEach(function (chr) {\n            B[chr] = chr;\n          });\n          return $assign({}, A)[symbol] != 7 || objectKeys($assign({}, B)).join('') != alphabet;\n        }) ? function assign(target, source) {\n          // eslint-disable-line no-unused-vars -- required for `.length`\n          var T = toObject(target);\n          var argumentsLength = arguments.length;\n          var index = 1;\n          var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n          var propertyIsEnumerable = propertyIsEnumerableModule.f;\n          while (argumentsLength > index) {\n            var S = IndexedObject(arguments[index++]);\n            var keys = getOwnPropertySymbols ? objectKeys(S).concat(getOwnPropertySymbols(S)) : objectKeys(S);\n            var length = keys.length;\n            var j = 0;\n            var key;\n            while (length > j) {\n              key = keys[j++];\n              if (!DESCRIPTORS || propertyIsEnumerable.call(S, key)) T[key] = S[key];\n            }\n          }\n          return T;\n        } : $assign;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-create.js\": (\n      /*!*********************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-create.js ***!\n        \\*********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        /* global ActiveXObject -- old IE, WSH */\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n        var defineProperties = __webpack_require__(/*! ../internals/object-define-properties */\"./node_modules/core-js/internals/object-define-properties.js\");\n        var enumBugKeys = __webpack_require__(/*! ../internals/enum-bug-keys */\"./node_modules/core-js/internals/enum-bug-keys.js\");\n        var hiddenKeys = __webpack_require__(/*! ../internals/hidden-keys */\"./node_modules/core-js/internals/hidden-keys.js\");\n        var html = __webpack_require__(/*! ../internals/html */\"./node_modules/core-js/internals/html.js\");\n        var documentCreateElement = __webpack_require__(/*! ../internals/document-create-element */\"./node_modules/core-js/internals/document-create-element.js\");\n        var sharedKey = __webpack_require__(/*! ../internals/shared-key */\"./node_modules/core-js/internals/shared-key.js\");\n        var GT = '>';\n        var LT = '<';\n        var PROTOTYPE = 'prototype';\n        var SCRIPT = 'script';\n        var IE_PROTO = sharedKey('IE_PROTO');\n        var EmptyConstructor = function () {/* empty */};\n        var scriptTag = function (content) {\n          return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n        };\n\n        // Create object with fake `null` prototype: use ActiveX Object with cleared prototype\n        var NullProtoObjectViaActiveX = function (activeXDocument) {\n          activeXDocument.write(scriptTag(''));\n          activeXDocument.close();\n          var temp = activeXDocument.parentWindow.Object;\n          activeXDocument = null; // avoid memory leak\n          return temp;\n        };\n\n        // Create object with fake `null` prototype: use iframe Object with cleared prototype\n        var NullProtoObjectViaIFrame = function () {\n          // Thrash, waste and sodomy: IE GC bug\n          var iframe = documentCreateElement('iframe');\n          var JS = 'java' + SCRIPT + ':';\n          var iframeDocument;\n          if (iframe.style) {\n            iframe.style.display = 'none';\n            html.appendChild(iframe);\n            // https://github.com/zloirock/core-js/issues/475\n            iframe.src = String(JS);\n            iframeDocument = iframe.contentWindow.document;\n            iframeDocument.open();\n            iframeDocument.write(scriptTag('document.F=Object'));\n            iframeDocument.close();\n            return iframeDocument.F;\n          }\n        };\n\n        // Check for document.domain and active x support\n        // No need to use active x approach when document.domain is not set\n        // see https://github.com/es-shims/es5-shim/issues/150\n        // variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n        // avoid IE GC bug\n        var activeXDocument;\n        var NullProtoObject = function () {\n          try {\n            activeXDocument = new ActiveXObject('htmlfile');\n          } catch (error) {/* ignore */}\n          NullProtoObject = document.domain && activeXDocument ? NullProtoObjectViaActiveX(activeXDocument) :\n          // old IE\n          NullProtoObjectViaIFrame() || NullProtoObjectViaActiveX(activeXDocument); // WSH\n          var length = enumBugKeys.length;\n          while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n          return NullProtoObject();\n        };\n        hiddenKeys[IE_PROTO] = true;\n\n        // `Object.create` method\n        // https://tc39.es/ecma262/#sec-object.create\n        module.exports = Object.create || function create(O, Properties) {\n          var result;\n          if (O !== null) {\n            EmptyConstructor[PROTOTYPE] = anObject(O);\n            result = new EmptyConstructor();\n            EmptyConstructor[PROTOTYPE] = null;\n            // add \"__proto__\" for Object.getPrototypeOf polyfill\n            result[IE_PROTO] = O;\n          } else result = NullProtoObject();\n          return Properties === undefined ? result : defineProperties(result, Properties);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-define-properties.js\": (\n      /*!********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-define-properties.js ***!\n        \\********************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var definePropertyModule = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\");\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n        var objectKeys = __webpack_require__(/*! ../internals/object-keys */\"./node_modules/core-js/internals/object-keys.js\");\n\n        // `Object.defineProperties` method\n        // https://tc39.es/ecma262/#sec-object.defineproperties\n        // eslint-disable-next-line es/no-object-defineproperties -- safe\n        module.exports = DESCRIPTORS ? Object.defineProperties : function defineProperties(O, Properties) {\n          anObject(O);\n          var keys = objectKeys(Properties);\n          var length = keys.length;\n          var index = 0;\n          var key;\n          while (length > index) definePropertyModule.f(O, key = keys[index++], Properties[key]);\n          return O;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-define-property.js\": (\n      /*!******************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-define-property.js ***!\n        \\******************************************************************/\n      /***/\n      function (__unused_webpack_module, exports, __webpack_require__) {\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var IE8_DOM_DEFINE = __webpack_require__(/*! ../internals/ie8-dom-define */\"./node_modules/core-js/internals/ie8-dom-define.js\");\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n        var toPropertyKey = __webpack_require__(/*! ../internals/to-property-key */\"./node_modules/core-js/internals/to-property-key.js\");\n\n        // eslint-disable-next-line es/no-object-defineproperty -- safe\n        var $defineProperty = Object.defineProperty;\n\n        // `Object.defineProperty` method\n        // https://tc39.es/ecma262/#sec-object.defineproperty\n        exports.f = DESCRIPTORS ? $defineProperty : function defineProperty(O, P, Attributes) {\n          anObject(O);\n          P = toPropertyKey(P);\n          anObject(Attributes);\n          if (IE8_DOM_DEFINE) try {\n            return $defineProperty(O, P, Attributes);\n          } catch (error) {/* empty */}\n          if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');\n          if ('value' in Attributes) O[P] = Attributes.value;\n          return O;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-get-own-property-descriptor.js\": (\n      /*!******************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-get-own-property-descriptor.js ***!\n        \\******************************************************************************/\n      /***/\n      function (__unused_webpack_module, exports, __webpack_require__) {\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var propertyIsEnumerableModule = __webpack_require__(/*! ../internals/object-property-is-enumerable */\"./node_modules/core-js/internals/object-property-is-enumerable.js\");\n        var createPropertyDescriptor = __webpack_require__(/*! ../internals/create-property-descriptor */\"./node_modules/core-js/internals/create-property-descriptor.js\");\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var toPropertyKey = __webpack_require__(/*! ../internals/to-property-key */\"./node_modules/core-js/internals/to-property-key.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var IE8_DOM_DEFINE = __webpack_require__(/*! ../internals/ie8-dom-define */\"./node_modules/core-js/internals/ie8-dom-define.js\");\n\n        // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n        var $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n        // `Object.getOwnPropertyDescriptor` method\n        // https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\n        exports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n          O = toIndexedObject(O);\n          P = toPropertyKey(P);\n          if (IE8_DOM_DEFINE) try {\n            return $getOwnPropertyDescriptor(O, P);\n          } catch (error) {/* empty */}\n          if (has(O, P)) return createPropertyDescriptor(!propertyIsEnumerableModule.f.call(O, P), O[P]);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-get-own-property-names-external.js\": (\n      /*!**********************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-get-own-property-names-external.js ***!\n        \\**********************************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        /* eslint-disable es/no-object-getownpropertynames -- safe */\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var $getOwnPropertyNames = __webpack_require__(/*! ../internals/object-get-own-property-names */\"./node_modules/core-js/internals/object-get-own-property-names.js\").f;\n        var toString = {}.toString;\n        var windowNames = typeof window == 'object' && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [];\n        var getWindowNames = function (it) {\n          try {\n            return $getOwnPropertyNames(it);\n          } catch (error) {\n            return windowNames.slice();\n          }\n        };\n\n        // fallback for IE11 buggy Object.getOwnPropertyNames with iframe and window\n        module.exports.f = function getOwnPropertyNames(it) {\n          return windowNames && toString.call(it) == '[object Window]' ? getWindowNames(it) : $getOwnPropertyNames(toIndexedObject(it));\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-get-own-property-names.js\": (\n      /*!*************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-get-own-property-names.js ***!\n        \\*************************************************************************/\n      /***/\n      function (__unused_webpack_module, exports, __webpack_require__) {\n        var internalObjectKeys = __webpack_require__(/*! ../internals/object-keys-internal */\"./node_modules/core-js/internals/object-keys-internal.js\");\n        var enumBugKeys = __webpack_require__(/*! ../internals/enum-bug-keys */\"./node_modules/core-js/internals/enum-bug-keys.js\");\n        var hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n        // `Object.getOwnPropertyNames` method\n        // https://tc39.es/ecma262/#sec-object.getownpropertynames\n        // eslint-disable-next-line es/no-object-getownpropertynames -- safe\n        exports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n          return internalObjectKeys(O, hiddenKeys);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-get-own-property-symbols.js\": (\n      /*!***************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-get-own-property-symbols.js ***!\n        \\***************************************************************************/\n      /***/\n      function (__unused_webpack_module, exports) {\n        // eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\n        exports.f = Object.getOwnPropertySymbols;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-get-prototype-of.js\": (\n      /*!*******************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-get-prototype-of.js ***!\n        \\*******************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var sharedKey = __webpack_require__(/*! ../internals/shared-key */\"./node_modules/core-js/internals/shared-key.js\");\n        var CORRECT_PROTOTYPE_GETTER = __webpack_require__(/*! ../internals/correct-prototype-getter */\"./node_modules/core-js/internals/correct-prototype-getter.js\");\n        var IE_PROTO = sharedKey('IE_PROTO');\n        var ObjectPrototype = Object.prototype;\n\n        // `Object.getPrototypeOf` method\n        // https://tc39.es/ecma262/#sec-object.getprototypeof\n        // eslint-disable-next-line es/no-object-getprototypeof -- safe\n        module.exports = CORRECT_PROTOTYPE_GETTER ? Object.getPrototypeOf : function (O) {\n          O = toObject(O);\n          if (has(O, IE_PROTO)) return O[IE_PROTO];\n          if (typeof O.constructor == 'function' && O instanceof O.constructor) {\n            return O.constructor.prototype;\n          }\n          return O instanceof Object ? ObjectPrototype : null;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-keys-internal.js\": (\n      /*!****************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-keys-internal.js ***!\n        \\****************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var indexOf = __webpack_require__(/*! ../internals/array-includes */\"./node_modules/core-js/internals/array-includes.js\").indexOf;\n        var hiddenKeys = __webpack_require__(/*! ../internals/hidden-keys */\"./node_modules/core-js/internals/hidden-keys.js\");\n        module.exports = function (object, names) {\n          var O = toIndexedObject(object);\n          var i = 0;\n          var result = [];\n          var key;\n          for (key in O) !has(hiddenKeys, key) && has(O, key) && result.push(key);\n          // Don't enum bug & hidden keys\n          while (names.length > i) if (has(O, key = names[i++])) {\n            ~indexOf(result, key) || result.push(key);\n          }\n          return result;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-keys.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-keys.js ***!\n        \\*******************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var internalObjectKeys = __webpack_require__(/*! ../internals/object-keys-internal */\"./node_modules/core-js/internals/object-keys-internal.js\");\n        var enumBugKeys = __webpack_require__(/*! ../internals/enum-bug-keys */\"./node_modules/core-js/internals/enum-bug-keys.js\");\n\n        // `Object.keys` method\n        // https://tc39.es/ecma262/#sec-object.keys\n        // eslint-disable-next-line es/no-object-keys -- safe\n        module.exports = Object.keys || function keys(O) {\n          return internalObjectKeys(O, enumBugKeys);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-property-is-enumerable.js\": (\n      /*!*************************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-property-is-enumerable.js ***!\n        \\*************************************************************************/\n      /***/\n      function (__unused_webpack_module, exports) {\n        \"use strict\";\n\n        var $propertyIsEnumerable = {}.propertyIsEnumerable;\n        // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n        var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n        // Nashorn ~ JDK8 bug\n        var NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({\n          1: 2\n        }, 1);\n\n        // `Object.prototype.propertyIsEnumerable` method implementation\n        // https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\n        exports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n          var descriptor = getOwnPropertyDescriptor(this, V);\n          return !!descriptor && descriptor.enumerable;\n        } : $propertyIsEnumerable;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-set-prototype-of.js\": (\n      /*!*******************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-set-prototype-of.js ***!\n        \\*******************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        /* eslint-disable no-proto -- safe */\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n        var aPossiblePrototype = __webpack_require__(/*! ../internals/a-possible-prototype */\"./node_modules/core-js/internals/a-possible-prototype.js\");\n\n        // `Object.setPrototypeOf` method\n        // https://tc39.es/ecma262/#sec-object.setprototypeof\n        // Works with __proto__ only. Old v8 can't work with null proto objects.\n        // eslint-disable-next-line es/no-object-setprototypeof -- safe\n        module.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n          var CORRECT_SETTER = false;\n          var test = {};\n          var setter;\n          try {\n            // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n            setter = Object.getOwnPropertyDescriptor(Object.prototype, '__proto__').set;\n            setter.call(test, []);\n            CORRECT_SETTER = test instanceof Array;\n          } catch (error) {/* empty */}\n          return function setPrototypeOf(O, proto) {\n            anObject(O);\n            aPossiblePrototype(proto);\n            if (CORRECT_SETTER) setter.call(O, proto);else O.__proto__ = proto;\n            return O;\n          };\n        }() : undefined);\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/object-to-string.js\": (\n      /*!************************************************************!*\\\n        !*** ./node_modules/core-js/internals/object-to-string.js ***!\n        \\************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var TO_STRING_TAG_SUPPORT = __webpack_require__(/*! ../internals/to-string-tag-support */\"./node_modules/core-js/internals/to-string-tag-support.js\");\n        var classof = __webpack_require__(/*! ../internals/classof */\"./node_modules/core-js/internals/classof.js\");\n\n        // `Object.prototype.toString` method implementation\n        // https://tc39.es/ecma262/#sec-object.prototype.tostring\n        module.exports = TO_STRING_TAG_SUPPORT ? {}.toString : function toString() {\n          return '[object ' + classof(this) + ']';\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/ordinary-to-primitive.js\": (\n      /*!*****************************************************************!*\\\n        !*** ./node_modules/core-js/internals/ordinary-to-primitive.js ***!\n        \\*****************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n\n        // `OrdinaryToPrimitive` abstract operation\n        // https://tc39.es/ecma262/#sec-ordinarytoprimitive\n        module.exports = function (input, pref) {\n          var fn, val;\n          if (pref === 'string' && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;\n          if (typeof (fn = input.valueOf) == 'function' && !isObject(val = fn.call(input))) return val;\n          if (pref !== 'string' && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;\n          throw TypeError(\"Can't convert object to primitive value\");\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/own-keys.js\": (\n      /*!****************************************************!*\\\n        !*** ./node_modules/core-js/internals/own-keys.js ***!\n        \\****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var getBuiltIn = __webpack_require__(/*! ../internals/get-built-in */\"./node_modules/core-js/internals/get-built-in.js\");\n        var getOwnPropertyNamesModule = __webpack_require__(/*! ../internals/object-get-own-property-names */\"./node_modules/core-js/internals/object-get-own-property-names.js\");\n        var getOwnPropertySymbolsModule = __webpack_require__(/*! ../internals/object-get-own-property-symbols */\"./node_modules/core-js/internals/object-get-own-property-symbols.js\");\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n\n        // all object keys, includes non-enumerable and symbols\n        module.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n          var keys = getOwnPropertyNamesModule.f(anObject(it));\n          var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n          return getOwnPropertySymbols ? keys.concat(getOwnPropertySymbols(it)) : keys;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/path.js\": (\n      /*!************************************************!*\\\n        !*** ./node_modules/core-js/internals/path.js ***!\n        \\************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        module.exports = global;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/redefine.js\": (\n      /*!****************************************************!*\\\n        !*** ./node_modules/core-js/internals/redefine.js ***!\n        \\****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var setGlobal = __webpack_require__(/*! ../internals/set-global */\"./node_modules/core-js/internals/set-global.js\");\n        var inspectSource = __webpack_require__(/*! ../internals/inspect-source */\"./node_modules/core-js/internals/inspect-source.js\");\n        var InternalStateModule = __webpack_require__(/*! ../internals/internal-state */\"./node_modules/core-js/internals/internal-state.js\");\n        var getInternalState = InternalStateModule.get;\n        var enforceInternalState = InternalStateModule.enforce;\n        var TEMPLATE = String(String).split('String');\n        (module.exports = function (O, key, value, options) {\n          var unsafe = options ? !!options.unsafe : false;\n          var simple = options ? !!options.enumerable : false;\n          var noTargetGet = options ? !!options.noTargetGet : false;\n          var state;\n          if (typeof value == 'function') {\n            if (typeof key == 'string' && !has(value, 'name')) {\n              createNonEnumerableProperty(value, 'name', key);\n            }\n            state = enforceInternalState(value);\n            if (!state.source) {\n              state.source = TEMPLATE.join(typeof key == 'string' ? key : '');\n            }\n          }\n          if (O === global) {\n            if (simple) O[key] = value;else setGlobal(key, value);\n            return;\n          } else if (!unsafe) {\n            delete O[key];\n          } else if (!noTargetGet && O[key]) {\n            simple = true;\n          }\n          if (simple) O[key] = value;else createNonEnumerableProperty(O, key, value);\n          // add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n        })(Function.prototype, 'toString', function toString() {\n          return typeof this == 'function' && getInternalState(this).source || inspectSource(this);\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/regexp-flags.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/internals/regexp-flags.js ***!\n        \\********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n\n        // `RegExp.prototype.flags` getter implementation\n        // https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\n        module.exports = function () {\n          var that = anObject(this);\n          var result = '';\n          if (that.global) result += 'g';\n          if (that.ignoreCase) result += 'i';\n          if (that.multiline) result += 'm';\n          if (that.dotAll) result += 's';\n          if (that.unicode) result += 'u';\n          if (that.sticky) result += 'y';\n          return result;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/require-object-coercible.js\": (\n      /*!********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/require-object-coercible.js ***!\n        \\********************************************************************/\n      /***/\n      function (module) {\n        // `RequireObjectCoercible` abstract operation\n        // https://tc39.es/ecma262/#sec-requireobjectcoercible\n        module.exports = function (it) {\n          if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n          return it;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/set-global.js\": (\n      /*!******************************************************!*\\\n        !*** ./node_modules/core-js/internals/set-global.js ***!\n        \\******************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        module.exports = function (key, value) {\n          try {\n            // eslint-disable-next-line es/no-object-defineproperty -- safe\n            Object.defineProperty(global, key, {\n              value: value,\n              configurable: true,\n              writable: true\n            });\n          } catch (error) {\n            global[key] = value;\n          }\n          return value;\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/set-to-string-tag.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/set-to-string-tag.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var defineProperty = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\").f;\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var TO_STRING_TAG = wellKnownSymbol('toStringTag');\n        module.exports = function (it, TAG, STATIC) {\n          if (it && !has(it = STATIC ? it : it.prototype, TO_STRING_TAG)) {\n            defineProperty(it, TO_STRING_TAG, {\n              configurable: true,\n              value: TAG\n            });\n          }\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/shared-key.js\": (\n      /*!******************************************************!*\\\n        !*** ./node_modules/core-js/internals/shared-key.js ***!\n        \\******************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var shared = __webpack_require__(/*! ../internals/shared */\"./node_modules/core-js/internals/shared.js\");\n        var uid = __webpack_require__(/*! ../internals/uid */\"./node_modules/core-js/internals/uid.js\");\n        var keys = shared('keys');\n        module.exports = function (key) {\n          return keys[key] || (keys[key] = uid(key));\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/shared-store.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/internals/shared-store.js ***!\n        \\********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var setGlobal = __webpack_require__(/*! ../internals/set-global */\"./node_modules/core-js/internals/set-global.js\");\n        var SHARED = '__core-js_shared__';\n        var store = global[SHARED] || setGlobal(SHARED, {});\n        module.exports = store;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/shared.js\": (\n      /*!**************************************************!*\\\n        !*** ./node_modules/core-js/internals/shared.js ***!\n        \\**************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var IS_PURE = __webpack_require__(/*! ../internals/is-pure */\"./node_modules/core-js/internals/is-pure.js\");\n        var store = __webpack_require__(/*! ../internals/shared-store */\"./node_modules/core-js/internals/shared-store.js\");\n        (module.exports = function (key, value) {\n          return store[key] || (store[key] = value !== undefined ? value : {});\n        })('versions', []).push({\n          version: '3.16.0',\n          mode: IS_PURE ? 'pure' : 'global',\n          copyright: '© 2021 Denis Pushkarev (zloirock.ru)'\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/string-html-forced.js\": (\n      /*!**************************************************************!*\\\n        !*** ./node_modules/core-js/internals/string-html-forced.js ***!\n        \\**************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n\n        // check the existence of a method, lowercase\n        // of a tag and escaping quotes in arguments\n        module.exports = function (METHOD_NAME) {\n          return fails(function () {\n            var test = ''[METHOD_NAME]('\"');\n            return test !== test.toLowerCase() || test.split('\"').length > 3;\n          });\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/string-multibyte.js\": (\n      /*!************************************************************!*\\\n        !*** ./node_modules/core-js/internals/string-multibyte.js ***!\n        \\************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var toInteger = __webpack_require__(/*! ../internals/to-integer */\"./node_modules/core-js/internals/to-integer.js\");\n        var toString = __webpack_require__(/*! ../internals/to-string */\"./node_modules/core-js/internals/to-string.js\");\n        var requireObjectCoercible = __webpack_require__(/*! ../internals/require-object-coercible */\"./node_modules/core-js/internals/require-object-coercible.js\");\n\n        // `String.prototype.codePointAt` methods implementation\n        var createMethod = function (CONVERT_TO_STRING) {\n          return function ($this, pos) {\n            var S = toString(requireObjectCoercible($this));\n            var position = toInteger(pos);\n            var size = S.length;\n            var first, second;\n            if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;\n            first = S.charCodeAt(position);\n            return first < 0xD800 || first > 0xDBFF || position + 1 === size || (second = S.charCodeAt(position + 1)) < 0xDC00 || second > 0xDFFF ? CONVERT_TO_STRING ? S.charAt(position) : first : CONVERT_TO_STRING ? S.slice(position, position + 2) : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;\n          };\n        };\n        module.exports = {\n          // `String.prototype.codePointAt` method\n          // https://tc39.es/ecma262/#sec-string.prototype.codepointat\n          codeAt: createMethod(false),\n          // `String.prototype.at` method\n          // https://github.com/mathiasbynens/String.prototype.at\n          charAt: createMethod(true)\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/string-trim.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/string-trim.js ***!\n        \\*******************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var requireObjectCoercible = __webpack_require__(/*! ../internals/require-object-coercible */\"./node_modules/core-js/internals/require-object-coercible.js\");\n        var toString = __webpack_require__(/*! ../internals/to-string */\"./node_modules/core-js/internals/to-string.js\");\n        var whitespaces = __webpack_require__(/*! ../internals/whitespaces */\"./node_modules/core-js/internals/whitespaces.js\");\n        var whitespace = '[' + whitespaces + ']';\n        var ltrim = RegExp('^' + whitespace + whitespace + '*');\n        var rtrim = RegExp(whitespace + whitespace + '*$');\n\n        // `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\n        var createMethod = function (TYPE) {\n          return function ($this) {\n            var string = toString(requireObjectCoercible($this));\n            if (TYPE & 1) string = string.replace(ltrim, '');\n            if (TYPE & 2) string = string.replace(rtrim, '');\n            return string;\n          };\n        };\n        module.exports = {\n          // `String.prototype.{ trimLeft, trimStart }` methods\n          // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n          start: createMethod(1),\n          // `String.prototype.{ trimRight, trimEnd }` methods\n          // https://tc39.es/ecma262/#sec-string.prototype.trimend\n          end: createMethod(2),\n          // `String.prototype.trim` method\n          // https://tc39.es/ecma262/#sec-string.prototype.trim\n          trim: createMethod(3)\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-absolute-index.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-absolute-index.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var toInteger = __webpack_require__(/*! ../internals/to-integer */\"./node_modules/core-js/internals/to-integer.js\");\n        var max = Math.max;\n        var min = Math.min;\n\n        // Helper for a popular repeating case of the spec:\n        // Let integer be ? ToInteger(index).\n        // If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\n        module.exports = function (index, length) {\n          var integer = toInteger(index);\n          return integer < 0 ? max(integer + length, 0) : min(integer, length);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-indexed-object.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-indexed-object.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        // toObject with fallback for non-array-like ES3 strings\n        var IndexedObject = __webpack_require__(/*! ../internals/indexed-object */\"./node_modules/core-js/internals/indexed-object.js\");\n        var requireObjectCoercible = __webpack_require__(/*! ../internals/require-object-coercible */\"./node_modules/core-js/internals/require-object-coercible.js\");\n        module.exports = function (it) {\n          return IndexedObject(requireObjectCoercible(it));\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-integer.js\": (\n      /*!******************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-integer.js ***!\n        \\******************************************************/\n      /***/\n      function (module) {\n        var ceil = Math.ceil;\n        var floor = Math.floor;\n\n        // `ToInteger` abstract operation\n        // https://tc39.es/ecma262/#sec-tointeger\n        module.exports = function (argument) {\n          return isNaN(argument = +argument) ? 0 : (argument > 0 ? floor : ceil)(argument);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-length.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-length.js ***!\n        \\*****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var toInteger = __webpack_require__(/*! ../internals/to-integer */\"./node_modules/core-js/internals/to-integer.js\");\n        var min = Math.min;\n\n        // `ToLength` abstract operation\n        // https://tc39.es/ecma262/#sec-tolength\n        module.exports = function (argument) {\n          return argument > 0 ? min(toInteger(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-object.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-object.js ***!\n        \\*****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var requireObjectCoercible = __webpack_require__(/*! ../internals/require-object-coercible */\"./node_modules/core-js/internals/require-object-coercible.js\");\n\n        // `ToObject` abstract operation\n        // https://tc39.es/ecma262/#sec-toobject\n        module.exports = function (argument) {\n          return Object(requireObjectCoercible(argument));\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-primitive.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-primitive.js ***!\n        \\********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var isSymbol = __webpack_require__(/*! ../internals/is-symbol */\"./node_modules/core-js/internals/is-symbol.js\");\n        var ordinaryToPrimitive = __webpack_require__(/*! ../internals/ordinary-to-primitive */\"./node_modules/core-js/internals/ordinary-to-primitive.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n        // `ToPrimitive` abstract operation\n        // https://tc39.es/ecma262/#sec-toprimitive\n        module.exports = function (input, pref) {\n          if (!isObject(input) || isSymbol(input)) return input;\n          var exoticToPrim = input[TO_PRIMITIVE];\n          var result;\n          if (exoticToPrim !== undefined) {\n            if (pref === undefined) pref = 'default';\n            result = exoticToPrim.call(input, pref);\n            if (!isObject(result) || isSymbol(result)) return result;\n            throw TypeError(\"Can't convert object to primitive value\");\n          }\n          if (pref === undefined) pref = 'number';\n          return ordinaryToPrimitive(input, pref);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-property-key.js\": (\n      /*!***********************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-property-key.js ***!\n        \\***********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var toPrimitive = __webpack_require__(/*! ../internals/to-primitive */\"./node_modules/core-js/internals/to-primitive.js\");\n        var isSymbol = __webpack_require__(/*! ../internals/is-symbol */\"./node_modules/core-js/internals/is-symbol.js\");\n\n        // `ToPropertyKey` abstract operation\n        // https://tc39.es/ecma262/#sec-topropertykey\n        module.exports = function (argument) {\n          var key = toPrimitive(argument, 'string');\n          return isSymbol(key) ? key : String(key);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-string-tag-support.js\": (\n      /*!*****************************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-string-tag-support.js ***!\n        \\*****************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var TO_STRING_TAG = wellKnownSymbol('toStringTag');\n        var test = {};\n        test[TO_STRING_TAG] = 'z';\n        module.exports = String(test) === '[object z]';\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/to-string.js\": (\n      /*!*****************************************************!*\\\n        !*** ./node_modules/core-js/internals/to-string.js ***!\n        \\*****************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var isSymbol = __webpack_require__(/*! ../internals/is-symbol */\"./node_modules/core-js/internals/is-symbol.js\");\n        module.exports = function (argument) {\n          if (isSymbol(argument)) throw TypeError('Cannot convert a Symbol value to a string');\n          return String(argument);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/uid.js\": (\n      /*!***********************************************!*\\\n        !*** ./node_modules/core-js/internals/uid.js ***!\n        \\***********************************************/\n      /***/\n      function (module) {\n        var id = 0;\n        var postfix = Math.random();\n        module.exports = function (key) {\n          return 'Symbol(' + String(key === undefined ? '' : key) + ')_' + (++id + postfix).toString(36);\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/use-symbol-as-uid.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/use-symbol-as-uid.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        /* eslint-disable es/no-symbol -- required for testing */\n        var NATIVE_SYMBOL = __webpack_require__(/*! ../internals/native-symbol */\"./node_modules/core-js/internals/native-symbol.js\");\n        module.exports = NATIVE_SYMBOL && !Symbol.sham && typeof Symbol.iterator == 'symbol';\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/well-known-symbol-wrapped.js\": (\n      /*!*********************************************************************!*\\\n        !*** ./node_modules/core-js/internals/well-known-symbol-wrapped.js ***!\n        \\*********************************************************************/\n      /***/\n      function (__unused_webpack_module, exports, __webpack_require__) {\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        exports.f = wellKnownSymbol;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/well-known-symbol.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/internals/well-known-symbol.js ***!\n        \\*************************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var shared = __webpack_require__(/*! ../internals/shared */\"./node_modules/core-js/internals/shared.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var uid = __webpack_require__(/*! ../internals/uid */\"./node_modules/core-js/internals/uid.js\");\n        var NATIVE_SYMBOL = __webpack_require__(/*! ../internals/native-symbol */\"./node_modules/core-js/internals/native-symbol.js\");\n        var USE_SYMBOL_AS_UID = __webpack_require__(/*! ../internals/use-symbol-as-uid */\"./node_modules/core-js/internals/use-symbol-as-uid.js\");\n        var WellKnownSymbolsStore = shared('wks');\n        var Symbol = global.Symbol;\n        var createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n        module.exports = function (name) {\n          if (!has(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n            if (NATIVE_SYMBOL && has(Symbol, name)) {\n              WellKnownSymbolsStore[name] = Symbol[name];\n            } else {\n              WellKnownSymbolsStore[name] = createWellKnownSymbol('Symbol.' + name);\n            }\n          }\n          return WellKnownSymbolsStore[name];\n        };\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/internals/whitespaces.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/internals/whitespaces.js ***!\n        \\*******************************************************/\n      /***/\n      function (module) {\n        // a string of all valid unicode whitespaces\n        module.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' + '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.array.concat.js\": (\n      /*!*********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.array.concat.js ***!\n        \\*********************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var isArray = __webpack_require__(/*! ../internals/is-array */\"./node_modules/core-js/internals/is-array.js\");\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var toLength = __webpack_require__(/*! ../internals/to-length */\"./node_modules/core-js/internals/to-length.js\");\n        var createProperty = __webpack_require__(/*! ../internals/create-property */\"./node_modules/core-js/internals/create-property.js\");\n        var arraySpeciesCreate = __webpack_require__(/*! ../internals/array-species-create */\"./node_modules/core-js/internals/array-species-create.js\");\n        var arrayMethodHasSpeciesSupport = __webpack_require__(/*! ../internals/array-method-has-species-support */\"./node_modules/core-js/internals/array-method-has-species-support.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var V8_VERSION = __webpack_require__(/*! ../internals/engine-v8-version */\"./node_modules/core-js/internals/engine-v8-version.js\");\n        var IS_CONCAT_SPREADABLE = wellKnownSymbol('isConcatSpreadable');\n        var MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFF;\n        var MAXIMUM_ALLOWED_INDEX_EXCEEDED = 'Maximum allowed index exceeded';\n\n        // We can't use this feature detection in V8 since it causes\n        // deoptimization and serious performance degradation\n        // https://github.com/zloirock/core-js/issues/679\n        var IS_CONCAT_SPREADABLE_SUPPORT = V8_VERSION >= 51 || !fails(function () {\n          var array = [];\n          array[IS_CONCAT_SPREADABLE] = false;\n          return array.concat()[0] !== array;\n        });\n        var SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('concat');\n        var isConcatSpreadable = function (O) {\n          if (!isObject(O)) return false;\n          var spreadable = O[IS_CONCAT_SPREADABLE];\n          return spreadable !== undefined ? !!spreadable : isArray(O);\n        };\n        var FORCED = !IS_CONCAT_SPREADABLE_SUPPORT || !SPECIES_SUPPORT;\n\n        // `Array.prototype.concat` method\n        // https://tc39.es/ecma262/#sec-array.prototype.concat\n        // with adding support of @@isConcatSpreadable and @@species\n        $({\n          target: 'Array',\n          proto: true,\n          forced: FORCED\n        }, {\n          // eslint-disable-next-line no-unused-vars -- required for `.length`\n          concat: function concat(arg) {\n            var O = toObject(this);\n            var A = arraySpeciesCreate(O, 0);\n            var n = 0;\n            var i, k, length, len, E;\n            for (i = -1, length = arguments.length; i < length; i++) {\n              E = i === -1 ? O : arguments[i];\n              if (isConcatSpreadable(E)) {\n                len = toLength(E.length);\n                if (n + len > MAX_SAFE_INTEGER) throw TypeError(MAXIMUM_ALLOWED_INDEX_EXCEEDED);\n                for (k = 0; k < len; k++, n++) if (k in E) createProperty(A, n, E[k]);\n              } else {\n                if (n >= MAX_SAFE_INTEGER) throw TypeError(MAXIMUM_ALLOWED_INDEX_EXCEEDED);\n                createProperty(A, n++, E);\n              }\n            }\n            A.length = n;\n            return A;\n          }\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.array.iterator.js\": (\n      /*!***********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.array.iterator.js ***!\n        \\***********************************************************/\n      /***/\n      function (module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var addToUnscopables = __webpack_require__(/*! ../internals/add-to-unscopables */\"./node_modules/core-js/internals/add-to-unscopables.js\");\n        var Iterators = __webpack_require__(/*! ../internals/iterators */\"./node_modules/core-js/internals/iterators.js\");\n        var InternalStateModule = __webpack_require__(/*! ../internals/internal-state */\"./node_modules/core-js/internals/internal-state.js\");\n        var defineIterator = __webpack_require__(/*! ../internals/define-iterator */\"./node_modules/core-js/internals/define-iterator.js\");\n        var ARRAY_ITERATOR = 'Array Iterator';\n        var setInternalState = InternalStateModule.set;\n        var getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);\n\n        // `Array.prototype.entries` method\n        // https://tc39.es/ecma262/#sec-array.prototype.entries\n        // `Array.prototype.keys` method\n        // https://tc39.es/ecma262/#sec-array.prototype.keys\n        // `Array.prototype.values` method\n        // https://tc39.es/ecma262/#sec-array.prototype.values\n        // `Array.prototype[@@iterator]` method\n        // https://tc39.es/ecma262/#sec-array.prototype-@@iterator\n        // `CreateArrayIterator` internal method\n        // https://tc39.es/ecma262/#sec-createarrayiterator\n        module.exports = defineIterator(Array, 'Array', function (iterated, kind) {\n          setInternalState(this, {\n            type: ARRAY_ITERATOR,\n            target: toIndexedObject(iterated),\n            // target\n            index: 0,\n            // next index\n            kind: kind // kind\n          });\n          // `%ArrayIteratorPrototype%.next` method\n          // https://tc39.es/ecma262/#sec-%arrayiteratorprototype%.next\n        }, function () {\n          var state = getInternalState(this);\n          var target = state.target;\n          var kind = state.kind;\n          var index = state.index++;\n          if (!target || index >= target.length) {\n            state.target = undefined;\n            return {\n              value: undefined,\n              done: true\n            };\n          }\n          if (kind == 'keys') return {\n            value: index,\n            done: false\n          };\n          if (kind == 'values') return {\n            value: target[index],\n            done: false\n          };\n          return {\n            value: [index, target[index]],\n            done: false\n          };\n        }, 'values');\n\n        // argumentsList[@@iterator] is %ArrayProto_values%\n        // https://tc39.es/ecma262/#sec-createunmappedargumentsobject\n        // https://tc39.es/ecma262/#sec-createmappedargumentsobject\n        Iterators.Arguments = Iterators.Array;\n\n        // https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\n        addToUnscopables('keys');\n        addToUnscopables('values');\n        addToUnscopables('entries');\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.array.join.js\": (\n      /*!*******************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.array.join.js ***!\n        \\*******************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var IndexedObject = __webpack_require__(/*! ../internals/indexed-object */\"./node_modules/core-js/internals/indexed-object.js\");\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var arrayMethodIsStrict = __webpack_require__(/*! ../internals/array-method-is-strict */\"./node_modules/core-js/internals/array-method-is-strict.js\");\n        var nativeJoin = [].join;\n        var ES3_STRINGS = IndexedObject != Object;\n        var STRICT_METHOD = arrayMethodIsStrict('join', ',');\n\n        // `Array.prototype.join` method\n        // https://tc39.es/ecma262/#sec-array.prototype.join\n        $({\n          target: 'Array',\n          proto: true,\n          forced: ES3_STRINGS || !STRICT_METHOD\n        }, {\n          join: function join(separator) {\n            return nativeJoin.call(toIndexedObject(this), separator === undefined ? ',' : separator);\n          }\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.array.map.js\": (\n      /*!******************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.array.map.js ***!\n        \\******************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var $map = __webpack_require__(/*! ../internals/array-iteration */\"./node_modules/core-js/internals/array-iteration.js\").map;\n        var arrayMethodHasSpeciesSupport = __webpack_require__(/*! ../internals/array-method-has-species-support */\"./node_modules/core-js/internals/array-method-has-species-support.js\");\n        var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('map');\n\n        // `Array.prototype.map` method\n        // https://tc39.es/ecma262/#sec-array.prototype.map\n        // with adding support of @@species\n        $({\n          target: 'Array',\n          proto: true,\n          forced: !HAS_SPECIES_SUPPORT\n        }, {\n          map: function map(callbackfn /* , thisArg */) {\n            return $map(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n          }\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.array.slice.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.array.slice.js ***!\n        \\********************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var isArray = __webpack_require__(/*! ../internals/is-array */\"./node_modules/core-js/internals/is-array.js\");\n        var toAbsoluteIndex = __webpack_require__(/*! ../internals/to-absolute-index */\"./node_modules/core-js/internals/to-absolute-index.js\");\n        var toLength = __webpack_require__(/*! ../internals/to-length */\"./node_modules/core-js/internals/to-length.js\");\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var createProperty = __webpack_require__(/*! ../internals/create-property */\"./node_modules/core-js/internals/create-property.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var arrayMethodHasSpeciesSupport = __webpack_require__(/*! ../internals/array-method-has-species-support */\"./node_modules/core-js/internals/array-method-has-species-support.js\");\n        var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('slice');\n        var SPECIES = wellKnownSymbol('species');\n        var nativeSlice = [].slice;\n        var max = Math.max;\n\n        // `Array.prototype.slice` method\n        // https://tc39.es/ecma262/#sec-array.prototype.slice\n        // fallback for not array-like ES3 strings and DOM objects\n        $({\n          target: 'Array',\n          proto: true,\n          forced: !HAS_SPECIES_SUPPORT\n        }, {\n          slice: function slice(start, end) {\n            var O = toIndexedObject(this);\n            var length = toLength(O.length);\n            var k = toAbsoluteIndex(start, length);\n            var fin = toAbsoluteIndex(end === undefined ? length : end, length);\n            // inline `ArraySpeciesCreate` for usage native `Array#slice` where it's possible\n            var Constructor, result, n;\n            if (isArray(O)) {\n              Constructor = O.constructor;\n              // cross-realm fallback\n              if (typeof Constructor == 'function' && (Constructor === Array || isArray(Constructor.prototype))) {\n                Constructor = undefined;\n              } else if (isObject(Constructor)) {\n                Constructor = Constructor[SPECIES];\n                if (Constructor === null) Constructor = undefined;\n              }\n              if (Constructor === Array || Constructor === undefined) {\n                return nativeSlice.call(O, k, fin);\n              }\n            }\n            result = new (Constructor === undefined ? Array : Constructor)(max(fin - k, 0));\n            for (n = 0; k < fin; k++, n++) if (k in O) createProperty(result, n, O[k]);\n            result.length = n;\n            return result;\n          }\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.function.name.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.function.name.js ***!\n        \\**********************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var defineProperty = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\").f;\n        var FunctionPrototype = Function.prototype;\n        var FunctionPrototypeToString = FunctionPrototype.toString;\n        var nameRE = /^\\s*function ([^ (]*)/;\n        var NAME = 'name';\n\n        // Function instances `.name` property\n        // https://tc39.es/ecma262/#sec-function-instances-name\n        if (DESCRIPTORS && !(NAME in FunctionPrototype)) {\n          defineProperty(FunctionPrototype, NAME, {\n            configurable: true,\n            get: function () {\n              try {\n                return FunctionPrototypeToString.call(this).match(nameRE)[1];\n              } catch (error) {\n                return '';\n              }\n            }\n          });\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.number.constructor.js\": (\n      /*!***************************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.number.constructor.js ***!\n        \\***************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var isForced = __webpack_require__(/*! ../internals/is-forced */\"./node_modules/core-js/internals/is-forced.js\");\n        var redefine = __webpack_require__(/*! ../internals/redefine */\"./node_modules/core-js/internals/redefine.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var classof = __webpack_require__(/*! ../internals/classof-raw */\"./node_modules/core-js/internals/classof-raw.js\");\n        var inheritIfRequired = __webpack_require__(/*! ../internals/inherit-if-required */\"./node_modules/core-js/internals/inherit-if-required.js\");\n        var isSymbol = __webpack_require__(/*! ../internals/is-symbol */\"./node_modules/core-js/internals/is-symbol.js\");\n        var toPrimitive = __webpack_require__(/*! ../internals/to-primitive */\"./node_modules/core-js/internals/to-primitive.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var create = __webpack_require__(/*! ../internals/object-create */\"./node_modules/core-js/internals/object-create.js\");\n        var getOwnPropertyNames = __webpack_require__(/*! ../internals/object-get-own-property-names */\"./node_modules/core-js/internals/object-get-own-property-names.js\").f;\n        var getOwnPropertyDescriptor = __webpack_require__(/*! ../internals/object-get-own-property-descriptor */\"./node_modules/core-js/internals/object-get-own-property-descriptor.js\").f;\n        var defineProperty = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\").f;\n        var trim = __webpack_require__(/*! ../internals/string-trim */\"./node_modules/core-js/internals/string-trim.js\").trim;\n        var NUMBER = 'Number';\n        var NativeNumber = global[NUMBER];\n        var NumberPrototype = NativeNumber.prototype;\n\n        // Opera ~12 has broken Object#toString\n        var BROKEN_CLASSOF = classof(create(NumberPrototype)) == NUMBER;\n\n        // `ToNumber` abstract operation\n        // https://tc39.es/ecma262/#sec-tonumber\n        var toNumber = function (argument) {\n          if (isSymbol(argument)) throw TypeError('Cannot convert a Symbol value to a number');\n          var it = toPrimitive(argument, 'number');\n          var first, third, radix, maxCode, digits, length, index, code;\n          if (typeof it == 'string' && it.length > 2) {\n            it = trim(it);\n            first = it.charCodeAt(0);\n            if (first === 43 || first === 45) {\n              third = it.charCodeAt(2);\n              if (third === 88 || third === 120) return NaN; // Number('+0x1') should be NaN, old V8 fix\n            } else if (first === 48) {\n              switch (it.charCodeAt(1)) {\n                case 66:\n                case 98:\n                  radix = 2;\n                  maxCode = 49;\n                  break;\n                // fast equal of /^0b[01]+$/i\n                case 79:\n                case 111:\n                  radix = 8;\n                  maxCode = 55;\n                  break;\n                // fast equal of /^0o[0-7]+$/i\n                default:\n                  return +it;\n              }\n              digits = it.slice(2);\n              length = digits.length;\n              for (index = 0; index < length; index++) {\n                code = digits.charCodeAt(index);\n                // parseInt parses a string to a first unavailable symbol\n                // but ToNumber should return NaN if a string contains unavailable symbols\n                if (code < 48 || code > maxCode) return NaN;\n              }\n              return parseInt(digits, radix);\n            }\n          }\n          return +it;\n        };\n\n        // `Number` constructor\n        // https://tc39.es/ecma262/#sec-number-constructor\n        if (isForced(NUMBER, !NativeNumber(' 0o1') || !NativeNumber('0b1') || NativeNumber('+0x1'))) {\n          var NumberWrapper = function Number(value) {\n            var it = arguments.length < 1 ? 0 : value;\n            var dummy = this;\n            return dummy instanceof NumberWrapper\n            // check on 1..constructor(foo) case\n            && (BROKEN_CLASSOF ? fails(function () {\n              NumberPrototype.valueOf.call(dummy);\n            }) : classof(dummy) != NUMBER) ? inheritIfRequired(new NativeNumber(toNumber(it)), dummy, NumberWrapper) : toNumber(it);\n          };\n          for (var keys = DESCRIPTORS ? getOwnPropertyNames(NativeNumber) : (\n            // ES3:\n            'MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,' +\n            // ES2015 (in case, if modules with ES2015 Number statics required before):\n            'EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,' + 'MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,' +\n            // ESNext\n            'fromString,range').split(','), j = 0, key; keys.length > j; j++) {\n            if (has(NativeNumber, key = keys[j]) && !has(NumberWrapper, key)) {\n              defineProperty(NumberWrapper, key, getOwnPropertyDescriptor(NativeNumber, key));\n            }\n          }\n          NumberWrapper.prototype = NumberPrototype;\n          NumberPrototype.constructor = NumberWrapper;\n          redefine(global, NUMBER, NumberWrapper);\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.object.assign.js\": (\n      /*!**********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.object.assign.js ***!\n        \\**********************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var assign = __webpack_require__(/*! ../internals/object-assign */\"./node_modules/core-js/internals/object-assign.js\");\n\n        // `Object.assign` method\n        // https://tc39.es/ecma262/#sec-object.assign\n        // eslint-disable-next-line es/no-object-assign -- required for testing\n        $({\n          target: 'Object',\n          stat: true,\n          forced: Object.assign !== assign\n        }, {\n          assign: assign\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.object.keys.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.object.keys.js ***!\n        \\********************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var nativeKeys = __webpack_require__(/*! ../internals/object-keys */\"./node_modules/core-js/internals/object-keys.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var FAILS_ON_PRIMITIVES = fails(function () {\n          nativeKeys(1);\n        });\n\n        // `Object.keys` method\n        // https://tc39.es/ecma262/#sec-object.keys\n        $({\n          target: 'Object',\n          stat: true,\n          forced: FAILS_ON_PRIMITIVES\n        }, {\n          keys: function keys(it) {\n            return nativeKeys(toObject(it));\n          }\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.object.to-string.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.object.to-string.js ***!\n        \\*************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var TO_STRING_TAG_SUPPORT = __webpack_require__(/*! ../internals/to-string-tag-support */\"./node_modules/core-js/internals/to-string-tag-support.js\");\n        var redefine = __webpack_require__(/*! ../internals/redefine */\"./node_modules/core-js/internals/redefine.js\");\n        var toString = __webpack_require__(/*! ../internals/object-to-string */\"./node_modules/core-js/internals/object-to-string.js\");\n\n        // `Object.prototype.toString` method\n        // https://tc39.es/ecma262/#sec-object.prototype.tostring\n        if (!TO_STRING_TAG_SUPPORT) {\n          redefine(Object.prototype, 'toString', toString, {\n            unsafe: true\n          });\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.regexp.to-string.js\": (\n      /*!*************************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.regexp.to-string.js ***!\n        \\*************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var redefine = __webpack_require__(/*! ../internals/redefine */\"./node_modules/core-js/internals/redefine.js\");\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n        var $toString = __webpack_require__(/*! ../internals/to-string */\"./node_modules/core-js/internals/to-string.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var flags = __webpack_require__(/*! ../internals/regexp-flags */\"./node_modules/core-js/internals/regexp-flags.js\");\n        var TO_STRING = 'toString';\n        var RegExpPrototype = RegExp.prototype;\n        var nativeToString = RegExpPrototype[TO_STRING];\n        var NOT_GENERIC = fails(function () {\n          return nativeToString.call({\n            source: 'a',\n            flags: 'b'\n          }) != '/a/b';\n        });\n        // FF44- RegExp#toString has a wrong name\n        var INCORRECT_NAME = nativeToString.name != TO_STRING;\n\n        // `RegExp.prototype.toString` method\n        // https://tc39.es/ecma262/#sec-regexp.prototype.tostring\n        if (NOT_GENERIC || INCORRECT_NAME) {\n          redefine(RegExp.prototype, TO_STRING, function toString() {\n            var R = anObject(this);\n            var p = $toString(R.source);\n            var rf = R.flags;\n            var f = $toString(rf === undefined && R instanceof RegExp && !('flags' in RegExpPrototype) ? flags.call(R) : rf);\n            return '/' + p + '/' + f;\n          }, {\n            unsafe: true\n          });\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.string.iterator.js\": (\n      /*!************************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.string.iterator.js ***!\n        \\************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var charAt = __webpack_require__(/*! ../internals/string-multibyte */\"./node_modules/core-js/internals/string-multibyte.js\").charAt;\n        var toString = __webpack_require__(/*! ../internals/to-string */\"./node_modules/core-js/internals/to-string.js\");\n        var InternalStateModule = __webpack_require__(/*! ../internals/internal-state */\"./node_modules/core-js/internals/internal-state.js\");\n        var defineIterator = __webpack_require__(/*! ../internals/define-iterator */\"./node_modules/core-js/internals/define-iterator.js\");\n        var STRING_ITERATOR = 'String Iterator';\n        var setInternalState = InternalStateModule.set;\n        var getInternalState = InternalStateModule.getterFor(STRING_ITERATOR);\n\n        // `String.prototype[@@iterator]` method\n        // https://tc39.es/ecma262/#sec-string.prototype-@@iterator\n        defineIterator(String, 'String', function (iterated) {\n          setInternalState(this, {\n            type: STRING_ITERATOR,\n            string: toString(iterated),\n            index: 0\n          });\n          // `%StringIteratorPrototype%.next` method\n          // https://tc39.es/ecma262/#sec-%stringiteratorprototype%.next\n        }, function next() {\n          var state = getInternalState(this);\n          var string = state.string;\n          var index = state.index;\n          var point;\n          if (index >= string.length) return {\n            value: undefined,\n            done: true\n          };\n          point = charAt(string, index);\n          state.index += point.length;\n          return {\n            value: point,\n            done: false\n          };\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.string.link.js\": (\n      /*!********************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.string.link.js ***!\n        \\********************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var createHTML = __webpack_require__(/*! ../internals/create-html */\"./node_modules/core-js/internals/create-html.js\");\n        var forcedStringHTMLMethod = __webpack_require__(/*! ../internals/string-html-forced */\"./node_modules/core-js/internals/string-html-forced.js\");\n\n        // `String.prototype.link` method\n        // https://tc39.es/ecma262/#sec-string.prototype.link\n        $({\n          target: 'String',\n          proto: true,\n          forced: forcedStringHTMLMethod('link')\n        }, {\n          link: function link(url) {\n            return createHTML(this, 'a', 'href', url);\n          }\n        });\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.symbol.description.js\": (\n      /*!***************************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.symbol.description.js ***!\n        \\***************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        // `Symbol.prototype.description` getter\n        // https://tc39.es/ecma262/#sec-symbol.prototype.description\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var defineProperty = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\").f;\n        var copyConstructorProperties = __webpack_require__(/*! ../internals/copy-constructor-properties */\"./node_modules/core-js/internals/copy-constructor-properties.js\");\n        var NativeSymbol = global.Symbol;\n        if (DESCRIPTORS && typeof NativeSymbol == 'function' && (!('description' in NativeSymbol.prototype) ||\n        // Safari 12 bug\n        NativeSymbol().description !== undefined)) {\n          var EmptyStringDescriptionStore = {};\n          // wrap Symbol constructor for correct work with undefined description\n          var SymbolWrapper = function Symbol() {\n            var description = arguments.length < 1 || arguments[0] === undefined ? undefined : String(arguments[0]);\n            var result = this instanceof SymbolWrapper ? new NativeSymbol(description)\n            // in Edge 13, String(Symbol(undefined)) === 'Symbol(undefined)'\n            : description === undefined ? NativeSymbol() : NativeSymbol(description);\n            if (description === '') EmptyStringDescriptionStore[result] = true;\n            return result;\n          };\n          copyConstructorProperties(SymbolWrapper, NativeSymbol);\n          var symbolPrototype = SymbolWrapper.prototype = NativeSymbol.prototype;\n          symbolPrototype.constructor = SymbolWrapper;\n          var symbolToString = symbolPrototype.toString;\n          var native = String(NativeSymbol('test')) == 'Symbol(test)';\n          var regexp = /^Symbol\\((.*)\\)[^)]+$/;\n          defineProperty(symbolPrototype, 'description', {\n            configurable: true,\n            get: function description() {\n              var symbol = isObject(this) ? this.valueOf() : this;\n              var string = symbolToString.call(symbol);\n              if (has(EmptyStringDescriptionStore, symbol)) return '';\n              var desc = native ? string.slice(7, -1) : string.replace(regexp, '$1');\n              return desc === '' ? undefined : desc;\n            }\n          });\n          $({\n            global: true,\n            forced: true\n          }, {\n            Symbol: SymbolWrapper\n          });\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.symbol.iterator.js\": (\n      /*!************************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.symbol.iterator.js ***!\n        \\************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var defineWellKnownSymbol = __webpack_require__(/*! ../internals/define-well-known-symbol */\"./node_modules/core-js/internals/define-well-known-symbol.js\");\n\n        // `Symbol.iterator` well-known symbol\n        // https://tc39.es/ecma262/#sec-symbol.iterator\n        defineWellKnownSymbol('iterator');\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/es.symbol.js\": (\n      /*!***************************************************!*\\\n        !*** ./node_modules/core-js/modules/es.symbol.js ***!\n        \\***************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        \"use strict\";\n\n        var $ = __webpack_require__(/*! ../internals/export */\"./node_modules/core-js/internals/export.js\");\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var getBuiltIn = __webpack_require__(/*! ../internals/get-built-in */\"./node_modules/core-js/internals/get-built-in.js\");\n        var IS_PURE = __webpack_require__(/*! ../internals/is-pure */\"./node_modules/core-js/internals/is-pure.js\");\n        var DESCRIPTORS = __webpack_require__(/*! ../internals/descriptors */\"./node_modules/core-js/internals/descriptors.js\");\n        var NATIVE_SYMBOL = __webpack_require__(/*! ../internals/native-symbol */\"./node_modules/core-js/internals/native-symbol.js\");\n        var fails = __webpack_require__(/*! ../internals/fails */\"./node_modules/core-js/internals/fails.js\");\n        var has = __webpack_require__(/*! ../internals/has */\"./node_modules/core-js/internals/has.js\");\n        var isArray = __webpack_require__(/*! ../internals/is-array */\"./node_modules/core-js/internals/is-array.js\");\n        var isObject = __webpack_require__(/*! ../internals/is-object */\"./node_modules/core-js/internals/is-object.js\");\n        var isSymbol = __webpack_require__(/*! ../internals/is-symbol */\"./node_modules/core-js/internals/is-symbol.js\");\n        var anObject = __webpack_require__(/*! ../internals/an-object */\"./node_modules/core-js/internals/an-object.js\");\n        var toObject = __webpack_require__(/*! ../internals/to-object */\"./node_modules/core-js/internals/to-object.js\");\n        var toIndexedObject = __webpack_require__(/*! ../internals/to-indexed-object */\"./node_modules/core-js/internals/to-indexed-object.js\");\n        var toPropertyKey = __webpack_require__(/*! ../internals/to-property-key */\"./node_modules/core-js/internals/to-property-key.js\");\n        var $toString = __webpack_require__(/*! ../internals/to-string */\"./node_modules/core-js/internals/to-string.js\");\n        var createPropertyDescriptor = __webpack_require__(/*! ../internals/create-property-descriptor */\"./node_modules/core-js/internals/create-property-descriptor.js\");\n        var nativeObjectCreate = __webpack_require__(/*! ../internals/object-create */\"./node_modules/core-js/internals/object-create.js\");\n        var objectKeys = __webpack_require__(/*! ../internals/object-keys */\"./node_modules/core-js/internals/object-keys.js\");\n        var getOwnPropertyNamesModule = __webpack_require__(/*! ../internals/object-get-own-property-names */\"./node_modules/core-js/internals/object-get-own-property-names.js\");\n        var getOwnPropertyNamesExternal = __webpack_require__(/*! ../internals/object-get-own-property-names-external */\"./node_modules/core-js/internals/object-get-own-property-names-external.js\");\n        var getOwnPropertySymbolsModule = __webpack_require__(/*! ../internals/object-get-own-property-symbols */\"./node_modules/core-js/internals/object-get-own-property-symbols.js\");\n        var getOwnPropertyDescriptorModule = __webpack_require__(/*! ../internals/object-get-own-property-descriptor */\"./node_modules/core-js/internals/object-get-own-property-descriptor.js\");\n        var definePropertyModule = __webpack_require__(/*! ../internals/object-define-property */\"./node_modules/core-js/internals/object-define-property.js\");\n        var propertyIsEnumerableModule = __webpack_require__(/*! ../internals/object-property-is-enumerable */\"./node_modules/core-js/internals/object-property-is-enumerable.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var redefine = __webpack_require__(/*! ../internals/redefine */\"./node_modules/core-js/internals/redefine.js\");\n        var shared = __webpack_require__(/*! ../internals/shared */\"./node_modules/core-js/internals/shared.js\");\n        var sharedKey = __webpack_require__(/*! ../internals/shared-key */\"./node_modules/core-js/internals/shared-key.js\");\n        var hiddenKeys = __webpack_require__(/*! ../internals/hidden-keys */\"./node_modules/core-js/internals/hidden-keys.js\");\n        var uid = __webpack_require__(/*! ../internals/uid */\"./node_modules/core-js/internals/uid.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var wrappedWellKnownSymbolModule = __webpack_require__(/*! ../internals/well-known-symbol-wrapped */\"./node_modules/core-js/internals/well-known-symbol-wrapped.js\");\n        var defineWellKnownSymbol = __webpack_require__(/*! ../internals/define-well-known-symbol */\"./node_modules/core-js/internals/define-well-known-symbol.js\");\n        var setToStringTag = __webpack_require__(/*! ../internals/set-to-string-tag */\"./node_modules/core-js/internals/set-to-string-tag.js\");\n        var InternalStateModule = __webpack_require__(/*! ../internals/internal-state */\"./node_modules/core-js/internals/internal-state.js\");\n        var $forEach = __webpack_require__(/*! ../internals/array-iteration */\"./node_modules/core-js/internals/array-iteration.js\").forEach;\n        var HIDDEN = sharedKey('hidden');\n        var SYMBOL = 'Symbol';\n        var PROTOTYPE = 'prototype';\n        var TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n        var setInternalState = InternalStateModule.set;\n        var getInternalState = InternalStateModule.getterFor(SYMBOL);\n        var ObjectPrototype = Object[PROTOTYPE];\n        var $Symbol = global.Symbol;\n        var $stringify = getBuiltIn('JSON', 'stringify');\n        var nativeGetOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n        var nativeDefineProperty = definePropertyModule.f;\n        var nativeGetOwnPropertyNames = getOwnPropertyNamesExternal.f;\n        var nativePropertyIsEnumerable = propertyIsEnumerableModule.f;\n        var AllSymbols = shared('symbols');\n        var ObjectPrototypeSymbols = shared('op-symbols');\n        var StringToSymbolRegistry = shared('string-to-symbol-registry');\n        var SymbolToStringRegistry = shared('symbol-to-string-registry');\n        var WellKnownSymbolsStore = shared('wks');\n        var QObject = global.QObject;\n        // Don't use setters in Qt Script, https://github.com/zloirock/core-js/issues/173\n        var USE_SETTER = !QObject || !QObject[PROTOTYPE] || !QObject[PROTOTYPE].findChild;\n\n        // fallback for old Android, https://code.google.com/p/v8/issues/detail?id=687\n        var setSymbolDescriptor = DESCRIPTORS && fails(function () {\n          return nativeObjectCreate(nativeDefineProperty({}, 'a', {\n            get: function () {\n              return nativeDefineProperty(this, 'a', {\n                value: 7\n              }).a;\n            }\n          })).a != 7;\n        }) ? function (O, P, Attributes) {\n          var ObjectPrototypeDescriptor = nativeGetOwnPropertyDescriptor(ObjectPrototype, P);\n          if (ObjectPrototypeDescriptor) delete ObjectPrototype[P];\n          nativeDefineProperty(O, P, Attributes);\n          if (ObjectPrototypeDescriptor && O !== ObjectPrototype) {\n            nativeDefineProperty(ObjectPrototype, P, ObjectPrototypeDescriptor);\n          }\n        } : nativeDefineProperty;\n        var wrap = function (tag, description) {\n          var symbol = AllSymbols[tag] = nativeObjectCreate($Symbol[PROTOTYPE]);\n          setInternalState(symbol, {\n            type: SYMBOL,\n            tag: tag,\n            description: description\n          });\n          if (!DESCRIPTORS) symbol.description = description;\n          return symbol;\n        };\n        var $defineProperty = function defineProperty(O, P, Attributes) {\n          if (O === ObjectPrototype) $defineProperty(ObjectPrototypeSymbols, P, Attributes);\n          anObject(O);\n          var key = toPropertyKey(P);\n          anObject(Attributes);\n          if (has(AllSymbols, key)) {\n            if (!Attributes.enumerable) {\n              if (!has(O, HIDDEN)) nativeDefineProperty(O, HIDDEN, createPropertyDescriptor(1, {}));\n              O[HIDDEN][key] = true;\n            } else {\n              if (has(O, HIDDEN) && O[HIDDEN][key]) O[HIDDEN][key] = false;\n              Attributes = nativeObjectCreate(Attributes, {\n                enumerable: createPropertyDescriptor(0, false)\n              });\n            }\n            return setSymbolDescriptor(O, key, Attributes);\n          }\n          return nativeDefineProperty(O, key, Attributes);\n        };\n        var $defineProperties = function defineProperties(O, Properties) {\n          anObject(O);\n          var properties = toIndexedObject(Properties);\n          var keys = objectKeys(properties).concat($getOwnPropertySymbols(properties));\n          $forEach(keys, function (key) {\n            if (!DESCRIPTORS || $propertyIsEnumerable.call(properties, key)) $defineProperty(O, key, properties[key]);\n          });\n          return O;\n        };\n        var $create = function create(O, Properties) {\n          return Properties === undefined ? nativeObjectCreate(O) : $defineProperties(nativeObjectCreate(O), Properties);\n        };\n        var $propertyIsEnumerable = function propertyIsEnumerable(V) {\n          var P = toPropertyKey(V);\n          var enumerable = nativePropertyIsEnumerable.call(this, P);\n          if (this === ObjectPrototype && has(AllSymbols, P) && !has(ObjectPrototypeSymbols, P)) return false;\n          return enumerable || !has(this, P) || !has(AllSymbols, P) || has(this, HIDDEN) && this[HIDDEN][P] ? enumerable : true;\n        };\n        var $getOwnPropertyDescriptor = function getOwnPropertyDescriptor(O, P) {\n          var it = toIndexedObject(O);\n          var key = toPropertyKey(P);\n          if (it === ObjectPrototype && has(AllSymbols, key) && !has(ObjectPrototypeSymbols, key)) return;\n          var descriptor = nativeGetOwnPropertyDescriptor(it, key);\n          if (descriptor && has(AllSymbols, key) && !(has(it, HIDDEN) && it[HIDDEN][key])) {\n            descriptor.enumerable = true;\n          }\n          return descriptor;\n        };\n        var $getOwnPropertyNames = function getOwnPropertyNames(O) {\n          var names = nativeGetOwnPropertyNames(toIndexedObject(O));\n          var result = [];\n          $forEach(names, function (key) {\n            if (!has(AllSymbols, key) && !has(hiddenKeys, key)) result.push(key);\n          });\n          return result;\n        };\n        var $getOwnPropertySymbols = function getOwnPropertySymbols(O) {\n          var IS_OBJECT_PROTOTYPE = O === ObjectPrototype;\n          var names = nativeGetOwnPropertyNames(IS_OBJECT_PROTOTYPE ? ObjectPrototypeSymbols : toIndexedObject(O));\n          var result = [];\n          $forEach(names, function (key) {\n            if (has(AllSymbols, key) && (!IS_OBJECT_PROTOTYPE || has(ObjectPrototype, key))) {\n              result.push(AllSymbols[key]);\n            }\n          });\n          return result;\n        };\n\n        // `Symbol` constructor\n        // https://tc39.es/ecma262/#sec-symbol-constructor\n        if (!NATIVE_SYMBOL) {\n          $Symbol = function Symbol() {\n            if (this instanceof $Symbol) throw TypeError('Symbol is not a constructor');\n            var description = !arguments.length || arguments[0] === undefined ? undefined : $toString(arguments[0]);\n            var tag = uid(description);\n            var setter = function (value) {\n              if (this === ObjectPrototype) setter.call(ObjectPrototypeSymbols, value);\n              if (has(this, HIDDEN) && has(this[HIDDEN], tag)) this[HIDDEN][tag] = false;\n              setSymbolDescriptor(this, tag, createPropertyDescriptor(1, value));\n            };\n            if (DESCRIPTORS && USE_SETTER) setSymbolDescriptor(ObjectPrototype, tag, {\n              configurable: true,\n              set: setter\n            });\n            return wrap(tag, description);\n          };\n          redefine($Symbol[PROTOTYPE], 'toString', function toString() {\n            return getInternalState(this).tag;\n          });\n          redefine($Symbol, 'withoutSetter', function (description) {\n            return wrap(uid(description), description);\n          });\n          propertyIsEnumerableModule.f = $propertyIsEnumerable;\n          definePropertyModule.f = $defineProperty;\n          getOwnPropertyDescriptorModule.f = $getOwnPropertyDescriptor;\n          getOwnPropertyNamesModule.f = getOwnPropertyNamesExternal.f = $getOwnPropertyNames;\n          getOwnPropertySymbolsModule.f = $getOwnPropertySymbols;\n          wrappedWellKnownSymbolModule.f = function (name) {\n            return wrap(wellKnownSymbol(name), name);\n          };\n          if (DESCRIPTORS) {\n            // https://github.com/tc39/proposal-Symbol-description\n            nativeDefineProperty($Symbol[PROTOTYPE], 'description', {\n              configurable: true,\n              get: function description() {\n                return getInternalState(this).description;\n              }\n            });\n            if (!IS_PURE) {\n              redefine(ObjectPrototype, 'propertyIsEnumerable', $propertyIsEnumerable, {\n                unsafe: true\n              });\n            }\n          }\n        }\n        $({\n          global: true,\n          wrap: true,\n          forced: !NATIVE_SYMBOL,\n          sham: !NATIVE_SYMBOL\n        }, {\n          Symbol: $Symbol\n        });\n        $forEach(objectKeys(WellKnownSymbolsStore), function (name) {\n          defineWellKnownSymbol(name);\n        });\n        $({\n          target: SYMBOL,\n          stat: true,\n          forced: !NATIVE_SYMBOL\n        }, {\n          // `Symbol.for` method\n          // https://tc39.es/ecma262/#sec-symbol.for\n          'for': function (key) {\n            var string = $toString(key);\n            if (has(StringToSymbolRegistry, string)) return StringToSymbolRegistry[string];\n            var symbol = $Symbol(string);\n            StringToSymbolRegistry[string] = symbol;\n            SymbolToStringRegistry[symbol] = string;\n            return symbol;\n          },\n          // `Symbol.keyFor` method\n          // https://tc39.es/ecma262/#sec-symbol.keyfor\n          keyFor: function keyFor(sym) {\n            if (!isSymbol(sym)) throw TypeError(sym + ' is not a symbol');\n            if (has(SymbolToStringRegistry, sym)) return SymbolToStringRegistry[sym];\n          },\n          useSetter: function () {\n            USE_SETTER = true;\n          },\n          useSimple: function () {\n            USE_SETTER = false;\n          }\n        });\n        $({\n          target: 'Object',\n          stat: true,\n          forced: !NATIVE_SYMBOL,\n          sham: !DESCRIPTORS\n        }, {\n          // `Object.create` method\n          // https://tc39.es/ecma262/#sec-object.create\n          create: $create,\n          // `Object.defineProperty` method\n          // https://tc39.es/ecma262/#sec-object.defineproperty\n          defineProperty: $defineProperty,\n          // `Object.defineProperties` method\n          // https://tc39.es/ecma262/#sec-object.defineproperties\n          defineProperties: $defineProperties,\n          // `Object.getOwnPropertyDescriptor` method\n          // https://tc39.es/ecma262/#sec-object.getownpropertydescriptors\n          getOwnPropertyDescriptor: $getOwnPropertyDescriptor\n        });\n        $({\n          target: 'Object',\n          stat: true,\n          forced: !NATIVE_SYMBOL\n        }, {\n          // `Object.getOwnPropertyNames` method\n          // https://tc39.es/ecma262/#sec-object.getownpropertynames\n          getOwnPropertyNames: $getOwnPropertyNames,\n          // `Object.getOwnPropertySymbols` method\n          // https://tc39.es/ecma262/#sec-object.getownpropertysymbols\n          getOwnPropertySymbols: $getOwnPropertySymbols\n        });\n\n        // Chrome 38 and 39 `Object.getOwnPropertySymbols` fails on primitives\n        // https://bugs.chromium.org/p/v8/issues/detail?id=3443\n        $({\n          target: 'Object',\n          stat: true,\n          forced: fails(function () {\n            getOwnPropertySymbolsModule.f(1);\n          })\n        }, {\n          getOwnPropertySymbols: function getOwnPropertySymbols(it) {\n            return getOwnPropertySymbolsModule.f(toObject(it));\n          }\n        });\n\n        // `JSON.stringify` method behavior with symbols\n        // https://tc39.es/ecma262/#sec-json.stringify\n        if ($stringify) {\n          var FORCED_JSON_STRINGIFY = !NATIVE_SYMBOL || fails(function () {\n            var symbol = $Symbol();\n            // MS Edge converts symbol values to JSON as {}\n            return $stringify([symbol]) != '[null]'\n            // WebKit converts symbol values to JSON as null\n            || $stringify({\n              a: symbol\n            }) != '{}'\n            // V8 throws on boxed symbols\n            || $stringify(Object(symbol)) != '{}';\n          });\n          $({\n            target: 'JSON',\n            stat: true,\n            forced: FORCED_JSON_STRINGIFY\n          }, {\n            // eslint-disable-next-line no-unused-vars -- required for `.length`\n            stringify: function stringify(it, replacer, space) {\n              var args = [it];\n              var index = 1;\n              var $replacer;\n              while (arguments.length > index) args.push(arguments[index++]);\n              $replacer = replacer;\n              if (!isObject(replacer) && it === undefined || isSymbol(it)) return; // IE8 returns string on undefined\n              if (!isArray(replacer)) replacer = function (key, value) {\n                if (typeof $replacer == 'function') value = $replacer.call(this, key, value);\n                if (!isSymbol(value)) return value;\n              };\n              args[1] = replacer;\n              return $stringify.apply(null, args);\n            }\n          });\n        }\n\n        // `Symbol.prototype[@@toPrimitive]` method\n        // https://tc39.es/ecma262/#sec-symbol.prototype-@@toprimitive\n        if (!$Symbol[PROTOTYPE][TO_PRIMITIVE]) {\n          createNonEnumerableProperty($Symbol[PROTOTYPE], TO_PRIMITIVE, $Symbol[PROTOTYPE].valueOf);\n        }\n        // `Symbol.prototype[@@toStringTag]` property\n        // https://tc39.es/ecma262/#sec-symbol.prototype-@@tostringtag\n        setToStringTag($Symbol, SYMBOL);\n        hiddenKeys[HIDDEN] = true;\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/web.dom-collections.for-each.js\": (\n      /*!**********************************************************************!*\\\n        !*** ./node_modules/core-js/modules/web.dom-collections.for-each.js ***!\n        \\**********************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var DOMIterables = __webpack_require__(/*! ../internals/dom-iterables */\"./node_modules/core-js/internals/dom-iterables.js\");\n        var forEach = __webpack_require__(/*! ../internals/array-for-each */\"./node_modules/core-js/internals/array-for-each.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        for (var COLLECTION_NAME in DOMIterables) {\n          var Collection = global[COLLECTION_NAME];\n          var CollectionPrototype = Collection && Collection.prototype;\n          // some Chrome versions have non-configurable methods on DOMTokenList\n          if (CollectionPrototype && CollectionPrototype.forEach !== forEach) try {\n            createNonEnumerableProperty(CollectionPrototype, 'forEach', forEach);\n          } catch (error) {\n            CollectionPrototype.forEach = forEach;\n          }\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/core-js/modules/web.dom-collections.iterator.js\": (\n      /*!**********************************************************************!*\\\n        !*** ./node_modules/core-js/modules/web.dom-collections.iterator.js ***!\n        \\**********************************************************************/\n      /***/\n      function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {\n        var global = __webpack_require__(/*! ../internals/global */\"./node_modules/core-js/internals/global.js\");\n        var DOMIterables = __webpack_require__(/*! ../internals/dom-iterables */\"./node_modules/core-js/internals/dom-iterables.js\");\n        var ArrayIteratorMethods = __webpack_require__(/*! ../modules/es.array.iterator */\"./node_modules/core-js/modules/es.array.iterator.js\");\n        var createNonEnumerableProperty = __webpack_require__(/*! ../internals/create-non-enumerable-property */\"./node_modules/core-js/internals/create-non-enumerable-property.js\");\n        var wellKnownSymbol = __webpack_require__(/*! ../internals/well-known-symbol */\"./node_modules/core-js/internals/well-known-symbol.js\");\n        var ITERATOR = wellKnownSymbol('iterator');\n        var TO_STRING_TAG = wellKnownSymbol('toStringTag');\n        var ArrayValues = ArrayIteratorMethods.values;\n        for (var COLLECTION_NAME in DOMIterables) {\n          var Collection = global[COLLECTION_NAME];\n          var CollectionPrototype = Collection && Collection.prototype;\n          if (CollectionPrototype) {\n            // some Chrome versions have non-configurable methods on DOMTokenList\n            if (CollectionPrototype[ITERATOR] !== ArrayValues) try {\n              createNonEnumerableProperty(CollectionPrototype, ITERATOR, ArrayValues);\n            } catch (error) {\n              CollectionPrototype[ITERATOR] = ArrayValues;\n            }\n            if (!CollectionPrototype[TO_STRING_TAG]) {\n              createNonEnumerableProperty(CollectionPrototype, TO_STRING_TAG, COLLECTION_NAME);\n            }\n            if (DOMIterables[COLLECTION_NAME]) for (var METHOD_NAME in ArrayIteratorMethods) {\n              // some Chrome versions have non-configurable methods on DOMTokenList\n              if (CollectionPrototype[METHOD_NAME] !== ArrayIteratorMethods[METHOD_NAME]) try {\n                createNonEnumerableProperty(CollectionPrototype, METHOD_NAME, ArrayIteratorMethods[METHOD_NAME]);\n              } catch (error) {\n                CollectionPrototype[METHOD_NAME] = ArrayIteratorMethods[METHOD_NAME];\n              }\n            }\n          }\n        }\n\n        /***/\n      }),\n      /***/\"./node_modules/es6-promise/dist/es6-promise.js\": (\n      /*!******************************************************!*\\\n        !*** ./node_modules/es6-promise/dist/es6-promise.js ***!\n        \\******************************************************/\n      /***/\n      function (module) {\n        /*!\n         * @overview es6-promise - a tiny implementation of Promises/A+.\n         * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)\n         * @license   Licensed under MIT license\n         *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n         * @version   v4.2.8+1e68dce6\n         */\n\n        (function (global, factory) {\n          true ? module.exports = factory() : 0;\n        })(this, function () {\n          'use strict';\n\n          function objectOrFunction(x) {\n            var type = typeof x;\n            return x !== null && (type === 'object' || type === 'function');\n          }\n          function isFunction(x) {\n            return typeof x === 'function';\n          }\n          var _isArray = void 0;\n          if (Array.isArray) {\n            _isArray = Array.isArray;\n          } else {\n            _isArray = function (x) {\n              return Object.prototype.toString.call(x) === '[object Array]';\n            };\n          }\n          var isArray = _isArray;\n          var len = 0;\n          var vertxNext = void 0;\n          var customSchedulerFn = void 0;\n          var asap = function asap(callback, arg) {\n            queue[len] = callback;\n            queue[len + 1] = arg;\n            len += 2;\n            if (len === 2) {\n              // If len is 2, that means that we need to schedule an async flush.\n              // If additional callbacks are queued before the queue is flushed, they\n              // will be processed by this flush that we are scheduling.\n              if (customSchedulerFn) {\n                customSchedulerFn(flush);\n              } else {\n                scheduleFlush();\n              }\n            }\n          };\n          function setScheduler(scheduleFn) {\n            customSchedulerFn = scheduleFn;\n          }\n          function setAsap(asapFn) {\n            asap = asapFn;\n          }\n          var browserWindow = typeof window !== 'undefined' ? window : undefined;\n          var browserGlobal = browserWindow || {};\n          var BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\n          var isNode = typeof self === 'undefined' && typeof process !== 'undefined' && {}.toString.call(process) === '[object process]';\n\n          // test for web worker but not in IE10\n          var isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n          // node\n          function useNextTick() {\n            // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n            // see https://github.com/cujojs/when/issues/410 for details\n            return function () {\n              return process.nextTick(flush);\n            };\n          }\n\n          // vertx\n          function useVertxTimer() {\n            if (typeof vertxNext !== 'undefined') {\n              return function () {\n                vertxNext(flush);\n              };\n            }\n            return useSetTimeout();\n          }\n          function useMutationObserver() {\n            var iterations = 0;\n            var observer = new BrowserMutationObserver(flush);\n            var node = document.createTextNode('');\n            observer.observe(node, {\n              characterData: true\n            });\n            return function () {\n              node.data = iterations = ++iterations % 2;\n            };\n          }\n\n          // web worker\n          function useMessageChannel() {\n            var channel = new MessageChannel();\n            channel.port1.onmessage = flush;\n            return function () {\n              return channel.port2.postMessage(0);\n            };\n          }\n          function useSetTimeout() {\n            // Store setTimeout reference so es6-promise will be unaffected by\n            // other code modifying setTimeout (like sinon.useFakeTimers())\n            var globalSetTimeout = setTimeout;\n            return function () {\n              return globalSetTimeout(flush, 1);\n            };\n          }\n          var queue = new Array(1000);\n          function flush() {\n            for (var i = 0; i < len; i += 2) {\n              var callback = queue[i];\n              var arg = queue[i + 1];\n              callback(arg);\n              queue[i] = undefined;\n              queue[i + 1] = undefined;\n            }\n            len = 0;\n          }\n          function attemptVertx() {\n            try {\n              var vertx = Function('return this')().require('vertx');\n              vertxNext = vertx.runOnLoop || vertx.runOnContext;\n              return useVertxTimer();\n            } catch (e) {\n              return useSetTimeout();\n            }\n          }\n          var scheduleFlush = void 0;\n          // Decide what async method to use to triggering processing of queued callbacks:\n          if (isNode) {\n            scheduleFlush = useNextTick();\n          } else if (BrowserMutationObserver) {\n            scheduleFlush = useMutationObserver();\n          } else if (isWorker) {\n            scheduleFlush = useMessageChannel();\n          } else if (browserWindow === undefined && \"function\" === 'function') {\n            scheduleFlush = attemptVertx();\n          } else {\n            scheduleFlush = useSetTimeout();\n          }\n          function then(onFulfillment, onRejection) {\n            var parent = this;\n            var child = new this.constructor(noop);\n            if (child[PROMISE_ID] === undefined) {\n              makePromise(child);\n            }\n            var _state = parent._state;\n            if (_state) {\n              var callback = arguments[_state - 1];\n              asap(function () {\n                return invokeCallback(_state, child, callback, parent._result);\n              });\n            } else {\n              subscribe(parent, child, onFulfillment, onRejection);\n            }\n            return child;\n          }\n\n          /**\n            `Promise.resolve` returns a promise that will become resolved with the\n            passed `value`. It is shorthand for the following:\n          \n            ```javascript\n            let promise = new Promise(function(resolve, reject){\n              resolve(1);\n            });\n          \n            promise.then(function(value){\n              // value === 1\n            });\n            ```\n          \n            Instead of writing the above, your code now simply becomes the following:\n          \n            ```javascript\n            let promise = Promise.resolve(1);\n          \n            promise.then(function(value){\n              // value === 1\n            });\n            ```\n          \n            @method resolve\n            @static\n            @param {Any} value value that the returned promise will be resolved with\n            Useful for tooling.\n            @return {Promise} a promise that will become fulfilled with the given\n            `value`\n          */\n          function resolve$1(object) {\n            /*jshint validthis:true */\n            var Constructor = this;\n            if (object && typeof object === 'object' && object.constructor === Constructor) {\n              return object;\n            }\n            var promise = new Constructor(noop);\n            resolve(promise, object);\n            return promise;\n          }\n          var PROMISE_ID = Math.random().toString(36).substring(2);\n          function noop() {}\n          var PENDING = void 0;\n          var FULFILLED = 1;\n          var REJECTED = 2;\n          function selfFulfillment() {\n            return new TypeError(\"You cannot resolve a promise with itself\");\n          }\n          function cannotReturnOwn() {\n            return new TypeError('A promises callback cannot return that same promise.');\n          }\n          function tryThen(then$$1, value, fulfillmentHandler, rejectionHandler) {\n            try {\n              then$$1.call(value, fulfillmentHandler, rejectionHandler);\n            } catch (e) {\n              return e;\n            }\n          }\n          function handleForeignThenable(promise, thenable, then$$1) {\n            asap(function (promise) {\n              var sealed = false;\n              var error = tryThen(then$$1, thenable, function (value) {\n                if (sealed) {\n                  return;\n                }\n                sealed = true;\n                if (thenable !== value) {\n                  resolve(promise, value);\n                } else {\n                  fulfill(promise, value);\n                }\n              }, function (reason) {\n                if (sealed) {\n                  return;\n                }\n                sealed = true;\n                reject(promise, reason);\n              }, 'Settle: ' + (promise._label || ' unknown promise'));\n              if (!sealed && error) {\n                sealed = true;\n                reject(promise, error);\n              }\n            }, promise);\n          }\n          function handleOwnThenable(promise, thenable) {\n            if (thenable._state === FULFILLED) {\n              fulfill(promise, thenable._result);\n            } else if (thenable._state === REJECTED) {\n              reject(promise, thenable._result);\n            } else {\n              subscribe(thenable, undefined, function (value) {\n                return resolve(promise, value);\n              }, function (reason) {\n                return reject(promise, reason);\n              });\n            }\n          }\n          function handleMaybeThenable(promise, maybeThenable, then$$1) {\n            if (maybeThenable.constructor === promise.constructor && then$$1 === then && maybeThenable.constructor.resolve === resolve$1) {\n              handleOwnThenable(promise, maybeThenable);\n            } else {\n              if (then$$1 === undefined) {\n                fulfill(promise, maybeThenable);\n              } else if (isFunction(then$$1)) {\n                handleForeignThenable(promise, maybeThenable, then$$1);\n              } else {\n                fulfill(promise, maybeThenable);\n              }\n            }\n          }\n          function resolve(promise, value) {\n            if (promise === value) {\n              reject(promise, selfFulfillment());\n            } else if (objectOrFunction(value)) {\n              var then$$1 = void 0;\n              try {\n                then$$1 = value.then;\n              } catch (error) {\n                reject(promise, error);\n                return;\n              }\n              handleMaybeThenable(promise, value, then$$1);\n            } else {\n              fulfill(promise, value);\n            }\n          }\n          function publishRejection(promise) {\n            if (promise._onerror) {\n              promise._onerror(promise._result);\n            }\n            publish(promise);\n          }\n          function fulfill(promise, value) {\n            if (promise._state !== PENDING) {\n              return;\n            }\n            promise._result = value;\n            promise._state = FULFILLED;\n            if (promise._subscribers.length !== 0) {\n              asap(publish, promise);\n            }\n          }\n          function reject(promise, reason) {\n            if (promise._state !== PENDING) {\n              return;\n            }\n            promise._state = REJECTED;\n            promise._result = reason;\n            asap(publishRejection, promise);\n          }\n          function subscribe(parent, child, onFulfillment, onRejection) {\n            var _subscribers = parent._subscribers;\n            var length = _subscribers.length;\n            parent._onerror = null;\n            _subscribers[length] = child;\n            _subscribers[length + FULFILLED] = onFulfillment;\n            _subscribers[length + REJECTED] = onRejection;\n            if (length === 0 && parent._state) {\n              asap(publish, parent);\n            }\n          }\n          function publish(promise) {\n            var subscribers = promise._subscribers;\n            var settled = promise._state;\n            if (subscribers.length === 0) {\n              return;\n            }\n            var child = void 0,\n              callback = void 0,\n              detail = promise._result;\n            for (var i = 0; i < subscribers.length; i += 3) {\n              child = subscribers[i];\n              callback = subscribers[i + settled];\n              if (child) {\n                invokeCallback(settled, child, callback, detail);\n              } else {\n                callback(detail);\n              }\n            }\n            promise._subscribers.length = 0;\n          }\n          function invokeCallback(settled, promise, callback, detail) {\n            var hasCallback = isFunction(callback),\n              value = void 0,\n              error = void 0,\n              succeeded = true;\n            if (hasCallback) {\n              try {\n                value = callback(detail);\n              } catch (e) {\n                succeeded = false;\n                error = e;\n              }\n              if (promise === value) {\n                reject(promise, cannotReturnOwn());\n                return;\n              }\n            } else {\n              value = detail;\n            }\n            if (promise._state !== PENDING) {\n              // noop\n            } else if (hasCallback && succeeded) {\n              resolve(promise, value);\n            } else if (succeeded === false) {\n              reject(promise, error);\n            } else if (settled === FULFILLED) {\n              fulfill(promise, value);\n            } else if (settled === REJECTED) {\n              reject(promise, value);\n            }\n          }\n          function initializePromise(promise, resolver) {\n            try {\n              resolver(function resolvePromise(value) {\n                resolve(promise, value);\n              }, function rejectPromise(reason) {\n                reject(promise, reason);\n              });\n            } catch (e) {\n              reject(promise, e);\n            }\n          }\n          var id = 0;\n          function nextId() {\n            return id++;\n          }\n          function makePromise(promise) {\n            promise[PROMISE_ID] = id++;\n            promise._state = undefined;\n            promise._result = undefined;\n            promise._subscribers = [];\n          }\n          function validationError() {\n            return new Error('Array Methods must be provided an Array');\n          }\n          var Enumerator = function () {\n            function Enumerator(Constructor, input) {\n              this._instanceConstructor = Constructor;\n              this.promise = new Constructor(noop);\n              if (!this.promise[PROMISE_ID]) {\n                makePromise(this.promise);\n              }\n              if (isArray(input)) {\n                this.length = input.length;\n                this._remaining = input.length;\n                this._result = new Array(this.length);\n                if (this.length === 0) {\n                  fulfill(this.promise, this._result);\n                } else {\n                  this.length = this.length || 0;\n                  this._enumerate(input);\n                  if (this._remaining === 0) {\n                    fulfill(this.promise, this._result);\n                  }\n                }\n              } else {\n                reject(this.promise, validationError());\n              }\n            }\n            Enumerator.prototype._enumerate = function _enumerate(input) {\n              for (var i = 0; this._state === PENDING && i < input.length; i++) {\n                this._eachEntry(input[i], i);\n              }\n            };\n            Enumerator.prototype._eachEntry = function _eachEntry(entry, i) {\n              var c = this._instanceConstructor;\n              var resolve$$1 = c.resolve;\n              if (resolve$$1 === resolve$1) {\n                var _then = void 0;\n                var error = void 0;\n                var didError = false;\n                try {\n                  _then = entry.then;\n                } catch (e) {\n                  didError = true;\n                  error = e;\n                }\n                if (_then === then && entry._state !== PENDING) {\n                  this._settledAt(entry._state, i, entry._result);\n                } else if (typeof _then !== 'function') {\n                  this._remaining--;\n                  this._result[i] = entry;\n                } else if (c === Promise$1) {\n                  var promise = new c(noop);\n                  if (didError) {\n                    reject(promise, error);\n                  } else {\n                    handleMaybeThenable(promise, entry, _then);\n                  }\n                  this._willSettleAt(promise, i);\n                } else {\n                  this._willSettleAt(new c(function (resolve$$1) {\n                    return resolve$$1(entry);\n                  }), i);\n                }\n              } else {\n                this._willSettleAt(resolve$$1(entry), i);\n              }\n            };\n            Enumerator.prototype._settledAt = function _settledAt(state, i, value) {\n              var promise = this.promise;\n              if (promise._state === PENDING) {\n                this._remaining--;\n                if (state === REJECTED) {\n                  reject(promise, value);\n                } else {\n                  this._result[i] = value;\n                }\n              }\n              if (this._remaining === 0) {\n                fulfill(promise, this._result);\n              }\n            };\n            Enumerator.prototype._willSettleAt = function _willSettleAt(promise, i) {\n              var enumerator = this;\n              subscribe(promise, undefined, function (value) {\n                return enumerator._settledAt(FULFILLED, i, value);\n              }, function (reason) {\n                return enumerator._settledAt(REJECTED, i, reason);\n              });\n            };\n            return Enumerator;\n          }();\n\n          /**\n            `Promise.all` accepts an array of promises, and returns a new promise which\n            is fulfilled with an array of fulfillment values for the passed promises, or\n            rejected with the reason of the first passed promise to be rejected. It casts all\n            elements of the passed iterable to promises as it runs this algorithm.\n          \n            Example:\n          \n            ```javascript\n            let promise1 = resolve(1);\n            let promise2 = resolve(2);\n            let promise3 = resolve(3);\n            let promises = [ promise1, promise2, promise3 ];\n          \n            Promise.all(promises).then(function(array){\n              // The array here would be [ 1, 2, 3 ];\n            });\n            ```\n          \n            If any of the `promises` given to `all` are rejected, the first promise\n            that is rejected will be given as an argument to the returned promises's\n            rejection handler. For example:\n          \n            Example:\n          \n            ```javascript\n            let promise1 = resolve(1);\n            let promise2 = reject(new Error(\"2\"));\n            let promise3 = reject(new Error(\"3\"));\n            let promises = [ promise1, promise2, promise3 ];\n          \n            Promise.all(promises).then(function(array){\n              // Code here never runs because there are rejected promises!\n            }, function(error) {\n              // error.message === \"2\"\n            });\n            ```\n          \n            @method all\n            @static\n            @param {Array} entries array of promises\n            @param {String} label optional string for labeling the promise.\n            Useful for tooling.\n            @return {Promise} promise that is fulfilled when all `promises` have been\n            fulfilled, or rejected if any of them become rejected.\n            @static\n          */\n          function all(entries) {\n            return new Enumerator(this, entries).promise;\n          }\n\n          /**\n            `Promise.race` returns a new promise which is settled in the same way as the\n            first passed promise to settle.\n          \n            Example:\n          \n            ```javascript\n            let promise1 = new Promise(function(resolve, reject){\n              setTimeout(function(){\n                resolve('promise 1');\n              }, 200);\n            });\n          \n            let promise2 = new Promise(function(resolve, reject){\n              setTimeout(function(){\n                resolve('promise 2');\n              }, 100);\n            });\n          \n            Promise.race([promise1, promise2]).then(function(result){\n              // result === 'promise 2' because it was resolved before promise1\n              // was resolved.\n            });\n            ```\n          \n            `Promise.race` is deterministic in that only the state of the first\n            settled promise matters. For example, even if other promises given to the\n            `promises` array argument are resolved, but the first settled promise has\n            become rejected before the other promises became fulfilled, the returned\n            promise will become rejected:\n          \n            ```javascript\n            let promise1 = new Promise(function(resolve, reject){\n              setTimeout(function(){\n                resolve('promise 1');\n              }, 200);\n            });\n          \n            let promise2 = new Promise(function(resolve, reject){\n              setTimeout(function(){\n                reject(new Error('promise 2'));\n              }, 100);\n            });\n          \n            Promise.race([promise1, promise2]).then(function(result){\n              // Code here never runs\n            }, function(reason){\n              // reason.message === 'promise 2' because promise 2 became rejected before\n              // promise 1 became fulfilled\n            });\n            ```\n          \n            An example real-world use case is implementing timeouts:\n          \n            ```javascript\n            Promise.race([ajax('foo.json'), timeout(5000)])\n            ```\n          \n            @method race\n            @static\n            @param {Array} promises array of promises to observe\n            Useful for tooling.\n            @return {Promise} a promise which settles in the same way as the first passed\n            promise to settle.\n          */\n          function race(entries) {\n            /*jshint validthis:true */\n            var Constructor = this;\n            if (!isArray(entries)) {\n              return new Constructor(function (_, reject) {\n                return reject(new TypeError('You must pass an array to race.'));\n              });\n            } else {\n              return new Constructor(function (resolve, reject) {\n                var length = entries.length;\n                for (var i = 0; i < length; i++) {\n                  Constructor.resolve(entries[i]).then(resolve, reject);\n                }\n              });\n            }\n          }\n\n          /**\n            `Promise.reject` returns a promise rejected with the passed `reason`.\n            It is shorthand for the following:\n          \n            ```javascript\n            let promise = new Promise(function(resolve, reject){\n              reject(new Error('WHOOPS'));\n            });\n          \n            promise.then(function(value){\n              // Code here doesn't run because the promise is rejected!\n            }, function(reason){\n              // reason.message === 'WHOOPS'\n            });\n            ```\n          \n            Instead of writing the above, your code now simply becomes the following:\n          \n            ```javascript\n            let promise = Promise.reject(new Error('WHOOPS'));\n          \n            promise.then(function(value){\n              // Code here doesn't run because the promise is rejected!\n            }, function(reason){\n              // reason.message === 'WHOOPS'\n            });\n            ```\n          \n            @method reject\n            @static\n            @param {Any} reason value that the returned promise will be rejected with.\n            Useful for tooling.\n            @return {Promise} a promise rejected with the given `reason`.\n          */\n          function reject$1(reason) {\n            /*jshint validthis:true */\n            var Constructor = this;\n            var promise = new Constructor(noop);\n            reject(promise, reason);\n            return promise;\n          }\n          function needsResolver() {\n            throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n          }\n          function needsNew() {\n            throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n          }\n\n          /**\n            Promise objects represent the eventual result of an asynchronous operation. The\n            primary way of interacting with a promise is through its `then` method, which\n            registers callbacks to receive either a promise's eventual value or the reason\n            why the promise cannot be fulfilled.\n          \n            Terminology\n            -----------\n          \n            - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n            - `thenable` is an object or function that defines a `then` method.\n            - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n            - `exception` is a value that is thrown using the throw statement.\n            - `reason` is a value that indicates why a promise was rejected.\n            - `settled` the final resting state of a promise, fulfilled or rejected.\n          \n            A promise can be in one of three states: pending, fulfilled, or rejected.\n          \n            Promises that are fulfilled have a fulfillment value and are in the fulfilled\n            state.  Promises that are rejected have a rejection reason and are in the\n            rejected state.  A fulfillment value is never a thenable.\n          \n            Promises can also be said to *resolve* a value.  If this value is also a\n            promise, then the original promise's settled state will match the value's\n            settled state.  So a promise that *resolves* a promise that rejects will\n            itself reject, and a promise that *resolves* a promise that fulfills will\n            itself fulfill.\n          \n          \n            Basic Usage:\n            ------------\n          \n            ```js\n            let promise = new Promise(function(resolve, reject) {\n              // on success\n              resolve(value);\n          \n              // on failure\n              reject(reason);\n            });\n          \n            promise.then(function(value) {\n              // on fulfillment\n            }, function(reason) {\n              // on rejection\n            });\n            ```\n          \n            Advanced Usage:\n            ---------------\n          \n            Promises shine when abstracting away asynchronous interactions such as\n            `XMLHttpRequest`s.\n          \n            ```js\n            function getJSON(url) {\n              return new Promise(function(resolve, reject){\n                let xhr = new XMLHttpRequest();\n          \n                xhr.open('GET', url);\n                xhr.onreadystatechange = handler;\n                xhr.responseType = 'json';\n                xhr.setRequestHeader('Accept', 'application/json');\n                xhr.send();\n          \n                function handler() {\n                  if (this.readyState === this.DONE) {\n                    if (this.status === 200) {\n                      resolve(this.response);\n                    } else {\n                      reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n                    }\n                  }\n                };\n              });\n            }\n          \n            getJSON('/posts.json').then(function(json) {\n              // on fulfillment\n            }, function(reason) {\n              // on rejection\n            });\n            ```\n          \n            Unlike callbacks, promises are great composable primitives.\n          \n            ```js\n            Promise.all([\n              getJSON('/posts'),\n              getJSON('/comments')\n            ]).then(function(values){\n              values[0] // => postsJSON\n              values[1] // => commentsJSON\n          \n              return values;\n            });\n            ```\n          \n            @class Promise\n            @param {Function} resolver\n            Useful for tooling.\n            @constructor\n          */\n\n          var Promise$1 = function () {\n            function Promise(resolver) {\n              this[PROMISE_ID] = nextId();\n              this._result = this._state = undefined;\n              this._subscribers = [];\n              if (noop !== resolver) {\n                typeof resolver !== 'function' && needsResolver();\n                this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n              }\n            }\n\n            /**\n            The primary way of interacting with a promise is through its `then` method,\n            which registers callbacks to receive either a promise's eventual value or the\n            reason why the promise cannot be fulfilled.\n             ```js\n            findUser().then(function(user){\n              // user is available\n            }, function(reason){\n              // user is unavailable, and you are given the reason why\n            });\n            ```\n             Chaining\n            --------\n             The return value of `then` is itself a promise.  This second, 'downstream'\n            promise is resolved with the return value of the first promise's fulfillment\n            or rejection handler, or rejected if the handler throws an exception.\n             ```js\n            findUser().then(function (user) {\n              return user.name;\n            }, function (reason) {\n              return 'default name';\n            }).then(function (userName) {\n              // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n              // will be `'default name'`\n            });\n             findUser().then(function (user) {\n              throw new Error('Found user, but still unhappy');\n            }, function (reason) {\n              throw new Error('`findUser` rejected and we're unhappy');\n            }).then(function (value) {\n              // never reached\n            }, function (reason) {\n              // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n              // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n            });\n            ```\n            If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n             ```js\n            findUser().then(function (user) {\n              throw new PedagogicalException('Upstream error');\n            }).then(function (value) {\n              // never reached\n            }).then(function (value) {\n              // never reached\n            }, function (reason) {\n              // The `PedgagocialException` is propagated all the way down to here\n            });\n            ```\n             Assimilation\n            ------------\n             Sometimes the value you want to propagate to a downstream promise can only be\n            retrieved asynchronously. This can be achieved by returning a promise in the\n            fulfillment or rejection handler. The downstream promise will then be pending\n            until the returned promise is settled. This is called *assimilation*.\n             ```js\n            findUser().then(function (user) {\n              return findCommentsByAuthor(user);\n            }).then(function (comments) {\n              // The user's comments are now available\n            });\n            ```\n             If the assimliated promise rejects, then the downstream promise will also reject.\n             ```js\n            findUser().then(function (user) {\n              return findCommentsByAuthor(user);\n            }).then(function (comments) {\n              // If `findCommentsByAuthor` fulfills, we'll have the value here\n            }, function (reason) {\n              // If `findCommentsByAuthor` rejects, we'll have the reason here\n            });\n            ```\n             Simple Example\n            --------------\n             Synchronous Example\n             ```javascript\n            let result;\n             try {\n              result = findResult();\n              // success\n            } catch(reason) {\n              // failure\n            }\n            ```\n             Errback Example\n             ```js\n            findResult(function(result, err){\n              if (err) {\n                // failure\n              } else {\n                // success\n              }\n            });\n            ```\n             Promise Example;\n             ```javascript\n            findResult().then(function(result){\n              // success\n            }, function(reason){\n              // failure\n            });\n            ```\n             Advanced Example\n            --------------\n             Synchronous Example\n             ```javascript\n            let author, books;\n             try {\n              author = findAuthor();\n              books  = findBooksByAuthor(author);\n              // success\n            } catch(reason) {\n              // failure\n            }\n            ```\n             Errback Example\n             ```js\n             function foundBooks(books) {\n             }\n             function failure(reason) {\n             }\n             findAuthor(function(author, err){\n              if (err) {\n                failure(err);\n                // failure\n              } else {\n                try {\n                  findBoooksByAuthor(author, function(books, err) {\n                    if (err) {\n                      failure(err);\n                    } else {\n                      try {\n                        foundBooks(books);\n                      } catch(reason) {\n                        failure(reason);\n                      }\n                    }\n                  });\n                } catch(error) {\n                  failure(err);\n                }\n                // success\n              }\n            });\n            ```\n             Promise Example;\n             ```javascript\n            findAuthor().\n              then(findBooksByAuthor).\n              then(function(books){\n                // found books\n            }).catch(function(reason){\n              // something went wrong\n            });\n            ```\n             @method then\n            @param {Function} onFulfilled\n            @param {Function} onRejected\n            Useful for tooling.\n            @return {Promise}\n            */\n\n            /**\n            `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n            as the catch block of a try/catch statement.\n            ```js\n            function findAuthor(){\n            throw new Error('couldn't find that author');\n            }\n            // synchronous\n            try {\n            findAuthor();\n            } catch(reason) {\n            // something went wrong\n            }\n            // async with promises\n            findAuthor().catch(function(reason){\n            // something went wrong\n            });\n            ```\n            @method catch\n            @param {Function} onRejection\n            Useful for tooling.\n            @return {Promise}\n            */\n\n            Promise.prototype.catch = function _catch(onRejection) {\n              return this.then(null, onRejection);\n            };\n\n            /**\n              `finally` will be invoked regardless of the promise's fate just as native\n              try/catch/finally behaves\n            \n              Synchronous example:\n            \n              ```js\n              findAuthor() {\n                if (Math.random() > 0.5) {\n                  throw new Error();\n                }\n                return new Author();\n              }\n            \n              try {\n                return findAuthor(); // succeed or fail\n              } catch(error) {\n                return findOtherAuther();\n              } finally {\n                // always runs\n                // doesn't affect the return value\n              }\n              ```\n            \n              Asynchronous example:\n            \n              ```js\n              findAuthor().catch(function(reason){\n                return findOtherAuther();\n              }).finally(function(){\n                // author was either found, or not\n              });\n              ```\n            \n              @method finally\n              @param {Function} callback\n              @return {Promise}\n            */\n\n            Promise.prototype.finally = function _finally(callback) {\n              var promise = this;\n              var constructor = promise.constructor;\n              if (isFunction(callback)) {\n                return promise.then(function (value) {\n                  return constructor.resolve(callback()).then(function () {\n                    return value;\n                  });\n                }, function (reason) {\n                  return constructor.resolve(callback()).then(function () {\n                    throw reason;\n                  });\n                });\n              }\n              return promise.then(callback, callback);\n            };\n            return Promise;\n          }();\n          Promise$1.prototype.then = then;\n          Promise$1.all = all;\n          Promise$1.race = race;\n          Promise$1.resolve = resolve$1;\n          Promise$1.reject = reject$1;\n          Promise$1._setScheduler = setScheduler;\n          Promise$1._setAsap = setAsap;\n          Promise$1._asap = asap;\n\n          /*global self*/\n          function polyfill() {\n            var local = void 0;\n            if (typeof global !== 'undefined') {\n              local = global;\n            } else if (typeof self !== 'undefined') {\n              local = self;\n            } else {\n              try {\n                local = Function('return this')();\n              } catch (e) {\n                throw new Error('polyfill failed because global object is unavailable in this environment');\n              }\n            }\n            var P = local.Promise;\n            if (P) {\n              var promiseToString = null;\n              try {\n                promiseToString = Object.prototype.toString.call(P.resolve());\n              } catch (e) {\n                // silently ignored\n              }\n              if (promiseToString === '[object Promise]' && !P.cast) {\n                return;\n              }\n            }\n            local.Promise = Promise$1;\n          }\n\n          // Strange compat..\n          Promise$1.polyfill = polyfill;\n          Promise$1.Promise = Promise$1;\n          return Promise$1;\n        });\n\n        //# sourceMappingURL=es6-promise.map\n\n        /***/\n      }),\n      /***/\"html2canvas\": (\n      /*!******************************!*\\\n        !*** external \"html2canvas\" ***!\n        \\******************************/\n      /***/\n      function (module) {\n        \"use strict\";\n\n        module.exports = __WEBPACK_EXTERNAL_MODULE_html2canvas__;\n\n        /***/\n      }),\n      /***/\"jspdf\": (\n      /*!************************!*\\\n        !*** external \"jspdf\" ***!\n        \\************************/\n      /***/\n      function (module) {\n        \"use strict\";\n\n        module.exports = __WEBPACK_EXTERNAL_MODULE_jspdf__;\n\n        /***/\n      })\n\n      /******/\n    };\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/var cachedModule = __webpack_module_cache__[moduleId];\n      /******/\n      if (cachedModule !== undefined) {\n        /******/return cachedModule.exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    /******/ /* webpack/runtime/compat get default export */\n    /******/\n    !function () {\n      /******/ // getDefaultExport function for compatibility with non-harmony modules\n      /******/__webpack_require__.n = function (module) {\n        /******/var getter = module && module.__esModule ? /******/function () {\n          return module['default'];\n        } : /******/function () {\n          return module;\n        };\n        /******/\n        __webpack_require__.d(getter, {\n          a: getter\n        });\n        /******/\n        return getter;\n        /******/\n      };\n      /******/\n    }();\n    /******/\n    /******/ /* webpack/runtime/define property getters */\n    /******/\n    !function () {\n      /******/ // define getter functions for harmony exports\n      /******/__webpack_require__.d = function (exports, definition) {\n        /******/for (var key in definition) {\n          /******/if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n            /******/Object.defineProperty(exports, key, {\n              enumerable: true,\n              get: definition[key]\n            });\n            /******/\n          }\n          /******/\n        }\n        /******/\n      };\n      /******/\n    }();\n    /******/\n    /******/ /* webpack/runtime/hasOwnProperty shorthand */\n    /******/\n    !function () {\n      /******/__webpack_require__.o = function (obj, prop) {\n        return Object.prototype.hasOwnProperty.call(obj, prop);\n      };\n      /******/\n    }();\n    /******/\n    /******/ /* webpack/runtime/make namespace object */\n    /******/\n    !function () {\n      /******/ // define __esModule on exports\n      /******/__webpack_require__.r = function (exports) {\n        /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n          /******/Object.defineProperty(exports, Symbol.toStringTag, {\n            value: 'Module'\n          });\n          /******/\n        }\n        /******/\n        Object.defineProperty(exports, '__esModule', {\n          value: true\n        });\n        /******/\n      };\n      /******/\n    }();\n    /******/\n    /************************************************************************/\n    var __webpack_exports__ = {};\n    // This entry need to be wrapped in an IIFE because it need to be in strict mode.\n    !function () {\n      \"use strict\";\n\n      /*!**********************!*\\\n        !*** ./src/index.js ***!\n        \\**********************/\n      __webpack_require__.r(__webpack_exports__);\n      /* harmony import */\n      var _worker_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./worker.js */\"./src/worker.js\");\n      /* harmony import */\n      var _plugin_jspdf_plugin_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plugin/jspdf-plugin.js */\"./src/plugin/jspdf-plugin.js\");\n      /* harmony import */\n      var _plugin_pagebreaks_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./plugin/pagebreaks.js */\"./src/plugin/pagebreaks.js\");\n      /* harmony import */\n      var _plugin_hyperlinks_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plugin/hyperlinks.js */\"./src/plugin/hyperlinks.js\");\n\n      /**\n       * Generate a PDF from an HTML element or string using html2canvas and jsPDF.\n       *\n       * @param {Element|string} source The source element or HTML string.\n       * @param {Object=} opt An object of optional settings: 'margin', 'filename',\n       *    'image' ('type' and 'quality'), and 'html2canvas' / 'jspdf', which are\n       *    sent as settings to their corresponding functions.\n       */\n\n      var html2pdf = function html2pdf(src, opt) {\n        // Create a new worker with the given options.\n        var worker = new html2pdf.Worker(opt);\n        if (src) {\n          // If src is specified, perform the traditional 'simple' operation.\n          return worker.from(src).save();\n        } else {\n          // Otherwise, return the worker for new Promise-based operation.\n          return worker;\n        }\n      };\n      html2pdf.Worker = _worker_js__WEBPACK_IMPORTED_MODULE_0__.default; // Expose the html2pdf function.\n\n      /* harmony default export */\n      __webpack_exports__[\"default\"] = html2pdf;\n    }();\n    __webpack_exports__ = __webpack_exports__.default;\n    /******/\n    return __webpack_exports__;\n    /******/\n  }();\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}