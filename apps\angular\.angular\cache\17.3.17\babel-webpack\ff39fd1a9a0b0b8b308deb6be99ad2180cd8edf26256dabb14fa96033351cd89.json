{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n  if (n === 1) return 1;\n  if (e === 0 && !(i === 0) && i % 1000000 === 0 && v === 0 || !(e >= 0 && e <= 5)) return 4;\n  return 5;\n}\nexport default [\"es-PA\", [[\"a. m.\", \"p. m.\"], u, u], u, [[\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"], [\"dom\", \"lun\", \"mar\", \"mié\", \"jue\", \"vie\", \"sáb\"], [\"domingo\", \"lunes\", \"martes\", \"miércoles\", \"jueves\", \"viernes\", \"sábado\"], [\"DO\", \"LU\", \"MA\", \"MI\", \"JU\", \"VI\", \"SA\"]], [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dom\", \"lun\", \"mar\", \"mié\", \"jue\", \"vie\", \"sáb\"], [\"domingo\", \"lunes\", \"martes\", \"miércoles\", \"jueves\", \"viernes\", \"sábado\"], [\"DO\", \"LU\", \"MA\", \"MI\", \"JU\", \"VI\", \"SA\"]], [[\"E\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"ene\", \"feb\", \"mar\", \"abr\", \"may\", \"jun\", \"jul\", \"ago\", \"sept\", \"oct\", \"nov\", \"dic\"], [\"enero\", \"febrero\", \"marzo\", \"abril\", \"mayo\", \"junio\", \"julio\", \"agosto\", \"septiembre\", \"octubre\", \"noviembre\", \"diciembre\"]], u, [[\"a. C.\", \"d. C.\"], u, [\"antes de Cristo\", \"después de Cristo\"]], 0, [6, 0], [\"MM/dd/yy\", \"MM/dd/y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", \"{1} {0}\", \"{1}, {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"¤#,##0.00\", \"#E0\"], \"PAB\", \"B/.\", \"balboa panameño\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"ESP\": [\"₧\"],\n  \"EUR\": [u, \"€\"],\n  \"FKP\": [u, \"FK£\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [u, \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PAB\": [\"B/.\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"SSP\": [u, \"SD£\"],\n  \"SYP\": [u, \"S£\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VEF\": [u, \"BsF\"],\n  \"VND\": [u, \"₫\"],\n  \"XAF\": [],\n  \"XCD\": [u, \"$\"],\n  \"XOF\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length", "e", "parseInt"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/es-PA.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n    if (n === 1)\n        return 1;\n    if (e === 0 && (!(i === 0) && (i % 1000000 === 0 && v === 0)) || !(e >= 0 && e <= 5))\n        return 4;\n    return 5;\n}\nexport default [\"es-PA\", [[\"a. m.\", \"p. m.\"], u, u], u, [[\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"], [\"dom\", \"lun\", \"mar\", \"mié\", \"jue\", \"vie\", \"sáb\"], [\"domingo\", \"lunes\", \"martes\", \"miércoles\", \"jueves\", \"viernes\", \"sábado\"], [\"DO\", \"LU\", \"MA\", \"MI\", \"JU\", \"VI\", \"SA\"]], [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dom\", \"lun\", \"mar\", \"mié\", \"jue\", \"vie\", \"sáb\"], [\"domingo\", \"lunes\", \"martes\", \"miércoles\", \"jueves\", \"viernes\", \"sábado\"], [\"DO\", \"LU\", \"MA\", \"MI\", \"JU\", \"VI\", \"SA\"]], [[\"E\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"ene\", \"feb\", \"mar\", \"abr\", \"may\", \"jun\", \"jul\", \"ago\", \"sept\", \"oct\", \"nov\", \"dic\"], [\"enero\", \"febrero\", \"marzo\", \"abril\", \"mayo\", \"junio\", \"julio\", \"agosto\", \"septiembre\", \"octubre\", \"noviembre\", \"diciembre\"]], u, [[\"a. C.\", \"d. C.\"], u, [\"antes de Cristo\", \"después de Cristo\"]], 0, [6, 0], [\"MM/dd/yy\", \"MM/dd/y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", \"{1} {0}\", \"{1}, {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"¤#,##0.00\", \"#E0\"], \"PAB\", \"B/.\", \"balboa panameño\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"ESP\": [\"₧\"], \"EUR\": [u, \"€\"], \"FKP\": [u, \"FK£\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PAB\": [\"B/.\"], \"PHP\": [u, \"₱\"], \"RON\": [u, \"L\"], \"SSP\": [u, \"SD£\"], \"SYP\": [u, \"S£\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VEF\": [u, \"BsF\"], \"VND\": [u, \"₫\"], \"XAF\": [], \"XCD\": [u, \"$\"], \"XOF\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;IAAEC,CAAC,GAAGC,QAAQ,CAACX,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;EACzK,IAAIP,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIS,CAAC,KAAK,CAAC,IAAK,EAAER,CAAC,KAAK,CAAC,CAAC,IAAKA,CAAC,GAAG,OAAO,KAAK,CAAC,IAAII,CAAC,KAAK,CAAG,IAAI,EAAEI,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAChF,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEb,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,oBAAoB,EAAE,0BAA0B,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}