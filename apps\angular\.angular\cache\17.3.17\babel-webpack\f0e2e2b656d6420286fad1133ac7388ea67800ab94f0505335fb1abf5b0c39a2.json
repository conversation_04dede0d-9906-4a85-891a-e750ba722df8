{"ast": null, "code": "function isPluralType(val) {\n  return val.one !== undefined;\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: 'ஒரு வினாடிக்கு குறைவாக',\n      in: 'ஒரு வினாடிக்குள்',\n      ago: 'ஒரு வினாடிக்கு முன்பு'\n    },\n    other: {\n      default: '{{count}} வினாடிகளுக்கு குறைவாக',\n      in: '{{count}} வினாடிகளுக்குள்',\n      ago: '{{count}} வினாடிகளுக்கு முன்பு'\n    }\n  },\n  xSeconds: {\n    one: {\n      default: '1 வினாடி',\n      in: '1 வினாடியில்',\n      ago: '1 வினாடி முன்பு'\n    },\n    other: {\n      default: '{{count}} விநாடிகள்',\n      in: '{{count}} வினாடிகளில்',\n      ago: '{{count}} விநாடிகளுக்கு முன்பு'\n    }\n  },\n  halfAMinute: {\n    default: 'அரை நிமிடம்',\n    in: 'அரை நிமிடத்தில்',\n    ago: 'அரை நிமிடம் முன்பு'\n  },\n  lessThanXMinutes: {\n    one: {\n      default: 'ஒரு நிமிடத்திற்கும் குறைவாக',\n      in: 'ஒரு நிமிடத்திற்குள்',\n      ago: 'ஒரு நிமிடத்திற்கு முன்பு'\n    },\n    other: {\n      default: '{{count}} நிமிடங்களுக்கும் குறைவாக',\n      in: '{{count}} நிமிடங்களுக்குள்',\n      ago: '{{count}} நிமிடங்களுக்கு முன்பு'\n    }\n  },\n  xMinutes: {\n    one: {\n      default: '1 நிமிடம்',\n      in: '1 நிமிடத்தில்',\n      ago: '1 நிமிடம் முன்பு'\n    },\n    other: {\n      default: '{{count}} நிமிடங்கள்',\n      in: '{{count}} நிமிடங்களில்',\n      ago: '{{count}} நிமிடங்களுக்கு முன்பு'\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: 'சுமார் 1 மணி நேரம்',\n      in: 'சுமார் 1 மணி நேரத்தில்',\n      ago: 'சுமார் 1 மணி நேரத்திற்கு முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} மணி நேரம்',\n      in: 'சுமார் {{count}} மணி நேரத்திற்கு முன்பு',\n      ago: 'சுமார் {{count}} மணி நேரத்தில்'\n    }\n  },\n  xHours: {\n    one: {\n      default: '1 மணி நேரம்',\n      in: '1 மணி நேரத்தில்',\n      ago: '1 மணி நேரத்திற்கு முன்பு'\n    },\n    other: {\n      default: '{{count}} மணி நேரம்',\n      in: '{{count}} மணி நேரத்தில்',\n      ago: '{{count}} மணி நேரத்திற்கு முன்பு'\n    }\n  },\n  xDays: {\n    one: {\n      default: '1 நாள்',\n      in: '1 நாளில்',\n      ago: '1 நாள் முன்பு'\n    },\n    other: {\n      default: '{{count}} நாட்கள்',\n      in: '{{count}} நாட்களில்',\n      ago: '{{count}} நாட்களுக்கு முன்பு'\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      default: 'சுமார் 1 வாரம்',\n      in: 'சுமார் 1 வாரத்தில்',\n      ago: 'சுமார் 1 வாரம் முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} வாரங்கள்',\n      in: 'சுமார் {{count}} வாரங்களில்',\n      ago: 'சுமார் {{count}} வாரங்களுக்கு முன்பு'\n    }\n  },\n  xWeeks: {\n    one: {\n      default: '1 வாரம்',\n      in: '1 வாரத்தில்',\n      ago: '1 வாரம் முன்பு'\n    },\n    other: {\n      default: '{{count}} வாரங்கள்',\n      in: '{{count}} வாரங்களில்',\n      ago: '{{count}} வாரங்களுக்கு முன்பு'\n    }\n  },\n  aboutXMonths: {\n    one: {\n      default: 'சுமார் 1 மாதம்',\n      in: 'சுமார் 1 மாதத்தில்',\n      ago: 'சுமார் 1 மாதத்திற்கு முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} மாதங்கள்',\n      in: 'சுமார் {{count}} மாதங்களில்',\n      ago: 'சுமார் {{count}} மாதங்களுக்கு முன்பு'\n    }\n  },\n  xMonths: {\n    one: {\n      default: '1 மாதம்',\n      in: '1 மாதத்தில்',\n      ago: '1 மாதம் முன்பு'\n    },\n    other: {\n      default: '{{count}} மாதங்கள்',\n      in: '{{count}} மாதங்களில்',\n      ago: '{{count}} மாதங்களுக்கு முன்பு'\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: 'சுமார் 1 வருடம்',\n      in: 'சுமார் 1 ஆண்டில்',\n      ago: 'சுமார் 1 வருடம் முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} ஆண்டுகள்',\n      in: 'சுமார் {{count}} ஆண்டுகளில்',\n      ago: 'சுமார் {{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  },\n  xYears: {\n    one: {\n      default: '1 வருடம்',\n      in: '1 ஆண்டில்',\n      ago: '1 வருடம் முன்பு'\n    },\n    other: {\n      default: '{{count}} ஆண்டுகள்',\n      in: '{{count}} ஆண்டுகளில்',\n      ago: '{{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  },\n  overXYears: {\n    one: {\n      default: '1 வருடத்திற்கு மேல்',\n      in: '1 வருடத்திற்கும் மேலாக',\n      ago: '1 வருடம் முன்பு'\n    },\n    other: {\n      default: '{{count}} ஆண்டுகளுக்கும் மேலாக',\n      in: '{{count}} ஆண்டுகளில்',\n      ago: '{{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  },\n  almostXYears: {\n    one: {\n      default: 'கிட்டத்தட்ட 1 வருடம்',\n      in: 'கிட்டத்தட்ட 1 ஆண்டில்',\n      ago: 'கிட்டத்தட்ட 1 வருடம் முன்பு'\n    },\n    other: {\n      default: 'கிட்டத்தட்ட {{count}} ஆண்டுகள்',\n      in: 'கிட்டத்தட்ட {{count}} ஆண்டுகளில்',\n      ago: 'கிட்டத்தட்ட {{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tense = options !== null && options !== void 0 && options.addSuffix ? options.comparison && options.comparison > 0 ? 'in' : 'ago' : 'default';\n  var tokenValue = formatDistanceLocale[token];\n  if (!isPluralType(tokenValue)) return tokenValue[tense];\n  if (count === 1) {\n    return tokenValue.one[tense];\n  } else {\n    return tokenValue.other[tense].replace('{{count}}', String(count));\n  }\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["isPluralType", "val", "one", "undefined", "formatDistanceLocale", "lessThanXSeconds", "default", "in", "ago", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "tense", "addSuffix", "comparison", "tokenValue", "replace", "String"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/ta/_lib/formatDistance/index.js"], "sourcesContent": ["function isPluralType(val) {\n  return val.one !== undefined;\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: 'ஒரு வினாடிக்கு குறைவாக',\n      in: 'ஒரு வினாடிக்குள்',\n      ago: 'ஒரு வினாடிக்கு முன்பு'\n    },\n    other: {\n      default: '{{count}} வினாடிகளுக்கு குறைவாக',\n      in: '{{count}} வினாடிகளுக்குள்',\n      ago: '{{count}} வினாடிகளுக்கு முன்பு'\n    }\n  },\n  xSeconds: {\n    one: {\n      default: '1 வினாடி',\n      in: '1 வினாடியில்',\n      ago: '1 வினாடி முன்பு'\n    },\n    other: {\n      default: '{{count}} விநாடிகள்',\n      in: '{{count}} வினாடிகளில்',\n      ago: '{{count}} விநாடிகளுக்கு முன்பு'\n    }\n  },\n  halfAMinute: {\n    default: 'அரை நிமிடம்',\n    in: 'அரை நிமிடத்தில்',\n    ago: 'அரை நிமிடம் முன்பு'\n  },\n  lessThanXMinutes: {\n    one: {\n      default: 'ஒரு நிமிடத்திற்கும் குறைவாக',\n      in: 'ஒரு நிமிடத்திற்குள்',\n      ago: 'ஒரு நிமிடத்திற்கு முன்பு'\n    },\n    other: {\n      default: '{{count}} நிமிடங்களுக்கும் குறைவாக',\n      in: '{{count}} நிமிடங்களுக்குள்',\n      ago: '{{count}} நிமிடங்களுக்கு முன்பு'\n    }\n  },\n  xMinutes: {\n    one: {\n      default: '1 நிமிடம்',\n      in: '1 நிமிடத்தில்',\n      ago: '1 நிமிடம் முன்பு'\n    },\n    other: {\n      default: '{{count}} நிமிடங்கள்',\n      in: '{{count}} நிமிடங்களில்',\n      ago: '{{count}} நிமிடங்களுக்கு முன்பு'\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: 'சுமார் 1 மணி நேரம்',\n      in: 'சுமார் 1 மணி நேரத்தில்',\n      ago: 'சுமார் 1 மணி நேரத்திற்கு முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} மணி நேரம்',\n      in: 'சுமார் {{count}} மணி நேரத்திற்கு முன்பு',\n      ago: 'சுமார் {{count}} மணி நேரத்தில்'\n    }\n  },\n  xHours: {\n    one: {\n      default: '1 மணி நேரம்',\n      in: '1 மணி நேரத்தில்',\n      ago: '1 மணி நேரத்திற்கு முன்பு'\n    },\n    other: {\n      default: '{{count}} மணி நேரம்',\n      in: '{{count}} மணி நேரத்தில்',\n      ago: '{{count}} மணி நேரத்திற்கு முன்பு'\n    }\n  },\n  xDays: {\n    one: {\n      default: '1 நாள்',\n      in: '1 நாளில்',\n      ago: '1 நாள் முன்பு'\n    },\n    other: {\n      default: '{{count}} நாட்கள்',\n      in: '{{count}} நாட்களில்',\n      ago: '{{count}} நாட்களுக்கு முன்பு'\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      default: 'சுமார் 1 வாரம்',\n      in: 'சுமார் 1 வாரத்தில்',\n      ago: 'சுமார் 1 வாரம் முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} வாரங்கள்',\n      in: 'சுமார் {{count}} வாரங்களில்',\n      ago: 'சுமார் {{count}} வாரங்களுக்கு முன்பு'\n    }\n  },\n  xWeeks: {\n    one: {\n      default: '1 வாரம்',\n      in: '1 வாரத்தில்',\n      ago: '1 வாரம் முன்பு'\n    },\n    other: {\n      default: '{{count}} வாரங்கள்',\n      in: '{{count}} வாரங்களில்',\n      ago: '{{count}} வாரங்களுக்கு முன்பு'\n    }\n  },\n  aboutXMonths: {\n    one: {\n      default: 'சுமார் 1 மாதம்',\n      in: 'சுமார் 1 மாதத்தில்',\n      ago: 'சுமார் 1 மாதத்திற்கு முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} மாதங்கள்',\n      in: 'சுமார் {{count}} மாதங்களில்',\n      ago: 'சுமார் {{count}} மாதங்களுக்கு முன்பு'\n    }\n  },\n  xMonths: {\n    one: {\n      default: '1 மாதம்',\n      in: '1 மாதத்தில்',\n      ago: '1 மாதம் முன்பு'\n    },\n    other: {\n      default: '{{count}} மாதங்கள்',\n      in: '{{count}} மாதங்களில்',\n      ago: '{{count}} மாதங்களுக்கு முன்பு'\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: 'சுமார் 1 வருடம்',\n      in: 'சுமார் 1 ஆண்டில்',\n      ago: 'சுமார் 1 வருடம் முன்பு'\n    },\n    other: {\n      default: 'சுமார் {{count}} ஆண்டுகள்',\n      in: 'சுமார் {{count}} ஆண்டுகளில்',\n      ago: 'சுமார் {{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  },\n  xYears: {\n    one: {\n      default: '1 வருடம்',\n      in: '1 ஆண்டில்',\n      ago: '1 வருடம் முன்பு'\n    },\n    other: {\n      default: '{{count}} ஆண்டுகள்',\n      in: '{{count}} ஆண்டுகளில்',\n      ago: '{{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  },\n  overXYears: {\n    one: {\n      default: '1 வருடத்திற்கு மேல்',\n      in: '1 வருடத்திற்கும் மேலாக',\n      ago: '1 வருடம் முன்பு'\n    },\n    other: {\n      default: '{{count}} ஆண்டுகளுக்கும் மேலாக',\n      in: '{{count}} ஆண்டுகளில்',\n      ago: '{{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  },\n  almostXYears: {\n    one: {\n      default: 'கிட்டத்தட்ட 1 வருடம்',\n      in: 'கிட்டத்தட்ட 1 ஆண்டில்',\n      ago: 'கிட்டத்தட்ட 1 வருடம் முன்பு'\n    },\n    other: {\n      default: 'கிட்டத்தட்ட {{count}} ஆண்டுகள்',\n      in: 'கிட்டத்தட்ட {{count}} ஆண்டுகளில்',\n      ago: 'கிட்டத்தட்ட {{count}} ஆண்டுகளுக்கு முன்பு'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tense = options !== null && options !== void 0 && options.addSuffix ? options.comparison && options.comparison > 0 ? 'in' : 'ago' : 'default';\n  var tokenValue = formatDistanceLocale[token];\n  if (!isPluralType(tokenValue)) return tokenValue[tense];\n  if (count === 1) {\n    return tokenValue.one[tense];\n  } else {\n    return tokenValue.other[tense].replace('{{count}}', String(count));\n  }\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACC,GAAG,KAAKC,SAAS;AAC9B;AACA,IAAIC,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBH,GAAG,EAAE;MACHI,OAAO,EAAE,wBAAwB;MACjCC,EAAE,EAAE,kBAAkB;MACtBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,iCAAiC;MAC1CC,EAAE,EAAE,2BAA2B;MAC/BC,GAAG,EAAE;IACP;EACF,CAAC;EACDE,QAAQ,EAAE;IACRR,GAAG,EAAE;MACHI,OAAO,EAAE,UAAU;MACnBC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE;IACP;EACF,CAAC;EACDG,WAAW,EAAE;IACXL,OAAO,EAAE,aAAa;IACtBC,EAAE,EAAE,iBAAiB;IACrBC,GAAG,EAAE;EACP,CAAC;EACDI,gBAAgB,EAAE;IAChBV,GAAG,EAAE;MACHI,OAAO,EAAE,6BAA6B;MACtCC,EAAE,EAAE,qBAAqB;MACzBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,oCAAoC;MAC7CC,EAAE,EAAE,4BAA4B;MAChCC,GAAG,EAAE;IACP;EACF,CAAC;EACDK,QAAQ,EAAE;IACRX,GAAG,EAAE;MACHI,OAAO,EAAE,WAAW;MACpBC,EAAE,EAAE,eAAe;MACnBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,sBAAsB;MAC/BC,EAAE,EAAE,wBAAwB;MAC5BC,GAAG,EAAE;IACP;EACF,CAAC;EACDM,WAAW,EAAE;IACXZ,GAAG,EAAE;MACHI,OAAO,EAAE,oBAAoB;MAC7BC,EAAE,EAAE,wBAAwB;MAC5BC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,4BAA4B;MACrCC,EAAE,EAAE,yCAAyC;MAC7CC,GAAG,EAAE;IACP;EACF,CAAC;EACDO,MAAM,EAAE;IACNb,GAAG,EAAE;MACHI,OAAO,EAAE,aAAa;MACtBC,EAAE,EAAE,iBAAiB;MACrBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,EAAE,EAAE,yBAAyB;MAC7BC,GAAG,EAAE;IACP;EACF,CAAC;EACDQ,KAAK,EAAE;IACLd,GAAG,EAAE;MACHI,OAAO,EAAE,QAAQ;MACjBC,EAAE,EAAE,UAAU;MACdC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,mBAAmB;MAC5BC,EAAE,EAAE,qBAAqB;MACzBC,GAAG,EAAE;IACP;EACF,CAAC;EACDS,WAAW,EAAE;IACXf,GAAG,EAAE;MACHI,OAAO,EAAE,gBAAgB;MACzBC,EAAE,EAAE,oBAAoB;MACxBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,2BAA2B;MACpCC,EAAE,EAAE,6BAA6B;MACjCC,GAAG,EAAE;IACP;EACF,CAAC;EACDU,MAAM,EAAE;IACNhB,GAAG,EAAE;MACHI,OAAO,EAAE,SAAS;MAClBC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,oBAAoB;MAC7BC,EAAE,EAAE,sBAAsB;MAC1BC,GAAG,EAAE;IACP;EACF,CAAC;EACDW,YAAY,EAAE;IACZjB,GAAG,EAAE;MACHI,OAAO,EAAE,gBAAgB;MACzBC,EAAE,EAAE,oBAAoB;MACxBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,2BAA2B;MACpCC,EAAE,EAAE,6BAA6B;MACjCC,GAAG,EAAE;IACP;EACF,CAAC;EACDY,OAAO,EAAE;IACPlB,GAAG,EAAE;MACHI,OAAO,EAAE,SAAS;MAClBC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,oBAAoB;MAC7BC,EAAE,EAAE,sBAAsB;MAC1BC,GAAG,EAAE;IACP;EACF,CAAC;EACDa,WAAW,EAAE;IACXnB,GAAG,EAAE;MACHI,OAAO,EAAE,iBAAiB;MAC1BC,EAAE,EAAE,kBAAkB;MACtBC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,2BAA2B;MACpCC,EAAE,EAAE,6BAA6B;MACjCC,GAAG,EAAE;IACP;EACF,CAAC;EACDc,MAAM,EAAE;IACNpB,GAAG,EAAE;MACHI,OAAO,EAAE,UAAU;MACnBC,EAAE,EAAE,WAAW;MACfC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,oBAAoB;MAC7BC,EAAE,EAAE,sBAAsB;MAC1BC,GAAG,EAAE;IACP;EACF,CAAC;EACDe,UAAU,EAAE;IACVrB,GAAG,EAAE;MACHI,OAAO,EAAE,qBAAqB;MAC9BC,EAAE,EAAE,wBAAwB;MAC5BC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,gCAAgC;MACzCC,EAAE,EAAE,sBAAsB;MAC1BC,GAAG,EAAE;IACP;EACF,CAAC;EACDgB,YAAY,EAAE;IACZtB,GAAG,EAAE;MACHI,OAAO,EAAE,sBAAsB;MAC/BC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,gCAAgC;MACzCC,EAAE,EAAE,kCAAkC;MACtCC,GAAG,EAAE;IACP;EACF;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,KAAK,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,UAAU,IAAIH,OAAO,CAACG,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,SAAS;EACjJ,IAAIC,UAAU,GAAG5B,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAI,CAAC1B,YAAY,CAACgC,UAAU,CAAC,EAAE,OAAOA,UAAU,CAACH,KAAK,CAAC;EACvD,IAAIF,KAAK,KAAK,CAAC,EAAE;IACf,OAAOK,UAAU,CAAC9B,GAAG,CAAC2B,KAAK,CAAC;EAC9B,CAAC,MAAM;IACL,OAAOG,UAAU,CAACvB,KAAK,CAACoB,KAAK,CAAC,CAACI,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EACpE;AACF,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}