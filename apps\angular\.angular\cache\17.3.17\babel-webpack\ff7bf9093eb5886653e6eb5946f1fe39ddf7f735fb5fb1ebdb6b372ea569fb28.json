{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  if (i === 2 && v === 0) return 2;\n  if (v === 0 && !(n >= 0 && n <= 10) && n % 10 === 0) return 4;\n  return 5;\n}\nexport default [\"he\", [[\"לפנה״צ\", \"אחה״צ\"], u, u], [[\"לפנה״צ\", \"אחה״צ\"], [\"AM\", \"PM\"], u], [[\"א׳\", \"ב׳\", \"ג׳\", \"ד׳\", \"ה׳\", \"ו׳\", \"ש׳\"], [\"יום א׳\", \"יום ב׳\", \"יום ג׳\", \"יום ד׳\", \"יום ה׳\", \"יום ו׳\", \"שבת\"], [\"יום ראשון\", \"יום שני\", \"יום שלישי\", \"יום רביעי\", \"יום חמישי\", \"יום שישי\", \"יום שבת\"], [\"א׳\", \"ב׳\", \"ג׳\", \"ד׳\", \"ה׳\", \"ו׳\", \"ש׳\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ינו׳\", \"פבר׳\", \"מרץ\", \"אפר׳\", \"מאי\", \"יוני\", \"יולי\", \"אוג׳\", \"ספט׳\", \"אוק׳\", \"נוב׳\", \"דצמ׳\"], [\"ינואר\", \"פברואר\", \"מרץ\", \"אפריל\", \"מאי\", \"יוני\", \"יולי\", \"אוגוסט\", \"ספטמבר\", \"אוקטובר\", \"נובמבר\", \"דצמבר\"]], u, [[\"לפני\", \"אחריי\"], [\"לפנה״ס\", \"לספירה\"], [\"לפני הספירה\", \"לספירה\"]], 0, [5, 6], [\"d.M.y\", \"d בMMM y\", \"d בMMMM y\", \"EEEE, d בMMMM y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} בשעה {0}\", u], [\".\", \",\", \";\", \"%\", \"‎+\", \"‎-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"‏#,##0.00 ¤;‏-#,##0.00 ¤\", \"#E0\"], \"ILS\", \"₪\", \"שקל חדש\", {\n  \"BYN\": [u, \"р\"],\n  \"CNY\": [\"‎CN¥‎\", \"¥\"],\n  \"ILP\": [\"ל״י\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"]\n}, \"rtl\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/he.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    if (i === 2 && v === 0)\n        return 2;\n    if (v === 0 && (!(n >= 0 && n <= 10) && n % 10 === 0))\n        return 4;\n    return 5;\n}\nexport default [\"he\", [[\"לפנה״צ\", \"אחה״צ\"], u, u], [[\"לפנה״צ\", \"אחה״צ\"], [\"AM\", \"PM\"], u], [[\"א׳\", \"ב׳\", \"ג׳\", \"ד׳\", \"ה׳\", \"ו׳\", \"ש׳\"], [\"יום א׳\", \"יום ב׳\", \"יום ג׳\", \"יום ד׳\", \"יום ה׳\", \"יום ו׳\", \"שבת\"], [\"יום ראשון\", \"יום שני\", \"יום שלישי\", \"יום רביעי\", \"יום חמישי\", \"יום שישי\", \"יום שבת\"], [\"א׳\", \"ב׳\", \"ג׳\", \"ד׳\", \"ה׳\", \"ו׳\", \"ש׳\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ינו׳\", \"פבר׳\", \"מרץ\", \"אפר׳\", \"מאי\", \"יוני\", \"יולי\", \"אוג׳\", \"ספט׳\", \"אוק׳\", \"נוב׳\", \"דצמ׳\"], [\"ינואר\", \"פברואר\", \"מרץ\", \"אפריל\", \"מאי\", \"יוני\", \"יולי\", \"אוגוסט\", \"ספטמבר\", \"אוקטובר\", \"נובמבר\", \"דצמבר\"]], u, [[\"לפני\", \"אחריי\"], [\"לפנה״ס\", \"לספירה\"], [\"לפני הספירה\", \"לספירה\"]], 0, [5, 6], [\"d.M.y\", \"d בMMM y\", \"d בMMMM y\", \"EEEE, d בMMMM y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} בשעה {0}\", u], [\".\", \",\", \";\", \"%\", \"‎+\", \"‎-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"‏#,##0.00 ¤;‏-#,##0.00 ¤\", \"#E0\"], \"ILS\", \"₪\", \"שקל חדש\", { \"BYN\": [u, \"р\"], \"CNY\": [\"‎CN¥‎\", \"¥\"], \"ILP\": [\"ל״י\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"] }, \"rtl\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;EAChG,IAAIP,CAAC,KAAK,CAAC,IAAII,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,IAAIJ,CAAC,KAAK,CAAC,IAAII,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,IAAK,EAAEL,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,CAAC,IAAIA,CAAC,GAAG,EAAE,KAAK,CAAE,EACjD,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,cAAc,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,0BAA0B,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}