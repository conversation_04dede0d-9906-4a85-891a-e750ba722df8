{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"os\", [[\"AM\", \"PM\"], u, [\"ӕмбисбоны размӕ\", \"ӕмбисбоны фӕстӕ\"]], [[\"AM\", \"PM\"], u, u], [[\"Х\", \"К\", \"Д\", \"Ӕ\", \"Ц\", \"М\", \"С\"], [\"хцб\", \"крс\", \"дцг\", \"ӕрт\", \"цпр\", \"мрб\", \"сбт\"], [\"хуыцаубон\", \"къуырисӕр\", \"дыццӕг\", \"ӕртыццӕг\", \"цыппӕрӕм\", \"майрӕмбон\", \"сабат\"], [\"хцб\", \"крс\", \"дцг\", \"ӕрт\", \"цпр\", \"мрб\", \"сбт\"]], [[\"Х\", \"К\", \"Д\", \"Ӕ\", \"Ц\", \"М\", \"С\"], [\"Хцб\", \"Крс\", \"Дцг\", \"Ӕрт\", \"Цпр\", \"Мрб\", \"Сбт\"], [\"Хуыцаубон\", \"Къуырисӕр\", \"Дыццӕг\", \"Ӕртыццӕг\", \"Цыппӕрӕм\", \"Майрӕмбон\", \"Сабат\"], [\"хцб\", \"крс\", \"дцг\", \"ӕрт\", \"цпр\", \"мрб\", \"сбт\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"янв.\", \"фев.\", \"мар.\", \"апр.\", \"майы\", \"июны\", \"июлы\", \"авг.\", \"сен.\", \"окт.\", \"ноя.\", \"дек.\"], [\"январы\", \"февралы\", \"мартъийы\", \"апрелы\", \"майы\", \"июны\", \"июлы\", \"августы\", \"сентябры\", \"октябры\", \"ноябры\", \"декабры\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"Янв.\", \"Февр.\", \"Март.\", \"Апр.\", \"Май\", \"Июнь\", \"Июль\", \"Авг.\", \"Сент.\", \"Окт.\", \"Нояб.\", \"Дек.\"], [\"Январь\", \"Февраль\", \"Мартъи\", \"Апрель\", \"Май\", \"Июнь\", \"Июль\", \"Август\", \"Сентябрь\", \"Октябрь\", \"Ноябрь\", \"Декабрь\"]], [[\"н.д.а.\", \"н.д.\"], u, u], 1, [6, 0], [\"dd.MM.yy\", \"dd MMM y 'аз'\", \"d MMMM, y 'аз'\", \"EEEE, d MMMM, y 'аз'\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"НН\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"GEL\", \"₾\", \"Лар\", {\n  \"GEL\": [\"₾\"],\n  \"JPY\": [\"JP¥\", \"¥\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/os.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"os\", [[\"AM\", \"PM\"], u, [\"ӕмбисбоны размӕ\", \"ӕмбисбоны фӕстӕ\"]], [[\"AM\", \"PM\"], u, u], [[\"Х\", \"К\", \"Д\", \"Ӕ\", \"Ц\", \"М\", \"С\"], [\"хцб\", \"крс\", \"дцг\", \"ӕрт\", \"цпр\", \"мрб\", \"сбт\"], [\"хуыцаубон\", \"къуырисӕр\", \"дыццӕг\", \"ӕртыццӕг\", \"цыппӕрӕм\", \"майрӕмбон\", \"сабат\"], [\"хцб\", \"крс\", \"дцг\", \"ӕрт\", \"цпр\", \"мрб\", \"сбт\"]], [[\"Х\", \"К\", \"Д\", \"Ӕ\", \"Ц\", \"М\", \"С\"], [\"Хцб\", \"Крс\", \"Дцг\", \"Ӕрт\", \"Цпр\", \"Мрб\", \"Сбт\"], [\"Хуыцаубон\", \"Къуырисӕр\", \"Дыццӕг\", \"Ӕртыццӕг\", \"Цыппӕрӕм\", \"Майрӕмбон\", \"Сабат\"], [\"хцб\", \"крс\", \"дцг\", \"ӕрт\", \"цпр\", \"мрб\", \"сбт\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"янв.\", \"фев.\", \"мар.\", \"апр.\", \"майы\", \"июны\", \"июлы\", \"авг.\", \"сен.\", \"окт.\", \"ноя.\", \"дек.\"], [\"январы\", \"февралы\", \"мартъийы\", \"апрелы\", \"майы\", \"июны\", \"июлы\", \"августы\", \"сентябры\", \"октябры\", \"ноябры\", \"декабры\"]], [[\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"], [\"Янв.\", \"Февр.\", \"Март.\", \"Апр.\", \"Май\", \"Июнь\", \"Июль\", \"Авг.\", \"Сент.\", \"Окт.\", \"Нояб.\", \"Дек.\"], [\"Январь\", \"Февраль\", \"Мартъи\", \"Апрель\", \"Май\", \"Июнь\", \"Июль\", \"Август\", \"Сентябрь\", \"Октябрь\", \"Ноябрь\", \"Декабрь\"]], [[\"н.д.а.\", \"н.д.\"], u, u], 1, [6, 0], [\"dd.MM.yy\", \"dd MMM y 'аз'\", \"d MMMM, y 'аз'\", \"EEEE, d MMMM, y 'аз'\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"НН\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"GEL\", \"₾\", \"Лар\", { \"GEL\": [\"₾\"], \"JPY\": [\"JP¥\", \"¥\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}