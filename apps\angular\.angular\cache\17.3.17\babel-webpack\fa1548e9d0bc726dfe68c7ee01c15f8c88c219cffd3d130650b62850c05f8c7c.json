{"ast": null, "code": "import { numberToLocale } from \"../localize/index.js\";\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'প্রায় ১ সেকেন্ড',\n    other: 'প্রায় {{count}} সেকেন্ড'\n  },\n  xSeconds: {\n    one: '১ সেকেন্ড',\n    other: '{{count}} সেকেন্ড'\n  },\n  halfAMinute: 'আধ মিনিট',\n  lessThanXMinutes: {\n    one: 'প্রায় ১ মিনিট',\n    other: 'প্রায় {{count}} মিনিট'\n  },\n  xMinutes: {\n    one: '১ মিনিট',\n    other: '{{count}} মিনিট'\n  },\n  aboutXHours: {\n    one: 'প্রায় ১ ঘন্টা',\n    other: 'প্রায় {{count}} ঘন্টা'\n  },\n  xHours: {\n    one: '১ ঘন্টা',\n    other: '{{count}} ঘন্টা'\n  },\n  xDays: {\n    one: '১ দিন',\n    other: '{{count}} দিন'\n  },\n  aboutXWeeks: {\n    one: 'প্রায় ১ সপ্তাহ',\n    other: 'প্রায় {{count}} সপ্তাহ'\n  },\n  xWeeks: {\n    one: '১ সপ্তাহ',\n    other: '{{count}} সপ্তাহ'\n  },\n  aboutXMonths: {\n    one: 'প্রায় ১ মাস',\n    other: 'প্রায় {{count}} মাস'\n  },\n  xMonths: {\n    one: '১ মাস',\n    other: '{{count}} মাস'\n  },\n  aboutXYears: {\n    one: 'প্রায় ১ বছর',\n    other: 'প্রায় {{count}} বছর'\n  },\n  xYears: {\n    one: '১ বছর',\n    other: '{{count}} বছর'\n  },\n  overXYears: {\n    one: '১ বছরের বেশি',\n    other: '{{count}} বছরের বেশি'\n  },\n  almostXYears: {\n    one: 'প্রায় ১ বছর',\n    other: 'প্রায় {{count}} বছর'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', numberToLocale(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' এর মধ্যে';\n    } else {\n      return result + ' আগে';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}