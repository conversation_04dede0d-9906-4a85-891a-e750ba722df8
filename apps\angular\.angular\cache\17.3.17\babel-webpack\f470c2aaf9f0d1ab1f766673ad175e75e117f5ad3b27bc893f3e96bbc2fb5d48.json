{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val));\n  if (i === 0 || i === 1) return 1;\n  return 5;\n}\nexport default [\"kab\", [[\"n tufat\", \"n tmeddit\"], u, u], u, [[\"Y\", \"S\", \"K\", \"K\", \"S\", \"S\", \"S\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>ra<PERSON>\", \"Kuẓ\", \"<PERSON>\", \"Sḍ<PERSON>\", \"Say\"], [\"Yanass\", \"Sanass\", \"Kraḍass\", \"Kuẓass\", \"Samass\", \"Sḍisass\", \"Sayass\"], [\"Yan\", \"San\", \"<PERSON>ra<PERSON>\", \"Kuẓ\", \"<PERSON>\", \"<PERSON>ḍ<PERSON>\", \"<PERSON>\"]], u, [[\"Y\", \"F\", \"M\", \"Y\", \"M\", \"Y\", \"Y\", \"Ɣ\", \"C\", \"T\", \"N\", \"D\"], [\"Yen\", \"Fur\", \"Meɣ\", \"Yeb\", \"May\", \"<PERSON>\", \"Yul\", \"Ɣuc\", \"Cte\", \"Tub\", \"Nun\", \"Duǧ\"], [\"Yennayer\", \"Fuṛar\", \"Meɣres\", \"Yebrir\", \"Mayyu\", \"Yunyu\", \"Yulyu\", \"Ɣuct\", \"Ctembeṛ\", \"Tubeṛ\", \"Nunembeṛ\", \"Duǧembeṛ\"]], u, [[\"snd. T.Ɛ\", \"sld. T.Ɛ\"], u, [\"send talalit n Ɛisa\", \"seld talalit n Ɛisa\"]], 6, [5, 6], [\"d/M/y\", \"d MMM, y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00¤\", \"#E0\"], \"DZD\", \"DA\", \"Adinar Azzayri\", {\n  \"DZD\": [\"DA\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/kab.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val));\n    if (i === 0 || i === 1)\n        return 1;\n    return 5;\n}\nexport default [\"kab\", [[\"n tufat\", \"n tmeddit\"], u, u], u, [[\"Y\", \"S\", \"K\", \"K\", \"S\", \"S\", \"S\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>ra<PERSON>\", \"Kuẓ\", \"<PERSON>\", \"Sḍ<PERSON>\", \"Say\"], [\"Yanass\", \"Sanass\", \"Kraḍass\", \"Kuẓass\", \"Samass\", \"Sḍisass\", \"Sayass\"], [\"Yan\", \"San\", \"<PERSON>ra<PERSON>\", \"Kuẓ\", \"<PERSON>\", \"<PERSON>ḍ<PERSON>\", \"<PERSON>\"]], u, [[\"Y\", \"F\", \"M\", \"Y\", \"M\", \"Y\", \"Y\", \"Ɣ\", \"C\", \"T\", \"N\", \"D\"], [\"Yen\", \"Fur\", \"Meɣ\", \"Yeb\", \"May\", \"<PERSON>\", \"Yul\", \"Ɣuc\", \"Cte\", \"Tub\", \"Nun\", \"Duǧ\"], [\"Yennayer\", \"Fuṛar\", \"Meɣres\", \"Yebrir\", \"Mayyu\", \"Yunyu\", \"Yulyu\", \"Ɣuct\", \"Ctembeṛ\", \"Tubeṛ\", \"Nunembeṛ\", \"Duǧembeṛ\"]], u, [[\"snd. T.Ɛ\", \"sld. T.Ɛ\"], u, [\"send talalit n Ɛisa\", \"seld talalit n Ɛisa\"]], 6, [5, 6], [\"d/M/y\", \"d MMM, y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00¤\", \"#E0\"], \"DZD\", \"DA\", \"Adinar Azzayri\", { \"DZD\": [\"DA\"], \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;EAC5C,IAAIE,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,EAAEL,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEA,CAAC,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,gBAAgB,EAAE;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}