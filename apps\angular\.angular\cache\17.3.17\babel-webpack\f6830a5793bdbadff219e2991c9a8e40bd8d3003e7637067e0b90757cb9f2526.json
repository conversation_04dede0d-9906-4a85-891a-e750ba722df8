{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"lb\", [[\"mo.\", \"nomë.\"], [\"moies\", \"nomëttes\"], u], [[\"moies\", \"nomëttes\"], u, u], [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"Son.\", \"Méi.\", \"Dën.\", \"Mët.\", \"Don.\", \"Fre.\", \"Sam.\"], [\"Sonndeg\", \"Méindeg\", \"Dënschdeg\", \"Mëttwoch\", \"Donneschdeg\", \"Freideg\", \"Samschdeg\"], [\"So.\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>.\", \"<PERSON><PERSON>.\", \"<PERSON><PERSON>\", \"<PERSON>.\", \"<PERSON>.\"]], [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"Son\", \"<PERSON><PERSON>i\", \"<PERSON>ën\", \"<PERSON>ët\", \"<PERSON>\", \"Fre\", \"Sam\"], [\"Sonndeg\", \"Méindeg\", \"Dënschdeg\", \"Mëttwoch\", \"<PERSON>neschdeg\", \"Freideg\", \"Samschdeg\"], [\"So.\", \"M<PERSON>.\", \"Dë.\", \"Më.\", \"Do.\", \"Fr.\", \"Sa.\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan.\", \"Feb.\", \"Mäe.\", \"Abr.\", \"Mee\", \"Juni\", \"Juli\", \"Aug.\", \"Sep.\", \"Okt.\", \"Nov.\", \"Dez.\"], [\"Januar\", \"Februar\", \"Mäerz\", \"Abrëll\", \"Mee\", \"Juni\", \"Juli\", \"August\", \"September\", \"Oktober\", \"November\", \"Dezember\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mäe\", \"Abr\", \"Mee\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Okt\", \"Nov\", \"Dez\"], [\"Januar\", \"Februar\", \"Mäerz\", \"Abrëll\", \"Mee\", \"Juni\", \"Juli\", \"August\", \"September\", \"Oktober\", \"November\", \"Dezember\"]], [[\"v. Chr.\", \"n. Chr.\"], u, u], 1, [6, 0], [\"dd.MM.yy\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Euro\", {\n  \"ATS\": [\"öS\"],\n  \"AUD\": [\"AU$\", \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/lb.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"lb\", [[\"mo.\", \"nomë.\"], [\"moies\", \"nomëttes\"], u], [[\"moies\", \"nomëttes\"], u, u], [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"Son.\", \"Méi.\", \"Dën.\", \"Mët.\", \"Don.\", \"Fre.\", \"Sam.\"], [\"Sonndeg\", \"Méindeg\", \"Dënschdeg\", \"Mëttwoch\", \"Donneschdeg\", \"Freideg\", \"Samschdeg\"], [\"So.\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>.\", \"<PERSON><PERSON>.\", \"<PERSON><PERSON>\", \"<PERSON>.\", \"<PERSON>.\"]], [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"Son\", \"<PERSON><PERSON>i\", \"<PERSON>ën\", \"<PERSON>ët\", \"<PERSON>\", \"Fre\", \"Sam\"], [\"Sonndeg\", \"Méindeg\", \"Dënschdeg\", \"Mëttwoch\", \"<PERSON>neschdeg\", \"Freideg\", \"Samschdeg\"], [\"So.\", \"M<PERSON>.\", \"Dë.\", \"Më.\", \"Do.\", \"Fr.\", \"Sa.\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan.\", \"Feb.\", \"Mäe.\", \"Abr.\", \"Mee\", \"Juni\", \"Juli\", \"Aug.\", \"Sep.\", \"Okt.\", \"Nov.\", \"Dez.\"], [\"Januar\", \"Februar\", \"Mäerz\", \"Abrëll\", \"Mee\", \"Juni\", \"Juli\", \"August\", \"September\", \"Oktober\", \"November\", \"Dezember\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mäe\", \"Abr\", \"Mee\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Okt\", \"Nov\", \"Dez\"], [\"Januar\", \"Februar\", \"Mäerz\", \"Abrëll\", \"Mee\", \"Juni\", \"Juli\", \"August\", \"September\", \"Oktober\", \"November\", \"Dezember\"]], [[\"v. Chr.\", \"n. Chr.\"], u, u], 1, [6, 0], [\"dd.MM.yy\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Euro\", { \"ATS\": [\"öS\"], \"AUD\": [\"AU$\", \"$\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}