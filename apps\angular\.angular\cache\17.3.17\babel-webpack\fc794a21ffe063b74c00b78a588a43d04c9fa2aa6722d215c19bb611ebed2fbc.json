{"ast": null, "code": "import { NEVER, throwError } from 'rxjs';\nimport { retry, catchError } from 'rxjs/operators';\nimport { PathLocationStrategy } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/shared/services/sweetalert.service\";\nexport class GlobalHttpErrorInterceptor {\n  constructor(injector, router, sweetAlert) {\n    this.injector = injector;\n    this.router = router;\n    this.sweetAlert = sweetAlert;\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(retry(1), catchError(error => {\n      if (error.error instanceof ErrorEvent) {\n        /** client-side error */\n        console.error('GlobalHttpErrorInterceptor ERROR: ', error);\n      } else {\n        /** server-side error */\n        const name = error.name || null;\n        const time = new Date().getTime();\n        const status = error.status || 0;\n        let message = error.message || JSON.stringify(error);\n        let url = '';\n        try {\n          const location = this.injector.get(PathLocationStrategy);\n          url = location.path();\n        } catch {\n          return throwError(() => error);\n        }\n        if (!navigator.onLine) {\n          message = 'Internet disconnected.';\n        }\n        console.log('.....................................');\n        console.error('GlobalHttpErrorInterceptor ERROR: ', {\n          name,\n          time,\n          status,\n          message,\n          url\n        });\n        console.log('.....................................');\n        if (status === 401) {\n          /** An unauthorized request was made, catch-all as existing RouteGuards should be catching these first */\n          this.router.navigate(['/home']);\n          return NEVER;\n        }\n        //Comment out below line to fix bug 2132 - Perhaps this should fix other places where below dialog shows\n        //this.sweetAlert.fireDialog({name: String(status), source:\"global-interceptor\", type:\"HttpError\"});\n        return throwError(() => error);\n      }\n      return throwError(() => new Error());\n    }));\n  }\n  static {\n    this.ɵfac = function GlobalHttpErrorInterceptor_Factory(t) {\n      return new (t || GlobalHttpErrorInterceptor)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.SweetAlertService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GlobalHttpErrorInterceptor,\n      factory: GlobalHttpErrorInterceptor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["NEVER", "throwError", "retry", "catchError", "PathLocationStrategy", "GlobalHttpErrorInterceptor", "constructor", "injector", "router", "<PERSON><PERSON><PERSON><PERSON>", "intercept", "request", "next", "handle", "pipe", "error", "ErrorEvent", "console", "name", "time", "Date", "getTime", "status", "message", "JSON", "stringify", "url", "location", "get", "path", "navigator", "onLine", "log", "navigate", "Error", "i0", "ɵɵinject", "Injector", "i1", "Router", "i2", "SweetAlertService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\errors\\interceptors\\global-http-error.interceptor.ts"], "sourcesContent": ["\r\nimport { Http<PERSON>vent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { NEVER, Observable, ObservableInput, throwError } from 'rxjs';\r\nimport { retry, catchError } from 'rxjs/operators';\r\n\r\nimport { Injector, Injectable } from '@angular/core';\r\nimport { LocationStrategy, PathLocationStrategy } from '@angular/common';\r\n\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\n\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class GlobalHttpErrorInterceptor implements HttpInterceptor {\r\n\r\n    constructor(\r\n        private injector: Injector,\r\n        private router: Router,\r\n        private sweetAlert: SweetAlertService) { }\r\n\r\n\r\n\r\n    intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {\r\n\r\n        return next.handle(request).pipe(\r\n\r\n            retry(1),\r\n\r\n            catchError((error: HttpErrorResponse): ObservableInput<never> => {\r\n\r\n\r\n                if (error.error instanceof ErrorEvent) {\r\n\r\n                    /** client-side error */\r\n\r\n                    console.error('GlobalHttpErrorInterceptor ERROR: ', error);\r\n\r\n                } else {\r\n\r\n                    /** server-side error */\r\n\r\n                    const name = error.name || null;\r\n                    const time = new Date().getTime();\r\n                    const status = error.status || 0;\r\n                    let message = error.message || JSON.stringify(error);\r\n\r\n                    let url = '';\r\n                    try {\r\n\r\n                        const location = this.injector.get<LocationStrategy>(PathLocationStrategy);\r\n                        url = location.path();\r\n\r\n                    } catch {\r\n\r\n                        return throwError(() => error);\r\n                    }\r\n\r\n                    if (!navigator.onLine) {\r\n\r\n                        message = 'Internet disconnected.';\r\n                    }\r\n\r\n                    console.log('.....................................');\r\n                    console.error('GlobalHttpErrorInterceptor ERROR: ', { name, time, status, message, url });\r\n                    console.log('.....................................');\r\n\r\n                    if (status === 401) {\r\n\r\n                        /** An unauthorized request was made, catch-all as existing RouteGuards should be catching these first */\r\n                        this.router.navigate(['/home']);\r\n                        return NEVER;\r\n                    }\r\n                    \r\n                    //Comment out below line to fix bug 2132 - Perhaps this should fix other places where below dialog shows\r\n                    //this.sweetAlert.fireDialog({name: String(status), source:\"global-interceptor\", type:\"HttpError\"});\r\n\r\n                    return throwError(() => error);\r\n                }\r\n\r\n                return throwError(() => new Error());\r\n            })\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAIA,SAASA,KAAK,EAA+BC,UAAU,QAAQ,MAAM;AACrE,SAASC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAGlD,SAA2BC,oBAAoB,QAAQ,iBAAiB;;;;AAMxE,OAAM,MAAOC,0BAA0B;EAEnCC,YACYC,QAAkB,EAClBC,MAAc,EACdC,UAA6B;IAF7B,KAAAF,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;EAAuB;EAI7CC,SAASA,CAACC,OAA6B,EAAEC,IAAiB;IAEtD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAE5BZ,KAAK,CAAC,CAAC,CAAC,EAERC,UAAU,CAAEY,KAAwB,IAA4B;MAG5D,IAAIA,KAAK,CAACA,KAAK,YAAYC,UAAU,EAAE;QAEnC;QAEAC,OAAO,CAACF,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAE9D,CAAC,MAAM;QAEH;QAEA,MAAMG,IAAI,GAAGH,KAAK,CAACG,IAAI,IAAI,IAAI;QAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;QACjC,MAAMC,MAAM,GAAGP,KAAK,CAACO,MAAM,IAAI,CAAC;QAChC,IAAIC,OAAO,GAAGR,KAAK,CAACQ,OAAO,IAAIC,IAAI,CAACC,SAAS,CAACV,KAAK,CAAC;QAEpD,IAAIW,GAAG,GAAG,EAAE;QACZ,IAAI;UAEA,MAAMC,QAAQ,GAAG,IAAI,CAACpB,QAAQ,CAACqB,GAAG,CAAmBxB,oBAAoB,CAAC;UAC1EsB,GAAG,GAAGC,QAAQ,CAACE,IAAI,EAAE;QAEzB,CAAC,CAAC,MAAM;UAEJ,OAAO5B,UAAU,CAAC,MAAMc,KAAK,CAAC;QAClC;QAEA,IAAI,CAACe,SAAS,CAACC,MAAM,EAAE;UAEnBR,OAAO,GAAG,wBAAwB;QACtC;QAEAN,OAAO,CAACe,GAAG,CAAC,uCAAuC,CAAC;QACpDf,OAAO,CAACF,KAAK,CAAC,oCAAoC,EAAE;UAAEG,IAAI;UAAEC,IAAI;UAAEG,MAAM;UAAEC,OAAO;UAAEG;QAAG,CAAE,CAAC;QACzFT,OAAO,CAACe,GAAG,CAAC,uCAAuC,CAAC;QAEpD,IAAIV,MAAM,KAAK,GAAG,EAAE;UAEhB;UACA,IAAI,CAACd,MAAM,CAACyB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;UAC/B,OAAOjC,KAAK;QAChB;QAEA;QACA;QAEA,OAAOC,UAAU,CAAC,MAAMc,KAAK,CAAC;MAClC;MAEA,OAAOd,UAAU,CAAC,MAAM,IAAIiC,KAAK,EAAE,CAAC;IACxC,CAAC,CAAC,CACL;EACL;;;uBArES7B,0BAA0B,EAAA8B,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,MAAA,GAAAJ,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;aAA1BpC,0BAA0B;MAAAqC,OAAA,EAA1BrC,0BAA0B,CAAAsC,IAAA;MAAAC,UAAA,EADb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}