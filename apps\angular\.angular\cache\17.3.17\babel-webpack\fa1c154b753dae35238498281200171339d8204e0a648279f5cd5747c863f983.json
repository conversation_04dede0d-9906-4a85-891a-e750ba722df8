{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport let RedFlagService = /*#__PURE__*/(() => {\n  class RedFlagService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'EconomicSubstanceService';\n      this.getAllRedFlagSettings = config => this.restService.request({\n        method: 'GET',\n        url: '/api/ESService/RedFlag/GetAllRedFlagSettings'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getRedFlagEventsDescriptions = (declarationId, config) => this.restService.request({\n        method: 'GET',\n        url: '/api/ESService/RedFlag/RedFlagEvents',\n        params: {\n          declarationId\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.getRedFlags = (request, config) => this.restService.request({\n        method: 'GET',\n        url: '/api/ESService/RedFlag',\n        params: {\n          isAscending: request.isAscending,\n          sorting: request.sorting,\n          skipCount: request.skipCount,\n          maxResultCount: request.maxResultCount\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.processRedFlagsOnAllDeclarationsByForced = (forced, config) => this.restService.request({\n        method: 'GET',\n        url: '/api/ESService/RedFlag/ProcessRedFlagsOnAllDeclarations',\n        params: {\n          forced\n        }\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n      this.updateRedFlag = (redFlagDto, config) => this.restService.request({\n        method: 'PUT',\n        url: '/api/ESService/RedFlag',\n        body: redFlagDto\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n    }\n    static {\n      this.ɵfac = function RedFlagService_Factory(t) {\n        return new (t || RedFlagService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RedFlagService,\n        factory: RedFlagService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RedFlagService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}