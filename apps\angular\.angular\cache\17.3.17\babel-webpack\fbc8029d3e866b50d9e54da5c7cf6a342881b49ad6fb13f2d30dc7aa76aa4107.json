{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"teo\", [[\"Taparachu\", \"Ebongi\"], u, u], u, [[\"J\", \"B\", \"A\", \"U\", \"U\", \"K\", \"S\"], [\"Jum\", \"Bar\", \"Aar\", \"Uni\", \"Ung\", \"Kan\", \"Sab\"], [\"Naka<PERSON><PERSON>\", \"Nakaebarasa\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>’on\", \"Nakakan<PERSON>\", \"Na<PERSON><PERSON><PERSON>\"], [\"Jum\", \"<PERSON>\", \"A<PERSON>\", \"Uni\", \"Ung\", \"Kan\", \"Sab\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"T\", \"<PERSON>\", \"<PERSON>\"], [\"Rar\", \"Mu<PERSON>\", \"<PERSON><PERSON>\", \"Dun\", \"<PERSON>\", \"Mod\", \"<PERSON>l\", \"<PERSON>ed\", \"<PERSON>k\", \"<PERSON>ib\", \"<PERSON>\", \"<PERSON>o\"], [\"Orara\", \"<PERSON>muk\", \"<PERSON>wamg’\", \"Odung’el\", \"<PERSON>uk\", \"<PERSON>modok’king’ol\", \"<PERSON>jola\", \"<PERSON><PERSON>l\", \"<PERSON>so<PERSON><PERSON>ma\", \"<PERSON>tibar\", \"<PERSON>labor\", \"<PERSON>oo\"]], u, [[\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"], u, [\"Kabla ya Christo\", \"Baada ya Christo\"]], 1, [0, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"UGX\", \"USh\", \"Ango’otol lok’ Uganda\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"UGX\": [\"USh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVvLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29tbW9uL2xvY2FsZXMvdGVvLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILDBDQUEwQztBQUMxQyxNQUFNLENBQUMsR0FBRyxTQUFTLENBQUM7QUFFcEIsU0FBUyxNQUFNLENBQUMsR0FBVztJQUMzQixNQUFNLENBQUMsR0FBRyxHQUFHLENBQUM7SUFFZCxJQUFJLENBQUMsS0FBSyxDQUFDO1FBQ1AsT0FBTyxDQUFDLENBQUM7SUFDYixPQUFPLENBQUMsQ0FBQztBQUNULENBQUM7QUFFRCxlQUFlLENBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxXQUFXLEVBQUMsUUFBUSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxhQUFhLEVBQUMsU0FBUyxFQUFDLFNBQVMsRUFBQyxZQUFZLEVBQUMsVUFBVSxFQUFDLFlBQVksQ0FBQyxFQUFDLENBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLE1BQU0sRUFBQyxTQUFTLEVBQUMsVUFBVSxFQUFDLFFBQVEsRUFBQyxnQkFBZ0IsRUFBQyxPQUFPLEVBQUMsUUFBUSxFQUFDLGFBQWEsRUFBQyxRQUFRLEVBQUMsUUFBUSxFQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsa0JBQWtCLEVBQUMsa0JBQWtCLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLFNBQVMsRUFBQyxTQUFTLEVBQUMsVUFBVSxFQUFDLGdCQUFnQixDQUFDLEVBQUMsQ0FBQyxPQUFPLEVBQUMsVUFBVSxFQUFDLFlBQVksRUFBQyxlQUFlLENBQUMsRUFBQyxDQUFDLFNBQVMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxRQUFRLEVBQUMsV0FBVyxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsdUJBQXVCLEVBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEVBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRISVMgQ09ERSBJUyBHRU5FUkFURUQgLSBETyBOT1QgTU9ESUZZLlxuY29uc3QgdSA9IHVuZGVmaW5lZDtcblxuZnVuY3Rpb24gcGx1cmFsKHZhbDogbnVtYmVyKTogbnVtYmVyIHtcbmNvbnN0IG4gPSB2YWw7XG5cbmlmIChuID09PSAxKVxuICAgIHJldHVybiAxO1xucmV0dXJuIDU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IFtcInRlb1wiLFtbXCJUYXBhcmFjaHVcIixcIkVib25naVwiXSx1LHVdLHUsW1tcIkpcIixcIkJcIixcIkFcIixcIlVcIixcIlVcIixcIktcIixcIlNcIl0sW1wiSnVtXCIsXCJCYXJcIixcIkFhclwiLFwiVW5pXCIsXCJVbmdcIixcIkthblwiLFwiU2FiXCJdLFtcIk5ha2FlanVtYVwiLFwiTmFrYWViYXJhc2FcIixcIk5ha2FhcmVcIixcIk5ha2F1bmlcIixcIk5ha2F1bmfigJlvblwiLFwiTmFrYWthbnlcIixcIk5ha2FzYWJpdGlcIl0sW1wiSnVtXCIsXCJCYXJcIixcIkFhclwiLFwiVW5pXCIsXCJVbmdcIixcIkthblwiLFwiU2FiXCJdXSx1LFtbXCJSXCIsXCJNXCIsXCJLXCIsXCJEXCIsXCJNXCIsXCJNXCIsXCJKXCIsXCJQXCIsXCJTXCIsXCJUXCIsXCJMXCIsXCJQXCJdLFtcIlJhclwiLFwiTXVrXCIsXCJLd2FcIixcIkR1blwiLFwiTWFyXCIsXCJNb2RcIixcIkpvbFwiLFwiUGVkXCIsXCJTb2tcIixcIlRpYlwiLFwiTGFiXCIsXCJQb29cIl0sW1wiT3JhcmFcIixcIk9tdWtcIixcIk9rd2FtZ+KAmVwiLFwiT2R1bmfigJllbFwiLFwiT21hcnVrXCIsXCJPbW9kb2vigJlraW5n4oCZb2xcIixcIk9qb2xhXCIsXCJPcGVkZWxcIixcIk9zb2tvc29rb21hXCIsXCJPdGliYXJcIixcIk9sYWJvclwiLFwiT3Bvb1wiXV0sdSxbW1wiS0tcIixcIkJLXCJdLHUsW1wiS2FibGEgeWEgQ2hyaXN0b1wiLFwiQmFhZGEgeWEgQ2hyaXN0b1wiXV0sMSxbMCwwXSxbXCJkZC9NTS95XCIsXCJkIE1NTSB5XCIsXCJkIE1NTU0geVwiLFwiRUVFRSwgZCBNTU1NIHlcIl0sW1wiSEg6bW1cIixcIkhIOm1tOnNzXCIsXCJISDptbTpzcyB6XCIsXCJISDptbTpzcyB6enp6XCJdLFtcInsxfSB7MH1cIix1LHUsdV0sW1wiLlwiLFwiLFwiLFwiO1wiLFwiJVwiLFwiK1wiLFwiLVwiLFwiRVwiLFwiw5dcIixcIuKAsFwiLFwi4oieXCIsXCJOYU5cIixcIjpcIl0sW1wiIywjIzAuIyMjXCIsXCIjLCMjMCVcIixcIsKkIywjIzAuMDBcIixcIiNFMFwiXSxcIlVHWFwiLFwiVVNoXCIsXCJBbmdv4oCZb3RvbCBsb2vigJkgVWdhbmRhXCIse1wiSlBZXCI6W1wiSlDCpVwiLFwiwqVcIl0sXCJVR1hcIjpbXCJVU2hcIl0sXCJVU0RcIjpbXCJVUyRcIixcIiRcIl19LFwibHRyXCIsIHBsdXJhbF07XG4iXX0=", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}