{"ast": null, "code": "var accusativeWeekdays = ['vas<PERSON>rnap', 'hétfőn', 'kedden', 'szerd<PERSON>', 'cs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'p<PERSON><PERSON><PERSON>', 'szombaton'];\nfunction week(isFuture) {\n  return function (date) {\n    var weekday = accusativeWeekdays[date.getUTCDay()];\n    var prefix = isFuture ? '' : \"'múlt' \";\n    return \"\".concat(prefix, \"'\").concat(weekday, \"' p'-kor'\");\n  };\n}\nvar formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["accusativeWeekdays", "week", "isFuture", "date", "weekday", "getUTCDay", "prefix", "concat", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/hu/_lib/formatRelative/index.js"], "sourcesContent": ["var accusativeWeekdays = ['vas<PERSON>rnap', 'hétfőn', 'kedden', 'szerd<PERSON>', 'cs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'p<PERSON><PERSON><PERSON>', 'szombaton'];\nfunction week(isFuture) {\n  return function (date) {\n    var weekday = accusativeWeekdays[date.getUTCDay()];\n    var prefix = isFuture ? '' : \"'múlt' \";\n    return \"\".concat(prefix, \"'\").concat(weekday, \"' p'-kor'\");\n  };\n}\nvar formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,kBAAkB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,CAAC;AAC5G,SAASC,IAAIA,CAACC,QAAQ,EAAE;EACtB,OAAO,UAAUC,IAAI,EAAE;IACrB,IAAIC,OAAO,GAAGJ,kBAAkB,CAACG,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;IAClD,IAAIC,MAAM,GAAGJ,QAAQ,GAAG,EAAE,GAAG,SAAS;IACtC,OAAO,EAAE,CAACK,MAAM,CAACD,MAAM,EAAE,GAAG,CAAC,CAACC,MAAM,CAACH,OAAO,EAAE,WAAW,CAAC;EAC5D,CAAC;AACH;AACA,IAAII,oBAAoB,GAAG;EACzBC,QAAQ,EAAER,IAAI,CAAC,KAAK,CAAC;EACrBS,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAEZ,IAAI,CAAC,IAAI,CAAC;EACpBa,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEb,IAAI,EAAE;EACxD,IAAIc,MAAM,GAAGT,oBAAoB,CAACQ,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACd,IAAI,CAAC;EACrB;EACA,OAAOc,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}