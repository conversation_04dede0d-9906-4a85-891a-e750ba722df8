{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgZone, Injectable, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport * as i2 from '@ngrx/store';\nimport { ActionsSubject, UPDATE, INIT, INITIAL_STATE, StateObservable, ReducerManagerDispatcher } from '@ngrx/store';\nimport { EMPTY, Observable, of, merge, queueScheduler, ReplaySubject } from 'rxjs';\nimport { share, filter, map, concatMap, timeout, debounceTime, catchError, take, takeUntil, switchMap, skip, observeOn, withLatestFrom, scan } from 'rxjs/operators';\nimport { toSignal } from '@angular/core/rxjs-interop';\nconst PERFORM_ACTION = 'PERFORM_ACTION';\nconst REFRESH = 'REFRESH';\nconst RESET = 'RESET';\nconst ROLLBACK = 'ROLLBACK';\nconst COMMIT = 'COMMIT';\nconst SWEEP = 'SWEEP';\nconst TOGGLE_ACTION = 'TOGGLE_ACTION';\nconst SET_ACTIONS_ACTIVE = 'SET_ACTIONS_ACTIVE';\nconst JUMP_TO_STATE = 'JUMP_TO_STATE';\nconst JUMP_TO_ACTION = 'JUMP_TO_ACTION';\nconst IMPORT_STATE = 'IMPORT_STATE';\nconst LOCK_CHANGES = 'LOCK_CHANGES';\nconst PAUSE_RECORDING = 'PAUSE_RECORDING';\nclass PerformAction {\n  constructor(action, timestamp) {\n    this.action = action;\n    this.timestamp = timestamp;\n    this.type = PERFORM_ACTION;\n    if (typeof action.type === 'undefined') {\n      throw new Error('Actions may not have an undefined \"type\" property. ' + 'Have you misspelled a constant?');\n    }\n  }\n}\nclass Refresh {\n  constructor() {\n    this.type = REFRESH;\n  }\n}\nclass Reset {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = RESET;\n  }\n}\nclass Rollback {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = ROLLBACK;\n  }\n}\nclass Commit {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = COMMIT;\n  }\n}\nclass Sweep {\n  constructor() {\n    this.type = SWEEP;\n  }\n}\nclass ToggleAction {\n  constructor(id) {\n    this.id = id;\n    this.type = TOGGLE_ACTION;\n  }\n}\nclass SetActionsActive {\n  constructor(start, end, active = true) {\n    this.start = start;\n    this.end = end;\n    this.active = active;\n    this.type = SET_ACTIONS_ACTIVE;\n  }\n}\nclass JumpToState {\n  constructor(index) {\n    this.index = index;\n    this.type = JUMP_TO_STATE;\n  }\n}\nclass JumpToAction {\n  constructor(actionId) {\n    this.actionId = actionId;\n    this.type = JUMP_TO_ACTION;\n  }\n}\nclass ImportState {\n  constructor(nextLiftedState) {\n    this.nextLiftedState = nextLiftedState;\n    this.type = IMPORT_STATE;\n  }\n}\nclass LockChanges {\n  constructor(status) {\n    this.status = status;\n    this.type = LOCK_CHANGES;\n  }\n}\nclass PauseRecording {\n  constructor(status) {\n    this.status = status;\n    this.type = PAUSE_RECORDING;\n  }\n}\n\n/**\n * Chrome extension documentation\n * @see https://github.com/reduxjs/redux-devtools/blob/main/extension/docs/API/Arguments.md\n * Firefox extension documentation\n * @see https://github.com/zalmoxisus/redux-devtools-extension/blob/master/docs/API/Arguments.md\n */\nclass StoreDevtoolsConfig {\n  constructor() {\n    /**\n     * Maximum allowed actions to be stored in the history tree (default: `false`)\n     */\n    this.maxAge = false;\n  }\n}\nconst STORE_DEVTOOLS_CONFIG = new InjectionToken('@ngrx/store-devtools Options');\n/**\n * Used to provide a `StoreDevtoolsConfig` for the store-devtools.\n */\nconst INITIAL_OPTIONS = new InjectionToken('@ngrx/store-devtools Initial Config');\nfunction noMonitor() {\n  return null;\n}\nconst DEFAULT_NAME = 'NgRx Store DevTools';\nfunction createConfig(optionsInput) {\n  const DEFAULT_OPTIONS = {\n    maxAge: false,\n    monitor: noMonitor,\n    actionSanitizer: undefined,\n    stateSanitizer: undefined,\n    name: DEFAULT_NAME,\n    serialize: false,\n    logOnly: false,\n    autoPause: false,\n    trace: false,\n    traceLimit: 75,\n    // Add all features explicitly. This prevent buggy behavior for\n    // options like \"lock\" which might otherwise not show up.\n    features: {\n      pause: true,\n      // Start/pause recording of dispatched actions\n      lock: true,\n      // Lock/unlock dispatching actions and side effects\n      persist: true,\n      // Persist states on page reloading\n      export: true,\n      // Export history of actions in a file\n      import: 'custom',\n      // Import history of actions from a file\n      jump: true,\n      // Jump back and forth (time travelling)\n      skip: true,\n      // Skip (cancel) actions\n      reorder: true,\n      // Drag and drop actions in the history list\n      dispatch: true,\n      // Dispatch custom actions or action creators\n      test: true // Generate tests for the selected actions\n    },\n    connectInZone: false\n  };\n  const options = typeof optionsInput === 'function' ? optionsInput() : optionsInput;\n  const logOnly = options.logOnly ? {\n    pause: true,\n    export: true,\n    test: true\n  } : false;\n  const features = options.features || logOnly || DEFAULT_OPTIONS.features;\n  if (features.import === true) {\n    features.import = 'custom';\n  }\n  const config = Object.assign({}, DEFAULT_OPTIONS, {\n    features\n  }, options);\n  if (config.maxAge && config.maxAge < 2) {\n    throw new Error(`Devtools 'maxAge' cannot be less than 2, got ${config.maxAge}`);\n  }\n  return config;\n}\nfunction difference(first, second) {\n  return first.filter(item => second.indexOf(item) < 0);\n}\n/**\n * Provides an app's view into the state of the lifted store.\n */\nfunction unliftState(liftedState) {\n  const {\n    computedStates,\n    currentStateIndex\n  } = liftedState;\n  // At start up NgRx dispatches init actions,\n  // When these init actions are being filtered out by the predicate or safe/block list options\n  // we don't have a complete computed states yet.\n  // At this point it could happen that we're out of bounds, when this happens we fall back to the last known state\n  if (currentStateIndex >= computedStates.length) {\n    const {\n      state\n    } = computedStates[computedStates.length - 1];\n    return state;\n  }\n  const {\n    state\n  } = computedStates[currentStateIndex];\n  return state;\n}\nfunction unliftAction(liftedState) {\n  return liftedState.actionsById[liftedState.nextActionId - 1];\n}\n/**\n * Lifts an app's action into an action on the lifted store.\n */\nfunction liftAction(action) {\n  return new PerformAction(action, +Date.now());\n}\n/**\n * Sanitizes given actions with given function.\n */\nfunction sanitizeActions(actionSanitizer, actions) {\n  return Object.keys(actions).reduce((sanitizedActions, actionIdx) => {\n    const idx = Number(actionIdx);\n    sanitizedActions[idx] = sanitizeAction(actionSanitizer, actions[idx], idx);\n    return sanitizedActions;\n  }, {});\n}\n/**\n * Sanitizes given action with given function.\n */\nfunction sanitizeAction(actionSanitizer, action, actionIdx) {\n  return {\n    ...action,\n    action: actionSanitizer(action.action, actionIdx)\n  };\n}\n/**\n * Sanitizes given states with given function.\n */\nfunction sanitizeStates(stateSanitizer, states) {\n  return states.map((computedState, idx) => ({\n    state: sanitizeState(stateSanitizer, computedState.state, idx),\n    error: computedState.error\n  }));\n}\n/**\n * Sanitizes given state with given function.\n */\nfunction sanitizeState(stateSanitizer, state, stateIdx) {\n  return stateSanitizer(state, stateIdx);\n}\n/**\n * Read the config and tell if actions should be filtered\n */\nfunction shouldFilterActions(config) {\n  return config.predicate || config.actionsSafelist || config.actionsBlocklist;\n}\n/**\n * Return a full filtered lifted state\n */\nfunction filterLiftedState(liftedState, predicate, safelist, blocklist) {\n  const filteredStagedActionIds = [];\n  const filteredActionsById = {};\n  const filteredComputedStates = [];\n  liftedState.stagedActionIds.forEach((id, idx) => {\n    const liftedAction = liftedState.actionsById[id];\n    if (!liftedAction) return;\n    if (idx && isActionFiltered(liftedState.computedStates[idx], liftedAction, predicate, safelist, blocklist)) {\n      return;\n    }\n    filteredActionsById[id] = liftedAction;\n    filteredStagedActionIds.push(id);\n    filteredComputedStates.push(liftedState.computedStates[idx]);\n  });\n  return {\n    ...liftedState,\n    stagedActionIds: filteredStagedActionIds,\n    actionsById: filteredActionsById,\n    computedStates: filteredComputedStates\n  };\n}\n/**\n * Return true is the action should be ignored\n */\nfunction isActionFiltered(state, action, predicate, safelist, blockedlist) {\n  const predicateMatch = predicate && !predicate(state, action.action);\n  const safelistMatch = safelist && !action.action.type.match(safelist.map(s => escapeRegExp(s)).join('|'));\n  const blocklistMatch = blockedlist && action.action.type.match(blockedlist.map(s => escapeRegExp(s)).join('|'));\n  return predicateMatch || safelistMatch || blocklistMatch;\n}\n/**\n * Return string with escaped RegExp special characters\n * https://stackoverflow.com/a/6969486/1337347\n */\nfunction escapeRegExp(s) {\n  return s.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\nfunction injectZoneConfig(connectInZone) {\n  const ngZone = connectInZone ? inject(NgZone) : null;\n  return {\n    ngZone,\n    connectInZone\n  };\n}\nlet DevtoolsDispatcher = /*#__PURE__*/(() => {\n  class DevtoolsDispatcher extends ActionsSubject {\n    /** @nocollapse */static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵDevtoolsDispatcher_BaseFactory;\n        return function DevtoolsDispatcher_Factory(t) {\n          return (ɵDevtoolsDispatcher_BaseFactory || (ɵDevtoolsDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(DevtoolsDispatcher)))(t || DevtoolsDispatcher);\n        };\n      })();\n    }\n    /** @nocollapse */\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: DevtoolsDispatcher,\n        factory: DevtoolsDispatcher.ɵfac\n      });\n    }\n  }\n  return DevtoolsDispatcher;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst ExtensionActionTypes = {\n  START: 'START',\n  DISPATCH: 'DISPATCH',\n  STOP: 'STOP',\n  ACTION: 'ACTION'\n};\nconst REDUX_DEVTOOLS_EXTENSION = new InjectionToken('@ngrx/store-devtools Redux Devtools Extension');\nlet DevtoolsExtension = /*#__PURE__*/(() => {\n  class DevtoolsExtension {\n    constructor(devtoolsExtension, config, dispatcher) {\n      this.config = config;\n      this.dispatcher = dispatcher;\n      this.zoneConfig = injectZoneConfig(this.config.connectInZone);\n      this.devtoolsExtension = devtoolsExtension;\n      this.createActionStreams();\n    }\n    notify(action, state) {\n      if (!this.devtoolsExtension) {\n        return;\n      }\n      // Check to see if the action requires a full update of the liftedState.\n      // If it is a simple action generated by the user's app and the recording\n      // is not locked/paused, only send the action and the current state (fast).\n      //\n      // A full liftedState update (slow: serializes the entire liftedState) is\n      // only required when:\n      //   a) redux-devtools-extension fires the @@Init action (ignored by\n      //      @ngrx/store-devtools)\n      //   b) an action is generated by an @ngrx module (e.g. @ngrx/effects/init\n      //      or @ngrx/store/update-reducers)\n      //   c) the state has been recomputed due to time-traveling\n      //   d) any action that is not a PerformAction to err on the side of\n      //      caution.\n      if (action.type === PERFORM_ACTION) {\n        if (state.isLocked || state.isPaused) {\n          return;\n        }\n        const currentState = unliftState(state);\n        if (shouldFilterActions(this.config) && isActionFiltered(currentState, action, this.config.predicate, this.config.actionsSafelist, this.config.actionsBlocklist)) {\n          return;\n        }\n        const sanitizedState = this.config.stateSanitizer ? sanitizeState(this.config.stateSanitizer, currentState, state.currentStateIndex) : currentState;\n        const sanitizedAction = this.config.actionSanitizer ? sanitizeAction(this.config.actionSanitizer, action, state.nextActionId) : action;\n        this.sendToReduxDevtools(() => this.extensionConnection.send(sanitizedAction, sanitizedState));\n      } else {\n        // Requires full state update\n        const sanitizedLiftedState = {\n          ...state,\n          stagedActionIds: state.stagedActionIds,\n          actionsById: this.config.actionSanitizer ? sanitizeActions(this.config.actionSanitizer, state.actionsById) : state.actionsById,\n          computedStates: this.config.stateSanitizer ? sanitizeStates(this.config.stateSanitizer, state.computedStates) : state.computedStates\n        };\n        this.sendToReduxDevtools(() => this.devtoolsExtension.send(null, sanitizedLiftedState, this.getExtensionConfig(this.config)));\n      }\n    }\n    createChangesObservable() {\n      if (!this.devtoolsExtension) {\n        return EMPTY;\n      }\n      return new Observable(subscriber => {\n        const connection = this.zoneConfig.connectInZone ?\n        // To reduce change detection cycles, we need to run the `connect` method\n        // outside of the Angular zone. The `connect` method adds a `message`\n        // event listener to communicate with an extension using `window.postMessage`\n        // and handle message events.\n        this.zoneConfig.ngZone.runOutsideAngular(() => this.devtoolsExtension.connect(this.getExtensionConfig(this.config))) : this.devtoolsExtension.connect(this.getExtensionConfig(this.config));\n        this.extensionConnection = connection;\n        connection.init();\n        connection.subscribe(change => subscriber.next(change));\n        return connection.unsubscribe;\n      });\n    }\n    createActionStreams() {\n      // Listens to all changes\n      const changes$ = this.createChangesObservable().pipe(share());\n      // Listen for the start action\n      const start$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.START));\n      // Listen for the stop action\n      const stop$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.STOP));\n      // Listen for lifted actions\n      const liftedActions$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.DISPATCH), map(change => this.unwrapAction(change.payload)), concatMap(action => {\n        if (action.type === IMPORT_STATE) {\n          // State imports may happen in two situations:\n          // 1. Explicitly by user\n          // 2. User activated the \"persist state accross reloads\" option\n          //    and now the state is imported during reload.\n          // Because of option 2, we need to give possible\n          // lazy loaded reducers time to instantiate.\n          // As soon as there is no UPDATE action within 1 second,\n          // it is assumed that all reducers are loaded.\n          return this.dispatcher.pipe(filter(action => action.type === UPDATE), timeout(1000), debounceTime(1000), map(() => action), catchError(() => of(action)), take(1));\n        } else {\n          return of(action);\n        }\n      }));\n      // Listen for unlifted actions\n      const actions$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.ACTION), map(change => this.unwrapAction(change.payload)));\n      const actionsUntilStop$ = actions$.pipe(takeUntil(stop$));\n      const liftedUntilStop$ = liftedActions$.pipe(takeUntil(stop$));\n      this.start$ = start$.pipe(takeUntil(stop$));\n      // Only take the action sources between the start/stop events\n      this.actions$ = this.start$.pipe(switchMap(() => actionsUntilStop$));\n      this.liftedActions$ = this.start$.pipe(switchMap(() => liftedUntilStop$));\n    }\n    unwrapAction(action) {\n      // indirect eval according to https://esbuild.github.io/content-types/#direct-eval\n      return typeof action === 'string' ? (0, eval)(`(${action})`) : action;\n    }\n    getExtensionConfig(config) {\n      const extensionOptions = {\n        name: config.name,\n        features: config.features,\n        serialize: config.serialize,\n        autoPause: config.autoPause ?? false,\n        trace: config.trace ?? false,\n        traceLimit: config.traceLimit ?? 75\n        // The action/state sanitizers are not added to the config\n        // because sanitation is done in this class already.\n        // It is done before sending it to the devtools extension for consistency:\n        // - If we call extensionConnection.send(...),\n        //   the extension would call the sanitizers.\n        // - If we call devtoolsExtension.send(...) (aka full state update),\n        //   the extension would NOT call the sanitizers, so we have to do it ourselves.\n      };\n      if (config.maxAge !== false /* support === 0 */) {\n        extensionOptions.maxAge = config.maxAge;\n      }\n      return extensionOptions;\n    }\n    sendToReduxDevtools(send) {\n      try {\n        send();\n      } catch (err) {\n        console.warn('@ngrx/store-devtools: something went wrong inside the redux devtools', err);\n      }\n    }\n    /** @nocollapse */\n    static {\n      this.ɵfac = function DevtoolsExtension_Factory(t) {\n        return new (t || DevtoolsExtension)(i0.ɵɵinject(REDUX_DEVTOOLS_EXTENSION), i0.ɵɵinject(STORE_DEVTOOLS_CONFIG), i0.ɵɵinject(DevtoolsDispatcher));\n      };\n    }\n    /** @nocollapse */\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: DevtoolsExtension,\n        factory: DevtoolsExtension.ɵfac\n      });\n    }\n  }\n  return DevtoolsExtension;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst INIT_ACTION = {\n  type: INIT\n};\nconst RECOMPUTE = '@ngrx/store-devtools/recompute';\nconst RECOMPUTE_ACTION = {\n  type: RECOMPUTE\n};\n/**\n * Computes the next entry in the log by applying an action.\n */\nfunction computeNextEntry(reducer, action, state, error, errorHandler) {\n  if (error) {\n    return {\n      state,\n      error: 'Interrupted by an error up the chain'\n    };\n  }\n  let nextState = state;\n  let nextError;\n  try {\n    nextState = reducer(state, action);\n  } catch (err) {\n    nextError = err.toString();\n    errorHandler.handleError(err);\n  }\n  return {\n    state: nextState,\n    error: nextError\n  };\n}\n/**\n * Runs the reducer on invalidated actions to get a fresh computation log.\n */\nfunction recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused) {\n  // Optimization: exit early and return the same reference\n  // if we know nothing could have changed.\n  if (minInvalidatedStateIndex >= computedStates.length && computedStates.length === stagedActionIds.length) {\n    return computedStates;\n  }\n  const nextComputedStates = computedStates.slice(0, minInvalidatedStateIndex);\n  // If the recording is paused, recompute all states up until the pause state,\n  // else recompute all states.\n  const lastIncludedActionId = stagedActionIds.length - (isPaused ? 1 : 0);\n  for (let i = minInvalidatedStateIndex; i < lastIncludedActionId; i++) {\n    const actionId = stagedActionIds[i];\n    const action = actionsById[actionId].action;\n    const previousEntry = nextComputedStates[i - 1];\n    const previousState = previousEntry ? previousEntry.state : committedState;\n    const previousError = previousEntry ? previousEntry.error : undefined;\n    const shouldSkip = skippedActionIds.indexOf(actionId) > -1;\n    const entry = shouldSkip ? previousEntry : computeNextEntry(reducer, action, previousState, previousError, errorHandler);\n    nextComputedStates.push(entry);\n  }\n  // If the recording is paused, the last state will not be recomputed,\n  // because it's essentially not part of the state history.\n  if (isPaused) {\n    nextComputedStates.push(computedStates[computedStates.length - 1]);\n  }\n  return nextComputedStates;\n}\nfunction liftInitialState(initialCommittedState, monitorReducer) {\n  return {\n    monitorState: monitorReducer(undefined, {}),\n    nextActionId: 1,\n    actionsById: {\n      0: liftAction(INIT_ACTION)\n    },\n    stagedActionIds: [0],\n    skippedActionIds: [],\n    committedState: initialCommittedState,\n    currentStateIndex: 0,\n    computedStates: [],\n    isLocked: false,\n    isPaused: false\n  };\n}\n/**\n * Creates a history state reducer from an app's reducer.\n */\nfunction liftReducerWith(initialCommittedState, initialLiftedState, errorHandler, monitorReducer, options = {}) {\n  /**\n   * Manages how the history actions modify the history state.\n   */\n  return reducer => (liftedState, liftedAction) => {\n    let {\n      monitorState,\n      actionsById,\n      nextActionId,\n      stagedActionIds,\n      skippedActionIds,\n      committedState,\n      currentStateIndex,\n      computedStates,\n      isLocked,\n      isPaused\n    } = liftedState || initialLiftedState;\n    if (!liftedState) {\n      // Prevent mutating initialLiftedState\n      actionsById = Object.create(actionsById);\n    }\n    function commitExcessActions(n) {\n      // Auto-commits n-number of excess actions.\n      let excess = n;\n      let idsToDelete = stagedActionIds.slice(1, excess + 1);\n      for (let i = 0; i < idsToDelete.length; i++) {\n        if (computedStates[i + 1].error) {\n          // Stop if error is found. Commit actions up to error.\n          excess = i;\n          idsToDelete = stagedActionIds.slice(1, excess + 1);\n          break;\n        } else {\n          delete actionsById[idsToDelete[i]];\n        }\n      }\n      skippedActionIds = skippedActionIds.filter(id => idsToDelete.indexOf(id) === -1);\n      stagedActionIds = [0, ...stagedActionIds.slice(excess + 1)];\n      committedState = computedStates[excess].state;\n      computedStates = computedStates.slice(excess);\n      currentStateIndex = currentStateIndex > excess ? currentStateIndex - excess : 0;\n    }\n    function commitChanges() {\n      // Consider the last committed state the new starting point.\n      // Squash any staged actions into a single committed state.\n      actionsById = {\n        0: liftAction(INIT_ACTION)\n      };\n      nextActionId = 1;\n      stagedActionIds = [0];\n      skippedActionIds = [];\n      committedState = computedStates[currentStateIndex].state;\n      currentStateIndex = 0;\n      computedStates = [];\n    }\n    // By default, aggressively recompute every state whatever happens.\n    // This has O(n) performance, so we'll override this to a sensible\n    // value whenever we feel like we don't have to recompute the states.\n    let minInvalidatedStateIndex = 0;\n    switch (liftedAction.type) {\n      case LOCK_CHANGES:\n        {\n          isLocked = liftedAction.status;\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n      case PAUSE_RECORDING:\n        {\n          isPaused = liftedAction.status;\n          if (isPaused) {\n            // Add a pause action to signal the devtools-user the recording is paused.\n            // The corresponding state will be overwritten on each update to always contain\n            // the latest state (see Actions.PERFORM_ACTION).\n            stagedActionIds = [...stagedActionIds, nextActionId];\n            actionsById[nextActionId] = new PerformAction({\n              type: '@ngrx/devtools/pause'\n            }, +Date.now());\n            nextActionId++;\n            minInvalidatedStateIndex = stagedActionIds.length - 1;\n            computedStates = computedStates.concat(computedStates[computedStates.length - 1]);\n            if (currentStateIndex === stagedActionIds.length - 2) {\n              currentStateIndex++;\n            }\n            minInvalidatedStateIndex = Infinity;\n          } else {\n            commitChanges();\n          }\n          break;\n        }\n      case RESET:\n        {\n          // Get back to the state the store was created with.\n          actionsById = {\n            0: liftAction(INIT_ACTION)\n          };\n          nextActionId = 1;\n          stagedActionIds = [0];\n          skippedActionIds = [];\n          committedState = initialCommittedState;\n          currentStateIndex = 0;\n          computedStates = [];\n          break;\n        }\n      case COMMIT:\n        {\n          commitChanges();\n          break;\n        }\n      case ROLLBACK:\n        {\n          // Forget about any staged actions.\n          // Start again from the last committed state.\n          actionsById = {\n            0: liftAction(INIT_ACTION)\n          };\n          nextActionId = 1;\n          stagedActionIds = [0];\n          skippedActionIds = [];\n          currentStateIndex = 0;\n          computedStates = [];\n          break;\n        }\n      case TOGGLE_ACTION:\n        {\n          // Toggle whether an action with given ID is skipped.\n          // Being skipped means it is a no-op during the computation.\n          const {\n            id: actionId\n          } = liftedAction;\n          const index = skippedActionIds.indexOf(actionId);\n          if (index === -1) {\n            skippedActionIds = [actionId, ...skippedActionIds];\n          } else {\n            skippedActionIds = skippedActionIds.filter(id => id !== actionId);\n          }\n          // Optimization: we know history before this action hasn't changed\n          minInvalidatedStateIndex = stagedActionIds.indexOf(actionId);\n          break;\n        }\n      case SET_ACTIONS_ACTIVE:\n        {\n          // Toggle whether an action with given ID is skipped.\n          // Being skipped means it is a no-op during the computation.\n          const {\n            start,\n            end,\n            active\n          } = liftedAction;\n          const actionIds = [];\n          for (let i = start; i < end; i++) actionIds.push(i);\n          if (active) {\n            skippedActionIds = difference(skippedActionIds, actionIds);\n          } else {\n            skippedActionIds = [...skippedActionIds, ...actionIds];\n          }\n          // Optimization: we know history before this action hasn't changed\n          minInvalidatedStateIndex = stagedActionIds.indexOf(start);\n          break;\n        }\n      case JUMP_TO_STATE:\n        {\n          // Without recomputing anything, move the pointer that tell us\n          // which state is considered the current one. Useful for sliders.\n          currentStateIndex = liftedAction.index;\n          // Optimization: we know the history has not changed.\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n      case JUMP_TO_ACTION:\n        {\n          // Jumps to a corresponding state to a specific action.\n          // Useful when filtering actions.\n          const index = stagedActionIds.indexOf(liftedAction.actionId);\n          if (index !== -1) currentStateIndex = index;\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n      case SWEEP:\n        {\n          // Forget any actions that are currently being skipped.\n          stagedActionIds = difference(stagedActionIds, skippedActionIds);\n          skippedActionIds = [];\n          currentStateIndex = Math.min(currentStateIndex, stagedActionIds.length - 1);\n          break;\n        }\n      case PERFORM_ACTION:\n        {\n          // Ignore action and return state as is if recording is locked\n          if (isLocked) {\n            return liftedState || initialLiftedState;\n          }\n          if (isPaused || liftedState && isActionFiltered(liftedState.computedStates[currentStateIndex], liftedAction, options.predicate, options.actionsSafelist, options.actionsBlocklist)) {\n            // If recording is paused or if the action should be ignored, overwrite the last state\n            // (corresponds to the pause action) and keep everything else as is.\n            // This way, the app gets the new current state while the devtools\n            // do not record another action.\n            const lastState = computedStates[computedStates.length - 1];\n            computedStates = [...computedStates.slice(0, -1), computeNextEntry(reducer, liftedAction.action, lastState.state, lastState.error, errorHandler)];\n            minInvalidatedStateIndex = Infinity;\n            break;\n          }\n          // Auto-commit as new actions come in.\n          if (options.maxAge && stagedActionIds.length === options.maxAge) {\n            commitExcessActions(1);\n          }\n          if (currentStateIndex === stagedActionIds.length - 1) {\n            currentStateIndex++;\n          }\n          const actionId = nextActionId++;\n          // Mutation! This is the hottest path, and we optimize on purpose.\n          // It is safe because we set a new key in a cache dictionary.\n          actionsById[actionId] = liftedAction;\n          stagedActionIds = [...stagedActionIds, actionId];\n          // Optimization: we know that only the new action needs computing.\n          minInvalidatedStateIndex = stagedActionIds.length - 1;\n          break;\n        }\n      case IMPORT_STATE:\n        {\n          // Completely replace everything.\n          ({\n            monitorState,\n            actionsById,\n            nextActionId,\n            stagedActionIds,\n            skippedActionIds,\n            committedState,\n            currentStateIndex,\n            computedStates,\n            isLocked,\n            isPaused\n          } = liftedAction.nextLiftedState);\n          break;\n        }\n      case INIT:\n        {\n          // Always recompute states on hot reload and init.\n          minInvalidatedStateIndex = 0;\n          if (options.maxAge && stagedActionIds.length > options.maxAge) {\n            // States must be recomputed before committing excess.\n            computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n            commitExcessActions(stagedActionIds.length - options.maxAge);\n            // Avoid double computation.\n            minInvalidatedStateIndex = Infinity;\n          }\n          break;\n        }\n      case UPDATE:\n        {\n          const stateHasErrors = computedStates.filter(state => state.error).length > 0;\n          if (stateHasErrors) {\n            // Recompute all states\n            minInvalidatedStateIndex = 0;\n            if (options.maxAge && stagedActionIds.length > options.maxAge) {\n              // States must be recomputed before committing excess.\n              computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n              commitExcessActions(stagedActionIds.length - options.maxAge);\n              // Avoid double computation.\n              minInvalidatedStateIndex = Infinity;\n            }\n          } else {\n            // If not paused/locked, add a new action to signal devtools-user\n            // that there was a reducer update.\n            if (!isPaused && !isLocked) {\n              if (currentStateIndex === stagedActionIds.length - 1) {\n                currentStateIndex++;\n              }\n              // Add a new action to only recompute state\n              const actionId = nextActionId++;\n              actionsById[actionId] = new PerformAction(liftedAction, +Date.now());\n              stagedActionIds = [...stagedActionIds, actionId];\n              minInvalidatedStateIndex = stagedActionIds.length - 1;\n              computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n            }\n            // Recompute state history with latest reducer and update action\n            computedStates = computedStates.map(cmp => ({\n              ...cmp,\n              state: reducer(cmp.state, RECOMPUTE_ACTION)\n            }));\n            currentStateIndex = stagedActionIds.length - 1;\n            if (options.maxAge && stagedActionIds.length > options.maxAge) {\n              commitExcessActions(stagedActionIds.length - options.maxAge);\n            }\n            // Avoid double computation.\n            minInvalidatedStateIndex = Infinity;\n          }\n          break;\n        }\n      default:\n        {\n          // If the action is not recognized, it's a monitor action.\n          // Optimization: a monitor action can't change history.\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n    }\n    computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n    monitorState = monitorReducer(monitorState, liftedAction);\n    return {\n      monitorState,\n      actionsById,\n      nextActionId,\n      stagedActionIds,\n      skippedActionIds,\n      committedState,\n      currentStateIndex,\n      computedStates,\n      isLocked,\n      isPaused\n    };\n  };\n}\nlet StoreDevtools = /*#__PURE__*/(() => {\n  class StoreDevtools {\n    constructor(dispatcher, actions$, reducers$, extension, scannedActions, errorHandler, initialState, config) {\n      const liftedInitialState = liftInitialState(initialState, config.monitor);\n      const liftReducer = liftReducerWith(initialState, liftedInitialState, errorHandler, config.monitor, config);\n      const liftedAction$ = merge(merge(actions$.asObservable().pipe(skip(1)), extension.actions$).pipe(map(liftAction)), dispatcher, extension.liftedActions$).pipe(observeOn(queueScheduler));\n      const liftedReducer$ = reducers$.pipe(map(liftReducer));\n      const zoneConfig = injectZoneConfig(config.connectInZone);\n      const liftedStateSubject = new ReplaySubject(1);\n      this.liftedStateSubscription = liftedAction$.pipe(withLatestFrom(liftedReducer$),\n      // The extension would post messages back outside of the Angular zone\n      // because we call `connect()` wrapped with `runOutsideAngular`. We run change\n      // detection only once at the end after all the required asynchronous tasks have\n      // been processed (for instance, `setInterval` scheduled by the `timeout` operator).\n      // We have to re-enter the Angular zone before the `scan` since it runs the reducer\n      // which must be run within the Angular zone.\n      emitInZone(zoneConfig), scan(({\n        state: liftedState\n      }, [action, reducer]) => {\n        let reducedLiftedState = reducer(liftedState, action);\n        // On full state update\n        // If we have actions filters, we must filter completely our lifted state to be sync with the extension\n        if (action.type !== PERFORM_ACTION && shouldFilterActions(config)) {\n          reducedLiftedState = filterLiftedState(reducedLiftedState, config.predicate, config.actionsSafelist, config.actionsBlocklist);\n        }\n        // Extension should be sent the sanitized lifted state\n        extension.notify(action, reducedLiftedState);\n        return {\n          state: reducedLiftedState,\n          action\n        };\n      }, {\n        state: liftedInitialState,\n        action: null\n      })).subscribe(({\n        state,\n        action\n      }) => {\n        liftedStateSubject.next(state);\n        if (action.type === PERFORM_ACTION) {\n          const unliftedAction = action.action;\n          scannedActions.next(unliftedAction);\n        }\n      });\n      this.extensionStartSubscription = extension.start$.pipe(emitInZone(zoneConfig)).subscribe(() => {\n        this.refresh();\n      });\n      const liftedState$ = liftedStateSubject.asObservable();\n      const state$ = liftedState$.pipe(map(unliftState));\n      Object.defineProperty(state$, 'state', {\n        value: toSignal(state$, {\n          manualCleanup: true,\n          requireSync: true\n        })\n      });\n      this.dispatcher = dispatcher;\n      this.liftedState = liftedState$;\n      this.state = state$;\n    }\n    ngOnDestroy() {\n      // Even though the store devtools plugin is recommended to be\n      // used only in development mode, it can still cause a memory leak\n      // in microfrontend applications that are being created and destroyed\n      // multiple times during development. This results in excessive memory\n      // consumption, as it prevents entire apps from being garbage collected.\n      this.liftedStateSubscription.unsubscribe();\n      this.extensionStartSubscription.unsubscribe();\n    }\n    dispatch(action) {\n      this.dispatcher.next(action);\n    }\n    next(action) {\n      this.dispatcher.next(action);\n    }\n    error(error) {}\n    complete() {}\n    performAction(action) {\n      this.dispatch(new PerformAction(action, +Date.now()));\n    }\n    refresh() {\n      this.dispatch(new Refresh());\n    }\n    reset() {\n      this.dispatch(new Reset(+Date.now()));\n    }\n    rollback() {\n      this.dispatch(new Rollback(+Date.now()));\n    }\n    commit() {\n      this.dispatch(new Commit(+Date.now()));\n    }\n    sweep() {\n      this.dispatch(new Sweep());\n    }\n    toggleAction(id) {\n      this.dispatch(new ToggleAction(id));\n    }\n    jumpToAction(actionId) {\n      this.dispatch(new JumpToAction(actionId));\n    }\n    jumpToState(index) {\n      this.dispatch(new JumpToState(index));\n    }\n    importState(nextLiftedState) {\n      this.dispatch(new ImportState(nextLiftedState));\n    }\n    lockChanges(status) {\n      this.dispatch(new LockChanges(status));\n    }\n    pauseRecording(status) {\n      this.dispatch(new PauseRecording(status));\n    }\n    /** @nocollapse */\n    static {\n      this.ɵfac = function StoreDevtools_Factory(t) {\n        return new (t || StoreDevtools)(i0.ɵɵinject(DevtoolsDispatcher), i0.ɵɵinject(i2.ActionsSubject), i0.ɵɵinject(i2.ReducerObservable), i0.ɵɵinject(DevtoolsExtension), i0.ɵɵinject(i2.ScannedActionsSubject), i0.ɵɵinject(i0.ErrorHandler), i0.ɵɵinject(INITIAL_STATE), i0.ɵɵinject(STORE_DEVTOOLS_CONFIG));\n      };\n    }\n    /** @nocollapse */\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: StoreDevtools,\n        factory: StoreDevtools.ɵfac\n      });\n    }\n  }\n  return StoreDevtools;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * If the devtools extension is connected out of the Angular zone,\n * this operator will emit all events within the zone.\n */\nfunction emitInZone({\n  ngZone,\n  connectInZone\n}) {\n  return source => connectInZone ? new Observable(subscriber => source.subscribe({\n    next: value => ngZone.run(() => subscriber.next(value)),\n    error: error => ngZone.run(() => subscriber.error(error)),\n    complete: () => ngZone.run(() => subscriber.complete())\n  })) : source;\n}\nconst IS_EXTENSION_OR_MONITOR_PRESENT = new InjectionToken('@ngrx/store-devtools Is Devtools Extension or Monitor Present');\nfunction createIsExtensionOrMonitorPresent(extension, config) {\n  return Boolean(extension) || config.monitor !== noMonitor;\n}\nfunction createReduxDevtoolsExtension() {\n  const extensionKey = '__REDUX_DEVTOOLS_EXTENSION__';\n  if (typeof window === 'object' && typeof window[extensionKey] !== 'undefined') {\n    return window[extensionKey];\n  } else {\n    return null;\n  }\n}\n/**\n * Provides developer tools and instrumentation for `Store`.\n *\n * @usageNotes\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideStoreDevtools({\n *       maxAge: 25,\n *       logOnly: !isDevMode(),\n *     }),\n *   ],\n * });\n * ```\n */\nfunction provideStoreDevtools(options = {}) {\n  return makeEnvironmentProviders([DevtoolsExtension, DevtoolsDispatcher, StoreDevtools, {\n    provide: INITIAL_OPTIONS,\n    useValue: options\n  }, {\n    provide: IS_EXTENSION_OR_MONITOR_PRESENT,\n    deps: [REDUX_DEVTOOLS_EXTENSION, STORE_DEVTOOLS_CONFIG],\n    useFactory: createIsExtensionOrMonitorPresent\n  }, {\n    provide: REDUX_DEVTOOLS_EXTENSION,\n    useFactory: createReduxDevtoolsExtension\n  }, {\n    provide: STORE_DEVTOOLS_CONFIG,\n    deps: [INITIAL_OPTIONS],\n    useFactory: createConfig\n  }, {\n    provide: StateObservable,\n    deps: [StoreDevtools],\n    useFactory: createStateObservable\n  }, {\n    provide: ReducerManagerDispatcher,\n    useExisting: DevtoolsDispatcher\n  }]);\n}\nfunction createStateObservable(devtools) {\n  return devtools.state;\n}\nlet StoreDevtoolsModule = /*#__PURE__*/(() => {\n  class StoreDevtoolsModule {\n    static instrument(options = {}) {\n      return {\n        ngModule: StoreDevtoolsModule,\n        providers: [provideStoreDevtools(options)]\n      };\n    }\n    /** @nocollapse */\n    static {\n      this.ɵfac = function StoreDevtoolsModule_Factory(t) {\n        return new (t || StoreDevtoolsModule)();\n      };\n    }\n    /** @nocollapse */\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: StoreDevtoolsModule\n      });\n    }\n    /** @nocollapse */\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return StoreDevtoolsModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_OPTIONS, RECOMPUTE, REDUX_DEVTOOLS_EXTENSION, StoreDevtools, StoreDevtoolsConfig, StoreDevtoolsModule, provideStoreDevtools };\n//# sourceMappingURL=ngrx-store-devtools.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}