{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  if (n === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 2 && n % 100 <= 10) return 3;\n  if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19) return 4;\n  return 5;\n}\nexport default [\"mt\", [[\"am\", \"pm\"], [\"AM\", \"PM\"], u], u, [[\"Ħd\", \"T\", \"Tl\", \"Er\", \"Ħm\", \"Ġm\", \"Sb\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"], [\"Il-Ħadd\", \"It-Tnejn\", \"It-Tlie<PERSON>\", \"L-Erbgħa\", \"Il-Ħami<PERSON>\", \"Il-Ġimgħa\", \"Is-Sibt\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"]], [[\"Ħd\", \"Tn\", \"Tl\", \"Er\", \"Ħm\", \"Ġm\", \"Sb\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"], [\"Il-Ħadd\", \"It-Tnejn\", \"It-Tlieta\", \"L-Erbgħa\", \"Il-Ħamis\", \"Il-Ġimgħa\", \"Is-Sibt\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"Ġ\", \"L\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Fra\", \"Mar\", \"Apr\", \"Mej\", \"Ġun\", \"Lul\", \"Aww\", \"Set\", \"Ott\", \"Nov\", \"Diċ\"], [\"Jannar\", \"Frar\", \"Marzu\", \"April\", \"Mejju\", \"Ġunju\", \"Lulju\", \"Awwissu\", \"Settembru\", \"Ottubru\", \"Novembru\", \"Diċembru\"]], [[\"Jn\", \"Fr\", \"Mz\", \"Ap\", \"Mj\", \"Ġn\", \"Lj\", \"Aw\", \"St\", \"Ob\", \"Nv\", \"Dċ\"], [\"Jan\", \"Fra\", \"Mar\", \"Apr\", \"Mej\", \"Ġun\", \"Lul\", \"Aww\", \"Set\", \"Ott\", \"Nov\", \"Diċ\"], [\"Jannar\", \"Frar\", \"Marzu\", \"April\", \"Mejju\", \"Ġunju\", \"Lulju\", \"Awwissu\", \"Settembru\", \"Ottubru\", \"Novembru\", \"Diċembru\"]], [[\"QK\", \"WK\"], u, [\"Qabel Kristu\", \"Wara Kristu\"]], 0, [6, 0], [\"dd/MM/y\", \"dd MMM y\", \"d 'ta'’ MMMM y\", \"EEEE, d 'ta'’ MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"EUR\", \"€\", \"ewro\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "Math", "floor"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/mt.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    if (n === 0 || n % 100 === Math.floor(n % 100) && (n % 100 >= 2 && n % 100 <= 10))\n        return 3;\n    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 19))\n        return 4;\n    return 5;\n}\nexport default [\"mt\", [[\"am\", \"pm\"], [\"AM\", \"PM\"], u], u, [[\"Ħd\", \"T\", \"Tl\", \"Er\", \"Ħm\", \"Ġm\", \"Sb\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"], [\"Il-Ħadd\", \"It-Tnejn\", \"It-Tlieta\", \"L-Erbgħa\", \"Il-Ħamis\", \"Il-Ġimgħa\", \"Is-Sibt\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"]], [[\"Ħd\", \"Tn\", \"Tl\", \"Er\", \"Ħm\", \"Ġm\", \"Sb\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"], [\"Il-Ħadd\", \"It-Tnejn\", \"It-Tlieta\", \"L-Erbgħa\", \"Il-Ħamis\", \"Il-Ġimgħa\", \"Is-Sibt\"], [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"Ġ\", \"L\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Fra\", \"Mar\", \"Apr\", \"Mej\", \"Ġun\", \"Lul\", \"Aww\", \"Set\", \"Ott\", \"Nov\", \"Diċ\"], [\"Jannar\", \"Frar\", \"Marzu\", \"April\", \"Mejju\", \"Ġunju\", \"Lulju\", \"Awwissu\", \"Settembru\", \"Ottubru\", \"Novembru\", \"Diċembru\"]], [[\"Jn\", \"Fr\", \"Mz\", \"Ap\", \"Mj\", \"Ġn\", \"Lj\", \"Aw\", \"St\", \"Ob\", \"Nv\", \"Dċ\"], [\"Jan\", \"Fra\", \"Mar\", \"Apr\", \"Mej\", \"Ġun\", \"Lul\", \"Aww\", \"Set\", \"Ott\", \"Nov\", \"Diċ\"], [\"Jannar\", \"Frar\", \"Marzu\", \"April\", \"Mejju\", \"Ġunju\", \"Lulju\", \"Awwissu\", \"Settembru\", \"Ottubru\", \"Novembru\", \"Diċembru\"]], [[\"QK\", \"WK\"], u, [\"Qabel Kristu\", \"Wara Kristu\"]], 0, [6, 0], [\"dd/MM/y\", \"dd MMM y\", \"d 'ta'’ MMMM y\", \"EEEE, d 'ta'’ MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"EUR\", \"€\", \"ewro\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAKA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAG,EAC7E,OAAO,CAAC;EACZ,IAAIA,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAKA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAG,EACnE,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}