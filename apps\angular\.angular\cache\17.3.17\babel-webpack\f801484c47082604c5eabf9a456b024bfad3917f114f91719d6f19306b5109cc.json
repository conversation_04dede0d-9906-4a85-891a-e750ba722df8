{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class InfoExchangeImportFileService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.discardFileByFileId = (fileId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Import/InfoExchange/DiscardFile',\n      params: {\n        fileId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.downloadDataErrorsByFileId = (fileId, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/DownloadDataErrors',\n      params: {\n        fileId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.downloadFileErrorsByFileId = (fileId, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/DownloadFileErrors',\n      params: {\n        fileId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.get = (id, config) => this.restService.request({\n      method: 'GET',\n      url: `/api/ESService/Import/InfoExchange/${id}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getDataErrorsByFileId = (fileId, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/GetDataErrors',\n      params: {\n        fileId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getFileErrorsByFileId = (fileId, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/GetFileErrors',\n      params: {\n        fileId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getFileListByInput = (input, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/GetFileList',\n      params: {\n        uploadedDateTime: input.uploadedDateTime,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getFileReadyToSubmit = config => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/GetFileReadyToSubmit'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getLastImportedFile = config => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/GetLastImportedFile'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getUploadProgress = config => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/GetUploadProgress'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.hasFileReadyToSumbit = config => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Import/InfoExchange/HasFileReadyToSumbit'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.submitFileByFileId = (fileId, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Import/InfoExchange/SubmitFile',\n      params: {\n        fileId\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function InfoExchangeImportFileService_Factory(t) {\n      return new (t || InfoExchangeImportFileService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: InfoExchangeImportFileService,\n      factory: InfoExchangeImportFileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["InfoExchangeImportFileService", "constructor", "restService", "apiName", "discardFileByFileId", "fileId", "config", "request", "method", "url", "params", "downloadDataErrorsByFileId", "downloadFileErrorsByFileId", "get", "id", "getDataErrorsByFileId", "getFileErrorsByFileId", "getFileListByInput", "input", "uploadedDateTime", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "getFileReadyToSubmit", "getLastImportedFile", "getUploadProgress", "hasFileReadyToSumbit", "submitFileByFileId", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\information-exchanges\\info-exchange-import-file.service.ts"], "sourcesContent": ["import type { GetInfoExchangeImportFileDto, InfoExchangeImportFileDto } from './historical-migration/models';\r\nimport { RestService, Rest } from '@abp/ng.core';\r\nimport type { PagedResultDto } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\nimport type { ValidationResult } from '../../../../fluent-validation/results/models';\r\nimport type { ProgressNotficiationEto } from '../../shared/hosting/microservices/eto/declaration-imports/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class InfoExchangeImportFileService {\r\n  apiName = 'EconomicSubstanceService';\r\n  \r\n\r\n  discardFileByFileId = (fileId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, void>({\r\n      method: 'POST',\r\n      url: '/api/ESService/Import/InfoExchange/DiscardFile',\r\n      params: { fileId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  downloadDataErrorsByFileId = (fileId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, number[]>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/DownloadDataErrors',\r\n      params: { fileId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  downloadFileErrorsByFileId = (fileId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, number[]>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/DownloadFileErrors',\r\n      params: { fileId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  get = (id: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, InfoExchangeImportFileDto>({\r\n      method: 'GET',\r\n      url: `/api/ESService/Import/InfoExchange/${id}`,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getDataErrorsByFileId = (fileId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, ValidationResult>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/GetDataErrors',\r\n      params: { fileId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getFileErrorsByFileId = (fileId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, ValidationResult>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/GetFileErrors',\r\n      params: { fileId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getFileListByInput = (input: GetInfoExchangeImportFileDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, PagedResultDto<InfoExchangeImportFileDto>>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/GetFileList',\r\n      params: { uploadedDateTime: input.uploadedDateTime, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getFileReadyToSubmit = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, InfoExchangeImportFileDto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/GetFileReadyToSubmit',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getLastImportedFile = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, InfoExchangeImportFileDto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/GetLastImportedFile',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getUploadProgress = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, ProgressNotficiationEto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/GetUploadProgress',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  hasFileReadyToSumbit = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, boolean>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Import/InfoExchange/HasFileReadyToSumbit',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  submitFileByFileId = (fileId: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, ValidationResult>({\r\n      method: 'POST',\r\n      url: '/api/ESService/Import/InfoExchange/SubmitFile',\r\n      params: { fileId },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAUA,OAAM,MAAOA,6BAA6B;EA0GxCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAzG/B,KAAAC,OAAO,GAAG,0BAA0B;IAGpC,KAAAC,mBAAmB,GAAG,CAACC,MAAc,EAAEC,MAA6B,KAClE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAY;MAClCC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,gDAAgD;MACrDC,MAAM,EAAE;QAAEL;MAAM;KACjB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAK,0BAA0B,GAAG,CAACN,MAAc,EAAEC,MAA6B,KACzE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAgB;MACtCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,uDAAuD;MAC5DC,MAAM,EAAE;QAAEL;MAAM;KACjB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAM,0BAA0B,GAAG,CAACP,MAAc,EAAEC,MAA6B,KACzE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAgB;MACtCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,uDAAuD;MAC5DC,MAAM,EAAE;QAAEL;MAAM;KACjB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAO,GAAG,GAAG,CAACC,EAAU,EAAER,MAA6B,KAC9C,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAiC;MACvDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,sCAAsCK,EAAE;KAC9C,EACD;MAAEX,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAS,qBAAqB,GAAG,CAACV,MAAc,EAAEC,MAA6B,KACpE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwB;MAC9CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,kDAAkD;MACvDC,MAAM,EAAE;QAAEL;MAAM;KACjB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAU,qBAAqB,GAAG,CAACX,MAAc,EAAEC,MAA6B,KACpE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwB;MAC9CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,kDAAkD;MACvDC,MAAM,EAAE;QAAEL;MAAM;KACjB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAW,kBAAkB,GAAG,CAACC,KAAmC,EAAEZ,MAA6B,KACtF,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAiD;MACvEC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,gDAAgD;MACrDC,MAAM,EAAE;QAAES,gBAAgB,EAAED,KAAK,CAACC,gBAAgB;QAAEC,OAAO,EAAEF,KAAK,CAACE,OAAO;QAAEC,SAAS,EAAEH,KAAK,CAACG,SAAS;QAAEC,cAAc,EAAEJ,KAAK,CAACI;MAAc;KAC7I,EACD;MAAEnB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAiB,oBAAoB,GAAIjB,MAA6B,IACnD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAiC;MACvDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAkB,mBAAmB,GAAIlB,MAA6B,IAClD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAiC;MACvDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAmB,iBAAiB,GAAInB,MAA6B,IAChD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAA+B;MACrDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAoB,oBAAoB,GAAIpB,MAA6B,IACnD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAe;MACrCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAqB,kBAAkB,GAAG,CAACtB,MAAc,EAAEC,MAA6B,KACjE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAwB;MAC9CC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,+CAA+C;MACpDC,MAAM,EAAE;QAAEL;MAAM;KACjB,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBA1GpCN,6BAA6B,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAA7B/B,6BAA6B;MAAAgC,OAAA,EAA7BhC,6BAA6B,CAAAiC,IAAA;MAAAC,UAAA,EAF5B;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}