{"ast": null, "code": "import { ConfigStateService, featuresFactory, RoutesService, noop } from '@abp/ng.core';\nimport { InjectionToken, inject, APP_INITIALIZER } from '@angular/core';\nimport { setModuleVisibilityFactory } from '@volo/abp.commercial.ng.ui/config';\nconst AUDIT_LOGGING_FEATURES = new InjectionToken('AUDIT_LOGGING_FEATURES', {\n  providedIn: 'root',\n  factory: () => {\n    const configState = inject(ConfigStateService);\n    const featureKey = 'AuditLogging.Enable';\n    const mapFn = features => ({\n      enable: features[featureKey].toLowerCase() !== 'false'\n    });\n    return featuresFactory(configState, [featureKey], mapFn);\n  }\n});\nconst SET_AUDIT_LOGGING_ROUTE_VISIBILITY = new InjectionToken('SET_AUDIT_LOGGING_ROUTE_VISIBILITY', {\n  providedIn: 'root',\n  factory: () => {\n    const routes = inject(RoutesService);\n    const stream = inject(AUDIT_LOGGING_FEATURES);\n    setModuleVisibilityFactory(stream, routes, \"AbpAuditLogging::Menu:AuditLogging\" /* eAuditLoggingRouteNames.AuditLogging */).subscribe();\n  }\n});\nconst AUDIT_LOGGING_FEATURES_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: noop,\n  deps: [SET_AUDIT_LOGGING_ROUTE_VISIBILITY],\n  multi: true\n}];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUDIT_LOGGING_FEATURES, AUDIT_LOGGING_FEATURES_PROVIDERS, SET_AUDIT_LOGGING_ROUTE_VISIBILITY };", "map": {"version": 3, "names": ["ConfigStateService", "featuresFactory", "RoutesService", "noop", "InjectionToken", "inject", "APP_INITIALIZER", "setModuleVisibilityFactory", "AUDIT_LOGGING_FEATURES", "providedIn", "factory", "configState", "<PERSON><PERSON><PERSON>", "mapFn", "features", "enable", "toLowerCase", "SET_AUDIT_LOGGING_ROUTE_VISIBILITY", "routes", "stream", "subscribe", "AUDIT_LOGGING_FEATURES_PROVIDERS", "provide", "useFactory", "deps", "multi"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@volo/abp.ng.audit-logging/fesm2022/volo-abp.ng.audit-logging-common.mjs"], "sourcesContent": ["import { ConfigStateService, featuresFactory, RoutesService, noop } from '@abp/ng.core';\nimport { InjectionToken, inject, APP_INITIALIZER } from '@angular/core';\nimport { setModuleVisibilityFactory } from '@volo/abp.commercial.ng.ui/config';\n\nconst AUDIT_LOGGING_FEATURES = new InjectionToken('AUDIT_LOGGING_FEATURES', {\n    providedIn: 'root',\n    factory: () => {\n        const configState = inject(ConfigStateService);\n        const featureKey = 'AuditLogging.Enable';\n        const mapFn = features => ({\n            enable: features[featureKey].toLowerCase() !== 'false',\n        });\n        return featuresFactory(configState, [featureKey], mapFn);\n    },\n});\nconst SET_AUDIT_LOGGING_ROUTE_VISIBILITY = new InjectionToken('SET_AUDIT_LOGGING_ROUTE_VISIBILITY', {\n    providedIn: 'root',\n    factory: () => {\n        const routes = inject(RoutesService);\n        const stream = inject(AUDIT_LOGGING_FEATURES);\n        setModuleVisibilityFactory(stream, routes, \"AbpAuditLogging::Menu:AuditLogging\" /* eAuditLoggingRouteNames.AuditLogging */).subscribe();\n    },\n});\nconst AUDIT_LOGGING_FEATURES_PROVIDERS = [\n    {\n        provide: APP_INITIALIZER,\n        useFactory: noop,\n        deps: [SET_AUDIT_LOGGING_ROUTE_VISIBILITY],\n        multi: true,\n    },\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUDIT_LOGGING_FEATURES, AUDIT_LOGGING_FEATURES_PROVIDERS, SET_AUDIT_LOGGING_ROUTE_VISIBILITY };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,IAAI,QAAQ,cAAc;AACvF,SAASC,cAAc,EAAEC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvE,SAASC,0BAA0B,QAAQ,mCAAmC;AAE9E,MAAMC,sBAAsB,GAAG,IAAIJ,cAAc,CAAC,wBAAwB,EAAE;EACxEK,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,WAAW,GAAGN,MAAM,CAACL,kBAAkB,CAAC;IAC9C,MAAMY,UAAU,GAAG,qBAAqB;IACxC,MAAMC,KAAK,GAAGC,QAAQ,KAAK;MACvBC,MAAM,EAAED,QAAQ,CAACF,UAAU,CAAC,CAACI,WAAW,CAAC,CAAC,KAAK;IACnD,CAAC,CAAC;IACF,OAAOf,eAAe,CAACU,WAAW,EAAE,CAACC,UAAU,CAAC,EAAEC,KAAK,CAAC;EAC5D;AACJ,CAAC,CAAC;AACF,MAAMI,kCAAkC,GAAG,IAAIb,cAAc,CAAC,oCAAoC,EAAE;EAChGK,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMQ,MAAM,GAAGb,MAAM,CAACH,aAAa,CAAC;IACpC,MAAMiB,MAAM,GAAGd,MAAM,CAACG,sBAAsB,CAAC;IAC7CD,0BAA0B,CAACY,MAAM,EAAED,MAAM,EAAE,oCAAoC,CAAC,0CAA0C,CAAC,CAACE,SAAS,CAAC,CAAC;EAC3I;AACJ,CAAC,CAAC;AACF,MAAMC,gCAAgC,GAAG,CACrC;EACIC,OAAO,EAAEhB,eAAe;EACxBiB,UAAU,EAAEpB,IAAI;EAChBqB,IAAI,EAAE,CAACP,kCAAkC,CAAC;EAC1CQ,KAAK,EAAE;AACX,CAAC,CACJ;;AAED;AACA;AACA;;AAEA,SAASjB,sBAAsB,EAAEa,gCAAgC,EAAEJ,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}