{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'li għadda' 'fil-'p\",\n  yesterday: \"'Il-bieraħ fil-'p\",\n  today: \"'<PERSON>lum fil-'p\",\n  tomorrow: \"'Għ<PERSON> fil-'p\",\n  nextWeek: \"eeee 'fil-'p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}