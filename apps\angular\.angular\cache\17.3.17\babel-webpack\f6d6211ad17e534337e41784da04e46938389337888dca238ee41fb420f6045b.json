{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Vietnamese locale reference: http://www.localeplanet.com/icu/vi-VN/index.html\n// Capitalization reference: http://hcmup.edu.vn/index.php?option=com_content&view=article&id=4106%3Avit-hoa-trong-vn-bn-hanh-chinh&catid=2345%3Atham-kho&Itemid=4103&lang=vi&site=134\nvar eraValues = {\n  narrow: ['TCN', 'SCN'],\n  abbreviated: ['trước CN', 'sau CN'],\n  wide: ['trước Công Nguyên', 'sau Cô<PERSON> Nguyên']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['Quý 1', 'Quý 2', 'Quý 3', 'Quý 4']\n};\nvar formattingQuarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  // I notice many news outlet use this \"quý II/2018\"\n  wide: ['quý I', 'quý II', 'quý III', 'quý IV']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n  abbreviated: ['Thg 1', 'Thg 2', 'Thg 3', 'Thg 4', 'Thg 5', 'Thg 6', 'Thg 7', 'Thg 8', 'Thg 9', 'Thg 10', 'Thg 11', 'Thg 12'],\n  wide: ['Tháng Một', 'Tháng Hai', 'Tháng Ba', 'Tháng Tư', 'Tháng Năm', 'Tháng Sáu', 'Tháng Bảy', 'Tháng Tám', 'Tháng Chín', 'Tháng Mười', 'Tháng Mười Một', 'Tháng Mười Hai']\n};\n// In Vietnamese date formatting, month number less than 10 expected to have leading zero\nvar formattingMonthValues = {\n  narrow: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],\n  abbreviated: ['thg 1', 'thg 2', 'thg 3', 'thg 4', 'thg 5', 'thg 6', 'thg 7', 'thg 8', 'thg 9', 'thg 10', 'thg 11', 'thg 12'],\n  wide: ['tháng 01', 'tháng 02', 'tháng 03', 'tháng 04', 'tháng 05', 'tháng 06', 'tháng 07', 'tháng 08', 'tháng 09', 'tháng 10', 'tháng 11', 'tháng 12']\n};\nvar dayValues = {\n  narrow: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],\n  short: ['CN', 'Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7'],\n  abbreviated: ['CN', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'],\n  wide: ['Chủ Nhật', 'Thứ Hai', 'Thứ Ba', 'Thứ Tư', 'Thứ Năm', 'Thứ Sáu', 'Thứ Bảy']\n};\n\n// Vietnamese are used to AM/PM borrowing from English, hence `narrow` and\n// `abbreviated` are just like English but I'm leaving the `wide`\n// format being localized with abbreviations found in some systems (SÁng / CHiều);\n// however, personally, I don't think `Chiều` sounds appropriate for `PM`\nvar dayPeriodValues = {\n  // narrow date period is extremely rare in Vietnamese\n  // I used abbreviated form for noon, morning and afternoon\n  // which are regconizable by Vietnamese, others cannot be any shorter\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'nửa đêm',\n    noon: 'tr',\n    morning: 'sg',\n    afternoon: 'ch',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'nửa đêm',\n    noon: 'trưa',\n    morning: 'sáng',\n    afternoon: 'chiều',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  wide: {\n    am: 'SA',\n    pm: 'CH',\n    midnight: 'nửa đêm',\n    noon: 'trưa',\n    morning: 'sáng',\n    afternoon: 'chiều',\n    evening: 'tối',\n    night: 'đêm'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'nửa đêm',\n    noon: 'tr',\n    morning: 'sg',\n    afternoon: 'ch',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'nửa đêm',\n    noon: 'trưa',\n    morning: 'sáng',\n    afternoon: 'chiều',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  wide: {\n    am: 'SA',\n    pm: 'CH',\n    midnight: 'nửa đêm',\n    noon: 'giữa trưa',\n    morning: 'vào buổi sáng',\n    afternoon: 'vào buổi chiều',\n    evening: 'vào buổi tối',\n    night: 'vào ban đêm'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (unit === 'quarter') {\n    // many news outlets use \"quý I\"...\n    switch (number) {\n      case 1:\n        return 'I';\n      case 2:\n        return 'II';\n      case 3:\n        return 'III';\n      case 4:\n        return 'IV';\n    }\n  } else if (unit === 'day') {\n    // day of week in Vietnamese has ordinal number meaning,\n    // so we should use them, else it'll sound weird\n    switch (number) {\n      case 1:\n        return 'thứ 2';\n      // meaning 2nd day but it's the first day of the week :D\n      case 2:\n        return 'thứ 3';\n      // meaning 3rd day\n      case 3:\n        return 'thứ 4';\n      // meaning 4th day and so on\n      case 4:\n        return 'thứ 5';\n      case 5:\n        return 'thứ 6';\n      case 6:\n        return 'thứ 7';\n      case 7:\n        return 'chủ nhật';\n      // meaning Sunday, there's no 8th day :D\n    }\n  } else if (unit === 'week') {\n    if (number === 1) {\n      return 'thứ nhất';\n    } else {\n      return 'thứ ' + number;\n    }\n  } else if (unit === 'dayOfYear') {\n    if (number === 1) {\n      return 'đầu tiên';\n    } else {\n      return 'thứ ' + number;\n    }\n  }\n\n  // there are no different forms of ordinal numbers in Vietnamese\n  return String(number);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "formattingQuarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "String", "localize", "era", "values", "defaultWidth", "quarter", "formattingValues", "defaultFormattingWidth", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/vi/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Vietnamese locale reference: http://www.localeplanet.com/icu/vi-VN/index.html\n// Capitalization reference: http://hcmup.edu.vn/index.php?option=com_content&view=article&id=4106%3Avit-hoa-trong-vn-bn-hanh-chinh&catid=2345%3Atham-kho&Itemid=4103&lang=vi&site=134\nvar eraValues = {\n  narrow: ['TCN', 'SCN'],\n  abbreviated: ['trước CN', 'sau CN'],\n  wide: ['trước Công Nguyên', 'sau Cô<PERSON> Nguyên']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['Quý 1', 'Quý 2', 'Quý 3', 'Quý 4']\n};\nvar formattingQuarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  // I notice many news outlet use this \"quý II/2018\"\n  wide: ['quý I', 'quý II', 'quý III', 'quý IV']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n  abbreviated: ['Thg 1', 'Thg 2', 'Thg 3', 'Thg 4', 'Thg 5', 'Thg 6', 'Thg 7', 'Thg 8', 'Thg 9', 'Thg 10', 'Thg 11', 'Thg 12'],\n  wide: ['Tháng Một', 'Tháng Hai', 'Tháng Ba', 'Tháng Tư', 'Tháng Năm', 'Tháng Sáu', 'Tháng Bảy', 'Tháng Tám', 'Tháng Chín', 'Tháng Mười', 'Tháng Mười Một', 'Tháng Mười Hai']\n};\n// In Vietnamese date formatting, month number less than 10 expected to have leading zero\nvar formattingMonthValues = {\n  narrow: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],\n  abbreviated: ['thg 1', 'thg 2', 'thg 3', 'thg 4', 'thg 5', 'thg 6', 'thg 7', 'thg 8', 'thg 9', 'thg 10', 'thg 11', 'thg 12'],\n  wide: ['tháng 01', 'tháng 02', 'tháng 03', 'tháng 04', 'tháng 05', 'tháng 06', 'tháng 07', 'tháng 08', 'tháng 09', 'tháng 10', 'tháng 11', 'tháng 12']\n};\nvar dayValues = {\n  narrow: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],\n  short: ['CN', 'Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7'],\n  abbreviated: ['CN', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'],\n  wide: ['Chủ Nhật', 'Thứ Hai', 'Thứ Ba', 'Thứ Tư', 'Thứ Năm', 'Thứ Sáu', 'Thứ Bảy']\n};\n\n// Vietnamese are used to AM/PM borrowing from English, hence `narrow` and\n// `abbreviated` are just like English but I'm leaving the `wide`\n// format being localized with abbreviations found in some systems (SÁng / CHiều);\n// however, personally, I don't think `Chiều` sounds appropriate for `PM`\nvar dayPeriodValues = {\n  // narrow date period is extremely rare in Vietnamese\n  // I used abbreviated form for noon, morning and afternoon\n  // which are regconizable by Vietnamese, others cannot be any shorter\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'nửa đêm',\n    noon: 'tr',\n    morning: 'sg',\n    afternoon: 'ch',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'nửa đêm',\n    noon: 'trưa',\n    morning: 'sáng',\n    afternoon: 'chiều',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  wide: {\n    am: 'SA',\n    pm: 'CH',\n    midnight: 'nửa đêm',\n    noon: 'trưa',\n    morning: 'sáng',\n    afternoon: 'chiều',\n    evening: 'tối',\n    night: 'đêm'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'nửa đêm',\n    noon: 'tr',\n    morning: 'sg',\n    afternoon: 'ch',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'nửa đêm',\n    noon: 'trưa',\n    morning: 'sáng',\n    afternoon: 'chiều',\n    evening: 'tối',\n    night: 'đêm'\n  },\n  wide: {\n    am: 'SA',\n    pm: 'CH',\n    midnight: 'nửa đêm',\n    noon: 'giữa trưa',\n    morning: 'vào buổi sáng',\n    afternoon: 'vào buổi chiều',\n    evening: 'vào buổi tối',\n    night: 'vào ban đêm'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (unit === 'quarter') {\n    // many news outlets use \"quý I\"...\n    switch (number) {\n      case 1:\n        return 'I';\n      case 2:\n        return 'II';\n      case 3:\n        return 'III';\n      case 4:\n        return 'IV';\n    }\n  } else if (unit === 'day') {\n    // day of week in Vietnamese has ordinal number meaning,\n    // so we should use them, else it'll sound weird\n    switch (number) {\n      case 1:\n        return 'thứ 2';\n      // meaning 2nd day but it's the first day of the week :D\n      case 2:\n        return 'thứ 3';\n      // meaning 3rd day\n      case 3:\n        return 'thứ 4';\n      // meaning 4th day and so on\n      case 4:\n        return 'thứ 5';\n      case 5:\n        return 'thứ 6';\n      case 6:\n        return 'thứ 7';\n      case 7:\n        return 'chủ nhật';\n      // meaning Sunday, there's no 8th day :D\n    }\n  } else if (unit === 'week') {\n    if (number === 1) {\n      return 'thứ nhất';\n    } else {\n      return 'thứ ' + number;\n    }\n  } else if (unit === 'dayOfYear') {\n    if (number === 1) {\n      return 'đầu tiên';\n    } else {\n      return 'thứ ' + number;\n    }\n  }\n\n  // there are no different forms of ordinal numbers in Vietnamese\n  return String(number);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC,CAAC,CAAC;AACtE;AACA,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACtBC,WAAW,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;EACnCC,IAAI,EAAE,CAAC,mBAAmB,EAAE,iBAAiB;AAC/C,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;AAC3C,CAAC;AACD,IAAIE,uBAAuB,GAAG;EAC5BJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrC;EACAC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIG,WAAW,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvEC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC5HC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB;AAC7K,CAAC;AACD;AACA,IAAII,qBAAqB,GAAG;EAC1BN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChFC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC5HC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AACvJ,CAAC;AACD,IAAIK,SAAS,GAAG;EACdP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAClDQ,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC7DP,WAAW,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACzEC,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;AACnF,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIO,eAAe,GAAG;EACpB;EACA;EACA;EACAT,MAAM,EAAE;IACNU,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BlB,MAAM,EAAE;IACNU,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,cAAc;IACvBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,IAAI,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI;EACzE,IAAIA,IAAI,KAAK,SAAS,EAAE;IACtB;IACA,QAAQF,MAAM;MACZ,KAAK,CAAC;QACJ,OAAO,GAAG;MACZ,KAAK,CAAC;QACJ,OAAO,IAAI;MACb,KAAK,CAAC;QACJ,OAAO,KAAK;MACd,KAAK,CAAC;QACJ,OAAO,IAAI;IACf;EACF,CAAC,MAAM,IAAIE,IAAI,KAAK,KAAK,EAAE;IACzB;IACA;IACA,QAAQF,MAAM;MACZ,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB;MACA,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB;MACA,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB;MACA,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB,KAAK,CAAC;QACJ,OAAO,UAAU;MACnB;IACF;EACF,CAAC,MAAM,IAAIE,IAAI,KAAK,MAAM,EAAE;IAC1B,IAAIF,MAAM,KAAK,CAAC,EAAE;MAChB,OAAO,UAAU;IACnB,CAAC,MAAM;MACL,OAAO,MAAM,GAAGA,MAAM;IACxB;EACF,CAAC,MAAM,IAAIE,IAAI,KAAK,WAAW,EAAE;IAC/B,IAAIF,MAAM,KAAK,CAAC,EAAE;MAChB,OAAO,UAAU;IACnB,CAAC,MAAM;MACL,OAAO,MAAM,GAAGA,MAAM;IACxB;EACF;;EAEA;EACA,OAAOG,MAAM,CAACH,MAAM,CAAC;AACvB,CAAC;AACD,IAAII,QAAQ,GAAG;EACbP,aAAa,EAAEA,aAAa;EAC5BQ,GAAG,EAAE7B,eAAe,CAAC;IACnB8B,MAAM,EAAE7B,SAAS;IACjB8B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAEhC,eAAe,CAAC;IACvB8B,MAAM,EAAEzB,aAAa;IACrB0B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE3B,uBAAuB;IACzC4B,sBAAsB,EAAE,MAAM;IAC9BC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACH,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFI,KAAK,EAAEpC,eAAe,CAAC;IACrB8B,MAAM,EAAEvB,WAAW;IACnBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAEzB,qBAAqB;IACvC0B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFG,GAAG,EAAErC,eAAe,CAAC;IACnB8B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAEtC,eAAe,CAAC;IACzB8B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAEb,yBAAyB;IAC3Cc,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}