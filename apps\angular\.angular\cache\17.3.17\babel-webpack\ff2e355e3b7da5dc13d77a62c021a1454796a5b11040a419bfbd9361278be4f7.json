{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\declarations\\models.ts"], "sourcesContent": ["import type { RelevantActivityType } from '../../shared/hosting/microservices/eto/declaration/base-classes/relevant-activity-type.enum';\r\nimport type { AuditedEntityDto, EntityDto } from '@abp/ng.core';\r\nimport type { ValidationResult } from '../../../../fluent-validation/results/models';\r\nimport type { DeclartionDocumentType } from '../declaration-imports/declartion-document-type.enum';\r\n\r\nexport interface Address {\r\n  addressLine1?: string;\r\n  addressLine2?: string;\r\n  country?: string;\r\n}\r\n\r\nexport interface AssessmentActivityDecisions {\r\n  relevantActivity: RelevantActivityType;\r\n  pass?: boolean;\r\n}\r\n\r\nexport interface AssessmentDetails {\r\n  assignedTo?: string;\r\n  assignedLevel: number;\r\n  relevantActivityDecisions: AssessmentActivityDecisions[];\r\n}\r\n\r\nexport interface CurrencyObject {\r\n  currency?: string;\r\n  currencyValue?: number;\r\n}\r\n\r\nexport interface DeclarationAssessmentDto extends EntityDto<string> {\r\n  tenantId?: string;\r\n  declarationTemplateId?: string;\r\n  statusLookupId?: string;\r\n  status?: string;\r\n  submittedDate?: string;\r\n  submittedById?: string;\r\n  submittedBy?: string;\r\n  periodEndYear?: number;\r\n  assessmentStatusId?: string;\r\n  assessmentStatus?: string;\r\n  actionEnforcedComments?: string;\r\n  actionEnforcedFileNames: string[];\r\n  informationRequestedArray: InformationRequested[];\r\n  surveyData: SurveyData;\r\n  assessmentDetails: AssessmentDetails;\r\n  isReopened?: boolean;\r\n  esLetterPath?: string;\r\n  isESLetterPublished?: boolean;\r\n}\r\n\r\nexport interface DeclarationDto extends EntityDto<string> {\r\n  statusLookupId?: string;\r\n  declarationTemplateId?: string;\r\n  status?: string;\r\n  submittedDate?: string;\r\n  submittedById?: string;\r\n  submittedBy?: string;\r\n  periodEndYear?: number;\r\n  actionEnforcedComments?: string;\r\n  actionEnforcedFileNames: string[];\r\n  informationRequestedArray: InformationRequested[];\r\n  surveyData: SurveyData;\r\n  isReopened?: boolean;\r\n  isESLetterPublished?: boolean;\r\n  lastSavedTime?: string;\r\n}\r\n\r\nexport interface DeclarationGetResult {\r\n  declarationDto: DeclarationDto;\r\n  validationResult: ValidationResult;\r\n}\r\n\r\nexport interface DeclarationHistoryDto extends AuditedEntityDto<string> {\r\n  tenantId?: string;\r\n  relatedDeclarationId?: string;\r\n  declarationContents: DeclarationDto;\r\n  submittedDate?: string;\r\n  submittedById?: string;\r\n  submittedBy?: string;\r\n  declarationStatusId?: string;\r\n}\r\n\r\nexport interface DeclarationHistoryListDto {\r\n  items: DeclarationHistoryListItemDto[];\r\n  totalCount: number;\r\n}\r\n\r\nexport interface DeclarationHistoryListItemDto {\r\n  id?: string;\r\n  submittedBy?: string;\r\n  submittedDate?: string;\r\n  statusId?: string;\r\n  declarationId?: string;\r\n  lastSavedTime?: string;\r\n}\r\n\r\nexport interface DeclarationSaveResult {\r\n  declarationId?: string;\r\n  validationResult: ValidationResult;\r\n}\r\n\r\nexport interface Director {\r\n  meetingNumber?: number;\r\n  name?: string;\r\n  physicallyPresent?: boolean;\r\n  qualification?: string;\r\n  yearsRelevantExperience?: number;\r\n}\r\n\r\nexport interface Employee {\r\n  name?: string;\r\n  qualification?: string;\r\n  yearsofRelevantExperience?: number;\r\n  contractType?: string;\r\n}\r\n\r\nexport interface File {\r\n  name?: string;\r\n  type?: string;\r\n  content?: string;\r\n}\r\n\r\nexport interface HoldingBusinessDetails {\r\n  holdingBussinessPartOfFinancialPeriod?: boolean;\r\n  holdingBussinessPartOfFinancialPeriodStartDate?: string;\r\n  holdingBussinessPartOfFinancialPeriodEndDate?: string;\r\n  holdingBussinessTotalGrossRelevantActivity: CurrencyObject[];\r\n  holdingBussinessNetBookValue?: number;\r\n  holdingBussinessDescAsset?: string;\r\n  holdingBussinessTotalExpenditureOperations: CurrencyObject[];\r\n  holdingBussinessTotalExpenditureBahamas: CurrencyObject[];\r\n  holdingBussinessTotalEmp?: number;\r\n  holdingBussinessTotalEmpEngaged?: number;\r\n  holdingBussinessTotalEmpEngagedInBahamas?: number;\r\n  holdingBussinessEmpQualifications: Employee[];\r\n  holdingBussinessAddresses: Address[];\r\n  holdingBussinessLawsAndRegulations?: boolean;\r\n}\r\n\r\nexport interface IPBusinessDetails {\r\n  intelPropPartOfFinancialPeriod?: boolean;\r\n  intelPropPartOfFinancialPeriodStartDate?: string;\r\n  intelPropPartOfFinancialPeriodEndDate?: string;\r\n  intelPropBusGrossIncome: CurrencyObject[];\r\n  intelPropBusNetBookValue?: number;\r\n  intelPropBusDescOfAssests?: string;\r\n  intelPropBusActivityDirectedManagedBahamas?: boolean;\r\n  intelPropBusDetailsPersonResponsibleRelevantActivity: Manager[];\r\n  intelPropBusNumberOfBoardMeetings?: number;\r\n  intelPropNumberOfBoardMeetingsInBahamas?: number;\r\n  intelPropBusQuorumForBoardMeetings?: number;\r\n  quorumOfDirectorsPhysicalPresentYN?: boolean;\r\n  intelPropBusBoardMeetingsInBahamas?: number;\r\n  intelPropBusListOfDirectorsForManagmentOfMeetings: Director[];\r\n  intelPropBusMinutesOfBoardMeetingsInBahamas?: boolean;\r\n  intelPropBusTotalExpenditureInOperations: CurrencyObject[];\r\n  intelPropBusTotalExpenditureInBahamas: CurrencyObject[];\r\n  intelPropBusTotalEmployAndCorporateLegalEntity?: number;\r\n  intelPropBusTotalEmployeesEngaged?: number;\r\n  intelPropBusTotalEngagedEmployeesInBahamas?: number;\r\n  intelPropBusEmployeeQualificaitions: Employee[];\r\n  intelPropBusAddressesOfPremises: Address[];\r\n  intelPropBusCIGAInBahamasForRelevantActivity: string[];\r\n  intelPropBusCIGAInBahamasForRelevantActivityComment?: string;\r\n  intelPropBusIsEntityHighRisk?: boolean;\r\n  intelPropBusRelevantIPHeld?: string;\r\n  intelPropBusHowIPAssetGenerateIncome?: string;\r\n  intelPropBusEmployeeResponsibleGenerationIncomeResponse?: string;\r\n  intelPropBusEmployeeResponsibleGenerationIncomeAttachment: File[];\r\n  intelPropBusStrategicDecisionsResponse?: string;\r\n  intelPropBusStrategicDecisionsAttachment: File[];\r\n  intelPropBusNatureOfTradingActivityResponse?: string;\r\n  intelPropBusNatureOfTradingActivityAttachment: File[];\r\n  highRiskGrossIncomeRoyalties: CurrencyObject[];\r\n  highRiskGrossIncomeThroughGains: CurrencyObject[];\r\n  highRiskGrossIncomeThroughOthers: CurrencyObject[];\r\n  highRiskBusinessPlanDetailsResponse?: string;\r\n  highRiskBusinessPlanDetailsAttachment: File[];\r\n  highRiskEvidenceDecisionMakingInBahamasResponse?: string;\r\n  highRiskEvidenceDecisionMakingInBahamasAttachment: File[];\r\n  highRiskOtherFactsResponse?: string;\r\n  highRiskOtherFactsAttachment: File[];\r\n  outsourcingIntellectualPropertyBusiness: IPOutsourcingDetails;\r\n}\r\n\r\nexport interface IPOutsourcingDetails {\r\n  outsourcingIncomeGeneratingActivity?: boolean;\r\n  outsourcingProportion?: number;\r\n  outSourcingProviderDetails: OutsourcingProvider[];\r\n  outsourcingTotalOutSourcingExpenditure: CurrencyObject[];\r\n}\r\n\r\nexport interface InformationRequested {\r\n  id?: string;\r\n  createdDate?: string;\r\n  dueDate?: string;\r\n  caComments?: string;\r\n  caAttachmentFileNames: string[];\r\n  raComments?: string;\r\n  raAttachmentFileNames: string[];\r\n  raSubmittedDate?: string;\r\n  raSubmittedById?: string;\r\n  raSubmittedBy?: string;\r\n  isSubmitted?: boolean;\r\n}\r\n\r\nexport interface InformationRequestedDto {\r\n  declarationId?: string;\r\n  informationRequestedId?: string;\r\n  comments?: string;\r\n  attachments: UploadDeclarationDocumentDto[];\r\n}\r\n\r\nexport interface Manager {\r\n  name?: string;\r\n  residentInIheBahamas?: boolean;\r\n  relationToTheEntity?: string;\r\n}\r\n\r\nexport interface OtherRelevantActivityDetails {\r\n  otherRelevantActivitiesPartOfFinancialPeriod?: boolean;\r\n  otherRelevantActivitiesPartOfFinancialPeriodStartDate?: string;\r\n  otherRelevantActivitiesPartOfFinancialPeriodEndDate?: string;\r\n  otherRelevantActivitiesTotalGrossIncome: CurrencyObject[];\r\n  otherRelevantActivitiesNetBookValue?: number;\r\n  otherRelevantActivitiesDescAsset?: string;\r\n  otherRelevantActivitiesIsDirectedInBahamas?: boolean;\r\n  otherRelevantActivitiesDetailsOfPersonsResponsible: Manager[];\r\n  otherRelevantActivitiesNumberOfBoardMeetings?: number;\r\n  otherRelevantActivitiesActivityInBahamas?: number;\r\n  otherRelevantActivitiesQuorumBoardMeetings?: number;\r\n  quorumOfDirectorsPhysicalPresentYN?: boolean;\r\n  otherRelevantActivitiesQuorumsDirectorsPresent?: number;\r\n  otherRelevantActivitiesListOfPersonsResponsible: Director[];\r\n  otherRelevantActivitiesMinutesBoardMeetingsInBahamas?: boolean;\r\n  otherRelevantActivitiesTotalExpenditureOperations: CurrencyObject[];\r\n  otherRelevantActivitiesTotalExpenditureBahamas: CurrencyObject[];\r\n  otherRelevantActivitiesTotalEmployees?: number;\r\n  otherRelevantActivitiesNumberEmployeesEngaged?: number;\r\n  otherRelevantActivitiesEmployeesPhysicallyPresent?: number;\r\n  otherRelevantActivitiesEmployeeQualifications: Employee[];\r\n  otherRelevantActivitiesAddressesOfPremises: Address[];\r\n  otherRelevantActivitiesCIGA: string[];\r\n  otherRelevantActivitiesCIGAComment?: string;\r\n  otherRelevantActivitiesAnyActivityOutSourced?: boolean;\r\n  otherRelevantActivitiesOutsourcingProportion?: number;\r\n  otherRelevantActivitiesDetailsOfOutSorucing: OutsourcingProvider[];\r\n  otherRelevantActivitiesOutsourcingExpenditure: CurrencyObject[];\r\n}\r\n\r\nexport interface OutsourcingProvider {\r\n  name?: string;\r\n  resourceDetails?: string;\r\n  numberOfStaff?: number;\r\n  isEntityMonitoringActivity?: boolean;\r\n  howIsEntityMonitoring?: string;\r\n  address?: string;\r\n}\r\n\r\nexport interface ParentEntity {\r\n  name?: string;\r\n  alternativeName?: string;\r\n  jurisdiction?: string;\r\n  incorporationNumber?: string;\r\n  identificationNumber?: string;\r\n}\r\n\r\nexport interface SurveyData {\r\n  financialPeriodChange?: boolean;\r\n  financialPeriodStartDate?: string;\r\n  financialPeriodEndDate?: string;\r\n  subjectToReclassification?: boolean;\r\n  esFiledOnOTAS?: boolean;\r\n  otasReceipt?: boolean;\r\n  otasReceiptUpload: File[];\r\n  entityId?: string;\r\n  entityDetailsTIN?: string;\r\n  entityDetailsAnnualIncome: CurrencyObject[];\r\n  entityDetailsBusinessSameAsRegisteredAddress?: boolean;\r\n  entityDetailsEnterDifferntBusinessAddress: Address[];\r\n  partOfMNEGroup?: boolean;\r\n  entityDetailsNameMNE?: string;\r\n  relevantActRelevantActivities: string[];\r\n  taxResidency100PercentBahamian?: boolean;\r\n  taxResidencyIsInvestmentFund?: boolean;\r\n  taxResidencyOutsideBahamas?: boolean;\r\n  taxResidencyJurisdictionEntityIsTaxResident?: string;\r\n  taxResidencyTaxpayerIDNumber?: string;\r\n  taxResidencyEvidenceOfTaxResidency: File[];\r\n  taxResidencyHasParent?: boolean;\r\n  taxResidencyUltimateParentEntityInfo: ParentEntity[];\r\n  hasImmediateParent?: boolean;\r\n  taxResidencyImmediateParentEntity: ParentEntity[];\r\n  intellectualPropertyBusiness: IPBusinessDetails;\r\n  holdingBusinessQuestions: HoldingBusinessDetails;\r\n  bankingQuestions: OtherRelevantActivityDetails;\r\n  insuranceQuestions: OtherRelevantActivityDetails;\r\n  fundManagmentQuestions: OtherRelevantActivityDetails;\r\n  financeQuestions: OtherRelevantActivityDetails;\r\n  headquartersQuestions: OtherRelevantActivityDetails;\r\n  shippingQuestions: OtherRelevantActivityDetails;\r\n  distributionQuestions: OtherRelevantActivityDetails;\r\n  comments?: string;\r\n  noActivityComments?: string;\r\n  supportingAttachment: File[];\r\n}\r\n\r\nexport interface UploadDeclarationDocumentDto {\r\n  uploadedDeclarationId?: string;\r\n  fileName?: string;\r\n  fileContents?: string;\r\n  docType: DeclartionDocumentType;\r\n  isImport: boolean;\r\n}\r\n\r\nexport interface AssessmentActionReturnDto {\r\n  declarationDto: DeclarationAssessmentDto;\r\n  internalNoteId?: string;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}