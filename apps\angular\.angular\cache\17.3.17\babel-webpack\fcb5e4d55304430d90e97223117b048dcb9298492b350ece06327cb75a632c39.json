{"ast": null, "code": "import { APP_INITIALIZER } from '@angular/core';\nimport { ManageProfileTabsService } from '../services/manage-profile-tabs.service';\nimport { PersonalSettingsComponent } from '../../../src/components';\nimport { ChangePasswordComponent } from '../../../src/components';\nexport const ACCOUNT_MANAGE_PROFILE_TAB_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureTabs,\n  deps: [ManageProfileTabsService],\n  multi: true\n}];\nexport function configureTabs(tabs) {\n  return () => {\n    tabs.add([\n    /*{\n      name: eAccountManageProfileTabNames.ProfilePicture,\n      order: 1,\n      component: null,\n    },*/\n    {\n      name: \"MY PROFILE\",\n      //eAccountManageProfileTabNames.PersonalInfo,\n      order: 2,\n      component: PersonalSettingsComponent\n    }, {\n      name: \"CHANGE PASSWORD\",\n      //eAccountManageProfileTabNames.ChangePassword,\n      order: 3,\n      component: ChangePasswordComponent\n    }, {\n      name: \"AbpAccount::ProfileTab:TwoFactor\" /* eAccountManageProfileTabNames.TwoFactor */,\n      order: 4,\n      component: null\n    }]);\n  };\n}", "map": {"version": 3, "names": ["APP_INITIALIZER", "ManageProfileTabsService", "PersonalSettingsComponent", "ChangePasswordComponent", "ACCOUNT_MANAGE_PROFILE_TAB_PROVIDERS", "provide", "useFactory", "configureTabs", "deps", "multi", "tabs", "add", "name", "order", "component"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\config\\src\\providers\\manage-profile-tab.provider.ts"], "sourcesContent": ["import { APP_INITIALIZER } from '@angular/core';\r\nimport { eAccountManageProfileTabNames } from '../enums/manage-profile-tab-names';\r\nimport { ManageProfileTabsService } from '../services/manage-profile-tabs.service';\r\nimport { PersonalSettingsComponent } from '../../../src/components';\r\nimport { ChangePasswordComponent } from '../../../src/components';\r\n\r\nexport const ACCOUNT_MANAGE_PROFILE_TAB_PROVIDERS = [\r\n  {\r\n    provide: APP_INITIALIZER,\r\n    useFactory: configureTabs,\r\n    deps: [ManageProfileTabsService],\r\n    multi: true,\r\n  },\r\n];\r\n\r\nexport function configureTabs(tabs: ManageProfileTabsService) {\r\n  return () => {\r\n    tabs.add([\r\n      /*{\r\n        name: eAccountManageProfileTabNames.ProfilePicture,\r\n        order: 1,\r\n        component: null,\r\n      },*/\r\n      {\r\n        name: \"MY PROFILE\", //eAccountManageProfileTabNames.PersonalInfo,\r\n        order: 2,\r\n        component: PersonalSettingsComponent,\r\n      },\r\n      {\r\n        name: \"CHANGE PASSWORD\",//eAccountManageProfileTabNames.ChangePassword,\r\n        order: 3,\r\n        component: ChangePasswordComponent,\r\n      },\r\n      {\r\n        name: eAccountManageProfileTabNames.TwoFactor,\r\n        order: 4,\r\n        component: null,\r\n      },\r\n    ]);\r\n  };\r\n}\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,eAAe;AAE/C,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,yBAAyB,QAAQ,yBAAyB;AACnE,SAASC,uBAAuB,QAAQ,yBAAyB;AAEjE,OAAO,MAAMC,oCAAoC,GAAG,CAClD;EACEC,OAAO,EAAEL,eAAe;EACxBM,UAAU,EAAEC,aAAa;EACzBC,IAAI,EAAE,CAACP,wBAAwB,CAAC;EAChCQ,KAAK,EAAE;CACR,CACF;AAED,OAAM,SAAUF,aAAaA,CAACG,IAA8B;EAC1D,OAAO,MAAK;IACVA,IAAI,CAACC,GAAG,CAAC;IACP;;;;;IAKA;MACEC,IAAI,EAAE,YAAY;MAAE;MACpBC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAEZ;KACZ,EACD;MACEU,IAAI,EAAE,iBAAiB;MAAC;MACxBC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAEX;KACZ,EACD;MACES,IAAI;MACJC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE;KACZ,CACF,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}