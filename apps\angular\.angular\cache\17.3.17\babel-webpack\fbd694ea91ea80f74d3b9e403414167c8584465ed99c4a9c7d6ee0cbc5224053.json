{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/is\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    var i = Math.floor(Math.abs(n)),\n      t = parseInt(n.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n    if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0)) return 1;\n    return 5;\n  }\n  exports.default = ['is', [['f.', 'e.'], ['f.h.', 'e.h.'], u], [['f.h.', 'e.h.'], u, u], [['S', 'M', 'Þ', 'M', 'F', 'F', 'L'], ['sun.', 'mán.', 'þri.', 'mið.', 'fim.', 'fös.', 'lau.'], ['sunnudagur', 'mánudagur', 'þriðjudagur', 'miðvikudagur', 'fimmtudagur', 'föstudagur', 'laugardagur'], ['su.', 'má.', 'þr.', 'mi.', 'fi.', 'fö.', 'la.']], u, [['J', 'F', 'M', 'A', 'M', 'J', 'J', 'Á', 'S', 'O', 'N', 'D'], ['jan.', 'feb.', 'mar.', 'apr.', 'maí', 'jún.', 'júl.', 'ágú.', 'sep.', 'okt.', 'nóv.', 'des.'], ['janúar', 'febrúar', 'mars', 'apríl', 'maí', 'júní', 'júlí', 'ágúst', 'september', 'október', 'nóvember', 'desember']], u, [['f.k.', 'e.k.'], ['f.Kr.', 'e.Kr.'], ['fyrir Krist', 'eftir Krist']], 1, [6, 0], ['d.M.y', 'd. MMM y', 'd. MMMM y', 'EEEE, d. MMMM y'], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1}, {0}', u, '{1} \\'kl\\'. {0}', u], [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'], 'ISK', 'ISK', 'íslensk króna', {\n    'AUD': [u, '$'],\n    'BRL': [u, 'R$'],\n    'CAD': [u, '$'],\n    'EUR': [u, '€'],\n    'GBP': [u, '£'],\n    'INR': [u, '₹'],\n    'JPY': ['JP¥', '¥'],\n    'KRW': [u, '₩'],\n    'MXN': [u, '$'],\n    'NZD': [u, '$'],\n    'TWD': [u, 'NT$'],\n    'USD': [u, '$'],\n    'VND': [u, '₫']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "i", "Math", "floor", "abs", "t", "parseInt", "toString", "replace", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/is.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/is\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        var i = Math.floor(Math.abs(n)), t = parseInt(n.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n        if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0))\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'is',\n        [['f.', 'e.'], ['f.h.', 'e.h.'], u],\n        [['f.h.', 'e.h.'], u, u],\n        [\n            ['S', 'M', 'Þ', 'M', 'F', 'F', 'L'], ['sun.', 'mán.', 'þri.', 'mið.', 'fim.', 'fös.', 'lau.'],\n            [\n                'sunnudagur', 'mánudagur', 'þriðjudagur', 'miðvikudagur', 'fimmtudagur', 'föstudagur',\n                'laugardagur'\n            ],\n            ['su.', 'má.', 'þr.', 'mi.', 'fi.', 'fö.', 'la.']\n        ],\n        u,\n        [\n            ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'Á', 'S', 'O', 'N', 'D'],\n            ['jan.', 'feb.', 'mar.', 'apr.', 'maí', 'jún.', 'júl.', 'ágú.', 'sep.', 'okt.', 'nóv.', 'des.'],\n            [\n                'janúar', 'febrúar', 'mars', 'apríl', 'maí', 'júní', 'júlí', 'ágúst', 'september', 'október',\n                'nóvember', 'desember'\n            ]\n        ],\n        u,\n        [['f.k.', 'e.k.'], ['f.Kr.', 'e.Kr.'], ['fyrir Krist', 'eftir Krist']],\n        1,\n        [6, 0],\n        ['d.M.y', 'd. MMM y', 'd. MMMM y', 'EEEE, d. MMMM y'],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1}, {0}', u, '{1} \\'kl\\'. {0}', u],\n        [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'],\n        'ISK',\n        'ISK',\n        'íslensk króna',\n        {\n            'AUD': [u, '$'],\n            'BRL': [u, 'R$'],\n            'CAD': [u, '$'],\n            'EUR': [u, '€'],\n            'GBP': [u, '£'],\n            'INR': [u, '₹'],\n            'JPY': ['JP¥', '¥'],\n            'KRW': [u, '₩'],\n            'MXN': [u, '$'],\n            'NZD': [u, '$'],\n            'TWD': [u, 'NT$'],\n            'USD': [u, '$'],\n            'VND': [u, '₫']\n        },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;MAAEK,CAAC,GAAGC,QAAQ,CAACN,CAAC,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;IAClG,IAAIH,CAAC,KAAK,CAAC,IAAIJ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,IAAI,EAAEI,CAAC,KAAK,CAAC,CAAC,EAC1D,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAhB,OAAO,CAACoB,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEX,CAAC,CAAC,EACnC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACxB,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAC7F,CACI,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EACrF,aAAa,CAChB,EACD,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CACpD,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAC/F,CACI,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAC5F,UAAU,EAAE,UAAU,CACzB,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,EACtE,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EACrD,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,UAAU,EAAEA,CAAC,EAAE,iBAAiB,EAAEA,CAAC,CAAC,EACrC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,KAAK,EACL,eAAe,EACf;IACI,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;EAClB,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}