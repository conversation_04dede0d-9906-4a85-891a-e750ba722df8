{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n// Licensed under the\nexport { Util, CorrelationIdHelper, DateTimeUtils, dateTimeUtilsNow, dateTimeUtilsDuration, UrlHelper, isInternalApplicationInsightsEndpoint, createDistributedTraceContextFromTrace } from \"./Util\";\nexport { ThrottleMgr } from \"./ThrottleMgr\";\nexport { parseConnectionString, ConnectionStringParser } from \"./ConnectionStringParser\";\nexport { RequestHeaders } from \"./RequestResponseHeaders\";\nexport { DisabledPropertyName, ProcessLegacy, SampleRate, HttpMethod, DEFAULT_BREEZE_ENDPOINT, DEFAULT_BREEZE_PATH, strNotSpecified } from \"./Constants\";\nexport { Envelope } from \"./Telemetry/Common/Envelope\";\nexport { Event } from \"./Telemetry/Event\";\nexport { Exception } from \"./Telemetry/Exception\";\nexport { Metric } from \"./Telemetry/Metric\";\nexport { PageView } from \"./Telemetry/PageView\";\nexport { RemoteDependencyData } from \"./Telemetry/RemoteDependencyData\";\nexport { Trace } from \"./Telemetry/Trace\";\nexport { PageViewPerformance } from \"./Telemetry/PageViewPerformance\";\nexport { Data } from \"./Telemetry/Common/Data\";\nexport { SeverityLevel } from \"./Interfaces/Contracts/SeverityLevel\";\nexport { ConfigurationManager } from \"./Interfaces/IConfig\";\nexport { ContextTagKeys } from \"./Interfaces/Contracts/ContextTagKeys\";\nexport { DataSanitizer, dataSanitizeKeyAndAddUniqueness, dataSanitizeKey, dataSanitizeString, dataSanitizeUrl, dataSanitizeMessage, dataSanitizeException, dataSanitizeProperties, dataSanitizeMeasurements, dataSanitizeId, dataSanitizeInput, dsPadNumber } from \"./Telemetry/Common/DataSanitizer\";\nexport { TelemetryItemCreator, createTelemetryItem } from \"./TelemetryItemCreator\";\nexport { CtxTagKeys, Extensions } from \"./Interfaces/PartAExtensions\";\nexport { DistributedTracingModes } from \"./Enums\";\nexport { stringToBoolOrDefault, msToTimeSpan, getExtensionByName, isCrossOriginError } from \"./HelperFuncs\";\nexport { isBeaconsSupported as isBeaconApiSupported, createTraceParent, parseTraceParent, isValidTraceId, isValidSpanId, isValidTraceParent, isSampledFlag, formatTraceParent, findW3cTraceParent } from \"@microsoft/applicationinsights-core-js\";\nexport { createDomEvent } from \"./DomHelperFuncs\";\nexport { utlDisableStorage, utlEnableStorage, utlCanUseLocalStorage, utlGetLocalStorage, utlSetLocalStorage, utlRemoveStorage, utlCanUseSessionStorage, utlGetSessionStorageKeys, utlGetSessionStorage, utlSetSessionStorage, utlRemoveSessionStorage, utlSetStoragePrefix } from \"./StorageHelperFuncs\";\nexport { urlParseUrl, urlGetAbsoluteUrl, urlGetPathName, urlGetCompleteUrl, urlParseHost, urlParseFullHost } from \"./UrlHelperFuncs\";\nexport var PropertiesPluginIdentifier = \"AppInsightsPropertiesPlugin\";\nexport var BreezeChannelIdentifier = \"AppInsightsChannelPlugin\";\nexport var AnalyticsPluginIdentifier = \"ApplicationInsightsAnalytics\";", "map": {"version": 3, "names": ["<PERSON><PERSON>", "CorrelationIdHelper", "DateTimeUtils", "dateTimeUtilsNow", "dateTimeUtilsDuration", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isInternalApplicationInsightsEndpoint", "createDistributedTraceContextFromTrace", "ThrottleMgr", "parseConnectionString", "ConnectionStringParser", "RequestHeaders", "DisabledPropertyName", "ProcessLegacy", "SampleRate", "HttpMethod", "DEFAULT_BREEZE_ENDPOINT", "DEFAULT_BREEZE_PATH", "strNotSpecified", "Envelope", "Event", "Exception", "Metric", "<PERSON><PERSON><PERSON><PERSON>", "RemoteDependencyData", "Trace", "PageViewPerformance", "Data", "SeverityLevel", "ConfigurationManager", "ContextTagKeys", "DataSanitizer", "dataSanitizeKeyAndAddUniqueness", "dataSanitizeKey", "dataSanitizeString", "dataSanitizeUrl", "dataSanitizeMessage", "dataSanitizeException", "dataSanitizeProperties", "dataSanitizeMeasurements", "dataSanitizeId", "dataSanitizeInput", "dsPadNumber", "TelemetryItemCreator", "createTelemetryItem", "CtxTagKeys", "Extensions", "DistributedTracingModes", "stringToBoolOrDefault", "msToTimeSpan", "getExtensionByName", "isCrossOriginError", "isBeaconsSupported", "isBeaconApiSupported", "createTraceParent", "parseTraceParent", "isValidTraceId", "isValidSpanId", "isValidTraceParent", "isSampledFlag", "formatTraceParent", "findW3cTraceParent", "createDomEvent", "utlDisableStorage", "utlEnableStorage", "utlCanUseLocalStorage", "utlGetLocalStorage", "utlSetLocalStorage", "utlRemoveStorage", "utlCanUseSessionStorage", "utlGetSessionStorageKeys", "utlGetSessionStorage", "utlSetSessionStorage", "utlRemoveSessionStorage", "utlSetStoragePrefix", "urlParseUrl", "urlGetAbsoluteUrl", "urlGetPathName", "urlGetCompleteUrl", "urlParseHost", "urlParseFullHost", "PropertiesPluginIdentifier", "BreezeChannelIdentifier", "AnalyticsPluginIdentifier"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/applicationinsights-common.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n// Licensed under the\r\nexport { Util, CorrelationIdHelper, DateTimeUtils, dateTimeUtilsNow, dateTimeUtilsDuration, UrlHelper, isInternalApplicationInsightsEndpoint, createDistributedTraceContextFromTrace } from \"./Util\";\r\nexport { ThrottleMgr } from \"./ThrottleMgr\";\r\nexport { parseConnectionString, ConnectionStringParser } from \"./ConnectionStringParser\";\r\nexport { RequestHeaders } from \"./RequestResponseHeaders\";\r\nexport { DisabledPropertyName, ProcessLegacy, SampleRate, HttpMethod, DEFAULT_BREEZE_ENDPOINT, DEFAULT_BREEZE_PATH, strNotSpecified } from \"./Constants\";\r\nexport { Envelope } from \"./Telemetry/Common/Envelope\";\r\nexport { Event } from \"./Telemetry/Event\";\r\nexport { Exception } from \"./Telemetry/Exception\";\r\nexport { Metric } from \"./Telemetry/Metric\";\r\nexport { PageView } from \"./Telemetry/PageView\";\r\nexport { RemoteDependencyData } from \"./Telemetry/RemoteDependencyData\";\r\nexport { Trace } from \"./Telemetry/Trace\";\r\nexport { PageViewPerformance } from \"./Telemetry/PageViewPerformance\";\r\nexport { Data } from \"./Telemetry/Common/Data\";\r\nexport { SeverityLevel } from \"./Interfaces/Contracts/SeverityLevel\";\r\nexport { ConfigurationManager } from \"./Interfaces/IConfig\";\r\nexport { ContextTagKeys } from \"./Interfaces/Contracts/ContextTagKeys\";\r\nexport { DataSanitizer, dataSanitizeKeyAndAddUniqueness, dataSanitizeKey, dataSanitizeString, dataSanitizeUrl, dataSanitizeMessage, dataSanitizeException, dataSanitizeProperties, dataSanitizeMeasurements, dataSanitizeId, dataSanitizeInput, dsPadNumber } from \"./Telemetry/Common/DataSanitizer\";\r\nexport { TelemetryItemCreator, createTelemetryItem } from \"./TelemetryItemCreator\";\r\nexport { CtxTagKeys, Extensions } from \"./Interfaces/PartAExtensions\";\r\nexport { DistributedTracingModes } from \"./Enums\";\r\nexport { stringToBoolOrDefault, msToTimeSpan, getExtensionByName, isCrossOriginError } from \"./HelperFuncs\";\r\nexport { isBeaconsSupported as isBeaconApiSupported, createTraceParent, parseTraceParent, isValidTraceId, isValidSpanId, isValidTraceParent, isSampledFlag, formatTraceParent, findW3cTraceParent } from \"@microsoft/applicationinsights-core-js\";\r\nexport { createDomEvent } from \"./DomHelperFuncs\";\r\nexport { utlDisableStorage, utlEnableStorage, utlCanUseLocalStorage, utlGetLocalStorage, utlSetLocalStorage, utlRemoveStorage, utlCanUseSessionStorage, utlGetSessionStorageKeys, utlGetSessionStorage, utlSetSessionStorage, utlRemoveSessionStorage, utlSetStoragePrefix } from \"./StorageHelperFuncs\";\r\nexport { urlParseUrl, urlGetAbsoluteUrl, urlGetPathName, urlGetCompleteUrl, urlParseHost, urlParseFullHost } from \"./UrlHelperFuncs\";\r\nexport var PropertiesPluginIdentifier = \"AppInsightsPropertiesPlugin\";\r\nexport var BreezeChannelIdentifier = \"AppInsightsChannelPlugin\";\r\nexport var AnalyticsPluginIdentifier = \"ApplicationInsightsAnalytics\";\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAI,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,qCAAqC,EAAEC,sCAAsC,QAAQ,QAAQ;AACpM,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,qBAAqB,EAAEC,sBAAsB,QAAQ,0BAA0B;AACxF,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,oBAAoB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,aAAa;AACxJ,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,aAAa,EAAEC,+BAA+B,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,kCAAkC;AACrS,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,wBAAwB;AAClF,SAASC,UAAU,EAAEC,UAAU,QAAQ,8BAA8B;AACrE,SAASC,uBAAuB,QAAQ,SAAS;AACjD,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,kBAAkB,QAAQ,eAAe;AAC3G,SAASC,kBAAkB,IAAIC,oBAAoB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,kBAAkB,QAAQ,wCAAwC;AACjP,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,uBAAuB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACxS,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,kBAAkB;AACpI,OAAO,IAAIC,0BAA0B,GAAG,6BAA6B;AACrE,OAAO,IAAIC,uBAAuB,GAAG,0BAA0B;AAC/D,OAAO,IAAIC,yBAAyB,GAAG,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}