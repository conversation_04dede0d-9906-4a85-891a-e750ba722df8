{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"yo-BJ\", [[\"Àárɔ̀\", \"Ɔ̀sán\"], u, u], u, [[\"À\", \"A\", \"Ì\", \"Ɔ\", \"Ɔ\", \"Ɛ\", \"À\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"], [\"Ɔjɔ́ Àìkú\", \"Ɔjɔ́ Ajé\", \"Ɔjɔ́ Ìsɛ́gun\", \"Ɔjɔ́rú\", \"Ɔjɔ́bɔ\", \"Ɔjɔ́ Ɛtì\", \"Ɔjɔ́ Àbámɛ́ta\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"]], [[\"À\", \"A\", \"Ì\", \"Ɔ\", \"Ɔ\", \"Ɛ\", \"À\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"], [\"Àìkú\", \"Ajé\", \"Ìsɛ́gun\", \"Ɔjɔ́rú\", \"Ɔjɔ́bɔ\", \"Ɛtì\", \"Àbámɛ́ta\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"]], [[\"S\", \"È\", \"Ɛ\", \"Ì\", \"Ɛ̀\", \"Ò\", \"A\", \"Ò\", \"O\", \"Ɔ̀\", \"B\", \"Ɔ̀\"], [\"Shɛ́r\", \"Èrèl\", \"Ɛrɛ̀n\", \"Ìgb\", \"Ɛ̀bi\", \"Òkú\", \"Agɛ\", \"Ògú\", \"Owe\", \"Ɔ̀wà\", \"Bél\", \"Ɔ̀pɛ\"], [\"Oshù Shɛ́rɛ́\", \"Oshù Èrèlè\", \"Oshù Ɛrɛ̀nà\", \"Oshù Ìgbé\", \"Oshù Ɛ̀bibi\", \"Oshù Òkúdu\", \"Oshù Agɛmɔ\", \"Oshù Ògún\", \"Oshù Owewe\", \"Oshù Ɔ̀wàrà\", \"Oshù Bélú\", \"Oshù Ɔ̀pɛ̀\"]], [[\"S\", \"È\", \"Ɛ\", \"Ì\", \"Ɛ̀\", \"Ò\", \"A\", \"Ò\", \"O\", \"Ɔ̀\", \"B\", \"Ɔ̀\"], [\"Shɛ́\", \"Èr\", \"Ɛr\", \"Ìg\", \"Ɛ̀b\", \"Òk\", \"Ag\", \"Òg\", \"Ow\", \"Ɔ̀w\", \"Bé\", \"Ɔ̀p\"], [\"Shɛ́rɛ́\", \"Èrèlè\", \"Ɛrɛ̀nà\", \"Ìgbé\", \"Ɛ̀bibi\", \"Òkúdu\", \"Agɛmɔ\", \"Ògún\", \"Owewe\", \"Ɔ̀wàrà\", \"Bélú\", \"Ɔ̀pɛ̀\"]], [[\"BCE\", \"AD\"], u, [\"Saju Kristi\", \"Lehin Kristi\"]], 1, [6, 0], [\"d/M/y\", \"d MM y\", \"d MMM y\", \"EEEE, d MMM y\"], [\"H:m\", \"H:m:s\", \"H:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"XOF\", \"F CFA\", \"Faransì ìwɔ̀-oorùn Afíríkà\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"NGN\": [\"₦\"],\n  \"RUB\": [\"₽\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/yo-BJ.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"yo-BJ\", [[\"Àárɔ̀\", \"Ɔ̀sán\"], u, u], u, [[\"À\", \"A\", \"Ì\", \"Ɔ\", \"Ɔ\", \"Ɛ\", \"À\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"], [\"Ɔjɔ́ Àìkú\", \"Ɔjɔ́ Ajé\", \"Ɔjɔ́ Ìsɛ́gun\", \"Ɔjɔ́rú\", \"Ɔjɔ́bɔ\", \"Ɔjɔ́ Ɛtì\", \"Ɔjɔ́ Àbámɛ́ta\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"]], [[\"À\", \"A\", \"Ì\", \"Ɔ\", \"Ɔ\", \"Ɛ\", \"À\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"], [\"Àìkú\", \"Ajé\", \"Ìsɛ́gun\", \"Ɔjɔ́rú\", \"Ɔjɔ́bɔ\", \"Ɛtì\", \"Àbámɛ́ta\"], [\"Àìk\", \"Aj\", \"Ìsɛ́g\", \"Ɔjɔ́r\", \"Ɔjɔ́b\", \"Ɛt\", \"Àbám\"]], [[\"S\", \"È\", \"Ɛ\", \"Ì\", \"Ɛ̀\", \"Ò\", \"A\", \"Ò\", \"O\", \"Ɔ̀\", \"B\", \"Ɔ̀\"], [\"Shɛ́r\", \"Èrèl\", \"Ɛrɛ̀n\", \"Ìgb\", \"Ɛ̀bi\", \"Òkú\", \"Agɛ\", \"Ògú\", \"Owe\", \"Ɔ̀wà\", \"Bél\", \"Ɔ̀pɛ\"], [\"Oshù Shɛ́rɛ́\", \"Oshù Èrèlè\", \"Oshù Ɛrɛ̀nà\", \"Oshù Ìgbé\", \"Oshù Ɛ̀bibi\", \"Oshù Òkúdu\", \"Oshù Agɛmɔ\", \"Oshù Ògún\", \"Oshù Owewe\", \"Oshù Ɔ̀wàrà\", \"Oshù Bélú\", \"Oshù Ɔ̀pɛ̀\"]], [[\"S\", \"È\", \"Ɛ\", \"Ì\", \"Ɛ̀\", \"Ò\", \"A\", \"Ò\", \"O\", \"Ɔ̀\", \"B\", \"Ɔ̀\"], [\"Shɛ́\", \"Èr\", \"Ɛr\", \"Ìg\", \"Ɛ̀b\", \"Òk\", \"Ag\", \"Òg\", \"Ow\", \"Ɔ̀w\", \"Bé\", \"Ɔ̀p\"], [\"Shɛ́rɛ́\", \"Èrèlè\", \"Ɛrɛ̀nà\", \"Ìgbé\", \"Ɛ̀bibi\", \"Òkúdu\", \"Agɛmɔ\", \"Ògún\", \"Owewe\", \"Ɔ̀wàrà\", \"Bélú\", \"Ɔ̀pɛ̀\"]], [[\"BCE\", \"AD\"], u, [\"Saju Kristi\", \"Lehin Kristi\"]], 1, [6, 0], [\"d/M/y\", \"d MM y\", \"d MMM y\", \"EEEE, d MMM y\"], [\"H:m\", \"H:m:s\", \"H:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"XOF\", \"F CFA\", \"Faransì ìwɔ̀-oorùn Afíríkà\", { \"JPY\": [\"JP¥\", \"¥\"], \"NGN\": [\"₦\"], \"RUB\": [\"₽\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,4BAA4B,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}