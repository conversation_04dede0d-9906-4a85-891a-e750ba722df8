{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SearchResultLegendComponent {\n  static {\n    this.ɵfac = function SearchResultLegendComponent_Factory(t) {\n      return new (t || SearchResultLegendComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchResultLegendComponent,\n      selectors: [[\"app-search-result-legend\"]],\n      decls: 13,\n      vars: 0,\n      consts: [[1, \"legend\", \"mat-elevation-z8\"], [1, \"status-not-started\"], [1, \"status-draft\"], [1, \"status-reopened\"], [1, \"status-submitted\"], [1, \"status-resubmitted\"], [1, \"status-deleted\"]],\n      template: function SearchResultLegendComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0)(1, \"li\", 1);\n          i0.ɵɵtext(2, \"Not Started\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"li\", 2);\n          i0.ɵɵtext(4, \"Draft\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"li\", 3);\n          i0.ɵɵtext(6, \"Reopened\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 4);\n          i0.ɵɵtext(8, \"Submitted\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"li\", 5);\n          i0.ɵɵtext(10, \"Resubmitted\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"li\", 6);\n          i0.ɵɵtext(12, \"Deleted\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".legend[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  min-width: 9em;\\n  max-width: 9em;\\n  min-height: 13em;\\n  display: inline-block;\\n  list-style: none;\\n  padding-left: 1em;\\n}\\n\\n.legend[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%] {\\n  font-weight: bolder;\\n  margin-top: 0.25em;\\n  margin-bottom: 0.25em;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNlYXJjaC1yZXN1bHQtbGVnZW5kLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0ksc0JBQUE7RUFDQSxjQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0FBQUo7O0FBRUE7RUFDSSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUE7QUFDSiIsImZpbGUiOiJzZWFyY2gtcmVzdWx0LWxlZ2VuZC5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG4ubGVnZW5ke1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgIG1pbi13aWR0aDogOWVtO1xyXG4gICAgbWF4LXdpZHRoOiA5ZW07XHJcbiAgICBtaW4taGVpZ2h0OiAxM2VtO1xyXG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gICAgbGlzdC1zdHlsZTogbm9uZTtcclxuICAgIHBhZGRpbmctbGVmdDogMWVtO1xyXG59XHJcbi5sZWdlbmQgPiBsaXtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkZXI7XHJcbiAgICBtYXJnaW4tdG9wOiAwLjI1ZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwLjI1ZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvc2VhcmNoLXJlc3VsdC9jb250YWluZXJzL3NlYXJjaC1yZXN1bHQtbGVnZW5kL3NlYXJjaC1yZXN1bHQtbGVnZW5kLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0ksc0JBQUE7RUFDQSxjQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0FBQUo7O0FBRUE7RUFDSSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUE7QUFDSjtBQUNBLHcwQkFBdzBCIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi5sZWdlbmR7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgbWluLXdpZHRoOiA5ZW07XHJcbiAgICBtYXgtd2lkdGg6IDllbTtcclxuICAgIG1pbi1oZWlnaHQ6IDEzZW07XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICBsaXN0LXN0eWxlOiBub25lO1xyXG4gICAgcGFkZGluZy1sZWZ0OiAxZW07XHJcbn1cclxuLmxlZ2VuZCA+IGxpe1xyXG4gICAgZm9udC13ZWlnaHQ6IGJvbGRlcjtcclxuICAgIG1hcmdpbi10b3A6IDAuMjVlbTtcclxuICAgIG1hcmdpbi1ib3R0b206IDAuMjVlbTtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SearchResultLegendComponent", "selectors", "decls", "vars", "consts", "template", "SearchResultLegendComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\search-result\\containers\\search-result-legend\\search-result-legend.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\search-result\\containers\\search-result-legend\\search-result-legend.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-search-result-legend',\r\n  templateUrl: './search-result-legend.component.html',\r\n  styleUrls: ['./search-result-legend.component.scss']\r\n})\r\nexport class SearchResultLegendComponent {\r\n\r\n}\r\n", "<ul class=\"legend mat-elevation-z8\">\r\n    <li class=\"status-not-started\">Not Started</li>\r\n    <li class=\"status-draft\">Draft</li>\r\n    <li class=\"status-reopened\">Reopened</li>\r\n    <li class=\"status-submitted\">Submitted</li>\r\n    <li class=\"status-resubmitted\">Resubmitted</li>\r\n    <li class=\"status-deleted\">Deleted</li>\r\n</ul>"], "mappings": ";AAOA,OAAM,MAAOA,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA,2BAA2B;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNpCE,EADJ,CAAAC,cAAA,YAAoC,YACD;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAC,cAAA,YAA4B;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAC,cAAA,YAA+B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}