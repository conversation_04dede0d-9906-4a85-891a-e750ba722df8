{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\n/** Rendering \" of red flags summary stats\n *  */\nexport let RedFlagsSummaryComponent = /*#__PURE__*/(() => {\n  class RedFlagsSummaryComponent extends AppComponentBase {\n    constructor(injector) {\n      super(injector);\n    }\n    ngOnChanges(changes) {\n      if (changes.statsSummaryData && this.statsSummaryData) {\n        console.log(this.statsSummaryData);\n      }\n    }\n    ngOnInit() {}\n    static {\n      this.ɵfac = function RedFlagsSummaryComponent_Factory(t) {\n        return new (t || RedFlagsSummaryComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RedFlagsSummaryComponent,\n        selectors: [[\"app-red-flags-summary\"]],\n        inputs: {\n          statsSummaryData: \"statsSummaryData\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n        decls: 42,\n        vars: 9,\n        consts: [[1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"]],\n        template: function RedFlagsSummaryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n            i0.ɵɵtext(2, \"Red Flag Events Summary:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\")(4, \"div\", 1)(5, \"div\", 2);\n            i0.ɵɵtext(6, \"Total Events\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 2);\n            i0.ɵɵtext(8, \"Total Filings with Events\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 2);\n            i0.ɵɵtext(10, \"Total Assessment Not Started\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 2);\n            i0.ɵɵtext(12, \"% Of Assessment Not started\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 2);\n            i0.ɵɵtext(14, \"Total Assessment Completed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 2);\n            i0.ɵɵtext(16, \"% Of Assessment Passed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 2);\n            i0.ɵɵtext(18, \"% Of Assessment Failed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 2);\n            i0.ɵɵtext(20, \"Total Assessment Closed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 2);\n            i0.ɵɵtext(22, \"% Of Assessment Closed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 1)(24, \"div\", 3);\n            i0.ɵɵtext(25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 3);\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 3);\n            i0.ɵɵtext(29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 3);\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 3);\n            i0.ɵɵtext(33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 3);\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\", 3);\n            i0.ɵɵtext(37);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"div\", 3);\n            i0.ɵɵtext(39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 3);\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(25);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalEvents);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalFilingWithEvents);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalAssessmentNotStarted);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentNotStarted, \"%\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalAssessmentCompleted);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentPassed, \"%\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentFailed, \"%\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalAssessmentClosed);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentClosed, \"%\");\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return RedFlagsSummaryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}