{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"th\", [[\"a\", \"p\"], [\"ก่อนเที่ยง\", \"หลังเที่ยง\"], u], [[\"ก่อนเที่ยง\", \"หลังเที่ยง\"], u, u], [[\"อา\", \"จ\", \"อ\", \"พ\", \"พฤ\", \"ศ\", \"ส\"], [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"], [\"วันอาทิตย์\", \"วันจันทร์\", \"วันอังคาร\", \"วันพุธ\", \"วันพฤหัสบดี\", \"วันศุกร์\", \"วันเสาร์\"], [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"]], u, [[\"ม.ค.\", \"ก.พ.\", \"มี.ค.\", \"เม.ย.\", \"พ.ค.\", \"มิ.ย.\", \"ก.ค.\", \"ส.ค.\", \"ก.ย.\", \"ต.ค.\", \"พ.ย.\", \"ธ.ค.\"], u, [\"มกราคม\", \"กุมภาพันธ์\", \"มีนาคม\", \"เมษายน\", \"พฤษภาคม\", \"มิถุนายน\", \"กรกฎาคม\", \"สิงหาคม\", \"กันยายน\", \"ตุลาคม\", \"พฤศจิกายน\", \"ธันวาคม\"]], u, [[\"ก่อน ค.ศ.\", \"ค.ศ.\"], u, [\"ปีก่อนคริสตกาล\", \"คริสต์ศักราช\"]], 0, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM G y\", \"EEEEที่ d MMMM G y\"], [\"HH:mm\", \"HH:mm:ss\", \"H นาฬิกา mm นาที ss วินาที z\", \"H นาฬิกา mm นาที ss วินาที zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"THB\", \"฿\", \"บาท\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"XXX\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/th.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"th\", [[\"a\", \"p\"], [\"ก่อนเที่ยง\", \"หลังเที่ยง\"], u], [[\"ก่อนเที่ยง\", \"หลังเที่ยง\"], u, u], [[\"อา\", \"จ\", \"อ\", \"พ\", \"พฤ\", \"ศ\", \"ส\"], [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"], [\"วันอาทิตย์\", \"วันจันทร์\", \"วันอังคาร\", \"วันพุธ\", \"วันพฤหัสบดี\", \"วันศุกร์\", \"วันเสาร์\"], [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"]], u, [[\"ม.ค.\", \"ก.พ.\", \"มี.ค.\", \"เม.ย.\", \"พ.ค.\", \"มิ.ย.\", \"ก.ค.\", \"ส.ค.\", \"ก.ย.\", \"ต.ค.\", \"พ.ย.\", \"ธ.ค.\"], u, [\"มกราคม\", \"กุมภาพันธ์\", \"มีนาคม\", \"เมษายน\", \"พฤษภาคม\", \"มิถุนายน\", \"กรกฎาคม\", \"สิงหาคม\", \"กันยายน\", \"ตุลาคม\", \"พฤศจิกายน\", \"ธันวาคม\"]], u, [[\"ก่อน ค.ศ.\", \"ค.ศ.\"], u, [\"ปีก่อนคริสตกาล\", \"คริสต์ศักราช\"]], 0, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM G y\", \"EEEEที่ d MMMM G y\"], [\"HH:mm\", \"HH:mm:ss\", \"H นาฬิกา mm นาที ss วินาที z\", \"H นาฬิกา mm นาที ss วินาที zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"THB\", \"฿\", \"บาท\", { \"AUD\": [\"AU$\", \"$\"], \"BYN\": [u, \"р.\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"], \"XXX\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,EAAEH,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,8BAA8B,EAAE,iCAAiC,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}