{"ast": null, "code": "import { BasicSearchComponent } from './container';\nimport { SharedModule } from '@app/shared/shared.module';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class BasicSearchModule {\n  static {\n    this.ɵfac = function BasicSearchModule_Factory(t) {\n      return new (t || BasicSearchModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BasicSearchModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BasicSearchModule, {\n    declarations: [BasicSearchComponent],\n    imports: [SharedModule, CommonModule],\n    exports: [BasicSearchComponent]\n  });\n})();", "map": {"version": 3, "names": ["BasicSearchComponent", "SharedModule", "CommonModule", "BasicSearchModule", "declarations", "imports", "exports"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\basic-search\\basic-search.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BasicSearchComponent } from './container';\r\nimport { SharedModule } from '@app/shared/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\n@NgModule({\r\n\r\n    imports: [\r\n        SharedModule,\r\n        CommonModule\r\n    ],\r\n\r\n    declarations: [\r\n        BasicSearchComponent\r\n    ],  \r\n    exports: [\r\n        BasicSearchComponent\r\n    ]\r\n})\r\nexport class BasicSearchModule { }\r\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,aAAa;AAClD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,iBAAiB;;AAe9C,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAXtBF,YAAY,EACZC,YAAY;IAAA;EAAA;;;2EAUPC,iBAAiB;IAAAC,YAAA,GANtBJ,oBAAoB;IAAAK,OAAA,GALpBJ,YAAY,EACZC,YAAY;IAAAI,OAAA,GAOZN,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}