{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ReplaySubject } from 'rxjs';\nimport { ContextMenuComponent } from '@volosoft/ngx-lepton-x';\nimport { UntilDestroy } from '@ngneat/until-destroy';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../toolbar\";\nimport * as i2 from \"@volo/ngx-lepton-x.core\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@volosoft/ngx-lepton-x\";\nimport * as i5 from \"../toolbar/toolbar.component\";\nconst _c0 = (a0, a1) => ({\n  user: a0,\n  profileRef: a1\n});\nconst _c1 = a0 => [a0];\nfunction BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tenantName_r3 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tenantName_r3, \" \");\n  }\n}\nfunction BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"lpx-context-menu-action-group\");\n    i0.ɵɵelement(2, \"lpx-navbar-routes\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const actions_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"navbarItems\", actions_r4)(\"routerItem\", false);\n  }\n}\nfunction BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"lpx-context-menu\", 4, 0);\n    i0.ɵɵlistener(\"lpxClickOutside\", function BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_Template_lpx_context_menu_lpxClickOutside_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const menu_r2 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(menu_r2.close());\n    });\n    i0.ɵɵelementStart(2, \"lpx-context-menu-header\")(3, \"div\", 5)(4, \"div\", 6);\n    i0.ɵɵelement(5, \"lpx-avatar\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"span\", 9);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_span_9_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementStart(10, \"span\", 11);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(12, BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_ng_container_12_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r5 = i0.ɵɵnextContext().ngIf;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"exceptedRefs\", i0.ɵɵpureFunction1(6, _c1, data_r5.profileRef));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"avatar\", data_r5.user == null ? null : data_r5.user.avatar);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(data_r5.user == null ? null : data_r5.user.userName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", data_r5.user == null ? null : data_r5.user.tenant == null ? null : data_r5.user.tenant.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r5.user == null ? null : data_r5.user.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.userActionGroups);\n  }\n}\nfunction BdoToolbarContainerComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, BdoToolbarContainerComponent_ng_container_1_lpx_context_menu_1_Template, 13, 8, \"lpx-context-menu\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const data_r5 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", data_r5.profileRef);\n  }\n}\nlet BdoToolbarContainerComponent = class BdoToolbarContainerComponent {\n  constructor(toolbarService, userProfileService, cdRef) {\n    this.toolbarService = toolbarService;\n    this.userProfileService = userProfileService;\n    this.cdRef = cdRef;\n    this.profileRef$ = new ReplaySubject(1);\n    this.userActionGroups = [];\n  }\n  ngOnInit() {\n    this.userProfileService.user$.subscribe(user => {\n      /** NOTE: we're going to override the toolbar (right-side) and remove entries */\n      this.userActionGroups = [...user.userActionGroups];\n      this.userActionGroups[0] = [...this.userActionGroups[0].filter(uag => uag.text !== 'AbpAccount::AuthorityDelegation')];\n      this.userActionGroups[0] = [...this.userActionGroups[0].filter(uag => uag.text !== 'AbpAccount::LinkedAccounts')];\n      this.userActionGroups[0] = [...this.userActionGroups[0].filter(uag => uag.text !== 'AbpAccount::MySecurityLogs')];\n      /** Multiple entries for myAccount, remove all and re-add one */\n      const myAccount = this.userActionGroups[0].filter(uag => uag.text === 'AbpAccount::MyAccount');\n      this.userActionGroups[0] = [...this.userActionGroups[0].filter(uag => uag.text !== 'AbpAccount::MyAccount')];\n      this.userActionGroups[0] = [myAccount[0], ...this.userActionGroups[0]];\n      // this.userActionGroups = [this.userActionGroups[0]];\n      this.cdRef.detectChanges();\n    });\n  }\n  toggleCtxMenu() {\n    this.ctxMenu.toggle();\n  }\n  static {\n    this.ɵfac = function BdoToolbarContainerComponent_Factory(t) {\n      return new (t || BdoToolbarContainerComponent)(i0.ɵɵdirectiveInject(i1.ToolbarService), i0.ɵɵdirectiveInject(i2.UserProfileService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BdoToolbarContainerComponent,\n      selectors: [[\"app-bdo-toolbar-container\"]],\n      viewQuery: function BdoToolbarContainerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ContextMenuComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ctxMenu = _t.first);\n        }\n      },\n      decls: 4,\n      vars: 9,\n      consts: [[\"menu\", \"lpx-context-menu\"], [3, \"profileClick\", \"profileRef\"], [4, \"ngIf\"], [3, \"exceptedRefs\", \"lpxClickOutside\", 4, \"ngIf\"], [3, \"lpxClickOutside\", \"exceptedRefs\"], [1, \"lpx-user-ctx-header\"], [1, \"lpx-user-ctx-img\"], [3, \"avatar\"], [1, \"lpx-user-ctx-info\"], [1, \"lpx-context-menu-user-name\"], [\"class\", \"lpx-context-menu-user-tenant\", 4, \"ngIf\"], [1, \"lpx-context-menu-user-email\"], [4, \"ngFor\", \"ngForOf\"], [1, \"lpx-context-menu-user-tenant\"], [3, \"navbarItems\", \"routerItem\"]],\n      template: function BdoToolbarContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-bdo-toolbar\", 1);\n          i0.ɵɵlistener(\"profileClick\", function BdoToolbarContainerComponent_Template_app_bdo_toolbar_profileClick_0_listener() {\n            return ctx.toggleCtxMenu();\n          });\n          i0.ɵɵtemplate(1, BdoToolbarContainerComponent_ng_container_1_Template, 2, 1, \"ng-container\", 2);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"profileRef\", ctx.profileRef$);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction2(6, _c0, i0.ɵɵpipeBind1(2, 2, ctx.userProfileService.user$), i0.ɵɵpipeBind1(3, 4, ctx.profileRef$)));\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ContextMenuComponent, i4.ContextMenuHeaderComponent, i4.ContextMenuActionGroupComponent, i2.ClickOutsideDirective, i2.AvatarComponent, i2.NavbarRoutesComponent, i5.BdoToolbarComponent, i3.AsyncPipe],\n      encapsulation: 2\n    });\n  }\n};\nBdoToolbarContainerComponent = __decorate([UntilDestroy({\n  checkProperties: true\n})], BdoToolbarContainerComponent);\nexport { BdoToolbarContainerComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}