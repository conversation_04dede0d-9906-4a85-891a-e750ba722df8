{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'өнгөрсөн' eeee 'гарагийн' p 'цагт'\",\n  yesterday: \"'өчигдөр' p 'цагт'\",\n  today: \"'өнөөдөр' p 'цагт'\",\n  tomorrow: \"'маргааш' p 'цагт'\",\n  nextWeek: \"'ирэх' eeee 'гарагийн' p 'цагт'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/mn/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'өнгөрсөн' eeee 'гарагийн' p 'цагт'\",\n  yesterday: \"'өчигдөр' p 'цагт'\",\n  today: \"'өнөөдөр' p 'цагт'\",\n  tomorrow: \"'маргааш' p 'цагт'\",\n  nextWeek: \"'ирэх' eeee 'гарагийн' p 'цагт'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,qCAAqC;EAC/CC,SAAS,EAAE,oBAAoB;EAC/BC,KAAK,EAAE,oBAAoB;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,iCAAiC;EAC3CC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}