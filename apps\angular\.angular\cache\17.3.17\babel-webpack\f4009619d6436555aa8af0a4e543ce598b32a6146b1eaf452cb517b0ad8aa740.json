{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === Math.floor(n) && n >= 0 && n <= 1) return 1;\n  return 5;\n}\nexport default [\"ln-CG\", [[\"ntɔ́ngɔ́\", \"mpókwa\"], u, u], u, [[\"e\", \"y\", \"m\", \"m\", \"m\", \"m\", \"p\"], [\"eye\", \"ybo\", \"mbl\", \"mst\", \"min\", \"mtn\", \"mps\"], [\"eyenga\", \"mokɔlɔ mwa yambo\", \"mokɔlɔ mwa míbalé\", \"mokɔlɔ mwa mísáto\", \"mokɔlɔ ya mínéi\", \"mokɔlɔ ya mítáno\", \"mpɔ́sɔ\"], [\"eye\", \"ybo\", \"mbl\", \"mst\", \"min\", \"mtn\", \"mps\"]], u, [[\"y\", \"f\", \"m\", \"a\", \"m\", \"y\", \"y\", \"a\", \"s\", \"ɔ\", \"n\", \"d\"], [\"yan\", \"fbl\", \"msi\", \"apl\", \"mai\", \"yun\", \"yul\", \"agt\", \"stb\", \"ɔtb\", \"nvb\", \"dsb\"], [\"sánzá ya yambo\", \"sánzá ya míbalé\", \"sánzá ya mísáto\", \"sánzá ya mínei\", \"sánzá ya mítáno\", \"sánzá ya motóbá\", \"sánzá ya nsambo\", \"sánzá ya mwambe\", \"sánzá ya libwa\", \"sánzá ya zómi\", \"sánzá ya zómi na mɔ̌kɔ́\", \"sánzá ya zómi na míbalé\"]], u, [[\"libóso ya\", \"nsima ya Y\"], u, [\"Yambo ya Yézu Krís\", \"Nsima ya Yézu Krís\"]], 1, [6, 0], [\"d/M/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"XAF\", \"FCFA\", \"Falánga CFA BEAC\", {\n  \"CDF\": [\"FC\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "Math", "floor"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ln-CG.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === Math.floor(n) && (n >= 0 && n <= 1))\n        return 1;\n    return 5;\n}\nexport default [\"ln-CG\", [[\"ntɔ́ngɔ́\", \"mpókwa\"], u, u], u, [[\"e\", \"y\", \"m\", \"m\", \"m\", \"m\", \"p\"], [\"eye\", \"ybo\", \"mbl\", \"mst\", \"min\", \"mtn\", \"mps\"], [\"eyenga\", \"mokɔlɔ mwa yambo\", \"mokɔlɔ mwa míbalé\", \"mokɔlɔ mwa mísáto\", \"mokɔlɔ ya mínéi\", \"mokɔlɔ ya mítáno\", \"mpɔ́sɔ\"], [\"eye\", \"ybo\", \"mbl\", \"mst\", \"min\", \"mtn\", \"mps\"]], u, [[\"y\", \"f\", \"m\", \"a\", \"m\", \"y\", \"y\", \"a\", \"s\", \"ɔ\", \"n\", \"d\"], [\"yan\", \"fbl\", \"msi\", \"apl\", \"mai\", \"yun\", \"yul\", \"agt\", \"stb\", \"ɔtb\", \"nvb\", \"dsb\"], [\"sánzá ya yambo\", \"sánzá ya míbalé\", \"sánzá ya mísáto\", \"sánzá ya mínei\", \"sánzá ya mítáno\", \"sánzá ya motóbá\", \"sánzá ya nsambo\", \"sánzá ya mwambe\", \"sánzá ya libwa\", \"sánzá ya zómi\", \"sánzá ya zómi na mɔ̌kɔ́\", \"sánzá ya zómi na míbalé\"]], u, [[\"libóso ya\", \"nsima ya Y\"], u, [\"Yambo ya Yézu Krís\", \"Nsima ya Yézu Krís\"]], 1, [6, 0], [\"d/M/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"XAF\", \"FCFA\", \"Falánga CFA BEAC\", { \"CDF\": [\"FC\"], \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC,IAAKA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAE,EACzC,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,yBAAyB,EAAE,yBAAyB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,EAAEA,CAAC,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}