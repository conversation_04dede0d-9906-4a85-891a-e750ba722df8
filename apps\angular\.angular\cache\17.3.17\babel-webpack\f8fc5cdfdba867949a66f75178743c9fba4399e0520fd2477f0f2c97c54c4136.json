{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n  if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11)) return 1;\n  if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 && !(i % 100 >= 12 && i % 100 <= 14) || f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 && !(f % 100 >= 12 && f % 100 <= 14)) return 3;\n  return 5;\n}\nexport default [\"bs-Latn\", [[\"prijepodne\", \"popodne\"], [\"AM\", \"<PERSON>\"], [\"prijepodne\", \"popodne\"]], u, [[\"N\", \"P\", \"U\", \"S\", \"<PERSON>\", \"P\", \"S\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"], [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"]], [[\"n\", \"p\", \"u\", \"s\", \"č\", \"p\", \"s\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"], [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"]], [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mar\", \"apr\", \"maj\", \"jun\", \"jul\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"], [\"januar\", \"februar\", \"mart\", \"april\", \"maj\", \"juni\", \"juli\", \"august\", \"septembar\", \"oktobar\", \"novembar\", \"decembar\"]], u, [[\"p.n.e.\", \"n. e.\"], [\"p. n. e.\", \"n. e.\"], [\"prije nove ere\", \"nove ere\"]], 1, [6, 0], [\"d. M. y.\", \"d. MMM y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, \"{1} 'u' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"BAM\", \"KM\", \"Bosanskohercegovačka konvertibilna marka\", {\n  \"AUD\": [u, \"$\"],\n  \"BAM\": [\"KM\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"HRK\": [\"kn\"],\n  \"ILS\": [u, \"₪\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RSD\": [\"din.\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"XCD\": [u, \"$\"],\n  \"XPF\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length", "f", "parseInt"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/bs-Latn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n    if (v === 0 && (i % 10 === 1 && !(i % 100 === 11)) || f % 10 === 1 && !(f % 100 === 11))\n        return 1;\n    if (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)) || f % 10 === Math.floor(f % 10) && (f % 10 >= 2 && f % 10 <= 4) && !(f % 100 >= 12 && f % 100 <= 14))\n        return 3;\n    return 5;\n}\nexport default [\"bs-Latn\", [[\"prijepodne\", \"popodne\"], [\"AM\", \"PM\"], [\"prijepodne\", \"popodne\"]], u, [[\"N\", \"P\", \"U\", \"S\", \"Č\", \"P\", \"S\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"], [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"]], [[\"n\", \"p\", \"u\", \"s\", \"č\", \"p\", \"s\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"], [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"]], [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mar\", \"apr\", \"maj\", \"jun\", \"jul\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"], [\"januar\", \"februar\", \"mart\", \"april\", \"maj\", \"juni\", \"juli\", \"august\", \"septembar\", \"oktobar\", \"novembar\", \"decembar\"]], u, [[\"p.n.e.\", \"n. e.\"], [\"p. n. e.\", \"n. e.\"], [\"prije nove ere\", \"nove ere\"]], 1, [6, 0], [\"d. M. y.\", \"d. MMM y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, \"{1} 'u' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"BAM\", \"KM\", \"Bosanskohercegovačka konvertibilna marka\", { \"AUD\": [u, \"$\"], \"BAM\": [\"KM\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"HRK\": [\"kn\"], \"ILS\": [u, \"₪\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"RSD\": [\"din.\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [u, \"$\"], \"XCD\": [u, \"$\"], \"XPF\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;IAAEC,CAAC,GAAGC,QAAQ,CAACX,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;EAChK,IAAIF,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAE,IAAIQ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,EACnF,OAAO,CAAC;EACZ,IAAIJ,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAE,IAAIQ,CAAC,GAAG,EAAE,KAAKP,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,EACrN,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,EAAEb,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAE,aAAa,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,0CAA0C,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}