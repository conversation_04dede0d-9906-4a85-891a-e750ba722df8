{"ast": null, "code": "export * from './identity-settings.component';\nexport * from './my-link-users-modal.component';\nexport * from './identity-setting-tabs/index';\nexport * from './authority-delegation/index';", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity-config\\components\\index.ts"], "sourcesContent": ["export * from './identity-settings.component';\r\nexport * from './my-link-users-modal.component';\r\nexport * from './identity-setting-tabs/index';\r\nexport * from './authority-delegation/index';\r\n"], "mappings": "AAAA,cAAc,+BAA+B;AAC7C,cAAc,iCAAiC;AAC/C,cAAc,+BAA+B;AAC7C,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}