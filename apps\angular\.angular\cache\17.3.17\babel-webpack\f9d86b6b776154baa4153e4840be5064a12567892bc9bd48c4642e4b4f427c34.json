{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the day of year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport default function getDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = differenceInCalendarDays(date, startOfYear(date));\n  var dayOfYear = diff + 1;\n  return dayOfYear;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}