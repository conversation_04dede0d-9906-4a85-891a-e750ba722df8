{"ast": null, "code": "function isPluralType(val) {\n  return val.one !== undefined;\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    present: {\n      one: 'manj kot {{count}} sekunda',\n      two: 'manj kot {{count}} sekundi',\n      few: 'manj kot {{count}} sekunde',\n      other: 'manj kot {{count}} sekund'\n    },\n    past: {\n      one: 'manj kot {{count}} sekundo',\n      two: 'manj kot {{count}} sekundama',\n      few: 'manj kot {{count}} sekundami',\n      other: 'manj kot {{count}} sekundami'\n    },\n    future: {\n      one: 'manj kot {{count}} sekundo',\n      two: 'manj kot {{count}} sekundi',\n      few: 'manj kot {{count}} sekunde',\n      other: 'manj kot {{count}} sekund'\n    }\n  },\n  xSeconds: {\n    present: {\n      one: '{{count}} sekunda',\n      two: '{{count}} sekundi',\n      few: '{{count}} sekunde',\n      other: '{{count}} sekund'\n    },\n    past: {\n      one: '{{count}} sekundo',\n      two: '{{count}} sekundama',\n      few: '{{count}} sekundami',\n      other: '{{count}} sekundami'\n    },\n    future: {\n      one: '{{count}} sekundo',\n      two: '{{count}} sekundi',\n      few: '{{count}} sekunde',\n      other: '{{count}} sekund'\n    }\n  },\n  halfAMinute: 'pol minute',\n  lessThanXMinutes: {\n    present: {\n      one: 'manj kot {{count}} minuta',\n      two: 'manj kot {{count}} minuti',\n      few: 'manj kot {{count}} minute',\n      other: 'manj kot {{count}} minut'\n    },\n    past: {\n      one: 'manj kot {{count}} minuto',\n      two: 'manj kot {{count}} minutama',\n      few: 'manj kot {{count}} minutami',\n      other: 'manj kot {{count}} minutami'\n    },\n    future: {\n      one: 'manj kot {{count}} minuto',\n      two: 'manj kot {{count}} minuti',\n      few: 'manj kot {{count}} minute',\n      other: 'manj kot {{count}} minut'\n    }\n  },\n  xMinutes: {\n    present: {\n      one: '{{count}} minuta',\n      two: '{{count}} minuti',\n      few: '{{count}} minute',\n      other: '{{count}} minut'\n    },\n    past: {\n      one: '{{count}} minuto',\n      two: '{{count}} minutama',\n      few: '{{count}} minutami',\n      other: '{{count}} minutami'\n    },\n    future: {\n      one: '{{count}} minuto',\n      two: '{{count}} minuti',\n      few: '{{count}} minute',\n      other: '{{count}} minut'\n    }\n  },\n  aboutXHours: {\n    present: {\n      one: 'približno {{count}} ura',\n      two: 'približno {{count}} uri',\n      few: 'približno {{count}} ure',\n      other: 'približno {{count}} ur'\n    },\n    past: {\n      one: 'približno {{count}} uro',\n      two: 'približno {{count}} urama',\n      few: 'približno {{count}} urami',\n      other: 'približno {{count}} urami'\n    },\n    future: {\n      one: 'približno {{count}} uro',\n      two: 'približno {{count}} uri',\n      few: 'približno {{count}} ure',\n      other: 'približno {{count}} ur'\n    }\n  },\n  xHours: {\n    present: {\n      one: '{{count}} ura',\n      two: '{{count}} uri',\n      few: '{{count}} ure',\n      other: '{{count}} ur'\n    },\n    past: {\n      one: '{{count}} uro',\n      two: '{{count}} urama',\n      few: '{{count}} urami',\n      other: '{{count}} urami'\n    },\n    future: {\n      one: '{{count}} uro',\n      two: '{{count}} uri',\n      few: '{{count}} ure',\n      other: '{{count}} ur'\n    }\n  },\n  xDays: {\n    present: {\n      one: '{{count}} dan',\n      two: '{{count}} dni',\n      few: '{{count}} dni',\n      other: '{{count}} dni'\n    },\n    past: {\n      one: '{{count}} dnem',\n      two: '{{count}} dnevoma',\n      few: '{{count}} dnevi',\n      other: '{{count}} dnevi'\n    },\n    future: {\n      one: '{{count}} dan',\n      two: '{{count}} dni',\n      few: '{{count}} dni',\n      other: '{{count}} dni'\n    }\n  },\n  // no tenses for weeks?\n  aboutXWeeks: {\n    one: 'približno {{count}} teden',\n    two: 'približno {{count}} tedna',\n    few: 'približno {{count}} tedne',\n    other: 'približno {{count}} tednov'\n  },\n  // no tenses for weeks?\n  xWeeks: {\n    one: '{{count}} teden',\n    two: '{{count}} tedna',\n    few: '{{count}} tedne',\n    other: '{{count}} tednov'\n  },\n  aboutXMonths: {\n    present: {\n      one: 'približno {{count}} mesec',\n      two: 'približno {{count}} meseca',\n      few: 'približno {{count}} mesece',\n      other: 'približno {{count}} mesecev'\n    },\n    past: {\n      one: 'približno {{count}} mesecem',\n      two: 'približno {{count}} mesecema',\n      few: 'približno {{count}} meseci',\n      other: 'približno {{count}} meseci'\n    },\n    future: {\n      one: 'približno {{count}} mesec',\n      two: 'približno {{count}} meseca',\n      few: 'približno {{count}} mesece',\n      other: 'približno {{count}} mesecev'\n    }\n  },\n  xMonths: {\n    present: {\n      one: '{{count}} mesec',\n      two: '{{count}} meseca',\n      few: '{{count}} meseci',\n      other: '{{count}} mesecev'\n    },\n    past: {\n      one: '{{count}} mesecem',\n      two: '{{count}} mesecema',\n      few: '{{count}} meseci',\n      other: '{{count}} meseci'\n    },\n    future: {\n      one: '{{count}} mesec',\n      two: '{{count}} meseca',\n      few: '{{count}} mesece',\n      other: '{{count}} mesecev'\n    }\n  },\n  aboutXYears: {\n    present: {\n      one: 'približno {{count}} leto',\n      two: 'približno {{count}} leti',\n      few: 'približno {{count}} leta',\n      other: 'približno {{count}} let'\n    },\n    past: {\n      one: 'približno {{count}} letom',\n      two: 'približno {{count}} letoma',\n      few: 'približno {{count}} leti',\n      other: 'približno {{count}} leti'\n    },\n    future: {\n      one: 'približno {{count}} leto',\n      two: 'približno {{count}} leti',\n      few: 'približno {{count}} leta',\n      other: 'približno {{count}} let'\n    }\n  },\n  xYears: {\n    present: {\n      one: '{{count}} leto',\n      two: '{{count}} leti',\n      few: '{{count}} leta',\n      other: '{{count}} let'\n    },\n    past: {\n      one: '{{count}} letom',\n      two: '{{count}} letoma',\n      few: '{{count}} leti',\n      other: '{{count}} leti'\n    },\n    future: {\n      one: '{{count}} leto',\n      two: '{{count}} leti',\n      few: '{{count}} leta',\n      other: '{{count}} let'\n    }\n  },\n  overXYears: {\n    present: {\n      one: 'več kot {{count}} leto',\n      two: 'več kot {{count}} leti',\n      few: 'več kot {{count}} leta',\n      other: 'več kot {{count}} let'\n    },\n    past: {\n      one: 'več kot {{count}} letom',\n      two: 'več kot {{count}} letoma',\n      few: 'več kot {{count}} leti',\n      other: 'več kot {{count}} leti'\n    },\n    future: {\n      one: 'več kot {{count}} leto',\n      two: 'več kot {{count}} leti',\n      few: 'več kot {{count}} leta',\n      other: 'več kot {{count}} let'\n    }\n  },\n  almostXYears: {\n    present: {\n      one: 'skoraj {{count}} leto',\n      two: 'skoraj {{count}} leti',\n      few: 'skoraj {{count}} leta',\n      other: 'skoraj {{count}} let'\n    },\n    past: {\n      one: 'skoraj {{count}} letom',\n      two: 'skoraj {{count}} letoma',\n      few: 'skoraj {{count}} leti',\n      other: 'skoraj {{count}} leti'\n    },\n    future: {\n      one: 'skoraj {{count}} leto',\n      two: 'skoraj {{count}} leti',\n      few: 'skoraj {{count}} leta',\n      other: 'skoraj {{count}} let'\n    }\n  }\n};\nfunction getFormFromCount(count) {\n  switch (count % 100) {\n    case 1:\n      return 'one';\n    case 2:\n      return 'two';\n    case 3:\n    case 4:\n      return 'few';\n    default:\n      return 'other';\n  }\n}\nvar formatDistance = function formatDistance(token, count, options) {\n  var result = '';\n  var tense = 'present';\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      tense = 'future';\n      result = 'čez ';\n    } else {\n      tense = 'past';\n      result = 'pred ';\n    }\n  }\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result += tokenValue;\n  } else {\n    var form = getFormFromCount(count);\n    if (isPluralType(tokenValue)) {\n      result += tokenValue[form].replace('{{count}}', String(count));\n    } else {\n      result += tokenValue[tense][form].replace('{{count}}', String(count));\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}