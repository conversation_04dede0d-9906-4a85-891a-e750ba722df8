{"ast": null, "code": "import { SharedModule } from '@app/shared/shared.module';\nimport { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';\nimport { MAT_DIALOG_DEFAULT_OPTIONS } from '@angular/material/dialog';\n// TODO remove temparary import\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { InformationExchangeRoutingModule } from './information-exchange.routing.module';\nimport * as i0 from \"@angular/core\";\nexport let InformationExchangeModule = /*#__PURE__*/(() => {\n  class InformationExchangeModule {\n    static {\n      this.ɵfac = function InformationExchangeModule_Factory(t) {\n        return new (t || InformationExchangeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: InformationExchangeModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [{\n          provide: MAT_DIALOG_DEFAULT_OPTIONS,\n          useValue: {\n            autoFocus: true,\n            disableClose: true,\n            restoreFocus: false,\n            width: '50%'\n          }\n        }],\n        imports: [SharedModule, InformationExchangeRoutingModule, CommonModule, FormsModule, SweetAlert2Module]\n      });\n    }\n  }\n  return InformationExchangeModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}