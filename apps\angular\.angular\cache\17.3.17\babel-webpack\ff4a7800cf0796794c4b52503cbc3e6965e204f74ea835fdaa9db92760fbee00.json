{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ce\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['ce', [['AM', 'PM'], u, u], u, [['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо'], u, ['кӀира', 'оршот', 'шинара', 'кхаара', 'еара', 'пӀераска', 'шуот'], ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо']], [['кӀ', 'о', 'ш', 'кх', 'е', 'пӀ', 'ш'], ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо'], ['кӀира', 'оршот', 'шинара', 'кхаара', 'еара', 'пӀераска', 'шуот'], ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо']], [['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'], ['янв', 'фев', 'мар', 'апр', 'май', 'июн', 'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'], ['январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь']], u, [['в. э. тӀ. я', 'в. э'], u, ['Ӏийса пайхамар вина де кхачале', 'Ӏийса пайхамар вина дийнахь дуьйна']], 1, [6, 0], ['y-MM-dd', 'y MMM d', 'y MMMM d', 'y MMMM d, EEEE'], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1} {0}', u, u, u], ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'Терхьаш дац', ':'], ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'], 'RUB', '₽', 'Российн сом', {\n    'JPY': ['JP¥', '¥'],\n    'RON': [u, 'лей'],\n    'RUB': ['₽'],\n    'USD': ['US$', '$']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/ce.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/ce\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'ce',\n        [['AM', 'PM'], u, u],\n        u,\n        [\n            ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо'], u,\n            ['кӀира', 'оршот', 'шинара', 'кхаара', 'еара', 'пӀераска', 'шуот'],\n            ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо']\n        ],\n        [\n            ['кӀ', 'о', 'ш', 'кх', 'е', 'пӀ', 'ш'], ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо'],\n            ['кӀира', 'оршот', 'шинара', 'кхаара', 'еара', 'пӀераска', 'шуот'],\n            ['кӀи', 'ор', 'ши', 'кха', 'еа', 'пӀе', 'шуо']\n        ],\n        [\n            ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n            ['янв', 'фев', 'мар', 'апр', 'май', 'июн', 'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'],\n            [\n                'январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь',\n                'ноябрь', 'декабрь'\n            ]\n        ],\n        u,\n        [\n            ['в. э. тӀ. я', 'в. э'], u,\n            ['Ӏийса пайхамар вина де кхачале', 'Ӏийса пайхамар вина дийнахь дуьйна']\n        ],\n        1,\n        [6, 0],\n        ['y-MM-dd', 'y MMM d', 'y MMMM d', 'y MMMM d, EEEE'],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1} {0}', u, u, u],\n        ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'Терхьаш дац', ':'],\n        ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'],\n        'RUB',\n        '₽',\n        'Российн сом',\n        { 'JPY': ['JP¥', '¥'], 'RON': [u, 'лей'], 'RUB': ['₽'], 'USD': ['US$', '$'] },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EACpBA,CAAC,EACD,CACI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAEA,CAAC,EACjD,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,EAClE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CACjD,EACD,CACI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EACtF,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,EAClE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CACjD,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACpF,CACI,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAC7F,QAAQ,EAAE,SAAS,CACtB,CACJ,EACDA,CAAC,EACD,CACI,CAAC,aAAa,EAAE,MAAM,CAAC,EAAEA,CAAC,EAC1B,CAAC,gCAAgC,EAAE,oCAAoC,CAAC,CAC3E,EACD,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EACpD,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC,EACtE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAC7C,KAAK,EACL,GAAG,EACH,aAAa,EACb;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IAAE,KAAK,EAAE,CAAC,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EAAE,CAAC,EAC7E,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}