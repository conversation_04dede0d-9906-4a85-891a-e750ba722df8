{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/proxies-audit-service/lib/proxy/bdo/ess/audit-service\";\n/**\n * Work for user authorization.\n * Work for communicate with Identity Server 4 solution with Open Id Connect from Angular6 application.\n * Corporate with oidc-client library.\n * Reference: https://www.scottbrady91.com/Angular/SPA-Authentiction-using-OpenID-Connect-Angular-CLI-and-oidc-client\n */\nexport class AuditTrailService {\n  constructor(http) {\n    this.http = http;\n    /** Carry properties of GetAuditLogsDto and shared between components */\n    this.auditLogSearchCondition = new Subject();\n    /** Notify other components if clear search condition event raised. */\n    this.auditLogSearchConditionClear = new Subject();\n    this.ipAddress = '';\n  }\n  /**\n   * Get client Ip address from local storage \"ESSClientIpAddress\"\n   * Note: Corporate with index.html javascript code where to get client ip address and save to local storage.\n   * @returns client ip address string.\n   */\n  getIPAddress() {\n    //\n    // Note: IP address is got from index.html javacsript code.\n    // Get client IP from api.ipify.org and save to local storage called \"ESSClientIpAddress\".\n    //\n    if (localStorage.getItem('ESSClientIpAddress')) {\n      this.ipAddress = localStorage.getItem('ESSClientIpAddress');\n    }\n    return this.ipAddress;\n  }\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\n  getFiscalYears() {\n    return this.http.getFiscalYears().pipe();\n  }\n  getActions() {\n    return this.http.getAuditActions().pipe();\n  }\n  getUsers() {\n    return this.http.getAuditUsers().pipe();\n  }\n  getAuditLogs(condition) {\n    return this.http.getAuditLogsByInput(condition).pipe();\n  }\n  sendSearchCondition(searchCondition) {\n    this.auditLogSearchCondition.next(searchCondition);\n  }\n  getAuditLogDetail(auditLogId) {\n    return this.http.getAuditLogDetailById(auditLogId).pipe();\n  }\n  /** Called when click \"Clear\" button in Search Condition component. */\n  clearSearchCondition() {\n    this.auditLogSearchConditionClear.next(true);\n  }\n  static {\n    this.ɵfac = function AuditTrailService_Factory(t) {\n      return new (t || AuditTrailService)(i0.ɵɵinject(i1.AuditService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuditTrailService,\n      factory: AuditTrailService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "AuditTrailService", "constructor", "http", "auditLogSearchCondition", "auditLogSearchConditionClear", "ip<PERSON><PERSON><PERSON>", "getIPAddress", "localStorage", "getItem", "getFiscalYears", "pipe", "getActions", "getAuditActions", "getUsers", "getAuditUsers", "getAuditLogs", "condition", "getAuditLogsByInput", "sendSearchCondition", "searchCondition", "next", "getAuditLogDetail", "auditLogId", "getAuditLogDetailById", "clearSearchCondition", "i0", "ɵɵinject", "i1", "AuditService", "factory", "ɵfac", "providedIn"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\audit\\services\\audit-trail.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { AuditService } from 'proxies/proxies-audit-service/lib/proxy/bdo/ess/audit-service';\r\nimport { GetAuditLogsDto } from 'proxies/proxies-audit-service/lib/proxy/bdo/ess/audit-service/models';\r\nimport { Observable, Subject } from 'rxjs';\r\n\r\n/**\r\n * Work for user authorization.\r\n * Work for communicate with Identity Server 4 solution with Open Id Connect from Angular6 application.\r\n * Corporate with oidc-client library.\r\n * Reference: https://www.scottbrady91.com/Angular/SPA-Authentiction-using-OpenID-Connect-Angular-CLI-and-oidc-client\r\n */\r\n@Injectable({\r\n  /**\r\n   * Define SecurityOidcService as a singletone service.\r\n   * <PERSON><PERSON> creates a single, shared instance of the service and injects into any class that asks for it.\r\n   * Reference: http://www.talkingdotnet.com/providing-shared-instance-of-service-in-angular-6/\r\n   */\r\n  providedIn: 'root', // Define the service as singletone service.\r\n})\r\nexport class AuditTrailService {\r\n  constructor(private http: AuditService) {}\r\n\r\n  /** Carry properties of GetAuditLogsDto and shared between components */\r\n  public auditLogSearchCondition: Subject<GetAuditLogsDto> = new Subject();\r\n  /** Notify other components if clear search condition event raised. */\r\n  public auditLogSearchConditionClear: Subject<boolean> = new Subject();\r\n\r\n  ipAddress: string = '';\r\n\r\n  /**\r\n   * Get client Ip address from local storage \"ESSClientIpAddress\"\r\n   * Note: Corporate with index.html javascript code where to get client ip address and save to local storage.\r\n   * @returns client ip address string.\r\n   */\r\n  getIPAddress(): string {\r\n    //\r\n    // Note: IP address is got from index.html javacsript code.\r\n    // Get client IP from api.ipify.org and save to local storage called \"ESSClientIpAddress\".\r\n    //\r\n    if (localStorage.getItem('ESSClientIpAddress')) {\r\n      this.ipAddress = localStorage.getItem('ESSClientIpAddress');\r\n    }\r\n    return this.ipAddress;\r\n  }\r\n\r\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\r\n  getFiscalYears() {\r\n    return this.http.getFiscalYears().pipe();\r\n  }\r\n\r\n  getActions() {\r\n    return this.http.getAuditActions().pipe();\r\n  }\r\n\r\n  getUsers() {\r\n    return this.http.getAuditUsers().pipe();\r\n  }\r\n\r\n  getAuditLogs(condition: GetAuditLogsDto) {\r\n    return this.http.getAuditLogsByInput(condition).pipe();\r\n  }\r\n\r\n  sendSearchCondition(searchCondition: GetAuditLogsDto) {\r\n    this.auditLogSearchCondition.next(searchCondition);\r\n  }\r\n\r\n  getAuditLogDetail(auditLogId: string) {\r\n    return this.http.getAuditLogDetailById(auditLogId).pipe();\r\n  }\r\n\r\n  /** Called when click \"Clear\" button in Search Condition component. */\r\n  clearSearchCondition() {\r\n    this.auditLogSearchConditionClear.next(true);\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAAqBA,OAAO,QAAQ,MAAM;;;AAE1C;;;;;;AAcA,OAAM,MAAOC,iBAAiB;EAC5BC,YAAoBC,IAAkB;IAAlB,KAAAA,IAAI,GAAJA,IAAI;IAExB;IACO,KAAAC,uBAAuB,GAA6B,IAAIJ,OAAO,EAAE;IACxE;IACO,KAAAK,4BAA4B,GAAqB,IAAIL,OAAO,EAAE;IAErE,KAAAM,SAAS,GAAW,EAAE;EAPmB;EASzC;;;;;EAKAC,YAAYA,CAAA;IACV;IACA;IACA;IACA;IACA,IAAIC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE;MAC9C,IAAI,CAACH,SAAS,GAAGE,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IAC7D;IACA,OAAO,IAAI,CAACH,SAAS;EACvB;EAEA;EACAI,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACP,IAAI,CAACO,cAAc,EAAE,CAACC,IAAI,EAAE;EAC1C;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACT,IAAI,CAACU,eAAe,EAAE,CAACF,IAAI,EAAE;EAC3C;EAEAG,QAAQA,CAAA;IACN,OAAO,IAAI,CAACX,IAAI,CAACY,aAAa,EAAE,CAACJ,IAAI,EAAE;EACzC;EAEAK,YAAYA,CAACC,SAA0B;IACrC,OAAO,IAAI,CAACd,IAAI,CAACe,mBAAmB,CAACD,SAAS,CAAC,CAACN,IAAI,EAAE;EACxD;EAEAQ,mBAAmBA,CAACC,eAAgC;IAClD,IAAI,CAAChB,uBAAuB,CAACiB,IAAI,CAACD,eAAe,CAAC;EACpD;EAEAE,iBAAiBA,CAACC,UAAkB;IAClC,OAAO,IAAI,CAACpB,IAAI,CAACqB,qBAAqB,CAACD,UAAU,CAAC,CAACZ,IAAI,EAAE;EAC3D;EAEA;EACAc,oBAAoBA,CAAA;IAClB,IAAI,CAACpB,4BAA4B,CAACgB,IAAI,CAAC,IAAI,CAAC;EAC9C;;;uBAtDWpB,iBAAiB,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;aAAjB5B,iBAAiB;MAAA6B,OAAA,EAAjB7B,iBAAiB,CAAA8B,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}