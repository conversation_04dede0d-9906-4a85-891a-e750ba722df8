{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"so-DJ\", [[\"h\", \"d\"], [\"GH\", \"GD\"], u], [[\"AM\", \"GD\"], u, [\"GH\", \"GD\"]], [[\"A\", \"I\", \"T\", \"A\", \"Kh\", \"J\", \"S\"], [\"Axd\", \"Isn\", \"Tldo\", \"Arbc\", \"Khms\", \"Jmc\", \"Sbti\"], [\"Axad\", \"Isniin\", \"<PERSON>laado\", \"Arbaco\", \"Khamiis\", \"Jim<PERSON>\", \"Sabti\"], [\"Axd\", \"Isn\", \"<PERSON><PERSON>\", \"<PERSON>rb<PERSON>\", \"<PERSON>hm<PERSON>\", \"<PERSON>mc\", \"<PERSON>bti\"]], [[\"A\", \"I\", \"T\", \"A\", \"Kh\", \"J\", \"S\"], [\"Axd\", \"Isn\", \"Tldo\", \"Arbc\", \"<PERSON>hms\", \"Jmc\", \"Sbti\"], [\"Axad\", \"Isniin\", \"Talaado\", \"Arbaco\", \"Khamiis\", \"Jimco\", \"Sabti\"], [\"Axd\", \"Isn\", \"Tldo\", \"Arbaco\", \"Khms\", \"Jmc\", \"Sbti\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"L\", \"O\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mar\", \"Abr\", \"May\", \"Jun\", \"Lul\", \"Ogs\", \"Seb\", \"Okt\", \"Nof\", \"Dis\"], [\"Bisha Koobaad\", \"Bisha Labaad\", \"Bisha Saddexaad\", \"Bisha Afraad\", \"Bisha Shanaad\", \"Bisha Lixaad\", \"Bisha Todobaad\", \"Bisha Sideedaad\", \"Bisha Sagaalaad\", \"Bisha Tobnaad\", \"Bisha Kow iyo Tobnaad\", \"Bisha Laba iyo Tobnaad\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"L\", \"O\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mar\", \"Abr\", \"May\", \"Jun\", \"Lul\", \"Ogs\", \"Seb\", \"Okt\", \"Nof\", \"Dis\"], [\"Jannaayo\", \"Febraayo\", \"Maarso\", \"Abriil\", \"May\", \"Juun\", \"Luuliyo\", \"Ogost\", \"Sebtembar\", \"Oktoobar\", \"Nofembar\", \"Desembar\"]], [[\"B\", \"A\"], [\"BC\", \"AD\"], [\"Ciise Hortii\", \"Ciise Dabadii\"]], 6, [6, 0], [\"dd/MM/yy\", \"dd-MMM-y\", \"MMMM d, y\", \"EEEE, MMMM d, y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", \"{1} 'ee' {0}\", u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"MaL\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"DJF\", \"Fdj\", \"Faran Jabuuti\", {\n  \"BBD\": [\"DBB\", \"$\"],\n  \"DJF\": [\"Fdj\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"SOS\": [\"S\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/so-DJ.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"so-DJ\", [[\"h\", \"d\"], [\"GH\", \"GD\"], u], [[\"AM\", \"GD\"], u, [\"GH\", \"GD\"]], [[\"A\", \"I\", \"T\", \"A\", \"Kh\", \"J\", \"S\"], [\"Axd\", \"Isn\", \"Tldo\", \"Arbc\", \"Khms\", \"Jmc\", \"Sbti\"], [\"Axad\", \"Isniin\", \"<PERSON>laado\", \"Arbaco\", \"Khamiis\", \"Jim<PERSON>\", \"Sabti\"], [\"Axd\", \"Isn\", \"<PERSON><PERSON>\", \"<PERSON>rb<PERSON>\", \"<PERSON>hm<PERSON>\", \"<PERSON>mc\", \"<PERSON>bti\"]], [[\"A\", \"I\", \"T\", \"A\", \"Kh\", \"J\", \"S\"], [\"Axd\", \"Isn\", \"Tldo\", \"Arbc\", \"<PERSON>hms\", \"Jmc\", \"Sbti\"], [\"Axad\", \"Isniin\", \"Talaado\", \"Arbaco\", \"Khamiis\", \"Jimco\", \"Sabti\"], [\"Axd\", \"Isn\", \"Tldo\", \"Arbaco\", \"Khms\", \"Jmc\", \"Sbti\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"L\", \"O\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mar\", \"Abr\", \"May\", \"Jun\", \"Lul\", \"Ogs\", \"Seb\", \"Okt\", \"Nof\", \"Dis\"], [\"Bisha Koobaad\", \"Bisha Labaad\", \"Bisha Saddexaad\", \"Bisha Afraad\", \"Bisha Shanaad\", \"Bisha Lixaad\", \"Bisha Todobaad\", \"Bisha Sideedaad\", \"Bisha Sagaalaad\", \"Bisha Tobnaad\", \"Bisha Kow iyo Tobnaad\", \"Bisha Laba iyo Tobnaad\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"L\", \"O\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mar\", \"Abr\", \"May\", \"Jun\", \"Lul\", \"Ogs\", \"Seb\", \"Okt\", \"Nof\", \"Dis\"], [\"Jannaayo\", \"Febraayo\", \"Maarso\", \"Abriil\", \"May\", \"Juun\", \"Luuliyo\", \"Ogost\", \"Sebtembar\", \"Oktoobar\", \"Nofembar\", \"Desembar\"]], [[\"B\", \"A\"], [\"BC\", \"AD\"], [\"Ciise Hortii\", \"Ciise Dabadii\"]], 6, [6, 0], [\"dd/MM/yy\", \"dd-MMM-y\", \"MMMM d, y\", \"EEEE, MMMM d, y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", \"{1} 'ee' {0}\", u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"MaL\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"DJF\", \"Fdj\", \"Faran Jabuuti\", { \"BBD\": [\"DBB\", \"$\"], \"DJF\": [\"Fdj\"], \"JPY\": [\"JP¥\", \"¥\"], \"SOS\": [\"S\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,uBAAuB,EAAE,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,cAAc,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}