{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class TemplateService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.create = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Template',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.delete = (id, config) => this.restService.request({\n      method: 'DELETE',\n      url: `/api/ESService/Template/${id}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.findByName = (name, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Template/FindByNameAsync',\n      params: {\n        name\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.get = (id, config) => this.restService.request({\n      method: 'GET',\n      url: `/api/ESService/Template/${id}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getActiveTemplate = config => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Template/GetActiveTemplateAsync'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getList = (input, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Template',\n      params: {\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getTemplateVersionById = (id, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/ESService/Template/GetTemplateVersion',\n      params: {\n        id\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.update = (id, input, config) => this.restService.request({\n      method: 'PUT',\n      url: `/api/ESService/Template/${id}`,\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function TemplateService_Factory(t) {\n      return new (t || TemplateService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TemplateService,\n      factory: TemplateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["TemplateService", "constructor", "restService", "apiName", "create", "input", "config", "request", "method", "url", "body", "delete", "id", "findByName", "name", "params", "get", "getActiveTemplate", "getList", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "getTemplateVersionById", "update", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\templates\\template.service.ts"], "sourcesContent": ["import type { GetTemplateListDto, TemplateDto } from './models';\r\nimport { RestService, Rest } from '@abp/ng.core';\r\nimport type { PagedResultDto } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\nimport type { TemplateVersion } from '../../shared/constants/declarations/template-version.enum';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TemplateService {\r\n  apiName = 'EconomicSubstanceService';\r\n  \r\n\r\n  create = (input: TemplateDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, TemplateDto>({\r\n      method: 'POST',\r\n      url: '/api/ESService/Template',\r\n      body: input,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  delete = (id: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, void>({\r\n      method: 'DELETE',\r\n      url: `/api/ESService/Template/${id}`,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  findByName = (name: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, TemplateDto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Template/FindByNameAsync',\r\n      params: { name },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  get = (id: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, TemplateDto>({\r\n      method: 'GET',\r\n      url: `/api/ESService/Template/${id}`,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getActiveTemplate = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, TemplateDto>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Template/GetActiveTemplateAsync',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getList = (input: GetTemplateListDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, PagedResultDto<TemplateDto>>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Template',\r\n      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  getTemplateVersionById = (id: string, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, TemplateVersion>({\r\n      method: 'GET',\r\n      url: '/api/ESService/Template/GetTemplateVersion',\r\n      params: { id },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  update = (id: string, input: TemplateDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, void>({\r\n      method: 'PUT',\r\n      url: `/api/ESService/Template/${id}`,\r\n      body: input,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AASA,OAAM,MAAOA,eAAe;EAwE1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAvE/B,KAAAC,OAAO,GAAG,0BAA0B;IAGpC,KAAAC,MAAM,GAAG,CAACC,KAAkB,EAAEC,MAA6B,KACzD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAmB;MACzCC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAEL;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAK,MAAM,GAAG,CAACC,EAAU,EAAEN,MAA6B,KACjD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAY;MAClCC,MAAM,EAAE,QAAQ;MAChBC,GAAG,EAAE,2BAA2BG,EAAE;KACnC,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAO,UAAU,GAAG,CAACC,IAAY,EAAER,MAA6B,KACvD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAmB;MACzCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,yCAAyC;MAC9CM,MAAM,EAAE;QAAED;MAAI;KACf,EACD;MAAEX,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAU,GAAG,GAAG,CAACJ,EAAU,EAAEN,MAA6B,KAC9C,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAmB;MACzCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,2BAA2BG,EAAE;KACnC,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAW,iBAAiB,GAAIX,MAA6B,IAChD,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAmB;MACzCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAY,OAAO,GAAG,CAACb,KAAyB,EAAEC,MAA6B,KACjE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAmC;MACzDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,yBAAyB;MAC9BM,MAAM,EAAE;QAAEI,OAAO,EAAEd,KAAK,CAACc,OAAO;QAAEC,SAAS,EAAEf,KAAK,CAACe,SAAS;QAAEC,cAAc,EAAEhB,KAAK,CAACgB;MAAc;KACnG,EACD;MAAElB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAgB,sBAAsB,GAAG,CAACV,EAAU,EAAEN,MAA6B,KACjE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAuB;MAC7CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,4CAA4C;MACjDM,MAAM,EAAE;QAAEH;MAAE;KACb,EACD;MAAET,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAiB,MAAM,GAAG,CAACX,EAAU,EAAEP,KAAkB,EAAEC,MAA6B,KACrE,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAY;MAClCC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,2BAA2BG,EAAE,EAAE;MACpCF,IAAI,EAAEL;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBAxEpCN,eAAe,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAf3B,eAAe;MAAA4B,OAAA,EAAf5B,eAAe,CAAA6B,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}