{"ast": null, "code": "import { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class ReplaySubject extends Subject {\n  constructor(_bufferSize = Infinity, _windowTime = Infinity, _timestampProvider = dateTimestampProvider) {\n    super();\n    this._bufferSize = _bufferSize;\n    this._windowTime = _windowTime;\n    this._timestampProvider = _timestampProvider;\n    this._buffer = [];\n    this._infiniteTimeWindow = true;\n    this._infiniteTimeWindow = _windowTime === Infinity;\n    this._bufferSize = Math.max(1, _bufferSize);\n    this._windowTime = Math.max(1, _windowTime);\n  }\n  next(value) {\n    const {\n      isStopped,\n      _buffer,\n      _infiniteTimeWindow,\n      _timestampProvider,\n      _windowTime\n    } = this;\n    if (!isStopped) {\n      _buffer.push(value);\n      !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n    }\n    this._trimBuffer();\n    super.next(value);\n  }\n  _subscribe(subscriber) {\n    this._throwIfClosed();\n    this._trimBuffer();\n    const subscription = this._innerSubscribe(subscriber);\n    const {\n      _infiniteTimeWindow,\n      _buffer\n    } = this;\n    const copy = _buffer.slice();\n    for (let i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n      subscriber.next(copy[i]);\n    }\n    this._checkFinalizedStatuses(subscriber);\n    return subscription;\n  }\n  _trimBuffer() {\n    const {\n      _bufferSize,\n      _timestampProvider,\n      _buffer,\n      _infiniteTimeWindow\n    } = this;\n    const adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n    _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n    if (!_infiniteTimeWindow) {\n      const now = _timestampProvider.now();\n      let last = 0;\n      for (let i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n        last = i;\n      }\n      last && _buffer.splice(0, last + 1);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subject", "dateTimestampProvider", "ReplaySubject", "constructor", "_bufferSize", "Infinity", "_windowTime", "_timestampProvider", "_buffer", "_infiniteTimeWindow", "Math", "max", "next", "value", "isStopped", "push", "now", "_trimBuffer", "_subscribe", "subscriber", "_throwIfClosed", "subscription", "_innerSubscribe", "copy", "slice", "i", "length", "closed", "_checkFinalizedStatuses", "adjustedBufferSize", "splice", "last"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/rxjs/dist/esm/internal/ReplaySubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class ReplaySubject extends Subject {\n    constructor(_bufferSize = Infinity, _windowTime = Infinity, _timestampProvider = dateTimestampProvider) {\n        super();\n        this._bufferSize = _bufferSize;\n        this._windowTime = _windowTime;\n        this._timestampProvider = _timestampProvider;\n        this._buffer = [];\n        this._infiniteTimeWindow = true;\n        this._infiniteTimeWindow = _windowTime === Infinity;\n        this._bufferSize = Math.max(1, _bufferSize);\n        this._windowTime = Math.max(1, _windowTime);\n    }\n    next(value) {\n        const { isStopped, _buffer, _infiniteTimeWindow, _timestampProvider, _windowTime } = this;\n        if (!isStopped) {\n            _buffer.push(value);\n            !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n        }\n        this._trimBuffer();\n        super.next(value);\n    }\n    _subscribe(subscriber) {\n        this._throwIfClosed();\n        this._trimBuffer();\n        const subscription = this._innerSubscribe(subscriber);\n        const { _infiniteTimeWindow, _buffer } = this;\n        const copy = _buffer.slice();\n        for (let i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n            subscriber.next(copy[i]);\n        }\n        this._checkFinalizedStatuses(subscriber);\n        return subscription;\n    }\n    _trimBuffer() {\n        const { _bufferSize, _timestampProvider, _buffer, _infiniteTimeWindow } = this;\n        const adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n        _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n        if (!_infiniteTimeWindow) {\n            const now = _timestampProvider.now();\n            let last = 0;\n            for (let i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n                last = i;\n            }\n            last && _buffer.splice(0, last + 1);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,OAAO,MAAMC,aAAa,SAASF,OAAO,CAAC;EACvCG,WAAWA,CAACC,WAAW,GAAGC,QAAQ,EAAEC,WAAW,GAAGD,QAAQ,EAAEE,kBAAkB,GAAGN,qBAAqB,EAAE;IACpG,KAAK,CAAC,CAAC;IACP,IAAI,CAACG,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACA,mBAAmB,GAAGH,WAAW,KAAKD,QAAQ;IACnD,IAAI,CAACD,WAAW,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,WAAW,CAAC;IAC3C,IAAI,CAACE,WAAW,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,WAAW,CAAC;EAC/C;EACAM,IAAIA,CAACC,KAAK,EAAE;IACR,MAAM;MAAEC,SAAS;MAAEN,OAAO;MAAEC,mBAAmB;MAAEF,kBAAkB;MAAED;IAAY,CAAC,GAAG,IAAI;IACzF,IAAI,CAACQ,SAAS,EAAE;MACZN,OAAO,CAACO,IAAI,CAACF,KAAK,CAAC;MACnB,CAACJ,mBAAmB,IAAID,OAAO,CAACO,IAAI,CAACR,kBAAkB,CAACS,GAAG,CAAC,CAAC,GAAGV,WAAW,CAAC;IAChF;IACA,IAAI,CAACW,WAAW,CAAC,CAAC;IAClB,KAAK,CAACL,IAAI,CAACC,KAAK,CAAC;EACrB;EACAK,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,WAAW,CAAC,CAAC;IAClB,MAAMI,YAAY,GAAG,IAAI,CAACC,eAAe,CAACH,UAAU,CAAC;IACrD,MAAM;MAAEV,mBAAmB;MAAED;IAAQ,CAAC,GAAG,IAAI;IAC7C,MAAMe,IAAI,GAAGf,OAAO,CAACgB,KAAK,CAAC,CAAC;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,IAAI,CAACP,UAAU,CAACQ,MAAM,EAAEF,CAAC,IAAIhB,mBAAmB,GAAG,CAAC,GAAG,CAAC,EAAE;MACrFU,UAAU,CAACP,IAAI,CAACW,IAAI,CAACE,CAAC,CAAC,CAAC;IAC5B;IACA,IAAI,CAACG,uBAAuB,CAACT,UAAU,CAAC;IACxC,OAAOE,YAAY;EACvB;EACAJ,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEb,WAAW;MAAEG,kBAAkB;MAAEC,OAAO;MAAEC;IAAoB,CAAC,GAAG,IAAI;IAC9E,MAAMoB,kBAAkB,GAAG,CAACpB,mBAAmB,GAAG,CAAC,GAAG,CAAC,IAAIL,WAAW;IACtEA,WAAW,GAAGC,QAAQ,IAAIwB,kBAAkB,GAAGrB,OAAO,CAACkB,MAAM,IAAIlB,OAAO,CAACsB,MAAM,CAAC,CAAC,EAAEtB,OAAO,CAACkB,MAAM,GAAGG,kBAAkB,CAAC;IACvH,IAAI,CAACpB,mBAAmB,EAAE;MACtB,MAAMO,GAAG,GAAGT,kBAAkB,CAACS,GAAG,CAAC,CAAC;MACpC,IAAIe,IAAI,GAAG,CAAC;MACZ,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,OAAO,CAACkB,MAAM,IAAIlB,OAAO,CAACiB,CAAC,CAAC,IAAIT,GAAG,EAAES,CAAC,IAAI,CAAC,EAAE;QAC7DM,IAAI,GAAGN,CAAC;MACZ;MACAM,IAAI,IAAIvB,OAAO,CAACsB,MAAM,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,CAAC;IACvC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}