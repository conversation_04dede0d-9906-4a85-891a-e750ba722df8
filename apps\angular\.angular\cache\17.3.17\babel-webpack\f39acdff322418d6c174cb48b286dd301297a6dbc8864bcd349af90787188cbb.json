{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Properties Plugin, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { dataSanitizeString } from \"@microsoft/applicationinsights-common\";\nimport { generateW3CId, getLocation } from \"@microsoft/applicationinsights-core-js\";\nvar TelemetryTrace = /** @class */function () {\n  function TelemetryTrace(id, parentId, name, logger) {\n    var _self = this;\n    _self.traceID = id || generateW3CId();\n    _self.parentID = parentId;\n    var location = getLocation();\n    if (!name && location && location.pathname) {\n      name = location.pathname;\n    }\n    _self.name = dataSanitizeString(logger, name);\n  }\n  return TelemetryTrace;\n}();\nexport { TelemetryTrace };", "map": {"version": 3, "names": ["dataSanitizeString", "generateW3CId", "getLocation", "TelemetryTrace", "id", "parentId", "name", "logger", "_self", "traceID", "parentID", "location", "pathname"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-properties-js/dist-esm/Context/TelemetryTrace.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Properties Plugin, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { dataSanitizeString } from \"@microsoft/applicationinsights-common\";\r\nimport { generateW3CId, getLocation } from \"@microsoft/applicationinsights-core-js\";\r\nvar TelemetryTrace = /** @class */ (function () {\r\n    function TelemetryTrace(id, parentId, name, logger) {\r\n        var _self = this;\r\n        _self.traceID = id || generateW3CId();\r\n        _self.parentID = parentId;\r\n        var location = getLocation();\r\n        if (!name && location && location.pathname) {\r\n            name = location.pathname;\r\n        }\r\n        _self.name = dataSanitizeString(logger, name);\r\n    }\r\n    return TelemetryTrace;\r\n}());\r\nexport { TelemetryTrace };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,aAAa,EAAEC,WAAW,QAAQ,wCAAwC;AACnF,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAE;IAChD,IAAIC,KAAK,GAAG,IAAI;IAChBA,KAAK,CAACC,OAAO,GAAGL,EAAE,IAAIH,aAAa,CAAC,CAAC;IACrCO,KAAK,CAACE,QAAQ,GAAGL,QAAQ;IACzB,IAAIM,QAAQ,GAAGT,WAAW,CAAC,CAAC;IAC5B,IAAI,CAACI,IAAI,IAAIK,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;MACxCN,IAAI,GAAGK,QAAQ,CAACC,QAAQ;IAC5B;IACAJ,KAAK,CAACF,IAAI,GAAGN,kBAAkB,CAACO,MAAM,EAAED,IAAI,CAAC;EACjD;EACA,OAAOH,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}