{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ak\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === Math.floor(n) && n >= 0 && n <= 1) return 1;\n    return 5;\n  }\n  exports.default = ['ak', [['AN', 'EW'], u, u], u, [['K', 'D', 'B', 'W', 'Y', 'F', 'M'], ['Kwe', 'Dwo', 'Ben', 'Wuk', 'Yaw', 'Fia', 'Mem'], ['Kwesida', 'Dwowda', '<PERSON>ada', 'Wukuda', 'Yawda', 'Fida', 'Memeneda'], ['Kwe', 'Dwo', 'Ben', 'Wuk', 'Yaw', 'Fia', 'Mem']], u, [['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], ['S-Ɔ', 'K-Ɔ', 'E-Ɔ', 'E-O', 'E-K', 'O-A', 'A-K', 'D-Ɔ', 'F-Ɛ', 'Ɔ-A', 'Ɔ-O', 'M-Ɔ'], ['Sanda-Ɔpɛpɔn', 'Kwakwar-Ɔgyefuo', 'Ebɔw-Ɔbenem', 'Ebɔbira-Oforisuo', 'Esusow Aketseaba-Kɔtɔnimba', 'Obirade-Ayɛwohomumu', 'Ayɛwoho-Kitawonsa', 'Difuu-Ɔsandaa', 'Fankwa-Ɛbɔ', 'Ɔbɛsɛ-Ahinime', 'Ɔberɛfɛw-Obubuo', 'Mumu-Ɔpɛnimba']], u, [['AK', 'KE'], u, ['Ansa Kristo', 'Kristo Ekyiri']], 1, [6, 0], ['yy/MM/dd', 'y MMM d', 'y MMMM d', 'EEEE, y MMMM dd'], ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'], ['{1} {0}', u, u, u], ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'], 'GHS', 'GH₵', 'Ghana Sidi', {\n    'GHS': ['GH₵'],\n    'JPY': ['JP¥', '¥'],\n    'USD': ['US$', '$']\n  }, 'ltr', plural];\n});\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}