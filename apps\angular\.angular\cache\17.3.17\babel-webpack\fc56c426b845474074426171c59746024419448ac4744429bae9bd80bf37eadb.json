{"ast": null, "code": "import { BdoTableColumnType } from \"@app/shared/components/bdo-table/bdo-table.model\";\nexport const EsAssesmentListTableColumnsDefination = [{\n  columnId: \"entityName\" /* EsAssesmentListTableColumns.ENTITY_NAME */,\n  type: BdoTableColumnType.String,\n  minWidth: 140,\n  frozenLeft: true,\n  isSortable: true,\n  columnName: 'Entity Name'\n}, {\n  columnId: \"raName\" /* EsAssesmentListTableColumns.RA_NAME */,\n  type: BdoTableColumnType.String,\n  minWidth: 120,\n  isSortable: true,\n  columnName: 'RA Name'\n}, {\n  columnId: \"incorporationNumber\" /* EsAssesmentListTableColumns.INCORPORATION_NUMBER */,\n  type: BdoTableColumnType.String,\n  minWidth: 200,\n  isSortable: true,\n  columnName: 'Incorp. #/Formation #'\n}, {\n  columnId: \"periodEndYear\" /* EsAssesmentListTableColumns.FINANCIAL_PERIOD_END */,\n  type: BdoTableColumnType.String,\n  minWidth: 220,\n  isSortable: true,\n  columnName: 'Financial Period End Year'\n}, {\n  columnId: \"submissionTime\" /* EsAssesmentListTableColumns.SUBMISSION_TIME */,\n  type: BdoTableColumnType.DateTime,\n  minWidth: 200,\n  isSortable: true,\n  columnName: 'Submission Date/Time'\n}, {\n  columnId: \"assessmentStatusId\" /* EsAssesmentListTableColumns.ASSESSMENT_STATUS */,\n  type: BdoTableColumnType.String,\n  minWidth: 190,\n  isSortable: true,\n  columnName: 'ES Assessment Status'\n}, {\n  columnId: \"assessmentAssignedLevel\" /* EsAssesmentListTableColumns.ASSIGNED_LEVEL */,\n  type: BdoTableColumnType.String,\n  minWidth: 150,\n  isSortable: true,\n  columnName: 'Assigned Level'\n}, {\n  columnId: \"assessmentAssignedTo\" /* EsAssesmentListTableColumns.ASSIGNED_USER */,\n  type: BdoTableColumnType.String,\n  minWidth: 145,\n  isSortable: true,\n  columnName: 'Assigned User'\n}, {\n  columnId: \"action\" /* EsAssesmentListTableColumns.ACTION */,\n  type: BdoTableColumnType.SingleActionButton,\n  minWidth: 100,\n  isSortable: false,\n  columnName: 'Actions'\n}];\nexport const actionButtons = [{\n  actionType: 'OpenESDeclaration',\n  icon: 'search',\n  source: \"esAssessmentList\",\n  tooltip: \"Open ES Declaration\"\n}];", "map": {"version": 3, "names": ["BdoTableColumnType", "EsAssesmentListTableColumnsDefination", "columnId", "type", "String", "min<PERSON><PERSON><PERSON>", "frozenLeft", "isSortable", "columnName", "DateTime", "SingleActionButton", "actionButtons", "actionType", "icon", "source", "tooltip"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\es-assesment-list\\containers\\es-assesment-list\\es-assesment-list-table-columns.ts"], "sourcesContent": ["import { BdoTableActionsRowData, BdoTableColumnDefinition, BdoTableColumnType } from \"@app/shared/components/bdo-table/bdo-table.model\";\r\nimport { EsAssesmentListTableColumns } from \"@app/shared/constants/general.constants\";\r\n  \r\nexport const EsAssesmentListTableColumnsDefination: BdoTableColumnDefinition[] = [\r\n    {\r\n        columnId: EsAssesmentListTableColumns.ENTITY_NAME,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 140,\r\n        frozenLeft: true,\r\n        isSortable: true,\r\n        columnName: 'Entity Name',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.RA_NAME,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 120,\r\n        isSortable: true,\r\n        columnName: 'RA Name',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.INCORPORATION_NUMBER,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 200,\r\n        isSortable: true,\r\n        columnName: 'Incorp. #/Formation #',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.FINANCIAL_PERIOD_END,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 220,\r\n        isSortable: true,\r\n        columnName: 'Financial Period End Year',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.SUBMISSION_TIME,\r\n        type: BdoTableColumnType.DateTime,\r\n        minWidth: 200,\r\n        isSortable: true,\r\n        columnName: 'Submission Date/Time',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.ASSESSMENT_STATUS,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 190,\r\n        isSortable: true,\r\n        columnName: 'ES Assessment Status',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.ASSIGNED_LEVEL,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 150,\r\n        isSortable: true,\r\n        columnName: 'Assigned Level',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.ASSIGNED_USER,\r\n        type: BdoTableColumnType.String,\r\n        minWidth: 145,\r\n        isSortable: true,\r\n        columnName: 'Assigned User',\r\n    },\r\n    {\r\n        columnId: EsAssesmentListTableColumns.ACTION,\r\n        type: BdoTableColumnType.SingleActionButton,\r\n        minWidth: 100,\r\n        isSortable: false,\r\n        columnName: 'Actions',\r\n    },\r\n  ];\r\n\r\n\r\n  export const actionButtons: BdoTableActionsRowData[] = [\r\n    {actionType: 'OpenESDeclaration', icon: 'search', source: \"esAssessmentList\", tooltip: \"Open ES Declaration\"},\r\n  ];"], "mappings": "AAAA,SAA2DA,kBAAkB,QAAQ,kDAAkD;AAGvI,OAAO,MAAMC,qCAAqC,GAA+B,CAC7E;EACIC,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACS,QAAQ;EACjCJ,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACI,MAAM;EAC/BC,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACf,EACD;EACIN,QAAQ;EACRC,IAAI,EAAEH,kBAAkB,CAACU,kBAAkB;EAC3CL,QAAQ,EAAE,GAAG;EACbE,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE;CACf,CACF;AAGD,OAAO,MAAMG,aAAa,GAA6B,CACrD;EAACC,UAAU,EAAE,mBAAmB;EAAEC,IAAI,EAAE,QAAQ;EAAEC,MAAM,EAAE,kBAAkB;EAAEC,OAAO,EAAE;AAAqB,CAAC,CAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}