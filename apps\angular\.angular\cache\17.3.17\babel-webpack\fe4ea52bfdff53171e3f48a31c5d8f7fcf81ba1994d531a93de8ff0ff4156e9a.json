{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/fa\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    var i = Math.floor(Math.abs(n));\n    if (i === 0 || n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['fa', [['ق', 'ب'], ['ق.ظ.', 'ب.ظ.'], ['قبل\\u200cازظهر', 'بعدازظهر']], u, [['ی', 'د', 'س', 'چ', 'پ', 'ج', 'ش'], ['یکشنبه', 'دوشنبه', 'سه\\u200cشنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه', 'شنبه'], u, ['۱ش', '۲ش', '۳ش', '۴ش', '۵ش', 'ج', 'ش']], u, [['ژ', 'ف', 'م', 'آ', 'م', 'ژ', 'ژ', 'ا', 'س', 'ا', 'ن', 'د'], ['ژانویهٔ', 'فوریهٔ', 'مارس', 'آوریل', 'مهٔ', 'ژوئن', 'ژوئیهٔ', 'اوت', 'سپتامبر', 'اکتبر', 'نوامبر', 'دسامبر'], u], [['ژ', 'ف', 'م', 'آ', 'م', 'ژ', 'ژ', 'ا', 'س', 'ا', 'ن', 'د'], ['ژانویه', 'فوریه', 'مارس', 'آوریل', 'مه', 'ژوئن', 'ژوئیه', 'اوت', 'سپتامبر', 'اکتبر', 'نوامبر', 'دسامبر'], u], [['ق', 'م'], ['ق.م.', 'م.'], ['قبل از میلاد', 'میلادی']], 6, [5, 5], ['y/M/d', 'd MMM y', 'd MMMM y', 'EEEE d MMMM y'], ['H:mm', 'H:mm:ss', 'H:mm:ss (z)', 'H:mm:ss (zzzz)'], ['{1}،\\u200f {0}', u, '{1}، ساعت {0}', u], ['.', ',', ';', '%', '\\u200e+', '\\u200e−', 'E', '×', '‰', '∞', 'ناعدد', ':'], ['#,##0.###', '#,##0%', '\\u200e¤ #,##0.00', '#E0'], 'IRR', 'ریال', 'ریال ایران', {\n    'AFN': ['؋'],\n    'CAD': ['$CA', '$'],\n    'CNY': ['¥CN', '¥'],\n    'HKD': ['$HK', '$'],\n    'IRR': ['ریال'],\n    'MXN': ['$MX', '$'],\n    'NZD': ['$NZ', '$'],\n    'THB': ['฿'],\n    'XCD': ['$EC', '$']\n  }, 'rtl', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "i", "Math", "floor", "abs", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/fa.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/fa\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        var i = Math.floor(Math.abs(n));\n        if (i === 0 || n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'fa',\n        [['ق', 'ب'], ['ق.ظ.', 'ب.ظ.'], ['قبل\\u200cازظهر', 'بعدازظهر']],\n        u,\n        [\n            ['ی', 'د', 'س', 'چ', 'پ', 'ج', 'ش'],\n            ['یکشنبه', 'دوشنبه', 'سه\\u200cشنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه', 'شنبه'], u,\n            ['۱ش', '۲ش', '۳ش', '۴ش', '۵ش', 'ج', 'ش']\n        ],\n        u,\n        [\n            ['ژ', 'ف', 'م', 'آ', 'م', 'ژ', 'ژ', 'ا', 'س', 'ا', 'ن', 'د'],\n            [\n                'ژانویهٔ', 'فوریهٔ', 'مارس', 'آوریل', 'مهٔ', 'ژوئن', 'ژوئیهٔ', 'اوت', 'سپتامبر', 'اکتبر',\n                'نوامبر', 'دسامبر'\n            ],\n            u\n        ],\n        [\n            ['ژ', 'ف', 'م', 'آ', 'م', 'ژ', 'ژ', 'ا', 'س', 'ا', 'ن', 'د'],\n            [\n                'ژانویه', 'فوریه', 'مارس', 'آوریل', 'مه', 'ژوئن', 'ژوئیه', 'اوت', 'سپتامبر', 'اکتبر',\n                'نوامبر', 'دسامبر'\n            ],\n            u\n        ],\n        [['ق', 'م'], ['ق.م.', 'م.'], ['قبل از میلاد', 'میلادی']],\n        6,\n        [5, 5],\n        ['y/M/d', 'd MMM y', 'd MMMM y', 'EEEE d MMMM y'],\n        ['H:mm', 'H:mm:ss', 'H:mm:ss (z)', 'H:mm:ss (zzzz)'],\n        ['{1}،\\u200f {0}', u, '{1}، ساعت {0}', u],\n        ['.', ',', ';', '%', '\\u200e+', '\\u200e−', 'E', '×', '‰', '∞', 'ناعدد', ':'],\n        ['#,##0.###', '#,##0%', '\\u200e¤ #,##0.00', '#E0'],\n        'IRR',\n        'ریال',\n        'ریال ایران',\n        {\n            'AFN': ['؋'],\n            'CAD': ['$CA', '$'],\n            'CNY': ['¥CN', '¥'],\n            'HKD': ['$HK', '$'],\n            'IRR': ['ریال'],\n            'MXN': ['$MX', '$'],\n            'NZD': ['$NZ', '$'],\n            'THB': ['฿'],\n            'XCD': ['$EC', '$']\n        },\n        'rtl',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;IAC/B,IAAIC,CAAC,KAAK,CAAC,IAAID,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACgB,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,EAC9DP,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACnC,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAC9E,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAC3C,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EACxF,QAAQ,EAAE,QAAQ,CACrB,EACDA,CAAC,CACJ,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EACpF,QAAQ,EAAE,QAAQ,CACrB,EACDA,CAAC,CACJ,EACD,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,EACxD,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EACjD,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,gBAAgB,CAAC,EACpD,CAAC,gBAAgB,EAAEA,CAAC,EAAE,eAAe,EAAEA,CAAC,CAAC,EACzC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,CAAC,EAC5E,CAAC,WAAW,EAAE,QAAQ,EAAE,kBAAkB,EAAE,KAAK,CAAC,EAClD,KAAK,EACL,MAAM,EACN,YAAY,EACZ;IACI,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EACtB,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}