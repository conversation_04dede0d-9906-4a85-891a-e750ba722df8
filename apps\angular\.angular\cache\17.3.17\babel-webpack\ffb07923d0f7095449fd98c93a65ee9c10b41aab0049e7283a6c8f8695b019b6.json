{"ast": null, "code": "export const getBase64Header = fileName => {\n  var split = fileName.split('.');\n  var extension = split[split.length - 1];\n  switch (extension.toLowerCase()) {\n    case \"png\":\n      {\n        return \"data:image/png;base64,\";\n      }\n    case \"bmp\":\n      {\n        return \"data:image/bmp;base64,\";\n      }\n    case \"jpg\":\n      {\n        return \"data:image/jpeg;base64,\";\n      }\n    case \"jpeg\":\n      {\n        return \"data:image/jpeg;base64,\";\n      }\n    case \"pdf\":\n      {\n        return \"data:application/pdf;base64,\";\n      }\n  }\n  return \"\";\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}