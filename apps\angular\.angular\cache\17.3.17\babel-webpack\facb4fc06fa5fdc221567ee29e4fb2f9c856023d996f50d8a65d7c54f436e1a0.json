{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Web Analytics, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nexport { AnalyticsPlugin, AnalyticsPlugin as ApplicationInsights } from \"./JavaScriptSDK/AnalyticsPlugin\";", "map": {"version": 3, "names": ["AnalyticsPlugin", "ApplicationInsights"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-analytics-js/dist-esm/applicationinsights-analytics-js.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Web Analytics, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nexport { AnalyticsPlugin, AnalyticsPlugin as ApplicationInsights } from \"./JavaScriptSDK/AnalyticsPlugin\";\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,eAAe,EAAEA,eAAe,IAAIC,mBAAmB,QAAQ,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}