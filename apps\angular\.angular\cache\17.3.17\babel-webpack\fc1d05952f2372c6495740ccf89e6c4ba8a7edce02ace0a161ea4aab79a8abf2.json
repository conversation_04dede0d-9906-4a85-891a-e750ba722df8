{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"wo\", [[\"<PERSON>\", \"Ngo\"], u, u], u, [[\"Dib\", \"Alt\", \"Tal\", \"<PERSON><PERSON>\", \"Alx\", \"Àjj\", \"<PERSON><PERSON>\"], u, [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\"], [\"Dib\", \"Alt\", \"Tal\", \"<PERSON><PERSON>\", \"Alx\", \"Àjj\", \"Ase\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>wr\", \"<PERSON><PERSON>\", \"<PERSON>w\", \"<PERSON>\", \"U<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>wiyee\", \"<PERSON><PERSON>yee\", \"<PERSON>\", \"Awril\", \"<PERSON>e\", \"<PERSON>we\", \"Sulet\", \"Ut\", \"Sàttumbar\", \"Oktoobar\", \"Nowàmbar\", \"Desàmbar\"]], u, [[\"J<PERSON>\", \"AD\"], u, [\"av. J<PERSON>\", \"AD\"]], 1, [6, 0], [\"dd-MM-y\", \"d MMM, y\", \"d MMMM, y\", \"EEEE, d MMM, y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} - {0}\", u, \"{1} 'ci' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"XOF\", \"F CFA\", \"Franc CFA bu Afrik Sowwu-jant\", {\n  \"JPY\": [\"JP¥\", \"¥\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/wo.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"wo\", [[\"<PERSON>\", \"Ngo\"], u, u], u, [[\"Dib\", \"Alt\", \"Tal\", \"<PERSON><PERSON>\", \"Alx\", \"Àjj\", \"<PERSON><PERSON>\"], u, [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\"], [\"Dib\", \"Alt\", \"Tal\", \"<PERSON><PERSON>\", \"Alx\", \"Àjj\", \"Ase\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>wr\", \"<PERSON><PERSON>\", \"<PERSON>w\", \"<PERSON>\", \"U<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>wiyee\", \"<PERSON><PERSON>yee\", \"<PERSON>\", \"Awril\", \"<PERSON>e\", \"<PERSON>we\", \"Sulet\", \"Ut\", \"Sàttumbar\", \"Oktoobar\", \"Nowàmbar\", \"Desàmbar\"]], u, [[\"J<PERSON>\", \"AD\"], u, [\"av. J<PERSON>\", \"AD\"]], 1, [6, 0], [\"dd-MM-y\", \"d MMM, y\", \"d MMMM, y\", \"EEEE, d MMM, y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} - {0}\", u, \"{1} 'ci' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"XOF\", \"F CFA\", \"Franc CFA bu Afrik Sowwu-jant\", { \"JPY\": [\"JP¥\", \"¥\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAEA,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,WAAW,EAAEA,CAAC,EAAE,cAAc,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,+BAA+B,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}