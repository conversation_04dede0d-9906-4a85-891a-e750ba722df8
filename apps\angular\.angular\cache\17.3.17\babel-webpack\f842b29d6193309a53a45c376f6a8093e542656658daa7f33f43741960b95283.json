{"ast": null, "code": "import { mapEnumToOptions } from '@abp/ng.core';\nexport var HistoryActionType = /*#__PURE__*/function (HistoryActionType) {\n  HistoryActionType[HistoryActionType[\"AssessmentUpdated\"] = 1] = \"AssessmentUpdated\";\n  HistoryActionType[HistoryActionType[\"AssessmentActionPerformed\"] = 2] = \"AssessmentActionPerformed\";\n  HistoryActionType[HistoryActionType[\"DeclarationReopened\"] = 3] = \"DeclarationReopened\";\n  return HistoryActionType;\n}(HistoryActionType || {});\nexport const historyActionTypeOptions = mapEnumToOptions(HistoryActionType);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}