{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"rn\", [[\"Z.MU.\", \"Z.MW.\"], u, u], u, [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"cu.\", \"mbe.\", \"kab.\", \"gtu.\", \"kan.\", \"gnu.\", \"gnd.\"], [\"Ku w’indwi\", \"Ku wa mbere\", \"Ku wa kabiri\", \"Ku wa gatatu\", \"Ku wa kane\", \"Ku wa gatanu\", \"Ku wa gatandatu\"], [\"cu.\", \"mbe.\", \"kab.\", \"gtu.\", \"kan.\", \"gnu.\", \"gnd.\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"Mut.\", \"Gas.\", \"Wer.\", \"Mat.\", \"Gic.\", \"Kam.\", \"Nya.\", \"Kan.\", \"Nze.\", \"Ukw.\", \"Ugu.\", \"Uku.\"], [\"Nzero\", \"Ruhuhuma\", \"Ntwarante\", \"Ndamukiza\", \"Rusama\", \"Ruheshi\", \"Mukakaro\", \"Nyandagaro\", \"Nyakanga\", \"Gitugutu\", \"Munyonyo\", \"Kigarama\"]], u, [[\"Mb.Y.\", \"Ny.Y\"], u, [\"Mbere ya Yezu\", \"Nyuma ya Yezu\"]], 1, [6, 0], [\"d/M/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00¤\", \"#E0\"], \"BIF\", \"FBu\", \"Ifaranga ry’Uburundi\", {\n  \"BIF\": [\"FBu\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/rn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"rn\", [[\"Z.MU.\", \"Z.MW.\"], u, u], u, [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"cu.\", \"mbe.\", \"kab.\", \"gtu.\", \"kan.\", \"gnu.\", \"gnd.\"], [\"Ku w’indwi\", \"Ku wa mbere\", \"Ku wa kabiri\", \"Ku wa gatatu\", \"Ku wa kane\", \"Ku wa gatanu\", \"Ku wa gatandatu\"], [\"cu.\", \"mbe.\", \"kab.\", \"gtu.\", \"kan.\", \"gnu.\", \"gnd.\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"Mut.\", \"Gas.\", \"Wer.\", \"Mat.\", \"Gic.\", \"Kam.\", \"Nya.\", \"Kan.\", \"Nze.\", \"Ukw.\", \"Ugu.\", \"Uku.\"], [\"Nzero\", \"Ruhuhuma\", \"Ntwarante\", \"Ndamukiza\", \"Rusama\", \"Ruheshi\", \"Mukakaro\", \"Nyandagaro\", \"Nyakanga\", \"Gitugutu\", \"Munyonyo\", \"Kigarama\"]], u, [[\"Mb.Y.\", \"Ny.Y\"], u, [\"Mbere ya Yezu\", \"Nyuma ya Yezu\"]], 1, [6, 0], [\"d/M/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00¤\", \"#E0\"], \"BIF\", \"FBu\", \"Ifaranga ry’Uburundi\", { \"BIF\": [\"FBu\"], \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}