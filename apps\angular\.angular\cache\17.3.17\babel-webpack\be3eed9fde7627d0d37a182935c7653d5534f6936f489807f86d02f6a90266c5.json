{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { tap } from 'rxjs';\nimport { BdoTableConstants } from './bdo-table.constants';\nimport { BdoTableCellLinkClickEvent, BdoTableCheckboxClickEvent, BdoTableColumnType, BdoTableLazyLoadEvent, BdoTableRowActionClickEvent, BdoTableRowClickEvent } from './bdo-table.model';\nimport { BdoTableRowDetailTemplateDirective } from './bdo-table-row-detail-template.directive';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { BdoTableFooterRowTemplateDirective } from './bdo-table-footer-row-template.directive';\nimport { AppComponentBase } from '../../../app-component-base';\nimport { StringHelper } from '@app/shared/utils';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/menu\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/paginator\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/sort\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/tooltip\";\nimport * as i12 from \"ngx-intl-tel-input\";\nconst _c0 = [\"bdotable\"];\nconst _c1 = () => [\"expandedDetail\"];\nconst _c2 = a0 => ({\n  row: a0\n});\nconst _c3 = () => ({});\nfunction BdoTableComponent_mat_header_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 20);\n    i0.ɵɵtext(1, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n  }\n}\nfunction BdoTableComponent_mat_cell_5_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_mat_cell_5_button_1_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const element_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.expandedElement = ctx_r3.expandedElement === element_r3 ? null : element_r3;\n      ctx_r3.toggleExpandRow(element_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BdoTableComponent_mat_cell_5_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_mat_cell_5_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const element_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.expandedElement = ctx_r3.expandedElement === element_r3 ? null : element_r3;\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BdoTableComponent_mat_cell_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_mat_cell_5_button_1_Template, 1, 0, \"button\", 21)(2, BdoTableComponent_mat_cell_5_button_2_Template, 1, 0, \"button\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !element_r3.hideExpand && ctx_r3.expandedElement !== element_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !element_r3.hideExpand && ctx_r3.expandedElement === element_r3);\n  }\n}\nfunction BdoTableComponent_mat_footer_cell_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\");\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n  }\n}\nfunction BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.isAllSelected, $event) || (ctx_r3.isAllSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.selectAll($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.isAllSelected);\n  }\n}\nfunction BdoTableComponent_mat_header_cell_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template, 1, 1, \"mat-checkbox\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasCheckboxAll);\n  }\n}\nfunction BdoTableComponent_mat_cell_9_mat_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BdoTableComponent_mat_cell_9_mat_checkbox_1_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const row_r8 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(row_r8.checked, $event) || (row_r8.checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function BdoTableComponent_mat_cell_9_mat_checkbox_1_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const row_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckRow(row_r8, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", row_r8.checked);\n  }\n}\nfunction BdoTableComponent_mat_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_mat_cell_9_mat_checkbox_1_Template, 1, 1, \"mat-checkbox\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !row_r8.cannotCheck);\n  }\n}\nfunction BdoTableComponent_mat_footer_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\");\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n  }\n}\nfunction BdoTableComponent_mat_cell_12_2_ng_template_0_Template(rf, ctx) {}\nfunction BdoTableComponent_mat_cell_12_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BdoTableComponent_mat_cell_12_2_ng_template_0_Template, 0, 0, \"ng-template\", 28);\n  }\n  if (rf & 2) {\n    const element_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.rowDetailTemplate.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, element_r9));\n  }\n}\nfunction BdoTableComponent_mat_cell_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"div\", 27);\n    i0.ɵɵtemplate(2, BdoTableComponent_mat_cell_12_2_Template, 1, 4, null, 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"colspan\", ctx_r3.displayedColumns.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", element_r9 === ctx_r3.expandedElement ? \"bdo-table-row-expanded\" : \"bdo-table-row-collapsed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.rowDetailTemplate);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_header_cell_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 33);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleProp(\"width\", col_r10.width ? col_r10.width + \"px\" : \"auto\")(\"min-width\", col_r10.minWidth ? col_r10.minWidth + \"px\" : \"100px\")(\"max-width\", col_r10.maxWidth ? col_r10.maxWidth + \"px\" : \"auto\");\n    i0.ɵɵproperty(\"mat-sort-header\", col_r10.columnId != \"actions\" && col_r10.isSortable ? col_r10.sortColumnId : null)(\"disabled\", col_r10.columnId === \"actions\" || !col_r10.isSortable ? true : false);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(col_r10.class);\n    i0.ɵɵproperty(\"innerHtml\", col_r10.columnName, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(row_r11, col_r10.columnId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r3.getCell(row_r11, col_r10.columnId).value));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getCell(row_r11, col_r10.columnId).value, \" \");\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getCell(row_r11, col_r10.columnId).value ? i0.ɵɵpipeBind1(2, 1, \"::General:Yes\") : i0.ɵɵpipeBind1(3, 3, \"::General:No\"), \" \");\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 41);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 42);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_1_Template, 1, 0, \"span\", 39)(2, BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_2_Template, 1, 0, \"span\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getCell(row_r11, col_r10.columnId).value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(row_r11, col_r10.columnId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(2, 2, (tmp_7_0 = ctx_r3.getCell(row_r11, col_r10.columnId)) == null ? null : tmp_7_0.value, \"dd/MM/yyyy\", \"local\"));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(row_r11, col_r10.columnId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(2, 2, (tmp_7_0 = ctx_r3.getCell(row_r11, col_r10.columnId)) == null ? null : tmp_7_0.value, \"dd/MM/yyyy HH:mm\", \"local\"));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getCell(row_r11, col_r10.columnId).value, \" \");\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.linkClick(row_r11, col_r10.columnId, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getCell(row_r11, col_r10.columnId).value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template, 1, 1, \"a\", 44)(2, BdoTableComponent_ng_container_13_mat_cell_2_div_9_div_2_Template, 2, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getCell(row_r11, col_r10.columnId).isTextInsteadOfLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Link && ctx_r3.getCell(row_r11, col_r10.columnId).isTextInsteadOfLink);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 48);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const i_r16 = i0.ɵɵnextContext().index;\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.linkClick(row_r11, col_r10.columnId, $event, i_r16));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(child_r17);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template, 2, 1, \"a\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getCell(row_r11, col_r10.columnId).isTextInsteadOfLink);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(child_r18);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_11_div_1_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"mat-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_mat_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const row_r11 = i0.ɵɵnextContext().$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.getCell(row_r11, col_r10.columnId).value, $event) || (ctx_r3.getCell(row_r11, col_r10.columnId).value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_mat_checkbox_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const row_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onCheckRow(row_r11, $event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 49);\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getCell(row_r11, col_r10.columnId).value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 51);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template_a_click_0_listener($event) {\n      const action_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.actionClick(action_r22.actionType, row_r11.id, row_r11, $event));\n    });\n    i0.ɵɵelement(1, \"span\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", action_r22.tooltip);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r22.icon);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template, 2, 4, \"a\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const row_r11 = i0.ɵɵnextContext().$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.actionClick(ctx_r3.getCell(row_r11, col_r10.columnId).value.actionType, row_r11.id, row_r11, null));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r3.getCell(row_r11, col_r10.columnId).value.tooltip);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getCell(row_r11, col_r10.columnId).value.icon);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const item_r26 = i0.ɵɵnextContext().$implicit;\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.actionClick(item_r26.actionType, row_r11.id, row_r11, null));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r26.displayName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r26.icon);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template, 4, 2, \"button\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r26 = ctx.$implicit;\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hideActionButton(item_r26, row_r11));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"button\", 54)(2, \"mat-icon\", 55);\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 1);\n    i0.ɵɵtemplate(6, BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const menu_r27 = i0.ɵɵreference(5);\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", menu_r27);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_1_Template, 2, 2, \"div\", 35)(2, BdoTableComponent_ng_container_13_mat_cell_2_div_2_Template, 3, 3, \"div\", 17)(3, BdoTableComponent_ng_container_13_mat_cell_2_div_3_Template, 2, 1, \"div\", 17)(4, BdoTableComponent_ng_container_13_mat_cell_2_div_4_Template, 4, 5, \"div\", 17)(5, BdoTableComponent_ng_container_13_mat_cell_2_div_5_Template, 3, 2, \"div\", 17)(6, BdoTableComponent_ng_container_13_mat_cell_2_div_6_Template, 3, 6, \"div\", 35)(7, BdoTableComponent_ng_container_13_mat_cell_2_div_7_Template, 3, 6, \"div\", 35)(8, BdoTableComponent_ng_container_13_mat_cell_2_div_8_Template, 2, 1, \"div\", 17)(9, BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template, 3, 2, \"div\", 36)(10, BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template, 2, 1, \"div\", 36)(11, BdoTableComponent_ng_container_13_mat_cell_2_div_11_Template, 2, 1, \"div\", 17)(12, BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template, 2, 1, \"div\", 36)(13, BdoTableComponent_ng_container_13_mat_cell_2_div_13_Template, 1, 1, \"div\", 37)(14, BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template, 2, 1, \"div\", 36)(15, BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template, 4, 2, \"div\", 36)(16, BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template, 7, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const row_r11 = ctx.$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap((tmp_5_0 = ctx_r3.getCell(row_r11, col_r10.columnId)) == null ? null : tmp_5_0.class);\n    i0.ɵɵstyleProp(\"width\", col_r10.width ? col_r10.width + \"px\" : \"auto\")(\"min-width\", col_r10.minWidth ? col_r10.minWidth + \"px\" : \"100px\")(\"max-width\", col_r10.maxWidth ? col_r10.maxWidth + \"px\" : \"auto\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.String);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Currency);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Boolean);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.YesNo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Date);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.DateTime);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.FileSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.LinkArray);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Array);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Checkbox && !ctx_r3.getCell(row_r11, col_r10.columnId).hide);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Html);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Actions && ctx_r3.getCell(row_r11, col_r10.columnId).value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.SingleActionButton && ctx_r3.getCell(row_r11, col_r10.columnId).value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.ThreeDotActions && ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_footer_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-footer-cell\");\n    i0.ɵɵelement(1, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", col_r10.width ? col_r10.width + \"px\" : \"auto\")(\"min-width\", col_r10.minWidth ? col_r10.minWidth + \"px\" : \"100px\")(\"max-width\", col_r10.maxWidth ? col_r10.maxWidth + \"px\" : \"auto\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (tmp_7_0 = ctx_r3.getFooterCell(col_r10.columnId)) == null ? null : tmp_7_0.value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 29);\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_header_cell_1_Template, 2, 11, \"mat-header-cell\", 30)(2, BdoTableComponent_ng_container_13_mat_cell_2_Template, 17, 24, \"mat-cell\", 31)(3, BdoTableComponent_ng_container_13_mat_footer_cell_3_Template, 2, 7, \"mat-footer-cell\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"matColumnDef\", col_r10.columnId)(\"sticky\", col_r10.frozenLeft)(\"stickyEnd\", col_r10.frozenRight || col_r10.columnId === \"actions\");\n  }\n}\nfunction BdoTableComponent_mat_header_row_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction BdoTableComponent_mat_row_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-row\", 58);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_mat_row_15_Template_mat_row_click_0_listener() {\n      const element_r29 = i0.ɵɵrestoreView(_r28).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.rowClick(element_r29));\n    })(\"click\", function BdoTableComponent_mat_row_15_Template_mat_row_click_0_listener() {\n      const element_r29 = i0.ɵɵrestoreView(_r28).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.expandedElement = ctx_r3.expandedElement === element_r29 ? null : element_r29);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r29 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(element_r29.class);\n    i0.ɵɵclassProp(\"bdo-table-row-selectable\", ctx_r3.rowSelectable)(\"bdo-table-row-selected\", ctx_r3.selectedRow === element_r29)(\"example-expanded-row\", ctx_r3.expandedElement === element_r29);\n  }\n}\nfunction BdoTableComponent_mat_row_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 59);\n  }\n}\nfunction BdoTableComponent_mat_footer_row_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-row\");\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r3.footerCells && ctx_r3.footerCells.length > 0 ? \"flex\" : \"none\");\n  }\n}\nfunction BdoTableComponent_18_ng_template_0_Template(rf, ctx) {}\nfunction BdoTableComponent_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BdoTableComponent_18_ng_template_0_Template, 0, 0, \"ng-template\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.footerTemplate.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction0(2, _c3));\n  }\n}\nfunction BdoTableComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 60);\n  }\n}\nconst tablePaginationRowOption = [50, 100, 200];\nexport class BdoTableComponent extends AppComponentBase {\n  constructor(injector, renderer, changeDetectorRef, permissionService) {\n    super(injector);\n    this.renderer = renderer;\n    this.changeDetectorRef = changeDetectorRef;\n    this.permissionService = permissionService;\n    this.defaultSortOrder = 'asc'; //asc, desc\n    this.pageSize = 25; //if 0, pagination is disabled\n    this.pageIndex = 0; //page number to display initially\n    this.selectedRow = null;\n    this.hasRowCheckbox = false; // indicates whether the rows have checkboxes\n    this.isVirtualScroll = false;\n    this.hidePagination = false;\n    this.scrollHeight = '70vh'; // define height of the scrollable body of table\n    this.headerHeight = 40; // height of column header row\n    this.footerHeight = 40; // height of footer row\n    this.isRowExpandable = false; // indicates if each row can be expanded to see more details\n    this.rowDetailHeight = 100; //height of row detail expanded section\n    this.pageSizeOptions = tablePaginationRowOption;\n    this.hideLastPage = false;\n    //event triggered when selecting a row on grid\n    this.onRowClick = new EventEmitter();\n    //event triggered when clicking on action icon on row\n    this.onActionClick = new EventEmitter();\n    //event triggered when clicking on link (anchor tag) on row cell\n    this.onLinkClick = new EventEmitter();\n    //event triggered instructing consumer component to initiate request to server to load data\n    //Used for server-side paging to work\n    this.onLazyLoad = new EventEmitter();\n    //event triggered when user clicks checkbox on grid row\n    this.onCheckboxClick = new EventEmitter();\n    //event triggered when paging the grid (user navigates to different page using pagination controls)\n    this.onPage = new EventEmitter();\n    // event triggered when a row is expanded\n    this.onRowExpand = new EventEmitter();\n    this.deleteAction = BdoTableConstants.ActionName.Delete;\n    //store copy of unfiltered data -> used for filter results for search filter\n    this.rows = [];\n    this.allRows = [];\n    this.cache = {};\n    //TODO - localize\n    this.totalMessage = 'total';\n    this.selectedMessage = null;\n    this.BdoTableColumnType = BdoTableColumnType;\n    this.displayedColumns = [];\n    this.sortField = '';\n    this.sortDirection = '';\n    this.expanded = {};\n    //avoids scroll event from firing multiple API calls to fetch same paged data\n    this.disableNextRowFetch = false;\n    this.footerCells = [];\n    this.isAllSelected = false;\n    this.totalMessage = \"Total\";\n  }\n  ngOnInit() {\n    this.sortField = this.defaultSortColumnId;\n    this.sortDirection = this.defaultSortOrder;\n    this.dataSubscription = this.tableService.data.subscribe(data => {\n      //update the correct table component if multiple instances of table on same page\n      if (data && data.tableId === this.id) {\n        this.footerCells = data.footerCells;\n        // set paginator page size\n        this.paginator.pageSize = this.pageSize;\n        this.sort.active = this.sortField;\n        this.sort.direction = this.sortDirection;\n        //need a delay to update columns on table if dynamically changed\n        setTimeout(() => {\n          this.displayedColumns = [];\n          if (this.hasRowCheckbox) {\n            this.displayedColumns.push('select');\n          }\n          if (this.isRowExpandable) {\n            this.displayedColumns.push('expand');\n          }\n          this.columns.forEach(x => {\n            this.displayedColumns.push(x.columnId);\n          });\n          this.changeDetectorRef.detectChanges();\n        }, 200);\n        //set height of table\n        if (this.id) {\n          const tableElement = document.getElementsByClassName(this.id);\n          if (tableElement && tableElement.length > 0 && tableElement[0].tagName === 'MAT-TABLE') {\n            this.renderer.setStyle(tableElement[0], 'max-height', this.scrollHeight);\n            this.renderer.setStyle(tableElement[0], 'overflow-y', 'auto');\n            //scroll to top whenever resetting to first page\n            if (data.resetToFirstPage) {\n              tableElement[0].scrollTop = 0;\n            }\n          }\n        }\n        this.changeDetectorRef.detectChanges();\n        //reset table to first page\n        if (data.resetToFirstPage) {\n          this.pageIndex = 0;\n          this.rows = [];\n          this.totalRecords = 0;\n          this.cache = {};\n        }\n        if (this.isVirtualScroll) {\n          // Update total count\n          this.totalRecords = data.totalRecords;\n          // Create array to store data if missing\n          // The array should have the correct number of with \"holes\" for missing data\n          if (!this.rows) {\n            this.rows = JSON.parse(JSON.stringify(data.data));\n          }\n          // Calc starting row offset\n          // This is the position to insert the new data\n          const start = this.pageIndex * this.pageSize;\n          // Copy existing data\n          const rows = [...this.rows];\n          // Insert new rows into correct position\n          if (data.data && data.data.length > 0) {\n            rows.splice(start, this.pageSize, ...JSON.parse(JSON.stringify(data.data)));\n          }\n          // Set rows to our new rows for display\n          this.rows = rows;\n          this.disableNextRowFetch = false;\n        } else {\n          this.allRows = JSON.parse(JSON.stringify(data.data));\n          if (this.lazyLoad) {\n            this.rows = this.allRows;\n          } else {\n            const start = this.pageIndex * this.pageSize;\n            var tempRows = [...this.allRows];\n            this.rows = tempRows.splice(start, this.pageSize);\n          }\n          this.totalRecords = data.totalRecords;\n        }\n        if (this.rowSelectable) {\n          if (this.table && this.rows[0]) {\n            //this.selectedRow = this.rows[0]; //auto selects first row in the table when rendered if rows are selectable \n            //this.rowClick(this.selectedRow);\n          }\n        }\n      }\n    });\n  }\n  ngAfterViewInit() {\n    if (this.paginator) {\n      this.paginator.pageSize = this.pageSize;\n    }\n    if (this.sort) {\n      this.sort.active = this.sortField;\n      this.sort.direction = this.sortDirection;\n    }\n    // when paginator event is invoked, retrieve the related data\n    this.pageSubscription = this.paginator.page.pipe(tap(() => {\n      this.pageIndex = this.paginator.pageIndex;\n      this.pageSize = this.paginator.pageSize;\n      if (this.lazyLoad) {\n        const lazyLoadEvent = new BdoTableLazyLoadEvent();\n        lazyLoadEvent.pageNumber = this.pageIndex;\n        lazyLoadEvent.pageSize = this.pageSize;\n        lazyLoadEvent.sortField = StringHelper.capitalizeFirstLetter(this.sortField);\n        lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\n        //triggers event instructing consumer component to initiate request to server to load data\n        //passes sorting and pagination info for consumer to include in requets object to load data from server\n        this.onLazyLoad.emit(lazyLoadEvent);\n      } else {\n        var tempRows = [...this.allRows];\n        var start = this.pageIndex * this.pageSize;\n        this.rows = tempRows.splice(start, this.pageSize);\n      }\n    })).subscribe();\n    this.sortSubscription = this.sort.sortChange.pipe(tap(() => {\n      this.sortField = this.sort.active;\n      this.sortDirection = this.sort.direction;\n      if (this.lazyLoad) {\n        this.pageIndex = 0;\n        const lazyLoadEvent = new BdoTableLazyLoadEvent();\n        lazyLoadEvent.pageNumber = this.pageIndex;\n        lazyLoadEvent.pageSize = this.pageSize;\n        lazyLoadEvent.sortField = StringHelper.capitalizeFirstLetter(this.sortField);\n        lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\n        //triggers event instructing consumer component to initiate request to server to load data\n        //passes sorting and pagination info for consumer to include in requets object to load data from server\n        this.onLazyLoad.emit(lazyLoadEvent);\n      } else {\n        const rows = [...this.rows];\n        rows.sort((a, b) => {\n          //TODO - handling sorting for dates, html, boolean, etc..\n          const fieldA = a.cells.find(x => x.columnId.toLowerCase() === this.sortField.toLowerCase())?.value;\n          const fieldB = b.cells.find(x => x.columnId.toLowerCase() === this.sortField.toLowerCase())?.value;\n          return fieldA.localeCompare(fieldB) * (this.sortDirection === 'desc' ? -1 : 1);\n        });\n        this.rows = rows;\n      }\n    })).subscribe();\n  }\n  ngAfterViewChecked() {\n    //set height of table\n    if (this.id) {\n      const tableElement = document.getElementsByClassName(this.id);\n      if (tableElement && tableElement.length > 0 && tableElement[0].tagName === 'MAT-TABLE') {\n        this.renderer.setStyle(tableElement[0], 'max-height', this.scrollHeight);\n        this.renderer.setStyle(tableElement[0], 'overflow-y', 'auto');\n      }\n    }\n    if (this.hideLastPage) {\n      var lastPageEl = document.getElementsByClassName('mat-mdc-paginator-navigation-last');\n      if (lastPageEl) {\n        var l = lastPageEl[0];\n        l.classList.add('hidden');\n      }\n    }\n  }\n  onTableScroll(e) {\n    //virtual scrolling is disabled\n    if (!this.isVirtualScroll) return;\n    //we have retrieved all records\n    if (this.rows.length === this.totalRecords || this.disableNextRowFetch) return;\n    const tableViewHeight = e.target.offsetHeight; // viewport: ~500px\n    const tableScrollHeight = e.target.scrollHeight; // length of all table\n    const scrollLocation = e.target.scrollTop; // how far user scrolled\n    // If the user has scrolled within 10px of the bottom, add more data\n    const buffer = 10;\n    const limit = tableScrollHeight - tableViewHeight - buffer;\n    if (scrollLocation > limit) {\n      this.disableNextRowFetch = true;\n      // We keep a index of server loaded pages so we don't load same data twice\n      // This is based on the server page not the UI\n      if (this.cache[this.pageIndex + 1]) return;\n      this.cache[this.pageIndex + 1] = true;\n      this.pageIndex = this.pageIndex + 1;\n      const lazyLoadEvent = new BdoTableLazyLoadEvent();\n      lazyLoadEvent.pageNumber = this.pageIndex;\n      lazyLoadEvent.pageSize = this.pageSize;\n      lazyLoadEvent.sortField = this.sortField;\n      lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\n      //triggers event instructing consumer component to initiate request to server to load data\n      //passes sorting and pagination info for consumer to include in requets object to load data from server\n      this.onLazyLoad.emit(lazyLoadEvent);\n    }\n  }\n  rowClick(row) {\n    //row is not selectable\n    if (!this.rowSelectable) return;\n    this.selectedRow = row;\n    const event = new BdoTableRowClickEvent();\n    event.id = row.id;\n    event.rowData = row;\n    this.onRowClick.emit(event);\n  }\n  linkClick(row, columnId, ev, i) {\n    const event = new BdoTableCellLinkClickEvent();\n    event.id = row.id;\n    event.rawData = row.rawData;\n    event.columnId = columnId;\n    event.event = ev;\n    event.arrayIndex = i;\n    this.onLinkClick.emit(event);\n  }\n  actionClick(action, id, row, ev) {\n    this.selectedRow = this.rows.find(x => {\n      return x.id === id;\n    });\n    if (this.selectedRow) {\n      this.rowClick(row);\n    }\n    const event = new BdoTableRowActionClickEvent();\n    event.action = action;\n    event.id = id;\n    event.data = row;\n    event.event = ev;\n    this.onActionClick.emit(event);\n  }\n  getCell(row, columnId) {\n    if (!row || !row.cells) return null;\n    const cell = row.cells.find(cell => {\n      return cell.columnId === columnId;\n    });\n    return cell;\n  }\n  getStatusClass(row, columnName) {\n    if (row.rawData?.isDeleted) {\n      return 'legend-pink legend-linethrough';\n    }\n    if (columnName === \"declarationStatus\" || columnName === 'assessmentStatus') {\n      if (row.rawData.assessmentStatus?.toLowerCase() == 'not started') return 'legend-black';\n      const status = row.rawData?.status ? row.rawData?.status.toLowerCase() : row.rawData.assessmentStatus?.toLowerCase();\n      switch (status) {\n        case 'not started':\n          return 'legend-pink';\n        case 'draft':\n        case 'information required':\n          return 'legend-navy';\n        case 'reopened':\n        case 'provisional fail':\n          return 'legend-orange';\n        case 'submitted':\n        case 'resubmitted':\n        case 'unsubmitted':\n          return 'legend-black';\n        case 'deleted':\n          return 'legend-pink legend-linethrough';\n        case 'pass':\n          return 'legend-green';\n        case 'fail':\n          return 'legend-red';\n        case 'provisional pass':\n          return 'legend-yellow';\n        case 'closed':\n          return 'legend-grey';\n        default:\n          return 'legend-black';\n      }\n    }\n    return '';\n  }\n  hideActionButton(buttonInfo, row) {\n    if (buttonInfo.source === \"declarationHistory\") {\n      const addEditSubmitPermission = this.permissionService.getGrantedPolicy('EsService.Declaration.Submit');\n      const viewPermission = this.permissionService.getGrantedPolicy('SearchService.BasicSearch.ViewDeclaration');\n      if (buttonInfo.actionType === 'delete') {\n        if (!(row.rawData.status === \"Draft\" || row.rawData.status === 'Reopened') || !addEditSubmitPermission) {\n          return false;\n        }\n      }\n      if (buttonInfo.actionType === 'edit') {\n        if (row.rawData.status === \"Submitted\" || row.rawData.status === 'Resubmitted' || !addEditSubmitPermission) {\n          return false;\n        }\n      }\n      if (buttonInfo.actionType === 'view') {\n        if (!viewPermission) {\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  getRowHeight(row) {\n    if (!row) {\n      return 40;\n    }\n    if (row.height === undefined) {\n      return 40;\n    }\n    return row.height;\n  }\n  getCellClass({\n    row,\n    column,\n    value\n  }) {\n    return row.cells.find(x => x.columnId === column.name)?.class;\n  }\n  getFooterCell(columnId) {\n    if (!this.footerCells) return null;\n    const cell = this.footerCells.find(cell => {\n      return cell.columnId === columnId;\n    });\n    return cell;\n  }\n  getRowClass(row) {\n    return row.class;\n  }\n  toggleExpandRow(row) {\n    setTimeout(() => {\n      this.onRowExpand.emit({\n        id: row.id,\n        rawData: row.rawData\n      });\n    }, 200);\n  }\n  selectAll(event) {\n    this.rows.forEach(r => {\n      r.checked = event.checked;\n    });\n    const checkBoxEvent = new BdoTableCheckboxClickEvent(event.checked ? this.rows : [], null, event.checked, true);\n    this.onCheckboxClick.emit(checkBoxEvent);\n  }\n  onCheckRow(row, event) {\n    const checkBoxEvent = new BdoTableCheckboxClickEvent([row], row.id, event.checked, false);\n    this.onCheckboxClick.emit(checkBoxEvent);\n  }\n  ngOnDestroy() {\n    if (this.dataSubscription) this.dataSubscription.unsubscribe();\n    if (this.pageSubscription) this.pageSubscription.unsubscribe();\n    if (this.sortSubscription) this.sortSubscription.unsubscribe();\n  }\n  static {\n    this.ɵfac = function BdoTableComponent_Factory(t) {\n      return new (t || BdoTableComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PermissionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BdoTableComponent,\n      selectors: [[\"bdo-table\"]],\n      contentQueries: function BdoTableComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, BdoTableRowDetailTemplateDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, BdoTableFooterRowTemplateDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowDetailTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n        }\n      },\n      viewQuery: function BdoTableComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        columns: \"columns\",\n        isSortable: \"isSortable\",\n        defaultSortColumnId: \"defaultSortColumnId\",\n        defaultSortOrder: \"defaultSortOrder\",\n        pageSize: \"pageSize\",\n        pageIndex: \"pageIndex\",\n        width: \"width\",\n        lazyLoad: \"lazyLoad\",\n        rowSelectable: \"rowSelectable\",\n        allowOnlySingleSelection: \"allowOnlySingleSelection\",\n        hasCheckboxAll: \"hasCheckboxAll\",\n        selectedRow: \"selectedRow\",\n        hasRowCheckbox: \"hasRowCheckbox\",\n        isVirtualScroll: \"isVirtualScroll\",\n        hidePagination: \"hidePagination\",\n        scrollHeight: \"scrollHeight\",\n        headerHeight: \"headerHeight\",\n        footerHeight: \"footerHeight\",\n        isRowExpandable: \"isRowExpandable\",\n        rowDetailHeight: \"rowDetailHeight\",\n        pageSizeOptions: \"pageSizeOptions\",\n        hideLastPage: \"hideLastPage\",\n        headerCheckboxValue: \"headerCheckboxValue\",\n        totalMessage: \"totalMessage\",\n        selectedMessage: \"selectedMessage\"\n      },\n      outputs: {\n        onRowClick: \"onRowClick\",\n        onActionClick: \"onActionClick\",\n        onLinkClick: \"onLinkClick\",\n        onLazyLoad: \"onLazyLoad\",\n        onCheckboxClick: \"onCheckboxClick\",\n        onPage: \"onPage\",\n        onRowExpand: \"onRowExpand\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 21,\n      vars: 25,\n      consts: [[\"bdotable\", \"\"], [\"menu\", \"matMenu\"], [1, \"bdo-table-container\", \"table-responsive\"], [\"matSort\", \"\", \"matSortDisableClear\", \"\", \"multiTemplateDataRows\", \"\", 1, \"row-hoverable\", 3, \"scroll\", \"dataSource\", \"matSortActive\", \"matSortDirection\"], [\"matColumnDef\", \"expand\", \"sticky\", \"\"], [\"aria-label\", \"row actions\", 3, \"max-width\", \"min-width\", 4, \"matHeaderCellDef\"], [3, \"max-width\", \"min-width\", 4, \"matCellDef\"], [3, \"max-width\", \"min-width\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"select\", 3, \"sticky\"], [3, \"max-width\", \"min-width\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"expandedDetail\"], [4, \"matCellDef\"], [3, \"matColumnDef\", \"sticky\", \"stickyEnd\", 4, \"ngFor\", \"ngForOf\"], [4, \"matHeaderRowDef\", \"matHeaderRowDefSticky\"], [\"class\", \"bdo-table-expanded-element-row\", 3, \"class\", \"bdo-table-row-selectable\", \"bdo-table-row-selected\", \"example-expanded-row\", \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"bdo-table-expanded-detail-row\", 4, \"matRowDef\", \"matRowDefColumns\"], [3, \"display\", 4, \"matFooterRowDef\", \"matFooterRowDefSticky\"], [4, \"ngIf\"], [\"class\", \"bdo-table-virtual-scroll-paginator-footer p-1 pb-0 ml-0 mr-0 grid\", 4, \"ngIf\"], [\"sticky\", \"\", 1, \"mat-paginator-sticky\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"disabled\", \"hidePageSize\", \"showFirstLastButtons\", \"pageIndex\"], [\"aria-label\", \"row actions\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-right\", \"class\", \"p-button-rounded p-button-text\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-down\", \"class\", \"p-button-rounded p-button-text\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-right\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-down\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\"], [\"class\", \"checkBox form-field\", 3, \"ngModel\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [1, \"checkBox\", \"form-field\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"bdo-table-expanded-element-detail\", 3, \"ngClass\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"matColumnDef\", \"sticky\", \"stickyEnd\"], [3, \"mat-sort-header\", \"disabled\", \"width\", \"min-width\", \"max-width\", 4, \"matHeaderCellDef\"], [3, \"class\", \"width\", \"min-width\", \"max-width\", 4, \"matCellDef\"], [3, \"width\", \"min-width\", \"max-width\", 4, \"matFooterCellDef\"], [3, \"mat-sort-header\", \"disabled\"], [1, \"multi-line\", 3, \"innerHtml\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"click\", 4, \"ngIf\"], [3, \"innerHTML\", 4, \"ngIf\"], [3, \"ngClass\"], [\"class\", \"cross-icon rm-icon rm-icon-close\", 4, \"ngIf\"], [\"class\", \"tick-icon rm-icon rm-icon-check\", 4, \"ngIf\"], [1, \"cross-icon\", \"rm-icon\", \"rm-icon-close\"], [1, \"tick-icon\", \"rm-icon\", \"rm-icon-check\"], [3, \"click\"], [\"class\", \"bdo-table-cell-link\", \"target\", \"_blank\", 3, \"innerHTML\", \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"bdo-table-cell-link\", 3, \"click\", \"innerHTML\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"bdo-table-cell-link\", \"target\", \"_blank\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"bdo-table-cell-link\", 3, \"click\"], [3, \"innerHTML\"], [\"class\", \"bdo-table-cell-action-link mr-2\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"bdo-table-cell-action-link\", \"mr-2\", 3, \"click\", \"title\"], [\"mat-raised-button\", \"\", 1, \"bdo-table-single-action-button\", 3, \"click\", \"matTooltip\"], [1, \"bdo-table-single-action-button-icon\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [1, \"rm-icon\", \"rm-icon-more-horiz\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"bdo-table-expanded-element-row\", 3, \"click\"], [1, \"bdo-table-expanded-detail-row\"], [1, \"bdo-table-virtual-scroll-paginator-footer\", \"p-1\", \"pb-0\", \"ml-0\", \"mr-0\", \"grid\"]],\n      template: function BdoTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"mat-table\", 3, 0);\n          i0.ɵɵlistener(\"scroll\", function BdoTableComponent_Template_mat_table_scroll_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTableScroll($event));\n          });\n          i0.ɵɵelementContainerStart(3, 4);\n          i0.ɵɵtemplate(4, BdoTableComponent_mat_header_cell_4_Template, 2, 4, \"mat-header-cell\", 5)(5, BdoTableComponent_mat_cell_5_Template, 3, 6, \"mat-cell\", 6)(6, BdoTableComponent_mat_footer_cell_6_Template, 1, 4, \"mat-footer-cell\", 7);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(7, 8);\n          i0.ɵɵtemplate(8, BdoTableComponent_mat_header_cell_8_Template, 2, 5, \"mat-header-cell\", 9)(9, BdoTableComponent_mat_cell_9_Template, 2, 5, \"mat-cell\", 6)(10, BdoTableComponent_mat_footer_cell_10_Template, 1, 4, \"mat-footer-cell\", 7);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(11, 10);\n          i0.ɵɵtemplate(12, BdoTableComponent_mat_cell_12_Template, 3, 3, \"mat-cell\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(13, BdoTableComponent_ng_container_13_Template, 4, 3, \"ng-container\", 12)(14, BdoTableComponent_mat_header_row_14_Template, 1, 0, \"mat-header-row\", 13)(15, BdoTableComponent_mat_row_15_Template, 1, 8, \"mat-row\", 14)(16, BdoTableComponent_mat_row_16_Template, 1, 0, \"mat-row\", 15)(17, BdoTableComponent_mat_footer_row_17_Template, 1, 2, \"mat-footer-row\", 16)(18, BdoTableComponent_18_Template, 1, 3, null, 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, BdoTableComponent_div_19_Template, 1, 0, \"div\", 18);\n          i0.ɵɵelement(20, \"mat-paginator\", 19);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.id);\n          i0.ɵɵproperty(\"dataSource\", ctx.rows)(\"matSortActive\", ctx.defaultSortColumnId)(\"matSortDirection\", ctx.defaultSortOrder === \"desc\" ? \"desc\" : \"asc\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"sticky\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.columns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns)(\"matHeaderRowDefSticky\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", i0.ɵɵpureFunction0(24, _c1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matFooterRowDef\", ctx.displayedColumns)(\"matFooterRowDefSticky\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.footerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isVirtualScroll);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"display\", ctx.footerHeight === 0 || ctx.hidePagination || ctx.isVirtualScroll ? \"none\" : \"block\");\n          i0.ɵɵproperty(\"length\", ctx.totalRecords)(\"pageSize\", ctx.pageSize)(\"pageSizeOptions\", ctx.pageSizeOptions)(\"disabled\", ctx.isVirtualScroll || ctx.hidePagination)(\"hidePageSize\", ctx.isVirtualScroll || ctx.hidePagination)(\"showFirstLastButtons\", !ctx.hidePagination)(\"pageIndex\", ctx.pageIndex);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i3.NgControlStatus, i4.MatMenu, i4.MatMenuItem, i4.MatMenuTrigger, i5.MatIcon, i6.MatButton, i6.MatIconButton, i7.MatPaginator, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatFooterCellDef, i8.MatFooterRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatFooterCell, i8.MatHeaderRow, i8.MatRow, i8.MatFooterRow, i9.MatSort, i9.MatSortHeader, i10.MatCheckbox, i11.MatTooltip, i3.NgModel, i12.NativeElementInjectorDirective, i2.DecimalPipe, i2.DatePipe, i1.LocalizationPipe],\n      styles: [\".bdo-table-cell {\\n  overflow: hidden;\\n  word-wrap: break-word;\\n}\\n\\n.bdo-table-cell-action-link {\\n  cursor: pointer;\\n}\\n\\n.bdo-table-cell-link {\\n  cursor: pointer;\\n}\\n\\n.bdo-table-checkbox-toggles {\\n  padding-top: 2px;\\n}\\n\\n.mdc-data-table__table .required-label::after,\\n.mat-table .required-label::after {\\n  font-size: 1em !important;\\n}\\n\\ntable {\\n  width: 100%;\\n}\\n\\n.table-responsive {\\n  display: block;\\n  width: 100%;\\n  overflow-x: auto;\\n}\\n\\n.bdo-table-expanded-detail-row {\\n  min-height: 0;\\n}\\n\\n.bdo-table-row-expanded {\\n  width: 100%;\\n  height: auto;\\n}\\n\\n.bdo-table-row-collapsed {\\n  height: 0;\\n  min-height: 0;\\n}\\n\\n.mat-mdc-cell.mat-column-expandedDetail {\\n  background-color: #f2f2f2;\\n}\\n\\nmat-cell div,\\nmat-cell p {\\n  word-break: break-word;\\n  white-space: break-spaces;\\n}\\n\\n.bdo-table-virtual-scroll-paginator-footer {\\n  font-size: 13px;\\n}\\n\\n.mat-mdc-cell .mat-icon-button {\\n  width: 30px !important;\\n  height: 30px !important;\\n  line-height: 30px !important;\\n}\\n\\n.mat-mdc-cell,\\n.row-hoverable .mat-mdc-row:not(.bdo-table-expanded-detail-row):hover .mat-mdc-cell {\\n  background-color: #7fbeb0;\\n}\\n\\n.bdo-table-row-selected {\\n  background-color: #f0ecec !important;\\n}\\n\\n.mat-mdc-header-cell div.multi-line {\\n  display: block;\\n  font-weight: bold !important;\\n}\\n\\n.bdo-table-row-selectable.mat-mdc-row .mat-mdc-cell {\\n  cursor: pointer;\\n}\\n\\nmat-row:nth-child(4n+0) {\\n  background-color: #eaf0f3;\\n}\\n\\n.bdo-table-single-action-button {\\n  background-color: #000000 !important;\\n  color: #fff !important;\\n}\\n\\n.bdo-table-single-action-button-icon {\\n  top: 18%;\\n  left: 27%;\\n  margin-top: -0.5em;\\n  margin-left: -0.5em !important;\\n  width: 1em !important;\\n  height: 1em !important;\\n}\\n\\n.mat-paginator-sticky .mat-mdc-paginator-navigation-last.hidden {\\n  display: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "tap", "BdoTableConstants", "BdoTableCellLinkClickEvent", "BdoTableCheckboxClickEvent", "BdoTableColumnType", "BdoTableLazyLoadEvent", "BdoTableRowActionClickEvent", "BdoTableRowClickEvent", "BdoTableRowDetailTemplateDirective", "MatPaginator", "MatSort", "BdoTableFooterRowTemplateDirective", "AppComponentBase", "StringHelper", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "ɵɵlistener", "BdoTableComponent_mat_cell_5_button_1_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r2", "element_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "expandedElement", "toggleExpandRow", "ɵɵresetView", "stopPropagation", "BdoTableComponent_mat_cell_5_button_2_Template_button_click_0_listener", "_r5", "ɵɵtemplate", "BdoTableComponent_mat_cell_5_button_1_Template", "BdoTableComponent_mat_cell_5_button_2_Template", "ɵɵadvance", "ɵɵproperty", "hideExpand", "ɵɵelement", "ɵɵtwoWayListener", "BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template_mat_checkbox_ngModelChange_0_listener", "_r6", "ɵɵtwoWayBindingSet", "isAllSelected", "BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template_mat_checkbox_change_0_listener", "selectAll", "ɵɵtwoWayProperty", "BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template", "hasCheckboxAll", "BdoTableComponent_mat_cell_9_mat_checkbox_1_Template_mat_checkbox_ngModelChange_0_listener", "_r7", "row_r8", "checked", "BdoTableComponent_mat_cell_9_mat_checkbox_1_Template_mat_checkbox_change_0_listener", "onCheckRow", "BdoTableComponent_mat_cell_9_mat_checkbox_1_Template", "<PERSON><PERSON><PERSON><PERSON>", "BdoTableComponent_mat_cell_12_2_ng_template_0_Template", "rowDetailTemplate", "template", "ɵɵpureFunction1", "_c2", "element_r9", "BdoTableComponent_mat_cell_12_2_Template", "col_r10", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "columnId", "isSortable", "sortColumnId", "ɵɵclassMap", "class", "columnName", "ɵɵsanitizeHtml", "getStatusClass", "row_r11", "ɵɵtextInterpolate", "getCell", "value", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_2_Template", "ɵɵpipeBind3", "tmp_7_0", "BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template_a_click_0_listener", "_r13", "linkClick", "BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template_div_click_0_listener", "_r12", "BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_9_div_2_Template", "isTextInsteadOfLink", "type", "Link", "BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template_a_click_0_listener", "_r15", "i_r16", "index", "child_r17", "BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template_div_click_0_listener", "_r14", "BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_Template", "child_r18", "BdoTableComponent_ng_container_13_mat_cell_2_div_11_div_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_div_click_0_listener", "_r19", "BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_mat_checkbox_ngModelChange_1_listener", "BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_mat_checkbox_change_1_listener", "BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template_a_click_0_listener", "action_r22", "_r21", "actionClick", "actionType", "id", "tooltip", "icon", "BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template_div_click_0_listener", "_r20", "BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template_div_click_0_listener", "_r23", "BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template_button_click_1_listener", "ɵɵpropertyInterpolate", "BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template_button_click_0_listener", "_r25", "item_r26", "displayName", "BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template", "hideActionButton", "BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template_div_click_0_listener", "_r24", "BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_Template", "menu_r27", "BdoTableComponent_ng_container_13_mat_cell_2_div_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_2_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_3_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_4_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_5_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_6_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_7_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_8_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_11_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_13_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template", "BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template", "tmp_5_0", "String", "Number", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "YesNo", "Date", "DateTime", "FileSize", "LinkArray", "Array", "Checkbox", "hide", "Html", "Actions", "SingleActionButton", "ThreeDotActions", "get<PERSON>ooterCell", "ɵɵelementContainerStart", "BdoTableComponent_ng_container_13_mat_header_cell_1_Template", "BdoTableComponent_ng_container_13_mat_cell_2_Template", "BdoTableComponent_ng_container_13_mat_footer_cell_3_Template", "frozenLeft", "frozenRight", "BdoTableComponent_mat_row_15_Template_mat_row_click_0_listener", "element_r29", "_r28", "rowClick", "ɵɵclassProp", "rowSelectable", "selectedRow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "BdoTableComponent_18_ng_template_0_Template", "footerTemplate", "ɵɵpureFunction0", "_c3", "tablePaginationRowOption", "BdoTableComponent", "constructor", "injector", "renderer", "changeDetectorRef", "permissionService", "defaultSortOrder", "pageSize", "pageIndex", "hasRowCheckbox", "isVirtualScroll", "hidePagination", "scrollHeight", "headerHeight", "footerHeight", "isRowExpandable", "rowDetailHeight", "pageSizeOptions", "hideLastPage", "onRowClick", "onActionClick", "onLinkClick", "onLazyLoad", "onCheckboxClick", "onPage", "onRowExpand", "deleteAction", "ActionName", "Delete", "rows", "allRows", "cache", "totalMessage", "selectedMessage", "displayedColumns", "sortField", "sortDirection", "expanded", "disableNextRowFetch", "ngOnInit", "defaultSortColumnId", "dataSubscription", "tableService", "data", "subscribe", "tableId", "paginator", "sort", "active", "direction", "setTimeout", "push", "columns", "for<PERSON>ach", "x", "detectChanges", "tableElement", "document", "getElementsByClassName", "tagName", "setStyle", "resetToFirstPage", "scrollTop", "totalRecords", "JSON", "parse", "stringify", "start", "splice", "lazyLoad", "tempRows", "table", "ngAfterViewInit", "pageSubscription", "page", "pipe", "lazyLoadEvent", "pageNumber", "capitalizeFirstLetter", "isAscending", "emit", "sortSubscription", "sortChange", "a", "b", "fieldA", "cells", "find", "toLowerCase", "fieldB", "localeCompare", "ngAfterViewChecked", "lastPageEl", "l", "classList", "add", "onTableScroll", "e", "tableViewHeight", "target", "offsetHeight", "tableScrollHeight", "scrollLocation", "buffer", "limit", "row", "event", "rowData", "ev", "i", "rawData", "arrayIndex", "action", "cell", "isDeleted", "assessmentStatus", "status", "buttonInfo", "source", "addEditSubmitPermission", "getGrantedPolicy", "viewPermission", "getRowHeight", "height", "undefined", "getCellClass", "column", "name", "getRowClass", "r", "checkBoxEvent", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "Injector", "Renderer2", "ChangeDetectorRef", "i1", "PermissionService", "selectors", "contentQueries", "BdoTableComponent_ContentQueries", "rf", "ctx", "dirIndex", "BdoTableComponent_Template_mat_table_scroll_1_listener", "_r1", "BdoTableComponent_mat_header_cell_4_Template", "BdoTableComponent_mat_cell_5_Template", "BdoTableComponent_mat_footer_cell_6_Template", "BdoTableComponent_mat_header_cell_8_Template", "BdoTableComponent_mat_cell_9_Template", "BdoTableComponent_mat_footer_cell_10_Template", "BdoTableComponent_mat_cell_12_Template", "BdoTableComponent_ng_container_13_Template", "BdoTableComponent_mat_header_row_14_Template", "BdoTableComponent_mat_row_15_Template", "BdoTableComponent_mat_row_16_Template", "BdoTableComponent_mat_footer_row_17_Template", "BdoTableComponent_18_Template", "BdoTableComponent_div_19_Template", "_c1"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\components\\bdo-table\\bdo-table.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\components\\bdo-table\\bdo-table.component.html"], "sourcesContent": ["import {\r\n    AfterViewInit,\r\n    ChangeDetectorRef,\r\n    Component,\r\n    ContentChild,\r\n    EventEmitter,\r\n    Injector,\r\n    Input,\r\n    Output,\r\n    Renderer2,\r\n    ViewChild,\r\n    ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { Subscription, tap } from 'rxjs';\r\nimport { BdoTableConstants } from './bdo-table.constants';\r\nimport {\r\n    BdoTableCellData,\r\n    BdoTableCellLinkClickEvent,\r\n    BdoTableCheckboxClickEvent,\r\n    BdoTableColumnDefinition,\r\n    BdoTableColumnType,\r\n    BdoTableLazyLoadEvent,\r\n    BdoTableRowActionClickEvent,\r\n    BdoTableRowClickEvent,\r\n    BdoTableRowData,\r\n    BdoTableRowExpandedEvent,\r\n} from './bdo-table.model';\r\nimport { BdoTableRowDetailTemplateDirective } from './bdo-table-row-detail-template.directive';\r\nimport { MatTable } from '@angular/material/table';\r\nimport { MatPaginator } from '@angular/material/paginator';\r\nimport { MatSort, SortDirection } from '@angular/material/sort';\r\nimport { BdoTableFooterRowTemplateDirective } from './bdo-table-footer-row-template.directive';\r\nimport { AppComponentBase } from '../../../app-component-base';\r\nimport { StringHelper } from '@app/shared/utils';\r\nimport { PermissionService } from '@abp/ng.core';\r\n\r\nconst tablePaginationRowOption: number[] = [50, 100, 200];\r\n\r\n@Component({\r\n    selector: 'bdo-table',\r\n    templateUrl: './bdo-table.component.html',\r\n    styleUrls: ['./bdo-table.component.scss'],\r\n    encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class BdoTableComponent extends AppComponentBase implements AfterViewInit {\r\n    @Input() id: string; //used to uniquely identify the grid, if multiple grids on same component\r\n    @Input() columns: BdoTableColumnDefinition[];\r\n    @Input() isSortable: boolean;\r\n    @Input() defaultSortColumnId: string;\r\n    @Input() defaultSortOrder: SortDirection = 'asc'; //asc, desc\r\n    @Input() pageSize = 25; //if 0, pagination is disabled\r\n    @Input() pageIndex = 0; //page number to display initially\r\n    @Input() width: string; //table width in px. If set and scrollable=true, horizontal scroll is enabled.\r\n    @Input() lazyLoad: boolean; // If true, loads data for each page at a time instead of entire dataset.\r\n    @Input() rowSelectable: boolean; // Set whether row is selectable or not.\r\n    @Input() allowOnlySingleSelection: boolean; // set if only one row should be selectable\r\n    @Input() hasCheckboxAll: boolean; // Adds the select all or none toggle at the top of the table, inline with the title\r\n    @Input() selectedRow: BdoTableRowData | null = null;\r\n    @Input() hasRowCheckbox = false; // indicates whether the rows have checkboxes\r\n    @Input() isVirtualScroll = false;\r\n    @Input() hidePagination = false;\r\n    @Input() scrollHeight = '70vh'; // define height of the scrollable body of table\r\n    @Input() headerHeight = 40; // height of column header row\r\n    @Input() footerHeight = 40; // height of footer row\r\n\r\n    @Input() isRowExpandable = false; // indicates if each row can be expanded to see more details\r\n    @Input() rowDetailHeight = 100; //height of row detail expanded section\r\n\r\n    @Input() pageSizeOptions: number[] = tablePaginationRowOption;\r\n\r\n    @Input() hideLastPage: boolean = false;\r\n\r\n    //event triggered when selecting a row on grid\r\n    @Output() onRowClick: EventEmitter<BdoTableRowClickEvent> = new EventEmitter();\r\n\r\n    //event triggered when clicking on action icon on row\r\n    @Output() onActionClick: EventEmitter<BdoTableRowActionClickEvent> = new EventEmitter();\r\n\r\n    //event triggered when clicking on link (anchor tag) on row cell\r\n    @Output() onLinkClick: EventEmitter<BdoTableCellLinkClickEvent> = new EventEmitter();\r\n\r\n    //event triggered instructing consumer component to initiate request to server to load data\r\n    //Used for server-side paging to work\r\n    @Output() onLazyLoad: EventEmitter<BdoTableLazyLoadEvent> = new EventEmitter();\r\n\r\n    //event triggered when user clicks checkbox on grid row\r\n    @Output() onCheckboxClick: EventEmitter<BdoTableCheckboxClickEvent> = new EventEmitter();\r\n\r\n    //event triggered when paging the grid (user navigates to different page using pagination controls)\r\n    @Output() onPage: EventEmitter<object> = new EventEmitter();\r\n\r\n    // event triggered when a row is expanded\r\n    @Output() onRowExpand: EventEmitter<BdoTableRowExpandedEvent> = new EventEmitter();\r\n\r\n    /**\r\n     * Row Detail templates gathered from the ContentChild\r\n     */\r\n    @ContentChild(BdoTableRowDetailTemplateDirective)\r\n    rowDetailTemplate: BdoTableRowDetailTemplateDirective;\r\n\r\n    /**\r\n     * Footer row templates gathered from the ContentChild\r\n     */\r\n    @ContentChild(BdoTableFooterRowTemplateDirective)\r\n    footerTemplate: BdoTableFooterRowTemplateDirective;\r\n\r\n    pageSubscription: Subscription;\r\n    sortSubscription: Subscription;\r\n    dataSubscription: Subscription;\r\n    totalRecords: number;\r\n    deleteAction = BdoTableConstants.ActionName.Delete;\r\n\r\n    //store copy of unfiltered data -> used for filter results for search filter\r\n    rows: BdoTableRowData[] = [];\r\n    allRows:BdoTableRowData[] = [];\r\n\r\n    @Input() headerCheckboxValue: boolean;\r\n    rowGroupMetadata: any;\r\n\r\n    cache: any = {};\r\n\r\n    //Reference to the table HTML element\r\n    @ViewChild('bdotable') table: MatTable<any>;\r\n\r\n    //TODO - localize\r\n    @Input() totalMessage = 'total';\r\n    @Input() selectedMessage: string | boolean | null= null;\r\n\r\n    BdoTableColumnType = BdoTableColumnType;\r\n    displayedColumns: string[] = [];\r\n\r\n    sortField = '';\r\n    sortDirection: SortDirection = '';\r\n\r\n    expanded: any = {};\r\n\r\n    @ViewChild(MatPaginator) paginator: MatPaginator;\r\n    @ViewChild(MatSort) sort: MatSort;\r\n\r\n    //avoids scroll event from firing multiple API calls to fetch same paged data\r\n    disableNextRowFetch = false;\r\n\r\n    expandedElement: BdoTableRowData | null;\r\n\r\n    footerCells: BdoTableCellData[] = [];\r\n\r\n    isAllSelected = false;\r\n\r\n    constructor(injector: Injector, private renderer: Renderer2, private changeDetectorRef: ChangeDetectorRef, private permissionService: PermissionService) {\r\n        super(injector);\r\n        this.totalMessage = \"Total\";\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        this.sortField = this.defaultSortColumnId;\r\n        this.sortDirection = this.defaultSortOrder;\r\n\r\n        this.dataSubscription = this.tableService.data.subscribe(data => {\r\n            //update the correct table component if multiple instances of table on same page\r\n            if (data && data.tableId === this.id) {\r\n                this.footerCells = data.footerCells;\r\n\r\n                // set paginator page size\r\n                this.paginator.pageSize = this.pageSize;\r\n                this.sort.active = this.sortField;\r\n                this.sort.direction = this.sortDirection;\r\n\r\n                //need a delay to update columns on table if dynamically changed\r\n                setTimeout(() => {\r\n                    this.displayedColumns = [];\r\n\r\n                    if (this.hasRowCheckbox) {\r\n                        this.displayedColumns.push('select');\r\n                    }\r\n\r\n                    if (this.isRowExpandable) {\r\n                        this.displayedColumns.push('expand');\r\n                    }\r\n\r\n                    this.columns.forEach(x => {\r\n                        this.displayedColumns.push(x.columnId);\r\n                    });\r\n\r\n                    this.changeDetectorRef.detectChanges();\r\n                }, 200);\r\n\r\n                //set height of table\r\n                if (this.id) {\r\n                    const tableElement = document.getElementsByClassName(this.id);\r\n                    if (tableElement && tableElement.length > 0 && tableElement[0].tagName === 'MAT-TABLE') {\r\n                        this.renderer.setStyle(tableElement[0], 'max-height', this.scrollHeight);\r\n                        this.renderer.setStyle(tableElement[0], 'overflow-y', 'auto');\r\n\r\n                        //scroll to top whenever resetting to first page\r\n                        if (data.resetToFirstPage) {\r\n                            tableElement[0].scrollTop = 0;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                this.changeDetectorRef.detectChanges();\r\n\r\n                //reset table to first page\r\n                if (data.resetToFirstPage) {\r\n                    this.pageIndex = 0;\r\n                    this.rows = [];\r\n                    this.totalRecords = 0;\r\n                    this.cache = {};\r\n                }\r\n\r\n                if (this.isVirtualScroll) {\r\n                    // Update total count\r\n                    this.totalRecords = data.totalRecords;\r\n\r\n                    // Create array to store data if missing\r\n                    // The array should have the correct number of with \"holes\" for missing data\r\n                    if (!this.rows) {\r\n                        this.rows = JSON.parse(JSON.stringify(data.data));\r\n                    }\r\n\r\n                    // Calc starting row offset\r\n                    // This is the position to insert the new data\r\n                    const start = this.pageIndex * this.pageSize;\r\n\r\n                    // Copy existing data\r\n                    const rows = [...this.rows];\r\n\r\n                    // Insert new rows into correct position\r\n                    if (data.data && data.data.length > 0) {\r\n                        rows.splice(start, this.pageSize, ...JSON.parse(JSON.stringify(data.data)));\r\n                    }\r\n\r\n                    // Set rows to our new rows for display\r\n                    this.rows = rows;\r\n\r\n                    this.disableNextRowFetch = false;\r\n                } else {\r\n                    this.allRows = JSON.parse(JSON.stringify(data.data));\r\n                    if(this.lazyLoad) {\r\n                        this.rows = this.allRows;\r\n                    } else {\r\n                        const start = this.pageIndex * this.pageSize;\r\n\r\n                        var tempRows = [...this.allRows];\r\n\r\n                        this.rows = tempRows.splice(start, this.pageSize);\r\n                    }\r\n                    this.totalRecords = data.totalRecords;\r\n                }\r\n                if(this.rowSelectable){\r\n                    if(this.table && this.rows[0]){\r\n                        //this.selectedRow = this.rows[0]; //auto selects first row in the table when rendered if rows are selectable \r\n                        //this.rowClick(this.selectedRow);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    ngAfterViewInit(): void {\r\n        if (this.paginator) {\r\n            this.paginator.pageSize = this.pageSize;\r\n        }\r\n\r\n        if (this.sort) {\r\n            this.sort.active = this.sortField;\r\n            this.sort.direction = this.sortDirection;\r\n        }\r\n\r\n        // when paginator event is invoked, retrieve the related data\r\n        this.pageSubscription = this.paginator.page\r\n            .pipe(\r\n                tap(() => {\r\n                    this.pageIndex = this.paginator.pageIndex;\r\n                    this.pageSize = this.paginator.pageSize;\r\n\r\n                    if (this.lazyLoad) {\r\n                        const lazyLoadEvent = new BdoTableLazyLoadEvent();\r\n                        lazyLoadEvent.pageNumber = this.pageIndex;\r\n                        lazyLoadEvent.pageSize = this.pageSize;\r\n                        lazyLoadEvent.sortField = StringHelper.capitalizeFirstLetter(this.sortField);\r\n                        lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\r\n\r\n                        //triggers event instructing consumer component to initiate request to server to load data\r\n                        //passes sorting and pagination info for consumer to include in requets object to load data from server\r\n                        this.onLazyLoad.emit(lazyLoadEvent);\r\n                    } else {\r\n                        \r\n                        var tempRows = [...this.allRows];\r\n                        var start = this.pageIndex * this.pageSize;\r\n                        this.rows = tempRows.splice(start,this.pageSize);\r\n                    }\r\n                })\r\n            )\r\n            .subscribe();\r\n\r\n        this.sortSubscription = this.sort.sortChange\r\n            .pipe(\r\n                tap(() => {\r\n                    this.sortField = this.sort.active;\r\n                    this.sortDirection = this.sort.direction;\r\n\r\n                    if (this.lazyLoad) {\r\n                        this.pageIndex = 0;\r\n\r\n                        const lazyLoadEvent = new BdoTableLazyLoadEvent();\r\n                        lazyLoadEvent.pageNumber = this.pageIndex;\r\n                        lazyLoadEvent.pageSize = this.pageSize;\r\n                        lazyLoadEvent.sortField = StringHelper.capitalizeFirstLetter(this.sortField);\r\n                        lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\r\n\r\n                        //triggers event instructing consumer component to initiate request to server to load data\r\n                        //passes sorting and pagination info for consumer to include in requets object to load data from server\r\n                        this.onLazyLoad.emit(lazyLoadEvent);\r\n                    } else {\r\n                        const rows = [...this.rows];\r\n                        rows.sort((a, b) => {\r\n                            //TODO - handling sorting for dates, html, boolean, etc..\r\n                            const fieldA = a.cells.find(x => x.columnId.toLowerCase() === this.sortField.toLowerCase())?.value;\r\n                            const fieldB = b.cells.find(x => x.columnId.toLowerCase() === this.sortField.toLowerCase())?.value;\r\n\r\n                            return fieldA.localeCompare(fieldB) * (this.sortDirection === 'desc' ? -1 : 1);\r\n                        });\r\n                        this.rows = rows;\r\n                    }\r\n                })\r\n            )\r\n            .subscribe();\r\n    }\r\n\r\n    ngAfterViewChecked(): void {\r\n        //set height of table\r\n        if (this.id) {\r\n            const tableElement = document.getElementsByClassName(this.id);\r\n            if (tableElement && tableElement.length > 0 && tableElement[0].tagName === 'MAT-TABLE') {\r\n                this.renderer.setStyle(tableElement[0], 'max-height', this.scrollHeight);\r\n                this.renderer.setStyle(tableElement[0], 'overflow-y', 'auto');\r\n            }\r\n        }\r\n        if(this.hideLastPage) {\r\n            var lastPageEl = document.getElementsByClassName('mat-mdc-paginator-navigation-last');\r\n            if(lastPageEl) {\r\n                var l = lastPageEl[0];\r\n                l.classList.add('hidden')\r\n            }\r\n        }\r\n    }\r\n\r\n    onTableScroll(e: any): void {\r\n        //virtual scrolling is disabled\r\n        if (!this.isVirtualScroll) return;\r\n\r\n        //we have retrieved all records\r\n        if (this.rows.length === this.totalRecords || this.disableNextRowFetch) return;\r\n\r\n        const tableViewHeight = e.target.offsetHeight; // viewport: ~500px\r\n        const tableScrollHeight = e.target.scrollHeight; // length of all table\r\n        const scrollLocation = e.target.scrollTop; // how far user scrolled\r\n\r\n        // If the user has scrolled within 10px of the bottom, add more data\r\n        const buffer = 10;\r\n        const limit = tableScrollHeight - tableViewHeight - buffer;\r\n        if (scrollLocation > limit) {\r\n            this.disableNextRowFetch = true;\r\n\r\n            // We keep a index of server loaded pages so we don't load same data twice\r\n            // This is based on the server page not the UI\r\n            if (this.cache[this.pageIndex + 1]) return;\r\n            this.cache[this.pageIndex + 1] = true;\r\n\r\n            this.pageIndex = this.pageIndex + 1;\r\n\r\n            const lazyLoadEvent = new BdoTableLazyLoadEvent();\r\n            lazyLoadEvent.pageNumber = this.pageIndex;\r\n            lazyLoadEvent.pageSize = this.pageSize;\r\n            lazyLoadEvent.sortField = this.sortField;\r\n            lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\r\n\r\n            //triggers event instructing consumer component to initiate request to server to load data\r\n            //passes sorting and pagination info for consumer to include in requets object to load data from server\r\n            this.onLazyLoad.emit(lazyLoadEvent);\r\n        }\r\n    }\r\n\r\n    rowClick(row: BdoTableRowData): void {\r\n        //row is not selectable\r\n        if (!this.rowSelectable) return;\r\n\r\n        this.selectedRow = row;\r\n\r\n        const event = new BdoTableRowClickEvent();\r\n        event.id = row.id;\r\n        event.rowData = row;\r\n\r\n        this.onRowClick.emit(event);\r\n    }\r\n\r\n    linkClick(row: BdoTableRowData, columnId, ev,i?): void {\r\n        const event = new BdoTableCellLinkClickEvent();\r\n        event.id = row.id;\r\n        event.rawData = row.rawData;\r\n        event.columnId = columnId;\r\n        event.event = ev;\r\n        event.arrayIndex = i;\r\n\r\n        this.onLinkClick.emit(event);\r\n    }\r\n\r\n    actionClick(action, id, row: BdoTableRowData, ev): void {\r\n        this.selectedRow = this.rows.find(x => {\r\n            return x.id === id;\r\n        });\r\n\r\n        if (this.selectedRow) {\r\n            this.rowClick(row);\r\n        }\r\n\r\n        const event = new BdoTableRowActionClickEvent();\r\n        event.action = action;\r\n        event.id = id;\r\n        event.data = row;\r\n        event.event = ev;\r\n\r\n        this.onActionClick.emit(event);\r\n    }\r\n\r\n    getCell(row: BdoTableRowData, columnId): BdoTableCellData {\r\n            if (!row || !row.cells) return null;\r\n\r\n            const cell = row.cells.find(cell => {\r\n                return cell.columnId === columnId;\r\n            });\r\n            return cell;   \r\n    }\r\n\r\n    getStatusClass(row:BdoTableRowData, columnName:string):string{\r\n        if(row.rawData?.isDeleted){\r\n            return 'legend-pink legend-linethrough'\r\n        }\r\n        if(columnName === \"declarationStatus\" || columnName === 'assessmentStatus'){\r\n            if(row.rawData.assessmentStatus?.toLowerCase() == 'not started') return 'legend-black';\r\n            \r\n            const status:string = row.rawData?.status ? row.rawData?.status.toLowerCase() : row.rawData.assessmentStatus?.toLowerCase();\r\n            switch (status) {\r\n                case 'not started':\r\n                  return 'legend-pink';\r\n\r\n                case 'draft':\r\n                case 'information required':\r\n                    return 'legend-navy';\r\n\r\n                case 'reopened':\r\n                case 'provisional fail':\r\n                    return 'legend-orange';\r\n\r\n                case 'submitted':\r\n                case 'resubmitted':\r\n                case 'unsubmitted':\r\n                  return 'legend-black';\r\n\r\n                case 'deleted':\r\n                  return 'legend-pink legend-linethrough';\r\n\r\n                case 'pass':\r\n                    return 'legend-green';\r\n\r\n                case 'fail':\r\n                    return 'legend-red';\r\n\r\n                case 'provisional pass':\r\n                    return 'legend-yellow';\r\n\r\n                case 'closed':\r\n                    return 'legend-grey'\r\n\r\n                default:\r\n                  return 'legend-black';\r\n              }\r\n        }\r\n        return '';\r\n    }\r\n\r\n    hideActionButton(buttonInfo, row): boolean{\r\n        if(buttonInfo.source === \"declarationHistory\"){\r\n            const addEditSubmitPermission = this.permissionService.getGrantedPolicy('EsService.Declaration.Submit');\r\n            const viewPermission =  this.permissionService.getGrantedPolicy('SearchService.BasicSearch.ViewDeclaration');\r\n            \r\n            if(buttonInfo.actionType === 'delete'){\r\n                if(!(row.rawData.status === \"Draft\" || row.rawData.status === 'Reopened') || !addEditSubmitPermission ){\r\n                        return false;\r\n                    }\r\n            }\r\n            if(buttonInfo.actionType === 'edit'){\r\n                if(row.rawData.status === \"Submitted\" || row.rawData.status === 'Resubmitted' || !addEditSubmitPermission){\r\n                    return false;\r\n                }\r\n            }\r\n            if(buttonInfo.actionType === 'view'){\r\n                if(!viewPermission){\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    getRowHeight(row): number {\r\n        if (!row) {\r\n            return 40;\r\n        }\r\n        if (row.height === undefined) {\r\n            return 40;\r\n        }\r\n        return row.height;\r\n    }\r\n\r\n    getCellClass({ row, column, value }): string {\r\n        return row.cells.find(x => x.columnId === column.name)?.class;\r\n    }\r\n\r\n    getFooterCell(columnId): BdoTableCellData {\r\n        if (!this.footerCells) return null;\r\n\r\n        const cell = this.footerCells.find(cell => {\r\n            return cell.columnId === columnId;\r\n        });\r\n        return cell;\r\n    }\r\n\r\n    getRowClass(row): string {\r\n        return row.class;\r\n    }\r\n\r\n    toggleExpandRow(row): void {\r\n        setTimeout(() => {\r\n            this.onRowExpand.emit({\r\n                id: row.id,\r\n                rawData: row.rawData,\r\n            });\r\n        }, 200);\r\n    }\r\n\r\n    selectAll(event): void {\r\n        this.rows.forEach(r => {\r\n            r.checked = event.checked;\r\n        });\r\n\r\n        const checkBoxEvent = new BdoTableCheckboxClickEvent(event.checked ? this.rows : [], null, event.checked, true);\r\n        this.onCheckboxClick.emit(checkBoxEvent);\r\n    }\r\n\r\n    onCheckRow(row: BdoTableRowData, event): void {\r\n        const checkBoxEvent = new BdoTableCheckboxClickEvent([row], row.id, event.checked, false);\r\n        this.onCheckboxClick.emit(checkBoxEvent);\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        if (this.dataSubscription) this.dataSubscription.unsubscribe();\r\n        if (this.pageSubscription) this.pageSubscription.unsubscribe();\r\n        if (this.sortSubscription) this.sortSubscription.unsubscribe();\r\n    }\r\n}\r\n", "<div class=\"bdo-table-container table-responsive\">\r\n    <mat-table\r\n        #bdotable\r\n        class=\"row-hoverable\"\r\n        [class]=\"id\"\r\n        [dataSource]=\"rows\"\r\n        (scroll)=\"onTableScroll($event)\"\r\n        matSort\r\n        [matSortActive]=\"defaultSortColumnId\"\r\n        [matSortDirection]=\"defaultSortOrder === 'desc' ? 'desc' : 'asc'\"\r\n        matSortDisableClear\r\n        multiTemplateDataRows\r\n    >\r\n        <ng-container matColumnDef=\"expand\" sticky>\r\n            <mat-header-cell *matHeaderCellDef aria-label=\"row actions\" [style.max-width]=\"'60px'\" [style.min-width]=\"'60px'\">&nbsp;</mat-header-cell>\r\n            <mat-cell *matCellDef=\"let element\" [style.max-width]=\"'60px'\" [style.min-width]=\"'60px'\">\r\n                <button\r\n                    *ngIf=\"!element.hideExpand && expandedElement !== element\"\r\n                    pButton\r\n                    pRipple\r\n                    type=\"button\"\r\n                    icon=\"rm-icon rm-icon-angle-right\"\r\n                    class=\"p-button-rounded p-button-text\"\r\n                    (click)=\"expandedElement = expandedElement === element ? null : element; toggleExpandRow(element); $event.stopPropagation()\"\r\n                ></button>\r\n                <button\r\n                    *ngIf=\"!element.hideExpand && expandedElement === element\"\r\n                    pButton\r\n                    pRipple\r\n                    type=\"button\"\r\n                    icon=\"rm-icon rm-icon-angle-down\"\r\n                    class=\"p-button-rounded p-button-text\"\r\n                    (click)=\"expandedElement = expandedElement === element ? null : element; $event.stopPropagation()\"\r\n                ></button>\r\n            </mat-cell>\r\n            <mat-footer-cell *matFooterCellDef [style.max-width]=\"'60px'\" [style.min-width]=\"'60px'\"></mat-footer-cell>\r\n        </ng-container>\r\n\r\n        <!-- Checkbox Column -->\r\n        <ng-container matColumnDef=\"select\" [sticky]=\"true\">\r\n            <mat-header-cell *matHeaderCellDef [style.max-width]=\"'60px'\" [style.min-width]=\"'60px'\">\r\n                <mat-checkbox *ngIf=\"hasCheckboxAll\" class=\"checkBox form-field\" [(ngModel)]=\"isAllSelected\" (change)=\"selectAll($event)\"></mat-checkbox>\r\n            </mat-header-cell>\r\n            <mat-cell *matCellDef=\"let row\" [style.max-width]=\"'60px'\" [style.min-width]=\"'60px'\">\r\n                <mat-checkbox *ngIf=\"!row.cannotCheck\" class=\"checkBox form-field\" [(ngModel)]=\"row.checked\" (change)=\"onCheckRow(row, $event)\"></mat-checkbox>\r\n            </mat-cell>\r\n            <mat-footer-cell *matFooterCellDef [style.max-width]=\"'60px'\" [style.min-width]=\"'60px'\"></mat-footer-cell>\r\n        </ng-container>\r\n\r\n        <!-- Expanded Content Column - The detail row is made up of this one column that spans across all columns -->\r\n        <ng-container matColumnDef=\"expandedDetail\">\r\n            <mat-cell *matCellDef=\"let element\" [attr.colspan]=\"displayedColumns.length\">\r\n                <div class=\"bdo-table-expanded-element-detail\" [ngClass]=\"element === expandedElement ? 'bdo-table-row-expanded' : 'bdo-table-row-collapsed'\">\r\n                    <ng-template\r\n                        *ngIf=\"rowDetailTemplate\"\r\n                        [ngTemplateOutlet]=\"rowDetailTemplate.template\"\r\n                        [ngTemplateOutletContext]=\"{\r\n                            row: element\r\n                        }\"\r\n                    >\r\n                    </ng-template>\r\n                </div>\r\n            </mat-cell>\r\n        </ng-container>\r\n\r\n        <ng-container\r\n            *ngFor=\"let col of columns\"\r\n            [matColumnDef]=\"col.columnId\"\r\n            [sticky]=\"col.frozenLeft\"\r\n            [stickyEnd]=\"col.frozenRight || col.columnId === 'actions'\"\r\n        >\r\n            <mat-header-cell\r\n                *matHeaderCellDef\r\n                [mat-sort-header]=\"col.columnId != 'actions' && col.isSortable ? col.sortColumnId : null\"\r\n                [disabled]=\"col.columnId === 'actions' || !col.isSortable ? true : false\"\r\n                [style.width]=\"col.width ? col.width + 'px' : 'auto'\"\r\n                [style.min-width]=\"col.minWidth ? col.minWidth + 'px' : '100px'\"\r\n                [style.max-width]=\"col.maxWidth ? col.maxWidth + 'px' : 'auto'\"\r\n                ><div class=\"multi-line\" [class]=\"col.class\" [innerHtml]=\"col.columnName\"></div\r\n            ></mat-header-cell>\r\n            <mat-cell\r\n                *matCellDef=\"let row\"\r\n                [class]=\"getCell(row, col.columnId)?.class\"\r\n                [style.width]=\"col.width ? col.width + 'px' : 'auto'\"\r\n                [style.min-width]=\"col.minWidth ? col.minWidth + 'px' : '100px'\"\r\n                [style.max-width]=\"col.maxWidth ? col.maxWidth + 'px' : 'auto'\"\r\n            >\r\n                <!--String display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.String\" [ngClass]=\"getStatusClass(row, col.columnId)\" >{{ getCell(row, col.columnId).value }}</div>\r\n\r\n                <!--Number display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Number\">{{ getCell(row, col.columnId).value | number }}</div>\r\n\r\n                <!--Currency display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Currency\">\r\n                    {{ getCell(row, col.columnId).value }}\r\n                </div>\r\n\r\n                <!--Boolean display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Boolean\">\r\n                    {{ getCell(row, col.columnId).value ? ('::General:Yes' | abpLocalization) : ('::General:No' | abpLocalization) }}\r\n                </div>\r\n\r\n                <!--Yes/No icon display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.YesNo\">\r\n                    <span *ngIf=\"!getCell(row, col.columnId).value\" class=\"cross-icon rm-icon rm-icon-close\"></span>\r\n                    <span *ngIf=\"getCell(row, col.columnId).value\" class=\"tick-icon rm-icon rm-icon-check\"></span>\r\n                </div>\r\n\r\n                <!--Date display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Date\" [ngClass]=\"getStatusClass(row, col.columnId)\">{{ getCell(row, col.columnId)?.value | date:'dd/MM/yyyy':'local' }}</div>\r\n\r\n                <!--Date/Time display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.DateTime\" [ngClass]=\"getStatusClass(row, col.columnId)\">{{ getCell(row, col.columnId)?.value | date:'dd/MM/yyyy HH:mm':'local' }}</div>\r\n\r\n                <!--File Size display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.FileSize\">\r\n                    {{ getCell(row, col.columnId).value }}\r\n                </div>\r\n\r\n                <!--Link display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Link\" (click)=\"$event.stopPropagation()\">\r\n                    <a\r\n                        class=\"bdo-table-cell-link\"\r\n                        (click)=\"linkClick(row, col.columnId, $event)\"\r\n                        target=\"_blank\"\r\n                        [innerHTML]=\"getCell(row, col.columnId).value\"\r\n                        *ngIf=\"!getCell(row, col.columnId).isTextInsteadOfLink\"\r\n                    ></a>\r\n                    <div *ngIf=\"col.type === BdoTableColumnType.Link && getCell(row, col.columnId).isTextInsteadOfLink\">{{ getCell(row, col.columnId).value }}</div>\r\n                </div>\r\n\r\n                <!-- Link Array -->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.LinkArray\"  (click)=\"$event.stopPropagation()\">\r\n                    <div *ngFor=\"let child of getCell(row, col.columnId).value; index as i\">\r\n                        <a\r\n                            class=\"bdo-table-cell-link\"\r\n                            (click)=\"linkClick(row,col.columnId, $event,i)\"\r\n                            target=\"_blank\"\r\n                            *ngIf=\"!getCell(row, col.columnId).isTextInsteadOfLink\"\r\n                        >{{ child }}</a>   \r\n                    </div>\r\n                </div>\r\n\r\n                <!--List display-->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Array\">\r\n                    <div *ngFor=\"let child of getCell(row, col.columnId).value\">{{ child }}</div>\r\n                </div>\r\n\r\n                <!-- Checkbox display -->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Checkbox && !getCell(row, col.columnId).hide\" (click)=\"$event.stopPropagation()\">\r\n                    <mat-checkbox class=\"checkBox form-field\" [(ngModel)]=\"getCell(row, col.columnId).value\" (change)=\"onCheckRow(row, $event)\"></mat-checkbox>\r\n                </div>\r\n\r\n                <!-- HTML display -->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Html\" [innerHTML]=\"getCell(row, col.columnId).value\"></div>\r\n\r\n                <!-- Actions List -->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.Actions && getCell(row, col.columnId).value\" (click)=\"$event.stopPropagation()\">\r\n                    <a\r\n                        *ngFor=\"let action of getCell(row, col.columnId).value\"\r\n                        class=\"bdo-table-cell-action-link mr-2\"\r\n                        [title]=\"action.tooltip\"\r\n                        (click)=\"actionClick(action.actionType, row.id, row, $event)\"\r\n                    >\r\n                        <span class=\"{{ action.icon }}\"></span>\r\n                    </a>\r\n                </div>\r\n\r\n                <!-- Single Action button -->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.SingleActionButton && getCell(row, col.columnId).value\" (click)=\"$event.stopPropagation()\">\r\n                    <button mat-raised-button class=\"bdo-table-single-action-button\" \r\n                    (click)=\"actionClick(getCell(row, col.columnId).value.actionType, row.id, row, null)\"\r\n                    matTooltip= \"{{getCell(row, col.columnId).value.tooltip}}\"\r\n                    >\r\n                        <mat-icon class=\"bdo-table-single-action-button-icon\">{{getCell(row, col.columnId).value.icon}}</mat-icon>\r\n                    </button>\r\n                </div>\r\n\r\n                <!-- 3 dot action button -->\r\n                <div *ngIf=\"col.type === BdoTableColumnType.ThreeDotActions && getCell(row, col.columnId).value\" (click)=\"$event.stopPropagation()\">                   \r\n                    <button mat-icon-button [matMenuTriggerFor]=\"menu\">\r\n                        <!-- <span class=\"rm-icon rm-icon-more-horiz\"></span> -->\r\n                        <mat-icon class=\"rm-icon rm-icon-more-horiz\">more_vert</mat-icon>\r\n                    </button>\r\n                    <mat-menu #menu=\"matMenu\">\r\n                        <div *ngFor=\"let item of getCell(row, col.columnId).value\">\r\n                            <button *ngIf = \"hideActionButton(item, row)\" mat-menu-item (click)=\"actionClick(item.actionType, row.id, row, null)\">\r\n                                {{item.displayName}}\r\n                                <mat-icon>{{item.icon}}</mat-icon>\r\n                            </button>\r\n                        </div>\r\n                    </mat-menu>\r\n                </div>\r\n            </mat-cell>\r\n\r\n            <mat-footer-cell\r\n                *matFooterCellDef\r\n                [style.width]=\"col.width ? col.width + 'px' : 'auto'\"\r\n                [style.min-width]=\"col.minWidth ? col.minWidth + 'px' : '100px'\"\r\n                [style.max-width]=\"col.maxWidth ? col.maxWidth + 'px' : 'auto'\"\r\n            >\r\n                <div [innerHTML]=\"getFooterCell(col.columnId)?.value\"></div>\r\n            </mat-footer-cell>\r\n        </ng-container>\r\n\r\n        <mat-header-row *matHeaderRowDef=\"displayedColumns; sticky: true\"></mat-header-row>\r\n\r\n        <mat-row\r\n            *matRowDef=\"let element; columns: displayedColumns\"\r\n            class=\"bdo-table-expanded-element-row\"\r\n            (click)=\"rowClick(element)\"\r\n            [class]=\"element.class\"\r\n            [class.bdo-table-row-selectable]=\"rowSelectable\"\r\n            [class.bdo-table-row-selected]=\"selectedRow === element\"\r\n            [class.example-expanded-row]=\"expandedElement === element\"\r\n            (click)=\"expandedElement = expandedElement === element ? null : element\"\r\n        ></mat-row>\r\n        <mat-row *matRowDef=\"let row; columns: ['expandedDetail']\" class=\"bdo-table-expanded-detail-row\"></mat-row>\r\n\r\n        <mat-footer-row\r\n            *matFooterRowDef=\"displayedColumns; sticky: true\"\r\n            [style.display]=\"footerCells && footerCells.length > 0 ? 'flex' : 'none'\"\r\n        ></mat-footer-row>\r\n        <ng-template *ngIf=\"footerTemplate\" [ngTemplateOutlet]=\"footerTemplate.template\" [ngTemplateOutletContext]=\"{}\"> </ng-template>\r\n    </mat-table>\r\n    <div *ngIf=\"isVirtualScroll\" class=\"bdo-table-virtual-scroll-paginator-footer p-1 pb-0 ml-0 mr-0 grid\">\r\n        <!-- TODO: Figure out what the commented code is for and fix the localization, right now it just displays ::ShowingOfTotalEntriesWithParameters -->\r\n        <!-- <div class=\"col pt-2 text-right\">\r\n            {{\r\n                '::ShowingOfTotalEntriesWithParameters'\r\n                    | abpLocalization : (totalRecords > 0 ? 1 : 0).toString() : rows.length.toString() : (totalRecords || 0).toString()\r\n            }}\r\n        </div> -->\r\n    </div>\r\n    <mat-paginator\r\n        [length]=\"totalRecords\"\r\n        [pageSize]=\"pageSize\"\r\n        class=\"mat-paginator-sticky\"\r\n        [pageSizeOptions]=\"pageSizeOptions\"\r\n        [disabled]=\"isVirtualScroll || hidePagination\"\r\n        [hidePageSize]=\"isVirtualScroll || hidePagination\"\r\n        [showFirstLastButtons]=\"!hidePagination\"\r\n        [pageIndex]=\"pageIndex\"\r\n        [style.display]=\"footerHeight === 0 || hidePagination || isVirtualScroll ? 'none' : 'block'\"\r\n        sticky\r\n    ></mat-paginator>\r\n</div>\r\n"], "mappings": "AAAA,SAKIA,YAAY,QAOT,eAAe;AACtB,SAAuBC,GAAG,QAAQ,MAAM;AACxC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAEIC,0BAA0B,EAC1BC,0BAA0B,EAE1BC,kBAAkB,EAClBC,qBAAqB,EACrBC,2BAA2B,EAC3BC,qBAAqB,QAGlB,mBAAmB;AAC1B,SAASC,kCAAkC,QAAQ,2CAA2C;AAE9F,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAuB,wBAAwB;AAC/D,SAASC,kCAAkC,QAAQ,2CAA2C;AAC9F,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,YAAY,QAAQ,mBAAmB;;;;;;;;;;;;;;;;;;;;;;ICnBpCC,EAAA,CAAAC,cAAA,0BAAkH;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;IAAnDH,EAA3B,CAAAI,WAAA,qBAA0B,qBAA2B;;;;;;IAE7GJ,EAAA,CAAAC,cAAA,iBAQC;IADGD,EAAA,CAAAK,UAAA,mBAAAC,uEAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAV,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAAE,MAAA,CAAAC,eAAA,GAAAD,MAAA,CAAAC,eAAA,KAAAJ,UAAA,GAAyD,IAAI,GAAAA,UAAA;MAAYG,MAAA,CAAAE,eAAA,CAAAL,UAAA,CAAwB;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAET,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IAC/HjB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACVH,EAAA,CAAAC,cAAA,iBAQC;IADGD,EAAA,CAAAK,UAAA,mBAAAa,uEAAAX,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAW,GAAA;MAAA,MAAAT,UAAA,GAAAV,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAAE,MAAA,CAAAC,eAAA,GAAAD,MAAA,CAAAC,eAAA,KAAAJ,UAAA,GAAyD,IAAI,GAAAA,UAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAYT,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IACrGjB,EAAA,CAAAG,YAAA,EAAS;;;;;IAlBdH,EAAA,CAAAC,cAAA,eAA0F;IAUtFD,EATA,CAAAoB,UAAA,IAAAC,8CAAA,qBAQC,IAAAC,8CAAA,qBASA;IACLtB,EAAA,CAAAG,YAAA,EAAW;;;;;IAnBoDH,EAA3B,CAAAI,WAAA,qBAA0B,qBAA2B;IAEhFJ,EAAA,CAAAuB,SAAA,EAAwD;IAAxDvB,EAAA,CAAAwB,UAAA,UAAAd,UAAA,CAAAe,UAAA,IAAAZ,MAAA,CAAAC,eAAA,KAAAJ,UAAA,CAAwD;IASxDV,EAAA,CAAAuB,SAAA,EAAwD;IAAxDvB,EAAA,CAAAwB,UAAA,UAAAd,UAAA,CAAAe,UAAA,IAAAZ,MAAA,CAAAC,eAAA,KAAAJ,UAAA,CAAwD;;;;;IASjEV,EAAA,CAAA0B,SAAA,sBAA2G;;;IAA7C1B,EAA3B,CAAAI,WAAA,qBAA0B,qBAA2B;;;;;;IAMpFJ,EAAA,CAAAC,cAAA,uBAA0H;IAAzDD,EAAA,CAAA2B,gBAAA,2BAAAC,kGAAArB,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAhB,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA8B,kBAAA,CAAAjB,MAAA,CAAAkB,aAAA,EAAAxB,MAAA,MAAAM,MAAA,CAAAkB,aAAA,GAAAxB,MAAA;MAAA,OAAAP,EAAA,CAAAgB,WAAA,CAAAT,MAAA;IAAA,EAA2B;IAACP,EAAA,CAAAK,UAAA,oBAAA2B,2FAAAzB,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAhB,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAAUH,MAAA,CAAAoB,SAAA,CAAA1B,MAAA,CAAiB;IAAA,EAAC;IAACP,EAAA,CAAAG,YAAA,EAAe;;;;IAAxEH,EAAA,CAAAkC,gBAAA,YAAArB,MAAA,CAAAkB,aAAA,CAA2B;;;;;IADhG/B,EAAA,CAAAC,cAAA,sBAAyF;IACrFD,EAAA,CAAAoB,UAAA,IAAAe,2DAAA,2BAA0H;IAC9HnC,EAAA,CAAAG,YAAA,EAAkB;;;;IAF4CH,EAA3B,CAAAI,WAAA,qBAA0B,qBAA2B;IACrEJ,EAAA,CAAAuB,SAAA,EAAoB;IAApBvB,EAAA,CAAAwB,UAAA,SAAAX,MAAA,CAAAuB,cAAA,CAAoB;;;;;;IAGnCpC,EAAA,CAAAC,cAAA,uBAAgI;IAA7DD,EAAA,CAAA2B,gBAAA,2BAAAU,2FAAA9B,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAAC,MAAA,GAAAvC,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAAZ,EAAA,CAAA8B,kBAAA,CAAAS,MAAA,CAAAC,OAAA,EAAAjC,MAAA,MAAAgC,MAAA,CAAAC,OAAA,GAAAjC,MAAA;MAAA,OAAAP,EAAA,CAAAgB,WAAA,CAAAT,MAAA;IAAA,EAAyB;IAACP,EAAA,CAAAK,UAAA,oBAAAoC,oFAAAlC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAAC,MAAA,GAAAvC,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAAUH,MAAA,CAAA6B,UAAA,CAAAH,MAAA,EAAAhC,MAAA,CAAuB;IAAA,EAAC;IAACP,EAAA,CAAAG,YAAA,EAAe;;;;IAA5EH,EAAA,CAAAkC,gBAAA,YAAAK,MAAA,CAAAC,OAAA,CAAyB;;;;;IADhGxC,EAAA,CAAAC,cAAA,eAAsF;IAClFD,EAAA,CAAAoB,UAAA,IAAAuB,oDAAA,2BAAgI;IACpI3C,EAAA,CAAAG,YAAA,EAAW;;;;IAFgDH,EAA3B,CAAAI,WAAA,qBAA0B,qBAA2B;IAClEJ,EAAA,CAAAuB,SAAA,EAAsB;IAAtBvB,EAAA,CAAAwB,UAAA,UAAAe,MAAA,CAAAK,WAAA,CAAsB;;;;;IAEzC5C,EAAA,CAAA0B,SAAA,sBAA2G;;;IAA7C1B,EAA3B,CAAAI,WAAA,qBAA0B,qBAA2B;;;;;;IAOhFJ,EAAA,CAAAoB,UAAA,IAAAyB,sDAAA,0BAMC;;;;;IAHG7C,EADA,CAAAwB,UAAA,qBAAAX,MAAA,CAAAiC,iBAAA,CAAAC,QAAA,CAA+C,4BAAA/C,EAAA,CAAAgD,eAAA,IAAAC,GAAA,EAAAC,UAAA,EAG7C;;;;;IANVlD,EADJ,CAAAC,cAAA,eAA6E,cACqE;IAC1ID,EAAA,CAAAoB,UAAA,IAAA+B,wCAAA,iBAMC;IAGTnD,EADI,CAAAG,YAAA,EAAM,EACC;;;;;;IAVwCH,EAAA,CAAAuB,SAAA,EAA8F;IAA9FvB,EAAA,CAAAwB,UAAA,YAAA0B,UAAA,KAAArC,MAAA,CAAAC,eAAA,wDAA8F;IAEpId,EAAA,CAAAuB,SAAA,EAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,SAAAX,MAAA,CAAAiC,iBAAA,CAAuB;;;;;IAiBpC9C,EAAA,CAAAC,cAAA,0BAOK;IAAAD,EAAA,CAAA0B,SAAA,cACJ;IAAA1B,EAAA,CAAAG,YAAA,EAAkB;;;;IAFfH,EAFA,CAAAI,WAAA,UAAAgD,OAAA,CAAAC,KAAA,GAAAD,OAAA,CAAAC,KAAA,iBAAqD,cAAAD,OAAA,CAAAE,QAAA,GAAAF,OAAA,CAAAE,QAAA,kBACW,cAAAF,OAAA,CAAAG,QAAA,GAAAH,OAAA,CAAAG,QAAA,iBACD;IAH/DvD,EADA,CAAAwB,UAAA,oBAAA4B,OAAA,CAAAI,QAAA,iBAAAJ,OAAA,CAAAK,UAAA,GAAAL,OAAA,CAAAM,YAAA,QAAyF,aAAAN,OAAA,CAAAI,QAAA,mBAAAJ,OAAA,CAAAK,UAAA,gBAChB;IAIhDzD,EAAA,CAAAuB,SAAA,EAAmB;IAAnBvB,EAAA,CAAA2D,UAAA,CAAAP,OAAA,CAAAQ,KAAA,CAAmB;IAAC5D,EAAA,CAAAwB,UAAA,cAAA4B,OAAA,CAAAS,UAAA,EAAA7D,EAAA,CAAA8D,cAAA,CAA4B;;;;;IAUzE9D,EAAA,CAAAC,cAAA,cAAmG;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAA3FH,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAkD,cAAA,CAAAC,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAA6C;IAAExD,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAiE,iBAAA,CAAApD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAsC;;;;;IAGzInE,EAAA,CAAAC,cAAA,UAAoD;IAAAD,EAAA,CAAAE,MAAA,GAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAArDH,EAAA,CAAAuB,SAAA,EAA+C;IAA/CvB,EAAA,CAAAiE,iBAAA,CAAAjE,EAAA,CAAAoE,WAAA,OAAAvD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,EAA+C;;;;;IAGnGnE,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IADFH,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAqE,kBAAA,MAAAxD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,MACJ;;;;;IAGAnE,EAAA,CAAAC,cAAA,UAAqD;IACjDD,EAAA,CAAAE,MAAA,GACJ;;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IADFH,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAqE,kBAAA,MAAAxD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,GAAAnE,EAAA,CAAAoE,WAAA,0BAAApE,EAAA,CAAAoE,WAAA,4BACJ;;;;;IAIIpE,EAAA,CAAA0B,SAAA,eAAgG;;;;;IAChG1B,EAAA,CAAA0B,SAAA,eAA8F;;;;;IAFlG1B,EAAA,CAAAC,cAAA,UAAmD;IAE/CD,EADA,CAAAoB,UAAA,IAAAkD,kEAAA,mBAAyF,IAAAC,kEAAA,mBACF;IAC3FvE,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFKH,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,UAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAuC;IACvCnE,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAsC;;;;;IAIjDnE,EAAA,CAAAC,cAAA,cAAgG;IAAAD,EAAA,CAAAE,MAAA,GAAmE;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAAvHH,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAkD,cAAA,CAAAC,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAA6C;IAACxD,EAAA,CAAAuB,SAAA,EAAmE;IAAnEvB,EAAA,CAAAiE,iBAAA,CAAAjE,EAAA,CAAAwE,WAAA,QAAAC,OAAA,GAAA5D,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,oBAAAiB,OAAA,CAAAN,KAAA,yBAAmE;;;;;IAGnKnE,EAAA,CAAAC,cAAA,cAAoG;IAAAD,EAAA,CAAAE,MAAA,GAAyE;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAA7HH,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAkD,cAAA,CAAAC,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAA6C;IAACxD,EAAA,CAAAuB,SAAA,EAAyE;IAAzEvB,EAAA,CAAAiE,iBAAA,CAAAjE,EAAA,CAAAwE,WAAA,QAAAC,OAAA,GAAA5D,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,oBAAAiB,OAAA,CAAAN,KAAA,+BAAyE;;;;;IAG7KnE,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IADFH,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAqE,kBAAA,MAAAxD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,MACJ;;;;;;IAIInE,EAAA,CAAAC,cAAA,YAMC;IAJGD,EAAA,CAAAK,UAAA,mBAAAqE,mFAAAnE,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAmE,IAAA;MAAA,MAAAX,OAAA,GAAAhE,EAAA,CAAAW,aAAA,IAAAC,SAAA;MAAA,MAAAwC,OAAA,GAAApD,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAASH,MAAA,CAAA+D,SAAA,CAAAZ,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAjD,MAAA,CAAoC;IAAA,EAAC;IAIjDP,EAAA,CAAAG,YAAA,EAAI;;;;;;IAFDH,EAAA,CAAAwB,UAAA,cAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,EAAAnE,EAAA,CAAA8D,cAAA,CAA8C;;;;;IAGlD9D,EAAA,CAAAC,cAAA,UAAoG;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAA5CH,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAiE,iBAAA,CAAApD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAsC;;;;;;IAR9InE,EAAA,CAAAC,cAAA,cAAqF;IAAnCD,EAAA,CAAAK,UAAA,mBAAAwE,iFAAAtE,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAsE,IAAA;MAAA,OAAA9E,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IAQhFjB,EAPA,CAAAoB,UAAA,IAAA2D,+DAAA,gBAMC,IAAAC,iEAAA,kBACmG;IACxGhF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAHGH,EAAA,CAAAuB,SAAA,EAAqD;IAArDvB,EAAA,CAAAwB,UAAA,UAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAyB,mBAAA,CAAqD;IAEpDjF,EAAA,CAAAuB,SAAA,EAA4F;IAA5FvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA6F,IAAA,IAAAtE,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAyB,mBAAA,CAA4F;;;;;;IAM9FjF,EAAA,CAAAC,cAAA,YAKC;IAHGD,EAAA,CAAAK,UAAA,mBAAA+E,0FAAA7E,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAA6E,IAAA;MAAA,MAAAC,KAAA,GAAAtF,EAAA,CAAAW,aAAA,GAAA4E,KAAA;MAAA,MAAAvB,OAAA,GAAAhE,EAAA,CAAAW,aAAA,IAAAC,SAAA;MAAA,MAAAwC,OAAA,GAAApD,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAASH,MAAA,CAAA+D,SAAA,CAAAZ,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAjD,MAAA,EAAA+E,KAAA,CAAqC;IAAA,EAAC;IAGlDtF,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAuB,SAAA,EAAW;IAAXvB,EAAA,CAAAiE,iBAAA,CAAAuB,SAAA,CAAW;;;;;IANhBxF,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAoB,UAAA,IAAAqE,sEAAA,gBAKC;IACLzF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAuB,SAAA,EAAqD;IAArDvB,EAAA,CAAAwB,UAAA,UAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAyB,mBAAA,CAAqD;;;;;;IANlEjF,EAAA,CAAAC,cAAA,cAA2F;IAAnCD,EAAA,CAAAK,UAAA,mBAAAqF,kFAAAnF,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAmF,IAAA;MAAA,OAAA3F,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IACtFjB,EAAA,CAAAoB,UAAA,IAAAwE,kEAAA,kBAAwE;IAQ5E5F,EAAA,CAAAG,YAAA,EAAM;;;;;;IARqBH,EAAA,CAAAuB,SAAA,EAAqC;IAArCvB,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAqC;;;;;IAY5DnE,EAAA,CAAAC,cAAA,UAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAjBH,EAAA,CAAAuB,SAAA,EAAW;IAAXvB,EAAA,CAAAiE,iBAAA,CAAA4B,SAAA,CAAW;;;;;IAD3E7F,EAAA,CAAAC,cAAA,UAAmD;IAC/CD,EAAA,CAAAoB,UAAA,IAAA0E,kEAAA,kBAA4D;IAChE9F,EAAA,CAAAG,YAAA,EAAM;;;;;;IADqBH,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAmC;;;;;;IAI9DnE,EAAA,CAAAC,cAAA,cAA6H;IAAnCD,EAAA,CAAAK,UAAA,mBAAA0F,kFAAAxF,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAwF,IAAA;MAAA,OAAAhG,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IACxHjB,EAAA,CAAAC,cAAA,uBAA4H;IAAlFD,EAAA,CAAA2B,gBAAA,2BAAAsE,mGAAA1F,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAwF,IAAA;MAAA,MAAAhC,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAwC,OAAA,GAAApD,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA8B,kBAAA,CAAajB,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,CAA0B,CAAAW,KAAA,EAAA5D,MAAA,MAA1BM,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,CAA0B,CAAAW,KAAA,GAAA5D,MAAA;MAAA,OAAAP,EAAA,CAAAgB,WAAA,CAAAT,MAAA;IAAA,EAAO;IAACP,EAAA,CAAAK,UAAA,oBAAA6F,4FAAA3F,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAwF,IAAA;MAAA,MAAAhC,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAAUH,MAAA,CAAA6B,UAAA,CAAAsB,OAAA,EAAAzD,MAAA,CAAuB;IAAA,EAAC;IAC/HP,EADgI,CAAAG,YAAA,EAAe,EACzI;;;;;;IADwCH,EAAA,CAAAuB,SAAA,EAA8C;IAA9CvB,EAAA,CAAAkC,gBAAA,YAAArB,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAA8C;;;;;IAI5FnE,EAAA,CAAA0B,SAAA,cAAuG;;;;;;IAArD1B,EAAA,CAAAwB,UAAA,cAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,EAAAnE,EAAA,CAAA8D,cAAA,CAA8C;;;;;;IAI5F9D,EAAA,CAAAC,cAAA,YAKC;IADGD,EAAA,CAAAK,UAAA,mBAAA8F,oFAAA5F,MAAA;MAAA,MAAA6F,UAAA,GAAApG,EAAA,CAAAQ,aAAA,CAAA6F,IAAA,EAAAzF,SAAA;MAAA,MAAAoD,OAAA,GAAAhE,EAAA,CAAAW,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAASH,MAAA,CAAAyF,WAAA,CAAAF,UAAA,CAAAG,UAAA,EAAAvC,OAAA,CAAAwC,EAAA,EAAAxC,OAAA,EAAAzD,MAAA,CAAmD;IAAA,EAAC;IAE7DP,EAAA,CAAA0B,SAAA,WAAuC;IAC3C1B,EAAA,CAAAG,YAAA,EAAI;;;;IAJAH,EAAA,CAAAwB,UAAA,UAAA4E,UAAA,CAAAK,OAAA,CAAwB;IAGlBzG,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAA2D,UAAA,CAAAyC,UAAA,CAAAM,IAAA,CAAyB;;;;;;IAPvC1G,EAAA,CAAAC,cAAA,cAA4H;IAAnCD,EAAA,CAAAK,UAAA,mBAAAsG,kFAAApG,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAoG,IAAA;MAAA,OAAA5G,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IACvHjB,EAAA,CAAAoB,UAAA,IAAAyF,gEAAA,gBAKC;IAGL7G,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPqBH,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAmC;;;;;;IAU9DnE,EAAA,CAAAC,cAAA,cAAuI;IAAnCD,EAAA,CAAAK,UAAA,mBAAAyG,kFAAAvG,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAuG,IAAA;MAAA,OAAA/G,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IAClIjB,EAAA,CAAAC,cAAA,iBAGC;IAFDD,EAAA,CAAAK,UAAA,mBAAA2G,qFAAA;MAAAhH,EAAA,CAAAQ,aAAA,CAAAuG,IAAA;MAAA,MAAA/C,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAwC,OAAA,GAAApD,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAASH,MAAA,CAAAyF,WAAA,CAAYzF,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,CAA0B,CAAAW,KAAA,CAAAoC,UAAA,EAAAvC,OAAA,CAAAwC,EAAA,EAAAxC,OAAA,EAAgC,IAAI,CAAC;IAAA,EAAC;IAGjFhE,EAAA,CAAAC,cAAA,mBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAEvGF,EAFuG,CAAAG,YAAA,EAAW,EACrG,EACP;;;;;;IAJFH,EAAA,CAAAuB,SAAA,EAA0D;IAA1DvB,EAAA,CAAAiH,qBAAA,eAAApG,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAAsC,OAAA,CAA0D;IAEAzG,EAAA,CAAAuB,SAAA,GAAyC;IAAzCvB,EAAA,CAAAiE,iBAAA,CAAApD,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAAuC,IAAA,CAAyC;;;;;;IAY3F1G,EAAA,CAAAC,cAAA,iBAAsH;IAA1DD,EAAA,CAAAK,UAAA,mBAAA6G,oGAAA;MAAAlH,EAAA,CAAAQ,aAAA,CAAA2G,IAAA;MAAA,MAAAC,QAAA,GAAApH,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAoD,OAAA,GAAAhE,EAAA,CAAAW,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAASH,MAAA,CAAAyF,WAAA,CAAAc,QAAA,CAAAb,UAAA,EAAAvC,OAAA,CAAAwC,EAAA,EAAAxC,OAAA,EAA0C,IAAI,CAAC;IAAA,EAAC;IACjHhE,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAC7B;;;;IAFLH,EAAA,CAAAuB,SAAA,EACA;IADAvB,EAAA,CAAAqE,kBAAA,MAAA+C,QAAA,CAAAC,WAAA,MACA;IAAUrH,EAAA,CAAAuB,SAAA,GAAa;IAAbvB,EAAA,CAAAiE,iBAAA,CAAAmD,QAAA,CAAAV,IAAA,CAAa;;;;;IAH/B1G,EAAA,CAAAC,cAAA,UAA2D;IACvDD,EAAA,CAAAoB,UAAA,IAAAkG,2EAAA,qBAAsH;IAI1HtH,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJOH,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAX,MAAA,CAAA0G,gBAAA,CAAAH,QAAA,EAAApD,OAAA,EAAmC;;;;;;IAPxDhE,EAAA,CAAAC,cAAA,cAAoI;IAAnCD,EAAA,CAAAK,UAAA,mBAAAmH,kFAAAjH,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAiH,IAAA;MAAA,OAAAzH,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,eAAA,EAAwB;IAAA,EAAC;IAG3HjB,EAFJ,CAAAC,cAAA,iBAAmD,mBAEF;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAC1DF,EAD0D,CAAAG,YAAA,EAAW,EAC5D;IACTH,EAAA,CAAAC,cAAA,wBAA0B;IACtBD,EAAA,CAAAoB,UAAA,IAAAsG,kEAAA,kBAA2D;IAOnE1H,EADI,CAAAG,YAAA,EAAW,EACT;;;;;;;IAZsBH,EAAA,CAAAuB,SAAA,EAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,sBAAAmG,QAAA,CAA0B;IAKxB3H,EAAA,CAAAuB,SAAA,GAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,YAAAX,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAmC;;;;;IA1GrEnE,EAAA,CAAAC,cAAA,eAMC;IA8FGD,EA5FA,CAAAoB,UAAA,IAAAwG,2DAAA,kBAAmG,IAAAC,2DAAA,kBAG/C,IAAAC,2DAAA,kBAGE,IAAAC,2DAAA,kBAKD,IAAAC,2DAAA,kBAKF,IAAAC,2DAAA,kBAM6C,IAAAC,2DAAA,kBAGI,IAAAC,2DAAA,kBAG9C,IAAAC,2DAAA,kBAK+B,KAAAC,4DAAA,kBAYM,KAAAC,4DAAA,kBAYxC,KAAAC,4DAAA,kBAK0E,KAAAC,4DAAA,kBAK5B,KAAAC,4DAAA,kBAG2B,KAAAC,4DAAA,kBAYW,KAAAC,4DAAA,kBAUH;IAcxI3I,EAAA,CAAAG,YAAA,EAAW;;;;;;;IAhHPH,EAAA,CAAA2D,UAAA,EAAAiF,OAAA,GAAA/H,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,oBAAAoF,OAAA,CAAAhF,KAAA,CAA2C;IAG3C5D,EAFA,CAAAI,WAAA,UAAAgD,OAAA,CAAAC,KAAA,GAAAD,OAAA,CAAAC,KAAA,iBAAqD,cAAAD,OAAA,CAAAE,QAAA,GAAAF,OAAA,CAAAE,QAAA,kBACW,cAAAF,OAAA,CAAAG,QAAA,GAAAH,OAAA,CAAAG,QAAA,iBACD;IAGzDvD,EAAA,CAAAuB,SAAA,EAA4C;IAA5CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAuJ,MAAA,CAA4C;IAG5C7I,EAAA,CAAAuB,SAAA,EAA4C;IAA5CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAwJ,MAAA,CAA4C;IAG5C9I,EAAA,CAAAuB,SAAA,EAA8C;IAA9CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAyJ,QAAA,CAA8C;IAK9C/I,EAAA,CAAAuB,SAAA,EAA6C;IAA7CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA0J,OAAA,CAA6C;IAK7ChJ,EAAA,CAAAuB,SAAA,EAA2C;IAA3CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA2J,KAAA,CAA2C;IAM3CjJ,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA4J,IAAA,CAA0C;IAG1ClJ,EAAA,CAAAuB,SAAA,EAA8C;IAA9CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA6J,QAAA,CAA8C;IAG9CnJ,EAAA,CAAAuB,SAAA,EAA8C;IAA9CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA8J,QAAA,CAA8C;IAK9CpJ,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA6F,IAAA,CAA0C;IAY1CnF,EAAA,CAAAuB,SAAA,EAA+C;IAA/CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAA+J,SAAA,CAA+C;IAY/CrJ,EAAA,CAAAuB,SAAA,EAA2C;IAA3CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAgK,KAAA,CAA2C;IAK3CtJ,EAAA,CAAAuB,SAAA,EAAkF;IAAlFvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAiK,QAAA,KAAA1I,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAgG,IAAA,CAAkF;IAKlFxJ,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAmK,IAAA,CAA0C;IAG1CzJ,EAAA,CAAAuB,SAAA,EAAiF;IAAjFvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAoK,OAAA,IAAA7I,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAiF;IAYjFnE,EAAA,CAAAuB,SAAA,EAA4F;IAA5FvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAqK,kBAAA,IAAA9I,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAA4F;IAU5FnE,EAAA,CAAAuB,SAAA,EAAyF;IAAzFvB,EAAA,CAAAwB,UAAA,SAAA4B,OAAA,CAAA8B,IAAA,KAAArE,MAAA,CAAAvB,kBAAA,CAAAsK,eAAA,IAAA/I,MAAA,CAAAqD,OAAA,CAAAF,OAAA,EAAAZ,OAAA,CAAAI,QAAA,EAAAW,KAAA,CAAyF;;;;;IAgBnGnE,EAAA,CAAAC,cAAA,sBAKC;IACGD,EAAA,CAAA0B,SAAA,cAA4D;IAChE1B,EAAA,CAAAG,YAAA,EAAkB;;;;;;IAHdH,EAFA,CAAAI,WAAA,UAAAgD,OAAA,CAAAC,KAAA,GAAAD,OAAA,CAAAC,KAAA,iBAAqD,cAAAD,OAAA,CAAAE,QAAA,GAAAF,OAAA,CAAAE,QAAA,kBACW,cAAAF,OAAA,CAAAG,QAAA,GAAAH,OAAA,CAAAG,QAAA,iBACD;IAE1DvD,EAAA,CAAAuB,SAAA,EAAgD;IAAhDvB,EAAA,CAAAwB,UAAA,eAAAiD,OAAA,GAAA5D,MAAA,CAAAgJ,aAAA,CAAAzG,OAAA,CAAAI,QAAA,oBAAAiB,OAAA,CAAAN,KAAA,EAAAnE,EAAA,CAAA8D,cAAA,CAAgD;;;;;IAzI7D9D,EAAA,CAAA8J,uBAAA,OAKC;IA8HG9J,EA7HA,CAAAoB,UAAA,IAAA2I,4DAAA,+BAOK,IAAAC,qDAAA,yBAQJ,IAAAC,4DAAA,8BAmHA;;;;;IApIDjK,EAFA,CAAAwB,UAAA,iBAAA4B,OAAA,CAAAI,QAAA,CAA6B,WAAAJ,OAAA,CAAA8G,UAAA,CACJ,cAAA9G,OAAA,CAAA+G,WAAA,IAAA/G,OAAA,CAAAI,QAAA,eACkC;;;;;IAyI/DxD,EAAA,CAAA0B,SAAA,qBAAmF;;;;;;IAEnF1B,EAAA,CAAAC,cAAA,kBASC;IADGD,EALA,CAAAK,UAAA,mBAAA+J,+DAAA;MAAA,MAAAC,WAAA,GAAArK,EAAA,CAAAQ,aAAA,CAAA8J,IAAA,EAAA1J,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAASH,MAAA,CAAA0J,QAAA,CAAAF,WAAA,CAAiB;IAAA,EAAC,mBAAAD,+DAAA;MAAA,MAAAC,WAAA,GAAArK,EAAA,CAAAQ,aAAA,CAAA8J,IAAA,EAAA1J,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAgB,WAAA,CAAAH,MAAA,CAAAC,eAAA,GAAAD,MAAA,CAAAC,eAAA,KAAAuJ,WAAA,GAK8B,IAAI,GAAAA,WAAA;IAAA,EAAW;IAC3ErK,EAAA,CAAAG,YAAA,EAAU;;;;;IALPH,EAAA,CAAA2D,UAAA,CAAA0G,WAAA,CAAAzG,KAAA,CAAuB;IAGvB5D,EAFA,CAAAwK,WAAA,6BAAA3J,MAAA,CAAA4J,aAAA,CAAgD,2BAAA5J,MAAA,CAAA6J,WAAA,KAAAL,WAAA,CACQ,yBAAAxJ,MAAA,CAAAC,eAAA,KAAAuJ,WAAA,CACE;;;;;IAG9DrK,EAAA,CAAA0B,SAAA,kBAA2G;;;;;IAE3G1B,EAAA,CAAA0B,SAAA,qBAGkB;;;;IADd1B,EAAA,CAAAI,WAAA,YAAAS,MAAA,CAAA8J,WAAA,IAAA9J,MAAA,CAAA8J,WAAA,CAAAC,MAAA,uBAAyE;;;;;;IAE7E5K,EAAA,CAAAoB,UAAA,IAAAyJ,2CAAA,0BAAgH;;;;IAA/B7K,EAA7C,CAAAwB,UAAA,qBAAAX,MAAA,CAAAiK,cAAA,CAAA/H,QAAA,CAA4C,4BAAA/C,EAAA,CAAA+K,eAAA,IAAAC,GAAA,EAA+B;;;;;IAEnHhL,EAAA,CAAA0B,SAAA,cAQM;;;ADtMV,MAAMuJ,wBAAwB,GAAa,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAQzD,OAAM,MAAOC,iBAAkB,SAAQpL,gBAAgB;EAwGnDqL,YAAYC,QAAkB,EAAUC,QAAmB,EAAUC,iBAAoC,EAAUC,iBAAoC;IACnJ,KAAK,CAACH,QAAQ,CAAC;IADqB,KAAAC,QAAQ,GAARA,QAAQ;IAAqB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAA6B,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAnG3H,KAAAC,gBAAgB,GAAkB,KAAK,CAAC,CAAC;IACzC,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAMf,KAAAhB,WAAW,GAA2B,IAAI;IAC1C,KAAAiB,cAAc,GAAG,KAAK,CAAC,CAAC;IACxB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,MAAM,CAAC,CAAC;IACvB,KAAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IACnB,KAAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IAEnB,KAAAC,eAAe,GAAG,KAAK,CAAC,CAAC;IACzB,KAAAC,eAAe,GAAG,GAAG,CAAC,CAAC;IAEvB,KAAAC,eAAe,GAAalB,wBAAwB;IAEpD,KAAAmB,YAAY,GAAY,KAAK;IAEtC;IACU,KAAAC,UAAU,GAAwC,IAAIpN,YAAY,EAAE;IAE9E;IACU,KAAAqN,aAAa,GAA8C,IAAIrN,YAAY,EAAE;IAEvF;IACU,KAAAsN,WAAW,GAA6C,IAAItN,YAAY,EAAE;IAEpF;IACA;IACU,KAAAuN,UAAU,GAAwC,IAAIvN,YAAY,EAAE;IAE9E;IACU,KAAAwN,eAAe,GAA6C,IAAIxN,YAAY,EAAE;IAExF;IACU,KAAAyN,MAAM,GAAyB,IAAIzN,YAAY,EAAE;IAE3D;IACU,KAAA0N,WAAW,GAA2C,IAAI1N,YAAY,EAAE;IAkBlF,KAAA2N,YAAY,GAAGzN,iBAAiB,CAAC0N,UAAU,CAACC,MAAM;IAElD;IACA,KAAAC,IAAI,GAAsB,EAAE;IAC5B,KAAAC,OAAO,GAAqB,EAAE;IAK9B,KAAAC,KAAK,GAAQ,EAAE;IAKf;IACS,KAAAC,YAAY,GAAG,OAAO;IACtB,KAAAC,eAAe,GAA2B,IAAI;IAEvD,KAAA7N,kBAAkB,GAAGA,kBAAkB;IACvC,KAAA8N,gBAAgB,GAAa,EAAE;IAE/B,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,aAAa,GAAkB,EAAE;IAEjC,KAAAC,QAAQ,GAAQ,EAAE;IAKlB;IACA,KAAAC,mBAAmB,GAAG,KAAK;IAI3B,KAAA7C,WAAW,GAAuB,EAAE;IAEpC,KAAA5I,aAAa,GAAG,KAAK;IAIjB,IAAI,CAACmL,YAAY,GAAG,OAAO;EAC/B;EAEAO,QAAQA,CAAA;IACJ,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACK,mBAAmB;IACzC,IAAI,CAACJ,aAAa,GAAG,IAAI,CAAC9B,gBAAgB;IAE1C,IAAI,CAACmC,gBAAgB,GAAG,IAAI,CAACC,YAAY,CAACC,IAAI,CAACC,SAAS,CAACD,IAAI,IAAG;MAC5D;MACA,IAAIA,IAAI,IAAIA,IAAI,CAACE,OAAO,KAAK,IAAI,CAACvH,EAAE,EAAE;QAClC,IAAI,CAACmE,WAAW,GAAGkD,IAAI,CAAClD,WAAW;QAEnC;QACA,IAAI,CAACqD,SAAS,CAACvC,QAAQ,GAAG,IAAI,CAACA,QAAQ;QACvC,IAAI,CAACwC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACb,SAAS;QACjC,IAAI,CAACY,IAAI,CAACE,SAAS,GAAG,IAAI,CAACb,aAAa;QAExC;QACAc,UAAU,CAAC,MAAK;UACZ,IAAI,CAAChB,gBAAgB,GAAG,EAAE;UAE1B,IAAI,IAAI,CAACzB,cAAc,EAAE;YACrB,IAAI,CAACyB,gBAAgB,CAACiB,IAAI,CAAC,QAAQ,CAAC;UACxC;UAEA,IAAI,IAAI,CAACpC,eAAe,EAAE;YACtB,IAAI,CAACmB,gBAAgB,CAACiB,IAAI,CAAC,QAAQ,CAAC;UACxC;UAEA,IAAI,CAACC,OAAO,CAACC,OAAO,CAACC,CAAC,IAAG;YACrB,IAAI,CAACpB,gBAAgB,CAACiB,IAAI,CAACG,CAAC,CAAChL,QAAQ,CAAC;UAC1C,CAAC,CAAC;UAEF,IAAI,CAAC8H,iBAAiB,CAACmD,aAAa,EAAE;QAC1C,CAAC,EAAE,GAAG,CAAC;QAEP;QACA,IAAI,IAAI,CAACjI,EAAE,EAAE;UACT,MAAMkI,YAAY,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,IAAI,CAACpI,EAAE,CAAC;UAC7D,IAAIkI,YAAY,IAAIA,YAAY,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,YAAY,CAAC,CAAC,CAAC,CAACG,OAAO,KAAK,WAAW,EAAE;YACpF,IAAI,CAACxD,QAAQ,CAACyD,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC5C,YAAY,CAAC;YACxE,IAAI,CAACT,QAAQ,CAACyD,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC;YAE7D;YACA,IAAIb,IAAI,CAACkB,gBAAgB,EAAE;cACvBL,YAAY,CAAC,CAAC,CAAC,CAACM,SAAS,GAAG,CAAC;YACjC;UACJ;QACJ;QAEA,IAAI,CAAC1D,iBAAiB,CAACmD,aAAa,EAAE;QAEtC;QACA,IAAIZ,IAAI,CAACkB,gBAAgB,EAAE;UACvB,IAAI,CAACrD,SAAS,GAAG,CAAC;UAClB,IAAI,CAACqB,IAAI,GAAG,EAAE;UACd,IAAI,CAACkC,YAAY,GAAG,CAAC;UACrB,IAAI,CAAChC,KAAK,GAAG,EAAE;QACnB;QAEA,IAAI,IAAI,CAACrB,eAAe,EAAE;UACtB;UACA,IAAI,CAACqD,YAAY,GAAGpB,IAAI,CAACoB,YAAY;UAErC;UACA;UACA,IAAI,CAAC,IAAI,CAAClC,IAAI,EAAE;YACZ,IAAI,CAACA,IAAI,GAAGmC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvB,IAAI,CAACA,IAAI,CAAC,CAAC;UACrD;UAEA;UACA;UACA,MAAMwB,KAAK,GAAG,IAAI,CAAC3D,SAAS,GAAG,IAAI,CAACD,QAAQ;UAE5C;UACA,MAAMsB,IAAI,GAAG,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC;UAE3B;UACA,IAAIc,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACjD,MAAM,GAAG,CAAC,EAAE;YACnCmC,IAAI,CAACuC,MAAM,CAACD,KAAK,EAAE,IAAI,CAAC5D,QAAQ,EAAE,GAAGyD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvB,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC;UAC/E;UAEA;UACA,IAAI,CAACd,IAAI,GAAGA,IAAI;UAEhB,IAAI,CAACS,mBAAmB,GAAG,KAAK;QACpC,CAAC,MAAM;UACH,IAAI,CAACR,OAAO,GAAGkC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvB,IAAI,CAACA,IAAI,CAAC,CAAC;UACpD,IAAG,IAAI,CAAC0B,QAAQ,EAAE;YACd,IAAI,CAACxC,IAAI,GAAG,IAAI,CAACC,OAAO;UAC5B,CAAC,MAAM;YACH,MAAMqC,KAAK,GAAG,IAAI,CAAC3D,SAAS,GAAG,IAAI,CAACD,QAAQ;YAE5C,IAAI+D,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACxC,OAAO,CAAC;YAEhC,IAAI,CAACD,IAAI,GAAGyC,QAAQ,CAACF,MAAM,CAACD,KAAK,EAAE,IAAI,CAAC5D,QAAQ,CAAC;UACrD;UACA,IAAI,CAACwD,YAAY,GAAGpB,IAAI,CAACoB,YAAY;QACzC;QACA,IAAG,IAAI,CAACxE,aAAa,EAAC;UAClB,IAAG,IAAI,CAACgF,KAAK,IAAI,IAAI,CAAC1C,IAAI,CAAC,CAAC,CAAC,EAAC;YAC1B;YACA;UAAA;QAER;MACJ;IACJ,CAAC,CAAC;EACN;EAEA2C,eAAeA,CAAA;IACX,IAAI,IAAI,CAAC1B,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACvC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC3C;IAEA,IAAI,IAAI,CAACwC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACb,SAAS;MACjC,IAAI,CAACY,IAAI,CAACE,SAAS,GAAG,IAAI,CAACb,aAAa;IAC5C;IAEA;IACA,IAAI,CAACqC,gBAAgB,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,IAAI,CACtCC,IAAI,CACD3Q,GAAG,CAAC,MAAK;MACL,IAAI,CAACwM,SAAS,GAAG,IAAI,CAACsC,SAAS,CAACtC,SAAS;MACzC,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACuC,SAAS,CAACvC,QAAQ;MAEvC,IAAI,IAAI,CAAC8D,QAAQ,EAAE;QACf,MAAMO,aAAa,GAAG,IAAIvQ,qBAAqB,EAAE;QACjDuQ,aAAa,CAACC,UAAU,GAAG,IAAI,CAACrE,SAAS;QACzCoE,aAAa,CAACrE,QAAQ,GAAG,IAAI,CAACA,QAAQ;QACtCqE,aAAa,CAACzC,SAAS,GAAGtN,YAAY,CAACiQ,qBAAqB,CAAC,IAAI,CAAC3C,SAAS,CAAC;QAC5EyC,aAAa,CAACG,WAAW,GAAG,IAAI,CAAC3C,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI;QAExE;QACA;QACA,IAAI,CAACd,UAAU,CAAC0D,IAAI,CAACJ,aAAa,CAAC;MACvC,CAAC,MAAM;QAEH,IAAIN,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACxC,OAAO,CAAC;QAChC,IAAIqC,KAAK,GAAG,IAAI,CAAC3D,SAAS,GAAG,IAAI,CAACD,QAAQ;QAC1C,IAAI,CAACsB,IAAI,GAAGyC,QAAQ,CAACF,MAAM,CAACD,KAAK,EAAC,IAAI,CAAC5D,QAAQ,CAAC;MACpD;IACJ,CAAC,CAAC,CACL,CACAqC,SAAS,EAAE;IAEhB,IAAI,CAACqC,gBAAgB,GAAG,IAAI,CAAClC,IAAI,CAACmC,UAAU,CACvCP,IAAI,CACD3Q,GAAG,CAAC,MAAK;MACL,IAAI,CAACmO,SAAS,GAAG,IAAI,CAACY,IAAI,CAACC,MAAM;MACjC,IAAI,CAACZ,aAAa,GAAG,IAAI,CAACW,IAAI,CAACE,SAAS;MAExC,IAAI,IAAI,CAACoB,QAAQ,EAAE;QACf,IAAI,CAAC7D,SAAS,GAAG,CAAC;QAElB,MAAMoE,aAAa,GAAG,IAAIvQ,qBAAqB,EAAE;QACjDuQ,aAAa,CAACC,UAAU,GAAG,IAAI,CAACrE,SAAS;QACzCoE,aAAa,CAACrE,QAAQ,GAAG,IAAI,CAACA,QAAQ;QACtCqE,aAAa,CAACzC,SAAS,GAAGtN,YAAY,CAACiQ,qBAAqB,CAAC,IAAI,CAAC3C,SAAS,CAAC;QAC5EyC,aAAa,CAACG,WAAW,GAAG,IAAI,CAAC3C,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI;QAExE;QACA;QACA,IAAI,CAACd,UAAU,CAAC0D,IAAI,CAACJ,aAAa,CAAC;MACvC,CAAC,MAAM;QACH,MAAM/C,IAAI,GAAG,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC;QAC3BA,IAAI,CAACkB,IAAI,CAAC,CAACoC,CAAC,EAAEC,CAAC,KAAI;UACf;UACA,MAAMC,MAAM,GAAGF,CAAC,CAACG,KAAK,CAACC,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAAChL,QAAQ,CAACkN,WAAW,EAAE,KAAK,IAAI,CAACrD,SAAS,CAACqD,WAAW,EAAE,CAAC,EAAEvM,KAAK;UAClG,MAAMwM,MAAM,GAAGL,CAAC,CAACE,KAAK,CAACC,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAAChL,QAAQ,CAACkN,WAAW,EAAE,KAAK,IAAI,CAACrD,SAAS,CAACqD,WAAW,EAAE,CAAC,EAAEvM,KAAK;UAElG,OAAOoM,MAAM,CAACK,aAAa,CAACD,MAAM,CAAC,IAAI,IAAI,CAACrD,aAAa,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAClF,CAAC,CAAC;QACF,IAAI,CAACP,IAAI,GAAGA,IAAI;MACpB;IACJ,CAAC,CAAC,CACL,CACAe,SAAS,EAAE;EACpB;EAEA+C,kBAAkBA,CAAA;IACd;IACA,IAAI,IAAI,CAACrK,EAAE,EAAE;MACT,MAAMkI,YAAY,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,IAAI,CAACpI,EAAE,CAAC;MAC7D,IAAIkI,YAAY,IAAIA,YAAY,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,YAAY,CAAC,CAAC,CAAC,CAACG,OAAO,KAAK,WAAW,EAAE;QACpF,IAAI,CAACxD,QAAQ,CAACyD,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC5C,YAAY,CAAC;QACxE,IAAI,CAACT,QAAQ,CAACyD,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC;MACjE;IACJ;IACA,IAAG,IAAI,CAACtC,YAAY,EAAE;MAClB,IAAI0E,UAAU,GAAGnC,QAAQ,CAACC,sBAAsB,CAAC,mCAAmC,CAAC;MACrF,IAAGkC,UAAU,EAAE;QACX,IAAIC,CAAC,GAAGD,UAAU,CAAC,CAAC,CAAC;QACrBC,CAAC,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC7B;IACJ;EACJ;EAEAC,aAAaA,CAACC,CAAM;IAChB;IACA,IAAI,CAAC,IAAI,CAACvF,eAAe,EAAE;IAE3B;IACA,IAAI,IAAI,CAACmB,IAAI,CAACnC,MAAM,KAAK,IAAI,CAACqE,YAAY,IAAI,IAAI,CAACzB,mBAAmB,EAAE;IAExE,MAAM4D,eAAe,GAAGD,CAAC,CAACE,MAAM,CAACC,YAAY,CAAC,CAAC;IAC/C,MAAMC,iBAAiB,GAAGJ,CAAC,CAACE,MAAM,CAACvF,YAAY,CAAC,CAAC;IACjD,MAAM0F,cAAc,GAAGL,CAAC,CAACE,MAAM,CAACrC,SAAS,CAAC,CAAC;IAE3C;IACA,MAAMyC,MAAM,GAAG,EAAE;IACjB,MAAMC,KAAK,GAAGH,iBAAiB,GAAGH,eAAe,GAAGK,MAAM;IAC1D,IAAID,cAAc,GAAGE,KAAK,EAAE;MACxB,IAAI,CAAClE,mBAAmB,GAAG,IAAI;MAE/B;MACA;MACA,IAAI,IAAI,CAACP,KAAK,CAAC,IAAI,CAACvB,SAAS,GAAG,CAAC,CAAC,EAAE;MACpC,IAAI,CAACuB,KAAK,CAAC,IAAI,CAACvB,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI;MAErC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;MAEnC,MAAMoE,aAAa,GAAG,IAAIvQ,qBAAqB,EAAE;MACjDuQ,aAAa,CAACC,UAAU,GAAG,IAAI,CAACrE,SAAS;MACzCoE,aAAa,CAACrE,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACtCqE,aAAa,CAACzC,SAAS,GAAG,IAAI,CAACA,SAAS;MACxCyC,aAAa,CAACG,WAAW,GAAG,IAAI,CAAC3C,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI;MAExE;MACA;MACA,IAAI,CAACd,UAAU,CAAC0D,IAAI,CAACJ,aAAa,CAAC;IACvC;EACJ;EAEAvF,QAAQA,CAACoH,GAAoB;IACzB;IACA,IAAI,CAAC,IAAI,CAAClH,aAAa,EAAE;IAEzB,IAAI,CAACC,WAAW,GAAGiH,GAAG;IAEtB,MAAMC,KAAK,GAAG,IAAInS,qBAAqB,EAAE;IACzCmS,KAAK,CAACpL,EAAE,GAAGmL,GAAG,CAACnL,EAAE;IACjBoL,KAAK,CAACC,OAAO,GAAGF,GAAG;IAEnB,IAAI,CAACtF,UAAU,CAAC6D,IAAI,CAAC0B,KAAK,CAAC;EAC/B;EAEAhN,SAASA,CAAC+M,GAAoB,EAAEnO,QAAQ,EAAEsO,EAAE,EAACC,CAAE;IAC3C,MAAMH,KAAK,GAAG,IAAIxS,0BAA0B,EAAE;IAC9CwS,KAAK,CAACpL,EAAE,GAAGmL,GAAG,CAACnL,EAAE;IACjBoL,KAAK,CAACI,OAAO,GAAGL,GAAG,CAACK,OAAO;IAC3BJ,KAAK,CAACpO,QAAQ,GAAGA,QAAQ;IACzBoO,KAAK,CAACA,KAAK,GAAGE,EAAE;IAChBF,KAAK,CAACK,UAAU,GAAGF,CAAC;IAEpB,IAAI,CAACxF,WAAW,CAAC2D,IAAI,CAAC0B,KAAK,CAAC;EAChC;EAEAtL,WAAWA,CAAC4L,MAAM,EAAE1L,EAAE,EAAEmL,GAAoB,EAAEG,EAAE;IAC5C,IAAI,CAACpH,WAAW,GAAG,IAAI,CAACqC,IAAI,CAAC0D,IAAI,CAACjC,CAAC,IAAG;MAClC,OAAOA,CAAC,CAAChI,EAAE,KAAKA,EAAE;IACtB,CAAC,CAAC;IAEF,IAAI,IAAI,CAACkE,WAAW,EAAE;MAClB,IAAI,CAACH,QAAQ,CAACoH,GAAG,CAAC;IACtB;IAEA,MAAMC,KAAK,GAAG,IAAIpS,2BAA2B,EAAE;IAC/CoS,KAAK,CAACM,MAAM,GAAGA,MAAM;IACrBN,KAAK,CAACpL,EAAE,GAAGA,EAAE;IACboL,KAAK,CAAC/D,IAAI,GAAG8D,GAAG;IAChBC,KAAK,CAACA,KAAK,GAAGE,EAAE;IAEhB,IAAI,CAACxF,aAAa,CAAC4D,IAAI,CAAC0B,KAAK,CAAC;EAClC;EAEA1N,OAAOA,CAACyN,GAAoB,EAAEnO,QAAQ;IAC9B,IAAI,CAACmO,GAAG,IAAI,CAACA,GAAG,CAACnB,KAAK,EAAE,OAAO,IAAI;IAEnC,MAAM2B,IAAI,GAAGR,GAAG,CAACnB,KAAK,CAACC,IAAI,CAAC0B,IAAI,IAAG;MAC/B,OAAOA,IAAI,CAAC3O,QAAQ,KAAKA,QAAQ;IACrC,CAAC,CAAC;IACF,OAAO2O,IAAI;EACnB;EAEApO,cAAcA,CAAC4N,GAAmB,EAAE9N,UAAiB;IACjD,IAAG8N,GAAG,CAACK,OAAO,EAAEI,SAAS,EAAC;MACtB,OAAO,gCAAgC;IAC3C;IACA,IAAGvO,UAAU,KAAK,mBAAmB,IAAIA,UAAU,KAAK,kBAAkB,EAAC;MACvE,IAAG8N,GAAG,CAACK,OAAO,CAACK,gBAAgB,EAAE3B,WAAW,EAAE,IAAI,aAAa,EAAE,OAAO,cAAc;MAEtF,MAAM4B,MAAM,GAAUX,GAAG,CAACK,OAAO,EAAEM,MAAM,GAAGX,GAAG,CAACK,OAAO,EAAEM,MAAM,CAAC5B,WAAW,EAAE,GAAGiB,GAAG,CAACK,OAAO,CAACK,gBAAgB,EAAE3B,WAAW,EAAE;MAC3H,QAAQ4B,MAAM;QACV,KAAK,aAAa;UAChB,OAAO,aAAa;QAEtB,KAAK,OAAO;QACZ,KAAK,sBAAsB;UACvB,OAAO,aAAa;QAExB,KAAK,UAAU;QACf,KAAK,kBAAkB;UACnB,OAAO,eAAe;QAE1B,KAAK,WAAW;QAChB,KAAK,aAAa;QAClB,KAAK,aAAa;UAChB,OAAO,cAAc;QAEvB,KAAK,SAAS;UACZ,OAAO,gCAAgC;QAEzC,KAAK,MAAM;UACP,OAAO,cAAc;QAEzB,KAAK,MAAM;UACP,OAAO,YAAY;QAEvB,KAAK,kBAAkB;UACnB,OAAO,eAAe;QAE1B,KAAK,QAAQ;UACT,OAAO,aAAa;QAExB;UACE,OAAO,cAAc;MACzB;IACN;IACA,OAAO,EAAE;EACb;EAEA/K,gBAAgBA,CAACgL,UAAU,EAAEZ,GAAG;IAC5B,IAAGY,UAAU,CAACC,MAAM,KAAK,oBAAoB,EAAC;MAC1C,MAAMC,uBAAuB,GAAG,IAAI,CAAClH,iBAAiB,CAACmH,gBAAgB,CAAC,8BAA8B,CAAC;MACvG,MAAMC,cAAc,GAAI,IAAI,CAACpH,iBAAiB,CAACmH,gBAAgB,CAAC,2CAA2C,CAAC;MAE5G,IAAGH,UAAU,CAAChM,UAAU,KAAK,QAAQ,EAAC;QAClC,IAAG,EAAEoL,GAAG,CAACK,OAAO,CAACM,MAAM,KAAK,OAAO,IAAIX,GAAG,CAACK,OAAO,CAACM,MAAM,KAAK,UAAU,CAAC,IAAI,CAACG,uBAAuB,EAAE;UAC/F,OAAO,KAAK;QAChB;MACR;MACA,IAAGF,UAAU,CAAChM,UAAU,KAAK,MAAM,EAAC;QAChC,IAAGoL,GAAG,CAACK,OAAO,CAACM,MAAM,KAAK,WAAW,IAAIX,GAAG,CAACK,OAAO,CAACM,MAAM,KAAK,aAAa,IAAI,CAACG,uBAAuB,EAAC;UACtG,OAAO,KAAK;QAChB;MACJ;MACA,IAAGF,UAAU,CAAChM,UAAU,KAAK,MAAM,EAAC;QAChC,IAAG,CAACoM,cAAc,EAAC;UACf,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,OAAO,IAAI;EACf;EAEAC,YAAYA,CAACjB,GAAG;IACZ,IAAI,CAACA,GAAG,EAAE;MACN,OAAO,EAAE;IACb;IACA,IAAIA,GAAG,CAACkB,MAAM,KAAKC,SAAS,EAAE;MAC1B,OAAO,EAAE;IACb;IACA,OAAOnB,GAAG,CAACkB,MAAM;EACrB;EAEAE,YAAYA,CAAC;IAAEpB,GAAG;IAAEqB,MAAM;IAAE7O;EAAK,CAAE;IAC/B,OAAOwN,GAAG,CAACnB,KAAK,CAACC,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAAChL,QAAQ,KAAKwP,MAAM,CAACC,IAAI,CAAC,EAAErP,KAAK;EACjE;EAEAiG,aAAaA,CAACrG,QAAQ;IAClB,IAAI,CAAC,IAAI,CAACmH,WAAW,EAAE,OAAO,IAAI;IAElC,MAAMwH,IAAI,GAAG,IAAI,CAACxH,WAAW,CAAC8F,IAAI,CAAC0B,IAAI,IAAG;MACtC,OAAOA,IAAI,CAAC3O,QAAQ,KAAKA,QAAQ;IACrC,CAAC,CAAC;IACF,OAAO2O,IAAI;EACf;EAEAe,WAAWA,CAACvB,GAAG;IACX,OAAOA,GAAG,CAAC/N,KAAK;EACpB;EAEA7C,eAAeA,CAAC4Q,GAAG;IACfvD,UAAU,CAAC,MAAK;MACZ,IAAI,CAACzB,WAAW,CAACuD,IAAI,CAAC;QAClB1J,EAAE,EAAEmL,GAAG,CAACnL,EAAE;QACVwL,OAAO,EAAEL,GAAG,CAACK;OAChB,CAAC;IACN,CAAC,EAAE,GAAG,CAAC;EACX;EAEA/P,SAASA,CAAC2P,KAAK;IACX,IAAI,CAAC7E,IAAI,CAACwB,OAAO,CAAC4E,CAAC,IAAG;MAClBA,CAAC,CAAC3Q,OAAO,GAAGoP,KAAK,CAACpP,OAAO;IAC7B,CAAC,CAAC;IAEF,MAAM4Q,aAAa,GAAG,IAAI/T,0BAA0B,CAACuS,KAAK,CAACpP,OAAO,GAAG,IAAI,CAACuK,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE6E,KAAK,CAACpP,OAAO,EAAE,IAAI,CAAC;IAC/G,IAAI,CAACiK,eAAe,CAACyD,IAAI,CAACkD,aAAa,CAAC;EAC5C;EAEA1Q,UAAUA,CAACiP,GAAoB,EAAEC,KAAK;IAClC,MAAMwB,aAAa,GAAG,IAAI/T,0BAA0B,CAAC,CAACsS,GAAG,CAAC,EAAEA,GAAG,CAACnL,EAAE,EAAEoL,KAAK,CAACpP,OAAO,EAAE,KAAK,CAAC;IACzF,IAAI,CAACiK,eAAe,CAACyD,IAAI,CAACkD,aAAa,CAAC;EAC5C;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAAC1F,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAAC2F,WAAW,EAAE;IAC9D,IAAI,IAAI,CAAC3D,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAAC2D,WAAW,EAAE;IAC9D,IAAI,IAAI,CAACnD,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACmD,WAAW,EAAE;EAClE;;;uBApgBSpI,iBAAiB,EAAAlL,EAAA,CAAAuT,iBAAA,CAAAvT,EAAA,CAAAwT,QAAA,GAAAxT,EAAA,CAAAuT,iBAAA,CAAAvT,EAAA,CAAAyT,SAAA,GAAAzT,EAAA,CAAAuT,iBAAA,CAAAvT,EAAA,CAAA0T,iBAAA,GAAA1T,EAAA,CAAAuT,iBAAA,CAAAI,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAjB1I,iBAAiB;MAAA2I,SAAA;MAAAC,cAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;sCAqDZtU,kCAAkC;sCAMlCG,kCAAkC;;;;;;;;;;;yBAiCrCF,YAAY;yBACZC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCxIlBI,EADJ,CAAAC,cAAA,aAAkD,sBAY7C;UANGD,EAAA,CAAAK,UAAA,oBAAA8T,uDAAA5T,MAAA;YAAAP,EAAA,CAAAQ,aAAA,CAAA4T,GAAA;YAAA,OAAApU,EAAA,CAAAgB,WAAA,CAAUiT,GAAA,CAAA/C,aAAA,CAAA3Q,MAAA,CAAqB;UAAA,EAAC;UAOhCP,EAAA,CAAA8J,uBAAA,MAA2C;UAsBvC9J,EArBA,CAAAoB,UAAA,IAAAiT,4CAAA,6BAAkH,IAAAC,qCAAA,sBACxB,IAAAC,4CAAA,6BAoBD;;UAI7FvU,EAAA,CAAA8J,uBAAA,MAAoD;UAOhD9J,EANA,CAAAoB,UAAA,IAAAoT,4CAAA,6BAAyF,IAAAC,qCAAA,sBAGH,KAAAC,6CAAA,6BAGG;;UAI7F1U,EAAA,CAAA8J,uBAAA,QAA4C;UACxC9J,EAAA,CAAAoB,UAAA,KAAAuT,sCAAA,uBAA6E;;UA6KjF3U,EA/JA,CAAAoB,UAAA,KAAAwT,0CAAA,2BAKC,KAAAC,4CAAA,6BAwIiE,KAAAC,qCAAA,sBAWjE,KAAAC,qCAAA,sBACgG,KAAAC,4CAAA,6BAKhG,KAAAC,6BAAA,iBAC+G;UACpHjV,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAoB,UAAA,KAAA8T,iCAAA,kBAAuG;UASvGlV,EAAA,CAAA0B,SAAA,yBAWiB;UACrB1B,EAAA,CAAAG,YAAA,EAAM;;;UAnPEH,EAAA,CAAAuB,SAAA,EAAY;UAAZvB,EAAA,CAAA2D,UAAA,CAAAsQ,GAAA,CAAAzN,EAAA,CAAY;UAKZxG,EAJA,CAAAwB,UAAA,eAAAyS,GAAA,CAAAlH,IAAA,CAAmB,kBAAAkH,GAAA,CAAAvG,mBAAA,CAGkB,qBAAAuG,GAAA,CAAAzI,gBAAA,6BAC4B;UA8B7BxL,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAAwB,UAAA,gBAAe;UA2B/BxB,EAAA,CAAAuB,SAAA,GAAU;UAAVvB,EAAA,CAAAwB,UAAA,YAAAyS,GAAA,CAAA3F,OAAA,CAAU;UA4IbtO,EAAA,CAAAuB,SAAA,EAAmC;UAAAvB,EAAnC,CAAAwB,UAAA,oBAAAyS,GAAA,CAAA7G,gBAAA,CAAmC,+BAAY;UAGnCpN,EAAA,CAAAuB,SAAA,EAAyB;UAAzBvB,EAAA,CAAAwB,UAAA,qBAAAyS,GAAA,CAAA7G,gBAAA,CAAyB;UASxBpN,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,qBAAAxB,EAAA,CAAA+K,eAAA,KAAAoK,GAAA,EAA2B;UAGpDnV,EAAA,CAAAuB,SAAA,EAAmC;UAAAvB,EAAnC,CAAAwB,UAAA,oBAAAyS,GAAA,CAAA7G,gBAAA,CAAmC,+BAAY;UAGtCpN,EAAA,CAAAuB,SAAA,EAAoB;UAApBvB,EAAA,CAAAwB,UAAA,SAAAyS,GAAA,CAAAnJ,cAAA,CAAoB;UAEhC9K,EAAA,CAAAuB,SAAA,EAAqB;UAArBvB,EAAA,CAAAwB,UAAA,SAAAyS,GAAA,CAAArI,eAAA,CAAqB;UAkBvB5L,EAAA,CAAAuB,SAAA,EAA4F;UAA5FvB,EAAA,CAAAI,WAAA,YAAA6T,GAAA,CAAAjI,YAAA,UAAAiI,GAAA,CAAApI,cAAA,IAAAoI,GAAA,CAAArI,eAAA,oBAA4F;UAD5F5L,EAPA,CAAAwB,UAAA,WAAAyS,GAAA,CAAAhF,YAAA,CAAuB,aAAAgF,GAAA,CAAAxI,QAAA,CACF,oBAAAwI,GAAA,CAAA9H,eAAA,CAEc,aAAA8H,GAAA,CAAArI,eAAA,IAAAqI,GAAA,CAAApI,cAAA,CACW,iBAAAoI,GAAA,CAAArI,eAAA,IAAAqI,GAAA,CAAApI,cAAA,CACI,0BAAAoI,GAAA,CAAApI,cAAA,CACV,cAAAoI,GAAA,CAAAvI,SAAA,CACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}