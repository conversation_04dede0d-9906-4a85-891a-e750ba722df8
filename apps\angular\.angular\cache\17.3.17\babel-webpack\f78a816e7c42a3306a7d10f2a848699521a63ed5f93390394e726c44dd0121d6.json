{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  return 5;\n}\nexport default [\"ast\", [[\"a\", \"p\"], [\"AM\", \"PM\"], [\"de la mañana\", \"de la tarde\"]], [[\"a\", \"p\"], [\"AM\", \"PM\"], [\"mañana\", \"tarde\"]], [[\"D\", \"L\", \"M\", \"M\", \"X\", \"V\", \"S\"], [\"dom\", \"llu\", \"mar\", \"mié\", \"xue\", \"vie\", \"sáb\"], [\"domingu\", \"llunes\", \"martes\", \"miércoles\", \"xueves\", \"vienres\", \"sábadu\"], [\"do\", \"ll\", \"ma\", \"mi\", \"xu\", \"vi\", \"sá\"]], u, [[\"X\", \"F\", \"M\", \"A\", \"M\", \"X\", \"X\", \"A\", \"S\", \"O\", \"P\", \"A\"], [\"xin\", \"feb\", \"mar\", \"abr\", \"may\", \"xun\", \"xnt\", \"ago\", \"set\", \"och\", \"pay\", \"avi\"], [\"de xineru\", \"de febreru\", \"de marzu\", \"d’abril\", \"de mayu\", \"de xunu\", \"de xunetu\", \"d’agostu\", \"de setiembre\", \"d’ochobre\", \"de payares\", \"d’avientu\"]], [[\"X\", \"F\", \"M\", \"A\", \"M\", \"X\", \"X\", \"A\", \"S\", \"O\", \"P\", \"A\"], [\"Xin\", \"Feb\", \"Mar\", \"Abr\", \"May\", \"Xun\", \"Xnt\", \"Ago\", \"Set\", \"Och\", \"Pay\", \"Avi\"], [\"xineru\", \"febreru\", \"marzu\", \"abril\", \"mayu\", \"xunu\", \"xunetu\", \"agostu\", \"setiembre\", \"ochobre\", \"payares\", \"avientu\"]], [[\"e.C.\", \"d.C.\"], u, [\"enantes de Cristu\", \"después de Cristu\"]], 1, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM 'de' y\", \"EEEE, d MMMM 'de' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", \"{1}, {0}\", \"{1} 'a' 'les' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"ND\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"DKK\": [],\n  \"HRK\": [],\n  \"ISK\": [],\n  \"NOK\": [],\n  \"PHP\": [u, \"₱\"],\n  \"PLN\": [],\n  \"SEK\": [],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"XXX\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ast.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    return 5;\n}\nexport default [\"ast\", [[\"a\", \"p\"], [\"AM\", \"PM\"], [\"de la mañana\", \"de la tarde\"]], [[\"a\", \"p\"], [\"AM\", \"PM\"], [\"mañana\", \"tarde\"]], [[\"D\", \"L\", \"M\", \"M\", \"X\", \"V\", \"S\"], [\"dom\", \"llu\", \"mar\", \"mié\", \"xue\", \"vie\", \"sáb\"], [\"domingu\", \"llunes\", \"martes\", \"miércoles\", \"xueves\", \"vienres\", \"sábadu\"], [\"do\", \"ll\", \"ma\", \"mi\", \"xu\", \"vi\", \"sá\"]], u, [[\"X\", \"F\", \"M\", \"A\", \"M\", \"X\", \"X\", \"A\", \"S\", \"O\", \"P\", \"A\"], [\"xin\", \"feb\", \"mar\", \"abr\", \"may\", \"xun\", \"xnt\", \"ago\", \"set\", \"och\", \"pay\", \"avi\"], [\"de xineru\", \"de febreru\", \"de marzu\", \"d’abril\", \"de mayu\", \"de xunu\", \"de xunetu\", \"d’agostu\", \"de setiembre\", \"d’ochobre\", \"de payares\", \"d’avientu\"]], [[\"X\", \"F\", \"M\", \"A\", \"M\", \"X\", \"X\", \"A\", \"S\", \"O\", \"P\", \"A\"], [\"Xin\", \"Feb\", \"Mar\", \"Abr\", \"May\", \"Xun\", \"Xnt\", \"Ago\", \"Set\", \"Och\", \"Pay\", \"Avi\"], [\"xineru\", \"febreru\", \"marzu\", \"abril\", \"mayu\", \"xunu\", \"xunetu\", \"agostu\", \"setiembre\", \"ochobre\", \"payares\", \"avientu\"]], [[\"e.C.\", \"d.C.\"], u, [\"enantes de Cristu\", \"después de Cristu\"]], 1, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM 'de' y\", \"EEEE, d MMMM 'de' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", \"{1}, {0}\", \"{1} 'a' 'les' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"ND\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", { \"DKK\": [], \"HRK\": [], \"ISK\": [], \"NOK\": [], \"PHP\": [u, \"₱\"], \"PLN\": [], \"SEK\": [], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"XXX\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;EAChG,IAAIP,CAAC,KAAK,CAAC,IAAII,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAET,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,mBAAmB,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}