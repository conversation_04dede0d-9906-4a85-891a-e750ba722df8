{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport { DashboardActivityListType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\nimport { RelevantActOverviewTableColumns } from './relevant-activities-overview-columns';\nimport { BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service\";\nimport * as i2 from \"../../services/ca-dashboard-service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/divider\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"../../../../shared/components/bdo-table/bdo-table.component\";\n/** Rendering \"Relevant Activities Overview\" widget in\n * \"Statistics\" Tab in CA Dashboard page.\n *  */\nexport class RelevantActivitiesOverviewComponent extends AppComponentBase {\n  constructor(injector, CADashboardController, CADashBoardService, router) {\n    super(injector);\n    this.CADashboardController = CADashboardController;\n    this.CADashBoardService = CADashBoardService;\n    this.router = router;\n    this.currentPageIndex = 0;\n    this.totalRecords = 10;\n    this.pageSizeOptions = [10, 20, 50, 100];\n    this.PageSize = 10;\n    this.caSearchResultColumns = RelevantActOverviewTableColumns;\n    this.TableId = 'relevantActivitiesOverview';\n    this.tabledata = [];\n    this.sort = 'relevantActivityName asc';\n    this.relevantActivities = ['Banking business', 'Insurance business', 'Fund management business', 'Finance and leasing business', 'Headquarters business', 'Shipping business', 'Holding business', 'Intellectual property business', 'Distribution and service centre business', 'None'];\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.dashboardData && this.dashboardData) {\n      this.selectedYear = this.dashboardData.fiscalYear;\n      this.sort = 'relevantActivityName asc';\n      this.GetData();\n    }\n  }\n  GetData() {\n    this.CADashboardController.getMainDashboardActivityOverviewByYearAndSorting(this.selectedYear, this.sort).subscribe(result => {\n      console.log(result);\n      if (result) {\n        this.tabledata = [];\n        result.forEach(element => {\n          const obj = {\n            numberOfCLP: element.numberOfCLP ?? 0,\n            numberOfD: element.numberOfD ?? 0,\n            numberOfELP: element.numberOfELP ?? 0,\n            numberOfF: element.numberOfF ?? 0,\n            numberOfFC: element.numberOfFC ?? 0,\n            numberOfFailures: element.numberOfFailures ?? 0,\n            numberOfFilings: element.numberOfFilings ?? 0,\n            numberOfIBC: element.numberOfIBC ?? 0,\n            numberOfLP: element.numberOfLP ?? 0,\n            numberOfIF: element.numberOfIF ?? 0,\n            relevantActivityName: element.relevantActivityName,\n            id: element.relevantActivity\n          };\n          this.tabledata.push(obj);\n        });\n        this.setTableData();\n      }\n    });\n  }\n  onLinkClick(event) {\n    let relevantActivityNumber = event.id;\n    let listingType = DashboardActivityListType.AnyActivity;\n    if (event.columnId == \"numberOfFilings\") {\n      listingType = DashboardActivityListType.PrimaryActivity;\n    }\n    this.router.navigate(['/search-result'], {\n      queryParams: {\n        source: \"dashboard\" /* DashboardConstants.DASHBOARD */,\n        type: \"Relevant Activity Overview\" /* DashboardConstants.RELEVANT_ACTIVITY_OVERVIEW */,\n        year: this.selectedYear,\n        relevantActivityType: relevantActivityNumber,\n        overviewByRelevantActListingType: listingType,\n        forExcluded: false\n      }\n    });\n  }\n  onLazyLoadEvent(event) {\n    var sortDir = event.isAscending ? \"asc\" /* SortDirection.ASCENDING */ : \"desc\" /* SortDirection.DESCENDING */;\n    this.sort = event.sortField + ' ' + sortDir;\n    this.GetData();\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableId;\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.tabledata.map(x => {\n      let cells = [];\n      cells = [{\n        columnId: \"relevantActivityName\" /* DashboardRelevantActOverviewTableColumns.RELEVANT_ACTIVITY */,\n        value: x.relevantActivityName\n      }, {\n        columnId: \"numberOfCLP\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_CLP */,\n        value: x.numberOfCLP\n      }, {\n        columnId: \"numberOfD\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_D */,\n        value: x.numberOfD\n      }, {\n        columnId: \"numberOfELP\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_ELP */,\n        value: x.numberOfELP\n      }, {\n        columnId: \"numberOfF\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_F */,\n        value: x.numberOfF\n      }, {\n        columnId: \"numberOfFC\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_FC */,\n        value: x.numberOfFC\n      }, {\n        columnId: \"numberOfFailures\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_FAILURES */,\n        value: x.numberOfFailures\n      }, {\n        columnId: \"numberOfFilings\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_FILINGS */,\n        value: x.numberOfFilings\n      }, {\n        columnId: \"numberOfIBC\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_IBC */,\n        value: x.numberOfIBC\n      }, {\n        columnId: \"numberOfLP\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_LP */,\n        value: x.numberOfLP\n      }, {\n        columnId: \"numberOfIF\" /* DashboardRelevantActOverviewTableColumns.NUM_OF_IF */,\n        value: x.numberOfIF\n      }];\n      return {\n        id: x.id,\n        rawData: x,\n        cells: cells\n      };\n    });\n    this.tableService.setGridData(tableData);\n  }\n  static {\n    this.ɵfac = function RelevantActivitiesOverviewComponent_Factory(t) {\n      return new (t || RelevantActivitiesOverviewComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.CADashboardContorllerService), i0.ɵɵdirectiveInject(i2.CADashboardService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RelevantActivitiesOverviewComponent,\n      selectors: [[\"app-relevant-activities-overview\"]],\n      inputs: {\n        dashboardData: \"dashboardData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 12,\n      vars: 10,\n      consts: [[1, \"dashboard-card-title\"], [1, \"divider-margin\"], [\"scrollHeight\", \"75vh\", \"defaultSortColumnId\", \"entityName\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSizeOptions\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [1, \"otas-label-margin\"]],\n      template: function RelevantActivitiesOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n          i0.ɵɵtext(3, \"RELEVANT ACTIVITIES OVERVIEW \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-content\");\n          i0.ɵɵelement(5, \"mat-divider\", 1);\n          i0.ɵɵelementStart(6, \"div\")(7, \"bdo-table\", 2);\n          i0.ɵɵlistener(\"onLazyLoad\", function RelevantActivitiesOverviewComponent_Template_bdo_table_onLazyLoad_7_listener($event) {\n            return ctx.onLazyLoadEvent($event);\n          })(\"onLinkClick\", function RelevantActivitiesOverviewComponent_Template_bdo_table_onLinkClick_7_listener($event) {\n            return ctx.onLinkClick($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"h5\", 3);\n          i0.ɵɵtext(9, \"Excludes declaration data filed in OTAS\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h5\", 3);\n          i0.ɵɵtext(11, \"Clicking on a specific number in column # Fillings, will open a linked page displaying declarations where the Primary Activity is \\u201CSelected Relevant Activity.\\\" Clicking on Relevant Activity Column such as \\\"Banking Business\\\" will reveal all declarations with \\\"Banking Business\\\" selected as one of their Relevant activities.\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.caSearchResultColumns)(\"defaultSortOrder\", \"asc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSizeOptions\", ctx.pageSizeOptions)(\"pageSize\", ctx.PageSize)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", false)(\"lazyLoad\", true);\n        }\n      },\n      dependencies: [i4.MatDivider, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, i6.BdoTableComponent],\n      styles: [\"\\n.otas-label-margin{\\n    margin-top: 1em;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlbGV2YW50LWFjdGl2aXRpZXMtb3ZlcnZpZXcuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0E7SUFDSSxlQUFlO0FBQ25CIiwiZmlsZSI6InJlbGV2YW50LWFjdGl2aXRpZXMtb3ZlcnZpZXcuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG4ub3Rhcy1sYWJlbC1tYXJnaW57XHJcbiAgICBtYXJnaW4tdG9wOiAxZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvc3RhdGlzdGljcy1jaGFydHMvcmVsZXZhbnQtYWN0aXZpdGllcy1vdmVydmlldy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQTtJQUNJLGVBQWU7QUFDbkI7QUFDQSx3WEFBd1giLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLm90YXMtbGFiZWwtbWFyZ2lue1xyXG4gICAgbWFyZ2luLXRvcDogMWVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "DashboardActivityListType", "RelevantActOverviewTableColumns", "BdoTableData", "RelevantActivitiesOverviewComponent", "constructor", "injector", "CADashboardController", "CADashBoardService", "router", "currentPageIndex", "totalRecords", "pageSizeOptions", "PageSize", "caSearchResultColumns", "TableId", "tabledata", "sort", "relevantActivities", "ngOnInit", "ngOnChanges", "changes", "dashboardData", "selected<PERSON>ear", "fiscalYear", "GetData", "getMainDashboardActivityOverviewByYearAndSorting", "subscribe", "result", "console", "log", "for<PERSON>ach", "element", "obj", "numberOfCLP", "numberOfD", "numberOfELP", "numberOfF", "numberOfFC", "numberOfFailures", "numberOfFilings", "numberOfIBC", "numberOfLP", "numberOfIF", "relevantActivityName", "id", "relevantActivity", "push", "setTableData", "onLinkClick", "event", "relevantActivityNumber", "listingType", "AnyActivity", "columnId", "PrimaryActivity", "navigate", "queryParams", "source", "type", "year", "relevantActivityType", "overviewByRelevantActListingType", "forExcluded", "onLazyLoadEvent", "sortDir", "isAscending", "sortField", "tableData", "resetToFirstPage", "tableId", "data", "map", "x", "cells", "value", "rawData", "tableService", "setGridData", "i0", "ɵɵdirectiveInject", "Injector", "i1", "CADashboardContorllerService", "i2", "CADashboardService", "i3", "Router", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "RelevantActivitiesOverviewComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "RelevantActivitiesOverviewComponent_Template_bdo_table_onLazyLoad_7_listener", "$event", "RelevantActivitiesOverviewComponent_Template_bdo_table_onLinkClick_7_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\statistics-charts\\relevant-activities-overview.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\statistics-charts\\relevant-activities-overview.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  Input,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport Chart, { ChartItem, ChartType, Colors } from 'chart.js/auto';\r\nimport{CADashboardContorllerService} from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service'\r\nimport { CADashboardService } from '../../services/ca-dashboard-service';\r\nimport { DashboardActivityListType, GetDashboardListingByActivityDto, GetDashboardListingByCountryDto, GetDashboardListingDto, StatisticMainDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { DashboardListingType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { Router } from '@angular/router';\r\nimport { DashboardRelevantActOverviewTableColumns, SortDirection } from '@app/shared/constants';\r\nimport { RelevantActOverviewTableColumns } from './relevant-activities-overview-columns';\r\nimport { BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\r\nimport { DashboardConstants } from '@app/shared/constants';\r\n/** Rendering \"Relevant Activities Overview\" widget in\r\n * \"Statistics\" Tab in CA Dashboard page.\r\n *  */\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-relevant-activities-overview',\r\n  templateUrl: './relevant-activities-overview.component.html',\r\n  styleUrls: ['./relevant-activities-overview.component.css'],\r\n})\r\nexport class RelevantActivitiesOverviewComponent extends AppComponentBase implements OnInit {\r\n\r\n  constructor(injector: Injector, private CADashboardController: CADashboardContorllerService, private CADashBoardService: CADashboardService, private router: Router) {\r\n    super(injector);\r\n  }\r\n\r\n  @Input() dashboardData: StatisticMainDto;\r\n  currentPageIndex = 0;\r\n  totalRecords = 10;\r\n  pageSizeOptions = [10, 20, 50, 100];\r\n  PageSize = 10;\r\n  caSearchResultColumns = RelevantActOverviewTableColumns;\r\n  TableId = 'relevantActivitiesOverview';\r\n  selectedYear:number;\r\n  tabledata = [];\r\n  sort = 'relevantActivityName asc'\r\n  relevantActivities = ['Banking business','Insurance business','Fund management business','Finance and leasing business',\r\n  'Headquarters business','Shipping business','Holding business','Intellectual property business',\r\n  'Distribution and service centre business','None'];\r\n  ngOnInit() {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.dashboardData && this.dashboardData) {\r\n      this.selectedYear = this.dashboardData.fiscalYear;\r\n      this.sort = 'relevantActivityName asc';\r\n      this.GetData();\r\n    }\r\n  }\r\n\r\n  GetData(){\r\n    this.CADashboardController.getMainDashboardActivityOverviewByYearAndSorting(this.selectedYear, this.sort ).subscribe(result =>{\r\n      console.log(result);\r\n      if(result){\r\n        this.tabledata = [];\r\n        result.forEach(element=>{\r\n            const obj= {\r\n              numberOfCLP: element.numberOfCLP ?? 0,\r\n              numberOfD: element.numberOfD ?? 0,\r\n              numberOfELP: element.numberOfELP ?? 0,\r\n              numberOfF: element.numberOfF ?? 0,\r\n              numberOfFC: element.numberOfFC ?? 0,\r\n              numberOfFailures: element.numberOfFailures ?? 0,\r\n              numberOfFilings: element.numberOfFilings ?? 0,\r\n              numberOfIBC: element.numberOfIBC ?? 0,\r\n              numberOfLP: element.numberOfLP ?? 0,\r\n              numberOfIF: element.numberOfIF ?? 0,\r\n              relevantActivityName: element.relevantActivityName,\r\n              id: element.relevantActivity\r\n            };\r\n\r\n            this.tabledata.push(obj);\r\n\r\n        })\r\n        this.setTableData();\r\n      }\r\n    })\r\n  }\r\n\r\n  onLinkClick(event):void{\r\n    let relevantActivityNumber = event.id;\r\n    let listingType = DashboardActivityListType.AnyActivity;\r\n    if(event.columnId == \"numberOfFilings\"){\r\n      listingType = DashboardActivityListType.PrimaryActivity;\r\n    }\r\n    this.router.navigate(['/search-result'], { queryParams: {source: DashboardConstants.DASHBOARD ,type: DashboardConstants.RELEVANT_ACTIVITY_OVERVIEW ,\r\n      year: this.selectedYear, relevantActivityType: relevantActivityNumber, overviewByRelevantActListingType: listingType, forExcluded: false}});\r\n  }\r\n\r\n  onLazyLoadEvent(event):void{\r\n    var sortDir = event.isAscending ? SortDirection.ASCENDING : SortDirection.DESCENDING;\r\n    this.sort = event.sortField + ' ' + sortDir;\r\n    this.GetData();\r\n  }\r\n\r\n  private setTableData(): void {\r\n\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableId;\r\n    tableData.totalRecords = this.totalRecords;\r\n\r\n    tableData.data = this.tabledata.map(x => {\r\n      let cells = [];\r\n        cells = [\r\n          { columnId: DashboardRelevantActOverviewTableColumns.RELEVANT_ACTIVITY, value: x.relevantActivityName },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_CLP, value: x.numberOfCLP },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_D, value: x.numberOfD },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_ELP, value: x.numberOfELP },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_F, value: x.numberOfF },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_FC, value: x.numberOfFC },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_FAILURES, value: x.numberOfFailures },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_FILINGS, value: x.numberOfFilings },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_IBC, value: x.numberOfIBC },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_LP, value: x.numberOfLP },\r\n          { columnId: DashboardRelevantActOverviewTableColumns.NUM_OF_IF, value: x.numberOfIF }\r\n        ];\r\n\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: cells\r\n      };\r\n    });\r\n    this.tableService.setGridData(tableData);\r\n  }\r\n\r\n}\r\n", "<mat-card>\r\n  <mat-card-header>\r\n    <mat-card-title class=\"dashboard-card-title\"\r\n      >RELEVANT ACTIVITIES OVERVIEW\r\n    </mat-card-title>\r\n  </mat-card-header>\r\n  <mat-card-content>\r\n    <mat-divider class=\"divider-margin\"></mat-divider>\r\n    <div>\r\n      <bdo-table [id]=\"TableId\" [columns]=\"caSearchResultColumns\" scrollHeight=\"75vh\" defaultSortColumnId=\"entityName\" [defaultSortOrder]=\"'asc'\" [pageIndex]=\"currentPageIndex\"\r\n      [pageSizeOptions]=\"pageSizeOptions\" [pageSize]=\"PageSize\" [isVirtualScroll]=\"false\" [hidePagination]=\"true\" [rowSelectable]=\"false\" [lazyLoad]=\"true\"\r\n      (onLazyLoad)=\"onLazyLoadEvent($event)\" (onLinkClick) = \"onLinkClick($event)\">\r\n      </bdo-table>\r\n      <h5 class=\"otas-label-margin\">Excludes declaration data filed in OTAS</h5>\r\n      <h5 class=\"otas-label-margin\">Clicking on a specific number in column # Fillings, will open a linked page displaying declarations where the Primary Activity is “Selected Relevant Activity.\"\r\n        Clicking on Relevant Activity Column such as \"Banking Business\" will reveal all declarations with \"Banking Business\" selected as one of their Relevant activities.</h5>\r\n    </div>\r\n  </mat-card-content>\r\n</mat-card>\r\n"], "mappings": "AAQA,SAASA,gBAAgB,QAAQ,yBAAyB;AAI1D,SAASC,yBAAyB,QAAqH,4FAA4F;AAInP,SAASC,+BAA+B,QAAQ,wCAAwC;AACxF,SAASC,YAAY,QAAQ,kDAAkD;;;;;;;;AAE/E;;;AASA,OAAM,MAAOC,mCAAoC,SAAQJ,gBAAgB;EAEvEK,YAAYC,QAAkB,EAAUC,qBAAmD,EAAUC,kBAAsC,EAAUC,MAAc;IACjK,KAAK,CAACH,QAAQ,CAAC;IADuB,KAAAC,qBAAqB,GAArBA,qBAAqB;IAAwC,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAK3J,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACnC,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,qBAAqB,GAAGZ,+BAA+B;IACvD,KAAAa,OAAO,GAAG,4BAA4B;IAEtC,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,IAAI,GAAG,0BAA0B;IACjC,KAAAC,kBAAkB,GAAG,CAAC,kBAAkB,EAAC,oBAAoB,EAAC,0BAA0B,EAAC,8BAA8B,EACvH,uBAAuB,EAAC,mBAAmB,EAAC,kBAAkB,EAAC,gCAAgC,EAC/F,0CAA0C,EAAC,MAAM,CAAC;EAdlD;EAeAC,QAAQA,CAAA,GAAI;EAEZC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,EAAE;MAC/C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU;MACjD,IAAI,CAACP,IAAI,GAAG,0BAA0B;MACtC,IAAI,CAACQ,OAAO,EAAE;IAChB;EACF;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAClB,qBAAqB,CAACmB,gDAAgD,CAAC,IAAI,CAACH,YAAY,EAAE,IAAI,CAACN,IAAI,CAAE,CAACU,SAAS,CAACC,MAAM,IAAG;MAC5HC,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;MACnB,IAAGA,MAAM,EAAC;QACR,IAAI,CAACZ,SAAS,GAAG,EAAE;QACnBY,MAAM,CAACG,OAAO,CAACC,OAAO,IAAE;UACpB,MAAMC,GAAG,GAAE;YACTC,WAAW,EAAEF,OAAO,CAACE,WAAW,IAAI,CAAC;YACrCC,SAAS,EAAEH,OAAO,CAACG,SAAS,IAAI,CAAC;YACjCC,WAAW,EAAEJ,OAAO,CAACI,WAAW,IAAI,CAAC;YACrCC,SAAS,EAAEL,OAAO,CAACK,SAAS,IAAI,CAAC;YACjCC,UAAU,EAAEN,OAAO,CAACM,UAAU,IAAI,CAAC;YACnCC,gBAAgB,EAAEP,OAAO,CAACO,gBAAgB,IAAI,CAAC;YAC/CC,eAAe,EAAER,OAAO,CAACQ,eAAe,IAAI,CAAC;YAC7CC,WAAW,EAAET,OAAO,CAACS,WAAW,IAAI,CAAC;YACrCC,UAAU,EAAEV,OAAO,CAACU,UAAU,IAAI,CAAC;YACnCC,UAAU,EAAEX,OAAO,CAACW,UAAU,IAAI,CAAC;YACnCC,oBAAoB,EAAEZ,OAAO,CAACY,oBAAoB;YAClDC,EAAE,EAAEb,OAAO,CAACc;WACb;UAED,IAAI,CAAC9B,SAAS,CAAC+B,IAAI,CAACd,GAAG,CAAC;QAE5B,CAAC,CAAC;QACF,IAAI,CAACe,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,KAAK;IACf,IAAIC,sBAAsB,GAAGD,KAAK,CAACL,EAAE;IACrC,IAAIO,WAAW,GAAGnD,yBAAyB,CAACoD,WAAW;IACvD,IAAGH,KAAK,CAACI,QAAQ,IAAI,iBAAiB,EAAC;MACrCF,WAAW,GAAGnD,yBAAyB,CAACsD,eAAe;IACzD;IACA,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;MAAEC,WAAW,EAAE;QAACC,MAAM;QAAgCC,IAAI;QACjGC,IAAI,EAAE,IAAI,CAACrC,YAAY;QAAEsC,oBAAoB,EAAEV,sBAAsB;QAAEW,gCAAgC,EAAEV,WAAW;QAAEW,WAAW,EAAE;MAAK;IAAC,CAAC,CAAC;EAC/I;EAEAC,eAAeA,CAACd,KAAK;IACnB,IAAIe,OAAO,GAAGf,KAAK,CAACgB,WAAW,GAAE,sCAA0B;IAC3D,IAAI,CAACjD,IAAI,GAAGiC,KAAK,CAACiB,SAAS,GAAG,GAAG,GAAGF,OAAO;IAC3C,IAAI,CAACxC,OAAO,EAAE;EAChB;EAEQuB,YAAYA,CAAA;IAElB,MAAMoB,SAAS,GAAG,IAAIjE,YAAY,EAAE;IACpCiE,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAACvD,OAAO;IAChCqD,SAAS,CAACzD,YAAY,GAAG,IAAI,CAACA,YAAY;IAE1CyD,SAAS,CAACG,IAAI,GAAG,IAAI,CAACvD,SAAS,CAACwD,GAAG,CAACC,CAAC,IAAG;MACtC,IAAIC,KAAK,GAAG,EAAE;MACZA,KAAK,GAAG,CACN;QAAEpB,QAAQ;QAA8DqB,KAAK,EAAEF,CAAC,CAAC7B;MAAoB,CAAE,EACvG;QAAEU,QAAQ;QAAuDqB,KAAK,EAAEF,CAAC,CAACvC;MAAW,CAAE,EACvF;QAAEoB,QAAQ;QAAqDqB,KAAK,EAAEF,CAAC,CAACtC;MAAS,CAAE,EACnF;QAAEmB,QAAQ;QAAuDqB,KAAK,EAAEF,CAAC,CAACrC;MAAW,CAAE,EACvF;QAAEkB,QAAQ;QAAqDqB,KAAK,EAAEF,CAAC,CAACpC;MAAS,CAAE,EACnF;QAAEiB,QAAQ;QAAsDqB,KAAK,EAAEF,CAAC,CAACnC;MAAU,CAAE,EACrF;QAAEgB,QAAQ;QAA4DqB,KAAK,EAAEF,CAAC,CAAClC;MAAgB,CAAE,EACjG;QAAEe,QAAQ;QAA2DqB,KAAK,EAAEF,CAAC,CAACjC;MAAe,CAAE,EAC/F;QAAEc,QAAQ;QAAuDqB,KAAK,EAAEF,CAAC,CAAChC;MAAW,CAAE,EACvF;QAAEa,QAAQ;QAAsDqB,KAAK,EAAEF,CAAC,CAAC/B;MAAU,CAAE,EACrF;QAAEY,QAAQ;QAAsDqB,KAAK,EAAEF,CAAC,CAAC9B;MAAU,CAAE,CACtF;MAEH,OAAO;QACLE,EAAE,EAAE4B,CAAC,CAAC5B,EAAE;QACR+B,OAAO,EAAEH,CAAC;QACVC,KAAK,EAAEA;OACR;IACH,CAAC,CAAC;IACF,IAAI,CAACG,YAAY,CAACC,WAAW,CAACV,SAAS,CAAC;EAC1C;;;uBAxGWhE,mCAAmC,EAAA2E,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,4BAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,kBAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAnCnF,mCAAmC;MAAAoF,SAAA;MAAAC,MAAA;QAAAnE,aAAA;MAAA;MAAAoE,QAAA,GAAAX,EAAA,CAAAY,0BAAA,EAAAZ,EAAA,CAAAa,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1B5CnB,EAFJ,CAAAqB,cAAA,eAAU,sBACS,wBAEZ;UAAArB,EAAA,CAAAsB,MAAA,oCACH;UACFtB,EADE,CAAAuB,YAAA,EAAiB,EACD;UAClBvB,EAAA,CAAAqB,cAAA,uBAAkB;UAChBrB,EAAA,CAAAwB,SAAA,qBAAkD;UAEhDxB,EADF,CAAAqB,cAAA,UAAK,mBAG0E;UAAtCrB,EAAvC,CAAAyB,UAAA,wBAAAC,6EAAAC,MAAA;YAAA,OAAcP,GAAA,CAAAnC,eAAA,CAAA0C,MAAA,CAAuB;UAAA,EAAC,yBAAAC,8EAAAD,MAAA;YAAA,OAAkBP,GAAA,CAAAlD,WAAA,CAAAyD,MAAA,CAAmB;UAAA,EAAC;UAC5E3B,EAAA,CAAAuB,YAAA,EAAY;UACZvB,EAAA,CAAAqB,cAAA,YAA8B;UAAArB,EAAA,CAAAsB,MAAA,8CAAuC;UAAAtB,EAAA,CAAAuB,YAAA,EAAK;UAC1EvB,EAAA,CAAAqB,cAAA,aAA8B;UAAArB,EAAA,CAAAsB,MAAA,oVACsI;UAG1KtB,EAH0K,CAAAuB,YAAA,EAAK,EACrK,EACW,EACV;;;UATMvB,EAAA,CAAA6B,SAAA,GAAc;UAC2G7B,EADzH,CAAA8B,UAAA,OAAAV,GAAA,CAAApF,OAAA,CAAc,YAAAoF,GAAA,CAAArF,qBAAA,CAAkC,2BAAgF,cAAAqF,GAAA,CAAAzF,gBAAA,CAA+B,oBAAAyF,GAAA,CAAAvF,eAAA,CACvI,aAAAuF,GAAA,CAAAtF,QAAA,CAAsB,0BAA0B,wBAAwB,wBAAwB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}