{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"bm\", [[\"AM\", \"PM\"], u, u], u, [[\"K\", \"N\", \"T\", \"A\", \"A\", \"J\", \"S\"], [\"kar\", \"ntɛ\", \"tar\", \"ara\", \"ala\", \"jum\", \"sib\"], [\"kari\", \"ntɛnɛ\", \"tarata\", \"araba\", \"alamisa\", \"juma\", \"sibiri\"], [\"kar\", \"ntɛ\", \"tar\", \"ara\", \"ala\", \"jum\", \"sib\"]], u, [[\"Z\", \"F\", \"M\", \"A\", \"M\", \"Z\", \"Z\", \"U\", \"<PERSON>\", \"Ɔ\", \"N\", \"D\"], [\"zan\", \"feb\", \"mar\", \"awi\", \"mɛ\", \"zuw\", \"zul\", \"uti\", \"sɛt\", \"ɔku\", \"now\", \"des\"], [\"zanwuye\", \"feburuye\", \"marisi\", \"awirili\", \"mɛ\", \"zuwɛn\", \"zuluye\", \"uti\", \"sɛtanburu\", \"ɔkutɔburu\", \"nowanburu\", \"desanburu\"]], u, [[\"J.-C. ɲɛ\", \"ni J.-C.\"], u, [\"jezu krisiti ɲɛ\", \"jezu krisiti minkɛ\"]], 1, [6, 0], [\"d/M/y\", \"d MMM, y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"XOF\", \"F CFA\", \"sefa Fraŋ (BCEAO)\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}