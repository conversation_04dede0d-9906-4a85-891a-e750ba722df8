{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"chr\", [[\"Ꮜ\", \"Ꮢ\"], [\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢ\"], [\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢᏗᏢ\"]], [[\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢ\"], u, [\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢᏗᏢ\"]], [[\"Ꮖ\", \"Ꮙ\", \"Ꮤ\", \"Ꮶ\", \"Ꮕ\", \"Ꮷ\", \"Ꭴ\"], [\"ᏆᏍᎬ\", \"ᏉᏅᎯ\", \"ᏔᎵᏁ\", \"ᏦᎢᏁ\", \"ᏅᎩᏁ\", \"ᏧᎾᎩ\", \"ᏈᏕᎾ\"], [\"ᎤᎾᏙᏓᏆᏍᎬ\", \"ᎤᎾᏙᏓᏉᏅᎯ\", \"ᏔᎵᏁᎢᎦ\", \"ᏦᎢᏁᎢᎦ\", \"ᏅᎩᏁᎢᎦ\", \"ᏧᎾᎩᎶᏍᏗ\", \"ᎤᎾᏙᏓᏈᏕᎾ\"], [\"ᏍᎬ\", \"ᏅᎯ\", \"ᏔᎵ\", \"ᏦᎢ\", \"ᏅᎩ\", \"ᏧᎾ\", \"ᏕᎾ\"]], u, [[\"Ꭴ\", \"Ꭷ\", \"Ꭰ\", \"Ꭷ\", \"Ꭰ\", \"Ꮥ\", \"Ꭻ\", \"Ꭶ\", \"Ꮪ\", \"Ꮪ\", \"Ꮕ\", \"Ꭵ\"], [\"ᎤᏃ\", \"ᎧᎦ\", \"ᎠᏅ\", \"ᎧᏬ\", \"ᎠᏂ\", \"ᏕᎭ\", \"ᎫᏰ\", \"ᎦᎶ\", \"ᏚᎵ\", \"ᏚᏂ\", \"ᏅᏓ\", \"ᎥᏍ\"], [\"ᎤᏃᎸᏔᏅ\", \"ᎧᎦᎵ\", \"ᎠᏅᏱ\", \"ᎧᏬᏂ\", \"ᎠᏂᏍᎬᏘ\", \"ᏕᎭᎷᏱ\", \"ᎫᏰᏉᏂ\", \"ᎦᎶᏂ\", \"ᏚᎵᏍᏗ\", \"ᏚᏂᏅᏗ\", \"ᏅᏓᏕᏆ\", \"ᎥᏍᎩᏱ\"]], u, [[\"BC\", \"AD\"], u, [\"ᏧᏓᎷᎸ ᎤᎷᎯᏍᏗ ᎦᎶᏁᏛ\", \"ᎠᏃ ᏙᎻᏂ\"]], 0, [6, 0], [\"M/d/yy\", \"MMM d, y\", \"MMMM d, y\", \"EEEE, MMMM d, y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} ᎤᎾᎢ {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"USD\", \"$\", \"US ᎠᏕᎳ\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/chr.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"chr\", [[\"Ꮜ\", \"Ꮢ\"], [\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢ\"], [\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢᏗᏢ\"]], [[\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢ\"], u, [\"ᏌᎾᎴ\", \"ᏒᎯᏱᎢᏗᏢ\"]], [[\"Ꮖ\", \"Ꮙ\", \"Ꮤ\", \"Ꮶ\", \"Ꮕ\", \"Ꮷ\", \"Ꭴ\"], [\"ᏆᏍᎬ\", \"ᏉᏅᎯ\", \"ᏔᎵᏁ\", \"ᏦᎢᏁ\", \"ᏅᎩᏁ\", \"ᏧᎾᎩ\", \"ᏈᏕᎾ\"], [\"ᎤᎾᏙᏓᏆᏍᎬ\", \"ᎤᎾᏙᏓᏉᏅᎯ\", \"ᏔᎵᏁᎢᎦ\", \"ᏦᎢᏁᎢᎦ\", \"ᏅᎩᏁᎢᎦ\", \"ᏧᎾᎩᎶᏍᏗ\", \"ᎤᎾᏙᏓᏈᏕᎾ\"], [\"ᏍᎬ\", \"ᏅᎯ\", \"ᏔᎵ\", \"ᏦᎢ\", \"ᏅᎩ\", \"ᏧᎾ\", \"ᏕᎾ\"]], u, [[\"Ꭴ\", \"Ꭷ\", \"Ꭰ\", \"Ꭷ\", \"Ꭰ\", \"Ꮥ\", \"Ꭻ\", \"Ꭶ\", \"Ꮪ\", \"Ꮪ\", \"Ꮕ\", \"Ꭵ\"], [\"ᎤᏃ\", \"ᎧᎦ\", \"ᎠᏅ\", \"ᎧᏬ\", \"ᎠᏂ\", \"ᏕᎭ\", \"ᎫᏰ\", \"ᎦᎶ\", \"ᏚᎵ\", \"ᏚᏂ\", \"ᏅᏓ\", \"ᎥᏍ\"], [\"ᎤᏃᎸᏔᏅ\", \"ᎧᎦᎵ\", \"ᎠᏅᏱ\", \"ᎧᏬᏂ\", \"ᎠᏂᏍᎬᏘ\", \"ᏕᎭᎷᏱ\", \"ᎫᏰᏉᏂ\", \"ᎦᎶᏂ\", \"ᏚᎵᏍᏗ\", \"ᏚᏂᏅᏗ\", \"ᏅᏓᏕᏆ\", \"ᎥᏍᎩᏱ\"]], u, [[\"BC\", \"AD\"], u, [\"ᏧᏓᎷᎸ ᎤᎷᎯᏍᏗ ᎦᎶᏁᏛ\", \"ᎠᏃ ᏙᎻᏂ\"]], 0, [6, 0], [\"M/d/yy\", \"MMM d, y\", \"MMMM d, y\", \"EEEE, MMMM d, y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} ᎤᎾᎢ {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"USD\", \"$\", \"US ᎠᏕᎳ\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAEJ,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,aAAa,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}