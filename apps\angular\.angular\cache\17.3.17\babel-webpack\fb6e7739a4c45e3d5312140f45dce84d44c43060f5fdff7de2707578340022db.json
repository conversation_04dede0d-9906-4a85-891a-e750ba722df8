{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['б.з.д.', 'б.з.'],\n  abbreviated: ['б.з.д.', 'б.з.'],\n  wide: ['біздің заманымызға дейін', 'біздің заманымыз']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ші тоқ.', '2-ші тоқ.', '3-ші тоқ.', '4-ші тоқ.'],\n  wide: ['1-ші тоқсан', '2-ші тоқсан', '3-ші тоқсан', '4-ші тоқсан']\n};\nvar monthValues = {\n  narrow: ['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],\n  abbreviated: ['қаң', 'ақп', 'нау', 'сәу', 'мам', 'мау', 'шіл', 'там', 'қыр', 'қаз', 'қар', 'жел'],\n  wide: ['қаңтар', 'ақпан', 'наурыз', 'сәуір', 'мамыр', 'маусым', 'шілде', 'тамыз', 'қыркүйек', 'қазан', 'қараша', 'желтоқсан']\n};\nvar formattingMonthValues = {\n  narrow: ['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],\n  abbreviated: ['қаң', 'ақп', 'нау', 'сәу', 'мам', 'мау', 'шіл', 'там', 'қыр', 'қаз', 'қар', 'жел'],\n  wide: ['қаңтар', 'ақпан', 'наурыз', 'сәуір', 'мамыр', 'маусым', 'шілде', 'тамыз', 'қыркүйек', 'қазан', 'қараша', 'желтоқсан']\n};\nvar dayValues = {\n  narrow: ['Ж', 'Д', 'С', 'С', 'Б', 'Ж', 'С'],\n  short: ['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],\n  abbreviated: ['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],\n  wide: ['жексенбі', 'дүйсенбі', 'сейсенбі', 'сәрсенбі', 'бейсенбі', 'жұма', 'сенбі']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасы',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күндіз',\n    evening: 'кеш',\n    night: 'түн'\n  },\n  wide: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасы',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күндіз',\n    evening: 'кеш',\n    night: 'түн'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасында',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күн',\n    evening: 'кеш',\n    night: 'түн'\n  },\n  wide: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасында',\n    noon: 'түсте',\n    morning: 'таңертең',\n    afternoon: 'күндіз',\n    evening: 'кеште',\n    night: 'түнде'\n  }\n};\nvar suffixes = {\n  0: '-ші',\n  1: '-ші',\n  2: '-ші',\n  3: '-ші',\n  4: '-ші',\n  5: '-ші',\n  6: '-шы',\n  7: '-ші',\n  8: '-ші',\n  9: '-шы',\n  10: '-шы',\n  20: '-шы',\n  30: '-шы',\n  40: '-шы',\n  50: '-ші',\n  60: '-шы',\n  70: '-ші',\n  80: '-ші',\n  90: '-шы',\n  100: '-ші'\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var mod10 = number % 10;\n  var b = number >= 100 ? 100 : null;\n  var suffix = suffixes[number] || suffixes[mod10] || b && suffixes[b] || '';\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "suffixes", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "mod10", "b", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/kk/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['б.з.д.', 'б.з.'],\n  abbreviated: ['б.з.д.', 'б.з.'],\n  wide: ['біздің заманымызға дейін', 'біздің заманымыз']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ші тоқ.', '2-ші тоқ.', '3-ші тоқ.', '4-ші тоқ.'],\n  wide: ['1-ші тоқсан', '2-ші тоқсан', '3-ші тоқсан', '4-ші тоқсан']\n};\nvar monthValues = {\n  narrow: ['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],\n  abbreviated: ['қаң', 'ақп', 'нау', 'сәу', 'мам', 'мау', 'шіл', 'там', 'қыр', 'қаз', 'қар', 'жел'],\n  wide: ['қаңтар', 'ақпан', 'наурыз', 'сәуір', 'мамыр', 'маусым', 'шілде', 'тамыз', 'қыркүйек', 'қазан', 'қараша', 'желтоқсан']\n};\nvar formattingMonthValues = {\n  narrow: ['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],\n  abbreviated: ['қаң', 'ақп', 'нау', 'сәу', 'мам', 'мау', 'шіл', 'там', 'қыр', 'қаз', 'қар', 'жел'],\n  wide: ['қаңтар', 'ақпан', 'наурыз', 'сәуір', 'мамыр', 'маусым', 'шілде', 'тамыз', 'қыркүйек', 'қазан', 'қараша', 'желтоқсан']\n};\nvar dayValues = {\n  narrow: ['Ж', 'Д', 'С', 'С', 'Б', 'Ж', 'С'],\n  short: ['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],\n  abbreviated: ['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],\n  wide: ['жексенбі', 'дүйсенбі', 'сейсенбі', 'сәрсенбі', 'бейсенбі', 'жұма', 'сенбі']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасы',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күндіз',\n    evening: 'кеш',\n    night: 'түн'\n  },\n  wide: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасы',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күндіз',\n    evening: 'кеш',\n    night: 'түн'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасында',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күн',\n    evening: 'кеш',\n    night: 'түн'\n  },\n  wide: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасында',\n    noon: 'түсте',\n    morning: 'таңертең',\n    afternoon: 'күндіз',\n    evening: 'кеште',\n    night: 'түнде'\n  }\n};\nvar suffixes = {\n  0: '-ші',\n  1: '-ші',\n  2: '-ші',\n  3: '-ші',\n  4: '-ші',\n  5: '-ші',\n  6: '-шы',\n  7: '-ші',\n  8: '-ші',\n  9: '-шы',\n  10: '-шы',\n  20: '-шы',\n  30: '-шы',\n  40: '-шы',\n  50: '-ші',\n  60: '-шы',\n  70: '-ші',\n  80: '-ші',\n  90: '-шы',\n  100: '-ші'\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var mod10 = number % 10;\n  var b = number >= 100 ? 100 : null;\n  var suffix = suffixes[number] || suffixes[mod10] || b && suffixes[b] || '';\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC1BC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC/BC,IAAI,EAAE,CAAC,0BAA0B,EAAE,kBAAkB;AACvD,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;EACjEC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW;AAC9H,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW;AAC9H,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvDC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO;AACpF,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,QAAQ,GAAG;EACb,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,KAAK,GAAGF,MAAM,GAAG,EAAE;EACvB,IAAIG,CAAC,GAAGH,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;EAClC,IAAII,MAAM,GAAGR,QAAQ,CAACI,MAAM,CAAC,IAAIJ,QAAQ,CAACM,KAAK,CAAC,IAAIC,CAAC,IAAIP,QAAQ,CAACO,CAAC,CAAC,IAAI,EAAE;EAC1E,OAAOH,MAAM,GAAGI,MAAM;AACxB,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbR,aAAa,EAAEA,aAAa;EAC5BS,GAAG,EAAE9B,eAAe,CAAC;IACnB+B,MAAM,EAAE9B,SAAS;IACjB+B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAEjC,eAAe,CAAC;IACvB+B,MAAM,EAAE1B,aAAa;IACrB2B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEnC,eAAe,CAAC;IACrB+B,MAAM,EAAEzB,WAAW;IACnB0B,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE7B,qBAAqB;IACvC8B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAEtC,eAAe,CAAC;IACnB+B,MAAM,EAAEvB,SAAS;IACjBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAEvC,eAAe,CAAC;IACzB+B,MAAM,EAAErB,eAAe;IACvBsB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEjB,yBAAyB;IAC3CkB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}