{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ug\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['ug', [['ب', 'ك'], ['چ.ب', 'چ.ك'], ['چۈشتىن بۇرۇن', 'چۈشتىن كېيىن']], [['چ.ب', 'چ.ك'], u, u], [['ي', 'د', 'س', 'چ', 'پ', 'ج', 'ش'], ['يە', 'دۈ', 'سە', 'چا', 'پە', 'جۈ', 'شە'], ['يەكشەنبە', 'دۈشەنبە', 'سەيشەنبە', 'چارشەنبە', 'پەيشەنبە', 'جۈمە', 'شەنبە'], ['ي', 'د', 'س', 'چ', 'پ', 'ج', 'ش']], u, [['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], ['يانۋار', 'فېۋرال', 'مارت', 'ئاپرېل', 'ماي', 'ئىيۇن', 'ئىيۇل', 'ئاۋغۇست', 'سېنتەبىر', 'ئۆكتەبىر', 'نويابىر', 'دېكابىر'], u], u, [['BCE', 'مىلادىيە'], u, ['مىلادىيەدىن بۇرۇن', 'مىلادىيە']], 0, [6, 0], ['y-MM-dd', 'd-MMM، y', 'd-MMMM، y', 'y d-MMMM، EEEE'], ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'], ['{1}، {0}', u, '{1} {0}', u], ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'], 'CNY', '￥', 'جۇڭگو يۈەنى', {\n    'CNY': ['￥', '¥'],\n    'JPY': ['JP¥', '¥']\n  }, 'rtl', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/ug.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/ug\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'ug',\n        [['ب', 'ك'], ['چ.ب', 'چ.ك'], ['چۈشتىن بۇرۇن', 'چۈشتىن كېيىن']],\n        [['چ.ب', 'چ.ك'], u, u],\n        [\n            ['ي', 'د', 'س', 'چ', 'پ', 'ج', 'ش'], ['يە', 'دۈ', 'سە', 'چا', 'پە', 'جۈ', 'شە'],\n            ['يەكشەنبە', 'دۈشەنبە', 'سەيشەنبە', 'چارشەنبە', 'پەيشەنبە', 'جۈمە', 'شەنبە'],\n            ['ي', 'د', 'س', 'چ', 'پ', 'ج', 'ش']\n        ],\n        u,\n        [\n            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n            [\n                'يانۋار', 'فېۋرال', 'مارت', 'ئاپرېل', 'ماي', 'ئىيۇن', 'ئىيۇل', 'ئاۋغۇست', 'سېنتەبىر',\n                'ئۆكتەبىر', 'نويابىر', 'دېكابىر'\n            ],\n            u\n        ],\n        u,\n        [['BCE', 'مىلادىيە'], u, ['مىلادىيەدىن بۇرۇن', 'مىلادىيە']],\n        0,\n        [6, 0],\n        ['y-MM-dd', 'd-MMM، y', 'd-MMMM، y', 'y d-MMMM، EEEE'],\n        ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'],\n        ['{1}، {0}', u, '{1} {0}', u],\n        ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'],\n        'CNY',\n        '￥',\n        'جۇڭگو يۈەنى',\n        { 'CNY': ['￥', '¥'], 'JPY': ['JP¥', '¥'] },\n        'rtl',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC,EAC9D,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EACtB,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAC/E,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAC5E,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CACtC,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAC/D,CACI,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EACpF,UAAU,EAAE,SAAS,EAAE,SAAS,CACnC,EACDA,CAAC,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,EAAEA,CAAC,EAAE,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC,EAC3D,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,CAAC,EACtD,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EACxD,CAAC,UAAU,EAAEA,CAAC,EAAE,SAAS,EAAEA,CAAC,CAAC,EAC7B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAC3C,KAAK,EACL,GAAG,EACH,aAAa,EACb;IAAE,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EAAE,CAAC,EAC1C,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}