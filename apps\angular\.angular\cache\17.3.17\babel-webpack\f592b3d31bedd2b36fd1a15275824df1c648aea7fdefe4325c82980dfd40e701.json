{"ast": null, "code": "/*!\n * Cropper.js v1.6.2\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2024-04-21T07:43:05.335Z\n */\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'cropper';\n\n// Actions\nvar ACTION_ALL = 'all';\nvar ACTION_CROP = 'crop';\nvar ACTION_MOVE = 'move';\nvar ACTION_ZOOM = 'zoom';\nvar ACTION_EAST = 'e';\nvar ACTION_WEST = 'w';\nvar ACTION_SOUTH = 's';\nvar ACTION_NORTH = 'n';\nvar ACTION_NORTH_EAST = 'ne';\nvar ACTION_NORTH_WEST = 'nw';\nvar ACTION_SOUTH_EAST = 'se';\nvar ACTION_SOUTH_WEST = 'sw';\n\n// Classes\nvar CLASS_CROP = \"\".concat(NAMESPACE, \"-crop\");\nvar CLASS_DISABLED = \"\".concat(NAMESPACE, \"-disabled\");\nvar CLASS_HIDDEN = \"\".concat(NAMESPACE, \"-hidden\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_MODAL = \"\".concat(NAMESPACE, \"-modal\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\");\n\n// Data keys\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\nvar DATA_PREVIEW = \"\".concat(NAMESPACE, \"Preview\");\n\n// Drag modes\nvar DRAG_MODE_CROP = 'crop';\nvar DRAG_MODE_MOVE = 'move';\nvar DRAG_MODE_NONE = 'none';\n\n// Events\nvar EVENT_CROP = 'crop';\nvar EVENT_CROP_END = 'cropend';\nvar EVENT_CROP_MOVE = 'cropmove';\nvar EVENT_CROP_START = 'cropstart';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_READY = 'ready';\nvar EVENT_RESIZE = 'resize';\nvar EVENT_WHEEL = 'wheel';\nvar EVENT_ZOOM = 'zoom';\n\n// Mime types\nvar MIME_TYPE_JPEG = 'image/jpeg';\n\n// RegExps\nvar REGEXP_ACTIONS = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;\nvar REGEXP_DATA_URL = /^data:/;\nvar REGEXP_DATA_URL_JPEG = /^data:image\\/jpeg;base64,/;\nvar REGEXP_TAG_NAME = /^img|canvas$/i;\n\n// Misc\n// Inspired by the default width and height of a canvas element.\nvar MIN_CONTAINER_WIDTH = 200;\nvar MIN_CONTAINER_HEIGHT = 100;\nvar DEFAULTS = {\n  // Define the view mode of the cropper\n  viewMode: 0,\n  // 0, 1, 2, 3\n\n  // Define the dragging mode of the cropper\n  dragMode: DRAG_MODE_CROP,\n  // 'crop', 'move' or 'none'\n\n  // Define the initial aspect ratio of the crop box\n  initialAspectRatio: NaN,\n  // Define the aspect ratio of the crop box\n  aspectRatio: NaN,\n  // An object with the previous cropping result data\n  data: null,\n  // A selector for adding extra containers to preview\n  preview: '',\n  // Re-render the cropper when resize the window\n  responsive: true,\n  // Restore the cropped area after resize the window\n  restore: true,\n  // Check if the current image is a cross-origin image\n  checkCrossOrigin: true,\n  // Check the current image's Exif Orientation information\n  checkOrientation: true,\n  // Show the black modal\n  modal: true,\n  // Show the dashed lines for guiding\n  guides: true,\n  // Show the center indicator for guiding\n  center: true,\n  // Show the white modal to highlight the crop box\n  highlight: true,\n  // Show the grid background\n  background: true,\n  // Enable to crop the image automatically when initialize\n  autoCrop: true,\n  // Define the percentage of automatic cropping area when initializes\n  autoCropArea: 0.8,\n  // Enable to move the image\n  movable: true,\n  // Enable to rotate the image\n  rotatable: true,\n  // Enable to scale the image\n  scalable: true,\n  // Enable to zoom the image\n  zoomable: true,\n  // Enable to zoom the image by dragging touch\n  zoomOnTouch: true,\n  // Enable to zoom the image by wheeling mouse\n  zoomOnWheel: true,\n  // Define zoom ratio when zoom the image by wheeling mouse\n  wheelZoomRatio: 0.1,\n  // Enable to move the crop box\n  cropBoxMovable: true,\n  // Enable to resize the crop box\n  cropBoxResizable: true,\n  // Toggle drag mode between \"crop\" and \"move\" when click twice on the cropper\n  toggleDragModeOnDblclick: true,\n  // Size limitation\n  minCanvasWidth: 0,\n  minCanvasHeight: 0,\n  minCropBoxWidth: 0,\n  minCropBoxHeight: 0,\n  minContainerWidth: MIN_CONTAINER_WIDTH,\n  minContainerHeight: MIN_CONTAINER_HEIGHT,\n  // Shortcuts of events\n  ready: null,\n  cropstart: null,\n  cropmove: null,\n  cropend: null,\n  crop: null,\n  zoom: null\n};\nvar TEMPLATE = '<div class=\"cropper-container\" touch-action=\"none\">' + '<div class=\"cropper-wrap-box\">' + '<div class=\"cropper-canvas\"></div>' + '</div>' + '<div class=\"cropper-drag-box\"></div>' + '<div class=\"cropper-crop-box\">' + '<span class=\"cropper-view-box\"></span>' + '<span class=\"cropper-dashed dashed-h\"></span>' + '<span class=\"cropper-dashed dashed-v\"></span>' + '<span class=\"cropper-center\"></span>' + '<span class=\"cropper-face\"></span>' + '<span class=\"cropper-line line-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-line line-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-line line-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-line line-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-point point-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-point point-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-point point-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-ne\" data-cropper-action=\"ne\"></span>' + '<span class=\"cropper-point point-nw\" data-cropper-action=\"nw\"></span>' + '<span class=\"cropper-point point-sw\" data-cropper-action=\"sw\"></span>' + '<span class=\"cropper-point point-se\" data-cropper-action=\"se\"></span>' + '</div>' + '</div>';\n\n/**\n * Check if the given value is not a number.\n */\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n\n/**\n * Check if the given value is a positive number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.\n */\nvar isPositiveNumber = function isPositiveNumber(value) {\n  return value > 0 && value < Infinity;\n};\n\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nvar slice = Array.prototype.slice;\n\n/**\n * Convert array-like or iterable object to an array.\n * @param {*} value - The value to convert.\n * @returns {Array} Returns a new array.\n */\nfunction toArray(value) {\n  return Array.from ? Array.from(value) : slice.call(value);\n}\n\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length) /* array-like */) {\n      toArray(data).forEach(function (value, key) {\n        callback.call(data, value, key, data);\n      });\n    } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n  return data;\n}\n\n/**\n * Extend the given object.\n * @param {*} target - The target object to extend.\n * @param {*} args - The rest objects for merging to the target object.\n * @returns {Object} The extended object.\n */\nvar assign = Object.assign || function assign(target) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (isObject(target) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          target[key] = arg[key];\n        });\n      }\n    });\n  }\n  return target;\n};\nvar REGEXP_DECIMALS = /\\.\\d*(?:0|9){12}\\d*$/;\n\n/**\n * Normalize decimal number.\n * Check out {@link https://0.30000000000000004.com/}\n * @param {number} value - The value to normalize.\n * @param {number} [times=100000000000] - The times for normalizing.\n * @returns {number} Returns the normalized number.\n */\nfunction normalizeDecimalNumber(value) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100000000000;\n  return REGEXP_DECIMALS.test(value) ? Math.round(value * times) / times : value;\n}\nvar REGEXP_SUFFIX = /^width|height|left|top|marginLeft|marginTop$/;\n\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value = \"\".concat(value, \"px\");\n    }\n    style[property] = value;\n  });\n}\n\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\nfunction hasClass(element, value) {\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\nfunction addClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n  var className = element.className.trim();\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\nfunction removeClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  }\n\n  // IE10-11 doesn't support the second parameter of `classList.toggle`\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_CAMEL_CASE = /([a-z\\d])([A-Z])/g;\n\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\nfunction toParamCase(value) {\n  return value.replace(REGEXP_CAMEL_CASE, '$1-$2').toLowerCase();\n}\n\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n  return element.getAttribute(\"data-\".concat(toParamCase(name)));\n}\n\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(toParamCase(name)), data);\n  }\n}\n\n/**\n * Remove data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to remove.\n */\nfunction removeData(element, name) {\n  if (isObject(element[name])) {\n    try {\n      delete element[name];\n    } catch (error) {\n      element[name] = undefined;\n    }\n  } else if (element.dataset) {\n    // #128 Safari not allows to delete dataset property\n    try {\n      delete element.dataset[name];\n    } catch (error) {\n      element.dataset[name] = undefined;\n    }\n  } else {\n    element.removeAttribute(\"data-\".concat(toParamCase(name)));\n  }\n}\nvar REGEXP_SPACES = /\\s\\s*/;\nvar onceSupported = function () {\n  var supported = false;\n  if (IS_BROWSER) {\n    var once = false;\n    var listener = function listener() {};\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n  return supported;\n}();\n\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n    element.removeEventListener(event, handler, options);\n  });\n}\n\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n        listeners = _element$listeners === void 0 ? {} : _element$listeners;\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        listener.apply(element, args);\n      };\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n    element.addEventListener(event, _handler, options);\n  });\n}\n\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\nfunction dispatchEvent(element, type, data) {\n  var event;\n\n  // Event and CustomEvent on IE9-11 are global objects, not constructors\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, {\n      detail: data,\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n  return element.dispatchEvent(event);\n}\n\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\nvar location = WINDOW.location;\nvar REGEXP_ORIGINS = /^(\\w+:)\\/\\/([^:/?#]*):?(\\d*)/i;\n\n/**\n * Check if the given URL is a cross origin URL.\n * @param {string} url - The target URL.\n * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.\n */\nfunction isCrossOriginURL(url) {\n  var parts = url.match(REGEXP_ORIGINS);\n  return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);\n}\n\n/**\n * Add timestamp to the given URL.\n * @param {string} url - The target URL.\n * @returns {string} The result URL.\n */\nfunction addTimestamp(url) {\n  var timestamp = \"timestamp=\".concat(new Date().getTime());\n  return url + (url.indexOf('?') === -1 ? '?' : '&') + timestamp;\n}\n\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n    scaleX = _ref.scaleX,\n    scaleY = _ref.scaleY,\n    translateX = _ref.translateX,\n    translateY = _ref.translateY;\n  var values = [];\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  }\n\n  // Rotate should come first before scale to match orientation transform\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n  var maxRatio = 0;\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n      if (Math.abs(ratio) > Math.abs(maxRatio)) {\n        maxRatio = ratio;\n      }\n    });\n  });\n  return maxRatio;\n}\n\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n    pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n      startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n\n/**\n * Get the max sizes in a rectangle under the given aspect ratio.\n * @param {Object} data - The original sizes.\n * @param {string} [type='contain'] - The adjust type.\n * @returns {Object} The result sizes.\n */\nfunction getAdjustedSizes(_ref4) {\n  var aspectRatio = _ref4.aspectRatio,\n    height = _ref4.height,\n    width = _ref4.width;\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'contain';\n  var isValidWidth = isPositiveNumber(width);\n  var isValidHeight = isPositiveNumber(height);\n  if (isValidWidth && isValidHeight) {\n    var adjustedWidth = height * aspectRatio;\n    if (type === 'contain' && adjustedWidth > width || type === 'cover' && adjustedWidth < width) {\n      height = width / aspectRatio;\n    } else {\n      width = height * aspectRatio;\n    }\n  } else if (isValidWidth) {\n    height = width / aspectRatio;\n  } else if (isValidHeight) {\n    width = height * aspectRatio;\n  }\n  return {\n    width: width,\n    height: height\n  };\n}\n\n/**\n * Get the new sizes of a rectangle after rotated.\n * @param {Object} data - The original sizes.\n * @returns {Object} The result sizes.\n */\nfunction getRotatedSizes(_ref5) {\n  var width = _ref5.width,\n    height = _ref5.height,\n    degree = _ref5.degree;\n  degree = Math.abs(degree) % 180;\n  if (degree === 90) {\n    return {\n      width: height,\n      height: width\n    };\n  }\n  var arc = degree % 90 * Math.PI / 180;\n  var sinArc = Math.sin(arc);\n  var cosArc = Math.cos(arc);\n  var newWidth = width * cosArc + height * sinArc;\n  var newHeight = width * sinArc + height * cosArc;\n  return degree > 90 ? {\n    width: newHeight,\n    height: newWidth\n  } : {\n    width: newWidth,\n    height: newHeight\n  };\n}\n\n/**\n * Get a canvas which drew the given image.\n * @param {HTMLImageElement} image - The image for drawing.\n * @param {Object} imageData - The image data.\n * @param {Object} canvasData - The canvas data.\n * @param {Object} options - The options.\n * @returns {HTMLCanvasElement} The result canvas.\n */\nfunction getSourceCanvas(image, _ref6, _ref7, _ref8) {\n  var imageAspectRatio = _ref6.aspectRatio,\n    imageNaturalWidth = _ref6.naturalWidth,\n    imageNaturalHeight = _ref6.naturalHeight,\n    _ref6$rotate = _ref6.rotate,\n    rotate = _ref6$rotate === void 0 ? 0 : _ref6$rotate,\n    _ref6$scaleX = _ref6.scaleX,\n    scaleX = _ref6$scaleX === void 0 ? 1 : _ref6$scaleX,\n    _ref6$scaleY = _ref6.scaleY,\n    scaleY = _ref6$scaleY === void 0 ? 1 : _ref6$scaleY;\n  var aspectRatio = _ref7.aspectRatio,\n    naturalWidth = _ref7.naturalWidth,\n    naturalHeight = _ref7.naturalHeight;\n  var _ref8$fillColor = _ref8.fillColor,\n    fillColor = _ref8$fillColor === void 0 ? 'transparent' : _ref8$fillColor,\n    _ref8$imageSmoothingE = _ref8.imageSmoothingEnabled,\n    imageSmoothingEnabled = _ref8$imageSmoothingE === void 0 ? true : _ref8$imageSmoothingE,\n    _ref8$imageSmoothingQ = _ref8.imageSmoothingQuality,\n    imageSmoothingQuality = _ref8$imageSmoothingQ === void 0 ? 'low' : _ref8$imageSmoothingQ,\n    _ref8$maxWidth = _ref8.maxWidth,\n    maxWidth = _ref8$maxWidth === void 0 ? Infinity : _ref8$maxWidth,\n    _ref8$maxHeight = _ref8.maxHeight,\n    maxHeight = _ref8$maxHeight === void 0 ? Infinity : _ref8$maxHeight,\n    _ref8$minWidth = _ref8.minWidth,\n    minWidth = _ref8$minWidth === void 0 ? 0 : _ref8$minWidth,\n    _ref8$minHeight = _ref8.minHeight,\n    minHeight = _ref8$minHeight === void 0 ? 0 : _ref8$minHeight;\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var maxSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var minSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var width = Math.min(maxSizes.width, Math.max(minSizes.width, naturalWidth));\n  var height = Math.min(maxSizes.height, Math.max(minSizes.height, naturalHeight));\n\n  // Note: should always use image's natural sizes for drawing as\n  // imageData.naturalWidth === canvasData.naturalHeight when rotate % 180 === 90\n  var destMaxSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var destMinSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var destWidth = Math.min(destMaxSizes.width, Math.max(destMinSizes.width, imageNaturalWidth));\n  var destHeight = Math.min(destMaxSizes.height, Math.max(destMinSizes.height, imageNaturalHeight));\n  var params = [-destWidth / 2, -destHeight / 2, destWidth, destHeight];\n  canvas.width = normalizeDecimalNumber(width);\n  canvas.height = normalizeDecimalNumber(height);\n  context.fillStyle = fillColor;\n  context.fillRect(0, 0, width, height);\n  context.save();\n  context.translate(width / 2, height / 2);\n  context.rotate(rotate * Math.PI / 180);\n  context.scale(scaleX, scaleY);\n  context.imageSmoothingEnabled = imageSmoothingEnabled;\n  context.imageSmoothingQuality = imageSmoothingQuality;\n  context.drawImage.apply(context, [image].concat(_toConsumableArray(params.map(function (param) {\n    return Math.floor(normalizeDecimalNumber(param));\n  }))));\n  context.restore();\n  return canvas;\n}\nvar fromCharCode = String.fromCharCode;\n\n/**\n * Get string from char code in data view.\n * @param {DataView} dataView - The data view for read.\n * @param {number} start - The start index.\n * @param {number} length - The read length.\n * @returns {string} The read result.\n */\nfunction getStringFromCharCode(dataView, start, length) {\n  var str = '';\n  length += start;\n  for (var i = start; i < length; i += 1) {\n    str += fromCharCode(dataView.getUint8(i));\n  }\n  return str;\n}\nvar REGEXP_DATA_URL_HEAD = /^data:.*,/;\n\n/**\n * Transform Data URL to array buffer.\n * @param {string} dataURL - The Data URL to transform.\n * @returns {ArrayBuffer} The result array buffer.\n */\nfunction dataURLToArrayBuffer(dataURL) {\n  var base64 = dataURL.replace(REGEXP_DATA_URL_HEAD, '');\n  var binary = atob(base64);\n  var arrayBuffer = new ArrayBuffer(binary.length);\n  var uint8 = new Uint8Array(arrayBuffer);\n  forEach(uint8, function (value, i) {\n    uint8[i] = binary.charCodeAt(i);\n  });\n  return arrayBuffer;\n}\n\n/**\n * Transform array buffer to Data URL.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to transform.\n * @param {string} mimeType - The mime type of the Data URL.\n * @returns {string} The result Data URL.\n */\nfunction arrayBufferToDataURL(arrayBuffer, mimeType) {\n  var chunks = [];\n\n  // Chunk Typed Array for better performance (#435)\n  var chunkSize = 8192;\n  var uint8 = new Uint8Array(arrayBuffer);\n  while (uint8.length > 0) {\n    // XXX: Babel's `toConsumableArray` helper will throw error in IE or Safari 9\n    // eslint-disable-next-line prefer-spread\n    chunks.push(fromCharCode.apply(null, toArray(uint8.subarray(0, chunkSize))));\n    uint8 = uint8.subarray(chunkSize);\n  }\n  return \"data:\".concat(mimeType, \";base64,\").concat(btoa(chunks.join('')));\n}\n\n/**\n * Get orientation value from given array buffer.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to read.\n * @returns {number} The read orientation value.\n */\nfunction resetAndGetOrientation(arrayBuffer) {\n  var dataView = new DataView(arrayBuffer);\n  var orientation;\n\n  // Ignores range error when the image does not have correct Exif information\n  try {\n    var littleEndian;\n    var app1Start;\n    var ifdStart;\n\n    // Only handle JPEG image (start by 0xFFD8)\n    if (dataView.getUint8(0) === 0xFF && dataView.getUint8(1) === 0xD8) {\n      var length = dataView.byteLength;\n      var offset = 2;\n      while (offset + 1 < length) {\n        if (dataView.getUint8(offset) === 0xFF && dataView.getUint8(offset + 1) === 0xE1) {\n          app1Start = offset;\n          break;\n        }\n        offset += 1;\n      }\n    }\n    if (app1Start) {\n      var exifIDCode = app1Start + 4;\n      var tiffOffset = app1Start + 10;\n      if (getStringFromCharCode(dataView, exifIDCode, 4) === 'Exif') {\n        var endianness = dataView.getUint16(tiffOffset);\n        littleEndian = endianness === 0x4949;\n        if (littleEndian || endianness === 0x4D4D /* bigEndian */) {\n          if (dataView.getUint16(tiffOffset + 2, littleEndian) === 0x002A) {\n            var firstIFDOffset = dataView.getUint32(tiffOffset + 4, littleEndian);\n            if (firstIFDOffset >= 0x00000008) {\n              ifdStart = tiffOffset + firstIFDOffset;\n            }\n          }\n        }\n      }\n    }\n    if (ifdStart) {\n      var _length = dataView.getUint16(ifdStart, littleEndian);\n      var _offset;\n      var i;\n      for (i = 0; i < _length; i += 1) {\n        _offset = ifdStart + i * 12 + 2;\n        if (dataView.getUint16(_offset, littleEndian) === 0x0112 /* Orientation */) {\n          // 8 is the offset of the current tag's value\n          _offset += 8;\n\n          // Get the original orientation value\n          orientation = dataView.getUint16(_offset, littleEndian);\n\n          // Override the orientation with its default value\n          dataView.setUint16(_offset, 1, littleEndian);\n          break;\n        }\n      }\n    }\n  } catch (error) {\n    orientation = 1;\n  }\n  return orientation;\n}\n\n/**\n * Parse Exif Orientation value.\n * @param {number} orientation - The orientation to parse.\n * @returns {Object} The parsed result.\n */\nfunction parseOrientation(orientation) {\n  var rotate = 0;\n  var scaleX = 1;\n  var scaleY = 1;\n  switch (orientation) {\n    // Flip horizontal\n    case 2:\n      scaleX = -1;\n      break;\n\n    // Rotate left 180°\n    case 3:\n      rotate = -180;\n      break;\n\n    // Flip vertical\n    case 4:\n      scaleY = -1;\n      break;\n\n    // Flip vertical and rotate right 90°\n    case 5:\n      rotate = 90;\n      scaleY = -1;\n      break;\n\n    // Rotate right 90°\n    case 6:\n      rotate = 90;\n      break;\n\n    // Flip horizontal and rotate right 90°\n    case 7:\n      rotate = 90;\n      scaleX = -1;\n      break;\n\n    // Rotate left 90°\n    case 8:\n      rotate = -90;\n      break;\n  }\n  return {\n    rotate: rotate,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initCanvas();\n    this.initCropBox();\n    this.renderCanvas();\n    if (this.cropped) {\n      this.renderCropBox();\n    }\n  },\n  initContainer: function initContainer() {\n    var element = this.element,\n      options = this.options,\n      container = this.container,\n      cropper = this.cropper;\n    var minWidth = Number(options.minContainerWidth);\n    var minHeight = Number(options.minContainerHeight);\n    addClass(cropper, CLASS_HIDDEN);\n    removeClass(element, CLASS_HIDDEN);\n    var containerData = {\n      width: Math.max(container.offsetWidth, minWidth >= 0 ? minWidth : MIN_CONTAINER_WIDTH),\n      height: Math.max(container.offsetHeight, minHeight >= 0 ? minHeight : MIN_CONTAINER_HEIGHT)\n    };\n    this.containerData = containerData;\n    setStyle(cropper, {\n      width: containerData.width,\n      height: containerData.height\n    });\n    addClass(element, CLASS_HIDDEN);\n    removeClass(cropper, CLASS_HIDDEN);\n  },\n  // Canvas (image wrapper)\n  initCanvas: function initCanvas() {\n    var containerData = this.containerData,\n      imageData = this.imageData;\n    var viewMode = this.options.viewMode;\n    var rotated = Math.abs(imageData.rotate) % 180 === 90;\n    var naturalWidth = rotated ? imageData.naturalHeight : imageData.naturalWidth;\n    var naturalHeight = rotated ? imageData.naturalWidth : imageData.naturalHeight;\n    var aspectRatio = naturalWidth / naturalHeight;\n    var canvasWidth = containerData.width;\n    var canvasHeight = containerData.height;\n    if (containerData.height * aspectRatio > containerData.width) {\n      if (viewMode === 3) {\n        canvasWidth = containerData.height * aspectRatio;\n      } else {\n        canvasHeight = containerData.width / aspectRatio;\n      }\n    } else if (viewMode === 3) {\n      canvasHeight = containerData.width / aspectRatio;\n    } else {\n      canvasWidth = containerData.height * aspectRatio;\n    }\n    var canvasData = {\n      aspectRatio: aspectRatio,\n      naturalWidth: naturalWidth,\n      naturalHeight: naturalHeight,\n      width: canvasWidth,\n      height: canvasHeight\n    };\n    this.canvasData = canvasData;\n    this.limited = viewMode === 1 || viewMode === 2;\n    this.limitCanvas(true, true);\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    canvasData.left = (containerData.width - canvasData.width) / 2;\n    canvasData.top = (containerData.height - canvasData.height) / 2;\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    this.initialCanvasData = assign({}, canvasData);\n  },\n  limitCanvas: function limitCanvas(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var viewMode = options.viewMode;\n    var aspectRatio = canvasData.aspectRatio;\n    var cropped = this.cropped && cropBoxData;\n    if (sizeLimited) {\n      var minCanvasWidth = Number(options.minCanvasWidth) || 0;\n      var minCanvasHeight = Number(options.minCanvasHeight) || 0;\n      if (viewMode > 1) {\n        minCanvasWidth = Math.max(minCanvasWidth, containerData.width);\n        minCanvasHeight = Math.max(minCanvasHeight, containerData.height);\n        if (viewMode === 3) {\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      } else if (viewMode > 0) {\n        if (minCanvasWidth) {\n          minCanvasWidth = Math.max(minCanvasWidth, cropped ? cropBoxData.width : 0);\n        } else if (minCanvasHeight) {\n          minCanvasHeight = Math.max(minCanvasHeight, cropped ? cropBoxData.height : 0);\n        } else if (cropped) {\n          minCanvasWidth = cropBoxData.width;\n          minCanvasHeight = cropBoxData.height;\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      }\n      var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: minCanvasWidth,\n        height: minCanvasHeight\n      });\n      minCanvasWidth = _getAdjustedSizes.width;\n      minCanvasHeight = _getAdjustedSizes.height;\n      canvasData.minWidth = minCanvasWidth;\n      canvasData.minHeight = minCanvasHeight;\n      canvasData.maxWidth = Infinity;\n      canvasData.maxHeight = Infinity;\n    }\n    if (positionLimited) {\n      if (viewMode > (cropped ? 0 : 1)) {\n        var newCanvasLeft = containerData.width - canvasData.width;\n        var newCanvasTop = containerData.height - canvasData.height;\n        canvasData.minLeft = Math.min(0, newCanvasLeft);\n        canvasData.minTop = Math.min(0, newCanvasTop);\n        canvasData.maxLeft = Math.max(0, newCanvasLeft);\n        canvasData.maxTop = Math.max(0, newCanvasTop);\n        if (cropped && this.limited) {\n          canvasData.minLeft = Math.min(cropBoxData.left, cropBoxData.left + (cropBoxData.width - canvasData.width));\n          canvasData.minTop = Math.min(cropBoxData.top, cropBoxData.top + (cropBoxData.height - canvasData.height));\n          canvasData.maxLeft = cropBoxData.left;\n          canvasData.maxTop = cropBoxData.top;\n          if (viewMode === 2) {\n            if (canvasData.width >= containerData.width) {\n              canvasData.minLeft = Math.min(0, newCanvasLeft);\n              canvasData.maxLeft = Math.max(0, newCanvasLeft);\n            }\n            if (canvasData.height >= containerData.height) {\n              canvasData.minTop = Math.min(0, newCanvasTop);\n              canvasData.maxTop = Math.max(0, newCanvasTop);\n            }\n          }\n        }\n      } else {\n        canvasData.minLeft = -canvasData.width;\n        canvasData.minTop = -canvasData.height;\n        canvasData.maxLeft = containerData.width;\n        canvasData.maxTop = containerData.height;\n      }\n    }\n  },\n  renderCanvas: function renderCanvas(changed, transformed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    if (transformed) {\n      var _getRotatedSizes = getRotatedSizes({\n          width: imageData.naturalWidth * Math.abs(imageData.scaleX || 1),\n          height: imageData.naturalHeight * Math.abs(imageData.scaleY || 1),\n          degree: imageData.rotate || 0\n        }),\n        naturalWidth = _getRotatedSizes.width,\n        naturalHeight = _getRotatedSizes.height;\n      var width = canvasData.width * (naturalWidth / canvasData.naturalWidth);\n      var height = canvasData.height * (naturalHeight / canvasData.naturalHeight);\n      canvasData.left -= (width - canvasData.width) / 2;\n      canvasData.top -= (height - canvasData.height) / 2;\n      canvasData.width = width;\n      canvasData.height = height;\n      canvasData.aspectRatio = naturalWidth / naturalHeight;\n      canvasData.naturalWidth = naturalWidth;\n      canvasData.naturalHeight = naturalHeight;\n      this.limitCanvas(true, false);\n    }\n    if (canvasData.width > canvasData.maxWidth || canvasData.width < canvasData.minWidth) {\n      canvasData.left = canvasData.oldLeft;\n    }\n    if (canvasData.height > canvasData.maxHeight || canvasData.height < canvasData.minHeight) {\n      canvasData.top = canvasData.oldTop;\n    }\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    this.limitCanvas(false, true);\n    canvasData.left = Math.min(Math.max(canvasData.left, canvasData.minLeft), canvasData.maxLeft);\n    canvasData.top = Math.min(Math.max(canvasData.top, canvasData.minTop), canvasData.maxTop);\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    setStyle(this.canvas, assign({\n      width: canvasData.width,\n      height: canvasData.height\n    }, getTransforms({\n      translateX: canvasData.left,\n      translateY: canvasData.top\n    })));\n    this.renderImage(changed);\n    if (this.cropped && this.limited) {\n      this.limitCropBox(true, true);\n    }\n  },\n  renderImage: function renderImage(changed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    var width = imageData.naturalWidth * (canvasData.width / canvasData.naturalWidth);\n    var height = imageData.naturalHeight * (canvasData.height / canvasData.naturalHeight);\n    assign(imageData, {\n      width: width,\n      height: height,\n      left: (canvasData.width - width) / 2,\n      top: (canvasData.height - height) / 2\n    });\n    setStyle(this.image, assign({\n      width: imageData.width,\n      height: imageData.height\n    }, getTransforms(assign({\n      translateX: imageData.left,\n      translateY: imageData.top\n    }, imageData))));\n    if (changed) {\n      this.output();\n    }\n  },\n  initCropBox: function initCropBox() {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var aspectRatio = options.aspectRatio || options.initialAspectRatio;\n    var autoCropArea = Number(options.autoCropArea) || 0.8;\n    var cropBoxData = {\n      width: canvasData.width,\n      height: canvasData.height\n    };\n    if (aspectRatio) {\n      if (canvasData.height * aspectRatio > canvasData.width) {\n        cropBoxData.height = cropBoxData.width / aspectRatio;\n      } else {\n        cropBoxData.width = cropBoxData.height * aspectRatio;\n      }\n    }\n    this.cropBoxData = cropBoxData;\n    this.limitCropBox(true, true);\n\n    // Initialize auto crop area\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n\n    // The width/height of auto crop area must large than \"minWidth/Height\"\n    cropBoxData.width = Math.max(cropBoxData.minWidth, cropBoxData.width * autoCropArea);\n    cropBoxData.height = Math.max(cropBoxData.minHeight, cropBoxData.height * autoCropArea);\n    cropBoxData.left = canvasData.left + (canvasData.width - cropBoxData.width) / 2;\n    cropBoxData.top = canvasData.top + (canvasData.height - cropBoxData.height) / 2;\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    this.initialCropBoxData = assign({}, cropBoxData);\n  },\n  limitCropBox: function limitCropBox(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData,\n      limited = this.limited;\n    var aspectRatio = options.aspectRatio;\n    if (sizeLimited) {\n      var minCropBoxWidth = Number(options.minCropBoxWidth) || 0;\n      var minCropBoxHeight = Number(options.minCropBoxHeight) || 0;\n      var maxCropBoxWidth = limited ? Math.min(containerData.width, canvasData.width, canvasData.width + canvasData.left, containerData.width - canvasData.left) : containerData.width;\n      var maxCropBoxHeight = limited ? Math.min(containerData.height, canvasData.height, canvasData.height + canvasData.top, containerData.height - canvasData.top) : containerData.height;\n\n      // The min/maxCropBoxWidth/Height must be less than container's width/height\n      minCropBoxWidth = Math.min(minCropBoxWidth, containerData.width);\n      minCropBoxHeight = Math.min(minCropBoxHeight, containerData.height);\n      if (aspectRatio) {\n        if (minCropBoxWidth && minCropBoxHeight) {\n          if (minCropBoxHeight * aspectRatio > minCropBoxWidth) {\n            minCropBoxHeight = minCropBoxWidth / aspectRatio;\n          } else {\n            minCropBoxWidth = minCropBoxHeight * aspectRatio;\n          }\n        } else if (minCropBoxWidth) {\n          minCropBoxHeight = minCropBoxWidth / aspectRatio;\n        } else if (minCropBoxHeight) {\n          minCropBoxWidth = minCropBoxHeight * aspectRatio;\n        }\n        if (maxCropBoxHeight * aspectRatio > maxCropBoxWidth) {\n          maxCropBoxHeight = maxCropBoxWidth / aspectRatio;\n        } else {\n          maxCropBoxWidth = maxCropBoxHeight * aspectRatio;\n        }\n      }\n\n      // The minWidth/Height must be less than maxWidth/Height\n      cropBoxData.minWidth = Math.min(minCropBoxWidth, maxCropBoxWidth);\n      cropBoxData.minHeight = Math.min(minCropBoxHeight, maxCropBoxHeight);\n      cropBoxData.maxWidth = maxCropBoxWidth;\n      cropBoxData.maxHeight = maxCropBoxHeight;\n    }\n    if (positionLimited) {\n      if (limited) {\n        cropBoxData.minLeft = Math.max(0, canvasData.left);\n        cropBoxData.minTop = Math.max(0, canvasData.top);\n        cropBoxData.maxLeft = Math.min(containerData.width, canvasData.left + canvasData.width) - cropBoxData.width;\n        cropBoxData.maxTop = Math.min(containerData.height, canvasData.top + canvasData.height) - cropBoxData.height;\n      } else {\n        cropBoxData.minLeft = 0;\n        cropBoxData.minTop = 0;\n        cropBoxData.maxLeft = containerData.width - cropBoxData.width;\n        cropBoxData.maxTop = containerData.height - cropBoxData.height;\n      }\n    }\n  },\n  renderCropBox: function renderCropBox() {\n    var options = this.options,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData;\n    if (cropBoxData.width > cropBoxData.maxWidth || cropBoxData.width < cropBoxData.minWidth) {\n      cropBoxData.left = cropBoxData.oldLeft;\n    }\n    if (cropBoxData.height > cropBoxData.maxHeight || cropBoxData.height < cropBoxData.minHeight) {\n      cropBoxData.top = cropBoxData.oldTop;\n    }\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n    this.limitCropBox(false, true);\n    cropBoxData.left = Math.min(Math.max(cropBoxData.left, cropBoxData.minLeft), cropBoxData.maxLeft);\n    cropBoxData.top = Math.min(Math.max(cropBoxData.top, cropBoxData.minTop), cropBoxData.maxTop);\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    if (options.movable && options.cropBoxMovable) {\n      // Turn to move the canvas when the crop box is equal to the container\n      setData(this.face, DATA_ACTION, cropBoxData.width >= containerData.width && cropBoxData.height >= containerData.height ? ACTION_MOVE : ACTION_ALL);\n    }\n    setStyle(this.cropBox, assign({\n      width: cropBoxData.width,\n      height: cropBoxData.height\n    }, getTransforms({\n      translateX: cropBoxData.left,\n      translateY: cropBoxData.top\n    })));\n    if (this.cropped && this.limited) {\n      this.limitCanvas(true, true);\n    }\n    if (!this.disabled) {\n      this.output();\n    }\n  },\n  output: function output() {\n    this.preview();\n    dispatchEvent(this.element, EVENT_CROP, this.getData());\n  }\n};\nvar preview = {\n  initPreview: function initPreview() {\n    var element = this.element,\n      crossOrigin = this.crossOrigin;\n    var preview = this.options.preview;\n    var url = crossOrigin ? this.crossOriginUrl : this.url;\n    var alt = element.alt || 'The image to preview';\n    var image = document.createElement('img');\n    if (crossOrigin) {\n      image.crossOrigin = crossOrigin;\n    }\n    image.src = url;\n    image.alt = alt;\n    this.viewBox.appendChild(image);\n    this.viewBoxImage = image;\n    if (!preview) {\n      return;\n    }\n    var previews = preview;\n    if (typeof preview === 'string') {\n      previews = element.ownerDocument.querySelectorAll(preview);\n    } else if (preview.querySelector) {\n      previews = [preview];\n    }\n    this.previews = previews;\n    forEach(previews, function (el) {\n      var img = document.createElement('img');\n\n      // Save the original size for recover\n      setData(el, DATA_PREVIEW, {\n        width: el.offsetWidth,\n        height: el.offsetHeight,\n        html: el.innerHTML\n      });\n      if (crossOrigin) {\n        img.crossOrigin = crossOrigin;\n      }\n      img.src = url;\n      img.alt = alt;\n\n      /**\n       * Override img element styles\n       * Add `display:block` to avoid margin top issue\n       * Add `height:auto` to override `height` attribute on IE8\n       * (Occur only when margin-top <= -height)\n       */\n      img.style.cssText = 'display:block;' + 'width:100%;' + 'height:auto;' + 'min-width:0!important;' + 'min-height:0!important;' + 'max-width:none!important;' + 'max-height:none!important;' + 'image-orientation:0deg!important;\"';\n      el.innerHTML = '';\n      el.appendChild(img);\n    });\n  },\n  resetPreview: function resetPreview() {\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      setStyle(element, {\n        width: data.width,\n        height: data.height\n      });\n      element.innerHTML = data.html;\n      removeData(element, DATA_PREVIEW);\n    });\n  },\n  preview: function preview() {\n    var imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var cropBoxWidth = cropBoxData.width,\n      cropBoxHeight = cropBoxData.height;\n    var width = imageData.width,\n      height = imageData.height;\n    var left = cropBoxData.left - canvasData.left - imageData.left;\n    var top = cropBoxData.top - canvasData.top - imageData.top;\n    if (!this.cropped || this.disabled) {\n      return;\n    }\n    setStyle(this.viewBoxImage, assign({\n      width: width,\n      height: height\n    }, getTransforms(assign({\n      translateX: -left,\n      translateY: -top\n    }, imageData))));\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      var originalWidth = data.width;\n      var originalHeight = data.height;\n      var newWidth = originalWidth;\n      var newHeight = originalHeight;\n      var ratio = 1;\n      if (cropBoxWidth) {\n        ratio = originalWidth / cropBoxWidth;\n        newHeight = cropBoxHeight * ratio;\n      }\n      if (cropBoxHeight && newHeight > originalHeight) {\n        ratio = originalHeight / cropBoxHeight;\n        newWidth = cropBoxWidth * ratio;\n        newHeight = originalHeight;\n      }\n      setStyle(element, {\n        width: newWidth,\n        height: newHeight\n      });\n      setStyle(element.getElementsByTagName('img')[0], assign({\n        width: width * ratio,\n        height: height * ratio\n      }, getTransforms(assign({\n        translateX: -left * ratio,\n        translateY: -top * ratio\n      }, imageData))));\n    });\n  }\n};\nvar events = {\n  bind: function bind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      addListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      addListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      addListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      addListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      addListener(element, EVENT_ZOOM, options.zoom);\n    }\n    addListener(cropper, EVENT_POINTER_DOWN, this.onCropStart = this.cropStart.bind(this));\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(cropper, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      addListener(cropper, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n    addListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove = this.cropMove.bind(this));\n    addListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd = this.cropEnd.bind(this));\n    if (options.responsive) {\n      addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      removeListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      removeListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      removeListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      removeListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      removeListener(element, EVENT_ZOOM, options.zoom);\n    }\n    removeListener(cropper, EVENT_POINTER_DOWN, this.onCropStart);\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(cropper, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      removeListener(cropper, EVENT_DBLCLICK, this.onDblclick);\n    }\n    removeListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove);\n    removeListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd);\n    if (options.responsive) {\n      removeListener(window, EVENT_RESIZE, this.onResize);\n    }\n  }\n};\nvar handlers = {\n  resize: function resize() {\n    if (this.disabled) {\n      return;\n    }\n    var options = this.options,\n      container = this.container,\n      containerData = this.containerData;\n    var ratioX = container.offsetWidth / containerData.width;\n    var ratioY = container.offsetHeight / containerData.height;\n    var ratio = Math.abs(ratioX - 1) > Math.abs(ratioY - 1) ? ratioX : ratioY;\n\n    // Resize when width changed or height changed\n    if (ratio !== 1) {\n      var canvasData;\n      var cropBoxData;\n      if (options.restore) {\n        canvasData = this.getCanvasData();\n        cropBoxData = this.getCropBoxData();\n      }\n      this.render();\n      if (options.restore) {\n        this.setCanvasData(forEach(canvasData, function (n, i) {\n          canvasData[i] = n * ratio;\n        }));\n        this.setCropBoxData(forEach(cropBoxData, function (n, i) {\n          cropBoxData[i] = n * ratio;\n        }));\n      }\n    }\n  },\n  dblclick: function dblclick() {\n    if (this.disabled || this.options.dragMode === DRAG_MODE_NONE) {\n      return;\n    }\n    this.setDragMode(hasClass(this.dragBox, CLASS_CROP) ? DRAG_MODE_MOVE : DRAG_MODE_CROP);\n  },\n  wheel: function wheel(event) {\n    var _this = this;\n    var ratio = Number(this.options.wheelZoomRatio) || 0.1;\n    var delta = 1;\n    if (this.disabled) {\n      return;\n    }\n    event.preventDefault();\n\n    // Limit wheel speed to prevent zoom too fast (#21)\n    if (this.wheeling) {\n      return;\n    }\n    this.wheeling = true;\n    setTimeout(function () {\n      _this.wheeling = false;\n    }, 50);\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n    this.zoom(-delta * ratio, event);\n  },\n  cropStart: function cropStart(event) {\n    var buttons = event.buttons,\n      button = event.button;\n    if (this.disabled\n\n    // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && (\n    // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0\n\n    // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n    var options = this.options,\n      pointers = this.pointers;\n    var action;\n    if (event.changedTouches) {\n      // Handle touch event\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      // Handle mouse event and pointer event\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n    if (Object.keys(pointers).length > 1 && options.zoomable && options.zoomOnTouch) {\n      action = ACTION_ZOOM;\n    } else {\n      action = getData(event.target, DATA_ACTION);\n    }\n    if (!REGEXP_ACTIONS.test(action)) {\n      return;\n    }\n    if (dispatchEvent(this.element, EVENT_CROP_START, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n\n    // This line is required for preventing page zooming in iOS browsers\n    event.preventDefault();\n    this.action = action;\n    this.cropping = false;\n    if (action === ACTION_CROP) {\n      this.cropping = true;\n      addClass(this.dragBox, CLASS_MODAL);\n    }\n  },\n  cropMove: function cropMove(event) {\n    var action = this.action;\n    if (this.disabled || !action) {\n      return;\n    }\n    var pointers = this.pointers;\n    event.preventDefault();\n    if (dispatchEvent(this.element, EVENT_CROP_MOVE, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        // The first parameter should not be undefined (#432)\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n    this.change(event);\n  },\n  cropEnd: function cropEnd(event) {\n    if (this.disabled) {\n      return;\n    }\n    var action = this.action,\n      pointers = this.pointers;\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        delete pointers[touch.identifier];\n      });\n    } else {\n      delete pointers[event.pointerId || 0];\n    }\n    if (!action) {\n      return;\n    }\n    event.preventDefault();\n    if (!Object.keys(pointers).length) {\n      this.action = '';\n    }\n    if (this.cropping) {\n      this.cropping = false;\n      toggleClass(this.dragBox, CLASS_MODAL, this.cropped && this.options.modal);\n    }\n    dispatchEvent(this.element, EVENT_CROP_END, {\n      originalEvent: event,\n      action: action\n    });\n  }\n};\nvar change = {\n  change: function change(event) {\n    var options = this.options,\n      canvasData = this.canvasData,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData,\n      pointers = this.pointers;\n    var action = this.action;\n    var aspectRatio = options.aspectRatio;\n    var left = cropBoxData.left,\n      top = cropBoxData.top,\n      width = cropBoxData.width,\n      height = cropBoxData.height;\n    var right = left + width;\n    var bottom = top + height;\n    var minLeft = 0;\n    var minTop = 0;\n    var maxWidth = containerData.width;\n    var maxHeight = containerData.height;\n    var renderable = true;\n    var offset;\n\n    // Locking aspect ratio in \"free mode\" by holding shift key\n    if (!aspectRatio && event.shiftKey) {\n      aspectRatio = width && height ? width / height : 1;\n    }\n    if (this.limited) {\n      minLeft = cropBoxData.minLeft;\n      minTop = cropBoxData.minTop;\n      maxWidth = minLeft + Math.min(containerData.width, canvasData.width, canvasData.left + canvasData.width);\n      maxHeight = minTop + Math.min(containerData.height, canvasData.height, canvasData.top + canvasData.height);\n    }\n    var pointer = pointers[Object.keys(pointers)[0]];\n    var range = {\n      x: pointer.endX - pointer.startX,\n      y: pointer.endY - pointer.startY\n    };\n    var check = function check(side) {\n      switch (side) {\n        case ACTION_EAST:\n          if (right + range.x > maxWidth) {\n            range.x = maxWidth - right;\n          }\n          break;\n        case ACTION_WEST:\n          if (left + range.x < minLeft) {\n            range.x = minLeft - left;\n          }\n          break;\n        case ACTION_NORTH:\n          if (top + range.y < minTop) {\n            range.y = minTop - top;\n          }\n          break;\n        case ACTION_SOUTH:\n          if (bottom + range.y > maxHeight) {\n            range.y = maxHeight - bottom;\n          }\n          break;\n      }\n    };\n    switch (action) {\n      // Move crop box\n      case ACTION_ALL:\n        left += range.x;\n        top += range.y;\n        break;\n\n      // Resize crop box\n      case ACTION_EAST:\n        if (range.x >= 0 && (right >= maxWidth || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_EAST);\n        width += range.x;\n        if (width < 0) {\n          action = ACTION_WEST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_NORTH:\n        if (range.y <= 0 && (top <= minTop || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_NORTH);\n        height -= range.y;\n        top += range.y;\n        if (height < 0) {\n          action = ACTION_SOUTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_WEST:\n        if (range.x <= 0 && (left <= minLeft || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_WEST);\n        width -= range.x;\n        left += range.x;\n        if (width < 0) {\n          action = ACTION_EAST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_SOUTH:\n        if (range.y >= 0 && (bottom >= maxHeight || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_SOUTH);\n        height += range.y;\n        if (height < 0) {\n          action = ACTION_NORTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_NORTH_EAST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || right >= maxWidth)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_NORTH_WEST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || left <= minLeft)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n          left += cropBoxData.width - width;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_WEST:\n        if (aspectRatio) {\n          if (range.x <= 0 && (left <= minLeft || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_WEST);\n          width -= range.x;\n          left += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_EAST:\n        if (aspectRatio) {\n          if (range.x >= 0 && (right >= maxWidth || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_EAST);\n          width += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n\n      // Move canvas\n      case ACTION_MOVE:\n        this.move(range.x, range.y);\n        renderable = false;\n        break;\n\n      // Zoom canvas\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), event);\n        renderable = false;\n        break;\n\n      // Create crop box\n      case ACTION_CROP:\n        if (!range.x || !range.y) {\n          renderable = false;\n          break;\n        }\n        offset = getOffset(this.cropper);\n        left = pointer.startX - offset.left;\n        top = pointer.startY - offset.top;\n        width = cropBoxData.minWidth;\n        height = cropBoxData.minHeight;\n        if (range.x > 0) {\n          action = range.y > 0 ? ACTION_SOUTH_EAST : ACTION_NORTH_EAST;\n        } else if (range.x < 0) {\n          left -= width;\n          action = range.y > 0 ? ACTION_SOUTH_WEST : ACTION_NORTH_WEST;\n        }\n        if (range.y < 0) {\n          top -= height;\n        }\n\n        // Show the crop box if is hidden\n        if (!this.cropped) {\n          removeClass(this.cropBox, CLASS_HIDDEN);\n          this.cropped = true;\n          if (this.limited) {\n            this.limitCropBox(true, true);\n          }\n        }\n        break;\n    }\n    if (renderable) {\n      cropBoxData.width = width;\n      cropBoxData.height = height;\n      cropBoxData.left = left;\n      cropBoxData.top = top;\n      this.action = action;\n      this.renderCropBox();\n    }\n\n    // Override\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  }\n};\nvar methods = {\n  // Show the crop box manually\n  crop: function crop() {\n    if (this.ready && !this.cropped && !this.disabled) {\n      this.cropped = true;\n      this.limitCropBox(true, true);\n      if (this.options.modal) {\n        addClass(this.dragBox, CLASS_MODAL);\n      }\n      removeClass(this.cropBox, CLASS_HIDDEN);\n      this.setCropBoxData(this.initialCropBoxData);\n    }\n    return this;\n  },\n  // Reset the image and crop box to their initial states\n  reset: function reset() {\n    if (this.ready && !this.disabled) {\n      this.imageData = assign({}, this.initialImageData);\n      this.canvasData = assign({}, this.initialCanvasData);\n      this.cropBoxData = assign({}, this.initialCropBoxData);\n      this.renderCanvas();\n      if (this.cropped) {\n        this.renderCropBox();\n      }\n    }\n    return this;\n  },\n  // Clear the crop box\n  clear: function clear() {\n    if (this.cropped && !this.disabled) {\n      assign(this.cropBoxData, {\n        left: 0,\n        top: 0,\n        width: 0,\n        height: 0\n      });\n      this.cropped = false;\n      this.renderCropBox();\n      this.limitCanvas(true, true);\n\n      // Render canvas after crop box rendered\n      this.renderCanvas();\n      removeClass(this.dragBox, CLASS_MODAL);\n      addClass(this.cropBox, CLASS_HIDDEN);\n    }\n    return this;\n  },\n  /**\n   * Replace the image's src and rebuild the cropper\n   * @param {string} url - The new URL.\n   * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.\n   * @returns {Cropper} this\n   */\n  replace: function replace(url) {\n    var hasSameSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!this.disabled && url) {\n      if (this.isImg) {\n        this.element.src = url;\n      }\n      if (hasSameSize) {\n        this.url = url;\n        this.image.src = url;\n        if (this.ready) {\n          this.viewBoxImage.src = url;\n          forEach(this.previews, function (element) {\n            element.getElementsByTagName('img')[0].src = url;\n          });\n        }\n      } else {\n        if (this.isImg) {\n          this.replaced = true;\n        }\n        this.options.data = null;\n        this.uncreate();\n        this.load(url);\n      }\n    }\n    return this;\n  },\n  // Enable (unfreeze) the cropper\n  enable: function enable() {\n    if (this.ready && this.disabled) {\n      this.disabled = false;\n      removeClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  // Disable (freeze) the cropper\n  disable: function disable() {\n    if (this.ready && !this.disabled) {\n      this.disabled = true;\n      addClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  /**\n   * Destroy the cropper and remove the instance from the image\n   * @returns {Cropper} this\n   */\n  destroy: function destroy() {\n    var element = this.element;\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n    element[NAMESPACE] = undefined;\n    if (this.isImg && this.replaced) {\n      element.src = this.originalUrl;\n    }\n    this.uncreate();\n    return this;\n  },\n  /**\n   * Move the canvas with relative offsets\n   * @param {number} offsetX - The relative offset distance on the x-axis.\n   * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.\n   * @returns {Cropper} this\n   */\n  move: function move(offsetX) {\n    var offsetY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : offsetX;\n    var _this$canvasData = this.canvasData,\n      left = _this$canvasData.left,\n      top = _this$canvasData.top;\n    return this.moveTo(isUndefined(offsetX) ? offsetX : left + Number(offsetX), isUndefined(offsetY) ? offsetY : top + Number(offsetY));\n  },\n  /**\n   * Move the canvas to an absolute point\n   * @param {number} x - The x-axis coordinate.\n   * @param {number} [y=x] - The y-axis coordinate.\n   * @returns {Cropper} this\n   */\n  moveTo: function moveTo(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var canvasData = this.canvasData;\n    var changed = false;\n    x = Number(x);\n    y = Number(y);\n    if (this.ready && !this.disabled && this.options.movable) {\n      if (isNumber(x)) {\n        canvasData.left = x;\n        changed = true;\n      }\n      if (isNumber(y)) {\n        canvasData.top = y;\n        changed = true;\n      }\n      if (changed) {\n        this.renderCanvas(true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Zoom the canvas with a relative ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoom: function zoom(ratio, _originalEvent) {\n    var canvasData = this.canvasData;\n    ratio = Number(ratio);\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n    return this.zoomTo(canvasData.width * ratio / canvasData.naturalWidth, null, _originalEvent);\n  },\n  /**\n   * Zoom the canvas to an absolute ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Object} pivot - The zoom pivot point coordinate.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoomTo: function zoomTo(ratio, pivot, _originalEvent) {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var width = canvasData.width,\n      height = canvasData.height,\n      naturalWidth = canvasData.naturalWidth,\n      naturalHeight = canvasData.naturalHeight;\n    ratio = Number(ratio);\n    if (ratio >= 0 && this.ready && !this.disabled && options.zoomable) {\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n      if (dispatchEvent(this.element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: width / naturalWidth,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n      if (_originalEvent) {\n        var pointers = this.pointers;\n        var offset = getOffset(this.cropper);\n        var center = pointers && Object.keys(pointers).length ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        };\n\n        // Zoom from the triggering point of the event\n        canvasData.left -= (newWidth - width) * ((center.pageX - offset.left - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((center.pageY - offset.top - canvasData.top) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        canvasData.left -= (newWidth - width) * ((pivot.x - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((pivot.y - canvasData.top) / height);\n      } else {\n        // Zoom from the center of the canvas\n        canvasData.left -= (newWidth - width) / 2;\n        canvasData.top -= (newHeight - height) / 2;\n      }\n      canvasData.width = newWidth;\n      canvasData.height = newHeight;\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Rotate the canvas with a relative degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotate: function rotate(degree) {\n    return this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n  },\n  /**\n   * Rotate the canvas to an absolute degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotateTo: function rotateTo(degree) {\n    degree = Number(degree);\n    if (isNumber(degree) && this.ready && !this.disabled && this.options.rotatable) {\n      this.imageData.rotate = degree % 360;\n      this.renderCanvas(true, true);\n    }\n    return this;\n  },\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Cropper} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    var scaleY = this.imageData.scaleY;\n    return this.scale(_scaleX, isNumber(scaleY) ? scaleY : 1);\n  },\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    var scaleX = this.imageData.scaleX;\n    return this.scale(isNumber(scaleX) ? scaleX : 1, _scaleY);\n  },\n  /**\n   * Scale the image\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scale: function scale(scaleX) {\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var imageData = this.imageData;\n    var transformed = false;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n    if (this.ready && !this.disabled && this.options.scalable) {\n      if (isNumber(scaleX)) {\n        imageData.scaleX = scaleX;\n        transformed = true;\n      }\n      if (isNumber(scaleY)) {\n        imageData.scaleY = scaleY;\n        transformed = true;\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Get the cropped area position and size data (base on the original image)\n   * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n   * @returns {Object} The result cropped data.\n   */\n  getData: function getData() {\n    var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        x: cropBoxData.left - canvasData.left,\n        y: cropBoxData.top - canvasData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n      var ratio = imageData.width / imageData.naturalWidth;\n      forEach(data, function (n, i) {\n        data[i] = n / ratio;\n      });\n      if (rounded) {\n        // In case rounding off leads to extra 1px in right or bottom border\n        // we should round the top-left corner and the dimension (#343).\n        var bottom = Math.round(data.y + data.height);\n        var right = Math.round(data.x + data.width);\n        data.x = Math.round(data.x);\n        data.y = Math.round(data.y);\n        data.width = right - data.x;\n        data.height = bottom - data.y;\n      }\n    } else {\n      data = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n    }\n    if (options.rotatable) {\n      data.rotate = imageData.rotate || 0;\n    }\n    if (options.scalable) {\n      data.scaleX = imageData.scaleX || 1;\n      data.scaleY = imageData.scaleY || 1;\n    }\n    return data;\n  },\n  /**\n   * Set the cropped area position and size with new data\n   * @param {Object} data - The new data.\n   * @returns {Cropper} this\n   */\n  setData: function setData(data) {\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData;\n    var cropBoxData = {};\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      var transformed = false;\n      if (options.rotatable) {\n        if (isNumber(data.rotate) && data.rotate !== imageData.rotate) {\n          imageData.rotate = data.rotate;\n          transformed = true;\n        }\n      }\n      if (options.scalable) {\n        if (isNumber(data.scaleX) && data.scaleX !== imageData.scaleX) {\n          imageData.scaleX = data.scaleX;\n          transformed = true;\n        }\n        if (isNumber(data.scaleY) && data.scaleY !== imageData.scaleY) {\n          imageData.scaleY = data.scaleY;\n          transformed = true;\n        }\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n      var ratio = imageData.width / imageData.naturalWidth;\n      if (isNumber(data.x)) {\n        cropBoxData.left = data.x * ratio + canvasData.left;\n      }\n      if (isNumber(data.y)) {\n        cropBoxData.top = data.y * ratio + canvasData.top;\n      }\n      if (isNumber(data.width)) {\n        cropBoxData.width = data.width * ratio;\n      }\n      if (isNumber(data.height)) {\n        cropBoxData.height = data.height * ratio;\n      }\n      this.setCropBoxData(cropBoxData);\n    }\n    return this;\n  },\n  /**\n   * Get the container size data.\n   * @returns {Object} The result container data.\n   */\n  getContainerData: function getContainerData() {\n    return this.ready ? assign({}, this.containerData) : {};\n  },\n  /**\n   * Get the image position and size data.\n   * @returns {Object} The result image data.\n   */\n  getImageData: function getImageData() {\n    return this.sized ? assign({}, this.imageData) : {};\n  },\n  /**\n   * Get the canvas position and size data.\n   * @returns {Object} The result canvas data.\n   */\n  getCanvasData: function getCanvasData() {\n    var canvasData = this.canvasData;\n    var data = {};\n    if (this.ready) {\n      forEach(['left', 'top', 'width', 'height', 'naturalWidth', 'naturalHeight'], function (n) {\n        data[n] = canvasData[n];\n      });\n    }\n    return data;\n  },\n  /**\n   * Set the canvas position and size with new data.\n   * @param {Object} data - The new canvas data.\n   * @returns {Cropper} this\n   */\n  setCanvasData: function setCanvasData(data) {\n    var canvasData = this.canvasData;\n    var aspectRatio = canvasData.aspectRatio;\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        canvasData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        canvasData.top = data.top;\n      }\n      if (isNumber(data.width)) {\n        canvasData.width = data.width;\n        canvasData.height = data.width / aspectRatio;\n      } else if (isNumber(data.height)) {\n        canvasData.height = data.height;\n        canvasData.width = data.height * aspectRatio;\n      }\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Get the crop box position and size data.\n   * @returns {Object} The result crop box data.\n   */\n  getCropBoxData: function getCropBoxData() {\n    var cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        left: cropBoxData.left,\n        top: cropBoxData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n    }\n    return data || {};\n  },\n  /**\n   * Set the crop box position and size with new data.\n   * @param {Object} data - The new crop box data.\n   * @returns {Cropper} this\n   */\n  setCropBoxData: function setCropBoxData(data) {\n    var cropBoxData = this.cropBoxData;\n    var aspectRatio = this.options.aspectRatio;\n    var widthChanged;\n    var heightChanged;\n    if (this.ready && this.cropped && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        cropBoxData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        cropBoxData.top = data.top;\n      }\n      if (isNumber(data.width) && data.width !== cropBoxData.width) {\n        widthChanged = true;\n        cropBoxData.width = data.width;\n      }\n      if (isNumber(data.height) && data.height !== cropBoxData.height) {\n        heightChanged = true;\n        cropBoxData.height = data.height;\n      }\n      if (aspectRatio) {\n        if (widthChanged) {\n          cropBoxData.height = cropBoxData.width / aspectRatio;\n        } else if (heightChanged) {\n          cropBoxData.width = cropBoxData.height * aspectRatio;\n        }\n      }\n      this.renderCropBox();\n    }\n    return this;\n  },\n  /**\n   * Get a canvas drawn the cropped image.\n   * @param {Object} [options={}] - The config options.\n   * @returns {HTMLCanvasElement} - The result canvas.\n   */\n  getCroppedCanvas: function getCroppedCanvas() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!this.ready || !window.HTMLCanvasElement) {\n      return null;\n    }\n    var canvasData = this.canvasData;\n    var source = getSourceCanvas(this.image, this.imageData, canvasData, options);\n\n    // Returns the source canvas if it is not cropped.\n    if (!this.cropped) {\n      return source;\n    }\n    var _this$getData = this.getData(options.rounded),\n      initialX = _this$getData.x,\n      initialY = _this$getData.y,\n      initialWidth = _this$getData.width,\n      initialHeight = _this$getData.height;\n    var ratio = source.width / Math.floor(canvasData.naturalWidth);\n    if (ratio !== 1) {\n      initialX *= ratio;\n      initialY *= ratio;\n      initialWidth *= ratio;\n      initialHeight *= ratio;\n    }\n    var aspectRatio = initialWidth / initialHeight;\n    var maxSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.maxWidth || Infinity,\n      height: options.maxHeight || Infinity\n    });\n    var minSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.minWidth || 0,\n      height: options.minHeight || 0\n    }, 'cover');\n    var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.width || (ratio !== 1 ? source.width : initialWidth),\n        height: options.height || (ratio !== 1 ? source.height : initialHeight)\n      }),\n      width = _getAdjustedSizes.width,\n      height = _getAdjustedSizes.height;\n    width = Math.min(maxSizes.width, Math.max(minSizes.width, width));\n    height = Math.min(maxSizes.height, Math.max(minSizes.height, height));\n    var canvas = document.createElement('canvas');\n    var context = canvas.getContext('2d');\n    canvas.width = normalizeDecimalNumber(width);\n    canvas.height = normalizeDecimalNumber(height);\n    context.fillStyle = options.fillColor || 'transparent';\n    context.fillRect(0, 0, width, height);\n    var _options$imageSmoothi = options.imageSmoothingEnabled,\n      imageSmoothingEnabled = _options$imageSmoothi === void 0 ? true : _options$imageSmoothi,\n      imageSmoothingQuality = options.imageSmoothingQuality;\n    context.imageSmoothingEnabled = imageSmoothingEnabled;\n    if (imageSmoothingQuality) {\n      context.imageSmoothingQuality = imageSmoothingQuality;\n    }\n\n    // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D.drawImage\n    var sourceWidth = source.width;\n    var sourceHeight = source.height;\n\n    // Source canvas parameters\n    var srcX = initialX;\n    var srcY = initialY;\n    var srcWidth;\n    var srcHeight;\n\n    // Destination canvas parameters\n    var dstX;\n    var dstY;\n    var dstWidth;\n    var dstHeight;\n    if (srcX <= -initialWidth || srcX > sourceWidth) {\n      srcX = 0;\n      srcWidth = 0;\n      dstX = 0;\n      dstWidth = 0;\n    } else if (srcX <= 0) {\n      dstX = -srcX;\n      srcX = 0;\n      srcWidth = Math.min(sourceWidth, initialWidth + srcX);\n      dstWidth = srcWidth;\n    } else if (srcX <= sourceWidth) {\n      dstX = 0;\n      srcWidth = Math.min(initialWidth, sourceWidth - srcX);\n      dstWidth = srcWidth;\n    }\n    if (srcWidth <= 0 || srcY <= -initialHeight || srcY > sourceHeight) {\n      srcY = 0;\n      srcHeight = 0;\n      dstY = 0;\n      dstHeight = 0;\n    } else if (srcY <= 0) {\n      dstY = -srcY;\n      srcY = 0;\n      srcHeight = Math.min(sourceHeight, initialHeight + srcY);\n      dstHeight = srcHeight;\n    } else if (srcY <= sourceHeight) {\n      dstY = 0;\n      srcHeight = Math.min(initialHeight, sourceHeight - srcY);\n      dstHeight = srcHeight;\n    }\n    var params = [srcX, srcY, srcWidth, srcHeight];\n\n    // Avoid \"IndexSizeError\"\n    if (dstWidth > 0 && dstHeight > 0) {\n      var scale = width / initialWidth;\n      params.push(dstX * scale, dstY * scale, dstWidth * scale, dstHeight * scale);\n    }\n\n    // All the numerical parameters should be integer for `drawImage`\n    // https://github.com/fengyuanchen/cropper/issues/476\n    context.drawImage.apply(context, [source].concat(_toConsumableArray(params.map(function (param) {\n      return Math.floor(normalizeDecimalNumber(param));\n    }))));\n    return canvas;\n  },\n  /**\n   * Change the aspect ratio of the crop box.\n   * @param {number} aspectRatio - The new aspect ratio.\n   * @returns {Cropper} this\n   */\n  setAspectRatio: function setAspectRatio(aspectRatio) {\n    var options = this.options;\n    if (!this.disabled && !isUndefined(aspectRatio)) {\n      // 0 -> NaN\n      options.aspectRatio = Math.max(0, aspectRatio) || NaN;\n      if (this.ready) {\n        this.initCropBox();\n        if (this.cropped) {\n          this.renderCropBox();\n        }\n      }\n    }\n    return this;\n  },\n  /**\n   * Change the drag mode.\n   * @param {string} mode - The new drag mode.\n   * @returns {Cropper} this\n   */\n  setDragMode: function setDragMode(mode) {\n    var options = this.options,\n      dragBox = this.dragBox,\n      face = this.face;\n    if (this.ready && !this.disabled) {\n      var croppable = mode === DRAG_MODE_CROP;\n      var movable = options.movable && mode === DRAG_MODE_MOVE;\n      mode = croppable || movable ? mode : DRAG_MODE_NONE;\n      options.dragMode = mode;\n      setData(dragBox, DATA_ACTION, mode);\n      toggleClass(dragBox, CLASS_CROP, croppable);\n      toggleClass(dragBox, CLASS_MOVE, movable);\n      if (!options.cropBoxMovable) {\n        // Sync drag mode to crop box when it is not movable\n        setData(face, DATA_ACTION, mode);\n        toggleClass(face, CLASS_CROP, croppable);\n        toggleClass(face, CLASS_MOVE, movable);\n      }\n    }\n    return this;\n  }\n};\nvar AnotherCropper = WINDOW.Cropper;\nvar Cropper = /*#__PURE__*/function () {\n  /**\n   * Create a new Cropper.\n   * @param {Element} element - The target element for cropping.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Cropper(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Cropper);\n    if (!element || !REGEXP_TAG_NAME.test(element.tagName)) {\n      throw new Error('The first argument is required and must be an <img> or <canvas> element.');\n    }\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.cropped = false;\n    this.disabled = false;\n    this.pointers = {};\n    this.ready = false;\n    this.reloading = false;\n    this.replaced = false;\n    this.sized = false;\n    this.sizing = false;\n    this.init();\n  }\n  return _createClass(Cropper, [{\n    key: \"init\",\n    value: function init() {\n      var element = this.element;\n      var tagName = element.tagName.toLowerCase();\n      var url;\n      if (element[NAMESPACE]) {\n        return;\n      }\n      element[NAMESPACE] = this;\n      if (tagName === 'img') {\n        this.isImg = true;\n\n        // e.g.: \"img/picture.jpg\"\n        url = element.getAttribute('src') || '';\n        this.originalUrl = url;\n\n        // Stop when it's a blank image\n        if (!url) {\n          return;\n        }\n\n        // e.g.: \"https://example.com/img/picture.jpg\"\n        url = element.src;\n      } else if (tagName === 'canvas' && window.HTMLCanvasElement) {\n        url = element.toDataURL();\n      }\n      this.load(url);\n    }\n  }, {\n    key: \"load\",\n    value: function load(url) {\n      var _this = this;\n      if (!url) {\n        return;\n      }\n      this.url = url;\n      this.imageData = {};\n      var element = this.element,\n        options = this.options;\n      if (!options.rotatable && !options.scalable) {\n        options.checkOrientation = false;\n      }\n\n      // Only IE10+ supports Typed Arrays\n      if (!options.checkOrientation || !window.ArrayBuffer) {\n        this.clone();\n        return;\n      }\n\n      // Detect the mime type of the image directly if it is a Data URL\n      if (REGEXP_DATA_URL.test(url)) {\n        // Read ArrayBuffer from Data URL of JPEG images directly for better performance\n        if (REGEXP_DATA_URL_JPEG.test(url)) {\n          this.read(dataURLToArrayBuffer(url));\n        } else {\n          // Only a JPEG image may contains Exif Orientation information,\n          // the rest types of Data URLs are not necessary to check orientation at all.\n          this.clone();\n        }\n        return;\n      }\n\n      // 1. Detect the mime type of the image by a XMLHttpRequest.\n      // 2. Load the image as ArrayBuffer for reading orientation if its a JPEG image.\n      var xhr = new XMLHttpRequest();\n      var clone = this.clone.bind(this);\n      this.reloading = true;\n      this.xhr = xhr;\n\n      // 1. Cross origin requests are only supported for protocol schemes:\n      // http, https, data, chrome, chrome-extension.\n      // 2. Access to XMLHttpRequest from a Data URL will be blocked by CORS policy\n      // in some browsers as IE11 and Safari.\n      xhr.onabort = clone;\n      xhr.onerror = clone;\n      xhr.ontimeout = clone;\n      xhr.onprogress = function () {\n        // Abort the request directly if it not a JPEG image for better performance\n        if (xhr.getResponseHeader('content-type') !== MIME_TYPE_JPEG) {\n          xhr.abort();\n        }\n      };\n      xhr.onload = function () {\n        _this.read(xhr.response);\n      };\n      xhr.onloadend = function () {\n        _this.reloading = false;\n        _this.xhr = null;\n      };\n\n      // Bust cache when there is a \"crossOrigin\" property to avoid browser cache error\n      if (options.checkCrossOrigin && isCrossOriginURL(url) && element.crossOrigin) {\n        url = addTimestamp(url);\n      }\n\n      // The third parameter is required for avoiding side-effect (#682)\n      xhr.open('GET', url, true);\n      xhr.responseType = 'arraybuffer';\n      xhr.withCredentials = element.crossOrigin === 'use-credentials';\n      xhr.send();\n    }\n  }, {\n    key: \"read\",\n    value: function read(arrayBuffer) {\n      var options = this.options,\n        imageData = this.imageData;\n\n      // Reset the orientation value to its default value 1\n      // as some iOS browsers will render image with its orientation\n      var orientation = resetAndGetOrientation(arrayBuffer);\n      var rotate = 0;\n      var scaleX = 1;\n      var scaleY = 1;\n      if (orientation > 1) {\n        // Generate a new URL which has the default orientation value\n        this.url = arrayBufferToDataURL(arrayBuffer, MIME_TYPE_JPEG);\n        var _parseOrientation = parseOrientation(orientation);\n        rotate = _parseOrientation.rotate;\n        scaleX = _parseOrientation.scaleX;\n        scaleY = _parseOrientation.scaleY;\n      }\n      if (options.rotatable) {\n        imageData.rotate = rotate;\n      }\n      if (options.scalable) {\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n      }\n      this.clone();\n    }\n  }, {\n    key: \"clone\",\n    value: function clone() {\n      var element = this.element,\n        url = this.url;\n      var crossOrigin = element.crossOrigin;\n      var crossOriginUrl = url;\n      if (this.options.checkCrossOrigin && isCrossOriginURL(url)) {\n        if (!crossOrigin) {\n          crossOrigin = 'anonymous';\n        }\n\n        // Bust cache when there is not a \"crossOrigin\" property (#519)\n        crossOriginUrl = addTimestamp(url);\n      }\n      this.crossOrigin = crossOrigin;\n      this.crossOriginUrl = crossOriginUrl;\n      var image = document.createElement('img');\n      if (crossOrigin) {\n        image.crossOrigin = crossOrigin;\n      }\n      image.src = crossOriginUrl || url;\n      image.alt = element.alt || 'The image to crop';\n      this.image = image;\n      image.onload = this.start.bind(this);\n      image.onerror = this.stop.bind(this);\n      addClass(image, CLASS_HIDE);\n      element.parentNode.insertBefore(image, element.nextSibling);\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      var _this2 = this;\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      this.sizing = true;\n\n      // Match all browsers that use WebKit as the layout engine in iOS devices,\n      // such as Safari for iOS, Chrome for iOS, and in-app browsers.\n      var isIOSWebKit = WINDOW.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);\n      var done = function done(naturalWidth, naturalHeight) {\n        assign(_this2.imageData, {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight,\n          aspectRatio: naturalWidth / naturalHeight\n        });\n        _this2.initialImageData = assign({}, _this2.imageData);\n        _this2.sizing = false;\n        _this2.sized = true;\n        _this2.build();\n      };\n\n      // Most modern browsers (excepts iOS WebKit)\n      if (image.naturalWidth && !isIOSWebKit) {\n        done(image.naturalWidth, image.naturalHeight);\n        return;\n      }\n      var sizingImage = document.createElement('img');\n      var body = document.body || document.documentElement;\n      this.sizingImage = sizingImage;\n      sizingImage.onload = function () {\n        done(sizingImage.width, sizingImage.height);\n        if (!isIOSWebKit) {\n          body.removeChild(sizingImage);\n        }\n      };\n      sizingImage.src = image.src;\n\n      // iOS WebKit will convert the image automatically\n      // with its orientation once append it into DOM (#279)\n      if (!isIOSWebKit) {\n        sizingImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n        body.appendChild(sizingImage);\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      image.parentNode.removeChild(image);\n      this.image = null;\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (!this.sized || this.ready) {\n        return;\n      }\n      var element = this.element,\n        options = this.options,\n        image = this.image;\n\n      // Create cropper elements\n      var container = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var cropper = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var canvas = cropper.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      var dragBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-drag-box\"));\n      var cropBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-crop-box\"));\n      var face = cropBox.querySelector(\".\".concat(NAMESPACE, \"-face\"));\n      this.container = container;\n      this.cropper = cropper;\n      this.canvas = canvas;\n      this.dragBox = dragBox;\n      this.cropBox = cropBox;\n      this.viewBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-view-box\"));\n      this.face = face;\n      canvas.appendChild(image);\n\n      // Hide the original image\n      addClass(element, CLASS_HIDDEN);\n\n      // Inserts the cropper after to the current image\n      container.insertBefore(cropper, element.nextSibling);\n\n      // Show the hidden image\n      removeClass(image, CLASS_HIDE);\n      this.initPreview();\n      this.bind();\n      options.initialAspectRatio = Math.max(0, options.initialAspectRatio) || NaN;\n      options.aspectRatio = Math.max(0, options.aspectRatio) || NaN;\n      options.viewMode = Math.max(0, Math.min(3, Math.round(options.viewMode))) || 0;\n      addClass(cropBox, CLASS_HIDDEN);\n      if (!options.guides) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-dashed\")), CLASS_HIDDEN);\n      }\n      if (!options.center) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-center\")), CLASS_HIDDEN);\n      }\n      if (options.background) {\n        addClass(cropper, \"\".concat(NAMESPACE, \"-bg\"));\n      }\n      if (!options.highlight) {\n        addClass(face, CLASS_INVISIBLE);\n      }\n      if (options.cropBoxMovable) {\n        addClass(face, CLASS_MOVE);\n        setData(face, DATA_ACTION, ACTION_ALL);\n      }\n      if (!options.cropBoxResizable) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-line\")), CLASS_HIDDEN);\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-point\")), CLASS_HIDDEN);\n      }\n      this.render();\n      this.ready = true;\n      this.setDragMode(options.dragMode);\n      if (options.autoCrop) {\n        this.crop();\n      }\n      this.setData(options.data);\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n      dispatchEvent(element, EVENT_READY);\n    }\n  }, {\n    key: \"unbuild\",\n    value: function unbuild() {\n      if (!this.ready) {\n        return;\n      }\n      this.ready = false;\n      this.unbind();\n      this.resetPreview();\n      var parentNode = this.cropper.parentNode;\n      if (parentNode) {\n        parentNode.removeChild(this.cropper);\n      }\n      removeClass(this.element, CLASS_HIDDEN);\n    }\n  }, {\n    key: \"uncreate\",\n    value: function uncreate() {\n      if (this.ready) {\n        this.unbuild();\n        this.ready = false;\n        this.cropped = false;\n      } else if (this.sizing) {\n        this.sizingImage.onload = null;\n        this.sizing = false;\n        this.sized = false;\n      } else if (this.reloading) {\n        this.xhr.onabort = null;\n        this.xhr.abort();\n      } else if (this.image) {\n        this.stop();\n      }\n    }\n\n    /**\n     * Get the no conflict cropper class.\n     * @returns {Cropper} The cropper class.\n     */\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Cropper = AnotherCropper;\n      return Cropper;\n    }\n\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n}();\nassign(Cropper.prototype, render, preview, events, handlers, change, methods);\nexport { Cropper as default };", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_toPrimitive", "Symbol", "toPrimitive", "i", "call", "TypeError", "String", "Number", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_typeof", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "configurable", "writable", "key", "_createClass", "protoProps", "staticProps", "obj", "value", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "minLen", "n", "toString", "slice", "name", "test", "len", "arr2", "IS_BROWSER", "window", "document", "WINDOW", "IS_TOUCH_DEVICE", "documentElement", "HAS_POINTER_EVENT", "NAMESPACE", "ACTION_ALL", "ACTION_CROP", "ACTION_MOVE", "ACTION_ZOOM", "ACTION_EAST", "ACTION_WEST", "ACTION_SOUTH", "ACTION_NORTH", "ACTION_NORTH_EAST", "ACTION_NORTH_WEST", "ACTION_SOUTH_EAST", "ACTION_SOUTH_WEST", "CLASS_CROP", "concat", "CLASS_DISABLED", "CLASS_HIDDEN", "CLASS_HIDE", "CLASS_INVISIBLE", "CLASS_MODAL", "CLASS_MOVE", "DATA_ACTION", "DATA_PREVIEW", "DRAG_MODE_CROP", "DRAG_MODE_MOVE", "DRAG_MODE_NONE", "EVENT_CROP", "EVENT_CROP_END", "EVENT_CROP_MOVE", "EVENT_CROP_START", "EVENT_DBLCLICK", "EVENT_TOUCH_START", "EVENT_TOUCH_MOVE", "EVENT_TOUCH_END", "EVENT_POINTER_DOWN", "EVENT_POINTER_MOVE", "EVENT_POINTER_UP", "EVENT_READY", "EVENT_RESIZE", "EVENT_WHEEL", "EVENT_ZOOM", "MIME_TYPE_JPEG", "REGEXP_ACTIONS", "REGEXP_DATA_URL", "REGEXP_DATA_URL_JPEG", "REGEXP_TAG_NAME", "MIN_CONTAINER_WIDTH", "MIN_CONTAINER_HEIGHT", "DEFAULTS", "viewMode", "dragMode", "initialAspectRatio", "NaN", "aspectRatio", "data", "preview", "responsive", "restore", "checkCrossOrigin", "checkOrientation", "modal", "guides", "center", "highlight", "background", "autoCrop", "autoCropArea", "movable", "rotatable", "scalable", "zoomable", "zoomOnTouch", "zoomOnWheel", "wheelZoomRatio", "cropBoxMovable", "cropBoxResizable", "toggleDragModeOnDblclick", "minCanvasWidth", "minCanvasHeight", "minCropBoxWidth", "minCropBoxHeight", "minContainer<PERSON><PERSON><PERSON>", "minContainerHeight", "ready", "cropstart", "cropmove", "cropend", "crop", "zoom", "TEMPLATE", "isNaN", "isNumber", "isPositiveNumber", "Infinity", "isUndefined", "isObject", "hasOwnProperty", "isPlainObject", "_constructor", "error", "isFunction", "toArray", "callback", "assign", "_len", "args", "_key", "arg", "REGEXP_DECIMALS", "normalizeDecimalNumber", "times", "undefined", "Math", "round", "REGEXP_SUFFIX", "setStyle", "element", "styles", "style", "property", "hasClass", "classList", "contains", "className", "indexOf", "addClass", "elem", "add", "trim", "removeClass", "remove", "replace", "toggleClass", "added", "REGEXP_CAMEL_CASE", "toParamCase", "toLowerCase", "getData", "dataset", "getAttribute", "setData", "setAttribute", "removeData", "removeAttribute", "REGEXP_SPACES", "onceSupported", "supported", "once", "listener", "options", "get", "set", "addEventListener", "removeEventListener", "removeListener", "type", "handler", "split", "event", "listeners", "addListener", "_handler", "_element$listeners", "_len2", "_key2", "dispatchEvent", "Event", "CustomEvent", "detail", "bubbles", "cancelable", "createEvent", "initCustomEvent", "getOffset", "box", "getBoundingClientRect", "left", "pageXOffset", "clientLeft", "top", "pageYOffset", "clientTop", "location", "REGEXP_ORIGINS", "isCrossOriginURL", "url", "parts", "match", "protocol", "hostname", "port", "addTimestamp", "timestamp", "Date", "getTime", "getTransforms", "_ref", "rotate", "scaleX", "scaleY", "translateX", "translateY", "values", "transform", "join", "WebkitTransform", "msTransform", "getMaxZoomRatio", "pointers", "pointers2", "maxRatio", "pointer", "pointerId", "pointer2", "x1", "abs", "startX", "y1", "startY", "x2", "endX", "y2", "endY", "z1", "sqrt", "z2", "ratio", "getPointer", "_ref2", "endOnly", "pageX", "pageY", "end", "getPointersCenter", "count", "_ref3", "getAdjustedSizes", "_ref4", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isValidHeight", "adjustedWidth", "getRotatedSizes", "_ref5", "degree", "arc", "PI", "sinArc", "sin", "cosArc", "cos", "newWidth", "newHeight", "getSourceCanvas", "image", "_ref6", "_ref7", "_ref8", "imageAspectRatio", "imageNaturalWidth", "naturalWidth", "imageNaturalHeight", "naturalHeight", "_ref6$rotate", "_ref6$scaleX", "_ref6$scaleY", "_ref8$fillColor", "fillColor", "_ref8$imageSmoothingE", "imageSmoothingEnabled", "_ref8$imageSmoothingQ", "imageSmoothingQuality", "_ref8$maxWidth", "max<PERSON><PERSON><PERSON>", "_ref8$maxHeight", "maxHeight", "_ref8$minWidth", "min<PERSON><PERSON><PERSON>", "_ref8$minHeight", "minHeight", "canvas", "createElement", "context", "getContext", "maxSizes", "minSizes", "min", "max", "destMaxSizes", "destMinSizes", "destWidth", "destHeight", "params", "fillStyle", "fillRect", "save", "translate", "scale", "drawImage", "map", "param", "floor", "fromCharCode", "getStringFromCharCode", "dataView", "start", "str", "getUint8", "REGEXP_DATA_URL_HEAD", "dataURLToArrayBuffer", "dataURL", "base64", "binary", "atob", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uint8", "Uint8Array", "charCodeAt", "arrayBufferToDataURL", "mimeType", "chunks", "chunkSize", "subarray", "btoa", "resetAndGetOrientation", "DataView", "orientation", "littleEndian", "app1Start", "ifdStart", "byteLength", "offset", "exifIDCode", "tiffOffset", "endianness", "getUint16", "firstIFDOffset", "getUint32", "_length", "_offset", "setUint16", "parseOrientation", "render", "initContainer", "initCanvas", "initCropBox", "renderCanvas", "cropped", "renderCropBox", "container", "cropper", "containerData", "offsetWidth", "offsetHeight", "imageData", "rotated", "canvasWidth", "canvasHeight", "canvasData", "limited", "limitCanvas", "oldLeft", "oldTop", "initialCanvasData", "sizeLimited", "positionLimited", "cropBoxData", "_getAdjustedSizes", "newCanvasLeft", "newCanvasTop", "minLeft", "minTop", "maxLeft", "maxTop", "changed", "transformed", "_getRotatedSizes", "renderImage", "limitCropBox", "output", "initialCropBoxData", "maxCropBox<PERSON>idth", "maxCropBoxHeight", "face", "cropBox", "disabled", "initPreview", "crossOrigin", "crossOriginUrl", "alt", "src", "viewBox", "append<PERSON><PERSON><PERSON>", "viewBoxImage", "previews", "ownerDocument", "querySelectorAll", "querySelector", "el", "img", "html", "innerHTML", "cssText", "resetPreview", "cropBoxWidth", "cropBoxHeight", "originalWidth", "originalHeight", "getElementsByTagName", "events", "bind", "onCropStart", "cropStart", "onWheel", "wheel", "passive", "capture", "onDblclick", "dblclick", "onCropMove", "cropMove", "onCropEnd", "cropEnd", "onResize", "resize", "unbind", "handlers", "ratioX", "ratioY", "getCanvasData", "getCropBoxData", "setCanvasData", "setCropBoxData", "setDragMode", "dragBox", "_this", "delta", "preventDefault", "wheeling", "setTimeout", "deltaY", "wheelDelta", "buttons", "button", "pointerType", "ctrl<PERSON>ey", "action", "changedTouches", "touch", "identifier", "originalEvent", "cropping", "change", "right", "bottom", "renderable", "shift<PERSON>ey", "range", "x", "y", "check", "side", "move", "p", "methods", "reset", "initialImageData", "clear", "hasSameSize", "isImg", "replaced", "uncreate", "load", "enable", "disable", "destroy", "originalUrl", "offsetX", "offsetY", "_this$canvasData", "moveTo", "_originalEvent", "zoomTo", "pivot", "oldRatio", "rotateTo", "_scaleX", "_scaleY", "rounded", "getContainerData", "getImageData", "sized", "widthChanged", "heightChanged", "getCroppedCanvas", "HTMLCanvasElement", "source", "_this$getData", "initialX", "initialY", "initialWidth", "initialHeight", "_options$imageSmoothi", "sourceWidth", "sourceHeight", "srcX", "srcY", "srcWidth", "srcHeight", "dstX", "dstY", "dstWidth", "dstHeight", "setAspectRatio", "mode", "croppable", "AnotherCropper", "C<PERSON>per", "tagName", "Error", "reloading", "sizing", "init", "toDataURL", "clone", "read", "xhr", "XMLHttpRequest", "<PERSON>ab<PERSON>", "onerror", "ontimeout", "onprogress", "getResponseHeader", "abort", "onload", "response", "onloadend", "open", "responseType", "withCredentials", "send", "_parseOrientation", "stop", "parentNode", "insertBefore", "nextS<PERSON>ling", "_this2", "isIOSWebKit", "navigator", "userAgent", "done", "build", "sizingImage", "body", "<PERSON><PERSON><PERSON><PERSON>", "template", "getElementsByClassName", "unbuild", "noConflict", "setDefaults", "default"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/cropperjs/dist/cropper.esm.js"], "sourcesContent": ["/*!\n * Cropper.js v1.6.2\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2024-04-21T07:43:05.335Z\n */\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'cropper';\n\n// Actions\nvar ACTION_ALL = 'all';\nvar ACTION_CROP = 'crop';\nvar ACTION_MOVE = 'move';\nvar ACTION_ZOOM = 'zoom';\nvar ACTION_EAST = 'e';\nvar ACTION_WEST = 'w';\nvar ACTION_SOUTH = 's';\nvar ACTION_NORTH = 'n';\nvar ACTION_NORTH_EAST = 'ne';\nvar ACTION_NORTH_WEST = 'nw';\nvar ACTION_SOUTH_EAST = 'se';\nvar ACTION_SOUTH_WEST = 'sw';\n\n// Classes\nvar CLASS_CROP = \"\".concat(NAMESPACE, \"-crop\");\nvar CLASS_DISABLED = \"\".concat(NAMESPACE, \"-disabled\");\nvar CLASS_HIDDEN = \"\".concat(NAMESPACE, \"-hidden\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_MODAL = \"\".concat(NAMESPACE, \"-modal\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\");\n\n// Data keys\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\nvar DATA_PREVIEW = \"\".concat(NAMESPACE, \"Preview\");\n\n// Drag modes\nvar DRAG_MODE_CROP = 'crop';\nvar DRAG_MODE_MOVE = 'move';\nvar DRAG_MODE_NONE = 'none';\n\n// Events\nvar EVENT_CROP = 'crop';\nvar EVENT_CROP_END = 'cropend';\nvar EVENT_CROP_MOVE = 'cropmove';\nvar EVENT_CROP_START = 'cropstart';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_READY = 'ready';\nvar EVENT_RESIZE = 'resize';\nvar EVENT_WHEEL = 'wheel';\nvar EVENT_ZOOM = 'zoom';\n\n// Mime types\nvar MIME_TYPE_JPEG = 'image/jpeg';\n\n// RegExps\nvar REGEXP_ACTIONS = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;\nvar REGEXP_DATA_URL = /^data:/;\nvar REGEXP_DATA_URL_JPEG = /^data:image\\/jpeg;base64,/;\nvar REGEXP_TAG_NAME = /^img|canvas$/i;\n\n// Misc\n// Inspired by the default width and height of a canvas element.\nvar MIN_CONTAINER_WIDTH = 200;\nvar MIN_CONTAINER_HEIGHT = 100;\n\nvar DEFAULTS = {\n  // Define the view mode of the cropper\n  viewMode: 0,\n  // 0, 1, 2, 3\n\n  // Define the dragging mode of the cropper\n  dragMode: DRAG_MODE_CROP,\n  // 'crop', 'move' or 'none'\n\n  // Define the initial aspect ratio of the crop box\n  initialAspectRatio: NaN,\n  // Define the aspect ratio of the crop box\n  aspectRatio: NaN,\n  // An object with the previous cropping result data\n  data: null,\n  // A selector for adding extra containers to preview\n  preview: '',\n  // Re-render the cropper when resize the window\n  responsive: true,\n  // Restore the cropped area after resize the window\n  restore: true,\n  // Check if the current image is a cross-origin image\n  checkCrossOrigin: true,\n  // Check the current image's Exif Orientation information\n  checkOrientation: true,\n  // Show the black modal\n  modal: true,\n  // Show the dashed lines for guiding\n  guides: true,\n  // Show the center indicator for guiding\n  center: true,\n  // Show the white modal to highlight the crop box\n  highlight: true,\n  // Show the grid background\n  background: true,\n  // Enable to crop the image automatically when initialize\n  autoCrop: true,\n  // Define the percentage of automatic cropping area when initializes\n  autoCropArea: 0.8,\n  // Enable to move the image\n  movable: true,\n  // Enable to rotate the image\n  rotatable: true,\n  // Enable to scale the image\n  scalable: true,\n  // Enable to zoom the image\n  zoomable: true,\n  // Enable to zoom the image by dragging touch\n  zoomOnTouch: true,\n  // Enable to zoom the image by wheeling mouse\n  zoomOnWheel: true,\n  // Define zoom ratio when zoom the image by wheeling mouse\n  wheelZoomRatio: 0.1,\n  // Enable to move the crop box\n  cropBoxMovable: true,\n  // Enable to resize the crop box\n  cropBoxResizable: true,\n  // Toggle drag mode between \"crop\" and \"move\" when click twice on the cropper\n  toggleDragModeOnDblclick: true,\n  // Size limitation\n  minCanvasWidth: 0,\n  minCanvasHeight: 0,\n  minCropBoxWidth: 0,\n  minCropBoxHeight: 0,\n  minContainerWidth: MIN_CONTAINER_WIDTH,\n  minContainerHeight: MIN_CONTAINER_HEIGHT,\n  // Shortcuts of events\n  ready: null,\n  cropstart: null,\n  cropmove: null,\n  cropend: null,\n  crop: null,\n  zoom: null\n};\n\nvar TEMPLATE = '<div class=\"cropper-container\" touch-action=\"none\">' + '<div class=\"cropper-wrap-box\">' + '<div class=\"cropper-canvas\"></div>' + '</div>' + '<div class=\"cropper-drag-box\"></div>' + '<div class=\"cropper-crop-box\">' + '<span class=\"cropper-view-box\"></span>' + '<span class=\"cropper-dashed dashed-h\"></span>' + '<span class=\"cropper-dashed dashed-v\"></span>' + '<span class=\"cropper-center\"></span>' + '<span class=\"cropper-face\"></span>' + '<span class=\"cropper-line line-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-line line-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-line line-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-line line-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-point point-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-point point-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-point point-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-ne\" data-cropper-action=\"ne\"></span>' + '<span class=\"cropper-point point-nw\" data-cropper-action=\"nw\"></span>' + '<span class=\"cropper-point point-sw\" data-cropper-action=\"sw\"></span>' + '<span class=\"cropper-point point-se\" data-cropper-action=\"se\"></span>' + '</div>' + '</div>';\n\n/**\n * Check if the given value is not a number.\n */\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n\n/**\n * Check if the given value is a positive number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.\n */\nvar isPositiveNumber = function isPositiveNumber(value) {\n  return value > 0 && value < Infinity;\n};\n\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nvar slice = Array.prototype.slice;\n\n/**\n * Convert array-like or iterable object to an array.\n * @param {*} value - The value to convert.\n * @returns {Array} Returns a new array.\n */\nfunction toArray(value) {\n  return Array.from ? Array.from(value) : slice.call(value);\n}\n\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length) /* array-like */) {\n      toArray(data).forEach(function (value, key) {\n        callback.call(data, value, key, data);\n      });\n    } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n  return data;\n}\n\n/**\n * Extend the given object.\n * @param {*} target - The target object to extend.\n * @param {*} args - The rest objects for merging to the target object.\n * @returns {Object} The extended object.\n */\nvar assign = Object.assign || function assign(target) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (isObject(target) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          target[key] = arg[key];\n        });\n      }\n    });\n  }\n  return target;\n};\nvar REGEXP_DECIMALS = /\\.\\d*(?:0|9){12}\\d*$/;\n\n/**\n * Normalize decimal number.\n * Check out {@link https://0.30000000000000004.com/}\n * @param {number} value - The value to normalize.\n * @param {number} [times=100000000000] - The times for normalizing.\n * @returns {number} Returns the normalized number.\n */\nfunction normalizeDecimalNumber(value) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100000000000;\n  return REGEXP_DECIMALS.test(value) ? Math.round(value * times) / times : value;\n}\nvar REGEXP_SUFFIX = /^width|height|left|top|marginLeft|marginTop$/;\n\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value = \"\".concat(value, \"px\");\n    }\n    style[property] = value;\n  });\n}\n\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\nfunction hasClass(element, value) {\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\nfunction addClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n  var className = element.className.trim();\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\nfunction removeClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  }\n\n  // IE10-11 doesn't support the second parameter of `classList.toggle`\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_CAMEL_CASE = /([a-z\\d])([A-Z])/g;\n\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\nfunction toParamCase(value) {\n  return value.replace(REGEXP_CAMEL_CASE, '$1-$2').toLowerCase();\n}\n\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n  return element.getAttribute(\"data-\".concat(toParamCase(name)));\n}\n\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(toParamCase(name)), data);\n  }\n}\n\n/**\n * Remove data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to remove.\n */\nfunction removeData(element, name) {\n  if (isObject(element[name])) {\n    try {\n      delete element[name];\n    } catch (error) {\n      element[name] = undefined;\n    }\n  } else if (element.dataset) {\n    // #128 Safari not allows to delete dataset property\n    try {\n      delete element.dataset[name];\n    } catch (error) {\n      element.dataset[name] = undefined;\n    }\n  } else {\n    element.removeAttribute(\"data-\".concat(toParamCase(name)));\n  }\n}\nvar REGEXP_SPACES = /\\s\\s*/;\nvar onceSupported = function () {\n  var supported = false;\n  if (IS_BROWSER) {\n    var once = false;\n    var listener = function listener() {};\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n  return supported;\n}();\n\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n    element.removeEventListener(event, handler, options);\n  });\n}\n\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n        listeners = _element$listeners === void 0 ? {} : _element$listeners;\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        listener.apply(element, args);\n      };\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n    element.addEventListener(event, _handler, options);\n  });\n}\n\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\nfunction dispatchEvent(element, type, data) {\n  var event;\n\n  // Event and CustomEvent on IE9-11 are global objects, not constructors\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, {\n      detail: data,\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n  return element.dispatchEvent(event);\n}\n\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\nvar location = WINDOW.location;\nvar REGEXP_ORIGINS = /^(\\w+:)\\/\\/([^:/?#]*):?(\\d*)/i;\n\n/**\n * Check if the given URL is a cross origin URL.\n * @param {string} url - The target URL.\n * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.\n */\nfunction isCrossOriginURL(url) {\n  var parts = url.match(REGEXP_ORIGINS);\n  return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);\n}\n\n/**\n * Add timestamp to the given URL.\n * @param {string} url - The target URL.\n * @returns {string} The result URL.\n */\nfunction addTimestamp(url) {\n  var timestamp = \"timestamp=\".concat(new Date().getTime());\n  return url + (url.indexOf('?') === -1 ? '?' : '&') + timestamp;\n}\n\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n    scaleX = _ref.scaleX,\n    scaleY = _ref.scaleY,\n    translateX = _ref.translateX,\n    translateY = _ref.translateY;\n  var values = [];\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  }\n\n  // Rotate should come first before scale to match orientation transform\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n  var maxRatio = 0;\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n      if (Math.abs(ratio) > Math.abs(maxRatio)) {\n        maxRatio = ratio;\n      }\n    });\n  });\n  return maxRatio;\n}\n\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n    pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n      startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n\n/**\n * Get the max sizes in a rectangle under the given aspect ratio.\n * @param {Object} data - The original sizes.\n * @param {string} [type='contain'] - The adjust type.\n * @returns {Object} The result sizes.\n */\nfunction getAdjustedSizes(_ref4) {\n  var aspectRatio = _ref4.aspectRatio,\n    height = _ref4.height,\n    width = _ref4.width;\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'contain';\n  var isValidWidth = isPositiveNumber(width);\n  var isValidHeight = isPositiveNumber(height);\n  if (isValidWidth && isValidHeight) {\n    var adjustedWidth = height * aspectRatio;\n    if (type === 'contain' && adjustedWidth > width || type === 'cover' && adjustedWidth < width) {\n      height = width / aspectRatio;\n    } else {\n      width = height * aspectRatio;\n    }\n  } else if (isValidWidth) {\n    height = width / aspectRatio;\n  } else if (isValidHeight) {\n    width = height * aspectRatio;\n  }\n  return {\n    width: width,\n    height: height\n  };\n}\n\n/**\n * Get the new sizes of a rectangle after rotated.\n * @param {Object} data - The original sizes.\n * @returns {Object} The result sizes.\n */\nfunction getRotatedSizes(_ref5) {\n  var width = _ref5.width,\n    height = _ref5.height,\n    degree = _ref5.degree;\n  degree = Math.abs(degree) % 180;\n  if (degree === 90) {\n    return {\n      width: height,\n      height: width\n    };\n  }\n  var arc = degree % 90 * Math.PI / 180;\n  var sinArc = Math.sin(arc);\n  var cosArc = Math.cos(arc);\n  var newWidth = width * cosArc + height * sinArc;\n  var newHeight = width * sinArc + height * cosArc;\n  return degree > 90 ? {\n    width: newHeight,\n    height: newWidth\n  } : {\n    width: newWidth,\n    height: newHeight\n  };\n}\n\n/**\n * Get a canvas which drew the given image.\n * @param {HTMLImageElement} image - The image for drawing.\n * @param {Object} imageData - The image data.\n * @param {Object} canvasData - The canvas data.\n * @param {Object} options - The options.\n * @returns {HTMLCanvasElement} The result canvas.\n */\nfunction getSourceCanvas(image, _ref6, _ref7, _ref8) {\n  var imageAspectRatio = _ref6.aspectRatio,\n    imageNaturalWidth = _ref6.naturalWidth,\n    imageNaturalHeight = _ref6.naturalHeight,\n    _ref6$rotate = _ref6.rotate,\n    rotate = _ref6$rotate === void 0 ? 0 : _ref6$rotate,\n    _ref6$scaleX = _ref6.scaleX,\n    scaleX = _ref6$scaleX === void 0 ? 1 : _ref6$scaleX,\n    _ref6$scaleY = _ref6.scaleY,\n    scaleY = _ref6$scaleY === void 0 ? 1 : _ref6$scaleY;\n  var aspectRatio = _ref7.aspectRatio,\n    naturalWidth = _ref7.naturalWidth,\n    naturalHeight = _ref7.naturalHeight;\n  var _ref8$fillColor = _ref8.fillColor,\n    fillColor = _ref8$fillColor === void 0 ? 'transparent' : _ref8$fillColor,\n    _ref8$imageSmoothingE = _ref8.imageSmoothingEnabled,\n    imageSmoothingEnabled = _ref8$imageSmoothingE === void 0 ? true : _ref8$imageSmoothingE,\n    _ref8$imageSmoothingQ = _ref8.imageSmoothingQuality,\n    imageSmoothingQuality = _ref8$imageSmoothingQ === void 0 ? 'low' : _ref8$imageSmoothingQ,\n    _ref8$maxWidth = _ref8.maxWidth,\n    maxWidth = _ref8$maxWidth === void 0 ? Infinity : _ref8$maxWidth,\n    _ref8$maxHeight = _ref8.maxHeight,\n    maxHeight = _ref8$maxHeight === void 0 ? Infinity : _ref8$maxHeight,\n    _ref8$minWidth = _ref8.minWidth,\n    minWidth = _ref8$minWidth === void 0 ? 0 : _ref8$minWidth,\n    _ref8$minHeight = _ref8.minHeight,\n    minHeight = _ref8$minHeight === void 0 ? 0 : _ref8$minHeight;\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var maxSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var minSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var width = Math.min(maxSizes.width, Math.max(minSizes.width, naturalWidth));\n  var height = Math.min(maxSizes.height, Math.max(minSizes.height, naturalHeight));\n\n  // Note: should always use image's natural sizes for drawing as\n  // imageData.naturalWidth === canvasData.naturalHeight when rotate % 180 === 90\n  var destMaxSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var destMinSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var destWidth = Math.min(destMaxSizes.width, Math.max(destMinSizes.width, imageNaturalWidth));\n  var destHeight = Math.min(destMaxSizes.height, Math.max(destMinSizes.height, imageNaturalHeight));\n  var params = [-destWidth / 2, -destHeight / 2, destWidth, destHeight];\n  canvas.width = normalizeDecimalNumber(width);\n  canvas.height = normalizeDecimalNumber(height);\n  context.fillStyle = fillColor;\n  context.fillRect(0, 0, width, height);\n  context.save();\n  context.translate(width / 2, height / 2);\n  context.rotate(rotate * Math.PI / 180);\n  context.scale(scaleX, scaleY);\n  context.imageSmoothingEnabled = imageSmoothingEnabled;\n  context.imageSmoothingQuality = imageSmoothingQuality;\n  context.drawImage.apply(context, [image].concat(_toConsumableArray(params.map(function (param) {\n    return Math.floor(normalizeDecimalNumber(param));\n  }))));\n  context.restore();\n  return canvas;\n}\nvar fromCharCode = String.fromCharCode;\n\n/**\n * Get string from char code in data view.\n * @param {DataView} dataView - The data view for read.\n * @param {number} start - The start index.\n * @param {number} length - The read length.\n * @returns {string} The read result.\n */\nfunction getStringFromCharCode(dataView, start, length) {\n  var str = '';\n  length += start;\n  for (var i = start; i < length; i += 1) {\n    str += fromCharCode(dataView.getUint8(i));\n  }\n  return str;\n}\nvar REGEXP_DATA_URL_HEAD = /^data:.*,/;\n\n/**\n * Transform Data URL to array buffer.\n * @param {string} dataURL - The Data URL to transform.\n * @returns {ArrayBuffer} The result array buffer.\n */\nfunction dataURLToArrayBuffer(dataURL) {\n  var base64 = dataURL.replace(REGEXP_DATA_URL_HEAD, '');\n  var binary = atob(base64);\n  var arrayBuffer = new ArrayBuffer(binary.length);\n  var uint8 = new Uint8Array(arrayBuffer);\n  forEach(uint8, function (value, i) {\n    uint8[i] = binary.charCodeAt(i);\n  });\n  return arrayBuffer;\n}\n\n/**\n * Transform array buffer to Data URL.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to transform.\n * @param {string} mimeType - The mime type of the Data URL.\n * @returns {string} The result Data URL.\n */\nfunction arrayBufferToDataURL(arrayBuffer, mimeType) {\n  var chunks = [];\n\n  // Chunk Typed Array for better performance (#435)\n  var chunkSize = 8192;\n  var uint8 = new Uint8Array(arrayBuffer);\n  while (uint8.length > 0) {\n    // XXX: Babel's `toConsumableArray` helper will throw error in IE or Safari 9\n    // eslint-disable-next-line prefer-spread\n    chunks.push(fromCharCode.apply(null, toArray(uint8.subarray(0, chunkSize))));\n    uint8 = uint8.subarray(chunkSize);\n  }\n  return \"data:\".concat(mimeType, \";base64,\").concat(btoa(chunks.join('')));\n}\n\n/**\n * Get orientation value from given array buffer.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to read.\n * @returns {number} The read orientation value.\n */\nfunction resetAndGetOrientation(arrayBuffer) {\n  var dataView = new DataView(arrayBuffer);\n  var orientation;\n\n  // Ignores range error when the image does not have correct Exif information\n  try {\n    var littleEndian;\n    var app1Start;\n    var ifdStart;\n\n    // Only handle JPEG image (start by 0xFFD8)\n    if (dataView.getUint8(0) === 0xFF && dataView.getUint8(1) === 0xD8) {\n      var length = dataView.byteLength;\n      var offset = 2;\n      while (offset + 1 < length) {\n        if (dataView.getUint8(offset) === 0xFF && dataView.getUint8(offset + 1) === 0xE1) {\n          app1Start = offset;\n          break;\n        }\n        offset += 1;\n      }\n    }\n    if (app1Start) {\n      var exifIDCode = app1Start + 4;\n      var tiffOffset = app1Start + 10;\n      if (getStringFromCharCode(dataView, exifIDCode, 4) === 'Exif') {\n        var endianness = dataView.getUint16(tiffOffset);\n        littleEndian = endianness === 0x4949;\n        if (littleEndian || endianness === 0x4D4D /* bigEndian */) {\n          if (dataView.getUint16(tiffOffset + 2, littleEndian) === 0x002A) {\n            var firstIFDOffset = dataView.getUint32(tiffOffset + 4, littleEndian);\n            if (firstIFDOffset >= 0x00000008) {\n              ifdStart = tiffOffset + firstIFDOffset;\n            }\n          }\n        }\n      }\n    }\n    if (ifdStart) {\n      var _length = dataView.getUint16(ifdStart, littleEndian);\n      var _offset;\n      var i;\n      for (i = 0; i < _length; i += 1) {\n        _offset = ifdStart + i * 12 + 2;\n        if (dataView.getUint16(_offset, littleEndian) === 0x0112 /* Orientation */) {\n          // 8 is the offset of the current tag's value\n          _offset += 8;\n\n          // Get the original orientation value\n          orientation = dataView.getUint16(_offset, littleEndian);\n\n          // Override the orientation with its default value\n          dataView.setUint16(_offset, 1, littleEndian);\n          break;\n        }\n      }\n    }\n  } catch (error) {\n    orientation = 1;\n  }\n  return orientation;\n}\n\n/**\n * Parse Exif Orientation value.\n * @param {number} orientation - The orientation to parse.\n * @returns {Object} The parsed result.\n */\nfunction parseOrientation(orientation) {\n  var rotate = 0;\n  var scaleX = 1;\n  var scaleY = 1;\n  switch (orientation) {\n    // Flip horizontal\n    case 2:\n      scaleX = -1;\n      break;\n\n    // Rotate left 180°\n    case 3:\n      rotate = -180;\n      break;\n\n    // Flip vertical\n    case 4:\n      scaleY = -1;\n      break;\n\n    // Flip vertical and rotate right 90°\n    case 5:\n      rotate = 90;\n      scaleY = -1;\n      break;\n\n    // Rotate right 90°\n    case 6:\n      rotate = 90;\n      break;\n\n    // Flip horizontal and rotate right 90°\n    case 7:\n      rotate = 90;\n      scaleX = -1;\n      break;\n\n    // Rotate left 90°\n    case 8:\n      rotate = -90;\n      break;\n  }\n  return {\n    rotate: rotate,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initCanvas();\n    this.initCropBox();\n    this.renderCanvas();\n    if (this.cropped) {\n      this.renderCropBox();\n    }\n  },\n  initContainer: function initContainer() {\n    var element = this.element,\n      options = this.options,\n      container = this.container,\n      cropper = this.cropper;\n    var minWidth = Number(options.minContainerWidth);\n    var minHeight = Number(options.minContainerHeight);\n    addClass(cropper, CLASS_HIDDEN);\n    removeClass(element, CLASS_HIDDEN);\n    var containerData = {\n      width: Math.max(container.offsetWidth, minWidth >= 0 ? minWidth : MIN_CONTAINER_WIDTH),\n      height: Math.max(container.offsetHeight, minHeight >= 0 ? minHeight : MIN_CONTAINER_HEIGHT)\n    };\n    this.containerData = containerData;\n    setStyle(cropper, {\n      width: containerData.width,\n      height: containerData.height\n    });\n    addClass(element, CLASS_HIDDEN);\n    removeClass(cropper, CLASS_HIDDEN);\n  },\n  // Canvas (image wrapper)\n  initCanvas: function initCanvas() {\n    var containerData = this.containerData,\n      imageData = this.imageData;\n    var viewMode = this.options.viewMode;\n    var rotated = Math.abs(imageData.rotate) % 180 === 90;\n    var naturalWidth = rotated ? imageData.naturalHeight : imageData.naturalWidth;\n    var naturalHeight = rotated ? imageData.naturalWidth : imageData.naturalHeight;\n    var aspectRatio = naturalWidth / naturalHeight;\n    var canvasWidth = containerData.width;\n    var canvasHeight = containerData.height;\n    if (containerData.height * aspectRatio > containerData.width) {\n      if (viewMode === 3) {\n        canvasWidth = containerData.height * aspectRatio;\n      } else {\n        canvasHeight = containerData.width / aspectRatio;\n      }\n    } else if (viewMode === 3) {\n      canvasHeight = containerData.width / aspectRatio;\n    } else {\n      canvasWidth = containerData.height * aspectRatio;\n    }\n    var canvasData = {\n      aspectRatio: aspectRatio,\n      naturalWidth: naturalWidth,\n      naturalHeight: naturalHeight,\n      width: canvasWidth,\n      height: canvasHeight\n    };\n    this.canvasData = canvasData;\n    this.limited = viewMode === 1 || viewMode === 2;\n    this.limitCanvas(true, true);\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    canvasData.left = (containerData.width - canvasData.width) / 2;\n    canvasData.top = (containerData.height - canvasData.height) / 2;\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    this.initialCanvasData = assign({}, canvasData);\n  },\n  limitCanvas: function limitCanvas(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var viewMode = options.viewMode;\n    var aspectRatio = canvasData.aspectRatio;\n    var cropped = this.cropped && cropBoxData;\n    if (sizeLimited) {\n      var minCanvasWidth = Number(options.minCanvasWidth) || 0;\n      var minCanvasHeight = Number(options.minCanvasHeight) || 0;\n      if (viewMode > 1) {\n        minCanvasWidth = Math.max(minCanvasWidth, containerData.width);\n        minCanvasHeight = Math.max(minCanvasHeight, containerData.height);\n        if (viewMode === 3) {\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      } else if (viewMode > 0) {\n        if (minCanvasWidth) {\n          minCanvasWidth = Math.max(minCanvasWidth, cropped ? cropBoxData.width : 0);\n        } else if (minCanvasHeight) {\n          minCanvasHeight = Math.max(minCanvasHeight, cropped ? cropBoxData.height : 0);\n        } else if (cropped) {\n          minCanvasWidth = cropBoxData.width;\n          minCanvasHeight = cropBoxData.height;\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      }\n      var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: minCanvasWidth,\n        height: minCanvasHeight\n      });\n      minCanvasWidth = _getAdjustedSizes.width;\n      minCanvasHeight = _getAdjustedSizes.height;\n      canvasData.minWidth = minCanvasWidth;\n      canvasData.minHeight = minCanvasHeight;\n      canvasData.maxWidth = Infinity;\n      canvasData.maxHeight = Infinity;\n    }\n    if (positionLimited) {\n      if (viewMode > (cropped ? 0 : 1)) {\n        var newCanvasLeft = containerData.width - canvasData.width;\n        var newCanvasTop = containerData.height - canvasData.height;\n        canvasData.minLeft = Math.min(0, newCanvasLeft);\n        canvasData.minTop = Math.min(0, newCanvasTop);\n        canvasData.maxLeft = Math.max(0, newCanvasLeft);\n        canvasData.maxTop = Math.max(0, newCanvasTop);\n        if (cropped && this.limited) {\n          canvasData.minLeft = Math.min(cropBoxData.left, cropBoxData.left + (cropBoxData.width - canvasData.width));\n          canvasData.minTop = Math.min(cropBoxData.top, cropBoxData.top + (cropBoxData.height - canvasData.height));\n          canvasData.maxLeft = cropBoxData.left;\n          canvasData.maxTop = cropBoxData.top;\n          if (viewMode === 2) {\n            if (canvasData.width >= containerData.width) {\n              canvasData.minLeft = Math.min(0, newCanvasLeft);\n              canvasData.maxLeft = Math.max(0, newCanvasLeft);\n            }\n            if (canvasData.height >= containerData.height) {\n              canvasData.minTop = Math.min(0, newCanvasTop);\n              canvasData.maxTop = Math.max(0, newCanvasTop);\n            }\n          }\n        }\n      } else {\n        canvasData.minLeft = -canvasData.width;\n        canvasData.minTop = -canvasData.height;\n        canvasData.maxLeft = containerData.width;\n        canvasData.maxTop = containerData.height;\n      }\n    }\n  },\n  renderCanvas: function renderCanvas(changed, transformed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    if (transformed) {\n      var _getRotatedSizes = getRotatedSizes({\n          width: imageData.naturalWidth * Math.abs(imageData.scaleX || 1),\n          height: imageData.naturalHeight * Math.abs(imageData.scaleY || 1),\n          degree: imageData.rotate || 0\n        }),\n        naturalWidth = _getRotatedSizes.width,\n        naturalHeight = _getRotatedSizes.height;\n      var width = canvasData.width * (naturalWidth / canvasData.naturalWidth);\n      var height = canvasData.height * (naturalHeight / canvasData.naturalHeight);\n      canvasData.left -= (width - canvasData.width) / 2;\n      canvasData.top -= (height - canvasData.height) / 2;\n      canvasData.width = width;\n      canvasData.height = height;\n      canvasData.aspectRatio = naturalWidth / naturalHeight;\n      canvasData.naturalWidth = naturalWidth;\n      canvasData.naturalHeight = naturalHeight;\n      this.limitCanvas(true, false);\n    }\n    if (canvasData.width > canvasData.maxWidth || canvasData.width < canvasData.minWidth) {\n      canvasData.left = canvasData.oldLeft;\n    }\n    if (canvasData.height > canvasData.maxHeight || canvasData.height < canvasData.minHeight) {\n      canvasData.top = canvasData.oldTop;\n    }\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    this.limitCanvas(false, true);\n    canvasData.left = Math.min(Math.max(canvasData.left, canvasData.minLeft), canvasData.maxLeft);\n    canvasData.top = Math.min(Math.max(canvasData.top, canvasData.minTop), canvasData.maxTop);\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    setStyle(this.canvas, assign({\n      width: canvasData.width,\n      height: canvasData.height\n    }, getTransforms({\n      translateX: canvasData.left,\n      translateY: canvasData.top\n    })));\n    this.renderImage(changed);\n    if (this.cropped && this.limited) {\n      this.limitCropBox(true, true);\n    }\n  },\n  renderImage: function renderImage(changed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    var width = imageData.naturalWidth * (canvasData.width / canvasData.naturalWidth);\n    var height = imageData.naturalHeight * (canvasData.height / canvasData.naturalHeight);\n    assign(imageData, {\n      width: width,\n      height: height,\n      left: (canvasData.width - width) / 2,\n      top: (canvasData.height - height) / 2\n    });\n    setStyle(this.image, assign({\n      width: imageData.width,\n      height: imageData.height\n    }, getTransforms(assign({\n      translateX: imageData.left,\n      translateY: imageData.top\n    }, imageData))));\n    if (changed) {\n      this.output();\n    }\n  },\n  initCropBox: function initCropBox() {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var aspectRatio = options.aspectRatio || options.initialAspectRatio;\n    var autoCropArea = Number(options.autoCropArea) || 0.8;\n    var cropBoxData = {\n      width: canvasData.width,\n      height: canvasData.height\n    };\n    if (aspectRatio) {\n      if (canvasData.height * aspectRatio > canvasData.width) {\n        cropBoxData.height = cropBoxData.width / aspectRatio;\n      } else {\n        cropBoxData.width = cropBoxData.height * aspectRatio;\n      }\n    }\n    this.cropBoxData = cropBoxData;\n    this.limitCropBox(true, true);\n\n    // Initialize auto crop area\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n\n    // The width/height of auto crop area must large than \"minWidth/Height\"\n    cropBoxData.width = Math.max(cropBoxData.minWidth, cropBoxData.width * autoCropArea);\n    cropBoxData.height = Math.max(cropBoxData.minHeight, cropBoxData.height * autoCropArea);\n    cropBoxData.left = canvasData.left + (canvasData.width - cropBoxData.width) / 2;\n    cropBoxData.top = canvasData.top + (canvasData.height - cropBoxData.height) / 2;\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    this.initialCropBoxData = assign({}, cropBoxData);\n  },\n  limitCropBox: function limitCropBox(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData,\n      limited = this.limited;\n    var aspectRatio = options.aspectRatio;\n    if (sizeLimited) {\n      var minCropBoxWidth = Number(options.minCropBoxWidth) || 0;\n      var minCropBoxHeight = Number(options.minCropBoxHeight) || 0;\n      var maxCropBoxWidth = limited ? Math.min(containerData.width, canvasData.width, canvasData.width + canvasData.left, containerData.width - canvasData.left) : containerData.width;\n      var maxCropBoxHeight = limited ? Math.min(containerData.height, canvasData.height, canvasData.height + canvasData.top, containerData.height - canvasData.top) : containerData.height;\n\n      // The min/maxCropBoxWidth/Height must be less than container's width/height\n      minCropBoxWidth = Math.min(minCropBoxWidth, containerData.width);\n      minCropBoxHeight = Math.min(minCropBoxHeight, containerData.height);\n      if (aspectRatio) {\n        if (minCropBoxWidth && minCropBoxHeight) {\n          if (minCropBoxHeight * aspectRatio > minCropBoxWidth) {\n            minCropBoxHeight = minCropBoxWidth / aspectRatio;\n          } else {\n            minCropBoxWidth = minCropBoxHeight * aspectRatio;\n          }\n        } else if (minCropBoxWidth) {\n          minCropBoxHeight = minCropBoxWidth / aspectRatio;\n        } else if (minCropBoxHeight) {\n          minCropBoxWidth = minCropBoxHeight * aspectRatio;\n        }\n        if (maxCropBoxHeight * aspectRatio > maxCropBoxWidth) {\n          maxCropBoxHeight = maxCropBoxWidth / aspectRatio;\n        } else {\n          maxCropBoxWidth = maxCropBoxHeight * aspectRatio;\n        }\n      }\n\n      // The minWidth/Height must be less than maxWidth/Height\n      cropBoxData.minWidth = Math.min(minCropBoxWidth, maxCropBoxWidth);\n      cropBoxData.minHeight = Math.min(minCropBoxHeight, maxCropBoxHeight);\n      cropBoxData.maxWidth = maxCropBoxWidth;\n      cropBoxData.maxHeight = maxCropBoxHeight;\n    }\n    if (positionLimited) {\n      if (limited) {\n        cropBoxData.minLeft = Math.max(0, canvasData.left);\n        cropBoxData.minTop = Math.max(0, canvasData.top);\n        cropBoxData.maxLeft = Math.min(containerData.width, canvasData.left + canvasData.width) - cropBoxData.width;\n        cropBoxData.maxTop = Math.min(containerData.height, canvasData.top + canvasData.height) - cropBoxData.height;\n      } else {\n        cropBoxData.minLeft = 0;\n        cropBoxData.minTop = 0;\n        cropBoxData.maxLeft = containerData.width - cropBoxData.width;\n        cropBoxData.maxTop = containerData.height - cropBoxData.height;\n      }\n    }\n  },\n  renderCropBox: function renderCropBox() {\n    var options = this.options,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData;\n    if (cropBoxData.width > cropBoxData.maxWidth || cropBoxData.width < cropBoxData.minWidth) {\n      cropBoxData.left = cropBoxData.oldLeft;\n    }\n    if (cropBoxData.height > cropBoxData.maxHeight || cropBoxData.height < cropBoxData.minHeight) {\n      cropBoxData.top = cropBoxData.oldTop;\n    }\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n    this.limitCropBox(false, true);\n    cropBoxData.left = Math.min(Math.max(cropBoxData.left, cropBoxData.minLeft), cropBoxData.maxLeft);\n    cropBoxData.top = Math.min(Math.max(cropBoxData.top, cropBoxData.minTop), cropBoxData.maxTop);\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    if (options.movable && options.cropBoxMovable) {\n      // Turn to move the canvas when the crop box is equal to the container\n      setData(this.face, DATA_ACTION, cropBoxData.width >= containerData.width && cropBoxData.height >= containerData.height ? ACTION_MOVE : ACTION_ALL);\n    }\n    setStyle(this.cropBox, assign({\n      width: cropBoxData.width,\n      height: cropBoxData.height\n    }, getTransforms({\n      translateX: cropBoxData.left,\n      translateY: cropBoxData.top\n    })));\n    if (this.cropped && this.limited) {\n      this.limitCanvas(true, true);\n    }\n    if (!this.disabled) {\n      this.output();\n    }\n  },\n  output: function output() {\n    this.preview();\n    dispatchEvent(this.element, EVENT_CROP, this.getData());\n  }\n};\n\nvar preview = {\n  initPreview: function initPreview() {\n    var element = this.element,\n      crossOrigin = this.crossOrigin;\n    var preview = this.options.preview;\n    var url = crossOrigin ? this.crossOriginUrl : this.url;\n    var alt = element.alt || 'The image to preview';\n    var image = document.createElement('img');\n    if (crossOrigin) {\n      image.crossOrigin = crossOrigin;\n    }\n    image.src = url;\n    image.alt = alt;\n    this.viewBox.appendChild(image);\n    this.viewBoxImage = image;\n    if (!preview) {\n      return;\n    }\n    var previews = preview;\n    if (typeof preview === 'string') {\n      previews = element.ownerDocument.querySelectorAll(preview);\n    } else if (preview.querySelector) {\n      previews = [preview];\n    }\n    this.previews = previews;\n    forEach(previews, function (el) {\n      var img = document.createElement('img');\n\n      // Save the original size for recover\n      setData(el, DATA_PREVIEW, {\n        width: el.offsetWidth,\n        height: el.offsetHeight,\n        html: el.innerHTML\n      });\n      if (crossOrigin) {\n        img.crossOrigin = crossOrigin;\n      }\n      img.src = url;\n      img.alt = alt;\n\n      /**\n       * Override img element styles\n       * Add `display:block` to avoid margin top issue\n       * Add `height:auto` to override `height` attribute on IE8\n       * (Occur only when margin-top <= -height)\n       */\n      img.style.cssText = 'display:block;' + 'width:100%;' + 'height:auto;' + 'min-width:0!important;' + 'min-height:0!important;' + 'max-width:none!important;' + 'max-height:none!important;' + 'image-orientation:0deg!important;\"';\n      el.innerHTML = '';\n      el.appendChild(img);\n    });\n  },\n  resetPreview: function resetPreview() {\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      setStyle(element, {\n        width: data.width,\n        height: data.height\n      });\n      element.innerHTML = data.html;\n      removeData(element, DATA_PREVIEW);\n    });\n  },\n  preview: function preview() {\n    var imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var cropBoxWidth = cropBoxData.width,\n      cropBoxHeight = cropBoxData.height;\n    var width = imageData.width,\n      height = imageData.height;\n    var left = cropBoxData.left - canvasData.left - imageData.left;\n    var top = cropBoxData.top - canvasData.top - imageData.top;\n    if (!this.cropped || this.disabled) {\n      return;\n    }\n    setStyle(this.viewBoxImage, assign({\n      width: width,\n      height: height\n    }, getTransforms(assign({\n      translateX: -left,\n      translateY: -top\n    }, imageData))));\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      var originalWidth = data.width;\n      var originalHeight = data.height;\n      var newWidth = originalWidth;\n      var newHeight = originalHeight;\n      var ratio = 1;\n      if (cropBoxWidth) {\n        ratio = originalWidth / cropBoxWidth;\n        newHeight = cropBoxHeight * ratio;\n      }\n      if (cropBoxHeight && newHeight > originalHeight) {\n        ratio = originalHeight / cropBoxHeight;\n        newWidth = cropBoxWidth * ratio;\n        newHeight = originalHeight;\n      }\n      setStyle(element, {\n        width: newWidth,\n        height: newHeight\n      });\n      setStyle(element.getElementsByTagName('img')[0], assign({\n        width: width * ratio,\n        height: height * ratio\n      }, getTransforms(assign({\n        translateX: -left * ratio,\n        translateY: -top * ratio\n      }, imageData))));\n    });\n  }\n};\n\nvar events = {\n  bind: function bind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      addListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      addListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      addListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      addListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      addListener(element, EVENT_ZOOM, options.zoom);\n    }\n    addListener(cropper, EVENT_POINTER_DOWN, this.onCropStart = this.cropStart.bind(this));\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(cropper, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      addListener(cropper, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n    addListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove = this.cropMove.bind(this));\n    addListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd = this.cropEnd.bind(this));\n    if (options.responsive) {\n      addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      removeListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      removeListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      removeListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      removeListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      removeListener(element, EVENT_ZOOM, options.zoom);\n    }\n    removeListener(cropper, EVENT_POINTER_DOWN, this.onCropStart);\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(cropper, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      removeListener(cropper, EVENT_DBLCLICK, this.onDblclick);\n    }\n    removeListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove);\n    removeListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd);\n    if (options.responsive) {\n      removeListener(window, EVENT_RESIZE, this.onResize);\n    }\n  }\n};\n\nvar handlers = {\n  resize: function resize() {\n    if (this.disabled) {\n      return;\n    }\n    var options = this.options,\n      container = this.container,\n      containerData = this.containerData;\n    var ratioX = container.offsetWidth / containerData.width;\n    var ratioY = container.offsetHeight / containerData.height;\n    var ratio = Math.abs(ratioX - 1) > Math.abs(ratioY - 1) ? ratioX : ratioY;\n\n    // Resize when width changed or height changed\n    if (ratio !== 1) {\n      var canvasData;\n      var cropBoxData;\n      if (options.restore) {\n        canvasData = this.getCanvasData();\n        cropBoxData = this.getCropBoxData();\n      }\n      this.render();\n      if (options.restore) {\n        this.setCanvasData(forEach(canvasData, function (n, i) {\n          canvasData[i] = n * ratio;\n        }));\n        this.setCropBoxData(forEach(cropBoxData, function (n, i) {\n          cropBoxData[i] = n * ratio;\n        }));\n      }\n    }\n  },\n  dblclick: function dblclick() {\n    if (this.disabled || this.options.dragMode === DRAG_MODE_NONE) {\n      return;\n    }\n    this.setDragMode(hasClass(this.dragBox, CLASS_CROP) ? DRAG_MODE_MOVE : DRAG_MODE_CROP);\n  },\n  wheel: function wheel(event) {\n    var _this = this;\n    var ratio = Number(this.options.wheelZoomRatio) || 0.1;\n    var delta = 1;\n    if (this.disabled) {\n      return;\n    }\n    event.preventDefault();\n\n    // Limit wheel speed to prevent zoom too fast (#21)\n    if (this.wheeling) {\n      return;\n    }\n    this.wheeling = true;\n    setTimeout(function () {\n      _this.wheeling = false;\n    }, 50);\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n    this.zoom(-delta * ratio, event);\n  },\n  cropStart: function cropStart(event) {\n    var buttons = event.buttons,\n      button = event.button;\n    if (this.disabled\n\n    // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && (\n    // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0\n\n    // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n    var options = this.options,\n      pointers = this.pointers;\n    var action;\n    if (event.changedTouches) {\n      // Handle touch event\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      // Handle mouse event and pointer event\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n    if (Object.keys(pointers).length > 1 && options.zoomable && options.zoomOnTouch) {\n      action = ACTION_ZOOM;\n    } else {\n      action = getData(event.target, DATA_ACTION);\n    }\n    if (!REGEXP_ACTIONS.test(action)) {\n      return;\n    }\n    if (dispatchEvent(this.element, EVENT_CROP_START, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n\n    // This line is required for preventing page zooming in iOS browsers\n    event.preventDefault();\n    this.action = action;\n    this.cropping = false;\n    if (action === ACTION_CROP) {\n      this.cropping = true;\n      addClass(this.dragBox, CLASS_MODAL);\n    }\n  },\n  cropMove: function cropMove(event) {\n    var action = this.action;\n    if (this.disabled || !action) {\n      return;\n    }\n    var pointers = this.pointers;\n    event.preventDefault();\n    if (dispatchEvent(this.element, EVENT_CROP_MOVE, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        // The first parameter should not be undefined (#432)\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n    this.change(event);\n  },\n  cropEnd: function cropEnd(event) {\n    if (this.disabled) {\n      return;\n    }\n    var action = this.action,\n      pointers = this.pointers;\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        delete pointers[touch.identifier];\n      });\n    } else {\n      delete pointers[event.pointerId || 0];\n    }\n    if (!action) {\n      return;\n    }\n    event.preventDefault();\n    if (!Object.keys(pointers).length) {\n      this.action = '';\n    }\n    if (this.cropping) {\n      this.cropping = false;\n      toggleClass(this.dragBox, CLASS_MODAL, this.cropped && this.options.modal);\n    }\n    dispatchEvent(this.element, EVENT_CROP_END, {\n      originalEvent: event,\n      action: action\n    });\n  }\n};\n\nvar change = {\n  change: function change(event) {\n    var options = this.options,\n      canvasData = this.canvasData,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData,\n      pointers = this.pointers;\n    var action = this.action;\n    var aspectRatio = options.aspectRatio;\n    var left = cropBoxData.left,\n      top = cropBoxData.top,\n      width = cropBoxData.width,\n      height = cropBoxData.height;\n    var right = left + width;\n    var bottom = top + height;\n    var minLeft = 0;\n    var minTop = 0;\n    var maxWidth = containerData.width;\n    var maxHeight = containerData.height;\n    var renderable = true;\n    var offset;\n\n    // Locking aspect ratio in \"free mode\" by holding shift key\n    if (!aspectRatio && event.shiftKey) {\n      aspectRatio = width && height ? width / height : 1;\n    }\n    if (this.limited) {\n      minLeft = cropBoxData.minLeft;\n      minTop = cropBoxData.minTop;\n      maxWidth = minLeft + Math.min(containerData.width, canvasData.width, canvasData.left + canvasData.width);\n      maxHeight = minTop + Math.min(containerData.height, canvasData.height, canvasData.top + canvasData.height);\n    }\n    var pointer = pointers[Object.keys(pointers)[0]];\n    var range = {\n      x: pointer.endX - pointer.startX,\n      y: pointer.endY - pointer.startY\n    };\n    var check = function check(side) {\n      switch (side) {\n        case ACTION_EAST:\n          if (right + range.x > maxWidth) {\n            range.x = maxWidth - right;\n          }\n          break;\n        case ACTION_WEST:\n          if (left + range.x < minLeft) {\n            range.x = minLeft - left;\n          }\n          break;\n        case ACTION_NORTH:\n          if (top + range.y < minTop) {\n            range.y = minTop - top;\n          }\n          break;\n        case ACTION_SOUTH:\n          if (bottom + range.y > maxHeight) {\n            range.y = maxHeight - bottom;\n          }\n          break;\n      }\n    };\n    switch (action) {\n      // Move crop box\n      case ACTION_ALL:\n        left += range.x;\n        top += range.y;\n        break;\n\n      // Resize crop box\n      case ACTION_EAST:\n        if (range.x >= 0 && (right >= maxWidth || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_EAST);\n        width += range.x;\n        if (width < 0) {\n          action = ACTION_WEST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_NORTH:\n        if (range.y <= 0 && (top <= minTop || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_NORTH);\n        height -= range.y;\n        top += range.y;\n        if (height < 0) {\n          action = ACTION_SOUTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_WEST:\n        if (range.x <= 0 && (left <= minLeft || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_WEST);\n        width -= range.x;\n        left += range.x;\n        if (width < 0) {\n          action = ACTION_EAST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_SOUTH:\n        if (range.y >= 0 && (bottom >= maxHeight || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_SOUTH);\n        height += range.y;\n        if (height < 0) {\n          action = ACTION_NORTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_NORTH_EAST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || right >= maxWidth)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_NORTH_WEST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || left <= minLeft)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n          left += cropBoxData.width - width;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_WEST:\n        if (aspectRatio) {\n          if (range.x <= 0 && (left <= minLeft || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_WEST);\n          width -= range.x;\n          left += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_EAST:\n        if (aspectRatio) {\n          if (range.x >= 0 && (right >= maxWidth || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_EAST);\n          width += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n\n      // Move canvas\n      case ACTION_MOVE:\n        this.move(range.x, range.y);\n        renderable = false;\n        break;\n\n      // Zoom canvas\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), event);\n        renderable = false;\n        break;\n\n      // Create crop box\n      case ACTION_CROP:\n        if (!range.x || !range.y) {\n          renderable = false;\n          break;\n        }\n        offset = getOffset(this.cropper);\n        left = pointer.startX - offset.left;\n        top = pointer.startY - offset.top;\n        width = cropBoxData.minWidth;\n        height = cropBoxData.minHeight;\n        if (range.x > 0) {\n          action = range.y > 0 ? ACTION_SOUTH_EAST : ACTION_NORTH_EAST;\n        } else if (range.x < 0) {\n          left -= width;\n          action = range.y > 0 ? ACTION_SOUTH_WEST : ACTION_NORTH_WEST;\n        }\n        if (range.y < 0) {\n          top -= height;\n        }\n\n        // Show the crop box if is hidden\n        if (!this.cropped) {\n          removeClass(this.cropBox, CLASS_HIDDEN);\n          this.cropped = true;\n          if (this.limited) {\n            this.limitCropBox(true, true);\n          }\n        }\n        break;\n    }\n    if (renderable) {\n      cropBoxData.width = width;\n      cropBoxData.height = height;\n      cropBoxData.left = left;\n      cropBoxData.top = top;\n      this.action = action;\n      this.renderCropBox();\n    }\n\n    // Override\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  }\n};\n\nvar methods = {\n  // Show the crop box manually\n  crop: function crop() {\n    if (this.ready && !this.cropped && !this.disabled) {\n      this.cropped = true;\n      this.limitCropBox(true, true);\n      if (this.options.modal) {\n        addClass(this.dragBox, CLASS_MODAL);\n      }\n      removeClass(this.cropBox, CLASS_HIDDEN);\n      this.setCropBoxData(this.initialCropBoxData);\n    }\n    return this;\n  },\n  // Reset the image and crop box to their initial states\n  reset: function reset() {\n    if (this.ready && !this.disabled) {\n      this.imageData = assign({}, this.initialImageData);\n      this.canvasData = assign({}, this.initialCanvasData);\n      this.cropBoxData = assign({}, this.initialCropBoxData);\n      this.renderCanvas();\n      if (this.cropped) {\n        this.renderCropBox();\n      }\n    }\n    return this;\n  },\n  // Clear the crop box\n  clear: function clear() {\n    if (this.cropped && !this.disabled) {\n      assign(this.cropBoxData, {\n        left: 0,\n        top: 0,\n        width: 0,\n        height: 0\n      });\n      this.cropped = false;\n      this.renderCropBox();\n      this.limitCanvas(true, true);\n\n      // Render canvas after crop box rendered\n      this.renderCanvas();\n      removeClass(this.dragBox, CLASS_MODAL);\n      addClass(this.cropBox, CLASS_HIDDEN);\n    }\n    return this;\n  },\n  /**\n   * Replace the image's src and rebuild the cropper\n   * @param {string} url - The new URL.\n   * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.\n   * @returns {Cropper} this\n   */\n  replace: function replace(url) {\n    var hasSameSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!this.disabled && url) {\n      if (this.isImg) {\n        this.element.src = url;\n      }\n      if (hasSameSize) {\n        this.url = url;\n        this.image.src = url;\n        if (this.ready) {\n          this.viewBoxImage.src = url;\n          forEach(this.previews, function (element) {\n            element.getElementsByTagName('img')[0].src = url;\n          });\n        }\n      } else {\n        if (this.isImg) {\n          this.replaced = true;\n        }\n        this.options.data = null;\n        this.uncreate();\n        this.load(url);\n      }\n    }\n    return this;\n  },\n  // Enable (unfreeze) the cropper\n  enable: function enable() {\n    if (this.ready && this.disabled) {\n      this.disabled = false;\n      removeClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  // Disable (freeze) the cropper\n  disable: function disable() {\n    if (this.ready && !this.disabled) {\n      this.disabled = true;\n      addClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  /**\n   * Destroy the cropper and remove the instance from the image\n   * @returns {Cropper} this\n   */\n  destroy: function destroy() {\n    var element = this.element;\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n    element[NAMESPACE] = undefined;\n    if (this.isImg && this.replaced) {\n      element.src = this.originalUrl;\n    }\n    this.uncreate();\n    return this;\n  },\n  /**\n   * Move the canvas with relative offsets\n   * @param {number} offsetX - The relative offset distance on the x-axis.\n   * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.\n   * @returns {Cropper} this\n   */\n  move: function move(offsetX) {\n    var offsetY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : offsetX;\n    var _this$canvasData = this.canvasData,\n      left = _this$canvasData.left,\n      top = _this$canvasData.top;\n    return this.moveTo(isUndefined(offsetX) ? offsetX : left + Number(offsetX), isUndefined(offsetY) ? offsetY : top + Number(offsetY));\n  },\n  /**\n   * Move the canvas to an absolute point\n   * @param {number} x - The x-axis coordinate.\n   * @param {number} [y=x] - The y-axis coordinate.\n   * @returns {Cropper} this\n   */\n  moveTo: function moveTo(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var canvasData = this.canvasData;\n    var changed = false;\n    x = Number(x);\n    y = Number(y);\n    if (this.ready && !this.disabled && this.options.movable) {\n      if (isNumber(x)) {\n        canvasData.left = x;\n        changed = true;\n      }\n      if (isNumber(y)) {\n        canvasData.top = y;\n        changed = true;\n      }\n      if (changed) {\n        this.renderCanvas(true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Zoom the canvas with a relative ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoom: function zoom(ratio, _originalEvent) {\n    var canvasData = this.canvasData;\n    ratio = Number(ratio);\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n    return this.zoomTo(canvasData.width * ratio / canvasData.naturalWidth, null, _originalEvent);\n  },\n  /**\n   * Zoom the canvas to an absolute ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Object} pivot - The zoom pivot point coordinate.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoomTo: function zoomTo(ratio, pivot, _originalEvent) {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var width = canvasData.width,\n      height = canvasData.height,\n      naturalWidth = canvasData.naturalWidth,\n      naturalHeight = canvasData.naturalHeight;\n    ratio = Number(ratio);\n    if (ratio >= 0 && this.ready && !this.disabled && options.zoomable) {\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n      if (dispatchEvent(this.element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: width / naturalWidth,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n      if (_originalEvent) {\n        var pointers = this.pointers;\n        var offset = getOffset(this.cropper);\n        var center = pointers && Object.keys(pointers).length ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        };\n\n        // Zoom from the triggering point of the event\n        canvasData.left -= (newWidth - width) * ((center.pageX - offset.left - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((center.pageY - offset.top - canvasData.top) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        canvasData.left -= (newWidth - width) * ((pivot.x - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((pivot.y - canvasData.top) / height);\n      } else {\n        // Zoom from the center of the canvas\n        canvasData.left -= (newWidth - width) / 2;\n        canvasData.top -= (newHeight - height) / 2;\n      }\n      canvasData.width = newWidth;\n      canvasData.height = newHeight;\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Rotate the canvas with a relative degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotate: function rotate(degree) {\n    return this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n  },\n  /**\n   * Rotate the canvas to an absolute degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotateTo: function rotateTo(degree) {\n    degree = Number(degree);\n    if (isNumber(degree) && this.ready && !this.disabled && this.options.rotatable) {\n      this.imageData.rotate = degree % 360;\n      this.renderCanvas(true, true);\n    }\n    return this;\n  },\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Cropper} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    var scaleY = this.imageData.scaleY;\n    return this.scale(_scaleX, isNumber(scaleY) ? scaleY : 1);\n  },\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    var scaleX = this.imageData.scaleX;\n    return this.scale(isNumber(scaleX) ? scaleX : 1, _scaleY);\n  },\n  /**\n   * Scale the image\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scale: function scale(scaleX) {\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var imageData = this.imageData;\n    var transformed = false;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n    if (this.ready && !this.disabled && this.options.scalable) {\n      if (isNumber(scaleX)) {\n        imageData.scaleX = scaleX;\n        transformed = true;\n      }\n      if (isNumber(scaleY)) {\n        imageData.scaleY = scaleY;\n        transformed = true;\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Get the cropped area position and size data (base on the original image)\n   * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n   * @returns {Object} The result cropped data.\n   */\n  getData: function getData() {\n    var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        x: cropBoxData.left - canvasData.left,\n        y: cropBoxData.top - canvasData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n      var ratio = imageData.width / imageData.naturalWidth;\n      forEach(data, function (n, i) {\n        data[i] = n / ratio;\n      });\n      if (rounded) {\n        // In case rounding off leads to extra 1px in right or bottom border\n        // we should round the top-left corner and the dimension (#343).\n        var bottom = Math.round(data.y + data.height);\n        var right = Math.round(data.x + data.width);\n        data.x = Math.round(data.x);\n        data.y = Math.round(data.y);\n        data.width = right - data.x;\n        data.height = bottom - data.y;\n      }\n    } else {\n      data = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n    }\n    if (options.rotatable) {\n      data.rotate = imageData.rotate || 0;\n    }\n    if (options.scalable) {\n      data.scaleX = imageData.scaleX || 1;\n      data.scaleY = imageData.scaleY || 1;\n    }\n    return data;\n  },\n  /**\n   * Set the cropped area position and size with new data\n   * @param {Object} data - The new data.\n   * @returns {Cropper} this\n   */\n  setData: function setData(data) {\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData;\n    var cropBoxData = {};\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      var transformed = false;\n      if (options.rotatable) {\n        if (isNumber(data.rotate) && data.rotate !== imageData.rotate) {\n          imageData.rotate = data.rotate;\n          transformed = true;\n        }\n      }\n      if (options.scalable) {\n        if (isNumber(data.scaleX) && data.scaleX !== imageData.scaleX) {\n          imageData.scaleX = data.scaleX;\n          transformed = true;\n        }\n        if (isNumber(data.scaleY) && data.scaleY !== imageData.scaleY) {\n          imageData.scaleY = data.scaleY;\n          transformed = true;\n        }\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n      var ratio = imageData.width / imageData.naturalWidth;\n      if (isNumber(data.x)) {\n        cropBoxData.left = data.x * ratio + canvasData.left;\n      }\n      if (isNumber(data.y)) {\n        cropBoxData.top = data.y * ratio + canvasData.top;\n      }\n      if (isNumber(data.width)) {\n        cropBoxData.width = data.width * ratio;\n      }\n      if (isNumber(data.height)) {\n        cropBoxData.height = data.height * ratio;\n      }\n      this.setCropBoxData(cropBoxData);\n    }\n    return this;\n  },\n  /**\n   * Get the container size data.\n   * @returns {Object} The result container data.\n   */\n  getContainerData: function getContainerData() {\n    return this.ready ? assign({}, this.containerData) : {};\n  },\n  /**\n   * Get the image position and size data.\n   * @returns {Object} The result image data.\n   */\n  getImageData: function getImageData() {\n    return this.sized ? assign({}, this.imageData) : {};\n  },\n  /**\n   * Get the canvas position and size data.\n   * @returns {Object} The result canvas data.\n   */\n  getCanvasData: function getCanvasData() {\n    var canvasData = this.canvasData;\n    var data = {};\n    if (this.ready) {\n      forEach(['left', 'top', 'width', 'height', 'naturalWidth', 'naturalHeight'], function (n) {\n        data[n] = canvasData[n];\n      });\n    }\n    return data;\n  },\n  /**\n   * Set the canvas position and size with new data.\n   * @param {Object} data - The new canvas data.\n   * @returns {Cropper} this\n   */\n  setCanvasData: function setCanvasData(data) {\n    var canvasData = this.canvasData;\n    var aspectRatio = canvasData.aspectRatio;\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        canvasData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        canvasData.top = data.top;\n      }\n      if (isNumber(data.width)) {\n        canvasData.width = data.width;\n        canvasData.height = data.width / aspectRatio;\n      } else if (isNumber(data.height)) {\n        canvasData.height = data.height;\n        canvasData.width = data.height * aspectRatio;\n      }\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Get the crop box position and size data.\n   * @returns {Object} The result crop box data.\n   */\n  getCropBoxData: function getCropBoxData() {\n    var cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        left: cropBoxData.left,\n        top: cropBoxData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n    }\n    return data || {};\n  },\n  /**\n   * Set the crop box position and size with new data.\n   * @param {Object} data - The new crop box data.\n   * @returns {Cropper} this\n   */\n  setCropBoxData: function setCropBoxData(data) {\n    var cropBoxData = this.cropBoxData;\n    var aspectRatio = this.options.aspectRatio;\n    var widthChanged;\n    var heightChanged;\n    if (this.ready && this.cropped && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        cropBoxData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        cropBoxData.top = data.top;\n      }\n      if (isNumber(data.width) && data.width !== cropBoxData.width) {\n        widthChanged = true;\n        cropBoxData.width = data.width;\n      }\n      if (isNumber(data.height) && data.height !== cropBoxData.height) {\n        heightChanged = true;\n        cropBoxData.height = data.height;\n      }\n      if (aspectRatio) {\n        if (widthChanged) {\n          cropBoxData.height = cropBoxData.width / aspectRatio;\n        } else if (heightChanged) {\n          cropBoxData.width = cropBoxData.height * aspectRatio;\n        }\n      }\n      this.renderCropBox();\n    }\n    return this;\n  },\n  /**\n   * Get a canvas drawn the cropped image.\n   * @param {Object} [options={}] - The config options.\n   * @returns {HTMLCanvasElement} - The result canvas.\n   */\n  getCroppedCanvas: function getCroppedCanvas() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!this.ready || !window.HTMLCanvasElement) {\n      return null;\n    }\n    var canvasData = this.canvasData;\n    var source = getSourceCanvas(this.image, this.imageData, canvasData, options);\n\n    // Returns the source canvas if it is not cropped.\n    if (!this.cropped) {\n      return source;\n    }\n    var _this$getData = this.getData(options.rounded),\n      initialX = _this$getData.x,\n      initialY = _this$getData.y,\n      initialWidth = _this$getData.width,\n      initialHeight = _this$getData.height;\n    var ratio = source.width / Math.floor(canvasData.naturalWidth);\n    if (ratio !== 1) {\n      initialX *= ratio;\n      initialY *= ratio;\n      initialWidth *= ratio;\n      initialHeight *= ratio;\n    }\n    var aspectRatio = initialWidth / initialHeight;\n    var maxSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.maxWidth || Infinity,\n      height: options.maxHeight || Infinity\n    });\n    var minSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.minWidth || 0,\n      height: options.minHeight || 0\n    }, 'cover');\n    var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.width || (ratio !== 1 ? source.width : initialWidth),\n        height: options.height || (ratio !== 1 ? source.height : initialHeight)\n      }),\n      width = _getAdjustedSizes.width,\n      height = _getAdjustedSizes.height;\n    width = Math.min(maxSizes.width, Math.max(minSizes.width, width));\n    height = Math.min(maxSizes.height, Math.max(minSizes.height, height));\n    var canvas = document.createElement('canvas');\n    var context = canvas.getContext('2d');\n    canvas.width = normalizeDecimalNumber(width);\n    canvas.height = normalizeDecimalNumber(height);\n    context.fillStyle = options.fillColor || 'transparent';\n    context.fillRect(0, 0, width, height);\n    var _options$imageSmoothi = options.imageSmoothingEnabled,\n      imageSmoothingEnabled = _options$imageSmoothi === void 0 ? true : _options$imageSmoothi,\n      imageSmoothingQuality = options.imageSmoothingQuality;\n    context.imageSmoothingEnabled = imageSmoothingEnabled;\n    if (imageSmoothingQuality) {\n      context.imageSmoothingQuality = imageSmoothingQuality;\n    }\n\n    // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D.drawImage\n    var sourceWidth = source.width;\n    var sourceHeight = source.height;\n\n    // Source canvas parameters\n    var srcX = initialX;\n    var srcY = initialY;\n    var srcWidth;\n    var srcHeight;\n\n    // Destination canvas parameters\n    var dstX;\n    var dstY;\n    var dstWidth;\n    var dstHeight;\n    if (srcX <= -initialWidth || srcX > sourceWidth) {\n      srcX = 0;\n      srcWidth = 0;\n      dstX = 0;\n      dstWidth = 0;\n    } else if (srcX <= 0) {\n      dstX = -srcX;\n      srcX = 0;\n      srcWidth = Math.min(sourceWidth, initialWidth + srcX);\n      dstWidth = srcWidth;\n    } else if (srcX <= sourceWidth) {\n      dstX = 0;\n      srcWidth = Math.min(initialWidth, sourceWidth - srcX);\n      dstWidth = srcWidth;\n    }\n    if (srcWidth <= 0 || srcY <= -initialHeight || srcY > sourceHeight) {\n      srcY = 0;\n      srcHeight = 0;\n      dstY = 0;\n      dstHeight = 0;\n    } else if (srcY <= 0) {\n      dstY = -srcY;\n      srcY = 0;\n      srcHeight = Math.min(sourceHeight, initialHeight + srcY);\n      dstHeight = srcHeight;\n    } else if (srcY <= sourceHeight) {\n      dstY = 0;\n      srcHeight = Math.min(initialHeight, sourceHeight - srcY);\n      dstHeight = srcHeight;\n    }\n    var params = [srcX, srcY, srcWidth, srcHeight];\n\n    // Avoid \"IndexSizeError\"\n    if (dstWidth > 0 && dstHeight > 0) {\n      var scale = width / initialWidth;\n      params.push(dstX * scale, dstY * scale, dstWidth * scale, dstHeight * scale);\n    }\n\n    // All the numerical parameters should be integer for `drawImage`\n    // https://github.com/fengyuanchen/cropper/issues/476\n    context.drawImage.apply(context, [source].concat(_toConsumableArray(params.map(function (param) {\n      return Math.floor(normalizeDecimalNumber(param));\n    }))));\n    return canvas;\n  },\n  /**\n   * Change the aspect ratio of the crop box.\n   * @param {number} aspectRatio - The new aspect ratio.\n   * @returns {Cropper} this\n   */\n  setAspectRatio: function setAspectRatio(aspectRatio) {\n    var options = this.options;\n    if (!this.disabled && !isUndefined(aspectRatio)) {\n      // 0 -> NaN\n      options.aspectRatio = Math.max(0, aspectRatio) || NaN;\n      if (this.ready) {\n        this.initCropBox();\n        if (this.cropped) {\n          this.renderCropBox();\n        }\n      }\n    }\n    return this;\n  },\n  /**\n   * Change the drag mode.\n   * @param {string} mode - The new drag mode.\n   * @returns {Cropper} this\n   */\n  setDragMode: function setDragMode(mode) {\n    var options = this.options,\n      dragBox = this.dragBox,\n      face = this.face;\n    if (this.ready && !this.disabled) {\n      var croppable = mode === DRAG_MODE_CROP;\n      var movable = options.movable && mode === DRAG_MODE_MOVE;\n      mode = croppable || movable ? mode : DRAG_MODE_NONE;\n      options.dragMode = mode;\n      setData(dragBox, DATA_ACTION, mode);\n      toggleClass(dragBox, CLASS_CROP, croppable);\n      toggleClass(dragBox, CLASS_MOVE, movable);\n      if (!options.cropBoxMovable) {\n        // Sync drag mode to crop box when it is not movable\n        setData(face, DATA_ACTION, mode);\n        toggleClass(face, CLASS_CROP, croppable);\n        toggleClass(face, CLASS_MOVE, movable);\n      }\n    }\n    return this;\n  }\n};\n\nvar AnotherCropper = WINDOW.Cropper;\nvar Cropper = /*#__PURE__*/function () {\n  /**\n   * Create a new Cropper.\n   * @param {Element} element - The target element for cropping.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Cropper(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Cropper);\n    if (!element || !REGEXP_TAG_NAME.test(element.tagName)) {\n      throw new Error('The first argument is required and must be an <img> or <canvas> element.');\n    }\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.cropped = false;\n    this.disabled = false;\n    this.pointers = {};\n    this.ready = false;\n    this.reloading = false;\n    this.replaced = false;\n    this.sized = false;\n    this.sizing = false;\n    this.init();\n  }\n  return _createClass(Cropper, [{\n    key: \"init\",\n    value: function init() {\n      var element = this.element;\n      var tagName = element.tagName.toLowerCase();\n      var url;\n      if (element[NAMESPACE]) {\n        return;\n      }\n      element[NAMESPACE] = this;\n      if (tagName === 'img') {\n        this.isImg = true;\n\n        // e.g.: \"img/picture.jpg\"\n        url = element.getAttribute('src') || '';\n        this.originalUrl = url;\n\n        // Stop when it's a blank image\n        if (!url) {\n          return;\n        }\n\n        // e.g.: \"https://example.com/img/picture.jpg\"\n        url = element.src;\n      } else if (tagName === 'canvas' && window.HTMLCanvasElement) {\n        url = element.toDataURL();\n      }\n      this.load(url);\n    }\n  }, {\n    key: \"load\",\n    value: function load(url) {\n      var _this = this;\n      if (!url) {\n        return;\n      }\n      this.url = url;\n      this.imageData = {};\n      var element = this.element,\n        options = this.options;\n      if (!options.rotatable && !options.scalable) {\n        options.checkOrientation = false;\n      }\n\n      // Only IE10+ supports Typed Arrays\n      if (!options.checkOrientation || !window.ArrayBuffer) {\n        this.clone();\n        return;\n      }\n\n      // Detect the mime type of the image directly if it is a Data URL\n      if (REGEXP_DATA_URL.test(url)) {\n        // Read ArrayBuffer from Data URL of JPEG images directly for better performance\n        if (REGEXP_DATA_URL_JPEG.test(url)) {\n          this.read(dataURLToArrayBuffer(url));\n        } else {\n          // Only a JPEG image may contains Exif Orientation information,\n          // the rest types of Data URLs are not necessary to check orientation at all.\n          this.clone();\n        }\n        return;\n      }\n\n      // 1. Detect the mime type of the image by a XMLHttpRequest.\n      // 2. Load the image as ArrayBuffer for reading orientation if its a JPEG image.\n      var xhr = new XMLHttpRequest();\n      var clone = this.clone.bind(this);\n      this.reloading = true;\n      this.xhr = xhr;\n\n      // 1. Cross origin requests are only supported for protocol schemes:\n      // http, https, data, chrome, chrome-extension.\n      // 2. Access to XMLHttpRequest from a Data URL will be blocked by CORS policy\n      // in some browsers as IE11 and Safari.\n      xhr.onabort = clone;\n      xhr.onerror = clone;\n      xhr.ontimeout = clone;\n      xhr.onprogress = function () {\n        // Abort the request directly if it not a JPEG image for better performance\n        if (xhr.getResponseHeader('content-type') !== MIME_TYPE_JPEG) {\n          xhr.abort();\n        }\n      };\n      xhr.onload = function () {\n        _this.read(xhr.response);\n      };\n      xhr.onloadend = function () {\n        _this.reloading = false;\n        _this.xhr = null;\n      };\n\n      // Bust cache when there is a \"crossOrigin\" property to avoid browser cache error\n      if (options.checkCrossOrigin && isCrossOriginURL(url) && element.crossOrigin) {\n        url = addTimestamp(url);\n      }\n\n      // The third parameter is required for avoiding side-effect (#682)\n      xhr.open('GET', url, true);\n      xhr.responseType = 'arraybuffer';\n      xhr.withCredentials = element.crossOrigin === 'use-credentials';\n      xhr.send();\n    }\n  }, {\n    key: \"read\",\n    value: function read(arrayBuffer) {\n      var options = this.options,\n        imageData = this.imageData;\n\n      // Reset the orientation value to its default value 1\n      // as some iOS browsers will render image with its orientation\n      var orientation = resetAndGetOrientation(arrayBuffer);\n      var rotate = 0;\n      var scaleX = 1;\n      var scaleY = 1;\n      if (orientation > 1) {\n        // Generate a new URL which has the default orientation value\n        this.url = arrayBufferToDataURL(arrayBuffer, MIME_TYPE_JPEG);\n        var _parseOrientation = parseOrientation(orientation);\n        rotate = _parseOrientation.rotate;\n        scaleX = _parseOrientation.scaleX;\n        scaleY = _parseOrientation.scaleY;\n      }\n      if (options.rotatable) {\n        imageData.rotate = rotate;\n      }\n      if (options.scalable) {\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n      }\n      this.clone();\n    }\n  }, {\n    key: \"clone\",\n    value: function clone() {\n      var element = this.element,\n        url = this.url;\n      var crossOrigin = element.crossOrigin;\n      var crossOriginUrl = url;\n      if (this.options.checkCrossOrigin && isCrossOriginURL(url)) {\n        if (!crossOrigin) {\n          crossOrigin = 'anonymous';\n        }\n\n        // Bust cache when there is not a \"crossOrigin\" property (#519)\n        crossOriginUrl = addTimestamp(url);\n      }\n      this.crossOrigin = crossOrigin;\n      this.crossOriginUrl = crossOriginUrl;\n      var image = document.createElement('img');\n      if (crossOrigin) {\n        image.crossOrigin = crossOrigin;\n      }\n      image.src = crossOriginUrl || url;\n      image.alt = element.alt || 'The image to crop';\n      this.image = image;\n      image.onload = this.start.bind(this);\n      image.onerror = this.stop.bind(this);\n      addClass(image, CLASS_HIDE);\n      element.parentNode.insertBefore(image, element.nextSibling);\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      var _this2 = this;\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      this.sizing = true;\n\n      // Match all browsers that use WebKit as the layout engine in iOS devices,\n      // such as Safari for iOS, Chrome for iOS, and in-app browsers.\n      var isIOSWebKit = WINDOW.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);\n      var done = function done(naturalWidth, naturalHeight) {\n        assign(_this2.imageData, {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight,\n          aspectRatio: naturalWidth / naturalHeight\n        });\n        _this2.initialImageData = assign({}, _this2.imageData);\n        _this2.sizing = false;\n        _this2.sized = true;\n        _this2.build();\n      };\n\n      // Most modern browsers (excepts iOS WebKit)\n      if (image.naturalWidth && !isIOSWebKit) {\n        done(image.naturalWidth, image.naturalHeight);\n        return;\n      }\n      var sizingImage = document.createElement('img');\n      var body = document.body || document.documentElement;\n      this.sizingImage = sizingImage;\n      sizingImage.onload = function () {\n        done(sizingImage.width, sizingImage.height);\n        if (!isIOSWebKit) {\n          body.removeChild(sizingImage);\n        }\n      };\n      sizingImage.src = image.src;\n\n      // iOS WebKit will convert the image automatically\n      // with its orientation once append it into DOM (#279)\n      if (!isIOSWebKit) {\n        sizingImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n        body.appendChild(sizingImage);\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      image.parentNode.removeChild(image);\n      this.image = null;\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (!this.sized || this.ready) {\n        return;\n      }\n      var element = this.element,\n        options = this.options,\n        image = this.image;\n\n      // Create cropper elements\n      var container = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var cropper = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var canvas = cropper.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      var dragBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-drag-box\"));\n      var cropBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-crop-box\"));\n      var face = cropBox.querySelector(\".\".concat(NAMESPACE, \"-face\"));\n      this.container = container;\n      this.cropper = cropper;\n      this.canvas = canvas;\n      this.dragBox = dragBox;\n      this.cropBox = cropBox;\n      this.viewBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-view-box\"));\n      this.face = face;\n      canvas.appendChild(image);\n\n      // Hide the original image\n      addClass(element, CLASS_HIDDEN);\n\n      // Inserts the cropper after to the current image\n      container.insertBefore(cropper, element.nextSibling);\n\n      // Show the hidden image\n      removeClass(image, CLASS_HIDE);\n      this.initPreview();\n      this.bind();\n      options.initialAspectRatio = Math.max(0, options.initialAspectRatio) || NaN;\n      options.aspectRatio = Math.max(0, options.aspectRatio) || NaN;\n      options.viewMode = Math.max(0, Math.min(3, Math.round(options.viewMode))) || 0;\n      addClass(cropBox, CLASS_HIDDEN);\n      if (!options.guides) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-dashed\")), CLASS_HIDDEN);\n      }\n      if (!options.center) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-center\")), CLASS_HIDDEN);\n      }\n      if (options.background) {\n        addClass(cropper, \"\".concat(NAMESPACE, \"-bg\"));\n      }\n      if (!options.highlight) {\n        addClass(face, CLASS_INVISIBLE);\n      }\n      if (options.cropBoxMovable) {\n        addClass(face, CLASS_MOVE);\n        setData(face, DATA_ACTION, ACTION_ALL);\n      }\n      if (!options.cropBoxResizable) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-line\")), CLASS_HIDDEN);\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-point\")), CLASS_HIDDEN);\n      }\n      this.render();\n      this.ready = true;\n      this.setDragMode(options.dragMode);\n      if (options.autoCrop) {\n        this.crop();\n      }\n      this.setData(options.data);\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n      dispatchEvent(element, EVENT_READY);\n    }\n  }, {\n    key: \"unbuild\",\n    value: function unbuild() {\n      if (!this.ready) {\n        return;\n      }\n      this.ready = false;\n      this.unbind();\n      this.resetPreview();\n      var parentNode = this.cropper.parentNode;\n      if (parentNode) {\n        parentNode.removeChild(this.cropper);\n      }\n      removeClass(this.element, CLASS_HIDDEN);\n    }\n  }, {\n    key: \"uncreate\",\n    value: function uncreate() {\n      if (this.ready) {\n        this.unbuild();\n        this.ready = false;\n        this.cropped = false;\n      } else if (this.sizing) {\n        this.sizingImage.onload = null;\n        this.sizing = false;\n        this.sized = false;\n      } else if (this.reloading) {\n        this.xhr.onabort = null;\n        this.xhr.abort();\n      } else if (this.image) {\n        this.stop();\n      }\n    }\n\n    /**\n     * Get the no conflict cropper class.\n     * @returns {Cropper} The cropper class.\n     */\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Cropper = AnotherCropper;\n      return Cropper;\n    }\n\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n}();\nassign(Cropper.prototype, render, preview, events, handlers, change, methods);\n\nexport { Cropper as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EACtB,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAChC,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IACvCC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAC9B,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IACzD,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EACzB;EACA,OAAOJ,CAAC;AACV;AACA,SAASU,cAAcA,CAACZ,CAAC,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IACzC,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAChDA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAClDe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAChJE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EACJ;EACA,OAAOD,CAAC;AACV;AACA,SAASoB,YAAYA,CAAClB,CAAC,EAAED,CAAC,EAAE;EAC1B,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EACxC,IAAIF,CAAC,GAAGE,CAAC,CAACmB,MAAM,CAACC,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKtB,CAAC,EAAE;IAChB,IAAIuB,CAAC,GAAGvB,CAAC,CAACwB,IAAI,CAACtB,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAClC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKxB,CAAC,GAAGyB,MAAM,GAAGC,MAAM,EAAEzB,CAAC,CAAC;AAC9C;AACA,SAAS0B,cAAcA,CAAC1B,CAAC,EAAE;EACzB,IAAIqB,CAAC,GAAGH,YAAY,CAAClB,CAAC,EAAE,QAAQ,CAAC;EACjC,OAAO,QAAQ,IAAI,OAAOqB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC1C;AACA,SAASM,OAAOA,CAACvB,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOuB,OAAO,GAAG,UAAU,IAAI,OAAOR,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACS,QAAQ,GAAG,UAAUxB,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOe,MAAM,IAAIf,CAAC,CAACyB,WAAW,KAAKV,MAAM,IAAIf,CAAC,KAAKe,MAAM,CAACW,SAAS,GAAG,QAAQ,GAAG,OAAO1B,CAAC;EACrH,CAAC,EAAEuB,OAAO,CAACvB,CAAC,CAAC;AACf;AACA,SAAS2B,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIV,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AACA,SAASW,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACxB,MAAM,EAAES,CAAC,EAAE,EAAE;IACrC,IAAIgB,UAAU,GAAGD,KAAK,CAACf,CAAC,CAAC;IACzBgB,UAAU,CAAC9B,UAAU,GAAG8B,UAAU,CAAC9B,UAAU,IAAI,KAAK;IACtD8B,UAAU,CAACC,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IACrDtC,MAAM,CAACgB,cAAc,CAACkB,MAAM,EAAET,cAAc,CAACW,UAAU,CAACG,GAAG,CAAC,EAAEH,UAAU,CAAC;EAC3E;AACF;AACA,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAER,iBAAiB,CAACD,WAAW,CAACH,SAAS,EAAEY,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAET,iBAAiB,CAACD,WAAW,EAAEU,WAAW,CAAC;EAC5D1C,MAAM,CAACgB,cAAc,CAACgB,WAAW,EAAE,WAAW,EAAE;IAC9CM,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAON,WAAW;AACpB;AACA,SAASnB,eAAeA,CAAC8B,GAAG,EAAEJ,GAAG,EAAEK,KAAK,EAAE;EACxCL,GAAG,GAAGd,cAAc,CAACc,GAAG,CAAC;EACzB,IAAIA,GAAG,IAAII,GAAG,EAAE;IACd3C,MAAM,CAACgB,cAAc,CAAC2B,GAAG,EAAEJ,GAAG,EAAE;MAC9BK,KAAK,EAAEA,KAAK;MACZtC,UAAU,EAAE,IAAI;MAChB+B,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLK,GAAG,CAACJ,GAAG,CAAC,GAAGK,KAAK;EAClB;EACA,OAAOD,GAAG;AACZ;AACA,SAASE,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AACrH;AACA,SAASH,kBAAkBA,CAACD,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AACvD;AACA,SAASE,gBAAgBA,CAACM,IAAI,EAAE;EAC9B,IAAI,OAAOpC,MAAM,KAAK,WAAW,IAAIoC,IAAI,CAACpC,MAAM,CAACS,QAAQ,CAAC,IAAI,IAAI,IAAI2B,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;AAC3H;AACA,SAASL,2BAA2BA,CAAC9C,CAAC,EAAEqD,MAAM,EAAE;EAC9C,IAAI,CAACrD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOkD,iBAAiB,CAAClD,CAAC,EAAEqD,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGzD,MAAM,CAAC6B,SAAS,CAAC6B,QAAQ,CAACrC,IAAI,CAAClB,CAAC,CAAC,CAACwD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIF,CAAC,KAAK,QAAQ,IAAItD,CAAC,CAACyB,WAAW,EAAE6B,CAAC,GAAGtD,CAAC,CAACyB,WAAW,CAACgC,IAAI;EAC3D,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAON,KAAK,CAACI,IAAI,CAACpD,CAAC,CAAC;EACpD,IAAIsD,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACI,IAAI,CAACJ,CAAC,CAAC,EAAE,OAAOJ,iBAAiB,CAAClD,CAAC,EAAEqD,MAAM,CAAC;AAClH;AACA,SAASH,iBAAiBA,CAACP,GAAG,EAAEgB,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGhB,GAAG,CAACnC,MAAM,EAAEmD,GAAG,GAAGhB,GAAG,CAACnC,MAAM;EACrD,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAE2C,IAAI,GAAG,IAAIZ,KAAK,CAACW,GAAG,CAAC,EAAE1C,CAAC,GAAG0C,GAAG,EAAE1C,CAAC,EAAE,EAAE2C,IAAI,CAAC3C,CAAC,CAAC,GAAG0B,GAAG,CAAC1B,CAAC,CAAC;EACrE,OAAO2C,IAAI;AACb;AACA,SAASb,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAI5B,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,IAAI0C,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW;AACxF,IAAIC,MAAM,GAAGH,UAAU,GAAGC,MAAM,GAAG,CAAC,CAAC;AACrC,IAAIG,eAAe,GAAGJ,UAAU,IAAIG,MAAM,CAACD,QAAQ,CAACG,eAAe,GAAG,cAAc,IAAIF,MAAM,CAACD,QAAQ,CAACG,eAAe,GAAG,KAAK;AAC/H,IAAIC,iBAAiB,GAAGN,UAAU,GAAG,cAAc,IAAIG,MAAM,GAAG,KAAK;AACrE,IAAII,SAAS,GAAG,SAAS;;AAEzB;AACA,IAAIC,UAAU,GAAG,KAAK;AACtB,IAAIC,WAAW,GAAG,MAAM;AACxB,IAAIC,WAAW,GAAG,MAAM;AACxB,IAAIC,WAAW,GAAG,MAAM;AACxB,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,iBAAiB,GAAG,IAAI;;AAE5B;AACA,IAAIC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACd,SAAS,EAAE,OAAO,CAAC;AAC9C,IAAIe,cAAc,GAAG,EAAE,CAACD,MAAM,CAACd,SAAS,EAAE,WAAW,CAAC;AACtD,IAAIgB,YAAY,GAAG,EAAE,CAACF,MAAM,CAACd,SAAS,EAAE,SAAS,CAAC;AAClD,IAAIiB,UAAU,GAAG,EAAE,CAACH,MAAM,CAACd,SAAS,EAAE,OAAO,CAAC;AAC9C,IAAIkB,eAAe,GAAG,EAAE,CAACJ,MAAM,CAACd,SAAS,EAAE,YAAY,CAAC;AACxD,IAAImB,WAAW,GAAG,EAAE,CAACL,MAAM,CAACd,SAAS,EAAE,QAAQ,CAAC;AAChD,IAAIoB,UAAU,GAAG,EAAE,CAACN,MAAM,CAACd,SAAS,EAAE,OAAO,CAAC;;AAE9C;AACA,IAAIqB,WAAW,GAAG,EAAE,CAACP,MAAM,CAACd,SAAS,EAAE,QAAQ,CAAC;AAChD,IAAIsB,YAAY,GAAG,EAAE,CAACR,MAAM,CAACd,SAAS,EAAE,SAAS,CAAC;;AAElD;AACA,IAAIuB,cAAc,GAAG,MAAM;AAC3B,IAAIC,cAAc,GAAG,MAAM;AAC3B,IAAIC,cAAc,GAAG,MAAM;;AAE3B;AACA,IAAIC,UAAU,GAAG,MAAM;AACvB,IAAIC,cAAc,GAAG,SAAS;AAC9B,IAAIC,eAAe,GAAG,UAAU;AAChC,IAAIC,gBAAgB,GAAG,WAAW;AAClC,IAAIC,cAAc,GAAG,UAAU;AAC/B,IAAIC,iBAAiB,GAAGlC,eAAe,GAAG,YAAY,GAAG,WAAW;AACpE,IAAImC,gBAAgB,GAAGnC,eAAe,GAAG,WAAW,GAAG,WAAW;AAClE,IAAIoC,eAAe,GAAGpC,eAAe,GAAG,sBAAsB,GAAG,SAAS;AAC1E,IAAIqC,kBAAkB,GAAGnC,iBAAiB,GAAG,aAAa,GAAGgC,iBAAiB;AAC9E,IAAII,kBAAkB,GAAGpC,iBAAiB,GAAG,aAAa,GAAGiC,gBAAgB;AAC7E,IAAII,gBAAgB,GAAGrC,iBAAiB,GAAG,yBAAyB,GAAGkC,eAAe;AACtF,IAAII,WAAW,GAAG,OAAO;AACzB,IAAIC,YAAY,GAAG,QAAQ;AAC3B,IAAIC,WAAW,GAAG,OAAO;AACzB,IAAIC,UAAU,GAAG,MAAM;;AAEvB;AACA,IAAIC,cAAc,GAAG,YAAY;;AAEjC;AACA,IAAIC,cAAc,GAAG,0CAA0C;AAC/D,IAAIC,eAAe,GAAG,QAAQ;AAC9B,IAAIC,oBAAoB,GAAG,2BAA2B;AACtD,IAAIC,eAAe,GAAG,eAAe;;AAErC;AACA;AACA,IAAIC,mBAAmB,GAAG,GAAG;AAC7B,IAAIC,oBAAoB,GAAG,GAAG;AAE9B,IAAIC,QAAQ,GAAG;EACb;EACAC,QAAQ,EAAE,CAAC;EACX;;EAEA;EACAC,QAAQ,EAAE3B,cAAc;EACxB;;EAEA;EACA4B,kBAAkB,EAAEC,GAAG;EACvB;EACAC,WAAW,EAAED,GAAG;EAChB;EACAE,IAAI,EAAE,IAAI;EACV;EACAC,OAAO,EAAE,EAAE;EACX;EACAC,UAAU,EAAE,IAAI;EAChB;EACAC,OAAO,EAAE,IAAI;EACb;EACAC,gBAAgB,EAAE,IAAI;EACtB;EACAC,gBAAgB,EAAE,IAAI;EACtB;EACAC,KAAK,EAAE,IAAI;EACX;EACAC,MAAM,EAAE,IAAI;EACZ;EACAC,MAAM,EAAE,IAAI;EACZ;EACAC,SAAS,EAAE,IAAI;EACf;EACAC,UAAU,EAAE,IAAI;EAChB;EACAC,QAAQ,EAAE,IAAI;EACd;EACAC,YAAY,EAAE,GAAG;EACjB;EACAC,OAAO,EAAE,IAAI;EACb;EACAC,SAAS,EAAE,IAAI;EACf;EACAC,QAAQ,EAAE,IAAI;EACd;EACAC,QAAQ,EAAE,IAAI;EACd;EACAC,WAAW,EAAE,IAAI;EACjB;EACAC,WAAW,EAAE,IAAI;EACjB;EACAC,cAAc,EAAE,GAAG;EACnB;EACAC,cAAc,EAAE,IAAI;EACpB;EACAC,gBAAgB,EAAE,IAAI;EACtB;EACAC,wBAAwB,EAAE,IAAI;EAC9B;EACAC,cAAc,EAAE,CAAC;EACjBC,eAAe,EAAE,CAAC;EAClBC,eAAe,EAAE,CAAC;EAClBC,gBAAgB,EAAE,CAAC;EACnBC,iBAAiB,EAAEnC,mBAAmB;EACtCoC,kBAAkB,EAAEnC,oBAAoB;EACxC;EACAoC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE;AACR,CAAC;AAED,IAAIC,QAAQ,GAAG,qDAAqD,GAAG,gCAAgC,GAAG,oCAAoC,GAAG,QAAQ,GAAG,sCAAsC,GAAG,gCAAgC,GAAG,wCAAwC,GAAG,+CAA+C,GAAG,+CAA+C,GAAG,sCAAsC,GAAG,oCAAoC,GAAG,mEAAmE,GAAG,mEAAmE,GAAG,mEAAmE,GAAG,mEAAmE,GAAG,qEAAqE,GAAG,qEAAqE,GAAG,qEAAqE,GAAG,qEAAqE,GAAG,uEAAuE,GAAG,uEAAuE,GAAG,uEAAuE,GAAG,uEAAuE,GAAG,QAAQ,GAAG,QAAQ;;AAE1zC;AACA;AACA;AACA,IAAIC,KAAK,GAAGzI,MAAM,CAACyI,KAAK,IAAI9F,MAAM,CAAC8F,KAAK;;AAExC;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACtH,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACqH,KAAK,CAACrH,KAAK,CAAC;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIuH,gBAAgB,GAAG,SAASA,gBAAgBA,CAACvH,KAAK,EAAE;EACtD,OAAOA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGwH,QAAQ;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACzH,KAAK,EAAE;EAC1B,OAAO,OAAOA,KAAK,KAAK,WAAW;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS0H,QAAQA,CAAC1H,KAAK,EAAE;EACvB,OAAOlB,OAAO,CAACkB,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACtD;AACA,IAAI2H,cAAc,GAAGvK,MAAM,CAAC6B,SAAS,CAAC0I,cAAc;;AAEpD;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAC5H,KAAK,EAAE;EAC5B,IAAI,CAAC0H,QAAQ,CAAC1H,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EACd;EACA,IAAI;IACF,IAAI6H,YAAY,GAAG7H,KAAK,CAAChB,WAAW;IACpC,IAAIC,SAAS,GAAG4I,YAAY,CAAC5I,SAAS;IACtC,OAAO4I,YAAY,IAAI5I,SAAS,IAAI0I,cAAc,CAAClJ,IAAI,CAACQ,SAAS,EAAE,eAAe,CAAC;EACrF,CAAC,CAAC,OAAO6I,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAC/H,KAAK,EAAE;EACzB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC;AACA,IAAIe,KAAK,GAAGR,KAAK,CAACtB,SAAS,CAAC8B,KAAK;;AAEjC;AACA;AACA;AACA;AACA;AACA,SAASiH,OAAOA,CAAChI,KAAK,EAAE;EACtB,OAAOO,KAAK,CAACI,IAAI,GAAGJ,KAAK,CAACI,IAAI,CAACX,KAAK,CAAC,GAAGe,KAAK,CAACtC,IAAI,CAACuB,KAAK,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShC,OAAOA,CAACiH,IAAI,EAAEgD,QAAQ,EAAE;EAC/B,IAAIhD,IAAI,IAAI8C,UAAU,CAACE,QAAQ,CAAC,EAAE;IAChC,IAAI1H,KAAK,CAACC,OAAO,CAACyE,IAAI,CAAC,IAAIqC,QAAQ,CAACrC,IAAI,CAAClH,MAAM,CAAC,CAAC,kBAAkB;MACjEiK,OAAO,CAAC/C,IAAI,CAAC,CAACjH,OAAO,CAAC,UAAUgC,KAAK,EAAEL,GAAG,EAAE;QAC1CsI,QAAQ,CAACxJ,IAAI,CAACwG,IAAI,EAAEjF,KAAK,EAAEL,GAAG,EAAEsF,IAAI,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIyC,QAAQ,CAACzC,IAAI,CAAC,EAAE;MACzB7H,MAAM,CAACC,IAAI,CAAC4H,IAAI,CAAC,CAACjH,OAAO,CAAC,UAAU2B,GAAG,EAAE;QACvCsI,QAAQ,CAACxJ,IAAI,CAACwG,IAAI,EAAEA,IAAI,CAACtF,GAAG,CAAC,EAAEA,GAAG,EAAEsF,IAAI,CAAC;MAC3C,CAAC,CAAC;IACJ;EACF;EACA,OAAOA,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiD,MAAM,GAAG9K,MAAM,CAAC8K,MAAM,IAAI,SAASA,MAAMA,CAAC5I,MAAM,EAAE;EACpD,KAAK,IAAI6I,IAAI,GAAGrK,SAAS,CAACC,MAAM,EAAEqK,IAAI,GAAG,IAAI7H,KAAK,CAAC4H,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IAC1GD,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGvK,SAAS,CAACuK,IAAI,CAAC;EAClC;EACA,IAAIX,QAAQ,CAACpI,MAAM,CAAC,IAAI8I,IAAI,CAACrK,MAAM,GAAG,CAAC,EAAE;IACvCqK,IAAI,CAACpK,OAAO,CAAC,UAAUsK,GAAG,EAAE;MAC1B,IAAIZ,QAAQ,CAACY,GAAG,CAAC,EAAE;QACjBlL,MAAM,CAACC,IAAI,CAACiL,GAAG,CAAC,CAACtK,OAAO,CAAC,UAAU2B,GAAG,EAAE;UACtCL,MAAM,CAACK,GAAG,CAAC,GAAG2I,GAAG,CAAC3I,GAAG,CAAC;QACxB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACA,OAAOL,MAAM;AACf,CAAC;AACD,IAAIiJ,eAAe,GAAG,sBAAsB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACxI,KAAK,EAAE;EACrC,IAAIyI,KAAK,GAAG3K,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY;EAC5F,OAAOyK,eAAe,CAACtH,IAAI,CAACjB,KAAK,CAAC,GAAG2I,IAAI,CAACC,KAAK,CAAC5I,KAAK,GAAGyI,KAAK,CAAC,GAAGA,KAAK,GAAGzI,KAAK;AAChF;AACA,IAAI6I,aAAa,GAAG,8CAA8C;;AAElE;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACjC,IAAIC,KAAK,GAAGF,OAAO,CAACE,KAAK;EACzBjL,OAAO,CAACgL,MAAM,EAAE,UAAUhJ,KAAK,EAAEkJ,QAAQ,EAAE;IACzC,IAAIL,aAAa,CAAC5H,IAAI,CAACiI,QAAQ,CAAC,IAAI5B,QAAQ,CAACtH,KAAK,CAAC,EAAE;MACnDA,KAAK,GAAG,EAAE,CAACyC,MAAM,CAACzC,KAAK,EAAE,IAAI,CAAC;IAChC;IACAiJ,KAAK,CAACC,QAAQ,CAAC,GAAGlJ,KAAK;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmJ,QAAQA,CAACJ,OAAO,EAAE/I,KAAK,EAAE;EAChC,OAAO+I,OAAO,CAACK,SAAS,GAAGL,OAAO,CAACK,SAAS,CAACC,QAAQ,CAACrJ,KAAK,CAAC,GAAG+I,OAAO,CAACO,SAAS,CAACC,OAAO,CAACvJ,KAAK,CAAC,GAAG,CAAC,CAAC;AACtG;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASwJ,QAAQA,CAACT,OAAO,EAAE/I,KAAK,EAAE;EAChC,IAAI,CAACA,KAAK,EAAE;IACV;EACF;EACA,IAAIsH,QAAQ,CAACyB,OAAO,CAAChL,MAAM,CAAC,EAAE;IAC5BC,OAAO,CAAC+K,OAAO,EAAE,UAAUU,IAAI,EAAE;MAC/BD,QAAQ,CAACC,IAAI,EAAEzJ,KAAK,CAAC;IACvB,CAAC,CAAC;IACF;EACF;EACA,IAAI+I,OAAO,CAACK,SAAS,EAAE;IACrBL,OAAO,CAACK,SAAS,CAACM,GAAG,CAAC1J,KAAK,CAAC;IAC5B;EACF;EACA,IAAIsJ,SAAS,GAAGP,OAAO,CAACO,SAAS,CAACK,IAAI,CAAC,CAAC;EACxC,IAAI,CAACL,SAAS,EAAE;IACdP,OAAO,CAACO,SAAS,GAAGtJ,KAAK;EAC3B,CAAC,MAAM,IAAIsJ,SAAS,CAACC,OAAO,CAACvJ,KAAK,CAAC,GAAG,CAAC,EAAE;IACvC+I,OAAO,CAACO,SAAS,GAAG,EAAE,CAAC7G,MAAM,CAAC6G,SAAS,EAAE,GAAG,CAAC,CAAC7G,MAAM,CAACzC,KAAK,CAAC;EAC7D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS4J,WAAWA,CAACb,OAAO,EAAE/I,KAAK,EAAE;EACnC,IAAI,CAACA,KAAK,EAAE;IACV;EACF;EACA,IAAIsH,QAAQ,CAACyB,OAAO,CAAChL,MAAM,CAAC,EAAE;IAC5BC,OAAO,CAAC+K,OAAO,EAAE,UAAUU,IAAI,EAAE;MAC/BG,WAAW,CAACH,IAAI,EAAEzJ,KAAK,CAAC;IAC1B,CAAC,CAAC;IACF;EACF;EACA,IAAI+I,OAAO,CAACK,SAAS,EAAE;IACrBL,OAAO,CAACK,SAAS,CAACS,MAAM,CAAC7J,KAAK,CAAC;IAC/B;EACF;EACA,IAAI+I,OAAO,CAACO,SAAS,CAACC,OAAO,CAACvJ,KAAK,CAAC,IAAI,CAAC,EAAE;IACzC+I,OAAO,CAACO,SAAS,GAAGP,OAAO,CAACO,SAAS,CAACQ,OAAO,CAAC9J,KAAK,EAAE,EAAE,CAAC;EAC1D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+J,WAAWA,CAAChB,OAAO,EAAE/I,KAAK,EAAEgK,KAAK,EAAE;EAC1C,IAAI,CAAChK,KAAK,EAAE;IACV;EACF;EACA,IAAIsH,QAAQ,CAACyB,OAAO,CAAChL,MAAM,CAAC,EAAE;IAC5BC,OAAO,CAAC+K,OAAO,EAAE,UAAUU,IAAI,EAAE;MAC/BM,WAAW,CAACN,IAAI,EAAEzJ,KAAK,EAAEgK,KAAK,CAAC;IACjC,CAAC,CAAC;IACF;EACF;;EAEA;EACA,IAAIA,KAAK,EAAE;IACTR,QAAQ,CAACT,OAAO,EAAE/I,KAAK,CAAC;EAC1B,CAAC,MAAM;IACL4J,WAAW,CAACb,OAAO,EAAE/I,KAAK,CAAC;EAC7B;AACF;AACA,IAAIiK,iBAAiB,GAAG,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAClK,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAAC8J,OAAO,CAACG,iBAAiB,EAAE,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACrB,OAAO,EAAE/H,IAAI,EAAE;EAC9B,IAAI0G,QAAQ,CAACqB,OAAO,CAAC/H,IAAI,CAAC,CAAC,EAAE;IAC3B,OAAO+H,OAAO,CAAC/H,IAAI,CAAC;EACtB;EACA,IAAI+H,OAAO,CAACsB,OAAO,EAAE;IACnB,OAAOtB,OAAO,CAACsB,OAAO,CAACrJ,IAAI,CAAC;EAC9B;EACA,OAAO+H,OAAO,CAACuB,YAAY,CAAC,OAAO,CAAC7H,MAAM,CAACyH,WAAW,CAAClJ,IAAI,CAAC,CAAC,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuJ,OAAOA,CAACxB,OAAO,EAAE/H,IAAI,EAAEiE,IAAI,EAAE;EACpC,IAAIyC,QAAQ,CAACzC,IAAI,CAAC,EAAE;IAClB8D,OAAO,CAAC/H,IAAI,CAAC,GAAGiE,IAAI;EACtB,CAAC,MAAM,IAAI8D,OAAO,CAACsB,OAAO,EAAE;IAC1BtB,OAAO,CAACsB,OAAO,CAACrJ,IAAI,CAAC,GAAGiE,IAAI;EAC9B,CAAC,MAAM;IACL8D,OAAO,CAACyB,YAAY,CAAC,OAAO,CAAC/H,MAAM,CAACyH,WAAW,CAAClJ,IAAI,CAAC,CAAC,EAAEiE,IAAI,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASwF,UAAUA,CAAC1B,OAAO,EAAE/H,IAAI,EAAE;EACjC,IAAI0G,QAAQ,CAACqB,OAAO,CAAC/H,IAAI,CAAC,CAAC,EAAE;IAC3B,IAAI;MACF,OAAO+H,OAAO,CAAC/H,IAAI,CAAC;IACtB,CAAC,CAAC,OAAO8G,KAAK,EAAE;MACdiB,OAAO,CAAC/H,IAAI,CAAC,GAAG0H,SAAS;IAC3B;EACF,CAAC,MAAM,IAAIK,OAAO,CAACsB,OAAO,EAAE;IAC1B;IACA,IAAI;MACF,OAAOtB,OAAO,CAACsB,OAAO,CAACrJ,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAO8G,KAAK,EAAE;MACdiB,OAAO,CAACsB,OAAO,CAACrJ,IAAI,CAAC,GAAG0H,SAAS;IACnC;EACF,CAAC,MAAM;IACLK,OAAO,CAAC2B,eAAe,CAAC,OAAO,CAACjI,MAAM,CAACyH,WAAW,CAAClJ,IAAI,CAAC,CAAC,CAAC;EAC5D;AACF;AACA,IAAI2J,aAAa,GAAG,OAAO;AAC3B,IAAIC,aAAa,GAAG,YAAY;EAC9B,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIzJ,UAAU,EAAE;IACd,IAAI0J,IAAI,GAAG,KAAK;IAChB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG,CAAC,CAAC;IACrC,IAAIC,OAAO,GAAG5N,MAAM,CAACgB,cAAc,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;MAC9C6M,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClBJ,SAAS,GAAG,IAAI;QAChB,OAAOC,IAAI;MACb,CAAC;MACD;AACN;AACA;AACA;AACA;MACMI,GAAG,EAAE,SAASA,GAAGA,CAAClL,KAAK,EAAE;QACvB8K,IAAI,GAAG9K,KAAK;MACd;IACF,CAAC,CAAC;IACFuB,MAAM,CAAC4J,gBAAgB,CAAC,MAAM,EAAEJ,QAAQ,EAAEC,OAAO,CAAC;IAClDzJ,MAAM,CAAC6J,mBAAmB,CAAC,MAAM,EAAEL,QAAQ,EAAEC,OAAO,CAAC;EACvD;EACA,OAAOH,SAAS;AAClB,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,cAAcA,CAACtC,OAAO,EAAEuC,IAAI,EAAEP,QAAQ,EAAE;EAC/C,IAAIC,OAAO,GAAGlN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIyN,OAAO,GAAGR,QAAQ;EACtBO,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC6B,KAAK,CAACb,aAAa,CAAC,CAAC3M,OAAO,CAAC,UAAUyN,KAAK,EAAE;IACxD,IAAI,CAACb,aAAa,EAAE;MAClB,IAAIc,SAAS,GAAG3C,OAAO,CAAC2C,SAAS;MACjC,IAAIA,SAAS,IAAIA,SAAS,CAACD,KAAK,CAAC,IAAIC,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC,EAAE;QAC/DQ,OAAO,GAAGG,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC;QACpC,OAAOW,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC;QACjC,IAAI3N,MAAM,CAACC,IAAI,CAACqO,SAAS,CAACD,KAAK,CAAC,CAAC,CAAC1N,MAAM,KAAK,CAAC,EAAE;UAC9C,OAAO2N,SAAS,CAACD,KAAK,CAAC;QACzB;QACA,IAAIrO,MAAM,CAACC,IAAI,CAACqO,SAAS,CAAC,CAAC3N,MAAM,KAAK,CAAC,EAAE;UACvC,OAAOgL,OAAO,CAAC2C,SAAS;QAC1B;MACF;IACF;IACA3C,OAAO,CAACqC,mBAAmB,CAACK,KAAK,EAAEF,OAAO,EAAEP,OAAO,CAAC;EACtD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,WAAWA,CAAC5C,OAAO,EAAEuC,IAAI,EAAEP,QAAQ,EAAE;EAC5C,IAAIC,OAAO,GAAGlN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAI8N,QAAQ,GAAGb,QAAQ;EACvBO,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC6B,KAAK,CAACb,aAAa,CAAC,CAAC3M,OAAO,CAAC,UAAUyN,KAAK,EAAE;IACxD,IAAIT,OAAO,CAACF,IAAI,IAAI,CAACF,aAAa,EAAE;MAClC,IAAIiB,kBAAkB,GAAG9C,OAAO,CAAC2C,SAAS;QACxCA,SAAS,GAAGG,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;MACrED,QAAQ,GAAG,SAASL,OAAOA,CAAA,EAAG;QAC5B,OAAOG,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC;QACjChC,OAAO,CAACqC,mBAAmB,CAACK,KAAK,EAAEG,QAAQ,EAAEZ,OAAO,CAAC;QACrD,KAAK,IAAIc,KAAK,GAAGhO,SAAS,CAACC,MAAM,EAAEqK,IAAI,GAAG,IAAI7H,KAAK,CAACuL,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UAC7F3D,IAAI,CAAC2D,KAAK,CAAC,GAAGjO,SAAS,CAACiO,KAAK,CAAC;QAChC;QACAhB,QAAQ,CAACnN,KAAK,CAACmL,OAAO,EAAEX,IAAI,CAAC;MAC/B,CAAC;MACD,IAAI,CAACsD,SAAS,CAACD,KAAK,CAAC,EAAE;QACrBC,SAAS,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC;MACvB;MACA,IAAIC,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC,EAAE;QAC9BhC,OAAO,CAACqC,mBAAmB,CAACK,KAAK,EAAEC,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC,EAAEC,OAAO,CAAC;MACzE;MACAU,SAAS,CAACD,KAAK,CAAC,CAACV,QAAQ,CAAC,GAAGa,QAAQ;MACrC7C,OAAO,CAAC2C,SAAS,GAAGA,SAAS;IAC/B;IACA3C,OAAO,CAACoC,gBAAgB,CAACM,KAAK,EAAEG,QAAQ,EAAEZ,OAAO,CAAC;EACpD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,aAAaA,CAACjD,OAAO,EAAEuC,IAAI,EAAErG,IAAI,EAAE;EAC1C,IAAIwG,KAAK;;EAET;EACA,IAAI1D,UAAU,CAACkE,KAAK,CAAC,IAAIlE,UAAU,CAACmE,WAAW,CAAC,EAAE;IAChDT,KAAK,GAAG,IAAIS,WAAW,CAACZ,IAAI,EAAE;MAC5Ba,MAAM,EAAElH,IAAI;MACZmH,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,MAAM;IACLZ,KAAK,GAAGnK,QAAQ,CAACgL,WAAW,CAAC,aAAa,CAAC;IAC3Cb,KAAK,CAACc,eAAe,CAACjB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAErG,IAAI,CAAC;EAC/C;EACA,OAAO8D,OAAO,CAACiD,aAAa,CAACP,KAAK,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASe,SAASA,CAACzD,OAAO,EAAE;EAC1B,IAAI0D,GAAG,GAAG1D,OAAO,CAAC2D,qBAAqB,CAAC,CAAC;EACzC,OAAO;IACLC,IAAI,EAAEF,GAAG,CAACE,IAAI,IAAItL,MAAM,CAACuL,WAAW,GAAGtL,QAAQ,CAACG,eAAe,CAACoL,UAAU,CAAC;IAC3EC,GAAG,EAAEL,GAAG,CAACK,GAAG,IAAIzL,MAAM,CAAC0L,WAAW,GAAGzL,QAAQ,CAACG,eAAe,CAACuL,SAAS;EACzE,CAAC;AACH;AACA,IAAIC,QAAQ,GAAG1L,MAAM,CAAC0L,QAAQ;AAC9B,IAAIC,cAAc,GAAG,+BAA+B;;AAEpD;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC7B,IAAIC,KAAK,GAAGD,GAAG,CAACE,KAAK,CAACJ,cAAc,CAAC;EACrC,OAAOG,KAAK,KAAK,IAAI,KAAKA,KAAK,CAAC,CAAC,CAAC,KAAKJ,QAAQ,CAACM,QAAQ,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAKJ,QAAQ,CAACO,QAAQ,IAAIH,KAAK,CAAC,CAAC,CAAC,KAAKJ,QAAQ,CAACQ,IAAI,CAAC;AAC3H;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACN,GAAG,EAAE;EACzB,IAAIO,SAAS,GAAG,YAAY,CAAClL,MAAM,CAAC,IAAImL,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EACzD,OAAOT,GAAG,IAAIA,GAAG,CAAC7D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGoE,SAAS;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACtBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,UAAU,GAAGL,IAAI,CAACK,UAAU;EAC9B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI/G,QAAQ,CAAC6G,UAAU,CAAC,IAAIA,UAAU,KAAK,CAAC,EAAE;IAC5CE,MAAM,CAAC1Q,IAAI,CAAC,aAAa,CAAC8E,MAAM,CAAC0L,UAAU,EAAE,KAAK,CAAC,CAAC;EACtD;EACA,IAAI7G,QAAQ,CAAC8G,UAAU,CAAC,IAAIA,UAAU,KAAK,CAAC,EAAE;IAC5CC,MAAM,CAAC1Q,IAAI,CAAC,aAAa,CAAC8E,MAAM,CAAC2L,UAAU,EAAE,KAAK,CAAC,CAAC;EACtD;;EAEA;EACA,IAAI9G,QAAQ,CAAC0G,MAAM,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACpCK,MAAM,CAAC1Q,IAAI,CAAC,SAAS,CAAC8E,MAAM,CAACuL,MAAM,EAAE,MAAM,CAAC,CAAC;EAC/C;EACA,IAAI1G,QAAQ,CAAC2G,MAAM,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACpCI,MAAM,CAAC1Q,IAAI,CAAC,SAAS,CAAC8E,MAAM,CAACwL,MAAM,EAAE,GAAG,CAAC,CAAC;EAC5C;EACA,IAAI3G,QAAQ,CAAC4G,MAAM,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACpCG,MAAM,CAAC1Q,IAAI,CAAC,SAAS,CAAC8E,MAAM,CAACyL,MAAM,EAAE,GAAG,CAAC,CAAC;EAC5C;EACA,IAAII,SAAS,GAAGD,MAAM,CAACtQ,MAAM,GAAGsQ,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;EACzD,OAAO;IACLC,eAAe,EAAEF,SAAS;IAC1BG,WAAW,EAAEH,SAAS;IACtBA,SAAS,EAAEA;EACb,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAACC,QAAQ,EAAE;EACjC,IAAIC,SAAS,GAAG/Q,cAAc,CAAC,CAAC,CAAC,EAAE8Q,QAAQ,CAAC;EAC5C,IAAIE,QAAQ,GAAG,CAAC;EAChB7Q,OAAO,CAAC2Q,QAAQ,EAAE,UAAUG,OAAO,EAAEC,SAAS,EAAE;IAC9C,OAAOH,SAAS,CAACG,SAAS,CAAC;IAC3B/Q,OAAO,CAAC4Q,SAAS,EAAE,UAAUI,QAAQ,EAAE;MACrC,IAAIC,EAAE,GAAGtG,IAAI,CAACuG,GAAG,CAACJ,OAAO,CAACK,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;MACnD,IAAIC,EAAE,GAAGzG,IAAI,CAACuG,GAAG,CAACJ,OAAO,CAACO,MAAM,GAAGL,QAAQ,CAACK,MAAM,CAAC;MACnD,IAAIC,EAAE,GAAG3G,IAAI,CAACuG,GAAG,CAACJ,OAAO,CAACS,IAAI,GAAGP,QAAQ,CAACO,IAAI,CAAC;MAC/C,IAAIC,EAAE,GAAG7G,IAAI,CAACuG,GAAG,CAACJ,OAAO,CAACW,IAAI,GAAGT,QAAQ,CAACS,IAAI,CAAC;MAC/C,IAAIC,EAAE,GAAG/G,IAAI,CAACgH,IAAI,CAACV,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE,CAAC;MACrC,IAAIQ,EAAE,GAAGjH,IAAI,CAACgH,IAAI,CAACL,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,CAAC;MACrC,IAAIK,KAAK,GAAG,CAACD,EAAE,GAAGF,EAAE,IAAIA,EAAE;MAC1B,IAAI/G,IAAI,CAACuG,GAAG,CAACW,KAAK,CAAC,GAAGlH,IAAI,CAACuG,GAAG,CAACL,QAAQ,CAAC,EAAE;QACxCA,QAAQ,GAAGgB,KAAK;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOhB,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,KAAK,GAAGH,KAAK,CAACG,KAAK;EACrB,IAAIC,GAAG,GAAG;IACRZ,IAAI,EAAEU,KAAK;IACXR,IAAI,EAAES;EACR,CAAC;EACD,OAAOF,OAAO,GAAGG,GAAG,GAAGtS,cAAc,CAAC;IACpCsR,MAAM,EAAEc,KAAK;IACbZ,MAAM,EAAEa;EACV,CAAC,EAAEC,GAAG,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACzB,QAAQ,EAAE;EACnC,IAAIsB,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIG,KAAK,GAAG,CAAC;EACbrS,OAAO,CAAC2Q,QAAQ,EAAE,UAAU2B,KAAK,EAAE;IACjC,IAAInB,MAAM,GAAGmB,KAAK,CAACnB,MAAM;MACvBE,MAAM,GAAGiB,KAAK,CAACjB,MAAM;IACvBY,KAAK,IAAId,MAAM;IACfe,KAAK,IAAIb,MAAM;IACfgB,KAAK,IAAI,CAAC;EACZ,CAAC,CAAC;EACFJ,KAAK,IAAII,KAAK;EACdH,KAAK,IAAIG,KAAK;EACd,OAAO;IACLJ,KAAK,EAAEA,KAAK;IACZC,KAAK,EAAEA;EACT,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,IAAIxL,WAAW,GAAGwL,KAAK,CAACxL,WAAW;IACjCyL,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBC,KAAK,GAAGF,KAAK,CAACE,KAAK;EACrB,IAAIpF,IAAI,GAAGxN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS;EACxF,IAAI6S,YAAY,GAAGpJ,gBAAgB,CAACmJ,KAAK,CAAC;EAC1C,IAAIE,aAAa,GAAGrJ,gBAAgB,CAACkJ,MAAM,CAAC;EAC5C,IAAIE,YAAY,IAAIC,aAAa,EAAE;IACjC,IAAIC,aAAa,GAAGJ,MAAM,GAAGzL,WAAW;IACxC,IAAIsG,IAAI,KAAK,SAAS,IAAIuF,aAAa,GAAGH,KAAK,IAAIpF,IAAI,KAAK,OAAO,IAAIuF,aAAa,GAAGH,KAAK,EAAE;MAC5FD,MAAM,GAAGC,KAAK,GAAG1L,WAAW;IAC9B,CAAC,MAAM;MACL0L,KAAK,GAAGD,MAAM,GAAGzL,WAAW;IAC9B;EACF,CAAC,MAAM,IAAI2L,YAAY,EAAE;IACvBF,MAAM,GAAGC,KAAK,GAAG1L,WAAW;EAC9B,CAAC,MAAM,IAAI4L,aAAa,EAAE;IACxBF,KAAK,GAAGD,MAAM,GAAGzL,WAAW;EAC9B;EACA,OAAO;IACL0L,KAAK,EAAEA,KAAK;IACZD,MAAM,EAAEA;EACV,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASK,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAIL,KAAK,GAAGK,KAAK,CAACL,KAAK;IACrBD,MAAM,GAAGM,KAAK,CAACN,MAAM;IACrBO,MAAM,GAAGD,KAAK,CAACC,MAAM;EACvBA,MAAM,GAAGrI,IAAI,CAACuG,GAAG,CAAC8B,MAAM,CAAC,GAAG,GAAG;EAC/B,IAAIA,MAAM,KAAK,EAAE,EAAE;IACjB,OAAO;MACLN,KAAK,EAAED,MAAM;MACbA,MAAM,EAAEC;IACV,CAAC;EACH;EACA,IAAIO,GAAG,GAAGD,MAAM,GAAG,EAAE,GAAGrI,IAAI,CAACuI,EAAE,GAAG,GAAG;EACrC,IAAIC,MAAM,GAAGxI,IAAI,CAACyI,GAAG,CAACH,GAAG,CAAC;EAC1B,IAAII,MAAM,GAAG1I,IAAI,CAAC2I,GAAG,CAACL,GAAG,CAAC;EAC1B,IAAIM,QAAQ,GAAGb,KAAK,GAAGW,MAAM,GAAGZ,MAAM,GAAGU,MAAM;EAC/C,IAAIK,SAAS,GAAGd,KAAK,GAAGS,MAAM,GAAGV,MAAM,GAAGY,MAAM;EAChD,OAAOL,MAAM,GAAG,EAAE,GAAG;IACnBN,KAAK,EAAEc,SAAS;IAChBf,MAAM,EAAEc;EACV,CAAC,GAAG;IACFb,KAAK,EAAEa,QAAQ;IACfd,MAAM,EAAEe;EACV,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACnD,IAAIC,gBAAgB,GAAGH,KAAK,CAAC3M,WAAW;IACtC+M,iBAAiB,GAAGJ,KAAK,CAACK,YAAY;IACtCC,kBAAkB,GAAGN,KAAK,CAACO,aAAa;IACxCC,YAAY,GAAGR,KAAK,CAAC3D,MAAM;IAC3BA,MAAM,GAAGmE,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IACnDC,YAAY,GAAGT,KAAK,CAAC1D,MAAM;IAC3BA,MAAM,GAAGmE,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IACnDC,YAAY,GAAGV,KAAK,CAACzD,MAAM;IAC3BA,MAAM,GAAGmE,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;EACrD,IAAIrN,WAAW,GAAG4M,KAAK,CAAC5M,WAAW;IACjCgN,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCE,aAAa,GAAGN,KAAK,CAACM,aAAa;EACrC,IAAII,eAAe,GAAGT,KAAK,CAACU,SAAS;IACnCA,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,eAAe;IACxEE,qBAAqB,GAAGX,KAAK,CAACY,qBAAqB;IACnDA,qBAAqB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACvFE,qBAAqB,GAAGb,KAAK,CAACc,qBAAqB;IACnDA,qBAAqB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACxFE,cAAc,GAAGf,KAAK,CAACgB,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGpL,QAAQ,GAAGoL,cAAc;IAChEE,eAAe,GAAGjB,KAAK,CAACkB,SAAS;IACjCA,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAGtL,QAAQ,GAAGsL,eAAe;IACnEE,cAAc,GAAGnB,KAAK,CAACoB,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc;IACzDE,eAAe,GAAGrB,KAAK,CAACsB,SAAS;IACjCA,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;EAC9D,IAAIE,MAAM,GAAG9R,QAAQ,CAAC+R,aAAa,CAAC,QAAQ,CAAC;EAC7C,IAAIC,OAAO,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EACrC,IAAIC,QAAQ,GAAGjD,gBAAgB,CAAC;IAC9BvL,WAAW,EAAEA,WAAW;IACxB0L,KAAK,EAAEmC,QAAQ;IACfpC,MAAM,EAAEsC;EACV,CAAC,CAAC;EACF,IAAIU,QAAQ,GAAGlD,gBAAgB,CAAC;IAC9BvL,WAAW,EAAEA,WAAW;IACxB0L,KAAK,EAAEuC,QAAQ;IACfxC,MAAM,EAAE0C;EACV,CAAC,EAAE,OAAO,CAAC;EACX,IAAIzC,KAAK,GAAG/H,IAAI,CAAC+K,GAAG,CAACF,QAAQ,CAAC9C,KAAK,EAAE/H,IAAI,CAACgL,GAAG,CAACF,QAAQ,CAAC/C,KAAK,EAAEsB,YAAY,CAAC,CAAC;EAC5E,IAAIvB,MAAM,GAAG9H,IAAI,CAAC+K,GAAG,CAACF,QAAQ,CAAC/C,MAAM,EAAE9H,IAAI,CAACgL,GAAG,CAACF,QAAQ,CAAChD,MAAM,EAAEyB,aAAa,CAAC,CAAC;;EAEhF;EACA;EACA,IAAI0B,YAAY,GAAGrD,gBAAgB,CAAC;IAClCvL,WAAW,EAAE8M,gBAAgB;IAC7BpB,KAAK,EAAEmC,QAAQ;IACfpC,MAAM,EAAEsC;EACV,CAAC,CAAC;EACF,IAAIc,YAAY,GAAGtD,gBAAgB,CAAC;IAClCvL,WAAW,EAAE8M,gBAAgB;IAC7BpB,KAAK,EAAEuC,QAAQ;IACfxC,MAAM,EAAE0C;EACV,CAAC,EAAE,OAAO,CAAC;EACX,IAAIW,SAAS,GAAGnL,IAAI,CAAC+K,GAAG,CAACE,YAAY,CAAClD,KAAK,EAAE/H,IAAI,CAACgL,GAAG,CAACE,YAAY,CAACnD,KAAK,EAAEqB,iBAAiB,CAAC,CAAC;EAC7F,IAAIgC,UAAU,GAAGpL,IAAI,CAAC+K,GAAG,CAACE,YAAY,CAACnD,MAAM,EAAE9H,IAAI,CAACgL,GAAG,CAACE,YAAY,CAACpD,MAAM,EAAEwB,kBAAkB,CAAC,CAAC;EACjG,IAAI+B,MAAM,GAAG,CAAC,CAACF,SAAS,GAAG,CAAC,EAAE,CAACC,UAAU,GAAG,CAAC,EAAED,SAAS,EAAEC,UAAU,CAAC;EACrEX,MAAM,CAAC1C,KAAK,GAAGlI,sBAAsB,CAACkI,KAAK,CAAC;EAC5C0C,MAAM,CAAC3C,MAAM,GAAGjI,sBAAsB,CAACiI,MAAM,CAAC;EAC9C6C,OAAO,CAACW,SAAS,GAAG1B,SAAS;EAC7Be,OAAO,CAACY,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAExD,KAAK,EAAED,MAAM,CAAC;EACrC6C,OAAO,CAACa,IAAI,CAAC,CAAC;EACdb,OAAO,CAACc,SAAS,CAAC1D,KAAK,GAAG,CAAC,EAAED,MAAM,GAAG,CAAC,CAAC;EACxC6C,OAAO,CAACtF,MAAM,CAACA,MAAM,GAAGrF,IAAI,CAACuI,EAAE,GAAG,GAAG,CAAC;EACtCoC,OAAO,CAACe,KAAK,CAACpG,MAAM,EAAEC,MAAM,CAAC;EAC7BoF,OAAO,CAACb,qBAAqB,GAAGA,qBAAqB;EACrDa,OAAO,CAACX,qBAAqB,GAAGA,qBAAqB;EACrDW,OAAO,CAACgB,SAAS,CAAC1W,KAAK,CAAC0V,OAAO,EAAE,CAAC5B,KAAK,CAAC,CAACjP,MAAM,CAACxC,kBAAkB,CAAC+T,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE;IAC7F,OAAO7L,IAAI,CAAC8L,KAAK,CAACjM,sBAAsB,CAACgM,KAAK,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC,CAAC,CAAC;EACLlB,OAAO,CAAClO,OAAO,CAAC,CAAC;EACjB,OAAOgO,MAAM;AACf;AACA,IAAIsB,YAAY,GAAG/V,MAAM,CAAC+V,YAAY;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,KAAK,EAAE9W,MAAM,EAAE;EACtD,IAAI+W,GAAG,GAAG,EAAE;EACZ/W,MAAM,IAAI8W,KAAK;EACf,KAAK,IAAIrW,CAAC,GAAGqW,KAAK,EAAErW,CAAC,GAAGT,MAAM,EAAES,CAAC,IAAI,CAAC,EAAE;IACtCsW,GAAG,IAAIJ,YAAY,CAACE,QAAQ,CAACG,QAAQ,CAACvW,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOsW,GAAG;AACZ;AACA,IAAIE,oBAAoB,GAAG,WAAW;;AAEtC;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,OAAO,EAAE;EACrC,IAAIC,MAAM,GAAGD,OAAO,CAACpL,OAAO,CAACkL,oBAAoB,EAAE,EAAE,CAAC;EACtD,IAAII,MAAM,GAAGC,IAAI,CAACF,MAAM,CAAC;EACzB,IAAIG,WAAW,GAAG,IAAIC,WAAW,CAACH,MAAM,CAACrX,MAAM,CAAC;EAChD,IAAIyX,KAAK,GAAG,IAAIC,UAAU,CAACH,WAAW,CAAC;EACvCtX,OAAO,CAACwX,KAAK,EAAE,UAAUxV,KAAK,EAAExB,CAAC,EAAE;IACjCgX,KAAK,CAAChX,CAAC,CAAC,GAAG4W,MAAM,CAACM,UAAU,CAAClX,CAAC,CAAC;EACjC,CAAC,CAAC;EACF,OAAO8W,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,oBAAoBA,CAACL,WAAW,EAAEM,QAAQ,EAAE;EACnD,IAAIC,MAAM,GAAG,EAAE;;EAEf;EACA,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIN,KAAK,GAAG,IAAIC,UAAU,CAACH,WAAW,CAAC;EACvC,OAAOE,KAAK,CAACzX,MAAM,GAAG,CAAC,EAAE;IACvB;IACA;IACA8X,MAAM,CAAClY,IAAI,CAAC+W,YAAY,CAAC9W,KAAK,CAAC,IAAI,EAAEoK,OAAO,CAACwN,KAAK,CAACO,QAAQ,CAAC,CAAC,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5EN,KAAK,GAAGA,KAAK,CAACO,QAAQ,CAACD,SAAS,CAAC;EACnC;EACA,OAAO,OAAO,CAACrT,MAAM,CAACmT,QAAQ,EAAE,UAAU,CAAC,CAACnT,MAAM,CAACuT,IAAI,CAACH,MAAM,CAACtH,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS0H,sBAAsBA,CAACX,WAAW,EAAE;EAC3C,IAAIV,QAAQ,GAAG,IAAIsB,QAAQ,CAACZ,WAAW,CAAC;EACxC,IAAIa,WAAW;;EAEf;EACA,IAAI;IACF,IAAIC,YAAY;IAChB,IAAIC,SAAS;IACb,IAAIC,QAAQ;;IAEZ;IACA,IAAI1B,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIH,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MAClE,IAAIhX,MAAM,GAAG6W,QAAQ,CAAC2B,UAAU;MAChC,IAAIC,MAAM,GAAG,CAAC;MACd,OAAOA,MAAM,GAAG,CAAC,GAAGzY,MAAM,EAAE;QAC1B,IAAI6W,QAAQ,CAACG,QAAQ,CAACyB,MAAM,CAAC,KAAK,IAAI,IAAI5B,QAAQ,CAACG,QAAQ,CAACyB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;UAChFH,SAAS,GAAGG,MAAM;UAClB;QACF;QACAA,MAAM,IAAI,CAAC;MACb;IACF;IACA,IAAIH,SAAS,EAAE;MACb,IAAII,UAAU,GAAGJ,SAAS,GAAG,CAAC;MAC9B,IAAIK,UAAU,GAAGL,SAAS,GAAG,EAAE;MAC/B,IAAI1B,qBAAqB,CAACC,QAAQ,EAAE6B,UAAU,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;QAC7D,IAAIE,UAAU,GAAG/B,QAAQ,CAACgC,SAAS,CAACF,UAAU,CAAC;QAC/CN,YAAY,GAAGO,UAAU,KAAK,MAAM;QACpC,IAAIP,YAAY,IAAIO,UAAU,KAAK,MAAM,CAAC,iBAAiB;UACzD,IAAI/B,QAAQ,CAACgC,SAAS,CAACF,UAAU,GAAG,CAAC,EAAEN,YAAY,CAAC,KAAK,MAAM,EAAE;YAC/D,IAAIS,cAAc,GAAGjC,QAAQ,CAACkC,SAAS,CAACJ,UAAU,GAAG,CAAC,EAAEN,YAAY,CAAC;YACrE,IAAIS,cAAc,IAAI,UAAU,EAAE;cAChCP,QAAQ,GAAGI,UAAU,GAAGG,cAAc;YACxC;UACF;QACF;MACF;IACF;IACA,IAAIP,QAAQ,EAAE;MACZ,IAAIS,OAAO,GAAGnC,QAAQ,CAACgC,SAAS,CAACN,QAAQ,EAAEF,YAAY,CAAC;MACxD,IAAIY,OAAO;MACX,IAAIxY,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuY,OAAO,EAAEvY,CAAC,IAAI,CAAC,EAAE;QAC/BwY,OAAO,GAAGV,QAAQ,GAAG9X,CAAC,GAAG,EAAE,GAAG,CAAC;QAC/B,IAAIoW,QAAQ,CAACgC,SAAS,CAACI,OAAO,EAAEZ,YAAY,CAAC,KAAK,MAAM,CAAC,mBAAmB;UAC1E;UACAY,OAAO,IAAI,CAAC;;UAEZ;UACAb,WAAW,GAAGvB,QAAQ,CAACgC,SAAS,CAACI,OAAO,EAAEZ,YAAY,CAAC;;UAEvD;UACAxB,QAAQ,CAACqC,SAAS,CAACD,OAAO,EAAE,CAAC,EAAEZ,YAAY,CAAC;UAC5C;QACF;MACF;IACF;EACF,CAAC,CAAC,OAAOtO,KAAK,EAAE;IACdqO,WAAW,GAAG,CAAC;EACjB;EACA,OAAOA,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASe,gBAAgBA,CAACf,WAAW,EAAE;EACrC,IAAInI,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EACd,QAAQiI,WAAW;IACjB;IACA,KAAK,CAAC;MACJlI,MAAM,GAAG,CAAC,CAAC;MACX;;IAEF;IACA,KAAK,CAAC;MACJD,MAAM,GAAG,CAAC,GAAG;MACb;;IAEF;IACA,KAAK,CAAC;MACJE,MAAM,GAAG,CAAC,CAAC;MACX;;IAEF;IACA,KAAK,CAAC;MACJF,MAAM,GAAG,EAAE;MACXE,MAAM,GAAG,CAAC,CAAC;MACX;;IAEF;IACA,KAAK,CAAC;MACJF,MAAM,GAAG,EAAE;MACX;;IAEF;IACA,KAAK,CAAC;MACJA,MAAM,GAAG,EAAE;MACXC,MAAM,GAAG,CAAC,CAAC;MACX;;IAEF;IACA,KAAK,CAAC;MACJD,MAAM,GAAG,CAAC,EAAE;MACZ;EACJ;EACA,OAAO;IACLA,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA;EACV,CAAC;AACH;AAEA,IAAIiJ,MAAM,GAAG;EACXA,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACC,aAAa,CAAC,CAAC;IACtB;EACF,CAAC;EACDL,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;IACtC,IAAIrO,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBiC,OAAO,GAAG,IAAI,CAACA,OAAO;MACtB0M,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1BC,OAAO,GAAG,IAAI,CAACA,OAAO;IACxB,IAAI1E,QAAQ,GAAGrU,MAAM,CAACoM,OAAO,CAACpE,iBAAiB,CAAC;IAChD,IAAIuM,SAAS,GAAGvU,MAAM,CAACoM,OAAO,CAACnE,kBAAkB,CAAC;IAClD2C,QAAQ,CAACmO,OAAO,EAAEhV,YAAY,CAAC;IAC/BiH,WAAW,CAACb,OAAO,EAAEpG,YAAY,CAAC;IAClC,IAAIiV,aAAa,GAAG;MAClBlH,KAAK,EAAE/H,IAAI,CAACgL,GAAG,CAAC+D,SAAS,CAACG,WAAW,EAAE5E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAGxO,mBAAmB,CAAC;MACtFgM,MAAM,EAAE9H,IAAI,CAACgL,GAAG,CAAC+D,SAAS,CAACI,YAAY,EAAE3E,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAGzO,oBAAoB;IAC5F,CAAC;IACD,IAAI,CAACkT,aAAa,GAAGA,aAAa;IAClC9O,QAAQ,CAAC6O,OAAO,EAAE;MAChBjH,KAAK,EAAEkH,aAAa,CAAClH,KAAK;MAC1BD,MAAM,EAAEmH,aAAa,CAACnH;IACxB,CAAC,CAAC;IACFjH,QAAQ,CAACT,OAAO,EAAEpG,YAAY,CAAC;IAC/BiH,WAAW,CAAC+N,OAAO,EAAEhV,YAAY,CAAC;EACpC,CAAC;EACD;EACA0U,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,IAAIO,aAAa,GAAG,IAAI,CAACA,aAAa;MACpCG,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5B,IAAInT,QAAQ,GAAG,IAAI,CAACoG,OAAO,CAACpG,QAAQ;IACpC,IAAIoT,OAAO,GAAGrP,IAAI,CAACuG,GAAG,CAAC6I,SAAS,CAAC/J,MAAM,CAAC,GAAG,GAAG,KAAK,EAAE;IACrD,IAAIgE,YAAY,GAAGgG,OAAO,GAAGD,SAAS,CAAC7F,aAAa,GAAG6F,SAAS,CAAC/F,YAAY;IAC7E,IAAIE,aAAa,GAAG8F,OAAO,GAAGD,SAAS,CAAC/F,YAAY,GAAG+F,SAAS,CAAC7F,aAAa;IAC9E,IAAIlN,WAAW,GAAGgN,YAAY,GAAGE,aAAa;IAC9C,IAAI+F,WAAW,GAAGL,aAAa,CAAClH,KAAK;IACrC,IAAIwH,YAAY,GAAGN,aAAa,CAACnH,MAAM;IACvC,IAAImH,aAAa,CAACnH,MAAM,GAAGzL,WAAW,GAAG4S,aAAa,CAAClH,KAAK,EAAE;MAC5D,IAAI9L,QAAQ,KAAK,CAAC,EAAE;QAClBqT,WAAW,GAAGL,aAAa,CAACnH,MAAM,GAAGzL,WAAW;MAClD,CAAC,MAAM;QACLkT,YAAY,GAAGN,aAAa,CAAClH,KAAK,GAAG1L,WAAW;MAClD;IACF,CAAC,MAAM,IAAIJ,QAAQ,KAAK,CAAC,EAAE;MACzBsT,YAAY,GAAGN,aAAa,CAAClH,KAAK,GAAG1L,WAAW;IAClD,CAAC,MAAM;MACLiT,WAAW,GAAGL,aAAa,CAACnH,MAAM,GAAGzL,WAAW;IAClD;IACA,IAAImT,UAAU,GAAG;MACfnT,WAAW,EAAEA,WAAW;MACxBgN,YAAY,EAAEA,YAAY;MAC1BE,aAAa,EAAEA,aAAa;MAC5BxB,KAAK,EAAEuH,WAAW;MAClBxH,MAAM,EAAEyH;IACV,CAAC;IACD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGxT,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC;IAC/C,IAAI,CAACyT,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;IAC5BF,UAAU,CAACzH,KAAK,GAAG/H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACwE,UAAU,CAACzH,KAAK,EAAEyH,UAAU,CAAClF,QAAQ,CAAC,EAAEkF,UAAU,CAACtF,QAAQ,CAAC;IACjGsF,UAAU,CAAC1H,MAAM,GAAG9H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACwE,UAAU,CAAC1H,MAAM,EAAE0H,UAAU,CAAChF,SAAS,CAAC,EAAEgF,UAAU,CAACpF,SAAS,CAAC;IACrGoF,UAAU,CAACxL,IAAI,GAAG,CAACiL,aAAa,CAAClH,KAAK,GAAGyH,UAAU,CAACzH,KAAK,IAAI,CAAC;IAC9DyH,UAAU,CAACrL,GAAG,GAAG,CAAC8K,aAAa,CAACnH,MAAM,GAAG0H,UAAU,CAAC1H,MAAM,IAAI,CAAC;IAC/D0H,UAAU,CAACG,OAAO,GAAGH,UAAU,CAACxL,IAAI;IACpCwL,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACrL,GAAG;IAClC,IAAI,CAAC0L,iBAAiB,GAAGtQ,MAAM,CAAC,CAAC,CAAC,EAAEiQ,UAAU,CAAC;EACjD,CAAC;EACDE,WAAW,EAAE,SAASA,WAAWA,CAACI,WAAW,EAAEC,eAAe,EAAE;IAC9D,IAAI1N,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB4M,aAAa,GAAG,IAAI,CAACA,aAAa;MAClCO,UAAU,GAAG,IAAI,CAACA,UAAU;MAC5BQ,WAAW,GAAG,IAAI,CAACA,WAAW;IAChC,IAAI/T,QAAQ,GAAGoG,OAAO,CAACpG,QAAQ;IAC/B,IAAII,WAAW,GAAGmT,UAAU,CAACnT,WAAW;IACxC,IAAIwS,OAAO,GAAG,IAAI,CAACA,OAAO,IAAImB,WAAW;IACzC,IAAIF,WAAW,EAAE;MACf,IAAIjS,cAAc,GAAG5H,MAAM,CAACoM,OAAO,CAACxE,cAAc,CAAC,IAAI,CAAC;MACxD,IAAIC,eAAe,GAAG7H,MAAM,CAACoM,OAAO,CAACvE,eAAe,CAAC,IAAI,CAAC;MAC1D,IAAI7B,QAAQ,GAAG,CAAC,EAAE;QAChB4B,cAAc,GAAGmC,IAAI,CAACgL,GAAG,CAACnN,cAAc,EAAEoR,aAAa,CAAClH,KAAK,CAAC;QAC9DjK,eAAe,GAAGkC,IAAI,CAACgL,GAAG,CAAClN,eAAe,EAAEmR,aAAa,CAACnH,MAAM,CAAC;QACjE,IAAI7L,QAAQ,KAAK,CAAC,EAAE;UAClB,IAAI6B,eAAe,GAAGzB,WAAW,GAAGwB,cAAc,EAAE;YAClDA,cAAc,GAAGC,eAAe,GAAGzB,WAAW;UAChD,CAAC,MAAM;YACLyB,eAAe,GAAGD,cAAc,GAAGxB,WAAW;UAChD;QACF;MACF,CAAC,MAAM,IAAIJ,QAAQ,GAAG,CAAC,EAAE;QACvB,IAAI4B,cAAc,EAAE;UAClBA,cAAc,GAAGmC,IAAI,CAACgL,GAAG,CAACnN,cAAc,EAAEgR,OAAO,GAAGmB,WAAW,CAACjI,KAAK,GAAG,CAAC,CAAC;QAC5E,CAAC,MAAM,IAAIjK,eAAe,EAAE;UAC1BA,eAAe,GAAGkC,IAAI,CAACgL,GAAG,CAAClN,eAAe,EAAE+Q,OAAO,GAAGmB,WAAW,CAAClI,MAAM,GAAG,CAAC,CAAC;QAC/E,CAAC,MAAM,IAAI+G,OAAO,EAAE;UAClBhR,cAAc,GAAGmS,WAAW,CAACjI,KAAK;UAClCjK,eAAe,GAAGkS,WAAW,CAAClI,MAAM;UACpC,IAAIhK,eAAe,GAAGzB,WAAW,GAAGwB,cAAc,EAAE;YAClDA,cAAc,GAAGC,eAAe,GAAGzB,WAAW;UAChD,CAAC,MAAM;YACLyB,eAAe,GAAGD,cAAc,GAAGxB,WAAW;UAChD;QACF;MACF;MACA,IAAI4T,iBAAiB,GAAGrI,gBAAgB,CAAC;QACvCvL,WAAW,EAAEA,WAAW;QACxB0L,KAAK,EAAElK,cAAc;QACrBiK,MAAM,EAAEhK;MACV,CAAC,CAAC;MACFD,cAAc,GAAGoS,iBAAiB,CAAClI,KAAK;MACxCjK,eAAe,GAAGmS,iBAAiB,CAACnI,MAAM;MAC1C0H,UAAU,CAAClF,QAAQ,GAAGzM,cAAc;MACpC2R,UAAU,CAAChF,SAAS,GAAG1M,eAAe;MACtC0R,UAAU,CAACtF,QAAQ,GAAGrL,QAAQ;MAC9B2Q,UAAU,CAACpF,SAAS,GAAGvL,QAAQ;IACjC;IACA,IAAIkR,eAAe,EAAE;MACnB,IAAI9T,QAAQ,IAAI4S,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QAChC,IAAIqB,aAAa,GAAGjB,aAAa,CAAClH,KAAK,GAAGyH,UAAU,CAACzH,KAAK;QAC1D,IAAIoI,YAAY,GAAGlB,aAAa,CAACnH,MAAM,GAAG0H,UAAU,CAAC1H,MAAM;QAC3D0H,UAAU,CAACY,OAAO,GAAGpQ,IAAI,CAAC+K,GAAG,CAAC,CAAC,EAAEmF,aAAa,CAAC;QAC/CV,UAAU,CAACa,MAAM,GAAGrQ,IAAI,CAAC+K,GAAG,CAAC,CAAC,EAAEoF,YAAY,CAAC;QAC7CX,UAAU,CAACc,OAAO,GAAGtQ,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEkF,aAAa,CAAC;QAC/CV,UAAU,CAACe,MAAM,GAAGvQ,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEmF,YAAY,CAAC;QAC7C,IAAItB,OAAO,IAAI,IAAI,CAACY,OAAO,EAAE;UAC3BD,UAAU,CAACY,OAAO,GAAGpQ,IAAI,CAAC+K,GAAG,CAACiF,WAAW,CAAChM,IAAI,EAAEgM,WAAW,CAAChM,IAAI,IAAIgM,WAAW,CAACjI,KAAK,GAAGyH,UAAU,CAACzH,KAAK,CAAC,CAAC;UAC1GyH,UAAU,CAACa,MAAM,GAAGrQ,IAAI,CAAC+K,GAAG,CAACiF,WAAW,CAAC7L,GAAG,EAAE6L,WAAW,CAAC7L,GAAG,IAAI6L,WAAW,CAAClI,MAAM,GAAG0H,UAAU,CAAC1H,MAAM,CAAC,CAAC;UACzG0H,UAAU,CAACc,OAAO,GAAGN,WAAW,CAAChM,IAAI;UACrCwL,UAAU,CAACe,MAAM,GAAGP,WAAW,CAAC7L,GAAG;UACnC,IAAIlI,QAAQ,KAAK,CAAC,EAAE;YAClB,IAAIuT,UAAU,CAACzH,KAAK,IAAIkH,aAAa,CAAClH,KAAK,EAAE;cAC3CyH,UAAU,CAACY,OAAO,GAAGpQ,IAAI,CAAC+K,GAAG,CAAC,CAAC,EAAEmF,aAAa,CAAC;cAC/CV,UAAU,CAACc,OAAO,GAAGtQ,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEkF,aAAa,CAAC;YACjD;YACA,IAAIV,UAAU,CAAC1H,MAAM,IAAImH,aAAa,CAACnH,MAAM,EAAE;cAC7C0H,UAAU,CAACa,MAAM,GAAGrQ,IAAI,CAAC+K,GAAG,CAAC,CAAC,EAAEoF,YAAY,CAAC;cAC7CX,UAAU,CAACe,MAAM,GAAGvQ,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEmF,YAAY,CAAC;YAC/C;UACF;QACF;MACF,CAAC,MAAM;QACLX,UAAU,CAACY,OAAO,GAAG,CAACZ,UAAU,CAACzH,KAAK;QACtCyH,UAAU,CAACa,MAAM,GAAG,CAACb,UAAU,CAAC1H,MAAM;QACtC0H,UAAU,CAACc,OAAO,GAAGrB,aAAa,CAAClH,KAAK;QACxCyH,UAAU,CAACe,MAAM,GAAGtB,aAAa,CAACnH,MAAM;MAC1C;IACF;EACF,CAAC;EACD8G,YAAY,EAAE,SAASA,YAAYA,CAAC4B,OAAO,EAAEC,WAAW,EAAE;IACxD,IAAIjB,UAAU,GAAG,IAAI,CAACA,UAAU;MAC9BJ,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5B,IAAIqB,WAAW,EAAE;MACf,IAAIC,gBAAgB,GAAGvI,eAAe,CAAC;UACnCJ,KAAK,EAAEqH,SAAS,CAAC/F,YAAY,GAAGrJ,IAAI,CAACuG,GAAG,CAAC6I,SAAS,CAAC9J,MAAM,IAAI,CAAC,CAAC;UAC/DwC,MAAM,EAAEsH,SAAS,CAAC7F,aAAa,GAAGvJ,IAAI,CAACuG,GAAG,CAAC6I,SAAS,CAAC7J,MAAM,IAAI,CAAC,CAAC;UACjE8C,MAAM,EAAE+G,SAAS,CAAC/J,MAAM,IAAI;QAC9B,CAAC,CAAC;QACFgE,YAAY,GAAGqH,gBAAgB,CAAC3I,KAAK;QACrCwB,aAAa,GAAGmH,gBAAgB,CAAC5I,MAAM;MACzC,IAAIC,KAAK,GAAGyH,UAAU,CAACzH,KAAK,IAAIsB,YAAY,GAAGmG,UAAU,CAACnG,YAAY,CAAC;MACvE,IAAIvB,MAAM,GAAG0H,UAAU,CAAC1H,MAAM,IAAIyB,aAAa,GAAGiG,UAAU,CAACjG,aAAa,CAAC;MAC3EiG,UAAU,CAACxL,IAAI,IAAI,CAAC+D,KAAK,GAAGyH,UAAU,CAACzH,KAAK,IAAI,CAAC;MACjDyH,UAAU,CAACrL,GAAG,IAAI,CAAC2D,MAAM,GAAG0H,UAAU,CAAC1H,MAAM,IAAI,CAAC;MAClD0H,UAAU,CAACzH,KAAK,GAAGA,KAAK;MACxByH,UAAU,CAAC1H,MAAM,GAAGA,MAAM;MAC1B0H,UAAU,CAACnT,WAAW,GAAGgN,YAAY,GAAGE,aAAa;MACrDiG,UAAU,CAACnG,YAAY,GAAGA,YAAY;MACtCmG,UAAU,CAACjG,aAAa,GAAGA,aAAa;MACxC,IAAI,CAACmG,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC;IAC/B;IACA,IAAIF,UAAU,CAACzH,KAAK,GAAGyH,UAAU,CAACtF,QAAQ,IAAIsF,UAAU,CAACzH,KAAK,GAAGyH,UAAU,CAAClF,QAAQ,EAAE;MACpFkF,UAAU,CAACxL,IAAI,GAAGwL,UAAU,CAACG,OAAO;IACtC;IACA,IAAIH,UAAU,CAAC1H,MAAM,GAAG0H,UAAU,CAACpF,SAAS,IAAIoF,UAAU,CAAC1H,MAAM,GAAG0H,UAAU,CAAChF,SAAS,EAAE;MACxFgF,UAAU,CAACrL,GAAG,GAAGqL,UAAU,CAACI,MAAM;IACpC;IACAJ,UAAU,CAACzH,KAAK,GAAG/H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACwE,UAAU,CAACzH,KAAK,EAAEyH,UAAU,CAAClF,QAAQ,CAAC,EAAEkF,UAAU,CAACtF,QAAQ,CAAC;IACjGsF,UAAU,CAAC1H,MAAM,GAAG9H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACwE,UAAU,CAAC1H,MAAM,EAAE0H,UAAU,CAAChF,SAAS,CAAC,EAAEgF,UAAU,CAACpF,SAAS,CAAC;IACrG,IAAI,CAACsF,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;IAC7BF,UAAU,CAACxL,IAAI,GAAGhE,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACwE,UAAU,CAACxL,IAAI,EAAEwL,UAAU,CAACY,OAAO,CAAC,EAAEZ,UAAU,CAACc,OAAO,CAAC;IAC7Fd,UAAU,CAACrL,GAAG,GAAGnE,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACwE,UAAU,CAACrL,GAAG,EAAEqL,UAAU,CAACa,MAAM,CAAC,EAAEb,UAAU,CAACe,MAAM,CAAC;IACzFf,UAAU,CAACG,OAAO,GAAGH,UAAU,CAACxL,IAAI;IACpCwL,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACrL,GAAG;IAClChE,QAAQ,CAAC,IAAI,CAACsK,MAAM,EAAElL,MAAM,CAAC;MAC3BwI,KAAK,EAAEyH,UAAU,CAACzH,KAAK;MACvBD,MAAM,EAAE0H,UAAU,CAAC1H;IACrB,CAAC,EAAE3C,aAAa,CAAC;MACfK,UAAU,EAAEgK,UAAU,CAACxL,IAAI;MAC3ByB,UAAU,EAAE+J,UAAU,CAACrL;IACzB,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACwM,WAAW,CAACH,OAAO,CAAC;IACzB,IAAI,IAAI,CAAC3B,OAAO,IAAI,IAAI,CAACY,OAAO,EAAE;MAChC,IAAI,CAACmB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/B;EACF,CAAC;EACDD,WAAW,EAAE,SAASA,WAAWA,CAACH,OAAO,EAAE;IACzC,IAAIhB,UAAU,GAAG,IAAI,CAACA,UAAU;MAC9BJ,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5B,IAAIrH,KAAK,GAAGqH,SAAS,CAAC/F,YAAY,IAAImG,UAAU,CAACzH,KAAK,GAAGyH,UAAU,CAACnG,YAAY,CAAC;IACjF,IAAIvB,MAAM,GAAGsH,SAAS,CAAC7F,aAAa,IAAIiG,UAAU,CAAC1H,MAAM,GAAG0H,UAAU,CAACjG,aAAa,CAAC;IACrFhK,MAAM,CAAC6P,SAAS,EAAE;MAChBrH,KAAK,EAAEA,KAAK;MACZD,MAAM,EAAEA,MAAM;MACd9D,IAAI,EAAE,CAACwL,UAAU,CAACzH,KAAK,GAAGA,KAAK,IAAI,CAAC;MACpC5D,GAAG,EAAE,CAACqL,UAAU,CAAC1H,MAAM,GAAGA,MAAM,IAAI;IACtC,CAAC,CAAC;IACF3H,QAAQ,CAAC,IAAI,CAAC4I,KAAK,EAAExJ,MAAM,CAAC;MAC1BwI,KAAK,EAAEqH,SAAS,CAACrH,KAAK;MACtBD,MAAM,EAAEsH,SAAS,CAACtH;IACpB,CAAC,EAAE3C,aAAa,CAAC5F,MAAM,CAAC;MACtBiG,UAAU,EAAE4J,SAAS,CAACpL,IAAI;MAC1ByB,UAAU,EAAE2J,SAAS,CAACjL;IACxB,CAAC,EAAEiL,SAAS,CAAC,CAAC,CAAC,CAAC;IAChB,IAAIoB,OAAO,EAAE;MACX,IAAI,CAACK,MAAM,CAAC,CAAC;IACf;EACF,CAAC;EACDlC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC,IAAItM,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBmN,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9B,IAAInT,WAAW,GAAGgG,OAAO,CAAChG,WAAW,IAAIgG,OAAO,CAAClG,kBAAkB;IACnE,IAAIe,YAAY,GAAGjH,MAAM,CAACoM,OAAO,CAACnF,YAAY,CAAC,IAAI,GAAG;IACtD,IAAI8S,WAAW,GAAG;MAChBjI,KAAK,EAAEyH,UAAU,CAACzH,KAAK;MACvBD,MAAM,EAAE0H,UAAU,CAAC1H;IACrB,CAAC;IACD,IAAIzL,WAAW,EAAE;MACf,IAAImT,UAAU,CAAC1H,MAAM,GAAGzL,WAAW,GAAGmT,UAAU,CAACzH,KAAK,EAAE;QACtDiI,WAAW,CAAClI,MAAM,GAAGkI,WAAW,CAACjI,KAAK,GAAG1L,WAAW;MACtD,CAAC,MAAM;QACL2T,WAAW,CAACjI,KAAK,GAAGiI,WAAW,CAAClI,MAAM,GAAGzL,WAAW;MACtD;IACF;IACA,IAAI,CAAC2T,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACY,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;;IAE7B;IACAZ,WAAW,CAACjI,KAAK,GAAG/H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAACjI,KAAK,EAAEiI,WAAW,CAAC1F,QAAQ,CAAC,EAAE0F,WAAW,CAAC9F,QAAQ,CAAC;IACrG8F,WAAW,CAAClI,MAAM,GAAG9H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAAClI,MAAM,EAAEkI,WAAW,CAACxF,SAAS,CAAC,EAAEwF,WAAW,CAAC5F,SAAS,CAAC;;IAEzG;IACA4F,WAAW,CAACjI,KAAK,GAAG/H,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAAC1F,QAAQ,EAAE0F,WAAW,CAACjI,KAAK,GAAG7K,YAAY,CAAC;IACpF8S,WAAW,CAAClI,MAAM,GAAG9H,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAACxF,SAAS,EAAEwF,WAAW,CAAClI,MAAM,GAAG5K,YAAY,CAAC;IACvF8S,WAAW,CAAChM,IAAI,GAAGwL,UAAU,CAACxL,IAAI,GAAG,CAACwL,UAAU,CAACzH,KAAK,GAAGiI,WAAW,CAACjI,KAAK,IAAI,CAAC;IAC/EiI,WAAW,CAAC7L,GAAG,GAAGqL,UAAU,CAACrL,GAAG,GAAG,CAACqL,UAAU,CAAC1H,MAAM,GAAGkI,WAAW,CAAClI,MAAM,IAAI,CAAC;IAC/EkI,WAAW,CAACL,OAAO,GAAGK,WAAW,CAAChM,IAAI;IACtCgM,WAAW,CAACJ,MAAM,GAAGI,WAAW,CAAC7L,GAAG;IACpC,IAAI,CAAC2M,kBAAkB,GAAGvR,MAAM,CAAC,CAAC,CAAC,EAAEyQ,WAAW,CAAC;EACnD,CAAC;EACDY,YAAY,EAAE,SAASA,YAAYA,CAACd,WAAW,EAAEC,eAAe,EAAE;IAChE,IAAI1N,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB4M,aAAa,GAAG,IAAI,CAACA,aAAa;MAClCO,UAAU,GAAG,IAAI,CAACA,UAAU;MAC5BQ,WAAW,GAAG,IAAI,CAACA,WAAW;MAC9BP,OAAO,GAAG,IAAI,CAACA,OAAO;IACxB,IAAIpT,WAAW,GAAGgG,OAAO,CAAChG,WAAW;IACrC,IAAIyT,WAAW,EAAE;MACf,IAAI/R,eAAe,GAAG9H,MAAM,CAACoM,OAAO,CAACtE,eAAe,CAAC,IAAI,CAAC;MAC1D,IAAIC,gBAAgB,GAAG/H,MAAM,CAACoM,OAAO,CAACrE,gBAAgB,CAAC,IAAI,CAAC;MAC5D,IAAI+S,eAAe,GAAGtB,OAAO,GAAGzP,IAAI,CAAC+K,GAAG,CAACkE,aAAa,CAAClH,KAAK,EAAEyH,UAAU,CAACzH,KAAK,EAAEyH,UAAU,CAACzH,KAAK,GAAGyH,UAAU,CAACxL,IAAI,EAAEiL,aAAa,CAAClH,KAAK,GAAGyH,UAAU,CAACxL,IAAI,CAAC,GAAGiL,aAAa,CAAClH,KAAK;MAChL,IAAIiJ,gBAAgB,GAAGvB,OAAO,GAAGzP,IAAI,CAAC+K,GAAG,CAACkE,aAAa,CAACnH,MAAM,EAAE0H,UAAU,CAAC1H,MAAM,EAAE0H,UAAU,CAAC1H,MAAM,GAAG0H,UAAU,CAACrL,GAAG,EAAE8K,aAAa,CAACnH,MAAM,GAAG0H,UAAU,CAACrL,GAAG,CAAC,GAAG8K,aAAa,CAACnH,MAAM;;MAEpL;MACA/J,eAAe,GAAGiC,IAAI,CAAC+K,GAAG,CAAChN,eAAe,EAAEkR,aAAa,CAAClH,KAAK,CAAC;MAChE/J,gBAAgB,GAAGgC,IAAI,CAAC+K,GAAG,CAAC/M,gBAAgB,EAAEiR,aAAa,CAACnH,MAAM,CAAC;MACnE,IAAIzL,WAAW,EAAE;QACf,IAAI0B,eAAe,IAAIC,gBAAgB,EAAE;UACvC,IAAIA,gBAAgB,GAAG3B,WAAW,GAAG0B,eAAe,EAAE;YACpDC,gBAAgB,GAAGD,eAAe,GAAG1B,WAAW;UAClD,CAAC,MAAM;YACL0B,eAAe,GAAGC,gBAAgB,GAAG3B,WAAW;UAClD;QACF,CAAC,MAAM,IAAI0B,eAAe,EAAE;UAC1BC,gBAAgB,GAAGD,eAAe,GAAG1B,WAAW;QAClD,CAAC,MAAM,IAAI2B,gBAAgB,EAAE;UAC3BD,eAAe,GAAGC,gBAAgB,GAAG3B,WAAW;QAClD;QACA,IAAI2U,gBAAgB,GAAG3U,WAAW,GAAG0U,eAAe,EAAE;UACpDC,gBAAgB,GAAGD,eAAe,GAAG1U,WAAW;QAClD,CAAC,MAAM;UACL0U,eAAe,GAAGC,gBAAgB,GAAG3U,WAAW;QAClD;MACF;;MAEA;MACA2T,WAAW,CAAC1F,QAAQ,GAAGtK,IAAI,CAAC+K,GAAG,CAAChN,eAAe,EAAEgT,eAAe,CAAC;MACjEf,WAAW,CAACxF,SAAS,GAAGxK,IAAI,CAAC+K,GAAG,CAAC/M,gBAAgB,EAAEgT,gBAAgB,CAAC;MACpEhB,WAAW,CAAC9F,QAAQ,GAAG6G,eAAe;MACtCf,WAAW,CAAC5F,SAAS,GAAG4G,gBAAgB;IAC1C;IACA,IAAIjB,eAAe,EAAE;MACnB,IAAIN,OAAO,EAAE;QACXO,WAAW,CAACI,OAAO,GAAGpQ,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEwE,UAAU,CAACxL,IAAI,CAAC;QAClDgM,WAAW,CAACK,MAAM,GAAGrQ,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEwE,UAAU,CAACrL,GAAG,CAAC;QAChD6L,WAAW,CAACM,OAAO,GAAGtQ,IAAI,CAAC+K,GAAG,CAACkE,aAAa,CAAClH,KAAK,EAAEyH,UAAU,CAACxL,IAAI,GAAGwL,UAAU,CAACzH,KAAK,CAAC,GAAGiI,WAAW,CAACjI,KAAK;QAC3GiI,WAAW,CAACO,MAAM,GAAGvQ,IAAI,CAAC+K,GAAG,CAACkE,aAAa,CAACnH,MAAM,EAAE0H,UAAU,CAACrL,GAAG,GAAGqL,UAAU,CAAC1H,MAAM,CAAC,GAAGkI,WAAW,CAAClI,MAAM;MAC9G,CAAC,MAAM;QACLkI,WAAW,CAACI,OAAO,GAAG,CAAC;QACvBJ,WAAW,CAACK,MAAM,GAAG,CAAC;QACtBL,WAAW,CAACM,OAAO,GAAGrB,aAAa,CAAClH,KAAK,GAAGiI,WAAW,CAACjI,KAAK;QAC7DiI,WAAW,CAACO,MAAM,GAAGtB,aAAa,CAACnH,MAAM,GAAGkI,WAAW,CAAClI,MAAM;MAChE;IACF;EACF,CAAC;EACDgH,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;IACtC,IAAIzM,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB4M,aAAa,GAAG,IAAI,CAACA,aAAa;MAClCe,WAAW,GAAG,IAAI,CAACA,WAAW;IAChC,IAAIA,WAAW,CAACjI,KAAK,GAAGiI,WAAW,CAAC9F,QAAQ,IAAI8F,WAAW,CAACjI,KAAK,GAAGiI,WAAW,CAAC1F,QAAQ,EAAE;MACxF0F,WAAW,CAAChM,IAAI,GAAGgM,WAAW,CAACL,OAAO;IACxC;IACA,IAAIK,WAAW,CAAClI,MAAM,GAAGkI,WAAW,CAAC5F,SAAS,IAAI4F,WAAW,CAAClI,MAAM,GAAGkI,WAAW,CAACxF,SAAS,EAAE;MAC5FwF,WAAW,CAAC7L,GAAG,GAAG6L,WAAW,CAACJ,MAAM;IACtC;IACAI,WAAW,CAACjI,KAAK,GAAG/H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAACjI,KAAK,EAAEiI,WAAW,CAAC1F,QAAQ,CAAC,EAAE0F,WAAW,CAAC9F,QAAQ,CAAC;IACrG8F,WAAW,CAAClI,MAAM,GAAG9H,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAAClI,MAAM,EAAEkI,WAAW,CAACxF,SAAS,CAAC,EAAEwF,WAAW,CAAC5F,SAAS,CAAC;IACzG,IAAI,CAACwG,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;IAC9BZ,WAAW,CAAChM,IAAI,GAAGhE,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAAChM,IAAI,EAAEgM,WAAW,CAACI,OAAO,CAAC,EAAEJ,WAAW,CAACM,OAAO,CAAC;IACjGN,WAAW,CAAC7L,GAAG,GAAGnE,IAAI,CAAC+K,GAAG,CAAC/K,IAAI,CAACgL,GAAG,CAACgF,WAAW,CAAC7L,GAAG,EAAE6L,WAAW,CAACK,MAAM,CAAC,EAAEL,WAAW,CAACO,MAAM,CAAC;IAC7FP,WAAW,CAACL,OAAO,GAAGK,WAAW,CAAChM,IAAI;IACtCgM,WAAW,CAACJ,MAAM,GAAGI,WAAW,CAAC7L,GAAG;IACpC,IAAI9B,OAAO,CAAClF,OAAO,IAAIkF,OAAO,CAAC3E,cAAc,EAAE;MAC7C;MACAkE,OAAO,CAAC,IAAI,CAACqP,IAAI,EAAE5W,WAAW,EAAE2V,WAAW,CAACjI,KAAK,IAAIkH,aAAa,CAAClH,KAAK,IAAIiI,WAAW,CAAClI,MAAM,IAAImH,aAAa,CAACnH,MAAM,GAAG3O,WAAW,GAAGF,UAAU,CAAC;IACpJ;IACAkH,QAAQ,CAAC,IAAI,CAAC+Q,OAAO,EAAE3R,MAAM,CAAC;MAC5BwI,KAAK,EAAEiI,WAAW,CAACjI,KAAK;MACxBD,MAAM,EAAEkI,WAAW,CAAClI;IACtB,CAAC,EAAE3C,aAAa,CAAC;MACfK,UAAU,EAAEwK,WAAW,CAAChM,IAAI;MAC5ByB,UAAU,EAAEuK,WAAW,CAAC7L;IAC1B,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,IAAI,CAAC0K,OAAO,IAAI,IAAI,CAACY,OAAO,EAAE;MAChC,IAAI,CAACC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC,IAAI,CAACyB,QAAQ,EAAE;MAClB,IAAI,CAACN,MAAM,CAAC,CAAC;IACf;EACF,CAAC;EACDA,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,IAAI,CAACtU,OAAO,CAAC,CAAC;IACd8G,aAAa,CAAC,IAAI,CAACjD,OAAO,EAAE1F,UAAU,EAAE,IAAI,CAAC+G,OAAO,CAAC,CAAC,CAAC;EACzD;AACF,CAAC;AAED,IAAIlF,OAAO,GAAG;EACZ6U,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC,IAAIhR,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBiR,WAAW,GAAG,IAAI,CAACA,WAAW;IAChC,IAAI9U,OAAO,GAAG,IAAI,CAAC8F,OAAO,CAAC9F,OAAO;IAClC,IAAIkI,GAAG,GAAG4M,WAAW,GAAG,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC7M,GAAG;IACtD,IAAI8M,GAAG,GAAGnR,OAAO,CAACmR,GAAG,IAAI,sBAAsB;IAC/C,IAAIxI,KAAK,GAAGpQ,QAAQ,CAAC+R,aAAa,CAAC,KAAK,CAAC;IACzC,IAAI2G,WAAW,EAAE;MACftI,KAAK,CAACsI,WAAW,GAAGA,WAAW;IACjC;IACAtI,KAAK,CAACyI,GAAG,GAAG/M,GAAG;IACfsE,KAAK,CAACwI,GAAG,GAAGA,GAAG;IACf,IAAI,CAACE,OAAO,CAACC,WAAW,CAAC3I,KAAK,CAAC;IAC/B,IAAI,CAAC4I,YAAY,GAAG5I,KAAK;IACzB,IAAI,CAACxM,OAAO,EAAE;MACZ;IACF;IACA,IAAIqV,QAAQ,GAAGrV,OAAO;IACtB,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/BqV,QAAQ,GAAGxR,OAAO,CAACyR,aAAa,CAACC,gBAAgB,CAACvV,OAAO,CAAC;IAC5D,CAAC,MAAM,IAAIA,OAAO,CAACwV,aAAa,EAAE;MAChCH,QAAQ,GAAG,CAACrV,OAAO,CAAC;IACtB;IACA,IAAI,CAACqV,QAAQ,GAAGA,QAAQ;IACxBvc,OAAO,CAACuc,QAAQ,EAAE,UAAUI,EAAE,EAAE;MAC9B,IAAIC,GAAG,GAAGtZ,QAAQ,CAAC+R,aAAa,CAAC,KAAK,CAAC;;MAEvC;MACA9I,OAAO,CAACoQ,EAAE,EAAE1X,YAAY,EAAE;QACxByN,KAAK,EAAEiK,EAAE,CAAC9C,WAAW;QACrBpH,MAAM,EAAEkK,EAAE,CAAC7C,YAAY;QACvB+C,IAAI,EAAEF,EAAE,CAACG;MACX,CAAC,CAAC;MACF,IAAId,WAAW,EAAE;QACfY,GAAG,CAACZ,WAAW,GAAGA,WAAW;MAC/B;MACAY,GAAG,CAACT,GAAG,GAAG/M,GAAG;MACbwN,GAAG,CAACV,GAAG,GAAGA,GAAG;;MAEb;AACN;AACA;AACA;AACA;AACA;MACMU,GAAG,CAAC3R,KAAK,CAAC8R,OAAO,GAAG,gBAAgB,GAAG,aAAa,GAAG,cAAc,GAAG,wBAAwB,GAAG,yBAAyB,GAAG,2BAA2B,GAAG,4BAA4B,GAAG,oCAAoC;MAChOJ,EAAE,CAACG,SAAS,GAAG,EAAE;MACjBH,EAAE,CAACN,WAAW,CAACO,GAAG,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;EACDI,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpChd,OAAO,CAAC,IAAI,CAACuc,QAAQ,EAAE,UAAUxR,OAAO,EAAE;MACxC,IAAI9D,IAAI,GAAGmF,OAAO,CAACrB,OAAO,EAAE9F,YAAY,CAAC;MACzC6F,QAAQ,CAACC,OAAO,EAAE;QAChB2H,KAAK,EAAEzL,IAAI,CAACyL,KAAK;QACjBD,MAAM,EAAExL,IAAI,CAACwL;MACf,CAAC,CAAC;MACF1H,OAAO,CAAC+R,SAAS,GAAG7V,IAAI,CAAC4V,IAAI;MAC7BpQ,UAAU,CAAC1B,OAAO,EAAE9F,YAAY,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EACDiC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAI6S,SAAS,GAAG,IAAI,CAACA,SAAS;MAC5BI,UAAU,GAAG,IAAI,CAACA,UAAU;MAC5BQ,WAAW,GAAG,IAAI,CAACA,WAAW;IAChC,IAAIsC,YAAY,GAAGtC,WAAW,CAACjI,KAAK;MAClCwK,aAAa,GAAGvC,WAAW,CAAClI,MAAM;IACpC,IAAIC,KAAK,GAAGqH,SAAS,CAACrH,KAAK;MACzBD,MAAM,GAAGsH,SAAS,CAACtH,MAAM;IAC3B,IAAI9D,IAAI,GAAGgM,WAAW,CAAChM,IAAI,GAAGwL,UAAU,CAACxL,IAAI,GAAGoL,SAAS,CAACpL,IAAI;IAC9D,IAAIG,GAAG,GAAG6L,WAAW,CAAC7L,GAAG,GAAGqL,UAAU,CAACrL,GAAG,GAAGiL,SAAS,CAACjL,GAAG;IAC1D,IAAI,CAAC,IAAI,CAAC0K,OAAO,IAAI,IAAI,CAACsC,QAAQ,EAAE;MAClC;IACF;IACAhR,QAAQ,CAAC,IAAI,CAACwR,YAAY,EAAEpS,MAAM,CAAC;MACjCwI,KAAK,EAAEA,KAAK;MACZD,MAAM,EAAEA;IACV,CAAC,EAAE3C,aAAa,CAAC5F,MAAM,CAAC;MACtBiG,UAAU,EAAE,CAACxB,IAAI;MACjByB,UAAU,EAAE,CAACtB;IACf,CAAC,EAAEiL,SAAS,CAAC,CAAC,CAAC,CAAC;IAChB/Z,OAAO,CAAC,IAAI,CAACuc,QAAQ,EAAE,UAAUxR,OAAO,EAAE;MACxC,IAAI9D,IAAI,GAAGmF,OAAO,CAACrB,OAAO,EAAE9F,YAAY,CAAC;MACzC,IAAIkY,aAAa,GAAGlW,IAAI,CAACyL,KAAK;MAC9B,IAAI0K,cAAc,GAAGnW,IAAI,CAACwL,MAAM;MAChC,IAAIc,QAAQ,GAAG4J,aAAa;MAC5B,IAAI3J,SAAS,GAAG4J,cAAc;MAC9B,IAAIvL,KAAK,GAAG,CAAC;MACb,IAAIoL,YAAY,EAAE;QAChBpL,KAAK,GAAGsL,aAAa,GAAGF,YAAY;QACpCzJ,SAAS,GAAG0J,aAAa,GAAGrL,KAAK;MACnC;MACA,IAAIqL,aAAa,IAAI1J,SAAS,GAAG4J,cAAc,EAAE;QAC/CvL,KAAK,GAAGuL,cAAc,GAAGF,aAAa;QACtC3J,QAAQ,GAAG0J,YAAY,GAAGpL,KAAK;QAC/B2B,SAAS,GAAG4J,cAAc;MAC5B;MACAtS,QAAQ,CAACC,OAAO,EAAE;QAChB2H,KAAK,EAAEa,QAAQ;QACfd,MAAM,EAAEe;MACV,CAAC,CAAC;MACF1I,QAAQ,CAACC,OAAO,CAACsS,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEnT,MAAM,CAAC;QACtDwI,KAAK,EAAEA,KAAK,GAAGb,KAAK;QACpBY,MAAM,EAAEA,MAAM,GAAGZ;MACnB,CAAC,EAAE/B,aAAa,CAAC5F,MAAM,CAAC;QACtBiG,UAAU,EAAE,CAACxB,IAAI,GAAGkD,KAAK;QACzBzB,UAAU,EAAE,CAACtB,GAAG,GAAG+C;MACrB,CAAC,EAAEkI,SAAS,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAIuD,MAAM,GAAG;EACXC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;IACpB,IAAIxS,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBiC,OAAO,GAAG,IAAI,CAACA,OAAO;MACtB2M,OAAO,GAAG,IAAI,CAACA,OAAO;IACxB,IAAI5P,UAAU,CAACiD,OAAO,CAACjE,SAAS,CAAC,EAAE;MACjC4E,WAAW,CAAC5C,OAAO,EAAEvF,gBAAgB,EAAEwH,OAAO,CAACjE,SAAS,CAAC;IAC3D;IACA,IAAIgB,UAAU,CAACiD,OAAO,CAAChE,QAAQ,CAAC,EAAE;MAChC2E,WAAW,CAAC5C,OAAO,EAAExF,eAAe,EAAEyH,OAAO,CAAChE,QAAQ,CAAC;IACzD;IACA,IAAIe,UAAU,CAACiD,OAAO,CAAC/D,OAAO,CAAC,EAAE;MAC/B0E,WAAW,CAAC5C,OAAO,EAAEzF,cAAc,EAAE0H,OAAO,CAAC/D,OAAO,CAAC;IACvD;IACA,IAAIc,UAAU,CAACiD,OAAO,CAAC9D,IAAI,CAAC,EAAE;MAC5ByE,WAAW,CAAC5C,OAAO,EAAE1F,UAAU,EAAE2H,OAAO,CAAC9D,IAAI,CAAC;IAChD;IACA,IAAIa,UAAU,CAACiD,OAAO,CAAC7D,IAAI,CAAC,EAAE;MAC5BwE,WAAW,CAAC5C,OAAO,EAAE5E,UAAU,EAAE6G,OAAO,CAAC7D,IAAI,CAAC;IAChD;IACAwE,WAAW,CAACgM,OAAO,EAAE9T,kBAAkB,EAAE,IAAI,CAAC2X,WAAW,GAAG,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IACtF,IAAIvQ,OAAO,CAAC/E,QAAQ,IAAI+E,OAAO,CAAC7E,WAAW,EAAE;MAC3CwF,WAAW,CAACgM,OAAO,EAAEzT,WAAW,EAAE,IAAI,CAACwX,OAAO,GAAG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC,EAAE;QACtEK,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACA,IAAI7Q,OAAO,CAACzE,wBAAwB,EAAE;MACpCoF,WAAW,CAACgM,OAAO,EAAElU,cAAc,EAAE,IAAI,CAACqY,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IAClF;IACA5P,WAAW,CAAC5C,OAAO,CAACyR,aAAa,EAAE1W,kBAAkB,EAAE,IAAI,CAACkY,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC;IAClG5P,WAAW,CAAC5C,OAAO,CAACyR,aAAa,EAAEzW,gBAAgB,EAAE,IAAI,CAACmY,SAAS,GAAG,IAAI,CAACC,OAAO,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9F,IAAIvQ,OAAO,CAAC7F,UAAU,EAAE;MACtBwG,WAAW,CAACtK,MAAM,EAAE4C,YAAY,EAAE,IAAI,CAACmY,QAAQ,GAAG,IAAI,CAACC,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3E;EACF,CAAC;EACDe,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,IAAIvT,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBiC,OAAO,GAAG,IAAI,CAACA,OAAO;MACtB2M,OAAO,GAAG,IAAI,CAACA,OAAO;IACxB,IAAI5P,UAAU,CAACiD,OAAO,CAACjE,SAAS,CAAC,EAAE;MACjCsE,cAAc,CAACtC,OAAO,EAAEvF,gBAAgB,EAAEwH,OAAO,CAACjE,SAAS,CAAC;IAC9D;IACA,IAAIgB,UAAU,CAACiD,OAAO,CAAChE,QAAQ,CAAC,EAAE;MAChCqE,cAAc,CAACtC,OAAO,EAAExF,eAAe,EAAEyH,OAAO,CAAChE,QAAQ,CAAC;IAC5D;IACA,IAAIe,UAAU,CAACiD,OAAO,CAAC/D,OAAO,CAAC,EAAE;MAC/BoE,cAAc,CAACtC,OAAO,EAAEzF,cAAc,EAAE0H,OAAO,CAAC/D,OAAO,CAAC;IAC1D;IACA,IAAIc,UAAU,CAACiD,OAAO,CAAC9D,IAAI,CAAC,EAAE;MAC5BmE,cAAc,CAACtC,OAAO,EAAE1F,UAAU,EAAE2H,OAAO,CAAC9D,IAAI,CAAC;IACnD;IACA,IAAIa,UAAU,CAACiD,OAAO,CAAC7D,IAAI,CAAC,EAAE;MAC5BkE,cAAc,CAACtC,OAAO,EAAE5E,UAAU,EAAE6G,OAAO,CAAC7D,IAAI,CAAC;IACnD;IACAkE,cAAc,CAACsM,OAAO,EAAE9T,kBAAkB,EAAE,IAAI,CAAC2X,WAAW,CAAC;IAC7D,IAAIxQ,OAAO,CAAC/E,QAAQ,IAAI+E,OAAO,CAAC7E,WAAW,EAAE;MAC3CkF,cAAc,CAACsM,OAAO,EAAEzT,WAAW,EAAE,IAAI,CAACwX,OAAO,EAAE;QACjDE,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACA,IAAI7Q,OAAO,CAACzE,wBAAwB,EAAE;MACpC8E,cAAc,CAACsM,OAAO,EAAElU,cAAc,EAAE,IAAI,CAACqY,UAAU,CAAC;IAC1D;IACAzQ,cAAc,CAACtC,OAAO,CAACyR,aAAa,EAAE1W,kBAAkB,EAAE,IAAI,CAACkY,UAAU,CAAC;IAC1E3Q,cAAc,CAACtC,OAAO,CAACyR,aAAa,EAAEzW,gBAAgB,EAAE,IAAI,CAACmY,SAAS,CAAC;IACvE,IAAIlR,OAAO,CAAC7F,UAAU,EAAE;MACtBkG,cAAc,CAAChK,MAAM,EAAE4C,YAAY,EAAE,IAAI,CAACmY,QAAQ,CAAC;IACrD;EACF;AACF,CAAC;AAED,IAAIG,QAAQ,GAAG;EACbF,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACvC,QAAQ,EAAE;MACjB;IACF;IACA,IAAI9O,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB0M,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1BE,aAAa,GAAG,IAAI,CAACA,aAAa;IACpC,IAAI4E,MAAM,GAAG9E,SAAS,CAACG,WAAW,GAAGD,aAAa,CAAClH,KAAK;IACxD,IAAI+L,MAAM,GAAG/E,SAAS,CAACI,YAAY,GAAGF,aAAa,CAACnH,MAAM;IAC1D,IAAIZ,KAAK,GAAGlH,IAAI,CAACuG,GAAG,CAACsN,MAAM,GAAG,CAAC,CAAC,GAAG7T,IAAI,CAACuG,GAAG,CAACuN,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM;;IAEzE;IACA,IAAI5M,KAAK,KAAK,CAAC,EAAE;MACf,IAAIsI,UAAU;MACd,IAAIQ,WAAW;MACf,IAAI3N,OAAO,CAAC5F,OAAO,EAAE;QACnB+S,UAAU,GAAG,IAAI,CAACuE,aAAa,CAAC,CAAC;QACjC/D,WAAW,GAAG,IAAI,CAACgE,cAAc,CAAC,CAAC;MACrC;MACA,IAAI,CAACxF,MAAM,CAAC,CAAC;MACb,IAAInM,OAAO,CAAC5F,OAAO,EAAE;QACnB,IAAI,CAACwX,aAAa,CAAC5e,OAAO,CAACma,UAAU,EAAE,UAAUtX,CAAC,EAAErC,CAAC,EAAE;UACrD2Z,UAAU,CAAC3Z,CAAC,CAAC,GAAGqC,CAAC,GAAGgP,KAAK;QAC3B,CAAC,CAAC,CAAC;QACH,IAAI,CAACgN,cAAc,CAAC7e,OAAO,CAAC2a,WAAW,EAAE,UAAU9X,CAAC,EAAErC,CAAC,EAAE;UACvDma,WAAW,CAACna,CAAC,CAAC,GAAGqC,CAAC,GAAGgP,KAAK;QAC5B,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;EACDkM,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACjC,QAAQ,IAAI,IAAI,CAAC9O,OAAO,CAACnG,QAAQ,KAAKzB,cAAc,EAAE;MAC7D;IACF;IACA,IAAI,CAAC0Z,WAAW,CAAC3T,QAAQ,CAAC,IAAI,CAAC4T,OAAO,EAAEva,UAAU,CAAC,GAAGW,cAAc,GAAGD,cAAc,CAAC;EACxF,CAAC;EACDyY,KAAK,EAAE,SAASA,KAAKA,CAAClQ,KAAK,EAAE;IAC3B,IAAIuR,KAAK,GAAG,IAAI;IAChB,IAAInN,KAAK,GAAGjR,MAAM,CAAC,IAAI,CAACoM,OAAO,CAAC5E,cAAc,CAAC,IAAI,GAAG;IACtD,IAAI6W,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACnD,QAAQ,EAAE;MACjB;IACF;IACArO,KAAK,CAACyR,cAAc,CAAC,CAAC;;IAEtB;IACA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjB;IACF;IACA,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpBC,UAAU,CAAC,YAAY;MACrBJ,KAAK,CAACG,QAAQ,GAAG,KAAK;IACxB,CAAC,EAAE,EAAE,CAAC;IACN,IAAI1R,KAAK,CAAC4R,MAAM,EAAE;MAChBJ,KAAK,GAAGxR,KAAK,CAAC4R,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC,MAAM,IAAI5R,KAAK,CAAC6R,UAAU,EAAE;MAC3BL,KAAK,GAAG,CAACxR,KAAK,CAAC6R,UAAU,GAAG,GAAG;IACjC,CAAC,MAAM,IAAI7R,KAAK,CAACU,MAAM,EAAE;MACvB8Q,KAAK,GAAGxR,KAAK,CAACU,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC;IACA,IAAI,CAAChF,IAAI,CAAC,CAAC8V,KAAK,GAAGpN,KAAK,EAAEpE,KAAK,CAAC;EAClC,CAAC;EACDgQ,SAAS,EAAE,SAASA,SAASA,CAAChQ,KAAK,EAAE;IACnC,IAAI8R,OAAO,GAAG9R,KAAK,CAAC8R,OAAO;MACzBC,MAAM,GAAG/R,KAAK,CAAC+R,MAAM;IACvB,IAAI,IAAI,CAAC1D;;IAET;IAAA,GACG,CAACrO,KAAK,CAACH,IAAI,KAAK,WAAW,IAAIG,KAAK,CAACH,IAAI,KAAK,aAAa,IAAIG,KAAK,CAACgS,WAAW,KAAK,OAAO;IAC/F;IACAnW,QAAQ,CAACiW,OAAO,CAAC,IAAIA,OAAO,KAAK,CAAC,IAAIjW,QAAQ,CAACkW,MAAM,CAAC,IAAIA,MAAM,KAAK;;IAErE;IAAA,GACG/R,KAAK,CAACiS,OAAO,CAAC,EAAE;MACjB;IACF;IACA,IAAI1S,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB2D,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1B,IAAIgP,MAAM;IACV,IAAIlS,KAAK,CAACmS,cAAc,EAAE;MACxB;MACA5f,OAAO,CAACyN,KAAK,CAACmS,cAAc,EAAE,UAAUC,KAAK,EAAE;QAC7ClP,QAAQ,CAACkP,KAAK,CAACC,UAAU,CAAC,GAAGhO,UAAU,CAAC+N,KAAK,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAlP,QAAQ,CAAClD,KAAK,CAACsD,SAAS,IAAI,CAAC,CAAC,GAAGe,UAAU,CAACrE,KAAK,CAAC;IACpD;IACA,IAAIrO,MAAM,CAACC,IAAI,CAACsR,QAAQ,CAAC,CAAC5Q,MAAM,GAAG,CAAC,IAAIiN,OAAO,CAAC/E,QAAQ,IAAI+E,OAAO,CAAC9E,WAAW,EAAE;MAC/EyX,MAAM,GAAG5b,WAAW;IACtB,CAAC,MAAM;MACL4b,MAAM,GAAGvT,OAAO,CAACqB,KAAK,CAACnM,MAAM,EAAE0D,WAAW,CAAC;IAC7C;IACA,IAAI,CAACqB,cAAc,CAACpD,IAAI,CAAC0c,MAAM,CAAC,EAAE;MAChC;IACF;IACA,IAAI3R,aAAa,CAAC,IAAI,CAACjD,OAAO,EAAEvF,gBAAgB,EAAE;MAChDua,aAAa,EAAEtS,KAAK;MACpBkS,MAAM,EAAEA;IACV,CAAC,CAAC,KAAK,KAAK,EAAE;MACZ;IACF;;IAEA;IACAlS,KAAK,CAACyR,cAAc,CAAC,CAAC;IACtB,IAAI,CAACS,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACK,QAAQ,GAAG,KAAK;IACrB,IAAIL,MAAM,KAAK9b,WAAW,EAAE;MAC1B,IAAI,CAACmc,QAAQ,GAAG,IAAI;MACpBxU,QAAQ,CAAC,IAAI,CAACuT,OAAO,EAAEja,WAAW,CAAC;IACrC;EACF,CAAC;EACDmZ,QAAQ,EAAE,SAASA,QAAQA,CAACxQ,KAAK,EAAE;IACjC,IAAIkS,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI,IAAI,CAAC7D,QAAQ,IAAI,CAAC6D,MAAM,EAAE;MAC5B;IACF;IACA,IAAIhP,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5BlD,KAAK,CAACyR,cAAc,CAAC,CAAC;IACtB,IAAIlR,aAAa,CAAC,IAAI,CAACjD,OAAO,EAAExF,eAAe,EAAE;MAC/Cwa,aAAa,EAAEtS,KAAK;MACpBkS,MAAM,EAAEA;IACV,CAAC,CAAC,KAAK,KAAK,EAAE;MACZ;IACF;IACA,IAAIlS,KAAK,CAACmS,cAAc,EAAE;MACxB5f,OAAO,CAACyN,KAAK,CAACmS,cAAc,EAAE,UAAUC,KAAK,EAAE;QAC7C;QACA3V,MAAM,CAACyG,QAAQ,CAACkP,KAAK,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAEhO,UAAU,CAAC+N,KAAK,EAAE,IAAI,CAAC,CAAC;MACnE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL3V,MAAM,CAACyG,QAAQ,CAAClD,KAAK,CAACsD,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEe,UAAU,CAACrE,KAAK,EAAE,IAAI,CAAC,CAAC;IACvE;IACA,IAAI,CAACwS,MAAM,CAACxS,KAAK,CAAC;EACpB,CAAC;EACD0Q,OAAO,EAAE,SAASA,OAAOA,CAAC1Q,KAAK,EAAE;IAC/B,IAAI,IAAI,CAACqO,QAAQ,EAAE;MACjB;IACF;IACA,IAAI6D,MAAM,GAAG,IAAI,CAACA,MAAM;MACtBhP,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1B,IAAIlD,KAAK,CAACmS,cAAc,EAAE;MACxB5f,OAAO,CAACyN,KAAK,CAACmS,cAAc,EAAE,UAAUC,KAAK,EAAE;QAC7C,OAAOlP,QAAQ,CAACkP,KAAK,CAACC,UAAU,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOnP,QAAQ,CAAClD,KAAK,CAACsD,SAAS,IAAI,CAAC,CAAC;IACvC;IACA,IAAI,CAAC4O,MAAM,EAAE;MACX;IACF;IACAlS,KAAK,CAACyR,cAAc,CAAC,CAAC;IACtB,IAAI,CAAC9f,MAAM,CAACC,IAAI,CAACsR,QAAQ,CAAC,CAAC5Q,MAAM,EAAE;MACjC,IAAI,CAAC4f,MAAM,GAAG,EAAE;IAClB;IACA,IAAI,IAAI,CAACK,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrBjU,WAAW,CAAC,IAAI,CAACgT,OAAO,EAAEja,WAAW,EAAE,IAAI,CAAC0U,OAAO,IAAI,IAAI,CAACxM,OAAO,CAACzF,KAAK,CAAC;IAC5E;IACAyG,aAAa,CAAC,IAAI,CAACjD,OAAO,EAAEzF,cAAc,EAAE;MAC1Cya,aAAa,EAAEtS,KAAK;MACpBkS,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAIM,MAAM,GAAG;EACXA,MAAM,EAAE,SAASA,MAAMA,CAACxS,KAAK,EAAE;IAC7B,IAAIT,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBmN,UAAU,GAAG,IAAI,CAACA,UAAU;MAC5BP,aAAa,GAAG,IAAI,CAACA,aAAa;MAClCe,WAAW,GAAG,IAAI,CAACA,WAAW;MAC9BhK,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1B,IAAIgP,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI3Y,WAAW,GAAGgG,OAAO,CAAChG,WAAW;IACrC,IAAI2H,IAAI,GAAGgM,WAAW,CAAChM,IAAI;MACzBG,GAAG,GAAG6L,WAAW,CAAC7L,GAAG;MACrB4D,KAAK,GAAGiI,WAAW,CAACjI,KAAK;MACzBD,MAAM,GAAGkI,WAAW,CAAClI,MAAM;IAC7B,IAAIyN,KAAK,GAAGvR,IAAI,GAAG+D,KAAK;IACxB,IAAIyN,MAAM,GAAGrR,GAAG,GAAG2D,MAAM;IACzB,IAAIsI,OAAO,GAAG,CAAC;IACf,IAAIC,MAAM,GAAG,CAAC;IACd,IAAInG,QAAQ,GAAG+E,aAAa,CAAClH,KAAK;IAClC,IAAIqC,SAAS,GAAG6E,aAAa,CAACnH,MAAM;IACpC,IAAI2N,UAAU,GAAG,IAAI;IACrB,IAAI5H,MAAM;;IAEV;IACA,IAAI,CAACxR,WAAW,IAAIyG,KAAK,CAAC4S,QAAQ,EAAE;MAClCrZ,WAAW,GAAG0L,KAAK,IAAID,MAAM,GAAGC,KAAK,GAAGD,MAAM,GAAG,CAAC;IACpD;IACA,IAAI,IAAI,CAAC2H,OAAO,EAAE;MAChBW,OAAO,GAAGJ,WAAW,CAACI,OAAO;MAC7BC,MAAM,GAAGL,WAAW,CAACK,MAAM;MAC3BnG,QAAQ,GAAGkG,OAAO,GAAGpQ,IAAI,CAAC+K,GAAG,CAACkE,aAAa,CAAClH,KAAK,EAAEyH,UAAU,CAACzH,KAAK,EAAEyH,UAAU,CAACxL,IAAI,GAAGwL,UAAU,CAACzH,KAAK,CAAC;MACxGqC,SAAS,GAAGiG,MAAM,GAAGrQ,IAAI,CAAC+K,GAAG,CAACkE,aAAa,CAACnH,MAAM,EAAE0H,UAAU,CAAC1H,MAAM,EAAE0H,UAAU,CAACrL,GAAG,GAAGqL,UAAU,CAAC1H,MAAM,CAAC;IAC5G;IACA,IAAI3B,OAAO,GAAGH,QAAQ,CAACvR,MAAM,CAACC,IAAI,CAACsR,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI2P,KAAK,GAAG;MACVC,CAAC,EAAEzP,OAAO,CAACS,IAAI,GAAGT,OAAO,CAACK,MAAM;MAChCqP,CAAC,EAAE1P,OAAO,CAACW,IAAI,GAAGX,OAAO,CAACO;IAC5B,CAAC;IACD,IAAIoP,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;MAC/B,QAAQA,IAAI;QACV,KAAK1c,WAAW;UACd,IAAIkc,KAAK,GAAGI,KAAK,CAACC,CAAC,GAAG1L,QAAQ,EAAE;YAC9ByL,KAAK,CAACC,CAAC,GAAG1L,QAAQ,GAAGqL,KAAK;UAC5B;UACA;QACF,KAAKjc,WAAW;UACd,IAAI0K,IAAI,GAAG2R,KAAK,CAACC,CAAC,GAAGxF,OAAO,EAAE;YAC5BuF,KAAK,CAACC,CAAC,GAAGxF,OAAO,GAAGpM,IAAI;UAC1B;UACA;QACF,KAAKxK,YAAY;UACf,IAAI2K,GAAG,GAAGwR,KAAK,CAACE,CAAC,GAAGxF,MAAM,EAAE;YAC1BsF,KAAK,CAACE,CAAC,GAAGxF,MAAM,GAAGlM,GAAG;UACxB;UACA;QACF,KAAK5K,YAAY;UACf,IAAIic,MAAM,GAAGG,KAAK,CAACE,CAAC,GAAGzL,SAAS,EAAE;YAChCuL,KAAK,CAACE,CAAC,GAAGzL,SAAS,GAAGoL,MAAM;UAC9B;UACA;MACJ;IACF,CAAC;IACD,QAAQR,MAAM;MACZ;MACA,KAAK/b,UAAU;QACb+K,IAAI,IAAI2R,KAAK,CAACC,CAAC;QACfzR,GAAG,IAAIwR,KAAK,CAACE,CAAC;QACd;;MAEF;MACA,KAAKxc,WAAW;QACd,IAAIsc,KAAK,CAACC,CAAC,IAAI,CAAC,KAAKL,KAAK,IAAIrL,QAAQ,IAAI7N,WAAW,KAAK8H,GAAG,IAAIkM,MAAM,IAAImF,MAAM,IAAIpL,SAAS,CAAC,CAAC,EAAE;UAChGqL,UAAU,GAAG,KAAK;UAClB;QACF;QACAK,KAAK,CAACzc,WAAW,CAAC;QAClB0O,KAAK,IAAI4N,KAAK,CAACC,CAAC;QAChB,IAAI7N,KAAK,GAAG,CAAC,EAAE;UACbiN,MAAM,GAAG1b,WAAW;UACpByO,KAAK,GAAG,CAACA,KAAK;UACd/D,IAAI,IAAI+D,KAAK;QACf;QACA,IAAI1L,WAAW,EAAE;UACfyL,MAAM,GAAGC,KAAK,GAAG1L,WAAW;UAC5B8H,GAAG,IAAI,CAAC6L,WAAW,CAAClI,MAAM,GAAGA,MAAM,IAAI,CAAC;QAC1C;QACA;MACF,KAAKtO,YAAY;QACf,IAAImc,KAAK,CAACE,CAAC,IAAI,CAAC,KAAK1R,GAAG,IAAIkM,MAAM,IAAIhU,WAAW,KAAK2H,IAAI,IAAIoM,OAAO,IAAImF,KAAK,IAAIrL,QAAQ,CAAC,CAAC,EAAE;UAC5FuL,UAAU,GAAG,KAAK;UAClB;QACF;QACAK,KAAK,CAACtc,YAAY,CAAC;QACnBsO,MAAM,IAAI6N,KAAK,CAACE,CAAC;QACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;QACd,IAAI/N,MAAM,GAAG,CAAC,EAAE;UACdkN,MAAM,GAAGzb,YAAY;UACrBuO,MAAM,GAAG,CAACA,MAAM;UAChB3D,GAAG,IAAI2D,MAAM;QACf;QACA,IAAIzL,WAAW,EAAE;UACf0L,KAAK,GAAGD,MAAM,GAAGzL,WAAW;UAC5B2H,IAAI,IAAI,CAACgM,WAAW,CAACjI,KAAK,GAAGA,KAAK,IAAI,CAAC;QACzC;QACA;MACF,KAAKzO,WAAW;QACd,IAAIqc,KAAK,CAACC,CAAC,IAAI,CAAC,KAAK5R,IAAI,IAAIoM,OAAO,IAAI/T,WAAW,KAAK8H,GAAG,IAAIkM,MAAM,IAAImF,MAAM,IAAIpL,SAAS,CAAC,CAAC,EAAE;UAC9FqL,UAAU,GAAG,KAAK;UAClB;QACF;QACAK,KAAK,CAACxc,WAAW,CAAC;QAClByO,KAAK,IAAI4N,KAAK,CAACC,CAAC;QAChB5R,IAAI,IAAI2R,KAAK,CAACC,CAAC;QACf,IAAI7N,KAAK,GAAG,CAAC,EAAE;UACbiN,MAAM,GAAG3b,WAAW;UACpB0O,KAAK,GAAG,CAACA,KAAK;UACd/D,IAAI,IAAI+D,KAAK;QACf;QACA,IAAI1L,WAAW,EAAE;UACfyL,MAAM,GAAGC,KAAK,GAAG1L,WAAW;UAC5B8H,GAAG,IAAI,CAAC6L,WAAW,CAAClI,MAAM,GAAGA,MAAM,IAAI,CAAC;QAC1C;QACA;MACF,KAAKvO,YAAY;QACf,IAAIoc,KAAK,CAACE,CAAC,IAAI,CAAC,KAAKL,MAAM,IAAIpL,SAAS,IAAI/N,WAAW,KAAK2H,IAAI,IAAIoM,OAAO,IAAImF,KAAK,IAAIrL,QAAQ,CAAC,CAAC,EAAE;UAClGuL,UAAU,GAAG,KAAK;UAClB;QACF;QACAK,KAAK,CAACvc,YAAY,CAAC;QACnBuO,MAAM,IAAI6N,KAAK,CAACE,CAAC;QACjB,IAAI/N,MAAM,GAAG,CAAC,EAAE;UACdkN,MAAM,GAAGxb,YAAY;UACrBsO,MAAM,GAAG,CAACA,MAAM;UAChB3D,GAAG,IAAI2D,MAAM;QACf;QACA,IAAIzL,WAAW,EAAE;UACf0L,KAAK,GAAGD,MAAM,GAAGzL,WAAW;UAC5B2H,IAAI,IAAI,CAACgM,WAAW,CAACjI,KAAK,GAAGA,KAAK,IAAI,CAAC;QACzC;QACA;MACF,KAAKtO,iBAAiB;QACpB,IAAI4C,WAAW,EAAE;UACf,IAAIsZ,KAAK,CAACE,CAAC,IAAI,CAAC,KAAK1R,GAAG,IAAIkM,MAAM,IAAIkF,KAAK,IAAIrL,QAAQ,CAAC,EAAE;YACxDuL,UAAU,GAAG,KAAK;YAClB;UACF;UACAK,KAAK,CAACtc,YAAY,CAAC;UACnBsO,MAAM,IAAI6N,KAAK,CAACE,CAAC;UACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;UACd9N,KAAK,GAAGD,MAAM,GAAGzL,WAAW;QAC9B,CAAC,MAAM;UACLyZ,KAAK,CAACtc,YAAY,CAAC;UACnBsc,KAAK,CAACzc,WAAW,CAAC;UAClB,IAAIsc,KAAK,CAACC,CAAC,IAAI,CAAC,EAAE;YAChB,IAAIL,KAAK,GAAGrL,QAAQ,EAAE;cACpBnC,KAAK,IAAI4N,KAAK,CAACC,CAAC;YAClB,CAAC,MAAM,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,IAAI1R,GAAG,IAAIkM,MAAM,EAAE;cACxCoF,UAAU,GAAG,KAAK;YACpB;UACF,CAAC,MAAM;YACL1N,KAAK,IAAI4N,KAAK,CAACC,CAAC;UAClB;UACA,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI1R,GAAG,GAAGkM,MAAM,EAAE;cAChBvI,MAAM,IAAI6N,KAAK,CAACE,CAAC;cACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;YAChB;UACF,CAAC,MAAM;YACL/N,MAAM,IAAI6N,KAAK,CAACE,CAAC;YACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;UAChB;QACF;QACA,IAAI9N,KAAK,GAAG,CAAC,IAAID,MAAM,GAAG,CAAC,EAAE;UAC3BkN,MAAM,GAAGpb,iBAAiB;UAC1BkO,MAAM,GAAG,CAACA,MAAM;UAChBC,KAAK,GAAG,CAACA,KAAK;UACd5D,GAAG,IAAI2D,MAAM;UACb9D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;UACpBiN,MAAM,GAAGtb,iBAAiB;UAC1BqO,KAAK,GAAG,CAACA,KAAK;UACd/D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAID,MAAM,GAAG,CAAC,EAAE;UACrBkN,MAAM,GAAGrb,iBAAiB;UAC1BmO,MAAM,GAAG,CAACA,MAAM;UAChB3D,GAAG,IAAI2D,MAAM;QACf;QACA;MACF,KAAKpO,iBAAiB;QACpB,IAAI2C,WAAW,EAAE;UACf,IAAIsZ,KAAK,CAACE,CAAC,IAAI,CAAC,KAAK1R,GAAG,IAAIkM,MAAM,IAAIrM,IAAI,IAAIoM,OAAO,CAAC,EAAE;YACtDqF,UAAU,GAAG,KAAK;YAClB;UACF;UACAK,KAAK,CAACtc,YAAY,CAAC;UACnBsO,MAAM,IAAI6N,KAAK,CAACE,CAAC;UACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;UACd9N,KAAK,GAAGD,MAAM,GAAGzL,WAAW;UAC5B2H,IAAI,IAAIgM,WAAW,CAACjI,KAAK,GAAGA,KAAK;QACnC,CAAC,MAAM;UACL+N,KAAK,CAACtc,YAAY,CAAC;UACnBsc,KAAK,CAACxc,WAAW,CAAC;UAClB,IAAIqc,KAAK,CAACC,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI5R,IAAI,GAAGoM,OAAO,EAAE;cAClBrI,KAAK,IAAI4N,KAAK,CAACC,CAAC;cAChB5R,IAAI,IAAI2R,KAAK,CAACC,CAAC;YACjB,CAAC,MAAM,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,IAAI1R,GAAG,IAAIkM,MAAM,EAAE;cACxCoF,UAAU,GAAG,KAAK;YACpB;UACF,CAAC,MAAM;YACL1N,KAAK,IAAI4N,KAAK,CAACC,CAAC;YAChB5R,IAAI,IAAI2R,KAAK,CAACC,CAAC;UACjB;UACA,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI1R,GAAG,GAAGkM,MAAM,EAAE;cAChBvI,MAAM,IAAI6N,KAAK,CAACE,CAAC;cACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;YAChB;UACF,CAAC,MAAM;YACL/N,MAAM,IAAI6N,KAAK,CAACE,CAAC;YACjB1R,GAAG,IAAIwR,KAAK,CAACE,CAAC;UAChB;QACF;QACA,IAAI9N,KAAK,GAAG,CAAC,IAAID,MAAM,GAAG,CAAC,EAAE;UAC3BkN,MAAM,GAAGrb,iBAAiB;UAC1BmO,MAAM,GAAG,CAACA,MAAM;UAChBC,KAAK,GAAG,CAACA,KAAK;UACd5D,GAAG,IAAI2D,MAAM;UACb9D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;UACpBiN,MAAM,GAAGvb,iBAAiB;UAC1BsO,KAAK,GAAG,CAACA,KAAK;UACd/D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAID,MAAM,GAAG,CAAC,EAAE;UACrBkN,MAAM,GAAGpb,iBAAiB;UAC1BkO,MAAM,GAAG,CAACA,MAAM;UAChB3D,GAAG,IAAI2D,MAAM;QACf;QACA;MACF,KAAKlO,iBAAiB;QACpB,IAAIyC,WAAW,EAAE;UACf,IAAIsZ,KAAK,CAACC,CAAC,IAAI,CAAC,KAAK5R,IAAI,IAAIoM,OAAO,IAAIoF,MAAM,IAAIpL,SAAS,CAAC,EAAE;YAC5DqL,UAAU,GAAG,KAAK;YAClB;UACF;UACAK,KAAK,CAACxc,WAAW,CAAC;UAClByO,KAAK,IAAI4N,KAAK,CAACC,CAAC;UAChB5R,IAAI,IAAI2R,KAAK,CAACC,CAAC;UACf9N,MAAM,GAAGC,KAAK,GAAG1L,WAAW;QAC9B,CAAC,MAAM;UACLyZ,KAAK,CAACvc,YAAY,CAAC;UACnBuc,KAAK,CAACxc,WAAW,CAAC;UAClB,IAAIqc,KAAK,CAACC,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI5R,IAAI,GAAGoM,OAAO,EAAE;cAClBrI,KAAK,IAAI4N,KAAK,CAACC,CAAC;cAChB5R,IAAI,IAAI2R,KAAK,CAACC,CAAC;YACjB,CAAC,MAAM,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,IAAIL,MAAM,IAAIpL,SAAS,EAAE;cAC9CqL,UAAU,GAAG,KAAK;YACpB;UACF,CAAC,MAAM;YACL1N,KAAK,IAAI4N,KAAK,CAACC,CAAC;YAChB5R,IAAI,IAAI2R,KAAK,CAACC,CAAC;UACjB;UACA,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,EAAE;YAChB,IAAIL,MAAM,GAAGpL,SAAS,EAAE;cACtBtC,MAAM,IAAI6N,KAAK,CAACE,CAAC;YACnB;UACF,CAAC,MAAM;YACL/N,MAAM,IAAI6N,KAAK,CAACE,CAAC;UACnB;QACF;QACA,IAAI9N,KAAK,GAAG,CAAC,IAAID,MAAM,GAAG,CAAC,EAAE;UAC3BkN,MAAM,GAAGvb,iBAAiB;UAC1BqO,MAAM,GAAG,CAACA,MAAM;UAChBC,KAAK,GAAG,CAACA,KAAK;UACd5D,GAAG,IAAI2D,MAAM;UACb9D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;UACpBiN,MAAM,GAAGrb,iBAAiB;UAC1BoO,KAAK,GAAG,CAACA,KAAK;UACd/D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAID,MAAM,GAAG,CAAC,EAAE;UACrBkN,MAAM,GAAGtb,iBAAiB;UAC1BoO,MAAM,GAAG,CAACA,MAAM;UAChB3D,GAAG,IAAI2D,MAAM;QACf;QACA;MACF,KAAKnO,iBAAiB;QACpB,IAAI0C,WAAW,EAAE;UACf,IAAIsZ,KAAK,CAACC,CAAC,IAAI,CAAC,KAAKL,KAAK,IAAIrL,QAAQ,IAAIsL,MAAM,IAAIpL,SAAS,CAAC,EAAE;YAC9DqL,UAAU,GAAG,KAAK;YAClB;UACF;UACAK,KAAK,CAACzc,WAAW,CAAC;UAClB0O,KAAK,IAAI4N,KAAK,CAACC,CAAC;UAChB9N,MAAM,GAAGC,KAAK,GAAG1L,WAAW;QAC9B,CAAC,MAAM;UACLyZ,KAAK,CAACvc,YAAY,CAAC;UACnBuc,KAAK,CAACzc,WAAW,CAAC;UAClB,IAAIsc,KAAK,CAACC,CAAC,IAAI,CAAC,EAAE;YAChB,IAAIL,KAAK,GAAGrL,QAAQ,EAAE;cACpBnC,KAAK,IAAI4N,KAAK,CAACC,CAAC;YAClB,CAAC,MAAM,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,IAAIL,MAAM,IAAIpL,SAAS,EAAE;cAC9CqL,UAAU,GAAG,KAAK;YACpB;UACF,CAAC,MAAM;YACL1N,KAAK,IAAI4N,KAAK,CAACC,CAAC;UAClB;UACA,IAAID,KAAK,CAACE,CAAC,IAAI,CAAC,EAAE;YAChB,IAAIL,MAAM,GAAGpL,SAAS,EAAE;cACtBtC,MAAM,IAAI6N,KAAK,CAACE,CAAC;YACnB;UACF,CAAC,MAAM;YACL/N,MAAM,IAAI6N,KAAK,CAACE,CAAC;UACnB;QACF;QACA,IAAI9N,KAAK,GAAG,CAAC,IAAID,MAAM,GAAG,CAAC,EAAE;UAC3BkN,MAAM,GAAGtb,iBAAiB;UAC1BoO,MAAM,GAAG,CAACA,MAAM;UAChBC,KAAK,GAAG,CAACA,KAAK;UACd5D,GAAG,IAAI2D,MAAM;UACb9D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;UACpBiN,MAAM,GAAGpb,iBAAiB;UAC1BmO,KAAK,GAAG,CAACA,KAAK;UACd/D,IAAI,IAAI+D,KAAK;QACf,CAAC,MAAM,IAAID,MAAM,GAAG,CAAC,EAAE;UACrBkN,MAAM,GAAGvb,iBAAiB;UAC1BqO,MAAM,GAAG,CAACA,MAAM;UAChB3D,GAAG,IAAI2D,MAAM;QACf;QACA;;MAEF;MACA,KAAK3O,WAAW;QACd,IAAI,CAAC6c,IAAI,CAACL,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,CAAC;QAC3BJ,UAAU,GAAG,KAAK;QAClB;;MAEF;MACA,KAAKrc,WAAW;QACd,IAAI,CAACoF,IAAI,CAACuH,eAAe,CAACC,QAAQ,CAAC,EAAElD,KAAK,CAAC;QAC3C2S,UAAU,GAAG,KAAK;QAClB;;MAEF;MACA,KAAKvc,WAAW;QACd,IAAI,CAACyc,KAAK,CAACC,CAAC,IAAI,CAACD,KAAK,CAACE,CAAC,EAAE;UACxBJ,UAAU,GAAG,KAAK;UAClB;QACF;QACA5H,MAAM,GAAGhK,SAAS,CAAC,IAAI,CAACmL,OAAO,CAAC;QAChChL,IAAI,GAAGmC,OAAO,CAACK,MAAM,GAAGqH,MAAM,CAAC7J,IAAI;QACnCG,GAAG,GAAGgC,OAAO,CAACO,MAAM,GAAGmH,MAAM,CAAC1J,GAAG;QACjC4D,KAAK,GAAGiI,WAAW,CAAC1F,QAAQ;QAC5BxC,MAAM,GAAGkI,WAAW,CAACxF,SAAS;QAC9B,IAAImL,KAAK,CAACC,CAAC,GAAG,CAAC,EAAE;UACfZ,MAAM,GAAGW,KAAK,CAACE,CAAC,GAAG,CAAC,GAAGlc,iBAAiB,GAAGF,iBAAiB;QAC9D,CAAC,MAAM,IAAIkc,KAAK,CAACC,CAAC,GAAG,CAAC,EAAE;UACtB5R,IAAI,IAAI+D,KAAK;UACbiN,MAAM,GAAGW,KAAK,CAACE,CAAC,GAAG,CAAC,GAAGjc,iBAAiB,GAAGF,iBAAiB;QAC9D;QACA,IAAIic,KAAK,CAACE,CAAC,GAAG,CAAC,EAAE;UACf1R,GAAG,IAAI2D,MAAM;QACf;;QAEA;QACA,IAAI,CAAC,IAAI,CAAC+G,OAAO,EAAE;UACjB5N,WAAW,CAAC,IAAI,CAACiQ,OAAO,EAAElX,YAAY,CAAC;UACvC,IAAI,CAAC6U,OAAO,GAAG,IAAI;UACnB,IAAI,IAAI,CAACY,OAAO,EAAE;YAChB,IAAI,CAACmB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;UAC/B;QACF;QACA;IACJ;IACA,IAAI6E,UAAU,EAAE;MACdzF,WAAW,CAACjI,KAAK,GAAGA,KAAK;MACzBiI,WAAW,CAAClI,MAAM,GAAGA,MAAM;MAC3BkI,WAAW,CAAChM,IAAI,GAAGA,IAAI;MACvBgM,WAAW,CAAC7L,GAAG,GAAGA,GAAG;MACrB,IAAI,CAAC6Q,MAAM,GAAGA,MAAM;MACpB,IAAI,CAAClG,aAAa,CAAC,CAAC;IACtB;;IAEA;IACAzZ,OAAO,CAAC2Q,QAAQ,EAAE,UAAUiQ,CAAC,EAAE;MAC7BA,CAAC,CAACzP,MAAM,GAAGyP,CAAC,CAACrP,IAAI;MACjBqP,CAAC,CAACvP,MAAM,GAAGuP,CAAC,CAACnP,IAAI;IACnB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAIoP,OAAO,GAAG;EACZ;EACA3X,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACJ,KAAK,IAAI,CAAC,IAAI,CAAC0Q,OAAO,IAAI,CAAC,IAAI,CAACsC,QAAQ,EAAE;MACjD,IAAI,CAACtC,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC+B,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC7B,IAAI,IAAI,CAACvO,OAAO,CAACzF,KAAK,EAAE;QACtBiE,QAAQ,CAAC,IAAI,CAACuT,OAAO,EAAEja,WAAW,CAAC;MACrC;MACA8G,WAAW,CAAC,IAAI,CAACiQ,OAAO,EAAElX,YAAY,CAAC;MACvC,IAAI,CAACka,cAAc,CAAC,IAAI,CAACpD,kBAAkB,CAAC;IAC9C;IACA,OAAO,IAAI;EACb,CAAC;EACD;EACAqF,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAChY,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,EAAE;MAChC,IAAI,CAAC/B,SAAS,GAAG7P,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC6W,gBAAgB,CAAC;MAClD,IAAI,CAAC5G,UAAU,GAAGjQ,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsQ,iBAAiB,CAAC;MACpD,IAAI,CAACG,WAAW,GAAGzQ,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACuR,kBAAkB,CAAC;MACtD,IAAI,CAAClC,YAAY,CAAC,CAAC;MACnB,IAAI,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAACC,aAAa,CAAC,CAAC;MACtB;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD;EACAuH,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACxH,OAAO,IAAI,CAAC,IAAI,CAACsC,QAAQ,EAAE;MAClC5R,MAAM,CAAC,IAAI,CAACyQ,WAAW,EAAE;QACvBhM,IAAI,EAAE,CAAC;QACPG,GAAG,EAAE,CAAC;QACN4D,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAI,CAAC+G,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACY,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;;MAE5B;MACA,IAAI,CAACd,YAAY,CAAC,CAAC;MACnB3N,WAAW,CAAC,IAAI,CAACmT,OAAO,EAAEja,WAAW,CAAC;MACtC0G,QAAQ,CAAC,IAAI,CAACqQ,OAAO,EAAElX,YAAY,CAAC;IACtC;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEmH,OAAO,EAAE,SAASA,OAAOA,CAACsD,GAAG,EAAE;IAC7B,IAAI6R,WAAW,GAAGnhB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC3F,IAAI,CAAC,IAAI,CAACgc,QAAQ,IAAI1M,GAAG,EAAE;MACzB,IAAI,IAAI,CAAC8R,KAAK,EAAE;QACd,IAAI,CAACnW,OAAO,CAACoR,GAAG,GAAG/M,GAAG;MACxB;MACA,IAAI6R,WAAW,EAAE;QACf,IAAI,CAAC7R,GAAG,GAAGA,GAAG;QACd,IAAI,CAACsE,KAAK,CAACyI,GAAG,GAAG/M,GAAG;QACpB,IAAI,IAAI,CAACtG,KAAK,EAAE;UACd,IAAI,CAACwT,YAAY,CAACH,GAAG,GAAG/M,GAAG;UAC3BpP,OAAO,CAAC,IAAI,CAACuc,QAAQ,EAAE,UAAUxR,OAAO,EAAE;YACxCA,OAAO,CAACsS,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAClB,GAAG,GAAG/M,GAAG;UAClD,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAI,IAAI,CAAC8R,KAAK,EAAE;UACd,IAAI,CAACC,QAAQ,GAAG,IAAI;QACtB;QACA,IAAI,CAACnU,OAAO,CAAC/F,IAAI,GAAG,IAAI;QACxB,IAAI,CAACma,QAAQ,CAAC,CAAC;QACf,IAAI,CAACC,IAAI,CAACjS,GAAG,CAAC;MAChB;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD;EACAkS,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACxY,KAAK,IAAI,IAAI,CAACgT,QAAQ,EAAE;MAC/B,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrBlQ,WAAW,CAAC,IAAI,CAAC+N,OAAO,EAAEjV,cAAc,CAAC;IAC3C;IACA,OAAO,IAAI;EACb,CAAC;EACD;EACA6c,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACzY,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,EAAE;MAChC,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpBtQ,QAAQ,CAAC,IAAI,CAACmO,OAAO,EAAEjV,cAAc,CAAC;IACxC;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACE8c,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAIzW,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAI,CAACA,OAAO,CAACpH,SAAS,CAAC,EAAE;MACvB,OAAO,IAAI;IACb;IACAoH,OAAO,CAACpH,SAAS,CAAC,GAAG+G,SAAS;IAC9B,IAAI,IAAI,CAACwW,KAAK,IAAI,IAAI,CAACC,QAAQ,EAAE;MAC/BpW,OAAO,CAACoR,GAAG,GAAG,IAAI,CAACsF,WAAW;IAChC;IACA,IAAI,CAACL,QAAQ,CAAC,CAAC;IACf,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACET,IAAI,EAAE,SAASA,IAAIA,CAACe,OAAO,EAAE;IAC3B,IAAIC,OAAO,GAAG7hB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG4hB,OAAO;IACzF,IAAIE,gBAAgB,GAAG,IAAI,CAACzH,UAAU;MACpCxL,IAAI,GAAGiT,gBAAgB,CAACjT,IAAI;MAC5BG,GAAG,GAAG8S,gBAAgB,CAAC9S,GAAG;IAC5B,OAAO,IAAI,CAAC+S,MAAM,CAACpY,WAAW,CAACiY,OAAO,CAAC,GAAGA,OAAO,GAAG/S,IAAI,GAAG/N,MAAM,CAAC8gB,OAAO,CAAC,EAAEjY,WAAW,CAACkY,OAAO,CAAC,GAAGA,OAAO,GAAG7S,GAAG,GAAGlO,MAAM,CAAC+gB,OAAO,CAAC,CAAC;EACrI,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEE,MAAM,EAAE,SAASA,MAAMA,CAACtB,CAAC,EAAE;IACzB,IAAIC,CAAC,GAAG1gB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAGygB,CAAC;IAC7E,IAAIpG,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIgB,OAAO,GAAG,KAAK;IACnBoF,CAAC,GAAG3f,MAAM,CAAC2f,CAAC,CAAC;IACbC,CAAC,GAAG5f,MAAM,CAAC4f,CAAC,CAAC;IACb,IAAI,IAAI,CAAC1X,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,IAAI,IAAI,CAAC9O,OAAO,CAAClF,OAAO,EAAE;MACxD,IAAIwB,QAAQ,CAACiX,CAAC,CAAC,EAAE;QACfpG,UAAU,CAACxL,IAAI,GAAG4R,CAAC;QACnBpF,OAAO,GAAG,IAAI;MAChB;MACA,IAAI7R,QAAQ,CAACkX,CAAC,CAAC,EAAE;QACfrG,UAAU,CAACrL,GAAG,GAAG0R,CAAC;QAClBrF,OAAO,GAAG,IAAI;MAChB;MACA,IAAIA,OAAO,EAAE;QACX,IAAI,CAAC5B,YAAY,CAAC,IAAI,CAAC;MACzB;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEpQ,IAAI,EAAE,SAASA,IAAIA,CAAC0I,KAAK,EAAEiQ,cAAc,EAAE;IACzC,IAAI3H,UAAU,GAAG,IAAI,CAACA,UAAU;IAChCtI,KAAK,GAAGjR,MAAM,CAACiR,KAAK,CAAC;IACrB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbA,KAAK,GAAG,CAAC,IAAI,CAAC,GAAGA,KAAK,CAAC;IACzB,CAAC,MAAM;MACLA,KAAK,GAAG,CAAC,GAAGA,KAAK;IACnB;IACA,OAAO,IAAI,CAACkQ,MAAM,CAAC5H,UAAU,CAACzH,KAAK,GAAGb,KAAK,GAAGsI,UAAU,CAACnG,YAAY,EAAE,IAAI,EAAE8N,cAAc,CAAC;EAC9F,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,MAAM,EAAE,SAASA,MAAMA,CAAClQ,KAAK,EAAEmQ,KAAK,EAAEF,cAAc,EAAE;IACpD,IAAI9U,OAAO,GAAG,IAAI,CAACA,OAAO;MACxBmN,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9B,IAAIzH,KAAK,GAAGyH,UAAU,CAACzH,KAAK;MAC1BD,MAAM,GAAG0H,UAAU,CAAC1H,MAAM;MAC1BuB,YAAY,GAAGmG,UAAU,CAACnG,YAAY;MACtCE,aAAa,GAAGiG,UAAU,CAACjG,aAAa;IAC1CrC,KAAK,GAAGjR,MAAM,CAACiR,KAAK,CAAC;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC/I,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,IAAI9O,OAAO,CAAC/E,QAAQ,EAAE;MAClE,IAAIsL,QAAQ,GAAGS,YAAY,GAAGnC,KAAK;MACnC,IAAI2B,SAAS,GAAGU,aAAa,GAAGrC,KAAK;MACrC,IAAI7D,aAAa,CAAC,IAAI,CAACjD,OAAO,EAAE5E,UAAU,EAAE;QAC1C0L,KAAK,EAAEA,KAAK;QACZoQ,QAAQ,EAAEvP,KAAK,GAAGsB,YAAY;QAC9B+L,aAAa,EAAE+B;MACjB,CAAC,CAAC,KAAK,KAAK,EAAE;QACZ,OAAO,IAAI;MACb;MACA,IAAIA,cAAc,EAAE;QAClB,IAAInR,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5B,IAAI6H,MAAM,GAAGhK,SAAS,CAAC,IAAI,CAACmL,OAAO,CAAC;QACpC,IAAIlS,MAAM,GAAGkJ,QAAQ,IAAIvR,MAAM,CAACC,IAAI,CAACsR,QAAQ,CAAC,CAAC5Q,MAAM,GAAGqS,iBAAiB,CAACzB,QAAQ,CAAC,GAAG;UACpFsB,KAAK,EAAE6P,cAAc,CAAC7P,KAAK;UAC3BC,KAAK,EAAE4P,cAAc,CAAC5P;QACxB,CAAC;;QAED;QACAiI,UAAU,CAACxL,IAAI,IAAI,CAAC4E,QAAQ,GAAGb,KAAK,KAAK,CAACjL,MAAM,CAACwK,KAAK,GAAGuG,MAAM,CAAC7J,IAAI,GAAGwL,UAAU,CAACxL,IAAI,IAAI+D,KAAK,CAAC;QAChGyH,UAAU,CAACrL,GAAG,IAAI,CAAC0E,SAAS,GAAGf,MAAM,KAAK,CAAChL,MAAM,CAACyK,KAAK,GAAGsG,MAAM,CAAC1J,GAAG,GAAGqL,UAAU,CAACrL,GAAG,IAAI2D,MAAM,CAAC;MAClG,CAAC,MAAM,IAAI7I,aAAa,CAACoY,KAAK,CAAC,IAAI1Y,QAAQ,CAAC0Y,KAAK,CAACzB,CAAC,CAAC,IAAIjX,QAAQ,CAAC0Y,KAAK,CAACxB,CAAC,CAAC,EAAE;QACzErG,UAAU,CAACxL,IAAI,IAAI,CAAC4E,QAAQ,GAAGb,KAAK,KAAK,CAACsP,KAAK,CAACzB,CAAC,GAAGpG,UAAU,CAACxL,IAAI,IAAI+D,KAAK,CAAC;QAC7EyH,UAAU,CAACrL,GAAG,IAAI,CAAC0E,SAAS,GAAGf,MAAM,KAAK,CAACuP,KAAK,CAACxB,CAAC,GAAGrG,UAAU,CAACrL,GAAG,IAAI2D,MAAM,CAAC;MAChF,CAAC,MAAM;QACL;QACA0H,UAAU,CAACxL,IAAI,IAAI,CAAC4E,QAAQ,GAAGb,KAAK,IAAI,CAAC;QACzCyH,UAAU,CAACrL,GAAG,IAAI,CAAC0E,SAAS,GAAGf,MAAM,IAAI,CAAC;MAC5C;MACA0H,UAAU,CAACzH,KAAK,GAAGa,QAAQ;MAC3B4G,UAAU,CAAC1H,MAAM,GAAGe,SAAS;MAC7B,IAAI,CAAC+F,YAAY,CAAC,IAAI,CAAC;IACzB;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEvJ,MAAM,EAAE,SAASA,MAAMA,CAACgD,MAAM,EAAE;IAC9B,OAAO,IAAI,CAACkP,QAAQ,CAAC,CAAC,IAAI,CAACnI,SAAS,CAAC/J,MAAM,IAAI,CAAC,IAAIpP,MAAM,CAACoS,MAAM,CAAC,CAAC;EACrE,CAAC;EACD;AACF;AACA;AACA;AACA;EACEkP,QAAQ,EAAE,SAASA,QAAQA,CAAClP,MAAM,EAAE;IAClCA,MAAM,GAAGpS,MAAM,CAACoS,MAAM,CAAC;IACvB,IAAI1J,QAAQ,CAAC0J,MAAM,CAAC,IAAI,IAAI,CAAClK,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,IAAI,IAAI,CAAC9O,OAAO,CAACjF,SAAS,EAAE;MAC9E,IAAI,CAACgS,SAAS,CAAC/J,MAAM,GAAGgD,MAAM,GAAG,GAAG;MACpC,IAAI,CAACuG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/B;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEtJ,MAAM,EAAE,SAASA,MAAMA,CAACkS,OAAO,EAAE;IAC/B,IAAIjS,MAAM,GAAG,IAAI,CAAC6J,SAAS,CAAC7J,MAAM;IAClC,OAAO,IAAI,CAACmG,KAAK,CAAC8L,OAAO,EAAE7Y,QAAQ,CAAC4G,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,CAAC;EAC3D,CAAC;EACD;AACF;AACA;AACA;AACA;EACEA,MAAM,EAAE,SAASA,MAAMA,CAACkS,OAAO,EAAE;IAC/B,IAAInS,MAAM,GAAG,IAAI,CAAC8J,SAAS,CAAC9J,MAAM;IAClC,OAAO,IAAI,CAACoG,KAAK,CAAC/M,QAAQ,CAAC2G,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,EAAEmS,OAAO,CAAC;EAC3D,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE/L,KAAK,EAAE,SAASA,KAAKA,CAACpG,MAAM,EAAE;IAC5B,IAAIC,MAAM,GAAGpQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAGmQ,MAAM;IACvF,IAAI8J,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIqB,WAAW,GAAG,KAAK;IACvBnL,MAAM,GAAGrP,MAAM,CAACqP,MAAM,CAAC;IACvBC,MAAM,GAAGtP,MAAM,CAACsP,MAAM,CAAC;IACvB,IAAI,IAAI,CAACpH,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,IAAI,IAAI,CAAC9O,OAAO,CAAChF,QAAQ,EAAE;MACzD,IAAIsB,QAAQ,CAAC2G,MAAM,CAAC,EAAE;QACpB8J,SAAS,CAAC9J,MAAM,GAAGA,MAAM;QACzBmL,WAAW,GAAG,IAAI;MACpB;MACA,IAAI9R,QAAQ,CAAC4G,MAAM,CAAC,EAAE;QACpB6J,SAAS,CAAC7J,MAAM,GAAGA,MAAM;QACzBkL,WAAW,GAAG,IAAI;MACpB;MACA,IAAIA,WAAW,EAAE;QACf,IAAI,CAAC7B,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC/B;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEnN,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAIiW,OAAO,GAAGviB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACvF,IAAIkN,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB+M,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1BI,UAAU,GAAG,IAAI,CAACA,UAAU;MAC5BQ,WAAW,GAAG,IAAI,CAACA,WAAW;IAChC,IAAI1T,IAAI;IACR,IAAI,IAAI,CAAC6B,KAAK,IAAI,IAAI,CAAC0Q,OAAO,EAAE;MAC9BvS,IAAI,GAAG;QACLsZ,CAAC,EAAE5F,WAAW,CAAChM,IAAI,GAAGwL,UAAU,CAACxL,IAAI;QACrC6R,CAAC,EAAE7F,WAAW,CAAC7L,GAAG,GAAGqL,UAAU,CAACrL,GAAG;QACnC4D,KAAK,EAAEiI,WAAW,CAACjI,KAAK;QACxBD,MAAM,EAAEkI,WAAW,CAAClI;MACtB,CAAC;MACD,IAAIZ,KAAK,GAAGkI,SAAS,CAACrH,KAAK,GAAGqH,SAAS,CAAC/F,YAAY;MACpDhU,OAAO,CAACiH,IAAI,EAAE,UAAUpE,CAAC,EAAErC,CAAC,EAAE;QAC5ByG,IAAI,CAACzG,CAAC,CAAC,GAAGqC,CAAC,GAAGgP,KAAK;MACrB,CAAC,CAAC;MACF,IAAIwQ,OAAO,EAAE;QACX;QACA;QACA,IAAIlC,MAAM,GAAGxV,IAAI,CAACC,KAAK,CAAC3D,IAAI,CAACuZ,CAAC,GAAGvZ,IAAI,CAACwL,MAAM,CAAC;QAC7C,IAAIyN,KAAK,GAAGvV,IAAI,CAACC,KAAK,CAAC3D,IAAI,CAACsZ,CAAC,GAAGtZ,IAAI,CAACyL,KAAK,CAAC;QAC3CzL,IAAI,CAACsZ,CAAC,GAAG5V,IAAI,CAACC,KAAK,CAAC3D,IAAI,CAACsZ,CAAC,CAAC;QAC3BtZ,IAAI,CAACuZ,CAAC,GAAG7V,IAAI,CAACC,KAAK,CAAC3D,IAAI,CAACuZ,CAAC,CAAC;QAC3BvZ,IAAI,CAACyL,KAAK,GAAGwN,KAAK,GAAGjZ,IAAI,CAACsZ,CAAC;QAC3BtZ,IAAI,CAACwL,MAAM,GAAG0N,MAAM,GAAGlZ,IAAI,CAACuZ,CAAC;MAC/B;IACF,CAAC,MAAM;MACLvZ,IAAI,GAAG;QACLsZ,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJ9N,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE;MACV,CAAC;IACH;IACA,IAAIzF,OAAO,CAACjF,SAAS,EAAE;MACrBd,IAAI,CAAC+I,MAAM,GAAG+J,SAAS,CAAC/J,MAAM,IAAI,CAAC;IACrC;IACA,IAAIhD,OAAO,CAAChF,QAAQ,EAAE;MACpBf,IAAI,CAACgJ,MAAM,GAAG8J,SAAS,CAAC9J,MAAM,IAAI,CAAC;MACnChJ,IAAI,CAACiJ,MAAM,GAAG6J,SAAS,CAAC7J,MAAM,IAAI,CAAC;IACrC;IACA,OAAOjJ,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEsF,OAAO,EAAE,SAASA,OAAOA,CAACtF,IAAI,EAAE;IAC9B,IAAI+F,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB+M,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1BI,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9B,IAAIQ,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC7R,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,IAAIlS,aAAa,CAAC3C,IAAI,CAAC,EAAE;MACvD,IAAImU,WAAW,GAAG,KAAK;MACvB,IAAIpO,OAAO,CAACjF,SAAS,EAAE;QACrB,IAAIuB,QAAQ,CAACrC,IAAI,CAAC+I,MAAM,CAAC,IAAI/I,IAAI,CAAC+I,MAAM,KAAK+J,SAAS,CAAC/J,MAAM,EAAE;UAC7D+J,SAAS,CAAC/J,MAAM,GAAG/I,IAAI,CAAC+I,MAAM;UAC9BoL,WAAW,GAAG,IAAI;QACpB;MACF;MACA,IAAIpO,OAAO,CAAChF,QAAQ,EAAE;QACpB,IAAIsB,QAAQ,CAACrC,IAAI,CAACgJ,MAAM,CAAC,IAAIhJ,IAAI,CAACgJ,MAAM,KAAK8J,SAAS,CAAC9J,MAAM,EAAE;UAC7D8J,SAAS,CAAC9J,MAAM,GAAGhJ,IAAI,CAACgJ,MAAM;UAC9BmL,WAAW,GAAG,IAAI;QACpB;QACA,IAAI9R,QAAQ,CAACrC,IAAI,CAACiJ,MAAM,CAAC,IAAIjJ,IAAI,CAACiJ,MAAM,KAAK6J,SAAS,CAAC7J,MAAM,EAAE;UAC7D6J,SAAS,CAAC7J,MAAM,GAAGjJ,IAAI,CAACiJ,MAAM;UAC9BkL,WAAW,GAAG,IAAI;QACpB;MACF;MACA,IAAIA,WAAW,EAAE;QACf,IAAI,CAAC7B,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC/B;MACA,IAAI1H,KAAK,GAAGkI,SAAS,CAACrH,KAAK,GAAGqH,SAAS,CAAC/F,YAAY;MACpD,IAAI1K,QAAQ,CAACrC,IAAI,CAACsZ,CAAC,CAAC,EAAE;QACpB5F,WAAW,CAAChM,IAAI,GAAG1H,IAAI,CAACsZ,CAAC,GAAG1O,KAAK,GAAGsI,UAAU,CAACxL,IAAI;MACrD;MACA,IAAIrF,QAAQ,CAACrC,IAAI,CAACuZ,CAAC,CAAC,EAAE;QACpB7F,WAAW,CAAC7L,GAAG,GAAG7H,IAAI,CAACuZ,CAAC,GAAG3O,KAAK,GAAGsI,UAAU,CAACrL,GAAG;MACnD;MACA,IAAIxF,QAAQ,CAACrC,IAAI,CAACyL,KAAK,CAAC,EAAE;QACxBiI,WAAW,CAACjI,KAAK,GAAGzL,IAAI,CAACyL,KAAK,GAAGb,KAAK;MACxC;MACA,IAAIvI,QAAQ,CAACrC,IAAI,CAACwL,MAAM,CAAC,EAAE;QACzBkI,WAAW,CAAClI,MAAM,GAAGxL,IAAI,CAACwL,MAAM,GAAGZ,KAAK;MAC1C;MACA,IAAI,CAACgN,cAAc,CAAClE,WAAW,CAAC;IAClC;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACE2H,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5C,OAAO,IAAI,CAACxZ,KAAK,GAAGoB,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0P,aAAa,CAAC,GAAG,CAAC,CAAC;EACzD,CAAC;EACD;AACF;AACA;AACA;EACE2I,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpC,OAAO,IAAI,CAACC,KAAK,GAAGtY,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC6P,SAAS,CAAC,GAAG,CAAC,CAAC;EACrD,CAAC;EACD;AACF;AACA;AACA;EACE2E,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;IACtC,IAAIvE,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIlT,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,IAAI,CAAC6B,KAAK,EAAE;MACd9I,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,eAAe,CAAC,EAAE,UAAU6C,CAAC,EAAE;QACxFoE,IAAI,CAACpE,CAAC,CAAC,GAAGsX,UAAU,CAACtX,CAAC,CAAC;MACzB,CAAC,CAAC;IACJ;IACA,OAAOoE,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACE2X,aAAa,EAAE,SAASA,aAAaA,CAAC3X,IAAI,EAAE;IAC1C,IAAIkT,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAInT,WAAW,GAAGmT,UAAU,CAACnT,WAAW;IACxC,IAAI,IAAI,CAAC8B,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,IAAIlS,aAAa,CAAC3C,IAAI,CAAC,EAAE;MACvD,IAAIqC,QAAQ,CAACrC,IAAI,CAAC0H,IAAI,CAAC,EAAE;QACvBwL,UAAU,CAACxL,IAAI,GAAG1H,IAAI,CAAC0H,IAAI;MAC7B;MACA,IAAIrF,QAAQ,CAACrC,IAAI,CAAC6H,GAAG,CAAC,EAAE;QACtBqL,UAAU,CAACrL,GAAG,GAAG7H,IAAI,CAAC6H,GAAG;MAC3B;MACA,IAAIxF,QAAQ,CAACrC,IAAI,CAACyL,KAAK,CAAC,EAAE;QACxByH,UAAU,CAACzH,KAAK,GAAGzL,IAAI,CAACyL,KAAK;QAC7ByH,UAAU,CAAC1H,MAAM,GAAGxL,IAAI,CAACyL,KAAK,GAAG1L,WAAW;MAC9C,CAAC,MAAM,IAAIsC,QAAQ,CAACrC,IAAI,CAACwL,MAAM,CAAC,EAAE;QAChC0H,UAAU,CAAC1H,MAAM,GAAGxL,IAAI,CAACwL,MAAM;QAC/B0H,UAAU,CAACzH,KAAK,GAAGzL,IAAI,CAACwL,MAAM,GAAGzL,WAAW;MAC9C;MACA,IAAI,CAACuS,YAAY,CAAC,IAAI,CAAC;IACzB;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACEoF,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxC,IAAIhE,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAI1T,IAAI;IACR,IAAI,IAAI,CAAC6B,KAAK,IAAI,IAAI,CAAC0Q,OAAO,EAAE;MAC9BvS,IAAI,GAAG;QACL0H,IAAI,EAAEgM,WAAW,CAAChM,IAAI;QACtBG,GAAG,EAAE6L,WAAW,CAAC7L,GAAG;QACpB4D,KAAK,EAAEiI,WAAW,CAACjI,KAAK;QACxBD,MAAM,EAAEkI,WAAW,CAAClI;MACtB,CAAC;IACH;IACA,OAAOxL,IAAI,IAAI,CAAC,CAAC;EACnB,CAAC;EACD;AACF;AACA;AACA;AACA;EACE4X,cAAc,EAAE,SAASA,cAAcA,CAAC5X,IAAI,EAAE;IAC5C,IAAI0T,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAI3T,WAAW,GAAG,IAAI,CAACgG,OAAO,CAAChG,WAAW;IAC1C,IAAIyb,YAAY;IAChB,IAAIC,aAAa;IACjB,IAAI,IAAI,CAAC5Z,KAAK,IAAI,IAAI,CAAC0Q,OAAO,IAAI,CAAC,IAAI,CAACsC,QAAQ,IAAIlS,aAAa,CAAC3C,IAAI,CAAC,EAAE;MACvE,IAAIqC,QAAQ,CAACrC,IAAI,CAAC0H,IAAI,CAAC,EAAE;QACvBgM,WAAW,CAAChM,IAAI,GAAG1H,IAAI,CAAC0H,IAAI;MAC9B;MACA,IAAIrF,QAAQ,CAACrC,IAAI,CAAC6H,GAAG,CAAC,EAAE;QACtB6L,WAAW,CAAC7L,GAAG,GAAG7H,IAAI,CAAC6H,GAAG;MAC5B;MACA,IAAIxF,QAAQ,CAACrC,IAAI,CAACyL,KAAK,CAAC,IAAIzL,IAAI,CAACyL,KAAK,KAAKiI,WAAW,CAACjI,KAAK,EAAE;QAC5D+P,YAAY,GAAG,IAAI;QACnB9H,WAAW,CAACjI,KAAK,GAAGzL,IAAI,CAACyL,KAAK;MAChC;MACA,IAAIpJ,QAAQ,CAACrC,IAAI,CAACwL,MAAM,CAAC,IAAIxL,IAAI,CAACwL,MAAM,KAAKkI,WAAW,CAAClI,MAAM,EAAE;QAC/DiQ,aAAa,GAAG,IAAI;QACpB/H,WAAW,CAAClI,MAAM,GAAGxL,IAAI,CAACwL,MAAM;MAClC;MACA,IAAIzL,WAAW,EAAE;QACf,IAAIyb,YAAY,EAAE;UAChB9H,WAAW,CAAClI,MAAM,GAAGkI,WAAW,CAACjI,KAAK,GAAG1L,WAAW;QACtD,CAAC,MAAM,IAAI0b,aAAa,EAAE;UACxB/H,WAAW,CAACjI,KAAK,GAAGiI,WAAW,CAAClI,MAAM,GAAGzL,WAAW;QACtD;MACF;MACA,IAAI,CAACyS,aAAa,CAAC,CAAC;IACtB;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEkJ,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5C,IAAI3V,OAAO,GAAGlN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAAC,IAAI,CAACgJ,KAAK,IAAI,CAACzF,MAAM,CAACuf,iBAAiB,EAAE;MAC5C,OAAO,IAAI;IACb;IACA,IAAIzI,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAI0I,MAAM,GAAGpP,eAAe,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACqG,SAAS,EAAEI,UAAU,EAAEnN,OAAO,CAAC;;IAE7E;IACA,IAAI,CAAC,IAAI,CAACwM,OAAO,EAAE;MACjB,OAAOqJ,MAAM;IACf;IACA,IAAIC,aAAa,GAAG,IAAI,CAAC1W,OAAO,CAACY,OAAO,CAACqV,OAAO,CAAC;MAC/CU,QAAQ,GAAGD,aAAa,CAACvC,CAAC;MAC1ByC,QAAQ,GAAGF,aAAa,CAACtC,CAAC;MAC1ByC,YAAY,GAAGH,aAAa,CAACpQ,KAAK;MAClCwQ,aAAa,GAAGJ,aAAa,CAACrQ,MAAM;IACtC,IAAIZ,KAAK,GAAGgR,MAAM,CAACnQ,KAAK,GAAG/H,IAAI,CAAC8L,KAAK,CAAC0D,UAAU,CAACnG,YAAY,CAAC;IAC9D,IAAInC,KAAK,KAAK,CAAC,EAAE;MACfkR,QAAQ,IAAIlR,KAAK;MACjBmR,QAAQ,IAAInR,KAAK;MACjBoR,YAAY,IAAIpR,KAAK;MACrBqR,aAAa,IAAIrR,KAAK;IACxB;IACA,IAAI7K,WAAW,GAAGic,YAAY,GAAGC,aAAa;IAC9C,IAAI1N,QAAQ,GAAGjD,gBAAgB,CAAC;MAC9BvL,WAAW,EAAEA,WAAW;MACxB0L,KAAK,EAAE1F,OAAO,CAAC6H,QAAQ,IAAIrL,QAAQ;MACnCiJ,MAAM,EAAEzF,OAAO,CAAC+H,SAAS,IAAIvL;IAC/B,CAAC,CAAC;IACF,IAAIiM,QAAQ,GAAGlD,gBAAgB,CAAC;MAC9BvL,WAAW,EAAEA,WAAW;MACxB0L,KAAK,EAAE1F,OAAO,CAACiI,QAAQ,IAAI,CAAC;MAC5BxC,MAAM,EAAEzF,OAAO,CAACmI,SAAS,IAAI;IAC/B,CAAC,EAAE,OAAO,CAAC;IACX,IAAIyF,iBAAiB,GAAGrI,gBAAgB,CAAC;QACrCvL,WAAW,EAAEA,WAAW;QACxB0L,KAAK,EAAE1F,OAAO,CAAC0F,KAAK,KAAKb,KAAK,KAAK,CAAC,GAAGgR,MAAM,CAACnQ,KAAK,GAAGuQ,YAAY,CAAC;QACnExQ,MAAM,EAAEzF,OAAO,CAACyF,MAAM,KAAKZ,KAAK,KAAK,CAAC,GAAGgR,MAAM,CAACpQ,MAAM,GAAGyQ,aAAa;MACxE,CAAC,CAAC;MACFxQ,KAAK,GAAGkI,iBAAiB,CAAClI,KAAK;MAC/BD,MAAM,GAAGmI,iBAAiB,CAACnI,MAAM;IACnCC,KAAK,GAAG/H,IAAI,CAAC+K,GAAG,CAACF,QAAQ,CAAC9C,KAAK,EAAE/H,IAAI,CAACgL,GAAG,CAACF,QAAQ,CAAC/C,KAAK,EAAEA,KAAK,CAAC,CAAC;IACjED,MAAM,GAAG9H,IAAI,CAAC+K,GAAG,CAACF,QAAQ,CAAC/C,MAAM,EAAE9H,IAAI,CAACgL,GAAG,CAACF,QAAQ,CAAChD,MAAM,EAAEA,MAAM,CAAC,CAAC;IACrE,IAAI2C,MAAM,GAAG9R,QAAQ,CAAC+R,aAAa,CAAC,QAAQ,CAAC;IAC7C,IAAIC,OAAO,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACrCH,MAAM,CAAC1C,KAAK,GAAGlI,sBAAsB,CAACkI,KAAK,CAAC;IAC5C0C,MAAM,CAAC3C,MAAM,GAAGjI,sBAAsB,CAACiI,MAAM,CAAC;IAC9C6C,OAAO,CAACW,SAAS,GAAGjJ,OAAO,CAACuH,SAAS,IAAI,aAAa;IACtDe,OAAO,CAACY,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAExD,KAAK,EAAED,MAAM,CAAC;IACrC,IAAI0Q,qBAAqB,GAAGnW,OAAO,CAACyH,qBAAqB;MACvDA,qBAAqB,GAAG0O,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;MACvFxO,qBAAqB,GAAG3H,OAAO,CAAC2H,qBAAqB;IACvDW,OAAO,CAACb,qBAAqB,GAAGA,qBAAqB;IACrD,IAAIE,qBAAqB,EAAE;MACzBW,OAAO,CAACX,qBAAqB,GAAGA,qBAAqB;IACvD;;IAEA;IACA,IAAIyO,WAAW,GAAGP,MAAM,CAACnQ,KAAK;IAC9B,IAAI2Q,YAAY,GAAGR,MAAM,CAACpQ,MAAM;;IAEhC;IACA,IAAI6Q,IAAI,GAAGP,QAAQ;IACnB,IAAIQ,IAAI,GAAGP,QAAQ;IACnB,IAAIQ,QAAQ;IACZ,IAAIC,SAAS;;IAEb;IACA,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,QAAQ;IACZ,IAAIC,SAAS;IACb,IAAIP,IAAI,IAAI,CAACL,YAAY,IAAIK,IAAI,GAAGF,WAAW,EAAE;MAC/CE,IAAI,GAAG,CAAC;MACRE,QAAQ,GAAG,CAAC;MACZE,IAAI,GAAG,CAAC;MACRE,QAAQ,GAAG,CAAC;IACd,CAAC,MAAM,IAAIN,IAAI,IAAI,CAAC,EAAE;MACpBI,IAAI,GAAG,CAACJ,IAAI;MACZA,IAAI,GAAG,CAAC;MACRE,QAAQ,GAAG7Y,IAAI,CAAC+K,GAAG,CAAC0N,WAAW,EAAEH,YAAY,GAAGK,IAAI,CAAC;MACrDM,QAAQ,GAAGJ,QAAQ;IACrB,CAAC,MAAM,IAAIF,IAAI,IAAIF,WAAW,EAAE;MAC9BM,IAAI,GAAG,CAAC;MACRF,QAAQ,GAAG7Y,IAAI,CAAC+K,GAAG,CAACuN,YAAY,EAAEG,WAAW,GAAGE,IAAI,CAAC;MACrDM,QAAQ,GAAGJ,QAAQ;IACrB;IACA,IAAIA,QAAQ,IAAI,CAAC,IAAID,IAAI,IAAI,CAACL,aAAa,IAAIK,IAAI,GAAGF,YAAY,EAAE;MAClEE,IAAI,GAAG,CAAC;MACRE,SAAS,GAAG,CAAC;MACbE,IAAI,GAAG,CAAC;MACRE,SAAS,GAAG,CAAC;IACf,CAAC,MAAM,IAAIN,IAAI,IAAI,CAAC,EAAE;MACpBI,IAAI,GAAG,CAACJ,IAAI;MACZA,IAAI,GAAG,CAAC;MACRE,SAAS,GAAG9Y,IAAI,CAAC+K,GAAG,CAAC2N,YAAY,EAAEH,aAAa,GAAGK,IAAI,CAAC;MACxDM,SAAS,GAAGJ,SAAS;IACvB,CAAC,MAAM,IAAIF,IAAI,IAAIF,YAAY,EAAE;MAC/BM,IAAI,GAAG,CAAC;MACRF,SAAS,GAAG9Y,IAAI,CAAC+K,GAAG,CAACwN,aAAa,EAAEG,YAAY,GAAGE,IAAI,CAAC;MACxDM,SAAS,GAAGJ,SAAS;IACvB;IACA,IAAIzN,MAAM,GAAG,CAACsN,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,CAAC;;IAE9C;IACA,IAAIG,QAAQ,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;MACjC,IAAIxN,KAAK,GAAG3D,KAAK,GAAGuQ,YAAY;MAChCjN,MAAM,CAACrW,IAAI,CAAC+jB,IAAI,GAAGrN,KAAK,EAAEsN,IAAI,GAAGtN,KAAK,EAAEuN,QAAQ,GAAGvN,KAAK,EAAEwN,SAAS,GAAGxN,KAAK,CAAC;IAC9E;;IAEA;IACA;IACAf,OAAO,CAACgB,SAAS,CAAC1W,KAAK,CAAC0V,OAAO,EAAE,CAACuN,MAAM,CAAC,CAACpe,MAAM,CAACxC,kBAAkB,CAAC+T,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE;MAC9F,OAAO7L,IAAI,CAAC8L,KAAK,CAACjM,sBAAsB,CAACgM,KAAK,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,OAAOpB,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;EACE0O,cAAc,EAAE,SAASA,cAAcA,CAAC9c,WAAW,EAAE;IACnD,IAAIgG,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAI,CAAC,IAAI,CAAC8O,QAAQ,IAAI,CAACrS,WAAW,CAACzC,WAAW,CAAC,EAAE;MAC/C;MACAgG,OAAO,CAAChG,WAAW,GAAG2D,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAE3O,WAAW,CAAC,IAAID,GAAG;MACrD,IAAI,IAAI,CAAC+B,KAAK,EAAE;QACd,IAAI,CAACwQ,WAAW,CAAC,CAAC;QAClB,IAAI,IAAI,CAACE,OAAO,EAAE;UAChB,IAAI,CAACC,aAAa,CAAC,CAAC;QACtB;MACF;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEqF,WAAW,EAAE,SAASA,WAAWA,CAACiF,IAAI,EAAE;IACtC,IAAI/W,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB+R,OAAO,GAAG,IAAI,CAACA,OAAO;MACtBnD,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,IAAI,IAAI,CAAC9S,KAAK,IAAI,CAAC,IAAI,CAACgT,QAAQ,EAAE;MAChC,IAAIkI,SAAS,GAAGD,IAAI,KAAK7e,cAAc;MACvC,IAAI4C,OAAO,GAAGkF,OAAO,CAAClF,OAAO,IAAIic,IAAI,KAAK5e,cAAc;MACxD4e,IAAI,GAAGC,SAAS,IAAIlc,OAAO,GAAGic,IAAI,GAAG3e,cAAc;MACnD4H,OAAO,CAACnG,QAAQ,GAAGkd,IAAI;MACvBxX,OAAO,CAACwS,OAAO,EAAE/Z,WAAW,EAAE+e,IAAI,CAAC;MACnChY,WAAW,CAACgT,OAAO,EAAEva,UAAU,EAAEwf,SAAS,CAAC;MAC3CjY,WAAW,CAACgT,OAAO,EAAEha,UAAU,EAAE+C,OAAO,CAAC;MACzC,IAAI,CAACkF,OAAO,CAAC3E,cAAc,EAAE;QAC3B;QACAkE,OAAO,CAACqP,IAAI,EAAE5W,WAAW,EAAE+e,IAAI,CAAC;QAChChY,WAAW,CAAC6P,IAAI,EAAEpX,UAAU,EAAEwf,SAAS,CAAC;QACxCjY,WAAW,CAAC6P,IAAI,EAAE7W,UAAU,EAAE+C,OAAO,CAAC;MACxC;IACF;IACA,OAAO,IAAI;EACb;AACF,CAAC;AAED,IAAImc,cAAc,GAAG1gB,MAAM,CAAC2gB,OAAO;AACnC,IAAIA,OAAO,GAAG,aAAa,YAAY;EACrC;AACF;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACnZ,OAAO,EAAE;IACxB,IAAIiC,OAAO,GAAGlN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4K,SAAS,GAAG5K,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpFoB,eAAe,CAAC,IAAI,EAAEgjB,OAAO,CAAC;IAC9B,IAAI,CAACnZ,OAAO,IAAI,CAACvE,eAAe,CAACvD,IAAI,CAAC8H,OAAO,CAACoZ,OAAO,CAAC,EAAE;MACtD,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;IAC7F;IACA,IAAI,CAACrZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiC,OAAO,GAAG9C,MAAM,CAAC,CAAC,CAAC,EAAEvD,QAAQ,EAAEiD,aAAa,CAACoD,OAAO,CAAC,IAAIA,OAAO,CAAC;IACtE,IAAI,CAACwM,OAAO,GAAG,KAAK;IACpB,IAAI,CAACsC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACnL,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAAC7H,KAAK,GAAG,KAAK;IAClB,IAAI,CAACub,SAAS,GAAG,KAAK;IACtB,IAAI,CAAClD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACqB,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC8B,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,CAAC,CAAC;EACb;EACA,OAAO3iB,YAAY,CAACsiB,OAAO,EAAE,CAAC;IAC5BviB,GAAG,EAAE,MAAM;IACXK,KAAK,EAAE,SAASuiB,IAAIA,CAAA,EAAG;MACrB,IAAIxZ,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIoZ,OAAO,GAAGpZ,OAAO,CAACoZ,OAAO,CAAChY,WAAW,CAAC,CAAC;MAC3C,IAAIiD,GAAG;MACP,IAAIrE,OAAO,CAACpH,SAAS,CAAC,EAAE;QACtB;MACF;MACAoH,OAAO,CAACpH,SAAS,CAAC,GAAG,IAAI;MACzB,IAAIwgB,OAAO,KAAK,KAAK,EAAE;QACrB,IAAI,CAACjD,KAAK,GAAG,IAAI;;QAEjB;QACA9R,GAAG,GAAGrE,OAAO,CAACuB,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;QACvC,IAAI,CAACmV,WAAW,GAAGrS,GAAG;;QAEtB;QACA,IAAI,CAACA,GAAG,EAAE;UACR;QACF;;QAEA;QACAA,GAAG,GAAGrE,OAAO,CAACoR,GAAG;MACnB,CAAC,MAAM,IAAIgI,OAAO,KAAK,QAAQ,IAAI9gB,MAAM,CAACuf,iBAAiB,EAAE;QAC3DxT,GAAG,GAAGrE,OAAO,CAACyZ,SAAS,CAAC,CAAC;MAC3B;MACA,IAAI,CAACnD,IAAI,CAACjS,GAAG,CAAC;IAChB;EACF,CAAC,EAAE;IACDzN,GAAG,EAAE,MAAM;IACXK,KAAK,EAAE,SAASqf,IAAIA,CAACjS,GAAG,EAAE;MACxB,IAAI4P,KAAK,GAAG,IAAI;MAChB,IAAI,CAAC5P,GAAG,EAAE;QACR;MACF;MACA,IAAI,CAACA,GAAG,GAAGA,GAAG;MACd,IAAI,CAAC2K,SAAS,GAAG,CAAC,CAAC;MACnB,IAAIhP,OAAO,GAAG,IAAI,CAACA,OAAO;QACxBiC,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB,IAAI,CAACA,OAAO,CAACjF,SAAS,IAAI,CAACiF,OAAO,CAAChF,QAAQ,EAAE;QAC3CgF,OAAO,CAAC1F,gBAAgB,GAAG,KAAK;MAClC;;MAEA;MACA,IAAI,CAAC0F,OAAO,CAAC1F,gBAAgB,IAAI,CAACjE,MAAM,CAACkU,WAAW,EAAE;QACpD,IAAI,CAACkN,KAAK,CAAC,CAAC;QACZ;MACF;;MAEA;MACA,IAAIne,eAAe,CAACrD,IAAI,CAACmM,GAAG,CAAC,EAAE;QAC7B;QACA,IAAI7I,oBAAoB,CAACtD,IAAI,CAACmM,GAAG,CAAC,EAAE;UAClC,IAAI,CAACsV,IAAI,CAACzN,oBAAoB,CAAC7H,GAAG,CAAC,CAAC;QACtC,CAAC,MAAM;UACL;UACA;UACA,IAAI,CAACqV,KAAK,CAAC,CAAC;QACd;QACA;MACF;;MAEA;MACA;MACA,IAAIE,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAC9B,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK,CAAClH,IAAI,CAAC,IAAI,CAAC;MACjC,IAAI,CAAC8G,SAAS,GAAG,IAAI;MACrB,IAAI,CAACM,GAAG,GAAGA,GAAG;;MAEd;MACA;MACA;MACA;MACAA,GAAG,CAACE,OAAO,GAAGJ,KAAK;MACnBE,GAAG,CAACG,OAAO,GAAGL,KAAK;MACnBE,GAAG,CAACI,SAAS,GAAGN,KAAK;MACrBE,GAAG,CAACK,UAAU,GAAG,YAAY;QAC3B;QACA,IAAIL,GAAG,CAACM,iBAAiB,CAAC,cAAc,CAAC,KAAK7e,cAAc,EAAE;UAC5Due,GAAG,CAACO,KAAK,CAAC,CAAC;QACb;MACF,CAAC;MACDP,GAAG,CAACQ,MAAM,GAAG,YAAY;QACvBnG,KAAK,CAAC0F,IAAI,CAACC,GAAG,CAACS,QAAQ,CAAC;MAC1B,CAAC;MACDT,GAAG,CAACU,SAAS,GAAG,YAAY;QAC1BrG,KAAK,CAACqF,SAAS,GAAG,KAAK;QACvBrF,KAAK,CAAC2F,GAAG,GAAG,IAAI;MAClB,CAAC;;MAED;MACA,IAAI3X,OAAO,CAAC3F,gBAAgB,IAAI8H,gBAAgB,CAACC,GAAG,CAAC,IAAIrE,OAAO,CAACiR,WAAW,EAAE;QAC5E5M,GAAG,GAAGM,YAAY,CAACN,GAAG,CAAC;MACzB;;MAEA;MACAuV,GAAG,CAACW,IAAI,CAAC,KAAK,EAAElW,GAAG,EAAE,IAAI,CAAC;MAC1BuV,GAAG,CAACY,YAAY,GAAG,aAAa;MAChCZ,GAAG,CAACa,eAAe,GAAGza,OAAO,CAACiR,WAAW,KAAK,iBAAiB;MAC/D2I,GAAG,CAACc,IAAI,CAAC,CAAC;IACZ;EACF,CAAC,EAAE;IACD9jB,GAAG,EAAE,MAAM;IACXK,KAAK,EAAE,SAAS0iB,IAAIA,CAACpN,WAAW,EAAE;MAChC,IAAItK,OAAO,GAAG,IAAI,CAACA,OAAO;QACxB+M,SAAS,GAAG,IAAI,CAACA,SAAS;;MAE5B;MACA;MACA,IAAI5B,WAAW,GAAGF,sBAAsB,CAACX,WAAW,CAAC;MACrD,IAAItH,MAAM,GAAG,CAAC;MACd,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIiI,WAAW,GAAG,CAAC,EAAE;QACnB;QACA,IAAI,CAAC/I,GAAG,GAAGuI,oBAAoB,CAACL,WAAW,EAAElR,cAAc,CAAC;QAC5D,IAAIsf,iBAAiB,GAAGxM,gBAAgB,CAACf,WAAW,CAAC;QACrDnI,MAAM,GAAG0V,iBAAiB,CAAC1V,MAAM;QACjCC,MAAM,GAAGyV,iBAAiB,CAACzV,MAAM;QACjCC,MAAM,GAAGwV,iBAAiB,CAACxV,MAAM;MACnC;MACA,IAAIlD,OAAO,CAACjF,SAAS,EAAE;QACrBgS,SAAS,CAAC/J,MAAM,GAAGA,MAAM;MAC3B;MACA,IAAIhD,OAAO,CAAChF,QAAQ,EAAE;QACpB+R,SAAS,CAAC9J,MAAM,GAAGA,MAAM;QACzB8J,SAAS,CAAC7J,MAAM,GAAGA,MAAM;MAC3B;MACA,IAAI,CAACuU,KAAK,CAAC,CAAC;IACd;EACF,CAAC,EAAE;IACD9iB,GAAG,EAAE,OAAO;IACZK,KAAK,EAAE,SAASyiB,KAAKA,CAAA,EAAG;MACtB,IAAI1Z,OAAO,GAAG,IAAI,CAACA,OAAO;QACxBqE,GAAG,GAAG,IAAI,CAACA,GAAG;MAChB,IAAI4M,WAAW,GAAGjR,OAAO,CAACiR,WAAW;MACrC,IAAIC,cAAc,GAAG7M,GAAG;MACxB,IAAI,IAAI,CAACpC,OAAO,CAAC3F,gBAAgB,IAAI8H,gBAAgB,CAACC,GAAG,CAAC,EAAE;QAC1D,IAAI,CAAC4M,WAAW,EAAE;UAChBA,WAAW,GAAG,WAAW;QAC3B;;QAEA;QACAC,cAAc,GAAGvM,YAAY,CAACN,GAAG,CAAC;MACpC;MACA,IAAI,CAAC4M,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;MACpC,IAAIvI,KAAK,GAAGpQ,QAAQ,CAAC+R,aAAa,CAAC,KAAK,CAAC;MACzC,IAAI2G,WAAW,EAAE;QACftI,KAAK,CAACsI,WAAW,GAAGA,WAAW;MACjC;MACAtI,KAAK,CAACyI,GAAG,GAAGF,cAAc,IAAI7M,GAAG;MACjCsE,KAAK,CAACwI,GAAG,GAAGnR,OAAO,CAACmR,GAAG,IAAI,mBAAmB;MAC9C,IAAI,CAACxI,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACyR,MAAM,GAAG,IAAI,CAACtO,KAAK,CAAC0G,IAAI,CAAC,IAAI,CAAC;MACpC7J,KAAK,CAACoR,OAAO,GAAG,IAAI,CAACa,IAAI,CAACpI,IAAI,CAAC,IAAI,CAAC;MACpC/R,QAAQ,CAACkI,KAAK,EAAE9O,UAAU,CAAC;MAC3BmG,OAAO,CAAC6a,UAAU,CAACC,YAAY,CAACnS,KAAK,EAAE3I,OAAO,CAAC+a,WAAW,CAAC;IAC7D;EACF,CAAC,EAAE;IACDnkB,GAAG,EAAE,OAAO;IACZK,KAAK,EAAE,SAAS6U,KAAKA,CAAA,EAAG;MACtB,IAAIkP,MAAM,GAAG,IAAI;MACjB,IAAIrS,KAAK,GAAG,IAAI,CAACA,KAAK;MACtBA,KAAK,CAACyR,MAAM,GAAG,IAAI;MACnBzR,KAAK,CAACoR,OAAO,GAAG,IAAI;MACpB,IAAI,CAACR,MAAM,GAAG,IAAI;;MAElB;MACA;MACA,IAAI0B,WAAW,GAAGziB,MAAM,CAAC0iB,SAAS,IAAI,qCAAqC,CAAChjB,IAAI,CAACM,MAAM,CAAC0iB,SAAS,CAACC,SAAS,CAAC;MAC5G,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACnS,YAAY,EAAEE,aAAa,EAAE;QACpDhK,MAAM,CAAC6b,MAAM,CAAChM,SAAS,EAAE;UACvB/F,YAAY,EAAEA,YAAY;UAC1BE,aAAa,EAAEA,aAAa;UAC5BlN,WAAW,EAAEgN,YAAY,GAAGE;QAC9B,CAAC,CAAC;QACF6R,MAAM,CAAChF,gBAAgB,GAAG7W,MAAM,CAAC,CAAC,CAAC,EAAE6b,MAAM,CAAChM,SAAS,CAAC;QACtDgM,MAAM,CAACzB,MAAM,GAAG,KAAK;QACrByB,MAAM,CAACvD,KAAK,GAAG,IAAI;QACnBuD,MAAM,CAACK,KAAK,CAAC,CAAC;MAChB,CAAC;;MAED;MACA,IAAI1S,KAAK,CAACM,YAAY,IAAI,CAACgS,WAAW,EAAE;QACtCG,IAAI,CAACzS,KAAK,CAACM,YAAY,EAAEN,KAAK,CAACQ,aAAa,CAAC;QAC7C;MACF;MACA,IAAImS,WAAW,GAAG/iB,QAAQ,CAAC+R,aAAa,CAAC,KAAK,CAAC;MAC/C,IAAIiR,IAAI,GAAGhjB,QAAQ,CAACgjB,IAAI,IAAIhjB,QAAQ,CAACG,eAAe;MACpD,IAAI,CAAC4iB,WAAW,GAAGA,WAAW;MAC9BA,WAAW,CAAClB,MAAM,GAAG,YAAY;QAC/BgB,IAAI,CAACE,WAAW,CAAC3T,KAAK,EAAE2T,WAAW,CAAC5T,MAAM,CAAC;QAC3C,IAAI,CAACuT,WAAW,EAAE;UAChBM,IAAI,CAACC,WAAW,CAACF,WAAW,CAAC;QAC/B;MACF,CAAC;MACDA,WAAW,CAAClK,GAAG,GAAGzI,KAAK,CAACyI,GAAG;;MAE3B;MACA;MACA,IAAI,CAAC6J,WAAW,EAAE;QAChBK,WAAW,CAACpb,KAAK,CAAC8R,OAAO,GAAG,SAAS,GAAG,4BAA4B,GAAG,2BAA2B,GAAG,yBAAyB,GAAG,wBAAwB,GAAG,YAAY,GAAG,oBAAoB,GAAG,QAAQ,GAAG,aAAa;QAC1NuJ,IAAI,CAACjK,WAAW,CAACgK,WAAW,CAAC;MAC/B;IACF;EACF,CAAC,EAAE;IACD1kB,GAAG,EAAE,MAAM;IACXK,KAAK,EAAE,SAAS2jB,IAAIA,CAAA,EAAG;MACrB,IAAIjS,KAAK,GAAG,IAAI,CAACA,KAAK;MACtBA,KAAK,CAACyR,MAAM,GAAG,IAAI;MACnBzR,KAAK,CAACoR,OAAO,GAAG,IAAI;MACpBpR,KAAK,CAACkS,UAAU,CAACW,WAAW,CAAC7S,KAAK,CAAC;MACnC,IAAI,CAACA,KAAK,GAAG,IAAI;IACnB;EACF,CAAC,EAAE;IACD/R,GAAG,EAAE,OAAO;IACZK,KAAK,EAAE,SAASokB,KAAKA,CAAA,EAAG;MACtB,IAAI,CAAC,IAAI,CAAC5D,KAAK,IAAI,IAAI,CAAC1Z,KAAK,EAAE;QAC7B;MACF;MACA,IAAIiC,OAAO,GAAG,IAAI,CAACA,OAAO;QACxBiC,OAAO,GAAG,IAAI,CAACA,OAAO;QACtB0G,KAAK,GAAG,IAAI,CAACA,KAAK;;MAEpB;MACA,IAAIgG,SAAS,GAAG3O,OAAO,CAAC6a,UAAU;MAClC,IAAIY,QAAQ,GAAGljB,QAAQ,CAAC+R,aAAa,CAAC,KAAK,CAAC;MAC5CmR,QAAQ,CAAC1J,SAAS,GAAG1T,QAAQ;MAC7B,IAAIuQ,OAAO,GAAG6M,QAAQ,CAAC9J,aAAa,CAAC,GAAG,CAACjY,MAAM,CAACd,SAAS,EAAE,YAAY,CAAC,CAAC;MACzE,IAAIyR,MAAM,GAAGuE,OAAO,CAAC+C,aAAa,CAAC,GAAG,CAACjY,MAAM,CAACd,SAAS,EAAE,SAAS,CAAC,CAAC;MACpE,IAAIob,OAAO,GAAGpF,OAAO,CAAC+C,aAAa,CAAC,GAAG,CAACjY,MAAM,CAACd,SAAS,EAAE,WAAW,CAAC,CAAC;MACvE,IAAIkY,OAAO,GAAGlC,OAAO,CAAC+C,aAAa,CAAC,GAAG,CAACjY,MAAM,CAACd,SAAS,EAAE,WAAW,CAAC,CAAC;MACvE,IAAIiY,IAAI,GAAGC,OAAO,CAACa,aAAa,CAAC,GAAG,CAACjY,MAAM,CAACd,SAAS,EAAE,OAAO,CAAC,CAAC;MAChE,IAAI,CAAC+V,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACvE,MAAM,GAAGA,MAAM;MACpB,IAAI,CAAC2J,OAAO,GAAGA,OAAO;MACtB,IAAI,CAAClD,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACO,OAAO,GAAGzC,OAAO,CAAC+C,aAAa,CAAC,GAAG,CAACjY,MAAM,CAACd,SAAS,EAAE,WAAW,CAAC,CAAC;MACxE,IAAI,CAACiY,IAAI,GAAGA,IAAI;MAChBxG,MAAM,CAACiH,WAAW,CAAC3I,KAAK,CAAC;;MAEzB;MACAlI,QAAQ,CAACT,OAAO,EAAEpG,YAAY,CAAC;;MAE/B;MACA+U,SAAS,CAACmM,YAAY,CAAClM,OAAO,EAAE5O,OAAO,CAAC+a,WAAW,CAAC;;MAEpD;MACAla,WAAW,CAAC8H,KAAK,EAAE9O,UAAU,CAAC;MAC9B,IAAI,CAACmX,WAAW,CAAC,CAAC;MAClB,IAAI,CAACwB,IAAI,CAAC,CAAC;MACXvQ,OAAO,CAAClG,kBAAkB,GAAG6D,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAE3I,OAAO,CAAClG,kBAAkB,CAAC,IAAIC,GAAG;MAC3EiG,OAAO,CAAChG,WAAW,GAAG2D,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAE3I,OAAO,CAAChG,WAAW,CAAC,IAAID,GAAG;MAC7DiG,OAAO,CAACpG,QAAQ,GAAG+D,IAAI,CAACgL,GAAG,CAAC,CAAC,EAAEhL,IAAI,CAAC+K,GAAG,CAAC,CAAC,EAAE/K,IAAI,CAACC,KAAK,CAACoC,OAAO,CAACpG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;MAC9E4E,QAAQ,CAACqQ,OAAO,EAAElX,YAAY,CAAC;MAC/B,IAAI,CAACqI,OAAO,CAACxF,MAAM,EAAE;QACnBgE,QAAQ,CAACqQ,OAAO,CAAC4K,sBAAsB,CAAC,EAAE,CAAChiB,MAAM,CAACd,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEgB,YAAY,CAAC;MACzF;MACA,IAAI,CAACqI,OAAO,CAACvF,MAAM,EAAE;QACnB+D,QAAQ,CAACqQ,OAAO,CAAC4K,sBAAsB,CAAC,EAAE,CAAChiB,MAAM,CAACd,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEgB,YAAY,CAAC;MACzF;MACA,IAAIqI,OAAO,CAACrF,UAAU,EAAE;QACtB6D,QAAQ,CAACmO,OAAO,EAAE,EAAE,CAAClV,MAAM,CAACd,SAAS,EAAE,KAAK,CAAC,CAAC;MAChD;MACA,IAAI,CAACqJ,OAAO,CAACtF,SAAS,EAAE;QACtB8D,QAAQ,CAACoQ,IAAI,EAAE/W,eAAe,CAAC;MACjC;MACA,IAAImI,OAAO,CAAC3E,cAAc,EAAE;QAC1BmD,QAAQ,CAACoQ,IAAI,EAAE7W,UAAU,CAAC;QAC1BwH,OAAO,CAACqP,IAAI,EAAE5W,WAAW,EAAEpB,UAAU,CAAC;MACxC;MACA,IAAI,CAACoJ,OAAO,CAAC1E,gBAAgB,EAAE;QAC7BkD,QAAQ,CAACqQ,OAAO,CAAC4K,sBAAsB,CAAC,EAAE,CAAChiB,MAAM,CAACd,SAAS,EAAE,OAAO,CAAC,CAAC,EAAEgB,YAAY,CAAC;QACrF6G,QAAQ,CAACqQ,OAAO,CAAC4K,sBAAsB,CAAC,EAAE,CAAChiB,MAAM,CAACd,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAEgB,YAAY,CAAC;MACxF;MACA,IAAI,CAACwU,MAAM,CAAC,CAAC;MACb,IAAI,CAACrQ,KAAK,GAAG,IAAI;MACjB,IAAI,CAACgW,WAAW,CAAC9R,OAAO,CAACnG,QAAQ,CAAC;MAClC,IAAImG,OAAO,CAACpF,QAAQ,EAAE;QACpB,IAAI,CAACsB,IAAI,CAAC,CAAC;MACb;MACA,IAAI,CAACqD,OAAO,CAACS,OAAO,CAAC/F,IAAI,CAAC;MAC1B,IAAI8C,UAAU,CAACiD,OAAO,CAAClE,KAAK,CAAC,EAAE;QAC7B6E,WAAW,CAAC5C,OAAO,EAAE/E,WAAW,EAAEgH,OAAO,CAAClE,KAAK,EAAE;UAC/CgE,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACAkB,aAAa,CAACjD,OAAO,EAAE/E,WAAW,CAAC;IACrC;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,SAAS;IACdK,KAAK,EAAE,SAAS0kB,OAAOA,CAAA,EAAG;MACxB,IAAI,CAAC,IAAI,CAAC5d,KAAK,EAAE;QACf;MACF;MACA,IAAI,CAACA,KAAK,GAAG,KAAK;MAClB,IAAI,CAACwV,MAAM,CAAC,CAAC;MACb,IAAI,CAACtB,YAAY,CAAC,CAAC;MACnB,IAAI4I,UAAU,GAAG,IAAI,CAACjM,OAAO,CAACiM,UAAU;MACxC,IAAIA,UAAU,EAAE;QACdA,UAAU,CAACW,WAAW,CAAC,IAAI,CAAC5M,OAAO,CAAC;MACtC;MACA/N,WAAW,CAAC,IAAI,CAACb,OAAO,EAAEpG,YAAY,CAAC;IACzC;EACF,CAAC,EAAE;IACDhD,GAAG,EAAE,UAAU;IACfK,KAAK,EAAE,SAASof,QAAQA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACtY,KAAK,EAAE;QACd,IAAI,CAAC4d,OAAO,CAAC,CAAC;QACd,IAAI,CAAC5d,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC0Q,OAAO,GAAG,KAAK;MACtB,CAAC,MAAM,IAAI,IAAI,CAAC8K,MAAM,EAAE;QACtB,IAAI,CAAC+B,WAAW,CAAClB,MAAM,GAAG,IAAI;QAC9B,IAAI,CAACb,MAAM,GAAG,KAAK;QACnB,IAAI,CAAC9B,KAAK,GAAG,KAAK;MACpB,CAAC,MAAM,IAAI,IAAI,CAAC6B,SAAS,EAAE;QACzB,IAAI,CAACM,GAAG,CAACE,OAAO,GAAG,IAAI;QACvB,IAAI,CAACF,GAAG,CAACO,KAAK,CAAC,CAAC;MAClB,CAAC,MAAM,IAAI,IAAI,CAACxR,KAAK,EAAE;QACrB,IAAI,CAACiS,IAAI,CAAC,CAAC;MACb;IACF;;IAEA;AACJ;AACA;AACA;EACE,CAAC,CAAC,EAAE,CAAC;IACHhkB,GAAG,EAAE,YAAY;IACjBK,KAAK,EAAE,SAAS2kB,UAAUA,CAAA,EAAG;MAC3BtjB,MAAM,CAAC6gB,OAAO,GAAGD,cAAc;MAC/B,OAAOC,OAAO;IAChB;;IAEA;AACJ;AACA;AACA;EACE,CAAC,EAAE;IACDviB,GAAG,EAAE,aAAa;IAClBK,KAAK,EAAE,SAAS4kB,WAAWA,CAAC5Z,OAAO,EAAE;MACnC9C,MAAM,CAACvD,QAAQ,EAAEiD,aAAa,CAACoD,OAAO,CAAC,IAAIA,OAAO,CAAC;IACrD;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH9C,MAAM,CAACga,OAAO,CAACjjB,SAAS,EAAEkY,MAAM,EAAEjS,OAAO,EAAEoW,MAAM,EAAEiB,QAAQ,EAAE0B,MAAM,EAAEY,OAAO,CAAC;AAE7E,SAASqD,OAAO,IAAI2C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}