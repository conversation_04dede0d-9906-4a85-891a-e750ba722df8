{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; // Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// CLDR #1807 - #1811\nvar dateFormats = {\n  full: 'd, MMMM y, EEEE',\n  long: 'd MMMM, y',\n  medium: 'd MMM, y',\n  short: 'dd-MM-yy'\n};\n\n// CLDR #1807 - #1811\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\n\n// CLDR #1815 - #1818\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}}'కి'\",\n  long: \"{{date}} {{time}}'కి'\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/te/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; // Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// CLDR #1807 - #1811\nvar dateFormats = {\n  full: 'd, MMMM y, EEEE',\n  long: 'd MMMM, y',\n  medium: 'd MMM, y',\n  short: 'dd-MM-yy'\n};\n\n// CLDR #1807 - #1811\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\n\n// CLDR #1815 - #1818\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}}'కి'\",\n  long: \"{{date}} {{time}}'కి'\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C,CAAC,CAAC;AAC1E;AACA,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,uBAAuB;EAC7BC,MAAM,EAAE,mBAAmB;EAC3BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}