{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"uz-Latn\", [[\"TO\", \"TK\"], u, u], u, [[\"Y\", \"D\", \"S\", \"C\", \"P\", \"J\", \"S\"], [\"Yak\", \"Dush\", \"Sesh\", \"Chor\", \"Pay\", \"Jum\", \"Shan\"], [\"yakshanba\", \"dushanba\", \"seshanba\", \"chorshanba\", \"payshanba\", \"juma\", \"shanba\"], [\"Ya\", \"Du\", \"Se\", \"Ch\", \"Pa\", \"Ju\", \"Sh\"]], u, [[\"Y\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"yan\", \"fev\", \"mar\", \"apr\", \"may\", \"iyn\", \"iyl\", \"avg\", \"sen\", \"okt\", \"noy\", \"dek\"], [\"yanvar\", \"fevral\", \"mart\", \"aprel\", \"may\", \"iyun\", \"iyul\", \"avgust\", \"sentabr\", \"oktabr\", \"noyabr\", \"dekabr\"]], [[\"Y\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Yan\", \"Fev\", \"Mar\", \"Apr\", \"May\", \"Iyn\", \"Iyl\", \"Avg\", \"Sen\", \"Okt\", \"Noy\", \"Dek\"], [\"Yanvar\", \"Fevral\", \"Mart\", \"Aprel\", \"May\", \"Iyun\", \"Iyul\", \"Avgust\", \"Sentabr\", \"Oktabr\", \"Noyabr\", \"Dekabr\"]], [[\"m.a.\", \"milodiy\"], u, [\"miloddan avvalgi\", \"milodiy\"]], 1, [6, 0], [\"dd/MM/yy\", \"d-MMM, y\", \"d-MMMM, y\", \"EEEE, d-MMMM, y\"], [\"HH:mm\", \"HH:mm:ss\", \"H:mm:ss (z)\", \"H:mm:ss (zzzz)\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"son emas\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"UZS\", \"soʻm\", \"O‘zbekiston so‘mi\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"UZS\": [\"soʻm\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/uz-Latn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"uz-Latn\", [[\"TO\", \"TK\"], u, u], u, [[\"Y\", \"D\", \"S\", \"C\", \"P\", \"J\", \"S\"], [\"Yak\", \"Dush\", \"Sesh\", \"Chor\", \"Pay\", \"Jum\", \"Shan\"], [\"yakshanba\", \"dushanba\", \"seshanba\", \"chorshanba\", \"payshanba\", \"juma\", \"shanba\"], [\"Ya\", \"Du\", \"Se\", \"Ch\", \"Pa\", \"Ju\", \"Sh\"]], u, [[\"Y\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"yan\", \"fev\", \"mar\", \"apr\", \"may\", \"iyn\", \"iyl\", \"avg\", \"sen\", \"okt\", \"noy\", \"dek\"], [\"yanvar\", \"fevral\", \"mart\", \"aprel\", \"may\", \"iyun\", \"iyul\", \"avgust\", \"sentabr\", \"oktabr\", \"noyabr\", \"dekabr\"]], [[\"Y\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Yan\", \"Fev\", \"Mar\", \"Apr\", \"May\", \"Iyn\", \"Iyl\", \"Avg\", \"Sen\", \"Okt\", \"Noy\", \"Dek\"], [\"Yanvar\", \"Fevral\", \"Mart\", \"Aprel\", \"May\", \"Iyun\", \"Iyul\", \"Avgust\", \"Sentabr\", \"Oktabr\", \"Noyabr\", \"Dekabr\"]], [[\"m.a.\", \"milodiy\"], u, [\"miloddan avvalgi\", \"milodiy\"]], 1, [6, 0], [\"dd/MM/yy\", \"d-MMM, y\", \"d-MMMM, y\", \"EEEE, d-MMMM, y\"], [\"HH:mm\", \"HH:mm:ss\", \"H:mm:ss (z)\", \"H:mm:ss (zzzz)\"], [\"{1}, {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"son emas\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"UZS\", \"soʻm\", \"O‘zbekiston so‘mi\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"], \"USD\": [\"US$\", \"$\"], \"UZS\": [\"soʻm\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAEA,CAAC,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}