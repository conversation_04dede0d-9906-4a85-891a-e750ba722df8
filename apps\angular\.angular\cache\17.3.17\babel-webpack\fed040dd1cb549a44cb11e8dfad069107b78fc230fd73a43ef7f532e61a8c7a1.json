{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/os-RU\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['os-RU', [['AM', 'PM'], u, ['ӕмбисбоны размӕ', 'ӕмбисбоны фӕстӕ']], [['AM', 'PM'], u, u], [['Х', 'К', 'Д', 'Ӕ', 'Ц', 'М', 'С'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт'], ['хуыцаубон', 'къуырисӕр', 'дыццӕг', 'ӕртыццӕг', 'цыппӕрӕм', 'майрӕмбон', 'сабат'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт']], [['Х', 'К', 'Д', 'Ӕ', 'Ц', 'М', 'С'], ['Хцб', 'Крс', 'Дцг', 'Ӕрт', 'Цпр', 'Мрб', 'Сбт'], ['Хуыцаубон', 'Къуырисӕр', 'Дыццӕг', 'Ӕртыццӕг', 'Цыппӕрӕм', 'Майрӕмбон', 'Сабат'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт']], [['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'], ['янв.', 'фев.', 'мар.', 'апр.', 'майы', 'июны', 'июлы', 'авг.', 'сен.', 'окт.', 'ноя.', 'дек.'], ['январы', 'февралы', 'мартъийы', 'апрелы', 'майы', 'июны', 'июлы', 'августы', 'сентябры', 'октябры', 'ноябры', 'декабры']], [['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'], ['Янв.', 'Февр.', 'Март.', 'Апр.', 'Май', 'Июнь', 'Июль', 'Авг.', 'Сент.', 'Окт.', 'Нояб.', 'Дек.'], ['Январь', 'Февраль', 'Мартъи', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь']], [['н.д.а.', 'н.д.'], u, u], 1, [6, 0], ['dd.MM.yy', 'dd MMM y \\'аз\\'', 'd MMMM, y \\'аз\\'', 'EEEE, d MMMM, y \\'аз\\''], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1}, {0}', u, u, u], [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'НН', ':'], ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'], 'RUB', '₽', 'Сом', {\n    'JPY': ['JP¥', '¥'],\n    'RUB': ['₽']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/os-RU.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/os-RU\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        if (n === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'os-RU',\n        [['AM', 'PM'], u, ['ӕмбисбоны размӕ', 'ӕмбисбоны фӕстӕ']],\n        [['AM', 'PM'], u, u],\n        [\n            ['Х', 'К', 'Д', 'Ӕ', 'Ц', 'М', 'С'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт'],\n            ['хуыцаубон', 'къуырисӕр', 'дыццӕг', 'ӕртыццӕг', 'цыппӕрӕм', 'майрӕмбон', 'сабат'],\n            ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт']\n        ],\n        [\n            ['Х', 'К', 'Д', 'Ӕ', 'Ц', 'М', 'С'], ['Хцб', 'Крс', 'Дцг', 'Ӕрт', 'Цпр', 'Мрб', 'Сбт'],\n            ['Хуыцаубон', 'Къуырисӕр', 'Дыццӕг', 'Ӕртыццӕг', 'Цыппӕрӕм', 'Майрӕмбон', 'Сабат'],\n            ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт']\n        ],\n        [\n            ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n            [\n                'янв.', 'фев.', 'мар.', 'апр.', 'майы', 'июны', 'июлы', 'авг.', 'сен.', 'окт.', 'ноя.', 'дек.'\n            ],\n            [\n                'январы', 'февралы', 'мартъийы', 'апрелы', 'майы', 'июны', 'июлы', 'августы', 'сентябры',\n                'октябры', 'ноябры', 'декабры'\n            ]\n        ],\n        [\n            ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n            [\n                'Янв.', 'Февр.', 'Март.', 'Апр.', 'Май', 'Июнь', 'Июль', 'Авг.', 'Сент.', 'Окт.', 'Нояб.',\n                'Дек.'\n            ],\n            [\n                'Январь', 'Февраль', 'Мартъи', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь',\n                'Октябрь', 'Ноябрь', 'Декабрь'\n            ]\n        ],\n        [['н.д.а.', 'н.д.'], u, u],\n        1,\n        [6, 0],\n        ['dd.MM.yy', 'dd MMM y \\'аз\\'', 'd MMMM, y \\'аз\\'', 'EEEE, d MMMM, y \\'аз\\''],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1}, {0}', u, u, u],\n        [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'НН', ':'],\n        ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'],\n        'RUB',\n        '₽',\n        'Сом',\n        { 'JPY': ['JP¥', '¥'], 'RUB': ['₽'] },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC5E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,OAAO,EACP,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,EACzD,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACtF,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,EAClF,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CACpD,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACtF,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,EAClF,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CACpD,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CACjG,EACD,CACI,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EACxF,SAAS,EAAE,QAAQ,EAAE,SAAS,CACjC,CACJ,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EACzF,MAAM,CACT,EACD,CACI,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EACpF,SAAS,EAAE,QAAQ,EAAE,SAAS,CACjC,CACJ,EACD,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAC1B,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,wBAAwB,CAAC,EAC7E,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,UAAU,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACrB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAC7D,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,GAAG,EACH,KAAK,EACL;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,GAAG;EAAE,CAAC,EACrC,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}