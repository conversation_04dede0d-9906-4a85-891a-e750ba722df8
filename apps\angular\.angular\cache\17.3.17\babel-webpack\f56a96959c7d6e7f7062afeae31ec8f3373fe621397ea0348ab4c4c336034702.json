{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"sq-MK\", [[\"p.d.\", \"m.d.\"], u, [\"e paradites\", \"e pasdites\"]], [[\"p.d.\", \"m.d.\"], u, [\"paradite\", \"pasdite\"]], [[\"d\", \"h\", \"m\", \"m\", \"e\", \"p\", \"sh\"], [\"Die\", \"Hën\", \"Mar\", \"Mër\", \"Enj\", \"Pre\", \"Sht\"], [\"e diel\", \"e hënë\", \"e martë\", \"e mërkurë\", \"e enjte\", \"e premte\", \"e shtunë\"], [\"die\", \"hën\", \"mar\", \"mër\", \"enj\", \"pre\", \"sht\"]], [[\"d\", \"h\", \"m\", \"m\", \"e\", \"p\", \"sh\"], [\"die\", \"hën\", \"mar\", \"mër\", \"enj\", \"pre\", \"sht\"], [\"e diel\", \"e hënë\", \"e martë\", \"e mërkurë\", \"e enjte\", \"e premte\", \"e shtunë\"], [\"die\", \"hën\", \"mar\", \"mër\", \"enj\", \"pre\", \"sht\"]], [[\"j\", \"sh\", \"m\", \"p\", \"m\", \"q\", \"k\", \"g\", \"sh\", \"t\", \"n\", \"dh\"], [\"jan\", \"shk\", \"mar\", \"pri\", \"maj\", \"qer\", \"korr\", \"gush\", \"sht\", \"tet\", \"nën\", \"dhj\"], [\"janar\", \"shkurt\", \"mars\", \"prill\", \"maj\", \"qershor\", \"korrik\", \"gusht\", \"shtator\", \"tetor\", \"nëntor\", \"dhjetor\"]], u, [[\"p.K.\", \"mb.K.\"], u, [\"para Krishtit\", \"mbas Krishtit\"]], 1, [6, 0], [\"d.M.yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'në' {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"MKD\", \"den\", \"Denari maqedonas\", {\n  \"AFN\": [],\n  \"ALL\": [\"Lekë\"],\n  \"AMD\": [],\n  \"AOA\": [],\n  \"ARS\": [],\n  \"AUD\": [\"A$\", \"AUD\"],\n  \"AZN\": [],\n  \"BAM\": [],\n  \"BBD\": [],\n  \"BDT\": [],\n  \"BMD\": [],\n  \"BND\": [],\n  \"BOB\": [],\n  \"BRL\": [],\n  \"BSD\": [],\n  \"BWP\": [],\n  \"BZD\": [],\n  \"CAD\": [\"CA$\", \"CAD\"],\n  \"CLP\": [],\n  \"CNY\": [\"CN¥\", \"CNY\"],\n  \"COP\": [],\n  \"CRC\": [],\n  \"CUC\": [],\n  \"CUP\": [],\n  \"CZK\": [],\n  \"DKK\": [],\n  \"DOP\": [],\n  \"EGP\": [],\n  \"FJD\": [],\n  \"FKP\": [],\n  \"GBP\": [\"£\", \"GBP\"],\n  \"GEL\": [],\n  \"GIP\": [],\n  \"GNF\": [],\n  \"GTQ\": [],\n  \"GYD\": [],\n  \"HKD\": [\"HK$\", \"HKS\"],\n  \"HNL\": [],\n  \"HRK\": [],\n  \"HUF\": [],\n  \"IDR\": [],\n  \"ILS\": [\"₪\", \"ILS\"],\n  \"INR\": [\"₹\", \"INR\"],\n  \"ISK\": [],\n  \"JMD\": [],\n  \"JPY\": [\"JP¥\", \"JPY\"],\n  \"KHR\": [],\n  \"KMF\": [],\n  \"KPW\": [],\n  \"KRW\": [\"₩\", \"KRW\"],\n  \"KYD\": [],\n  \"KZT\": [],\n  \"LAK\": [],\n  \"LBP\": [],\n  \"LKR\": [],\n  \"LRD\": [],\n  \"MGA\": [],\n  \"MKD\": [\"den\"],\n  \"MMK\": [],\n  \"MNT\": [],\n  \"MUR\": [],\n  \"MXN\": [\"MX$\", \"MXN\"],\n  \"MYR\": [],\n  \"NAD\": [],\n  \"NGN\": [],\n  \"NIO\": [],\n  \"NOK\": [],\n  \"NPR\": [],\n  \"NZD\": [\"NZ$\", \"NZD\"],\n  \"PHP\": [],\n  \"PKR\": [],\n  \"PLN\": [],\n  \"PYG\": [],\n  \"RON\": [],\n  \"RUB\": [],\n  \"RWF\": [],\n  \"SBD\": [],\n  \"SEK\": [],\n  \"SGD\": [],\n  \"SHP\": [],\n  \"SRD\": [],\n  \"SSP\": [],\n  \"STN\": [],\n  \"SYP\": [],\n  \"THB\": [\"฿\", \"THB\"],\n  \"TOP\": [],\n  \"TRY\": [],\n  \"TTD\": [],\n  \"TWD\": [\"NT$\", \"TWD\"],\n  \"UAH\": [],\n  \"USD\": [\"US$\", \"USD\"],\n  \"UYU\": [],\n  \"VND\": [\"₫\", \"VND\"],\n  \"XCD\": [\"EC$\", \"XCD\"],\n  \"ZAR\": [],\n  \"ZMW\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/sq-MK.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"sq-MK\", [[\"p.d.\", \"m.d.\"], u, [\"e paradites\", \"e pasdites\"]], [[\"p.d.\", \"m.d.\"], u, [\"paradite\", \"pasdite\"]], [[\"d\", \"h\", \"m\", \"m\", \"e\", \"p\", \"sh\"], [\"Die\", \"Hën\", \"Mar\", \"Mër\", \"Enj\", \"Pre\", \"Sht\"], [\"e diel\", \"e hënë\", \"e martë\", \"e mërkurë\", \"e enjte\", \"e premte\", \"e shtunë\"], [\"die\", \"hën\", \"mar\", \"mër\", \"enj\", \"pre\", \"sht\"]], [[\"d\", \"h\", \"m\", \"m\", \"e\", \"p\", \"sh\"], [\"die\", \"hën\", \"mar\", \"mër\", \"enj\", \"pre\", \"sht\"], [\"e diel\", \"e hënë\", \"e martë\", \"e mërkurë\", \"e enjte\", \"e premte\", \"e shtunë\"], [\"die\", \"hën\", \"mar\", \"mër\", \"enj\", \"pre\", \"sht\"]], [[\"j\", \"sh\", \"m\", \"p\", \"m\", \"q\", \"k\", \"g\", \"sh\", \"t\", \"n\", \"dh\"], [\"jan\", \"shk\", \"mar\", \"pri\", \"maj\", \"qer\", \"korr\", \"gush\", \"sht\", \"tet\", \"nën\", \"dhj\"], [\"janar\", \"shkurt\", \"mars\", \"prill\", \"maj\", \"qershor\", \"korrik\", \"gusht\", \"shtator\", \"tetor\", \"nëntor\", \"dhjetor\"]], u, [[\"p.K.\", \"mb.K.\"], u, [\"para Krishtit\", \"mbas Krishtit\"]], 1, [6, 0], [\"d.M.yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'në' {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"MKD\", \"den\", \"Denari maqedonas\", { \"AFN\": [], \"ALL\": [\"Lekë\"], \"AMD\": [], \"AOA\": [], \"ARS\": [], \"AUD\": [\"A$\", \"AUD\"], \"AZN\": [], \"BAM\": [], \"BBD\": [], \"BDT\": [], \"BMD\": [], \"BND\": [], \"BOB\": [], \"BRL\": [], \"BSD\": [], \"BWP\": [], \"BZD\": [], \"CAD\": [\"CA$\", \"CAD\"], \"CLP\": [], \"CNY\": [\"CN¥\", \"CNY\"], \"COP\": [], \"CRC\": [], \"CUC\": [], \"CUP\": [], \"CZK\": [], \"DKK\": [], \"DOP\": [], \"EGP\": [], \"FJD\": [], \"FKP\": [], \"GBP\": [\"£\", \"GBP\"], \"GEL\": [], \"GIP\": [], \"GNF\": [], \"GTQ\": [], \"GYD\": [], \"HKD\": [\"HK$\", \"HKS\"], \"HNL\": [], \"HRK\": [], \"HUF\": [], \"IDR\": [], \"ILS\": [\"₪\", \"ILS\"], \"INR\": [\"₹\", \"INR\"], \"ISK\": [], \"JMD\": [], \"JPY\": [\"JP¥\", \"JPY\"], \"KHR\": [], \"KMF\": [], \"KPW\": [], \"KRW\": [\"₩\", \"KRW\"], \"KYD\": [], \"KZT\": [], \"LAK\": [], \"LBP\": [], \"LKR\": [], \"LRD\": [], \"MGA\": [], \"MKD\": [\"den\"], \"MMK\": [], \"MNT\": [], \"MUR\": [], \"MXN\": [\"MX$\", \"MXN\"], \"MYR\": [], \"NAD\": [], \"NGN\": [], \"NIO\": [], \"NOK\": [], \"NPR\": [], \"NZD\": [\"NZ$\", \"NZD\"], \"PHP\": [], \"PKR\": [], \"PLN\": [], \"PYG\": [], \"RON\": [], \"RUB\": [], \"RWF\": [], \"SBD\": [], \"SEK\": [], \"SGD\": [], \"SHP\": [], \"SRD\": [], \"SSP\": [], \"STN\": [], \"SYP\": [], \"THB\": [\"฿\", \"THB\"], \"TOP\": [], \"TRY\": [], \"TTD\": [], \"TWD\": [\"NT$\", \"TWD\"], \"UAH\": [], \"USD\": [\"US$\", \"USD\"], \"UYU\": [], \"VND\": [\"₫\", \"VND\"], \"XCD\": [\"EC$\", \"XCD\"], \"ZAR\": [], \"ZMW\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEJ,CAAC,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,cAAc,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}