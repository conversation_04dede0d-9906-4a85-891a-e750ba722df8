{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/card\";\nimport * as i2 from \"@angular/common\";\n/** Rendering section \"THE NUMBER OF ENTITIES BY YEAR\" in the third tab \"Additional Statistics\" in Dashboard page.\n *  Requirement number: 2972; 2973; 2974; OECD requirements\n *\n */\nexport class EntitiesByYearComponent extends AppComponentBase {\n  /**\n   * @constructor\n   * @param {Injector} injector\n   */\n  constructor(injector) {\n    super(injector);\n    //\n    //Note: Work for OECD requirements\n    //\n    // #2972\n    this.numberOfEntitiesGrantedExclusion = 0;\n    this.numberOfEntitiesGrantedExclusionConductReleveantActivity = 0;\n    // #2973\n    this.numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction = 0;\n    this.numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity = 0;\n    this.numberOfEntitiesOutOfScope = 0;\n    this.numberOfHighRiskIP = 0;\n    this.numberOfAllIPEntities = 0;\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.dashboardData) {\n      this.getData();\n    }\n  }\n  getData() {\n    if (this.dashboardData) {\n      this.numberOfEntitiesGrantedExclusion = this.dashboardData.numberOfEntitiesGrantedExclusion ?? 0;\n      this.numberOfEntitiesGrantedExclusionConductReleveantActivity = this.dashboardData.numberOfEntitiesGrantedExclusionConductRelevantActivity ?? 0;\n      this.numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction = this.dashboardData.numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction ?? 0;\n      this.numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity = this.dashboardData.numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity ?? 0;\n      this.numberOfEntitiesOutOfScope = this.dashboardData.numberOfEntitiesOutOfScope ?? 0;\n      this.numberOfHighRiskIP = this.dashboardData.numberOfHighRiskIP ?? 0;\n      this.numberOfAllIPEntities = this.dashboardData.numberOfAllIPEntities ?? 0;\n    }\n  }\n  static {\n    this.ɵfac = function EntitiesByYearComponent_Factory(t) {\n      return new (t || EntitiesByYearComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EntitiesByYearComponent,\n      selectors: [[\"app-entities-by-year\"]],\n      inputs: {\n        dashboardData: \"dashboardData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 41,\n      vars: 25,\n      consts: [[1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"]],\n      template: function EntitiesByYearComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n          i0.ɵɵtext(3, \"THE NUMBER OF ENTITIES BY YEAR\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 1)(6, \"div\", 2);\n          i0.ɵɵtext(7, \" The Number Of Entities that were granted exclusion \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 2);\n          i0.ɵɵtext(9, \" Of those excluded entities, number of entities that were conducting relevant activity \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 2);\n          i0.ɵɵtext(11, \" Total Number of relevant entities claiming tax residence in another jurisdiction \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 2);\n          i0.ɵɵtext(13, \" The Number Of Entities Claiming Tax Residence in Another Jurisdiction Carrying On Relevant Activity \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 2);\n          i0.ɵɵtext(15, \"The Number Of Declarations with IP Business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 2);\n          i0.ɵɵtext(17, \" The Number Of Declarations with High Risk IP Business \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 2);\n          i0.ɵɵtext(19, \"The Number Of Entities out of scope\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 1)(21, \"div\", 3);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 3);\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 3);\n          i0.ɵɵtext(27);\n          i0.ɵɵpipe(28, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 3);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 3);\n          i0.ɵɵtext(33);\n          i0.ɵɵpipe(34, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 3);\n          i0.ɵɵtext(36);\n          i0.ɵɵpipe(37, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 3);\n          i0.ɵɵtext(39);\n          i0.ɵɵpipe(40, \"number\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.numberOfEntitiesGrantedExclusion);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(25, 7, ctx.numberOfEntitiesGrantedExclusionConductReleveantActivity, \"1.0-2\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 10, ctx.numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction, \"1.0-2\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(31, 13, ctx.numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity, \"1.0-2\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(34, 16, ctx.numberOfAllIPEntities, \"1.0-2\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(37, 19, ctx.numberOfHighRiskIP, \"1.0-2\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(40, 22, ctx.numberOfEntitiesOutOfScope, \"1.0-2\"), \" \");\n        }\n      },\n      dependencies: [i1.MatCard, i1.MatCardContent, i1.MatCardHeader, i1.MatCardTitle, i2.DecimalPipe],\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJlbnRpdGllcy1ieS15ZWFyLmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvYWRkaXRpb25hbC1zdGF0aXN0aWNzL2VudGl0aWVzLWJ5LXllYXIuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "EntitiesByYearComponent", "constructor", "injector", "numberOfEntitiesGrantedExclusion", "numberOfEntitiesGrantedExclusionConductReleveantActivity", "numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction", "numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity", "numberOfEntitiesOutOfScope", "numberOfHighRiskIP", "numberOfAllIPEntities", "ngOnInit", "ngOnChanges", "changes", "dashboardData", "getData", "numberOfEntitiesGrantedExclusionConductRelevantActivity", "i0", "ɵɵdirectiveInject", "Injector", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "EntitiesByYearComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "ɵɵpipeBind2"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\additional-statistics\\entities-by-year.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\additional-statistics\\entities-by-year.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  Input,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { StatisticMainDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\n\r\n/** Rendering section \"THE NUMBER OF ENTITIES BY YEAR\" in the third tab \"Additional Statistics\" in Dashboard page.\r\n *  Requirement number: 2972; 2973; 2974; OECD requirements\r\n *\r\n */\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-entities-by-year',\r\n  templateUrl: './entities-by-year.component.html',\r\n  styleUrls: ['./entities-by-year.component.css'],\r\n})\r\nexport class EntitiesByYearComponent\r\n  extends AppComponentBase\r\n  implements OnInit, OnChanges\r\n{\r\n  @Input() dashboardData: StatisticMainDto;\r\n  //\r\n  //Note: Work for OECD requirements\r\n  //\r\n  // #2972\r\n  numberOfEntitiesGrantedExclusion: number = 0;\r\n  numberOfEntitiesGrantedExclusionConductReleveantActivity: number = 0;\r\n  // #2973\r\n  numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction: number = 0;\r\n  numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity = 0;\r\n\r\n  numberOfEntitiesOutOfScope: number = 0;\r\n  numberOfHighRiskIP: number = 0;\r\n  numberOfAllIPEntities: number = 0;\r\n\r\n  /**\r\n   * @constructor\r\n   * @param {Injector} injector\r\n   */\r\n  constructor(injector: Injector) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit() {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.dashboardData) {\r\n      this.getData();\r\n    }\r\n  }\r\n\r\n  private getData() {\r\n    if (this.dashboardData) {\r\n      this.numberOfEntitiesGrantedExclusion =\r\n        this.dashboardData.numberOfEntitiesGrantedExclusion ?? 0;\r\n      this.numberOfEntitiesGrantedExclusionConductReleveantActivity =\r\n        this.dashboardData\r\n          .numberOfEntitiesGrantedExclusionConductRelevantActivity ?? 0;\r\n      this.numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction =\r\n        this.dashboardData\r\n          .numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction ?? 0;\r\n      this.numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity =\r\n        this.dashboardData\r\n          .numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity ??\r\n        0;\r\n      this.numberOfEntitiesOutOfScope =\r\n        this.dashboardData.numberOfEntitiesOutOfScope ?? 0;\r\n      this.numberOfHighRiskIP = this.dashboardData.numberOfHighRiskIP ?? 0;\r\n      this.numberOfAllIPEntities =\r\n        this.dashboardData.numberOfAllIPEntities ?? 0;\r\n    }\r\n  }\r\n}\r\n", "<mat-card>\r\n  <mat-card-header>\r\n    <mat-card-title class=\"dashboard-card-title\"\r\n      >THE NUMBER OF ENTITIES BY YEAR</mat-card-title\r\n    >\r\n  </mat-card-header>\r\n  <mat-card-content>\r\n    <div class=\"dashboard-table\">\r\n      <div class=\"col title\">\r\n        The Number Of Entities that were granted exclusion\r\n      </div>\r\n      <div class=\"col title\">\r\n        Of those excluded entities, number of entities that were conducting\r\n        relevant activity\r\n      </div>\r\n      <div class=\"col title\">\r\n        Total Number of relevant entities claiming tax residence in another\r\n        jurisdiction\r\n      </div>\r\n      <div class=\"col title\">\r\n        The Number Of Entities Claiming Tax Residence in Another Jurisdiction\r\n        Carrying On Relevant Activity\r\n      </div>\r\n      <div class=\"col title\">The Number Of Declarations with IP Business</div>\r\n      <div class=\"col title\">\r\n        The Number Of Declarations with High Risk IP Business\r\n      </div>\r\n      <div class=\"col title\">The Number Of Entities out of scope</div>\r\n    </div>\r\n    <div class=\"dashboard-table\">\r\n      <div class=\"col item\">{{ numberOfEntitiesGrantedExclusion }}</div>\r\n      <div class=\"col item\">\r\n        {{\r\n          numberOfEntitiesGrantedExclusionConductReleveantActivity\r\n            | number : \"1.0-2\"\r\n        }}\r\n      </div>\r\n      <div class=\"col item\">\r\n        {{\r\n          numberOfRelevantEntitiesTaxResidenceInOtherJurisdiction\r\n            | number : \"1.0-2\"\r\n        }}\r\n      </div>\r\n      <div class=\"col item\">\r\n        {{\r\n          numberOfRelevantEntitiesTaxResidenceInOtherJurisdictionCarryingOnRelevantActivity\r\n            | number : \"1.0-2\"\r\n        }}\r\n      </div>\r\n      <div class=\"col item\">{{ numberOfAllIPEntities | number : \"1.0-2\" }}</div>\r\n      <div class=\"col item\">{{ numberOfHighRiskIP | number : \"1.0-2\" }}</div>\r\n      <div class=\"col item\">\r\n        {{ numberOfEntitiesOutOfScope | number : \"1.0-2\" }}\r\n      </div>\r\n    </div>\r\n  </mat-card-content>\r\n</mat-card>\r\n"], "mappings": "AASA,SAASA,gBAAgB,QAAQ,yBAAyB;;;;AAG1D;;;;AAUA,OAAM,MAAOC,uBACX,SAAQD,gBAAgB;EAkBxB;;;;EAIAE,YAAYC,QAAkB;IAC5B,KAAK,CAACA,QAAQ,CAAC;IAnBjB;IACA;IACA;IACA;IACA,KAAAC,gCAAgC,GAAW,CAAC;IAC5C,KAAAC,wDAAwD,GAAW,CAAC;IACpE;IACA,KAAAC,uDAAuD,GAAW,CAAC;IACnE,KAAAC,iFAAiF,GAAG,CAAC;IAErF,KAAAC,0BAA0B,GAAW,CAAC;IACtC,KAAAC,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,qBAAqB,GAAW,CAAC;EAQjC;EAEAC,QAAQA,CAAA,GAAI;EAEZC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACC,aAAa,EAAE;MACzB,IAAI,CAACC,OAAO,EAAE;IAChB;EACF;EAEQA,OAAOA,CAAA;IACb,IAAI,IAAI,CAACD,aAAa,EAAE;MACtB,IAAI,CAACV,gCAAgC,GACnC,IAAI,CAACU,aAAa,CAACV,gCAAgC,IAAI,CAAC;MAC1D,IAAI,CAACC,wDAAwD,GAC3D,IAAI,CAACS,aAAa,CACfE,uDAAuD,IAAI,CAAC;MACjE,IAAI,CAACV,uDAAuD,GAC1D,IAAI,CAACQ,aAAa,CACfR,uDAAuD,IAAI,CAAC;MACjE,IAAI,CAACC,iFAAiF,GACpF,IAAI,CAACO,aAAa,CACfP,iFAAiF,IACpF,CAAC;MACH,IAAI,CAACC,0BAA0B,GAC7B,IAAI,CAACM,aAAa,CAACN,0BAA0B,IAAI,CAAC;MACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACK,aAAa,CAACL,kBAAkB,IAAI,CAAC;MACpE,IAAI,CAACC,qBAAqB,GACxB,IAAI,CAACI,aAAa,CAACJ,qBAAqB,IAAI,CAAC;IACjD;EACF;;;uBAvDWT,uBAAuB,EAAAgB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA;IAAA;EAAA;;;YAAvBlB,uBAAuB;MAAAmB,SAAA;MAAAC,MAAA;QAAAP,aAAA;MAAA;MAAAQ,QAAA,GAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBhCb,EAFJ,CAAAe,cAAA,eAAU,sBACS,wBAEZ;UAAAf,EAAA,CAAAgB,MAAA,qCAA8B;UAEnChB,EAFmC,CAAAiB,YAAA,EAChC,EACe;UAGdjB,EAFJ,CAAAe,cAAA,uBAAkB,aACa,aACJ;UACrBf,EAAA,CAAAgB,MAAA,2DACF;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,aAAuB;UACrBf,EAAA,CAAAgB,MAAA,8FAEF;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAuB;UACrBf,EAAA,CAAAgB,MAAA,0FAEF;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAuB;UACrBf,EAAA,CAAAgB,MAAA,6GAEF;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,mDAA2C;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACxEjB,EAAA,CAAAe,cAAA,cAAuB;UACrBf,EAAA,CAAAgB,MAAA,+DACF;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,2CAAmC;UAC5DhB,EAD4D,CAAAiB,YAAA,EAAM,EAC5D;UAEJjB,EADF,CAAAe,cAAA,cAA6B,cACL;UAAAf,EAAA,CAAAgB,MAAA,IAAsC;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAClEjB,EAAA,CAAAe,cAAA,cAAsB;UACpBf,EAAA,CAAAgB,MAAA,IAIF;;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAsB;UACpBf,EAAA,CAAAgB,MAAA,IAIF;;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAsB;UACpBf,EAAA,CAAAgB,MAAA,IAIF;;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAA8C;;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC1EjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAA2C;;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACvEjB,EAAA,CAAAe,cAAA,cAAsB;UACpBf,EAAA,CAAAgB,MAAA,IACF;;UAGNhB,EAHM,CAAAiB,YAAA,EAAM,EACF,EACW,EACV;;;UA1BiBjB,EAAA,CAAAkB,SAAA,IAAsC;UAAtClB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAA3B,gCAAA,CAAsC;UAE1Da,EAAA,CAAAkB,SAAA,GAIF;UAJElB,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAqB,WAAA,QAAAP,GAAA,CAAA1B,wDAAA,gBAIF;UAEEY,EAAA,CAAAkB,SAAA,GAIF;UAJElB,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAqB,WAAA,SAAAP,GAAA,CAAAzB,uDAAA,gBAIF;UAEEW,EAAA,CAAAkB,SAAA,GAIF;UAJElB,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAqB,WAAA,SAAAP,GAAA,CAAAxB,iFAAA,gBAIF;UACsBU,EAAA,CAAAkB,SAAA,GAA8C;UAA9ClB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAqB,WAAA,SAAAP,GAAA,CAAArB,qBAAA,WAA8C;UAC9CO,EAAA,CAAAkB,SAAA,GAA2C;UAA3ClB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAqB,WAAA,SAAAP,GAAA,CAAAtB,kBAAA,WAA2C;UAE/DQ,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAqB,WAAA,SAAAP,GAAA,CAAAvB,0BAAA,gBACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}