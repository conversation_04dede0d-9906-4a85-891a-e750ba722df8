{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ResourcePageComponent } from './containers/resource-page/resource-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ResourcePageComponent\n}];\nexport let ResourcePageRoutingModule = /*#__PURE__*/(() => {\n  class ResourcePageRoutingModule {\n    static {\n      this.ɵfac = function ResourcePageRoutingModule_Factory(t) {\n        return new (t || ResourcePageRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ResourcePageRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ResourcePageRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}