{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/uz-Latn\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['uz-Latn', [['TO', 'TK'], u, u], u, [['Y', 'D', 'S', 'C', 'P', 'J', 'S'], ['Yak', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Chor', 'Pay', 'Ju<PERSON>', '<PERSON>'], ['yaks<PERSON><PERSON>', 'dushanba', 'seshanba', 'chorshanba', 'payshanba', 'juma', 'shanba'], ['Ya', '<PERSON>', 'Se', '<PERSON>', 'Pa', '<PERSON>', 'Sh']], u, [['Y', 'F', '<PERSON>', 'A', 'M', 'I', 'I', 'A', 'S', 'O', 'N', '<PERSON>'], ['yan', 'fev', 'mar', 'apr', 'may', 'iyn', 'iyl', 'avg', 'sen', 'okt', 'noy', 'dek'], ['yanvar', 'fevral', 'mart', 'aprel', 'may', 'iyun', 'iyul', 'avgust', 'sentabr', 'oktabr', 'noyabr', 'dekabr']], [['Y', 'F', 'M', 'A', 'M', 'I', 'I', 'A', 'S', 'O', 'N', 'D'], ['Yan', 'Fev', 'Mar', 'Apr', 'May', 'Iyn', 'Iyl', 'Avg', 'Sen', 'Okt', 'Noy', 'Dek'], ['Yanvar', 'Fevral', 'Mart', 'Aprel', 'May', 'Iyun', 'Iyul', 'Avgust', 'Sentabr', 'Oktabr', 'Noyabr', 'Dekabr']], [['m.a.', 'milodiy'], u, ['miloddan avvalgi', 'milodiy']], 1, [6, 0], ['dd/MM/yy', 'd-MMM, y', 'd-MMMM, y', 'EEEE, d-MMMM, y'], ['HH:mm', 'HH:mm:ss', 'H:mm:ss (z)', 'H:mm:ss (zzzz)'], ['{1}, {0}', u, u, u], [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'son emas', ':'], ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'], 'UZS', 'soʻm', 'O‘zbekiston so‘mi', {\n    'JPY': ['JP¥', '¥'],\n    'USD': ['US$', '$'],\n    'UZS': ['soʻm']\n  }, 'ltr', plural];\n});\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}