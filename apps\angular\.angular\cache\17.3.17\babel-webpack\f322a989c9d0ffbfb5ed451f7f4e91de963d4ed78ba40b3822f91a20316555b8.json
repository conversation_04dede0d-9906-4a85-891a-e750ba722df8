{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\nimport { ContextTagKeys } from \"./Contracts/ContextTagKeys\";\nexport var Extensions = {\n  UserExt: \"user\",\n  DeviceExt: \"device\",\n  TraceExt: \"trace\",\n  WebExt: \"web\",\n  AppExt: \"app\",\n  OSExt: \"os\",\n  SessionExt: \"ses\",\n  SDKExt: \"sdk\"\n};\nexport var CtxTagKeys = new ContextTagKeys();\n//# sourceMappingURL=PartAExtensions.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}