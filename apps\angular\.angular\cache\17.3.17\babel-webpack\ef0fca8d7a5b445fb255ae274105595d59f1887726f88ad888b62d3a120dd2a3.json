{"ast": null, "code": "import { Observable, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@volo/ngx-lepton-x.core\";\nfunction ToolbarItemComponent_ng_container_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ToolbarItemComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function ToolbarItemComponent_ng_container_0_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.actionClick());\n    });\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 6);\n    i0.ɵɵtemplate(4, ToolbarItemComponent_ng_container_0_ng_container_4_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngComponentOutlet\", ctx_r1.component);\n  }\n}\nfunction ToolbarItemComponent_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.template);\n  }\n}\nfunction ToolbarItemComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ToolbarItemComponent_ng_template_1_ng_container_0_Template, 1, 1, \"ng-container\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const htmlTemplate_r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.template)(\"ngIfElse\", htmlTemplate_r3);\n  }\n}\nfunction ToolbarItemComponent_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function ToolbarItemComponent_ng_template_3_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.actionClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r1.html, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ToolbarItemComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ToolbarItemComponent_ng_template_3_div_0_Template, 1, 1, \"div\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const iconTemplate_r5 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.html)(\"ngIfElse\", iconTemplate_r5);\n  }\n}\nfunction ToolbarItemComponent_ng_template_5_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const badge_r7 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(badge_r7);\n  }\n}\nfunction ToolbarItemComponent_ng_template_5_lpx_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lpx-icon\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"iconClass\", ctx_r1.icon);\n  }\n}\nfunction ToolbarItemComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function ToolbarItemComponent_ng_template_5_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.actionClick());\n    });\n    i0.ɵɵelementStart(1, \"span\", 5);\n    i0.ɵɵtemplate(2, ToolbarItemComponent_ng_template_5_small_2_Template, 2, 1, \"small\", 13);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, ToolbarItemComponent_ng_template_5_lpx_icon_4_Template, 1, 1, \"lpx-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 2, ctx_r1.badge$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.icon);\n  }\n}\nexport let ToolbarItemComponent = /*#__PURE__*/(() => {\n  class ToolbarItemComponent {\n    get badge$() {\n      if (this.badge instanceof Observable) {\n        return this.badge;\n      }\n      return of(this.badge);\n    }\n    actionClick() {\n      if (this.action) {\n        this.action();\n      }\n    }\n    static {\n      this.ɵfac = function ToolbarItemComponent_Factory(t) {\n        return new (t || ToolbarItemComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ToolbarItemComponent,\n        selectors: [[\"lpx-toolbar-item\"]],\n        inputs: {\n          component: \"component\",\n          template: \"template\",\n          icon: \"icon\",\n          badge: \"badge\",\n          html: \"html\",\n          action: \"action\"\n        },\n        decls: 7,\n        vars: 2,\n        consts: [[\"templateRef\", \"\"], [\"htmlTemplate\", \"\"], [\"iconTemplate\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"lpx-menu-item-link\", 3, \"click\"], [1, \"lpx-menu-item-icon\"], [1, \"lpx-icon\"], [4, \"ngComponentOutlet\"], [3, \"ngTemplateOutlet\", 4, \"ngIf\", \"ngIfElse\"], [3, \"ngTemplateOutlet\"], [3, \"innerHtml\", \"click\", 4, \"ngIf\", \"ngIfElse\"], [3, \"click\", \"innerHtml\"], [1, \"lpx-menu-item-link\", \"active-menu-item\", 3, \"click\"], [\"class\", \"menu-item-badge\", 4, \"ngIf\"], [\"class\", \"action-icon\", 3, \"iconClass\", 4, \"ngIf\"], [1, \"menu-item-badge\"], [1, \"action-icon\", 3, \"iconClass\"]],\n        template: function ToolbarItemComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ToolbarItemComponent_ng_container_0_Template, 5, 1, \"ng-container\", 3)(1, ToolbarItemComponent_ng_template_1_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, ToolbarItemComponent_ng_template_3_Template, 1, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, ToolbarItemComponent_ng_template_5_Template, 5, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const templateRef_r8 = i0.ɵɵreference(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.component)(\"ngIfElse\", templateRef_r8);\n          }\n        },\n        dependencies: [i1.NgComponentOutlet, i1.NgIf, i1.NgTemplateOutlet, i2.IconComponent, i1.AsyncPipe],\n        encapsulation: 2\n      });\n    }\n  }\n  return ToolbarItemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}