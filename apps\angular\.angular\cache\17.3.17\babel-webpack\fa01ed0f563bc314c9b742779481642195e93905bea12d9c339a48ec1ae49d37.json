{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'malpli ol sekundo',\n    other: 'malpli ol {{count}} sekundoj'\n  },\n  xSeconds: {\n    one: '1 sekundo',\n    other: '{{count}} sekundoj'\n  },\n  halfAMinute: 'duonminuto',\n  lessThanXMinutes: {\n    one: 'malpli ol minuto',\n    other: 'malpli ol {{count}} minutoj'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutoj'\n  },\n  aboutXHours: {\n    one: 'proksimume 1 horo',\n    other: 'proksimume {{count}} horoj'\n  },\n  xHours: {\n    one: '1 horo',\n    other: '{{count}} horoj'\n  },\n  xDays: {\n    one: '1 tago',\n    other: '{{count}} tagoj'\n  },\n  aboutXMonths: {\n    one: 'proksimume 1 monato',\n    other: 'proksimume {{count}} monatoj'\n  },\n  xWeeks: {\n    one: '1 semajno',\n    other: '{{count}} semajnoj'\n  },\n  aboutXWeeks: {\n    one: 'proksimume 1 semajno',\n    other: 'proksimume {{count}} semajnoj'\n  },\n  xMonths: {\n    one: '1 monato',\n    other: '{{count}} monatoj'\n  },\n  aboutXYears: {\n    one: 'proksimume 1 jaro',\n    other: 'proksimume {{count}} jaroj'\n  },\n  xYears: {\n    one: '1 jaro',\n    other: '{{count}} jaroj'\n  },\n  overXYears: {\n    one: 'pli ol 1 jaro',\n    other: 'pli ol {{count}} jaroj'\n  },\n  almostXYears: {\n    one: 'preskaŭ 1 jaro',\n    other: 'preskaŭ {{count}} jaroj'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options !== null && options !== void 0 && options.comparison && options.comparison > 0) {\n      return 'post ' + result;\n    } else {\n      return 'antaŭ ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}