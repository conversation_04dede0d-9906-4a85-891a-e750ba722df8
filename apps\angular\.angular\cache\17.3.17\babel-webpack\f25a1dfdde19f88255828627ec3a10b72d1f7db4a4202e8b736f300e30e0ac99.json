{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TwoDigitDecimalNumberDirective = /*#__PURE__*/(() => {\n  class TwoDigitDecimalNumberDirective {\n    constructor(el) {\n      this.el = el;\n      // Allow decimal numbers\n      this.regex = new RegExp(/^\\d*\\.?\\d{0,2}$/g);\n      // Allow key codes for special events. Reflect :\n      this.specialKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];\n    }\n    onKeyDown(event) {\n      // Allow Backspace, tab, end, and home keys\n      if (this.specialKeys.indexOf(event.key) !== -1) {\n        return;\n      }\n      let current = this.el.nativeElement.value;\n      const position = this.el.nativeElement.selectionStart;\n      const next = [current.slice(0, position), event.key == 'Decimal' ? '.' : event.key, current.slice(position)].join('');\n      if (next && !String(next).match(this.regex)) {\n        event.preventDefault();\n      }\n    }\n    static {\n      this.ɵfac = function TwoDigitDecimalNumberDirective_Factory(t) {\n        return new (t || TwoDigitDecimalNumberDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n        type: TwoDigitDecimalNumberDirective,\n        selectors: [[\"\", \"appTwoDigitDecimalNumber\", \"\"]],\n        hostBindings: function TwoDigitDecimalNumberDirective_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"keydown\", function TwoDigitDecimalNumberDirective_keydown_HostBindingHandler($event) {\n              return ctx.onKeyDown($event);\n            });\n          }\n        }\n      });\n    }\n  }\n  return TwoDigitDecimalNumberDirective;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}