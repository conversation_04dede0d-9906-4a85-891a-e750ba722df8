{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport Chart from 'chart.js/auto';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service\";\nimport * as i2 from \"../../services/ca-dashboard-service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/divider\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/common\";\nfunction TaxResidentsOutsideBahamasChartComponent_h5_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\");\n    i0.ɵɵtext(1, \"Excludes declaration data filed in OTAS\");\n    i0.ɵɵelementEnd();\n  }\n}\n/** Rendering \"# OF TAX RESIDENTS OUTSIDE OF BAHAMAS\" widget in\n * \"Statistics\" Tab in CA Dashboard page.\n *  */\nexport let TaxResidentsOutsideBahamasChartComponent = /*#__PURE__*/(() => {\n  class TaxResidentsOutsideBahamasChartComponent extends AppComponentBase {\n    constructor(injector, CADashboardController, CADashBoardService, router) {\n      super(injector);\n      this.CADashboardController = CADashboardController;\n      this.CADashBoardService = CADashBoardService;\n      this.router = router;\n      this.countries = [];\n      this.numberOfFilings = [];\n      this.percentOfFilings = [];\n      this.chartVisible = false;\n    }\n    ngOnInit() {}\n    ngOnChanges(changes) {\n      if (changes.dashboardData && this.dashboardData) {\n        this.selectedYear = this.dashboardData.fiscalYear;\n        this.getTaxResidentsByYearData();\n      }\n    }\n    getTaxResidentsByYearData() {\n      this.countries = [];\n      this.numberOfFilings = [];\n      this.percentOfFilings = [];\n      this.CADashboardController.getMainCountryTaxResidentsByYear(this.selectedYear).subscribe(result => {\n        result.forEach(element => {\n          if (element.country == 'Other' && element.numberOfFilings != 0 && element.percentageOfFilings != 0) {\n            this.countries.push(element.country);\n            this.numberOfFilings.push(element.numberOfFilings);\n            this.percentOfFilings.push(element.percentageOfFilings);\n          }\n          if (element.country != 'Other') {\n            this.countries.push(element.country);\n            this.numberOfFilings.push(element.numberOfFilings);\n            this.percentOfFilings.push(element.percentageOfFilings);\n          }\n        });\n        this.generateChart();\n      });\n    }\n    generateChart() {\n      if (this.chart) {\n        if (this.numberOfFilings.length === 0 && this.countries.length === 0 && this.percentOfFilings.length === 0) {\n          this.chart.legend.options.display = false;\n          this.chart.hide(0);\n          this.chartVisible = false;\n        } else {\n          this.chart.data.datasets[0].data = this.numberOfFilings;\n          this.chart.data.labels = this.countries;\n          this.chart.legend.options.display = true;\n          this.chart.show(0);\n          this.chart.update();\n          this.chartVisible = true;\n        }\n      } else {\n        this.chart = new Chart(document.getElementById('taxResidentChart'), {\n          type: 'pie',\n          data: {\n            labels: this.countries,\n            datasets: [{\n              backgroundColor: [\"#B71C1C\", \"#004D40\", \"#2525ff\", \"#0D47A1\", \"#FFD600\", \"#6A1B9A\", \"#5D4037\", \"#607D8B\", \"#8BC34A\", \"#E91E63\", \"#f0f0f0\", \"#333333\", \"#cf9a9a\", \"#ff6600\", \"#95835d\"],\n              data: this.numberOfFilings\n            }]\n          },\n          options: {\n            onClick: (event, element) => {\n              this.listing(element[0].index);\n            },\n            onHover: (event, element) => {\n              if (element.length == 1) {\n                if (event.native.target instanceof HTMLElement) {\n                  event.native.target.style.cursor = 'pointer';\n                }\n              }\n              if (element.length == 0) {\n                if (event.native.target instanceof HTMLElement) {\n                  event.native.target.style.cursor = 'default';\n                }\n              }\n            },\n            plugins: {\n              tooltip: {\n                callbacks: {\n                  afterBody: context => {\n                    return this.percentOfFilings[context[0].dataIndex] + '%';\n                  }\n                }\n              }\n            },\n            responsive: true,\n            maintainAspectRatio: false\n          }\n        });\n        if (this.numberOfFilings.length !== 0 && this.countries.length !== 0 && this.percentOfFilings.length !== 0) {\n          this.chartVisible = true;\n        }\n      }\n    }\n    listing(index) {\n      let dto = {\n        country: this.countries[index],\n        fiscalYear: this.selectedYear,\n        maxResultCount: 10\n      };\n      this.router.navigate(['/search-result'], {\n        queryParams: {\n          source: \"dashboard\" /* DashboardConstants.DASHBOARD */,\n          type: \"Tax Residents\" /* DashboardConstants.TAX_RESIDENTS */,\n          year: dto.fiscalYear,\n          country: dto.country\n        }\n      });\n    }\n    static {\n      this.ɵfac = function TaxResidentsOutsideBahamasChartComponent_Factory(t) {\n        return new (t || TaxResidentsOutsideBahamasChartComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.CADashboardContorllerService), i0.ɵɵdirectiveInject(i2.CADashboardService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TaxResidentsOutsideBahamasChartComponent,\n        selectors: [[\"app-tax-resident-outside-bahamas-chart\"]],\n        inputs: {\n          dashboardData: \"dashboardData\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n        decls: 9,\n        vars: 1,\n        consts: [[1, \"dashboard-card-title\"], [1, \"divider-margin\"], [1, \"chart-container\"], [\"id\", \"taxResidentChart\"], [4, \"ngIf\"]],\n        template: function TaxResidentsOutsideBahamasChartComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"mat-card-title\", 0);\n            i0.ɵɵtext(3, \"# OF TAX RESIDENTS OUTSIDE OF BAHAMAS\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"mat-card-content\");\n            i0.ɵɵelement(5, \"mat-divider\", 1);\n            i0.ɵɵelementStart(6, \"div\", 2);\n            i0.ɵɵelement(7, \"canvas\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, TaxResidentsOutsideBahamasChartComponent_h5_8_Template, 2, 0, \"h5\", 4);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.chartVisible);\n          }\n        },\n        dependencies: [i4.MatDivider, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, i6.NgIf],\n        styles: [\".chart-container{position:relative;min-height:20em;min-width:30em}\\n\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return TaxResidentsOutsideBahamasChartComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}