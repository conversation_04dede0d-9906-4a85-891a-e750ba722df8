{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport { BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { AuditTrailLogDetailsComponent } from './audit-trail-log-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/audit-trail.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"../../../shared/components/bdo-table/bdo-table.component\";\n/** Audit Logs Grid section */\nexport class AuditTrailSearchResultComponent extends AppComponentBase {\n  constructor(injector, auditService, auditLogDetailDialog) {\n    super(injector);\n    this.auditService = auditService;\n    this.auditLogDetailDialog = auditLogDetailDialog;\n    /** Define if current process is for CA portal or RA portal. True = CA portal, False = RA portal. */\n    this.isCa = false;\n    this.auditLogs = [];\n    this.totalRecords = 0;\n    /** Records per page. Work for pagination. */\n    this.pageSize = 10;\n    this.currentPageIndex = 0;\n    this.currentSearchCondition = {\n      maxResultCount: this.pageSize\n    };\n    this.tableColumns = [];\n    this.pageSizeOptions = [10, 20, 50, 100];\n    this.tableId = 'auditLogsTable';\n  }\n  ngOnInit() {\n    this.searchConditionSubscription = this.auditService.auditLogSearchCondition.subscribe(condition => {\n      condition.sorting = this.currentSearchCondition.sorting;\n      this.currentSearchCondition = condition;\n      this.onLazyLoadEvent(undefined);\n    });\n    this.searchConditionClearSubscription = this.auditService.auditLogSearchConditionClear.subscribe(response => {\n      if (response) {\n        this.auditLogs = [];\n        this.totalRecords = 0;\n        this.pageSize = 10;\n        this.currentPageIndex = 0;\n        this.setTableData();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.searchConditionSubscription.unsubscribe();\n    this.searchConditionClearSubscription.unsubscribe();\n  }\n  /**\n   * When Main component pass isCa paramter to this component,\n   * trigger this method to update the table columns and data based on current CA or RA portal.\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    if (changes.isCa) {\n      this.isCa = changes.isCa.currentValue;\n      this.setTableColumns();\n      this.setTableData();\n    }\n  }\n  setTableColumns() {\n    if (this.isCa) {\n      //\n      // For CA portal.\n      //\n      this.tableColumns = [{\n        columnId: 'UserName',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Audited User'\n      }, {\n        columnId: 'AuditDateTime',\n        type: BdoTableColumnType.DateTime,\n        isSortable: true,\n        columnName: 'Date and Time'\n      }, {\n        columnId: 'EntityName',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Entity Name'\n      }, {\n        columnId: 'ESPeriodEnd',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'ES Period End'\n      },\n      // {\n      //   columnId: 'EntityFormationNumber',\n      //   type: BdoTableColumnType.String,\n      //   isSortable: true,\n      //   columnName: 'Incorp.# / Formation# (where applicable)',\n      // },\n      {\n        columnId: 'Description',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Description of the action'\n      }];\n    } else {\n      //\n      // For RA portal.\n      //\n      this.tableColumns = [{\n        columnId: 'UserName',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Audited User'\n      }, {\n        columnId: 'IpAddress',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'IP Address'\n      }, {\n        columnId: 'AuditDateTime',\n        type: BdoTableColumnType.DateTime,\n        isSortable: true,\n        columnName: 'Date and Time'\n      }, {\n        columnId: 'EntityName',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Entity Name'\n      }, {\n        columnId: 'EntityUniqueId',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Entity Unique ID'\n      }, {\n        columnId: 'EntityFormationNumber',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Incorp# / Formation#'\n      }, {\n        columnId: 'Description',\n        type: BdoTableColumnType.String,\n        isSortable: true,\n        columnName: 'Description'\n      }];\n    }\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = 'auditLogsTable';\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.auditLogs.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: 'UserName',\n          value: x.userName\n        }, {\n          columnId: 'AuditDateTime',\n          value: x.auditDateTime\n        }, {\n          columnId: 'EntityName',\n          value: x.entityName\n        }, {\n          columnId: 'EntityUniqueId',\n          value: x.entityUniqueId\n        }, {\n          columnId: 'EntityFormationNumber',\n          value: x.entityFormationNumber\n        }, {\n          columnId: 'IpAddress',\n          value: x.ipAddress\n        }, {\n          columnId: 'ESPeriodEnd',\n          value: x.esPeriodEnd\n        }, {\n          columnId: 'Description',\n          value: x.description\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 200);\n  }\n  onLazyLoadEvent(event) {\n    if (event) {\n      if (this.pageSize === (event.pageSize ?? 10)) {\n        this.currentPageIndex = event.pageNumber ?? 0;\n      } else {\n        this.pageSize = event.pageSize ?? 10;\n        this.currentPageIndex = event.pageNumber ?? 0;\n      }\n    } else {\n      this.currentPageIndex = 0;\n      this.pageSize = 10;\n    }\n    this.currentSearchCondition.maxResultCount = this.pageSize;\n    this.currentSearchCondition.skipCount = this.currentPageIndex * this.pageSize;\n    this.currentSearchCondition.sorting = event?.sortField + (event?.isAscending ? '_asc' : '_desc');\n    this.auditService.getAuditLogs(this.currentSearchCondition).subscribe(response => {\n      if (response) {\n        this.auditLogs = response.items ?? [];\n        this.totalRecords = response.totalCount ?? 0;\n      } else {\n        this.auditLogs = [];\n        this.totalRecords = 0;\n      }\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  onRowClick(event) {\n    var data = event.rowData.rawData;\n    //\n    // Pass primary key \"Id\" of AuditLogs table.\n    //\n    this.openAuditLogDetailDialog(data.id, data.userName, data.auditDateTime, data.description, data.ipAddress, data.entityUniqueId, data.entityFormationNumber);\n  }\n  openAuditLogDetailDialog(auditLogId, userName, auditDateTime, description, ipAddress, entityUniqueId, entityFormationNumber) {\n    const dialogRef = this.auditLogDetailDialog.open(AuditTrailLogDetailsComponent, {\n      height: '90vh',\n      width: '200vh',\n      //data refer to auditLogDetailDialogData\n      data: {\n        auditLogId: auditLogId,\n        userName: userName,\n        auditDateTime: auditDateTime,\n        description: description,\n        ipAddress: ipAddress,\n        entityUniqueId: entityUniqueId,\n        entityFormationNumber: entityFormationNumber,\n        isCa: this.isCa\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      console.log('The audit log detail dialog was closed', result);\n    });\n  }\n  static {\n    this.ɵfac = function AuditTrailSearchResultComponent_Factory(t) {\n      return new (t || AuditTrailSearchResultComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.AuditTrailService), i0.ɵɵdirectiveInject(i2.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AuditTrailSearchResultComponent,\n      selectors: [[\"app-audit-trail-search-result\"]],\n      inputs: {\n        isCa: \"isCa\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 4,\n      vars: 10,\n      consts: [[1, \"col-md-12\", \"audit-header-section\"], [1, \"audit-title\"], [\"scrollHeight\", \"68vh\", \"defaultSortColumnId\", \"AuditDateTime\", 1, \"audit-table\", 3, \"onLazyLoad\", \"onRowClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSizeOptions\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"]],\n      template: function AuditTrailSearchResultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtext(2, \"AUDIT TRAIL SUMMARY\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(3, \"bdo-table\", 2);\n          i0.ɵɵlistener(\"onLazyLoad\", function AuditTrailSearchResultComponent_Template_bdo_table_onLazyLoad_3_listener($event) {\n            return ctx.onLazyLoadEvent($event);\n          })(\"onRowClick\", function AuditTrailSearchResultComponent_Template_bdo_table_onRowClick_3_listener($event) {\n            return ctx.onRowClick($event);\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"id\", ctx.tableId)(\"columns\", ctx.tableColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSizeOptions\", ctx.pageSizeOptions)(\"pageSize\", ctx.pageSize)(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n        }\n      },\n      dependencies: [i3.BdoTableComponent],\n      styles: [\".audit-table{\\n    font-size: 0.75rem;\\n    width:100%;\\n    overflow: scroll;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImF1ZGl0LXRyYWlsLXNlYXJjaC1yZXN1bHQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLGtCQUFrQjtJQUNsQixVQUFVO0lBQ1YsZ0JBQWdCO0FBQ3BCIiwiZmlsZSI6ImF1ZGl0LXRyYWlsLXNlYXJjaC1yZXN1bHQuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi5hdWRpdC10YWJsZXtcclxuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgIHdpZHRoOjEwMCU7XHJcbiAgICBvdmVyZmxvdzogc2Nyb2xsO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYXVkaXQvY29udGFpbmVycy9hdWRpdC10cmFpbC1zZWFyY2gtcmVzdWx0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxrQkFBa0I7SUFDbEIsVUFBVTtJQUNWLGdCQUFnQjtBQUNwQjs7QUFFQSx3Y0FBd2MiLCJzb3VyY2VzQ29udGVudCI6WyIuYXVkaXQtdGFibGV7XHJcbiAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICB3aWR0aDoxMDAlO1xyXG4gICAgb3ZlcmZsb3c6IHNjcm9sbDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "BdoTableColumnType", "BdoTableData", "AuditTrailLogDetailsComponent", "AuditTrailSearchResultComponent", "constructor", "injector", "auditService", "auditLogDetailDialog", "isCa", "auditLogs", "totalRecords", "pageSize", "currentPageIndex", "currentSearchCondition", "maxResultCount", "tableColumns", "pageSizeOptions", "tableId", "ngOnInit", "searchConditionSubscription", "auditLogSearchCondition", "subscribe", "condition", "sorting", "onLazyLoadEvent", "undefined", "searchConditionClearSubscription", "auditLogSearchConditionClear", "response", "setTableData", "ngOnDestroy", "unsubscribe", "ngOnChanges", "changes", "currentValue", "setTableColumns", "columnId", "type", "String", "isSortable", "columnName", "DateTime", "tableData", "resetToFirstPage", "data", "map", "x", "id", "rawData", "cells", "value", "userName", "auditDateTime", "entityName", "entityUniqueId", "entityFormationNumber", "ip<PERSON><PERSON><PERSON>", "esPeriodEnd", "description", "setTimeout", "tableService", "setGridData", "event", "pageNumber", "skip<PERSON><PERSON>nt", "sortField", "isAscending", "getAuditLogs", "items", "totalCount", "onRowClick", "rowData", "openAuditLogDetailDialog", "auditLogId", "dialogRef", "open", "height", "width", "afterClosed", "result", "console", "log", "i0", "ɵɵdirectiveInject", "Injector", "i1", "AuditTrailService", "i2", "MatDialog", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "AuditTrailSearchResultComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AuditTrailSearchResultComponent_Template_bdo_table_onLazyLoad_3_listener", "$event", "AuditTrailSearchResultComponent_Template_bdo_table_onRowClick_3_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\audit\\containers\\audit-trail-search-result.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\audit\\containers\\audit-trail-search-result.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  OnDestroy,\r\n  Input,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { AuditTrailService } from '../services/audit-trail.service';\r\nimport { Subscription } from 'rxjs';\r\nimport { GetAuditLogsDto } from 'proxies/proxies-audit-service/lib/proxy/bdo/ess/audit-service';\r\nimport { AuditLogDto } from 'proxies/proxies-audit-service/lib/proxy/bdo/ess/audit-service/models/models';\r\nimport {\r\n  BdoTableColumnDefinition,\r\n  BdoTableColumnType,\r\n  BdoTableData,\r\n  BdoTableRowClickEvent,\r\n} from '@app/shared/components/bdo-table/bdo-table.model';\r\nimport { AuditTrailLogDetailsComponent } from './audit-trail-log-details.component';\r\nimport { MatDialog } from '@angular/material/dialog';\r\n\r\n/** Audit Logs Grid section */\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-audit-trail-search-result',\r\n  templateUrl: './audit-trail-search-result.component.html',\r\n  styleUrls: ['./audit-trail-search-result.component.css'],\r\n})\r\nexport class AuditTrailSearchResultComponent\r\n  extends AppComponentBase\r\n  implements OnInit, OnDestroy, OnChanges\r\n{\r\n  /** Define if current process is for CA portal or RA portal. True = CA portal, False = RA portal. */\r\n  @Input() isCa = false;\r\n\r\n  private searchConditionSubscription: Subscription;\r\n  private searchConditionClearSubscription: Subscription;\r\n\r\n  auditLogs: AuditLogDto[] = [];\r\n  totalRecords = 0;\r\n  /** Records per page. Work for pagination. */\r\n  pageSize = 10;\r\n  currentPageIndex = 0;\r\n\r\n  currentSearchCondition: GetAuditLogsDto = {\r\n    maxResultCount: this.pageSize,\r\n  } as GetAuditLogsDto;\r\n\r\n  tableColumns: BdoTableColumnDefinition[] = [];\r\n\r\n  pageSizeOptions = [10, 20, 50, 100];\r\n  tableId = 'auditLogsTable';\r\n\r\n  constructor(\r\n    injector: Injector,\r\n    private auditService: AuditTrailService,\r\n    public auditLogDetailDialog: MatDialog\r\n  ) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.searchConditionSubscription =\r\n      this.auditService.auditLogSearchCondition.subscribe(\r\n        (condition: GetAuditLogsDto) => {\r\n          condition.sorting = this.currentSearchCondition.sorting;\r\n\r\n          this.currentSearchCondition = condition;\r\n\r\n          this.onLazyLoadEvent(undefined);\r\n        }\r\n      );\r\n\r\n    this.searchConditionClearSubscription =\r\n      this.auditService.auditLogSearchConditionClear.subscribe(\r\n        (response: boolean) => {\r\n          if (response) {\r\n            this.auditLogs = [];\r\n            this.totalRecords = 0;\r\n            this.pageSize = 10;\r\n            this.currentPageIndex = 0;\r\n            this.setTableData();\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.searchConditionSubscription.unsubscribe();\r\n    this.searchConditionClearSubscription.unsubscribe();\r\n  }\r\n\r\n  /**\r\n   * When Main component pass isCa paramter to this component,\r\n   * trigger this method to update the table columns and data based on current CA or RA portal.\r\n   * @param changes\r\n   */\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.isCa) {\r\n      this.isCa = changes.isCa.currentValue;\r\n      this.setTableColumns();\r\n      this.setTableData();\r\n    }\r\n  }\r\n\r\n  setTableColumns(): void {\r\n    if (this.isCa) {\r\n      //\r\n      // For CA portal.\r\n      //\r\n      this.tableColumns = [\r\n        {\r\n          columnId: 'UserName',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Audited User',\r\n        },\r\n        {\r\n          columnId: 'AuditDateTime',\r\n          type: BdoTableColumnType.DateTime,\r\n          isSortable: true,\r\n          columnName: 'Date and Time',\r\n        },\r\n        {\r\n          columnId: 'EntityName',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Entity Name',\r\n        },\r\n        {\r\n          columnId: 'ESPeriodEnd',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'ES Period End',\r\n        },\r\n        // {\r\n        //   columnId: 'EntityFormationNumber',\r\n        //   type: BdoTableColumnType.String,\r\n        //   isSortable: true,\r\n        //   columnName: 'Incorp.# / Formation# (where applicable)',\r\n        // },\r\n        {\r\n          columnId: 'Description',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Description of the action',\r\n        },\r\n      ];\r\n    } else {\r\n      //\r\n      // For RA portal.\r\n      //\r\n      this.tableColumns = [\r\n        {\r\n          columnId: 'UserName',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Audited User',\r\n        },\r\n        {\r\n          columnId: 'IpAddress',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'IP Address',\r\n        },\r\n        {\r\n          columnId: 'AuditDateTime',\r\n          type: BdoTableColumnType.DateTime,\r\n          isSortable: true,\r\n          columnName: 'Date and Time',\r\n        },\r\n        {\r\n          columnId: 'EntityName',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Entity Name',\r\n        },\r\n        {\r\n          columnId: 'EntityUniqueId',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Entity Unique ID',\r\n        },\r\n\r\n        {\r\n          columnId: 'EntityFormationNumber',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Incorp# / Formation#',\r\n        },\r\n        {\r\n          columnId: 'Description',\r\n          type: BdoTableColumnType.String,\r\n          isSortable: true,\r\n          columnName: 'Description',\r\n        },\r\n      ];\r\n    }\r\n  }\r\n\r\n  setTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = 'auditLogsTable';\r\n    tableData.totalRecords = this.totalRecords;\r\n    tableData.data = this.auditLogs.map((x: AuditLogDto) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: 'UserName',\r\n            value: x.userName,\r\n          },\r\n          {\r\n            columnId: 'AuditDateTime',\r\n            value: x.auditDateTime,\r\n          },\r\n          {\r\n            columnId: 'EntityName',\r\n            value: x.entityName,\r\n          },\r\n          {\r\n            columnId: 'EntityUniqueId',\r\n            value: x.entityUniqueId,\r\n          },\r\n          {\r\n            columnId: 'EntityFormationNumber',\r\n            value: x.entityFormationNumber,\r\n          },\r\n          {\r\n            columnId: 'IpAddress',\r\n            value: x.ipAddress,\r\n          },\r\n          {\r\n            columnId: 'ESPeriodEnd',\r\n            value: x.esPeriodEnd,\r\n          },\r\n          {\r\n            columnId: 'Description',\r\n            value: x.description,\r\n          },\r\n        ],\r\n      };\r\n    });\r\n\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 200);\r\n  }\r\n\r\n  onLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.pageSize === (event.pageSize ?? 10)) {\r\n        this.currentPageIndex = event.pageNumber ?? 0;\r\n      } else {\r\n        this.pageSize = event.pageSize ?? 10;\r\n        this.currentPageIndex = event.pageNumber ?? 0;\r\n      }\r\n    } else {\r\n      this.currentPageIndex = 0;\r\n      this.pageSize = 10;\r\n    }\r\n\r\n    this.currentSearchCondition.maxResultCount = this.pageSize;\r\n    this.currentSearchCondition.skipCount =\r\n      this.currentPageIndex * this.pageSize;\r\n    this.currentSearchCondition.sorting =\r\n      event?.sortField + (event?.isAscending ? '_asc' : '_desc');\r\n\r\n    this.auditService\r\n      .getAuditLogs(this.currentSearchCondition)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.auditLogs = response.items ?? [];\r\n          this.totalRecords = response.totalCount ?? 0;\r\n        } else {\r\n          this.auditLogs = [];\r\n          this.totalRecords = 0;\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onRowClick(event: BdoTableRowClickEvent) {\r\n    var data = event.rowData.rawData as AuditLogDto;\r\n    //\r\n    // Pass primary key \"Id\" of AuditLogs table.\r\n    //\r\n    this.openAuditLogDetailDialog(\r\n      data.id,\r\n      data.userName,\r\n      data.auditDateTime,\r\n      data.description,\r\n      data.ipAddress,\r\n      data.entityUniqueId,\r\n      data.entityFormationNumber\r\n    );\r\n  }\r\n\r\n  openAuditLogDetailDialog(\r\n    auditLogId: string,\r\n    userName: string,\r\n    auditDateTime: string,\r\n    description: string,\r\n    ipAddress: string,\r\n    entityUniqueId: string,\r\n    entityFormationNumber: string\r\n  ) {\r\n    const dialogRef = this.auditLogDetailDialog.open(\r\n      AuditTrailLogDetailsComponent,\r\n      {\r\n        height: '90vh',\r\n        width: '200vh',\r\n        //data refer to auditLogDetailDialogData\r\n        data: {\r\n          auditLogId: auditLogId,\r\n          userName: userName,\r\n          auditDateTime: auditDateTime,\r\n          description: description,\r\n          ipAddress: ipAddress,\r\n          entityUniqueId: entityUniqueId,\r\n          entityFormationNumber: entityFormationNumber,\r\n          isCa: this.isCa,\r\n        },\r\n      }\r\n    );\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      console.log('The audit log detail dialog was closed', result);\r\n    });\r\n  }\r\n}\r\n", "<div class=\"col-md-12 audit-header-section\">\r\n  <div class=\"audit-title\">AUDIT TRAIL SUMMARY</div>\r\n</div>\r\n<bdo-table\r\n  [id]=\"tableId\"\r\n  [columns]=\"tableColumns\"\r\n  scrollHeight=\"68vh\"\r\n  defaultSortColumnId=\"AuditDateTime\"\r\n  [defaultSortOrder]=\"'desc'\"\r\n  [pageIndex]=\"currentPageIndex\"\r\n  [pageSizeOptions]=\"pageSizeOptions\"\r\n  [pageSize]=\"pageSize\"\r\n  [isVirtualScroll]=\"false\"\r\n  [hidePagination]=\"false\"\r\n  [rowSelectable]=\"true\"\r\n  [lazyLoad]=\"true\"\r\n  class=\"audit-table\"\r\n  (onLazyLoad)=\"onLazyLoadEvent($event)\"\r\n  (onRowClick)=\"onRowClick($event)\"\r\n>\r\n</bdo-table>\r\n"], "mappings": "AAUA,SAASA,gBAAgB,QAAQ,yBAAyB;AAK1D,SAEEC,kBAAkB,EAClBC,YAAY,QAEP,kDAAkD;AACzD,SAASC,6BAA6B,QAAQ,qCAAqC;;;;;AAGnF;AAOA,OAAM,MAAOC,+BACX,SAAQJ,gBAAgB;EAwBxBK,YACEC,QAAkB,EACVC,YAA+B,EAChCC,oBAA+B;IAEtC,KAAK,CAACF,QAAQ,CAAC;IAHP,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IAxB7B;IACS,KAAAC,IAAI,GAAG,KAAK;IAKrB,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,YAAY,GAAG,CAAC;IAChB;IACA,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,gBAAgB,GAAG,CAAC;IAEpB,KAAAC,sBAAsB,GAAoB;MACxCC,cAAc,EAAE,IAAI,CAACH;KACH;IAEpB,KAAAI,YAAY,GAA+B,EAAE;IAE7C,KAAAC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACnC,KAAAC,OAAO,GAAG,gBAAgB;EAQ1B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAACb,YAAY,CAACc,uBAAuB,CAACC,SAAS,CAChDC,SAA0B,IAAI;MAC7BA,SAAS,CAACC,OAAO,GAAG,IAAI,CAACV,sBAAsB,CAACU,OAAO;MAEvD,IAAI,CAACV,sBAAsB,GAAGS,SAAS;MAEvC,IAAI,CAACE,eAAe,CAACC,SAAS,CAAC;IACjC,CAAC,CACF;IAEH,IAAI,CAACC,gCAAgC,GACnC,IAAI,CAACpB,YAAY,CAACqB,4BAA4B,CAACN,SAAS,CACrDO,QAAiB,IAAI;MACpB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACnB,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,YAAY,GAAG,CAAC;QACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACC,gBAAgB,GAAG,CAAC;QACzB,IAAI,CAACiB,YAAY,EAAE;MACrB;IACF,CAAC,CACF;EACL;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,2BAA2B,CAACY,WAAW,EAAE;IAC9C,IAAI,CAACL,gCAAgC,CAACK,WAAW,EAAE;EACrD;EAEA;;;;;EAKAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACzB,IAAI,EAAE;MAChB,IAAI,CAACA,IAAI,GAAGyB,OAAO,CAACzB,IAAI,CAAC0B,YAAY;MACrC,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACN,YAAY,EAAE;IACrB;EACF;EAEAM,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3B,IAAI,EAAE;MACb;MACA;MACA;MACA,IAAI,CAACO,YAAY,GAAG,CAClB;QACEqB,QAAQ,EAAE,UAAU;QACpBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,eAAe;QACzBC,IAAI,EAAErC,kBAAkB,CAACyC,QAAQ;QACjCF,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,YAAY;QACtBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,aAAa;QACvBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb;MACD;MACA;MACA;MACA;MACA;MACA;MACA;QACEJ,QAAQ,EAAE,aAAa;QACvBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,CACF;IACH,CAAC,MAAM;MACL;MACA;MACA;MACA,IAAI,CAACzB,YAAY,GAAG,CAClB;QACEqB,QAAQ,EAAE,UAAU;QACpBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,WAAW;QACrBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,eAAe;QACzBC,IAAI,EAAErC,kBAAkB,CAACyC,QAAQ;QACjCF,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,YAAY;QACtBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,gBAAgB;QAC1BC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EAED;QACEJ,QAAQ,EAAE,uBAAuB;QACjCC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,EACD;QACEJ,QAAQ,EAAE,aAAa;QACvBC,IAAI,EAAErC,kBAAkB,CAACsC,MAAM;QAC/BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE;OACb,CACF;IACH;EACF;EAEAX,YAAYA,CAAA;IACV,MAAMa,SAAS,GAAG,IAAIzC,YAAY,EAAE;IACpCyC,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACzB,OAAO,GAAG,gBAAgB;IACpCyB,SAAS,CAAChC,YAAY,GAAG,IAAI,CAACA,YAAY;IAC1CgC,SAAS,CAACE,IAAI,GAAG,IAAI,CAACnC,SAAS,CAACoC,GAAG,CAAEC,CAAc,IAAI;MACrD,OAAO;QACLC,EAAE,EAAED,CAAC,CAACC,EAAE;QACRC,OAAO,EAAEF,CAAC;QACVG,KAAK,EAAE,CACL;UACEb,QAAQ,EAAE,UAAU;UACpBc,KAAK,EAAEJ,CAAC,CAACK;SACV,EACD;UACEf,QAAQ,EAAE,eAAe;UACzBc,KAAK,EAAEJ,CAAC,CAACM;SACV,EACD;UACEhB,QAAQ,EAAE,YAAY;UACtBc,KAAK,EAAEJ,CAAC,CAACO;SACV,EACD;UACEjB,QAAQ,EAAE,gBAAgB;UAC1Bc,KAAK,EAAEJ,CAAC,CAACQ;SACV,EACD;UACElB,QAAQ,EAAE,uBAAuB;UACjCc,KAAK,EAAEJ,CAAC,CAACS;SACV,EACD;UACEnB,QAAQ,EAAE,WAAW;UACrBc,KAAK,EAAEJ,CAAC,CAACU;SACV,EACD;UACEpB,QAAQ,EAAE,aAAa;UACvBc,KAAK,EAAEJ,CAAC,CAACW;SACV,EACD;UACErB,QAAQ,EAAE,aAAa;UACvBc,KAAK,EAAEJ,CAAC,CAACY;SACV;OAEJ;IACH,CAAC,CAAC;IAEFC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,YAAY,CAACC,WAAW,CAACnB,SAAS,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAlB,eAAeA,CAACsC,KAAK;IACnB,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAACnD,QAAQ,MAAMmD,KAAK,CAACnD,QAAQ,IAAI,EAAE,CAAC,EAAE;QAC5C,IAAI,CAACC,gBAAgB,GAAGkD,KAAK,CAACC,UAAU,IAAI,CAAC;MAC/C,CAAC,MAAM;QACL,IAAI,CAACpD,QAAQ,GAAGmD,KAAK,CAACnD,QAAQ,IAAI,EAAE;QACpC,IAAI,CAACC,gBAAgB,GAAGkD,KAAK,CAACC,UAAU,IAAI,CAAC;MAC/C;IACF,CAAC,MAAM;MACL,IAAI,CAACnD,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACD,QAAQ,GAAG,EAAE;IACpB;IAEA,IAAI,CAACE,sBAAsB,CAACC,cAAc,GAAG,IAAI,CAACH,QAAQ;IAC1D,IAAI,CAACE,sBAAsB,CAACmD,SAAS,GACnC,IAAI,CAACpD,gBAAgB,GAAG,IAAI,CAACD,QAAQ;IACvC,IAAI,CAACE,sBAAsB,CAACU,OAAO,GACjCuC,KAAK,EAAEG,SAAS,IAAIH,KAAK,EAAEI,WAAW,GAAG,MAAM,GAAG,OAAO,CAAC;IAE5D,IAAI,CAAC5D,YAAY,CACd6D,YAAY,CAAC,IAAI,CAACtD,sBAAsB,CAAC,CACzCQ,SAAS,CAAEO,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACnB,SAAS,GAAGmB,QAAQ,CAACwC,KAAK,IAAI,EAAE;QACrC,IAAI,CAAC1D,YAAY,GAAGkB,QAAQ,CAACyC,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAAC5D,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,YAAY,GAAG,CAAC;MACvB;MAEAiD,UAAU,CAAC,MAAK;QACd,IAAI,CAAC9B,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAyC,UAAUA,CAACR,KAA4B;IACrC,IAAIlB,IAAI,GAAGkB,KAAK,CAACS,OAAO,CAACvB,OAAsB;IAC/C;IACA;IACA;IACA,IAAI,CAACwB,wBAAwB,CAC3B5B,IAAI,CAACG,EAAE,EACPH,IAAI,CAACO,QAAQ,EACbP,IAAI,CAACQ,aAAa,EAClBR,IAAI,CAACc,WAAW,EAChBd,IAAI,CAACY,SAAS,EACdZ,IAAI,CAACU,cAAc,EACnBV,IAAI,CAACW,qBAAqB,CAC3B;EACH;EAEAiB,wBAAwBA,CACtBC,UAAkB,EAClBtB,QAAgB,EAChBC,aAAqB,EACrBM,WAAmB,EACnBF,SAAiB,EACjBF,cAAsB,EACtBC,qBAA6B;IAE7B,MAAMmB,SAAS,GAAG,IAAI,CAACnE,oBAAoB,CAACoE,IAAI,CAC9CzE,6BAA6B,EAC7B;MACE0E,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,OAAO;MACd;MACAjC,IAAI,EAAE;QACJ6B,UAAU,EAAEA,UAAU;QACtBtB,QAAQ,EAAEA,QAAQ;QAClBC,aAAa,EAAEA,aAAa;QAC5BM,WAAW,EAAEA,WAAW;QACxBF,SAAS,EAAEA,SAAS;QACpBF,cAAc,EAAEA,cAAc;QAC9BC,qBAAqB,EAAEA,qBAAqB;QAC5C/C,IAAI,EAAE,IAAI,CAACA;;KAEd,CACF;IAEDkE,SAAS,CAACI,WAAW,EAAE,CAACzD,SAAS,CAAE0D,MAAM,IAAI;MAC3CC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,MAAM,CAAC;IAC/D,CAAC,CAAC;EACJ;;;uBAlTW5E,+BAA+B,EAAA+E,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,iBAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAA/BrF,+BAA+B;MAAAsF,SAAA;MAAAC,MAAA;QAAAlF,IAAA;MAAA;MAAAmF,QAAA,GAAAT,EAAA,CAAAU,0BAAA,EAAAV,EAAA,CAAAW,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9B1CjB,EADF,CAAAmB,cAAA,aAA4C,aACjB;UAAAnB,EAAA,CAAAoB,MAAA,0BAAmB;UAC9CpB,EAD8C,CAAAqB,YAAA,EAAM,EAC9C;UACNrB,EAAA,CAAAmB,cAAA,mBAgBC;UADCnB,EADA,CAAAsB,UAAA,wBAAAC,yEAAAC,MAAA;YAAA,OAAcN,GAAA,CAAA5E,eAAA,CAAAkF,MAAA,CAAuB;UAAA,EAAC,wBAAAC,yEAAAD,MAAA;YAAA,OACxBN,GAAA,CAAA9B,UAAA,CAAAoC,MAAA,CAAkB;UAAA,EAAC;UAEnCxB,EAAA,CAAAqB,YAAA,EAAY;;;UAhBVrB,EAAA,CAAA0B,SAAA,GAAc;UAWd1B,EAXA,CAAA2B,UAAA,OAAAT,GAAA,CAAAnF,OAAA,CAAc,YAAAmF,GAAA,CAAArF,YAAA,CACU,4BAGG,cAAAqF,GAAA,CAAAxF,gBAAA,CACG,oBAAAwF,GAAA,CAAApF,eAAA,CACK,aAAAoF,GAAA,CAAAzF,QAAA,CACd,0BACI,yBACD,uBACF,kBACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}