{"ast": null, "code": "import { mapEnumToOptions } from '@abp/ng.core';\nexport var UBOType;\n(function (UBOType) {\n  UBOType[UBOType[\"LegalPerson\"] = 0] = \"LegalPerson\";\n  UBOType[UBOType[\"LegalPersonOther\"] = 1] = \"LegalPersonOther\";\n  UBOType[UBOType[\"LegalPersonSenior\"] = 2] = \"LegalPersonSenior\";\n  UBOType[UBOType[\"LegalSettlor\"] = 3] = \"LegalSettlor\";\n  UBOType[UBOType[\"LegalTrustee\"] = 4] = \"LegalTrustee\";\n  UBOType[UBOType[\"LegalProtector\"] = 5] = \"LegalProtector\";\n  UBOType[UBOType[\"LegalBeneficiary\"] = 6] = \"LegalBeneficiary\";\n  UBOType[UBOType[\"LegalOther\"] = 7] = \"LegalOther\";\n  UBOType[UBOType[\"LegalSettlorEquivalent\"] = 8] = \"LegalSettlorEquivalent\";\n  UBOType[UBOType[\"LegalTrusteeEquivalent\"] = 9] = \"LegalTrusteeEquivalent\";\n  UBOType[UBOType[\"LegalProtectorEquivalent\"] = 10] = \"LegalProtectorEquivalent\";\n  UBOType[UBOType[\"LegalBeneficiaryEquivalent\"] = 11] = \"LegalBeneficiaryEquivalent\";\n  UBOType[UBOType[\"LegalOtherEquivalent\"] = 12] = \"LegalOtherEquivalent\";\n})(UBOType || (UBOType = {}));\nexport const uboTypeOptions = mapEnumToOptions(UBOType);", "map": {"version": 3, "names": ["mapEnumToOptions", "UBOType", "uboTypeOptions"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\shared\\constants\\declarations\\ubotype.enum.ts"], "sourcesContent": ["import { mapEnumToOptions } from '@abp/ng.core';\r\n\r\nexport enum UBOType {\r\n  LegalPerson = 0,\r\n  LegalPersonOther = 1,\r\n  LegalPersonSenior = 2,\r\n  LegalSettlor = 3,\r\n  LegalTrustee = 4,\r\n  LegalProtector = 5,\r\n  LegalBeneficiary = 6,\r\n  LegalOther = 7,\r\n  LegalSettlorEquivalent = 8,\r\n  LegalTrusteeEquivalent = 9,\r\n  LegalProtectorEquivalent = 10,\r\n  LegalBeneficiaryEquivalent = 11,\r\n  LegalOtherEquivalent = 12,\r\n}\r\n\r\nexport const uboTypeOptions = mapEnumToOptions(UBOType);\r\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,cAAc;AAE/C,WAAYC,OAcX;AAdD,WAAYA,OAAO;EACjBA,OAAA,CAAAA,OAAA,oCAAe;EACfA,OAAA,CAAAA,OAAA,8CAAoB;EACpBA,OAAA,CAAAA,OAAA,gDAAqB;EACrBA,OAAA,CAAAA,OAAA,sCAAgB;EAChBA,OAAA,CAAAA,OAAA,sCAAgB;EAChBA,OAAA,CAAAA,OAAA,0CAAkB;EAClBA,OAAA,CAAAA,OAAA,8CAAoB;EACpBA,OAAA,CAAAA,OAAA,kCAAc;EACdA,OAAA,CAAAA,OAAA,0DAA0B;EAC1BA,OAAA,CAAAA,OAAA,0DAA0B;EAC1BA,OAAA,CAAAA,OAAA,+DAA6B;EAC7BA,OAAA,CAAAA,OAAA,mEAA+B;EAC/BA,OAAA,CAAAA,OAAA,uDAAyB;AAC3B,CAAC,EAdWA,OAAO,KAAPA,OAAO;AAgBnB,OAAO,MAAMC,cAAc,GAAGF,gBAAgB,CAACC,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}