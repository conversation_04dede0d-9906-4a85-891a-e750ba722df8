{"ast": null, "code": "import { inject, Injector } from '@angular/core';\nimport { IdentityUserDelegationService } from '@volo/abp.ng.account/public/proxy';\nimport { from } from 'rxjs';\nimport { AuthService, PIPE_TO_LOGIN_FN_KEY } from '@abp/ng.core';\nimport * as i0 from \"@angular/core\";\nexport class AbpAuthorityDelegationService {\n  constructor() {\n    this.service = inject(IdentityUserDelegationService);\n    this.authService = inject(AuthService);\n    this.pipeToLogin = inject(PIPE_TO_LOGIN_FN_KEY);\n    this.injector = inject(Injector);\n    this.grantType = 'Impersonation';\n  }\n  isVisible() {\n    return this.authService.isAuthenticated;\n  }\n  getUsers() {\n    return this.service.getActiveDelegations();\n  }\n  delegatedImpersonate(userDelegationId) {\n    const promise = this.authService.loginUsingGrant(this.grantType, {\n      UserDelegationId: userDelegationId\n    });\n    return from(promise).pipe(this.pipeToLogin && this.pipeToLogin({}, this.injector));\n  }\n  getStatus(row) {\n    const {\n      startTime,\n      endTime\n    } = row;\n    const curr = new Date().getTime();\n    const beg = new Date(startTime).getTime();\n    const end = new Date(endTime).getTime();\n    if (beg > curr) {\n      return 'Future';\n    } else if (curr > end) {\n      return 'Expired';\n    } else if (beg < curr && curr < end) {\n      return 'Active';\n    } else {\n      return 'Expired';\n    }\n  }\n  statusClass(row) {\n    const status = this.getStatus(row);\n    switch (status) {\n      case 'Active':\n        return 'success';\n      case 'Expired':\n        return 'danger';\n      case 'Future':\n        return 'warning';\n      default:\n        return '';\n    }\n  }\n  static {\n    this.ɵfac = function AbpAuthorityDelegationService_Factory(t) {\n      return new (t || AbpAuthorityDelegationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AbpAuthorityDelegationService,\n      factory: AbpAuthorityDelegationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "Injector", "IdentityUserDelegationService", "from", "AuthService", "PIPE_TO_LOGIN_FN_KEY", "AbpAuthorityDelegationService", "constructor", "service", "authService", "pipeToLogin", "injector", "grantType", "isVisible", "isAuthenticated", "getUsers", "getActiveDelegations", "delegatedImpersonate", "userDelegationId", "promise", "loginUsingGrant", "UserDelegationId", "pipe", "getStatus", "row", "startTime", "endTime", "curr", "Date", "getTime", "beg", "end", "statusClass", "status", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity-config\\services\\authority-delegation.service.ts"], "sourcesContent": ["import { ComponentRef, inject, Injectable, Injector } from '@angular/core';\r\nimport {\r\n  IdentityUserDelegationService,\r\n  UserDelegationDto,\r\n} from '@volo/abp.ng.account/public/proxy';\r\nimport { from } from 'rxjs';\r\nimport { AuthService, PIPE_TO_LOGIN_FN_KEY } from '@abp/ng.core';\r\nimport { AuthorityDelegationComponent } from '../components';\r\nimport { Status } from '../models/delegate-authority';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AbpAuthorityDelegationService {\r\n  private service = inject(IdentityUserDelegationService);\r\n  private authService = inject(AuthService);\r\n  private pipeToLogin = inject(PIPE_TO_LOGIN_FN_KEY);\r\n  private injector = inject(Injector);\r\n  private grantType = 'Impersonation';\r\n\r\n  modalRef: ComponentRef<AuthorityDelegationComponent>;\r\n\r\n  isVisible() {\r\n    return this.authService.isAuthenticated;\r\n  }\r\n\r\n  getUsers() {\r\n    return this.service.getActiveDelegations();\r\n  }\r\n\r\n  delegatedImpersonate(userDelegationId: string) {\r\n    const promise = this.authService.loginUsingGrant(this.grantType, {\r\n      UserDelegationId: userDelegationId,\r\n    });\r\n    return from(promise).pipe(this.pipeToLogin && this.pipeToLogin({}, this.injector));\r\n  }\r\n\r\n  getStatus(row: UserDelegationDto): Status {\r\n    const { startTime, endTime } = row;\r\n    const curr = new Date().getTime();\r\n    const beg = new Date(startTime).getTime();\r\n    const end = new Date(endTime).getTime();\r\n\r\n    if (beg > curr) {\r\n      return 'Future';\r\n    } else if (curr > end) {\r\n      return 'Expired';\r\n    } else if (beg < curr && curr < end) {\r\n      return 'Active';\r\n    }\r\n    else {\r\n      return 'Expired';\r\n    }\r\n  }\r\n\r\n  statusClass(row: UserDelegationDto): string {\r\n    const status = this.getStatus(row);\r\n    switch (status) {\r\n      case 'Active':\r\n        return 'success';\r\n      case 'Expired':\r\n        return 'danger';\r\n      case 'Future':\r\n        return 'warning';\r\n      default:\r\n        return '';\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAuBA,MAAM,EAAcC,QAAQ,QAAQ,eAAe;AAC1E,SACEC,6BAA6B,QAExB,mCAAmC;AAC1C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,cAAc;;AAOhE,OAAM,MAAOC,6BAA6B;EAH1CC,YAAA;IAIU,KAAAC,OAAO,GAAGR,MAAM,CAACE,6BAA6B,CAAC;IAC/C,KAAAO,WAAW,GAAGT,MAAM,CAACI,WAAW,CAAC;IACjC,KAAAM,WAAW,GAAGV,MAAM,CAACK,oBAAoB,CAAC;IAC1C,KAAAM,QAAQ,GAAGX,MAAM,CAACC,QAAQ,CAAC;IAC3B,KAAAW,SAAS,GAAG,eAAe;;EAInCC,SAASA,CAAA;IACP,OAAO,IAAI,CAACJ,WAAW,CAACK,eAAe;EACzC;EAEAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACP,OAAO,CAACQ,oBAAoB,EAAE;EAC5C;EAEAC,oBAAoBA,CAACC,gBAAwB;IAC3C,MAAMC,OAAO,GAAG,IAAI,CAACV,WAAW,CAACW,eAAe,CAAC,IAAI,CAACR,SAAS,EAAE;MAC/DS,gBAAgB,EAAEH;KACnB,CAAC;IACF,OAAOf,IAAI,CAACgB,OAAO,CAAC,CAACG,IAAI,CAAC,IAAI,CAACZ,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC,EAAE,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;EACpF;EAEAY,SAASA,CAACC,GAAsB;IAC9B,MAAM;MAAEC,SAAS;MAAEC;IAAO,CAAE,GAAGF,GAAG;IAClC,MAAMG,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;IACjC,MAAMC,GAAG,GAAG,IAAIF,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,EAAE;IACzC,MAAME,GAAG,GAAG,IAAIH,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,EAAE;IAEvC,IAAIC,GAAG,GAAGH,IAAI,EAAE;MACd,OAAO,QAAQ;IACjB,CAAC,MAAM,IAAIA,IAAI,GAAGI,GAAG,EAAE;MACrB,OAAO,SAAS;IAClB,CAAC,MAAM,IAAID,GAAG,GAAGH,IAAI,IAAIA,IAAI,GAAGI,GAAG,EAAE;MACnC,OAAO,QAAQ;IACjB,CAAC,MACI;MACH,OAAO,SAAS;IAClB;EACF;EAEAC,WAAWA,CAACR,GAAsB;IAChC,MAAMS,MAAM,GAAG,IAAI,CAACV,SAAS,CAACC,GAAG,CAAC;IAClC,QAAQS,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB;QACE,OAAO,EAAE;IACb;EACF;;;uBAtDW3B,6BAA6B;IAAA;EAAA;;;aAA7BA,6BAA6B;MAAA4B,OAAA,EAA7B5B,6BAA6B,CAAA6B,IAAA;MAAAC,UAAA,EAF5B;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}