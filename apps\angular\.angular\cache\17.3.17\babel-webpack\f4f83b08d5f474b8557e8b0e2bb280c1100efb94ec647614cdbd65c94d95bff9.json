{"ast": null, "code": "import getUTCDayOfYear from \"../../../_lib/getUTCDayOfYear/index.js\";\nimport getUTCISOWeek from \"../../../_lib/getUTCISOWeek/index.js\";\nimport getUTCISOWeekYear from \"../../../_lib/getUTCISOWeekYear/index.js\";\nimport getUTCWeek from \"../../../_lib/getUTCWeek/index.js\";\nimport getUTCWeekYear from \"../../../_lib/getUTCWeekYear/index.js\";\nimport addLeadingZeros from \"../../addLeadingZeros/index.js\";\nimport lightFormatters from \"../lightFormatters/index.js\";\nvar dayPeriodEnum = {\n  am: 'am',\n  pm: 'pm',\n  midnight: 'midnight',\n  noon: 'noon',\n  morning: 'morning',\n  afternoon: 'afternoon',\n  evening: 'evening',\n  night: 'night'\n};\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nvar formatters = {\n  // Era\n  G: function G(date, token, localize) {\n    var era = date.getUTCFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case 'G':\n      case 'GG':\n      case 'GGG':\n        return localize.era(era, {\n          width: 'abbreviated'\n        });\n      // A, B\n      case 'GGGGG':\n        return localize.era(era, {\n          width: 'narrow'\n        });\n      // Anno Domini, Before Christ\n      case 'GGGG':\n      default:\n        return localize.era(era, {\n          width: 'wide'\n        });\n    }\n  },\n  // Year\n  y: function y(date, token, localize) {\n    // Ordinal number\n    if (token === 'yo') {\n      var signedYear = date.getUTCFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      var year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, {\n        unit: 'year'\n      });\n    }\n    return lightFormatters.y(date, token);\n  },\n  // Local week-numbering year\n  Y: function Y(date, token, localize, options) {\n    var signedWeekYear = getUTCWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    var weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === 'YY') {\n      var twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === 'Yo') {\n      return localize.ordinalNumber(weekYear, {\n        unit: 'year'\n      });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n  // ISO week-numbering year\n  R: function R(date, token) {\n    var isoWeekYear = getUTCISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function u(date, token) {\n    var year = date.getUTCFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n  // Quarter\n  Q: function Q(date, token, localize) {\n    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case 'Q':\n        return String(quarter);\n      // 01, 02, 03, 04\n      case 'QQ':\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case 'Qo':\n        return localize.ordinalNumber(quarter, {\n          unit: 'quarter'\n        });\n      // Q1, Q2, Q3, Q4\n      case 'QQQ':\n        return localize.quarter(quarter, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case 'QQQQQ':\n        return localize.quarter(quarter, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // 1st quarter, 2nd quarter, ...\n      case 'QQQQ':\n      default:\n        return localize.quarter(quarter, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone quarter\n  q: function q(date, token, localize) {\n    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case 'q':\n        return String(quarter);\n      // 01, 02, 03, 04\n      case 'qq':\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case 'qo':\n        return localize.ordinalNumber(quarter, {\n          unit: 'quarter'\n        });\n      // Q1, Q2, Q3, Q4\n      case 'qqq':\n        return localize.quarter(quarter, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case 'qqqqq':\n        return localize.quarter(quarter, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // 1st quarter, 2nd quarter, ...\n      case 'qqqq':\n      default:\n        return localize.quarter(quarter, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // Month\n  M: function M(date, token, localize) {\n    var month = date.getUTCMonth();\n    switch (token) {\n      case 'M':\n      case 'MM':\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case 'Mo':\n        return localize.ordinalNumber(month + 1, {\n          unit: 'month'\n        });\n      // Jan, Feb, ..., Dec\n      case 'MMM':\n        return localize.month(month, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // J, F, ..., D\n      case 'MMMMM':\n        return localize.month(month, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // January, February, ..., December\n      case 'MMMM':\n      default:\n        return localize.month(month, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone month\n  L: function L(date, token, localize) {\n    var month = date.getUTCMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case 'L':\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case 'LL':\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case 'Lo':\n        return localize.ordinalNumber(month + 1, {\n          unit: 'month'\n        });\n      // Jan, Feb, ..., Dec\n      case 'LLL':\n        return localize.month(month, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // J, F, ..., D\n      case 'LLLLL':\n        return localize.month(month, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // January, February, ..., December\n      case 'LLLL':\n      default:\n        return localize.month(month, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // Local week of year\n  w: function w(date, token, localize, options) {\n    var week = getUTCWeek(date, options);\n    if (token === 'wo') {\n      return localize.ordinalNumber(week, {\n        unit: 'week'\n      });\n    }\n    return addLeadingZeros(week, token.length);\n  },\n  // ISO week of year\n  I: function I(date, token, localize) {\n    var isoWeek = getUTCISOWeek(date);\n    if (token === 'Io') {\n      return localize.ordinalNumber(isoWeek, {\n        unit: 'week'\n      });\n    }\n    return addLeadingZeros(isoWeek, token.length);\n  },\n  // Day of the month\n  d: function d(date, token, localize) {\n    if (token === 'do') {\n      return localize.ordinalNumber(date.getUTCDate(), {\n        unit: 'date'\n      });\n    }\n    return lightFormatters.d(date, token);\n  },\n  // Day of year\n  D: function D(date, token, localize) {\n    var dayOfYear = getUTCDayOfYear(date);\n    if (token === 'Do') {\n      return localize.ordinalNumber(dayOfYear, {\n        unit: 'dayOfYear'\n      });\n    }\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n  // Day of week\n  E: function E(date, token, localize) {\n    var dayOfWeek = date.getUTCDay();\n    switch (token) {\n      // Tue\n      case 'E':\n      case 'EE':\n      case 'EEE':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n      case 'EEEEE':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n      case 'EEEEEE':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n      case 'EEEE':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Local day of week\n  e: function e(date, token, localize, options) {\n    var dayOfWeek = date.getUTCDay();\n    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case 'e':\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case 'ee':\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case 'eo':\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: 'day'\n        });\n      case 'eee':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n      case 'eeeee':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n      case 'eeeeee':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n      case 'eeee':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone local day of week\n  c: function c(date, token, localize, options) {\n    var dayOfWeek = date.getUTCDay();\n    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case 'c':\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case 'cc':\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case 'co':\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: 'day'\n        });\n      case 'ccc':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // T\n      case 'ccccc':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // Tu\n      case 'cccccc':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'standalone'\n        });\n      // Tuesday\n      case 'cccc':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // ISO day of week\n  i: function i(date, token, localize) {\n    var dayOfWeek = date.getUTCDay();\n    var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case 'i':\n        return String(isoDayOfWeek);\n      // 02\n      case 'ii':\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case 'io':\n        return localize.ordinalNumber(isoDayOfWeek, {\n          unit: 'day'\n        });\n      // Tue\n      case 'iii':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n      case 'iiiii':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n      case 'iiiiii':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n      case 'iiii':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // AM or PM\n  a: function a(date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'aaa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        }).toLowerCase();\n      case 'aaaaa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'aaaa':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // AM, PM, midnight, noon\n  b: function b(date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';\n    }\n    switch (token) {\n      case 'b':\n      case 'bb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'bbb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        }).toLowerCase();\n      case 'bbbbb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'bbbb':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // in the morning, in the afternoon, in the evening, at night\n  B: function B(date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n    switch (token) {\n      case 'B':\n      case 'BB':\n      case 'BBB':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'BBBBB':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'BBBB':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Hour [1-12]\n  h: function h(date, token, localize) {\n    if (token === 'ho') {\n      var hours = date.getUTCHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return lightFormatters.h(date, token);\n  },\n  // Hour [0-23]\n  H: function H(date, token, localize) {\n    if (token === 'Ho') {\n      return localize.ordinalNumber(date.getUTCHours(), {\n        unit: 'hour'\n      });\n    }\n    return lightFormatters.H(date, token);\n  },\n  // Hour [0-11]\n  K: function K(date, token, localize) {\n    var hours = date.getUTCHours() % 12;\n    if (token === 'Ko') {\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Hour [1-24]\n  k: function k(date, token, localize) {\n    var hours = date.getUTCHours();\n    if (hours === 0) hours = 24;\n    if (token === 'ko') {\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Minute\n  m: function m(date, token, localize) {\n    if (token === 'mo') {\n      return localize.ordinalNumber(date.getUTCMinutes(), {\n        unit: 'minute'\n      });\n    }\n    return lightFormatters.m(date, token);\n  },\n  // Second\n  s: function s(date, token, localize) {\n    if (token === 'so') {\n      return localize.ordinalNumber(date.getUTCSeconds(), {\n        unit: 'second'\n      });\n    }\n    return lightFormatters.s(date, token);\n  },\n  // Fraction of second\n  S: function S(date, token) {\n    return lightFormatters.S(date, token);\n  },\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function X(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    if (timezoneOffset === 0) {\n      return 'Z';\n    }\n    switch (token) {\n      // Hours and optional minutes\n      case 'X':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case 'XXXX':\n      case 'XX':\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case 'XXXXX':\n      case 'XXX': // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function x(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Hours and optional minutes\n      case 'x':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case 'xxxx':\n      case 'xx':\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case 'xxxxx':\n      case 'xxx': // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (GMT)\n  O: function O(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case 'O':\n      case 'OO':\n      case 'OOO':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n      case 'OOOO':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (specific non-location)\n  z: function z(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case 'z':\n      case 'zz':\n      case 'zzz':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n      case 'zzzz':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Seconds timestamp\n  t: function t(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timestamp = Math.floor(originalDate.getTime() / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n  // Milliseconds timestamp\n  T: function T(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timestamp = originalDate.getTime();\n    return addLeadingZeros(timestamp, token.length);\n  }\n};\nfunction formatTimezoneShort(offset, dirtyDelimiter) {\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = Math.floor(absOffset / 60);\n  var minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  var delimiter = dirtyDelimiter || '';\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\nfunction formatTimezoneWithOptionalMinutes(offset, dirtyDelimiter) {\n  if (offset % 60 === 0) {\n    var sign = offset > 0 ? '-' : '+';\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, dirtyDelimiter);\n}\nfunction formatTimezone(offset, dirtyDelimiter) {\n  var delimiter = dirtyDelimiter || '';\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n  var minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\nexport default formatters;", "map": {"version": 3, "names": ["getUTCDayOfYear", "getUTCISOWeek", "getUTCISOWeekYear", "getUTCWeek", "getUTCWeekYear", "addLeadingZeros", "lightFormatters", "dayPeriodEnum", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formatters", "G", "date", "token", "localize", "era", "getUTCFullYear", "width", "y", "signedYear", "year", "ordinalNumber", "unit", "Y", "options", "signedWeekYear", "weekYear", "twoDigitYear", "length", "R", "isoWeekYear", "u", "Q", "quarter", "Math", "ceil", "getUTCMonth", "String", "context", "q", "M", "month", "L", "w", "week", "I", "isoWeek", "d", "getUTCDate", "D", "dayOfYear", "E", "dayOfWeek", "getUTCDay", "day", "e", "localDayOfWeek", "weekStartsOn", "c", "i", "isoDayOfWeek", "a", "hours", "getUTCHours", "dayPeriodEnumValue", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "b", "B", "h", "H", "K", "k", "m", "getUTCMinutes", "s", "getUTCSeconds", "S", "X", "_localize", "originalDate", "_originalDate", "timezoneOffset", "getTimezoneOffset", "formatTimezoneWithOptionalMinutes", "formatTimezone", "x", "O", "formatTimezoneShort", "z", "t", "timestamp", "floor", "getTime", "T", "offset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "absOffset", "abs", "minutes", "delimiter"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/_lib/format/formatters/index.js"], "sourcesContent": ["import getUTCDayOfYear from \"../../../_lib/getUTCDayOfYear/index.js\";\nimport getUTCISOWeek from \"../../../_lib/getUTCISOWeek/index.js\";\nimport getUTCISOWeekYear from \"../../../_lib/getUTCISOWeekYear/index.js\";\nimport getUTCWeek from \"../../../_lib/getUTCWeek/index.js\";\nimport getUTCWeekYear from \"../../../_lib/getUTCWeekYear/index.js\";\nimport addLeadingZeros from \"../../addLeadingZeros/index.js\";\nimport lightFormatters from \"../lightFormatters/index.js\";\nvar dayPeriodEnum = {\n  am: 'am',\n  pm: 'pm',\n  midnight: 'midnight',\n  noon: 'noon',\n  morning: 'morning',\n  afternoon: 'afternoon',\n  evening: 'evening',\n  night: 'night'\n};\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nvar formatters = {\n  // Era\n  G: function G(date, token, localize) {\n    var era = date.getUTCFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case 'G':\n      case 'GG':\n      case 'GGG':\n        return localize.era(era, {\n          width: 'abbreviated'\n        });\n      // A, B\n      case 'GGGGG':\n        return localize.era(era, {\n          width: 'narrow'\n        });\n      // Anno Domini, Before Christ\n      case 'GGGG':\n      default:\n        return localize.era(era, {\n          width: 'wide'\n        });\n    }\n  },\n  // Year\n  y: function y(date, token, localize) {\n    // Ordinal number\n    if (token === 'yo') {\n      var signedYear = date.getUTCFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      var year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, {\n        unit: 'year'\n      });\n    }\n    return lightFormatters.y(date, token);\n  },\n  // Local week-numbering year\n  Y: function Y(date, token, localize, options) {\n    var signedWeekYear = getUTCWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    var weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === 'YY') {\n      var twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === 'Yo') {\n      return localize.ordinalNumber(weekYear, {\n        unit: 'year'\n      });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n  // ISO week-numbering year\n  R: function R(date, token) {\n    var isoWeekYear = getUTCISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function u(date, token) {\n    var year = date.getUTCFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n  // Quarter\n  Q: function Q(date, token, localize) {\n    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case 'Q':\n        return String(quarter);\n      // 01, 02, 03, 04\n      case 'QQ':\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case 'Qo':\n        return localize.ordinalNumber(quarter, {\n          unit: 'quarter'\n        });\n      // Q1, Q2, Q3, Q4\n      case 'QQQ':\n        return localize.quarter(quarter, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case 'QQQQQ':\n        return localize.quarter(quarter, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // 1st quarter, 2nd quarter, ...\n      case 'QQQQ':\n      default:\n        return localize.quarter(quarter, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone quarter\n  q: function q(date, token, localize) {\n    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case 'q':\n        return String(quarter);\n      // 01, 02, 03, 04\n      case 'qq':\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case 'qo':\n        return localize.ordinalNumber(quarter, {\n          unit: 'quarter'\n        });\n      // Q1, Q2, Q3, Q4\n      case 'qqq':\n        return localize.quarter(quarter, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case 'qqqqq':\n        return localize.quarter(quarter, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // 1st quarter, 2nd quarter, ...\n      case 'qqqq':\n      default:\n        return localize.quarter(quarter, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // Month\n  M: function M(date, token, localize) {\n    var month = date.getUTCMonth();\n    switch (token) {\n      case 'M':\n      case 'MM':\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case 'Mo':\n        return localize.ordinalNumber(month + 1, {\n          unit: 'month'\n        });\n      // Jan, Feb, ..., Dec\n      case 'MMM':\n        return localize.month(month, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // J, F, ..., D\n      case 'MMMMM':\n        return localize.month(month, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // January, February, ..., December\n      case 'MMMM':\n      default:\n        return localize.month(month, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone month\n  L: function L(date, token, localize) {\n    var month = date.getUTCMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case 'L':\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case 'LL':\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case 'Lo':\n        return localize.ordinalNumber(month + 1, {\n          unit: 'month'\n        });\n      // Jan, Feb, ..., Dec\n      case 'LLL':\n        return localize.month(month, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // J, F, ..., D\n      case 'LLLLL':\n        return localize.month(month, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // January, February, ..., December\n      case 'LLLL':\n      default:\n        return localize.month(month, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // Local week of year\n  w: function w(date, token, localize, options) {\n    var week = getUTCWeek(date, options);\n    if (token === 'wo') {\n      return localize.ordinalNumber(week, {\n        unit: 'week'\n      });\n    }\n    return addLeadingZeros(week, token.length);\n  },\n  // ISO week of year\n  I: function I(date, token, localize) {\n    var isoWeek = getUTCISOWeek(date);\n    if (token === 'Io') {\n      return localize.ordinalNumber(isoWeek, {\n        unit: 'week'\n      });\n    }\n    return addLeadingZeros(isoWeek, token.length);\n  },\n  // Day of the month\n  d: function d(date, token, localize) {\n    if (token === 'do') {\n      return localize.ordinalNumber(date.getUTCDate(), {\n        unit: 'date'\n      });\n    }\n    return lightFormatters.d(date, token);\n  },\n  // Day of year\n  D: function D(date, token, localize) {\n    var dayOfYear = getUTCDayOfYear(date);\n    if (token === 'Do') {\n      return localize.ordinalNumber(dayOfYear, {\n        unit: 'dayOfYear'\n      });\n    }\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n  // Day of week\n  E: function E(date, token, localize) {\n    var dayOfWeek = date.getUTCDay();\n    switch (token) {\n      // Tue\n      case 'E':\n      case 'EE':\n      case 'EEE':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n      case 'EEEEE':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n      case 'EEEEEE':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n      case 'EEEE':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Local day of week\n  e: function e(date, token, localize, options) {\n    var dayOfWeek = date.getUTCDay();\n    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case 'e':\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case 'ee':\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case 'eo':\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: 'day'\n        });\n      case 'eee':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n      case 'eeeee':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n      case 'eeeeee':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n      case 'eeee':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone local day of week\n  c: function c(date, token, localize, options) {\n    var dayOfWeek = date.getUTCDay();\n    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case 'c':\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case 'cc':\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case 'co':\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: 'day'\n        });\n      case 'ccc':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // T\n      case 'ccccc':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // Tu\n      case 'cccccc':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'standalone'\n        });\n      // Tuesday\n      case 'cccc':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // ISO day of week\n  i: function i(date, token, localize) {\n    var dayOfWeek = date.getUTCDay();\n    var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case 'i':\n        return String(isoDayOfWeek);\n      // 02\n      case 'ii':\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case 'io':\n        return localize.ordinalNumber(isoDayOfWeek, {\n          unit: 'day'\n        });\n      // Tue\n      case 'iii':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n      case 'iiiii':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n      case 'iiiiii':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n      case 'iiii':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // AM or PM\n  a: function a(date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'aaa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        }).toLowerCase();\n      case 'aaaaa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'aaaa':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // AM, PM, midnight, noon\n  b: function b(date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';\n    }\n    switch (token) {\n      case 'b':\n      case 'bb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'bbb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        }).toLowerCase();\n      case 'bbbbb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'bbbb':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // in the morning, in the afternoon, in the evening, at night\n  B: function B(date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n    switch (token) {\n      case 'B':\n      case 'BB':\n      case 'BBB':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'BBBBB':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'BBBB':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Hour [1-12]\n  h: function h(date, token, localize) {\n    if (token === 'ho') {\n      var hours = date.getUTCHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return lightFormatters.h(date, token);\n  },\n  // Hour [0-23]\n  H: function H(date, token, localize) {\n    if (token === 'Ho') {\n      return localize.ordinalNumber(date.getUTCHours(), {\n        unit: 'hour'\n      });\n    }\n    return lightFormatters.H(date, token);\n  },\n  // Hour [0-11]\n  K: function K(date, token, localize) {\n    var hours = date.getUTCHours() % 12;\n    if (token === 'Ko') {\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Hour [1-24]\n  k: function k(date, token, localize) {\n    var hours = date.getUTCHours();\n    if (hours === 0) hours = 24;\n    if (token === 'ko') {\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Minute\n  m: function m(date, token, localize) {\n    if (token === 'mo') {\n      return localize.ordinalNumber(date.getUTCMinutes(), {\n        unit: 'minute'\n      });\n    }\n    return lightFormatters.m(date, token);\n  },\n  // Second\n  s: function s(date, token, localize) {\n    if (token === 'so') {\n      return localize.ordinalNumber(date.getUTCSeconds(), {\n        unit: 'second'\n      });\n    }\n    return lightFormatters.s(date, token);\n  },\n  // Fraction of second\n  S: function S(date, token) {\n    return lightFormatters.S(date, token);\n  },\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function X(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    if (timezoneOffset === 0) {\n      return 'Z';\n    }\n    switch (token) {\n      // Hours and optional minutes\n      case 'X':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case 'XXXX':\n      case 'XX':\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case 'XXXXX':\n      case 'XXX': // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function x(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Hours and optional minutes\n      case 'x':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case 'xxxx':\n      case 'xx':\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case 'xxxxx':\n      case 'xxx': // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (GMT)\n  O: function O(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case 'O':\n      case 'OO':\n      case 'OOO':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n      case 'OOOO':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (specific non-location)\n  z: function z(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case 'z':\n      case 'zz':\n      case 'zzz':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n      case 'zzzz':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Seconds timestamp\n  t: function t(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timestamp = Math.floor(originalDate.getTime() / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n  // Milliseconds timestamp\n  T: function T(date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timestamp = originalDate.getTime();\n    return addLeadingZeros(timestamp, token.length);\n  }\n};\nfunction formatTimezoneShort(offset, dirtyDelimiter) {\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = Math.floor(absOffset / 60);\n  var minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  var delimiter = dirtyDelimiter || '';\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\nfunction formatTimezoneWithOptionalMinutes(offset, dirtyDelimiter) {\n  if (offset % 60 === 0) {\n    var sign = offset > 0 ? '-' : '+';\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, dirtyDelimiter);\n}\nfunction formatTimezone(offset, dirtyDelimiter) {\n  var delimiter = dirtyDelimiter || '';\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n  var minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\nexport default formatters;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,UAAU,MAAM,mCAAmC;AAC1D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,IAAIC,aAAa,GAAG;EAClBC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG;EACf;EACAC,CAAC,EAAE,SAASA,CAACA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIC,GAAG,GAAGH,IAAI,CAACI,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3C,QAAQH,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAOC,QAAQ,CAACC,GAAG,CAACA,GAAG,EAAE;UACvBE,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOH,QAAQ,CAACC,GAAG,CAACA,GAAG,EAAE;UACvBE,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOH,QAAQ,CAACC,GAAG,CAACA,GAAG,EAAE;UACvBE,KAAK,EAAE;QACT,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAC,CAAC,EAAE,SAASA,CAACA,CAACN,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC;IACA,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,IAAIM,UAAU,GAAGP,IAAI,CAACI,cAAc,CAAC,CAAC;MACtC;MACA,IAAII,IAAI,GAAGD,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAU;MACvD,OAAOL,QAAQ,CAACO,aAAa,CAACD,IAAI,EAAE;QAClCE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOtB,eAAe,CAACkB,CAAC,CAACN,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACAU,CAAC,EAAE,SAASA,CAACA,CAACX,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC5C,IAAIC,cAAc,GAAG3B,cAAc,CAACc,IAAI,EAAEY,OAAO,CAAC;IAClD;IACA,IAAIE,QAAQ,GAAGD,cAAc,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAGA,cAAc;;IAEvE;IACA,IAAIZ,KAAK,KAAK,IAAI,EAAE;MAClB,IAAIc,YAAY,GAAGD,QAAQ,GAAG,GAAG;MACjC,OAAO3B,eAAe,CAAC4B,YAAY,EAAE,CAAC,CAAC;IACzC;;IAEA;IACA,IAAId,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACK,QAAQ,EAAE;QACtCJ,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOvB,eAAe,CAAC2B,QAAQ,EAAEb,KAAK,CAACe,MAAM,CAAC;EAChD,CAAC;EACD;EACAC,CAAC,EAAE,SAASA,CAACA,CAACjB,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIiB,WAAW,GAAGlC,iBAAiB,CAACgB,IAAI,CAAC;;IAEzC;IACA,OAAOb,eAAe,CAAC+B,WAAW,EAAEjB,KAAK,CAACe,MAAM,CAAC;EACnD,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,CAAC,EAAE,SAASA,CAACA,CAACnB,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIO,IAAI,GAAGR,IAAI,CAACI,cAAc,CAAC,CAAC;IAChC,OAAOjB,eAAe,CAACqB,IAAI,EAAEP,KAAK,CAACe,MAAM,CAAC;EAC5C,CAAC;EACD;EACAI,CAAC,EAAE,SAASA,CAACA,CAACpB,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAImB,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACvB,IAAI,CAACwB,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACrD,QAAQvB,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACJ,OAAO,CAAC;MACxB;MACA,KAAK,IAAI;QACP,OAAOlC,eAAe,CAACkC,OAAO,EAAE,CAAC,CAAC;MACpC;MACA,KAAK,IAAI;QACP,OAAOnB,QAAQ,CAACO,aAAa,CAACY,OAAO,EAAE;UACrCX,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAC,CAAC,EAAE,SAASA,CAACA,CAAC3B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAImB,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACvB,IAAI,CAACwB,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACrD,QAAQvB,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACJ,OAAO,CAAC;MACxB;MACA,KAAK,IAAI;QACP,OAAOlC,eAAe,CAACkC,OAAO,EAAE,CAAC,CAAC;MACpC;MACA,KAAK,IAAI;QACP,OAAOnB,QAAQ,CAACO,aAAa,CAACY,OAAO,EAAE;UACrCX,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAE,CAAC,EAAE,SAASA,CAACA,CAAC5B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAI2B,KAAK,GAAG7B,IAAI,CAACwB,WAAW,CAAC,CAAC;IAC9B,QAAQvB,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOb,eAAe,CAACwC,CAAC,CAAC5B,IAAI,EAAEC,KAAK,CAAC;MACvC;MACA,KAAK,IAAI;QACP,OAAOC,QAAQ,CAACO,aAAa,CAACoB,KAAK,GAAG,CAAC,EAAE;UACvCnB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAI,CAAC,EAAE,SAASA,CAACA,CAAC9B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAI2B,KAAK,GAAG7B,IAAI,CAACwB,WAAW,CAAC,CAAC;IAC9B,QAAQvB,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC;MAC1B;MACA,KAAK,IAAI;QACP,OAAO1C,eAAe,CAAC0C,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;MACtC;MACA,KAAK,IAAI;QACP,OAAO3B,QAAQ,CAACO,aAAa,CAACoB,KAAK,GAAG,CAAC,EAAE;UACvCnB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAK,CAAC,EAAE,SAASA,CAACA,CAAC/B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC5C,IAAIoB,IAAI,GAAG/C,UAAU,CAACe,IAAI,EAAEY,OAAO,CAAC;IACpC,IAAIX,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACuB,IAAI,EAAE;QAClCtB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOvB,eAAe,CAAC6C,IAAI,EAAE/B,KAAK,CAACe,MAAM,CAAC;EAC5C,CAAC;EACD;EACAiB,CAAC,EAAE,SAASA,CAACA,CAACjC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIgC,OAAO,GAAGnD,aAAa,CAACiB,IAAI,CAAC;IACjC,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACyB,OAAO,EAAE;QACrCxB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOvB,eAAe,CAAC+C,OAAO,EAAEjC,KAAK,CAACe,MAAM,CAAC;EAC/C,CAAC;EACD;EACAmB,CAAC,EAAE,SAASA,CAACA,CAACnC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAACoC,UAAU,CAAC,CAAC,EAAE;QAC/C1B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOtB,eAAe,CAAC+C,CAAC,CAACnC,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACAoC,CAAC,EAAE,SAASA,CAACA,CAACrC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIoC,SAAS,GAAGxD,eAAe,CAACkB,IAAI,CAAC;IACrC,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAAC6B,SAAS,EAAE;QACvC5B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOvB,eAAe,CAACmD,SAAS,EAAErC,KAAK,CAACe,MAAM,CAAC;EACjD,CAAC;EACD;EACAuB,CAAC,EAAE,SAASA,CAACA,CAACvC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIsC,SAAS,GAAGxC,IAAI,CAACyC,SAAS,CAAC,CAAC;IAChC,QAAQxC,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAOC,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAiB,CAAC,EAAE,SAASA,CAACA,CAAC3C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC5C,IAAI4B,SAAS,GAAGxC,IAAI,CAACyC,SAAS,CAAC,CAAC;IAChC,IAAIG,cAAc,GAAG,CAACJ,SAAS,GAAG5B,OAAO,CAACiC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACpE,QAAQ5C,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACmB,cAAc,CAAC;MAC/B;MACA,KAAK,IAAI;QACP,OAAOzD,eAAe,CAACyD,cAAc,EAAE,CAAC,CAAC;MAC3C;MACA,KAAK,IAAI;QACP,OAAO1C,QAAQ,CAACO,aAAa,CAACmC,cAAc,EAAE;UAC5ClC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAoB,CAAC,EAAE,SAASA,CAACA,CAAC9C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC5C,IAAI4B,SAAS,GAAGxC,IAAI,CAACyC,SAAS,CAAC,CAAC;IAChC,IAAIG,cAAc,GAAG,CAACJ,SAAS,GAAG5B,OAAO,CAACiC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACpE,QAAQ5C,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACmB,cAAc,CAAC;MAC/B;MACA,KAAK,IAAI;QACP,OAAOzD,eAAe,CAACyD,cAAc,EAAE3C,KAAK,CAACe,MAAM,CAAC;MACtD;MACA,KAAK,IAAI;QACP,OAAOd,QAAQ,CAACO,aAAa,CAACmC,cAAc,EAAE;UAC5ClC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAqB,CAAC,EAAE,SAASA,CAACA,CAAC/C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIsC,SAAS,GAAGxC,IAAI,CAACyC,SAAS,CAAC,CAAC;IAChC,IAAIO,YAAY,GAAGR,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGA,SAAS;IAClD,QAAQvC,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACuB,YAAY,CAAC;MAC7B;MACA,KAAK,IAAI;QACP,OAAO7D,eAAe,CAAC6D,YAAY,EAAE/C,KAAK,CAACe,MAAM,CAAC;MACpD;MACA,KAAK,IAAI;QACP,OAAOd,QAAQ,CAACO,aAAa,CAACuC,YAAY,EAAE;UAC1CtC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACAuB,CAAC,EAAE,SAASA,CAACA,CAACjD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIgD,KAAK,GAAGlD,IAAI,CAACmD,WAAW,CAAC,CAAC;IAC9B,IAAIC,kBAAkB,GAAGF,KAAK,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACtD,QAAQjD,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOC,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,KAAK;QACR,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC,CAAC4B,WAAW,CAAC,CAAC;MAClB,KAAK,OAAO;QACV,OAAOpD,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACA6B,CAAC,EAAE,SAASA,CAACA,CAACvD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIgD,KAAK,GAAGlD,IAAI,CAACmD,WAAW,CAAC,CAAC;IAC9B,IAAIC,kBAAkB;IACtB,IAAIF,KAAK,KAAK,EAAE,EAAE;MAChBE,kBAAkB,GAAG/D,aAAa,CAACI,IAAI;IACzC,CAAC,MAAM,IAAIyD,KAAK,KAAK,CAAC,EAAE;MACtBE,kBAAkB,GAAG/D,aAAa,CAACG,QAAQ;IAC7C,CAAC,MAAM;MACL4D,kBAAkB,GAAGF,KAAK,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACpD;IACA,QAAQjD,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOC,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,KAAK;QACR,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC,CAAC4B,WAAW,CAAC,CAAC;MAClB,KAAK,OAAO;QACV,OAAOpD,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACA8B,CAAC,EAAE,SAASA,CAACA,CAACxD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIgD,KAAK,GAAGlD,IAAI,CAACmD,WAAW,CAAC,CAAC;IAC9B,IAAIC,kBAAkB;IACtB,IAAIF,KAAK,IAAI,EAAE,EAAE;MACfE,kBAAkB,GAAG/D,aAAa,CAACO,OAAO;IAC5C,CAAC,MAAM,IAAIsD,KAAK,IAAI,EAAE,EAAE;MACtBE,kBAAkB,GAAG/D,aAAa,CAACM,SAAS;IAC9C,CAAC,MAAM,IAAIuD,KAAK,IAAI,CAAC,EAAE;MACrBE,kBAAkB,GAAG/D,aAAa,CAACK,OAAO;IAC5C,CAAC,MAAM;MACL0D,kBAAkB,GAAG/D,aAAa,CAACQ,KAAK;IAC1C;IACA,QAAQI,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAOC,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EACD;EACA+B,CAAC,EAAE,SAASA,CAACA,CAACzD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,IAAIiD,KAAK,GAAGlD,IAAI,CAACmD,WAAW,CAAC,CAAC,GAAG,EAAE;MACnC,IAAID,KAAK,KAAK,CAAC,EAAEA,KAAK,GAAG,EAAE;MAC3B,OAAOhD,QAAQ,CAACO,aAAa,CAACyC,KAAK,EAAE;QACnCxC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOtB,eAAe,CAACqE,CAAC,CAACzD,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACAyD,CAAC,EAAE,SAASA,CAACA,CAAC1D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAACmD,WAAW,CAAC,CAAC,EAAE;QAChDzC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOtB,eAAe,CAACsE,CAAC,CAAC1D,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACA0D,CAAC,EAAE,SAASA,CAACA,CAAC3D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIgD,KAAK,GAAGlD,IAAI,CAACmD,WAAW,CAAC,CAAC,GAAG,EAAE;IACnC,IAAIlD,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACyC,KAAK,EAAE;QACnCxC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOvB,eAAe,CAAC+D,KAAK,EAAEjD,KAAK,CAACe,MAAM,CAAC;EAC7C,CAAC;EACD;EACA4C,CAAC,EAAE,SAASA,CAACA,CAAC5D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAIgD,KAAK,GAAGlD,IAAI,CAACmD,WAAW,CAAC,CAAC;IAC9B,IAAID,KAAK,KAAK,CAAC,EAAEA,KAAK,GAAG,EAAE;IAC3B,IAAIjD,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACyC,KAAK,EAAE;QACnCxC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOvB,eAAe,CAAC+D,KAAK,EAAEjD,KAAK,CAACe,MAAM,CAAC;EAC7C,CAAC;EACD;EACA6C,CAAC,EAAE,SAASA,CAACA,CAAC7D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAAC8D,aAAa,CAAC,CAAC,EAAE;QAClDpD,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOtB,eAAe,CAACyE,CAAC,CAAC7D,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACA8D,CAAC,EAAE,SAASA,CAACA,CAAC/D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACnC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAACgE,aAAa,CAAC,CAAC,EAAE;QAClDtD,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,OAAOtB,eAAe,CAAC2E,CAAC,CAAC/D,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACAgE,CAAC,EAAE,SAASA,CAACA,CAACjE,IAAI,EAAEC,KAAK,EAAE;IACzB,OAAOb,eAAe,CAAC6E,CAAC,CAACjE,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EACD;EACAiE,CAAC,EAAE,SAASA,CAACA,CAAClE,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAEvD,OAAO,EAAE;IAC7C,IAAIwD,YAAY,GAAGxD,OAAO,CAACyD,aAAa,IAAIrE,IAAI;IAChD,IAAIsE,cAAc,GAAGF,YAAY,CAACG,iBAAiB,CAAC,CAAC;IACrD,IAAID,cAAc,KAAK,CAAC,EAAE;MACxB,OAAO,GAAG;IACZ;IACA,QAAQrE,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOuE,iCAAiC,CAACF,cAAc,CAAC;;MAE1D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QACP;QACA,OAAOG,cAAc,CAACH,cAAc,CAAC;;MAEvC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACE,OAAOG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IAC9C;EACF,CAAC;EACD;EACAI,CAAC,EAAE,SAASA,CAACA,CAAC1E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAEvD,OAAO,EAAE;IAC7C,IAAIwD,YAAY,GAAGxD,OAAO,CAACyD,aAAa,IAAIrE,IAAI;IAChD,IAAIsE,cAAc,GAAGF,YAAY,CAACG,iBAAiB,CAAC,CAAC;IACrD,QAAQtE,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOuE,iCAAiC,CAACF,cAAc,CAAC;;MAE1D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QACP;QACA,OAAOG,cAAc,CAACH,cAAc,CAAC;;MAEvC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACE,OAAOG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IAC9C;EACF,CAAC;EACD;EACAK,CAAC,EAAE,SAASA,CAACA,CAAC3E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAEvD,OAAO,EAAE;IAC7C,IAAIwD,YAAY,GAAGxD,OAAO,CAACyD,aAAa,IAAIrE,IAAI;IAChD,IAAIsE,cAAc,GAAGF,YAAY,CAACG,iBAAiB,CAAC,CAAC;IACrD,QAAQtE,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAO,KAAK,GAAG2E,mBAAmB,CAACN,cAAc,EAAE,GAAG,CAAC;MACzD;MACA,KAAK,MAAM;MACX;QACE,OAAO,KAAK,GAAGG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IACtD;EACF,CAAC;EACD;EACAO,CAAC,EAAE,SAASA,CAACA,CAAC7E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAEvD,OAAO,EAAE;IAC7C,IAAIwD,YAAY,GAAGxD,OAAO,CAACyD,aAAa,IAAIrE,IAAI;IAChD,IAAIsE,cAAc,GAAGF,YAAY,CAACG,iBAAiB,CAAC,CAAC;IACrD,QAAQtE,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAO,KAAK,GAAG2E,mBAAmB,CAACN,cAAc,EAAE,GAAG,CAAC;MACzD;MACA,KAAK,MAAM;MACX;QACE,OAAO,KAAK,GAAGG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IACtD;EACF,CAAC;EACD;EACAQ,CAAC,EAAE,SAASA,CAACA,CAAC9E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAEvD,OAAO,EAAE;IAC7C,IAAIwD,YAAY,GAAGxD,OAAO,CAACyD,aAAa,IAAIrE,IAAI;IAChD,IAAI+E,SAAS,GAAGzD,IAAI,CAAC0D,KAAK,CAACZ,YAAY,CAACa,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IACzD,OAAO9F,eAAe,CAAC4F,SAAS,EAAE9E,KAAK,CAACe,MAAM,CAAC;EACjD,CAAC;EACD;EACAkE,CAAC,EAAE,SAASA,CAACA,CAAClF,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAEvD,OAAO,EAAE;IAC7C,IAAIwD,YAAY,GAAGxD,OAAO,CAACyD,aAAa,IAAIrE,IAAI;IAChD,IAAI+E,SAAS,GAAGX,YAAY,CAACa,OAAO,CAAC,CAAC;IACtC,OAAO9F,eAAe,CAAC4F,SAAS,EAAE9E,KAAK,CAACe,MAAM,CAAC;EACjD;AACF,CAAC;AACD,SAAS4D,mBAAmBA,CAACO,MAAM,EAAEC,cAAc,EAAE;EACnD,IAAIC,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACjC,IAAIG,SAAS,GAAGhE,IAAI,CAACiE,GAAG,CAACJ,MAAM,CAAC;EAChC,IAAIjC,KAAK,GAAG5B,IAAI,CAAC0D,KAAK,CAACM,SAAS,GAAG,EAAE,CAAC;EACtC,IAAIE,OAAO,GAAGF,SAAS,GAAG,EAAE;EAC5B,IAAIE,OAAO,KAAK,CAAC,EAAE;IACjB,OAAOH,IAAI,GAAG5D,MAAM,CAACyB,KAAK,CAAC;EAC7B;EACA,IAAIuC,SAAS,GAAGL,cAAc,IAAI,EAAE;EACpC,OAAOC,IAAI,GAAG5D,MAAM,CAACyB,KAAK,CAAC,GAAGuC,SAAS,GAAGtG,eAAe,CAACqG,OAAO,EAAE,CAAC,CAAC;AACvE;AACA,SAAShB,iCAAiCA,CAACW,MAAM,EAAEC,cAAc,EAAE;EACjE,IAAID,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;IACrB,IAAIE,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACjC,OAAOE,IAAI,GAAGlG,eAAe,CAACmC,IAAI,CAACiE,GAAG,CAACJ,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACzD;EACA,OAAOV,cAAc,CAACU,MAAM,EAAEC,cAAc,CAAC;AAC/C;AACA,SAASX,cAAcA,CAACU,MAAM,EAAEC,cAAc,EAAE;EAC9C,IAAIK,SAAS,GAAGL,cAAc,IAAI,EAAE;EACpC,IAAIC,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACjC,IAAIG,SAAS,GAAGhE,IAAI,CAACiE,GAAG,CAACJ,MAAM,CAAC;EAChC,IAAIjC,KAAK,GAAG/D,eAAe,CAACmC,IAAI,CAAC0D,KAAK,CAACM,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1D,IAAIE,OAAO,GAAGrG,eAAe,CAACmG,SAAS,GAAG,EAAE,EAAE,CAAC,CAAC;EAChD,OAAOD,IAAI,GAAGnC,KAAK,GAAGuC,SAAS,GAAGD,OAAO;AAC3C;AACA,eAAe1F,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}