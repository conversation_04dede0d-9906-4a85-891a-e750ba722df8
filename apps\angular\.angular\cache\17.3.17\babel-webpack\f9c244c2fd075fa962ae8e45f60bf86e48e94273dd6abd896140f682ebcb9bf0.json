{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isProtectedDayOfYearToken = isProtectedDayOfYearToken;\nexports.isProtectedWeekYearToken = isProtectedWeekYearToken;\nexports.throwProtectedError = throwProtectedError;\nvar protectedDayOfYearTokens = ['D', 'DD'];\nvar protectedWeekYearTokens = ['YY', 'YYYY'];\nfunction isProtectedDayOfYearToken(token) {\n  return protectedDayOfYearTokens.indexOf(token) !== -1;\n}\nfunction isProtectedWeekYearToken(token) {\n  return protectedWeekYearTokens.indexOf(token) !== -1;\n}\nfunction throwProtectedError(token, format, input) {\n  if (token === 'YYYY') {\n    throw new RangeError(\"Use `yyyy` instead of `YYYY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'YY') {\n    throw new RangeError(\"Use `yy` instead of `YY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'D') {\n    throw new RangeError(\"Use `d` instead of `D` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'DD') {\n    throw new RangeError(\"Use `dd` instead of `DD` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isProtectedDayOfYearToken", "isProtectedWeekYearToken", "throwProtectedError", "protectedDayOfYearTokens", "protectedWeekYearTokens", "token", "indexOf", "format", "input", "RangeError", "concat"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/_lib/protectedTokens/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isProtectedDayOfYearToken = isProtectedDayOfYearToken;\nexports.isProtectedWeekYearToken = isProtectedWeekYearToken;\nexports.throwProtectedError = throwProtectedError;\nvar protectedDayOfYearTokens = ['D', 'DD'];\nvar protectedWeekYearTokens = ['YY', 'YYYY'];\nfunction isProtectedDayOfYearToken(token) {\n  return protectedDayOfYearTokens.indexOf(token) !== -1;\n}\nfunction isProtectedWeekYearToken(token) {\n  return protectedWeekYearTokens.indexOf(token) !== -1;\n}\nfunction throwProtectedError(token, format, input) {\n  if (token === 'YYYY') {\n    throw new RangeError(\"Use `yyyy` instead of `YYYY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'YY') {\n    throw new RangeError(\"Use `yy` instead of `YY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'D') {\n    throw new RangeError(\"Use `d` instead of `D` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'DD') {\n    throw new RangeError(\"Use `dd` instead of `DD` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB;AAC7DF,OAAO,CAACG,wBAAwB,GAAGA,wBAAwB;AAC3DH,OAAO,CAACI,mBAAmB,GAAGA,mBAAmB;AACjD,IAAIC,wBAAwB,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;AAC1C,IAAIC,uBAAuB,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC;AAC5C,SAASJ,yBAAyBA,CAACK,KAAK,EAAE;EACxC,OAAOF,wBAAwB,CAACG,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AACvD;AACA,SAASJ,wBAAwBA,CAACI,KAAK,EAAE;EACvC,OAAOD,uBAAuB,CAACE,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AACtD;AACA,SAASH,mBAAmBA,CAACG,KAAK,EAAEE,MAAM,EAAEC,KAAK,EAAE;EACjD,IAAIH,KAAK,KAAK,MAAM,EAAE;IACpB,MAAM,IAAII,UAAU,CAAC,oCAAoC,CAACC,MAAM,CAACH,MAAM,EAAE,wCAAwC,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EACrN,CAAC,MAAM,IAAIH,KAAK,KAAK,IAAI,EAAE;IACzB,MAAM,IAAII,UAAU,CAAC,gCAAgC,CAACC,MAAM,CAACH,MAAM,EAAE,wCAAwC,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EACjN,CAAC,MAAM,IAAIH,KAAK,KAAK,GAAG,EAAE;IACxB,MAAM,IAAII,UAAU,CAAC,8BAA8B,CAACC,MAAM,CAACH,MAAM,EAAE,oDAAoD,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EAC3N,CAAC,MAAM,IAAIH,KAAK,KAAK,IAAI,EAAE;IACzB,MAAM,IAAII,UAAU,CAAC,gCAAgC,CAACC,MAAM,CAACH,MAAM,EAAE,oDAAoD,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EAC7N;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}