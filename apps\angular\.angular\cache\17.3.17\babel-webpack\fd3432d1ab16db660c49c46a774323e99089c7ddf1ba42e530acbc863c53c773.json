{"ast": null, "code": "import { inject } from '@angular/core';\nimport { ChangePasswordService } from '../../services/change-password.service';\nimport { SubscriptionService } from '@abp/ng.core';\nimport { OAuthService } from 'angular-oauth2-oidc';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@abp/ng.core\";\nimport * as i4 from \"@ngx-validate/core\";\nimport * as i5 from \"@abp/ng.theme.shared\";\nimport * as i6 from \"ngx-intl-tel-input\";\nfunction ChangePasswordComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"label\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \" * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"abp-password\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"AbpIdentity::DisplayName:CurrentPassword\"));\n  }\n}\nexport class ChangePasswordComponent {\n  constructor() {\n    this.service = inject(ChangePasswordService);\n    this.subscription = inject(SubscriptionService);\n    this.hideCurrentPassword = false;\n    this.oAuthService = inject(OAuthService);\n    this.mapErrorsFn = this.service.MapErrorsFnFactory();\n  }\n  ngOnInit() {\n    this.hideCurrentPassword = !this.service.hasPassword;\n    this.form = this.service.buildForm(this.hideCurrentPassword);\n  }\n  onSuccess() {\n    this.service.showSuccessMessage();\n    this.hideCurrentPassword = false;\n    this.form = this.service.buildForm(this.hideCurrentPassword);\n  }\n  onSubmit() {\n    if (this.form.invalid) return;\n    const sub = this.service.changePassword(this.form.value);\n    this.subscription.addOne(sub, value => {\n      this.service.showSuccessMessage();\n      this.hideCurrentPassword = false;\n      this.form = this.service.buildForm(this.hideCurrentPassword);\n      //logout the user after changing password\n      setTimeout(() => {\n        this.oAuthService.revokeTokenAndLogout();\n      }, 3000);\n    }, this.service.showErrorMessage);\n  }\n  static {\n    this.ɵfac = function ChangePasswordComponent_Factory(t) {\n      return new (t || ChangePasswordComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChangePasswordComponent,\n      selectors: [[\"abp-change-password-form\"]],\n      exportAs: [\"abpChangePasswordForm\"],\n      features: [i0.ɵɵProvidersFeature([SubscriptionService])],\n      decls: 19,\n      vars: 12,\n      consts: [[3, \"ngSubmit\", \"formGroup\", \"mapErrorsFn\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"mb-3\"], [\"for\", \"new-password\", 1, \"form-label\"], [\"inputId\", \"new-password\", \"formControlName\", \"newPassword\"], [\"for\", \"confirm-new-password\", 1, \"form-label\"], [\"inputId\", \"confirm-new-password\", \"formControlName\", \"repeatNewPassword\"], [\"iconClass\", \"fa fa-check\", \"buttonClass\", \"btn btn-primary color-white\", \"buttonType\", \"submit\"], [\"for\", \"current-password\", 1, \"form-label\"], [\"inputId\", \"current-password\", \"formControlName\", \"currentPassword\"]],\n      template: function ChangePasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function ChangePasswordComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(1, ChangePasswordComponent_div_1_Template, 7, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"label\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"abpLocalization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \" * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"abp-password\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 2)(10, \"label\", 5);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"abpLocalization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \" * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"abp-password\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"abp-button\", 7);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"abpLocalization\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form)(\"mapErrorsFn\", ctx.mapErrorsFn);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hideCurrentPassword);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 6, \"AbpIdentity::DisplayName:NewPassword\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 8, \"AbpIdentity::DisplayName:NewPasswordConfirm\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 10, \"AbpIdentity::Save\"));\n        }\n      },\n      dependencies: [i1.NgIf, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i3.FormSubmitDirective, i4.ValidationGroupDirective, i4.ValidationDirective, i5.PasswordComponent, i5.ButtonComponent, i6.NativeElementInjectorDirective, i3.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "ChangePasswordService", "SubscriptionService", "OAuthService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ChangePasswordComponent", "constructor", "service", "subscription", "hideCurrentPassword", "oAuthService", "mapErrorsFn", "MapErrorsFnFactory", "ngOnInit", "hasPassword", "form", "buildForm", "onSuccess", "showSuccessMessage", "onSubmit", "invalid", "sub", "changePassword", "value", "addOne", "setTimeout", "revokeTokenAndLogout", "showErrorMessage", "selectors", "exportAs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "ChangePasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ChangePasswordComponent_Template_form_ngSubmit_0_listener", "ɵɵtemplate", "ChangePasswordComponent_div_1_Template", "ɵɵproperty"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\components\\change-password\\change-password.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\components\\change-password\\change-password.component.html"], "sourcesContent": ["import { Component, inject, OnInit } from '@angular/core';\r\nimport { UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { ChangePasswordService } from '../../services/change-password.service';\r\nimport { SubscriptionService } from '@abp/ng.core';\r\nimport { OAuthService } from 'angular-oauth2-oidc';\r\n\r\n@Component({\r\n  selector: 'abp-change-password-form',\r\n  templateUrl: './change-password.component.html',\r\n  exportAs: 'abpChangePasswordForm',\r\n  providers: [SubscriptionService],\r\n})\r\nexport class ChangePasswordComponent implements OnInit {\r\n  form: UntypedFormGroup;\r\n  inProgress: boolean;\r\n  private readonly service: ChangePasswordService = inject(ChangePasswordService);\r\n  protected readonly subscription = inject(SubscriptionService);\r\n  public hideCurrentPassword = false;\r\n  private oAuthService: OAuthService = inject(OAuthService);\r\n\r\n  mapErrorsFn = this.service.MapErrorsFnFactory();\r\n\r\n  ngOnInit(): void {\r\n    this.hideCurrentPassword = !this.service.hasPassword;\r\n    this.form = this.service.buildForm(this.hideCurrentPassword);\r\n  }\r\n\r\n  onSuccess() {\r\n    this.service.showSuccessMessage();\r\n    this.hideCurrentPassword = false;\r\n    this.form = this.service.buildForm(this.hideCurrentPassword);\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.form.invalid) return;\r\n\r\n    const sub = this.service.changePassword(this.form.value);\r\n    this.subscription.addOne(sub, \r\n      (value: any) => {\r\n        this.service.showSuccessMessage();\r\n        this.hideCurrentPassword = false;\r\n        this.form = this.service.buildForm(this.hideCurrentPassword);\r\n\r\n        //logout the user after changing password\r\n        setTimeout(() => {\r\n          this.oAuthService.revokeTokenAndLogout();\r\n        }, 3000)\r\n      }\r\n    , this.service.showErrorMessage);\r\n  }\r\n}\r\n", "<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" [mapErrorsFn]=\"mapErrorsFn\">\r\n  <div *ngIf=\"!hideCurrentPassword\" class=\"mb-3\">\r\n    <label class=\"form-label\" for=\"current-password\">{{\r\n      'AbpIdentity::DisplayName:CurrentPassword' | abpLocalization\r\n    }}</label\r\n    ><span> * </span>\r\n    <abp-password\r\n      inputId=\"current-password\"\r\n      formControlName=\"currentPassword\"\r\n    ></abp-password>\r\n  </div>\r\n  <div class=\"mb-3\">\r\n    <label class=\"form-label\" for=\"new-password\">{{\r\n      'AbpIdentity::DisplayName:NewPassword' | abpLocalization\r\n    }}</label\r\n    ><span> * </span>\r\n    <abp-password\r\n      inputId=\"new-password\"\r\n      formControlName=\"newPassword\"\r\n    ></abp-password>\r\n  </div>\r\n  <div class=\"mb-3\">\r\n    <label class=\"form-label\" for=\"confirm-new-password\">{{\r\n      'AbpIdentity::DisplayName:NewPasswordConfirm' | abpLocalization\r\n    }}</label\r\n    ><span> * </span>\r\n\r\n    <abp-password\r\n      inputId=\"confirm-new-password\"\r\n      formControlName=\"repeatNewPassword\"\r\n    ></abp-password>\r\n  </div>\r\n  <abp-button\r\n    iconClass=\"fa fa-check\"\r\n    buttonClass=\"btn btn-primary color-white\"\r\n    buttonType=\"submit\"\r\n    >{{ 'AbpIdentity::Save' | abpLocalization }}</abp-button\r\n  >\r\n</form>\r\n"], "mappings": "AAAA,SAAoBA,MAAM,QAAgB,eAAe;AAEzD,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,YAAY,QAAQ,qBAAqB;;;;;;;;;;ICF9CC,EADF,CAAAC,cAAA,aAA+C,eACI;IAAAD,EAAA,CAAAE,MAAA,GAE/C;;IAAAF,EAAA,CAAAG,YAAA,EACD;IAAAH,EAAA,CAAAC,cAAA,WAAM;IAACD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjBH,EAAA,CAAAI,SAAA,sBAGgB;IAClBJ,EAAA,CAAAG,YAAA,EAAM;;;IAR6CH,EAAA,CAAAK,SAAA,GAE/C;IAF+CL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,mDAE/C;;;ADQN,OAAM,MAAOC,uBAAuB;EANpCC,YAAA;IASmB,KAAAC,OAAO,GAA0Bd,MAAM,CAACC,qBAAqB,CAAC;IAC5D,KAAAc,YAAY,GAAGf,MAAM,CAACE,mBAAmB,CAAC;IACtD,KAAAc,mBAAmB,GAAG,KAAK;IAC1B,KAAAC,YAAY,GAAiBjB,MAAM,CAACG,YAAY,CAAC;IAEzD,KAAAe,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACK,kBAAkB,EAAE;;EAE/CC,QAAQA,CAAA;IACN,IAAI,CAACJ,mBAAmB,GAAG,CAAC,IAAI,CAACF,OAAO,CAACO,WAAW;IACpD,IAAI,CAACC,IAAI,GAAG,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,IAAI,CAACP,mBAAmB,CAAC;EAC9D;EAEAQ,SAASA,CAAA;IACP,IAAI,CAACV,OAAO,CAACW,kBAAkB,EAAE;IACjC,IAAI,CAACT,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACM,IAAI,GAAG,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,IAAI,CAACP,mBAAmB,CAAC;EAC9D;EAEAU,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,IAAI,CAACK,OAAO,EAAE;IAEvB,MAAMC,GAAG,GAAG,IAAI,CAACd,OAAO,CAACe,cAAc,CAAC,IAAI,CAACP,IAAI,CAACQ,KAAK,CAAC;IACxD,IAAI,CAACf,YAAY,CAACgB,MAAM,CAACH,GAAG,EACzBE,KAAU,IAAI;MACb,IAAI,CAAChB,OAAO,CAACW,kBAAkB,EAAE;MACjC,IAAI,CAACT,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACM,IAAI,GAAG,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,IAAI,CAACP,mBAAmB,CAAC;MAE5D;MACAgB,UAAU,CAAC,MAAK;QACd,IAAI,CAACf,YAAY,CAACgB,oBAAoB,EAAE;MAC1C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EACD,IAAI,CAACnB,OAAO,CAACoB,gBAAgB,CAAC;EAClC;;;uBArCWtB,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAuB,SAAA;MAAAC,QAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,kBAAA,CAFvB,CAACpC,mBAAmB,CAAC;MAAAqC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVlCxC,EAAA,CAAAC,cAAA,cAA6E;UAApDD,EAAA,CAAA0C,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAAnB,QAAA,EAAU;UAAA,EAAC;UAC9CtB,EAAA,CAAA4C,UAAA,IAAAC,sCAAA,iBAA+C;UAW7C7C,EADF,CAAAC,cAAA,aAAkB,eAC6B;UAAAD,EAAA,CAAAE,MAAA,GAE3C;;UAAAF,EAAA,CAAAG,YAAA,EACD;UAAAH,EAAA,CAAAC,cAAA,WAAM;UAACD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAI,SAAA,sBAGgB;UAClBJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,aAAkB,gBACqC;UAAAD,EAAA,CAAAE,MAAA,IAEnD;;UAAAF,EAAA,CAAAG,YAAA,EACD;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAACD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEjBH,EAAA,CAAAI,SAAA,uBAGgB;UAClBJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,qBAIG;UAAAD,EAAA,CAAAE,MAAA,IAA2C;;UAEhDF,EAFgD,CAAAG,YAAA,EAC7C,EACI;;;UAtC0CH,EAA3C,CAAA8C,UAAA,cAAAL,GAAA,CAAAvB,IAAA,CAAkB,gBAAAuB,GAAA,CAAA3B,WAAA,CAAoD;UACpEd,EAAA,CAAAK,SAAA,EAA0B;UAA1BL,EAAA,CAAA8C,UAAA,UAAAL,GAAA,CAAA7B,mBAAA,CAA0B;UAWeZ,EAAA,CAAAK,SAAA,GAE3C;UAF2CL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,+CAE3C;UAQmDP,EAAA,CAAAK,SAAA,GAEnD;UAFmDL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,uDAEnD;UAYDP,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,8BAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}