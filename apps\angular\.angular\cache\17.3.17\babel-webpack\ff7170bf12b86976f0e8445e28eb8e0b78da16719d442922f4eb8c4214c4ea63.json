{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"sr-Latn-XK\", [[\"AM\", \"PM\"], u, u], [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"n\", \"p\", \"u\", \"s\", \"č\", \"p\", \"s\"], [\"ned\", \"pon\", \"uto\", \"sre\", \"čet\", \"pet\", \"sub\"], [\"nedelja\", \"ponedeljak\", \"utorak\", \"sreda\", \"četvrtak\", \"petak\", \"subota\"], [\"ne\", \"po\", \"ut\", \"sr\", \"če\", \"pe\", \"su\"]], u, [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mart\", \"apr\", \"maj\", \"jun\", \"jul\", \"avg\", \"sept\", \"okt\", \"nov\", \"dec\"], [\"januar\", \"februar\", \"mart\", \"april\", \"maj\", \"jun\", \"jul\", \"avgust\", \"septembar\", \"oktobar\", \"novembar\", \"decembar\"]], u, [[\"p.n.e.\", \"n.e.\"], [\"p. n. e.\", \"n. e.\"], [\"pre nove ere\", \"nove ere\"]], 1, [6, 0], [\"d.M.yy.\", \"d. M. y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Evro\", {\n  \"AUD\": [u, \"$\"],\n  \"BAM\": [\"KM\"],\n  \"BYN\": [u, \"r.\"],\n  \"GEL\": [u, \"ლ\"],\n  \"KRW\": [u, \"₩\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"VND\": [u, \"₫\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/sr-Latn-XK.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"sr-Latn-XK\", [[\"AM\", \"PM\"], u, u], [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"n\", \"p\", \"u\", \"s\", \"č\", \"p\", \"s\"], [\"ned\", \"pon\", \"uto\", \"sre\", \"čet\", \"pet\", \"sub\"], [\"nedelja\", \"ponedeljak\", \"utorak\", \"sreda\", \"četvrtak\", \"petak\", \"subota\"], [\"ne\", \"po\", \"ut\", \"sr\", \"če\", \"pe\", \"su\"]], u, [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mart\", \"apr\", \"maj\", \"jun\", \"jul\", \"avg\", \"sept\", \"okt\", \"nov\", \"dec\"], [\"januar\", \"februar\", \"mart\", \"april\", \"maj\", \"jun\", \"jul\", \"avgust\", \"septembar\", \"oktobar\", \"novembar\", \"decembar\"]], u, [[\"p.n.e.\", \"n.e.\"], [\"p. n. e.\", \"n. e.\"], [\"pre nove ere\", \"nove ere\"]], 1, [6, 0], [\"d.M.yy.\", \"d. M. y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Evro\", { \"AUD\": [u, \"$\"], \"BAM\": [\"KM\"], \"BYN\": [u, \"r.\"], \"GEL\": [u, \"ლ\"], \"KRW\": [u, \"₩\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"], \"VND\": [u, \"₫\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}