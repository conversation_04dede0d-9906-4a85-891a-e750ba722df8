{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\lookup-service\\lib\\proxy\\bdo\\ess\\lookup-service\\corporate-entity\\models.ts"], "sourcesContent": ["import type { EntityDto } from '@abp/ng.core';\r\n\r\nexport interface CorporateEntityStatusDto extends EntityDto<string> {\r\n  name?: string;\r\n}\r\n\r\nexport interface CorporateEntityTypeDto extends EntityDto<string> {\r\n  name?: string;\r\n}\r\n\r\nexport interface StockExchangeDto extends EntityDto<string> {\r\n  name?: string;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}