{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '@shared/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'home',\n  pathMatch: 'full'\n}, {\n  path: 'home',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/home/<USER>').then(m => m.HomeModule)\n}, {\n  path: 'account',\n  loadChildren: () => import('./abp-modules/account/public/src/account-public.module').then(m => m.AccountPublicModule.forLazy())\n}, {\n  path: 'identity',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./identity/identity.module').then(m => m.IdentityModule.forLazy())\n}, {\n  path: 'language-management',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('@volo/abp.ng.language-management').then(m => m.LanguageManagementModule.forLazy())\n}, {\n  path: 'saas',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('@volo/abp.ng.saas').then(m => m.SaasModule.forLazy())\n}, {\n  path: 'audit-logs',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('@volo/abp.ng.audit-logging').then(m => m.AuditLoggingModule.forLazy())\n}, {\n  path: 'openiddict',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('@volo/abp.ng.openiddictpro').then(m => m.OpeniddictproModule.forLazy())\n}, {\n  path: 'text-template-management',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('@volo/abp.ng.text-template-management').then(m => m.TextTemplateManagementModule.forLazy())\n}, {\n  path: 'setting-management',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('@abp/ng.setting-management').then(m => m.SettingManagementModule.forLazy())\n}, {\n  path: 'es-declaration',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/es-declaration/es-declaration.module').then(m => m.EsDeclarationModule)\n}, {\n  path: 'declaration-templates',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/declaration-templates/declaration-templates.module').then(m => m.DeclarationTemplatesModule)\n}, {\n  path: 'es-import',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/declaration-import/declaration-import.module').then(m => m.DeclarationImportModule)\n}, {\n  path: 'compliance-email',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/compliance-notification-emails/compliance-notification-emails.module').then(m => m.ComplianceNotificationModule)\n}, {\n  path: 'es-info-exchange',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/information-exchange/information-exchange.module').then(m => m.InformationExchangeModule)\n}, {\n  path: 'search-result',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/search-result/search-result.module').then(m => m.SearchResultModule)\n}, {\n  path: 'advanced-search',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/advanced-search/advanced-search.module').then(m => m.AdvancedSearchModule)\n}, {\n  path: 'logout',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./shared/components/logout/logout.module').then(m => m.LogoutModule)\n}, {\n  path: 'resource-page',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/resource-page/resource-page.module').then(m => m.ResourcePageModule)\n}, {\n  path: 'action-page',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/ca-action-page/ca-action-page.module').then(m => m.CaActionPageModule)\n}, {\n  path: 'dashboard',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)\n}, {\n  path: 'redflags',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/redflags/redflags.module').then(m => m.RedflagsModule)\n}, {\n  path: 'audit-trail',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/audit/audit-trail.module').then(m => m.AuditTrailModule)\n}, {\n  path: 'es-assessment-list',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./features/es-assesment-list/es-assesment-list.module').then(m => m.EsAssesmentListModule)\n}, {\n  path: '**',\n  loadChildren: () => import('./features/errors/errors.module').then(m => m.ErrorsModule),\n  data: {\n    error: 404\n  }\n}];\nconst extraOptions = {\n  'enableTracing': true\n};\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "canActivate", "loadChildren", "then", "m", "HomeModule", "AccountPublicModule", "forLazy", "IdentityModule", "LanguageManagementModule", "SaasModule", "AuditLoggingModule", "OpeniddictproModule", "TextTemplateManagementModule", "SettingManagementModule", "EsDeclarationModule", "DeclarationTemplatesModule", "DeclarationImportModule", "ComplianceNotificationModule", "InformationExchangeModule", "SearchResultModule", "AdvancedSearchModule", "LogoutModule", "ResourcePageModule", "CaActionPageModule", "DashboardModule", "RedflagsModule", "AuditTrailModule", "EsAssesmentListModule", "ErrorsModule", "data", "error", "extraOptions", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\app-routing.module.ts"], "sourcesContent": ["\r\nimport { NgModule } from '@angular/core';\r\nimport { ExtraOptions, RouterModule, Routes } from '@angular/router';\r\n\r\nimport { AuthGuard } from '@shared/guards/auth.guard';\r\n\r\n\r\n\r\nconst routes: Routes = [\r\n\r\n  { path: '', redirectTo: 'home', pathMatch: 'full' },\r\n\r\n  { path: 'home', canActivate: [AuthGuard], loadChildren: () => import('./features/home/<USER>').then(m => m.HomeModule) },\r\n\r\n  { path: 'account', loadChildren: () => import('./abp-modules/account/public/src/account-public.module').then(m => m.AccountPublicModule.forLazy()) },\r\n  { path: 'identity', canActivate: [AuthGuard], loadChildren: () => import('./identity/identity.module').then(m => m.IdentityModule.forLazy()) },\r\n  { path: 'language-management', canActivate: [AuthGuard], loadChildren: () => import('@volo/abp.ng.language-management').then(m => m.LanguageManagementModule.forLazy()) },\r\n  { path: 'saas', canActivate: [AuthGuard], loadChildren: () => import('@volo/abp.ng.saas').then(m => m.SaasModule.forLazy()) },\r\n  { path: 'audit-logs', canActivate: [AuthGuard], loadChildren: () => import('@volo/abp.ng.audit-logging').then(m => m.AuditLoggingModule.forLazy()) },\r\n  { path: 'openiddict', canActivate: [AuthGuard], loadChildren: () => import('@volo/abp.ng.openiddictpro').then(m => m.OpeniddictproModule.forLazy()) },\r\n  { path: 'text-template-management', canActivate: [AuthGuard], loadChildren: () => import('@volo/abp.ng.text-template-management').then(m => m.TextTemplateManagementModule.forLazy()) },\r\n  { path: 'setting-management', canActivate: [AuthGuard], loadChildren: () => import('@abp/ng.setting-management').then(m => m.SettingManagementModule.forLazy()) },\r\n  { path: 'es-declaration', canActivate: [AuthGuard], loadChildren: () => import('./features/es-declaration/es-declaration.module').then(m => m.EsDeclarationModule) },\r\n  { path: 'declaration-templates', canActivate: [AuthGuard], loadChildren: () => import('./features/declaration-templates/declaration-templates.module').then(m => m.DeclarationTemplatesModule) },\r\n  { path: 'es-import', canActivate: [AuthGuard], loadChildren: () => import('./features/declaration-import/declaration-import.module').then(m => m.DeclarationImportModule) },\r\n  { path: 'compliance-email', canActivate: [AuthGuard], loadChildren: () => import('./features/compliance-notification-emails/compliance-notification-emails.module').then(m => m.ComplianceNotificationModule) },\r\n\r\n  { path: 'es-info-exchange', canActivate: [AuthGuard], loadChildren: () => import('./features/information-exchange/information-exchange.module').then(m => m.InformationExchangeModule) },\r\n\r\n  { path: 'search-result', canActivate: [AuthGuard], loadChildren: () => import('./features/search-result/search-result.module').then(m => m.SearchResultModule) },\r\n  { path: 'advanced-search', canActivate: [AuthGuard], loadChildren: () => import('./features/advanced-search/advanced-search.module').then(m => m.AdvancedSearchModule) },\r\n  { path: 'logout', canActivate: [AuthGuard], loadChildren: () => import('./shared/components/logout/logout.module').then(m => m.LogoutModule) },\r\n  { path: 'resource-page', canActivate: [AuthGuard], loadChildren: () => import('./features/resource-page/resource-page.module').then(m => m.ResourcePageModule) },\r\n  { path: 'action-page', canActivate: [AuthGuard], loadChildren: () => import('./features/ca-action-page/ca-action-page.module').then(m => m.CaActionPageModule) },\r\n  { path: 'dashboard', canActivate: [AuthGuard], loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule) },\r\n  { path: 'redflags', canActivate: [AuthGuard], loadChildren: () => import('./features/redflags/redflags.module').then(m => m.RedflagsModule) },\r\n  { path: 'audit-trail', canActivate: [AuthGuard], loadChildren: () => import('./features/audit/audit-trail.module').then(m => m.AuditTrailModule) },\r\n  { path: 'es-assessment-list', canActivate: [AuthGuard], loadChildren: () => import('./features/es-assesment-list/es-assesment-list.module').then(m => m.EsAssesmentListModule) },\r\n  { path: '**', loadChildren: () => import('./features/errors/errors.module').then(m => m.ErrorsModule), data: { error: 404 } }\r\n];\r\n\r\nconst extraOptions: ExtraOptions = {\r\n  'enableTracing': true\r\n};\r\n\r\n\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AAEA,SAAuBA,YAAY,QAAgB,iBAAiB;AAEpE,SAASC,SAAS,QAAQ,2BAA2B;;;AAIrD,MAAMC,MAAM,GAAW,CAErB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,MAAM;EAAEC,SAAS,EAAE;AAAM,CAAE,EAEnD;EAAEF,IAAI,EAAE,MAAM;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;AAAC,CAAE,EAE7H;EAAEP,IAAI,EAAE,SAAS;EAAEI,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wDAAwD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,mBAAmB,CAACC,OAAO,EAAE;AAAC,CAAE,EACpJ;EAAET,IAAI,EAAE,UAAU;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,cAAc,CAACD,OAAO,EAAE;AAAC,CAAE,EAC9I;EAAET,IAAI,EAAE,qBAAqB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,wBAAwB,CAACF,OAAO,EAAE;AAAC,CAAE,EACzK;EAAET,IAAI,EAAE,MAAM;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,UAAU,CAACH,OAAO,EAAE;AAAC,CAAE,EAC7H;EAAET,IAAI,EAAE,YAAY;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,kBAAkB,CAACJ,OAAO,EAAE;AAAC,CAAE,EACpJ;EAAET,IAAI,EAAE,YAAY;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,mBAAmB,CAACL,OAAO,EAAE;AAAC,CAAE,EACrJ;EAAET,IAAI,EAAE,0BAA0B;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,4BAA4B,CAACN,OAAO,EAAE;AAAC,CAAE,EACvL;EAAET,IAAI,EAAE,oBAAoB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,uBAAuB,CAACP,OAAO,EAAE;AAAC,CAAE,EACjK;EAAET,IAAI,EAAE,gBAAgB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iDAAiD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,mBAAmB;AAAC,CAAE,EACpK;EAAEjB,IAAI,EAAE,uBAAuB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+DAA+D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,0BAA0B;AAAC,CAAE,EAChM;EAAElB,IAAI,EAAE,WAAW;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,yDAAyD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,uBAAuB;AAAC,CAAE,EAC3K;EAAEnB,IAAI,EAAE,kBAAkB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iFAAiF,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,4BAA4B;AAAC,CAAE,EAE/M;EAAEpB,IAAI,EAAE,kBAAkB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6DAA6D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,yBAAyB;AAAC,CAAE,EAExL;EAAErB,IAAI,EAAE,eAAe;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACgB,kBAAkB;AAAC,CAAE,EAChK;EAAEtB,IAAI,EAAE,iBAAiB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACiB,oBAAoB;AAAC,CAAE,EACxK;EAAEvB,IAAI,EAAE,QAAQ;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACkB,YAAY;AAAC,CAAE,EAC9I;EAAExB,IAAI,EAAE,eAAe;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACmB,kBAAkB;AAAC,CAAE,EAChK;EAAEzB,IAAI,EAAE,aAAa;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iDAAiD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACoB,kBAAkB;AAAC,CAAE,EAChK;EAAE1B,IAAI,EAAE,WAAW;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACqB,eAAe;AAAC,CAAE,EACjJ;EAAE3B,IAAI,EAAE,UAAU;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACsB,cAAc;AAAC,CAAE,EAC7I;EAAE5B,IAAI,EAAE,aAAa;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACuB,gBAAgB;AAAC,CAAE,EAClJ;EAAE7B,IAAI,EAAE,oBAAoB;EAAEG,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uDAAuD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACwB,qBAAqB;AAAC,CAAE,EAChL;EAAE9B,IAAI,EAAE,IAAI;EAAEI,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACyB,YAAY,CAAC;EAAEC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAG;AAAE,CAAE,CAC9H;AAED,MAAMC,YAAY,GAAiB;EACjC,eAAe,EAAE;CAClB;AAQD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBtC,YAAY,CAACuC,OAAO,CAACrC,MAAM,CAAC,EAC5BF,YAAY;IAAA;EAAA;;;2EAEXsC,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAzC,YAAA;IAAA0C,OAAA,GAFjB1C,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}