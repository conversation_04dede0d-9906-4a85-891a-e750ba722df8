{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = toDate;\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\nvar _index = _interopRequireDefault(require(\"../_lib/requiredArgs/index.js\"));\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @param {Date|Number} argument - the value to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nfunction toDate(argument) {\n  (0, _index.default)(1, arguments);\n  var argStr = Object.prototype.toString.call(argument);\n\n  // Clone the date\n  if (argument instanceof Date || (0, _typeof2.default)(argument) === 'object' && argStr === '[object Date]') {\n    // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n    return new Date(argument.getTime());\n  } else if (typeof argument === 'number' || argStr === '[object Number]') {\n    return new Date(argument);\n  } else {\n    if ((typeof argument === 'string' || argStr === '[object String]') && typeof console !== 'undefined') {\n      // eslint-disable-next-line no-console\n      console.warn(\"Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments\");\n      // eslint-disable-next-line no-console\n      console.warn(new Error().stack);\n    }\n    return new Date(NaN);\n  }\n}\nmodule.exports = exports.default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "toDate", "_typeof2", "_index", "argument", "arguments", "argStr", "prototype", "toString", "call", "Date", "getTime", "console", "warn", "Error", "stack", "NaN", "module"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/toDate/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = toDate;\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\nvar _index = _interopRequireDefault(require(\"../_lib/requiredArgs/index.js\"));\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @param {Date|Number} argument - the value to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nfunction toDate(argument) {\n  (0, _index.default)(1, arguments);\n  var argStr = Object.prototype.toString.call(argument);\n\n  // Clone the date\n  if (argument instanceof Date || (0, _typeof2.default)(argument) === 'object' && argStr === '[object Date]') {\n    // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n    return new Date(argument.getTime());\n  } else if (typeof argument === 'number' || argStr === '[object Number]') {\n    return new Date(argument);\n  } else {\n    if ((typeof argument === 'string' || argStr === '[object String]') && typeof console !== 'undefined') {\n      // eslint-disable-next-line no-console\n      console.warn(\"Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments\");\n      // eslint-disable-next-line no-console\n      console.warn(new Error().stack);\n    }\n    return new Date(NaN);\n  }\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,MAAM;AACxB,IAAIC,QAAQ,GAAGR,sBAAsB,CAACC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC/E,IAAIQ,MAAM,GAAGT,sBAAsB,CAACC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,MAAMA,CAACG,QAAQ,EAAE;EACxB,CAAC,CAAC,EAAED,MAAM,CAACP,OAAO,EAAE,CAAC,EAAES,SAAS,CAAC;EACjC,IAAIC,MAAM,GAAGT,MAAM,CAACU,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,QAAQ,CAAC;;EAErD;EACA,IAAIA,QAAQ,YAAYM,IAAI,IAAI,CAAC,CAAC,EAAER,QAAQ,CAACN,OAAO,EAAEQ,QAAQ,CAAC,KAAK,QAAQ,IAAIE,MAAM,KAAK,eAAe,EAAE;IAC1G;IACA,OAAO,IAAII,IAAI,CAACN,QAAQ,CAACO,OAAO,CAAC,CAAC,CAAC;EACrC,CAAC,MAAM,IAAI,OAAOP,QAAQ,KAAK,QAAQ,IAAIE,MAAM,KAAK,iBAAiB,EAAE;IACvE,OAAO,IAAII,IAAI,CAACN,QAAQ,CAAC;EAC3B,CAAC,MAAM;IACL,IAAI,CAAC,OAAOA,QAAQ,KAAK,QAAQ,IAAIE,MAAM,KAAK,iBAAiB,KAAK,OAAOM,OAAO,KAAK,WAAW,EAAE;MACpG;MACAA,OAAO,CAACC,IAAI,CAAC,oNAAoN,CAAC;MAClO;MACAD,OAAO,CAACC,IAAI,CAAC,IAAIC,KAAK,CAAC,CAAC,CAACC,KAAK,CAAC;IACjC;IACA,OAAO,IAAIL,IAAI,CAACM,GAAG,CAAC;EACtB;AACF;AACAC,MAAM,CAAClB,OAAO,GAAGA,OAAO,CAACH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}