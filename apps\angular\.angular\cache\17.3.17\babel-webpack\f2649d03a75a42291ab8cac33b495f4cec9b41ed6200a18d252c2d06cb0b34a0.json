{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"lo\", [[\"ກ່ອນທ່ຽງ\", \"ຫຼັງທ່ຽງ\"], u, u], u, [[\"ອາ\", \"ຈ\", \"ອ\", \"ພ\", \"ພຫ\", \"ສຸ\", \"ສ\"], [\"ອາທິດ\", \"ຈັນ\", \"ອັງຄານ\", \"ພຸດ\", \"ພະຫັດ\", \"ສຸກ\", \"ເສົາ\"], [\"ວັນອາທິດ\", \"ວັນຈັນ\", \"ວັນອັງຄານ\", \"ວັນພຸດ\", \"ວັນພະຫັດ\", \"ວັນສຸກ\", \"ວັນເສົາ\"], [\"ອາ.\", \"ຈ.\", \"ອ.\", \"ພ.\", \"ພຫ.\", \"ສຸ.\", \"ສ.\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ມ.ກ.\", \"ກ.ພ.\", \"ມ.ນ.\", \"ມ.ສ.\", \"ພ.ພ.\", \"ມິ.ຖ.\", \"ກ.ລ.\", \"ສ.ຫ.\", \"ກ.ຍ.\", \"ຕ.ລ.\", \"ພ.ຈ.\", \"ທ.ວ.\"], [\"ມັງກອນ\", \"ກຸມພາ\", \"ມີນາ\", \"ເມສາ\", \"ພຶດສະພາ\", \"ມິຖຸນາ\", \"ກໍລະກົດ\", \"ສິງຫາ\", \"ກັນຍາ\", \"ຕຸລາ\", \"ພະຈິກ\", \"ທັນວາ\"]], u, [[\"ກ່ອນ ຄ.ສ.\", \"ຄ.ສ.\"], u, [\"ກ່ອນຄຣິດສັກກະລາດ\", \"ຄຣິດສັກກະລາດ\"]], 0, [6, 0], [\"d/M/y\", \"d MMM y\", \"d MMMM y\", \"EEEE ທີ d MMMM G y\"], [\"H:mm\", \"H:mm:ss\", \"H ໂມງ m ນາທີ ss ວິນາທີ z\", \"H ໂມງ m ນາທີ ss ວິນາທີ zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"ບໍ່​ແມ່ນ​ໂຕ​ເລກ\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00;¤-#,##0.00\", \"#\"], \"LAK\", \"₭\", \"ລາວ ກີບ\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"LAK\": [\"₭\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}