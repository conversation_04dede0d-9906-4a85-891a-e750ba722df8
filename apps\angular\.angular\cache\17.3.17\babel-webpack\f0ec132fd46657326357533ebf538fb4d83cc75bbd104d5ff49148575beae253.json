{"ast": null, "code": "import { SurveyCreatorModel } from \"survey-creator-core\";\nimport \"survey-core/survey.i18n.js\";\nimport \"survey-creator-core/survey-creator-core.i18n.js\";\nimport { AppComponentBase } from '@app/app-component-base';\nimport * as SurveyCore from \"survey-core\";\nimport * as widgets from \"surveyjs-widgets\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../service/declaration-template-service.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates\";\nimport * as i4 from \"@app/shared/services/composite-question.service\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"survey-creator-angular\";\nwidgets.inputmask(SurveyCore);\nexport let DeclarationEditorComponent = /*#__PURE__*/(() => {\n  class DeclarationEditorComponent extends AppComponentBase {\n    constructor(injector, DeclarationTemplateservice, router, TemplateService, compositeQuestionService) {\n      super(injector);\n      this.DeclarationTemplateservice = DeclarationTemplateservice;\n      this.router = router;\n      this.TemplateService = TemplateService;\n      this.compositeQuestionService = compositeQuestionService;\n      this.id = this.DeclarationTemplateservice.getId();\n      this.json = '';\n      this.options = {\n        showLogicTab: true\n      };\n      this.creator = new SurveyCreatorModel(this.options);\n      this.model = this.creator;\n      //this.compositeQuestionService.setCompositeQuestions();\n    }\n    ngOnInit() {\n      this.editorSetup();\n    }\n    editorSetup() {\n      this.TemplateService.get(this.id).subscribe(response => {\n        this.json = JSON.stringify(response.survey, (key, value) => {\n          if (value !== null) return value;\n        });\n        this.creator.JSON = JSON.parse(this.json);\n        this.creator.text = this.json;\n        this.model = this.creator;\n        this.currentTemplate = response;\n        this.compositeQuestionService.setCompositeQuestions(this.currentTemplate);\n      });\n    }\n    saveSurvey() {\n      const survey = this.creator.JSON;\n      const template = {\n        name: this.currentTemplate.name,\n        description: this.currentTemplate.description,\n        effectiveDate: this.currentTemplate.effectiveDate,\n        expiryDate: this.currentTemplate.expiryDate,\n        survey: survey,\n        isActive: this.currentTemplate.isActive\n      };\n      this.TemplateService.update(this.id, template).subscribe();\n    }\n    closeEditor() {\n      // Add save logic before navigating back to the templates list \n      this.saveSurvey();\n      this.router.navigateByUrl('declaration-templates');\n    }\n    static {\n      this.ɵfac = function DeclarationEditorComponent_Factory(t) {\n        return new (t || DeclarationEditorComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.DeclarationTemplateServiceService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i4.CompositeQuestionService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DeclarationEditorComponent,\n        selectors: [[\"app-declaration-editor\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 4,\n        vars: 1,\n        consts: [[\"mat-raised-button\", \"\", 1, \"button-margin\", 3, \"click\"], [2, \"height\", \"100vh\"], [3, \"model\"]],\n        template: function DeclarationEditorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"button\", 0);\n            i0.ɵɵlistener(\"click\", function DeclarationEditorComponent_Template_button_click_0_listener() {\n              return ctx.closeEditor();\n            });\n            i0.ɵɵtext(1, \"Save and Close Editor\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"div\", 1);\n            i0.ɵɵelement(3, \"survey-creator\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.model);\n          }\n        },\n        dependencies: [i5.MatButton, i6.CreatorComponent]\n      });\n    }\n  }\n  return DeclarationEditorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}