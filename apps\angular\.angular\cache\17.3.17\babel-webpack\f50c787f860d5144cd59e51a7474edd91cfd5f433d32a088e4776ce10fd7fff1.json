{"ast": null, "code": "import { ConfigStateService } from '@abp/ng.core';\nimport { ExtensionsService, getObjectExtensionEntitiesFromStore, mapEntitiesToContributors, mergeWithDefaultActions, mergeWithDefaultProps } from '@abp/ng.components/extensible';\nimport { map, mapTo, tap } from 'rxjs/operators';\nimport { DEFAULT_IDENTITY_CREATE_FORM_PROPS, DEFAULT_IDENTITY_EDIT_FORM_PROPS, DEFAULT_IDENTITY_ENTITY_ACTIONS, DEFAULT_IDENTITY_ENTITY_PROPS, DEFAULT_IDENTITY_TOOLBAR_ACTIONS, IDENTITY_CREATE_FORM_PROP_CONTRIBUTORS, IDENTITY_EDIT_FORM_PROP_CONTRIBUTORS, IDENTITY_ENTITY_ACTION_CONTRIBUTORS, IDENTITY_ENTITY_PROP_CONTRIBUTORS, IDENTITY_TOOLBAR_ACTION_CONTRIBUTORS } from '../tokens/extensions.token';\nimport * as i0 from \"@angular/core\";\nexport class IdentityExtensionsGuard {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  canActivate() {\n    const extensions = this.injector.get(ExtensionsService);\n    const actionContributors = this.injector.get(IDENTITY_ENTITY_ACTION_CONTRIBUTORS, null) || {};\n    const toolbarContributors = this.injector.get(IDENTITY_TOOLBAR_ACTION_CONTRIBUTORS, null) || {};\n    const propContributors = this.injector.get(IDENTITY_ENTITY_PROP_CONTRIBUTORS, null) || {};\n    const createFormContributors = this.injector.get(IDENTITY_CREATE_FORM_PROP_CONTRIBUTORS, null) || {};\n    const editFormContributors = this.injector.get(IDENTITY_EDIT_FORM_PROP_CONTRIBUTORS, null) || {};\n    const configState = this.injector.get(ConfigStateService);\n    return getObjectExtensionEntitiesFromStore(configState, 'Identity').pipe(map(entities => ({\n      [\"Identity.ClaimsComponent\" /* eIdentityComponents.Claims */]: entities.ClaimType,\n      [\"Identity.RolesComponent\" /* eIdentityComponents.Roles */]: entities.Role,\n      [\"Identity.UsersComponent\" /* eIdentityComponents.Users */]: entities.User,\n      [\"Identity.OrganizationUnitsComponent\" /* eIdentityComponents.OrganizationUnits */]: entities.OrganizationUnit\n    })), mapEntitiesToContributors(configState, 'AbpIdentity'), tap(objectExtensionContributors => {\n      mergeWithDefaultActions(extensions.entityActions, DEFAULT_IDENTITY_ENTITY_ACTIONS, actionContributors);\n      mergeWithDefaultActions(extensions.toolbarActions, DEFAULT_IDENTITY_TOOLBAR_ACTIONS, toolbarContributors);\n      mergeWithDefaultProps(extensions.entityProps, DEFAULT_IDENTITY_ENTITY_PROPS, objectExtensionContributors.prop, propContributors);\n      mergeWithDefaultProps(extensions.createFormProps, DEFAULT_IDENTITY_CREATE_FORM_PROPS, objectExtensionContributors.createForm, createFormContributors);\n      mergeWithDefaultProps(extensions.editFormProps, DEFAULT_IDENTITY_EDIT_FORM_PROPS, objectExtensionContributors.editForm, editFormContributors);\n    }), mapTo(true));\n  }\n  static {\n    this.ɵfac = function IdentityExtensionsGuard_Factory(t) {\n      return new (t || IdentityExtensionsGuard)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IdentityExtensionsGuard,\n      factory: IdentityExtensionsGuard.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["ConfigStateService", "ExtensionsService", "getObjectExtensionEntitiesFromStore", "mapEntitiesToContributors", "mergeWithDefaultActions", "mergeWithDefaultProps", "map", "mapTo", "tap", "DEFAULT_IDENTITY_CREATE_FORM_PROPS", "DEFAULT_IDENTITY_EDIT_FORM_PROPS", "DEFAULT_IDENTITY_ENTITY_ACTIONS", "DEFAULT_IDENTITY_ENTITY_PROPS", "DEFAULT_IDENTITY_TOOLBAR_ACTIONS", "IDENTITY_CREATE_FORM_PROP_CONTRIBUTORS", "IDENTITY_EDIT_FORM_PROP_CONTRIBUTORS", "IDENTITY_ENTITY_ACTION_CONTRIBUTORS", "IDENTITY_ENTITY_PROP_CONTRIBUTORS", "IDENTITY_TOOLBAR_ACTION_CONTRIBUTORS", "IdentityExtensionsGuard", "constructor", "injector", "canActivate", "extensions", "get", "actionContributors", "toolbarContributors", "propContributors", "createFormContributors", "editFormContributors", "configState", "pipe", "entities", "ClaimType", "Role", "User", "OrganizationUnit", "objectExtensionContributors", "entityActions", "toolbarActions", "entityProps", "prop", "createFormProps", "createForm", "editFormProps", "editForm", "i0", "ɵɵinject", "Injector", "factory", "ɵfac"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\guards\\extensions.guard.ts"], "sourcesContent": ["import { ConfigStateService } from '@abp/ng.core';\r\nimport {\r\n  ExtensionsService,\r\n  getObjectExtensionEntitiesFromStore,\r\n  mapEntitiesToContributors,\r\n  mergeWithDefaultActions,\r\n  mergeWithDefaultProps,\r\n} from '@abp/ng.components/extensible';\r\nimport { Injectable, Injector } from '@angular/core';\r\nimport { CanActivate } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { map, mapTo, tap } from 'rxjs/operators';\r\nimport { eIdentityComponents } from '../enums/components';\r\nimport {\r\n  IdentityCreateFormPropContributors,\r\n  IdentityEditFormPropContributors,\r\n  IdentityEntityActionContributors,\r\n  IdentityEntityPropContributors,\r\n  IdentityToolbarActionContributors,\r\n} from '../models/config-options';\r\nimport {\r\n  DEFAULT_IDENTITY_CREATE_FORM_PROPS,\r\n  DEFAULT_IDENTITY_EDIT_FORM_PROPS,\r\n  DEFAULT_IDENTITY_ENTITY_ACTIONS,\r\n  DEFAULT_IDENTITY_ENTITY_PROPS,\r\n  DEFAULT_IDENTITY_TOOLBAR_ACTIONS,\r\n  IDENTITY_CREATE_FORM_PROP_CONTRIBUTORS,\r\n  IDENTITY_EDIT_FORM_PROP_CONTRIBUTORS,\r\n  IDENTITY_ENTITY_ACTION_CONTRIBUTORS,\r\n  IDENTITY_ENTITY_PROP_CONTRIBUTORS,\r\n  IDENTITY_TOOLBAR_ACTION_CONTRIBUTORS,\r\n} from '../tokens/extensions.token';\r\n\r\n@Injectable()\r\nexport class IdentityExtensionsGuard implements CanActivate {\r\n  constructor(private injector: Injector) {}\r\n\r\n  canActivate(): Observable<boolean> {\r\n    const extensions: ExtensionsService = this.injector.get(ExtensionsService);\r\n    const actionContributors: IdentityEntityActionContributors =\r\n      this.injector.get(IDENTITY_ENTITY_ACTION_CONTRIBUTORS, null) || {};\r\n    const toolbarContributors: IdentityToolbarActionContributors =\r\n      this.injector.get(IDENTITY_TOOLBAR_ACTION_CONTRIBUTORS, null) || {};\r\n    const propContributors: IdentityEntityPropContributors =\r\n      this.injector.get(IDENTITY_ENTITY_PROP_CONTRIBUTORS, null) || {};\r\n    const createFormContributors: IdentityCreateFormPropContributors =\r\n      this.injector.get(IDENTITY_CREATE_FORM_PROP_CONTRIBUTORS, null) || {};\r\n    const editFormContributors: IdentityEditFormPropContributors =\r\n      this.injector.get(IDENTITY_EDIT_FORM_PROP_CONTRIBUTORS, null) || {};\r\n\r\n    const configState = this.injector.get(ConfigStateService);\r\n    return getObjectExtensionEntitiesFromStore(configState, 'Identity').pipe(\r\n      map(entities => ({\r\n        [eIdentityComponents.Claims]: entities.ClaimType,\r\n        [eIdentityComponents.Roles]: entities.Role,\r\n        [eIdentityComponents.Users]: entities.User,\r\n        [eIdentityComponents.OrganizationUnits]: entities.OrganizationUnit,\r\n      })),\r\n      mapEntitiesToContributors(configState, 'AbpIdentity'),\r\n      tap(objectExtensionContributors => {\r\n        mergeWithDefaultActions(\r\n          extensions.entityActions,\r\n          DEFAULT_IDENTITY_ENTITY_ACTIONS,\r\n          actionContributors,\r\n        );\r\n        mergeWithDefaultActions(\r\n          extensions.toolbarActions,\r\n          DEFAULT_IDENTITY_TOOLBAR_ACTIONS,\r\n          toolbarContributors,\r\n        );\r\n        mergeWithDefaultProps(\r\n          extensions.entityProps,\r\n          DEFAULT_IDENTITY_ENTITY_PROPS,\r\n          objectExtensionContributors.prop,\r\n          propContributors,\r\n        );\r\n        mergeWithDefaultProps(\r\n          extensions.createFormProps,\r\n          DEFAULT_IDENTITY_CREATE_FORM_PROPS,\r\n          objectExtensionContributors.createForm,\r\n          createFormContributors,\r\n        );\r\n        mergeWithDefaultProps(\r\n          extensions.editFormProps,\r\n          DEFAULT_IDENTITY_EDIT_FORM_PROPS,\r\n          objectExtensionContributors.editForm,\r\n          editFormContributors,\r\n        );\r\n      }),\r\n      mapTo(true),\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SACEC,iBAAiB,EACjBC,mCAAmC,EACnCC,yBAAyB,EACzBC,uBAAuB,EACvBC,qBAAqB,QAChB,+BAA+B;AAItC,SAASC,GAAG,EAAEC,KAAK,EAAEC,GAAG,QAAQ,gBAAgB;AAShD,SACEC,kCAAkC,EAClCC,gCAAgC,EAChCC,+BAA+B,EAC/BC,6BAA6B,EAC7BC,gCAAgC,EAChCC,sCAAsC,EACtCC,oCAAoC,EACpCC,mCAAmC,EACnCC,iCAAiC,EACjCC,oCAAoC,QAC/B,4BAA4B;;AAGnC,OAAM,MAAOC,uBAAuB;EAClCC,YAAoBC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;EAAa;EAEzCC,WAAWA,CAAA;IACT,MAAMC,UAAU,GAAsB,IAAI,CAACF,QAAQ,CAACG,GAAG,CAACvB,iBAAiB,CAAC;IAC1E,MAAMwB,kBAAkB,GACtB,IAAI,CAACJ,QAAQ,CAACG,GAAG,CAACR,mCAAmC,EAAE,IAAI,CAAC,IAAI,EAAE;IACpE,MAAMU,mBAAmB,GACvB,IAAI,CAACL,QAAQ,CAACG,GAAG,CAACN,oCAAoC,EAAE,IAAI,CAAC,IAAI,EAAE;IACrE,MAAMS,gBAAgB,GACpB,IAAI,CAACN,QAAQ,CAACG,GAAG,CAACP,iCAAiC,EAAE,IAAI,CAAC,IAAI,EAAE;IAClE,MAAMW,sBAAsB,GAC1B,IAAI,CAACP,QAAQ,CAACG,GAAG,CAACV,sCAAsC,EAAE,IAAI,CAAC,IAAI,EAAE;IACvE,MAAMe,oBAAoB,GACxB,IAAI,CAACR,QAAQ,CAACG,GAAG,CAACT,oCAAoC,EAAE,IAAI,CAAC,IAAI,EAAE;IAErE,MAAMe,WAAW,GAAG,IAAI,CAACT,QAAQ,CAACG,GAAG,CAACxB,kBAAkB,CAAC;IACzD,OAAOE,mCAAmC,CAAC4B,WAAW,EAAE,UAAU,CAAC,CAACC,IAAI,CACtEzB,GAAG,CAAC0B,QAAQ,KAAK;MACf,+DAA8BA,QAAQ,CAACC,SAAS;MAChD,6DAA6BD,QAAQ,CAACE,IAAI;MAC1C,6DAA6BF,QAAQ,CAACG,IAAI;MAC1C,qFAAyCH,QAAQ,CAACI;KACnD,CAAC,CAAC,EACHjC,yBAAyB,CAAC2B,WAAW,EAAE,aAAa,CAAC,EACrDtB,GAAG,CAAC6B,2BAA2B,IAAG;MAChCjC,uBAAuB,CACrBmB,UAAU,CAACe,aAAa,EACxB3B,+BAA+B,EAC/Bc,kBAAkB,CACnB;MACDrB,uBAAuB,CACrBmB,UAAU,CAACgB,cAAc,EACzB1B,gCAAgC,EAChCa,mBAAmB,CACpB;MACDrB,qBAAqB,CACnBkB,UAAU,CAACiB,WAAW,EACtB5B,6BAA6B,EAC7ByB,2BAA2B,CAACI,IAAI,EAChCd,gBAAgB,CACjB;MACDtB,qBAAqB,CACnBkB,UAAU,CAACmB,eAAe,EAC1BjC,kCAAkC,EAClC4B,2BAA2B,CAACM,UAAU,EACtCf,sBAAsB,CACvB;MACDvB,qBAAqB,CACnBkB,UAAU,CAACqB,aAAa,EACxBlC,gCAAgC,EAChC2B,2BAA2B,CAACQ,QAAQ,EACpChB,oBAAoB,CACrB;IACH,CAAC,CAAC,EACFtB,KAAK,CAAC,IAAI,CAAC,CACZ;EACH;;;uBAzDWY,uBAAuB,EAAA2B,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAE,QAAA;IAAA;EAAA;;;aAAvB7B,uBAAuB;MAAA8B,OAAA,EAAvB9B,uBAAuB,CAAA+B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}