{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar adjectivesLastWeek = {\n  masculine: 'ostatni',\n  feminine: 'ostatnia'\n};\nvar adjectivesThisWeek = {\n  masculine: 'ten',\n  feminine: 'ta'\n};\nvar adjectivesNextWeek = {\n  masculine: 'następny',\n  feminine: 'następna'\n};\nvar dayGrammaticalGender = {\n  0: 'feminine',\n  1: 'masculine',\n  2: 'masculine',\n  3: 'feminine',\n  4: 'masculine',\n  5: 'masculine',\n  6: 'feminine'\n};\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n  var adjectives;\n  if (isSameUTCWeek(date, baseDate, options)) {\n    adjectives = adjectivesThisWeek;\n  } else if (token === 'lastWeek') {\n    adjectives = adjectivesLastWeek;\n  } else if (token === 'nextWeek') {\n    adjectives = adjectivesNextWeek;\n  } else {\n    throw new Error(\"Cannot determine adjectives for token \".concat(token));\n  }\n  var day = date.getUTCDay();\n  var grammaticalGender = dayGrammaticalGender[day];\n  var adjective = adjectives[grammaticalGender];\n  return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nvar formatRelativeLocale = {\n  lastWeek: dayAndTimeWithAdjective,\n  yesterday: \"'wczoraj o' p\",\n  today: \"'dzisiaj o' p\",\n  tomorrow: \"'jutro o' p\",\n  nextWeek: dayAndTimeWithAdjective,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(token, date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}