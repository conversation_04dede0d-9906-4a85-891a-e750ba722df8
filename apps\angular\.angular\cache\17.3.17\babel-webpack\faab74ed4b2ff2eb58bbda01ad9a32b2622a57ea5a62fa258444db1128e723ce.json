{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let SearchResultLegendComponent = /*#__PURE__*/(() => {\n  class SearchResultLegendComponent {\n    static {\n      this.ɵfac = function SearchResultLegendComponent_Factory(t) {\n        return new (t || SearchResultLegendComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchResultLegendComponent,\n        selectors: [[\"app-search-result-legend\"]],\n        decls: 13,\n        vars: 0,\n        consts: [[1, \"legend\", \"mat-elevation-z8\"], [1, \"status-not-started\"], [1, \"status-draft\"], [1, \"status-reopened\"], [1, \"status-submitted\"], [1, \"status-resubmitted\"], [1, \"status-deleted\"]],\n        template: function SearchResultLegendComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"ul\", 0)(1, \"li\", 1);\n            i0.ɵɵtext(2, \"Not Started\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"li\", 2);\n            i0.ɵɵtext(4, \"Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"li\", 3);\n            i0.ɵɵtext(6, \"Reopened\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"li\", 4);\n            i0.ɵɵtext(8, \"Submitted\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"li\", 5);\n            i0.ɵɵtext(10, \"Resubmitted\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"li\", 6);\n            i0.ɵɵtext(12, \"Deleted\");\n            i0.ɵɵelementEnd()();\n          }\n        },\n        styles: [\".legend[_ngcontent-%COMP%]{background-color:#fff;min-width:9em;max-width:9em;min-height:13em;display:inline-block;list-style:none;padding-left:1em}.legend[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{font-weight:bolder;margin-top:.25em;margin-bottom:.25em}\"]\n      });\n    }\n  }\n  return SearchResultLegendComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}