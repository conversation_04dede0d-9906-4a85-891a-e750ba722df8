{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'λιγότερο από ένα δευτερόλεπτο',\n    other: 'λιγότερο από {{count}} δευτερόλεπτα'\n  },\n  xSeconds: {\n    one: '1 δευτερόλεπτο',\n    other: '{{count}} δευτερόλεπτα'\n  },\n  halfAMinute: 'μισό λεπτό',\n  lessThanXMinutes: {\n    one: 'λιγότερο από ένα λεπτό',\n    other: 'λιγότερο από {{count}} λεπτά'\n  },\n  xMinutes: {\n    one: '1 λεπτό',\n    other: '{{count}} λεπτά'\n  },\n  aboutXHours: {\n    one: 'περίπου 1 ώρα',\n    other: 'περίπου {{count}} ώρες'\n  },\n  xHours: {\n    one: '1 ώρα',\n    other: '{{count}} ώρες'\n  },\n  xDays: {\n    one: '1 ημέρα',\n    other: '{{count}} ημέρες'\n  },\n  aboutXWeeks: {\n    one: 'περίπου 1 εβδομάδα',\n    other: 'περίπου {{count}} εβδομάδες'\n  },\n  xWeeks: {\n    one: '1 εβδομάδα',\n    other: '{{count}} εβδομάδες'\n  },\n  aboutXMonths: {\n    one: 'περίπου 1 μήνας',\n    other: 'περίπου {{count}} μήνες'\n  },\n  xMonths: {\n    one: '1 μήνας',\n    other: '{{count}} μήνες'\n  },\n  aboutXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  },\n  xYears: {\n    one: '1 χρόνο',\n    other: '{{count}} χρόνια'\n  },\n  overXYears: {\n    one: 'πάνω από 1 χρόνο',\n    other: 'πάνω από {{count}} χρόνια'\n  },\n  almostXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'σε ' + result;\n    } else {\n      return result + ' πριν';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}