{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"ses\", [[\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], u, u], u, [[\"H\", \"T\", \"T\", \"L\", \"L\", \"L\", \"S\"], [\"Alh\", \"Ati\", \"Ata\", \"Ala\", \"Alm\", \"<PERSON>z\", \"<PERSON>i\"], [\"<PERSON><PERSON>i\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], [\"Alh\", \"Ati\", \"Ata\", \"Ala\", \"Alm\", \"Alz\", \"<PERSON>i\"]], u, [[\"Ž\", \"F\", \"M\", \"A\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>u<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>t\", \"<PERSON>o\", \"<PERSON>\"], [\"Žanwiye\", \"<PERSON>ewiriye\", \"<PERSON>i\", \"Awiril\", \"Me\", \"Žuweŋ\", \"Žuyye\", \"Ut\", \"Sektanbur\", \"Oktoobur\", \"Noowanbur\", \"<PERSON>sanbur\"]], u, [[\"I<PERSON>\", \"I<PERSON>\"], u, [\"<PERSON>a jine\", \"<PERSON>a zamanoo\"]], 1, [6, 0], [\"d/M/y\", \"d MMM, y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00¤\", \"#E0\"], \"XOF\", \"F CFA\", \"CFA Fraŋ (BCEAO)\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ses.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"ses\", [[\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], u, u], u, [[\"H\", \"T\", \"T\", \"L\", \"L\", \"L\", \"S\"], [\"Alh\", \"Ati\", \"Ata\", \"Ala\", \"Alm\", \"<PERSON>z\", \"<PERSON>i\"], [\"<PERSON><PERSON>i\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], [\"Alh\", \"Ati\", \"Ata\", \"Ala\", \"Alm\", \"Alz\", \"<PERSON>i\"]], u, [[\"Ž\", \"F\", \"M\", \"A\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>u<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>t\", \"<PERSON>o\", \"<PERSON>\"], [\"Žanwiye\", \"<PERSON>ewiriye\", \"<PERSON>i\", \"Awiril\", \"Me\", \"Žuweŋ\", \"Žuyye\", \"Ut\", \"Sektanbur\", \"Oktoobur\", \"Noowanbur\", \"<PERSON>sanbur\"]], u, [[\"I<PERSON>\", \"I<PERSON>\"], u, [\"<PERSON>a jine\", \"<PERSON>a zamanoo\"]], 1, [6, 0], [\"d/M/y\", \"d MMM, y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00¤\", \"#E0\"], \"XOF\", \"F CFA\", \"CFA Fraŋ (BCEAO)\", { \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}