{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"az-Latn\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"7\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\"], [\"B.\", \"B.e.\", \"Ç.a.\", \"Ç.\", \"C.a.\", \"C.\", \"Ş.\"], [\"bazar\", \"bazar ertəsi\", \"çərşənbə axşamı\", \"çərşənbə\", \"cümə axşamı\", \"cümə\", \"şənbə\"], [\"B.\", \"B.E.\", \"Ç.A.\", \"Ç.\", \"C.A.\", \"C.\", \"Ş.\"]], [[\"7\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\"], [\"B.\", \"B.E.\", \"Ç.A.\", \"Ç.\", \"C.A.\", \"C.\", \"Ş.\"], [\"bazar\", \"bazar ertəsi\", \"çərşənbə axşamı\", \"çərşənbə\", \"cümə axşamı\", \"cümə\", \"şənbə\"], [\"B.\", \"B.E.\", \"Ç.A.\", \"Ç.\", \"C.A.\", \"C.\", \"Ş.\"]], [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"yan\", \"fev\", \"mar\", \"apr\", \"may\", \"iyn\", \"iyl\", \"avq\", \"sen\", \"okt\", \"noy\", \"dek\"], [\"yanvar\", \"fevral\", \"mart\", \"aprel\", \"may\", \"iyun\", \"iyul\", \"avqust\", \"sentyabr\", \"oktyabr\", \"noyabr\", \"dekabr\"]], u, [[\"e.ə.\", \"y.e.\"], u, [\"eramızdan əvvəl\", \"yeni era\"]], 1, [6, 0], [\"dd.MM.yy\", \"d MMM y\", \"d MMMM y\", \"d MMMM y, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"AZN\", \"₼\", \"Azərbaycan Manatı\", {\n  \"AZN\": [\"₼\"],\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"ley\"],\n  \"SYP\": [u, \"S£\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/az-Latn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"az-Latn\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"7\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\"], [\"B.\", \"B.e.\", \"Ç.a.\", \"Ç.\", \"C.a.\", \"C.\", \"Ş.\"], [\"bazar\", \"bazar ertəsi\", \"çərşənbə axşamı\", \"çərşənbə\", \"cümə axşamı\", \"cümə\", \"şənbə\"], [\"B.\", \"B.E.\", \"Ç.A.\", \"Ç.\", \"C.A.\", \"C.\", \"Ş.\"]], [[\"7\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\"], [\"B.\", \"B.E.\", \"Ç.A.\", \"Ç.\", \"C.A.\", \"C.\", \"Ş.\"], [\"bazar\", \"bazar ertəsi\", \"çərşənbə axşamı\", \"çərşənbə\", \"cümə axşamı\", \"cümə\", \"şənbə\"], [\"B.\", \"B.E.\", \"Ç.A.\", \"Ç.\", \"C.A.\", \"C.\", \"Ş.\"]], [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"yan\", \"fev\", \"mar\", \"apr\", \"may\", \"iyn\", \"iyl\", \"avq\", \"sen\", \"okt\", \"noy\", \"dek\"], [\"yanvar\", \"fevral\", \"mart\", \"aprel\", \"may\", \"iyun\", \"iyul\", \"avqust\", \"sentyabr\", \"oktyabr\", \"noyabr\", \"dekabr\"]], u, [[\"e.ə.\", \"y.e.\"], u, [\"eramızdan əvvəl\", \"yeni era\"]], 1, [6, 0], [\"dd.MM.yy\", \"d MMM y\", \"d MMMM y\", \"d MMMM y, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"AZN\", \"₼\", \"Azərbaycan Manatı\", { \"AZN\": [\"₼\"], \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"], \"RON\": [u, \"ley\"], \"SYP\": [u, \"S£\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,mBAAmB,EAAE;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}