{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Channel, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\nimport { _throwInternal, getJSON, isArray, isFunction, isObject, objFor<PERSON><PERSON><PERSON><PERSON> } from \"@microsoft/applicationinsights-core-js\";\nimport { _DYN_LENGTH, _DYN_PUSH, _DYN_STRINGIFY, _DYN_TO_STRING } from \"./__DynamicConstants\";\nvar Serializer = /** @class */function () {\n  function Serializer(logger) {\n    dynamicProto(Serializer, this, function (_self) {\n      /**\r\n       * Serializes the current object to a JSON string.\r\n       */\n      _self.serialize = function (input) {\n        var output = _serializeObject(input, \"root\");\n        try {\n          return getJSON()[_DYN_STRINGIFY /* @min:%2estringify */](output);\n        } catch (e) {\n          // if serialization fails return an empty string\n          _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 48 /* _eInternalMessageId.CannotSerializeObject */, e && isFunction(e[_DYN_TO_STRING /* @min:%2etoString */]) ? e[_DYN_TO_STRING /* @min:%2etoString */]() : \"Error serializing object\", null, true);\n        }\n      };\n      function _serializeObject(source, name) {\n        var circularReferenceCheck = \"__aiCircularRefCheck\";\n        var output = {};\n        if (!source) {\n          _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 48 /* _eInternalMessageId.CannotSerializeObject */, \"cannot serialize object because it is null or undefined\", {\n            name: name\n          }, true);\n          return output;\n        }\n        if (source[circularReferenceCheck]) {\n          _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 50 /* _eInternalMessageId.CircularReferenceDetected */, \"Circular reference detected while serializing object\", {\n            name: name\n          }, true);\n          return output;\n        }\n        if (!source.aiDataContract) {\n          // special case for measurements/properties/tags\n          if (name === \"measurements\") {\n            output = _serializeStringMap(source, \"number\", name);\n          } else if (name === \"properties\") {\n            output = _serializeStringMap(source, \"string\", name);\n          } else if (name === \"tags\") {\n            output = _serializeStringMap(source, \"string\", name);\n          } else if (isArray(source)) {\n            output = _serializeArray(source, name);\n          } else {\n            _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 49 /* _eInternalMessageId.CannotSerializeObjectNonSerializable */, \"Attempting to serialize an object which does not implement ISerializable\", {\n              name: name\n            }, true);\n            try {\n              // verify that the object can be stringified\n              getJSON()[_DYN_STRINGIFY /* @min:%2estringify */](source);\n              output = source;\n            } catch (e) {\n              // if serialization fails return an empty string\n              _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 48 /* _eInternalMessageId.CannotSerializeObject */, e && isFunction(e[_DYN_TO_STRING /* @min:%2etoString */]) ? e[_DYN_TO_STRING /* @min:%2etoString */]() : \"Error serializing object\", null, true);\n            }\n          }\n          return output;\n        }\n        source[circularReferenceCheck] = true;\n        objForEachKey(source.aiDataContract, function (field, contract) {\n          var isRequired = isFunction(contract) ? contract() & 1 /* FieldType.Required */ : contract & 1 /* FieldType.Required */;\n          var isHidden = isFunction(contract) ? contract() & 4 /* FieldType.Hidden */ : contract & 4 /* FieldType.Hidden */;\n          var isArray = contract & 2 /* FieldType.Array */;\n          var isPresent = source[field] !== undefined;\n          var isObj = isObject(source[field]) && source[field] !== null;\n          if (isRequired && !isPresent && !isArray) {\n            _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 24 /* _eInternalMessageId.MissingRequiredFieldSpecification */, \"Missing required field specification. The field is required but not present on source\", {\n              field: field,\n              name: name\n            });\n            // If not in debug mode, continue and hope the error is permissible\n          } else if (!isHidden) {\n            // Don't serialize hidden fields\n            var value = void 0;\n            if (isObj) {\n              if (isArray) {\n                // special case; recurse on each object in the source array\n                value = _serializeArray(source[field], field);\n              } else {\n                // recurse on the source object in this field\n                value = _serializeObject(source[field], field);\n              }\n            } else {\n              // assign the source field to the output even if undefined or required\n              value = source[field];\n            }\n            // only emit this field if the value is defined\n            if (value !== undefined) {\n              output[field] = value;\n            }\n          }\n        });\n        delete source[circularReferenceCheck];\n        return output;\n      }\n      function _serializeArray(sources, name) {\n        var output;\n        if (!!sources) {\n          if (!isArray(sources)) {\n            _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 54 /* _eInternalMessageId.ItemNotInArray */, \"This field was specified as an array in the contract but the item is not an array.\\r\\n\", {\n              name: name\n            }, true);\n          } else {\n            output = [];\n            for (var i = 0; i < sources[_DYN_LENGTH /* @min:%2elength */]; i++) {\n              var source = sources[i];\n              var item = _serializeObject(source, name + \"[\" + i + \"]\");\n              output[_DYN_PUSH /* @min:%2epush */](item);\n            }\n          }\n        }\n        return output;\n      }\n      function _serializeStringMap(map, expectedType, name) {\n        var output;\n        if (map) {\n          output = {};\n          objForEachKey(map, function (field, value) {\n            if (expectedType === \"string\") {\n              if (value === undefined) {\n                output[field] = \"undefined\";\n              } else if (value === null) {\n                output[field] = \"null\";\n              } else if (!value[_DYN_TO_STRING /* @min:%2etoString */]) {\n                output[field] = \"invalid field: toString() is not defined.\";\n              } else {\n                output[field] = value[_DYN_TO_STRING /* @min:%2etoString */]();\n              }\n            } else if (expectedType === \"number\") {\n              if (value === undefined) {\n                output[field] = \"undefined\";\n              } else if (value === null) {\n                output[field] = \"null\";\n              } else {\n                var num = parseFloat(value);\n                if (isNaN(num)) {\n                  output[field] = \"NaN\";\n                } else {\n                  output[field] = num;\n                }\n              }\n            } else {\n              output[field] = \"invalid field: \" + name + \" is of unknown type.\";\n              _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, output[field], null, true);\n            }\n          });\n        }\n        return output;\n      }\n    });\n  }\n  // Removed Stub for Serializer.prototype.serialize.\n  // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n  // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n  // this will be removed when ES3 support is dropped.\n  Serializer.__ieDyn = 1;\n  return Serializer;\n}();\nexport { Serializer };", "map": {"version": 3, "names": ["dynamicProto", "_throwInternal", "getJSON", "isArray", "isFunction", "isObject", "objFor<PERSON><PERSON><PERSON>", "_DYN_LENGTH", "_DYN_PUSH", "_DYN_STRINGIFY", "_DYN_TO_STRING", "Serializer", "logger", "_self", "serialize", "input", "output", "_serializeObject", "e", "source", "name", "circularReferenceCheck", "aiDataContract", "_serializeStringMap", "_serializeArray", "field", "contract", "isRequired", "isHidden", "isPresent", "undefined", "isObj", "value", "sources", "i", "item", "map", "expectedType", "num", "parseFloat", "isNaN", "__ie<PERSON>yn"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-channel-js/dist-esm/Serializer.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Channel, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\r\nimport { _throwInternal, getJSON, isArray, isFunction, isObject, objFor<PERSON><PERSON><PERSON><PERSON> } from \"@microsoft/applicationinsights-core-js\";\r\nimport { _DYN_LENGTH, _DYN_PUSH, _DYN_STRINGIFY, _DYN_TO_STRING } from \"./__DynamicConstants\";\r\nvar Serializer = /** @class */ (function () {\r\n    function Serializer(logger) {\r\n        dynamicProto(Serializer, this, function (_self) {\r\n            /**\r\n             * Serializes the current object to a JSON string.\r\n             */\r\n            _self.serialize = function (input) {\r\n                var output = _serializeObject(input, \"root\");\r\n                try {\r\n                    return getJSON()[_DYN_STRINGIFY /* @min:%2estringify */](output);\r\n                }\r\n                catch (e) {\r\n                    // if serialization fails return an empty string\r\n                    _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 48 /* _eInternalMessageId.CannotSerializeObject */, (e && isFunction(e[_DYN_TO_STRING /* @min:%2etoString */])) ? e[_DYN_TO_STRING /* @min:%2etoString */]() : \"Error serializing object\", null, true);\r\n                }\r\n            };\r\n            function _serializeObject(source, name) {\r\n                var circularReferenceCheck = \"__aiCircularRefCheck\";\r\n                var output = {};\r\n                if (!source) {\r\n                    _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 48 /* _eInternalMessageId.CannotSerializeObject */, \"cannot serialize object because it is null or undefined\", { name: name }, true);\r\n                    return output;\r\n                }\r\n                if (source[circularReferenceCheck]) {\r\n                    _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 50 /* _eInternalMessageId.CircularReferenceDetected */, \"Circular reference detected while serializing object\", { name: name }, true);\r\n                    return output;\r\n                }\r\n                if (!source.aiDataContract) {\r\n                    // special case for measurements/properties/tags\r\n                    if (name === \"measurements\") {\r\n                        output = _serializeStringMap(source, \"number\", name);\r\n                    }\r\n                    else if (name === \"properties\") {\r\n                        output = _serializeStringMap(source, \"string\", name);\r\n                    }\r\n                    else if (name === \"tags\") {\r\n                        output = _serializeStringMap(source, \"string\", name);\r\n                    }\r\n                    else if (isArray(source)) {\r\n                        output = _serializeArray(source, name);\r\n                    }\r\n                    else {\r\n                        _throwInternal(logger, 2 /* eLoggingSeverity.WARNING */, 49 /* _eInternalMessageId.CannotSerializeObjectNonSerializable */, \"Attempting to serialize an object which does not implement ISerializable\", { name: name }, true);\r\n                        try {\r\n                            // verify that the object can be stringified\r\n                            getJSON()[_DYN_STRINGIFY /* @min:%2estringify */](source);\r\n                            output = source;\r\n                        }\r\n                        catch (e) {\r\n                            // if serialization fails return an empty string\r\n                            _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 48 /* _eInternalMessageId.CannotSerializeObject */, (e && isFunction(e[_DYN_TO_STRING /* @min:%2etoString */])) ? e[_DYN_TO_STRING /* @min:%2etoString */]() : \"Error serializing object\", null, true);\r\n                        }\r\n                    }\r\n                    return output;\r\n                }\r\n                source[circularReferenceCheck] = true;\r\n                objForEachKey(source.aiDataContract, function (field, contract) {\r\n                    var isRequired = (isFunction(contract)) ? (contract() & 1 /* FieldType.Required */) : (contract & 1 /* FieldType.Required */);\r\n                    var isHidden = (isFunction(contract)) ? (contract() & 4 /* FieldType.Hidden */) : (contract & 4 /* FieldType.Hidden */);\r\n                    var isArray = contract & 2 /* FieldType.Array */;\r\n                    var isPresent = source[field] !== undefined;\r\n                    var isObj = isObject(source[field]) && source[field] !== null;\r\n                    if (isRequired && !isPresent && !isArray) {\r\n                        _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 24 /* _eInternalMessageId.MissingRequiredFieldSpecification */, \"Missing required field specification. The field is required but not present on source\", { field: field, name: name });\r\n                        // If not in debug mode, continue and hope the error is permissible\r\n                    }\r\n                    else if (!isHidden) { // Don't serialize hidden fields\r\n                        var value = void 0;\r\n                        if (isObj) {\r\n                            if (isArray) {\r\n                                // special case; recurse on each object in the source array\r\n                                value = _serializeArray(source[field], field);\r\n                            }\r\n                            else {\r\n                                // recurse on the source object in this field\r\n                                value = _serializeObject(source[field], field);\r\n                            }\r\n                        }\r\n                        else {\r\n                            // assign the source field to the output even if undefined or required\r\n                            value = source[field];\r\n                        }\r\n                        // only emit this field if the value is defined\r\n                        if (value !== undefined) {\r\n                            output[field] = value;\r\n                        }\r\n                    }\r\n                });\r\n                delete source[circularReferenceCheck];\r\n                return output;\r\n            }\r\n            function _serializeArray(sources, name) {\r\n                var output;\r\n                if (!!sources) {\r\n                    if (!isArray(sources)) {\r\n                        _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, 54 /* _eInternalMessageId.ItemNotInArray */, \"This field was specified as an array in the contract but the item is not an array.\\r\\n\", { name: name }, true);\r\n                    }\r\n                    else {\r\n                        output = [];\r\n                        for (var i = 0; i < sources[_DYN_LENGTH /* @min:%2elength */]; i++) {\r\n                            var source = sources[i];\r\n                            var item = _serializeObject(source, name + \"[\" + i + \"]\");\r\n                            output[_DYN_PUSH /* @min:%2epush */](item);\r\n                        }\r\n                    }\r\n                }\r\n                return output;\r\n            }\r\n            function _serializeStringMap(map, expectedType, name) {\r\n                var output;\r\n                if (map) {\r\n                    output = {};\r\n                    objForEachKey(map, function (field, value) {\r\n                        if (expectedType === \"string\") {\r\n                            if (value === undefined) {\r\n                                output[field] = \"undefined\";\r\n                            }\r\n                            else if (value === null) {\r\n                                output[field] = \"null\";\r\n                            }\r\n                            else if (!value[_DYN_TO_STRING /* @min:%2etoString */]) {\r\n                                output[field] = \"invalid field: toString() is not defined.\";\r\n                            }\r\n                            else {\r\n                                output[field] = value[_DYN_TO_STRING /* @min:%2etoString */]();\r\n                            }\r\n                        }\r\n                        else if (expectedType === \"number\") {\r\n                            if (value === undefined) {\r\n                                output[field] = \"undefined\";\r\n                            }\r\n                            else if (value === null) {\r\n                                output[field] = \"null\";\r\n                            }\r\n                            else {\r\n                                var num = parseFloat(value);\r\n                                if (isNaN(num)) {\r\n                                    output[field] = \"NaN\";\r\n                                }\r\n                                else {\r\n                                    output[field] = num;\r\n                                }\r\n                            }\r\n                        }\r\n                        else {\r\n                            output[field] = \"invalid field: \" + name + \" is of unknown type.\";\r\n                            _throwInternal(logger, 1 /* eLoggingSeverity.CRITICAL */, output[field], null, true);\r\n                        }\r\n                    });\r\n                }\r\n                return output;\r\n            }\r\n        });\r\n    }\r\n// Removed Stub for Serializer.prototype.serialize.\r\n    // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n    // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n    // this will be removed when ES3 support is dropped.\n    Serializer.__ieDyn=1;\n\n    return Serializer;\r\n}());\r\nexport { Serializer };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,SAASC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,wCAAwC;AAC9H,SAASC,WAAW,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AAC7F,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,MAAM,EAAE;IACxBZ,YAAY,CAACW,UAAU,EAAE,IAAI,EAAE,UAAUE,KAAK,EAAE;MAC5C;AACZ;AACA;MACYA,KAAK,CAACC,SAAS,GAAG,UAAUC,KAAK,EAAE;QAC/B,IAAIC,MAAM,GAAGC,gBAAgB,CAACF,KAAK,EAAE,MAAM,CAAC;QAC5C,IAAI;UACA,OAAOb,OAAO,CAAC,CAAC,CAACO,cAAc,CAAC,wBAAwB,CAACO,MAAM,CAAC;QACpE,CAAC,CACD,OAAOE,CAAC,EAAE;UACN;UACAjB,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,iCAAiC,EAAE,CAAC,iDAAkDM,CAAC,IAAId,UAAU,CAACc,CAAC,CAACR,cAAc,CAAC,uBAAuB,CAAC,GAAIQ,CAAC,CAACR,cAAc,CAAC,uBAAuB,CAAC,CAAC,GAAG,0BAA0B,EAAE,IAAI,EAAE,IAAI,CAAC;QACpQ;MACJ,CAAC;MACD,SAASO,gBAAgBA,CAACE,MAAM,EAAEC,IAAI,EAAE;QACpC,IAAIC,sBAAsB,GAAG,sBAAsB;QACnD,IAAIL,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,CAACG,MAAM,EAAE;UACTlB,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,iCAAiC,EAAE,CAAC,iDAAiD,yDAAyD,EAAE;YAAEQ,IAAI,EAAEA;UAAK,CAAC,EAAE,IAAI,CAAC;UAC9L,OAAOJ,MAAM;QACjB;QACA,IAAIG,MAAM,CAACE,sBAAsB,CAAC,EAAE;UAChCpB,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,gCAAgC,EAAE,CAAC,qDAAqD,sDAAsD,EAAE;YAAEQ,IAAI,EAAEA;UAAK,CAAC,EAAE,IAAI,CAAC;UAC9L,OAAOJ,MAAM;QACjB;QACA,IAAI,CAACG,MAAM,CAACG,cAAc,EAAE;UACxB;UACA,IAAIF,IAAI,KAAK,cAAc,EAAE;YACzBJ,MAAM,GAAGO,mBAAmB,CAACJ,MAAM,EAAE,QAAQ,EAAEC,IAAI,CAAC;UACxD,CAAC,MACI,IAAIA,IAAI,KAAK,YAAY,EAAE;YAC5BJ,MAAM,GAAGO,mBAAmB,CAACJ,MAAM,EAAE,QAAQ,EAAEC,IAAI,CAAC;UACxD,CAAC,MACI,IAAIA,IAAI,KAAK,MAAM,EAAE;YACtBJ,MAAM,GAAGO,mBAAmB,CAACJ,MAAM,EAAE,QAAQ,EAAEC,IAAI,CAAC;UACxD,CAAC,MACI,IAAIjB,OAAO,CAACgB,MAAM,CAAC,EAAE;YACtBH,MAAM,GAAGQ,eAAe,CAACL,MAAM,EAAEC,IAAI,CAAC;UAC1C,CAAC,MACI;YACDnB,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,gCAAgC,EAAE,CAAC,gEAAgE,0EAA0E,EAAE;cAAEQ,IAAI,EAAEA;YAAK,CAAC,EAAE,IAAI,CAAC;YAC7N,IAAI;cACA;cACAlB,OAAO,CAAC,CAAC,CAACO,cAAc,CAAC,wBAAwB,CAACU,MAAM,CAAC;cACzDH,MAAM,GAAGG,MAAM;YACnB,CAAC,CACD,OAAOD,CAAC,EAAE;cACN;cACAjB,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,iCAAiC,EAAE,CAAC,iDAAkDM,CAAC,IAAId,UAAU,CAACc,CAAC,CAACR,cAAc,CAAC,uBAAuB,CAAC,GAAIQ,CAAC,CAACR,cAAc,CAAC,uBAAuB,CAAC,CAAC,GAAG,0BAA0B,EAAE,IAAI,EAAE,IAAI,CAAC;YACpQ;UACJ;UACA,OAAOM,MAAM;QACjB;QACAG,MAAM,CAACE,sBAAsB,CAAC,GAAG,IAAI;QACrCf,aAAa,CAACa,MAAM,CAACG,cAAc,EAAE,UAAUG,KAAK,EAAEC,QAAQ,EAAE;UAC5D,IAAIC,UAAU,GAAIvB,UAAU,CAACsB,QAAQ,CAAC,GAAKA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,2BAA6BA,QAAQ,GAAG,CAAC,CAAC,wBAAyB;UAC7H,IAAIE,QAAQ,GAAIxB,UAAU,CAACsB,QAAQ,CAAC,GAAKA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,yBAA2BA,QAAQ,GAAG,CAAC,CAAC,sBAAuB;UACvH,IAAIvB,OAAO,GAAGuB,QAAQ,GAAG,CAAC,CAAC;UAC3B,IAAIG,SAAS,GAAGV,MAAM,CAACM,KAAK,CAAC,KAAKK,SAAS;UAC3C,IAAIC,KAAK,GAAG1B,QAAQ,CAACc,MAAM,CAACM,KAAK,CAAC,CAAC,IAAIN,MAAM,CAACM,KAAK,CAAC,KAAK,IAAI;UAC7D,IAAIE,UAAU,IAAI,CAACE,SAAS,IAAI,CAAC1B,OAAO,EAAE;YACtCF,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,iCAAiC,EAAE,CAAC,6DAA6D,uFAAuF,EAAE;cAAEa,KAAK,EAAEA,KAAK;cAAEL,IAAI,EAAEA;YAAK,CAAC,CAAC;YAChP;UACJ,CAAC,MACI,IAAI,CAACQ,QAAQ,EAAE;YAAE;YAClB,IAAII,KAAK,GAAG,KAAK,CAAC;YAClB,IAAID,KAAK,EAAE;cACP,IAAI5B,OAAO,EAAE;gBACT;gBACA6B,KAAK,GAAGR,eAAe,CAACL,MAAM,CAACM,KAAK,CAAC,EAAEA,KAAK,CAAC;cACjD,CAAC,MACI;gBACD;gBACAO,KAAK,GAAGf,gBAAgB,CAACE,MAAM,CAACM,KAAK,CAAC,EAAEA,KAAK,CAAC;cAClD;YACJ,CAAC,MACI;cACD;cACAO,KAAK,GAAGb,MAAM,CAACM,KAAK,CAAC;YACzB;YACA;YACA,IAAIO,KAAK,KAAKF,SAAS,EAAE;cACrBd,MAAM,CAACS,KAAK,CAAC,GAAGO,KAAK;YACzB;UACJ;QACJ,CAAC,CAAC;QACF,OAAOb,MAAM,CAACE,sBAAsB,CAAC;QACrC,OAAOL,MAAM;MACjB;MACA,SAASQ,eAAeA,CAACS,OAAO,EAAEb,IAAI,EAAE;QACpC,IAAIJ,MAAM;QACV,IAAI,CAAC,CAACiB,OAAO,EAAE;UACX,IAAI,CAAC9B,OAAO,CAAC8B,OAAO,CAAC,EAAE;YACnBhC,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,iCAAiC,EAAE,CAAC,0CAA0C,wFAAwF,EAAE;cAAEQ,IAAI,EAAEA;YAAK,CAAC,EAAE,IAAI,CAAC;UAC1N,CAAC,MACI;YACDJ,MAAM,GAAG,EAAE;YACX,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAAC1B,WAAW,CAAC,qBAAqB,EAAE2B,CAAC,EAAE,EAAE;cAChE,IAAIf,MAAM,GAAGc,OAAO,CAACC,CAAC,CAAC;cACvB,IAAIC,IAAI,GAAGlB,gBAAgB,CAACE,MAAM,EAAEC,IAAI,GAAG,GAAG,GAAGc,CAAC,GAAG,GAAG,CAAC;cACzDlB,MAAM,CAACR,SAAS,CAAC,mBAAmB,CAAC2B,IAAI,CAAC;YAC9C;UACJ;QACJ;QACA,OAAOnB,MAAM;MACjB;MACA,SAASO,mBAAmBA,CAACa,GAAG,EAAEC,YAAY,EAAEjB,IAAI,EAAE;QAClD,IAAIJ,MAAM;QACV,IAAIoB,GAAG,EAAE;UACLpB,MAAM,GAAG,CAAC,CAAC;UACXV,aAAa,CAAC8B,GAAG,EAAE,UAAUX,KAAK,EAAEO,KAAK,EAAE;YACvC,IAAIK,YAAY,KAAK,QAAQ,EAAE;cAC3B,IAAIL,KAAK,KAAKF,SAAS,EAAE;gBACrBd,MAAM,CAACS,KAAK,CAAC,GAAG,WAAW;cAC/B,CAAC,MACI,IAAIO,KAAK,KAAK,IAAI,EAAE;gBACrBhB,MAAM,CAACS,KAAK,CAAC,GAAG,MAAM;cAC1B,CAAC,MACI,IAAI,CAACO,KAAK,CAACtB,cAAc,CAAC,uBAAuB,EAAE;gBACpDM,MAAM,CAACS,KAAK,CAAC,GAAG,2CAA2C;cAC/D,CAAC,MACI;gBACDT,MAAM,CAACS,KAAK,CAAC,GAAGO,KAAK,CAACtB,cAAc,CAAC,uBAAuB,CAAC,CAAC;cAClE;YACJ,CAAC,MACI,IAAI2B,YAAY,KAAK,QAAQ,EAAE;cAChC,IAAIL,KAAK,KAAKF,SAAS,EAAE;gBACrBd,MAAM,CAACS,KAAK,CAAC,GAAG,WAAW;cAC/B,CAAC,MACI,IAAIO,KAAK,KAAK,IAAI,EAAE;gBACrBhB,MAAM,CAACS,KAAK,CAAC,GAAG,MAAM;cAC1B,CAAC,MACI;gBACD,IAAIa,GAAG,GAAGC,UAAU,CAACP,KAAK,CAAC;gBAC3B,IAAIQ,KAAK,CAACF,GAAG,CAAC,EAAE;kBACZtB,MAAM,CAACS,KAAK,CAAC,GAAG,KAAK;gBACzB,CAAC,MACI;kBACDT,MAAM,CAACS,KAAK,CAAC,GAAGa,GAAG;gBACvB;cACJ;YACJ,CAAC,MACI;cACDtB,MAAM,CAACS,KAAK,CAAC,GAAG,iBAAiB,GAAGL,IAAI,GAAG,sBAAsB;cACjEnB,cAAc,CAACW,MAAM,EAAE,CAAC,CAAC,iCAAiCI,MAAM,CAACS,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;YACxF;UACJ,CAAC,CAAC;QACN;QACA,OAAOT,MAAM;MACjB;IACJ,CAAC,CAAC;EACN;EACJ;EACI;EACA;EACA;EACAL,UAAU,CAAC8B,OAAO,GAAC,CAAC;EAEpB,OAAO9B,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}