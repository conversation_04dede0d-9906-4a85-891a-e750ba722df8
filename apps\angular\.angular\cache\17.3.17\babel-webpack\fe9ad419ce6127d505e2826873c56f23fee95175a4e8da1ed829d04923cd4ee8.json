{"ast": null, "code": "import { mapTo, switchMap } from 'rxjs/operators';\nimport { RecaptchaV2Strategy, RecaptchaV3Strategy } from '../strategies/recaptcha.strategy';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nimport * as i2 from \"./account.service\";\nexport let RecaptchaService = /*#__PURE__*/(() => {\n  class RecaptchaService {\n    get isEnabled() {\n      return this.pageStrategy.isEnabled;\n    }\n    get reCaptchaVersion() {\n      return this.configState.getSetting('Abp.Account.Captcha.Version');\n    }\n    constructor(injector, configState, accountService) {\n      this.injector = injector;\n      this.configState = configState;\n      this.accountService = accountService;\n    }\n    setStrategy(strategy) {\n      this.pageStrategy = strategy;\n      if (!this.pageStrategy.isEnabled) return;\n      this.init();\n    }\n    init() {\n      if (this.reCaptchaVersion === '2') this.strategy = new RecaptchaV2Strategy(this.injector, this.pageStrategy.targetElement);else if (this.reCaptchaVersion === '3') {\n        this.strategy = new RecaptchaV3Strategy(this.injector, this.pageStrategy.action);\n      }\n    }\n    validate() {\n      return this.strategy.getVerificationToken().pipe(switchMap(token => this.accountService.recaptchaByCaptchaResponse(token).pipe(mapTo(true))));\n    }\n    getVerificationToken() {\n      return this.strategy.getVerificationToken();\n    }\n    ngOnDestroy() {\n      if (!this.pageStrategy.isEnabled) return;\n      this.strategy.destroy();\n    }\n    reset() {\n      if (!this.isEnabled) return;\n      this.strategy.reset();\n    }\n    static {\n      this.ɵfac = function RecaptchaService_Factory(t) {\n        return new (t || RecaptchaService)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i1.ConfigStateService), i0.ɵɵinject(i2.AccountService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RecaptchaService,\n        factory: RecaptchaService.ɵfac\n      });\n    }\n  }\n  return RecaptchaService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}