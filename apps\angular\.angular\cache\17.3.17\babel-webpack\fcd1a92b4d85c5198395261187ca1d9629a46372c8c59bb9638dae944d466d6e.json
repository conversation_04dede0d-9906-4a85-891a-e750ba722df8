{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/os\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['os', [['AM', 'PM'], u, ['ӕмбисбоны размӕ', 'ӕмбисбоны фӕстӕ']], [['AM', 'PM'], u, u], [['Х', 'К', 'Д', 'Ӕ', 'Ц', 'М', 'С'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт'], ['хуыцаубон', 'къуырисӕр', 'дыццӕг', 'ӕртыццӕг', 'цыппӕрӕм', 'майрӕмбон', 'сабат'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт']], [['Х', 'К', 'Д', 'Ӕ', 'Ц', 'М', 'С'], ['Хцб', 'Крс', 'Дцг', 'Ӕрт', 'Цпр', 'Мрб', 'Сбт'], ['Хуыцаубон', 'Къуырисӕр', 'Дыццӕг', 'Ӕртыццӕг', 'Цыппӕрӕм', 'Майрӕмбон', 'Сабат'], ['хцб', 'крс', 'дцг', 'ӕрт', 'цпр', 'мрб', 'сбт']], [['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'], ['янв.', 'фев.', 'мар.', 'апр.', 'майы', 'июны', 'июлы', 'авг.', 'сен.', 'окт.', 'ноя.', 'дек.'], ['январы', 'февралы', 'мартъийы', 'апрелы', 'майы', 'июны', 'июлы', 'августы', 'сентябры', 'октябры', 'ноябры', 'декабры']], [['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'], ['Янв.', 'Февр.', 'Март.', 'Апр.', 'Май', 'Июнь', 'Июль', 'Авг.', 'Сент.', 'Окт.', 'Нояб.', 'Дек.'], ['Январь', 'Февраль', 'Мартъи', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь']], [['н.д.а.', 'н.д.'], u, u], 1, [6, 0], ['dd.MM.yy', 'dd MMM y \\'аз\\'', 'd MMMM, y \\'аз\\'', 'EEEE, d MMMM, y \\'аз\\''], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1}, {0}', u, u, u], [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'НН', ':'], ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'], 'GEL', '₾', 'Лар', {\n    'GEL': ['₾'],\n    'JPY': ['JP¥', '¥']\n  }, 'ltr', plural];\n});\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}