{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    t = parseInt(val.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n  if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0)) return 1;\n  return 5;\n}\nexport default [\"is\", [[\"f.\", \"e.\"], [\"f.h.\", \"e.h.\"], u], [[\"f.h.\", \"e.h.\"], u, u], [[\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"], [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"], [\"sunnudagur\", \"mánudagur\", \"þriðjudagur\", \"miðvikudagur\", \"fimmtudagur\", \"föstudagur\", \"laugardagur\"], [\"su.\", \"má.\", \"þr.\", \"mi.\", \"fi.\", \"fö.\", \"la.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maí\", \"jún.\", \"júl.\", \"ágú.\", \"sep.\", \"okt.\", \"nóv.\", \"des.\"], [\"janúar\", \"febrúar\", \"mars\", \"apríl\", \"maí\", \"júní\", \"júlí\", \"ágúst\", \"september\", \"október\", \"nóvember\", \"desember\"]], u, [[\"f.k.\", \"e.k.\"], [\"f.Kr.\", \"e.Kr.\"], [\"fyrir Krist\", \"eftir Krist\"]], 1, [6, 0], [\"d.M.y\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'kl'. {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"ISK\", \"ISK\", \"íslensk króna\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"CAD\": [u, \"$\"],\n  \"EUR\": [u, \"€\"],\n  \"GBP\": [u, \"£\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "t", "parseInt", "toString", "replace"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/is.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), t = parseInt(val.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n    if (t === 0 && (i % 10 === 1 && !(i % 100 === 11)) || !(t === 0))\n        return 1;\n    return 5;\n}\nexport default [\"is\", [[\"f.\", \"e.\"], [\"f.h.\", \"e.h.\"], u], [[\"f.h.\", \"e.h.\"], u, u], [[\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"], [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"], [\"sunnudagur\", \"mánudagur\", \"þriðjudagur\", \"miðvikudagur\", \"fimmtudagur\", \"föstudagur\", \"laugardagur\"], [\"su.\", \"má.\", \"þr.\", \"mi.\", \"fi.\", \"fö.\", \"la.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maí\", \"jún.\", \"júl.\", \"ágú.\", \"sep.\", \"okt.\", \"nóv.\", \"des.\"], [\"janúar\", \"febrúar\", \"mars\", \"apríl\", \"maí\", \"júní\", \"júlí\", \"ágúst\", \"september\", \"október\", \"nóvember\", \"desember\"]], u, [[\"f.k.\", \"e.k.\"], [\"f.Kr.\", \"e.Kr.\"], [\"fyrir Krist\", \"eftir Krist\"]], 1, [6, 0], [\"d.M.y\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'kl'. {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"ISK\", \"ISK\", \"íslensk króna\", { \"AUD\": [u, \"$\"], \"BRL\": [u, \"R$\"], \"CAD\": [u, \"$\"], \"EUR\": [u, \"€\"], \"GBP\": [u, \"£\"], \"INR\": [u, \"₹\"], \"JPY\": [\"JP¥\", \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGC,QAAQ,CAACP,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;EACjH,IAAIH,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAE,IAAI,EAAEI,CAAC,KAAK,CAAC,CAAC,EAC5D,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAET,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,eAAe,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}