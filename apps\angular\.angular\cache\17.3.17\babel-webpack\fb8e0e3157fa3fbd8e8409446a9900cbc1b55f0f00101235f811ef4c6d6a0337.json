{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\n/** Rendering \" of red flags summary stats\n *  */\nexport class RedFlagsSummaryComponent extends AppComponentBase {\n  constructor(injector) {\n    super(injector);\n  }\n  ngOnChanges(changes) {\n    if (changes.statsSummaryData && this.statsSummaryData) {\n      console.log(this.statsSummaryData);\n    }\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function RedFlagsSummaryComponent_Factory(t) {\n      return new (t || RedFlagsSummaryComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RedFlagsSummaryComponent,\n      selectors: [[\"app-red-flags-summary\"]],\n      inputs: {\n        statsSummaryData: \"statsSummaryData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 42,\n      vars: 9,\n      consts: [[1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"]],\n      template: function RedFlagsSummaryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n          i0.ɵɵtext(2, \"Red Flag Events Summary:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\")(4, \"div\", 1)(5, \"div\", 2);\n          i0.ɵɵtext(6, \"Total Events\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 2);\n          i0.ɵɵtext(8, \"Total Filings with Events\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 2);\n          i0.ɵɵtext(10, \"Total Assessment Not Started\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 2);\n          i0.ɵɵtext(12, \"% Of Assessment Not started\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 2);\n          i0.ɵɵtext(14, \"Total Assessment Completed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 2);\n          i0.ɵɵtext(16, \"% Of Assessment Passed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 2);\n          i0.ɵɵtext(18, \"% Of Assessment Failed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 2);\n          i0.ɵɵtext(20, \"Total Assessment Closed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 2);\n          i0.ɵɵtext(22, \"% Of Assessment Closed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 1)(24, \"div\", 3);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 3);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 3);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 3);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 3);\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 3);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 3);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 3);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 3);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalEvents);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalFilingWithEvents);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalAssessmentNotStarted);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentNotStarted, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalAssessmentCompleted);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentPassed, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentFailed, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.statsSummaryData.totalAssessmentClosed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.statsSummaryData.percentageOfAssessmentClosed, \"%\");\n        }\n      },\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZWQtZmxhZ3Mtc3VtbWFyeS5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvcmVkLWZsYWdzLWV2ZW50cy9yZWQtZmxhZ3Mtc3VtbWFyeS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTEFBZ0wiLCJzb3VyY2VSb290IjoiIn0= */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "RedFlagsSummaryComponent", "constructor", "injector", "ngOnChanges", "changes", "statsSummaryData", "console", "log", "ngOnInit", "i0", "ɵɵdirectiveInject", "Injector", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "RedFlagsSummaryComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "totalEvents", "totalFilingWithEvents", "totalAssessmentNotStarted", "ɵɵtextInterpolate1", "percentageOfAssessmentNotStarted", "totalAssessmentCompleted", "percentageOfAssessmentPassed", "percentageOfAssessmentFailed", "totalAssessmentClosed", "percentageOfAssessmentClosed"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\red-flags-events\\red-flags-summary.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\red-flags-events\\red-flags-summary.component.html"], "sourcesContent": ["import {\r\n    Component,\r\n    OnInit,\r\n    ViewEncapsulation,\r\n    Injector,\r\n    Input,\r\n    SimpleChanges,\r\n  } from '@angular/core';\r\n  import { AppComponentBase } from '@app/app-component-base';\r\n  import { DashboardStatsSummaryDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\n  \r\n  /** Rendering \" of red flags summary stats\r\n   *  */\r\n  @Component({\r\n    encapsulation: ViewEncapsulation.None,\r\n    selector: 'app-red-flags-summary',\r\n    templateUrl: './red-flags-summary.component.html',\r\n    styleUrls: ['./red-flags-summary.component.css'],\r\n  })\r\n  export class RedFlagsSummaryComponent extends AppComponentBase implements OnInit {\r\n    \r\n    @Input() statsSummaryData: DashboardStatsSummaryDto;\r\n    constructor(injector: Injector) {\r\n      super(injector);\r\n    }\r\n    \r\n    ngOnChanges (changes: SimpleChanges){\r\n      if(changes.statsSummaryData && this.statsSummaryData){\r\n        console.log(this.statsSummaryData)\r\n      }\r\n    }\r\n  \r\n    ngOnInit() {\r\n      \r\n    }\r\n  \r\n  }", "<div>\r\n    <div class=\"dashboard-card-title\">Red Flag Events Summary:</div>\r\n    <div>\r\n      <div class=\"dashboard-table\">\r\n        <div class=\"col title\">Total Events</div>\r\n        <div class=\"col title\">Total Filings with Events</div>\r\n        <div class=\"col title\">Total Assessment Not Started</div>\r\n        <div class=\"col title\">% Of Assessment Not started</div>\r\n        <div class=\"col title\">Total Assessment Completed</div>\r\n        <div class=\"col title\">% Of Assessment Passed</div>\r\n        <div class=\"col title\">% Of Assessment Failed</div>\r\n        <div class=\"col title\">Total Assessment Closed</div>\r\n        <div class=\"col title\">% Of Assessment Closed</div>\r\n      </div>\r\n      <div class=\"dashboard-table\">\r\n        <div class=\"col item\">{{statsSummaryData.totalEvents}}</div>\r\n        <div class=\"col item\">{{statsSummaryData.totalFilingWithEvents}}</div>\r\n        <div class=\"col item\">{{statsSummaryData.totalAssessmentNotStarted}}</div>\r\n        <div class=\"col item\">{{statsSummaryData.percentageOfAssessmentNotStarted}}%</div>\r\n        <div class=\"col item\">{{statsSummaryData.totalAssessmentCompleted}}</div>\r\n        <div class=\"col item\">{{statsSummaryData.percentageOfAssessmentPassed}}%</div>\r\n        <div class=\"col item\">{{statsSummaryData.percentageOfAssessmentFailed}}%</div>\r\n        <div class=\"col item\">{{statsSummaryData.totalAssessmentClosed}}</div>\r\n        <div class=\"col item\">{{statsSummaryData.percentageOfAssessmentClosed}}%</div>\r\n      </div>\r\n    </div>\r\n</div>"], "mappings": "AAQE,SAASA,gBAAgB,QAAQ,yBAAyB;;AAG1D;;AAQA,OAAM,MAAOC,wBAAyB,SAAQD,gBAAgB;EAG5DE,YAAYC,QAAkB;IAC5B,KAAK,CAACA,QAAQ,CAAC;EACjB;EAEAC,WAAWA,CAAEC,OAAsB;IACjC,IAAGA,OAAO,CAACC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,EAAC;MACnDC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACF,gBAAgB,CAAC;IACpC;EACF;EAEAG,QAAQA,CAAA,GAER;;;uBAfWR,wBAAwB,EAAAS,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA;IAAA;EAAA;;;YAAxBX,wBAAwB;MAAAY,SAAA;MAAAC,MAAA;QAAAR,gBAAA;MAAA;MAAAS,QAAA,GAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBnCb,EADJ,CAAAe,cAAA,UAAK,aACiC;UAAAf,EAAA,CAAAgB,MAAA,+BAAwB;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAG5DjB,EAFJ,CAAAe,cAAA,UAAK,aAC0B,aACJ;UAAAf,EAAA,CAAAgB,MAAA,mBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACzCjB,EAAA,CAAAe,cAAA,aAAuB;UAAAf,EAAA,CAAAgB,MAAA,gCAAyB;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACtDjB,EAAA,CAAAe,cAAA,aAAuB;UAAAf,EAAA,CAAAgB,MAAA,oCAA4B;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACzDjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,mCAA2B;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACxDjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,kCAA0B;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACvDjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,8BAAsB;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACnDjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,8BAAsB;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACnDjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,+BAAuB;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACpDjB,EAAA,CAAAe,cAAA,cAAuB;UAAAf,EAAA,CAAAgB,MAAA,8BAAsB;UAC/ChB,EAD+C,CAAAiB,YAAA,EAAM,EAC/C;UAEJjB,EADF,CAAAe,cAAA,cAA6B,cACL;UAAAf,EAAA,CAAAgB,MAAA,IAAgC;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC5DjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAA0C;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACtEjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAA8C;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC1EjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAAsD;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAClFjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAA6C;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACzEjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAAkD;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC9EjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAAkD;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC9EjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAA0C;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACtEjB,EAAA,CAAAe,cAAA,cAAsB;UAAAf,EAAA,CAAAgB,MAAA,IAAkD;UAGhFhB,EAHgF,CAAAiB,YAAA,EAAM,EAC1E,EACF,EACJ;;;UAXwBjB,EAAA,CAAAkB,SAAA,IAAgC;UAAhClB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,gBAAA,CAAAwB,WAAA,CAAgC;UAChCpB,EAAA,CAAAkB,SAAA,GAA0C;UAA1ClB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,gBAAA,CAAAyB,qBAAA,CAA0C;UAC1CrB,EAAA,CAAAkB,SAAA,GAA8C;UAA9ClB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,gBAAA,CAAA0B,yBAAA,CAA8C;UAC9CtB,EAAA,CAAAkB,SAAA,GAAsD;UAAtDlB,EAAA,CAAAuB,kBAAA,KAAAT,GAAA,CAAAlB,gBAAA,CAAA4B,gCAAA,MAAsD;UACtDxB,EAAA,CAAAkB,SAAA,GAA6C;UAA7ClB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,gBAAA,CAAA6B,wBAAA,CAA6C;UAC7CzB,EAAA,CAAAkB,SAAA,GAAkD;UAAlDlB,EAAA,CAAAuB,kBAAA,KAAAT,GAAA,CAAAlB,gBAAA,CAAA8B,4BAAA,MAAkD;UAClD1B,EAAA,CAAAkB,SAAA,GAAkD;UAAlDlB,EAAA,CAAAuB,kBAAA,KAAAT,GAAA,CAAAlB,gBAAA,CAAA+B,4BAAA,MAAkD;UAClD3B,EAAA,CAAAkB,SAAA,GAA0C;UAA1ClB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,gBAAA,CAAAgC,qBAAA,CAA0C;UAC1C5B,EAAA,CAAAkB,SAAA,GAAkD;UAAlDlB,EAAA,CAAAuB,kBAAA,KAAAT,GAAA,CAAAlB,gBAAA,CAAAiC,4BAAA,MAAkD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}