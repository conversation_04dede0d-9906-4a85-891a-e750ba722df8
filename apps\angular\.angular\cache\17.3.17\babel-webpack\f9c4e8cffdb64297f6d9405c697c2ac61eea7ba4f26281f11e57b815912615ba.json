{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'menos dun segundo',\n    other: 'menos de {{count}} segundos'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundos'\n  },\n  halfAMinute: 'medio minuto',\n  lessThanXMinutes: {\n    one: 'menos dun minuto',\n    other: 'menos de {{count}} minutos'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutos'\n  },\n  aboutXHours: {\n    one: 'arredor dunha hora',\n    other: 'arredor de {{count}} horas'\n  },\n  xHours: {\n    one: '1 hora',\n    other: '{{count}} horas'\n  },\n  xDays: {\n    one: '1 día',\n    other: '{{count}} días'\n  },\n  aboutXWeeks: {\n    one: 'arredor dunha semana',\n    other: 'arredor de {{count}} semanas'\n  },\n  xWeeks: {\n    one: '1 semana',\n    other: '{{count}} semanas'\n  },\n  aboutXMonths: {\n    one: 'arredor de 1 mes',\n    other: 'arredor de {{count}} meses'\n  },\n  xMonths: {\n    one: '1 mes',\n    other: '{{count}} meses'\n  },\n  aboutXYears: {\n    one: 'arredor dun ano',\n    other: 'arredor de {{count}} anos'\n  },\n  xYears: {\n    one: '1 ano',\n    other: '{{count}} anos'\n  },\n  overXYears: {\n    one: 'máis dun ano',\n    other: 'máis de {{count}} anos'\n  },\n  almostXYears: {\n    one: 'case un ano',\n    other: 'case {{count}} anos'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'en ' + result;\n    } else {\n      return 'hai ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}