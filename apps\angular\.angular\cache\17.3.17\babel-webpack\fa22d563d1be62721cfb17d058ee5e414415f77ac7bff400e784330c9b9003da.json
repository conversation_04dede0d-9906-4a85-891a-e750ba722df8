{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/pt\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    var i = Math.floor(Math.abs(n));\n    if (i === Math.floor(i) && i >= 0 && i <= 1) return 1;\n    return 5;\n  }\n  exports.default = ['pt', [['AM', 'PM'], u, u], u, [['D', 'S', 'T', 'Q', 'Q', 'S', 'S'], ['dom.', 'seg.', 'ter.', 'qua.', 'qui.', 'sex.', 'sáb.'], ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'], ['dom.', 'seg.', 'ter.', 'qua.', 'qui.', 'sex.', 'sáb.']], u, [['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'], ['jan.', 'fev.', 'mar.', 'abr.', 'mai.', 'jun.', 'jul.', 'ago.', 'set.', 'out.', 'nov.', 'dez.'], ['janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho', 'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro']], u, [['a.C.', 'd.C.'], u, ['antes de Cristo', 'depois de Cristo']], 0, [6, 0], ['dd/MM/y', 'd \\'de\\' MMM \\'de\\' y', 'd \\'de\\' MMMM \\'de\\' y', 'EEEE, d \\'de\\' MMMM \\'de\\' y'], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1} {0}', u, u, u], [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'], 'BRL', 'R$', 'Real brasileiro', {\n    'AUD': ['AU$', '$'],\n    'JPY': ['JP¥', '¥'],\n    'PTE': ['Esc.'],\n    'RON': [u, 'L'],\n    'SYP': [u, 'S£'],\n    'THB': ['฿'],\n    'TWD': ['NT$'],\n    'USD': ['US$', '$']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "i", "Math", "floor", "abs", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/pt.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/pt\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        var i = Math.floor(Math.abs(n));\n        if (i === Math.floor(i) && i >= 0 && i <= 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'pt',\n        [['AM', 'PM'], u, u],\n        u,\n        [\n            ['D', 'S', 'T', 'Q', 'Q', 'S', 'S'], ['dom.', 'seg.', 'ter.', 'qua.', 'qui.', 'sex.', 'sáb.'],\n            [\n                'domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira',\n                'sábado'\n            ],\n            ['dom.', 'seg.', 'ter.', 'qua.', 'qui.', 'sex.', 'sáb.']\n        ],\n        u,\n        [\n            ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n            [\n                'jan.', 'fev.', 'mar.', 'abr.', 'mai.', 'jun.', 'jul.', 'ago.', 'set.', 'out.', 'nov.', 'dez.'\n            ],\n            [\n                'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho', 'julho', 'agosto', 'setembro',\n                'outubro', 'novembro', 'dezembro'\n            ]\n        ],\n        u,\n        [['a.C.', 'd.C.'], u, ['antes de Cristo', 'depois de Cristo']],\n        0,\n        [6, 0],\n        ['dd/MM/y', 'd \\'de\\' MMM \\'de\\' y', 'd \\'de\\' MMMM \\'de\\' y', 'EEEE, d \\'de\\' MMMM \\'de\\' y'],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1} {0}', u, u, u],\n        [',', '.', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'],\n        'BRL',\n        'R$',\n        'Real brasileiro',\n        {\n            'AUD': ['AU$', '$'],\n            'JPY': ['JP¥', '¥'],\n            'PTE': ['Esc.'],\n            'RON': [u, 'L'],\n            'SYP': [u, 'S£'],\n            'THB': ['฿'],\n            'TWD': ['NT$'],\n            'USD': ['US$', '$']\n        },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;IAC/B,IAAIC,CAAC,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EACvC,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAZ,OAAO,CAACgB,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEP,CAAC,EAAEA,CAAC,CAAC,EACpBA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAC7F,CACI,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EACxF,QAAQ,CACX,EACD,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAC3D,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CACjG,EACD,CACI,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EACxF,SAAS,EAAE,UAAU,EAAE,UAAU,CACpC,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,EAC9D,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,SAAS,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,8BAA8B,CAAC,EAC9F,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,IAAI,EACJ,iBAAiB,EACjB;IACI,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EACtB,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}