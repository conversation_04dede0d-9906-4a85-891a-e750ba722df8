{"ast": null, "code": "import { take, tap } from 'rxjs/operators';\nimport { ChangePasswordComponent } from '../components/change-password/change-password.component';\nimport { PersonalSettingsComponent } from '../components/personal-settings/personal-settings.component';\nimport { ProfilePictureComponent } from '../components/profile-picture/profile-picture.component';\nimport { TwoFactorTabComponent } from '../components/two-factor-tab/two-factor-tab.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../config/src/services\";\nimport * as i2 from \"@abp/ng.core\";\nexport class ManageProfileResolver {\n  constructor(manageProfileTabs, replaceableComponentsService) {\n    this.manageProfileTabs = manageProfileTabs;\n    this.replaceableComponentsService = replaceableComponentsService;\n    this.addComponentsToTabs = tabs => {\n      tabs.forEach(tab => {\n        if (tab.component) return;\n        const replaceableComponent = this.replaceableComponentsService.get(tab.name);\n        const component = replaceableComponent ? replaceableComponent.component : ComponentList[tab.name];\n        if (!component) {\n          return;\n        }\n        this.manageProfileTabs.patch(tab.name, {\n          component\n        });\n      });\n    };\n  }\n  resolve() {\n    return this.manageProfileTabs.visible$.pipe(tap(this.addComponentsToTabs), take(1));\n  }\n  static {\n    this.ɵfac = function ManageProfileResolver_Factory(t) {\n      return new (t || ManageProfileResolver)(i0.ɵɵinject(i1.ManageProfileTabsService), i0.ɵɵinject(i2.ReplaceableComponentsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ManageProfileResolver,\n      factory: ManageProfileResolver.ɵfac\n    });\n  }\n}\nconst ComponentList = {\n  [\"AbpAccount::ProfileTab:Picture\" /* eAccountManageProfileTabNames.ProfilePicture */]: ProfilePictureComponent,\n  [\"AbpAccount::ProfileTab:Password\" /* eAccountManageProfileTabNames.ChangePassword */]: ChangePasswordComponent,\n  [\"AbpAccount::ProfileTab:PersonalInfo\" /* eAccountManageProfileTabNames.PersonalInfo */]: PersonalSettingsComponent,\n  [\"AbpAccount::ProfileTab:TwoFactor\" /* eAccountManageProfileTabNames.TwoFactor */]: TwoFactorTabComponent\n};", "map": {"version": 3, "names": ["take", "tap", "ChangePasswordComponent", "PersonalSettingsComponent", "ProfilePictureComponent", "TwoFactorTabComponent", "ManageProfileResolver", "constructor", "manageProfileTabs", "replaceableComponentsService", "addComponentsToTabs", "tabs", "for<PERSON>ach", "tab", "component", "replaceableComponent", "get", "name", "ComponentList", "patch", "resolve", "visible$", "pipe", "i0", "ɵɵinject", "i1", "ManageProfileTabsService", "i2", "ReplaceableComponentsService", "factory", "ɵfac"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\resolvers\\manage-profile.resolver.ts"], "sourcesContent": ["import { ABP, ReplaceableComponentsService, TreeNode } from '@abp/ng.core';\r\nimport { Injectable, Type } from '@angular/core';\r\nimport {\r\n  eAccountManageProfileTabNames,\r\n} from '@volo/abp.ng.account/public/config';\r\nimport { take, tap } from 'rxjs/operators';\r\nimport { ChangePasswordComponent } from '../components/change-password/change-password.component';\r\nimport { PersonalSettingsComponent } from '../components/personal-settings/personal-settings.component';\r\nimport { ProfilePictureComponent } from '../components/profile-picture/profile-picture.component';\r\nimport { TwoFactorTabComponent } from '../components/two-factor-tab/two-factor-tab.component';\r\nimport { ManageProfileTabsService } from '../../config/src/services';\r\n\r\n@Injectable()\r\nexport class ManageProfileResolver {\r\n  constructor(\r\n    private manageProfileTabs: ManageProfileTabsService,\r\n    private replaceableComponentsService: ReplaceableComponentsService,\r\n  ) {}\r\n\r\n  resolve() {\r\n    return this.manageProfileTabs.visible$.pipe(tap(this.addComponentsToTabs), take(1));\r\n  }\r\n\r\n  private addComponentsToTabs = (tabs: TreeNode<Omit<ABP.Tab, 'parentName'>>[]) => {\r\n    tabs.forEach(tab => {\r\n      if (tab.component) return;\r\n\r\n      const replaceableComponent = this.replaceableComponentsService.get(tab.name);\r\n      const component: Type<any> = replaceableComponent\r\n        ? replaceableComponent.component\r\n        : ComponentList[tab.name];\r\n      if (!component) {\r\n        return;\r\n      }\r\n      this.manageProfileTabs.patch(tab.name, { component });\r\n    });\r\n  };\r\n}\r\n\r\nconst ComponentList = {\r\n  [eAccountManageProfileTabNames.ProfilePicture]: ProfilePictureComponent,\r\n  [eAccountManageProfileTabNames.ChangePassword]: ChangePasswordComponent,\r\n  [eAccountManageProfileTabNames.PersonalInfo]: PersonalSettingsComponent,\r\n  [eAccountManageProfileTabNames.TwoFactor]: TwoFactorTabComponent,\r\n};\r\n"], "mappings": "AAKA,SAASA,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAC1C,SAASC,uBAAuB,QAAQ,yDAAyD;AACjG,SAASC,yBAAyB,QAAQ,6DAA6D;AACvG,SAASC,uBAAuB,QAAQ,yDAAyD;AACjG,SAASC,qBAAqB,QAAQ,uDAAuD;;;;AAI7F,OAAM,MAAOC,qBAAqB;EAChCC,YACUC,iBAA2C,EAC3CC,4BAA0D;IAD1D,KAAAD,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAO9B,KAAAC,mBAAmB,GAAIC,IAA6C,IAAI;MAC9EA,IAAI,CAACC,OAAO,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,SAAS,EAAE;QAEnB,MAAMC,oBAAoB,GAAG,IAAI,CAACN,4BAA4B,CAACO,GAAG,CAACH,GAAG,CAACI,IAAI,CAAC;QAC5E,MAAMH,SAAS,GAAcC,oBAAoB,GAC7CA,oBAAoB,CAACD,SAAS,GAC9BI,aAAa,CAACL,GAAG,CAACI,IAAI,CAAC;QAC3B,IAAI,CAACH,SAAS,EAAE;UACd;QACF;QACA,IAAI,CAACN,iBAAiB,CAACW,KAAK,CAACN,GAAG,CAACI,IAAI,EAAE;UAAEH;QAAS,CAAE,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC;EAnBE;EAEHM,OAAOA,CAAA;IACL,OAAO,IAAI,CAACZ,iBAAiB,CAACa,QAAQ,CAACC,IAAI,CAACrB,GAAG,CAAC,IAAI,CAACS,mBAAmB,CAAC,EAAEV,IAAI,CAAC,CAAC,CAAC,CAAC;EACrF;;;uBARWM,qBAAqB,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,wBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,4BAAA;IAAA;EAAA;;;aAArBtB,qBAAqB;MAAAuB,OAAA,EAArBvB,qBAAqB,CAAAwB;IAAA;EAAA;;AA0BlC,MAAMZ,aAAa,GAAG;EACpB,uFAAgDd,uBAAuB;EACvE,wFAAgDF,uBAAuB;EACvE,0FAA8CC,yBAAyB;EACvE,oFAA2CE;CAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}