{"ast": null, "code": "import { SelectionModel, isDataSource } from '@angular/cdk/collections';\nimport { isObservable, Subject, BehaviorSubject, of } from 'rxjs';\nimport { take, filter, takeUntil } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, numberAttribute, booleanAttribute, NgModule } from '@angular/core';\nimport * as i2 from '@angular/cdk/bidi';\n\n/** Base tree control. It has basic toggle/expand/collapse operations on a single data node. */\nclass BaseTreeControl {\n  constructor() {\n    /** A selection model with multi-selection to track expansion status. */\n    this.expansionModel = new SelectionModel(true);\n  }\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode) {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n  /** Expands one single data node. */\n  expand(dataNode) {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n  /** Collapses one single data node. */\n  collapse(dataNode) {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode) {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode) {\n    this.expansionModel.isSelected(this._trackByValue(dataNode)) ? this.collapseDescendants(dataNode) : this.expandDescendants(dataNode);\n  }\n  /** Collapse all dataNodes in the tree. */\n  collapseAll() {\n    this.expansionModel.clear();\n  }\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  _trackByValue(value) {\n    return this.trackBy ? this.trackBy(value) : value;\n  }\n}\n\n/** Flat tree control. Able to expand/collapse a subtree recursively for flattened tree. */\nclass FlatTreeControl extends BaseTreeControl {\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(getLevel, isExpandable, options) {\n    super();\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode) {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results = [];\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n\n/** Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type. */\nclass NestedTreeControl extends BaseTreeControl {\n  /** Construct with nested tree function getChildren. */\n  constructor(getChildren, options) {\n    super();\n    this.getChildren = getChildren;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode) {\n    const descendants = [];\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n  /** A helper function to get descendants recursively. */\n  _getDescendants(descendants, dataNode) {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach(child => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = /*#__PURE__*/new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nlet CdkTreeNodeOutlet = /*#__PURE__*/(() => {\n  class CdkTreeNodeOutlet {\n    constructor(viewContainer, _node) {\n      this.viewContainer = viewContainer;\n      this._node = _node;\n    }\n    static {\n      this.ɵfac = function CdkTreeNodeOutlet_Factory(t) {\n        return new (t || CdkTreeNodeOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_TREE_NODE_OUTLET_NODE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTreeNodeOutlet,\n        selectors: [[\"\", \"cdkTreeNodeOutlet\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkTreeNodeOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n  constructor(data) {\n    this.$implicit = data;\n  }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nlet CdkTreeNodeDef = /*#__PURE__*/(() => {\n  class CdkTreeNodeDef {\n    /** @docs-private */\n    constructor(template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function CdkTreeNodeDef_Factory(t) {\n        return new (t || CdkTreeNodeDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTreeNodeDef,\n        selectors: [[\"\", \"cdkTreeNodeDef\", \"\"]],\n        inputs: {\n          when: [i0.ɵɵInputFlags.None, \"cdkTreeNodeDefWhen\", \"when\"]\n        },\n        standalone: true\n      });\n    }\n  }\n  return CdkTreeNodeDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there are tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n  return Error(`Could not find a tree control for the tree.`);\n}\n/**\n * Returns an error to be thrown when tree control did not implement functions for flat/nested node.\n * @docs-private\n */\nfunction getTreeControlFunctionsMissingError() {\n  return Error(`Could not find functions for nested/flat tree in tree control.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nlet CdkTree = /*#__PURE__*/(() => {\n  class CdkTree {\n    /**\n     * Provides a stream containing the latest data array to render. Influenced by the tree's\n     * stream of view window (what dataNodes are currently on screen).\n     * Data source can be an observable of data array, or a data array to render.\n     */\n    get dataSource() {\n      return this._dataSource;\n    }\n    set dataSource(dataSource) {\n      if (this._dataSource !== dataSource) {\n        this._switchDataSource(dataSource);\n      }\n    }\n    constructor(_differs, _changeDetectorRef) {\n      this._differs = _differs;\n      this._changeDetectorRef = _changeDetectorRef;\n      /** Subject that emits when the component has been destroyed. */\n      this._onDestroy = new Subject();\n      /** Level of nodes */\n      this._levels = new Map();\n      // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n      //     Remove the MAX_VALUE in viewChange\n      /**\n       * Stream containing the latest information on what rows are being displayed on screen.\n       * Can be used by the data source to as a heuristic of what data should be provided.\n       */\n      this.viewChange = new BehaviorSubject({\n        start: 0,\n        end: Number.MAX_VALUE\n      });\n    }\n    ngOnInit() {\n      this._dataDiffer = this._differs.find([]).create(this.trackBy);\n      if (!this.treeControl && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTreeControlMissingError();\n      }\n    }\n    ngOnDestroy() {\n      this._nodeOutlet.viewContainer.clear();\n      this.viewChange.complete();\n      this._onDestroy.next();\n      this._onDestroy.complete();\n      if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n        this.dataSource.disconnect(this);\n      }\n      if (this._dataSubscription) {\n        this._dataSubscription.unsubscribe();\n        this._dataSubscription = null;\n      }\n    }\n    ngAfterContentChecked() {\n      const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n      if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTreeMultipleDefaultNodeDefsError();\n      }\n      this._defaultNodeDef = defaultNodeDefs[0];\n      if (this.dataSource && this._nodeDefs && !this._dataSubscription) {\n        this._observeRenderChanges();\n      }\n    }\n    // TODO(tinayuangao): Work on keyboard traversal and actions, make sure it's working for RTL\n    //     and nested trees.\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the node outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n      if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n        this.dataSource.disconnect(this);\n      }\n      if (this._dataSubscription) {\n        this._dataSubscription.unsubscribe();\n        this._dataSubscription = null;\n      }\n      // Remove the all dataNodes if there is now no data source\n      if (!dataSource) {\n        this._nodeOutlet.viewContainer.clear();\n      }\n      this._dataSource = dataSource;\n      if (this._nodeDefs) {\n        this._observeRenderChanges();\n      }\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n      let dataStream;\n      if (isDataSource(this._dataSource)) {\n        dataStream = this._dataSource.connect(this);\n      } else if (isObservable(this._dataSource)) {\n        dataStream = this._dataSource;\n      } else if (Array.isArray(this._dataSource)) {\n        dataStream = of(this._dataSource);\n      }\n      if (dataStream) {\n        this._dataSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => this.renderNodeChanges(data));\n      } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        throw getTreeNoValidDataSourceError();\n      }\n    }\n    /** Check for changes made in the data and render each change (node added/removed/moved). */\n    renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n      const changes = dataDiffer.diff(data);\n      if (!changes) {\n        return;\n      }\n      changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n        if (item.previousIndex == null) {\n          this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n        } else if (currentIndex == null) {\n          viewContainer.remove(adjustedPreviousIndex);\n          this._levels.delete(item.item);\n        } else {\n          const view = viewContainer.get(adjustedPreviousIndex);\n          viewContainer.move(view, currentIndex);\n        }\n      });\n      this._changeDetectorRef.detectChanges();\n    }\n    /**\n     * Finds the matching node definition that should be used for this node data. If there is only\n     * one node definition, it is returned. Otherwise, find the node definition that has a when\n     * predicate that returns true with the data. If none return true, return the default node\n     * definition.\n     */\n    _getNodeDef(data, i) {\n      if (this._nodeDefs.length === 1) {\n        return this._nodeDefs.first;\n      }\n      const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n      if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTreeMissingMatchingNodeDefError();\n      }\n      return nodeDef;\n    }\n    /**\n     * Create the embedded view for the data node template and place it in the correct index location\n     * within the data node view container.\n     */\n    insertNode(nodeData, index, viewContainer, parentData) {\n      const node = this._getNodeDef(nodeData, index);\n      // Node context that will be provided to created embedded view\n      const context = new CdkTreeNodeOutletContext(nodeData);\n      // If the tree is flat tree, then use the `getLevel` function in flat tree control\n      // Otherwise, use the level of parent node.\n      if (this.treeControl.getLevel) {\n        context.level = this.treeControl.getLevel(nodeData);\n      } else if (typeof parentData !== 'undefined' && this._levels.has(parentData)) {\n        context.level = this._levels.get(parentData) + 1;\n      } else {\n        context.level = 0;\n      }\n      this._levels.set(nodeData, context.level);\n      // Use default tree nodeOutlet, or nested node's nodeOutlet\n      const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n      container.createEmbeddedView(node.template, context, index);\n      // Set the data to just created `CdkTreeNode`.\n      // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n      //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n      if (CdkTreeNode.mostRecentTreeNode) {\n        CdkTreeNode.mostRecentTreeNode.data = nodeData;\n      }\n    }\n    static {\n      this.ɵfac = function CdkTree_Factory(t) {\n        return new (t || CdkTree)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkTree,\n        selectors: [[\"cdk-tree\"]],\n        contentQueries: function CdkTree_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeDef, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeDefs = _t);\n          }\n        },\n        viewQuery: function CdkTree_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(CdkTreeNodeOutlet, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n          }\n        },\n        hostAttrs: [\"role\", \"tree\", 1, \"cdk-tree\"],\n        inputs: {\n          dataSource: \"dataSource\",\n          treeControl: \"treeControl\",\n          trackBy: \"trackBy\"\n        },\n        exportAs: [\"cdkTree\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 0,\n        consts: [[\"cdkTreeNodeOutlet\", \"\"]],\n        template: function CdkTree_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementContainer(0, 0);\n          }\n        },\n        dependencies: [CdkTreeNodeOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkTree;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nlet CdkTreeNode = /*#__PURE__*/(() => {\n  class CdkTreeNode {\n    /**\n     * The role of the tree node.\n     * @deprecated The correct role is 'treeitem', 'group' should not be used. This input will be\n     *   removed in a future version.\n     * @breaking-change 12.0.0 Remove this input\n     */\n    get role() {\n      return 'treeitem';\n    }\n    set role(_role) {\n      // TODO: move to host after View Engine deprecation\n      this._elementRef.nativeElement.setAttribute('role', _role);\n    }\n    /**\n     * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n     * in `CdkTree` and set the data to it.\n     */\n    static {\n      this.mostRecentTreeNode = null;\n    }\n    /** The tree node's data. */\n    get data() {\n      return this._data;\n    }\n    set data(value) {\n      if (value !== this._data) {\n        this._data = value;\n        this._setRoleFromData();\n        this._dataChanges.next();\n      }\n    }\n    get isExpanded() {\n      return this._tree.treeControl.isExpanded(this._data);\n    }\n    get level() {\n      // If the treeControl has a getLevel method, use it to get the level. Otherwise read the\n      // aria-level off the parent node and use it as the level for this node (note aria-level is\n      // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n      return this._tree.treeControl.getLevel ? this._tree.treeControl.getLevel(this._data) : this._parentNodeAriaLevel;\n    }\n    constructor(_elementRef, _tree) {\n      this._elementRef = _elementRef;\n      this._tree = _tree;\n      /** Subject that emits when the component has been destroyed. */\n      this._destroyed = new Subject();\n      /** Emits when the node's data has changed. */\n      this._dataChanges = new Subject();\n      CdkTreeNode.mostRecentTreeNode = this;\n      this.role = 'treeitem';\n    }\n    ngOnInit() {\n      this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n      this._elementRef.nativeElement.setAttribute('aria-level', `${this.level + 1}`);\n    }\n    ngOnDestroy() {\n      // If this is the last tree node being destroyed,\n      // clear out the reference to avoid leaking memory.\n      if (CdkTreeNode.mostRecentTreeNode === this) {\n        CdkTreeNode.mostRecentTreeNode = null;\n      }\n      this._dataChanges.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Focuses the menu item. Implements for FocusableOption. */\n    focus() {\n      this._elementRef.nativeElement.focus();\n    }\n    // TODO: role should eventually just be set in the component host\n    _setRoleFromData() {\n      if (!this._tree.treeControl.isExpandable && !this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTreeControlFunctionsMissingError();\n      }\n      this.role = 'treeitem';\n    }\n    static {\n      this.ɵfac = function CdkTreeNode_Factory(t) {\n        return new (t || CdkTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CdkTree));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTreeNode,\n        selectors: [[\"cdk-tree-node\"]],\n        hostAttrs: [1, \"cdk-tree-node\"],\n        hostVars: 1,\n        hostBindings: function CdkTreeNode_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-expanded\", ctx.isExpanded);\n          }\n        },\n        inputs: {\n          role: \"role\"\n        },\n        exportAs: [\"cdkTreeNode\"],\n        standalone: true\n      });\n    }\n  }\n  return CdkTreeNode;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction getParentNodeAriaLevel(nodeElement) {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level'));\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\nfunction isNodeElement(element) {\n  const classList = element.classList;\n  return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nlet CdkNestedTreeNode = /*#__PURE__*/(() => {\n  class CdkNestedTreeNode extends CdkTreeNode {\n    constructor(elementRef, tree, _differs) {\n      super(elementRef, tree);\n      this._differs = _differs;\n    }\n    ngAfterContentInit() {\n      this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n      if (!this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTreeControlFunctionsMissingError();\n      }\n      const childrenNodes = this._tree.treeControl.getChildren(this.data);\n      if (Array.isArray(childrenNodes)) {\n        this.updateChildrenNodes(childrenNodes);\n      } else if (isObservable(childrenNodes)) {\n        childrenNodes.pipe(takeUntil(this._destroyed)).subscribe(result => this.updateChildrenNodes(result));\n      }\n      this.nodeOutlet.changes.pipe(takeUntil(this._destroyed)).subscribe(() => this.updateChildrenNodes());\n    }\n    // This is a workaround for https://github.com/angular/angular/issues/23091\n    // In aot mode, the lifecycle hooks from parent class are not called.\n    ngOnInit() {\n      super.ngOnInit();\n    }\n    ngOnDestroy() {\n      this._clear();\n      super.ngOnDestroy();\n    }\n    /** Add children dataNodes to the NodeOutlet */\n    updateChildrenNodes(children) {\n      const outlet = this._getNodeOutlet();\n      if (children) {\n        this._children = children;\n      }\n      if (outlet && this._children) {\n        const viewContainer = outlet.viewContainer;\n        this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n      } else {\n        // Reset the data differ if there's no children nodes displayed\n        this._dataDiffer.diff([]);\n      }\n    }\n    /** Clear the children dataNodes. */\n    _clear() {\n      const outlet = this._getNodeOutlet();\n      if (outlet) {\n        outlet.viewContainer.clear();\n        this._dataDiffer.diff([]);\n      }\n    }\n    /** Gets the outlet for the current node. */\n    _getNodeOutlet() {\n      const outlets = this.nodeOutlet;\n      // Note that since we use `descendants: true` on the query, we have to ensure\n      // that we don't pick up the outlet of a child node by accident.\n      return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n    }\n    static {\n      this.ɵfac = function CdkNestedTreeNode_Factory(t) {\n        return new (t || CdkNestedTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkNestedTreeNode,\n        selectors: [[\"cdk-nested-tree-node\"]],\n        contentQueries: function CdkNestedTreeNode_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeOutlet, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeOutlet = _t);\n          }\n        },\n        hostAttrs: [1, \"cdk-nested-tree-node\"],\n        exportAs: [\"cdkNestedTreeNode\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CdkTreeNode,\n          useExisting: CdkNestedTreeNode\n        }, {\n          provide: CDK_TREE_NODE_OUTLET_NODE,\n          useExisting: CdkNestedTreeNode\n        }]), i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return CdkNestedTreeNode;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nlet CdkTreeNodePadding = /*#__PURE__*/(() => {\n  class CdkTreeNodePadding {\n    /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n    get level() {\n      return this._level;\n    }\n    set level(value) {\n      this._setLevelInput(value);\n    }\n    /**\n     * The indent for each level. Can be a number or a CSS string.\n     * Default number 40px from material design menu sub-menu spec.\n     */\n    get indent() {\n      return this._indent;\n    }\n    set indent(indent) {\n      this._setIndentInput(indent);\n    }\n    constructor(_treeNode, _tree, _element, _dir) {\n      this._treeNode = _treeNode;\n      this._tree = _tree;\n      this._element = _element;\n      this._dir = _dir;\n      /** Subject that emits when the component has been destroyed. */\n      this._destroyed = new Subject();\n      /** CSS units used for the indentation value. */\n      this.indentUnits = 'px';\n      this._indent = 40;\n      this._setPadding();\n      if (_dir) {\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n      }\n      // In Ivy the indentation binding might be set before the tree node's data has been added,\n      // which means that we'll miss the first render. We have to subscribe to changes in the\n      // data to ensure that everything is up to date.\n      _treeNode._dataChanges.subscribe(() => this._setPadding());\n    }\n    ngOnDestroy() {\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n    _paddingIndent() {\n      const nodeLevel = this._treeNode.data && this._tree.treeControl.getLevel ? this._tree.treeControl.getLevel(this._treeNode.data) : null;\n      const level = this._level == null ? nodeLevel : this._level;\n      return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n    }\n    _setPadding(forceChange = false) {\n      const padding = this._paddingIndent();\n      if (padding !== this._currentPadding || forceChange) {\n        const element = this._element.nativeElement;\n        const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n        const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n        element.style[paddingProp] = padding || '';\n        element.style[resetProp] = '';\n        this._currentPadding = padding;\n      }\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setLevelInput(value) {\n      // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n      // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n      // they set 0 explicitly.\n      this._level = isNaN(value) ? null : value;\n      this._setPadding();\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setIndentInput(indent) {\n      let value = indent;\n      let units = 'px';\n      if (typeof indent === 'string') {\n        const parts = indent.split(cssUnitPattern);\n        value = parts[0];\n        units = parts[1] || units;\n      }\n      this.indentUnits = units;\n      this._indent = numberAttribute(value);\n      this._setPadding();\n    }\n    static {\n      this.ɵfac = function CdkTreeNodePadding_Factory(t) {\n        return new (t || CdkTreeNodePadding)(i0.ɵɵdirectiveInject(CdkTreeNode), i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTreeNodePadding,\n        selectors: [[\"\", \"cdkTreeNodePadding\", \"\"]],\n        inputs: {\n          level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTreeNodePadding\", \"level\", numberAttribute],\n          indent: [i0.ɵɵInputFlags.None, \"cdkTreeNodePaddingIndent\", \"indent\"]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return CdkTreeNodePadding;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Node toggle to expand/collapse the node.\n */\nlet CdkTreeNodeToggle = /*#__PURE__*/(() => {\n  class CdkTreeNodeToggle {\n    constructor(_tree, _treeNode) {\n      this._tree = _tree;\n      this._treeNode = _treeNode;\n      /** Whether expand/collapse the node recursively. */\n      this.recursive = false;\n    }\n    _toggle(event) {\n      this.recursive ? this._tree.treeControl.toggleDescendants(this._treeNode.data) : this._tree.treeControl.toggle(this._treeNode.data);\n      event.stopPropagation();\n    }\n    static {\n      this.ɵfac = function CdkTreeNodeToggle_Factory(t) {\n        return new (t || CdkTreeNodeToggle)(i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(CdkTreeNode));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTreeNodeToggle,\n        selectors: [[\"\", \"cdkTreeNodeToggle\", \"\"]],\n        hostBindings: function CdkTreeNodeToggle_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function CdkTreeNodeToggle_click_HostBindingHandler($event) {\n              return ctx._toggle($event);\n            });\n          }\n        },\n        inputs: {\n          recursive: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return CdkTreeNodeToggle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst EXPORTED_DECLARATIONS = [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet];\nlet CdkTreeModule = /*#__PURE__*/(() => {\n  class CdkTreeModule {\n    static {\n      this.ɵfac = function CdkTreeModule_Factory(t) {\n        return new (t || CdkTreeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: CdkTreeModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return CdkTreeModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getTreeControlFunctionsMissingError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };\n//# sourceMappingURL=tree.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}