{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'prošlu nedjelju u' p\";\n      case 3:\n        return \"'prošlu srijedu u' p\";\n      case 6:\n        return \"'prošlu subotu u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'jučer u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: function nextWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'iduću nedjelju u' p\";\n      case 3:\n        return \"'iduću srijedu u' p\";\n      case 6:\n        return \"'iduću subotu u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/hr/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'prošlu nedjelju u' p\";\n      case 3:\n        return \"'prošlu srijedu u' p\";\n      case 6:\n        return \"'prošlu subotu u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'jučer u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: function nextWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'iduću nedjelju u' p\";\n      case 3:\n        return \"'iduću srijedu u' p\";\n      case 6:\n        return \"'iduću subotu u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,QAAQA,IAAI,CAACC,SAAS,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EACDC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,SAASA,QAAQA,CAACL,IAAI,EAAE;IAChC,QAAQA,IAAI,CAACC,SAAS,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,oBAAoB;MAC7B;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EACDK,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAER,IAAI,EAAES,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,MAAM,GAAGb,oBAAoB,CAACU,KAAK,CAAC;EACxC,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,CAAC;EACrB;EACA,OAAOW,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}