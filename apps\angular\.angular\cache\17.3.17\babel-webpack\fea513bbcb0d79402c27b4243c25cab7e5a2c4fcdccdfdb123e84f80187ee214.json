{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"ks-Deva\", [[\"AM\", \"PM\"], u, u], u, [[\"अ\", \"च\", \"ब\", \"ब\", \"ब\", \"ज\", \"ब\"], [\"आथवार\", \"चंदिरवार\", \"बुवार\", \"बोदवार\", \"ब्रेसवार\", \"जुम्मा\", \"बटवार\"], u, u], u, [[\"ज\", \"फ़\", \"म\", \"अ\", \"म\", \"ज\", \"ज\", \"अ\", \"स\", \"ओ\", \"न\", \"द\"], [\"जनवरी\", \"फ़रवरी\", \"मार्च\", \"अप्रैल\", \"मे\", \"जून\", \"जुलाई\", \"अगस्त\", \"सतुंबर\", \"अकतुम्बर\", \"नवूमबर\", \"दसूमबर\"], u], u, [[\"BC\", \"AD\"], u, u], 0, [0, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"a h:mm\", \"a h:mm:ss\", \"a h:mm:ss z\", \"a h:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{0} पेठ {1}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"INR\", \"₹\", \"इंडियन रूपी\", {\n  \"JPY\": [\"JP¥\", \"¥\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ks-Deva.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"ks-Deva\", [[\"AM\", \"PM\"], u, u], u, [[\"अ\", \"च\", \"ब\", \"ब\", \"ब\", \"ज\", \"ब\"], [\"आथवार\", \"चंदिरवार\", \"बुवार\", \"बोदवार\", \"ब्रेसवार\", \"जुम्मा\", \"बटवार\"], u, u], u, [[\"ज\", \"फ़\", \"म\", \"अ\", \"म\", \"ज\", \"ज\", \"अ\", \"स\", \"ओ\", \"न\", \"द\"], [\"जनवरी\", \"फ़रवरी\", \"मार्च\", \"अप्रैल\", \"मे\", \"जून\", \"जुलाई\", \"अगस्त\", \"सतुंबर\", \"अकतुम्बर\", \"नवूमबर\", \"दसूमबर\"], u], u, [[\"BC\", \"AD\"], u, u], 0, [0, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"a h:mm\", \"a h:mm:ss\", \"a h:mm:ss z\", \"a h:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{0} पेठ {1}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"INR\", \"₹\", \"इंडियन रूपी\", { \"JPY\": [\"JP¥\", \"¥\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,aAAa,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}