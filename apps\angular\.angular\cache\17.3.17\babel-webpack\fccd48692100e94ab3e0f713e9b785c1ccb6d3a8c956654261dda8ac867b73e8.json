{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { inject, InjectionToken, Injectable, Inject, NgModule, Component, ViewEncapsulation, Input, Directive, Optional, Pipe, Injector, input, EventEmitter, Output, effect, PLATFORM_ID, TemplateRef, ContentChild, SecurityContext, HostListener, APP_INITIALIZER, SkipSelf, ElementRef } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { Router, RouterModule, NavigationEnd, NavigationError, NavigationCancel } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { CommonModule, isPlatformBrowser, DOCUMENT } from '@angular/common';\nimport { map, distinctUntilChanged, filter, take, tap, switchMap, startWith, distinctUntilKeyChanged } from 'rxjs/operators';\nimport { BehaviorSubject, Subject, Observable, from, of, EMPTY, filter as filter$1, map as map$1, combineLatest, fromEvent, Subscription } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport PerfectScrollbar from 'perfect-scrollbar';\nimport { DomSanitizer } from '@angular/platform-browser';\nfunction _forTrack0($index, $item) {\n  return this.item.children;\n}\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction SubNavbarComponent_Conditional_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SubNavbarComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SubNavbarComponent_Conditional_0_ng_container_0_Template, 1, 0, \"ng-container\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngComponentOutlet\", ctx_r0.item.component)(\"ngComponentOutletInjector\", ctx_r0.injector);\n  }\n}\nfunction SubNavbarComponent_Conditional_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SubNavbarComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SubNavbarComponent_Conditional_1_ng_container_0_Template, 1, 0, \"ng-container\", 3);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const defaultTemplate_r2 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", defaultTemplate_r2);\n  }\n}\nfunction SubNavbarComponent_ng_template_2_lpx_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lpx-icon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"iconClass\", ctx_r0.item.icon);\n  }\n}\nfunction SubNavbarComponent_ng_template_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SubNavbarComponent_ng_template_2_ng_template_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"lpxTranslate\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, i0.ɵɵpipeBind1(2, 1, ctx_r0.item.text)));\n  }\n}\nfunction SubNavbarComponent_ng_template_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SubNavbarComponent_ng_template_2_ng_template_3_Conditional_0_Template, 4, 5, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r0.item.text ? 0 : -1);\n  }\n}\nfunction SubNavbarComponent_ng_template_2_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lpx-icon\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"iconClass\", ctx_r0.item.expanded ? \"chevronUp\" : \"chevronDown\");\n  }\n}\nfunction SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 12)(1, \"lpx-sub-navbar\", 14);\n    i0.ɵɵlistener(\"routeClick\", function SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template_lpx_sub_navbar_routeClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.routeClick.emit($event));\n    })(\"expand\", function SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template_lpx_sub_navbar_expand_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.onChildExpand($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const child_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", child_r5);\n  }\n}\nfunction SubNavbarComponent_ng_template_2_Conditional_6_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template, 2, 1, \"li\", 13);\n  }\n  if (rf & 2) {\n    const child_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"lpxVisible\", !child_r5.visible || child_r5.visible(child_r5, ctx_r0.injector));\n  }\n}\nfunction SubNavbarComponent_ng_template_2_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 11);\n    i0.ɵɵrepeaterCreate(1, SubNavbarComponent_ng_template_2_Conditional_6_For_2_Template, 1, 1, \"li\", 12, _forTrack0, true);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"collapsed\", !ctx_r0.item.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.item.children);\n  }\n}\nfunction SubNavbarComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 4);\n    i0.ɵɵlistener(\"click\", function SubNavbarComponent_ng_template_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onItemClick(ctx_r0.item));\n    });\n    i0.ɵɵtemplate(1, SubNavbarComponent_ng_template_2_lpx_icon_1_Template, 1, 1, \"lpx-icon\", 5)(2, SubNavbarComponent_ng_template_2_ng_container_2_Template, 1, 0, \"ng-container\", 6)(3, SubNavbarComponent_ng_template_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, SubNavbarComponent_ng_template_2_Conditional_5_Template, 1, 1, \"lpx-icon\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SubNavbarComponent_ng_template_2_Conditional_6_Template, 3, 2, \"ul\", 8);\n  }\n  if (rf & 2) {\n    const textTmpl_r6 = i0.ɵɵreference(4);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r0.item.selected)(\"expanded\", (ctx_r0.item.children == null ? null : ctx_r0.item.children.length) && ctx_r0.item.expanded);\n    i0.ɵɵproperty(\"routerLink\", ctx_r0.item.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.item.template || textTmpl_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c0, ctx_r0.item));\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(5, (ctx_r0.item.children == null ? null : ctx_r0.item.children.length) ? 5 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(6, (ctx_r0.item.children == null ? null : ctx_r0.item.children.length) ? 6 : -1);\n  }\n}\nfunction NavbarRoutesComponent_Conditional_1_For_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarRoutesComponent_Conditional_1_For_1_For_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarRoutesComponent_Conditional_1_For_1_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarRoutesComponent_Conditional_1_For_1_For_2_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n  }\n  if (rf & 2) {\n    const navbarItem_r1 = ctx.$implicit;\n    i0.ɵɵnextContext(3);\n    const itemTemplate_r2 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", itemTemplate_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, navbarItem_r1));\n  }\n}\nfunction NavbarRoutesComponent_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarRoutesComponent_Conditional_1_For_1_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵrepeaterCreate(1, NavbarRoutesComponent_Conditional_1_For_1_For_2_Template, 1, 4, \"ng-container\", null, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const groupText_r4 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", groupText_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(item_r3.items);\n  }\n}\nfunction NavbarRoutesComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NavbarRoutesComponent_Conditional_1_For_1_Template, 3, 4, null, null, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r4.groupedItems);\n  }\n}\nfunction NavbarRoutesComponent_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarRoutesComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarRoutesComponent_Conditional_2_ng_container_0_Template, 1, 0, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const defaultRoute_r6 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", defaultRoute_r6);\n  }\n}\nfunction NavbarRoutesComponent_ng_template_3_For_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarRoutesComponent_ng_template_3_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarRoutesComponent_ng_template_3_For_1_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const itemTemplate_r2 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", itemTemplate_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, item_r7));\n  }\n}\nfunction NavbarRoutesComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NavbarRoutesComponent_ng_template_3_For_1_Template, 1, 4, \"ng-container\", null, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r4.navbarItems);\n  }\n}\nfunction NavbarRoutesComponent_ng_template_5_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"lpxTranslate\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, i0.ɵɵpipeBind1(2, 1, item_r8.group)), \" \");\n  }\n}\nfunction NavbarRoutesComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarRoutesComponent_ng_template_5_Conditional_0_Template, 4, 5, \"li\", 6);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵconditional(0, item_r8.items.length ? 0 : -1);\n  }\n}\nfunction NavbarRoutesComponent_ng_template_7_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8)(1, \"lpx-sub-navbar\", 9);\n    i0.ɵɵlistener(\"expand\", function NavbarRoutesComponent_ng_template_7_li_0_Template_lpx_sub_navbar_expand_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSubnavbarExpand($event, ctx_r4.navbarItems));\n    })(\"routeClick\", function NavbarRoutesComponent_ng_template_7_li_0_Template_lpx_sub_navbar_routeClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onRouteClick($event, ctx_r4.navbarItems));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", item_r10)(\"routerItem\", ctx_r4.routerItem());\n  }\n}\nfunction NavbarRoutesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarRoutesComponent_ng_template_7_li_0_Template, 2, 2, \"li\", 7);\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"lpxVisible\", !item_r10.visible || item_r10.visible(item_r10, ctx_r4.injector));\n  }\n}\nconst _c1 = (a0, a1) => ({\n  $implicit: a0,\n  groupItems: a1\n});\nfunction NavbarComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarComponent_Conditional_4_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarComponent_Conditional_4_ng_container_0_Template, 1, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const customContentTemplate_r3 = i0.ɵɵreference(13);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", customContentTemplate_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx_r1.contentBefore));\n  }\n}\nfunction NavbarComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lpx-navbar-routes\", 8);\n  }\n  if (rf & 2) {\n    const items_r4 = ctx.$implicit;\n    const groupItems_r5 = ctx.groupItems;\n    i0.ɵɵproperty(\"navbarItems\", items_r4)(\"groupedItems\", groupItems_r5)(\"routerItem\", true);\n  }\n}\nfunction NavbarComponent_ng_template_12_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NavbarComponent_ng_template_12_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarComponent_ng_template_12_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const component_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngComponentOutlet\", component_r6)(\"ngComponentOutletInjector\", ctx_r1.injector);\n  }\n}\nfunction NavbarComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarComponent_ng_template_12_ng_container_0_Template, 2, 2, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const contents_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngForOf\", contents_r7);\n  }\n}\nfunction NavbarComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lpx-brand-logo\");\n  }\n}\nfunction BreadcrumbComponent_ng_container_2_lpx_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lpx-icon\", 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"iconClass\", item_r2.icon);\n  }\n}\nfunction BreadcrumbComponent_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction BreadcrumbComponent_ng_container_2_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 10);\n    i0.ɵɵelement(1, \"lpx-icon\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BreadcrumbComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 5);\n    i0.ɵɵlistener(\"click\", function BreadcrumbComponent_ng_container_2_Template_li_click_1_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClick(item_r2));\n    })(\"lpxClickOutside\", function BreadcrumbComponent_ng_container_2_Template_li_lpxClickOutside_1_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      return i0.ɵɵresetView(item_r2.expanded = false);\n    });\n    i0.ɵɵtemplate(2, BreadcrumbComponent_ng_container_2_lpx_icon_2_Template, 1, 1, \"lpx-icon\", 6)(3, BreadcrumbComponent_ng_container_2_ng_container_3_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BreadcrumbComponent_ng_container_2_li_4_Template, 2, 0, \"li\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵnextContext();\n    const linkTemplate_r5 = i0.ɵɵreference(5);\n    const textTemplate_r6 = i0.ɵɵreference(7);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", item_r2.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", (item_r2.children == null ? null : item_r2.children.length) ? textTemplate_r6 : linkTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c0, item_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n  }\n}\nfunction BreadcrumbComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"toObservable\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r7.link);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, i0.ɵɵpipeBind1(2, 2, item_r7.text)), \" \");\n  }\n}\nfunction BreadcrumbComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"toObservable\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, i0.ɵɵpipeBind1(2, 1, item_r8.text)), \" \");\n  }\n}\nfunction AvatarComponent_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"lpx-icon\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconClass\", ctx_r0.avatar.source);\n  }\n}\nfunction AvatarComponent_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.avatar.source, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AvatarComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementContainerStart(1, 2);\n    i0.ɵɵtemplate(2, AvatarComponent_div_0_ng_container_2_Template, 2, 1, \"ng-container\", 3)(3, AvatarComponent_div_0_ng_container_3_Template, 2, 1, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r0.avatar.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"icon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"image\");\n  }\n}\nconst _c2 = a0 => [a0];\nfunction FooterComponent_ng_container_0_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const footerValues_r1 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c2, footerValues_r1.descUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", footerValues_r1.desc, \"\");\n  }\n}\nfunction FooterComponent_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const footerValues_r1 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", footerValues_r1.desc, \"\");\n  }\n}\nfunction FooterComponent_ng_container_0_ng_container_7_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const footerLink_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c2, footerLink_r2.link));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(footerLink_r2.text);\n  }\n}\nfunction FooterComponent_ng_container_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FooterComponent_ng_container_0_ng_container_7_a_1_Template, 2, 4, \"a\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const footerLink_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", footerLink_r2);\n  }\n}\nfunction FooterComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵtemplate(3, FooterComponent_ng_container_0_a_3_Template, 2, 4, \"a\", 4)(4, FooterComponent_ng_container_0_ng_template_4_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 5);\n    i0.ɵɵtemplate(7, FooterComponent_ng_container_0_ng_container_7_Template, 2, 1, \"ng-container\", 6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const footerValues_r1 = ctx.ngIf;\n    const footerDesc_r3 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", footerValues_r1.descUrl)(\"ngIfElse\", footerDesc_r3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", footerValues_r1.footerLinks);\n  }\n}\nclass LpxPerfectScrollbar {\n  constructor() {\n    this.router = inject(Router);\n  }\n}\nconst LPX_LANGUAGE = new InjectionToken('LPX_LANGUAGE');\nclass DataStore {\n  get state() {\n    return this.state$.value;\n  }\n  constructor(initialState) {\n    this.initialState = initialState;\n    this.state$ = new BehaviorSubject(this.initialState);\n    this.update$ = new Subject();\n    this.sliceState = (selector, compareFn = (s1, s2) => s1 === s2) => this.state$.pipe(map(selector), distinctUntilChanged(compareFn));\n    this.sliceUpdate = (selector, filterFn = x => x !== undefined) => this.update$.pipe(map(selector), filter(filterFn));\n  }\n  patch(state) {\n    let patchedState = state;\n    if (typeof state === 'object' && !Array.isArray(state)) {\n      patchedState = {\n        ...this.state,\n        ...state\n      };\n    }\n    this.state$.next(patchedState);\n    this.update$.next(patchedState);\n  }\n  set(state) {\n    this.state$.next(state);\n    this.update$.next(state);\n  }\n  reset() {\n    this.set(this.initialState);\n  }\n}\nvar LanguageTranslateKeys;\n(function (LanguageTranslateKeys) {\n  LanguageTranslateKeys[\"SettingsTitle\"] = \"language.settings.title\";\n})(LanguageTranslateKeys || (LanguageTranslateKeys = {}));\nconst LanguageTranslateDefaults = {\n  [LanguageTranslateKeys.SettingsTitle]: 'Language Options'\n};\nclass LanguageService {\n  get selectedLanguage() {\n    return this.store.state.selectedLanguage;\n  }\n  constructor(languages) {\n    this.languages = languages;\n    this.store = new DataStore({\n      languages: []\n    });\n    this.id = 'languages';\n    this.convertLanguageToNavbarItem = languages => {\n      return languages.map(lang => ({\n        icon: '',\n        text: lang.displayName,\n        selected: lang.selected,\n        action: () => {\n          this.setSelectedLanguage(lang);\n          return true;\n        }\n      }));\n    };\n    this.selectedLanguage$ = this.store.sliceState(({\n      selectedLanguage\n    }) => selectedLanguage);\n    this.languageChange$ = this.selectedLanguage$.pipe(\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    filter(lang => lang !== undefined), distinctUntilChanged((a, b) => a?.cultureName === b?.cultureName));\n    this.languages$ = this.store.sliceState(state => state.languages);\n    this.languagesAsNavbarItems$ = this.languages$.pipe(map(this.convertLanguageToNavbarItem));\n    //TODO: PROVIDE API\n    this.languagesAsSettingsGroup$ = this.languagesAsNavbarItems$.pipe(map(languages => ({\n      text: LanguageTranslateKeys.SettingsTitle,\n      icon: 'bi bi-globe',\n      id: this.id,\n      children: languages\n    })));\n    this.init(this.languages);\n  }\n  setLanguages(languages) {\n    this.init(languages);\n  }\n  init(languages) {\n    this.store.patch({\n      languages,\n      selectedLanguage: languages.find(lang => lang.selected)\n    });\n  }\n  setSelectedLanguage(lang) {\n    this.store.patch({\n      selectedLanguage: lang\n    });\n  }\n  static {\n    this.ɵfac = function LanguageService_Factory(t) {\n      return new (t || LanguageService)(i0.ɵɵinject(LPX_LANGUAGE));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LanguageService,\n      factory: LanguageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LanguageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LPX_LANGUAGE]\n    }]\n  }], null);\n})();\nconst LPX_TRANSLATE_SERVICE_TOKEN = new InjectionToken('LPX_TRANSLATE_SERVICE_TOKEN');\nconst LPX_TRANSLATE_TOKEN = new InjectionToken('LPX_TRANSLATE_TOKEN');\nclass LpxLanguageModule {\n  static forRoot(options) {\n    return {\n      ngModule: LpxLanguageModule,\n      providers: [{\n        provide: LPX_LANGUAGE,\n        useValue: options?.languages || []\n      }, {\n        provide: LPX_TRANSLATE_TOKEN,\n        useValue: [LanguageTranslateDefaults],\n        multi: true\n      }, LanguageService]\n    };\n  }\n  static {\n    this.ɵfac = function LpxLanguageModule_Factory(t) {\n      return new (t || LpxLanguageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxLanguageModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxLanguageModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\nconst ICON_MAP = {\n  bagFill: 'bi bi-bag-fill',\n  bellFill: 'bi bi-bell-fill',\n  calendarWeek: 'bi bi-calendar2-week',\n  chatDots: 'bi bi-chat-dots',\n  chevronDown: 'bi bi-chevron-down',\n  chevronUp: 'bi bi-chevron-up',\n  gearConnected: 'bi bi-gear-wide-connected',\n  filter: 'bi bi-filter',\n  filterFill: 'bi bi-filter-circle-fill',\n  layoutThreeColumns: 'bi bi-layout-three-columns',\n  moon: 'bi bi-moon',\n  square: 'bi bi-square',\n  sunset: 'bi bi-brightness-alt-high-fill',\n  sunup: 'bi bi-brightness-high-fill',\n  star: 'bi bi-star',\n  x: 'bi bi-x',\n  xCircleFill: 'bi bi-x-circle-fill'\n};\nconst LEPTON_X_ICON_SET = new InjectionToken('LEPTON_X_ICON_SET');\nclass IconComponent {\n  get styleClass() {\n    return this.iconSet[this.iconClass] || this.iconClass;\n  }\n  constructor(iconSet) {\n    this.iconSet = iconSet;\n  }\n  static {\n    this.ɵfac = function IconComponent_Factory(t) {\n      return new (t || IconComponent)(i0.ɵɵdirectiveInject(LEPTON_X_ICON_SET));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: IconComponent,\n      selectors: [[\"lpx-icon\"]],\n      inputs: {\n        iconClass: \"iconClass\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"aria-hidden\", \"true\", 1, \"lpx-icon\", 3, \"ngClass\"]],\n      template: function IconComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"i\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.styleClass);\n        }\n      },\n      dependencies: [i1.NgClass],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-icon',\n      template: ` <i class=\"lpx-icon\" [ngClass]=\"styleClass\" aria-hidden=\"true\"></i> `,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LEPTON_X_ICON_SET]\n    }]\n  }], {\n    iconClass: [{\n      type: Input\n    }]\n  });\n})();\nclass LpxIconModule {\n  static forRoot(options) {\n    return {\n      ngModule: LpxIconModule,\n      providers: [{\n        provide: LEPTON_X_ICON_SET,\n        useValue: options?.iconSet || ICON_MAP\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function LpxIconModule_Factory(t) {\n      return new (t || LpxIconModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxIconModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxIconModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [IconComponent],\n      imports: [CommonModule],\n      exports: [IconComponent]\n    }]\n  }], null, null);\n})();\nclass BrandLogoComponent {\n  static {\n    this.ɵfac = function BrandLogoComponent_Factory(t) {\n      return new (t || BrandLogoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: BrandLogoComponent,\n      selectors: [[\"lpx-brand-logo\"]],\n      decls: 2,\n      vars: 0,\n      consts: [[\"routerLink\", \"/\"], [1, \"lpx-brand-logo\"]],\n      template: function BrandLogoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"a\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i2.RouterLink],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrandLogoComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-brand-logo',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<a routerLink=\\\"/\\\">\\r\\n  <div class=\\\"lpx-brand-logo\\\"></div>\\r\\n</a>\\r\\n\"\n    }]\n  }], null, null);\n})();\nclass LpxBrandLogoModule {\n  static {\n    this.ɵfac = function LpxBrandLogoModule_Factory(t) {\n      return new (t || LpxBrandLogoModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxBrandLogoModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [RouterModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxBrandLogoModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [BrandLogoComponent],\n      imports: [RouterModule],\n      exports: [BrandLogoComponent]\n    }]\n  }], null, null);\n})();\nclass LayoutService {\n  constructor() {\n    this.store = new DataStore({\n      containerClass: ['']\n    });\n    this.containerClass$ = this.store.sliceState(({\n      containerClass\n    }) => containerClass || []);\n  }\n  setClass(cssClass) {\n    const containerClass = Array.isArray(cssClass) ? cssClass : [cssClass];\n    this.patchStore(containerClass);\n  }\n  addClass(cssClass) {\n    const {\n      containerClass\n    } = this.store.state;\n    this.patchStore([...containerClass, cssClass]);\n  }\n  removeClass(cssClass) {\n    const {\n      containerClass\n    } = this.store.state;\n    const index = containerClass.findIndex(item => item === cssClass);\n    if (index === -1) return;\n    const update = [...containerClass.slice(0, index), ...containerClass.slice(index + 1)];\n    this.patchStore(update);\n  }\n  removeClasses(classlist) {\n    const {\n      containerClass\n    } = this.store.state;\n    const filteredClasslist = containerClass.filter(clss => !classlist.includes(clss));\n    this.patchStore(filteredClasslist);\n  }\n  toggleClass(cssClass) {\n    const {\n      containerClass\n    } = this.store.state;\n    const index = containerClass.findIndex(item => item === cssClass);\n    if (index === -1) {\n      this.addClass(cssClass);\n    } else {\n      this.removeClass(cssClass);\n    }\n  }\n  patchStore(containerClass) {\n    this.store.patch({\n      containerClass\n    });\n  }\n  static {\n    this.ɵfac = function LayoutService_Factory(t) {\n      return new (t || LayoutService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LayoutService,\n      factory: LayoutService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst CONTENT_BEFORE_ROUTES = new InjectionToken('CONTENT_BEFORE_ROUTES');\nconst CONTENT_AFTER_ROUTES = new InjectionToken('CONTENT_AFTER_ROUTES');\nconst LPX_MENU_ITEMS = new InjectionToken('LPX_MENU_ITEMS');\nfunction sortItems(a, b) {\n  if (!a.order) {\n    return 1;\n  }\n  if (!b.order) {\n    return -1;\n  }\n  return a.order - b.order;\n}\nfunction flatArrayDeepToObject(arr) {\n  return arr.reduce((acc, curr) => ({\n    ...acc,\n    ...(Array.isArray(curr) ? flatArrayDeepToObject(curr) : curr)\n  }), {});\n}\nfunction getStream$(source) {\n  return source instanceof Observable ? source : source instanceof Promise ? from(source) : of(source);\n}\nfunction isNullOrUndefined(obj) {\n  return obj === null || obj === undefined;\n}\nfunction isArray(obj) {\n  return Array.isArray(obj);\n}\nfunction createGroupMap(list, othersGroupKey, skipGroupCheck = false) {\n  if (!skipGroupCheck && (!isArray(list) || !list.some(node => Boolean(node.group)))) return undefined;\n  const mapGroup = new Map();\n  for (const node of list) {\n    const group = node?.group || othersGroupKey;\n    if (typeof group !== 'string') {\n      throw new Error(`Invalid group: ${group}`);\n    }\n    const items = mapGroup.get(group) || [];\n    items.push(node);\n    mapGroup.set(group, items);\n  }\n  return mapGroup;\n}\nfunction getItemsFromGroup(list, pred) {\n  return list?.reduce((acc, {\n    items\n  }) => [...acc, ...(pred ? items.filter(pred) : items)], []);\n}\nconst OTHERS_GROUP_KEY = 'AbpUi::OthersGroup';\nclass NavbarService {\n  constructor() {\n    this.router = inject(Router);\n    this.menuItems = inject(LPX_MENU_ITEMS);\n    this.store = new DataStore(this.addContainerLinks(this.menuItems));\n    this.navbarItems$ = this.store.sliceState(state => state);\n    this.groupedNavbarItems$ = this.store.sliceState(state => state).pipe(filter(navItems => navItems.some(f => !!f.group)), map(items => {\n      const map = createGroupMap(items, OTHERS_GROUP_KEY) || [];\n      return Array.from(map, ([group, items]) => ({\n        group,\n        items\n      }));\n    }));\n    this.expandItemByLink$().pipe(take(1)).subscribe();\n  }\n  addNavbarItems(...menuItems) {\n    this.store.set([...this.store.state, ...this.addContainerLinks(menuItems)]);\n  }\n  setNavbarItems(...menuItems) {\n    this.store.set([...this.addContainerLinks(menuItems)]);\n  }\n  // TODO: muhammed: refactor this method to be readable\n  addChildren(id, ...menuItems) {\n    const parent = this.findById(id, this.store.state);\n    const update = (items, location, link = '') => {\n      const i = location.shift();\n      return items.reduce((acc, item, index) => {\n        return [...acc, ...(index === i ? [{\n          ...item,\n          children: !location.length ? [...(item.children || []), ...this.addContainerLinks(menuItems, `${link}/${item.containerLink}`)] : update(item.children || [], location, `${link}/${item.containerLink}`)\n        }] : [item])];\n      }, []);\n    };\n    const updated = update(this.store.state, parent.location);\n    this.store.patch(updated);\n  }\n  findByLink(link, items) {\n    return this.findByProp('link', link, items);\n  }\n  expandItemByLink$() {\n    return this.router.events.pipe(filter(e => e instanceof NavigationEnd), tap(() => this.expandItems()));\n  }\n  expandItems() {\n    const route = this.getRouteItem();\n    if (route?.item) {\n      const expanded = this.calculateExpandState(this.store.state, route.location);\n      this.store.patch(expanded);\n    }\n  }\n  getRouteItem() {\n    return this.findByLink(this.router.url);\n  }\n  calculateExpandState(items, indexes) {\n    const matchIndex = indexes.shift();\n    return items.reduce((acc, item, index) => {\n      if (index === matchIndex) {\n        return [...acc, {\n          ...item,\n          expanded: true,\n          selected: true,\n          children: this.calculateExpandState(item.children || [], indexes)\n        }];\n      }\n      const newItem = {\n        ...item,\n        ...(item.children ? {\n          children: this.collapseChildren(item.children)\n        } : {})\n      };\n      return [...acc, {\n        ...newItem,\n        expanded: false,\n        selected: false\n      }];\n    }, []);\n  }\n  collapseChildren(children) {\n    return [...children.map(child => ({\n      ...child,\n      expanded: false,\n      selected: false,\n      children: child.children ? this.collapseChildren(child.children) : []\n    }))];\n  }\n  findById(id, items) {\n    return this.findByProp('id', id, items);\n  }\n  findByProp(prop, value, items, location = []) {\n    const navbarItems = items || this.store.state;\n    const itemIndex = navbarItems.findIndex(i => i[prop] === value);\n    let item;\n    if (itemIndex === -1) {\n      navbarItems.forEach((i, index) => {\n        if (i.children) {\n          const child = this.findByProp(prop, value, i.children, [...location, index]);\n          if (child?.item) {\n            item = child.item;\n            location = child.location;\n          }\n        }\n      });\n    } else {\n      item = navbarItems[itemIndex];\n      location.push(itemIndex);\n    }\n    return {\n      item,\n      location\n    };\n  }\n  addContainerLinks(items, link = '') {\n    return items.map(item => ({\n      ...item,\n      ...(item.link && link ? {\n        link: `${link}/${item.link}`\n      } : {}),\n      children: this.addContainerLinks(item.children || [], `${link ? link + '/' : ''}${item.containerLink || ''}`)\n    }));\n  }\n  static {\n    this.ɵfac = function NavbarService_Factory(t) {\n      return new (t || NavbarService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NavbarService,\n      factory: NavbarService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavbarService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass NavbarRoutesDirective {\n  static {\n    this.ɵfac = function NavbarRoutesDirective_Factory(t) {\n      return new (t || NavbarRoutesDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NavbarRoutesDirective,\n      selectors: [[\"\", \"lpx-navbar-routes\", \"\"], [\"\", \"lpxNavbarRoutes\", \"\"]],\n      exportAs: [\"lpxNavbarRoutes\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavbarRoutesDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[lpx-navbar-routes],[lpxNavbarRoutes]',\n      exportAs: 'lpxNavbarRoutes'\n    }]\n  }], null, null);\n})();\nclass LogoPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function LogoPanelDirective_Factory(t) {\n      return new (t || LogoPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LogoPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-logo-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LogoPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-logo-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass RoutesService {\n  constructor() {\n    this.router = inject(Router);\n    this.currentNavigation = toSignal(this.router.events.pipe(filter(e => e instanceof NavigationEnd),\n    //TODO: location object might be problem in the future for SSR\n    map(() => location.pathname)), {\n      initialValue: location.pathname\n    });\n  }\n  static {\n    this.ɵfac = function RoutesService_Factory(t) {\n      return new (t || RoutesService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RoutesService,\n      factory: RoutesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutesService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass LpxVisibleDirective {\n  set lpxVisible(value) {\n    this.condition$ = checkType(value);\n    this.subscribeToCondition();\n  }\n  constructor(viewContainerRef, templateRef) {\n    this.viewContainerRef = viewContainerRef;\n    this.templateRef = templateRef;\n    this.condition$ = of(false);\n  }\n  ngOnInit() {\n    this.updateVisibility();\n  }\n  ngOnDestroy() {\n    this.conditionSubscription?.unsubscribe();\n  }\n  subscribeToCondition() {\n    this.conditionSubscription = this.condition$.subscribe(value => {\n      this.isVisible = value;\n      this.updateVisibility();\n    });\n  }\n  updateVisibility() {\n    this.viewContainerRef.clear();\n    // it should be false not falsy\n    if (this.isVisible === false) {\n      return;\n    }\n    this.viewContainerRef.createEmbeddedView(this.templateRef);\n  }\n  static {\n    this.ɵfac = function LpxVisibleDirective_Factory(t) {\n      return new (t || LpxVisibleDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LpxVisibleDirective,\n      selectors: [[\"\", \"lpxVisible\", \"\"]],\n      inputs: {\n        lpxVisible: \"lpxVisible\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxVisibleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[lpxVisible]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    lpxVisible: [{\n      type: Input,\n      args: ['lpxVisible']\n    }]\n  });\n})();\nfunction checkType(value) {\n  if (value instanceof Promise) {\n    return from(value);\n  } else if (value instanceof Observable) {\n    return value;\n  } else if (typeof value === 'boolean') {\n    return of(value);\n  } else if (value === undefined || value === null) {\n    return of(true);\n  } else {\n    return EMPTY;\n  }\n}\nclass UserProfileService {\n  constructor() {\n    this.store = new DataStore({});\n    this.user$ = this.store.sliceState(state => state);\n  }\n  setUser(user) {\n    this.store.set(user);\n  }\n  patchUser(user) {\n    this.store.patch(user);\n  }\n  static {\n    this.ɵfac = function UserProfileService_Factory(t) {\n      return new (t || UserProfileService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UserProfileService,\n      factory: UserProfileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UserProfileService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass BodyService {\n  constructor() {\n    this.body = document.querySelector('body');\n    this.classes = {\n      overflowYHidden: 'overflow-y-hidden'\n    };\n  }\n  disableScrollY() {\n    this.body?.classList.add(this.classes.overflowYHidden);\n  }\n  enableScrollY() {\n    this.body?.classList.remove(this.classes.overflowYHidden);\n  }\n  static {\n    this.ɵfac = function BodyService_Factory(t) {\n      return new (t || BodyService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BodyService,\n      factory: BodyService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BodyService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass DefaultTranslateService {\n  get$(key, defaultValue) {\n    return of(defaultValue || key || '');\n  }\n  get(key, defaultValue) {\n    return defaultValue || key || '';\n  }\n  static {\n    this.ɵfac = function DefaultTranslateService_Factory(t) {\n      return new (t || DefaultTranslateService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DefaultTranslateService,\n      factory: DefaultTranslateService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultTranslateService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass LpxThemeTranslateService {\n  constructor(translateValues, translateService) {\n    this.translateValues = translateValues;\n    this.translateService = translateService;\n    this._content = flatArrayDeepToObject(this.translateValues);\n  }\n  // TODO: PROVIDE API : Implement args\n  translate$(key, ...args) {\n    return this.translateService.get$(key, this._content[key]);\n  }\n  static {\n    this.ɵfac = function LpxThemeTranslateService_Factory(t) {\n      return new (t || LpxThemeTranslateService)(i0.ɵɵinject(LPX_TRANSLATE_TOKEN, 8), i0.ɵɵinject(LPX_TRANSLATE_SERVICE_TOKEN));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LpxThemeTranslateService,\n      factory: LpxThemeTranslateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxThemeTranslateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: Array,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LPX_TRANSLATE_TOKEN]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LPX_TRANSLATE_SERVICE_TOKEN]\n    }]\n  }], null);\n})();\nclass DefaultAuthService {\n  constructor(userProfileService) {\n    this.userProfileService = userProfileService;\n    this.isUserExists$ = this.userProfileService.user$.pipe(map(user => !!user && Object.keys(user).length > 0));\n  }\n  navigateToLogin() {\n    return;\n  }\n  static {\n    this.ɵfac = function DefaultAuthService_Factory(t) {\n      return new (t || DefaultAuthService)(i0.ɵɵinject(UserProfileService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DefaultAuthService,\n      factory: DefaultAuthService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultAuthService, [{\n    type: Injectable\n  }], () => [{\n    type: UserProfileService\n  }], null);\n})();\nconst LPX_AUTH_SERVICE_TOKEN = new InjectionToken('LPX_AUTH_SERVICE_TOKEN');\nconst LPX_AUTH_SERVICE_PROVIDER = {\n  provide: LPX_AUTH_SERVICE_TOKEN,\n  useClass: DefaultAuthService\n};\nclass LpxLocalStorageService {\n  constructor() {}\n  get length() {\n    return localStorage.length;\n  }\n  clear() {\n    localStorage.clear();\n  }\n  getItem(key) {\n    return localStorage.getItem(key);\n  }\n  key(index) {\n    return localStorage.key(index);\n  }\n  removeItem(key) {\n    localStorage.removeItem(key);\n  }\n  setItem(key, value) {\n    localStorage.setItem(key, value);\n  }\n  static {\n    this.ɵfac = function LpxLocalStorageService_Factory(t) {\n      return new (t || LpxLocalStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LpxLocalStorageService,\n      factory: LpxLocalStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxLocalStorageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass LpxPerfectScrollbarService extends LpxPerfectScrollbar {\n  setElement(value) {\n    if (value) {\n      this.elementRef = value;\n    }\n  }\n  setOptions(value) {\n    if (value) {\n      this.options = value;\n    }\n  }\n  createScrollbar() {\n    this.perfectScrollbar = new PerfectScrollbar(this.elementRef.nativeElement, this.options);\n  }\n  onResize() {\n    this.perfectScrollbar.update();\n  }\n  afterViewInit() {\n    this.createScrollbar();\n    this.subscription?.unsubscribe();\n    this.subscription = this.router.events.pipe(filter$1(event => event instanceof NavigationEnd || event instanceof NavigationError || event instanceof NavigationCancel)).subscribe(() => {\n      const {\n        element\n      } = this.perfectScrollbar;\n      const {\n        topAfterNavigate,\n        leftAfterNavigate\n      } = this.options || {};\n      element.scrollTop = topAfterNavigate || 0;\n      element.scrollLeft = leftAfterNavigate || 0;\n    });\n  }\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵLpxPerfectScrollbarService_BaseFactory;\n      return function LpxPerfectScrollbarService_Factory(t) {\n        return (ɵLpxPerfectScrollbarService_BaseFactory || (ɵLpxPerfectScrollbarService_BaseFactory = i0.ɵɵgetInheritedFactory(LpxPerfectScrollbarService)))(t || LpxPerfectScrollbarService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LpxPerfectScrollbarService,\n      factory: LpxPerfectScrollbarService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxPerfectScrollbarService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslatePipe {\n  constructor(lpxThemeTranslateService) {\n    this.lpxThemeTranslateService = lpxThemeTranslateService;\n  }\n  transform(value, ...args) {\n    return this.lpxThemeTranslateService.translate$(value, args);\n  }\n  static {\n    this.ɵfac = function TranslatePipe_Factory(t) {\n      return new (t || TranslatePipe)(i0.ɵɵdirectiveInject(LpxThemeTranslateService, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"lpxTranslate\",\n      type: TranslatePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'lpxTranslate'\n    }]\n  }], () => [{\n    type: LpxThemeTranslateService\n  }], null);\n})();\nclass SubNavbarComponent {\n  constructor() {\n    this.injector = inject(Injector);\n    this.routerItem = input();\n    this.routeClick = new EventEmitter();\n    this.expand = new EventEmitter();\n  }\n  onItemClick(menuItem) {\n    let action$ = of(true);\n    if (menuItem.action) {\n      const result = menuItem.action();\n      action$ = getStream$(result);\n    }\n    action$.pipe(take(1)).subscribe(result => {\n      if (result) {\n        this.processItemClick(menuItem);\n      }\n    });\n  }\n  onChildExpand(child) {\n    if (child.expanded) {\n      this.item?.children?.filter(otherChild => otherChild !== child).forEach(otherChild => {\n        otherChild.expanded = false;\n        otherChild.selected = false;\n      });\n    }\n  }\n  processItemClick(menuItem) {\n    if (menuItem.children?.length) {\n      menuItem.expanded = !menuItem.expanded;\n      this.expand.emit(menuItem);\n      return;\n    }\n    this.routeClick.emit(menuItem);\n    if (!this.routerItem()) {\n      menuItem.selected = true;\n    }\n  }\n  static {\n    this.ɵfac = function SubNavbarComponent_Factory(t) {\n      return new (t || SubNavbarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SubNavbarComponent,\n      selectors: [[\"lpx-sub-navbar\"]],\n      inputs: {\n        item: \"item\",\n        routerItem: [i0.ɵɵInputFlags.SignalBased, \"routerItem\"]\n      },\n      outputs: {\n        routeClick: \"routeClick\",\n        expand: \"expand\"\n      },\n      decls: 4,\n      vars: 1,\n      consts: [[\"defaultTemplate\", \"\"], [\"textTmpl\", \"\"], [4, \"ngComponentOutlet\", \"ngComponentOutletInjector\"], [4, \"ngTemplateOutlet\"], [1, \"lpx-menu-item-link\", 3, \"click\", \"routerLink\"], [\"class\", \"lpx-menu-item-icon\", 3, \"iconClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"dd-icon\", \"hidden-in-hover-trigger\", 3, \"iconClass\"], [1, \"lpx-inner-menu\", \"hidden-in-hover-trigger\", 3, \"collapsed\"], [1, \"lpx-menu-item-icon\", 3, \"iconClass\"], [1, \"lpx-menu-item-text\", \"hidden-in-hover-trigger\"], [1, \"lpx-inner-menu\", \"hidden-in-hover-trigger\"], [1, \"lpx-inner-menu-item\"], [\"class\", \"lpx-inner-menu-item\", 4, \"lpxVisible\"], [3, \"routeClick\", \"expand\", \"item\"]],\n      template: function SubNavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SubNavbarComponent_Conditional_0_Template, 1, 2, \"ng-container\")(1, SubNavbarComponent_Conditional_1_Template, 1, 1)(2, SubNavbarComponent_ng_template_2_Template, 7, 12, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.item.component ? 0 : 1);\n        }\n      },\n      dependencies: [i1.NgComponentOutlet, i1.NgIf, i1.NgTemplateOutlet, i2.RouterLink, IconComponent, LpxVisibleDirective, SubNavbarComponent, i1.AsyncPipe, TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SubNavbarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-sub-navbar',\n      encapsulation: ViewEncapsulation.None,\n      template: \"@if (item.component) {\\r\\n  <ng-container\\r\\n    *ngComponentOutlet=\\\"item.component; injector: injector\\\"\\r\\n  ></ng-container>\\r\\n} @else {\\r\\n  <ng-container *ngTemplateOutlet=\\\"defaultTemplate\\\"></ng-container>\\r\\n}\\r\\n<ng-template #defaultTemplate>\\r\\n  <a\\r\\n    class=\\\"lpx-menu-item-link\\\"\\r\\n    [routerLink]=\\\"item.link\\\"\\r\\n    [class.selected]=\\\"item.selected\\\"\\r\\n    [class.expanded]=\\\"item.children?.length && item.expanded\\\"\\r\\n    (click)=\\\"onItemClick(item)\\\"\\r\\n  >\\r\\n    <lpx-icon\\r\\n      class=\\\"lpx-menu-item-icon\\\"\\r\\n      *ngIf=\\\"item.icon\\\"\\r\\n      [iconClass]=\\\"item.icon\\\"\\r\\n    ></lpx-icon>\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"\\r\\n        item.template || textTmpl;\\r\\n        context: { $implicit: item }\\r\\n      \\\"\\r\\n    ></ng-container>\\r\\n    <ng-template #textTmpl>\\r\\n      @if (item.text) {\\r\\n        <span class=\\\"lpx-menu-item-text hidden-in-hover-trigger\\\">{{\\r\\n          item.text | lpxTranslate | async\\r\\n        }}</span>\\r\\n      }\\r\\n    </ng-template>\\r\\n\\r\\n    @if (item.children?.length) {\\r\\n      <lpx-icon\\r\\n        [iconClass]=\\\"item.expanded ? 'chevronUp' : 'chevronDown'\\\"\\r\\n        class=\\\"dd-icon hidden-in-hover-trigger\\\"\\r\\n      >\\r\\n      </lpx-icon>\\r\\n    }\\r\\n  </a>\\r\\n\\r\\n  @if (item.children?.length) {\\r\\n    <ul\\r\\n      class=\\\"lpx-inner-menu hidden-in-hover-trigger\\\"\\r\\n      [class.collapsed]=\\\"!item.expanded\\\"\\r\\n    >\\r\\n      @for (child of item.children; track item.children) {\\r\\n        <li\\r\\n          class=\\\"lpx-inner-menu-item\\\"\\r\\n          *lpxVisible=\\\"!child.visible || child.visible(child, injector)\\\"\\r\\n        >\\r\\n          <lpx-sub-navbar\\r\\n            [item]=\\\"child\\\"\\r\\n            (routeClick)=\\\"this.routeClick.emit($event)\\\"\\r\\n            (expand)=\\\"onChildExpand($event)\\\"\\r\\n          ></lpx-sub-navbar>\\r\\n        </li>\\r\\n      }\\r\\n    </ul>\\r\\n  }\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], null, {\n    item: [{\n      type: Input\n    }],\n    routeClick: [{\n      type: Output\n    }],\n    expand: [{\n      type: Output\n    }]\n  });\n})();\nclass NavbarRoutesComponent {\n  get itemsFromGroup() {\n    if (!this.groupedItems) {\n      return undefined;\n    }\n    return getItemsFromGroup(this.groupedItems);\n  }\n  constructor() {\n    this.injector = inject(Injector);\n    this.routesService = inject(RoutesService);\n    this.routerItem = input();\n    this.routeClick = new EventEmitter();\n    this.isExpandedOrSelected = item => !!(item.expanded || item.selected);\n    this.fixNavbarItemsByRouter();\n  }\n  onSubnavbarExpand(menuItem, menuItems) {\n    if (menuItem.expanded) {\n      const items = this.itemsFromGroup || menuItems;\n      if (!items) {\n        return;\n      }\n      items.filter(item => item !== menuItem).forEach(item => item.expanded = false);\n    }\n  }\n  onRouteClick(menuItem, menuItems) {\n    const expandedItems = menuItems?.filter(this.isExpandedOrSelected);\n    const expandedGroupItems = this.itemsFromGroup?.filter(this.isExpandedOrSelected);\n    const items = expandedGroupItems || expandedItems;\n    if (items) {\n      items.filter(item => item !== menuItem).reduce((acc, item) => {\n        return [...acc, item, ...this.flatChildren(item.children || [])];\n      }, [])?.filter(item => !this.checkChildrenIncludesItem(item, menuItem) && item !== menuItem).forEach(item => {\n        item.selected = false;\n        item.expanded = false;\n      });\n    }\n    this.routeClick.emit(menuItem);\n  }\n  checkChildrenIncludesItem(item, menuItem) {\n    return item.children?.reduce((acc, child) => acc || child === menuItem || this.checkChildrenIncludesItem(child, menuItem), false) || false;\n  }\n  flatChildren(menuItems) {\n    return menuItems?.reduce((acc, item) => {\n      return [...acc, item, ...this.flatChildren(item.children || [])];\n    }, []) || [];\n  }\n  fixNavbarItemsByRouter() {\n    effect(() => {\n      const currentNavigation = this.routesService.currentNavigation();\n      if (!currentNavigation) {\n        return;\n      }\n      this.fixNavbarItems(currentNavigation, this.navbarItems);\n    });\n  }\n  fixNavbarItems(currentUrl, items) {\n    if (!items) {\n      return;\n    }\n    for (const item of items) {\n      if (item.children?.length) {\n        item.expanded = this.hasUrlInChildren(item, currentUrl);\n        this.fixNavbarItems(currentUrl, item.children);\n      } else {\n        item.selected = item.link === currentUrl;\n      }\n    }\n  }\n  hasUrlInChildren(item, url) {\n    if (item.link === url) {\n      return true;\n    }\n    if (item.children) {\n      for (const child of item.children) {\n        const found = this.hasUrlInChildren(child, url);\n        if (found) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function NavbarRoutesComponent_Factory(t) {\n      return new (t || NavbarRoutesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NavbarRoutesComponent,\n      selectors: [[\"lpx-navbar-routes\"]],\n      inputs: {\n        groupedItems: \"groupedItems\",\n        navbarItems: \"navbarItems\",\n        routerItem: [i0.ɵɵInputFlags.SignalBased, \"routerItem\"]\n      },\n      outputs: {\n        routeClick: \"routeClick\"\n      },\n      decls: 9,\n      vars: 1,\n      consts: [[\"defaultRoute\", \"\"], [\"groupText\", \"\"], [\"itemTemplate\", \"\"], [1, \"lpx-nav-menu\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [4, \"ngTemplateOutlet\"], [1, \"group-menu-item\", \"hidden-in-hover-trigger\"], [\"class\", \"outer-menu-item\", 4, \"lpxVisible\"], [1, \"outer-menu-item\"], [3, \"expand\", \"routeClick\", \"item\", \"routerItem\"]],\n      template: function NavbarRoutesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 3);\n          i0.ɵɵtemplate(1, NavbarRoutesComponent_Conditional_1_Template, 2, 0)(2, NavbarRoutesComponent_Conditional_2_Template, 1, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, NavbarRoutesComponent_ng_template_3_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, NavbarRoutesComponent_ng_template_5_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(7, NavbarRoutesComponent_ng_template_7_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.groupedItems ? 1 : 2);\n        }\n      },\n      dependencies: [i1.NgTemplateOutlet, LpxVisibleDirective, SubNavbarComponent, i1.AsyncPipe, TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavbarRoutesComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-navbar-routes',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ul class=\\\"lpx-nav-menu\\\">\\r\\n  @if (groupedItems) {\\r\\n    @for (item of groupedItems; track item) {\\r\\n      <ng-container\\r\\n        *ngTemplateOutlet=\\\"groupText; context: { $implicit: item }\\\"\\r\\n      ></ng-container>\\r\\n\\r\\n      @for (navbarItem of item.items; track navbarItem) {\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"itemTemplate; context: { $implicit: navbarItem }\\\"\\r\\n        ></ng-container>\\r\\n      }\\r\\n    }\\r\\n  } @else {\\r\\n    <ng-container *ngTemplateOutlet=\\\"defaultRoute\\\"></ng-container>\\r\\n  }\\r\\n</ul>\\r\\n\\r\\n<ng-template #defaultRoute>\\r\\n  @for (item of navbarItems; track item) {\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"itemTemplate; context: { $implicit: item }\\\"\\r\\n    ></ng-container>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #groupText let-item>\\r\\n  @if (item.items.length) {\\r\\n    <li class=\\\"group-menu-item hidden-in-hover-trigger\\\">\\r\\n      {{ item.group | lpxTranslate | async }}\\r\\n    </li>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #itemTemplate let-item>\\r\\n  <li\\r\\n    class=\\\"outer-menu-item\\\"\\r\\n    *lpxVisible=\\\"!item.visible || item.visible(item, injector)\\\"\\r\\n  >\\r\\n    <lpx-sub-navbar\\r\\n      [item]=\\\"item\\\"\\r\\n      (expand)=\\\"onSubnavbarExpand($event, navbarItems)\\\"\\r\\n      (routeClick)=\\\"onRouteClick($event, navbarItems)\\\"\\r\\n      [routerItem]=\\\"routerItem()\\\"\\r\\n    ></lpx-sub-navbar>\\r\\n  </li>\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], () => [], {\n    groupedItems: [{\n      type: Input\n    }],\n    navbarItems: [{\n      type: Input\n    }],\n    routeClick: [{\n      type: Output\n    }]\n  });\n})();\nclass NavbarComponent {\n  constructor() {\n    this.layoutService = inject(LayoutService);\n    this.platformId = inject(PLATFORM_ID);\n    this.service = inject(NavbarService);\n    this.injector = inject(Injector);\n    this.didResized = false;\n    this.showFilterMenu$ = this.service.navbarItems$.pipe(map$1(items => !!items.length));\n    this.contentBefore = this.flatContents(CONTENT_BEFORE_ROUTES);\n    this.contentAfter = this.flatContents(CONTENT_AFTER_ROUTES);\n  }\n  toggleSidebarHover() {\n    this.didResized = true;\n    this.layoutService.toggleClass('hover-trigger');\n  }\n  ngAfterViewChecked() {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n    if (this.didResized) {\n      this.didResized = false;\n      window.dispatchEvent(new Event('resize'));\n    }\n  }\n  flatContents(token) {\n    const contents = this.injector.get(token, []);\n    return contents.reduce((acc, val) => acc.concat(val), []);\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"lpx-navbar\"]],\n      contentQueries: function NavbarComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NavbarRoutesDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, LogoPanelDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.routesTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.logoPanel = _t.first);\n        }\n      },\n      decls: 16,\n      vars: 17,\n      consts: [[\"defaultRouteTemplate\", \"\"], [\"customContentTemplate\", \"\"], [\"defaultLogo\", \"\"], [1, \"lpx-nav\"], [1, \"lpx-logo-container\"], [4, \"ngTemplateOutlet\"], [\"iconClass\", \"bi bi-filter-left\", 1, \"menu-collapse-icon\", \"hidden-in-hover-trigger\", 3, \"click\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"navbarItems\", \"groupedItems\", \"routerItem\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngComponentOutlet\", \"ngComponentOutletInjector\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nav\", 3)(1, \"div\", 4);\n          i0.ɵɵtemplate(2, NavbarComponent_ng_container_2_Template, 1, 0, \"ng-container\", 5);\n          i0.ɵɵelementStart(3, \"lpx-icon\", 6);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_lpx_icon_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSidebarHover());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, NavbarComponent_Conditional_4_Template, 1, 4, \"ng-container\");\n          i0.ɵɵpipe(5, \"async\");\n          i0.ɵɵtemplate(6, NavbarComponent_ng_container_6_Template, 1, 0, \"ng-container\", 7);\n          i0.ɵɵpipe(7, \"async\");\n          i0.ɵɵpipe(8, \"async\");\n          i0.ɵɵtemplate(9, NavbarComponent_ng_container_9_Template, 1, 0, \"ng-container\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, NavbarComponent_ng_template_10_Template, 1, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(12, NavbarComponent_ng_template_12_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(14, NavbarComponent_ng_template_14_Template, 1, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const defaultRouteTemplate_r8 = i0.ɵɵreference(11);\n          const customContentTemplate_r3 = i0.ɵɵreference(13);\n          const defaultLogo_r9 = i0.ɵɵreference(15);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", (ctx.logoPanel == null ? null : ctx.logoPanel.template) || defaultLogo_r9);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(4, i0.ɵɵpipeBind1(5, 6, ctx.showFilterMenu$) ? 4 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.routesTemplate || defaultRouteTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(12, _c1, i0.ɵɵpipeBind1(7, 8, ctx.service.navbarItems$), i0.ɵɵpipeBind1(8, 10, ctx.service.groupedNavbarItems$)));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", customContentTemplate_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c0, ctx.contentAfter));\n        }\n      },\n      dependencies: [i1.NgComponentOutlet, i1.NgForOf, i1.NgTemplateOutlet, BrandLogoComponent, IconComponent, NavbarRoutesComponent, i1.AsyncPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavbarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-navbar',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<nav class=\\\"lpx-nav\\\">\\r\\n  <div class=\\\"lpx-logo-container\\\">\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"logoPanel?.template || defaultLogo\\\"\\r\\n    ></ng-container>\\r\\n    <lpx-icon\\r\\n      class=\\\"menu-collapse-icon hidden-in-hover-trigger\\\"\\r\\n      iconClass=\\\"bi bi-filter-left\\\"\\r\\n      (click)=\\\"toggleSidebarHover()\\\"\\r\\n    ></lpx-icon>\\r\\n  </div>\\r\\n\\r\\n  @if (showFilterMenu$ | async) {\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"\\r\\n        customContentTemplate;\\r\\n        context: { $implicit: contentBefore }\\r\\n      \\\"\\r\\n    ></ng-container>\\r\\n  }\\r\\n\\r\\n  <ng-container\\r\\n    *ngTemplateOutlet=\\\"\\r\\n      routesTemplate || defaultRouteTemplate;\\r\\n      context: {\\r\\n        $implicit: service.navbarItems$ | async,\\r\\n        groupItems: service.groupedNavbarItems$ | async\\r\\n      }\\r\\n    \\\"\\r\\n  ></ng-container>\\r\\n\\r\\n  <ng-container\\r\\n    *ngTemplateOutlet=\\\"\\r\\n      customContentTemplate;\\r\\n      context: { $implicit: contentAfter }\\r\\n    \\\"\\r\\n  ></ng-container>\\r\\n</nav>\\r\\n\\r\\n<ng-template #defaultRouteTemplate let-items let-groupItems=\\\"groupItems\\\">\\r\\n  <lpx-navbar-routes\\r\\n    [navbarItems]=\\\"items\\\"\\r\\n    [groupedItems]=\\\"groupItems\\\"\\r\\n    [routerItem]=\\\"true\\\"\\r\\n  ></lpx-navbar-routes>\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #customContentTemplate let-contents>\\r\\n  <ng-container *ngFor=\\\"let component of contents\\\">\\r\\n    <ng-container\\r\\n      *ngComponentOutlet=\\\"component; injector: injector\\\"\\r\\n    ></ng-container>\\r\\n  </ng-container>\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #defaultLogo>\\r\\n  <lpx-brand-logo></lpx-brand-logo>\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], () => [], {\n    routesTemplate: [{\n      type: ContentChild,\n      args: [NavbarRoutesDirective, {\n        read: TemplateRef\n      }]\n    }],\n    logoPanel: [{\n      type: ContentChild,\n      args: [LogoPanelDirective]\n    }]\n  });\n})();\nclass ToObservablePipe {\n  transform(value) {\n    return value ? getStream$(value) : of('');\n  }\n  static {\n    this.ɵfac = function ToObservablePipe_Factory(t) {\n      return new (t || ToObservablePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"toObservable\",\n      type: ToObservablePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToObservablePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'toObservable'\n    }]\n  }], null, null);\n})();\nclass ToObservableModule {\n  static {\n    this.ɵfac = function ToObservableModule_Factory(t) {\n      return new (t || ToObservableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ToObservableModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToObservableModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ToObservablePipe],\n      imports: [CommonModule],\n      exports: [ToObservablePipe]\n    }]\n  }], null, null);\n})();\nclass SafeHtmlPipe {\n  constructor() {\n    this.sanitizer = inject(DomSanitizer);\n  }\n  transform(value) {\n    if (!value || typeof value !== 'string') return '';\n    return this.sanitizer.sanitize(SecurityContext.HTML, value) || '';\n  }\n  static {\n    this.ɵfac = function SafeHtmlPipe_Factory(t) {\n      return new (t || SafeHtmlPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"lpxSafeHtml\",\n      type: SafeHtmlPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SafeHtmlPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'lpxSafeHtml',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass LpxTranslateModule {\n  static {\n    this.ɵfac = function LpxTranslateModule_Factory(t) {\n      return new (t || LpxTranslateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxTranslateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxTranslateModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TranslatePipe],\n      imports: [CommonModule],\n      exports: [TranslatePipe]\n    }]\n  }], null, null);\n})();\nconst exportedDeclarations$1 = [NavbarComponent, SubNavbarComponent, NavbarRoutesComponent, NavbarRoutesDirective];\nclass LpxNavbarModule {\n  static forRoot(options = {}) {\n    return {\n      ngModule: LpxNavbarModule,\n      providers: [{\n        provide: LPX_MENU_ITEMS,\n        useValue: options?.menuItems || []\n      }, {\n        provide: CONTENT_AFTER_ROUTES,\n        useValue: options?.contentAfterRoutes || [],\n        multi: true\n      }, {\n        provide: CONTENT_BEFORE_ROUTES,\n        useValue: options?.contentBeforeRoutes || [],\n        multi: true\n      }]\n    };\n  }\n  static forChild(options = {}) {\n    return {\n      ngModule: LpxNavbarModule,\n      providers: [{\n        provide: CONTENT_AFTER_ROUTES,\n        useValue: options?.contentAfterRoutes || [],\n        multi: true\n      }, {\n        provide: CONTENT_BEFORE_ROUTES,\n        useValue: options?.contentBeforeRoutes || [],\n        multi: true\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function LpxNavbarModule_Factory(t) {\n      return new (t || LpxNavbarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxNavbarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, RouterModule, LpxBrandLogoModule, LpxIconModule, ToObservableModule, LpxTranslateModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxNavbarModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [...exportedDeclarations$1],\n      imports: [CommonModule, FormsModule, RouterModule, LpxBrandLogoModule, LpxIconModule, ToObservableModule, LpxTranslateModule, LpxVisibleDirective],\n      exports: [...exportedDeclarations$1]\n    }]\n  }], null, null);\n})();\nclass BreadcrumbService {\n  constructor() {\n    this.store = new DataStore([]);\n    this.items$ = this.store.sliceState(state => state);\n  }\n  // TODO: generate id per item\n  add(item) {\n    const items = Array.isArray(item) ? item : [item];\n    this.store.set([...this.store.state, ...items]);\n  }\n  // TODO: generate id per item\n  insert(item, index) {\n    const state = this.store.state;\n    const items = Array.isArray(item) ? item : [item];\n    this.store.set([...state.slice(0, index), ...items, ...state.slice(index)]);\n  }\n  // TODO: generate id per item\n  setItems(items) {\n    this.store.set(items);\n  }\n  static {\n    this.ɵfac = function BreadcrumbService_Factory(t) {\n      return new (t || BreadcrumbService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BreadcrumbService,\n      factory: BreadcrumbService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadcrumbService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ClickOutsideDirective {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.lpxClickOutside = new EventEmitter();\n    this.exceptedRefs = [];\n  }\n  onDocumentClick(event) {\n    if (!(this.elementRef.nativeElement.contains(event.target) || this.exceptedRefs.some(ref => ref.nativeElement.contains(event.target)))) {\n      this.lpxClickOutside.emit();\n    }\n  }\n  static {\n    this.ɵfac = function ClickOutsideDirective_Factory(t) {\n      return new (t || ClickOutsideDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ClickOutsideDirective,\n      selectors: [[\"\", \"lpxClickOutside\", \"\"]],\n      hostBindings: function ClickOutsideDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function ClickOutsideDirective_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        exceptedRefs: \"exceptedRefs\"\n      },\n      outputs: {\n        lpxClickOutside: \"lpxClickOutside\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClickOutsideDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[lpxClickOutside]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    lpxClickOutside: [{\n      type: Output\n    }],\n    exceptedRefs: [{\n      type: Input\n    }],\n    onDocumentClick: [{\n      type: HostListener,\n      args: ['document:click', ['$event']]\n    }]\n  });\n})();\nclass BreadcrumbComponent {\n  constructor(service) {\n    this.service = service;\n    this.icon = ICON_MAP;\n  }\n  onClick(item) {\n    if (item.children) {\n      item.expanded = !item.expanded;\n    }\n  }\n  static {\n    this.ɵfac = function BreadcrumbComponent_Factory(t) {\n      return new (t || BreadcrumbComponent)(i0.ɵɵdirectiveInject(BreadcrumbService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: BreadcrumbComponent,\n      selectors: [[\"lpx-breadcrumb\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[\"linkTemplate\", \"\"], [\"textTemplate\", \"\"], [\"aria-label\", \"breadcrumb\"], [1, \"lpx-breadcrumb\"], [4, \"ngFor\", \"ngForOf\"], [1, \"lpx-breadcrumb-item\", 3, \"click\", \"lpxClickOutside\"], [\"class\", \"lpx-breadcrumb-item-icon\", 3, \"iconClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"lpx-breadcrumb-separator\", 4, \"ngIf\"], [1, \"lpx-breadcrumb-item-icon\", 3, \"iconClass\"], [1, \"lpx-breadcrumb-separator\"], [\"iconClass\", \"bi bi-chevron-right\"], [3, \"routerLink\"], [1, \"lpx-breadcrumb-item-text\"]],\n      template: function BreadcrumbComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 2)(1, \"ol\", 3);\n          i0.ɵɵtemplate(2, BreadcrumbComponent_ng_container_2_Template, 5, 8, \"ng-container\", 4);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, BreadcrumbComponent_ng_template_4_Template, 4, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(6, BreadcrumbComponent_ng_template_6_Template, 4, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 1, ctx.service.items$));\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, IconComponent, i2.RouterLink, ClickOutsideDirective, i1.AsyncPipe, ToObservablePipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadcrumbComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-breadcrumb',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<nav aria-label=\\\"breadcrumb\\\">\\r\\n  <ol class=\\\"lpx-breadcrumb\\\">\\r\\n    <ng-container *ngFor=\\\"let item of service.items$ | async; last as last\\\">\\r\\n      <li\\r\\n        class=\\\"lpx-breadcrumb-item\\\"\\r\\n        (click)=\\\"onClick(item)\\\"\\r\\n        [class.expanded]=\\\"item.expanded\\\"\\r\\n        (lpxClickOutside)=\\\"item.expanded = false\\\"\\r\\n      >\\r\\n        <lpx-icon\\r\\n          class=\\\"lpx-breadcrumb-item-icon\\\"\\r\\n          *ngIf=\\\"item.icon\\\"\\r\\n          [iconClass]=\\\"item.icon\\\"\\r\\n        ></lpx-icon>\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"\\r\\n            item.children?.length ? textTemplate : linkTemplate;\\r\\n            context: { $implicit: item }\\r\\n          \\\"\\r\\n        ></ng-container>\\r\\n      </li>\\r\\n      <li *ngIf=\\\"!last\\\" class=\\\"lpx-breadcrumb-separator\\\">\\r\\n        <lpx-icon iconClass=\\\"bi bi-chevron-right\\\"></lpx-icon>\\r\\n      </li>\\r\\n    </ng-container>\\r\\n  </ol>\\r\\n</nav>\\r\\n\\r\\n<ng-template #linkTemplate let-item>\\r\\n  <a [routerLink]=\\\"item.link\\\"> {{ item.text | toObservable | async }} </a>\\r\\n</ng-template>\\r\\n<ng-template #textTemplate let-item>\\r\\n  <span class=\\\"lpx-breadcrumb-item-text\\\">\\r\\n    {{ item.text | toObservable | async }}\\r\\n  </span>\\r\\n</ng-template>\\r\\n\"\n    }]\n  }], () => [{\n    type: BreadcrumbService\n  }], null);\n})();\nclass LpxClickOutsideModule {\n  static {\n    this.ɵfac = function LpxClickOutsideModule_Factory(t) {\n      return new (t || LpxClickOutsideModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxClickOutsideModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxClickOutsideModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ClickOutsideDirective],\n      imports: [CommonModule],\n      exports: [ClickOutsideDirective]\n    }]\n  }], null, null);\n})();\nclass BreadcrumbRouteListenerService {\n  constructor(navbarService, router, breadcrumbService) {\n    this.navbarService = navbarService;\n    this.router = router;\n    this.breadcrumbService = breadcrumbService;\n  }\n  subscribeRoute() {\n    combineLatest([this.router.events.pipe(filter(event => event instanceof NavigationEnd)), this.navbarService.navbarItems$.pipe(filter(items => !!items.length))]).subscribe(([event, items]) => {\n      let activeItem = this.navbarService.findByLink(event.url);\n      if (!activeItem.item) {\n        activeItem = this.navbarService.findByLink('/');\n      }\n      const breadCrumbItems = activeItem.location.reduce((acc, itemIndex) => {\n        const parent = acc[acc.length - 1]?.children || items;\n        const item = parent[itemIndex];\n        return [...acc, {\n          ...item,\n          siblings: parent\n        }];\n      }, []);\n      this.breadcrumbService.setItems(this.mapNavbarItemToBreadcrumbItem(breadCrumbItems));\n    });\n  }\n  mapNavbarItemToBreadcrumbItem(items) {\n    return items.map(({\n      breadcrumbText,\n      text,\n      link,\n      icon,\n      siblings\n    }) => ({\n      text: breadcrumbText || text || '',\n      link,\n      icon,\n      children: this.mapNavbarItemToBreadcrumbItem(siblings || [])\n    }));\n  }\n  static {\n    this.ɵfac = function BreadcrumbRouteListenerService_Factory(t) {\n      return new (t || BreadcrumbRouteListenerService)(i0.ɵɵinject(NavbarService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(BreadcrumbService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BreadcrumbRouteListenerService,\n      factory: BreadcrumbRouteListenerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadcrumbRouteListenerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: NavbarService\n  }, {\n    type: i2.Router\n  }, {\n    type: BreadcrumbService\n  }], null);\n})();\nconst exportedDeclarations = [BreadcrumbComponent];\nclass LpxBreadcrumbModule {\n  static forRoot() {\n    return {\n      ngModule: LpxBreadcrumbModule,\n      providers: [{\n        provide: APP_INITIALIZER,\n        useFactory: breadCrumbInit,\n        multi: true,\n        deps: [Injector]\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function LpxBreadcrumbModule_Factory(t) {\n      return new (t || LpxBreadcrumbModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxBreadcrumbModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, LpxIconModule, ToObservableModule, RouterModule, LpxClickOutsideModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxBreadcrumbModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [...exportedDeclarations],\n      imports: [CommonModule, LpxIconModule, ToObservableModule, RouterModule, LpxClickOutsideModule],\n      exports: [...exportedDeclarations]\n    }]\n  }], null, null);\n})();\nfunction breadCrumbInit(injector) {\n  const subs = () => {\n    const service = injector.get(BreadcrumbRouteListenerService);\n    service.subscribeRoute();\n  };\n  return subs;\n}\nconst LPX_TRANSLATE_SERVICE_PROVIDER = {\n  provide: LPX_TRANSLATE_SERVICE_TOKEN,\n  useClass: DefaultTranslateService\n};\nconst LPX_TRANSLATE_PROVIDERS = [LPX_TRANSLATE_SERVICE_PROVIDER];\nconst LPX_INITIAL_STYLES = new InjectionToken('LPX_INITIAL_STYLES_TOKEN');\nconst LPX_STYLE_FINAL = new InjectionToken('LPX_STYLE_FINAL_TOKEN');\nconst LPX_LAYOUT_STYLE_FINAL = new InjectionToken('LPX_LAYOUT_STYLE_FINALIZE_TOKEN');\nclass StyleService {\n  constructor(initialStyles, document) {\n    this.initialStyles = initialStyles;\n    this.document = document;\n    this.lastInjectedStyle = null;\n    this.initialized$ = new BehaviorSubject(false);\n  }\n  initStyles(direction) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      for (const style of _this.initialStyles) {\n        yield _this.loadStyle(style, direction);\n      }\n      _this.initialized$.next(true);\n    })();\n  }\n  loadStyle(style, direction) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, reject) => {\n        const linkElem = _this2.createLinkElem(style, direction, resolve);\n        //TODO: find a better way for understand style laaded by angular json\n        const appStyles = document.querySelector('link[rel=\"stylesheet\"][href*=\"styles\"]');\n        if (appStyles) {\n          if (_this2.lastInjectedStyle && _this2.lastInjectedStyle.isConnected) {\n            _this2.lastInjectedStyle.insertAdjacentElement('afterend', linkElem);\n          } else {\n            appStyles.insertAdjacentElement('beforebegin', linkElem);\n          }\n        } else {\n          _this2.document.head.appendChild(linkElem);\n        }\n        _this2.lastInjectedStyle = linkElem;\n        return Promise.resolve(linkElem);\n      });\n    })();\n  }\n  replaceStyle(style, direction) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const loaded = _this3.document.querySelector(`link#${style.bundleName}`);\n      if (loaded) {\n        loaded.remove();\n      }\n      return _this3.loadStyle(style, direction);\n    })();\n  }\n  reloadInitialStyles(direction) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      for (const style of _this4.initialStyles) {\n        yield _this4.replaceStyle(style, direction);\n      }\n    })();\n  }\n  createLinkElem(style, direction, resolve) {\n    const linkElem = document.createElement('link');\n    linkElem.rel = 'stylesheet';\n    linkElem.id = style.bundleName;\n    linkElem.href = `${style.bundleName}${direction === 'rtl' ? '.rtl' : ''}.css`;\n    linkElem.onload = () => {\n      resolve(linkElem);\n    };\n    return linkElem;\n  }\n  static {\n    this.ɵfac = function StyleService_Factory(t) {\n      return new (t || StyleService)(i0.ɵɵinject(LPX_STYLE_FINAL), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: StyleService,\n      factory: StyleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StyleService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LPX_STYLE_FINAL]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nfunction createStyleFactory(handler) {\n  return handler || (defaultValue => defaultValue);\n}\nfunction styleLoadFactory(styleList, layoutStyles) {\n  styleList.push({\n    bundleName: 'ng-bundle'\n  });\n  styleList.push({\n    bundleName: 'font-bundle'\n  });\n  return [...styleList, ...layoutStyles];\n}\nconst LPX_STYLE_PROVIDERS = [{\n  provide: LPX_INITIAL_STYLES,\n  useFactory: () => []\n}, {\n  provide: APP_INITIALIZER,\n  deps: [StyleService, LanguageService],\n  useFactory: loadInitialStyles,\n  multi: true\n}];\nfunction loadInitialStyles(styleService, languageService) {\n  return () => {\n    return languageService.languageChange$.pipe(take(1), switchMap(lang => from(styleService.initStyles(lang.isRTL ? 'rtl' : 'ltr'))));\n  };\n}\nconst WINDOW = new InjectionToken('WINDOW');\nfunction createWindowProvider(windowObj) {\n  return {\n    provide: WINDOW,\n    useValue: windowObj || window\n  };\n}\nconst RESPONSIVE_BREAKPOINTS = new InjectionToken('RESPONSIVE_BREAKPOINTS');\nclass ResponsiveService {\n  constructor(providedBreakpoints, window) {\n    this.providedBreakpoints = providedBreakpoints;\n    this.window = window;\n    this.defaultBreakpoint = {\n      name: \"all\" /* ResponsiveTokens.all */,\n      width: 0\n    };\n    this.breakpoints = this.buildBreakpoints(this.providedBreakpoints);\n    this.getCurrentSize = () => ({\n      height: this.window.innerHeight,\n      width: this.window.innerWidth\n    });\n    this.mapSizeToBreakpoint = ({\n      width\n    } = this.getCurrentSize()) => {\n      return this.breakpoints.find(s => width >= s.width);\n    };\n    this.currentSize$ = new BehaviorSubject(this.mapSizeToBreakpoint());\n    this.shouldRenderWithCurrentSize = query => {\n      return this.matchQuery(query);\n    };\n    this.setupListener();\n  }\n  setupListener() {\n    this.currentResolution$ = fromEvent(this.window, 'resize').pipe(map(this.getCurrentSize)).pipe(startWith(this.getCurrentSize()));\n    this.currentResolution$.pipe(map(this.mapSizeToBreakpoint), distinctUntilChanged()).subscribe(current => {\n      this.currentSize$.next(current);\n    });\n  }\n  buildBreakpoints(breakpoints) {\n    return [...Object.keys(breakpoints).map(key => ({\n      name: key,\n      width: breakpoints[key]\n    })).sort((a, b) => b.width - a.width), this.defaultBreakpoint];\n  }\n  matchQuery(query) {\n    const {\n      width\n    } = this.getCurrentSize();\n    const tokens = query.split(' ');\n    const findInTokens = size => tokens.find(token => token.split(\"-\" /* ResponsiveTokens.separator */)[0] === size);\n    const matchedBreakpoint = this.breakpoints.find(breakpoint => width >= breakpoint.width && findInTokens(breakpoint.name));\n    if (matchedBreakpoint) {\n      const token = findInTokens(matchedBreakpoint.name);\n      const shouldBeBigger = !token?.includes(\"none\" /* ResponsiveTokens.none */);\n      return shouldBeBigger === width >= matchedBreakpoint.width;\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function ResponsiveService_Factory(t) {\n      return new (t || ResponsiveService)(i0.ɵɵinject(RESPONSIVE_BREAKPOINTS), i0.ɵɵinject(WINDOW));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ResponsiveService,\n      factory: ResponsiveService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResponsiveService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [RESPONSIVE_BREAKPOINTS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [WINDOW]\n    }]\n  }], null);\n})();\nclass ResponsiveDirective {\n  constructor(templateRef, viewContainer, service, parentCdr) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.service = service;\n    this.parentCdr = parentCdr;\n    this.hasRendered = false;\n    this.sub = new Subscription();\n    this.render = shouldRender => {\n      if (shouldRender && !this.hasRendered) {\n        this.viewContainer.createEmbeddedView(this.templateRef);\n        this.hasRendered = true;\n      } else if (!shouldRender && this.hasRendered) {\n        this.viewContainer.clear();\n        this.hasRendered = false;\n      }\n      this.parentCdr.detectChanges();\n    };\n  }\n  ngOnInit() {\n    this.sub.add(this.service.currentSize$.pipe(map(_ => this.service.shouldRenderWithCurrentSize(this.query))).subscribe(this.render));\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n  }\n  static {\n    this.ɵfac = function ResponsiveDirective_Factory(t) {\n      return new (t || ResponsiveDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(ResponsiveService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ResponsiveDirective,\n      selectors: [[\"\", \"lpxResponsive\", \"\"]],\n      inputs: {\n        query: [i0.ɵɵInputFlags.None, \"lpxResponsive\", \"query\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResponsiveDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[lpxResponsive]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: ResponsiveService\n  }, {\n    type: i0.ChangeDetectorRef,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], {\n    query: [{\n      type: Input,\n      args: ['lpxResponsive']\n    }]\n  });\n})();\nclass LpxResponsiveModule {\n  static {\n    this.ɵfac = function LpxResponsiveModule_Factory(t) {\n      return new (t || LpxResponsiveModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxResponsiveModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxResponsiveModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ResponsiveDirective],\n      imports: [CommonModule],\n      exports: [ResponsiveDirective]\n    }]\n  }], null, null);\n})();\nconst LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS = {\n  sm: 480,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nfunction createResponsiveProvider(responsiveSettings) {\n  return {\n    provide: RESPONSIVE_BREAKPOINTS,\n    useValue: responsiveSettings || LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS\n  };\n}\nfunction createDirectionProvider(listenDirection) {\n  return {\n    provide: APP_INITIALIZER,\n    multi: true,\n    deps: [LanguageService, StyleService],\n    useFactory: listenDirection ? listenDirectionChange : () => () => null\n  };\n}\n// subscribe to direction from documentElement and load direction stylesheet\nfunction listenDirectionChange(languageService, styleService) {\n  return () => {\n    return new Promise(resolve => {\n      styleService.initialized$.pipe(filter(Boolean), take(1), switchMap(() => languageService.languageChange$), distinctUntilKeyChanged('isRTL')).subscribe(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (lang) {\n          const direction = lang?.isRTL ? 'rtl' : 'ltr';\n          const documentElement = document.documentElement;\n          if (documentElement.dir !== direction) {\n            documentElement.dir = direction;\n          }\n          yield styleService.reloadInitialStyles(direction);\n          resolve(null);\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    });\n  };\n}\nconst LPX_PERFECT_SCROLLBAR = new InjectionToken('LPX_PERFECT_SCROLLBAR');\nclass LpxCoreModule {\n  static forRoot(options) {\n    return {\n      ngModule: LpxCoreModule,\n      providers: [{\n        provide: LPX_PERFECT_SCROLLBAR,\n        useClass: LpxPerfectScrollbarService\n      }, createResponsiveProvider(options?.responsiveSettings), createWindowProvider(options?.window), LpxIconModule.forRoot(options?.iconSettings).providers, LpxLanguageModule.forRoot(options?.languageSettings).providers, LpxNavbarModule.forRoot(options?.navbarSettings).providers, LpxBreadcrumbModule.forRoot().providers, LPX_TRANSLATE_PROVIDERS, ...LPX_STYLE_PROVIDERS, createDirectionProvider(options?.listenDirectionChanges || true)]\n    };\n  }\n  static {\n    this.ɵfac = function LpxCoreModule_Factory(t) {\n      return new (t || LpxCoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxCoreModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxCoreModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, LpxVisibleDirective]\n    }]\n  }], null, null);\n})();\nclass AvatarComponent {\n  static {\n    this.ɵfac = function AvatarComponent_Factory(t) {\n      return new (t || AvatarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AvatarComponent,\n      selectors: [[\"lpx-avatar\"]],\n      inputs: {\n        avatar: \"avatar\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"lpx-avatar\", 4, \"ngIf\"], [1, \"lpx-avatar\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"lpx-avatar-icon\", 3, \"iconClass\"], [1, \"lpx-avatar-img\", 3, \"src\"]],\n      template: function AvatarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AvatarComponent_div_0_Template, 4, 3, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.avatar && (ctx.avatar == null ? null : ctx.avatar.source));\n        }\n      },\n      dependencies: [i1.NgIf, i1.NgSwitch, i1.NgSwitchCase, IconComponent],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-avatar',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"lpx-avatar\\\" *ngIf=\\\"avatar && avatar?.source\\\">\\r\\n  <ng-container [ngSwitch]=\\\"avatar.type\\\">\\r\\n    <ng-container *ngSwitchCase=\\\"'icon'\\\">\\r\\n      <lpx-icon class=\\\"lpx-avatar-icon\\\" [iconClass]=\\\"avatar.source\\\"></lpx-icon>\\r\\n    </ng-container>\\r\\n    <ng-container *ngSwitchCase=\\\"'image'\\\">\\r\\n      <img class=\\\"lpx-avatar-img\\\" [src]=\\\"avatar.source\\\" />\\r\\n    </ng-container>\\r\\n  </ng-container>\\r\\n</div>\\r\\n\"\n    }]\n  }], null, {\n    avatar: [{\n      type: Input\n    }]\n  });\n})();\nclass LpxAvatarModule {\n  static {\n    this.ɵfac = function LpxAvatarModule_Factory(t) {\n      return new (t || LpxAvatarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxAvatarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, LpxIconModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxAvatarModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [AvatarComponent],\n      imports: [CommonModule, LpxIconModule],\n      exports: [AvatarComponent]\n    }]\n  }], null, null);\n})();\nclass FooterLinksService {\n  constructor() {\n    this.store = new DataStore({});\n    this.footerInfo$ = this.store.sliceState(state => state);\n  }\n  setFooterInfo(links) {\n    this.store.set(links);\n  }\n  static {\n    this.ɵfac = function FooterLinksService_Factory(t) {\n      return new (t || FooterLinksService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FooterLinksService,\n      factory: FooterLinksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterLinksService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass FooterComponent {\n  constructor(service) {\n    this.service = service;\n    this.footerValues$ = this.service.footerInfo$;\n  }\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)(i0.ɵɵdirectiveInject(FooterLinksService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"lpx-footer\"]],\n      decls: 2,\n      vars: 3,\n      consts: [[\"footerDesc\", \"\"], [4, \"ngIf\"], [1, \"lpx-footbar\"], [1, \"lpx-footbar-copyright\"], [3, \"routerLink\", 4, \"ngIf\", \"ngIfElse\"], [1, \"lpx-footbar-solo-links\"], [4, \"ngFor\", \"ngForOf\"], [3, \"routerLink\"], [3, \"routerLink\", 4, \"ngIf\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, FooterComponent_ng_container_0_Template, 8, 3, \"ng-container\", 1);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.footerValues$));\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.RouterLink, i1.AsyncPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lpx-footer',\n      template: \"<ng-container *ngIf=\\\"footerValues$ | async as footerValues\\\">\\r\\n\\r\\n    <div class=\\\"lpx-footbar\\\">\\r\\n      <div class=\\\"lpx-footbar-copyright\\\">\\r\\n     \\r\\n        <a\\r\\n          *ngIf=\\\"footerValues.descUrl; else footerDesc\\\"\\r\\n          [routerLink]=\\\"[footerValues.descUrl]\\\"\\r\\n        >\\r\\n          {{ footerValues.desc }}</a\\r\\n        >\\r\\n        <ng-template #footerDesc>\\r\\n          <a> {{ footerValues.desc }}</a>\\r\\n        </ng-template>\\r\\n      </div>\\r\\n      <div class=\\\"lpx-footbar-solo-links\\\">\\r\\n        <ng-container *ngFor=\\\"let footerLink of footerValues.footerLinks\\\">\\r\\n          <a *ngIf=\\\"footerLink\\\" [routerLink]=\\\"[footerLink.link]\\\">{{\\r\\n            footerLink.text\\r\\n          }}</a>\\r\\n        </ng-container>\\r\\n      </div>\\r\\n    </div>\\r\\n\\r\\n</ng-container>\\r\\n\"\n    }]\n  }], () => [{\n    type: FooterLinksService\n  }], null);\n})();\nclass LpxFooterModule {\n  static forRoot() {\n    return {\n      ngModule: LpxFooterModule,\n      providers: []\n    };\n  }\n  static {\n    this.ɵfac = function LpxFooterModule_Factory(t) {\n      return new (t || LpxFooterModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LpxFooterModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LpxFooterModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [FooterComponent],\n      exports: [FooterComponent],\n      imports: [CommonModule, RouterModule]\n    }]\n  }], null, null);\n})();\nclass PerfectScrollbarDirective {\n  constructor() {\n    this.elementRef = inject(ElementRef);\n    this.lpxPerfectService = inject(LPX_PERFECT_SCROLLBAR);\n  }\n  set lpxPerfectScrollbarOptions(value) {\n    this.lpxPerfectService.setOptions(value);\n  }\n  onResize() {\n    this.lpxPerfectService.onResize();\n  }\n  ngAfterViewInit() {\n    this.lpxPerfectService.setElement(this.elementRef);\n    this.lpxPerfectService.afterViewInit();\n  }\n  static {\n    this.ɵfac = function PerfectScrollbarDirective_Factory(t) {\n      return new (t || PerfectScrollbarDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PerfectScrollbarDirective,\n      selectors: [[\"\", \"lpxPerfectScrollbar\", \"\"]],\n      hostBindings: function PerfectScrollbarDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function PerfectScrollbarDirective_resize_HostBindingHandler() {\n            return ctx.onResize();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        lpxPerfectScrollbarOptions: \"lpxPerfectScrollbarOptions\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PerfectScrollbarDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[lpxPerfectScrollbar]',\n      standalone: true\n    }]\n  }], null, {\n    lpxPerfectScrollbarOptions: [{\n      type: Input\n    }],\n    onResize: [{\n      type: HostListener,\n      args: ['window:resize']\n    }]\n  });\n})();\nclass BreadcrumbPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function BreadcrumbPanelDirective_Factory(t) {\n      return new (t || BreadcrumbPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BreadcrumbPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-breadcrumb-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadcrumbPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-breadcrumb-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass ContentPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function ContentPanelDirective_Factory(t) {\n      return new (t || ContentPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ContentPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-content\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-content]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass CurrentUserImagePanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CurrentUserImagePanelDirective_Factory(t) {\n      return new (t || CurrentUserImagePanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CurrentUserImagePanelDirective,\n      selectors: [[\"ng-template\", \"lpx-current-user-image-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CurrentUserImagePanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-current-user-image-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass CurrentUserPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CurrentUserPanelDirective_Factory(t) {\n      return new (t || CurrentUserPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CurrentUserPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-current-user-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CurrentUserPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-current-user-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass FooterPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function FooterPanelDirective_Factory(t) {\n      return new (t || FooterPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FooterPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-footer-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-footer-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass LanguagePanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function LanguagePanelDirective_Factory(t) {\n      return new (t || LanguagePanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LanguagePanelDirective,\n      selectors: [[\"ng-template\", \"lpx-language-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LanguagePanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-language-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass MobileNavbarPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function MobileNavbarPanelDirective_Factory(t) {\n      return new (t || MobileNavbarPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MobileNavbarPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-mobile-navbar-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MobileNavbarPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-mobile-navbar-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NavbarPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NavbarPanelDirective_Factory(t) {\n      return new (t || NavbarPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NavbarPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-navbar-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavbarPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-navbar-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NavitemPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NavitemPanelDirective_Factory(t) {\n      return new (t || NavitemPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NavitemPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-navitem-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavitemPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-navitem-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass ToolbarPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function ToolbarPanelDirective_Factory(t) {\n      return new (t || ToolbarPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ToolbarPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-toolbar-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToolbarPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-toolbar-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass TopNavbarPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function TopNavbarPanelDirective_Factory(t) {\n      return new (t || TopNavbarPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TopNavbarPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-top-navbar-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TopNavbarPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-top-navbar-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass SettingsPanelDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function SettingsPanelDirective_Factory(t) {\n      return new (t || SettingsPanelDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SettingsPanelDirective,\n      selectors: [[\"ng-template\", \"lpx-settings-panel\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SettingsPanelDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[lpx-settings-panel]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nconst declarationsAndExports = [BreadcrumbPanelDirective, ContentPanelDirective, CurrentUserImagePanelDirective, CurrentUserPanelDirective, FooterPanelDirective, LanguagePanelDirective, LogoPanelDirective, MobileNavbarPanelDirective, NavbarPanelDirective, NavitemPanelDirective, SettingsPanelDirective, TopNavbarPanelDirective, ToolbarPanelDirective];\nclass PanelsModule {\n  static {\n    this.ɵfac = function PanelsModule_Factory(t) {\n      return new (t || PanelsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PanelsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelsModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [...declarationsAndExports],\n      imports: [CommonModule],\n      exports: [...declarationsAndExports]\n    }]\n  }], null, null);\n})();\nclass ToolbarService {\n  constructor() {\n    this.store = new DataStore({\n      items: []\n    });\n    this.items$ = this.store.sliceState(({\n      items\n    }) => items);\n  }\n  setItems(items) {\n    this.store.patch({\n      items: items.sort(sortItems)\n    });\n  }\n  addItem(item) {\n    this.setItems([...this.store.state.items, item]);\n  }\n  patchItem(itemId, item) {\n    const {\n      items\n    } = this.store.state;\n    const index = items.findIndex(({\n      id\n    }) => id === itemId);\n    if (index === -1) {\n      return;\n    }\n    const updateItems = [...items];\n    updateItems[index] = {\n      id: itemId,\n      ...item\n    };\n    this.setItems(updateItems);\n  }\n  removeItem(id) {\n    const {\n      items\n    } = this.store.state;\n    const index = items.findIndex(item => item.id === id);\n    if (index === -1) {\n      return;\n    }\n    const updateItems = [...items.slice(0, index), ...items.slice(index + 1)];\n    this.store.patch({\n      items: updateItems\n    });\n  }\n  static {\n    this.ɵfac = function ToolbarService_Factory(t) {\n      return new (t || ToolbarService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ToolbarService,\n      factory: ToolbarService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToolbarService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AvatarComponent, BodyService, BrandLogoComponent, BreadcrumbComponent, BreadcrumbPanelDirective, BreadcrumbRouteListenerService, BreadcrumbService, CONTENT_AFTER_ROUTES, CONTENT_BEFORE_ROUTES, ClickOutsideDirective, ContentPanelDirective, CurrentUserImagePanelDirective, CurrentUserPanelDirective, DataStore, DefaultAuthService, DefaultTranslateService, FooterComponent, FooterLinksService, FooterPanelDirective, ICON_MAP, IconComponent, LEPTON_X_ICON_SET, LPX_AUTH_SERVICE_PROVIDER, LPX_AUTH_SERVICE_TOKEN, LPX_INITIAL_STYLES, LPX_LANGUAGE, LPX_LAYOUT_STYLE_FINAL, LPX_MENU_ITEMS, LPX_PERFECT_SCROLLBAR, LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS, LPX_STYLE_FINAL, LPX_STYLE_PROVIDERS, LPX_TRANSLATE_SERVICE_TOKEN, LPX_TRANSLATE_TOKEN, LanguagePanelDirective, LanguageService, LanguageTranslateDefaults, LanguageTranslateKeys, LayoutService, LogoPanelDirective, LpxAvatarModule, LpxBrandLogoModule, LpxBreadcrumbModule, LpxClickOutsideModule, LpxCoreModule, LpxFooterModule, LpxIconModule, LpxLanguageModule, LpxLocalStorageService, LpxNavbarModule, LpxPerfectScrollbar, LpxPerfectScrollbarService, LpxResponsiveModule, LpxThemeTranslateService, LpxTranslateModule, LpxVisibleDirective, MobileNavbarPanelDirective, NavbarComponent, NavbarPanelDirective, NavbarRoutesComponent, NavbarRoutesDirective, NavbarService, NavitemPanelDirective, OTHERS_GROUP_KEY, PanelsModule, PerfectScrollbarDirective, RESPONSIVE_BREAKPOINTS, ResponsiveDirective, ResponsiveService, RoutesService, SafeHtmlPipe, SettingsPanelDirective, StyleService, SubNavbarComponent, ToObservableModule, ToObservablePipe, ToolbarPanelDirective, ToolbarService, TopNavbarPanelDirective, TranslatePipe, UserProfileService, WINDOW, breadCrumbInit, createDirectionProvider, createGroupMap, createResponsiveProvider, createStyleFactory, createWindowProvider, exportedDeclarations, flatArrayDeepToObject, getItemsFromGroup, getStream$, isArray, isNullOrUndefined, listenDirectionChange, loadInitialStyles, sortItems, styleLoadFactory };", "map": {"version": 3, "names": ["i0", "inject", "InjectionToken", "Injectable", "Inject", "NgModule", "Component", "ViewEncapsulation", "Input", "Directive", "Optional", "<PERSON><PERSON>", "Injector", "input", "EventEmitter", "Output", "effect", "PLATFORM_ID", "TemplateRef", "ContentChild", "SecurityContext", "HostListener", "APP_INITIALIZER", "SkipSelf", "ElementRef", "i2", "Router", "RouterModule", "NavigationEnd", "NavigationError", "NavigationCancel", "i1", "CommonModule", "isPlatformBrowser", "DOCUMENT", "map", "distinctUntilChanged", "filter", "take", "tap", "switchMap", "startWith", "distinctUntilKeyChanged", "BehaviorSubject", "Subject", "Observable", "from", "of", "EMPTY", "filter$1", "map$1", "combineLatest", "fromEvent", "Subscription", "FormsModule", "toSignal", "PerfectScrollbar", "Dom<PERSON><PERSON><PERSON>zer", "_forTrack0", "$index", "$item", "this", "item", "children", "_c0", "a0", "$implicit", "SubNavbarComponent_Conditional_0_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "SubNavbarComponent_Conditional_0_Template", "ɵɵtemplate", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "component", "injector", "SubNavbarComponent_Conditional_1_ng_container_0_Template", "SubNavbarComponent_Conditional_1_Template", "defaultTemplate_r2", "ɵɵreference", "SubNavbarComponent_ng_template_2_lpx_icon_1_Template", "ɵɵelement", "icon", "SubNavbarComponent_ng_template_2_ng_container_2_Template", "SubNavbarComponent_ng_template_2_ng_template_3_Conditional_0_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵpipe", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "text", "SubNavbarComponent_ng_template_2_ng_template_3_Template", "ɵɵconditional", "SubNavbarComponent_ng_template_2_Conditional_5_Template", "expanded", "SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template", "_r4", "ɵɵgetCurrentView", "ɵɵlistener", "SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template_lpx_sub_navbar_routeClick_1_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "routeClick", "emit", "SubNavbarComponent_ng_template_2_Conditional_6_For_2_li_0_Template_lpx_sub_navbar_expand_1_listener", "onChildExpand", "child_r5", "SubNavbarComponent_ng_template_2_Conditional_6_For_2_Template", "visible", "SubNavbarComponent_ng_template_2_Conditional_6_Template", "ɵɵrepeaterCreate", "ɵɵclassProp", "ɵɵrepeater", "SubNavbarComponent_ng_template_2_Template", "_r3", "SubNavbarComponent_ng_template_2_Template_a_click_0_listener", "onItemClick", "ɵɵtemplateRefExtractor", "textTmpl_r6", "selected", "length", "link", "template", "ɵɵpureFunction1", "NavbarRoutesComponent_Conditional_1_For_1_ng_container_0_Template", "NavbarRoutesComponent_Conditional_1_For_1_For_2_ng_container_0_Template", "NavbarRoutesComponent_Conditional_1_For_1_For_2_Template", "navbarItem_r1", "itemTemplate_r2", "NavbarRoutesComponent_Conditional_1_For_1_Template", "ɵɵrepeaterTrackByIdentity", "item_r3", "groupText_r4", "items", "NavbarRoutesComponent_Conditional_1_Template", "ctx_r4", "groupedItems", "NavbarRoutesComponent_Conditional_2_ng_container_0_Template", "NavbarRoutesComponent_Conditional_2_Template", "defaultRoute_r6", "NavbarRoutesComponent_ng_template_3_For_1_ng_container_0_Template", "NavbarRoutesComponent_ng_template_3_For_1_Template", "item_r7", "NavbarRoutesComponent_ng_template_3_Template", "navbarItems", "NavbarRoutesComponent_ng_template_5_Conditional_0_Template", "item_r8", "ɵɵtextInterpolate1", "group", "NavbarRoutesComponent_ng_template_5_Template", "NavbarRoutesComponent_ng_template_7_li_0_Template", "_r9", "NavbarRoutesComponent_ng_template_7_li_0_Template_lpx_sub_navbar_expand_1_listener", "onSubnavbarExpand", "NavbarRoutesComponent_ng_template_7_li_0_Template_lpx_sub_navbar_routeClick_1_listener", "onRouteClick", "item_r10", "routerItem", "NavbarRoutesComponent_ng_template_7_Template", "_c1", "a1", "groupItems", "NavbarComponent_ng_container_2_Template", "NavbarComponent_Conditional_4_ng_container_0_Template", "NavbarComponent_Conditional_4_Template", "ctx_r1", "customContentTemplate_r3", "contentBefore", "NavbarComponent_ng_container_6_Template", "NavbarComponent_ng_container_9_Template", "NavbarComponent_ng_template_10_Template", "items_r4", "groupItems_r5", "NavbarComponent_ng_template_12_ng_container_0_ng_container_1_Template", "NavbarComponent_ng_template_12_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "component_r6", "NavbarComponent_ng_template_12_Template", "contents_r7", "NavbarComponent_ng_template_14_Template", "BreadcrumbComponent_ng_container_2_lpx_icon_2_Template", "item_r2", "BreadcrumbComponent_ng_container_2_ng_container_3_Template", "BreadcrumbComponent_ng_container_2_li_4_Template", "BreadcrumbComponent_ng_container_2_Template", "_r1", "BreadcrumbComponent_ng_container_2_Template_li_click_1_listener", "ctx_r2", "onClick", "BreadcrumbComponent_ng_container_2_Template_li_lpxClickOutside_1_listener", "last_r4", "last", "linkTemplate_r5", "textTemplate_r6", "BreadcrumbComponent_ng_template_4_Template", "BreadcrumbComponent_ng_template_6_Template", "AvatarComponent_div_0_ng_container_2_Template", "avatar", "source", "AvatarComponent_div_0_ng_container_3_Template", "ɵɵsanitizeUrl", "AvatarComponent_div_0_Template", "type", "_c2", "FooterComponent_ng_container_0_a_3_Template", "footerValues_r1", "ngIf", "descUrl", "desc", "FooterComponent_ng_container_0_ng_template_4_Template", "FooterComponent_ng_container_0_ng_container_7_a_1_Template", "footerLink_r2", "FooterComponent_ng_container_0_ng_container_7_Template", "FooterComponent_ng_container_0_Template", "footerDesc_r3", "footerLinks", "LpxPerfectScrollbar", "constructor", "router", "LPX_LANGUAGE", "DataStore", "state", "state$", "value", "initialState", "update$", "sliceState", "selector", "compareFn", "s1", "s2", "pipe", "sliceUpdate", "filterFn", "x", "undefined", "patch", "patchedState", "Array", "isArray", "next", "set", "reset", "LanguageTranslateKeys", "LanguageTranslateDefaults", "SettingsTitle", "LanguageService", "selectedLanguage", "store", "languages", "id", "convertLanguageToNavbarItem", "lang", "displayName", "action", "setSelectedLanguage", "selectedLanguage$", "languageChange$", "a", "b", "cultureName", "languages$", "languagesAsNavbarItems$", "languagesAsSettingsGroup$", "init", "setLanguages", "find", "ɵfac", "LanguageService_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "decorators", "LPX_TRANSLATE_SERVICE_TOKEN", "LPX_TRANSLATE_TOKEN", "LpxLanguageModule", "forRoot", "options", "ngModule", "providers", "provide", "useValue", "multi", "LpxLanguageModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "ICON_MAP", "bagFill", "bellFill", "calendarWeek", "chatDots", "chevronDown", "chevronUp", "gearConnected", "filterFill", "layoutThreeColumns", "moon", "square", "sunset", "sunup", "star", "xCircleFill", "LEPTON_X_ICON_SET", "IconComponent", "styleClass", "iconSet", "iconClass", "IconComponent_Factory", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "decls", "vars", "consts", "IconComponent_Template", "dependencies", "Ng<PERSON><PERSON>", "encapsulation", "None", "LpxIconModule", "LpxIconModule_Factory", "exports", "BrandLogoComponent", "BrandLogoComponent_Factory", "BrandLogoComponent_Template", "RouterLink", "LpxBrandLogoModule", "LpxBrandLogoModule_Factory", "LayoutService", "containerClass", "containerClass$", "setClass", "cssClass", "patchStore", "addClass", "removeClass", "index", "findIndex", "update", "slice", "removeClasses", "classlist", "filteredClasslist", "clss", "includes", "toggleClass", "LayoutService_Factory", "CONTENT_BEFORE_ROUTES", "CONTENT_AFTER_ROUTES", "LPX_MENU_ITEMS", "sortItems", "order", "flatArrayDeepToObject", "arr", "reduce", "acc", "curr", "getStream$", "Promise", "isNullOrUndefined", "obj", "createGroupMap", "list", "othersGroupKey", "skipGroupCheck", "some", "node", "Boolean", "mapGroup", "Map", "Error", "get", "push", "getItemsFromGroup", "pred", "OTHERS_GROUP_KEY", "NavbarService", "menuItems", "addContainerLinks", "navbarItems$", "groupedNavbarItems$", "navItems", "f", "expandItemByLink$", "subscribe", "addNavbarItems", "setNavbarItems", "add<PERSON><PERSON><PERSON><PERSON>", "parent", "findById", "location", "i", "shift", "containerLink", "updated", "findByLink", "findByProp", "events", "e", "expandItems", "route", "getRouteItem", "calculateExpandState", "url", "indexes", "matchIndex", "newItem", "collapse<PERSON><PERSON><PERSON><PERSON>", "child", "prop", "itemIndex", "for<PERSON>ach", "NavbarService_Factory", "NavbarRoutesDirective", "NavbarRoutesDirective_Factory", "ɵdir", "ɵɵdefineDirective", "exportAs", "LogoPanelDirective", "LogoPanelDirective_Factory", "RoutesService", "currentNavigation", "pathname", "initialValue", "RoutesService_Factory", "LpxVisibleDirective", "lpxVisible", "condition$", "checkType", "subscribeToCondition", "viewContainerRef", "templateRef", "ngOnInit", "updateVisibility", "ngOnDestroy", "conditionSubscription", "unsubscribe", "isVisible", "clear", "createEmbeddedView", "LpxVisibleDirective_Factory", "ViewContainerRef", "standalone", "UserProfileService", "user$", "setUser", "user", "patchUser", "UserProfileService_Factory", "BodyService", "body", "document", "querySelector", "classes", "overflowYHidden", "disableScrollY", "classList", "add", "enableScrollY", "remove", "BodyService_Factory", "DefaultTranslateService", "get$", "key", "defaultValue", "DefaultTranslateService_Factory", "LpxThemeTranslateService", "translateValues", "translateService", "_content", "translate$", "LpxThemeTranslateService_Factory", "DefaultAuthService", "userProfileService", "isUserExists$", "Object", "keys", "navigateToLogin", "DefaultAuthService_Factory", "LPX_AUTH_SERVICE_TOKEN", "LPX_AUTH_SERVICE_PROVIDER", "useClass", "LpxLocalStorageService", "localStorage", "getItem", "removeItem", "setItem", "LpxLocalStorageService_Factory", "LpxPerfectScrollbarService", "setElement", "elementRef", "setOptions", "createScrollbar", "perfectScrollbar", "nativeElement", "onResize", "afterViewInit", "subscription", "event", "element", "topAfterNavigate", "leftAfterNavigate", "scrollTop", "scrollLeft", "ɵLpxPerfectScrollbarService_BaseFactory", "LpxPerfectScrollbarService_Factory", "ɵɵgetInheritedFactory", "TranslatePipe", "lpxThemeTranslateService", "transform", "TranslatePipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "SubNavbarComponent", "expand", "menuItem", "action$", "result", "processItemClick", "<PERSON><PERSON><PERSON><PERSON>", "SubNavbarComponent_Factory", "ɵɵInputFlags", "SignalBased", "outputs", "SubNavbarComponent_Template", "NgComponentOutlet", "NgIf", "NgTemplateOutlet", "AsyncPipe", "NavbarRoutesComponent", "itemsFromGroup", "routesService", "isExpandedOrSelected", "fixNavbarItemsByRouter", "expandedItems", "expandedGroupItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkChildrenIncludesItem", "fixNavbarItems", "currentUrl", "hasUrlInChildren", "found", "NavbarRoutesComponent_Factory", "NavbarRoutesComponent_Template", "NavbarComponent", "layoutService", "platformId", "service", "didResized", "showFilterMenu$", "flatContents", "contentAfter", "toggleSidebarHover", "ngAfterViewChecked", "window", "dispatchEvent", "Event", "contents", "val", "concat", "NavbarComponent_Factory", "contentQueries", "NavbarComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "routesTemplate", "first", "logoPanel", "NavbarComponent_Template", "NavbarComponent_Template_lpx_icon_click_3_listener", "defaultRouteTemplate_r8", "defaultLogo_r9", "ɵɵpureFunction2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "read", "ToObservablePipe", "ToObservablePipe_Factory", "ToObservableModule", "ToObservableModule_Factory", "SafeHtmlPipe", "sanitizer", "sanitize", "HTML", "SafeHtmlPipe_Factory", "LpxTranslateModule", "LpxTranslateModule_Factory", "exportedDeclarations$1", "LpxNavbarModule", "contentAfterRoutes", "contentBeforeRoutes", "<PERSON><PERSON><PERSON><PERSON>", "LpxNavbarModule_Factory", "BreadcrumbService", "items$", "insert", "setItems", "BreadcrumbService_Factory", "ClickOutsideDirective", "lpxClickOutside", "exceptedRefs", "onDocumentClick", "contains", "target", "ref", "ClickOutsideDirective_Factory", "hostBindings", "ClickOutsideDirective_HostBindings", "ClickOutsideDirective_click_HostBindingHandler", "ɵɵresolveDocument", "BreadcrumbComponent", "BreadcrumbComponent_Factory", "BreadcrumbComponent_Template", "LpxClickOutsideModule", "LpxClickOutsideModule_Factory", "BreadcrumbRouteListenerService", "navbarService", "breadcrumbService", "subscribeRoute", "activeItem", "breadCrumbItems", "siblings", "mapNavbarItemToBreadcrumbItem", "breadcrumbText", "BreadcrumbRouteListenerService_Factory", "exportedDeclarations", "LpxBreadcrumbModule", "useFactory", "breadCrumbInit", "deps", "LpxBreadcrumbModule_Factory", "subs", "LPX_TRANSLATE_SERVICE_PROVIDER", "LPX_TRANSLATE_PROVIDERS", "LPX_INITIAL_STYLES", "LPX_STYLE_FINAL", "LPX_LAYOUT_STYLE_FINAL", "StyleService", "initialStyles", "lastInjectedStyle", "initialized$", "initStyles", "direction", "_this", "_asyncToGenerator", "style", "loadStyle", "_this2", "resolve", "reject", "linkElem", "createLinkElem", "appStyles", "isConnected", "insertAdjacentElement", "head", "append<PERSON><PERSON><PERSON>", "replaceStyle", "_this3", "loaded", "bundleName", "reloadInitialStyles", "_this4", "createElement", "rel", "href", "onload", "StyleService_Factory", "Document", "createStyleFactory", "handler", "styleLoadFactory", "styleList", "layoutStyles", "LPX_STYLE_PROVIDERS", "loadInitialStyles", "styleService", "languageService", "isRTL", "WINDOW", "createWindowProvider", "windowObj", "RESPONSIVE_BREAKPOINTS", "ResponsiveService", "providedBreakpoints", "defaultBreakpoint", "width", "breakpoints", "buildBreakpoints", "getCurrentSize", "height", "innerHeight", "innerWidth", "mapSizeToBreakpoint", "s", "currentSize$", "shouldRenderWithCurrentSize", "query", "matchQuery", "setupListener", "currentResolution$", "current", "sort", "tokens", "split", "findInTokens", "size", "matchedBreakpoint", "breakpoint", "shouldBeBigger", "ResponsiveService_Factory", "ResponsiveDirective", "viewContainer", "parentCdr", "hasRendered", "sub", "render", "shouldRender", "detectChanges", "_", "ResponsiveDirective_Factory", "ChangeDetectorRef", "LpxResponsiveModule", "LpxResponsiveModule_Factory", "LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS", "sm", "md", "lg", "xl", "createResponsiveProvider", "responsiveSettings", "createDirectionProvider", "listenDirection", "listenDirectionChange", "_ref", "documentElement", "dir", "_x", "apply", "arguments", "LPX_PERFECT_SCROLLBAR", "LpxCoreModule", "iconSettings", "languageSettings", "navbarSettings", "listenDirectionChanges", "LpxCoreModule_Factory", "AvatarComponent", "AvatarComponent_Factory", "AvatarComponent_Template", "NgSwitch", "NgSwitchCase", "LpxAvatarModule", "LpxAvatarModule_Factory", "FooterLinksService", "footerInfo$", "setFooterInfo", "links", "FooterLinksService_Factory", "FooterComponent", "footerValues$", "FooterComponent_Factory", "FooterComponent_Template", "LpxFooterModule", "LpxFooterModule_Factory", "PerfectScrollbarDirective", "lpxPerfectService", "lpxPerfectScrollbarOptions", "ngAfterViewInit", "PerfectScrollbarDirective_Factory", "PerfectScrollbarDirective_HostBindings", "PerfectScrollbarDirective_resize_HostBindingHandler", "ɵɵresolveWindow", "BreadcrumbPanelDirective", "BreadcrumbPanelDirective_Factory", "ContentPanelDirective", "ContentPanelDirective_Factory", "CurrentUserImagePanelDirective", "CurrentUserImagePanelDirective_Factory", "CurrentUserPanelDirective", "CurrentUserPanelDirective_Factory", "FooterPanelDirective", "FooterPanelDirective_Factory", "LanguagePanelDirective", "LanguagePanelDirective_Factory", "MobileNavbarPanelDirective", "MobileNavbarPanelDirective_Factory", "NavbarPanelDirective", "NavbarPanelDirective_Factory", "NavitemPanelDirective", "NavitemPanelDirective_Factory", "ToolbarPanelDirective", "ToolbarPanelDirective_Factory", "TopNavbarPanelDirective", "TopNavbarPanelDirective_Factory", "SettingsPanelDirective", "SettingsPanelDirective_Factory", "declarationsAndExports", "PanelsModule", "PanelsModule_Factory", "ToolbarService", "addItem", "patchItem", "itemId", "updateItems", "ToolbarService_Factory"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@volo/ngx-lepton-x.core/fesm2022/volo-ngx-lepton-x.core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, InjectionToken, Injectable, Inject, NgModule, Component, ViewEncapsulation, Input, Directive, Optional, Pipe, Injector, input, EventEmitter, Output, effect, PLATFORM_ID, TemplateRef, ContentChild, SecurityContext, HostListener, APP_INITIALIZER, SkipSelf, ElementRef } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { Router, RouterModule, NavigationEnd, NavigationError, NavigationCancel } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { CommonModule, isPlatformBrowser, DOCUMENT } from '@angular/common';\nimport { map, distinctUntilChanged, filter, take, tap, switchMap, startWith, distinctUntilKeyChanged } from 'rxjs/operators';\nimport { BehaviorSubject, Subject, Observable, from, of, EMPTY, filter as filter$1, map as map$1, combineLatest, fromEvent, Subscription } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport PerfectScrollbar from 'perfect-scrollbar';\nimport { DomSanitizer } from '@angular/platform-browser';\n\nclass LpxPerfectScrollbar {\n    constructor() {\n        this.router = inject(Router);\n    }\n}\n\nconst LPX_LANGUAGE = new InjectionToken('LPX_LANGUAGE');\n\nclass DataStore {\n    get state() {\n        return this.state$.value;\n    }\n    constructor(initialState) {\n        this.initialState = initialState;\n        this.state$ = new BehaviorSubject(this.initialState);\n        this.update$ = new Subject();\n        this.sliceState = (selector, compareFn = (s1, s2) => s1 === s2) => this.state$.pipe(map(selector), distinctUntilChanged(compareFn));\n        this.sliceUpdate = (selector, filterFn = (x) => x !== undefined) => this.update$.pipe(map(selector), filter(filterFn));\n    }\n    patch(state) {\n        let patchedState = state;\n        if (typeof state === 'object' && !Array.isArray(state)) {\n            patchedState = { ...this.state, ...state };\n        }\n        this.state$.next(patchedState);\n        this.update$.next(patchedState);\n    }\n    set(state) {\n        this.state$.next(state);\n        this.update$.next(state);\n    }\n    reset() {\n        this.set(this.initialState);\n    }\n}\n\nvar LanguageTranslateKeys;\n(function (LanguageTranslateKeys) {\n    LanguageTranslateKeys[\"SettingsTitle\"] = \"language.settings.title\";\n})(LanguageTranslateKeys || (LanguageTranslateKeys = {}));\nconst LanguageTranslateDefaults = {\n    [LanguageTranslateKeys.SettingsTitle]: 'Language Options',\n};\n\nclass LanguageService {\n    get selectedLanguage() {\n        return this.store.state.selectedLanguage;\n    }\n    constructor(languages) {\n        this.languages = languages;\n        this.store = new DataStore({ languages: [] });\n        this.id = 'languages';\n        this.convertLanguageToNavbarItem = (languages) => {\n            return languages.map((lang) => ({\n                icon: '',\n                text: lang.displayName,\n                selected: lang.selected,\n                action: () => {\n                    this.setSelectedLanguage(lang);\n                    return true;\n                },\n            }));\n        };\n        this.selectedLanguage$ = this.store.sliceState(({ selectedLanguage }) => selectedLanguage);\n        this.languageChange$ = this.selectedLanguage$.pipe(\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        filter((lang) => lang !== undefined), distinctUntilChanged((a, b) => a?.cultureName === b?.cultureName));\n        this.languages$ = this.store.sliceState((state) => state.languages);\n        this.languagesAsNavbarItems$ = this.languages$.pipe(map(this.convertLanguageToNavbarItem));\n        //TODO: PROVIDE API\n        this.languagesAsSettingsGroup$ = this.languagesAsNavbarItems$.pipe(map((languages) => ({\n            text: LanguageTranslateKeys.SettingsTitle,\n            icon: 'bi bi-globe',\n            id: this.id,\n            children: languages,\n        })));\n        this.init(this.languages);\n    }\n    setLanguages(languages) {\n        this.init(languages);\n    }\n    init(languages) {\n        this.store.patch({\n            languages,\n            selectedLanguage: languages.find((lang) => lang.selected),\n        });\n    }\n    setSelectedLanguage(lang) {\n        this.store.patch({\n            selectedLanguage: lang,\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LanguageService, deps: [{ token: LPX_LANGUAGE }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LanguageService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LanguageService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [LPX_LANGUAGE]\n                }] }] });\n\nconst LPX_TRANSLATE_SERVICE_TOKEN = new InjectionToken('LPX_TRANSLATE_SERVICE_TOKEN');\nconst LPX_TRANSLATE_TOKEN = new InjectionToken('LPX_TRANSLATE_TOKEN');\n\nclass LpxLanguageModule {\n    static forRoot(options) {\n        return {\n            ngModule: LpxLanguageModule,\n            providers: [\n                {\n                    provide: LPX_LANGUAGE,\n                    useValue: options?.languages || [],\n                },\n                {\n                    provide: LPX_TRANSLATE_TOKEN,\n                    useValue: [LanguageTranslateDefaults],\n                    multi: true,\n                },\n                LanguageService,\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLanguageModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLanguageModule, imports: [CommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLanguageModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLanguageModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [],\n                    imports: [CommonModule],\n                }]\n        }] });\n\nconst ICON_MAP = {\n    bagFill: 'bi bi-bag-fill',\n    bellFill: 'bi bi-bell-fill',\n    calendarWeek: 'bi bi-calendar2-week',\n    chatDots: 'bi bi-chat-dots',\n    chevronDown: 'bi bi-chevron-down',\n    chevronUp: 'bi bi-chevron-up',\n    gearConnected: 'bi bi-gear-wide-connected',\n    filter: 'bi bi-filter',\n    filterFill: 'bi bi-filter-circle-fill',\n    layoutThreeColumns: 'bi bi-layout-three-columns',\n    moon: 'bi bi-moon',\n    square: 'bi bi-square',\n    sunset: 'bi bi-brightness-alt-high-fill',\n    sunup: 'bi bi-brightness-high-fill',\n    star: 'bi bi-star',\n    x: 'bi bi-x',\n    xCircleFill: 'bi bi-x-circle-fill',\n};\nconst LEPTON_X_ICON_SET = new InjectionToken('LEPTON_X_ICON_SET');\n\nclass IconComponent {\n    get styleClass() {\n        return this.iconSet[this.iconClass] || this.iconClass;\n    }\n    constructor(iconSet) {\n        this.iconSet = iconSet;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: IconComponent, deps: [{ token: LEPTON_X_ICON_SET }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: IconComponent, selector: \"lpx-icon\", inputs: { iconClass: \"iconClass\" }, ngImport: i0, template: ` <i class=\"lpx-icon\" [ngClass]=\"styleClass\" aria-hidden=\"true\"></i> `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: IconComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'lpx-icon',\n                    template: ` <i class=\"lpx-icon\" [ngClass]=\"styleClass\" aria-hidden=\"true\"></i> `,\n                    encapsulation: ViewEncapsulation.None,\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [LEPTON_X_ICON_SET]\n                }] }], propDecorators: { iconClass: [{\n                type: Input\n            }] } });\n\nclass LpxIconModule {\n    static forRoot(options) {\n        return {\n            ngModule: LpxIconModule,\n            providers: [\n                {\n                    provide: LEPTON_X_ICON_SET,\n                    useValue: options?.iconSet || ICON_MAP,\n                },\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxIconModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxIconModule, declarations: [IconComponent], imports: [CommonModule], exports: [IconComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxIconModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxIconModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [IconComponent],\n                    imports: [CommonModule],\n                    exports: [IconComponent],\n                }]\n        }] });\n\nclass BrandLogoComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BrandLogoComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: BrandLogoComponent, selector: \"lpx-brand-logo\", ngImport: i0, template: \"<a routerLink=\\\"/\\\">\\r\\n  <div class=\\\"lpx-brand-logo\\\"></div>\\r\\n</a>\\r\\n\", dependencies: [{ kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BrandLogoComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-brand-logo', encapsulation: ViewEncapsulation.None, template: \"<a routerLink=\\\"/\\\">\\r\\n  <div class=\\\"lpx-brand-logo\\\"></div>\\r\\n</a>\\r\\n\" }]\n        }] });\n\nclass LpxBrandLogoModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBrandLogoModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBrandLogoModule, declarations: [BrandLogoComponent], imports: [RouterModule], exports: [BrandLogoComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBrandLogoModule, imports: [RouterModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBrandLogoModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [BrandLogoComponent],\n                    imports: [\n                        RouterModule\n                    ],\n                    exports: [BrandLogoComponent]\n                }]\n        }] });\n\nclass LayoutService {\n    constructor() {\n        this.store = new DataStore({\n            containerClass: [''],\n        });\n        this.containerClass$ = this.store.sliceState(({ containerClass }) => containerClass || []);\n    }\n    setClass(cssClass) {\n        const containerClass = Array.isArray(cssClass) ? cssClass : [cssClass];\n        this.patchStore(containerClass);\n    }\n    addClass(cssClass) {\n        const { containerClass } = this.store.state;\n        this.patchStore([...containerClass, cssClass]);\n    }\n    removeClass(cssClass) {\n        const { containerClass } = this.store.state;\n        const index = containerClass.findIndex(item => item === cssClass);\n        if (index === -1)\n            return;\n        const update = [...containerClass.slice(0, index), ...containerClass.slice(index + 1)];\n        this.patchStore(update);\n    }\n    removeClasses(classlist) {\n        const { containerClass } = this.store.state;\n        const filteredClasslist = containerClass.filter(clss => !classlist.includes(clss));\n        this.patchStore(filteredClasslist);\n    }\n    toggleClass(cssClass) {\n        const { containerClass } = this.store.state;\n        const index = containerClass.findIndex(item => item === cssClass);\n        if (index === -1) {\n            this.addClass(cssClass);\n        }\n        else {\n            this.removeClass(cssClass);\n        }\n    }\n    patchStore(containerClass) {\n        this.store.patch({\n            containerClass,\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LayoutService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LayoutService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LayoutService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nconst CONTENT_BEFORE_ROUTES = new InjectionToken('CONTENT_BEFORE_ROUTES');\nconst CONTENT_AFTER_ROUTES = new InjectionToken('CONTENT_AFTER_ROUTES');\nconst LPX_MENU_ITEMS = new InjectionToken('LPX_MENU_ITEMS');\n\nfunction sortItems(a, b) {\n    if (!a.order) {\n        return 1;\n    }\n    if (!b.order) {\n        return -1;\n    }\n    return a.order - b.order;\n}\nfunction flatArrayDeepToObject(arr) {\n    return arr.reduce((acc, curr) => ({\n        ...acc,\n        ...(Array.isArray(curr) ? flatArrayDeepToObject(curr) : curr),\n    }), {});\n}\nfunction getStream$(source) {\n    return source instanceof Observable\n        ? source\n        : source instanceof Promise\n            ? from(source)\n            : of(source);\n}\nfunction isNullOrUndefined(obj) {\n    return obj === null || obj === undefined;\n}\nfunction isArray(obj) {\n    return Array.isArray(obj);\n}\n\nfunction createGroupMap(list, othersGroupKey, skipGroupCheck = false) {\n    if (!skipGroupCheck &&\n        (!isArray(list) || !list.some((node) => Boolean(node.group))))\n        return undefined;\n    const mapGroup = new Map();\n    for (const node of list) {\n        const group = node?.group || othersGroupKey;\n        if (typeof group !== 'string') {\n            throw new Error(`Invalid group: ${group}`);\n        }\n        const items = mapGroup.get(group) || [];\n        items.push(node);\n        mapGroup.set(group, items);\n    }\n    return mapGroup;\n}\nfunction getItemsFromGroup(list, pred) {\n    return list?.reduce((acc, { items }) => [...acc, ...(pred ? items.filter(pred) : items)], []);\n}\n\nconst OTHERS_GROUP_KEY = 'AbpUi::OthersGroup';\n\nclass NavbarService {\n    constructor() {\n        this.router = inject(Router);\n        this.menuItems = inject(LPX_MENU_ITEMS);\n        this.store = new DataStore(this.addContainerLinks(this.menuItems));\n        this.navbarItems$ = this.store.sliceState((state) => state);\n        this.groupedNavbarItems$ = this.store\n            .sliceState((state) => state)\n            .pipe(filter((navItems) => navItems.some((f) => !!f.group)), map((items) => {\n            const map = createGroupMap(items, OTHERS_GROUP_KEY) || [];\n            return Array.from(map, ([group, items]) => ({\n                group,\n                items,\n            }));\n        }));\n        this.expandItemByLink$().pipe(take(1)).subscribe();\n    }\n    addNavbarItems(...menuItems) {\n        this.store.set([...this.store.state, ...this.addContainerLinks(menuItems)]);\n    }\n    setNavbarItems(...menuItems) {\n        this.store.set([...this.addContainerLinks(menuItems)]);\n    }\n    // TODO: muhammed: refactor this method to be readable\n    addChildren(id, ...menuItems) {\n        const parent = this.findById(id, this.store.state);\n        const update = (items, location, link = '') => {\n            const i = location.shift();\n            return items.reduce((acc, item, index) => {\n                return [\n                    ...acc,\n                    ...(index === i\n                        ? [\n                            {\n                                ...item,\n                                children: !location.length\n                                    ? [\n                                        ...(item.children || []),\n                                        ...this.addContainerLinks(menuItems, `${link}/${item.containerLink}`),\n                                    ]\n                                    : update(item.children || [], location, `${link}/${item.containerLink}`),\n                            },\n                        ]\n                        : [item]),\n                ];\n            }, []);\n        };\n        const updated = update(this.store.state, parent.location);\n        this.store.patch(updated);\n    }\n    findByLink(link, items) {\n        return this.findByProp('link', link, items);\n    }\n    expandItemByLink$() {\n        return this.router.events.pipe(filter((e) => e instanceof NavigationEnd), tap(() => this.expandItems()));\n    }\n    expandItems() {\n        const route = this.getRouteItem();\n        if (route?.item) {\n            const expanded = this.calculateExpandState(this.store.state, route.location);\n            this.store.patch(expanded);\n        }\n    }\n    getRouteItem() {\n        return this.findByLink(this.router.url);\n    }\n    calculateExpandState(items, indexes) {\n        const matchIndex = indexes.shift();\n        return items.reduce((acc, item, index) => {\n            if (index === matchIndex) {\n                return [\n                    ...acc,\n                    {\n                        ...item,\n                        expanded: true,\n                        selected: true,\n                        children: this.calculateExpandState(item.children || [], indexes),\n                    },\n                ];\n            }\n            const newItem = {\n                ...item,\n                ...(item.children\n                    ? { children: this.collapseChildren(item.children) }\n                    : {}),\n            };\n            return [...acc, { ...newItem, expanded: false, selected: false }];\n        }, []);\n    }\n    collapseChildren(children) {\n        return [\n            ...children.map((child) => ({\n                ...child,\n                expanded: false,\n                selected: false,\n                children: child.children ? this.collapseChildren(child.children) : [],\n            })),\n        ];\n    }\n    findById(id, items) {\n        return this.findByProp('id', id, items);\n    }\n    findByProp(prop, value, items, location = []) {\n        const navbarItems = items || this.store.state;\n        const itemIndex = navbarItems.findIndex((i) => i[prop] === value);\n        let item;\n        if (itemIndex === -1) {\n            navbarItems.forEach((i, index) => {\n                if (i.children) {\n                    const child = this.findByProp(prop, value, i.children, [\n                        ...location,\n                        index,\n                    ]);\n                    if (child?.item) {\n                        item = child.item;\n                        location = child.location;\n                    }\n                }\n            });\n        }\n        else {\n            item = navbarItems[itemIndex];\n            location.push(itemIndex);\n        }\n        return { item, location };\n    }\n    addContainerLinks(items, link = '') {\n        return items.map((item) => ({\n            ...item,\n            ...(item.link && link ? { link: `${link}/${item.link}` } : {}),\n            children: this.addContainerLinks(item.children || [], `${link ? link + '/' : ''}${item.containerLink || ''}`),\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [] });\n\nclass NavbarRoutesDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarRoutesDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: NavbarRoutesDirective, selector: \"[lpx-navbar-routes],[lpxNavbarRoutes]\", exportAs: [\"lpxNavbarRoutes\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarRoutesDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[lpx-navbar-routes],[lpxNavbarRoutes]',\n                    exportAs: 'lpxNavbarRoutes',\n                }]\n        }] });\n\nclass LogoPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LogoPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: LogoPanelDirective, selector: \"ng-template[lpx-logo-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LogoPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-logo-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass RoutesService {\n    constructor() {\n        this.router = inject(Router);\n        this.currentNavigation = toSignal(this.router.events.pipe(filter((e) => e instanceof NavigationEnd), \n        //TODO: location object might be problem in the future for SSR\n        map(() => location.pathname)), { initialValue: location.pathname });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: RoutesService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: RoutesService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: RoutesService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass LpxVisibleDirective {\n    set lpxVisible(value) {\n        this.condition$ = checkType(value);\n        this.subscribeToCondition();\n    }\n    constructor(viewContainerRef, templateRef) {\n        this.viewContainerRef = viewContainerRef;\n        this.templateRef = templateRef;\n        this.condition$ = of(false);\n    }\n    ngOnInit() {\n        this.updateVisibility();\n    }\n    ngOnDestroy() {\n        this.conditionSubscription?.unsubscribe();\n    }\n    subscribeToCondition() {\n        this.conditionSubscription = this.condition$.subscribe((value) => {\n            this.isVisible = value;\n            this.updateVisibility();\n        });\n    }\n    updateVisibility() {\n        this.viewContainerRef.clear();\n        // it should be false not falsy\n        if (this.isVisible === false) {\n            return;\n        }\n        this.viewContainerRef.createEmbeddedView(this.templateRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxVisibleDirective, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: LpxVisibleDirective, isStandalone: true, selector: \"[lpxVisible]\", inputs: { lpxVisible: \"lpxVisible\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxVisibleDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[lpxVisible]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }], propDecorators: { lpxVisible: [{\n                type: Input,\n                args: ['lpxVisible']\n            }] } });\nfunction checkType(value) {\n    if (value instanceof Promise) {\n        return from(value);\n    }\n    else if (value instanceof Observable) {\n        return value;\n    }\n    else if (typeof value === 'boolean') {\n        return of(value);\n    }\n    else if (value === undefined || value === null) {\n        return of(true);\n    }\n    else {\n        return EMPTY;\n    }\n}\n\nclass UserProfileService {\n    constructor() {\n        this.store = new DataStore({});\n        this.user$ = this.store.sliceState((state) => state);\n    }\n    setUser(user) {\n        this.store.set(user);\n    }\n    patchUser(user) {\n        this.store.patch(user);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: UserProfileService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: UserProfileService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: UserProfileService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass BodyService {\n    constructor() {\n        this.body = document.querySelector('body');\n        this.classes = {\n            overflowYHidden: 'overflow-y-hidden',\n        };\n    }\n    disableScrollY() {\n        this.body?.classList.add(this.classes.overflowYHidden);\n    }\n    enableScrollY() {\n        this.body?.classList.remove(this.classes.overflowYHidden);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BodyService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BodyService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BodyService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass DefaultTranslateService {\n    get$(key, defaultValue) {\n        return of(defaultValue || key || '');\n    }\n    get(key, defaultValue) {\n        return defaultValue || key || '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: DefaultTranslateService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: DefaultTranslateService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: DefaultTranslateService, decorators: [{\n            type: Injectable\n        }] });\n\nclass LpxThemeTranslateService {\n    constructor(translateValues, translateService) {\n        this.translateValues = translateValues;\n        this.translateService = translateService;\n        this._content = flatArrayDeepToObject(this.translateValues);\n    }\n    // TODO: PROVIDE API : Implement args\n    translate$(key, ...args) {\n        return this.translateService.get$(key, this._content[key]);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxThemeTranslateService, deps: [{ token: LPX_TRANSLATE_TOKEN, optional: true }, { token: LPX_TRANSLATE_SERVICE_TOKEN }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxThemeTranslateService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxThemeTranslateService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: Array, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LPX_TRANSLATE_TOKEN]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [LPX_TRANSLATE_SERVICE_TOKEN]\n                }] }] });\n\nclass DefaultAuthService {\n    constructor(userProfileService) {\n        this.userProfileService = userProfileService;\n        this.isUserExists$ = this.userProfileService.user$.pipe(map((user) => !!user && Object.keys(user).length > 0));\n    }\n    navigateToLogin() {\n        return;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: DefaultAuthService, deps: [{ token: UserProfileService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: DefaultAuthService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: DefaultAuthService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: UserProfileService }] });\n\nconst LPX_AUTH_SERVICE_TOKEN = new InjectionToken('LPX_AUTH_SERVICE_TOKEN');\n\nconst LPX_AUTH_SERVICE_PROVIDER = {\n    provide: LPX_AUTH_SERVICE_TOKEN,\n    useClass: DefaultAuthService,\n};\n\nclass LpxLocalStorageService {\n    constructor() { }\n    get length() {\n        return localStorage.length;\n    }\n    clear() {\n        localStorage.clear();\n    }\n    getItem(key) {\n        return localStorage.getItem(key);\n    }\n    key(index) {\n        return localStorage.key(index);\n    }\n    removeItem(key) {\n        localStorage.removeItem(key);\n    }\n    setItem(key, value) {\n        localStorage.setItem(key, value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLocalStorageService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLocalStorageService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxLocalStorageService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [] });\n\nclass LpxPerfectScrollbarService extends LpxPerfectScrollbar {\n    setElement(value) {\n        if (value) {\n            this.elementRef = value;\n        }\n    }\n    setOptions(value) {\n        if (value) {\n            this.options = value;\n        }\n    }\n    createScrollbar() {\n        this.perfectScrollbar = new PerfectScrollbar(this.elementRef.nativeElement, this.options);\n    }\n    onResize() {\n        this.perfectScrollbar.update();\n    }\n    afterViewInit() {\n        this.createScrollbar();\n        this.subscription?.unsubscribe();\n        this.subscription = this.router.events\n            .pipe(filter$1((event) => event instanceof NavigationEnd ||\n            event instanceof NavigationError ||\n            event instanceof NavigationCancel))\n            .subscribe(() => {\n            const { element } = this.perfectScrollbar;\n            const { topAfterNavigate, leftAfterNavigate } = this.options || {};\n            element.scrollTop = topAfterNavigate || 0;\n            element.scrollLeft = leftAfterNavigate || 0;\n        });\n    }\n    ngOnDestroy() {\n        this.subscription?.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxPerfectScrollbarService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxPerfectScrollbarService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxPerfectScrollbarService, decorators: [{\n            type: Injectable\n        }] });\n\nclass TranslatePipe {\n    constructor(lpxThemeTranslateService) {\n        this.lpxThemeTranslateService = lpxThemeTranslateService;\n    }\n    transform(value, ...args) {\n        return this.lpxThemeTranslateService.translate$(value, args);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TranslatePipe, deps: [{ token: LpxThemeTranslateService }], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: TranslatePipe, name: \"lpxTranslate\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TranslatePipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'lpxTranslate',\n                }]\n        }], ctorParameters: () => [{ type: LpxThemeTranslateService }] });\n\nclass SubNavbarComponent {\n    constructor() {\n        this.injector = inject(Injector);\n        this.routerItem = input();\n        this.routeClick = new EventEmitter();\n        this.expand = new EventEmitter();\n    }\n    onItemClick(menuItem) {\n        let action$ = of(true);\n        if (menuItem.action) {\n            const result = menuItem.action();\n            action$ = getStream$(result);\n        }\n        action$.pipe(take(1)).subscribe((result) => {\n            if (result) {\n                this.processItemClick(menuItem);\n            }\n        });\n    }\n    onChildExpand(child) {\n        if (child.expanded) {\n            this.item?.children\n                ?.filter((otherChild) => otherChild !== child)\n                .forEach((otherChild) => {\n                otherChild.expanded = false;\n                otherChild.selected = false;\n            });\n        }\n    }\n    processItemClick(menuItem) {\n        if (menuItem.children?.length) {\n            menuItem.expanded = !menuItem.expanded;\n            this.expand.emit(menuItem);\n            return;\n        }\n        this.routeClick.emit(menuItem);\n        if (!this.routerItem()) {\n            menuItem.selected = true;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: SubNavbarComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: SubNavbarComponent, selector: \"lpx-sub-navbar\", inputs: { item: { classPropertyName: \"item\", publicName: \"item\", isSignal: false, isRequired: false, transformFunction: null }, routerItem: { classPropertyName: \"routerItem\", publicName: \"routerItem\", isSignal: true, isRequired: false, transformFunction: null } }, outputs: { routeClick: \"routeClick\", expand: \"expand\" }, ngImport: i0, template: \"@if (item.component) {\\r\\n  <ng-container\\r\\n    *ngComponentOutlet=\\\"item.component; injector: injector\\\"\\r\\n  ></ng-container>\\r\\n} @else {\\r\\n  <ng-container *ngTemplateOutlet=\\\"defaultTemplate\\\"></ng-container>\\r\\n}\\r\\n<ng-template #defaultTemplate>\\r\\n  <a\\r\\n    class=\\\"lpx-menu-item-link\\\"\\r\\n    [routerLink]=\\\"item.link\\\"\\r\\n    [class.selected]=\\\"item.selected\\\"\\r\\n    [class.expanded]=\\\"item.children?.length && item.expanded\\\"\\r\\n    (click)=\\\"onItemClick(item)\\\"\\r\\n  >\\r\\n    <lpx-icon\\r\\n      class=\\\"lpx-menu-item-icon\\\"\\r\\n      *ngIf=\\\"item.icon\\\"\\r\\n      [iconClass]=\\\"item.icon\\\"\\r\\n    ></lpx-icon>\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"\\r\\n        item.template || textTmpl;\\r\\n        context: { $implicit: item }\\r\\n      \\\"\\r\\n    ></ng-container>\\r\\n    <ng-template #textTmpl>\\r\\n      @if (item.text) {\\r\\n        <span class=\\\"lpx-menu-item-text hidden-in-hover-trigger\\\">{{\\r\\n          item.text | lpxTranslate | async\\r\\n        }}</span>\\r\\n      }\\r\\n    </ng-template>\\r\\n\\r\\n    @if (item.children?.length) {\\r\\n      <lpx-icon\\r\\n        [iconClass]=\\\"item.expanded ? 'chevronUp' : 'chevronDown'\\\"\\r\\n        class=\\\"dd-icon hidden-in-hover-trigger\\\"\\r\\n      >\\r\\n      </lpx-icon>\\r\\n    }\\r\\n  </a>\\r\\n\\r\\n  @if (item.children?.length) {\\r\\n    <ul\\r\\n      class=\\\"lpx-inner-menu hidden-in-hover-trigger\\\"\\r\\n      [class.collapsed]=\\\"!item.expanded\\\"\\r\\n    >\\r\\n      @for (child of item.children; track item.children) {\\r\\n        <li\\r\\n          class=\\\"lpx-inner-menu-item\\\"\\r\\n          *lpxVisible=\\\"!child.visible || child.visible(child, injector)\\\"\\r\\n        >\\r\\n          <lpx-sub-navbar\\r\\n            [item]=\\\"child\\\"\\r\\n            (routeClick)=\\\"this.routeClick.emit($event)\\\"\\r\\n            (expand)=\\\"onChildExpand($event)\\\"\\r\\n          ></lpx-sub-navbar>\\r\\n        </li>\\r\\n      }\\r\\n    </ul>\\r\\n  }\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.NgComponentOutlet, selector: \"[ngComponentOutlet]\", inputs: [\"ngComponentOutlet\", \"ngComponentOutletInputs\", \"ngComponentOutletInjector\", \"ngComponentOutletContent\", \"ngComponentOutletNgModule\", \"ngComponentOutletNgModuleFactory\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"component\", type: IconComponent, selector: \"lpx-icon\", inputs: [\"iconClass\"] }, { kind: \"directive\", type: LpxVisibleDirective, selector: \"[lpxVisible]\", inputs: [\"lpxVisible\"] }, { kind: \"component\", type: SubNavbarComponent, selector: \"lpx-sub-navbar\", inputs: [\"item\", \"routerItem\"], outputs: [\"routeClick\", \"expand\"] }, { kind: \"pipe\", type: i1.AsyncPipe, name: \"async\" }, { kind: \"pipe\", type: TranslatePipe, name: \"lpxTranslate\" }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: SubNavbarComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-sub-navbar', encapsulation: ViewEncapsulation.None, template: \"@if (item.component) {\\r\\n  <ng-container\\r\\n    *ngComponentOutlet=\\\"item.component; injector: injector\\\"\\r\\n  ></ng-container>\\r\\n} @else {\\r\\n  <ng-container *ngTemplateOutlet=\\\"defaultTemplate\\\"></ng-container>\\r\\n}\\r\\n<ng-template #defaultTemplate>\\r\\n  <a\\r\\n    class=\\\"lpx-menu-item-link\\\"\\r\\n    [routerLink]=\\\"item.link\\\"\\r\\n    [class.selected]=\\\"item.selected\\\"\\r\\n    [class.expanded]=\\\"item.children?.length && item.expanded\\\"\\r\\n    (click)=\\\"onItemClick(item)\\\"\\r\\n  >\\r\\n    <lpx-icon\\r\\n      class=\\\"lpx-menu-item-icon\\\"\\r\\n      *ngIf=\\\"item.icon\\\"\\r\\n      [iconClass]=\\\"item.icon\\\"\\r\\n    ></lpx-icon>\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"\\r\\n        item.template || textTmpl;\\r\\n        context: { $implicit: item }\\r\\n      \\\"\\r\\n    ></ng-container>\\r\\n    <ng-template #textTmpl>\\r\\n      @if (item.text) {\\r\\n        <span class=\\\"lpx-menu-item-text hidden-in-hover-trigger\\\">{{\\r\\n          item.text | lpxTranslate | async\\r\\n        }}</span>\\r\\n      }\\r\\n    </ng-template>\\r\\n\\r\\n    @if (item.children?.length) {\\r\\n      <lpx-icon\\r\\n        [iconClass]=\\\"item.expanded ? 'chevronUp' : 'chevronDown'\\\"\\r\\n        class=\\\"dd-icon hidden-in-hover-trigger\\\"\\r\\n      >\\r\\n      </lpx-icon>\\r\\n    }\\r\\n  </a>\\r\\n\\r\\n  @if (item.children?.length) {\\r\\n    <ul\\r\\n      class=\\\"lpx-inner-menu hidden-in-hover-trigger\\\"\\r\\n      [class.collapsed]=\\\"!item.expanded\\\"\\r\\n    >\\r\\n      @for (child of item.children; track item.children) {\\r\\n        <li\\r\\n          class=\\\"lpx-inner-menu-item\\\"\\r\\n          *lpxVisible=\\\"!child.visible || child.visible(child, injector)\\\"\\r\\n        >\\r\\n          <lpx-sub-navbar\\r\\n            [item]=\\\"child\\\"\\r\\n            (routeClick)=\\\"this.routeClick.emit($event)\\\"\\r\\n            (expand)=\\\"onChildExpand($event)\\\"\\r\\n          ></lpx-sub-navbar>\\r\\n        </li>\\r\\n      }\\r\\n    </ul>\\r\\n  }\\r\\n</ng-template>\\r\\n\" }]\n        }], propDecorators: { item: [{\n                type: Input\n            }], routeClick: [{\n                type: Output\n            }], expand: [{\n                type: Output\n            }] } });\n\nclass NavbarRoutesComponent {\n    get itemsFromGroup() {\n        if (!this.groupedItems) {\n            return undefined;\n        }\n        return getItemsFromGroup(this.groupedItems);\n    }\n    constructor() {\n        this.injector = inject(Injector);\n        this.routesService = inject(RoutesService);\n        this.routerItem = input();\n        this.routeClick = new EventEmitter();\n        this.isExpandedOrSelected = (item) => !!(item.expanded || item.selected);\n        this.fixNavbarItemsByRouter();\n    }\n    onSubnavbarExpand(menuItem, menuItems) {\n        if (menuItem.expanded) {\n            const items = this.itemsFromGroup || menuItems;\n            if (!items) {\n                return;\n            }\n            items\n                .filter((item) => item !== menuItem)\n                .forEach((item) => (item.expanded = false));\n        }\n    }\n    onRouteClick(menuItem, menuItems) {\n        const expandedItems = menuItems?.filter(this.isExpandedOrSelected);\n        const expandedGroupItems = this.itemsFromGroup?.filter(this.isExpandedOrSelected);\n        const items = expandedGroupItems || expandedItems;\n        if (items) {\n            items\n                .filter((item) => item !== menuItem)\n                .reduce((acc, item) => {\n                return [...acc, item, ...this.flatChildren(item.children || [])];\n            }, [])\n                ?.filter((item) => !this.checkChildrenIncludesItem(item, menuItem) &&\n                item !== menuItem)\n                .forEach((item) => {\n                item.selected = false;\n                item.expanded = false;\n            });\n        }\n        this.routeClick.emit(menuItem);\n    }\n    checkChildrenIncludesItem(item, menuItem) {\n        return (item.children?.reduce((acc, child) => acc ||\n            child === menuItem ||\n            this.checkChildrenIncludesItem(child, menuItem), false) || false);\n    }\n    flatChildren(menuItems) {\n        return (menuItems?.reduce((acc, item) => {\n            return [...acc, item, ...this.flatChildren(item.children || [])];\n        }, []) || []);\n    }\n    fixNavbarItemsByRouter() {\n        effect(() => {\n            const currentNavigation = this.routesService.currentNavigation();\n            if (!currentNavigation) {\n                return;\n            }\n            this.fixNavbarItems(currentNavigation, this.navbarItems);\n        });\n    }\n    fixNavbarItems(currentUrl, items) {\n        if (!items) {\n            return;\n        }\n        for (const item of items) {\n            if (item.children?.length) {\n                item.expanded = this.hasUrlInChildren(item, currentUrl);\n                this.fixNavbarItems(currentUrl, item.children);\n            }\n            else {\n                item.selected = item.link === currentUrl;\n            }\n        }\n    }\n    hasUrlInChildren(item, url) {\n        if (item.link === url) {\n            return true;\n        }\n        if (item.children) {\n            for (const child of item.children) {\n                const found = this.hasUrlInChildren(child, url);\n                if (found) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarRoutesComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: NavbarRoutesComponent, selector: \"lpx-navbar-routes\", inputs: { groupedItems: { classPropertyName: \"groupedItems\", publicName: \"groupedItems\", isSignal: false, isRequired: false, transformFunction: null }, navbarItems: { classPropertyName: \"navbarItems\", publicName: \"navbarItems\", isSignal: false, isRequired: false, transformFunction: null }, routerItem: { classPropertyName: \"routerItem\", publicName: \"routerItem\", isSignal: true, isRequired: false, transformFunction: null } }, outputs: { routeClick: \"routeClick\" }, ngImport: i0, template: \"<ul class=\\\"lpx-nav-menu\\\">\\r\\n  @if (groupedItems) {\\r\\n    @for (item of groupedItems; track item) {\\r\\n      <ng-container\\r\\n        *ngTemplateOutlet=\\\"groupText; context: { $implicit: item }\\\"\\r\\n      ></ng-container>\\r\\n\\r\\n      @for (navbarItem of item.items; track navbarItem) {\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"itemTemplate; context: { $implicit: navbarItem }\\\"\\r\\n        ></ng-container>\\r\\n      }\\r\\n    }\\r\\n  } @else {\\r\\n    <ng-container *ngTemplateOutlet=\\\"defaultRoute\\\"></ng-container>\\r\\n  }\\r\\n</ul>\\r\\n\\r\\n<ng-template #defaultRoute>\\r\\n  @for (item of navbarItems; track item) {\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"itemTemplate; context: { $implicit: item }\\\"\\r\\n    ></ng-container>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #groupText let-item>\\r\\n  @if (item.items.length) {\\r\\n    <li class=\\\"group-menu-item hidden-in-hover-trigger\\\">\\r\\n      {{ item.group | lpxTranslate | async }}\\r\\n    </li>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #itemTemplate let-item>\\r\\n  <li\\r\\n    class=\\\"outer-menu-item\\\"\\r\\n    *lpxVisible=\\\"!item.visible || item.visible(item, injector)\\\"\\r\\n  >\\r\\n    <lpx-sub-navbar\\r\\n      [item]=\\\"item\\\"\\r\\n      (expand)=\\\"onSubnavbarExpand($event, navbarItems)\\\"\\r\\n      (routeClick)=\\\"onRouteClick($event, navbarItems)\\\"\\r\\n      [routerItem]=\\\"routerItem()\\\"\\r\\n    ></lpx-sub-navbar>\\r\\n  </li>\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: LpxVisibleDirective, selector: \"[lpxVisible]\", inputs: [\"lpxVisible\"] }, { kind: \"component\", type: SubNavbarComponent, selector: \"lpx-sub-navbar\", inputs: [\"item\", \"routerItem\"], outputs: [\"routeClick\", \"expand\"] }, { kind: \"pipe\", type: i1.AsyncPipe, name: \"async\" }, { kind: \"pipe\", type: TranslatePipe, name: \"lpxTranslate\" }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarRoutesComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-navbar-routes', encapsulation: ViewEncapsulation.None, template: \"<ul class=\\\"lpx-nav-menu\\\">\\r\\n  @if (groupedItems) {\\r\\n    @for (item of groupedItems; track item) {\\r\\n      <ng-container\\r\\n        *ngTemplateOutlet=\\\"groupText; context: { $implicit: item }\\\"\\r\\n      ></ng-container>\\r\\n\\r\\n      @for (navbarItem of item.items; track navbarItem) {\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"itemTemplate; context: { $implicit: navbarItem }\\\"\\r\\n        ></ng-container>\\r\\n      }\\r\\n    }\\r\\n  } @else {\\r\\n    <ng-container *ngTemplateOutlet=\\\"defaultRoute\\\"></ng-container>\\r\\n  }\\r\\n</ul>\\r\\n\\r\\n<ng-template #defaultRoute>\\r\\n  @for (item of navbarItems; track item) {\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"itemTemplate; context: { $implicit: item }\\\"\\r\\n    ></ng-container>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #groupText let-item>\\r\\n  @if (item.items.length) {\\r\\n    <li class=\\\"group-menu-item hidden-in-hover-trigger\\\">\\r\\n      {{ item.group | lpxTranslate | async }}\\r\\n    </li>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #itemTemplate let-item>\\r\\n  <li\\r\\n    class=\\\"outer-menu-item\\\"\\r\\n    *lpxVisible=\\\"!item.visible || item.visible(item, injector)\\\"\\r\\n  >\\r\\n    <lpx-sub-navbar\\r\\n      [item]=\\\"item\\\"\\r\\n      (expand)=\\\"onSubnavbarExpand($event, navbarItems)\\\"\\r\\n      (routeClick)=\\\"onRouteClick($event, navbarItems)\\\"\\r\\n      [routerItem]=\\\"routerItem()\\\"\\r\\n    ></lpx-sub-navbar>\\r\\n  </li>\\r\\n</ng-template>\\r\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { groupedItems: [{\n                type: Input\n            }], navbarItems: [{\n                type: Input\n            }], routeClick: [{\n                type: Output\n            }] } });\n\nclass NavbarComponent {\n    constructor() {\n        this.layoutService = inject(LayoutService);\n        this.platformId = inject(PLATFORM_ID);\n        this.service = inject(NavbarService);\n        this.injector = inject(Injector);\n        this.didResized = false;\n        this.showFilterMenu$ = this.service.navbarItems$.pipe(map$1((items) => !!items.length));\n        this.contentBefore = this.flatContents(CONTENT_BEFORE_ROUTES);\n        this.contentAfter = this.flatContents(CONTENT_AFTER_ROUTES);\n    }\n    toggleSidebarHover() {\n        this.didResized = true;\n        this.layoutService.toggleClass('hover-trigger');\n    }\n    ngAfterViewChecked() {\n        if (!isPlatformBrowser(this.platformId)) {\n            return;\n        }\n        if (this.didResized) {\n            this.didResized = false;\n            window.dispatchEvent(new Event('resize'));\n        }\n    }\n    flatContents(token) {\n        const contents = this.injector.get(token, []);\n        return contents.reduce((acc, val) => acc.concat(val), []);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.1.3\", type: NavbarComponent, selector: \"lpx-navbar\", queries: [{ propertyName: \"routesTemplate\", first: true, predicate: NavbarRoutesDirective, descendants: true, read: TemplateRef }, { propertyName: \"logoPanel\", first: true, predicate: LogoPanelDirective, descendants: true }], ngImport: i0, template: \"<nav class=\\\"lpx-nav\\\">\\r\\n  <div class=\\\"lpx-logo-container\\\">\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"logoPanel?.template || defaultLogo\\\"\\r\\n    ></ng-container>\\r\\n    <lpx-icon\\r\\n      class=\\\"menu-collapse-icon hidden-in-hover-trigger\\\"\\r\\n      iconClass=\\\"bi bi-filter-left\\\"\\r\\n      (click)=\\\"toggleSidebarHover()\\\"\\r\\n    ></lpx-icon>\\r\\n  </div>\\r\\n\\r\\n  @if (showFilterMenu$ | async) {\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"\\r\\n        customContentTemplate;\\r\\n        context: { $implicit: contentBefore }\\r\\n      \\\"\\r\\n    ></ng-container>\\r\\n  }\\r\\n\\r\\n  <ng-container\\r\\n    *ngTemplateOutlet=\\\"\\r\\n      routesTemplate || defaultRouteTemplate;\\r\\n      context: {\\r\\n        $implicit: service.navbarItems$ | async,\\r\\n        groupItems: service.groupedNavbarItems$ | async\\r\\n      }\\r\\n    \\\"\\r\\n  ></ng-container>\\r\\n\\r\\n  <ng-container\\r\\n    *ngTemplateOutlet=\\\"\\r\\n      customContentTemplate;\\r\\n      context: { $implicit: contentAfter }\\r\\n    \\\"\\r\\n  ></ng-container>\\r\\n</nav>\\r\\n\\r\\n<ng-template #defaultRouteTemplate let-items let-groupItems=\\\"groupItems\\\">\\r\\n  <lpx-navbar-routes\\r\\n    [navbarItems]=\\\"items\\\"\\r\\n    [groupedItems]=\\\"groupItems\\\"\\r\\n    [routerItem]=\\\"true\\\"\\r\\n  ></lpx-navbar-routes>\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #customContentTemplate let-contents>\\r\\n  <ng-container *ngFor=\\\"let component of contents\\\">\\r\\n    <ng-container\\r\\n      *ngComponentOutlet=\\\"component; injector: injector\\\"\\r\\n    ></ng-container>\\r\\n  </ng-container>\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #defaultLogo>\\r\\n  <lpx-brand-logo></lpx-brand-logo>\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.NgComponentOutlet, selector: \"[ngComponentOutlet]\", inputs: [\"ngComponentOutlet\", \"ngComponentOutletInputs\", \"ngComponentOutletInjector\", \"ngComponentOutletContent\", \"ngComponentOutletNgModule\", \"ngComponentOutletNgModuleFactory\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: BrandLogoComponent, selector: \"lpx-brand-logo\" }, { kind: \"component\", type: IconComponent, selector: \"lpx-icon\", inputs: [\"iconClass\"] }, { kind: \"component\", type: NavbarRoutesComponent, selector: \"lpx-navbar-routes\", inputs: [\"groupedItems\", \"navbarItems\", \"routerItem\"], outputs: [\"routeClick\"] }, { kind: \"pipe\", type: i1.AsyncPipe, name: \"async\" }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-navbar', encapsulation: ViewEncapsulation.None, template: \"<nav class=\\\"lpx-nav\\\">\\r\\n  <div class=\\\"lpx-logo-container\\\">\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"logoPanel?.template || defaultLogo\\\"\\r\\n    ></ng-container>\\r\\n    <lpx-icon\\r\\n      class=\\\"menu-collapse-icon hidden-in-hover-trigger\\\"\\r\\n      iconClass=\\\"bi bi-filter-left\\\"\\r\\n      (click)=\\\"toggleSidebarHover()\\\"\\r\\n    ></lpx-icon>\\r\\n  </div>\\r\\n\\r\\n  @if (showFilterMenu$ | async) {\\r\\n    <ng-container\\r\\n      *ngTemplateOutlet=\\\"\\r\\n        customContentTemplate;\\r\\n        context: { $implicit: contentBefore }\\r\\n      \\\"\\r\\n    ></ng-container>\\r\\n  }\\r\\n\\r\\n  <ng-container\\r\\n    *ngTemplateOutlet=\\\"\\r\\n      routesTemplate || defaultRouteTemplate;\\r\\n      context: {\\r\\n        $implicit: service.navbarItems$ | async,\\r\\n        groupItems: service.groupedNavbarItems$ | async\\r\\n      }\\r\\n    \\\"\\r\\n  ></ng-container>\\r\\n\\r\\n  <ng-container\\r\\n    *ngTemplateOutlet=\\\"\\r\\n      customContentTemplate;\\r\\n      context: { $implicit: contentAfter }\\r\\n    \\\"\\r\\n  ></ng-container>\\r\\n</nav>\\r\\n\\r\\n<ng-template #defaultRouteTemplate let-items let-groupItems=\\\"groupItems\\\">\\r\\n  <lpx-navbar-routes\\r\\n    [navbarItems]=\\\"items\\\"\\r\\n    [groupedItems]=\\\"groupItems\\\"\\r\\n    [routerItem]=\\\"true\\\"\\r\\n  ></lpx-navbar-routes>\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #customContentTemplate let-contents>\\r\\n  <ng-container *ngFor=\\\"let component of contents\\\">\\r\\n    <ng-container\\r\\n      *ngComponentOutlet=\\\"component; injector: injector\\\"\\r\\n    ></ng-container>\\r\\n  </ng-container>\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #defaultLogo>\\r\\n  <lpx-brand-logo></lpx-brand-logo>\\r\\n</ng-template>\\r\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { routesTemplate: [{\n                type: ContentChild,\n                args: [NavbarRoutesDirective, { read: TemplateRef }]\n            }], logoPanel: [{\n                type: ContentChild,\n                args: [LogoPanelDirective]\n            }] } });\n\nclass ToObservablePipe {\n    transform(value) {\n        return value ? getStream$(value) : of('');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservablePipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservablePipe, name: \"toObservable\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservablePipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'toObservable',\n                }]\n        }] });\n\nclass ToObservableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservableModule, declarations: [ToObservablePipe], imports: [CommonModule], exports: [ToObservablePipe] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservableModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToObservableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [ToObservablePipe],\n                    imports: [CommonModule],\n                    exports: [ToObservablePipe],\n                }]\n        }] });\n\nclass SafeHtmlPipe {\n    constructor() {\n        this.sanitizer = inject(DomSanitizer);\n    }\n    transform(value) {\n        if (!value || typeof value !== 'string')\n            return '';\n        return this.sanitizer.sanitize(SecurityContext.HTML, value) || '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: SafeHtmlPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: SafeHtmlPipe, isStandalone: true, name: \"lpxSafeHtml\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: SafeHtmlPipe, decorators: [{\n            type: Pipe,\n            args: [{ name: 'lpxSafeHtml', standalone: true }]\n        }] });\n\nclass LpxTranslateModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxTranslateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxTranslateModule, declarations: [TranslatePipe], imports: [CommonModule], exports: [TranslatePipe] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxTranslateModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxTranslateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [TranslatePipe],\n                    imports: [CommonModule],\n                    exports: [TranslatePipe],\n                }]\n        }] });\n\nconst exportedDeclarations$1 = [\n    NavbarComponent,\n    SubNavbarComponent,\n    NavbarRoutesComponent,\n    NavbarRoutesDirective,\n];\nclass LpxNavbarModule {\n    static forRoot(options = {}) {\n        return {\n            ngModule: LpxNavbarModule,\n            providers: [\n                {\n                    provide: LPX_MENU_ITEMS,\n                    useValue: options?.menuItems || [],\n                },\n                {\n                    provide: CONTENT_AFTER_ROUTES,\n                    useValue: options?.contentAfterRoutes || [],\n                    multi: true,\n                },\n                {\n                    provide: CONTENT_BEFORE_ROUTES,\n                    useValue: options?.contentBeforeRoutes || [],\n                    multi: true,\n                },\n            ],\n        };\n    }\n    static forChild(options = {}) {\n        return {\n            ngModule: LpxNavbarModule,\n            providers: [\n                {\n                    provide: CONTENT_AFTER_ROUTES,\n                    useValue: options?.contentAfterRoutes || [],\n                    multi: true,\n                },\n                {\n                    provide: CONTENT_BEFORE_ROUTES,\n                    useValue: options?.contentBeforeRoutes || [],\n                    multi: true,\n                },\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxNavbarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxNavbarModule, declarations: [NavbarComponent,\n            SubNavbarComponent,\n            NavbarRoutesComponent,\n            NavbarRoutesDirective], imports: [CommonModule,\n            FormsModule,\n            RouterModule,\n            LpxBrandLogoModule,\n            LpxIconModule,\n            ToObservableModule,\n            LpxTranslateModule,\n            LpxVisibleDirective], exports: [NavbarComponent,\n            SubNavbarComponent,\n            NavbarRoutesComponent,\n            NavbarRoutesDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxNavbarModule, imports: [CommonModule,\n            FormsModule,\n            RouterModule,\n            LpxBrandLogoModule,\n            LpxIconModule,\n            ToObservableModule,\n            LpxTranslateModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxNavbarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [...exportedDeclarations$1],\n                    imports: [\n                        CommonModule,\n                        FormsModule,\n                        RouterModule,\n                        LpxBrandLogoModule,\n                        LpxIconModule,\n                        ToObservableModule,\n                        LpxTranslateModule,\n                        LpxVisibleDirective,\n                    ],\n                    exports: [...exportedDeclarations$1],\n                }]\n        }] });\n\nclass BreadcrumbService {\n    constructor() {\n        this.store = new DataStore([]);\n        this.items$ = this.store.sliceState((state) => state);\n    }\n    // TODO: generate id per item\n    add(item) {\n        const items = Array.isArray(item) ? item : [item];\n        this.store.set([...this.store.state, ...items]);\n    }\n    // TODO: generate id per item\n    insert(item, index) {\n        const state = this.store.state;\n        const items = Array.isArray(item) ? item : [item];\n        this.store.set([...state.slice(0, index), ...items, ...state.slice(index)]);\n    }\n    // TODO: generate id per item\n    setItems(items) {\n        this.store.set(items);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass ClickOutsideDirective {\n    constructor(elementRef) {\n        this.elementRef = elementRef;\n        this.lpxClickOutside = new EventEmitter();\n        this.exceptedRefs = [];\n    }\n    onDocumentClick(event) {\n        if (!(this.elementRef.nativeElement.contains(event.target) ||\n            this.exceptedRefs.some(ref => ref.nativeElement.contains(event.target)))) {\n            this.lpxClickOutside.emit();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ClickOutsideDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: ClickOutsideDirective, selector: \"[lpxClickOutside]\", inputs: { exceptedRefs: \"exceptedRefs\" }, outputs: { lpxClickOutside: \"lpxClickOutside\" }, host: { listeners: { \"document:click\": \"onDocumentClick($event)\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ClickOutsideDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[lpxClickOutside]',\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { lpxClickOutside: [{\n                type: Output\n            }], exceptedRefs: [{\n                type: Input\n            }], onDocumentClick: [{\n                type: HostListener,\n                args: ['document:click', ['$event']]\n            }] } });\n\nclass BreadcrumbComponent {\n    constructor(service) {\n        this.service = service;\n        this.icon = ICON_MAP;\n    }\n    onClick(item) {\n        if (item.children) {\n            item.expanded = !item.expanded;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbComponent, deps: [{ token: BreadcrumbService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: BreadcrumbComponent, selector: \"lpx-breadcrumb\", ngImport: i0, template: \"<nav aria-label=\\\"breadcrumb\\\">\\r\\n  <ol class=\\\"lpx-breadcrumb\\\">\\r\\n    <ng-container *ngFor=\\\"let item of service.items$ | async; last as last\\\">\\r\\n      <li\\r\\n        class=\\\"lpx-breadcrumb-item\\\"\\r\\n        (click)=\\\"onClick(item)\\\"\\r\\n        [class.expanded]=\\\"item.expanded\\\"\\r\\n        (lpxClickOutside)=\\\"item.expanded = false\\\"\\r\\n      >\\r\\n        <lpx-icon\\r\\n          class=\\\"lpx-breadcrumb-item-icon\\\"\\r\\n          *ngIf=\\\"item.icon\\\"\\r\\n          [iconClass]=\\\"item.icon\\\"\\r\\n        ></lpx-icon>\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"\\r\\n            item.children?.length ? textTemplate : linkTemplate;\\r\\n            context: { $implicit: item }\\r\\n          \\\"\\r\\n        ></ng-container>\\r\\n      </li>\\r\\n      <li *ngIf=\\\"!last\\\" class=\\\"lpx-breadcrumb-separator\\\">\\r\\n        <lpx-icon iconClass=\\\"bi bi-chevron-right\\\"></lpx-icon>\\r\\n      </li>\\r\\n    </ng-container>\\r\\n  </ol>\\r\\n</nav>\\r\\n\\r\\n<ng-template #linkTemplate let-item>\\r\\n  <a [routerLink]=\\\"item.link\\\"> {{ item.text | toObservable | async }} </a>\\r\\n</ng-template>\\r\\n<ng-template #textTemplate let-item>\\r\\n  <span class=\\\"lpx-breadcrumb-item-text\\\">\\r\\n    {{ item.text | toObservable | async }}\\r\\n  </span>\\r\\n</ng-template>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: IconComponent, selector: \"lpx-icon\", inputs: [\"iconClass\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: ClickOutsideDirective, selector: \"[lpxClickOutside]\", inputs: [\"exceptedRefs\"], outputs: [\"lpxClickOutside\"] }, { kind: \"pipe\", type: i1.AsyncPipe, name: \"async\" }, { kind: \"pipe\", type: ToObservablePipe, name: \"toObservable\" }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-breadcrumb', encapsulation: ViewEncapsulation.None, template: \"<nav aria-label=\\\"breadcrumb\\\">\\r\\n  <ol class=\\\"lpx-breadcrumb\\\">\\r\\n    <ng-container *ngFor=\\\"let item of service.items$ | async; last as last\\\">\\r\\n      <li\\r\\n        class=\\\"lpx-breadcrumb-item\\\"\\r\\n        (click)=\\\"onClick(item)\\\"\\r\\n        [class.expanded]=\\\"item.expanded\\\"\\r\\n        (lpxClickOutside)=\\\"item.expanded = false\\\"\\r\\n      >\\r\\n        <lpx-icon\\r\\n          class=\\\"lpx-breadcrumb-item-icon\\\"\\r\\n          *ngIf=\\\"item.icon\\\"\\r\\n          [iconClass]=\\\"item.icon\\\"\\r\\n        ></lpx-icon>\\r\\n        <ng-container\\r\\n          *ngTemplateOutlet=\\\"\\r\\n            item.children?.length ? textTemplate : linkTemplate;\\r\\n            context: { $implicit: item }\\r\\n          \\\"\\r\\n        ></ng-container>\\r\\n      </li>\\r\\n      <li *ngIf=\\\"!last\\\" class=\\\"lpx-breadcrumb-separator\\\">\\r\\n        <lpx-icon iconClass=\\\"bi bi-chevron-right\\\"></lpx-icon>\\r\\n      </li>\\r\\n    </ng-container>\\r\\n  </ol>\\r\\n</nav>\\r\\n\\r\\n<ng-template #linkTemplate let-item>\\r\\n  <a [routerLink]=\\\"item.link\\\"> {{ item.text | toObservable | async }} </a>\\r\\n</ng-template>\\r\\n<ng-template #textTemplate let-item>\\r\\n  <span class=\\\"lpx-breadcrumb-item-text\\\">\\r\\n    {{ item.text | toObservable | async }}\\r\\n  </span>\\r\\n</ng-template>\\r\\n\" }]\n        }], ctorParameters: () => [{ type: BreadcrumbService }] });\n\nclass LpxClickOutsideModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxClickOutsideModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxClickOutsideModule, declarations: [ClickOutsideDirective], imports: [CommonModule], exports: [ClickOutsideDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxClickOutsideModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxClickOutsideModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [ClickOutsideDirective],\n                    imports: [CommonModule],\n                    exports: [ClickOutsideDirective],\n                }]\n        }] });\n\nclass BreadcrumbRouteListenerService {\n    constructor(navbarService, router, breadcrumbService) {\n        this.navbarService = navbarService;\n        this.router = router;\n        this.breadcrumbService = breadcrumbService;\n    }\n    subscribeRoute() {\n        combineLatest([\n            this.router.events.pipe(filter((event) => event instanceof NavigationEnd)),\n            this.navbarService.navbarItems$.pipe(filter((items) => !!items.length)),\n        ]).subscribe(([event, items]) => {\n            let activeItem = this.navbarService.findByLink(event.url);\n            if (!activeItem.item) {\n                activeItem = this.navbarService.findByLink('/');\n            }\n            const breadCrumbItems = activeItem.location.reduce((acc, itemIndex) => {\n                const parent = acc[acc.length - 1]?.children || items;\n                const item = parent[itemIndex];\n                return [\n                    ...acc,\n                    { ...item, siblings: parent },\n                ];\n            }, []);\n            this.breadcrumbService.setItems(this.mapNavbarItemToBreadcrumbItem(breadCrumbItems));\n        });\n    }\n    mapNavbarItemToBreadcrumbItem(items) {\n        return items.map(({ breadcrumbText, text, link, icon, siblings }) => ({\n            text: breadcrumbText || text || '',\n            link,\n            icon,\n            children: this.mapNavbarItemToBreadcrumbItem(siblings || []),\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbRouteListenerService, deps: [{ token: NavbarService }, { token: i2.Router }, { token: BreadcrumbService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbRouteListenerService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbRouteListenerService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: NavbarService }, { type: i2.Router }, { type: BreadcrumbService }] });\n\nconst exportedDeclarations = [BreadcrumbComponent];\nclass LpxBreadcrumbModule {\n    static forRoot() {\n        return {\n            ngModule: LpxBreadcrumbModule,\n            providers: [\n                {\n                    provide: APP_INITIALIZER,\n                    useFactory: breadCrumbInit,\n                    multi: true,\n                    deps: [Injector],\n                },\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBreadcrumbModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBreadcrumbModule, declarations: [BreadcrumbComponent], imports: [CommonModule,\n            LpxIconModule,\n            ToObservableModule,\n            RouterModule,\n            LpxClickOutsideModule], exports: [BreadcrumbComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBreadcrumbModule, imports: [CommonModule,\n            LpxIconModule,\n            ToObservableModule,\n            RouterModule,\n            LpxClickOutsideModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxBreadcrumbModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [...exportedDeclarations],\n                    imports: [\n                        CommonModule,\n                        LpxIconModule,\n                        ToObservableModule,\n                        RouterModule,\n                        LpxClickOutsideModule,\n                    ],\n                    exports: [...exportedDeclarations],\n                }]\n        }] });\nfunction breadCrumbInit(injector) {\n    const subs = () => {\n        const service = injector.get(BreadcrumbRouteListenerService);\n        service.subscribeRoute();\n    };\n    return subs;\n}\n\nconst LPX_TRANSLATE_SERVICE_PROVIDER = {\n    provide: LPX_TRANSLATE_SERVICE_TOKEN,\n    useClass: DefaultTranslateService,\n};\nconst LPX_TRANSLATE_PROVIDERS = [\n    LPX_TRANSLATE_SERVICE_PROVIDER,\n];\n\nconst LPX_INITIAL_STYLES = new InjectionToken('LPX_INITIAL_STYLES_TOKEN');\nconst LPX_STYLE_FINAL = new InjectionToken('LPX_STYLE_FINAL_TOKEN');\nconst LPX_LAYOUT_STYLE_FINAL = new InjectionToken('LPX_LAYOUT_STYLE_FINALIZE_TOKEN');\n\nclass StyleService {\n    constructor(initialStyles, document) {\n        this.initialStyles = initialStyles;\n        this.document = document;\n        this.lastInjectedStyle = null;\n        this.initialized$ = new BehaviorSubject(false);\n    }\n    async initStyles(direction) {\n        for (const style of this.initialStyles) {\n            await this.loadStyle(style, direction);\n        }\n        this.initialized$.next(true);\n    }\n    async loadStyle(style, direction) {\n        return new Promise((resolve, reject) => {\n            const linkElem = this.createLinkElem(style, direction, resolve);\n            //TODO: find a better way for understand style laaded by angular json\n            const appStyles = document.querySelector('link[rel=\"stylesheet\"][href*=\"styles\"]');\n            if (appStyles) {\n                if (this.lastInjectedStyle && this.lastInjectedStyle.isConnected) {\n                    this.lastInjectedStyle.insertAdjacentElement('afterend', linkElem);\n                }\n                else {\n                    appStyles.insertAdjacentElement('beforebegin', linkElem);\n                }\n            }\n            else {\n                this.document.head.appendChild(linkElem);\n            }\n            this.lastInjectedStyle = linkElem;\n            return Promise.resolve(linkElem);\n        });\n    }\n    async replaceStyle(style, direction) {\n        const loaded = this.document.querySelector(`link#${style.bundleName}`);\n        if (loaded) {\n            loaded.remove();\n        }\n        return this.loadStyle(style, direction);\n    }\n    async reloadInitialStyles(direction) {\n        for (const style of this.initialStyles) {\n            await this.replaceStyle(style, direction);\n        }\n    }\n    createLinkElem(style, direction, resolve) {\n        const linkElem = document.createElement('link');\n        linkElem.rel = 'stylesheet';\n        linkElem.id = style.bundleName;\n        linkElem.href = `${style.bundleName}${direction === 'rtl' ? '.rtl' : ''}.css`;\n        linkElem.onload = () => {\n            resolve(linkElem);\n        };\n        return linkElem;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: StyleService, deps: [{ token: LPX_STYLE_FINAL }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: StyleService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: StyleService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [LPX_STYLE_FINAL]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\nfunction createStyleFactory(handler) {\n    return handler || ((defaultValue) => defaultValue);\n}\n\nfunction styleLoadFactory(styleList, layoutStyles) {\n    styleList.push({\n        bundleName: 'ng-bundle',\n    });\n    styleList.push({\n        bundleName: 'font-bundle',\n    });\n    return [...styleList, ...layoutStyles];\n}\n\nconst LPX_STYLE_PROVIDERS = [\n    {\n        provide: LPX_INITIAL_STYLES,\n        useFactory: () => [],\n    },\n    {\n        provide: APP_INITIALIZER,\n        deps: [StyleService, LanguageService],\n        useFactory: loadInitialStyles,\n        multi: true,\n    },\n];\nfunction loadInitialStyles(styleService, languageService) {\n    return () => {\n        return languageService.languageChange$.pipe(take(1), switchMap((lang) => from(styleService.initStyles(lang.isRTL ? 'rtl' : 'ltr'))));\n    };\n}\n\nconst WINDOW = new InjectionToken('WINDOW');\n\nfunction createWindowProvider(windowObj) {\n    return { provide: WINDOW, useValue: windowObj || window };\n}\n\nconst RESPONSIVE_BREAKPOINTS = new InjectionToken('RESPONSIVE_BREAKPOINTS');\n\nclass ResponsiveService {\n    constructor(providedBreakpoints, window) {\n        this.providedBreakpoints = providedBreakpoints;\n        this.window = window;\n        this.defaultBreakpoint = {\n            name: \"all\" /* ResponsiveTokens.all */,\n            width: 0,\n        };\n        this.breakpoints = this.buildBreakpoints(this.providedBreakpoints);\n        this.getCurrentSize = () => ({\n            height: this.window.innerHeight,\n            width: this.window.innerWidth,\n        });\n        this.mapSizeToBreakpoint = ({ width } = this.getCurrentSize()) => {\n            return this.breakpoints.find(s => width >= s.width);\n        };\n        this.currentSize$ = new BehaviorSubject(this.mapSizeToBreakpoint());\n        this.shouldRenderWithCurrentSize = (query) => {\n            return this.matchQuery(query);\n        };\n        this.setupListener();\n    }\n    setupListener() {\n        this.currentResolution$ = fromEvent(this.window, 'resize')\n            .pipe(map(this.getCurrentSize))\n            .pipe(startWith(this.getCurrentSize()));\n        this.currentResolution$\n            .pipe(map(this.mapSizeToBreakpoint), distinctUntilChanged())\n            .subscribe(current => {\n            this.currentSize$.next(current);\n        });\n    }\n    buildBreakpoints(breakpoints) {\n        return [\n            ...Object.keys(breakpoints)\n                .map(key => ({\n                name: key,\n                width: breakpoints[key],\n            }))\n                .sort((a, b) => b.width - a.width),\n            this.defaultBreakpoint,\n        ];\n    }\n    matchQuery(query) {\n        const { width } = this.getCurrentSize();\n        const tokens = query.split(' ');\n        const findInTokens = (size) => tokens.find(token => token.split(\"-\" /* ResponsiveTokens.separator */)[0] === size);\n        const matchedBreakpoint = this.breakpoints.find(breakpoint => width >= breakpoint.width && findInTokens(breakpoint.name));\n        if (matchedBreakpoint) {\n            const token = findInTokens(matchedBreakpoint.name);\n            const shouldBeBigger = !token?.includes(\"none\" /* ResponsiveTokens.none */);\n            return shouldBeBigger === width >= matchedBreakpoint.width;\n        }\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ResponsiveService, deps: [{ token: RESPONSIVE_BREAKPOINTS }, { token: WINDOW }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ResponsiveService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ResponsiveService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [RESPONSIVE_BREAKPOINTS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [WINDOW]\n                }] }] });\n\nclass ResponsiveDirective {\n    constructor(templateRef, viewContainer, service, parentCdr) {\n        this.templateRef = templateRef;\n        this.viewContainer = viewContainer;\n        this.service = service;\n        this.parentCdr = parentCdr;\n        this.hasRendered = false;\n        this.sub = new Subscription();\n        this.render = (shouldRender) => {\n            if (shouldRender && !this.hasRendered) {\n                this.viewContainer.createEmbeddedView(this.templateRef);\n                this.hasRendered = true;\n            }\n            else if (!shouldRender && this.hasRendered) {\n                this.viewContainer.clear();\n                this.hasRendered = false;\n            }\n            this.parentCdr.detectChanges();\n        };\n    }\n    ngOnInit() {\n        this.sub.add(this.service.currentSize$\n            .pipe(map(_ => this.service.shouldRenderWithCurrentSize(this.query)))\n            .subscribe(this.render));\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ResponsiveDirective, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: ResponsiveService }, { token: i0.ChangeDetectorRef, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: ResponsiveDirective, selector: \"[lpxResponsive]\", inputs: { query: [\"lpxResponsive\", \"query\"] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ResponsiveDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[lpxResponsive]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: ResponsiveService }, { type: i0.ChangeDetectorRef, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }], propDecorators: { query: [{\n                type: Input,\n                args: ['lpxResponsive']\n            }] } });\n\nclass LpxResponsiveModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxResponsiveModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxResponsiveModule, declarations: [ResponsiveDirective], imports: [CommonModule], exports: [ResponsiveDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxResponsiveModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxResponsiveModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [ResponsiveDirective],\n                    imports: [CommonModule],\n                    exports: [ResponsiveDirective],\n                }]\n        }] });\n\nconst LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS = {\n    sm: 480,\n    md: 768,\n    lg: 992,\n    xl: 1200,\n};\n\nfunction createResponsiveProvider(responsiveSettings) {\n    return {\n        provide: RESPONSIVE_BREAKPOINTS,\n        useValue: responsiveSettings || LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS,\n    };\n}\n\nfunction createDirectionProvider(listenDirection) {\n    return {\n        provide: APP_INITIALIZER,\n        multi: true,\n        deps: [LanguageService, StyleService],\n        useFactory: listenDirection ? listenDirectionChange : () => () => null,\n    };\n}\n// subscribe to direction from documentElement and load direction stylesheet\nfunction listenDirectionChange(languageService, styleService) {\n    return () => {\n        return new Promise((resolve) => {\n            styleService.initialized$\n                .pipe(filter(Boolean), take(1), switchMap(() => languageService.languageChange$), distinctUntilKeyChanged('isRTL'))\n                .subscribe(async (lang) => {\n                const direction = lang?.isRTL ? 'rtl' : 'ltr';\n                const documentElement = document.documentElement;\n                if (documentElement.dir !== direction) {\n                    documentElement.dir = direction;\n                }\n                await styleService.reloadInitialStyles(direction);\n                resolve(null);\n            });\n        });\n    };\n}\n\nconst LPX_PERFECT_SCROLLBAR = new InjectionToken('LPX_PERFECT_SCROLLBAR');\n\nclass LpxCoreModule {\n    static forRoot(options) {\n        return {\n            ngModule: LpxCoreModule,\n            providers: [\n                {\n                    provide: LPX_PERFECT_SCROLLBAR,\n                    useClass: LpxPerfectScrollbarService,\n                },\n                createResponsiveProvider(options?.responsiveSettings),\n                createWindowProvider(options?.window),\n                LpxIconModule.forRoot(options?.iconSettings).providers,\n                LpxLanguageModule.forRoot(options?.languageSettings)\n                    .providers,\n                LpxNavbarModule.forRoot(options?.navbarSettings).providers,\n                LpxBreadcrumbModule.forRoot().providers,\n                LPX_TRANSLATE_PROVIDERS,\n                ...LPX_STYLE_PROVIDERS,\n                createDirectionProvider(options?.listenDirectionChanges || true),\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxCoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxCoreModule, imports: [CommonModule, LpxVisibleDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxCoreModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxCoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, LpxVisibleDirective],\n                }]\n        }] });\n\nclass AvatarComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AvatarComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: AvatarComponent, selector: \"lpx-avatar\", inputs: { avatar: \"avatar\" }, ngImport: i0, template: \"<div class=\\\"lpx-avatar\\\" *ngIf=\\\"avatar && avatar?.source\\\">\\r\\n  <ng-container [ngSwitch]=\\\"avatar.type\\\">\\r\\n    <ng-container *ngSwitchCase=\\\"'icon'\\\">\\r\\n      <lpx-icon class=\\\"lpx-avatar-icon\\\" [iconClass]=\\\"avatar.source\\\"></lpx-icon>\\r\\n    </ng-container>\\r\\n    <ng-container *ngSwitchCase=\\\"'image'\\\">\\r\\n      <img class=\\\"lpx-avatar-img\\\" [src]=\\\"avatar.source\\\" />\\r\\n    </ng-container>\\r\\n  </ng-container>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i1.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"component\", type: IconComponent, selector: \"lpx-icon\", inputs: [\"iconClass\"] }], encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: AvatarComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-avatar', encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"lpx-avatar\\\" *ngIf=\\\"avatar && avatar?.source\\\">\\r\\n  <ng-container [ngSwitch]=\\\"avatar.type\\\">\\r\\n    <ng-container *ngSwitchCase=\\\"'icon'\\\">\\r\\n      <lpx-icon class=\\\"lpx-avatar-icon\\\" [iconClass]=\\\"avatar.source\\\"></lpx-icon>\\r\\n    </ng-container>\\r\\n    <ng-container *ngSwitchCase=\\\"'image'\\\">\\r\\n      <img class=\\\"lpx-avatar-img\\\" [src]=\\\"avatar.source\\\" />\\r\\n    </ng-container>\\r\\n  </ng-container>\\r\\n</div>\\r\\n\" }]\n        }], propDecorators: { avatar: [{\n                type: Input\n            }] } });\n\nclass LpxAvatarModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxAvatarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxAvatarModule, declarations: [AvatarComponent], imports: [CommonModule, LpxIconModule], exports: [AvatarComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxAvatarModule, imports: [CommonModule, LpxIconModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxAvatarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [AvatarComponent],\n                    imports: [CommonModule, LpxIconModule],\n                    exports: [AvatarComponent],\n                }]\n        }] });\n\nclass FooterLinksService {\n    constructor() {\n        this.store = new DataStore({});\n        this.footerInfo$ = this.store.sliceState((state) => state);\n    }\n    setFooterInfo(links) {\n        this.store.set(links);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterLinksService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterLinksService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterLinksService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass FooterComponent {\n    constructor(service) {\n        this.service = service;\n        this.footerValues$ = this.service.footerInfo$;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterComponent, deps: [{ token: FooterLinksService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.1.3\", type: FooterComponent, selector: \"lpx-footer\", ngImport: i0, template: \"<ng-container *ngIf=\\\"footerValues$ | async as footerValues\\\">\\r\\n\\r\\n    <div class=\\\"lpx-footbar\\\">\\r\\n      <div class=\\\"lpx-footbar-copyright\\\">\\r\\n     \\r\\n        <a\\r\\n          *ngIf=\\\"footerValues.descUrl; else footerDesc\\\"\\r\\n          [routerLink]=\\\"[footerValues.descUrl]\\\"\\r\\n        >\\r\\n          {{ footerValues.desc }}</a\\r\\n        >\\r\\n        <ng-template #footerDesc>\\r\\n          <a> {{ footerValues.desc }}</a>\\r\\n        </ng-template>\\r\\n      </div>\\r\\n      <div class=\\\"lpx-footbar-solo-links\\\">\\r\\n        <ng-container *ngFor=\\\"let footerLink of footerValues.footerLinks\\\">\\r\\n          <a *ngIf=\\\"footerLink\\\" [routerLink]=\\\"[footerLink.link]\\\">{{\\r\\n            footerLink.text\\r\\n          }}</a>\\r\\n        </ng-container>\\r\\n      </div>\\r\\n    </div>\\r\\n\\r\\n</ng-container>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"pipe\", type: i1.AsyncPipe, name: \"async\" }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lpx-footer', template: \"<ng-container *ngIf=\\\"footerValues$ | async as footerValues\\\">\\r\\n\\r\\n    <div class=\\\"lpx-footbar\\\">\\r\\n      <div class=\\\"lpx-footbar-copyright\\\">\\r\\n     \\r\\n        <a\\r\\n          *ngIf=\\\"footerValues.descUrl; else footerDesc\\\"\\r\\n          [routerLink]=\\\"[footerValues.descUrl]\\\"\\r\\n        >\\r\\n          {{ footerValues.desc }}</a\\r\\n        >\\r\\n        <ng-template #footerDesc>\\r\\n          <a> {{ footerValues.desc }}</a>\\r\\n        </ng-template>\\r\\n      </div>\\r\\n      <div class=\\\"lpx-footbar-solo-links\\\">\\r\\n        <ng-container *ngFor=\\\"let footerLink of footerValues.footerLinks\\\">\\r\\n          <a *ngIf=\\\"footerLink\\\" [routerLink]=\\\"[footerLink.link]\\\">{{\\r\\n            footerLink.text\\r\\n          }}</a>\\r\\n        </ng-container>\\r\\n      </div>\\r\\n    </div>\\r\\n\\r\\n</ng-container>\\r\\n\" }]\n        }], ctorParameters: () => [{ type: FooterLinksService }] });\n\nclass LpxFooterModule {\n    static forRoot() {\n        return {\n            ngModule: LpxFooterModule,\n            providers: [],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxFooterModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxFooterModule, declarations: [FooterComponent], imports: [CommonModule, RouterModule], exports: [FooterComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxFooterModule, imports: [CommonModule, RouterModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LpxFooterModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [FooterComponent],\n                    exports: [FooterComponent],\n                    imports: [CommonModule, RouterModule],\n                }]\n        }] });\n\nclass PerfectScrollbarDirective {\n    constructor() {\n        this.elementRef = inject(ElementRef);\n        this.lpxPerfectService = inject(LPX_PERFECT_SCROLLBAR);\n    }\n    set lpxPerfectScrollbarOptions(value) {\n        this.lpxPerfectService.setOptions(value);\n    }\n    onResize() {\n        this.lpxPerfectService.onResize();\n    }\n    ngAfterViewInit() {\n        this.lpxPerfectService.setElement(this.elementRef);\n        this.lpxPerfectService.afterViewInit();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PerfectScrollbarDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: PerfectScrollbarDirective, isStandalone: true, selector: \"[lpxPerfectScrollbar]\", inputs: { lpxPerfectScrollbarOptions: \"lpxPerfectScrollbarOptions\" }, host: { listeners: { \"window:resize\": \"onResize()\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PerfectScrollbarDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[lpxPerfectScrollbar]',\n                    standalone: true,\n                }]\n        }], propDecorators: { lpxPerfectScrollbarOptions: [{\n                type: Input\n            }], onResize: [{\n                type: HostListener,\n                args: ['window:resize']\n            }] } });\n\nclass BreadcrumbPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: BreadcrumbPanelDirective, selector: \"ng-template[lpx-breadcrumb-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: BreadcrumbPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-breadcrumb-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass ContentPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ContentPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: ContentPanelDirective, selector: \"ng-template[lpx-content]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ContentPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-content]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass CurrentUserImagePanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: CurrentUserImagePanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: CurrentUserImagePanelDirective, selector: \"ng-template[lpx-current-user-image-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: CurrentUserImagePanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-current-user-image-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass CurrentUserPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: CurrentUserPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: CurrentUserPanelDirective, selector: \"ng-template[lpx-current-user-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: CurrentUserPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-current-user-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass FooterPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: FooterPanelDirective, selector: \"ng-template[lpx-footer-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: FooterPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-footer-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass LanguagePanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LanguagePanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: LanguagePanelDirective, selector: \"ng-template[lpx-language-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: LanguagePanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-language-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass MobileNavbarPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: MobileNavbarPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: MobileNavbarPanelDirective, selector: \"ng-template[lpx-mobile-navbar-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: MobileNavbarPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-mobile-navbar-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass NavbarPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: NavbarPanelDirective, selector: \"ng-template[lpx-navbar-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavbarPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-navbar-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass NavitemPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavitemPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: NavitemPanelDirective, selector: \"ng-template[lpx-navitem-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: NavitemPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-navitem-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass ToolbarPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToolbarPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: ToolbarPanelDirective, selector: \"ng-template[lpx-toolbar-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToolbarPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-toolbar-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass TopNavbarPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TopNavbarPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: TopNavbarPanelDirective, selector: \"ng-template[lpx-top-navbar-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TopNavbarPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-top-navbar-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass SettingsPanelDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: SettingsPanelDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.1.3\", type: SettingsPanelDirective, selector: \"ng-template[lpx-settings-panel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: SettingsPanelDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[lpx-settings-panel]',\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nconst declarationsAndExports = [\n    BreadcrumbPanelDirective,\n    ContentPanelDirective,\n    CurrentUserImagePanelDirective,\n    CurrentUserPanelDirective,\n    FooterPanelDirective,\n    LanguagePanelDirective,\n    LogoPanelDirective,\n    MobileNavbarPanelDirective,\n    NavbarPanelDirective,\n    NavitemPanelDirective,\n    SettingsPanelDirective,\n    TopNavbarPanelDirective,\n    ToolbarPanelDirective,\n];\nclass PanelsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PanelsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.1.3\", ngImport: i0, type: PanelsModule, declarations: [BreadcrumbPanelDirective,\n            ContentPanelDirective,\n            CurrentUserImagePanelDirective,\n            CurrentUserPanelDirective,\n            FooterPanelDirective,\n            LanguagePanelDirective,\n            LogoPanelDirective,\n            MobileNavbarPanelDirective,\n            NavbarPanelDirective,\n            NavitemPanelDirective,\n            SettingsPanelDirective,\n            TopNavbarPanelDirective,\n            ToolbarPanelDirective], imports: [CommonModule], exports: [BreadcrumbPanelDirective,\n            ContentPanelDirective,\n            CurrentUserImagePanelDirective,\n            CurrentUserPanelDirective,\n            FooterPanelDirective,\n            LanguagePanelDirective,\n            LogoPanelDirective,\n            MobileNavbarPanelDirective,\n            NavbarPanelDirective,\n            NavitemPanelDirective,\n            SettingsPanelDirective,\n            TopNavbarPanelDirective,\n            ToolbarPanelDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PanelsModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: PanelsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [...declarationsAndExports],\n                    imports: [CommonModule],\n                    exports: [...declarationsAndExports],\n                }]\n        }] });\n\nclass ToolbarService {\n    constructor() {\n        this.store = new DataStore({ items: [] });\n        this.items$ = this.store.sliceState(({ items }) => items);\n    }\n    setItems(items) {\n        this.store.patch({ items: items.sort(sortItems) });\n    }\n    addItem(item) {\n        this.setItems([...this.store.state.items, item]);\n    }\n    patchItem(itemId, item) {\n        const { items } = this.store.state;\n        const index = items.findIndex(({ id }) => id === itemId);\n        if (index === -1) {\n            return;\n        }\n        const updateItems = [...items];\n        updateItems[index] = { id: itemId, ...item };\n        this.setItems(updateItems);\n    }\n    removeItem(id) {\n        const { items } = this.store.state;\n        const index = items.findIndex((item) => item.id === id);\n        if (index === -1) {\n            return;\n        }\n        const updateItems = [...items.slice(0, index), ...items.slice(index + 1)];\n        this.store.patch({ items: updateItems });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToolbarService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToolbarService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: ToolbarService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AvatarComponent, BodyService, BrandLogoComponent, BreadcrumbComponent, BreadcrumbPanelDirective, BreadcrumbRouteListenerService, BreadcrumbService, CONTENT_AFTER_ROUTES, CONTENT_BEFORE_ROUTES, ClickOutsideDirective, ContentPanelDirective, CurrentUserImagePanelDirective, CurrentUserPanelDirective, DataStore, DefaultAuthService, DefaultTranslateService, FooterComponent, FooterLinksService, FooterPanelDirective, ICON_MAP, IconComponent, LEPTON_X_ICON_SET, LPX_AUTH_SERVICE_PROVIDER, LPX_AUTH_SERVICE_TOKEN, LPX_INITIAL_STYLES, LPX_LANGUAGE, LPX_LAYOUT_STYLE_FINAL, LPX_MENU_ITEMS, LPX_PERFECT_SCROLLBAR, LPX_RESPONSIVE_BREAKPOINTS_DEFAULTS, LPX_STYLE_FINAL, LPX_STYLE_PROVIDERS, LPX_TRANSLATE_SERVICE_TOKEN, LPX_TRANSLATE_TOKEN, LanguagePanelDirective, LanguageService, LanguageTranslateDefaults, LanguageTranslateKeys, LayoutService, LogoPanelDirective, LpxAvatarModule, LpxBrandLogoModule, LpxBreadcrumbModule, LpxClickOutsideModule, LpxCoreModule, LpxFooterModule, LpxIconModule, LpxLanguageModule, LpxLocalStorageService, LpxNavbarModule, LpxPerfectScrollbar, LpxPerfectScrollbarService, LpxResponsiveModule, LpxThemeTranslateService, LpxTranslateModule, LpxVisibleDirective, MobileNavbarPanelDirective, NavbarComponent, NavbarPanelDirective, NavbarRoutesComponent, NavbarRoutesDirective, NavbarService, NavitemPanelDirective, OTHERS_GROUP_KEY, PanelsModule, PerfectScrollbarDirective, RESPONSIVE_BREAKPOINTS, ResponsiveDirective, ResponsiveService, RoutesService, SafeHtmlPipe, SettingsPanelDirective, StyleService, SubNavbarComponent, ToObservableModule, ToObservablePipe, ToolbarPanelDirective, ToolbarService, TopNavbarPanelDirective, TranslatePipe, UserProfileService, WINDOW, breadCrumbInit, createDirectionProvider, createGroupMap, createResponsiveProvider, createStyleFactory, createWindowProvider, exportedDeclarations, flatArrayDeepToObject, getItemsFromGroup, getStream$, isArray, isNullOrUndefined, listenDirectionChange, loadInitialStyles, sortItems, styleLoadFactory };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,YAAY,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AACjT,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,iBAAiB;AACxG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,iBAAiB;AAC3E,SAASC,GAAG,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,gBAAgB;AAC5H,SAASC,eAAe,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAEC,EAAE,EAAEC,KAAK,EAAEX,MAAM,IAAIY,QAAQ,EAAEd,GAAG,IAAIe,KAAK,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,QAAQ,MAAM;AACtJ,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAOC,gBAAgB,MAAM,mBAAmB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AAAC,SAAAC,WAAAC,MAAA,EAAAC,KAAA;EAAA,OAAAC,IAAA,CAAAC,IAAA,CAAAC,QAAA;AAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,yDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+F2CpE,EAAE,CAAAsE,kBAAA,EA6tBsgB,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tBzgBpE,EAAE,CAAAwE,UAAA,IAAAL,wDAAA,yBA6tBuf,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAK,MAAA,GA7tB1fzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2E,UAAA,sBAAAF,MAAA,CAAAX,IAAA,CAAAc,SA6tB4d,CAAC,8BAAAH,MAAA,CAAAI,QAAiB,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tBjfpE,EAAE,CAAAsE,kBAAA,EA6tB4lB,CAAC;EAAA;AAAA;AAAA,SAAAS,0CAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tB/lBpE,EAAE,CAAAwE,UAAA,IAAAM,wDAAA,yBA6tB6kB,CAAC;EAAA;EAAA,IAAAV,EAAA;IA7tBhlBpE,EAAE,CAAA0E,aAAA;IAAA,MAAAM,kBAAA,GAAFhF,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2E,UAAA,qBAAAK,kBA6tB0kB,CAAC;EAAA;AAAA;AAAA,SAAAE,qDAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tB7kBpE,EAAE,CAAAmF,SAAA,iBA6tBq/B,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GA7tBx/BzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2E,UAAA,cAAAF,MAAA,CAAAX,IAAA,CAAAsB,IA6tBi+B,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tBp+BpE,EAAE,CAAAsE,kBAAA,EA6tB0pC,CAAC;EAAA;AAAA;AAAA,SAAAgB,sEAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tB7pCpE,EAAE,CAAAuF,cAAA,cA6tB2xC,CAAC;IA7tB9xCvF,EAAE,CAAAwF,MAAA,EA6tBy1C,CAAC;IA7tB51CxF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAA0F,YAAA,CA6tBg2C,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAK,MAAA,GA7tBn2CzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2F,SAAA,CA6tBy1C,CAAC;IA7tB51C3F,EAAE,CAAA4F,iBAAA,CAAF5F,EAAE,CAAA6F,WAAA,OAAF7F,EAAE,CAAA6F,WAAA,OAAApB,MAAA,CAAAX,IAAA,CAAAgC,IAAA,EA6tBy1C,CAAC;EAAA;AAAA;AAAA,SAAAC,wDAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tB51CpE,EAAE,CAAAwE,UAAA,IAAAc,qEAAA,kBA6tBotC,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAK,MAAA,GA7tBvtCzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAAgG,aAAA,IAAAvB,MAAA,CAAAX,IAAA,CAAAgC,IAAA,SA6tB22C,CAAC;EAAA;AAAA;AAAA,SAAAG,wDAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tB92CpE,EAAE,CAAAmF,SAAA,iBA6tBylD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GA7tB5lDzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2E,UAAA,cAAAF,MAAA,CAAAX,IAAA,CAAAoC,QAAA,8BA6tBogD,CAAC;EAAA;AAAA;AAAA,SAAAC,mEAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgC,GAAA,GA7tBvgDpG,EAAE,CAAAqG,gBAAA;IAAFrG,EAAE,CAAAuF,cAAA,YA6tBk+D,CAAC,wBAA0L,CAAC;IA7tBhqEvF,EAAE,CAAAsG,UAAA,wBAAAC,wGAAAC,MAAA;MAAFxG,EAAE,CAAAyG,aAAA,CAAAL,GAAA;MAAA,MAAA3B,MAAA,GAAFzE,EAAE,CAAA0E,aAAA;MAAA,OAAF1E,EAAE,CAAA0G,WAAA,CA6tB+jEjC,MAAA,CAAAkC,UAAA,CAAAC,IAAA,CAAAJ,MAA2B,CAAC;IAAA,CAAC,CAAC,oBAAAK,oGAAAL,MAAA;MA7tB/lExG,EAAE,CAAAyG,aAAA,CAAAL,GAAA;MAAA,MAAA3B,MAAA,GAAFzE,EAAE,CAAA0E,aAAA;MAAA,OAAF1E,EAAE,CAAA0G,WAAA,CA6tBwnEjC,MAAA,CAAAqC,aAAA,CAAAN,MAAoB,CAAC;IAAA,CAAC,CAAC;IA7tBjpExG,EAAE,CAAA0F,YAAA,CA6tB8qE,CAAC,CAAgB,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA2C,QAAA,GA7tBlsE/G,EAAE,CAAA0E,aAAA,GAAAR,SAAA;IAAFlE,EAAE,CAAA2F,SAAA,CA6tB+hE,CAAC;IA7tBliE3F,EAAE,CAAA2E,UAAA,SAAAoC,QA6tB+hE,CAAC;EAAA;AAAA;AAAA,SAAAC,8DAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tBliEpE,EAAE,CAAAwE,UAAA,IAAA2B,kEAAA,gBA6tBk+D,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAAA,MAAA2C,QAAA,GAAA1C,GAAA,CAAAH,SAAA;IAAA,MAAAO,MAAA,GA7tBr+DzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2E,UAAA,gBAAAoC,QAAA,CAAAE,OAAA,IAAAF,QAAA,CAAAE,OAAA,CAAAF,QAAA,EAAAtC,MAAA,CAAAI,QAAA,CA6tBm9D,CAAC;EAAA;AAAA;AAAA,SAAAqC,wDAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tBt9DpE,EAAE,CAAAuF,cAAA,YA6tB+wD,CAAC;IA7tBlxDvF,EAAE,CAAAmH,gBAAA,IAAAH,6DAAA,kBAAAtD,UAAA,MA6tB0sE,CAAC;IA7tB7sE1D,EAAE,CAAA0F,YAAA,CA6tButE,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAK,MAAA,GA7tB1tEzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAAoH,WAAA,eAAA3C,MAAA,CAAAX,IAAA,CAAAoC,QA6tBswD,CAAC;IA7tBzwDlG,EAAE,CAAA2F,SAAA,CA6tB0sE,CAAC;IA7tB7sE3F,EAAE,CAAAqH,UAAA,CAAA5C,MAAA,CAAAX,IAAA,CAAAC,QA6tB0sE,CAAC;EAAA;AAAA;AAAA,SAAAuD,0CAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmD,GAAA,GA7tB7sEvH,EAAE,CAAAqG,gBAAA;IAAFrG,EAAE,CAAAuF,cAAA,UA6tB02B,CAAC;IA7tB72BvF,EAAE,CAAAsG,UAAA,mBAAAkB,6DAAA;MAAFxH,EAAE,CAAAyG,aAAA,CAAAc,GAAA;MAAA,MAAA9C,MAAA,GAAFzE,EAAE,CAAA0E,aAAA;MAAA,OAAF1E,EAAE,CAAA0G,WAAA,CA6tBi1BjC,MAAA,CAAAgD,WAAA,CAAAhD,MAAA,CAAAX,IAAgB,CAAC;IAAA,CAAC,CAAC;IA7tBt2B9D,EAAE,CAAAwE,UAAA,IAAAU,oDAAA,qBA6tB0+B,CAAC,IAAAG,wDAAA,yBAAgK,CAAC,IAAAU,uDAAA,gCA7tB9oC/F,EAAE,CAAA0H,sBA6tByrC,CAAC,IAAAzB,uDAAA,qBAAgP,CAAC;IA7tB76CjG,EAAE,CAAA0F,YAAA,CA6tB4mD,CAAC;IA7tB/mD1F,EAAE,CAAAwE,UAAA,IAAA0C,uDAAA,eA6tBmpD,CAAC;EAAA;EAAA,IAAA9C,EAAA;IAAA,MAAAuD,WAAA,GA7tBtpD3H,EAAE,CAAAiF,WAAA;IAAA,MAAAR,MAAA,GAAFzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAAoH,WAAA,aAAA3C,MAAA,CAAAX,IAAA,CAAA8D,QA6tB2vB,CAAC,cAAAnD,MAAA,CAAAX,IAAA,CAAAC,QAAA,kBAAAU,MAAA,CAAAX,IAAA,CAAAC,QAAA,CAAA8D,MAAA,KAAApD,MAAA,CAAAX,IAAA,CAAAoC,QAAkE,CAAC;IA7tBj0BlG,EAAE,CAAA2E,UAAA,eAAAF,MAAA,CAAAX,IAAA,CAAAgE,IA6tBitB,CAAC;IA7tBptB9H,EAAE,CAAA2F,SAAA,CA6tB47B,CAAC;IA7tB/7B3F,EAAE,CAAA2E,UAAA,SAAAF,MAAA,CAAAX,IAAA,CAAAsB,IA6tB47B,CAAC;IA7tB/7BpF,EAAE,CAAA2F,SAAA,CA6tBgmC,CAAC;IA7tBnmC3F,EAAE,CAAA2E,UAAA,qBAAAF,MAAA,CAAAX,IAAA,CAAAiE,QAAA,IAAAJ,WA6tBgmC,CAAC,4BA7tBnmC3H,EAAE,CAAAgI,eAAA,KAAAhE,GAAA,EAAAS,MAAA,CAAAX,IAAA,CA6tB4nC,CAAC;IA7tB/nC9D,EAAE,CAAA2F,SAAA,EA6tBkmD,CAAC;IA7tBrmD3F,EAAE,CAAAgG,aAAA,KAAAvB,MAAA,CAAAX,IAAA,CAAAC,QAAA,kBAAAU,MAAA,CAAAX,IAAA,CAAAC,QAAA,CAAA8D,MAAA,UA6tBkmD,CAAC;IA7tBrmD7H,EAAE,CAAA2F,SAAA,CA6tB8tE,CAAC;IA7tBjuE3F,EAAE,CAAAgG,aAAA,KAAAvB,MAAA,CAAAX,IAAA,CAAAC,QAAA,kBAAAU,MAAA,CAAAX,IAAA,CAAAC,QAAA,CAAA8D,MAAA,UA6tB8tE,CAAC;EAAA;AAAA;AAAA,SAAAI,kEAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7tBjuEpE,EAAE,CAAAsE,kBAAA,EAu0B6vB,CAAC;EAAA;AAAA;AAAA,SAAA4D,wEAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0BhwBpE,EAAE,CAAAsE,kBAAA,EAu0Bu8B,CAAC;EAAA;AAAA;AAAA,SAAA6D,yDAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0B18BpE,EAAE,CAAAwE,UAAA,IAAA0D,uEAAA,yBAu0Bw7B,CAAC;EAAA;EAAA,IAAA9D,EAAA;IAAA,MAAAgE,aAAA,GAAA/D,GAAA,CAAAH,SAAA;IAv0B37BlE,EAAE,CAAA0E,aAAA;IAAA,MAAA2D,eAAA,GAAFrI,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2E,UAAA,qBAAA0D,eAu0Bu4B,CAAC,4BAv0B14BrI,EAAE,CAAAgI,eAAA,IAAAhE,GAAA,EAAAoE,aAAA,CAu0By6B,CAAC;EAAA;AAAA;AAAA,SAAAE,mDAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0B56BpE,EAAE,CAAAwE,UAAA,IAAAyD,iEAAA,yBAu0B8uB,CAAC;IAv0BjvBjI,EAAE,CAAAmH,gBAAA,IAAAgB,wDAAA,8BAAFnI,EAAE,CAAAuI,yBAu0Bk9B,CAAC;EAAA;EAAA,IAAAnE,EAAA;IAAA,MAAAoE,OAAA,GAAAnE,GAAA,CAAAH,SAAA;IAv0Br9BlE,EAAE,CAAA0E,aAAA;IAAA,MAAA+D,YAAA,GAAFzI,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2E,UAAA,qBAAA8D,YAu0BqsB,CAAC,4BAv0BxsBzI,EAAE,CAAAgI,eAAA,IAAAhE,GAAA,EAAAwE,OAAA,CAu0BiuB,CAAC;IAv0BpuBxI,EAAE,CAAA2F,SAAA,CAu0Bk9B,CAAC;IAv0Br9B3F,EAAE,CAAAqH,UAAA,CAAAmB,OAAA,CAAAE,KAu0Bk9B,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0Br9BpE,EAAE,CAAAmH,gBAAA,IAAAmB,kDAAA,oBAAFtI,EAAE,CAAAuI,yBAu0B29B,CAAC;EAAA;EAAA,IAAAnE,EAAA;IAAA,MAAAwE,MAAA,GAv0B99B5I,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAAqH,UAAA,CAAAuB,MAAA,CAAAC,YAu0B29B,CAAC;EAAA;AAAA;AAAA,SAAAC,4DAAA1E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0B99BpE,EAAE,CAAAsE,kBAAA,EAu0BkjC,CAAC;EAAA;AAAA;AAAA,SAAAyE,6CAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0BrjCpE,EAAE,CAAAwE,UAAA,IAAAsE,2DAAA,yBAu0BmiC,CAAC;EAAA;EAAA,IAAA1E,EAAA;IAv0BtiCpE,EAAE,CAAA0E,aAAA;IAAA,MAAAsE,eAAA,GAAFhJ,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2E,UAAA,qBAAAqE,eAu0BgiC,CAAC;EAAA;AAAA;AAAA,SAAAC,kEAAA7E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0BniCpE,EAAE,CAAAsE,kBAAA,EAu0B0wC,CAAC;EAAA;AAAA;AAAA,SAAA4E,mDAAA9E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0B7wCpE,EAAE,CAAAwE,UAAA,IAAAyE,iEAAA,yBAu0B2vC,CAAC;EAAA;EAAA,IAAA7E,EAAA;IAAA,MAAA+E,OAAA,GAAA9E,GAAA,CAAAH,SAAA;IAv0B9vClE,EAAE,CAAA0E,aAAA;IAAA,MAAA2D,eAAA,GAAFrI,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2E,UAAA,qBAAA0D,eAu0BotC,CAAC,4BAv0BvtCrI,EAAE,CAAAgI,eAAA,IAAAhE,GAAA,EAAAmF,OAAA,CAu0BgvC,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0BnvCpE,EAAE,CAAAmH,gBAAA,IAAA+B,kDAAA,8BAAFlJ,EAAE,CAAAuI,yBAu0BixC,CAAC;EAAA;EAAA,IAAAnE,EAAA;IAAA,MAAAwE,MAAA,GAv0BpxC5I,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAAqH,UAAA,CAAAuB,MAAA,CAAAS,WAu0BixC,CAAC;EAAA;AAAA;AAAA,SAAAC,2DAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0BpxCpE,EAAE,CAAAuF,cAAA,WAu0By6C,CAAC;IAv0B56CvF,EAAE,CAAAwF,MAAA,EAu0Bk+C,CAAC;IAv0Br+CxF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAA0F,YAAA,CAu0Bu+C,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAmF,OAAA,GAv0B1+CvJ,EAAE,CAAA0E,aAAA,GAAAR,SAAA;IAAFlE,EAAE,CAAA2F,SAAA,CAu0Bk+C,CAAC;IAv0Br+C3F,EAAE,CAAAwJ,kBAAA,MAAFxJ,EAAE,CAAA6F,WAAA,OAAF7F,EAAE,CAAA6F,WAAA,OAAA0D,OAAA,CAAAE,KAAA,OAu0Bk+C,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0Br+CpE,EAAE,CAAAwE,UAAA,IAAA8E,0DAAA,eAu0B22C,CAAC;EAAA;EAAA,IAAAlF,EAAA;IAAA,MAAAmF,OAAA,GAAAlF,GAAA,CAAAH,SAAA;IAv0B92ClE,EAAE,CAAAgG,aAAA,IAAAuD,OAAA,CAAAb,KAAA,CAAAb,MAAA,SAu0B8+C,CAAC;EAAA;AAAA;AAAA,SAAA8B,kDAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwF,GAAA,GAv0Bj/C5J,EAAE,CAAAqG,gBAAA;IAAFrG,EAAE,CAAAuF,cAAA,WAu0BkqD,CAAC,uBAAwN,CAAC;IAv0B93DvF,EAAE,CAAAsG,UAAA,oBAAAuD,mFAAArD,MAAA;MAAFxG,EAAE,CAAAyG,aAAA,CAAAmD,GAAA;MAAA,MAAAhB,MAAA,GAAF5I,EAAE,CAAA0E,aAAA;MAAA,OAAF1E,EAAE,CAAA0G,WAAA,CAu0BwuDkC,MAAA,CAAAkB,iBAAA,CAAAtD,MAAA,EAAAoC,MAAA,CAAAS,WAAqC,CAAC;IAAA,CAAC,CAAC,wBAAAU,uFAAAvD,MAAA;MAv0BlxDxG,EAAE,CAAAyG,aAAA,CAAAmD,GAAA;MAAA,MAAAhB,MAAA,GAAF5I,EAAE,CAAA0E,aAAA;MAAA,OAAF1E,EAAE,CAAA0G,WAAA,CAu0ByyDkC,MAAA,CAAAoB,YAAA,CAAAxD,MAAA,EAAAoC,MAAA,CAAAS,WAAgC,CAAC;IAAA,CAAC,CAAC;IAv0B90DrJ,EAAE,CAAA0F,YAAA,CAu0B44D,CAAC,CAAU,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA6F,QAAA,GAv0B15DjK,EAAE,CAAA0E,aAAA,GAAAR,SAAA;IAAA,MAAA0E,MAAA,GAAF5I,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2F,SAAA,CAu0BktD,CAAC;IAv0BrtD3F,EAAE,CAAA2E,UAAA,SAAAsF,QAu0BktD,CAAC,eAAArB,MAAA,CAAAsB,UAAA,EAA+J,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAA/F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0Br3DpE,EAAE,CAAAwE,UAAA,IAAAmF,iDAAA,eAu0BkqD,CAAC;EAAA;EAAA,IAAAvF,EAAA;IAAA,MAAA6F,QAAA,GAAA5F,GAAA,CAAAH,SAAA;IAAA,MAAA0E,MAAA,GAv0BrqD5I,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2E,UAAA,gBAAAsF,QAAA,CAAAhD,OAAA,IAAAgD,QAAA,CAAAhD,OAAA,CAAAgD,QAAA,EAAArB,MAAA,CAAA/D,QAAA,CAu0BypD,CAAC;EAAA;AAAA;AAAA,MAAAuF,GAAA,GAAAA,CAAAnG,EAAA,EAAAoG,EAAA;EAAAnG,SAAA,EAAAD,EAAA;EAAAqG,UAAA,EAAAD;AAAA;AAAA,SAAAE,wCAAAnG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv0B5pDpE,EAAE,CAAAsE,kBAAA,EAi3B6c,CAAC;EAAA;AAAA;AAAA,SAAAkG,sDAAApG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3BhdpE,EAAE,CAAAsE,kBAAA,EAi3Bk2B,CAAC;EAAA;AAAA;AAAA,SAAAmG,uCAAArG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3Br2BpE,EAAE,CAAAwE,UAAA,IAAAgG,qDAAA,yBAi3Bm1B,CAAC;EAAA;EAAA,IAAApG,EAAA;IAAA,MAAAsG,MAAA,GAj3Bt1B1K,EAAE,CAAA0E,aAAA;IAAA,MAAAiG,wBAAA,GAAF3K,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2E,UAAA,qBAAAgG,wBAi3B+xB,CAAC,4BAj3BlyB3K,EAAE,CAAAgI,eAAA,IAAAhE,GAAA,EAAA0G,MAAA,CAAAE,aAAA,CAi3Bo0B,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAAzG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3Bv0BpE,EAAE,CAAAsE,kBAAA,EAi3B2nC,CAAC;EAAA;AAAA;AAAA,SAAAwG,wCAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3B9nCpE,EAAE,CAAAsE,kBAAA,EAi3B4xC,CAAC;EAAA;AAAA;AAAA,SAAAyG,wCAAA3G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3B/xCpE,EAAE,CAAAmF,SAAA,0BAi3B6gD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA4G,QAAA,GAAA3G,GAAA,CAAAH,SAAA;IAAA,MAAA+G,aAAA,GAAA5G,GAAA,CAAAiG,UAAA;IAj3BhhDtK,EAAE,CAAA2E,UAAA,gBAAAqG,QAi3Bg7C,CAAC,iBAAAC,aAAoC,CAAC,mBAA4B,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAA9G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3Br/CpE,EAAE,CAAAsE,kBAAA,EAi3B4vD,CAAC;EAAA;AAAA;AAAA,SAAA6G,uDAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3B/vDpE,EAAE,CAAAoL,uBAAA,EAi3BipD,CAAC;IAj3BppDpL,EAAE,CAAAwE,UAAA,IAAA0G,qEAAA,0BAi3B6uD,CAAC;IAj3BhvDlL,EAAE,CAAAqL,qBAAA;EAAA;EAAA,IAAAjH,EAAA;IAAA,MAAAkH,YAAA,GAAAjH,GAAA,CAAAH,SAAA;IAAA,MAAAwG,MAAA,GAAF1K,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2F,SAAA,CAi3BgtD,CAAC;IAj3BntD3F,EAAE,CAAA2E,UAAA,sBAAA2G,YAi3BgtD,CAAC,8BAAAZ,MAAA,CAAA7F,QAAiB,CAAC;EAAA;AAAA;AAAA,SAAA0G,wCAAAnH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3BruDpE,EAAE,CAAAwE,UAAA,IAAA2G,sDAAA,yBAi3BipD,CAAC;EAAA;EAAA,IAAA/G,EAAA;IAAA,MAAAoH,WAAA,GAAAnH,GAAA,CAAAH,SAAA;IAj3BppDlE,EAAE,CAAA2E,UAAA,YAAA6G,WAi3B8oD,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAArH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3BjpDpE,EAAE,CAAAmF,SAAA,oBAi3B42D,CAAC;EAAA;AAAA;AAAA,SAAAuG,uDAAAtH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj3B/2DpE,EAAE,CAAAmF,SAAA,iBAqlCykB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAuH,OAAA,GArlC5kB3L,EAAE,CAAA0E,aAAA,GAAAR,SAAA;IAAFlE,EAAE,CAAA2E,UAAA,cAAAgH,OAAA,CAAAvG,IAqlCijB,CAAC;EAAA;AAAA;AAAA,SAAAwG,2DAAAxH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArlCpjBpE,EAAE,CAAAsE,kBAAA,EAqlCgyB,CAAC;EAAA;AAAA;AAAA,SAAAuH,iDAAAzH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArlCnyBpE,EAAE,CAAAuF,cAAA,YAqlCg3B,CAAC;IArlCn3BvF,EAAE,CAAAmF,SAAA,kBAqlCm7B,CAAC;IArlCt7BnF,EAAE,CAAA0F,YAAA,CAqlCk8B,CAAC;EAAA;AAAA;AAAA,SAAAoG,4CAAA1H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2H,GAAA,GArlCr8B/L,EAAE,CAAAqG,gBAAA;IAAFrG,EAAE,CAAAoL,uBAAA,EAqlCyN,CAAC;IArlC5NpL,EAAE,CAAAuF,cAAA,WAqlCoa,CAAC;IArlCvavF,EAAE,CAAAsG,UAAA,mBAAA0F,gEAAA;MAAA,MAAAL,OAAA,GAAF3L,EAAE,CAAAyG,aAAA,CAAAsF,GAAA,EAAA7H,SAAA;MAAA,MAAA+H,MAAA,GAAFjM,EAAE,CAAA0E,aAAA;MAAA,OAAF1E,EAAE,CAAA0G,WAAA,CAqlCsSuF,MAAA,CAAAC,OAAA,CAAAP,OAAY,CAAC;IAAA,CAAC,CAAC,6BAAAQ,0EAAA;MAAA,MAAAR,OAAA,GArlCvT3L,EAAE,CAAAyG,aAAA,CAAAsF,GAAA,EAAA7H,SAAA;MAAA,OAAFlE,EAAE,CAAA0G,WAAA,CAAAiF,OAAA,CAAAzF,QAAA,GAqlCmZ,KAAK;IAAA,CAAC,CAAC;IArlC5ZlG,EAAE,CAAAwE,UAAA,IAAAkH,sDAAA,qBAqlC8jB,CAAC,IAAAE,0DAAA,yBAAkN,CAAC;IArlCpxB5L,EAAE,CAAA0F,YAAA,CAqlC+yB,CAAC;IArlClzB1F,EAAE,CAAAwE,UAAA,IAAAqH,gDAAA,eAqlCg3B,CAAC;IArlCn3B7L,EAAE,CAAAqL,qBAAA;EAAA;EAAA,IAAAjH,EAAA;IAAA,MAAAuH,OAAA,GAAAtH,GAAA,CAAAH,SAAA;IAAA,MAAAkI,OAAA,GAAA/H,GAAA,CAAAgI,IAAA;IAAFrM,EAAE,CAAA0E,aAAA;IAAA,MAAA4H,eAAA,GAAFtM,EAAE,CAAAiF,WAAA;IAAA,MAAAsH,eAAA,GAAFvM,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2F,SAAA,CAqlCkW,CAAC;IArlCrW3F,EAAE,CAAAoH,WAAA,aAAAuE,OAAA,CAAAzF,QAqlCkW,CAAC;IArlCrWlG,EAAE,CAAA2F,SAAA,CAqlCwgB,CAAC;IArlC3gB3F,EAAE,CAAA2E,UAAA,SAAAgH,OAAA,CAAAvG,IAqlCwgB,CAAC;IArlC3gBpF,EAAE,CAAA2F,SAAA,CAqlCkuB,CAAC;IArlCruB3F,EAAE,CAAA2E,UAAA,sBAAAgH,OAAA,CAAA5H,QAAA,kBAAA4H,OAAA,CAAA5H,QAAA,CAAA8D,MAAA,IAAA0E,eAAA,GAAAD,eAqlCkuB,CAAC,4BArlCruBtM,EAAE,CAAAgI,eAAA,IAAAhE,GAAA,EAAA2H,OAAA,CAqlC8vB,CAAC;IArlCjwB3L,EAAE,CAAA2F,SAAA,CAqlC00B,CAAC;IArlC70B3F,EAAE,CAAA2E,UAAA,UAAAyH,OAqlC00B,CAAC;EAAA;AAAA;AAAA,SAAAI,2CAAApI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArlC70BpE,EAAE,CAAAuF,cAAA,WAqlC8jC,CAAC;IArlCjkCvF,EAAE,CAAAwF,MAAA,EAqlCsmC,CAAC;IArlCzmCxF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAA0F,YAAA,CAqlC0mC,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA+E,OAAA,GAAA9E,GAAA,CAAAH,SAAA;IArlC7mClE,EAAE,CAAA2E,UAAA,eAAAwE,OAAA,CAAArB,IAqlC6jC,CAAC;IArlChkC9H,EAAE,CAAA2F,SAAA,CAqlCsmC,CAAC;IArlCzmC3F,EAAE,CAAAwJ,kBAAA,MAAFxJ,EAAE,CAAA6F,WAAA,OAAF7F,EAAE,CAAA6F,WAAA,OAAAsD,OAAA,CAAArD,IAAA,OAqlCsmC,CAAC;EAAA;AAAA;AAAA,SAAA2G,2CAAArI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArlCzmCpE,EAAE,CAAAuF,cAAA,cAqlCmtC,CAAC;IArlCttCvF,EAAE,CAAAwF,MAAA,EAqlCuwC,CAAC;IArlC1wCxF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAAyF,MAAA;IAAFzF,EAAE,CAAA0F,YAAA,CAqlC8wC,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAmF,OAAA,GAAAlF,GAAA,CAAAH,SAAA;IArlCjxClE,EAAE,CAAA2F,SAAA,CAqlCuwC,CAAC;IArlC1wC3F,EAAE,CAAAwJ,kBAAA,MAAFxJ,EAAE,CAAA6F,WAAA,OAAF7F,EAAE,CAAA6F,WAAA,OAAA0D,OAAA,CAAAzD,IAAA,OAqlCuwC,CAAC;EAAA;AAAA;AAAA,SAAA4G,8CAAAtI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArlC1wCpE,EAAE,CAAAoL,uBAAA,EAkhDsP,CAAC;IAlhDzPpL,EAAE,CAAAmF,SAAA,iBAkhD6U,CAAC;IAlhDhVnF,EAAE,CAAAqL,qBAAA;EAAA;EAAA,IAAAjH,EAAA;IAAA,MAAAK,MAAA,GAAFzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2F,SAAA,CAkhDiU,CAAC;IAlhDpU3F,EAAE,CAAA2E,UAAA,cAAAF,MAAA,CAAAkI,MAAA,CAAAC,MAkhDiU,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAAzI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlhDpUpE,EAAE,CAAAoL,uBAAA,EAkhDoZ,CAAC;IAlhDvZpL,EAAE,CAAAmF,SAAA,YAkhDsd,CAAC;IAlhDzdnF,EAAE,CAAAqL,qBAAA;EAAA;EAAA,IAAAjH,EAAA;IAAA,MAAAK,MAAA,GAAFzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2F,SAAA,CAkhDmd,CAAC;IAlhDtd3F,EAAE,CAAA2E,UAAA,QAAAF,MAAA,CAAAkI,MAAA,CAAAC,MAAA,EAAF5M,EAAE,CAAA8M,aAkhDmd,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAA3I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlhDtdpE,EAAE,CAAAuF,cAAA,YAkhDwJ,CAAC;IAlhD3JvF,EAAE,CAAAoL,uBAAA,KAkhDuM,CAAC;IAlhD1MpL,EAAE,CAAAwE,UAAA,IAAAkI,6CAAA,yBAkhDsP,CAAC,IAAAG,6CAAA,yBAA6J,CAAC;IAlhDvZ7M,EAAE,CAAAqL,qBAAA;IAAFrL,EAAE,CAAA0F,YAAA,CAkhD4gB,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAK,MAAA,GAlhD/gBzE,EAAE,CAAA0E,aAAA;IAAF1E,EAAE,CAAA2F,SAAA,CAkhDsM,CAAC;IAlhDzM3F,EAAE,CAAA2E,UAAA,aAAAF,MAAA,CAAAkI,MAAA,CAAAK,IAkhDsM,CAAC;IAlhDzMhN,EAAE,CAAA2F,SAAA,CAkhDmP,CAAC;IAlhDtP3F,EAAE,CAAA2E,UAAA,uBAkhDmP,CAAC;IAlhDtP3E,EAAE,CAAA2F,SAAA,CAkhDiZ,CAAC;IAlhDpZ3F,EAAE,CAAA2E,UAAA,wBAkhDiZ,CAAC;EAAA;AAAA;AAAA,MAAAsI,GAAA,GAAAhJ,EAAA,KAAAA,EAAA;AAAA,SAAAiJ,4CAAA9I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlhDpZpE,EAAE,CAAAuF,cAAA,UAikDuW,CAAC;IAjkD1WvF,EAAE,CAAAwF,MAAA,EAikD4Y,CAAC;IAjkD/YxF,EAAE,CAAA0F,YAAA,CAikD4Z,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA+I,eAAA,GAjkD/ZnN,EAAE,CAAA0E,aAAA,GAAA0I,IAAA;IAAFpN,EAAE,CAAA2E,UAAA,eAAF3E,EAAE,CAAAgI,eAAA,IAAAiF,GAAA,EAAAE,eAAA,CAAAE,OAAA,CAikD0V,CAAC;IAjkD7VrN,EAAE,CAAA2F,SAAA,CAikD4Y,CAAC;IAjkD/Y3F,EAAE,CAAAwJ,kBAAA,MAAA2D,eAAA,CAAAG,IAAA,IAikD4Y,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAnJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjkD/YpE,EAAE,CAAAuF,cAAA,OAikDkd,CAAC;IAjkDrdvF,EAAE,CAAAwF,MAAA,EAikD0e,CAAC;IAjkD7exF,EAAE,CAAA0F,YAAA,CAikD8e,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA+I,eAAA,GAjkDjfnN,EAAE,CAAA0E,aAAA,GAAA0I,IAAA;IAAFpN,EAAE,CAAA2F,SAAA,CAikD0e,CAAC;IAjkD7e3F,EAAE,CAAAwJ,kBAAA,MAAA2D,eAAA,CAAAG,IAAA,IAikD0e,CAAC;EAAA;AAAA;AAAA,SAAAE,2DAAApJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjkD7epE,EAAE,CAAAuF,cAAA,UAikDiuB,CAAC;IAjkDpuBvF,EAAE,CAAAwF,MAAA,EAikDkxB,CAAC;IAjkDrxBxF,EAAE,CAAA0F,YAAA,CAikDsxB,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAqJ,aAAA,GAjkDzxBzN,EAAE,CAAA0E,aAAA,GAAAR,SAAA;IAAFlE,EAAE,CAAA2E,UAAA,eAAF3E,EAAE,CAAAgI,eAAA,IAAAiF,GAAA,EAAAQ,aAAA,CAAA3F,IAAA,CAikDguB,CAAC;IAjkDnuB9H,EAAE,CAAA2F,SAAA,CAikDkxB,CAAC;IAjkDrxB3F,EAAE,CAAA4F,iBAAA,CAAA6H,aAAA,CAAA3H,IAikDkxB,CAAC;EAAA;AAAA;AAAA,SAAA4H,uDAAAtJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjkDrxBpE,EAAE,CAAAoL,uBAAA,EAikDwpB,CAAC;IAjkD3pBpL,EAAE,CAAAwE,UAAA,IAAAgJ,0DAAA,cAikDiuB,CAAC;IAjkDpuBxN,EAAE,CAAAqL,qBAAA;EAAA;EAAA,IAAAjH,EAAA;IAAA,MAAAqJ,aAAA,GAAApJ,GAAA,CAAAH,SAAA;IAAFlE,EAAE,CAAA2F,SAAA,CAikD2rB,CAAC;IAjkD9rB3F,EAAE,CAAA2E,UAAA,SAAA8I,aAikD2rB,CAAC;EAAA;AAAA;AAAA,SAAAE,wCAAAvJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjkD9rBpE,EAAE,CAAAoL,uBAAA,EAikD2H,CAAC;IAjkD9HpL,EAAE,CAAAuF,cAAA,YAikDkK,CAAC,YAA8C,CAAC;IAjkDpNvF,EAAE,CAAAwE,UAAA,IAAA0I,2CAAA,cAikDuW,CAAC,IAAAK,qDAAA,gCAjkD1WvN,EAAE,CAAA0H,sBAikDic,CAAC;IAjkDpc1H,EAAE,CAAA0F,YAAA,CAikDwhB,CAAC;IAjkD3hB1F,EAAE,CAAAuF,cAAA,YAikDwkB,CAAC;IAjkD3kBvF,EAAE,CAAAwE,UAAA,IAAAkJ,sDAAA,yBAikDwpB,CAAC;IAjkD3pB1N,EAAE,CAAA0F,YAAA,CAikDi0B,CAAC,CAAa,CAAC;IAjkDl1B1F,EAAE,CAAAqL,qBAAA;EAAA;EAAA,IAAAjH,EAAA;IAAA,MAAA+I,eAAA,GAAA9I,GAAA,CAAA+I,IAAA;IAAA,MAAAQ,aAAA,GAAF5N,EAAE,CAAAiF,WAAA;IAAFjF,EAAE,CAAA2F,SAAA,EAikDoR,CAAC;IAjkDvR3F,EAAE,CAAA2E,UAAA,SAAAwI,eAAA,CAAAE,OAikDoR,CAAC,aAAAO,aAAc,CAAC;IAjkDtS5N,EAAE,CAAA2F,SAAA,EAikDqpB,CAAC;IAjkDxpB3F,EAAE,CAAA2E,UAAA,YAAAwI,eAAA,CAAAU,WAikDqpB,CAAC;EAAA;AAAA;AA9pD5vB,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG/N,MAAM,CAACyB,MAAM,CAAC;EAChC;AACJ;AAEA,MAAMuM,YAAY,GAAG,IAAI/N,cAAc,CAAC,cAAc,CAAC;AAEvD,MAAMgO,SAAS,CAAC;EACZ,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK;EAC5B;EACAN,WAAWA,CAACO,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACF,MAAM,GAAG,IAAIzL,eAAe,CAAC,IAAI,CAAC2L,YAAY,CAAC;IACpD,IAAI,CAACC,OAAO,GAAG,IAAI3L,OAAO,CAAC,CAAC;IAC5B,IAAI,CAAC4L,UAAU,GAAG,CAACC,QAAQ,EAAEC,SAAS,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE,KAAK,IAAI,CAACR,MAAM,CAACS,IAAI,CAAC1M,GAAG,CAACsM,QAAQ,CAAC,EAAErM,oBAAoB,CAACsM,SAAS,CAAC,CAAC;IACnI,IAAI,CAACI,WAAW,GAAG,CAACL,QAAQ,EAAEM,QAAQ,GAAIC,CAAC,IAAKA,CAAC,KAAKC,SAAS,KAAK,IAAI,CAACV,OAAO,CAACM,IAAI,CAAC1M,GAAG,CAACsM,QAAQ,CAAC,EAAEpM,MAAM,CAAC0M,QAAQ,CAAC,CAAC;EAC1H;EACAG,KAAKA,CAACf,KAAK,EAAE;IACT,IAAIgB,YAAY,GAAGhB,KAAK;IACxB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACiB,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,EAAE;MACpDgB,YAAY,GAAG;QAAE,GAAG,IAAI,CAAChB,KAAK;QAAE,GAAGA;MAAM,CAAC;IAC9C;IACA,IAAI,CAACC,MAAM,CAACkB,IAAI,CAACH,YAAY,CAAC;IAC9B,IAAI,CAACZ,OAAO,CAACe,IAAI,CAACH,YAAY,CAAC;EACnC;EACAI,GAAGA,CAACpB,KAAK,EAAE;IACP,IAAI,CAACC,MAAM,CAACkB,IAAI,CAACnB,KAAK,CAAC;IACvB,IAAI,CAACI,OAAO,CAACe,IAAI,CAACnB,KAAK,CAAC;EAC5B;EACAqB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACD,GAAG,CAAC,IAAI,CAACjB,YAAY,CAAC;EAC/B;AACJ;AAEA,IAAImB,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAAC,eAAe,CAAC,GAAG,yBAAyB;AACtE,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,MAAMC,yBAAyB,GAAG;EAC9B,CAACD,qBAAqB,CAACE,aAAa,GAAG;AAC3C,CAAC;AAED,MAAMC,eAAe,CAAC;EAClB,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,KAAK,CAAC3B,KAAK,CAAC0B,gBAAgB;EAC5C;EACA9B,WAAWA,CAACgC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,KAAK,GAAG,IAAI5B,SAAS,CAAC;MAAE6B,SAAS,EAAE;IAAG,CAAC,CAAC;IAC7C,IAAI,CAACC,EAAE,GAAG,WAAW;IACrB,IAAI,CAACC,2BAA2B,GAAIF,SAAS,IAAK;MAC9C,OAAOA,SAAS,CAAC5N,GAAG,CAAE+N,IAAI,KAAM;QAC5B9K,IAAI,EAAE,EAAE;QACRU,IAAI,EAAEoK,IAAI,CAACC,WAAW;QACtBvI,QAAQ,EAAEsI,IAAI,CAACtI,QAAQ;QACvBwI,MAAM,EAAEA,CAAA,KAAM;UACV,IAAI,CAACC,mBAAmB,CAACH,IAAI,CAAC;UAC9B,OAAO,IAAI;QACf;MACJ,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACR,KAAK,CAACtB,UAAU,CAAC,CAAC;MAAEqB;IAAiB,CAAC,KAAKA,gBAAgB,CAAC;IAC1F,IAAI,CAACU,eAAe,GAAG,IAAI,CAACD,iBAAiB,CAACzB,IAAI;IAClD;IACA;IACAxM,MAAM,CAAE6N,IAAI,IAAKA,IAAI,KAAKjB,SAAS,CAAC,EAAE7M,oBAAoB,CAAC,CAACoO,CAAC,EAAEC,CAAC,KAAKD,CAAC,EAAEE,WAAW,KAAKD,CAAC,EAAEC,WAAW,CAAC,CAAC;IACxG,IAAI,CAACC,UAAU,GAAG,IAAI,CAACb,KAAK,CAACtB,UAAU,CAAEL,KAAK,IAAKA,KAAK,CAAC4B,SAAS,CAAC;IACnE,IAAI,CAACa,uBAAuB,GAAG,IAAI,CAACD,UAAU,CAAC9B,IAAI,CAAC1M,GAAG,CAAC,IAAI,CAAC8N,2BAA2B,CAAC,CAAC;IAC1F;IACA,IAAI,CAACY,yBAAyB,GAAG,IAAI,CAACD,uBAAuB,CAAC/B,IAAI,CAAC1M,GAAG,CAAE4N,SAAS,KAAM;MACnFjK,IAAI,EAAE2J,qBAAqB,CAACE,aAAa;MACzCvK,IAAI,EAAE,aAAa;MACnB4K,EAAE,EAAE,IAAI,CAACA,EAAE;MACXjM,QAAQ,EAAEgM;IACd,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACe,IAAI,CAAC,IAAI,CAACf,SAAS,CAAC;EAC7B;EACAgB,YAAYA,CAAChB,SAAS,EAAE;IACpB,IAAI,CAACe,IAAI,CAACf,SAAS,CAAC;EACxB;EACAe,IAAIA,CAACf,SAAS,EAAE;IACZ,IAAI,CAACD,KAAK,CAACZ,KAAK,CAAC;MACba,SAAS;MACTF,gBAAgB,EAAEE,SAAS,CAACiB,IAAI,CAAEd,IAAI,IAAKA,IAAI,CAACtI,QAAQ;IAC5D,CAAC,CAAC;EACN;EACAyI,mBAAmBA,CAACH,IAAI,EAAE;IACtB,IAAI,CAACJ,KAAK,CAACZ,KAAK,CAAC;MACbW,gBAAgB,EAAEK;IACtB,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACe,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFvB,eAAe,EAAzB5P,EAAE,CAAAoR,QAAA,CAAyCnD,YAAY;IAAA,CAA6C;EAAE;EACtM;IAAS,IAAI,CAACoD,KAAK,kBAD6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EACY3B,eAAe;MAAA4B,OAAA,EAAf5B,eAAe,CAAAqB,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG1R,EAAE,CAAA2R,iBAAA,CAGX/B,eAAe,EAAc,CAAC;IAC7G5C,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzE,IAAI,EAAEiC,SAAS;IAAE4C,UAAU,EAAE,CAAC;MAC/C7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAAC3D,YAAY;IACvB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAM6D,2BAA2B,GAAG,IAAI5R,cAAc,CAAC,6BAA6B,CAAC;AACrF,MAAM6R,mBAAmB,GAAG,IAAI7R,cAAc,CAAC,qBAAqB,CAAC;AAErE,MAAM8R,iBAAiB,CAAC;EACpB,OAAOC,OAAOA,CAACC,OAAO,EAAE;IACpB,OAAO;MACHC,QAAQ,EAAEH,iBAAiB;MAC3BI,SAAS,EAAE,CACP;QACIC,OAAO,EAAEpE,YAAY;QACrBqE,QAAQ,EAAEJ,OAAO,EAAEnC,SAAS,IAAI;MACpC,CAAC,EACD;QACIsC,OAAO,EAAEN,mBAAmB;QAC5BO,QAAQ,EAAE,CAAC5C,yBAAyB,CAAC;QACrC6C,KAAK,EAAE;MACX,CAAC,EACD3C,eAAe;IAEvB,CAAC;EACL;EACA;IAAS,IAAI,CAACqB,IAAI,YAAAuB,0BAAArB,CAAA;MAAA,YAAAA,CAAA,IAAwFa,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACS,IAAI,kBAnC8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAmCSgF;IAAiB,EAA4B;EAAE;EAC1J;IAAS,IAAI,CAACW,IAAI,kBApC8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAoCsC7Q,YAAY;IAAA,EAAI;EAAE;AAC9J;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KAtCoG1R,EAAE,CAAA2R,iBAAA,CAsCXK,iBAAiB,EAAc,CAAC;IAC/GhF,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,EAAE;MAChBD,OAAO,EAAE,CAAC7Q,YAAY;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM+Q,QAAQ,GAAG;EACbC,OAAO,EAAE,gBAAgB;EACzBC,QAAQ,EAAE,iBAAiB;EAC3BC,YAAY,EAAE,sBAAsB;EACpCC,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,oBAAoB;EACjCC,SAAS,EAAE,kBAAkB;EAC7BC,aAAa,EAAE,2BAA2B;EAC1CjR,MAAM,EAAE,cAAc;EACtBkR,UAAU,EAAE,0BAA0B;EACtCC,kBAAkB,EAAE,4BAA4B;EAChDC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,cAAc;EACtBC,MAAM,EAAE,gCAAgC;EACxCC,KAAK,EAAE,4BAA4B;EACnCC,IAAI,EAAE,YAAY;EAClB7E,CAAC,EAAE,SAAS;EACZ8E,WAAW,EAAE;AACjB,CAAC;AACD,MAAMC,iBAAiB,GAAG,IAAI7T,cAAc,CAAC,mBAAmB,CAAC;AAEjE,MAAM8T,aAAa,CAAC;EAChB,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,IAAI,CAACA,SAAS;EACzD;EACApG,WAAWA,CAACmG,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;IAAS,IAAI,CAACjD,IAAI,YAAAmD,sBAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwF6C,aAAa,EA1EvBhU,EAAE,CAAAqU,iBAAA,CA0EuCN,iBAAiB;IAAA,CAA4C;EAAE;EACxM;IAAS,IAAI,CAACO,IAAI,kBA3E8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EA2EJgH,aAAa;MAAAQ,SAAA;MAAAC,MAAA;QAAAN,SAAA;MAAA;MAAAO,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAA8M,uBAAAzQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3EXpE,EAAE,CAAAmF,SAAA,UA2EgK,CAAC;QAAA;QAAA,IAAAf,EAAA;UA3EnKpE,EAAE,CAAA2E,UAAA,YAAAN,GAAA,CAAA4P,UA2EwI,CAAC;QAAA;MAAA;MAAAa,YAAA,GAAsF/S,EAAE,CAACgT,OAAO;MAAAC,aAAA;IAAA,EAAqG;EAAE;AACtb;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KA7EoG1R,EAAE,CAAA2R,iBAAA,CA6EXqC,aAAa,EAAc,CAAC;IAC3GhH,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE,UAAU;MACpB1G,QAAQ,EAAE,sEAAsE;MAChFiN,aAAa,EAAEzU,iBAAiB,CAAC0U;IACrC,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjI,IAAI,EAAEiC,SAAS;IAAE4C,UAAU,EAAE,CAAC;MAC/C7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAACmC,iBAAiB;IAC5B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEI,SAAS,EAAE,CAAC;MACrCnH,IAAI,EAAExM;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0U,aAAa,CAAC;EAChB,OAAOjD,OAAOA,CAACC,OAAO,EAAE;IACpB,OAAO;MACHC,QAAQ,EAAE+C,aAAa;MACvB9C,SAAS,EAAE,CACP;QACIC,OAAO,EAAE0B,iBAAiB;QAC1BzB,QAAQ,EAAEJ,OAAO,EAAEgC,OAAO,IAAInB;MAClC,CAAC;IAET,CAAC;EACL;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAAkE,sBAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAwF+D,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACzC,IAAI,kBAxG8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAwGSkI;IAAa,EAAqF;EAAE;EAC/M;IAAS,IAAI,CAACvC,IAAI,kBAzG8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAyGkC7Q,YAAY;IAAA,EAAI;EAAE;AAC1J;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KA3GoG1R,EAAE,CAAA2R,iBAAA,CA2GXuD,aAAa,EAAc,CAAC;IAC3GlI,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACkB,aAAa,CAAC;MAC7BnB,OAAO,EAAE,CAAC7Q,YAAY,CAAC;MACvBoT,OAAO,EAAE,CAACpB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMqB,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACpE,IAAI,YAAAqE,2BAAAnE,CAAA;MAAA,YAAAA,CAAA,IAAwFkE,kBAAkB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACf,IAAI,kBAtH8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EAsHJqI,kBAAkB;MAAAb,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAAwN,4BAAAnR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtHhBpE,EAAE,CAAAuF,cAAA,UAsHwF,CAAC;UAtH3FvF,EAAE,CAAAmF,SAAA,YAsHkI,CAAC;UAtHrInF,EAAE,CAAA0F,YAAA,CAsH0I,CAAC;QAAA;MAAA;MAAAoP,YAAA,GAAiDrT,EAAE,CAAC+T,UAAU;MAAAR,aAAA;IAAA,EAAqP;EAAE;AACtiB;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAxHoG1R,EAAE,CAAA2R,iBAAA,CAwHX0D,kBAAkB,EAAc,CAAC;IAChHrI,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,gBAAgB;MAAEuG,aAAa,EAAEzU,iBAAiB,CAAC0U,IAAI;MAAElN,QAAQ,EAAE;IAA6E,CAAC;EACxK,CAAC,CAAC;AAAA;AAEV,MAAM0N,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACxE,IAAI,YAAAyE,2BAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAwFsE,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAAChD,IAAI,kBA/H8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EA+HSyI;IAAkB,EAA+F;EAAE;EAC9N;IAAS,IAAI,CAAC9C,IAAI,kBAhI8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAgIuClR,YAAY;IAAA,EAAI;EAAE;AAC/J;AACA;EAAA,QAAA+P,SAAA,oBAAAA,SAAA,KAlIoG1R,EAAE,CAAA2R,iBAAA,CAkIX8D,kBAAkB,EAAc,CAAC;IAChHzI,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACuC,kBAAkB,CAAC;MAClCxC,OAAO,EAAE,CACLlR,YAAY,CACf;MACDyT,OAAO,EAAE,CAACC,kBAAkB;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMM,aAAa,CAAC;EAChB5H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+B,KAAK,GAAG,IAAI5B,SAAS,CAAC;MACvB0H,cAAc,EAAE,CAAC,EAAE;IACvB,CAAC,CAAC;IACF,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC/F,KAAK,CAACtB,UAAU,CAAC,CAAC;MAAEoH;IAAe,CAAC,KAAKA,cAAc,IAAI,EAAE,CAAC;EAC9F;EACAE,QAAQA,CAACC,QAAQ,EAAE;IACf,MAAMH,cAAc,GAAGxG,KAAK,CAACC,OAAO,CAAC0G,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IACtE,IAAI,CAACC,UAAU,CAACJ,cAAc,CAAC;EACnC;EACAK,QAAQA,CAACF,QAAQ,EAAE;IACf,MAAM;MAAEH;IAAe,CAAC,GAAG,IAAI,CAAC9F,KAAK,CAAC3B,KAAK;IAC3C,IAAI,CAAC6H,UAAU,CAAC,CAAC,GAAGJ,cAAc,EAAEG,QAAQ,CAAC,CAAC;EAClD;EACAG,WAAWA,CAACH,QAAQ,EAAE;IAClB,MAAM;MAAEH;IAAe,CAAC,GAAG,IAAI,CAAC9F,KAAK,CAAC3B,KAAK;IAC3C,MAAMgI,KAAK,GAAGP,cAAc,CAACQ,SAAS,CAACtS,IAAI,IAAIA,IAAI,KAAKiS,QAAQ,CAAC;IACjE,IAAII,KAAK,KAAK,CAAC,CAAC,EACZ;IACJ,MAAME,MAAM,GAAG,CAAC,GAAGT,cAAc,CAACU,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAE,GAAGP,cAAc,CAACU,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAAC;IACtF,IAAI,CAACH,UAAU,CAACK,MAAM,CAAC;EAC3B;EACAE,aAAaA,CAACC,SAAS,EAAE;IACrB,MAAM;MAAEZ;IAAe,CAAC,GAAG,IAAI,CAAC9F,KAAK,CAAC3B,KAAK;IAC3C,MAAMsI,iBAAiB,GAAGb,cAAc,CAACvT,MAAM,CAACqU,IAAI,IAAI,CAACF,SAAS,CAACG,QAAQ,CAACD,IAAI,CAAC,CAAC;IAClF,IAAI,CAACV,UAAU,CAACS,iBAAiB,CAAC;EACtC;EACAG,WAAWA,CAACb,QAAQ,EAAE;IAClB,MAAM;MAAEH;IAAe,CAAC,GAAG,IAAI,CAAC9F,KAAK,CAAC3B,KAAK;IAC3C,MAAMgI,KAAK,GAAGP,cAAc,CAACQ,SAAS,CAACtS,IAAI,IAAIA,IAAI,KAAKiS,QAAQ,CAAC;IACjE,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;MACd,IAAI,CAACF,QAAQ,CAACF,QAAQ,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACG,WAAW,CAACH,QAAQ,CAAC;IAC9B;EACJ;EACAC,UAAUA,CAACJ,cAAc,EAAE;IACvB,IAAI,CAAC9F,KAAK,CAACZ,KAAK,CAAC;MACb0G;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC3E,IAAI,YAAA4F,sBAAA1F,CAAA;MAAA,YAAAA,CAAA,IAAwFwE,aAAa;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACtE,KAAK,kBAzL6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAyLYoE,aAAa;MAAAnE,OAAA,EAAbmE,aAAa,CAAA1E,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3LoG1R,EAAE,CAAA2R,iBAAA,CA2LXgE,aAAa,EAAc,CAAC;IAC3G3I,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMqF,qBAAqB,GAAG,IAAI5W,cAAc,CAAC,uBAAuB,CAAC;AACzE,MAAM6W,oBAAoB,GAAG,IAAI7W,cAAc,CAAC,sBAAsB,CAAC;AACvE,MAAM8W,cAAc,GAAG,IAAI9W,cAAc,CAAC,gBAAgB,CAAC;AAE3D,SAAS+W,SAASA,CAACzG,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAI,CAACD,CAAC,CAAC0G,KAAK,EAAE;IACV,OAAO,CAAC;EACZ;EACA,IAAI,CAACzG,CAAC,CAACyG,KAAK,EAAE;IACV,OAAO,CAAC,CAAC;EACb;EACA,OAAO1G,CAAC,CAAC0G,KAAK,GAAGzG,CAAC,CAACyG,KAAK;AAC5B;AACA,SAASC,qBAAqBA,CAACC,GAAG,EAAE;EAChC,OAAOA,GAAG,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,MAAM;IAC9B,GAAGD,GAAG;IACN,IAAIlI,KAAK,CAACC,OAAO,CAACkI,IAAI,CAAC,GAAGJ,qBAAqB,CAACI,IAAI,CAAC,GAAGA,IAAI;EAChE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACX;AACA,SAASC,UAAUA,CAAC5K,MAAM,EAAE;EACxB,OAAOA,MAAM,YAAY/J,UAAU,GAC7B+J,MAAM,GACNA,MAAM,YAAY6K,OAAO,GACrB3U,IAAI,CAAC8J,MAAM,CAAC,GACZ7J,EAAE,CAAC6J,MAAM,CAAC;AACxB;AACA,SAAS8K,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK1I,SAAS;AAC5C;AACA,SAASI,OAAOA,CAACsI,GAAG,EAAE;EAClB,OAAOvI,KAAK,CAACC,OAAO,CAACsI,GAAG,CAAC;AAC7B;AAEA,SAASC,cAAcA,CAACC,IAAI,EAAEC,cAAc,EAAEC,cAAc,GAAG,KAAK,EAAE;EAClE,IAAI,CAACA,cAAc,KACd,CAAC1I,OAAO,CAACwI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACG,IAAI,CAAEC,IAAI,IAAKC,OAAO,CAACD,IAAI,CAACxO,KAAK,CAAC,CAAC,CAAC,EAC7D,OAAOwF,SAAS;EACpB,MAAMkJ,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1B,KAAK,MAAMH,IAAI,IAAIJ,IAAI,EAAE;IACrB,MAAMpO,KAAK,GAAGwO,IAAI,EAAExO,KAAK,IAAIqO,cAAc;IAC3C,IAAI,OAAOrO,KAAK,KAAK,QAAQ,EAAE;MAC3B,MAAM,IAAI4O,KAAK,CAAC,kBAAkB5O,KAAK,EAAE,CAAC;IAC9C;IACA,MAAMf,KAAK,GAAGyP,QAAQ,CAACG,GAAG,CAAC7O,KAAK,CAAC,IAAI,EAAE;IACvCf,KAAK,CAAC6P,IAAI,CAACN,IAAI,CAAC;IAChBE,QAAQ,CAAC5I,GAAG,CAAC9F,KAAK,EAAEf,KAAK,CAAC;EAC9B;EACA,OAAOyP,QAAQ;AACnB;AACA,SAASK,iBAAiBA,CAACX,IAAI,EAAEY,IAAI,EAAE;EACnC,OAAOZ,IAAI,EAAER,MAAM,CAAC,CAACC,GAAG,EAAE;IAAE5O;EAAM,CAAC,KAAK,CAAC,GAAG4O,GAAG,EAAE,IAAImB,IAAI,GAAG/P,KAAK,CAACrG,MAAM,CAACoW,IAAI,CAAC,GAAG/P,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AACjG;AAEA,MAAMgQ,gBAAgB,GAAG,oBAAoB;AAE7C,MAAMC,aAAa,CAAC;EAChB5K,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG/N,MAAM,CAACyB,MAAM,CAAC;IAC5B,IAAI,CAACkX,SAAS,GAAG3Y,MAAM,CAAC+W,cAAc,CAAC;IACvC,IAAI,CAAClH,KAAK,GAAG,IAAI5B,SAAS,CAAC,IAAI,CAAC2K,iBAAiB,CAAC,IAAI,CAACD,SAAS,CAAC,CAAC;IAClE,IAAI,CAACE,YAAY,GAAG,IAAI,CAAChJ,KAAK,CAACtB,UAAU,CAAEL,KAAK,IAAKA,KAAK,CAAC;IAC3D,IAAI,CAAC4K,mBAAmB,GAAG,IAAI,CAACjJ,KAAK,CAChCtB,UAAU,CAAEL,KAAK,IAAKA,KAAK,CAAC,CAC5BU,IAAI,CAACxM,MAAM,CAAE2W,QAAQ,IAAKA,QAAQ,CAAChB,IAAI,CAAEiB,CAAC,IAAK,CAAC,CAACA,CAAC,CAACxP,KAAK,CAAC,CAAC,EAAEtH,GAAG,CAAEuG,KAAK,IAAK;MAC5E,MAAMvG,GAAG,GAAGyV,cAAc,CAAClP,KAAK,EAAEgQ,gBAAgB,CAAC,IAAI,EAAE;MACzD,OAAOtJ,KAAK,CAACtM,IAAI,CAACX,GAAG,EAAE,CAAC,CAACsH,KAAK,EAAEf,KAAK,CAAC,MAAM;QACxCe,KAAK;QACLf;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,IAAI,CAACwQ,iBAAiB,CAAC,CAAC,CAACrK,IAAI,CAACvM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC6W,SAAS,CAAC,CAAC;EACtD;EACAC,cAAcA,CAAC,GAAGR,SAAS,EAAE;IACzB,IAAI,CAAC9I,KAAK,CAACP,GAAG,CAAC,CAAC,GAAG,IAAI,CAACO,KAAK,CAAC3B,KAAK,EAAE,GAAG,IAAI,CAAC0K,iBAAiB,CAACD,SAAS,CAAC,CAAC,CAAC;EAC/E;EACAS,cAAcA,CAAC,GAAGT,SAAS,EAAE;IACzB,IAAI,CAAC9I,KAAK,CAACP,GAAG,CAAC,CAAC,GAAG,IAAI,CAACsJ,iBAAiB,CAACD,SAAS,CAAC,CAAC,CAAC;EAC1D;EACA;EACAU,WAAWA,CAACtJ,EAAE,EAAE,GAAG4I,SAAS,EAAE;IAC1B,MAAMW,MAAM,GAAG,IAAI,CAACC,QAAQ,CAACxJ,EAAE,EAAE,IAAI,CAACF,KAAK,CAAC3B,KAAK,CAAC;IAClD,MAAMkI,MAAM,GAAGA,CAAC3N,KAAK,EAAE+Q,QAAQ,EAAE3R,IAAI,GAAG,EAAE,KAAK;MAC3C,MAAM4R,CAAC,GAAGD,QAAQ,CAACE,KAAK,CAAC,CAAC;MAC1B,OAAOjR,KAAK,CAAC2O,MAAM,CAAC,CAACC,GAAG,EAAExT,IAAI,EAAEqS,KAAK,KAAK;QACtC,OAAO,CACH,GAAGmB,GAAG,EACN,IAAInB,KAAK,KAAKuD,CAAC,GACT,CACE;UACI,GAAG5V,IAAI;UACPC,QAAQ,EAAE,CAAC0V,QAAQ,CAAC5R,MAAM,GACpB,CACE,IAAI/D,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC,EACxB,GAAG,IAAI,CAAC8U,iBAAiB,CAACD,SAAS,EAAE,GAAG9Q,IAAI,IAAIhE,IAAI,CAAC8V,aAAa,EAAE,CAAC,CACxE,GACCvD,MAAM,CAACvS,IAAI,CAACC,QAAQ,IAAI,EAAE,EAAE0V,QAAQ,EAAE,GAAG3R,IAAI,IAAIhE,IAAI,CAAC8V,aAAa,EAAE;QAC/E,CAAC,CACJ,GACC,CAAC9V,IAAI,CAAC,CAAC,CAChB;MACL,CAAC,EAAE,EAAE,CAAC;IACV,CAAC;IACD,MAAM+V,OAAO,GAAGxD,MAAM,CAAC,IAAI,CAACvG,KAAK,CAAC3B,KAAK,EAAEoL,MAAM,CAACE,QAAQ,CAAC;IACzD,IAAI,CAAC3J,KAAK,CAACZ,KAAK,CAAC2K,OAAO,CAAC;EAC7B;EACAC,UAAUA,CAAChS,IAAI,EAAEY,KAAK,EAAE;IACpB,OAAO,IAAI,CAACqR,UAAU,CAAC,MAAM,EAAEjS,IAAI,EAAEY,KAAK,CAAC;EAC/C;EACAwQ,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAClL,MAAM,CAACgM,MAAM,CAACnL,IAAI,CAACxM,MAAM,CAAE4X,CAAC,IAAKA,CAAC,YAAYrY,aAAa,CAAC,EAAEW,GAAG,CAAC,MAAM,IAAI,CAAC2X,WAAW,CAAC,CAAC,CAAC,CAAC;EAC5G;EACAA,WAAWA,CAAA,EAAG;IACV,MAAMC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACjC,IAAID,KAAK,EAAErW,IAAI,EAAE;MACb,MAAMoC,QAAQ,GAAG,IAAI,CAACmU,oBAAoB,CAAC,IAAI,CAACvK,KAAK,CAAC3B,KAAK,EAAEgM,KAAK,CAACV,QAAQ,CAAC;MAC5E,IAAI,CAAC3J,KAAK,CAACZ,KAAK,CAAChJ,QAAQ,CAAC;IAC9B;EACJ;EACAkU,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACN,UAAU,CAAC,IAAI,CAAC9L,MAAM,CAACsM,GAAG,CAAC;EAC3C;EACAD,oBAAoBA,CAAC3R,KAAK,EAAE6R,OAAO,EAAE;IACjC,MAAMC,UAAU,GAAGD,OAAO,CAACZ,KAAK,CAAC,CAAC;IAClC,OAAOjR,KAAK,CAAC2O,MAAM,CAAC,CAACC,GAAG,EAAExT,IAAI,EAAEqS,KAAK,KAAK;MACtC,IAAIA,KAAK,KAAKqE,UAAU,EAAE;QACtB,OAAO,CACH,GAAGlD,GAAG,EACN;UACI,GAAGxT,IAAI;UACPoC,QAAQ,EAAE,IAAI;UACd0B,QAAQ,EAAE,IAAI;UACd7D,QAAQ,EAAE,IAAI,CAACsW,oBAAoB,CAACvW,IAAI,CAACC,QAAQ,IAAI,EAAE,EAAEwW,OAAO;QACpE,CAAC,CACJ;MACL;MACA,MAAME,OAAO,GAAG;QACZ,GAAG3W,IAAI;QACP,IAAIA,IAAI,CAACC,QAAQ,GACX;UAAEA,QAAQ,EAAE,IAAI,CAAC2W,gBAAgB,CAAC5W,IAAI,CAACC,QAAQ;QAAE,CAAC,GAClD,CAAC,CAAC;MACZ,CAAC;MACD,OAAO,CAAC,GAAGuT,GAAG,EAAE;QAAE,GAAGmD,OAAO;QAAEvU,QAAQ,EAAE,KAAK;QAAE0B,QAAQ,EAAE;MAAM,CAAC,CAAC;IACrE,CAAC,EAAE,EAAE,CAAC;EACV;EACA8S,gBAAgBA,CAAC3W,QAAQ,EAAE;IACvB,OAAO,CACH,GAAGA,QAAQ,CAAC5B,GAAG,CAAEwY,KAAK,KAAM;MACxB,GAAGA,KAAK;MACRzU,QAAQ,EAAE,KAAK;MACf0B,QAAQ,EAAE,KAAK;MACf7D,QAAQ,EAAE4W,KAAK,CAAC5W,QAAQ,GAAG,IAAI,CAAC2W,gBAAgB,CAACC,KAAK,CAAC5W,QAAQ,CAAC,GAAG;IACvE,CAAC,CAAC,CAAC,CACN;EACL;EACAyV,QAAQA,CAACxJ,EAAE,EAAEtH,KAAK,EAAE;IAChB,OAAO,IAAI,CAACqR,UAAU,CAAC,IAAI,EAAE/J,EAAE,EAAEtH,KAAK,CAAC;EAC3C;EACAqR,UAAUA,CAACa,IAAI,EAAEvM,KAAK,EAAE3F,KAAK,EAAE+Q,QAAQ,GAAG,EAAE,EAAE;IAC1C,MAAMpQ,WAAW,GAAGX,KAAK,IAAI,IAAI,CAACoH,KAAK,CAAC3B,KAAK;IAC7C,MAAM0M,SAAS,GAAGxR,WAAW,CAAC+M,SAAS,CAAEsD,CAAC,IAAKA,CAAC,CAACkB,IAAI,CAAC,KAAKvM,KAAK,CAAC;IACjE,IAAIvK,IAAI;IACR,IAAI+W,SAAS,KAAK,CAAC,CAAC,EAAE;MAClBxR,WAAW,CAACyR,OAAO,CAAC,CAACpB,CAAC,EAAEvD,KAAK,KAAK;QAC9B,IAAIuD,CAAC,CAAC3V,QAAQ,EAAE;UACZ,MAAM4W,KAAK,GAAG,IAAI,CAACZ,UAAU,CAACa,IAAI,EAAEvM,KAAK,EAAEqL,CAAC,CAAC3V,QAAQ,EAAE,CACnD,GAAG0V,QAAQ,EACXtD,KAAK,CACR,CAAC;UACF,IAAIwE,KAAK,EAAE7W,IAAI,EAAE;YACbA,IAAI,GAAG6W,KAAK,CAAC7W,IAAI;YACjB2V,QAAQ,GAAGkB,KAAK,CAAClB,QAAQ;UAC7B;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD3V,IAAI,GAAGuF,WAAW,CAACwR,SAAS,CAAC;MAC7BpB,QAAQ,CAAClB,IAAI,CAACsC,SAAS,CAAC;IAC5B;IACA,OAAO;MAAE/W,IAAI;MAAE2V;IAAS,CAAC;EAC7B;EACAZ,iBAAiBA,CAACnQ,KAAK,EAAEZ,IAAI,GAAG,EAAE,EAAE;IAChC,OAAOY,KAAK,CAACvG,GAAG,CAAE2B,IAAI,KAAM;MACxB,GAAGA,IAAI;MACP,IAAIA,IAAI,CAACgE,IAAI,IAAIA,IAAI,GAAG;QAAEA,IAAI,EAAE,GAAGA,IAAI,IAAIhE,IAAI,CAACgE,IAAI;MAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9D/D,QAAQ,EAAE,IAAI,CAAC8U,iBAAiB,CAAC/U,IAAI,CAACC,QAAQ,IAAI,EAAE,EAAE,GAAG+D,IAAI,GAAGA,IAAI,GAAG,GAAG,GAAG,EAAE,GAAGhE,IAAI,CAAC8V,aAAa,IAAI,EAAE,EAAE;IAChH,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC3I,IAAI,YAAA8J,sBAAA5J,CAAA;MAAA,YAAAA,CAAA,IAAwFwH,aAAa;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACtH,KAAK,kBA/X6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA+XYoH,aAAa;MAAAnH,OAAA,EAAbmH,aAAa,CAAA1H,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjYoG1R,EAAE,CAAA2R,iBAAA,CAiYXgH,aAAa,EAAc,CAAC;IAC3G3L,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMuJ,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAC/J,IAAI,YAAAgK,8BAAA9J,CAAA;MAAA,YAAAA,CAAA,IAAwF6J,qBAAqB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACE,IAAI,kBA1Y8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA0YJgO,qBAAqB;MAAAxG,SAAA;MAAA4G,QAAA;IAAA,EAAmG;EAAE;AAC5N;AACA;EAAA,QAAA1J,SAAA,oBAAAA,SAAA,KA5YoG1R,EAAE,CAAA2R,iBAAA,CA4YXqJ,qBAAqB,EAAc,CAAC;IACnHhO,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE,uCAAuC;MACjD2M,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,kBAAkB,CAAC;EACrBtN,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAqK,2BAAAnK,CAAA;MAAA,YAAAA,CAAA,IAAwFkK,kBAAkB,EAxZ5Brb,EAAE,CAAAqU,iBAAA,CAwZ4CrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC1M;IAAS,IAAI,CAACga,IAAI,kBAzZ8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAyZJqO,kBAAkB;MAAA7G,SAAA;IAAA,EAA0D;EAAE;AAChL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA3ZoG1R,EAAE,CAAA2R,iBAAA,CA2ZX0J,kBAAkB,EAAc,CAAC;IAChHrO,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMqa,aAAa,CAAC;EAChBxN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG/N,MAAM,CAACyB,MAAM,CAAC;IAC5B,IAAI,CAAC8Z,iBAAiB,GAAGjY,QAAQ,CAAC,IAAI,CAACyK,MAAM,CAACgM,MAAM,CAACnL,IAAI,CAACxM,MAAM,CAAE4X,CAAC,IAAKA,CAAC,YAAYrY,aAAa,CAAC;IACnG;IACAO,GAAG,CAAC,MAAMsX,QAAQ,CAACgC,QAAQ,CAAC,CAAC,EAAE;MAAEC,YAAY,EAAEjC,QAAQ,CAACgC;IAAS,CAAC,CAAC;EACvE;EACA;IAAS,IAAI,CAACxK,IAAI,YAAA0K,sBAAAxK,CAAA;MAAA,YAAAA,CAAA,IAAwFoK,aAAa;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAAClK,KAAK,kBA1a6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA0aYgK,aAAa;MAAA/J,OAAA,EAAb+J,aAAa,CAAAtK,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5aoG1R,EAAE,CAAA2R,iBAAA,CA4aX4J,aAAa,EAAc,CAAC;IAC3GvO,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMmK,mBAAmB,CAAC;EACtB,IAAIC,UAAUA,CAACxN,KAAK,EAAE;IAClB,IAAI,CAACyN,UAAU,GAAGC,SAAS,CAAC1N,KAAK,CAAC;IAClC,IAAI,CAAC2N,oBAAoB,CAAC,CAAC;EAC/B;EACAjO,WAAWA,CAACkO,gBAAgB,EAAEC,WAAW,EAAE;IACvC,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACJ,UAAU,GAAG/Y,EAAE,CAAC,KAAK,CAAC;EAC/B;EACAoZ,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,qBAAqB,EAAEC,WAAW,CAAC,CAAC;EAC7C;EACAP,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACM,qBAAqB,GAAG,IAAI,CAACR,UAAU,CAAC3C,SAAS,CAAE9K,KAAK,IAAK;MAC9D,IAAI,CAACmO,SAAS,GAAGnO,KAAK;MACtB,IAAI,CAAC+N,gBAAgB,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAA,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACH,gBAAgB,CAACQ,KAAK,CAAC,CAAC;IAC7B;IACA,IAAI,IAAI,CAACD,SAAS,KAAK,KAAK,EAAE;MAC1B;IACJ;IACA,IAAI,CAACP,gBAAgB,CAACS,kBAAkB,CAAC,IAAI,CAACR,WAAW,CAAC;EAC9D;EACA;IAAS,IAAI,CAACjL,IAAI,YAAA0L,4BAAAxL,CAAA;MAAA,YAAAA,CAAA,IAAwFyK,mBAAmB,EAjd7B5b,EAAE,CAAAqU,iBAAA,CAid6CrU,EAAE,CAAC4c,gBAAgB,GAjdlE5c,EAAE,CAAAqU,iBAAA,CAid6ErU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC3O;IAAS,IAAI,CAACga,IAAI,kBAld8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAkdJ4O,mBAAmB;MAAApH,SAAA;MAAAC,MAAA;QAAAoH,UAAA;MAAA;MAAAgB,UAAA;IAAA,EAAqG;EAAE;AAC5N;AACA;EAAA,QAAAnL,SAAA,oBAAAA,SAAA,KApdoG1R,EAAE,CAAA2R,iBAAA,CAodXiK,mBAAmB,EAAc,CAAC;IACjH5O,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE,cAAc;MACxBoO,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7P,IAAI,EAAEhN,EAAE,CAAC4c;EAAiB,CAAC,EAAE;IAAE5P,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC,EAAkB;IAAE2a,UAAU,EAAE,CAAC;MAC5G7O,IAAI,EAAExM,KAAK;MACXoR,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,SAASmK,SAASA,CAAC1N,KAAK,EAAE;EACtB,IAAIA,KAAK,YAAYoJ,OAAO,EAAE;IAC1B,OAAO3U,IAAI,CAACuL,KAAK,CAAC;EACtB,CAAC,MACI,IAAIA,KAAK,YAAYxL,UAAU,EAAE;IAClC,OAAOwL,KAAK;EAChB,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;IACjC,OAAOtL,EAAE,CAACsL,KAAK,CAAC;EACpB,CAAC,MACI,IAAIA,KAAK,KAAKY,SAAS,IAAIZ,KAAK,KAAK,IAAI,EAAE;IAC5C,OAAOtL,EAAE,CAAC,IAAI,CAAC;EACnB,CAAC,MACI;IACD,OAAOC,KAAK;EAChB;AACJ;AAEA,MAAM8Z,kBAAkB,CAAC;EACrB/O,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+B,KAAK,GAAG,IAAI5B,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC6O,KAAK,GAAG,IAAI,CAACjN,KAAK,CAACtB,UAAU,CAAEL,KAAK,IAAKA,KAAK,CAAC;EACxD;EACA6O,OAAOA,CAACC,IAAI,EAAE;IACV,IAAI,CAACnN,KAAK,CAACP,GAAG,CAAC0N,IAAI,CAAC;EACxB;EACAC,SAASA,CAACD,IAAI,EAAE;IACZ,IAAI,CAACnN,KAAK,CAACZ,KAAK,CAAC+N,IAAI,CAAC;EAC1B;EACA;IAAS,IAAI,CAAChM,IAAI,YAAAkM,2BAAAhM,CAAA;MAAA,YAAAA,CAAA,IAAwF2L,kBAAkB;IAAA,CAAoD;EAAE;EAClL;IAAS,IAAI,CAACzL,KAAK,kBA5f6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA4fYuL,kBAAkB;MAAAtL,OAAA,EAAlBsL,kBAAkB,CAAA7L,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC7J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9foG1R,EAAE,CAAA2R,iBAAA,CA8fXmL,kBAAkB,EAAc,CAAC;IAChH9P,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM2L,WAAW,CAAC;EACdrP,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsP,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACC,OAAO,GAAG;MACXC,eAAe,EAAE;IACrB,CAAC;EACL;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAACL,IAAI,EAAEM,SAAS,CAACC,GAAG,CAAC,IAAI,CAACJ,OAAO,CAACC,eAAe,CAAC;EAC1D;EACAI,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACR,IAAI,EAAEM,SAAS,CAACG,MAAM,CAAC,IAAI,CAACN,OAAO,CAACC,eAAe,CAAC;EAC7D;EACA;IAAS,IAAI,CAACxM,IAAI,YAAA8M,oBAAA5M,CAAA;MAAA,YAAAA,CAAA,IAAwFiM,WAAW;IAAA,CAAoD;EAAE;EAC3K;IAAS,IAAI,CAAC/L,KAAK,kBAnhB6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAmhBY6L,WAAW;MAAA5L,OAAA,EAAX4L,WAAW,CAAAnM,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACtJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArhBoG1R,EAAE,CAAA2R,iBAAA,CAqhBXyL,WAAW,EAAc,CAAC;IACzGpQ,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMuM,uBAAuB,CAAC;EAC1BC,IAAIA,CAACC,GAAG,EAAEC,YAAY,EAAE;IACpB,OAAOpb,EAAE,CAACob,YAAY,IAAID,GAAG,IAAI,EAAE,CAAC;EACxC;EACA5F,GAAGA,CAAC4F,GAAG,EAAEC,YAAY,EAAE;IACnB,OAAOA,YAAY,IAAID,GAAG,IAAI,EAAE;EACpC;EACA;IAAS,IAAI,CAACjN,IAAI,YAAAmN,gCAAAjN,CAAA;MAAA,YAAAA,CAAA,IAAwF6M,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAAC3M,KAAK,kBApiB6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAoiBYyM,uBAAuB;MAAAxM,OAAA,EAAvBwM,uBAAuB,CAAA/M;IAAA,EAAG;EAAE;AAC9I;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAtiBoG1R,EAAE,CAAA2R,iBAAA,CAsiBXqM,uBAAuB,EAAc,CAAC;IACrHhR,IAAI,EAAE7M;EACV,CAAC,CAAC;AAAA;AAEV,MAAMke,wBAAwB,CAAC;EAC3BtQ,WAAWA,CAACuQ,eAAe,EAAEC,gBAAgB,EAAE;IAC3C,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGrH,qBAAqB,CAAC,IAAI,CAACmH,eAAe,CAAC;EAC/D;EACA;EACAG,UAAUA,CAACP,GAAG,EAAE,GAAGtM,IAAI,EAAE;IACrB,OAAO,IAAI,CAAC2M,gBAAgB,CAACN,IAAI,CAACC,GAAG,EAAE,IAAI,CAACM,QAAQ,CAACN,GAAG,CAAC,CAAC;EAC9D;EACA;IAAS,IAAI,CAACjN,IAAI,YAAAyN,iCAAAvN,CAAA;MAAA,YAAAA,CAAA,IAAwFkN,wBAAwB,EApjBlCre,EAAE,CAAAoR,QAAA,CAojBkDW,mBAAmB,MApjBvE/R,EAAE,CAAAoR,QAAA,CAojBkGU,2BAA2B;IAAA,CAA6C;EAAE;EAC9Q;IAAS,IAAI,CAACT,KAAK,kBArjB6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAqjBY8M,wBAAwB;MAAA7M,OAAA,EAAxB6M,wBAAwB,CAAApN,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvjBoG1R,EAAE,CAAA2R,iBAAA,CAujBX0M,wBAAwB,EAAc,CAAC;IACtHrR,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzE,IAAI,EAAEoC,KAAK;IAAEyC,UAAU,EAAE,CAAC;MAC3C7E,IAAI,EAAEtM;IACV,CAAC,EAAE;MACCsM,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAACG,mBAAmB;IAC9B,CAAC;EAAE,CAAC,EAAE;IAAE/E,IAAI,EAAEiC,SAAS;IAAE4C,UAAU,EAAE,CAAC;MAClC7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAACE,2BAA2B;IACtC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAM6M,kBAAkB,CAAC;EACrB5Q,WAAWA,CAAC6Q,kBAAkB,EAAE;IAC5B,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAAC7B,KAAK,CAAClO,IAAI,CAAC1M,GAAG,CAAE8a,IAAI,IAAK,CAAC,CAACA,IAAI,IAAI6B,MAAM,CAACC,IAAI,CAAC9B,IAAI,CAAC,CAACpV,MAAM,GAAG,CAAC,CAAC,CAAC;EAClH;EACAmX,eAAeA,CAAA,EAAG;IACd;EACJ;EACA;IAAS,IAAI,CAAC/N,IAAI,YAAAgO,2BAAA9N,CAAA;MAAA,YAAAA,CAAA,IAAwFwN,kBAAkB,EA9kB5B3e,EAAE,CAAAoR,QAAA,CA8kB4C0L,kBAAkB;IAAA,CAA6C;EAAE;EAC/M;IAAS,IAAI,CAACzL,KAAK,kBA/kB6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA+kBYoN,kBAAkB;MAAAnN,OAAA,EAAlBmN,kBAAkB,CAAA1N;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAjlBoG1R,EAAE,CAAA2R,iBAAA,CAilBXgN,kBAAkB,EAAc,CAAC;IAChH3R,IAAI,EAAE7M;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6M,IAAI,EAAE8P;EAAmB,CAAC,CAAC;AAAA;AAEhE,MAAMoC,sBAAsB,GAAG,IAAIhf,cAAc,CAAC,wBAAwB,CAAC;AAE3E,MAAMif,yBAAyB,GAAG;EAC9B9M,OAAO,EAAE6M,sBAAsB;EAC/BE,QAAQ,EAAET;AACd,CAAC;AAED,MAAMU,sBAAsB,CAAC;EACzBtR,WAAWA,CAAA,EAAG,CAAE;EAChB,IAAIlG,MAAMA,CAAA,EAAG;IACT,OAAOyX,YAAY,CAACzX,MAAM;EAC9B;EACA4U,KAAKA,CAAA,EAAG;IACJ6C,YAAY,CAAC7C,KAAK,CAAC,CAAC;EACxB;EACA8C,OAAOA,CAACrB,GAAG,EAAE;IACT,OAAOoB,YAAY,CAACC,OAAO,CAACrB,GAAG,CAAC;EACpC;EACAA,GAAGA,CAAC/H,KAAK,EAAE;IACP,OAAOmJ,YAAY,CAACpB,GAAG,CAAC/H,KAAK,CAAC;EAClC;EACAqJ,UAAUA,CAACtB,GAAG,EAAE;IACZoB,YAAY,CAACE,UAAU,CAACtB,GAAG,CAAC;EAChC;EACAuB,OAAOA,CAACvB,GAAG,EAAE7P,KAAK,EAAE;IAChBiR,YAAY,CAACG,OAAO,CAACvB,GAAG,EAAE7P,KAAK,CAAC;EACpC;EACA;IAAS,IAAI,CAAC4C,IAAI,YAAAyO,+BAAAvO,CAAA;MAAA,YAAAA,CAAA,IAAwFkO,sBAAsB;IAAA,CAAoD;EAAE;EACtL;IAAS,IAAI,CAAChO,KAAK,kBAjnB6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAinBY8N,sBAAsB;MAAA7N,OAAA,EAAtB6N,sBAAsB,CAAApO,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACjK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnnBoG1R,EAAE,CAAA2R,iBAAA,CAmnBX0N,sBAAsB,EAAc,CAAC;IACpHrS,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMkO,0BAA0B,SAAS7R,mBAAmB,CAAC;EACzD8R,UAAUA,CAACvR,KAAK,EAAE;IACd,IAAIA,KAAK,EAAE;MACP,IAAI,CAACwR,UAAU,GAAGxR,KAAK;IAC3B;EACJ;EACAyR,UAAUA,CAACzR,KAAK,EAAE;IACd,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC6D,OAAO,GAAG7D,KAAK;IACxB;EACJ;EACA0R,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,gBAAgB,GAAG,IAAIxc,gBAAgB,CAAC,IAAI,CAACqc,UAAU,CAACI,aAAa,EAAE,IAAI,CAAC/N,OAAO,CAAC;EAC7F;EACAgO,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,gBAAgB,CAAC3J,MAAM,CAAC,CAAC;EAClC;EACA8J,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACJ,eAAe,CAAC,CAAC;IACtB,IAAI,CAACK,YAAY,EAAE7D,WAAW,CAAC,CAAC;IAChC,IAAI,CAAC6D,YAAY,GAAG,IAAI,CAACpS,MAAM,CAACgM,MAAM,CACjCnL,IAAI,CAAC5L,QAAQ,CAAEod,KAAK,IAAKA,KAAK,YAAYze,aAAa,IACxDye,KAAK,YAAYxe,eAAe,IAChCwe,KAAK,YAAYve,gBAAgB,CAAC,CAAC,CAClCqX,SAAS,CAAC,MAAM;MACjB,MAAM;QAAEmH;MAAQ,CAAC,GAAG,IAAI,CAACN,gBAAgB;MACzC,MAAM;QAAEO,gBAAgB;QAAEC;MAAkB,CAAC,GAAG,IAAI,CAACtO,OAAO,IAAI,CAAC,CAAC;MAClEoO,OAAO,CAACG,SAAS,GAAGF,gBAAgB,IAAI,CAAC;MACzCD,OAAO,CAACI,UAAU,GAAGF,iBAAiB,IAAI,CAAC;IAC/C,CAAC,CAAC;EACN;EACAnE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+D,YAAY,EAAE7D,WAAW,CAAC,CAAC;EACpC;EACA;IAAS,IAAI,CAACtL,IAAI;MAAA,IAAA0P,uCAAA;MAAA,gBAAAC,mCAAAzP,CAAA;QAAA,QAAAwP,uCAAA,KAAAA,uCAAA,GA5pB8E3gB,EAAE,CAAA6gB,qBAAA,CA4pBQlB,0BAA0B,IAAAxO,CAAA,IAA1BwO,0BAA0B;MAAA;IAAA,IAAsD;EAAE;EAC5L;IAAS,IAAI,CAACtO,KAAK,kBA7pB6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA6pBYoO,0BAA0B;MAAAnO,OAAA,EAA1BmO,0BAA0B,CAAA1O;IAAA,EAAG;EAAE;AACjJ;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA/pBoG1R,EAAE,CAAA2R,iBAAA,CA+pBXgO,0BAA0B,EAAc,CAAC;IACxH3S,IAAI,EAAE7M;EACV,CAAC,CAAC;AAAA;AAEV,MAAM2gB,aAAa,CAAC;EAChB/S,WAAWA,CAACgT,wBAAwB,EAAE;IAClC,IAAI,CAACA,wBAAwB,GAAGA,wBAAwB;EAC5D;EACAC,SAASA,CAAC3S,KAAK,EAAE,GAAGuD,IAAI,EAAE;IACtB,OAAO,IAAI,CAACmP,wBAAwB,CAACtC,UAAU,CAACpQ,KAAK,EAAEuD,IAAI,CAAC;EAChE;EACA;IAAS,IAAI,CAACX,IAAI,YAAAgQ,sBAAA9P,CAAA;MAAA,YAAAA,CAAA,IAAwF2P,aAAa,EA1qBvB9gB,EAAE,CAAAqU,iBAAA,CA0qBuCgK,wBAAwB;IAAA,CAAuC;EAAE;EAC1M;IAAS,IAAI,CAAC6C,KAAK,kBA3qB6ElhB,EAAE,CAAAmhB,YAAA;MAAAC,IAAA;MAAApU,IAAA,EA2qBM8T,aAAa;MAAAO,IAAA;IAAA,EAAyB;EAAE;AACpJ;AACA;EAAA,QAAA3P,SAAA,oBAAAA,SAAA,KA7qBoG1R,EAAE,CAAA2R,iBAAA,CA6qBXmP,aAAa,EAAc,CAAC;IAC3G9T,IAAI,EAAErM,IAAI;IACViR,IAAI,EAAE,CAAC;MACCwP,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpU,IAAI,EAAEqR;EAAyB,CAAC,CAAC;AAAA;AAEtE,MAAMiD,kBAAkB,CAAC;EACrBvT,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClJ,QAAQ,GAAG5E,MAAM,CAACW,QAAQ,CAAC;IAChC,IAAI,CAACsJ,UAAU,GAAGrJ,KAAK,CAAC,CAAC;IACzB,IAAI,CAAC8F,UAAU,GAAG,IAAI7F,YAAY,CAAC,CAAC;IACpC,IAAI,CAACygB,MAAM,GAAG,IAAIzgB,YAAY,CAAC,CAAC;EACpC;EACA2G,WAAWA,CAAC+Z,QAAQ,EAAE;IAClB,IAAIC,OAAO,GAAG1e,EAAE,CAAC,IAAI,CAAC;IACtB,IAAIye,QAAQ,CAACpR,MAAM,EAAE;MACjB,MAAMsR,MAAM,GAAGF,QAAQ,CAACpR,MAAM,CAAC,CAAC;MAChCqR,OAAO,GAAGjK,UAAU,CAACkK,MAAM,CAAC;IAChC;IACAD,OAAO,CAAC5S,IAAI,CAACvM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC6W,SAAS,CAAEuI,MAAM,IAAK;MACxC,IAAIA,MAAM,EAAE;QACR,IAAI,CAACC,gBAAgB,CAACH,QAAQ,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACA1a,aAAaA,CAAC6T,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACzU,QAAQ,EAAE;MAChB,IAAI,CAACpC,IAAI,EAAEC,QAAQ,EACb1B,MAAM,CAAEuf,UAAU,IAAKA,UAAU,KAAKjH,KAAK,CAAC,CAC7CG,OAAO,CAAE8G,UAAU,IAAK;QACzBA,UAAU,CAAC1b,QAAQ,GAAG,KAAK;QAC3B0b,UAAU,CAACha,QAAQ,GAAG,KAAK;MAC/B,CAAC,CAAC;IACN;EACJ;EACA+Z,gBAAgBA,CAACH,QAAQ,EAAE;IACvB,IAAIA,QAAQ,CAACzd,QAAQ,EAAE8D,MAAM,EAAE;MAC3B2Z,QAAQ,CAACtb,QAAQ,GAAG,CAACsb,QAAQ,CAACtb,QAAQ;MACtC,IAAI,CAACqb,MAAM,CAAC3a,IAAI,CAAC4a,QAAQ,CAAC;MAC1B;IACJ;IACA,IAAI,CAAC7a,UAAU,CAACC,IAAI,CAAC4a,QAAQ,CAAC;IAC9B,IAAI,CAAC,IAAI,CAACtX,UAAU,CAAC,CAAC,EAAE;MACpBsX,QAAQ,CAAC5Z,QAAQ,GAAG,IAAI;IAC5B;EACJ;EACA;IAAS,IAAI,CAACqJ,IAAI,YAAA4Q,2BAAA1Q,CAAA;MAAA,YAAAA,CAAA,IAAwFmQ,kBAAkB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAChN,IAAI,kBA7tB8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EA6tBJsU,kBAAkB;MAAA9M,SAAA;MAAAC,MAAA;QAAA3Q,IAAA;QAAAoG,UAAA,GA7tBhBlK,EAAE,CAAA8hB,YAAA,CAAAC,WAAA;MAAA;MAAAC,OAAA;QAAArb,UAAA;QAAA4a,MAAA;MAAA;MAAA7M,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAAka,4BAAA7d,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpE,EAAE,CAAAwE,UAAA,IAAAD,yCAAA,sBA6tB4Z,CAAC,IAAAQ,yCAAA,MAAsH,CAAC,IAAAuC,yCAAA,iCA7tBthBtH,EAAE,CAAA0H,sBA6tBmoB,CAAC;QAAA;QAAA,IAAAtD,EAAA;UA7tBtoBpE,EAAE,CAAAgG,aAAA,IAAA3B,GAAA,CAAAP,IAAA,CAAAc,SAAA,QA6tBimB,CAAC;QAAA;MAAA;MAAAkQ,YAAA,GAAgsD/S,EAAE,CAACmgB,iBAAiB,EAAoPngB,EAAE,CAACogB,IAAI,EAA6FpgB,EAAE,CAACqgB,gBAAgB,EAAoJ3gB,EAAE,CAAC+T,UAAU,EAAoOxB,aAAa,EAA4E4H,mBAAmB,EAAiF0F,kBAAkB,EAAyHvf,EAAE,CAACsgB,SAAS,EAAyCvB,aAAa;MAAA9L,aAAA;IAAA,EAAsE;EAAE;AAC9lH;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KA/tBoG1R,EAAE,CAAA2R,iBAAA,CA+tBX2P,kBAAkB,EAAc,CAAC;IAChHtU,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,gBAAgB;MAAEuG,aAAa,EAAEzU,iBAAiB,CAAC0U,IAAI;MAAElN,QAAQ,EAAE;IAAi3D,CAAC;EAC58D,CAAC,CAAC,QAAkB;IAAEjE,IAAI,EAAE,CAAC;MACrBkJ,IAAI,EAAExM;IACV,CAAC,CAAC;IAAEmG,UAAU,EAAE,CAAC;MACbqG,IAAI,EAAEjM;IACV,CAAC,CAAC;IAAEwgB,MAAM,EAAE,CAAC;MACTvU,IAAI,EAAEjM;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuhB,qBAAqB,CAAC;EACxB,IAAIC,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC1Z,YAAY,EAAE;MACpB,OAAOoG,SAAS;IACpB;IACA,OAAOuJ,iBAAiB,CAAC,IAAI,CAAC3P,YAAY,CAAC;EAC/C;EACAkF,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClJ,QAAQ,GAAG5E,MAAM,CAACW,QAAQ,CAAC;IAChC,IAAI,CAAC4hB,aAAa,GAAGviB,MAAM,CAACsb,aAAa,CAAC;IAC1C,IAAI,CAACrR,UAAU,GAAGrJ,KAAK,CAAC,CAAC;IACzB,IAAI,CAAC8F,UAAU,GAAG,IAAI7F,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC2hB,oBAAoB,GAAI3e,IAAI,IAAK,CAAC,EAAEA,IAAI,CAACoC,QAAQ,IAAIpC,IAAI,CAAC8D,QAAQ,CAAC;IACxE,IAAI,CAAC8a,sBAAsB,CAAC,CAAC;EACjC;EACA5Y,iBAAiBA,CAAC0X,QAAQ,EAAE5I,SAAS,EAAE;IACnC,IAAI4I,QAAQ,CAACtb,QAAQ,EAAE;MACnB,MAAMwC,KAAK,GAAG,IAAI,CAAC6Z,cAAc,IAAI3J,SAAS;MAC9C,IAAI,CAAClQ,KAAK,EAAE;QACR;MACJ;MACAA,KAAK,CACArG,MAAM,CAAEyB,IAAI,IAAKA,IAAI,KAAK0d,QAAQ,CAAC,CACnC1G,OAAO,CAAEhX,IAAI,IAAMA,IAAI,CAACoC,QAAQ,GAAG,KAAM,CAAC;IACnD;EACJ;EACA8D,YAAYA,CAACwX,QAAQ,EAAE5I,SAAS,EAAE;IAC9B,MAAM+J,aAAa,GAAG/J,SAAS,EAAEvW,MAAM,CAAC,IAAI,CAACogB,oBAAoB,CAAC;IAClE,MAAMG,kBAAkB,GAAG,IAAI,CAACL,cAAc,EAAElgB,MAAM,CAAC,IAAI,CAACogB,oBAAoB,CAAC;IACjF,MAAM/Z,KAAK,GAAGka,kBAAkB,IAAID,aAAa;IACjD,IAAIja,KAAK,EAAE;MACPA,KAAK,CACArG,MAAM,CAAEyB,IAAI,IAAKA,IAAI,KAAK0d,QAAQ,CAAC,CACnCnK,MAAM,CAAC,CAACC,GAAG,EAAExT,IAAI,KAAK;QACvB,OAAO,CAAC,GAAGwT,GAAG,EAAExT,IAAI,EAAE,GAAG,IAAI,CAAC+e,YAAY,CAAC/e,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC,CAAC;MACpE,CAAC,EAAE,EAAE,CAAC,EACA1B,MAAM,CAAEyB,IAAI,IAAK,CAAC,IAAI,CAACgf,yBAAyB,CAAChf,IAAI,EAAE0d,QAAQ,CAAC,IAClE1d,IAAI,KAAK0d,QAAQ,CAAC,CACjB1G,OAAO,CAAEhX,IAAI,IAAK;QACnBA,IAAI,CAAC8D,QAAQ,GAAG,KAAK;QACrB9D,IAAI,CAACoC,QAAQ,GAAG,KAAK;MACzB,CAAC,CAAC;IACN;IACA,IAAI,CAACS,UAAU,CAACC,IAAI,CAAC4a,QAAQ,CAAC;EAClC;EACAsB,yBAAyBA,CAAChf,IAAI,EAAE0d,QAAQ,EAAE;IACtC,OAAQ1d,IAAI,CAACC,QAAQ,EAAEsT,MAAM,CAAC,CAACC,GAAG,EAAEqD,KAAK,KAAKrD,GAAG,IAC7CqD,KAAK,KAAK6G,QAAQ,IAClB,IAAI,CAACsB,yBAAyB,CAACnI,KAAK,EAAE6G,QAAQ,CAAC,EAAE,KAAK,CAAC,IAAI,KAAK;EACxE;EACAqB,YAAYA,CAACjK,SAAS,EAAE;IACpB,OAAQA,SAAS,EAAEvB,MAAM,CAAC,CAACC,GAAG,EAAExT,IAAI,KAAK;MACrC,OAAO,CAAC,GAAGwT,GAAG,EAAExT,IAAI,EAAE,GAAG,IAAI,CAAC+e,YAAY,CAAC/e,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC,CAAC;IACpE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE;EAChB;EACA2e,sBAAsBA,CAAA,EAAG;IACrB1hB,MAAM,CAAC,MAAM;MACT,MAAMwa,iBAAiB,GAAG,IAAI,CAACgH,aAAa,CAAChH,iBAAiB,CAAC,CAAC;MAChE,IAAI,CAACA,iBAAiB,EAAE;QACpB;MACJ;MACA,IAAI,CAACuH,cAAc,CAACvH,iBAAiB,EAAE,IAAI,CAACnS,WAAW,CAAC;IAC5D,CAAC,CAAC;EACN;EACA0Z,cAAcA,CAACC,UAAU,EAAEta,KAAK,EAAE;IAC9B,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,KAAK,MAAM5E,IAAI,IAAI4E,KAAK,EAAE;MACtB,IAAI5E,IAAI,CAACC,QAAQ,EAAE8D,MAAM,EAAE;QACvB/D,IAAI,CAACoC,QAAQ,GAAG,IAAI,CAAC+c,gBAAgB,CAACnf,IAAI,EAAEkf,UAAU,CAAC;QACvD,IAAI,CAACD,cAAc,CAACC,UAAU,EAAElf,IAAI,CAACC,QAAQ,CAAC;MAClD,CAAC,MACI;QACDD,IAAI,CAAC8D,QAAQ,GAAG9D,IAAI,CAACgE,IAAI,KAAKkb,UAAU;MAC5C;IACJ;EACJ;EACAC,gBAAgBA,CAACnf,IAAI,EAAEwW,GAAG,EAAE;IACxB,IAAIxW,IAAI,CAACgE,IAAI,KAAKwS,GAAG,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAIxW,IAAI,CAACC,QAAQ,EAAE;MACf,KAAK,MAAM4W,KAAK,IAAI7W,IAAI,CAACC,QAAQ,EAAE;QAC/B,MAAMmf,KAAK,GAAG,IAAI,CAACD,gBAAgB,CAACtI,KAAK,EAAEL,GAAG,CAAC;QAC/C,IAAI4I,KAAK,EAAE;UACP,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAACjS,IAAI,YAAAkS,8BAAAhS,CAAA;MAAA,YAAAA,CAAA,IAAwFmR,qBAAqB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAAChO,IAAI,kBAv0B8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EAu0BJsV,qBAAqB;MAAA9N,SAAA;MAAAC,MAAA;QAAA5L,YAAA;QAAAQ,WAAA;QAAAa,UAAA,GAv0BnBlK,EAAE,CAAA8hB,YAAA,CAAAC,WAAA;MAAA;MAAAC,OAAA;QAAArb,UAAA;MAAA;MAAA+N,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAAqb,+BAAAhf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpE,EAAE,CAAAuF,cAAA,WAu0BwjB,CAAC;UAv0B3jBvF,EAAE,CAAAwE,UAAA,IAAAmE,4CAAA,MAu0BklB,CAAC,IAAAI,4CAAA,MAAuZ,CAAC;UAv0B7+B/I,EAAE,CAAA0F,YAAA,CAu0BkkC,CAAC;UAv0BrkC1F,EAAE,CAAAwE,UAAA,IAAA4E,4CAAA,gCAAFpJ,EAAE,CAAA0H,sBAu0BqmC,CAAC,IAAAgC,4CAAA,gCAv0BxmC1J,EAAE,CAAA0H,sBAu0B40C,CAAC,IAAAyC,4CAAA,gCAv0B/0CnK,EAAE,CAAA0H,sBAu0B4iD,CAAC;QAAA;QAAA,IAAAtD,EAAA;UAv0B/iDpE,EAAE,CAAA2F,SAAA,CAu0ByjC,CAAC;UAv0B5jC3F,EAAE,CAAAgG,aAAA,IAAA3B,GAAA,CAAAwE,YAAA,QAu0ByjC,CAAC;QAAA;MAAA;MAAAiM,YAAA,GAAi6B/S,EAAE,CAACqgB,gBAAgB,EAAoJxG,mBAAmB,EAAiF0F,kBAAkB,EAAyHvf,EAAE,CAACsgB,SAAS,EAAyCvB,aAAa;MAAA9L,aAAA;IAAA,EAAsE;EAAE;AACjmF;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAz0BoG1R,EAAE,CAAA2R,iBAAA,CAy0BX2Q,qBAAqB,EAAc,CAAC;IACnHtV,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,mBAAmB;MAAEuG,aAAa,EAAEzU,iBAAiB,CAAC0U,IAAI;MAAElN,QAAQ,EAAE;IAAm5C,CAAC;EACj/C,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEc,YAAY,EAAE,CAAC;MACvDmE,IAAI,EAAExM;IACV,CAAC,CAAC;IAAE6I,WAAW,EAAE,CAAC;MACd2D,IAAI,EAAExM;IACV,CAAC,CAAC;IAAEmG,UAAU,EAAE,CAAC;MACbqG,IAAI,EAAEjM;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsiB,eAAe,CAAC;EAClBtV,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuV,aAAa,GAAGrjB,MAAM,CAAC0V,aAAa,CAAC;IAC1C,IAAI,CAAC4N,UAAU,GAAGtjB,MAAM,CAACgB,WAAW,CAAC;IACrC,IAAI,CAACuiB,OAAO,GAAGvjB,MAAM,CAAC0Y,aAAa,CAAC;IACpC,IAAI,CAAC9T,QAAQ,GAAG5E,MAAM,CAACW,QAAQ,CAAC;IAChC,IAAI,CAAC6iB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,eAAe,GAAG,IAAI,CAACF,OAAO,CAAC1K,YAAY,CAACjK,IAAI,CAAC3L,KAAK,CAAEwF,KAAK,IAAK,CAAC,CAACA,KAAK,CAACb,MAAM,CAAC,CAAC;IACvF,IAAI,CAAC+C,aAAa,GAAG,IAAI,CAAC+Y,YAAY,CAAC7M,qBAAqB,CAAC;IAC7D,IAAI,CAAC8M,YAAY,GAAG,IAAI,CAACD,YAAY,CAAC5M,oBAAoB,CAAC;EAC/D;EACA8M,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACJ,UAAU,GAAG,IAAI;IACtB,IAAI,CAACH,aAAa,CAAC1M,WAAW,CAAC,eAAe,CAAC;EACnD;EACAkN,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC7hB,iBAAiB,CAAC,IAAI,CAACshB,UAAU,CAAC,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvBM,MAAM,CAACC,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C;EACJ;EACAN,YAAYA,CAACpS,KAAK,EAAE;IAChB,MAAM2S,QAAQ,GAAG,IAAI,CAACrf,QAAQ,CAACyT,GAAG,CAAC/G,KAAK,EAAE,EAAE,CAAC;IAC7C,OAAO2S,QAAQ,CAAC7M,MAAM,CAAC,CAACC,GAAG,EAAE6M,GAAG,KAAK7M,GAAG,CAAC8M,MAAM,CAACD,GAAG,CAAC,EAAE,EAAE,CAAC;EAC7D;EACA;IAAS,IAAI,CAAClT,IAAI,YAAAoT,wBAAAlT,CAAA;MAAA,YAAAA,CAAA,IAAwFkS,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAAC/O,IAAI,kBAj3B8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EAi3BJqW,eAAe;MAAA7O,SAAA;MAAA8P,cAAA,WAAAC,+BAAAngB,EAAA,EAAAC,GAAA,EAAAmgB,QAAA;QAAA,IAAApgB,EAAA;UAj3BbpE,EAAE,CAAAykB,cAAA,CAAAD,QAAA,EAi3ByGxJ,qBAAqB,KAA2B9Z,WAAW;UAj3BtKlB,EAAE,CAAAykB,cAAA,CAAAD,QAAA,EAi3B6NnJ,kBAAkB;QAAA;QAAA,IAAAjX,EAAA;UAAA,IAAAsgB,EAAA;UAj3BjP1kB,EAAE,CAAA2kB,cAAA,CAAAD,EAAA,GAAF1kB,EAAE,CAAA4kB,WAAA,QAAAvgB,GAAA,CAAAwgB,cAAA,GAAAH,EAAA,CAAAI,KAAA;UAAF9kB,EAAE,CAAA2kB,cAAA,CAAAD,EAAA,GAAF1kB,EAAE,CAAA4kB,WAAA,QAAAvgB,GAAA,CAAA0gB,SAAA,GAAAL,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAApQ,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAAid,yBAAA5gB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA2H,GAAA,GAAF/L,EAAE,CAAAqG,gBAAA;UAAFrG,EAAE,CAAAuF,cAAA,YAi3BsT,CAAC,YAAuC,CAAC;UAj3BjWvF,EAAE,CAAAwE,UAAA,IAAA+F,uCAAA,yBAi3B8b,CAAC;UAj3BjcvK,EAAE,CAAAuF,cAAA,iBAi3BwnB,CAAC;UAj3B3nBvF,EAAE,CAAAsG,UAAA,mBAAA2e,mDAAA;YAAFjlB,EAAE,CAAAyG,aAAA,CAAAsF,GAAA;YAAA,OAAF/L,EAAE,CAAA0G,WAAA,CAi3B0lBrC,GAAA,CAAAwf,kBAAA,CAAmB,CAAC;UAAA,CAAC,CAAC;UAj3BlnB7jB,EAAE,CAAA0F,YAAA,CAi3BmoB,CAAC,CAAW,CAAC;UAj3BlpB1F,EAAE,CAAAwE,UAAA,IAAAiG,sCAAA,sBAi3BwrB,CAAC;UAj3B3rBzK,EAAE,CAAAyF,MAAA;UAAFzF,EAAE,CAAAwE,UAAA,IAAAqG,uCAAA,yBAi3B4mC,CAAC;UAj3B/mC7K,EAAE,CAAAyF,MAAA;UAAFzF,EAAE,CAAAyF,MAAA;UAAFzF,EAAE,CAAAwE,UAAA,IAAAsG,uCAAA,yBAi3B6wC,CAAC;UAj3BhxC9K,EAAE,CAAA0F,YAAA,CAi3BsyC,CAAC;UAj3BzyC1F,EAAE,CAAAwE,UAAA,KAAAuG,uCAAA,gCAAF/K,EAAE,CAAA0H,sBAi3By3C,CAAC,KAAA6D,uCAAA,gCAj3B53CvL,EAAE,CAAA0H,sBAi3BwlD,CAAC,KAAA+D,uCAAA,gCAj3B3lDzL,EAAE,CAAA0H,sBAi3Bq0D,CAAC;QAAA;QAAA,IAAAtD,EAAA;UAAA,MAAA8gB,uBAAA,GAj3Bx0DllB,EAAE,CAAAiF,WAAA;UAAA,MAAA0F,wBAAA,GAAF3K,EAAE,CAAAiF,WAAA;UAAA,MAAAkgB,cAAA,GAAFnlB,EAAE,CAAAiF,WAAA;UAAFjF,EAAE,CAAA2F,SAAA,EAi3Bmb,CAAC;UAj3Btb3F,EAAE,CAAA2E,UAAA,sBAAAN,GAAA,CAAA0gB,SAAA,kBAAA1gB,GAAA,CAAA0gB,SAAA,CAAAhd,QAAA,KAAAod,cAi3Bmb,CAAC;UAj3BtbnlB,EAAE,CAAA2F,SAAA,EAi3By2B,CAAC;UAj3B52B3F,EAAE,CAAAgG,aAAA,IAAFhG,EAAE,CAAA6F,WAAA,OAAAxB,GAAA,CAAAqf,eAAA,UAi3By2B,CAAC;UAj3B52B1jB,EAAE,CAAA2F,SAAA,EAi3B29B,CAAC;UAj3B99B3F,EAAE,CAAA2E,UAAA,qBAAAN,GAAA,CAAAwgB,cAAA,IAAAK,uBAi3B29B,CAAC,4BAj3B99BllB,EAAE,CAAAolB,eAAA,KAAAhb,GAAA,EAAFpK,EAAE,CAAA6F,WAAA,OAAAxB,GAAA,CAAAmf,OAAA,CAAA1K,YAAA,GAAF9Y,EAAE,CAAA6F,WAAA,QAAAxB,GAAA,CAAAmf,OAAA,CAAAzK,mBAAA,EAi3BslC,CAAC;UAj3BzlC/Y,EAAE,CAAA2F,SAAA,EAi3B4tC,CAAC;UAj3B/tC3F,EAAE,CAAA2E,UAAA,qBAAAgG,wBAi3B4tC,CAAC,4BAj3B/tC3K,EAAE,CAAAgI,eAAA,KAAAhE,GAAA,EAAAK,GAAA,CAAAuf,YAAA,CAi3BgwC,CAAC;QAAA;MAAA;MAAA9O,YAAA,GAA+qB/S,EAAE,CAACmgB,iBAAiB,EAAoPngB,EAAE,CAACsjB,OAAO,EAAmHtjB,EAAE,CAACqgB,gBAAgB,EAAoJ/M,kBAAkB,EAA2DrB,aAAa,EAA4EsO,qBAAqB,EAAyIvgB,EAAE,CAACsgB,SAAS;MAAArN,aAAA;IAAA,EAA+D;EAAE;AACn9F;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAn3BoG1R,EAAE,CAAA2R,iBAAA,CAm3BX0R,eAAe,EAAc,CAAC;IAC7GrW,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,YAAY;MAAEuG,aAAa,EAAEzU,iBAAiB,CAAC0U,IAAI;MAAElN,QAAQ,EAAE;IAAsmD,CAAC;EAC7rD,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE8c,cAAc,EAAE,CAAC;MACzD7X,IAAI,EAAE7L,YAAY;MAClByQ,IAAI,EAAE,CAACoJ,qBAAqB,EAAE;QAAEsK,IAAI,EAAEpkB;MAAY,CAAC;IACvD,CAAC,CAAC;IAAE6jB,SAAS,EAAE,CAAC;MACZ/X,IAAI,EAAE7L,YAAY;MAClByQ,IAAI,EAAE,CAACyJ,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkK,gBAAgB,CAAC;EACnBvE,SAASA,CAAC3S,KAAK,EAAE;IACb,OAAOA,KAAK,GAAGmJ,UAAU,CAACnJ,KAAK,CAAC,GAAGtL,EAAE,CAAC,EAAE,CAAC;EAC7C;EACA;IAAS,IAAI,CAACkO,IAAI,YAAAuU,yBAAArU,CAAA;MAAA,YAAAA,CAAA,IAAwFoU,gBAAgB;IAAA,CAA8C;EAAE;EAC1K;IAAS,IAAI,CAACrE,KAAK,kBAn4B6ElhB,EAAE,CAAAmhB,YAAA;MAAAC,IAAA;MAAApU,IAAA,EAm4BMuY,gBAAgB;MAAAlE,IAAA;IAAA,EAAyB;EAAE;AACvJ;AACA;EAAA,QAAA3P,SAAA,oBAAAA,SAAA,KAr4BoG1R,EAAE,CAAA2R,iBAAA,CAq4BX4T,gBAAgB,EAAc,CAAC;IAC9GvY,IAAI,EAAErM,IAAI;IACViR,IAAI,EAAE,CAAC;MACCwP,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMqE,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACxU,IAAI,YAAAyU,2BAAAvU,CAAA;MAAA,YAAAA,CAAA,IAAwFsU,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAAChT,IAAI,kBA94B8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EA84BSyY;IAAkB,EAA2F;EAAE;EAC1N;IAAS,IAAI,CAAC9S,IAAI,kBA/4B8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GA+4BuC7Q,YAAY;IAAA,EAAI;EAAE;AAC/J;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KAj5BoG1R,EAAE,CAAA2R,iBAAA,CAi5BX8T,kBAAkB,EAAc,CAAC;IAChHzY,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACyS,gBAAgB,CAAC;MAChC1S,OAAO,EAAE,CAAC7Q,YAAY,CAAC;MACvBoT,OAAO,EAAE,CAACmQ,gBAAgB;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMI,YAAY,CAAC;EACf5X,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6X,SAAS,GAAG3lB,MAAM,CAACwD,YAAY,CAAC;EACzC;EACAud,SAASA,CAAC3S,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACnC,OAAO,EAAE;IACb,OAAO,IAAI,CAACuX,SAAS,CAACC,QAAQ,CAACzkB,eAAe,CAAC0kB,IAAI,EAAEzX,KAAK,CAAC,IAAI,EAAE;EACrE;EACA;IAAS,IAAI,CAAC4C,IAAI,YAAA8U,qBAAA5U,CAAA;MAAA,YAAAA,CAAA,IAAwFwU,YAAY;IAAA,CAA8C;EAAE;EACtK;IAAS,IAAI,CAACzE,KAAK,kBAp6B6ElhB,EAAE,CAAAmhB,YAAA;MAAAC,IAAA;MAAApU,IAAA,EAo6BM2Y,YAAY;MAAAtE,IAAA;MAAAxE,UAAA;IAAA,EAA4C;EAAE;AACtK;AACA;EAAA,QAAAnL,SAAA,oBAAAA,SAAA,KAt6BoG1R,EAAE,CAAA2R,iBAAA,CAs6BXgU,YAAY,EAAc,CAAC;IAC1G3Y,IAAI,EAAErM,IAAI;IACViR,IAAI,EAAE,CAAC;MAAEwP,IAAI,EAAE,aAAa;MAAEvE,UAAU,EAAE;IAAK,CAAC;EACpD,CAAC,CAAC;AAAA;AAEV,MAAMmJ,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAAC/U,IAAI,YAAAgV,2BAAA9U,CAAA;MAAA,YAAAA,CAAA,IAAwF6U,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACvT,IAAI,kBA76B8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EA66BSgZ;IAAkB,EAAqF;EAAE;EACpN;IAAS,IAAI,CAACrT,IAAI,kBA96B8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GA86BuC7Q,YAAY;IAAA,EAAI;EAAE;AAC/J;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KAh7BoG1R,EAAE,CAAA2R,iBAAA,CAg7BXqU,kBAAkB,EAAc,CAAC;IAChHhZ,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACgO,aAAa,CAAC;MAC7BjO,OAAO,EAAE,CAAC7Q,YAAY,CAAC;MACvBoT,OAAO,EAAE,CAAC0L,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMoF,sBAAsB,GAAG,CAC3B7C,eAAe,EACf/B,kBAAkB,EAClBgB,qBAAqB,EACrBtH,qBAAqB,CACxB;AACD,MAAMmL,eAAe,CAAC;EAClB,OAAOlU,OAAOA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO;MACHC,QAAQ,EAAEgU,eAAe;MACzB/T,SAAS,EAAE,CACP;QACIC,OAAO,EAAE2E,cAAc;QACvB1E,QAAQ,EAAEJ,OAAO,EAAE0G,SAAS,IAAI;MACpC,CAAC,EACD;QACIvG,OAAO,EAAE0E,oBAAoB;QAC7BzE,QAAQ,EAAEJ,OAAO,EAAEkU,kBAAkB,IAAI,EAAE;QAC3C7T,KAAK,EAAE;MACX,CAAC,EACD;QACIF,OAAO,EAAEyE,qBAAqB;QAC9BxE,QAAQ,EAAEJ,OAAO,EAAEmU,mBAAmB,IAAI,EAAE;QAC5C9T,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACL;EACA,OAAO+T,QAAQA,CAACpU,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAO;MACHC,QAAQ,EAAEgU,eAAe;MACzB/T,SAAS,EAAE,CACP;QACIC,OAAO,EAAE0E,oBAAoB;QAC7BzE,QAAQ,EAAEJ,OAAO,EAAEkU,kBAAkB,IAAI,EAAE;QAC3C7T,KAAK,EAAE;MACX,CAAC,EACD;QACIF,OAAO,EAAEyE,qBAAqB;QAC9BxE,QAAQ,EAAEJ,OAAO,EAAEmU,mBAAmB,IAAI,EAAE;QAC5C9T,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACL;EACA;IAAS,IAAI,CAACtB,IAAI,YAAAsV,wBAAApV,CAAA;MAAA,YAAAA,CAAA,IAAwFgV,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAAC1T,IAAI,kBAv+B8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAu+BSmZ;IAAe,EAazF;EAAE;EACnC;IAAS,IAAI,CAACxT,IAAI,kBAr/B8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAq/BoC7Q,YAAY,EAC1IsB,WAAW,EACX3B,YAAY,EACZ8T,kBAAkB,EAClBP,aAAa,EACbuQ,kBAAkB,EAClBO,kBAAkB;IAAA,EAAI;EAAE;AACpC;AACA;EAAA,QAAAtU,SAAA,oBAAAA,SAAA,KA7/BoG1R,EAAE,CAAA2R,iBAAA,CA6/BXwU,eAAe,EAAc,CAAC;IAC7GnZ,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAAC,GAAGoT,sBAAsB,CAAC;MACzCrT,OAAO,EAAE,CACL7Q,YAAY,EACZsB,WAAW,EACX3B,YAAY,EACZ8T,kBAAkB,EAClBP,aAAa,EACbuQ,kBAAkB,EAClBO,kBAAkB,EAClBpK,mBAAmB,CACtB;MACDxG,OAAO,EAAE,CAAC,GAAG8Q,sBAAsB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMM,iBAAiB,CAAC;EACpBzY,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+B,KAAK,GAAG,IAAI5B,SAAS,CAAC,EAAE,CAAC;IAC9B,IAAI,CAACuY,MAAM,GAAG,IAAI,CAAC3W,KAAK,CAACtB,UAAU,CAAEL,KAAK,IAAKA,KAAK,CAAC;EACzD;EACA;EACAyP,GAAGA,CAAC9Z,IAAI,EAAE;IACN,MAAM4E,KAAK,GAAG0G,KAAK,CAACC,OAAO,CAACvL,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjD,IAAI,CAACgM,KAAK,CAACP,GAAG,CAAC,CAAC,GAAG,IAAI,CAACO,KAAK,CAAC3B,KAAK,EAAE,GAAGzF,KAAK,CAAC,CAAC;EACnD;EACA;EACAge,MAAMA,CAAC5iB,IAAI,EAAEqS,KAAK,EAAE;IAChB,MAAMhI,KAAK,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,KAAK;IAC9B,MAAMzF,KAAK,GAAG0G,KAAK,CAACC,OAAO,CAACvL,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjD,IAAI,CAACgM,KAAK,CAACP,GAAG,CAAC,CAAC,GAAGpB,KAAK,CAACmI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAE,GAAGzN,KAAK,EAAE,GAAGyF,KAAK,CAACmI,KAAK,CAACH,KAAK,CAAC,CAAC,CAAC;EAC/E;EACA;EACAwQ,QAAQA,CAACje,KAAK,EAAE;IACZ,IAAI,CAACoH,KAAK,CAACP,GAAG,CAAC7G,KAAK,CAAC;EACzB;EACA;IAAS,IAAI,CAACuI,IAAI,YAAA2V,0BAAAzV,CAAA;MAAA,YAAAA,CAAA,IAAwFqV,iBAAiB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACnV,KAAK,kBApiC6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAoiCYiV,iBAAiB;MAAAhV,OAAA,EAAjBgV,iBAAiB,CAAAvV,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtiCoG1R,EAAE,CAAA2R,iBAAA,CAsiCX6U,iBAAiB,EAAc,CAAC;IAC/GxZ,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMoV,qBAAqB,CAAC;EACxB9Y,WAAWA,CAAC8R,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACiH,eAAe,GAAG,IAAIhmB,YAAY,CAAC,CAAC;IACzC,IAAI,CAACimB,YAAY,GAAG,EAAE;EAC1B;EACAC,eAAeA,CAAC3G,KAAK,EAAE;IACnB,IAAI,EAAE,IAAI,CAACR,UAAU,CAACI,aAAa,CAACgH,QAAQ,CAAC5G,KAAK,CAAC6G,MAAM,CAAC,IACtD,IAAI,CAACH,YAAY,CAAC/O,IAAI,CAACmP,GAAG,IAAIA,GAAG,CAAClH,aAAa,CAACgH,QAAQ,CAAC5G,KAAK,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAE;MAC1E,IAAI,CAACJ,eAAe,CAAClgB,IAAI,CAAC,CAAC;IAC/B;EACJ;EACA;IAAS,IAAI,CAACqK,IAAI,YAAAmW,8BAAAjW,CAAA;MAAA,YAAAA,CAAA,IAAwF0V,qBAAqB,EAzjC/B7mB,EAAE,CAAAqU,iBAAA,CAyjC+CrU,EAAE,CAACwB,UAAU;IAAA,CAA4C;EAAE;EAC5M;IAAS,IAAI,CAAC0Z,IAAI,kBA1jC8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA0jCJ6Z,qBAAqB;MAAArS,SAAA;MAAA6S,YAAA,WAAAC,mCAAAljB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1jCnBpE,EAAE,CAAAsG,UAAA,mBAAAihB,+CAAA/gB,MAAA;YAAA,OA0jCJnC,GAAA,CAAA2iB,eAAA,CAAAxgB,MAAsB,CAAC;UAAA,UA1jCrBxG,EAAE,CAAAwnB,iBA0jCgB,CAAC;QAAA;MAAA;MAAA/S,MAAA;QAAAsS,YAAA;MAAA;MAAA/E,OAAA;QAAA8E,eAAA;MAAA;IAAA,EAAiN;EAAE;AAC1U;AACA;EAAA,QAAApV,SAAA,oBAAAA,SAAA,KA5jCoG1R,EAAE,CAAA2R,iBAAA,CA4jCXkV,qBAAqB,EAAc,CAAC;IACnH7Z,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACwB;EAAW,CAAC,CAAC,EAAkB;IAAEslB,eAAe,EAAE,CAAC;MACjF9Z,IAAI,EAAEjM;IACV,CAAC,CAAC;IAAEgmB,YAAY,EAAE,CAAC;MACf/Z,IAAI,EAAExM;IACV,CAAC,CAAC;IAAEwmB,eAAe,EAAE,CAAC;MAClBha,IAAI,EAAE3L,YAAY;MAClBuQ,IAAI,EAAE,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;IACvC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6V,mBAAmB,CAAC;EACtB1Z,WAAWA,CAACyV,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACpe,IAAI,GAAG2N,QAAQ;EACxB;EACA7G,OAAOA,CAACpI,IAAI,EAAE;IACV,IAAIA,IAAI,CAACC,QAAQ,EAAE;MACfD,IAAI,CAACoC,QAAQ,GAAG,CAACpC,IAAI,CAACoC,QAAQ;IAClC;EACJ;EACA;IAAS,IAAI,CAAC+K,IAAI,YAAAyW,4BAAAvW,CAAA;MAAA,YAAAA,CAAA,IAAwFsW,mBAAmB,EAplC7BznB,EAAE,CAAAqU,iBAAA,CAolC6CmS,iBAAiB;IAAA,CAA4C;EAAE;EAC9M;IAAS,IAAI,CAAClS,IAAI,kBArlC8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EAqlCJya,mBAAmB;MAAAjT,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAA4f,6BAAAvjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArlCjBpE,EAAE,CAAAuF,cAAA,YAqlCoG,CAAC,WAAkC,CAAC;UArlC1IvF,EAAE,CAAAwE,UAAA,IAAAsH,2CAAA,yBAqlCyN,CAAC;UArlC5N9L,EAAE,CAAAyF,MAAA;UAAFzF,EAAE,CAAA0F,YAAA,CAqlCo+B,CAAC,CAAS,CAAC;UArlCj/B1F,EAAE,CAAAwE,UAAA,IAAAgI,0CAAA,gCAAFxM,EAAE,CAAA0H,sBAqlC0hC,CAAC,IAAA+E,0CAAA,gCArlC7hCzM,EAAE,CAAA0H,sBAqlCoqC,CAAC;QAAA;QAAA,IAAAtD,EAAA;UArlCvqCpE,EAAE,CAAA2F,SAAA,EAqlC0M,CAAC;UArlC7M3F,EAAE,CAAA2E,UAAA,YAAF3E,EAAE,CAAA6F,WAAA,OAAAxB,GAAA,CAAAmf,OAAA,CAAAiD,MAAA,CAqlC0M,CAAC;QAAA;MAAA;MAAA3R,YAAA,GAAuoC/S,EAAE,CAACsjB,OAAO,EAAmHtjB,EAAE,CAACogB,IAAI,EAA6FpgB,EAAE,CAACqgB,gBAAgB,EAAoJpO,aAAa,EAA4EvS,EAAE,CAAC+T,UAAU,EAAoOqR,qBAAqB,EAAiH9kB,EAAE,CAACsgB,SAAS,EAAyCkD,gBAAgB;MAAAvQ,aAAA;IAAA,EAAsE;EAAE;AAC75E;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAvlCoG1R,EAAE,CAAA2R,iBAAA,CAulCX8V,mBAAmB,EAAc,CAAC;IACjHza,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,gBAAgB;MAAEuG,aAAa,EAAEzU,iBAAiB,CAAC0U,IAAI;MAAElN,QAAQ,EAAE;IAAkuC,CAAC;EAC7zC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiF,IAAI,EAAEwZ;EAAkB,CAAC,CAAC;AAAA;AAE/D,MAAMoB,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAC3W,IAAI,YAAA4W,8BAAA1W,CAAA;MAAA,YAAAA,CAAA,IAAwFyW,qBAAqB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACnV,IAAI,kBA9lC8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EA8lCS4a;IAAqB,EAAqG;EAAE;EACvO;IAAS,IAAI,CAACjV,IAAI,kBA/lC8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GA+lC0C7Q,YAAY;IAAA,EAAI;EAAE;AAClK;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KAjmCoG1R,EAAE,CAAA2R,iBAAA,CAimCXiW,qBAAqB,EAAc,CAAC;IACnH5a,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAAC+T,qBAAqB,CAAC;MACrChU,OAAO,EAAE,CAAC7Q,YAAY,CAAC;MACvBoT,OAAO,EAAE,CAACyR,qBAAqB;IACnC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMiB,8BAA8B,CAAC;EACjC/Z,WAAWA,CAACga,aAAa,EAAE/Z,MAAM,EAAEga,iBAAiB,EAAE;IAClD,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC/Z,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACga,iBAAiB,GAAGA,iBAAiB;EAC9C;EACAC,cAAcA,CAAA,EAAG;IACb9kB,aAAa,CAAC,CACV,IAAI,CAAC6K,MAAM,CAACgM,MAAM,CAACnL,IAAI,CAACxM,MAAM,CAAEge,KAAK,IAAKA,KAAK,YAAYze,aAAa,CAAC,CAAC,EAC1E,IAAI,CAACmmB,aAAa,CAACjP,YAAY,CAACjK,IAAI,CAACxM,MAAM,CAAEqG,KAAK,IAAK,CAAC,CAACA,KAAK,CAACb,MAAM,CAAC,CAAC,CAC1E,CAAC,CAACsR,SAAS,CAAC,CAAC,CAACkH,KAAK,EAAE3X,KAAK,CAAC,KAAK;MAC7B,IAAIwf,UAAU,GAAG,IAAI,CAACH,aAAa,CAACjO,UAAU,CAACuG,KAAK,CAAC/F,GAAG,CAAC;MACzD,IAAI,CAAC4N,UAAU,CAACpkB,IAAI,EAAE;QAClBokB,UAAU,GAAG,IAAI,CAACH,aAAa,CAACjO,UAAU,CAAC,GAAG,CAAC;MACnD;MACA,MAAMqO,eAAe,GAAGD,UAAU,CAACzO,QAAQ,CAACpC,MAAM,CAAC,CAACC,GAAG,EAAEuD,SAAS,KAAK;QACnE,MAAMtB,MAAM,GAAGjC,GAAG,CAACA,GAAG,CAACzP,MAAM,GAAG,CAAC,CAAC,EAAE9D,QAAQ,IAAI2E,KAAK;QACrD,MAAM5E,IAAI,GAAGyV,MAAM,CAACsB,SAAS,CAAC;QAC9B,OAAO,CACH,GAAGvD,GAAG,EACN;UAAE,GAAGxT,IAAI;UAAEskB,QAAQ,EAAE7O;QAAO,CAAC,CAChC;MACL,CAAC,EAAE,EAAE,CAAC;MACN,IAAI,CAACyO,iBAAiB,CAACrB,QAAQ,CAAC,IAAI,CAAC0B,6BAA6B,CAACF,eAAe,CAAC,CAAC;IACxF,CAAC,CAAC;EACN;EACAE,6BAA6BA,CAAC3f,KAAK,EAAE;IACjC,OAAOA,KAAK,CAACvG,GAAG,CAAC,CAAC;MAAEmmB,cAAc;MAAExiB,IAAI;MAAEgC,IAAI;MAAE1C,IAAI;MAAEgjB;IAAS,CAAC,MAAM;MAClEtiB,IAAI,EAAEwiB,cAAc,IAAIxiB,IAAI,IAAI,EAAE;MAClCgC,IAAI;MACJ1C,IAAI;MACJrB,QAAQ,EAAE,IAAI,CAACskB,6BAA6B,CAACD,QAAQ,IAAI,EAAE;IAC/D,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAACnX,IAAI,YAAAsX,uCAAApX,CAAA;MAAA,YAAAA,CAAA,IAAwF2W,8BAA8B,EA5oCxC9nB,EAAE,CAAAoR,QAAA,CA4oCwDuH,aAAa,GA5oCvE3Y,EAAE,CAAAoR,QAAA,CA4oCkF3P,EAAE,CAACC,MAAM,GA5oC7F1B,EAAE,CAAAoR,QAAA,CA4oCwGoV,iBAAiB;IAAA,CAA6C;EAAE;EAC1Q;IAAS,IAAI,CAACnV,KAAK,kBA7oC6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA6oCYuW,8BAA8B;MAAAtW,OAAA,EAA9BsW,8BAA8B,CAAA7W,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/oCoG1R,EAAE,CAAA2R,iBAAA,CA+oCXmW,8BAA8B,EAAc,CAAC;IAC5H9a,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzE,IAAI,EAAE2L;EAAc,CAAC,EAAE;IAAE3L,IAAI,EAAEvL,EAAE,CAACC;EAAO,CAAC,EAAE;IAAEsL,IAAI,EAAEwZ;EAAkB,CAAC,CAAC;AAAA;AAE7G,MAAMgC,oBAAoB,GAAG,CAACf,mBAAmB,CAAC;AAClD,MAAMgB,mBAAmB,CAAC;EACtB,OAAOxW,OAAOA,CAAA,EAAG;IACb,OAAO;MACHE,QAAQ,EAAEsW,mBAAmB;MAC7BrW,SAAS,EAAE,CACP;QACIC,OAAO,EAAE/Q,eAAe;QACxBonB,UAAU,EAAEC,cAAc;QAC1BpW,KAAK,EAAE,IAAI;QACXqW,IAAI,EAAE,CAAChoB,QAAQ;MACnB,CAAC;IAET,CAAC;EACL;EACA;IAAS,IAAI,CAACqQ,IAAI,YAAA4X,4BAAA1X,CAAA;MAAA,YAAAA,CAAA,IAAwFsX,mBAAmB;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAAChW,IAAI,kBAtqC8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAsqCSyb;IAAmB,EAI7D;EAAE;EACnE;IAAS,IAAI,CAAC9V,IAAI,kBA3qC8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GA2qCwC7Q,YAAY,EAC9IkT,aAAa,EACbuQ,kBAAkB,EAClB9jB,YAAY,EACZimB,qBAAqB;IAAA,EAAI;EAAE;AACvC;AACA;EAAA,QAAAlW,SAAA,oBAAAA,SAAA,KAjrCoG1R,EAAE,CAAA2R,iBAAA,CAirCX8W,mBAAmB,EAAc,CAAC;IACjHzb,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAAC,GAAG0V,oBAAoB,CAAC;MACvC3V,OAAO,EAAE,CACL7Q,YAAY,EACZkT,aAAa,EACbuQ,kBAAkB,EAClB9jB,YAAY,EACZimB,qBAAqB,CACxB;MACDxS,OAAO,EAAE,CAAC,GAAGoT,oBAAoB;IACrC,CAAC;EACT,CAAC,CAAC;AAAA;AACV,SAASG,cAAcA,CAAC9jB,QAAQ,EAAE;EAC9B,MAAMikB,IAAI,GAAGA,CAAA,KAAM;IACf,MAAMtF,OAAO,GAAG3e,QAAQ,CAACyT,GAAG,CAACwP,8BAA8B,CAAC;IAC5DtE,OAAO,CAACyE,cAAc,CAAC,CAAC;EAC5B,CAAC;EACD,OAAOa,IAAI;AACf;AAEA,MAAMC,8BAA8B,GAAG;EACnC1W,OAAO,EAAEP,2BAA2B;EACpCsN,QAAQ,EAAEpB;AACd,CAAC;AACD,MAAMgL,uBAAuB,GAAG,CAC5BD,8BAA8B,CACjC;AAED,MAAME,kBAAkB,GAAG,IAAI/oB,cAAc,CAAC,0BAA0B,CAAC;AACzE,MAAMgpB,eAAe,GAAG,IAAIhpB,cAAc,CAAC,uBAAuB,CAAC;AACnE,MAAMipB,sBAAsB,GAAG,IAAIjpB,cAAc,CAAC,iCAAiC,CAAC;AAEpF,MAAMkpB,YAAY,CAAC;EACfrb,WAAWA,CAACsb,aAAa,EAAE/L,QAAQ,EAAE;IACjC,IAAI,CAAC+L,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC/L,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgM,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,YAAY,GAAG,IAAI5mB,eAAe,CAAC,KAAK,CAAC;EAClD;EACM6mB,UAAUA,CAACC,SAAS,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxB,KAAK,MAAMC,KAAK,IAAIF,KAAI,CAACL,aAAa,EAAE;QACpC,MAAMK,KAAI,CAACG,SAAS,CAACD,KAAK,EAAEH,SAAS,CAAC;MAC1C;MACAC,KAAI,CAACH,YAAY,CAACja,IAAI,CAAC,IAAI,CAAC;IAAC;EACjC;EACMua,SAASA,CAACD,KAAK,EAAEH,SAAS,EAAE;IAAA,IAAAK,MAAA;IAAA,OAAAH,iBAAA;MAC9B,OAAO,IAAIlS,OAAO,CAAC,CAACsS,OAAO,EAAEC,MAAM,KAAK;QACpC,MAAMC,QAAQ,GAAGH,MAAI,CAACI,cAAc,CAACN,KAAK,EAAEH,SAAS,EAAEM,OAAO,CAAC;QAC/D;QACA,MAAMI,SAAS,GAAG7M,QAAQ,CAACC,aAAa,CAAC,wCAAwC,CAAC;QAClF,IAAI4M,SAAS,EAAE;UACX,IAAIL,MAAI,CAACR,iBAAiB,IAAIQ,MAAI,CAACR,iBAAiB,CAACc,WAAW,EAAE;YAC9DN,MAAI,CAACR,iBAAiB,CAACe,qBAAqB,CAAC,UAAU,EAAEJ,QAAQ,CAAC;UACtE,CAAC,MACI;YACDE,SAAS,CAACE,qBAAqB,CAAC,aAAa,EAAEJ,QAAQ,CAAC;UAC5D;QACJ,CAAC,MACI;UACDH,MAAI,CAACxM,QAAQ,CAACgN,IAAI,CAACC,WAAW,CAACN,QAAQ,CAAC;QAC5C;QACAH,MAAI,CAACR,iBAAiB,GAAGW,QAAQ;QACjC,OAAOxS,OAAO,CAACsS,OAAO,CAACE,QAAQ,CAAC;MACpC,CAAC,CAAC;IAAC;EACP;EACMO,YAAYA,CAACZ,KAAK,EAAEH,SAAS,EAAE;IAAA,IAAAgB,MAAA;IAAA,OAAAd,iBAAA;MACjC,MAAMe,MAAM,GAAGD,MAAI,CAACnN,QAAQ,CAACC,aAAa,CAAC,QAAQqM,KAAK,CAACe,UAAU,EAAE,CAAC;MACtE,IAAID,MAAM,EAAE;QACRA,MAAM,CAAC5M,MAAM,CAAC,CAAC;MACnB;MACA,OAAO2M,MAAI,CAACZ,SAAS,CAACD,KAAK,EAAEH,SAAS,CAAC;IAAC;EAC5C;EACMmB,mBAAmBA,CAACnB,SAAS,EAAE;IAAA,IAAAoB,MAAA;IAAA,OAAAlB,iBAAA;MACjC,KAAK,MAAMC,KAAK,IAAIiB,MAAI,CAACxB,aAAa,EAAE;QACpC,MAAMwB,MAAI,CAACL,YAAY,CAACZ,KAAK,EAAEH,SAAS,CAAC;MAC7C;IAAC;EACL;EACAS,cAAcA,CAACN,KAAK,EAAEH,SAAS,EAAEM,OAAO,EAAE;IACtC,MAAME,QAAQ,GAAG3M,QAAQ,CAACwN,aAAa,CAAC,MAAM,CAAC;IAC/Cb,QAAQ,CAACc,GAAG,GAAG,YAAY;IAC3Bd,QAAQ,CAACja,EAAE,GAAG4Z,KAAK,CAACe,UAAU;IAC9BV,QAAQ,CAACe,IAAI,GAAG,GAAGpB,KAAK,CAACe,UAAU,GAAGlB,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,EAAE,MAAM;IAC7EQ,QAAQ,CAACgB,MAAM,GAAG,MAAM;MACpBlB,OAAO,CAACE,QAAQ,CAAC;IACrB,CAAC;IACD,OAAOA,QAAQ;EACnB;EACA;IAAS,IAAI,CAAChZ,IAAI,YAAAia,qBAAA/Z,CAAA;MAAA,YAAAA,CAAA,IAAwFiY,YAAY,EA1wCtBppB,EAAE,CAAAoR,QAAA,CA0wCsC8X,eAAe,GA1wCvDlpB,EAAE,CAAAoR,QAAA,CA0wCkElP,QAAQ;IAAA,CAA6C;EAAE;EAC3N;IAAS,IAAI,CAACmP,KAAK,kBA3wC6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA2wCY6X,YAAY;MAAA5X,OAAA,EAAZ4X,YAAY,CAAAnY,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACvJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7wCoG1R,EAAE,CAAA2R,iBAAA,CA6wCXyX,YAAY,EAAc,CAAC;IAC1Gpc,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzE,IAAI,EAAEiC,SAAS;IAAE4C,UAAU,EAAE,CAAC;MAC/C7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAACsX,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAElc,IAAI,EAAEme,QAAQ;IAAEtZ,UAAU,EAAE,CAAC;MACjC7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAAC1P,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,SAASkpB,kBAAkBA,CAACC,OAAO,EAAE;EACjC,OAAOA,OAAO,KAAMlN,YAAY,IAAKA,YAAY,CAAC;AACtD;AAEA,SAASmN,gBAAgBA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC/CD,SAAS,CAAChT,IAAI,CAAC;IACXoS,UAAU,EAAE;EAChB,CAAC,CAAC;EACFY,SAAS,CAAChT,IAAI,CAAC;IACXoS,UAAU,EAAE;EAChB,CAAC,CAAC;EACF,OAAO,CAAC,GAAGY,SAAS,EAAE,GAAGC,YAAY,CAAC;AAC1C;AAEA,MAAMC,mBAAmB,GAAG,CACxB;EACIpZ,OAAO,EAAE4W,kBAAkB;EAC3BP,UAAU,EAAEA,CAAA,KAAM;AACtB,CAAC,EACD;EACIrW,OAAO,EAAE/Q,eAAe;EACxBsnB,IAAI,EAAE,CAACQ,YAAY,EAAExZ,eAAe,CAAC;EACrC8Y,UAAU,EAAEgD,iBAAiB;EAC7BnZ,KAAK,EAAE;AACX,CAAC,CACJ;AACD,SAASmZ,iBAAiBA,CAACC,YAAY,EAAEC,eAAe,EAAE;EACtD,OAAO,MAAM;IACT,OAAOA,eAAe,CAACrb,eAAe,CAAC1B,IAAI,CAACvM,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAE0N,IAAI,IAAKpN,IAAI,CAAC6oB,YAAY,CAACnC,UAAU,CAACtZ,IAAI,CAAC2b,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EACxI,CAAC;AACL;AAEA,MAAMC,MAAM,GAAG,IAAI5rB,cAAc,CAAC,QAAQ,CAAC;AAE3C,SAAS6rB,oBAAoBA,CAACC,SAAS,EAAE;EACrC,OAAO;IAAE3Z,OAAO,EAAEyZ,MAAM;IAAExZ,QAAQ,EAAE0Z,SAAS,IAAIjI;EAAO,CAAC;AAC7D;AAEA,MAAMkI,sBAAsB,GAAG,IAAI/rB,cAAc,CAAC,wBAAwB,CAAC;AAE3E,MAAMgsB,iBAAiB,CAAC;EACpBne,WAAWA,CAACoe,mBAAmB,EAAEpI,MAAM,EAAE;IACrC,IAAI,CAACoI,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACpI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqI,iBAAiB,GAAG;MACrBhL,IAAI,EAAE,KAAK,CAAC;MACZiL,KAAK,EAAE;IACX,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACJ,mBAAmB,CAAC;IAClE,IAAI,CAACK,cAAc,GAAG,OAAO;MACzBC,MAAM,EAAE,IAAI,CAAC1I,MAAM,CAAC2I,WAAW;MAC/BL,KAAK,EAAE,IAAI,CAACtI,MAAM,CAAC4I;IACvB,CAAC,CAAC;IACF,IAAI,CAACC,mBAAmB,GAAG,CAAC;MAAEP;IAAM,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC,CAAC,KAAK;MAC9D,OAAO,IAAI,CAACF,WAAW,CAACtb,IAAI,CAAC6b,CAAC,IAAIR,KAAK,IAAIQ,CAAC,CAACR,KAAK,CAAC;IACvD,CAAC;IACD,IAAI,CAACS,YAAY,GAAG,IAAInqB,eAAe,CAAC,IAAI,CAACiqB,mBAAmB,CAAC,CAAC,CAAC;IACnE,IAAI,CAACG,2BAA2B,GAAIC,KAAK,IAAK;MAC1C,OAAO,IAAI,CAACC,UAAU,CAACD,KAAK,CAAC;IACjC,CAAC;IACD,IAAI,CAACE,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,kBAAkB,GAAG/pB,SAAS,CAAC,IAAI,CAAC2gB,MAAM,EAAE,QAAQ,CAAC,CACrDlV,IAAI,CAAC1M,GAAG,CAAC,IAAI,CAACqqB,cAAc,CAAC,CAAC,CAC9B3d,IAAI,CAACpM,SAAS,CAAC,IAAI,CAAC+pB,cAAc,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACW,kBAAkB,CAClBte,IAAI,CAAC1M,GAAG,CAAC,IAAI,CAACyqB,mBAAmB,CAAC,EAAExqB,oBAAoB,CAAC,CAAC,CAAC,CAC3D+W,SAAS,CAACiU,OAAO,IAAI;MACtB,IAAI,CAACN,YAAY,CAACxd,IAAI,CAAC8d,OAAO,CAAC;IACnC,CAAC,CAAC;EACN;EACAb,gBAAgBA,CAACD,WAAW,EAAE;IAC1B,OAAO,CACH,GAAGxN,MAAM,CAACC,IAAI,CAACuN,WAAW,CAAC,CACtBnqB,GAAG,CAAC+b,GAAG,KAAK;MACbkD,IAAI,EAAElD,GAAG;MACTmO,KAAK,EAAEC,WAAW,CAACpO,GAAG;IAC1B,CAAC,CAAC,CAAC,CACEmP,IAAI,CAAC,CAAC7c,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC4b,KAAK,GAAG7b,CAAC,CAAC6b,KAAK,CAAC,EACtC,IAAI,CAACD,iBAAiB,CACzB;EACL;EACAa,UAAUA,CAACD,KAAK,EAAE;IACd,MAAM;MAAEX;IAAM,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC,CAAC;IACvC,MAAMc,MAAM,GAAGN,KAAK,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/B,MAAMC,YAAY,GAAIC,IAAI,IAAKH,MAAM,CAACtc,IAAI,CAACO,KAAK,IAAIA,KAAK,CAACgc,KAAK,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,KAAKE,IAAI,CAAC;IAClH,MAAMC,iBAAiB,GAAG,IAAI,CAACpB,WAAW,CAACtb,IAAI,CAAC2c,UAAU,IAAItB,KAAK,IAAIsB,UAAU,CAACtB,KAAK,IAAImB,YAAY,CAACG,UAAU,CAACvM,IAAI,CAAC,CAAC;IACzH,IAAIsM,iBAAiB,EAAE;MACnB,MAAMnc,KAAK,GAAGic,YAAY,CAACE,iBAAiB,CAACtM,IAAI,CAAC;MAClD,MAAMwM,cAAc,GAAG,CAACrc,KAAK,EAAEoF,QAAQ,CAAC,MAAM,CAAC,2BAA2B,CAAC;MAC3E,OAAOiX,cAAc,KAAKvB,KAAK,IAAIqB,iBAAiB,CAACrB,KAAK;IAC9D;IACA,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAACpb,IAAI,YAAA4c,0BAAA1c,CAAA;MAAA,YAAAA,CAAA,IAAwF+a,iBAAiB,EAz3C3BlsB,EAAE,CAAAoR,QAAA,CAy3C2C6a,sBAAsB,GAz3CnEjsB,EAAE,CAAAoR,QAAA,CAy3C8E0a,MAAM;IAAA,CAA6C;EAAE;EACrO;IAAS,IAAI,CAACza,KAAK,kBA13C6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EA03CY2a,iBAAiB;MAAA1a,OAAA,EAAjB0a,iBAAiB,CAAAjb,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA53CoG1R,EAAE,CAAA2R,iBAAA,CA43CXua,iBAAiB,EAAc,CAAC;IAC/Glf,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzE,IAAI,EAAEiC,SAAS;IAAE4C,UAAU,EAAE,CAAC;MAC/C7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAACqa,sBAAsB;IACjC,CAAC;EAAE,CAAC,EAAE;IAAEjf,IAAI,EAAEiC,SAAS;IAAE4C,UAAU,EAAE,CAAC;MAClC7E,IAAI,EAAE5M,MAAM;MACZwR,IAAI,EAAE,CAACka,MAAM;IACjB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMgC,mBAAmB,CAAC;EACtB/f,WAAWA,CAACmO,WAAW,EAAE6R,aAAa,EAAEvK,OAAO,EAAEwK,SAAS,EAAE;IACxD,IAAI,CAAC9R,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC6R,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACvK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACwK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,GAAG,GAAG,IAAI7qB,YAAY,CAAC,CAAC;IAC7B,IAAI,CAAC8qB,MAAM,GAAIC,YAAY,IAAK;MAC5B,IAAIA,YAAY,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;QACnC,IAAI,CAACF,aAAa,CAACrR,kBAAkB,CAAC,IAAI,CAACR,WAAW,CAAC;QACvD,IAAI,CAAC+R,WAAW,GAAG,IAAI;MAC3B,CAAC,MACI,IAAI,CAACG,YAAY,IAAI,IAAI,CAACH,WAAW,EAAE;QACxC,IAAI,CAACF,aAAa,CAACtR,KAAK,CAAC,CAAC;QAC1B,IAAI,CAACwR,WAAW,GAAG,KAAK;MAC5B;MACA,IAAI,CAACD,SAAS,CAACK,aAAa,CAAC,CAAC;IAClC,CAAC;EACL;EACAlS,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC+R,GAAG,CAACtQ,GAAG,CAAC,IAAI,CAAC4F,OAAO,CAACsJ,YAAY,CACjCje,IAAI,CAAC1M,GAAG,CAACmsB,CAAC,IAAI,IAAI,CAAC9K,OAAO,CAACuJ,2BAA2B,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CACpE7T,SAAS,CAAC,IAAI,CAACgV,MAAM,CAAC,CAAC;EAChC;EACA9R,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6R,GAAG,CAAC3R,WAAW,CAAC,CAAC;EAC1B;EACA;IAAS,IAAI,CAACtL,IAAI,YAAAsd,4BAAApd,CAAA;MAAA,YAAAA,CAAA,IAAwF2c,mBAAmB,EAr6C7B9tB,EAAE,CAAAqU,iBAAA,CAq6C6CrU,EAAE,CAACkB,WAAW,GAr6C7DlB,EAAE,CAAAqU,iBAAA,CAq6CwErU,EAAE,CAAC4c,gBAAgB,GAr6C7F5c,EAAE,CAAAqU,iBAAA,CAq6CwG6X,iBAAiB,GAr6C3HlsB,EAAE,CAAAqU,iBAAA,CAq6CsIrU,EAAE,CAACwuB,iBAAiB;IAAA,CAA4E;EAAE;EAC1U;IAAS,IAAI,CAACtT,IAAI,kBAt6C8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAs6CJ8gB,mBAAmB;MAAAtZ,SAAA;MAAAC,MAAA;QAAAuY,KAAA,GAt6CjBhtB,EAAE,CAAA8hB,YAAA,CAAA7M,IAAA;MAAA;IAAA,EAs6C4G;EAAE;AACpN;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAx6CoG1R,EAAE,CAAA2R,iBAAA,CAw6CXmc,mBAAmB,EAAc,CAAC;IACjH9gB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,EAAE;IAAE8L,IAAI,EAAEhN,EAAE,CAAC4c;EAAiB,CAAC,EAAE;IAAE5P,IAAI,EAAEkf;EAAkB,CAAC,EAAE;IAAElf,IAAI,EAAEhN,EAAE,CAACwuB,iBAAiB;IAAE3c,UAAU,EAAE,CAAC;MAChJ7E,IAAI,EAAEtM;IACV,CAAC,EAAE;MACCsM,IAAI,EAAEzL;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyrB,KAAK,EAAE,CAAC;MACjChgB,IAAI,EAAExM,KAAK;MACXoR,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6c,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAACxd,IAAI,YAAAyd,4BAAAvd,CAAA;MAAA,YAAAA,CAAA,IAAwFsd,mBAAmB;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAAChc,IAAI,kBAx7C8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAw7CSyhB;IAAmB,EAAiG;EAAE;EACjO;IAAS,IAAI,CAAC9b,IAAI,kBAz7C8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAy7CwC7Q,YAAY;IAAA,EAAI;EAAE;AAChK;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KA37CoG1R,EAAE,CAAA2R,iBAAA,CA27CX8c,mBAAmB,EAAc,CAAC;IACjHzhB,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACgb,mBAAmB,CAAC;MACnCjb,OAAO,EAAE,CAAC7Q,YAAY,CAAC;MACvBoT,OAAO,EAAE,CAAC0Y,mBAAmB;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMa,mCAAmC,GAAG;EACxCC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE;AACR,CAAC;AAED,SAASC,wBAAwBA,CAACC,kBAAkB,EAAE;EAClD,OAAO;IACH5c,OAAO,EAAE4Z,sBAAsB;IAC/B3Z,QAAQ,EAAE2c,kBAAkB,IAAIN;EACpC,CAAC;AACL;AAEA,SAASO,uBAAuBA,CAACC,eAAe,EAAE;EAC9C,OAAO;IACH9c,OAAO,EAAE/Q,eAAe;IACxBiR,KAAK,EAAE,IAAI;IACXqW,IAAI,EAAE,CAAChZ,eAAe,EAAEwZ,YAAY,CAAC;IACrCV,UAAU,EAAEyG,eAAe,GAAGC,qBAAqB,GAAG,MAAM,MAAM;EACtE,CAAC;AACL;AACA;AACA,SAASA,qBAAqBA,CAACxD,eAAe,EAAED,YAAY,EAAE;EAC1D,OAAO,MAAM;IACT,OAAO,IAAIlU,OAAO,CAAEsS,OAAO,IAAK;MAC5B4B,YAAY,CAACpC,YAAY,CACpB1a,IAAI,CAACxM,MAAM,CAAC6V,OAAO,CAAC,EAAE5V,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC,MAAMopB,eAAe,CAACrb,eAAe,CAAC,EAAE7N,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAClHyW,SAAS;QAAA,IAAAkW,IAAA,GAAA1F,iBAAA,CAAC,WAAOzZ,IAAI,EAAK;UAC3B,MAAMuZ,SAAS,GAAGvZ,IAAI,EAAE2b,KAAK,GAAG,KAAK,GAAG,KAAK;UAC7C,MAAMyD,eAAe,GAAGhS,QAAQ,CAACgS,eAAe;UAChD,IAAIA,eAAe,CAACC,GAAG,KAAK9F,SAAS,EAAE;YACnC6F,eAAe,CAACC,GAAG,GAAG9F,SAAS;UACnC;UACA,MAAMkC,YAAY,CAACf,mBAAmB,CAACnB,SAAS,CAAC;UACjDM,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC;QAAA,iBAAAyF,EAAA;UAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IACN,CAAC,CAAC;EACN,CAAC;AACL;AAEA,MAAMC,qBAAqB,GAAG,IAAIzvB,cAAc,CAAC,uBAAuB,CAAC;AAEzE,MAAM0vB,aAAa,CAAC;EAChB,OAAO3d,OAAOA,CAACC,OAAO,EAAE;IACpB,OAAO;MACHC,QAAQ,EAAEyd,aAAa;MACvBxd,SAAS,EAAE,CACP;QACIC,OAAO,EAAEsd,qBAAqB;QAC9BvQ,QAAQ,EAAEO;MACd,CAAC,EACDqP,wBAAwB,CAAC9c,OAAO,EAAE+c,kBAAkB,CAAC,EACrDlD,oBAAoB,CAAC7Z,OAAO,EAAE6R,MAAM,CAAC,EACrC7O,aAAa,CAACjD,OAAO,CAACC,OAAO,EAAE2d,YAAY,CAAC,CAACzd,SAAS,EACtDJ,iBAAiB,CAACC,OAAO,CAACC,OAAO,EAAE4d,gBAAgB,CAAC,CAC/C1d,SAAS,EACd+T,eAAe,CAAClU,OAAO,CAACC,OAAO,EAAE6d,cAAc,CAAC,CAAC3d,SAAS,EAC1DqW,mBAAmB,CAACxW,OAAO,CAAC,CAAC,CAACG,SAAS,EACvC4W,uBAAuB,EACvB,GAAGyC,mBAAmB,EACtByD,uBAAuB,CAAChd,OAAO,EAAE8d,sBAAsB,IAAI,IAAI,CAAC;IAExE,CAAC;EACL;EACA;IAAS,IAAI,CAAC/e,IAAI,YAAAgf,sBAAA9e,CAAA;MAAA,YAAAA,CAAA,IAAwFye,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACnd,IAAI,kBAtgD8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAsgDS4iB;IAAa,EAAiD;EAAE;EAC3K;IAAS,IAAI,CAACjd,IAAI,kBAvgD8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAugDkC7Q,YAAY;IAAA,EAAI;EAAE;AAC1J;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KAzgDoG1R,EAAE,CAAA2R,iBAAA,CAygDXie,aAAa,EAAc,CAAC;IAC3G5iB,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCiB,OAAO,EAAE,CAAC7Q,YAAY,EAAE4Z,mBAAmB;IAC/C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsU,eAAe,CAAC;EAClB;IAAS,IAAI,CAACjf,IAAI,YAAAkf,wBAAAhf,CAAA;MAAA,YAAAA,CAAA,IAAwF+e,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAAC5b,IAAI,kBAlhD8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EAkhDJkjB,eAAe;MAAA1b,SAAA;MAAAC,MAAA;QAAA9H,MAAA;MAAA;MAAA+H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAAqoB,yBAAAhsB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlhDbpE,EAAE,CAAAwE,UAAA,IAAAuI,8BAAA,gBAkhDwJ,CAAC;QAAA;QAAA,IAAA3I,EAAA;UAlhD3JpE,EAAE,CAAA2E,UAAA,SAAAN,GAAA,CAAAsI,MAAA,KAAAtI,GAAA,CAAAsI,MAAA,kBAAAtI,GAAA,CAAAsI,MAAA,CAAAC,MAAA,CAkhDqJ,CAAC;QAAA;MAAA;MAAAkI,YAAA,GAAwa/S,EAAE,CAACogB,IAAI,EAA6FpgB,EAAE,CAACsuB,QAAQ,EAA6EtuB,EAAE,CAACuuB,YAAY,EAAqFtc,aAAa;MAAAgB,aAAA;IAAA,EAA6F;EAAE;AAChjC;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAphDoG1R,EAAE,CAAA2R,iBAAA,CAohDXue,eAAe,EAAc,CAAC;IAC7GljB,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,YAAY;MAAEuG,aAAa,EAAEzU,iBAAiB,CAAC0U,IAAI;MAAElN,QAAQ,EAAE;IAAwb,CAAC;EAC/gB,CAAC,CAAC,QAAkB;IAAE4E,MAAM,EAAE,CAAC;MACvBK,IAAI,EAAExM;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+vB,eAAe,CAAC;EAClB;IAAS,IAAI,CAACtf,IAAI,YAAAuf,wBAAArf,CAAA;MAAA,YAAAA,CAAA,IAAwFof,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAAC9d,IAAI,kBA7hD8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EA6hDSujB;IAAe,EAAwG;EAAE;EACpO;IAAS,IAAI,CAAC5d,IAAI,kBA9hD8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GA8hDoC7Q,YAAY,EAAEkT,aAAa;IAAA,EAAI;EAAE;AAC3K;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAhiDoG1R,EAAE,CAAA2R,iBAAA,CAgiDX4e,eAAe,EAAc,CAAC;IAC7GvjB,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACod,eAAe,CAAC;MAC/Brd,OAAO,EAAE,CAAC7Q,YAAY,EAAEkT,aAAa,CAAC;MACtCE,OAAO,EAAE,CAAC8a,eAAe;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMO,kBAAkB,CAAC;EACrB1iB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+B,KAAK,GAAG,IAAI5B,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACwiB,WAAW,GAAG,IAAI,CAAC5gB,KAAK,CAACtB,UAAU,CAAEL,KAAK,IAAKA,KAAK,CAAC;EAC9D;EACAwiB,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,CAAC9gB,KAAK,CAACP,GAAG,CAACqhB,KAAK,CAAC;EACzB;EACA;IAAS,IAAI,CAAC3f,IAAI,YAAA4f,2BAAA1f,CAAA;MAAA,YAAAA,CAAA,IAAwFsf,kBAAkB;IAAA,CAAoD;EAAE;EAClL;IAAS,IAAI,CAACpf,KAAK,kBAljD6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAkjDYkf,kBAAkB;MAAAjf,OAAA,EAAlBif,kBAAkB,CAAAxf,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC7J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApjDoG1R,EAAE,CAAA2R,iBAAA,CAojDX8e,kBAAkB,EAAc,CAAC;IAChHzjB,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMqf,eAAe,CAAC;EAClB/iB,WAAWA,CAACyV,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuN,aAAa,GAAG,IAAI,CAACvN,OAAO,CAACkN,WAAW;EACjD;EACA;IAAS,IAAI,CAACzf,IAAI,YAAA+f,wBAAA7f,CAAA;MAAA,YAAAA,CAAA,IAAwF2f,eAAe,EAhkDzB9wB,EAAE,CAAAqU,iBAAA,CAgkDyCoc,kBAAkB;IAAA,CAA4C;EAAE;EAC3M;IAAS,IAAI,CAACnc,IAAI,kBAjkD8EtU,EAAE,CAAAuU,iBAAA;MAAAvH,IAAA,EAikDJ8jB,eAAe;MAAAtc,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7M,QAAA,WAAAkpB,yBAAA7sB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjkDbpE,EAAE,CAAAwE,UAAA,IAAAmJ,uCAAA,yBAikD2H,CAAC;UAjkD9H3N,EAAE,CAAAyF,MAAA;QAAA;QAAA,IAAArB,EAAA;UAAFpE,EAAE,CAAA2E,UAAA,SAAF3E,EAAE,CAAA6F,WAAA,OAAAxB,GAAA,CAAA0sB,aAAA,CAikDyG,CAAC;QAAA;MAAA;MAAAjc,YAAA,GAA8yB/S,EAAE,CAACsjB,OAAO,EAAmHtjB,EAAE,CAACogB,IAAI,EAA6F1gB,EAAE,CAAC+T,UAAU,EAA+NzT,EAAE,CAACsgB,SAAS;MAAArN,aAAA;IAAA,EAAqB;EAAE;AAC9+C;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAnkDoG1R,EAAE,CAAA2R,iBAAA,CAmkDXmf,eAAe,EAAc,CAAC;IAC7G9jB,IAAI,EAAE1M,SAAS;IACfsR,IAAI,EAAE,CAAC;MAAEnD,QAAQ,EAAE,YAAY;MAAE1G,QAAQ,EAAE;IAAgzB,CAAC;EACh2B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiF,IAAI,EAAEyjB;EAAmB,CAAC,CAAC;AAAA;AAEhE,MAAMS,eAAe,CAAC;EAClB,OAAOjf,OAAOA,CAAA,EAAG;IACb,OAAO;MACHE,QAAQ,EAAE+e,eAAe;MACzB9e,SAAS,EAAE;IACf,CAAC;EACL;EACA;IAAS,IAAI,CAACnB,IAAI,YAAAkgB,wBAAAhgB,CAAA;MAAA,YAAAA,CAAA,IAAwF+f,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACze,IAAI,kBAhlD8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAglDSkkB;IAAe,EAAuG;EAAE;EACnO;IAAS,IAAI,CAACve,IAAI,kBAjlD8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GAilDoC7Q,YAAY,EAAEL,YAAY;IAAA,EAAI;EAAE;AAC1K;AACA;EAAA,QAAA+P,SAAA,oBAAAA,SAAA,KAnlDoG1R,EAAE,CAAA2R,iBAAA,CAmlDXuf,eAAe,EAAc,CAAC;IAC7GlkB,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAACge,eAAe,CAAC;MAC/B1b,OAAO,EAAE,CAAC0b,eAAe,CAAC;MAC1Bje,OAAO,EAAE,CAAC7Q,YAAY,EAAEL,YAAY;IACxC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMyvB,yBAAyB,CAAC;EAC5BrjB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8R,UAAU,GAAG5f,MAAM,CAACuB,UAAU,CAAC;IACpC,IAAI,CAAC6vB,iBAAiB,GAAGpxB,MAAM,CAAC0vB,qBAAqB,CAAC;EAC1D;EACA,IAAI2B,0BAA0BA,CAACjjB,KAAK,EAAE;IAClC,IAAI,CAACgjB,iBAAiB,CAACvR,UAAU,CAACzR,KAAK,CAAC;EAC5C;EACA6R,QAAQA,CAAA,EAAG;IACP,IAAI,CAACmR,iBAAiB,CAACnR,QAAQ,CAAC,CAAC;EACrC;EACAqR,eAAeA,CAAA,EAAG;IACd,IAAI,CAACF,iBAAiB,CAACzR,UAAU,CAAC,IAAI,CAACC,UAAU,CAAC;IAClD,IAAI,CAACwR,iBAAiB,CAAClR,aAAa,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAAClP,IAAI,YAAAugB,kCAAArgB,CAAA;MAAA,YAAAA,CAAA,IAAwFigB,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAAClW,IAAI,kBA5mD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA4mDJokB,yBAAyB;MAAA5c,SAAA;MAAA6S,YAAA,WAAAoK,uCAAArtB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5mDvBpE,EAAE,CAAAsG,UAAA,oBAAAorB,oDAAA;YAAA,OA4mDJrtB,GAAA,CAAA6b,QAAA,CAAS,CAAC;UAAA,UA5mDRlgB,EAAE,CAAA2xB,eA4mDoB,CAAC;QAAA;MAAA;MAAAld,MAAA;QAAA6c,0BAAA;MAAA;MAAAzU,UAAA;IAAA,EAAsM;EAAE;AACnU;AACA;EAAA,QAAAnL,SAAA,oBAAAA,SAAA,KA9mDoG1R,EAAE,CAAA2R,iBAAA,CA8mDXyf,yBAAyB,EAAc,CAAC;IACvHpkB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE,uBAAuB;MACjCoO,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEyU,0BAA0B,EAAE,CAAC;MAC3CtkB,IAAI,EAAExM;IACV,CAAC,CAAC;IAAE0f,QAAQ,EAAE,CAAC;MACXlT,IAAI,EAAE3L,YAAY;MAClBuQ,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMggB,wBAAwB,CAAC;EAC3B7jB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAA4gB,iCAAA1gB,CAAA;MAAA,YAAAA,CAAA,IAAwFygB,wBAAwB,EA/nDlC5xB,EAAE,CAAAqU,iBAAA,CA+nDkDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAChN;IAAS,IAAI,CAACga,IAAI,kBAhoD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAgoDJ4kB,wBAAwB;MAAApd,SAAA;IAAA,EAAgE;EAAE;AAC5L;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KAloDoG1R,EAAE,CAAA2R,iBAAA,CAkoDXigB,wBAAwB,EAAc,CAAC;IACtH5kB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAM4wB,qBAAqB,CAAC;EACxB/jB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAA8gB,8BAAA5gB,CAAA;MAAA,YAAAA,CAAA,IAAwF2gB,qBAAqB,EA7oD/B9xB,EAAE,CAAAqU,iBAAA,CA6oD+CrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC7M;IAAS,IAAI,CAACga,IAAI,kBA9oD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA8oDJ8kB,qBAAqB;MAAAtd,SAAA;IAAA,EAAuD;EAAE;AAChL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KAhpDoG1R,EAAE,CAAA2R,iBAAA,CAgpDXmgB,qBAAqB,EAAc,CAAC;IACnH9kB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAM8wB,8BAA8B,CAAC;EACjCjkB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAghB,uCAAA9gB,CAAA;MAAA,YAAAA,CAAA,IAAwF6gB,8BAA8B,EA3pDxChyB,EAAE,CAAAqU,iBAAA,CA2pDwDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EACtN;IAAS,IAAI,CAACga,IAAI,kBA5pD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA4pDJglB,8BAA8B;MAAAxd,SAAA;IAAA,EAAwE;EAAE;AAC1M;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA9pDoG1R,EAAE,CAAA2R,iBAAA,CA8pDXqgB,8BAA8B,EAAc,CAAC;IAC5HhlB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMgxB,yBAAyB,CAAC;EAC5BnkB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAkhB,kCAAAhhB,CAAA;MAAA,YAAAA,CAAA,IAAwF+gB,yBAAyB,EAzqDnClyB,EAAE,CAAAqU,iBAAA,CAyqDmDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EACjN;IAAS,IAAI,CAACga,IAAI,kBA1qD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA0qDJklB,yBAAyB;MAAA1d,SAAA;IAAA,EAAkE;EAAE;AAC/L;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA5qDoG1R,EAAE,CAAA2R,iBAAA,CA4qDXugB,yBAAyB,EAAc,CAAC;IACvHllB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMkxB,oBAAoB,CAAC;EACvBrkB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAohB,6BAAAlhB,CAAA;MAAA,YAAAA,CAAA,IAAwFihB,oBAAoB,EAvrD9BpyB,EAAE,CAAAqU,iBAAA,CAurD8CrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC5M;IAAS,IAAI,CAACga,IAAI,kBAxrD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAwrDJolB,oBAAoB;MAAA5d,SAAA;IAAA,EAA4D;EAAE;AACpL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA1rDoG1R,EAAE,CAAA2R,iBAAA,CA0rDXygB,oBAAoB,EAAc,CAAC;IAClHplB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMoxB,sBAAsB,CAAC;EACzBvkB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAshB,+BAAAphB,CAAA;MAAA,YAAAA,CAAA,IAAwFmhB,sBAAsB,EArsDhCtyB,EAAE,CAAAqU,iBAAA,CAqsDgDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC9M;IAAS,IAAI,CAACga,IAAI,kBAtsD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAssDJslB,sBAAsB;MAAA9d,SAAA;IAAA,EAA8D;EAAE;AACxL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KAxsDoG1R,EAAE,CAAA2R,iBAAA,CAwsDX2gB,sBAAsB,EAAc,CAAC;IACpHtlB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMsxB,0BAA0B,CAAC;EAC7BzkB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAwhB,mCAAAthB,CAAA;MAAA,YAAAA,CAAA,IAAwFqhB,0BAA0B,EAntDpCxyB,EAAE,CAAAqU,iBAAA,CAmtDoDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAClN;IAAS,IAAI,CAACga,IAAI,kBAptD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAotDJwlB,0BAA0B;MAAAhe,SAAA;IAAA,EAAmE;EAAE;AACjM;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KAttDoG1R,EAAE,CAAA2R,iBAAA,CAstDX6gB,0BAA0B,EAAc,CAAC;IACxHxlB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMwxB,oBAAoB,CAAC;EACvB3kB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAA0hB,6BAAAxhB,CAAA;MAAA,YAAAA,CAAA,IAAwFuhB,oBAAoB,EAjuD9B1yB,EAAE,CAAAqU,iBAAA,CAiuD8CrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC5M;IAAS,IAAI,CAACga,IAAI,kBAluD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAkuDJ0lB,oBAAoB;MAAAle,SAAA;IAAA,EAA4D;EAAE;AACpL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KApuDoG1R,EAAE,CAAA2R,iBAAA,CAouDX+gB,oBAAoB,EAAc,CAAC;IAClH1lB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAM0xB,qBAAqB,CAAC;EACxB7kB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAA4hB,8BAAA1hB,CAAA;MAAA,YAAAA,CAAA,IAAwFyhB,qBAAqB,EA/uD/B5yB,EAAE,CAAAqU,iBAAA,CA+uD+CrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC7M;IAAS,IAAI,CAACga,IAAI,kBAhvD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EAgvDJ4lB,qBAAqB;MAAApe,SAAA;IAAA,EAA6D;EAAE;AACtL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KAlvDoG1R,EAAE,CAAA2R,iBAAA,CAkvDXihB,qBAAqB,EAAc,CAAC;IACnH5lB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAM4xB,qBAAqB,CAAC;EACxB/kB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAA8hB,8BAAA5hB,CAAA;MAAA,YAAAA,CAAA,IAAwF2hB,qBAAqB,EA7vD/B9yB,EAAE,CAAAqU,iBAAA,CA6vD+CrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC7M;IAAS,IAAI,CAACga,IAAI,kBA9vD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA8vDJ8lB,qBAAqB;MAAAte,SAAA;IAAA,EAA6D;EAAE;AACtL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KAhwDoG1R,EAAE,CAAA2R,iBAAA,CAgwDXmhB,qBAAqB,EAAc,CAAC;IACnH9lB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAM8xB,uBAAuB,CAAC;EAC1BjlB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAgiB,gCAAA9hB,CAAA;MAAA,YAAAA,CAAA,IAAwF6hB,uBAAuB,EA3wDjChzB,EAAE,CAAAqU,iBAAA,CA2wDiDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC/M;IAAS,IAAI,CAACga,IAAI,kBA5wD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA4wDJgmB,uBAAuB;MAAAxe,SAAA;IAAA,EAAgE;EAAE;AAC3L;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA9wDoG1R,EAAE,CAAA2R,iBAAA,CA8wDXqhB,uBAAuB,EAAc,CAAC;IACrHhmB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMgyB,sBAAsB,CAAC;EACzBnlB,WAAWA,CAAChG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACkJ,IAAI,YAAAkiB,+BAAAhiB,CAAA;MAAA,YAAAA,CAAA,IAAwF+hB,sBAAsB,EAzxDhClzB,EAAE,CAAAqU,iBAAA,CAyxDgDrU,EAAE,CAACkB,WAAW;IAAA,CAA4C;EAAE;EAC9M;IAAS,IAAI,CAACga,IAAI,kBA1xD8Elb,EAAE,CAAAmb,iBAAA;MAAAnO,IAAA,EA0xDJkmB,sBAAsB;MAAA1e,SAAA;IAAA,EAA8D;EAAE;AACxL;AACA;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA5xDoG1R,EAAE,CAAA2R,iBAAA,CA4xDXuhB,sBAAsB,EAAc,CAAC;IACpHlmB,IAAI,EAAEvM,SAAS;IACfmR,IAAI,EAAE,CAAC;MACCnD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhN,EAAE,CAACkB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMkyB,sBAAsB,GAAG,CAC3BxB,wBAAwB,EACxBE,qBAAqB,EACrBE,8BAA8B,EAC9BE,yBAAyB,EACzBE,oBAAoB,EACpBE,sBAAsB,EACtBjX,kBAAkB,EAClBmX,0BAA0B,EAC1BE,oBAAoB,EACpBE,qBAAqB,EACrBM,sBAAsB,EACtBF,uBAAuB,EACvBF,qBAAqB,CACxB;AACD,MAAMO,YAAY,CAAC;EACf;IAAS,IAAI,CAACpiB,IAAI,YAAAqiB,qBAAAniB,CAAA;MAAA,YAAAA,CAAA,IAAwFkiB,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAAC5gB,IAAI,kBApzD8EzS,EAAE,CAAA0S,gBAAA;MAAA1F,IAAA,EAozDSqmB;IAAY,EAwBtF;EAAE;EACnC;IAAS,IAAI,CAAC1gB,IAAI,kBA70D8E3S,EAAE,CAAA4S,gBAAA;MAAAC,OAAA,GA60DiC7Q,YAAY;IAAA,EAAI;EAAE;AACzJ;AACA;EAAA,QAAA0P,SAAA,oBAAAA,SAAA,KA/0DoG1R,EAAE,CAAA2R,iBAAA,CA+0DX0hB,YAAY,EAAc,CAAC;IAC1GrmB,IAAI,EAAE3M,QAAQ;IACduR,IAAI,EAAE,CAAC;MACCkB,YAAY,EAAE,CAAC,GAAGsgB,sBAAsB,CAAC;MACzCvgB,OAAO,EAAE,CAAC7Q,YAAY,CAAC;MACvBoT,OAAO,EAAE,CAAC,GAAGge,sBAAsB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMG,cAAc,CAAC;EACjBxlB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+B,KAAK,GAAG,IAAI5B,SAAS,CAAC;MAAExF,KAAK,EAAE;IAAG,CAAC,CAAC;IACzC,IAAI,CAAC+d,MAAM,GAAG,IAAI,CAAC3W,KAAK,CAACtB,UAAU,CAAC,CAAC;MAAE9F;IAAM,CAAC,KAAKA,KAAK,CAAC;EAC7D;EACAie,QAAQA,CAACje,KAAK,EAAE;IACZ,IAAI,CAACoH,KAAK,CAACZ,KAAK,CAAC;MAAExG,KAAK,EAAEA,KAAK,CAAC2kB,IAAI,CAACpW,SAAS;IAAE,CAAC,CAAC;EACtD;EACAuc,OAAOA,CAAC1vB,IAAI,EAAE;IACV,IAAI,CAAC6iB,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC7W,KAAK,CAAC3B,KAAK,CAACzF,KAAK,EAAE5E,IAAI,CAAC,CAAC;EACpD;EACA2vB,SAASA,CAACC,MAAM,EAAE5vB,IAAI,EAAE;IACpB,MAAM;MAAE4E;IAAM,CAAC,GAAG,IAAI,CAACoH,KAAK,CAAC3B,KAAK;IAClC,MAAMgI,KAAK,GAAGzN,KAAK,CAAC0N,SAAS,CAAC,CAAC;MAAEpG;IAAG,CAAC,KAAKA,EAAE,KAAK0jB,MAAM,CAAC;IACxD,IAAIvd,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IACA,MAAMwd,WAAW,GAAG,CAAC,GAAGjrB,KAAK,CAAC;IAC9BirB,WAAW,CAACxd,KAAK,CAAC,GAAG;MAAEnG,EAAE,EAAE0jB,MAAM;MAAE,GAAG5vB;IAAK,CAAC;IAC5C,IAAI,CAAC6iB,QAAQ,CAACgN,WAAW,CAAC;EAC9B;EACAnU,UAAUA,CAACxP,EAAE,EAAE;IACX,MAAM;MAAEtH;IAAM,CAAC,GAAG,IAAI,CAACoH,KAAK,CAAC3B,KAAK;IAClC,MAAMgI,KAAK,GAAGzN,KAAK,CAAC0N,SAAS,CAAEtS,IAAI,IAAKA,IAAI,CAACkM,EAAE,KAAKA,EAAE,CAAC;IACvD,IAAImG,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IACA,MAAMwd,WAAW,GAAG,CAAC,GAAGjrB,KAAK,CAAC4N,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAE,GAAGzN,KAAK,CAAC4N,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAAC;IACzE,IAAI,CAACrG,KAAK,CAACZ,KAAK,CAAC;MAAExG,KAAK,EAAEirB;IAAY,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAAC1iB,IAAI,YAAA2iB,uBAAAziB,CAAA;MAAA,YAAAA,CAAA,IAAwFoiB,cAAc;IAAA,CAAoD;EAAE;EAC9K;IAAS,IAAI,CAACliB,KAAK,kBAv3D6ErR,EAAE,CAAAsR,kBAAA;MAAAC,KAAA,EAu3DYgiB,cAAc;MAAA/hB,OAAA,EAAd+hB,cAAc,CAAAtiB,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAz3DoG1R,EAAE,CAAA2R,iBAAA,CAy3DX4hB,cAAc,EAAc,CAAC;IAC5GvmB,IAAI,EAAE7M,UAAU;IAChByR,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASye,eAAe,EAAE9S,WAAW,EAAE/H,kBAAkB,EAAEoS,mBAAmB,EAAEmK,wBAAwB,EAAE9J,8BAA8B,EAAEtB,iBAAiB,EAAEzP,oBAAoB,EAAED,qBAAqB,EAAE+P,qBAAqB,EAAEiL,qBAAqB,EAAEE,8BAA8B,EAAEE,yBAAyB,EAAEhkB,SAAS,EAAEyQ,kBAAkB,EAAEX,uBAAuB,EAAE8S,eAAe,EAAEL,kBAAkB,EAAE2B,oBAAoB,EAAErf,QAAQ,EAAEiB,aAAa,EAAED,iBAAiB,EAAEoL,yBAAyB,EAAED,sBAAsB,EAAE+J,kBAAkB,EAAEhb,YAAY,EAAEkb,sBAAsB,EAAEnS,cAAc,EAAE2Y,qBAAqB,EAAEhB,mCAAmC,EAAEzF,eAAe,EAAEuC,mBAAmB,EAAE3Z,2BAA2B,EAAEC,mBAAmB,EAAEugB,sBAAsB,EAAE1iB,eAAe,EAAEF,yBAAyB,EAAED,qBAAqB,EAAEkG,aAAa,EAAE0F,kBAAkB,EAAEkV,eAAe,EAAE9a,kBAAkB,EAAEgT,mBAAmB,EAAEb,qBAAqB,EAAEgI,aAAa,EAAEsB,eAAe,EAAEhc,aAAa,EAAElD,iBAAiB,EAAEqN,sBAAsB,EAAE8G,eAAe,EAAErY,mBAAmB,EAAE6R,0BAA0B,EAAE8O,mBAAmB,EAAEpQ,wBAAwB,EAAE2H,kBAAkB,EAAEpK,mBAAmB,EAAE4W,0BAA0B,EAAEnP,eAAe,EAAEqP,oBAAoB,EAAEpQ,qBAAqB,EAAEtH,qBAAqB,EAAErC,aAAa,EAAEia,qBAAqB,EAAEla,gBAAgB,EAAE2a,YAAY,EAAEjC,yBAAyB,EAAEnF,sBAAsB,EAAE6B,mBAAmB,EAAE5B,iBAAiB,EAAE3Q,aAAa,EAAEoK,YAAY,EAAEuN,sBAAsB,EAAE9J,YAAY,EAAE9H,kBAAkB,EAAEmE,kBAAkB,EAAEF,gBAAgB,EAAEuN,qBAAqB,EAAES,cAAc,EAAEP,uBAAuB,EAAElS,aAAa,EAAEhE,kBAAkB,EAAEgP,MAAM,EAAEnD,cAAc,EAAEuG,uBAAuB,EAAEtX,cAAc,EAAEoX,wBAAwB,EAAE5D,kBAAkB,EAAEW,oBAAoB,EAAEvD,oBAAoB,EAAErR,qBAAqB,EAAEqB,iBAAiB,EAAEhB,UAAU,EAAEnI,OAAO,EAAEqI,iBAAiB,EAAE0X,qBAAqB,EAAE1D,iBAAiB,EAAEzU,SAAS,EAAEqU,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}