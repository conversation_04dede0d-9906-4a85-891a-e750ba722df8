{"ast": null, "code": "import { LOCAL_STORAGE_KEYS } from '@app/shared/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"angular-oauth2-oidc\";\nexport let LogoutComponent = /*#__PURE__*/(() => {\n  class LogoutComponent {\n    constructor(authService) {\n      if (localStorage) {\n        localStorage.removeItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\n      }\n      authService.revokeTokenAndLogout();\n    }\n    static {\n      this.ɵfac = function LogoutComponent_Factory(t) {\n        return new (t || LogoutComponent)(i0.ɵɵdirectiveInject(i1.OAuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LogoutComponent,\n        selectors: [[\"app-logout\"]],\n        decls: 0,\n        vars: 0,\n        template: function LogoutComponent_Template(rf, ctx) {},\n        encapsulation: 2\n      });\n    }\n  }\n  return LogoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}