{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'sekunddan kam',\n    other: '{{count}} sekunddan kam'\n  },\n  xSeconds: {\n    one: '1 sekund',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: 'yarim minut',\n  lessThanXMinutes: {\n    one: 'bir minutdan kam',\n    other: '{{count}} minutdan kam'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minut'\n  },\n  aboutXHours: {\n    one: 'tahminan 1 soat',\n    other: 'tahminan {{count}} soat'\n  },\n  xHours: {\n    one: '1 soat',\n    other: '{{count}} soat'\n  },\n  xDays: {\n    one: '1 kun',\n    other: '{{count}} kun'\n  },\n  aboutXWeeks: {\n    one: 'tahminan 1 hafta',\n    other: 'tahminan {{count}} hafta'\n  },\n  xWeeks: {\n    one: '1 hafta',\n    other: '{{count}} hafta'\n  },\n  aboutXMonths: {\n    one: 'tahminan 1 oy',\n    other: 'tahminan {{count}} oy'\n  },\n  xMonths: {\n    one: '1 oy',\n    other: '{{count}} oy'\n  },\n  aboutXYears: {\n    one: 'tahminan 1 yil',\n    other: 'tahminan {{count}} yil'\n  },\n  xYears: {\n    one: '1 yil',\n    other: '{{count}} yil'\n  },\n  overXYears: {\n    one: \"1 yildan ko'p\",\n    other: \"{{count}} yildan ko'p\"\n  },\n  almostXYears: {\n    one: 'deyarli 1 yil',\n    other: 'deyarli {{count}} yil'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' dan keyin';\n    } else {\n      return result + ' oldin';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}