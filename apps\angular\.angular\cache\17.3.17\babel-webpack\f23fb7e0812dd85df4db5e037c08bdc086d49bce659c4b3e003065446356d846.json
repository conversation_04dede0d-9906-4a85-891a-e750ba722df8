{"ast": null, "code": "import { EntityAction } from '@abp/ng.components/extensible';\nexport const DEFAULT_SECURITY_LOGS_ENTITY_ACTIONS = EntityAction.createMany([]);", "map": {"version": 3, "names": ["EntityAction", "DEFAULT_SECURITY_LOGS_ENTITY_ACTIONS", "createMany"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\identity\\defaults\\security-logs\\default-security-logs-entity-actions.ts"], "sourcesContent": ["import { IdentitySecurityLogDto } from '@volo/abp.commercial.ng.ui/config';\r\nimport { EntityAction } from '@abp/ng.components/extensible';\r\n\r\nexport const DEFAULT_SECURITY_LOGS_ENTITY_ACTIONS = EntityAction.createMany<IdentitySecurityLogDto>(\r\n  [],\r\n);\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,+BAA+B;AAE5D,OAAO,MAAMC,oCAAoC,GAAGD,YAAY,CAACE,UAAU,CACzE,EAAE,CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}