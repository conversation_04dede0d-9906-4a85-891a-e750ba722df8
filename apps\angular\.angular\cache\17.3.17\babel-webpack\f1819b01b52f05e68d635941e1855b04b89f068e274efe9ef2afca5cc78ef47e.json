{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/tt\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    return 5;\n  }\n  exports.default = ['tt', [['AM', 'PM'], u, u], u, [['Я', 'Д', 'С', 'Ч', 'П', 'Җ', 'Ш'], ['якш.', 'дүш.', 'сиш.', 'чәр.', 'пәнҗ.', 'җом.', 'шим.'], ['якшәмбе', 'дүшәмбе', 'сишәмбе', 'чәршәмбе', 'пәнҗешәмбе', 'җомга', 'шимбә'], ['якш.', 'дүш.', 'сиш.', 'чәр.', 'пәнҗ.', 'җом.', 'шим.']], u, [['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], ['гыйн.', 'фев.', 'мар.', 'апр.', 'май', 'июнь', 'июль', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'], ['гыйнвар', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь']], u, [['б.э.к.', 'милади'], u, ['безнең эрага кадәр', 'безнең эра']], 1, [6, 0], ['dd.MM.y', 'd MMM, y \\'ел\\'', 'd MMMM, y \\'ел\\'', 'd MMMM, y \\'ел\\', EEEE'], ['H:mm', 'H:mm:ss', 'H:mm:ss z', 'H:mm:ss zzzz'], ['{1}, {0}', u, u, u], [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'], 'RUB', '₽', 'Россия сумы', {\n    'JPY': ['JP¥', '¥'],\n    'RUB': ['₽']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/tt.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/tt\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        return 5;\n    }\n    exports.default = [\n        'tt',\n        [['AM', 'PM'], u, u],\n        u,\n        [\n            ['Я', 'Д', 'С', 'Ч', 'П', 'Җ', 'Ш'], ['якш.', 'дүш.', 'сиш.', 'чәр.', 'пәнҗ.', 'җом.', 'шим.'],\n            ['якшәмбе', 'дүшәмбе', 'сишәмбе', 'чәршәмбе', 'пәнҗешәмбе', 'җомга', 'шимбә'],\n            ['якш.', 'дүш.', 'сиш.', 'чәр.', 'пәнҗ.', 'җом.', 'шим.']\n        ],\n        u,\n        [\n            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n            [\n                'гыйн.', 'фев.', 'мар.', 'апр.', 'май', 'июнь', 'июль', 'авг.', 'сент.', 'окт.', 'нояб.',\n                'дек.'\n            ],\n            [\n                'гыйнвар', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь',\n                'октябрь', 'ноябрь', 'декабрь'\n            ]\n        ],\n        u,\n        [['б.э.к.', 'милади'], u, ['безнең эрага кадәр', 'безнең эра']],\n        1,\n        [6, 0],\n        ['dd.MM.y', 'd MMM, y \\'ел\\'', 'd MMMM, y \\'ел\\'', 'd MMMM, y \\'ел\\', EEEE'],\n        ['H:mm', 'H:mm:ss', 'H:mm:ss z', 'H:mm:ss zzzz'],\n        ['{1}, {0}', u, u, u],\n        [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'],\n        'RUB',\n        '₽',\n        'Россия сумы',\n        { 'JPY': ['JP¥', '¥'], 'RUB': ['₽'] },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,4BAA4B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EACzE;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,IAAI,EACJ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EACpBA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAC9F,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,EAC7E,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAC5D,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAC/D,CACI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EACxF,MAAM,CACT,EACD,CACI,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EACnF,SAAS,EAAE,QAAQ,EAAE,SAAS,CACjC,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEA,CAAC,EAAE,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC,EAC/D,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,wBAAwB,CAAC,EAC5E,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EAChD,CAAC,UAAU,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACrB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAC7C,KAAK,EACL,GAAG,EACH,aAAa,EACb;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,GAAG;EAAE,CAAC,EACrC,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}