{"ast": null, "code": "import { getRedirectUrl } from '../../utils/auth-utils';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@volo/abp.ng.account/public/proxy\";\nimport * as i3 from \"@abp/ng.core\";\nexport class LinkLoggedComponent {\n  get linkUser() {\n    const {\n      linkUserId,\n      linkTenantId,\n      linkUserName,\n      linkTenantName\n    } = this.route.snapshot.queryParams;\n    return {\n      linkUserId,\n      linkTenantId,\n      linkUserName,\n      linkTenantName\n    };\n  }\n  constructor(injector, router, route, identityLinkUserService) {\n    this.injector = injector;\n    this.router = router;\n    this.route = route;\n    this.identityLinkUserService = identityLinkUserService;\n    this.tenantAndUserName = '';\n  }\n  ngOnInit() {\n    this.init();\n  }\n  init() {\n    const {\n      linkUserId,\n      linkTenantId,\n      linkUserName,\n      linkTenantName\n    } = this.linkUser;\n    if (!linkUserId) {\n      this.navigateToLogin();\n      return;\n    }\n    this.identityLinkUserService.isLinked({\n      tenantId: linkTenantId,\n      userId: linkUserId\n    }).subscribe(res => {\n      if (!res) {\n        this.navigateToLogin();\n      }\n    });\n    this.tenantAndUserName = linkTenantId ? `${linkTenantName}\\\\${linkUserName}` : linkUserName;\n  }\n  navigateToLogin() {\n    this.router.navigateByUrl('/account/login');\n  }\n  navigateToMainPage(queryParams) {\n    this.router.navigate([getRedirectUrl(this.injector) || '/'], {\n      queryParams\n    });\n  }\n  navigateToMainPageForLinkLogin() {\n    const {\n      linkUserId,\n      linkTenantId\n    } = this.linkUser;\n    this.navigateToMainPage({\n      handler: 'linkLogin',\n      linkUserId,\n      linkTenantId\n    });\n  }\n  static {\n    this.ɵfac = function LinkLoggedComponent_Factory(t) {\n      return new (t || LinkLoggedComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.IdentityLinkUserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LinkLoggedComponent,\n      selectors: [[\"abp-link-logged\"]],\n      decls: 11,\n      vars: 7,\n      consts: [[1, \"d-grid\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"mb-3\", 3, \"click\"], [1, \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"me-1\"]],\n      template: function LinkLoggedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function LinkLoggedComponent_Template_button_click_1_listener() {\n            return ctx.navigateToMainPage();\n          });\n          i0.ɵɵtext(2, \" \\u2190 \");\n          i0.ɵɵelementStart(3, \"span\", 2);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"abpLocalization\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LinkLoggedComponent_Template_button_click_6_listener() {\n            return ctx.navigateToMainPageForLinkLogin();\n          });\n          i0.ɵɵelementStart(7, \"span\", 4);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"abpLocalization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" \\u2192 \");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"AbpAccount::StayWithCurrentAccount\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 4, \"AbpAccount::ReturnToPreviousAccount\", ctx.tenantAndUserName));\n        }\n      },\n      dependencies: [i3.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["getRedirectUrl", "LinkLoggedComponent", "linkUser", "linkUserId", "linkTenantId", "linkUserName", "linkTenantName", "route", "snapshot", "queryParams", "constructor", "injector", "router", "identityLinkUserService", "tenantAndUserName", "ngOnInit", "init", "navigateToLogin", "isLinked", "tenantId", "userId", "subscribe", "res", "navigateByUrl", "navigateToMainPage", "navigate", "navigateToMainPageForLinkLogin", "handler", "i0", "ɵɵdirectiveInject", "Injector", "i1", "Router", "ActivatedRoute", "i2", "IdentityLinkUserService", "selectors", "decls", "vars", "consts", "template", "LinkLoggedComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "LinkLoggedComponent_Template_button_click_1_listener", "ɵɵtext", "ɵɵelementEnd", "LinkLoggedComponent_Template_button_click_6_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵpipeBind2"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\components\\link-logged\\link-logged.component.ts"], "sourcesContent": ["import { Component, Injector, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Params, Router } from '@angular/router';\r\nimport { IdentityLinkUserService } from '@volo/abp.ng.account/public/proxy';\r\nimport { getRedirectUrl } from '../../utils/auth-utils';\r\n\r\n@Component({\r\n  selector: 'abp-link-logged',\r\n  template: `\r\n    <div class=\"d-grid\">\r\n      <button type=\"button\" class=\"btn btn-primary mb-3\" (click)=\"navigateToMainPage()\">\r\n        &larr;\r\n        <span class=\"ms-1\">{{ 'AbpAccount::StayWithCurrentAccount' | abpLocalization }}</span>\r\n      </button>\r\n      <button (click)=\"navigateToMainPageForLinkLogin()\" class=\"btn btn-secondary\" type=\"button\">\r\n        <span class=\"me-1\">{{\r\n          'AbpAccount::ReturnToPreviousAccount' | abpLocalization: tenantAndUserName\r\n        }}</span>\r\n        &rarr;\r\n      </button>\r\n    </div>\r\n  `,\r\n})\r\nexport class LinkLoggedComponent implements OnInit {\r\n  tenantAndUserName = '';\r\n\r\n  get linkUser() {\r\n    const { linkUserId, linkTenantId, linkUserName, linkTenantName } =\r\n      this.route.snapshot.queryParams;\r\n\r\n    return { linkUserId, linkTenantId, linkUserName, linkTenantName };\r\n  }\r\n\r\n  constructor(\r\n    private injector: Injector,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private identityLinkUserService: IdentityLinkUserService,\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.init();\r\n  }\r\n\r\n  protected init() {\r\n    const { linkUserId, linkTenantId, linkUserName, linkTenantName } = this.linkUser;\r\n\r\n    if (!linkUserId) {\r\n      this.navigateToLogin();\r\n      return;\r\n    }\r\n\r\n    this.identityLinkUserService\r\n      .isLinked({ tenantId: linkTenantId, userId: linkUserId })\r\n      .subscribe(res => {\r\n        if (!res) {\r\n          this.navigateToLogin();\r\n        }\r\n      });\r\n\r\n    this.tenantAndUserName = linkTenantId ? `${linkTenantName}\\\\${linkUserName}` : linkUserName;\r\n  }\r\n\r\n  protected navigateToLogin() {\r\n    this.router.navigateByUrl('/account/login');\r\n  }\r\n\r\n  navigateToMainPage(queryParams?: Params) {\r\n    this.router.navigate([getRedirectUrl(this.injector) || '/'], { queryParams });\r\n  }\r\n\r\n  navigateToMainPageForLinkLogin() {\r\n    const { linkUserId, linkTenantId } = this.linkUser;\r\n    this.navigateToMainPage({ handler: 'linkLogin', linkUserId, linkTenantId });\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,cAAc,QAAQ,wBAAwB;;;;;AAmBvD,OAAM,MAAOC,mBAAmB;EAG9B,IAAIC,QAAQA,CAAA;IACV,MAAM;MAAEC,UAAU;MAAEC,YAAY;MAAEC,YAAY;MAAEC;IAAc,CAAE,GAC9D,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,WAAW;IAEjC,OAAO;MAAEN,UAAU;MAAEC,YAAY;MAAEC,YAAY;MAAEC;IAAc,CAAE;EACnE;EAEAI,YACUC,QAAkB,EAClBC,MAAc,EACdL,KAAqB,EACrBM,uBAAgD;IAHhD,KAAAF,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAM,uBAAuB,GAAvBA,uBAAuB;IAbjC,KAAAC,iBAAiB,GAAG,EAAE;EAcnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,EAAE;EACb;EAEUA,IAAIA,CAAA;IACZ,MAAM;MAAEb,UAAU;MAAEC,YAAY;MAAEC,YAAY;MAAEC;IAAc,CAAE,GAAG,IAAI,CAACJ,QAAQ;IAEhF,IAAI,CAACC,UAAU,EAAE;MACf,IAAI,CAACc,eAAe,EAAE;MACtB;IACF;IAEA,IAAI,CAACJ,uBAAuB,CACzBK,QAAQ,CAAC;MAAEC,QAAQ,EAAEf,YAAY;MAAEgB,MAAM,EAAEjB;IAAU,CAAE,CAAC,CACxDkB,SAAS,CAACC,GAAG,IAAG;MACf,IAAI,CAACA,GAAG,EAAE;QACR,IAAI,CAACL,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;IAEJ,IAAI,CAACH,iBAAiB,GAAGV,YAAY,GAAG,GAAGE,cAAc,KAAKD,YAAY,EAAE,GAAGA,YAAY;EAC7F;EAEUY,eAAeA,CAAA;IACvB,IAAI,CAACL,MAAM,CAACW,aAAa,CAAC,gBAAgB,CAAC;EAC7C;EAEAC,kBAAkBA,CAACf,WAAoB;IACrC,IAAI,CAACG,MAAM,CAACa,QAAQ,CAAC,CAACzB,cAAc,CAAC,IAAI,CAACW,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE;MAAEF;IAAW,CAAE,CAAC;EAC/E;EAEAiB,8BAA8BA,CAAA;IAC5B,MAAM;MAAEvB,UAAU;MAAEC;IAAY,CAAE,GAAG,IAAI,CAACF,QAAQ;IAClD,IAAI,CAACsB,kBAAkB,CAAC;MAAEG,OAAO,EAAE,WAAW;MAAExB,UAAU;MAAEC;IAAY,CAAE,CAAC;EAC7E;;;uBAnDWH,mBAAmB,EAAA2B,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,QAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAE,cAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,uBAAA;IAAA;EAAA;;;YAAnBlC,mBAAmB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAb1Bd,EADF,CAAAgB,cAAA,aAAoB,gBACgE;UAA/BhB,EAAA,CAAAiB,UAAA,mBAAAC,qDAAA;YAAA,OAASH,GAAA,CAAAnB,kBAAA,EAAoB;UAAA,EAAC;UAC/EI,EAAA,CAAAmB,MAAA,eACA;UAAAnB,EAAA,CAAAgB,cAAA,cAAmB;UAAAhB,EAAA,CAAAmB,MAAA,GAA4D;;UACjFnB,EADiF,CAAAoB,YAAA,EAAO,EAC/E;UACTpB,EAAA,CAAAgB,cAAA,gBAA2F;UAAnFhB,EAAA,CAAAiB,UAAA,mBAAAI,qDAAA;YAAA,OAASN,GAAA,CAAAjB,8BAAA,EAAgC;UAAA,EAAC;UAChDE,EAAA,CAAAgB,cAAA,cAAmB;UAAAhB,EAAA,CAAAmB,MAAA,GAEjB;;UAAAnB,EAAA,CAAAoB,YAAA,EAAO;UACTpB,EAAA,CAAAmB,MAAA,gBACF;UACFnB,EADE,CAAAoB,YAAA,EAAS,EACL;;;UARiBpB,EAAA,CAAAsB,SAAA,GAA4D;UAA5DtB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAwB,WAAA,6CAA4D;UAG5DxB,EAAA,CAAAsB,SAAA,GAEjB;UAFiBtB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAyB,WAAA,8CAAAV,GAAA,CAAA7B,iBAAA,EAEjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}