{"ast": null, "code": "import { InformationExchangeStatus } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\nimport { AppComponentBase } from '../../../../app-component-base';\nimport { BdoTableColumnType, BdoTableData } from '../../../../shared/components/bdo-table/bdo-table.model';\nimport { ExchangeReasonDic, InformationExchangeStatusDic, CTSUploadStatusDic, CTSUploadExchangeReasonDic } from '../../../../shared/constants';\nimport Swal from 'sweetalert2';\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/shared/constants/cts-package-request';\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i3 from \"../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges\";\nimport * as i4 from \"@abp/ng.core\";\nimport * as i5 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service\";\nimport * as i8 from \"../../../../shared/services/upload-file.service\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i11 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/button\";\nimport * as i16 from \"@angular/material/select\";\nimport * as i17 from \"@angular/material/core\";\nimport * as i18 from \"@angular/material/tabs\";\nimport * as i19 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i20 from \"@angular/common\";\nconst _c0 = () => [10, 20, 50, 100];\nfunction InformationExchangeMainComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"CA Certificate expired at \", i0.ɵɵpipeBind2(2, 1, ctx_r0.certificateExpirationDate, \"dd/MM/yyyy\"), \"\");\n  }\n}\nfunction InformationExchangeMainComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCaCertificateDialog());\n    });\n    i0.ɵɵtext(1, \" Update CA Certificate \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(0));\n    });\n    i0.ɵɵtext(1, \" Non-compliance XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(1));\n    });\n    i0.ɵɵtext(1, \" High Risk IP XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(2));\n    });\n    i0.ɵɵtext(1, \" Non-resident XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(3));\n    });\n    i0.ɵɵtext(1, \" Other Cases XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r7, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r8.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r8.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r9, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r10.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r10.description, \"\");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r11.code2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r11.name, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r12.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r12.description, \"\");\n  }\n}\nexport class InformationExchangeMainComponent extends AppComponentBase {\n  constructor(injector, router, informationExchangeService, informationExchangeDetailService, permissionService, dashboardService, dialog, certificateService, fileUploadService, toasterService, countryService, ctsPackageRequestService) {\n    super(injector);\n    this.router = router;\n    this.informationExchangeService = informationExchangeService;\n    this.informationExchangeDetailService = informationExchangeDetailService;\n    this.permissionService = permissionService;\n    this.dashboardService = dashboardService;\n    this.dialog = dialog;\n    this.certificateService = certificateService;\n    this.fileUploadService = fileUploadService;\n    this.toasterService = toasterService;\n    this.countryService = countryService;\n    this.ctsPackageRequestService = ctsPackageRequestService;\n    this.input = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: 'ExchangeReason asc',\n      informationExchangeStatus: InformationExchangeStatus.None,\n      entityName: '',\n      year: ''\n    };\n    this.TableId = 'information_ex-results';\n    /* Work for pagination. Default value = 0, it is rendering first page by default. */\n    this.currentPageIndex = 0;\n    /** It is string year number array. */\n    this.year = [];\n    /** Selected year from Financial Period End Years dropdown, default is current year. */\n    this.selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\n    this.exchangeResultColumns = [{\n      columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Exchange Reason'\n    }, {\n      columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'RA Name'\n    }, {\n      columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n      type: BdoTableColumnType.String,\n      minWidth: 100,\n      isSortable: true,\n      columnName: 'Entity Name'\n    }, {\n      columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Incop#/Formation#'\n    }, {\n      columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n      type: BdoTableColumnType.Date,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Financial Period End Date'\n    }, {\n      columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n      type: BdoTableColumnType.Date,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Due Date'\n    }, {\n      columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Information Exchange Status'\n    }, {\n      columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'View Declaration'\n    }, {\n      columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'XML Data'\n    }, {\n      columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'View History'\n    }];\n    /** Page size setting for \"TableId\" grid */\n    this.PageSize = 10;\n    this.exchangeInformationResultRecords = [];\n    this.selectReportStatus = InformationExchangeStatus.None;\n    this.informationExchangedDic = InformationExchangeStatusDic;\n    this.TableIdS = 'information_ex_summary';\n    this.currentPageIndexS = 0;\n    this.summaryExchangeColumns = [{\n      columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports'\n    }, {\n      columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Exchanged'\n    }, {\n      columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Ready for Exchange'\n    }, {\n      columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n      type: BdoTableColumnType.Number,\n      minWidth: 200,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)'\n    }, {\n      columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Not Started'\n    }];\n    /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\n    this.PageSizeS = 10;\n    this.totalRecords = 0;\n    /** Note: Only logon user with permission \"Generate XML\"\n     * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\n    this.showButton = true;\n    this.showOtherCase = true;\n    /* Default current year value as string. */\n    this.currnetYear = new Date().getFullYear().toString();\n    this.certificateExpirationDate = null;\n    this.isCaSystemAdmin = false;\n    // Dashboard columns for CTS Upload & Transmission\n    this.ctsDashboardColumns = [{\n      columnId: 'totalNotUploaded',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Do Not Upload'\n    }, {\n      columnId: 'totalReadyForUpload',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Ready For Upload'\n    }, {\n      columnId: 'totalFailedUpload',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Failed in Upload'\n    }, {\n      columnId: 'totalUploadedToCTS',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Uploaded to CTS'\n    }, {\n      columnId: 'totalNotEnrolled',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Receiving Country Not Enrolled'\n    }];\n    // Dashboard data for CTS Upload & Transmission\n    this.ctsDashboardList = [{\n      id: 1,\n      totalNotUploaded: 0,\n      totalReadyForUpload: 1,\n      totalFailedUpload: 1,\n      totalUploadedToCTS: 2,\n      totalNotEnrolled: 2\n    }];\n    // Grid columns for CTS Upload & Transmission\n    this.ctsUploadColumns = [{\n      columnId: 'exchangeReason',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Exchange Reason'\n    }, {\n      columnId: 'dataPacket',\n      type: BdoTableColumnType.Link,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'Data Packet'\n    }, {\n      columnId: 'fileCreationDate',\n      type: BdoTableColumnType.Date,\n      minWidth: 120,\n      isSortable: true,\n      columnName: 'File Creation Date'\n    }, {\n      columnId: 'receivingCountry',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'Receiving Country'\n    }, {\n      columnId: 'ctsUploadStatus',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'CTS Upload Status'\n    }, {\n      columnId: 'uploadedAt',\n      type: BdoTableColumnType.Date,\n      minWidth: 120,\n      isSortable: true,\n      columnName: 'Uploaded At'\n    }, {\n      columnId: 'ctsTransmissionStatus',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'CTS Transmission Status'\n    }, {\n      columnId: 'viewExchangeRecords',\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'View Exchange Records'\n    }, {\n      columnId: 'viewComments',\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'View Comments'\n    }, {\n      columnId: 'regeneratePacket',\n      type: BdoTableColumnType.Actions,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'Regenerate Packet'\n    }, {\n      columnId: 'ctsUpload',\n      type: BdoTableColumnType.Actions,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'CTS Upload'\n    }, {\n      columnId: 'excludeFromCtsUpload',\n      type: BdoTableColumnType.Checkbox,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'Exclude From CTS Upload'\n    }];\n    // CTS Upload & Transmission Dashboard\n    this.ctsUploadExchangeReasonDic = CTSUploadExchangeReasonDic;\n    this.ctsUploadStatusDic = CTSUploadStatusDic;\n    this.ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \n    this.ctsUploadResultRecords = [];\n    this.selectExchangeReason = -1;\n    this.selectCtsUploadStatus = CTSUploadStatus.NotStarted;\n    this.selectReceivingCountry = '';\n    // Table IDs and page settings\n    this.ctsDashboardTableId = 'cts_dashboard';\n    this.ctsUploadTableId = 'cts_upload_grid';\n    this.ctsUploadPageSize = 10;\n    this.ctsUploadCurrentPage = 0;\n    this.ctsUploadTotalRecords = 0;\n    this.ctsUploadInput = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: 'ExchangeReason asc',\n      ctsUploadStatus: null,\n      exchangeReason: null,\n      financialEndYear: '',\n      receivingCountry: ''\n    };\n    this.summaryExchangeList = [{\n      id: 1,\n      totalNoReport: 100,\n      totalNoReportSent: 10,\n      totalNoReportRExchange: 5,\n      totalNoReportRReview: 2,\n      totalNoReportNotSent: 5\n    }];\n  }\n  ngOnInit() {\n    this.getFiscalYears().subscribe(response => {\n      if (response && response.length > 0) {\n        this.year = [];\n        response.forEach(element => {\n          this.year.push(element.toString());\n        });\n      }\n      ;\n    });\n    if (localStorage.getItem('selectedYear')) {\n      this.selectedYear = localStorage.getItem('selectedYear') ?? this.currnetYear;\n    }\n    if (localStorage.getItem('selectReportStatus')) {\n      this.selectReportStatus = Number(localStorage.getItem('selectReportStatus'));\n    }\n    this.informationExchangeDetailService.standardMonitoringFromYear().subscribe(response => {\n      this.standardMonitoringYear = response;\n      this.IsShowOtherCase(this.selectedYear);\n    });\n    this.onLazyLoadEvent(undefined);\n    this.onLazyLoadEventS(undefined);\n    this.showButton = this.checkUserPermission();\n    // CTS Upload & Transmission Dashboard\n    // Check CA System Admin role\n    const currentUser = this.configState.getOne('currentUser');\n    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');\n    if (localStorage.getItem('ctsUploadSelectedYear')) {\n      this.ctsUploadSelectedYear = localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\n    }\n    if (localStorage.getItem('selectExchangeReason')) {\n      this.selectExchangeReason = Number(localStorage.getItem('selectExchangeReason'));\n    }\n    if (localStorage.getItem('selectCtsUploadStatus')) {\n      this.selectCtsUploadStatus = Number(localStorage.getItem('selectCtsUploadStatus'));\n    }\n    if (localStorage.getItem('selectReceivingCountry')) {\n      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\n    }\n    // Fetch certificate expiration date\n    this.getBahamasCertificateInfo();\n    this.getCountries();\n    this.setCtsDashboardTableData();\n    this.onCtsUploadLazyLoadEvent(undefined);\n    this.onCtsDashboardLazyLoadEvent(undefined);\n  }\n  IsShowOtherCase(year) {\n    const selectedYearAsInt = parseInt(year, 10);\n    const standardMonitoringYearAsInt = parseInt(this.standardMonitoringYear, 10);\n    this.showOtherCase = selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\n  }\n  /** Lazy load event works for \"TableIds\" grid only. */\n  onLazyLoadEventS(event) {\n    this.currentPageIndexS = 0;\n    this.informationExchangeService.getSummaryByYearByYear(this.selectedYear).subscribe(response => {\n      this.summaryExchangeList = response;\n      setTimeout(() => {\n        this.setTableDataS();\n      }, 200);\n    });\n  }\n  /** Lazy load event works for grid \"TableId\" only. */\n  onLazyLoadEvent(event) {\n    if (event) {\n      if (this.PageSize === (event.pageSize ?? 10)) {\n        this.currentPageIndex = event.pageNumber ?? 0;\n      } else {\n        //\n        // if Page size got changed through pagination control,\n        // need to reset current page index to 0.\n        //\n        this.PageSize = event.pageSize ?? 10;\n        this.currentPageIndex = 0;\n      }\n      this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n      this.input.maxResultCount = this.PageSize ?? 10;\n      if (event.isAscending === false) {\n        this.input.sorting = `${event.sortField} desc`;\n      } else {\n        this.input.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.currentPageIndex = 0;\n      this.PageSize = 10;\n      this.input.informationExchangeStatus = this.selectReportStatus;\n      this.input.year = this.selectedYear;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n    }\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      if (response) {\n        this.totalRecords = response.totalCount;\n        this.exchangeInformationResultRecords = response.items;\n      } else {\n        this.totalRecords = 0;\n        this.exchangeInformationResultRecords = [];\n      }\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  setTableDataS() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableIdS;\n    tableData.totalRecords = 1;\n    tableData.data = this.summaryExchangeList.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n          value: x.totalNoofReports\n        }, {\n          columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n          value: x.totalNoofExchangedReports\n        }, {\n          columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n          value: x.totalNoofReadyExchangedReports\n        }, {\n          columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n          value: x.totalNoofReviewReports\n        }, {\n          columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n          value: x.totalNotSentReports\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 10);\n  }\n  getExchangeReasonDescription(input) {\n    const foundStatus = ExchangeReasonDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  getInformationExchangeStatusDescription(input) {\n    const foundStatus = InformationExchangeStatusDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  base64ToUint8Array(x) {\n    const raw = atob(x);\n    var rawLength = raw.length;\n    var array = new Uint8Array(new ArrayBuffer(rawLength));\n    for (let i = 0; i < rawLength; i++) {\n      array[i] = raw.charCodeAt(i);\n    }\n    return array;\n  }\n  downloadFile(content, name) {\n    var file = new Blob([this.base64ToUint8Array(content)]);\n    var fileURL = window.URL.createObjectURL(file);\n    var element = document.createElement('a');\n    document.body.appendChild(element);\n    element.style.display = 'none';\n    element.href = fileURL;\n    element.download = name;\n    element.click();\n    element.remove();\n  }\n  GenerateXMlByType(exchangeType) {\n    this.informationExchangeService.getXMLFilesFilterByExchangeTypeByReasonAndYear(exchangeType, this.selectedYear).subscribe(result => {\n      this.onLazyLoadEvent(undefined);\n      if (result.fileName != '') {\n        this.downloadFile(result.content.toString(), result.fileName);\n      } else Swal.fire({\n        icon: 'info',\n        title: 'XML Import',\n        text: 'No data to export.',\n        allowOutsideClick: false\n      });\n    });\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableId;\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.exchangeInformationResultRecords.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n          value: this.getExchangeReasonDescription(x.exchangeReason)\n        }, {\n          columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n          value: x.raCode\n        }, {\n          columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n          value: x.entityName\n        }, {\n          columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n          value: x.companyFormationNumber\n        }, {\n          columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n          value: x.fiscalEndDate\n        }, {\n          columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n          value: x.dueDate\n        }, {\n          columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n          value: this.getInformationExchangeStatusDescription(x.informationExchangeStatus)\n        }, {\n          columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n\n          /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\n          value: x.isMigrated === false ? 'view' : ''\n        }, {\n          columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n          value: 'XML Data'\n        }, {\n          columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n          value: x.hasHistoryRecord ? 'View History' : ''\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 100);\n  }\n  onYearChange(ob) {\n    this.selectedYear = ob.value;\n    this.IsShowOtherCase(this.selectedYear);\n    // Keep the selected Year in local storage.\n    localStorage.setItem('selectedYear', ob.value);\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.onLazyLoadEventS(undefined);\n  }\n  onSearch() {\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.onLazyLoadEventS(undefined);\n  }\n  onReportChange(ob) {\n    this.selectReportStatus = Number(ob.value);\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    // Keep the selected Report Status in local storage.\n    localStorage.setItem('selectReportStatus', ob.value);\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  onLinkClick(event) {\n    const data = event.rawData;\n    if (event.columnId === \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */) {\n      //\n      // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\n      // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\n      //\n      this.router.navigate(['/es-info-exchange/exchangedetail'], {\n        //\n        // Passed \"Id\" of table dbo.InformationExchanges.\n        //\n        queryParams: {\n          id: data.id,\n          ishistory: false\n        }\n      });\n    } else if (event.columnId === \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */) {\n      //\n      // When click the \"view\" link button in the Information Exchange records grid.\n      // Route to CaActionPageComponent.ts component\n      //\n      this.router.navigate(['/action-page'], {\n        queryParams: {\n          declarationid: data.declarationId,\n          entityid: data.corporateEntityId,\n          from: 'info-exchange'\n        }\n      });\n    } else if (event.columnId === \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */) {\n      //\n      // Open dialog to show history records. informantion-exchange-history.component.ts\n      //\n      this.openInformationExchangeHistoryDialog(data.id);\n    }\n  }\n  openInformationExchangeHistoryDialog(informationExchangeId) {\n    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\n      height: '750px',\n      width: '1200px',\n      data: {\n        informationExchangeId: informationExchangeId\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      console.log('The dialog was closed', result);\n    });\n  }\n  /** Check if logon user has permission \"Generate XML\".\n   *  Work for show/hide three xml buttons.\n   */\n  checkUserPermission() {\n    let result = false;\n    // Get current logon user object.\n    const currentUser = this.configState.getOne('currentUser');\n    if (currentUser) {\n      result = this.permissionService.getGrantedPolicy(\"DashboardService.Dashboard.GenerateXML\" /* Permissions.DASHBOARD_GENERATE_XML */);\n    }\n    return result;\n  }\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\n  getFiscalYears() {\n    return this.dashboardService.getFiscalYears().pipe();\n  }\n  // CTS Upload & Transmission Dashboard Methods\n  getCTSUploadStatusDescription(input) {\n    const foundStatus = CTSUploadStatusDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  getReceivingCountryName(input) {\n    const foundCountry = this.countries.find(status => status.code2 === input);\n    if (foundCountry) return foundCountry.name;\n    return '';\n  }\n  getBahamasCertificateInfo() {\n    return this.certificateService.getBahamasCertificateInfo().subscribe({\n      next: info => {\n        this.certificateExpirationDate = info?.expiredAt || null;\n      },\n      error: () => {\n        this.certificateExpirationDate = null;\n      }\n    });\n  }\n  openUpdateCaCertificateDialog() {\n    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\n      width: '400px',\n      data: {}\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.file) {\n        const formData = new FormData();\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n        // Only call upload if file is present        \n        this.fileUploadService.uploadBahamasCertificate(formData, result.password).subscribe({\n          next: response => {\n            if (response) {\n              // Fetch certificate expiration date\n              this.getBahamasCertificateInfo();\n              this.toasterService.success('Bahamas Certificate successfully uploaded', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, {\n                life: 7000\n              });\n            }\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error uploading Bahamas certificate:', error);\n          }\n        });\n      }\n    });\n  }\n  getCountries() {\n    this.countryService.getList({\n      sorting: \"name asc\",\n      maxResultCount: 1000\n    }).subscribe(response => {\n      this.countries = response.items;\n      // Remove code2 with empty string and null values in countries\n      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');\n      // add new country ALL in countries \n      this.countries.unshift({\n        name: 'All',\n        code2: ''\n      });\n    });\n  }\n  // CTS Upload & Transmission Dashboard Methods\n  setCtsDashboardTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.ctsDashboardTableId;\n    tableData.totalRecords = 1;\n    tableData.data = this.ctsDashboardList.map(x => ({\n      id: x.id,\n      rawData: x,\n      cells: [{\n        columnId: 'totalNotUploaded',\n        value: x.totalNotUploaded\n      }, {\n        columnId: 'totalReadyForUpload',\n        value: x.totalReadyForUpload\n      }, {\n        columnId: 'totalFailedUpload',\n        value: x.totalFailedUpload\n      }, {\n        columnId: 'totalUploadedToCTS',\n        value: x.totalUploadedToCTS\n      }, {\n        columnId: 'totalNotEnrolled',\n        value: x.totalNotEnrolled\n      }]\n    }));\n    setTimeout(() => this.tableService.setGridData(tableData), 10);\n  }\n  setCtsUploadTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.ctsUploadTableId;\n    tableData.totalRecords = this.ctsUploadTotalRecords;\n    tableData.data = this.ctsUploadResultRecords.map(x => ({\n      id: x.id,\n      rawData: x,\n      cells: [{\n        columnId: 'exchangeReason',\n        value: this.getExchangeReasonDescription(x.exchangeReason)\n      }, {\n        columnId: 'dataPacket',\n        value: x.dataPacket\n      }, {\n        columnId: 'fileCreationDate',\n        value: x.fileCreationDate\n      }, {\n        columnId: 'receivingCountry',\n        value: this.getReceivingCountryName(x.receivingCountry)\n      }, {\n        columnId: 'ctsUploadStatus',\n        value: this.getCTSUploadStatusDescription(x.ctsUploadStatus)\n      }, {\n        columnId: 'uploadedAt',\n        value: x.uploadedAt\n      }, {\n        columnId: 'ctsTransmissionStatus',\n        value: x.ctsTransmissionStatus\n      }, {\n        columnId: 'viewExchangeRecords',\n        value: x.viewExchangeRecords === false ? 'View' : ''\n      }, {\n        columnId: 'viewComments',\n        value: x.viewComments.length > 0 ? 'View Comments' : ''\n      }, {\n        columnId: 'regeneratePacket',\n        value: x.regeneratePacket\n      }, {\n        columnId: 'ctsUpload',\n        value: x.ctsUpload\n      }, {\n        columnId: 'excludeFromCtsUpload',\n        value: x.excludeFromCtsUpload\n      }]\n    }));\n    setTimeout(() => this.tableService.setGridData(tableData), 100);\n  }\n  onCtsDashboardLazyLoadEvent(event) {\n    this.currentPageIndexS = 0;\n    this.ctsPackageRequestService.getSummaryByYearByYear(this.ctsUploadSelectedYear).subscribe(response => {\n      this.ctsDashboardList = response;\n      setTimeout(() => {\n        this.setCtsDashboardTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadLazyLoadEvent(event) {\n    if (event) {\n      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\n        this.ctsUploadCurrentPage = event.pageNumber ?? 0;\n      } else {\n        //\n        // if Page size got changed through pagination control,\n        // need to reset current page index to 0.\n        //\n        this.ctsUploadPageSize = event.pageSize ?? 10;\n        this.ctsUploadCurrentPage = 0;\n      }\n      this.ctsUploadInput.skipCount = (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\n      if (event.isAscending === false) {\n        this.ctsUploadInput.sorting = `${event.sortField} desc`;\n      } else {\n        this.ctsUploadInput.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.ctsUploadCurrentPage = 0;\n      this.ctsUploadPageSize = 10;\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n      this.ctsUploadInput.skipCount = 0;\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    }\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      if (response) {\n        this.ctsUploadTotalRecords = response.totalCount;\n        this.ctsUploadResultRecords = response.items;\n      } else {\n        this.ctsUploadTotalRecords = 0;\n        this.ctsUploadResultRecords = [];\n      }\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadYearChange(ob) {\n    this.ctsUploadSelectedYear = ob.value;\n    localStorage.setItem('ctsUploadSelectedYear', ob.value);\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n    this.ctsUploadInput.skipCount = 0;\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      this.ctsUploadTotalRecords = response.totalCount;\n      this.ctsUploadResultRecords = response.items;\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n    this.onCtsDashboardLazyLoadEvent(undefined);\n  }\n  onCtsUploadSearch() {\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n    this.ctsUploadInput.skipCount = 0;\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      this.ctsUploadTotalRecords = response.totalCount;\n      this.ctsUploadResultRecords = response.items;\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadExchangeReasonChange(ob) {\n    this.selectExchangeReason = Number(ob.value);\n    // Keep the selected Exchange Reason in local storage.\n    localStorage.setItem('selectExchangeReason', ob.value);\n  }\n  onCtsUploadStatusChange(ob) {\n    this.selectCtsUploadStatus = Number(ob.value);\n    // Keep the selected Upload Status in local storage.\n    localStorage.setItem('selectCtsUploadStatus', ob.value);\n  }\n  onCtsUploadReceivingCountryChange(ob) {\n    this.selectReceivingCountry = ob.value;\n    // Keep the selected Receiving Country in local storage.\n    localStorage.setItem('selectReceivingCountry', ob.value);\n  }\n  onCtsUploadLinkClick(event) {\n    if (event.columnId === 'viewExchangeRecords') {\n      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {\n        width: '1200px',\n        data: {\n          row: event.rawData\n        },\n        panelClass: 'associated-exchange-dialog'\n      });\n    }\n  }\n  static {\n    this.ɵfac = function InformationExchangeMainComponent_Factory(t) {\n      return new (t || InformationExchangeMainComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.InformationExchangeService), i0.ɵɵdirectiveInject(i3.InformationExchangeDetailsService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i5.CADashboardContorllerService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.CertificateService), i0.ɵɵdirectiveInject(i8.FileUploadService), i0.ɵɵdirectiveInject(i9.ToasterService), i0.ɵɵdirectiveInject(i10.CountryService), i0.ɵɵdirectiveInject(i11.CtsPackageRequestService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InformationExchangeMainComponent,\n      selectors: [[\"app-information-exchange-main\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 82,\n      vars: 61,\n      consts: [[\"label\", \"Info Exchange Readiness\"], [1, \"top-action-row-exchange\", \"row\"], [1, \"table-container\"], [\"scrollHeight\", \"100%\", \"defaultSortColumnId\", \"uploadedDateTime\", 3, \"onLazyLoad\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\", \"pageSizeOptions\"], [1, \"top-action-column-exchange\", \"row\", \"justify-content-end\"], [1, \"col-md-auto\"], [\"class\", \"certificate-text\", 4, \"ngIf\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", 3, \"click\", 4, \"ngIf\"], [1, \"top-action-column-exchange\", \"row\"], [1, \"col-md-3\", \"col-sm-12\", \"margin-top\"], [1, \"outside-mat-label\"], [1, \"form-field-reduce-length\"], [\"placeholder\", \"Financial Period End\", 3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"Report Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"matInput\", \"\", \"placeholder\", \"File Name\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\", \"col-sm-12\", \"margin-top\", \"search-button-column\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"search-button\", 3, \"click\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"label\", \"CTS Upload & Transmission\"], [1, \"row\", \"top-action-row-exchange\"], [\"scrollHeight\", \"auto\", 3, \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\"], [1, \"col-md-5\", \"col-sm-12\", \"margin-top\"], [1, \"col-md-2\", \"col-sm-12\", \"margin-top\"], [\"placeholder\", \"Exchange Reason\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"Receiving Country\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"CTS Upload Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [1, \"col-md-1\", \"col-sm-12\", \"search-button-column\", \"justify-content-center\"], [1, \"certificate-text\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [3, \"value\"]],\n      template: function InformationExchangeMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-tab-group\")(1, \"mat-tab\", 0)(2, \"div\", 1)(3, \"div\", 2)(4, \"bdo-table\", 3);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener($event) {\n            return ctx.onLazyLoadEventS($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵtemplate(7, InformationExchangeMainComponent_span_7_Template, 3, 4, \"span\", 6)(8, InformationExchangeMainComponent_button_8_Template, 2, 0, \"button\", 7)(9, InformationExchangeMainComponent_button_9_Template, 2, 0, \"button\", 7)(10, InformationExchangeMainComponent_button_10_Template, 2, 0, \"button\", 7)(11, InformationExchangeMainComponent_button_11_Template, 2, 0, \"button\", 7)(12, InformationExchangeMainComponent_button_12_Template, 2, 0, \"button\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"mat-label\", 10);\n          i0.ɵɵtext(16, \"Financial Period End \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"mat-form-field\", 11)(18, \"mat-select\", 12);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedYear, $event) || (ctx.selectedYear = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_18_listener($event) {\n            return ctx.onYearChange($event);\n          });\n          i0.ɵɵtemplate(19, InformationExchangeMainComponent_mat_option_19_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 9)(21, \"mat-label\", 10);\n          i0.ɵɵtext(22, \"Report Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"mat-form-field\", 11)(24, \"mat-select\", 14);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_24_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectReportStatus, $event) || (ctx.selectReportStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_24_listener($event) {\n            return ctx.onReportChange($event);\n          });\n          i0.ɵɵtemplate(25, InformationExchangeMainComponent_mat_option_25_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 9)(27, \"mat-label\", 10);\n          i0.ɵɵtext(28, \"Entity Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-form-field\", 11)(30, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function InformationExchangeMainComponent_Template_input_ngModelChange_30_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchEntityName, $event) || (ctx.searchEntityName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 16)(32, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_32_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵtext(33, \" Search \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 1)(35, \"div\", 2)(36, \"bdo-table\", 18);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_36_listener($event) {\n            return ctx.onLazyLoadEvent($event);\n          })(\"onLinkClick\", function InformationExchangeMainComponent_Template_bdo_table_onLinkClick_36_listener($event) {\n            return ctx.onLinkClick($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"mat-tab\", 19)(38, \"div\", 20)(39, \"div\", 2);\n          i0.ɵɵelement(40, \"bdo-table\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 4)(42, \"div\", 5)(43, \"button\", 22);\n          i0.ɵɵtext(44, \"Decrypt Received Data Packet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 22);\n          i0.ɵɵtext(46, \"Upload Historical XML\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 22);\n          i0.ɵɵtext(48, \"CTS Upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 22);\n          i0.ɵɵtext(50, \"Refresh Status\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 8)(52, \"div\", 23)(53, \"mat-label\", 10);\n          i0.ɵɵtext(54, \"Financial Period End Year\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"mat-form-field\", 11)(56, \"mat-select\", 12);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_56_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.ctsUploadSelectedYear, $event) || (ctx.ctsUploadSelectedYear = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_56_listener($event) {\n            return ctx.onCtsUploadYearChange($event);\n          });\n          i0.ɵɵtemplate(57, InformationExchangeMainComponent_mat_option_57_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 24)(59, \"mat-label\", 10);\n          i0.ɵɵtext(60, \"Exchange Reason\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 11)(62, \"mat-select\", 25);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_62_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectExchangeReason, $event) || (ctx.selectExchangeReason = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_62_listener($event) {\n            return ctx.onCtsUploadExchangeReasonChange($event);\n          });\n          i0.ɵɵtemplate(63, InformationExchangeMainComponent_mat_option_63_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"div\", 24)(65, \"mat-label\", 10);\n          i0.ɵɵtext(66, \"Receiving Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"mat-form-field\", 11)(68, \"mat-select\", 26);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_68_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectReceivingCountry, $event) || (ctx.selectReceivingCountry = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_68_listener($event) {\n            return ctx.onCtsUploadReceivingCountryChange($event);\n          });\n          i0.ɵɵtemplate(69, InformationExchangeMainComponent_mat_option_69_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 24)(71, \"mat-label\", 10);\n          i0.ɵɵtext(72, \"CTS Upload Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"mat-form-field\", 11)(74, \"mat-select\", 27);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_74_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectCtsUploadStatus, $event) || (ctx.selectCtsUploadStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_74_listener($event) {\n            return ctx.onCtsUploadStatusChange($event);\n          });\n          i0.ɵɵtemplate(75, InformationExchangeMainComponent_mat_option_75_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"div\", 28)(77, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_77_listener() {\n            return ctx.onCtsUploadSearch();\n          });\n          i0.ɵɵtext(78, \"Search\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\", 1)(80, \"div\", 2)(81, \"bdo-table\", 18);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_81_listener($event) {\n            return ctx.onCtsUploadLazyLoadEvent($event);\n          })(\"onLinkClick\", function InformationExchangeMainComponent_Template_bdo_table_onLinkClick_81_listener($event) {\n            return ctx.onCtsUploadLinkClick($event);\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"id\", ctx.TableIdS)(\"columns\", ctx.summaryExchangeColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndexS)(\"pageSize\", ctx.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", true)(\"lazyLoad\", true)(\"pageSizeOptions\", i0.ɵɵpureFunction0(58, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.certificateExpirationDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCaSystemAdmin);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton && ctx.showOtherCase);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedYear);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.year);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectReportStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.informationExchangedDic);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchEntityName);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.exchangeResultColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSize\", ctx.PageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(59, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"id\", ctx.ctsDashboardTableId)(\"columns\", ctx.ctsDashboardColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndexS)(\"pageSize\", ctx.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", false)(\"lazyLoad\", false);\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.ctsUploadSelectedYear);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.year);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectExchangeReason);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.ctsUploadExchangeReasonDic);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectReceivingCountry);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectCtsUploadStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.ctsUploadStatusDic);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"id\", ctx.ctsUploadTableId)(\"columns\", ctx.ctsUploadColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.ctsUploadCurrentPage)(\"pageSize\", ctx.ctsUploadPageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(60, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n        }\n      },\n      dependencies: [i12.DefaultValueAccessor, i12.NgControlStatus, i13.MatInput, i14.MatFormField, i14.MatLabel, i15.MatButton, i16.MatSelect, i17.MatOption, i18.MatTab, i18.MatTabGroup, i19.BdoTableComponent, i20.NgForOf, i20.NgIf, i12.NgModel, i20.DatePipe],\n      styles: [\".search-title[_ngcontent-%COMP%] {\\n  font-size: 2em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.display-flex-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100% !important;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  z-index: 0;\\n  position: relative;\\n  margin-right: 0.5em;\\n  min-height: 100% !important;\\n  max-width: 100% !important;\\n}\\n\\n.display-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.top-action-row-exchange[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  padding-top: 10px;\\n}\\n\\n.top-action-row-exchange-noalign[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n\\n.top-action-column-exchange[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: flex-start;\\n  margin-top: 1em;\\n}\\n\\n.margin-top[_ngcontent-%COMP%] {\\n  margin-top: 1.5em;\\n}\\n\\n.margin-left-push[_ngcontent-%COMP%] {\\n  margin-left: 30em;\\n}\\n\\n.margin-left[_ngcontent-%COMP%] {\\n  margin-left: 2em;\\n}\\n\\n.top-action-row-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.upload-import-container[_ngcontent-%COMP%] {\\n  display: flex; \\n\\n}\\n\\n.outside-mat-label[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n  margin-right: 10px;\\n  margin-top: 2em;\\n}\\n\\n.form-field-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.mat-form-field[_ngcontent-%COMP%] {\\n  margin-right: 10px; \\n\\n}\\n\\n.search-button-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  vertical-align: middle;\\n  height: 100px !important;\\n}\\n\\n@media (max-width: 1750px) {\\n  .outside-mat-label[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n    margin-right: 10px;\\n    margin-top: 0em;\\n  }\\n  .search-button[_ngcontent-%COMP%] {\\n    margin-top: 1.9em;\\n  }\\n}\\n@media (max-width: 900px) {\\n  .outside-mat-label[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n    margin-right: 10px;\\n  }\\n  .search-button[_ngcontent-%COMP%] {\\n    margin-top: 0em;\\n  }\\n}\\n.certificate-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  margin-right: 15px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["InformationExchangeStatus", "AppComponentBase", "BdoTableColumnType", "BdoTableData", "ExchangeReasonDic", "InformationExchangeStatusDic", "CTSUploadStatusDic", "CTSUploadExchangeReasonDic", "<PERSON><PERSON>", "InformationExchangeHistoryComponent", "UpdateCaCertificateDialogComponent", "CTSUploadStatus", "ViewAssociatedExchangeRecordsComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r0", "certificateExpirationDate", "ɵɵlistener", "InformationExchangeMainComponent_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "openUpdateCaCertificateDialog", "InformationExchangeMainComponent_button_9_Template_button_click_0_listener", "_r3", "GenerateXMlByType", "InformationExchangeMainComponent_button_10_Template_button_click_0_listener", "_r4", "InformationExchangeMainComponent_button_11_Template_button_click_0_listener", "_r5", "InformationExchangeMainComponent_button_12_Template_button_click_0_listener", "_r6", "ɵɵproperty", "element_r7", "element_r8", "value", "description", "element_r9", "element_r10", "element_r11", "code2", "name", "element_r12", "InformationExchangeMainComponent", "constructor", "injector", "router", "informationExchangeService", "informationExchangeDetailService", "permissionService", "dashboardService", "dialog", "certificateService", "fileUploadService", "toasterService", "countryService", "ctsPackageRequestService", "input", "maxResultCount", "skip<PERSON><PERSON>nt", "sorting", "informationExchangeStatus", "None", "entityName", "year", "TableId", "currentPageIndex", "selected<PERSON>ear", "Date", "getFullYear", "toString", "exchangeResultColumns", "columnId", "type", "String", "min<PERSON><PERSON><PERSON>", "frozenLeft", "isSortable", "columnName", "Link", "PageSize", "exchangeInformationResultRecords", "selectReportStatus", "informationExchangedDic", "TableIdS", "currentPageIndexS", "summaryExchangeColumns", "Number", "PageSizeS", "totalRecords", "showButton", "showOtherCase", "currnetYear", "isCaSystemAdmin", "ctsDashboardColumns", "ctsDashboardList", "id", "totalNotUploaded", "totalReadyForUpload", "totalFailedUpload", "totalUploadedToCTS", "totalNotEnrolled", "ctsUploadColumns", "Actions", "Checkbox", "ctsUploadExchangeReasonDic", "ctsUploadStatusDic", "ctsUploadSelectedYear", "ctsUploadResultRecords", "selectExchangeReason", "selectCtsUploadStatus", "NotStarted", "selectReceivingCountry", "ctsDashboardTableId", "ctsUploadTableId", "ctsUploadPageSize", "ctsUploadCurrentPage", "ctsUploadTotalRecords", "ctsUploadInput", "ctsUploadStatus", "exchangeReason", "financialEndYear", "receivingCountry", "summaryExchangeList", "totalNoReport", "totalNoReportSent", "totalNoReportRExchange", "totalNoReportRReview", "totalNoReportNotSent", "ngOnInit", "getFiscalYears", "subscribe", "response", "length", "for<PERSON>ach", "element", "push", "localStorage", "getItem", "standardMonitoringFromYear", "standardMonitoringYear", "IsShowOtherCase", "onLazyLoadEvent", "undefined", "onLazyLoadEventS", "checkUserPermission", "currentUser", "configState", "getOne", "roles", "includes", "getBahamasCertificateInfo", "getCountries", "setCtsDashboardTableData", "onCtsUploadLazyLoadEvent", "onCtsDashboardLazyLoadEvent", "selectedYearAsInt", "parseInt", "standardMonitoringYearAsInt", "event", "getSummaryByYearByYear", "setTimeout", "setTableDataS", "pageSize", "pageNumber", "isAscending", "sortField", "getALLInformationExchangeByInput", "totalCount", "items", "setTableData", "tableData", "resetToFirstPage", "tableId", "data", "map", "x", "rawData", "cells", "totalNoofReports", "totalNoofExchangedReports", "totalNoofReadyExchangedReports", "totalNoofReviewReports", "totalNotSentReports", "tableService", "setGridData", "getExchangeReasonDescription", "found<PERSON><PERSON><PERSON>", "find", "status", "getInformationExchangeStatusDescription", "base64ToUint8Array", "raw", "atob", "<PERSON><PERSON><PERSON><PERSON>", "array", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "charCodeAt", "downloadFile", "content", "file", "Blob", "fileURL", "window", "URL", "createObjectURL", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "style", "display", "href", "download", "click", "remove", "exchangeType", "getXMLFilesFilterByExchangeTypeByReasonAndYear", "result", "fileName", "fire", "icon", "title", "text", "allowOutsideClick", "raCode", "companyFormationNumber", "fiscalEndDate", "dueDate", "isMigrated", "hasHistoryRecord", "onYearChange", "ob", "setItem", "searchEntityName", "onSearch", "onReportChange", "onLinkClick", "navigate", "queryParams", "ishistory", "declarationid", "declarationId", "entityid", "corporateEntityId", "from", "openInformationExchangeHistoryDialog", "informationExchangeId", "dialogRef", "open", "height", "width", "afterClosed", "console", "log", "getGrantedPolicy", "pipe", "getCTSUploadStatusDescription", "getReceivingCountryName", "foundCountry", "countries", "next", "info", "expiredAt", "error", "formData", "FormData", "append", "uploadBahamasCertificate", "password", "success", "life", "warn", "getList", "filter", "country", "trim", "unshift", "setCtsUploadTableData", "dataPacket", "fileCreationDate", "uploadedAt", "ctsTransmissionStatus", "viewExchangeRecords", "viewComments", "regeneratePacket", "ctsUpload", "excludeFromCtsUpload", "getAllCtsPackageRequestByInput", "onCtsUploadYearChange", "onCtsUploadSearch", "onCtsUploadExchangeReasonChange", "onCtsUploadStatusChange", "onCtsUploadReceivingCountryChange", "onCtsUploadLinkClick", "row", "panelClass", "ɵɵdirectiveInject", "Injector", "i1", "Router", "i2", "InformationExchangeService", "i3", "InformationExchangeDetailsService", "i4", "PermissionService", "i5", "CADashboardContorllerService", "i6", "MatDialog", "i7", "CertificateService", "i8", "FileUploadService", "i9", "ToasterService", "i10", "CountryService", "i11", "CtsPackageRequestService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "InformationExchangeMainComponent_Template", "rf", "ctx", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener", "$event", "ɵɵtemplate", "InformationExchangeMainComponent_span_7_Template", "InformationExchangeMainComponent_button_8_Template", "InformationExchangeMainComponent_button_9_Template", "InformationExchangeMainComponent_button_10_Template", "InformationExchangeMainComponent_button_11_Template", "InformationExchangeMainComponent_button_12_Template", "ɵɵtwoWayListener", "InformationExchangeMainComponent_Template_mat_select_valueChange_18_listener", "ɵɵtwoWayBindingSet", "InformationExchangeMainComponent_Template_mat_select_selectionChange_18_listener", "InformationExchangeMainComponent_mat_option_19_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_24_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_24_listener", "InformationExchangeMainComponent_mat_option_25_Template", "InformationExchangeMainComponent_Template_input_ngModelChange_30_listener", "InformationExchangeMainComponent_Template_button_click_32_listener", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_36_listener", "InformationExchangeMainComponent_Template_bdo_table_onLinkClick_36_listener", "ɵɵelement", "InformationExchangeMainComponent_Template_mat_select_valueChange_56_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_56_listener", "InformationExchangeMainComponent_mat_option_57_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_62_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_62_listener", "InformationExchangeMainComponent_mat_option_63_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_68_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_68_listener", "InformationExchangeMainComponent_mat_option_69_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_74_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_74_listener", "InformationExchangeMainComponent_mat_option_75_Template", "InformationExchangeMainComponent_Template_button_click_77_listener", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_81_listener", "InformationExchangeMainComponent_Template_bdo_table_onLinkClick_81_listener", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\information-exchange-main\\information-exchange-main.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\information-exchange-main\\information-exchange-main.component.html"], "sourcesContent": ["import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { InformationExchangeService } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers';\r\nimport {\r\n  GetInformationExchangeDto,\r\n  InformationExchangeDto,\r\n} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/information-exchange-dashboard/dto';\r\nimport {\r\n  ExchangeReason,\r\n  InformationExchangeStatus,\r\n} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\r\nimport { AppComponentBase } from '../../../../app-component-base';\r\nimport {\r\n  BdoTableCellLinkClickEvent,\r\n  BdoTableColumnDefinition,\r\n  BdoTableColumnType,\r\n  BdoTableData,\r\n} from '../../../../shared/components/bdo-table/bdo-table.model';\r\nimport {\r\n  ExchangeReasonDic,\r\n  ExchangeSummaryTableColumns,\r\n  InformationExchangeStatusDic,\r\n  InformationExchangeTableColumns,\r\n  Permissions,\r\n  CTSUploadStatusDic,\r\n  CTSUploadExchangeReasonDic,\r\n} from '../../../../shared/constants';\r\nimport Swal from 'sweetalert2';\r\nimport { CurrentUserDto, PermissionService } from '@abp/ng.core';\r\nimport { InformationExchangeDetailsService } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\r\nimport { CADashboardContorllerService } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers';\r\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\r\nimport { CertificateService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service';\r\nimport { FileUploadService } from '../../../../shared/services/upload-file.service';\r\nimport { ToasterService } from '@abp/ng.theme.shared';\r\nimport { CountryService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { GetCtsPackageRequestDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/application/contracts/cts-package-request';\r\nimport { CtsPackageRequestService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service';\r\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/shared/constants/cts-package-request';\r\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\r\n\r\n@Component({\r\n  selector: 'app-information-exchange-main',\r\n  templateUrl: './information-exchange-main.component.html',\r\n  styleUrls: ['./information-exchange-main.component.scss']\r\n})\r\nexport class InformationExchangeMainComponent\r\n  extends AppComponentBase\r\n  implements OnInit {\r\n  input: GetInformationExchangeDto = {\r\n    maxResultCount: 10,\r\n    skipCount: 0,\r\n    sorting: 'ExchangeReason asc',\r\n    informationExchangeStatus: InformationExchangeStatus.None,\r\n    entityName: '',\r\n    year: '',\r\n  };\r\n  TableId = 'information_ex-results';\r\n  /* Work for pagination. Default value = 0, it is rendering first page by default. */\r\n  currentPageIndex = 0;\r\n  InformationExchange: InformationExchangeDto[];\r\n  /** It is string year number array. */\r\n  year = [];\r\n  /** Selected year from Financial Period End Years dropdown, default is current year. */\r\n  selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\r\n  exchangeResultColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: InformationExchangeTableColumns.EXCHANGE_REASON,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Exchange Reason',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.RA_CODE,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'RA Name',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.ENTITY_NAME,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 100,\r\n      isSortable: true,\r\n      columnName: 'Entity Name',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.INCROP_NUMBER,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Incop#/Formation#',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Financial Period End Date',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.DUE_DATE,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Due Date',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Information Exchange Status',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.VIEW_DECLARATION,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'View Declaration',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.XML_DATA,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'XML Data',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.VIEW_HISTORY,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'View History',\r\n    },\r\n  ];\r\n  /** Page size setting for \"TableId\" grid */\r\n  PageSize = 10;\r\n  exchangeInformationResultRecords = [];\r\n  selectReportStatus: number = InformationExchangeStatus.None; \r\n  searchEntityName: any;\r\n  informationExchangedDic: any = InformationExchangeStatusDic;\r\n  TableIdS = 'information_ex_summary';\r\n  currentPageIndexS = 0;\r\n  summaryExchangeColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Exchanged',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Ready for Exchange',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 200,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName:\r\n        'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Not Started',\r\n    },\r\n  ];\r\n  /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\r\n  PageSizeS = 10;\r\n  totalRecords = 0;\r\n  /** Note: Only logon user with permission \"Generate XML\"\r\n   * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\r\n  showButton = true;\r\n  showOtherCase = true;\r\n  standardMonitoringYear: any;\r\n  /* Default current year value as string. */\r\n  currnetYear = new Date().getFullYear().toString();\r\n\r\n  certificateExpirationDate: string | null = null;\r\n  isCaSystemAdmin: boolean = false;\r\n  \r\n  // Dashboard columns for CTS Upload & Transmission\r\n  ctsDashboardColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: 'totalNotUploaded',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Do Not Upload',\r\n    },\r\n    {\r\n      columnId: 'totalReadyForUpload',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Ready For Upload',\r\n    },\r\n    {\r\n      columnId: 'totalFailedUpload',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Failed in Upload',\r\n    },\r\n    {\r\n      columnId: 'totalUploadedToCTS',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Uploaded to CTS',\r\n    },\r\n    {\r\n      columnId: 'totalNotEnrolled',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Receiving Country Not Enrolled',\r\n    },\r\n  ];\r\n\r\n  // Dashboard data for CTS Upload & Transmission\r\n  ctsDashboardList: any[] = [\r\n    {  \r\n      id: 1,    \r\n      totalNotUploaded: 0,\r\n      totalReadyForUpload: 1,\r\n      totalFailedUpload: 1,\r\n      totalUploadedToCTS: 2,\r\n      totalNotEnrolled: 2,\r\n    },\r\n  ];\r\n\r\n  // Grid columns for CTS Upload & Transmission\r\n  ctsUploadColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: 'exchangeReason',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Exchange Reason',\r\n    },\r\n    {\r\n      columnId: 'dataPacket',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'Data Packet',\r\n    },\r\n    {\r\n      columnId: 'fileCreationDate',\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 120,\r\n      isSortable: true,\r\n      columnName: 'File Creation Date',\r\n    },\r\n    {\r\n      columnId: 'receivingCountry',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'Receiving Country',\r\n    },\r\n    {\r\n      columnId: 'ctsUploadStatus',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'CTS Upload Status',\r\n    },\r\n    {\r\n      columnId: 'uploadedAt',\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 120,\r\n      isSortable: true,\r\n      columnName: 'Uploaded At',\r\n    },\r\n    {\r\n      columnId: 'ctsTransmissionStatus',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'CTS Transmission Status',\r\n    },\r\n    {\r\n      columnId: 'viewExchangeRecords',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'View Exchange Records',\r\n    },\r\n    {\r\n      columnId: 'viewComments',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'View Comments',\r\n    },\r\n    {\r\n      columnId: 'regeneratePacket',\r\n      type: BdoTableColumnType.Actions,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'Regenerate Packet',\r\n    },\r\n    {\r\n      columnId: 'ctsUpload',\r\n      type: BdoTableColumnType.Actions,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'CTS Upload',\r\n    },\r\n    {\r\n      columnId: 'excludeFromCtsUpload',\r\n      type: BdoTableColumnType.Checkbox,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'Exclude From CTS Upload',\r\n    },\r\n  ];   \r\n  \r\n  // CTS Upload & Transmission Dashboard\r\n  ctsUploadExchangeReasonDic: any = CTSUploadExchangeReasonDic;\r\n  ctsUploadStatusDic: any = CTSUploadStatusDic;\r\n  countries: any;\r\n  ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \r\n  ctsUploadResultRecords = [];\r\n  selectExchangeReason: number = -1;\r\n  selectCtsUploadStatus: number = CTSUploadStatus.NotStarted;\r\n  selectReceivingCountry: string = '';\r\n\r\n  // Table IDs and page settings\r\n  ctsDashboardTableId = 'cts_dashboard';\r\n  ctsUploadTableId = 'cts_upload_grid';\r\n  ctsUploadPageSize = 10;\r\n  ctsUploadCurrentPage = 0;\r\n  ctsUploadTotalRecords = 0;\r\n  ctsUploadInput: GetCtsPackageRequestDto = {\r\n    maxResultCount: 10,\r\n    skipCount: 0,\r\n    sorting: 'ExchangeReason asc',\r\n    ctsUploadStatus: null,\r\n    exchangeReason: null,\r\n    financialEndYear: '',\r\n    receivingCountry: '',\r\n  };\r\n\r\n  constructor(\r\n    injector: Injector,\r\n    private router: Router,\r\n    private informationExchangeService: InformationExchangeService,\r\n    private informationExchangeDetailService: InformationExchangeDetailsService,\r\n    private permissionService: PermissionService,\r\n    private dashboardService: CADashboardContorllerService,\r\n    public dialog: MatDialog,\r\n    private certificateService: CertificateService,\r\n    private fileUploadService: FileUploadService,\r\n    private toasterService: ToasterService,\r\n    private countryService: CountryService,\r\n    private ctsPackageRequestService: CtsPackageRequestService,\r\n  ) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getFiscalYears().subscribe((response) => {\r\n      if (response && response.length > 0) {\r\n        this.year = [];\r\n        response.forEach((element) => {\r\n          this.year.push(element.toString());\r\n        });\r\n      };\r\n    });\r\n\r\n    if (localStorage.getItem('selectedYear')) {\r\n      this.selectedYear =\r\n        localStorage.getItem('selectedYear') ?? this.currnetYear;\r\n    }\r\n\r\n    if (localStorage.getItem('selectReportStatus')) {\r\n      this.selectReportStatus = Number(\r\n        localStorage.getItem('selectReportStatus')\r\n      );\r\n    }\r\n    this.informationExchangeDetailService\r\n      .standardMonitoringFromYear()\r\n      .subscribe((response) => {\r\n        this.standardMonitoringYear = response;\r\n\r\n        this.IsShowOtherCase(this.selectedYear);\r\n      });\r\n\r\n    this.onLazyLoadEvent(undefined);\r\n    this.onLazyLoadEventS(undefined);\r\n\r\n    this.showButton = this.checkUserPermission();   \r\n\r\n    // CTS Upload & Transmission Dashboard\r\n    // Check CA System Admin role\r\n    const currentUser = this.configState.getOne('currentUser') as CurrentUserDto;\r\n    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');    \r\n     \r\n    if (localStorage.getItem('ctsUploadSelectedYear')) {\r\n      this.ctsUploadSelectedYear =\r\n        localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\r\n    }\r\n\r\n    if (localStorage.getItem('selectExchangeReason')) {\r\n      this.selectExchangeReason = Number(\r\n        localStorage.getItem('selectExchangeReason')\r\n      );\r\n    }\r\n\r\n    if (localStorage.getItem('selectCtsUploadStatus')) {\r\n      this.selectCtsUploadStatus = Number(\r\n        localStorage.getItem('selectCtsUploadStatus')\r\n      );\r\n    }\r\n\r\n    if (localStorage.getItem('selectReceivingCountry')) {\r\n      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\r\n    }\r\n\r\n    // Fetch certificate expiration date\r\n    this.getBahamasCertificateInfo();\r\n    this.getCountries();\r\n    this.setCtsDashboardTableData();    \r\n    this.onCtsUploadLazyLoadEvent(undefined);\r\n    this.onCtsDashboardLazyLoadEvent(undefined);\r\n  }\r\n\r\n  IsShowOtherCase(year: string) {\r\n    const selectedYearAsInt: number = parseInt(year, 10);\r\n    const standardMonitoringYearAsInt: number = parseInt(\r\n      this.standardMonitoringYear,\r\n      10\r\n    );\r\n    this.showOtherCase =\r\n      selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\r\n  }\r\n\r\n  /** Lazy load event works for \"TableIds\" grid only. */\r\n  onLazyLoadEventS(event): void {\r\n    this.currentPageIndexS = 0;\r\n    this.informationExchangeService\r\n      .getSummaryByYearByYear(this.selectedYear)\r\n      .subscribe((response) => {\r\n        this.summaryExchangeList = response;\r\n        setTimeout(() => {\r\n          this.setTableDataS();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  /** Lazy load event works for grid \"TableId\" only. */\r\n  onLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.PageSize === (event.pageSize ?? 10)) {\r\n        this.currentPageIndex = event.pageNumber ?? 0;\r\n      } else {\r\n        //\r\n        // if Page size got changed through pagination control,\r\n        // need to reset current page index to 0.\r\n        //\r\n        this.PageSize = event.pageSize ?? 10;\r\n        this.currentPageIndex = 0;\r\n      }\r\n\r\n      this.input.skipCount =\r\n        (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\r\n\r\n      this.input.maxResultCount = this.PageSize ?? 10;\r\n\r\n      if (event.isAscending === false) {\r\n        this.input.sorting = `${event.sortField} desc`;\r\n      } else {\r\n        this.input.sorting = `${event.sortField} asc`;\r\n      }\r\n    } else {\r\n      this.currentPageIndex = 0;\r\n      this.PageSize = 10;\r\n      this.input.informationExchangeStatus = this.selectReportStatus;\r\n      this.input.year = this.selectedYear;\r\n      this.input.skipCount = 0;\r\n      this.input.maxResultCount = this.PageSize;\r\n    }\r\n\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.totalRecords = response.totalCount;\r\n          this.exchangeInformationResultRecords = response.items;\r\n        } else {\r\n          this.totalRecords = 0;\r\n          this.exchangeInformationResultRecords = [];\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  summaryExchangeList: any[] = [\r\n    {\r\n      id: 1,\r\n      totalNoReport: 100,\r\n      totalNoReportSent: 10,\r\n      totalNoReportRExchange: 5,\r\n      totalNoReportRReview: 2,\r\n      totalNoReportNotSent: 5,\r\n    },\r\n  ];\r\n\r\n  setTableDataS(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableIdS;\r\n    tableData.totalRecords = 1;\r\n    tableData.data = this.summaryExchangeList.map((x) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,\r\n            value: x.totalNoofReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,\r\n            value: x.totalNoofExchangedReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,\r\n            value: x.totalNoofReadyExchangedReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,\r\n            value: x.totalNoofReviewReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,\r\n            value: x.totalNotSentReports,\r\n          },\r\n        ],\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 10);\r\n  }\r\n\r\n  getExchangeReasonDescription(input: any) {\r\n    const foundStatus = ExchangeReasonDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  getInformationExchangeStatusDescription(input: any) {\r\n    const foundStatus = InformationExchangeStatusDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  base64ToUint8Array(x: string) {\r\n    const raw = atob(x);\r\n    var rawLength = raw.length;\r\n    var array = new Uint8Array(new ArrayBuffer(rawLength));\r\n\r\n    for (let i = 0; i < rawLength; i++) {\r\n      array[i] = raw.charCodeAt(i);\r\n    }\r\n    return array;\r\n  }\r\n\r\n  downloadFile(content: string, name: string) {\r\n    var file = new Blob([this.base64ToUint8Array(content)]);\r\n    var fileURL = window.URL.createObjectURL(file);\r\n\r\n    var element = document.createElement('a');\r\n    document.body.appendChild(element);\r\n    element.style.display = 'none';\r\n    element.href = fileURL;\r\n    element.download = name;\r\n    element.click();\r\n    element.remove();\r\n  }\r\n\r\n  GenerateXMlByType(exchangeType: ExchangeReason) {\r\n    this.informationExchangeService\r\n      .getXMLFilesFilterByExchangeTypeByReasonAndYear(\r\n        exchangeType,\r\n        this.selectedYear\r\n      )\r\n      .subscribe((result) => {\r\n        this.onLazyLoadEvent(undefined);\r\n        if (result.fileName != '') {\r\n          this.downloadFile(result.content.toString(), result.fileName);\r\n        } else\r\n          Swal.fire({\r\n            icon: 'info',\r\n            title: 'XML Import',\r\n            text: 'No data to export.',\r\n            allowOutsideClick: false,\r\n          });\r\n      });\r\n  }\r\n\r\n  setTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableId;\r\n    tableData.totalRecords = this.totalRecords;\r\n    tableData.data = this.exchangeInformationResultRecords.map((x) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: InformationExchangeTableColumns.EXCHANGE_REASON,\r\n            value: this.getExchangeReasonDescription(x.exchangeReason),\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.RA_CODE,\r\n            value: x.raCode,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.ENTITY_NAME,\r\n            value: x.entityName,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.INCROP_NUMBER,\r\n            value: x.companyFormationNumber,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,\r\n            value: x.fiscalEndDate,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.DUE_DATE,\r\n            value: x.dueDate,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,\r\n            value: this.getInformationExchangeStatusDescription(\r\n              x.informationExchangeStatus\r\n            ),\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.VIEW_DECLARATION,\r\n            /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\r\n            value: x.isMigrated === false ? 'view' : '',\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.XML_DATA,\r\n            value: 'XML Data',\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.VIEW_HISTORY,\r\n            value: x.hasHistoryRecord ? 'View History' : '',\r\n          },\r\n        ],\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 100);\r\n  }\r\n\r\n  onYearChange(ob) {\r\n    this.selectedYear = ob.value;\r\n    this.IsShowOtherCase(this.selectedYear);\r\n    // Keep the selected Year in local storage.\r\n    localStorage.setItem('selectedYear', ob.value);\r\n\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onLazyLoadEventS(undefined);\r\n  }\r\n\r\n  onSearch() {\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onLazyLoadEventS(undefined);\r\n  }\r\n\r\n  onReportChange(ob) {\r\n    this.selectReportStatus = Number(ob.value);\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n\r\n    // Keep the selected Report Status in local storage.\r\n    localStorage.setItem('selectReportStatus', ob.value);\r\n\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onLinkClick(event: BdoTableCellLinkClickEvent) {\r\n    const data = event.rawData as InformationExchangeDto;\r\n    if (event.columnId === InformationExchangeTableColumns.XML_DATA) {\r\n      //\r\n      // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\r\n      // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\r\n      //\r\n      this.router.navigate(['/es-info-exchange/exchangedetail'], {\r\n        //\r\n        // Passed \"Id\" of table dbo.InformationExchanges.\r\n        //\r\n        queryParams: { id: data.id, ishistory: false },\r\n      });\r\n    } else if (\r\n      event.columnId === InformationExchangeTableColumns.VIEW_DECLARATION\r\n    ) {\r\n      //\r\n      // When click the \"view\" link button in the Information Exchange records grid.\r\n      // Route to CaActionPageComponent.ts component\r\n      //\r\n      this.router.navigate(['/action-page'], {\r\n        queryParams: {\r\n          declarationid: data.declarationId,\r\n          entityid: data.corporateEntityId,\r\n          from: 'info-exchange',\r\n        },\r\n      });\r\n    } else if (\r\n      event.columnId === InformationExchangeTableColumns.VIEW_HISTORY\r\n    ) {\r\n      //\r\n      // Open dialog to show history records. informantion-exchange-history.component.ts\r\n      //\r\n      this.openInformationExchangeHistoryDialog(data.id);\r\n    }\r\n  }\r\n\r\n  openInformationExchangeHistoryDialog(informationExchangeId: string) {\r\n    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\r\n      height: '750px',\r\n      width: '1200px',\r\n      data: { informationExchangeId: informationExchangeId },\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      console.log('The dialog was closed', result);\r\n    });\r\n  }\r\n\r\n  /** Check if logon user has permission \"Generate XML\".\r\n   *  Work for show/hide three xml buttons.\r\n   */\r\n  checkUserPermission() {\r\n    let result = false;\r\n    // Get current logon user object.\r\n    const currentUser = this.configState.getOne(\r\n      'currentUser'\r\n    ) as CurrentUserDto;\r\n\r\n    if (currentUser) {\r\n      result = this.permissionService.getGrantedPolicy(\r\n        Permissions.DASHBOARD_GENERATE_XML\r\n      );\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\r\n  getFiscalYears() {\r\n    return this.dashboardService.getFiscalYears().pipe();\r\n  }\r\n\r\n\r\n  // CTS Upload & Transmission Dashboard Methods\r\n  getCTSUploadStatusDescription(input: any) {\r\n    const foundStatus = CTSUploadStatusDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  getReceivingCountryName(input: any) {\r\n    const foundCountry = this.countries.find(\r\n      (status) => status.code2 === input\r\n    );\r\n    if (foundCountry) return foundCountry.name;\r\n    return '';\r\n  }\r\n\r\n  getBahamasCertificateInfo() {\r\n    return this.certificateService.getBahamasCertificateInfo().subscribe({\r\n      next: (info) => {\r\n        this.certificateExpirationDate = info?.expiredAt || null;\r\n      },\r\n      error: () => {\r\n        this.certificateExpirationDate = null;\r\n      }\r\n    });\r\n  }\r\n\r\n  openUpdateCaCertificateDialog() {\r\n    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\r\n      width: '400px',\r\n      data: {},\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.file) {\r\n        const formData: FormData = new FormData();\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n        // Only call upload if file is present        \r\n        this.fileUploadService\r\n          .uploadBahamasCertificate(formData, result.password).subscribe({\r\n            next: (response) => {\r\n              if (response) {\r\n                // Fetch certificate expiration date\r\n                this.getBahamasCertificateInfo();\r\n                this.toasterService.success('Bahamas Certificate successfully uploaded', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, { life: 7000 });\r\n              }\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error uploading Bahamas certificate:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n  }\r\n\r\n  getCountries() {\r\n    this.countryService.getList({ sorting: \"name asc\", maxResultCount: 1000 }).subscribe(response => {\r\n      this.countries = response.items;\r\n      // Remove code2 with empty string and null values in countries\r\n      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');      \r\n      // add new country ALL in countries \r\n      this.countries.unshift({ name: 'All',  code2: '' });      \r\n    });\r\n  }\r\n  \r\n  // CTS Upload & Transmission Dashboard Methods\r\n  setCtsDashboardTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.ctsDashboardTableId;\r\n    tableData.totalRecords = 1;\r\n    tableData.data = this.ctsDashboardList.map(x => ({\r\n      id: x.id,\r\n      rawData: x,\r\n      cells: [\r\n        { columnId: 'totalNotUploaded', value: x.totalNotUploaded },\r\n        { columnId: 'totalReadyForUpload', value: x.totalReadyForUpload },\r\n        { columnId: 'totalFailedUpload', value: x.totalFailedUpload },\r\n        { columnId: 'totalUploadedToCTS', value: x.totalUploadedToCTS },\r\n        { columnId: 'totalNotEnrolled', value: x.totalNotEnrolled },\r\n      ],\r\n    }));\r\n    setTimeout(() => this.tableService.setGridData(tableData), 10);\r\n  }\r\n\r\n  setCtsUploadTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.ctsUploadTableId;\r\n    tableData.totalRecords = this.ctsUploadTotalRecords;\r\n    tableData.data = this.ctsUploadResultRecords.map(x => ({\r\n      id: x.id,\r\n      rawData: x,\r\n      cells: [\r\n        { columnId: 'exchangeReason', value: this.getExchangeReasonDescription(x.exchangeReason)},\r\n        { columnId: 'dataPacket', value: x.dataPacket },\r\n        { columnId: 'fileCreationDate', value: x.fileCreationDate },\r\n        { columnId: 'receivingCountry', value: this.getReceivingCountryName(x.receivingCountry) },\r\n        { columnId: 'ctsUploadStatus', value: this.getCTSUploadStatusDescription(x.ctsUploadStatus) },\r\n        { columnId: 'uploadedAt', value: x.uploadedAt },\r\n        { columnId: 'ctsTransmissionStatus', value: x.ctsTransmissionStatus },\r\n        { columnId: 'viewExchangeRecords', value: x.viewExchangeRecords === false ? 'View' : '' },\r\n        { columnId: 'viewComments', value: x.viewComments.length > 0 ? 'View Comments' : '' },\r\n        { columnId: 'regeneratePacket', value: x.regeneratePacket },\r\n        { columnId: 'ctsUpload', value: x.ctsUpload },\r\n        { columnId: 'excludeFromCtsUpload', value: x.excludeFromCtsUpload },\r\n      ],\r\n    }));\r\n\r\n    setTimeout(() => this.tableService.setGridData(tableData), 100);\r\n  }\r\n\r\n  onCtsDashboardLazyLoadEvent(event): void {\r\n    this.currentPageIndexS = 0;\r\n    this.ctsPackageRequestService\r\n      .getSummaryByYearByYear(this.ctsUploadSelectedYear)\r\n      .subscribe((response) => {\r\n        this.ctsDashboardList = response;\r\n        setTimeout(() => {\r\n          this.setCtsDashboardTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\r\n        this.ctsUploadCurrentPage = event.pageNumber ?? 0;\r\n      } else {\r\n        //\r\n        // if Page size got changed through pagination control,\r\n        // need to reset current page index to 0.\r\n        //\r\n        this.ctsUploadPageSize = event.pageSize ?? 10;\r\n        this.ctsUploadCurrentPage = 0;\r\n      }\r\n\r\n      this.ctsUploadInput.skipCount =\r\n        (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\r\n\r\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\r\n\r\n      if (event.isAscending === false) {\r\n        this.ctsUploadInput.sorting = `${event.sortField} desc`;\r\n      } else {\r\n        this.ctsUploadInput.sorting = `${event.sortField} asc`;\r\n      }\r\n    } else {\r\n      this.ctsUploadCurrentPage = 0;\r\n      this.ctsUploadPageSize = 10;     \r\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\r\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;        \r\n      this.ctsUploadInput.skipCount = 0;\r\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    }\r\n\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.ctsUploadTotalRecords = response.totalCount;\r\n          this.ctsUploadResultRecords = response.items;\r\n        } else {\r\n          this.ctsUploadTotalRecords = 0;\r\n          this.ctsUploadResultRecords = [];\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n  \r\n  onCtsUploadYearChange(ob) {\r\n    this.ctsUploadSelectedYear = ob.value;   \r\n    localStorage.setItem('ctsUploadSelectedYear', ob.value);   \r\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;   \r\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;        \r\n    this.ctsUploadInput.skipCount = 0;\r\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        this.ctsUploadTotalRecords = response.totalCount;\r\n        this.ctsUploadResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();          \r\n        }, 200);\r\n      });\r\n\r\n      this.onCtsDashboardLazyLoadEvent(undefined);\r\n  }\r\n\r\n  onCtsUploadSearch() {   \r\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;    \r\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;        \r\n    this.ctsUploadInput.skipCount = 0;\r\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n       this.ctsUploadTotalRecords = response.totalCount;\r\n        this.ctsUploadResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });    \r\n  }\r\n\r\n  onCtsUploadExchangeReasonChange(ob) {\r\n    this.selectExchangeReason = Number(ob.value);       \r\n    // Keep the selected Exchange Reason in local storage.\r\n    localStorage.setItem('selectExchangeReason', ob.value);\r\n  }\r\n\r\n  onCtsUploadStatusChange(ob) {\r\n    this.selectCtsUploadStatus = Number(ob.value);       \r\n    // Keep the selected Upload Status in local storage.\r\n    localStorage.setItem('selectCtsUploadStatus', ob.value);\r\n  }\r\n\r\n  onCtsUploadReceivingCountryChange(ob) {\r\n    this.selectReceivingCountry = ob.value;       \r\n    // Keep the selected Receiving Country in local storage.\r\n    localStorage.setItem('selectReceivingCountry', ob.value);\r\n  }\r\n\r\n  onCtsUploadLinkClick(event: BdoTableCellLinkClickEvent) {\r\n    if (event.columnId === 'viewExchangeRecords') {\r\n      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {       \r\n        width: '1200px',      \r\n        data: {\r\n          row: event.rawData\r\n        },\r\n        panelClass: 'associated-exchange-dialog'\r\n      });\r\n    }    \r\n  }\r\n}\r\n", "<mat-tab-group>\r\n  <mat-tab label=\"Info Exchange Readiness\">   \r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table\r\n          [id]=\"TableIdS\"\r\n          [columns]=\"summaryExchangeColumns\"\r\n          scrollHeight=\"100%\"\r\n          defaultSortColumnId=\"uploadedDateTime\"\r\n          [defaultSortOrder]=\"'desc'\"\r\n          [pageIndex]=\"currentPageIndexS\"\r\n          [pageSize]=\"PageSizeS\"\r\n          [isVirtualScroll]=\"false\"\r\n          [hidePagination]=\"true\"\r\n          [rowSelectable]=\"true\"\r\n          [lazyLoad]=\"true\"\r\n          (onLazyLoad)=\"onLazyLoadEventS($event)\"\r\n          [pageSizeOptions]=\"[10, 20, 50, 100]\"\r\n        >\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-column-exchange row justify-content-end\">\r\n      <div class=\"col-md-auto\">\r\n        <span class=\"certificate-text\" *ngIf=\"certificateExpirationDate\">CA Certificate expired at {{ certificateExpirationDate | date:'dd/MM/yyyy'}}</span>\r\n        <button \r\n        *ngIf=\"isCaSystemAdmin\"\r\n        type=\"button\" \r\n        mat-raised-button \r\n        class=\"ui-button margin-l-5\" \r\n        (click)=\"openUpdateCaCertificateDialog()\"\r\n        >\r\n        Update CA Certificate\r\n      </button>\r\n        <button\r\n          *ngIf=\"showButton\"\r\n          type=\"button\"\r\n          mat-raised-button\r\n          class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(0)\"\r\n        >\r\n          Non-compliance XML\r\n        </button>\r\n        <button\r\n          *ngIf=\"showButton\"\r\n          type=\"button\"\r\n          mat-raised-button\r\n          class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(1)\"\r\n        >\r\n          High Risk IP XML\r\n        </button>\r\n        <button\r\n          *ngIf=\"showButton\"\r\n          type=\"button\"\r\n          mat-raised-button\r\n          class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(2)\"\r\n        >\r\n          Non-resident XML\r\n        </button>\r\n        <button\r\n          *ngIf=\"showButton && showOtherCase\"\r\n          type=\"button\"\r\n          mat-raised-button\r\n          class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(3)\"\r\n        >\r\n          Other Cases XML\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-column-exchange row\">\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Financial Period End </mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select\r\n            placeholder=\"Financial Period End\"\r\n            [(value)]=\"selectedYear\"\r\n            (selectionChange)=\"onYearChange($event)\"\r\n          >\r\n            <mat-option *ngFor=\"let element of year\" [value]=\"element\">\r\n              {{ element }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Report Status</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select\r\n            placeholder=\"Report Status\"\r\n            [(value)]=\"selectReportStatus\"\r\n            (selectionChange)=\"onReportChange($event)\"\r\n          >\r\n            <mat-option\r\n              *ngFor=\"let element of informationExchangedDic\"\r\n              [value]=\"element.value\"\r\n            >\r\n              {{ element.description }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Entity Name</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <input matInput placeholder=\"File Name\" [(ngModel)]=\"searchEntityName\" />\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-2 col-sm-12 margin-top search-button-column\">\r\n        <button\r\n          type=\"button\"\r\n          mat-raised-button\r\n          (click)=\"onSearch()\"\r\n          class=\"ui-button search-button\"\r\n        >\r\n          Search\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table\r\n          [id]=\"TableId\"\r\n          scrollHeight=\"36vh\"\r\n          [columns]=\"exchangeResultColumns\"\r\n          defaultSortColumnId=\"ExchangeReason\"\r\n          [defaultSortOrder]=\"'desc'\"\r\n          [pageIndex]=\"currentPageIndex\"\r\n          [pageSize]=\"PageSize\"\r\n          [pageSizeOptions]=\"[10, 20, 50, 100]\"\r\n          [isVirtualScroll]=\"false\"\r\n          [hidePagination]=\"false\"\r\n          [rowSelectable]=\"true\"\r\n          [lazyLoad]=\"true\"\r\n          (onLazyLoad)=\"onLazyLoadEvent($event)\"\r\n          (onLinkClick)=\"onLinkClick($event)\"\r\n        >\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n  </mat-tab>\r\n  <mat-tab label=\"CTS Upload & Transmission\">       \r\n\r\n      <!-- 1. Dashboard Section -->\r\n      <div class=\"row top-action-row-exchange\">\r\n        <div class=\"table-container\">\r\n          <bdo-table [id]=\"ctsDashboardTableId\"\r\n                     [columns]=\"ctsDashboardColumns\"\r\n                     scrollHeight=\"auto\"\r\n                     [defaultSortOrder]=\"'desc'\"\r\n                     [pageIndex]=\"currentPageIndexS\"\r\n                     [pageSize]=\"PageSizeS\"\r\n                     [isVirtualScroll]=\"false\"\r\n                     [hidePagination]=\"true\"\r\n                     [rowSelectable]=\"false\"\r\n                     [lazyLoad]=\"false\">\r\n            </bdo-table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 2. Button List Section -->      \r\n      <div class=\"top-action-column-exchange row justify-content-end\">\r\n         <div class=\"col-md-auto\">         \r\n          <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\">Decrypt Received Data Packet</button>\r\n          <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\">Upload Historical XML</button>\r\n          <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\">CTS Upload</button>\r\n          <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\">Refresh Status</button>\r\n        </div>\r\n      </div> \r\n\r\n      <!-- 3. Filters and Search Section -->\r\n        <div class=\"top-action-column-exchange row\">\r\n        <div class=\"col-md-5 col-sm-12 margin-top\">\r\n          <mat-label class=\"outside-mat-label\">Financial Period End Year</mat-label>\r\n          <mat-form-field class=\"form-field-reduce-length\">\r\n            <mat-select placeholder=\"Financial Period End\"\r\n             [(value)]=\"ctsUploadSelectedYear\"\r\n             (selectionChange)=\"onCtsUploadYearChange($event)\">\r\n              <mat-option *ngFor=\"let element of year\" [value]=\"element\">\r\n                {{ element }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n        </div>\r\n        <div class=\"col-md-2 col-sm-12 margin-top\">\r\n          <mat-label class=\"outside-mat-label\">Exchange Reason</mat-label>\r\n          <mat-form-field class=\"form-field-reduce-length\">\r\n            <mat-select placeholder=\"Exchange Reason\" \r\n              [(value)]=\"selectExchangeReason\"\r\n              (selectionChange)=\"onCtsUploadExchangeReasonChange($event)\">             \r\n              <mat-option *ngFor=\"let element of ctsUploadExchangeReasonDic\" [value]=\"element.value\"> {{ element.description }}</mat-option>\r\n            </mat-select>\r\n         </mat-form-field>\r\n        </div>\r\n        <div class=\"col-md-2 col-sm-12 margin-top\">\r\n          <mat-label class=\"outside-mat-label\">Receiving Country</mat-label>\r\n          <mat-form-field class=\"form-field-reduce-length\">           \r\n            <mat-select placeholder=\"Receiving Country\" [(value)]=\"selectReceivingCountry\"\r\n              (selectionChange)=\"onCtsUploadReceivingCountryChange($event)\">              \r\n               <mat-option *ngFor=\"let element of countries\" [value]=\"element.code2\">\r\n                  {{element.name}}\r\n                </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n        </div>\r\n        <div class=\"col-md-2 col-sm-12 margin-top\">\r\n          <mat-label class=\"outside-mat-label\">CTS Upload Status</mat-label>\r\n          <mat-form-field class=\"form-field-reduce-length\">            \r\n            <mat-select placeholder=\"CTS Upload Status\" [(value)]=\"selectCtsUploadStatus\"\r\n              (selectionChange)=\"onCtsUploadStatusChange($event)\">             \r\n              <mat-option *ngFor=\"let element of ctsUploadStatusDic\" [value]=\"element.value\"> {{ element.description }}</mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n        </div>\r\n        <div class=\"col-md-1 col-sm-12 search-button-column justify-content-center\">\r\n          <button type=\"button\" mat-raised-button class=\"ui-button search-button\"\r\n           (click)=\"onCtsUploadSearch()\">Search</button>\r\n        </div>\r\n        </div>\r\n\r\n      <!-- 4. Grid Section -->\r\n      <div class=\"top-action-row-exchange row\">\r\n        <div class=\"table-container\">\r\n          <bdo-table [id]=\"ctsUploadTableId\"\r\n                     [columns]=\"ctsUploadColumns\"\r\n                     scrollHeight=\"36vh\"\r\n                     defaultSortColumnId=\"ExchangeReason\"\r\n                     [defaultSortOrder]=\"'desc'\"\r\n                     [pageIndex]=\"ctsUploadCurrentPage\"\r\n                     [pageSize]=\"ctsUploadPageSize\"\r\n                     [pageSizeOptions]=\"[10, 20, 50, 100]\"\r\n                     [isVirtualScroll]=\"false\"\r\n                     [hidePagination]=\"false\"\r\n                     [rowSelectable]=\"true\"                      \r\n                     [lazyLoad]=\"true\"\r\n                     (onLazyLoad)=\"onCtsUploadLazyLoadEvent($event)\"\r\n                     (onLinkClick)=\"onCtsUploadLinkClick($event)\">\r\n          </bdo-table>\r\n        </div>\r\n      </div>\r\n    \r\n    </mat-tab>\r\n</mat-tab-group>\r\n\r\n\r\n"], "mappings": "AAOA,SAEEA,yBAAyB,QACpB,mHAAmH;AAC1H,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAGEC,kBAAkB,EAClBC,YAAY,QACP,yDAAyD;AAChE,SACEC,iBAAiB,EAEjBC,4BAA4B,EAG5BC,kBAAkB,EAClBC,0BAA0B,QACrB,8BAA8B;AACrC,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,mCAAmC,QAAQ,wEAAwE;AAE5H,SAASC,kCAAkC,QAAQ,wEAAwE;AAO3H,SAASC,eAAe,QAAQ,+FAA+F;AAC/H,SAASC,sCAAsC,QAAQ,gFAAgF;;;;;;;;;;;;;;;;;;;;;;;;;ICjB/HC,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,GAA4E;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnFH,EAAA,CAAAI,SAAA,EAA4E;IAA5EJ,EAAA,CAAAK,kBAAA,+BAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,yBAAA,oBAA4E;;;;;;IAC7IR,EAAA,CAAAC,cAAA,iBAMC;IADDD,EAAA,CAAAS,UAAA,mBAAAC,2EAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,6BAAA,EAA+B;IAAA,EAAC;IAEzCf,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACPH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAS,UAAA,mBAAAO,2EAAA;MAAAhB,EAAA,CAAAW,aAAA,CAAAM,GAAA;MAAA,MAAAV,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAW,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAE9BlB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAS,UAAA,mBAAAU,4EAAA;MAAAnB,EAAA,CAAAW,aAAA,CAAAS,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAW,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAE9BlB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAS,UAAA,mBAAAY,4EAAA;MAAArB,EAAA,CAAAW,aAAA,CAAAW,GAAA;MAAA,MAAAf,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAW,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAE9BlB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAS,UAAA,mBAAAc,4EAAA;MAAAvB,EAAA,CAAAW,aAAA,CAAAa,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAW,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAE9BlB,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYLH,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAAyB,UAAA,UAAAC,UAAA,CAAiB;IACxD1B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAqB,UAAA,MACF;;;;;IAYA1B,EAAA,CAAAC,cAAA,qBAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAHXH,EAAA,CAAAyB,UAAA,UAAAE,UAAA,CAAAC,KAAA,CAAuB;IAEvB5B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsB,UAAA,CAAAE,WAAA,MACF;;;;;IAgFE7B,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAAyB,UAAA,UAAAK,UAAA,CAAiB;IACxD9B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAyB,UAAA,MACF;;;;;IAUA9B,EAAA,CAAAC,cAAA,qBAAuF;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAA/DH,EAAA,CAAAyB,UAAA,UAAAM,WAAA,CAAAH,KAAA,CAAuB;IAAE5B,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,kBAAA,MAAA0B,WAAA,CAAAF,WAAA,KAAyB;;;;;IAShH7B,EAAA,CAAAC,cAAA,qBAAsE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAyB,UAAA,UAAAO,WAAA,CAAAC,KAAA,CAAuB;IAClEjC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA2B,WAAA,CAAAE,IAAA,MACF;;;;;IASFlC,EAAA,CAAAC,cAAA,qBAA+E;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAA/DH,EAAA,CAAAyB,UAAA,UAAAU,WAAA,CAAAP,KAAA,CAAuB;IAAE5B,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,kBAAA,MAAA8B,WAAA,CAAAN,WAAA,KAAyB;;;ADpKvH,OAAM,MAAOO,gCACX,SAAQhD,gBAAgB;EAoUxBiD,YACEC,QAAkB,EACVC,MAAc,EACdC,0BAAsD,EACtDC,gCAAmE,EACnEC,iBAAoC,EACpCC,gBAA8C,EAC/CC,MAAiB,EAChBC,kBAAsC,EACtCC,iBAAoC,EACpCC,cAA8B,EAC9BC,cAA8B,EAC9BC,wBAAkD;IAE1D,KAAK,CAACX,QAAQ,CAAC;IAZP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAC1B,KAAAC,gCAAgC,GAAhCA,gCAAgC;IAChC,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IA9UlC,KAAAC,KAAK,GAA8B;MACjCC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,oBAAoB;MAC7BC,yBAAyB,EAAEnE,yBAAyB,CAACoE,IAAI;MACzDC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE;KACP;IACD,KAAAC,OAAO,GAAG,wBAAwB;IAClC;IACA,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAF,IAAI,GAAG,EAAE;IACT;IACA,KAAAG,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAC;IACpD,KAAAC,qBAAqB,GAA+B,CAClD;MACEC,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAACwE,IAAI;MAC7BO,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAACwE,IAAI;MAC7BO,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAACmF,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAACmF,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAACmF,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,CACF;IACD;IACA,KAAAE,QAAQ,GAAG,EAAE;IACb,KAAAC,gCAAgC,GAAG,EAAE;IACrC,KAAAC,kBAAkB,GAAWxF,yBAAyB,CAACoE,IAAI;IAE3D,KAAAqB,uBAAuB,GAAQpF,4BAA4B;IAC3D,KAAAqF,QAAQ,GAAG,wBAAwB;IACnC,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,sBAAsB,GAA+B,CACnD;MACEd,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EACR;KACH,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IACD;IACA,KAAAU,SAAS,GAAG,EAAE;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB;;IAEA,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,WAAW,GAAG,IAAIxB,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE;IAEjD,KAAAvD,yBAAyB,GAAkB,IAAI;IAC/C,KAAA8E,eAAe,GAAY,KAAK;IAEhC;IACA,KAAAC,mBAAmB,GAA+B,CAChD;MACEtB,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,mBAAmB;MAC7BC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE7E,kBAAkB,CAAC2F,MAAM;MAC/BZ,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IAED;IACA,KAAAiB,gBAAgB,GAAU,CACxB;MACEC,EAAE,EAAE,CAAC;MACLC,gBAAgB,EAAE,CAAC;MACnBC,mBAAmB,EAAE,CAAC;MACtBC,iBAAiB,EAAE,CAAC;MACpBC,kBAAkB,EAAE,CAAC;MACrBC,gBAAgB,EAAE;KACnB,CACF;IAED;IACA,KAAAC,gBAAgB,GAA+B,CAC7C;MACE9B,QAAQ,EAAE,gBAAgB;MAC1BC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE7E,kBAAkB,CAACmF,IAAI;MAC7BJ,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE7E,kBAAkB,CAACwE,IAAI;MAC7BO,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE7E,kBAAkB,CAACwE,IAAI;MAC7BO,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAE7E,kBAAkB,CAAC8E,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE7E,kBAAkB,CAACmF,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,cAAc;MACxBC,IAAI,EAAE7E,kBAAkB,CAACmF,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE7E,kBAAkB,CAAC2G,OAAO;MAChC5B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,WAAW;MACrBC,IAAI,EAAE7E,kBAAkB,CAAC2G,OAAO;MAChC5B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE7E,kBAAkB,CAAC4G,QAAQ;MACjC7B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IAED;IACA,KAAA2B,0BAA0B,GAAQxG,0BAA0B;IAC5D,KAAAyG,kBAAkB,GAAQ1G,kBAAkB;IAE5C,KAAA2G,qBAAqB,GAAG,IAAIvC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAC;IAC7D,KAAAsC,sBAAsB,GAAG,EAAE;IAC3B,KAAAC,oBAAoB,GAAW,CAAC,CAAC;IACjC,KAAAC,qBAAqB,GAAWzG,eAAe,CAAC0G,UAAU;IAC1D,KAAAC,sBAAsB,GAAW,EAAE;IAEnC;IACA,KAAAC,mBAAmB,GAAG,eAAe;IACrC,KAAAC,gBAAgB,GAAG,iBAAiB;IACpC,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,cAAc,GAA4B;MACxC5D,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,oBAAoB;MAC7B2D,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE;KACnB;IA+JD,KAAAC,mBAAmB,GAAU,CAC3B;MACE3B,EAAE,EAAE,CAAC;MACL4B,aAAa,EAAE,GAAG;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,sBAAsB,EAAE,CAAC;MACzBC,oBAAoB,EAAE,CAAC;MACvBC,oBAAoB,EAAE;KACvB,CACF;EAvJD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC3C,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACrE,IAAI,GAAG,EAAE;QACdoE,QAAQ,CAACE,OAAO,CAAEC,OAAO,IAAI;UAC3B,IAAI,CAACvE,IAAI,CAACwE,IAAI,CAACD,OAAO,CAACjE,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC;MACJ;MAAC;IACH,CAAC,CAAC;IAEF,IAAImE,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE;MACxC,IAAI,CAACvE,YAAY,GACfsE,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC9C,WAAW;IAC5D;IAEA,IAAI6C,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE;MAC9C,IAAI,CAACxD,kBAAkB,GAAGK,MAAM,CAC9BkD,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC3C;IACH;IACA,IAAI,CAAC1F,gCAAgC,CAClC2F,0BAA0B,EAAE,CAC5BR,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACQ,sBAAsB,GAAGR,QAAQ;MAEtC,IAAI,CAACS,eAAe,CAAC,IAAI,CAAC1E,YAAY,CAAC;IACzC,CAAC,CAAC;IAEJ,IAAI,CAAC2E,eAAe,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;IAEhC,IAAI,CAACrD,UAAU,GAAG,IAAI,CAACuD,mBAAmB,EAAE;IAE5C;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,aAAa,CAAmB;IAC5E,IAAI,CAACvD,eAAe,GAAG,CAAC,CAACqD,WAAW,EAAEG,KAAK,EAAEC,QAAQ,CAAC,iBAAiB,CAAC;IAExE,IAAIb,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MACjD,IAAI,CAAC/B,qBAAqB,GACxB8B,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC9C,WAAW;IACrE;IAEA,IAAI6C,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAChD,IAAI,CAAC7B,oBAAoB,GAAGtB,MAAM,CAChCkD,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAC7C;IACH;IAEA,IAAID,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MACjD,IAAI,CAAC5B,qBAAqB,GAAGvB,MAAM,CACjCkD,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAC9C;IACH;IAEA,IAAID,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;MAClD,IAAI,CAAC1B,sBAAsB,GAAGyB,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC9E;IAEA;IACA,IAAI,CAACa,yBAAyB,EAAE;IAChC,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,wBAAwB,CAACX,SAAS,CAAC;IACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;EAC7C;EAEAF,eAAeA,CAAC7E,IAAY;IAC1B,MAAM4F,iBAAiB,GAAWC,QAAQ,CAAC7F,IAAI,EAAE,EAAE,CAAC;IACpD,MAAM8F,2BAA2B,GAAWD,QAAQ,CAClD,IAAI,CAACjB,sBAAsB,EAC3B,EAAE,CACH;IACD,IAAI,CAACjD,aAAa,GAChBiE,iBAAiB,IAAIE,2BAA2B,GAAG,IAAI,GAAG,KAAK;EACnE;EAEA;EACAd,gBAAgBA,CAACe,KAAK;IACpB,IAAI,CAAC1E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACtC,0BAA0B,CAC5BiH,sBAAsB,CAAC,IAAI,CAAC7F,YAAY,CAAC,CACzCgE,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACT,mBAAmB,GAAGS,QAAQ;MACnC6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEA;EACApB,eAAeA,CAACiB,KAAK;IACnB,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAAC/E,QAAQ,MAAM+E,KAAK,CAACI,QAAQ,IAAI,EAAE,CAAC,EAAE;QAC5C,IAAI,CAACjG,gBAAgB,GAAG6F,KAAK,CAACK,UAAU,IAAI,CAAC;MAC/C,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACpF,QAAQ,GAAG+E,KAAK,CAACI,QAAQ,IAAI,EAAE;QACpC,IAAI,CAACjG,gBAAgB,GAAG,CAAC;MAC3B;MAEA,IAAI,CAACT,KAAK,CAACE,SAAS,GAClB,CAAC,IAAI,CAACO,gBAAgB,IAAI,CAAC,KAAK,IAAI,CAACc,QAAQ,IAAI,EAAE,CAAC;MAEtD,IAAI,CAACvB,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ,IAAI,EAAE;MAE/C,IAAI+E,KAAK,CAACM,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAAC5G,KAAK,CAACG,OAAO,GAAG,GAAGmG,KAAK,CAACO,SAAS,OAAO;MAChD,CAAC,MAAM;QACL,IAAI,CAAC7G,KAAK,CAACG,OAAO,GAAG,GAAGmG,KAAK,CAACO,SAAS,MAAM;MAC/C;IACF,CAAC,MAAM;MACL,IAAI,CAACpG,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACc,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACvB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;MAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;MACnC,IAAI,CAACV,KAAK,CAACE,SAAS,GAAG,CAAC;MACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IAC3C;IAEA,IAAI,CAACjC,0BAA0B,CAC5BwH,gCAAgC,CAAC,IAAI,CAAC9G,KAAK,CAAC,CAC5C0E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC3C,YAAY,GAAG2C,QAAQ,CAACoC,UAAU;QACvC,IAAI,CAACvF,gCAAgC,GAAGmD,QAAQ,CAACqC,KAAK;MACxD,CAAC,MAAM;QACL,IAAI,CAAChF,YAAY,GAAG,CAAC;QACrB,IAAI,CAACR,gCAAgC,GAAG,EAAE;MAC5C;MAEAgF,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAaAR,aAAaA,CAAA;IACX,MAAMS,SAAS,GAAG,IAAI9K,YAAY,EAAE;IACpC8K,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAACzF,QAAQ;IACjCuF,SAAS,CAAClF,YAAY,GAAG,CAAC;IAC1BkF,SAAS,CAACG,IAAI,GAAG,IAAI,CAACnD,mBAAmB,CAACoD,GAAG,CAAEC,CAAC,IAAI;MAClD,OAAO;QACLhF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;QACRiF,OAAO,EAAED,CAAC;QACVE,KAAK,EAAE,CACL;UACE1G,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACG;SACV,EACD;UACE3G,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACI;SACV,EACD;UACE5G,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACK;SACV,EACD;UACE7G,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACM;SACV,EACD;UACE9G,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACO;SACV;OAEJ;IACH,CAAC,CAAC;IACFtB,UAAU,CAAC,MAAK;MACd,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC;EACR;EAEAe,4BAA4BA,CAACjI,KAAU;IACrC,MAAMkI,WAAW,GAAG7L,iBAAiB,CAAC8L,IAAI,CACvCC,MAAM,IAAKA,MAAM,CAAC1J,KAAK,KAAKsB,KAAK,CACnC;IACD,IAAIkI,WAAW,EAAE,OAAOA,WAAW,CAACvJ,WAAW;IAC/C,OAAO,EAAE;EACX;EAEA0J,uCAAuCA,CAACrI,KAAU;IAChD,MAAMkI,WAAW,GAAG5L,4BAA4B,CAAC6L,IAAI,CAClDC,MAAM,IAAKA,MAAM,CAAC1J,KAAK,KAAKsB,KAAK,CACnC;IACD,IAAIkI,WAAW,EAAE,OAAOA,WAAW,CAACvJ,WAAW;IAC/C,OAAO,EAAE;EACX;EAEA2J,kBAAkBA,CAACf,CAAS;IAC1B,MAAMgB,GAAG,GAAGC,IAAI,CAACjB,CAAC,CAAC;IACnB,IAAIkB,SAAS,GAAGF,GAAG,CAAC3D,MAAM;IAC1B,IAAI8D,KAAK,GAAG,IAAIC,UAAU,CAAC,IAAIC,WAAW,CAACH,SAAS,CAAC,CAAC;IAEtD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;MAClCH,KAAK,CAACG,CAAC,CAAC,GAAGN,GAAG,CAACO,UAAU,CAACD,CAAC,CAAC;IAC9B;IACA,OAAOH,KAAK;EACd;EAEAK,YAAYA,CAACC,OAAe,EAAEhK,IAAY;IACxC,IAAIiK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,IAAI,CAACZ,kBAAkB,CAACU,OAAO,CAAC,CAAC,CAAC;IACvD,IAAIG,OAAO,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAE9C,IAAInE,OAAO,GAAGyE,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACzCD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC5E,OAAO,CAAC;IAClCA,OAAO,CAAC6E,KAAK,CAACC,OAAO,GAAG,MAAM;IAC9B9E,OAAO,CAAC+E,IAAI,GAAGV,OAAO;IACtBrE,OAAO,CAACgF,QAAQ,GAAG9K,IAAI;IACvB8F,OAAO,CAACiF,KAAK,EAAE;IACfjF,OAAO,CAACkF,MAAM,EAAE;EAClB;EAEAhM,iBAAiBA,CAACiM,YAA4B;IAC5C,IAAI,CAAC3K,0BAA0B,CAC5B4K,8CAA8C,CAC7CD,YAAY,EACZ,IAAI,CAACvJ,YAAY,CAClB,CACAgE,SAAS,CAAEyF,MAAM,IAAI;MACpB,IAAI,CAAC9E,eAAe,CAACC,SAAS,CAAC;MAC/B,IAAI6E,MAAM,CAACC,QAAQ,IAAI,EAAE,EAAE;QACzB,IAAI,CAACrB,YAAY,CAACoB,MAAM,CAACnB,OAAO,CAACnI,QAAQ,EAAE,EAAEsJ,MAAM,CAACC,QAAQ,CAAC;MAC/D,CAAC,MACC3N,IAAI,CAAC4N,IAAI,CAAC;QACRC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,oBAAoB;QAC1BC,iBAAiB,EAAE;OACpB,CAAC;IACN,CAAC,CAAC;EACN;EAEAxD,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAG,IAAI9K,YAAY,EAAE;IACpC8K,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC5G,OAAO;IAChC0G,SAAS,CAAClF,YAAY,GAAG,IAAI,CAACA,YAAY;IAC1CkF,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC7F,gCAAgC,CAAC8F,GAAG,CAAEC,CAAC,IAAI;MAC/D,OAAO;QACLhF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;QACRiF,OAAO,EAAED,CAAC;QACVE,KAAK,EAAE,CACL;UACE1G,QAAQ;UACRrC,KAAK,EAAE,IAAI,CAACuJ,4BAA4B,CAACV,CAAC,CAACxD,cAAc;SAC1D,EACD;UACEhD,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACmD;SACV,EACD;UACE3J,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACjH;SACV,EACD;UACES,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACoD;SACV,EACD;UACE5J,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACqD;SACV,EACD;UACE7J,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACsD;SACV,EACD;UACE9J,QAAQ;UACRrC,KAAK,EAAE,IAAI,CAAC2J,uCAAuC,CACjDd,CAAC,CAACnH,yBAAyB;SAE9B,EACD;UACEW,QAAQ;;UACR;UACArC,KAAK,EAAE6I,CAAC,CAACuD,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG;SAC1C,EACD;UACE/J,QAAQ;UACRrC,KAAK,EAAE;SACR,EACD;UACEqC,QAAQ;UACRrC,KAAK,EAAE6I,CAAC,CAACwD,gBAAgB,GAAG,cAAc,GAAG;SAC9C;OAEJ;IACH,CAAC,CAAC;IACFvE,UAAU,CAAC,MAAK;MACd,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA8D,YAAYA,CAACC,EAAE;IACb,IAAI,CAACvK,YAAY,GAAGuK,EAAE,CAACvM,KAAK;IAC5B,IAAI,CAAC0G,eAAe,CAAC,IAAI,CAAC1E,YAAY,CAAC;IACvC;IACAsE,YAAY,CAACkG,OAAO,CAAC,cAAc,EAAED,EAAE,CAACvM,KAAK,CAAC;IAE9C,IAAI,CAACsB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;IAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;IACnC,IAAI,CAACV,KAAK,CAACM,UAAU,GAAG,IAAI,CAAC6K,gBAAgB;IAC7C,IAAI,CAACnL,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IACzC,IAAI,CAACjC,0BAA0B,CAC5BwH,gCAAgC,CAAC,IAAI,CAAC9G,KAAK,CAAC,CAC5C0E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC3C,YAAY,GAAG2C,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAACvF,gCAAgC,GAAGmD,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAAC1B,gBAAgB,CAACD,SAAS,CAAC;EAClC;EAEA8F,QAAQA,CAAA;IACN,IAAI,CAACpL,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;IAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;IACnC,IAAI,CAACV,KAAK,CAACM,UAAU,GAAG,IAAI,CAAC6K,gBAAgB;IAC7C,IAAI,CAACnL,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IACzC,IAAI,CAACjC,0BAA0B,CAC5BwH,gCAAgC,CAAC,IAAI,CAAC9G,KAAK,CAAC,CAC5C0E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC3C,YAAY,GAAG2C,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAACvF,gCAAgC,GAAGmD,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAAC1B,gBAAgB,CAACD,SAAS,CAAC;EAClC;EAEA+F,cAAcA,CAACJ,EAAE;IACf,IAAI,CAACxJ,kBAAkB,GAAGK,MAAM,CAACmJ,EAAE,CAACvM,KAAK,CAAC;IAC1C,IAAI,CAACsB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACqB,kBAAkB;IAC9D,IAAI,CAACzB,KAAK,CAACO,IAAI,GAAG,IAAI,CAACG,YAAY;IACnC,IAAI,CAACV,KAAK,CAACM,UAAU,GAAG,IAAI,CAAC6K,gBAAgB;IAC7C,IAAI,CAACnL,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACsB,QAAQ;IAEzC;IACAyD,YAAY,CAACkG,OAAO,CAAC,oBAAoB,EAAED,EAAE,CAACvM,KAAK,CAAC;IAEpD,IAAI,CAACY,0BAA0B,CAC5BwH,gCAAgC,CAAC,IAAI,CAAC9G,KAAK,CAAC,CAC5C0E,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC3C,YAAY,GAAG2C,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAACvF,gCAAgC,GAAGmD,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAqE,WAAWA,CAAChF,KAAiC;IAC3C,MAAMe,IAAI,GAAGf,KAAK,CAACkB,OAAiC;IACpD,IAAIlB,KAAK,CAACvF,QAAQ,+DAA+C;MAC/D;MACA;MACA;MACA;MACA,IAAI,CAAC1B,MAAM,CAACkM,QAAQ,CAAC,CAAC,kCAAkC,CAAC,EAAE;QACzD;QACA;QACA;QACAC,WAAW,EAAE;UAAEjJ,EAAE,EAAE8E,IAAI,CAAC9E,EAAE;UAAEkJ,SAAS,EAAE;QAAK;OAC7C,CAAC;IACJ,CAAC,MAAM,IACLnF,KAAK,CAACvF,QAAQ,gFACd;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC1B,MAAM,CAACkM,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QACrCC,WAAW,EAAE;UACXE,aAAa,EAAErE,IAAI,CAACsE,aAAa;UACjCC,QAAQ,EAAEvE,IAAI,CAACwE,iBAAiB;UAChCC,IAAI,EAAE;;OAET,CAAC;IACJ,CAAC,MAAM,IACLxF,KAAK,CAACvF,QAAQ,uEACd;MACA;MACA;MACA;MACA,IAAI,CAACgL,oCAAoC,CAAC1E,IAAI,CAAC9E,EAAE,CAAC;IACpD;EACF;EAEAwJ,oCAAoCA,CAACC,qBAA6B;IAChE,MAAMC,SAAS,GAAG,IAAI,CAACvM,MAAM,CAACwM,IAAI,CAACxP,mCAAmC,EAAE;MACtEyP,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,QAAQ;MACf/E,IAAI,EAAE;QAAE2E,qBAAqB,EAAEA;MAAqB;KACrD,CAAC;IAEFC,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3CmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpC,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGA3E,mBAAmBA,CAAA;IACjB,IAAI2E,MAAM,GAAG,KAAK;IAClB;IACA,MAAM1E,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,CACzC,aAAa,CACI;IAEnB,IAAIF,WAAW,EAAE;MACf0E,MAAM,GAAG,IAAI,CAAC3K,iBAAiB,CAACgN,gBAAgB,mFAE/C;IACH;IAEA,OAAOrC,MAAM;EACf;EAEA;EACA1F,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAChF,gBAAgB,CAACgF,cAAc,EAAE,CAACgI,IAAI,EAAE;EACtD;EAGA;EACAC,6BAA6BA,CAAC1M,KAAU;IACtC,MAAMkI,WAAW,GAAG3L,kBAAkB,CAAC4L,IAAI,CACxCC,MAAM,IAAKA,MAAM,CAAC1J,KAAK,KAAKsB,KAAK,CACnC;IACD,IAAIkI,WAAW,EAAE,OAAOA,WAAW,CAACvJ,WAAW;IAC/C,OAAO,EAAE;EACX;EAEAgO,uBAAuBA,CAAC3M,KAAU;IAChC,MAAM4M,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC1E,IAAI,CACrCC,MAAM,IAAKA,MAAM,CAACrJ,KAAK,KAAKiB,KAAK,CACnC;IACD,IAAI4M,YAAY,EAAE,OAAOA,YAAY,CAAC5N,IAAI;IAC1C,OAAO,EAAE;EACX;EAEA8G,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAACnG,kBAAkB,CAACmG,yBAAyB,EAAE,CAACpB,SAAS,CAAC;MACnEoI,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACzP,yBAAyB,GAAGyP,IAAI,EAAEC,SAAS,IAAI,IAAI;MAC1D,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3P,yBAAyB,GAAG,IAAI;MACvC;KACD,CAAC;EACJ;EAEAO,6BAA6BA,CAAA;IAC3B,MAAMoO,SAAS,GAAG,IAAI,CAACvM,MAAM,CAACwM,IAAI,CAACvP,kCAAkC,EAAE;MACrEyP,KAAK,EAAE,OAAO;MACd/E,IAAI,EAAE;KACP,CAAC;IAEF4E,SAAS,CAACI,WAAW,EAAE,CAAC3H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAAClB,IAAI,EAAE;QACzB,MAAMiE,QAAQ,GAAa,IAAIC,QAAQ,EAAE;QACzCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEjD,MAAM,CAAClB,IAAI,CAACjK,IAAI,CAAC;QAC7CkO,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEjD,MAAM,CAAClB,IAAI,CAAC;QACpCiE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEjD,MAAM,CAAClB,IAAI,CAACjI,IAAI,CAAC;QAC7C;QACA,IAAI,CAACpB,iBAAiB,CACnByN,wBAAwB,CAACH,QAAQ,EAAE/C,MAAM,CAACmD,QAAQ,CAAC,CAAC5I,SAAS,CAAC;UAC7DoI,IAAI,EAAGnI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,EAAE;cACZ;cACA,IAAI,CAACmB,yBAAyB,EAAE;cAChC,IAAI,CAACjG,cAAc,CAAC0N,OAAO,CAAC,2CAA2C,EAAE,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAI,CAAE,CAAC;YAC9F,CAAC,MACI;cACH,IAAI,CAAC3N,cAAc,CAAC4N,IAAI,CAAC,kEAAkE,EAAE,IAAI,EAAE;gBAAED,IAAI,EAAE;cAAI,CAAE,CAAC;YACpH;UACF,CAAC;UACDP,KAAK,EAAGA,KAAK,IAAI;YACf;YACAX,OAAO,CAACW,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC9D;SACD,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EAEAlH,YAAYA,CAAA;IACV,IAAI,CAACjG,cAAc,CAAC4N,OAAO,CAAC;MAAEvN,OAAO,EAAE,UAAU;MAAEF,cAAc,EAAE;IAAI,CAAE,CAAC,CAACyE,SAAS,CAACC,QAAQ,IAAG;MAC9F,IAAI,CAACkI,SAAS,GAAGlI,QAAQ,CAACqC,KAAK;MAC/B;MACA,IAAI,CAAC6F,SAAS,GAAG,IAAI,CAACA,SAAS,CAACc,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC7O,KAAK,IAAI6O,OAAO,CAAC7O,KAAK,CAAC8O,IAAI,EAAE,KAAK,EAAE,CAAC;MAC/F;MACA,IAAI,CAAChB,SAAS,CAACiB,OAAO,CAAC;QAAE9O,IAAI,EAAE,KAAK;QAAGD,KAAK,EAAE;MAAE,CAAE,CAAC;IACrD,CAAC,CAAC;EACJ;EAEA;EACAiH,wBAAwBA,CAAA;IACtB,MAAMkB,SAAS,GAAG,IAAI9K,YAAY,EAAE;IACpC8K,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC5D,mBAAmB;IAC5C0D,SAAS,CAAClF,YAAY,GAAG,CAAC;IAC1BkF,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC/E,gBAAgB,CAACgF,GAAG,CAACC,CAAC,KAAK;MAC/ChF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;MACRiF,OAAO,EAAED,CAAC;MACVE,KAAK,EAAE,CACL;QAAE1G,QAAQ,EAAE,kBAAkB;QAAErC,KAAK,EAAE6I,CAAC,CAAC/E;MAAgB,CAAE,EAC3D;QAAEzB,QAAQ,EAAE,qBAAqB;QAAErC,KAAK,EAAE6I,CAAC,CAAC9E;MAAmB,CAAE,EACjE;QAAE1B,QAAQ,EAAE,mBAAmB;QAAErC,KAAK,EAAE6I,CAAC,CAAC7E;MAAiB,CAAE,EAC7D;QAAE3B,QAAQ,EAAE,oBAAoB;QAAErC,KAAK,EAAE6I,CAAC,CAAC5E;MAAkB,CAAE,EAC/D;QAAE5B,QAAQ,EAAE,kBAAkB;QAAErC,KAAK,EAAE6I,CAAC,CAAC3E;MAAgB,CAAE;KAE9D,CAAC,CAAC;IACH4D,UAAU,CAAC,MAAM,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC,EAAE,EAAE,CAAC;EAChE;EAEA6G,qBAAqBA,CAAA;IACnB,MAAM7G,SAAS,GAAG,IAAI9K,YAAY,EAAE;IACpC8K,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC3D,gBAAgB;IACzCyD,SAAS,CAAClF,YAAY,GAAG,IAAI,CAAC4B,qBAAqB;IACnDsD,SAAS,CAACG,IAAI,GAAG,IAAI,CAAClE,sBAAsB,CAACmE,GAAG,CAACC,CAAC,KAAK;MACrDhF,EAAE,EAAEgF,CAAC,CAAChF,EAAE;MACRiF,OAAO,EAAED,CAAC;MACVE,KAAK,EAAE,CACL;QAAE1G,QAAQ,EAAE,gBAAgB;QAAErC,KAAK,EAAE,IAAI,CAACuJ,4BAA4B,CAACV,CAAC,CAACxD,cAAc;MAAC,CAAC,EACzF;QAAEhD,QAAQ,EAAE,YAAY;QAAErC,KAAK,EAAE6I,CAAC,CAACyG;MAAU,CAAE,EAC/C;QAAEjN,QAAQ,EAAE,kBAAkB;QAAErC,KAAK,EAAE6I,CAAC,CAAC0G;MAAgB,CAAE,EAC3D;QAAElN,QAAQ,EAAE,kBAAkB;QAAErC,KAAK,EAAE,IAAI,CAACiO,uBAAuB,CAACpF,CAAC,CAACtD,gBAAgB;MAAC,CAAE,EACzF;QAAElD,QAAQ,EAAE,iBAAiB;QAAErC,KAAK,EAAE,IAAI,CAACgO,6BAA6B,CAACnF,CAAC,CAACzD,eAAe;MAAC,CAAE,EAC7F;QAAE/C,QAAQ,EAAE,YAAY;QAAErC,KAAK,EAAE6I,CAAC,CAAC2G;MAAU,CAAE,EAC/C;QAAEnN,QAAQ,EAAE,uBAAuB;QAAErC,KAAK,EAAE6I,CAAC,CAAC4G;MAAqB,CAAE,EACrE;QAAEpN,QAAQ,EAAE,qBAAqB;QAAErC,KAAK,EAAE6I,CAAC,CAAC6G,mBAAmB,KAAK,KAAK,GAAG,MAAM,GAAG;MAAE,CAAE,EACzF;QAAErN,QAAQ,EAAE,cAAc;QAAErC,KAAK,EAAE6I,CAAC,CAAC8G,YAAY,CAACzJ,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG;MAAE,CAAE,EACrF;QAAE7D,QAAQ,EAAE,kBAAkB;QAAErC,KAAK,EAAE6I,CAAC,CAAC+G;MAAgB,CAAE,EAC3D;QAAEvN,QAAQ,EAAE,WAAW;QAAErC,KAAK,EAAE6I,CAAC,CAACgH;MAAS,CAAE,EAC7C;QAAExN,QAAQ,EAAE,sBAAsB;QAAErC,KAAK,EAAE6I,CAAC,CAACiH;MAAoB,CAAE;KAEtE,CAAC,CAAC;IAEHhI,UAAU,CAAC,MAAM,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC,EAAE,GAAG,CAAC;EACjE;EAEAhB,2BAA2BA,CAACI,KAAK;IAC/B,IAAI,CAAC1E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAAC7B,wBAAwB,CAC1BwG,sBAAsB,CAAC,IAAI,CAACrD,qBAAqB,CAAC,CAClDwB,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACrC,gBAAgB,GAAGqC,QAAQ;MAChC6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACR,wBAAwB,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAC,wBAAwBA,CAACK,KAAK;IAC5B,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAAC5C,iBAAiB,MAAM4C,KAAK,CAACI,QAAQ,IAAI,EAAE,CAAC,EAAE;QACrD,IAAI,CAAC/C,oBAAoB,GAAG2C,KAAK,CAACK,UAAU,IAAI,CAAC;MACnD,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACjD,iBAAiB,GAAG4C,KAAK,CAACI,QAAQ,IAAI,EAAE;QAC7C,IAAI,CAAC/C,oBAAoB,GAAG,CAAC;MAC/B;MAEA,IAAI,CAACE,cAAc,CAAC3D,SAAS,GAC3B,CAAC,IAAI,CAACyD,oBAAoB,IAAI,CAAC,KAAK,IAAI,CAACD,iBAAiB,IAAI,EAAE,CAAC;MAEnE,IAAI,CAACG,cAAc,CAAC5D,cAAc,GAAG,IAAI,CAACyD,iBAAiB,IAAI,EAAE;MAEjE,IAAI4C,KAAK,CAACM,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAAC/C,cAAc,CAAC1D,OAAO,GAAG,GAAGmG,KAAK,CAACO,SAAS,OAAO;MACzD,CAAC,MAAM;QACL,IAAI,CAAChD,cAAc,CAAC1D,OAAO,GAAG,GAAGmG,KAAK,CAACO,SAAS,MAAM;MACxD;IACF,CAAC,MAAM;MACL,IAAI,CAAClD,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAACD,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACG,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACd,qBAAqB;MACjE,IAAI,CAACW,cAAc,CAACE,cAAc,GAAG,IAAI,CAACX,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;MACxG,IAAI,CAACS,cAAc,CAACC,eAAe,GAAG,IAAI,CAACT,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;MAC3G,IAAI,CAACQ,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACvG,IAAI,CAACM,cAAc,CAAC3D,SAAS,GAAG,CAAC;MACjC,IAAI,CAAC2D,cAAc,CAAC5D,cAAc,GAAG,IAAI,CAACyD,iBAAiB;IAC7D;IAEA,IAAI,CAAC3D,wBAAwB,CAC1B0O,8BAA8B,CAAC,IAAI,CAAC5K,cAAc,CAAC,CACnDa,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACf,qBAAqB,GAAGe,QAAQ,CAACoC,UAAU;QAChD,IAAI,CAAC5D,sBAAsB,GAAGwB,QAAQ,CAACqC,KAAK;MAC9C,CAAC,MAAM;QACL,IAAI,CAACpD,qBAAqB,GAAG,CAAC;QAC9B,IAAI,CAACT,sBAAsB,GAAG,EAAE;MAClC;MAEAqD,UAAU,CAAC,MAAK;QACd,IAAI,CAACuH,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAW,qBAAqBA,CAACzD,EAAE;IACtB,IAAI,CAAC/H,qBAAqB,GAAG+H,EAAE,CAACvM,KAAK;IACrCsG,YAAY,CAACkG,OAAO,CAAC,uBAAuB,EAAED,EAAE,CAACvM,KAAK,CAAC;IACvD,IAAI,CAACmF,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACd,qBAAqB;IACjE,IAAI,CAACW,cAAc,CAACE,cAAc,GAAG,IAAI,CAACX,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACxG,IAAI,CAACS,cAAc,CAACC,eAAe,GAAG,IAAI,CAACT,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;IAC3G,IAAI,CAACQ,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACvG,IAAI,CAACM,cAAc,CAAC3D,SAAS,GAAG,CAAC;IACjC,IAAI,CAAC2D,cAAc,CAAC5D,cAAc,GAAG,IAAI,CAACyD,iBAAiB;IAC3D,IAAI,CAAC3D,wBAAwB,CAC1B0O,8BAA8B,CAAC,IAAI,CAAC5K,cAAc,CAAC,CACnDa,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACf,qBAAqB,GAAGe,QAAQ,CAACoC,UAAU;MAChD,IAAI,CAAC5D,sBAAsB,GAAGwB,QAAQ,CAACqC,KAAK;MAE5CR,UAAU,CAAC,MAAK;QACd,IAAI,CAACuH,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,IAAI,CAAC7H,2BAA2B,CAACZ,SAAS,CAAC;EAC/C;EAEAqJ,iBAAiBA,CAAA;IACf,IAAI,CAAC9K,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACd,qBAAqB;IACjE,IAAI,CAACW,cAAc,CAACE,cAAc,GAAG,IAAI,CAACX,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACxG,IAAI,CAACS,cAAc,CAACC,eAAe,GAAG,IAAI,CAACT,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;IAC3G,IAAI,CAACQ,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACvG,IAAI,CAACM,cAAc,CAAC3D,SAAS,GAAG,CAAC;IACjC,IAAI,CAAC2D,cAAc,CAAC5D,cAAc,GAAG,IAAI,CAACyD,iBAAiB;IAC3D,IAAI,CAAC3D,wBAAwB,CAC1B0O,8BAA8B,CAAC,IAAI,CAAC5K,cAAc,CAAC,CACnDa,SAAS,CAAEC,QAAQ,IAAI;MACvB,IAAI,CAACf,qBAAqB,GAAGe,QAAQ,CAACoC,UAAU;MAC/C,IAAI,CAAC5D,sBAAsB,GAAGwB,QAAQ,CAACqC,KAAK;MAE5CR,UAAU,CAAC,MAAK;QACd,IAAI,CAACuH,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAa,+BAA+BA,CAAC3D,EAAE;IAChC,IAAI,CAAC7H,oBAAoB,GAAGtB,MAAM,CAACmJ,EAAE,CAACvM,KAAK,CAAC;IAC5C;IACAsG,YAAY,CAACkG,OAAO,CAAC,sBAAsB,EAAED,EAAE,CAACvM,KAAK,CAAC;EACxD;EAEAmQ,uBAAuBA,CAAC5D,EAAE;IACxB,IAAI,CAAC5H,qBAAqB,GAAGvB,MAAM,CAACmJ,EAAE,CAACvM,KAAK,CAAC;IAC7C;IACAsG,YAAY,CAACkG,OAAO,CAAC,uBAAuB,EAAED,EAAE,CAACvM,KAAK,CAAC;EACzD;EAEAoQ,iCAAiCA,CAAC7D,EAAE;IAClC,IAAI,CAAC1H,sBAAsB,GAAG0H,EAAE,CAACvM,KAAK;IACtC;IACAsG,YAAY,CAACkG,OAAO,CAAC,wBAAwB,EAAED,EAAE,CAACvM,KAAK,CAAC;EAC1D;EAEAqQ,oBAAoBA,CAACzI,KAAiC;IACpD,IAAIA,KAAK,CAACvF,QAAQ,KAAK,qBAAqB,EAAE;MAC5C,IAAI,CAACrB,MAAM,CAACwM,IAAI,CAACrP,sCAAsC,EAAE;QACvDuP,KAAK,EAAE,QAAQ;QACf/E,IAAI,EAAE;UACJ2H,GAAG,EAAE1I,KAAK,CAACkB;SACZ;QACDyH,UAAU,EAAE;OACb,CAAC;IACJ;EACF;;;uBAthCW/P,gCAAgC,EAAApC,EAAA,CAAAoS,iBAAA,CAAApS,EAAA,CAAAqS,QAAA,GAAArS,EAAA,CAAAoS,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAAvS,EAAA,CAAAoS,iBAAA,CAAAI,EAAA,CAAAC,0BAAA,GAAAzS,EAAA,CAAAoS,iBAAA,CAAAM,EAAA,CAAAC,iCAAA,GAAA3S,EAAA,CAAAoS,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAA7S,EAAA,CAAAoS,iBAAA,CAAAU,EAAA,CAAAC,4BAAA,GAAA/S,EAAA,CAAAoS,iBAAA,CAAAY,EAAA,CAAAC,SAAA,GAAAjT,EAAA,CAAAoS,iBAAA,CAAAc,EAAA,CAAAC,kBAAA,GAAAnT,EAAA,CAAAoS,iBAAA,CAAAgB,EAAA,CAAAC,iBAAA,GAAArT,EAAA,CAAAoS,iBAAA,CAAAkB,EAAA,CAAAC,cAAA,GAAAvT,EAAA,CAAAoS,iBAAA,CAAAoB,GAAA,CAAAC,cAAA,GAAAzT,EAAA,CAAAoS,iBAAA,CAAAsB,GAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAhCvR,gCAAgC;MAAAwR,SAAA;MAAAC,QAAA,GAAA7T,EAAA,CAAA8T,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CrCpU,EAJR,CAAAC,cAAA,oBAAe,iBAC4B,aACE,aACV,mBAe1B;UAFCD,EAAA,CAAAS,UAAA,wBAAA6T,0EAAAC,MAAA;YAAA,OAAcF,GAAA,CAAA5L,gBAAA,CAAA8L,MAAA,CAAwB;UAAA,EAAC;UAK7CvU,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,aAAgE,aACrC;UAsCvBD,EArCA,CAAAwU,UAAA,IAAAC,gDAAA,kBAAiE,IAAAC,kDAAA,oBAOhE,IAAAC,kDAAA,oBASA,KAAAC,mDAAA,oBASA,KAAAC,mDAAA,oBASA,KAAAC,mDAAA,oBASA;UAIL9U,EADE,CAAAG,YAAA,EAAM,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAA4C,cACC,qBACJ;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEpEH,EADF,CAAAC,cAAA,0BAAiD,sBAK9C;UAFCD,EAAA,CAAA+U,gBAAA,yBAAAC,6EAAAT,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAAzQ,YAAA,EAAA2Q,MAAA,MAAAF,GAAA,CAAAzQ,YAAA,GAAA2Q,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxBvU,EAAA,CAAAS,UAAA,6BAAAyU,iFAAAX,MAAA;YAAA,OAAmBF,GAAA,CAAAnG,YAAA,CAAAqG,MAAA,CAAoB;UAAA,EAAC;UAExCvU,EAAA,CAAAwU,UAAA,KAAAW,uDAAA,yBAA2D;UAKjEnV,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,cAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE5DH,EADF,CAAAC,cAAA,0BAAiD,sBAK9C;UAFCD,EAAA,CAAA+U,gBAAA,yBAAAK,6EAAAb,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAA1P,kBAAA,EAAA4P,MAAA,MAAAF,GAAA,CAAA1P,kBAAA,GAAA4P,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAC9BvU,EAAA,CAAAS,UAAA,6BAAA4U,iFAAAd,MAAA;YAAA,OAAmBF,GAAA,CAAA9F,cAAA,CAAAgG,MAAA,CAAsB;UAAA,EAAC;UAE1CvU,EAAA,CAAAwU,UAAA,KAAAc,uDAAA,yBAGC;UAKPtV,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,cAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE1DH,EADF,CAAAC,cAAA,0BAAiD,iBAC0B;UAAjCD,EAAA,CAAA+U,gBAAA,2BAAAQ,0EAAAhB,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAAhG,gBAAA,EAAAkG,MAAA,MAAAF,GAAA,CAAAhG,gBAAA,GAAAkG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAE1EvU,EAFI,CAAAG,YAAA,EAAyE,EAC1D,EACb;UAEJH,EADF,CAAAC,cAAA,eAAgE,kBAM7D;UAFCD,EAAA,CAAAS,UAAA,mBAAA+U,mEAAA;YAAA,OAASnB,GAAA,CAAA/F,QAAA,EAAU;UAAA,EAAC;UAGpBtO,EAAA,CAAAE,MAAA,gBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAyC,cACV,qBAgB1B;UADCD,EADA,CAAAS,UAAA,wBAAAgV,2EAAAlB,MAAA;YAAA,OAAcF,GAAA,CAAA9L,eAAA,CAAAgM,MAAA,CAAuB;UAAA,EAAC,yBAAAmB,4EAAAnB,MAAA;YAAA,OACvBF,GAAA,CAAA7F,WAAA,CAAA+F,MAAA,CAAmB;UAAA,EAAC;UAK3CvU,EAHM,CAAAG,YAAA,EAAY,EACR,EACF,EACE;UAKJH,EAJN,CAAAC,cAAA,mBAA2C,eAGE,cACV;UAC3BD,EAAA,CAAA2V,SAAA,qBAUc;UAElB3V,EADE,CAAAG,YAAA,EAAM,EACF;UAKFH,EAFJ,CAAAC,cAAA,cAAgE,cACpC,kBAC6C;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAqE;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAC,cAAA,kBAAqE;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxFH,EAAA,CAAAC,cAAA,kBAAqE;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAEvFF,EAFuF,CAAAG,YAAA,EAAS,EACxF,EACF;UAKFH,EAFF,CAAAC,cAAA,cAA4C,eACD,qBACJ;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAExEH,EADF,CAAAC,cAAA,0BAAiD,sBAGI;UADlDD,EAAA,CAAA+U,gBAAA,yBAAAa,6EAAArB,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAAjO,qBAAA,EAAAmO,MAAA,MAAAF,GAAA,CAAAjO,qBAAA,GAAAmO,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UACjCvU,EAAA,CAAAS,UAAA,6BAAAoV,iFAAAtB,MAAA;YAAA,OAAmBF,GAAA,CAAAzC,qBAAA,CAAA2C,MAAA,CAA6B;UAAA,EAAC;UAChDvU,EAAA,CAAAwU,UAAA,KAAAsB,uDAAA,yBAA2D;UAKjE9V,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE9DH,EADF,CAAAC,cAAA,0BAAiD,sBAGe;UAD5DD,EAAA,CAAA+U,gBAAA,yBAAAgB,6EAAAxB,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAA/N,oBAAA,EAAAiO,MAAA,MAAAF,GAAA,CAAA/N,oBAAA,GAAAiO,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAChCvU,EAAA,CAAAS,UAAA,6BAAAuV,iFAAAzB,MAAA;YAAA,OAAmBF,GAAA,CAAAvC,+BAAA,CAAAyC,MAAA,CAAuC;UAAA,EAAC;UAC3DvU,EAAA,CAAAwU,UAAA,KAAAyB,uDAAA,yBAAuF;UAG7FjW,EAFI,CAAAG,YAAA,EAAa,EACC,EACZ;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEhEH,EADF,CAAAC,cAAA,0BAAiD,sBAEiB;UADpBD,EAAA,CAAA+U,gBAAA,yBAAAmB,6EAAA3B,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAA5N,sBAAA,EAAA8N,MAAA,MAAAF,GAAA,CAAA5N,sBAAA,GAAA8N,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAC5EvU,EAAA,CAAAS,UAAA,6BAAA0V,iFAAA5B,MAAA;YAAA,OAAmBF,GAAA,CAAArC,iCAAA,CAAAuC,MAAA,CAAyC;UAAA,EAAC;UAC5DvU,EAAA,CAAAwU,UAAA,KAAA4B,uDAAA,yBAAsE;UAK7EpW,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEhEH,EADF,CAAAC,cAAA,0BAAiD,sBAEO;UADVD,EAAA,CAAA+U,gBAAA,yBAAAsB,6EAAA9B,MAAA;YAAAvU,EAAA,CAAAiV,kBAAA,CAAAZ,GAAA,CAAA9N,qBAAA,EAAAgO,MAAA,MAAAF,GAAA,CAAA9N,qBAAA,GAAAgO,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAC3EvU,EAAA,CAAAS,UAAA,6BAAA6V,iFAAA/B,MAAA;YAAA,OAAmBF,GAAA,CAAAtC,uBAAA,CAAAwC,MAAA,CAA+B;UAAA,EAAC;UACnDvU,EAAA,CAAAwU,UAAA,KAAA+B,uDAAA,yBAA+E;UAGrFvW,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA4E,kBAE3C;UAA9BD,EAAA,CAAAS,UAAA,mBAAA+V,mEAAA;YAAA,OAASnC,GAAA,CAAAxC,iBAAA,EAAmB;UAAA,EAAC;UAAC7R,EAAA,CAAAE,MAAA,cAAM;UAEvCF,EAFuC,CAAAG,YAAA,EAAS,EAC1C,EACA;UAKJH,EAFJ,CAAAC,cAAA,cAAyC,cACV,qBAc6B;UAA7CD,EADA,CAAAS,UAAA,wBAAAgW,2EAAAlC,MAAA;YAAA,OAAcF,GAAA,CAAAlL,wBAAA,CAAAoL,MAAA,CAAgC;UAAA,EAAC,yBAAAmC,4EAAAnC,MAAA;YAAA,OAChCF,GAAA,CAAApC,oBAAA,CAAAsC,MAAA,CAA4B;UAAA,EAAC;UAMjEvU,EALU,CAAAG,YAAA,EAAY,EACR,EACF,EAEE,EACE;;;UA/ONH,EAAA,CAAAI,SAAA,GAAe;UAYfJ,EAZA,CAAAyB,UAAA,OAAA4S,GAAA,CAAAxP,QAAA,CAAe,YAAAwP,GAAA,CAAAtP,sBAAA,CACmB,4BAGP,cAAAsP,GAAA,CAAAvP,iBAAA,CACI,aAAAuP,GAAA,CAAApP,SAAA,CACT,0BACG,wBACF,uBACD,kBACL,oBAAAjF,EAAA,CAAA2W,eAAA,KAAAC,GAAA,EAEoB;UAOP5W,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAyB,UAAA,SAAA4S,GAAA,CAAA7T,yBAAA,CAA+B;UAE9DR,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAyB,UAAA,SAAA4S,GAAA,CAAA/O,eAAA,CAAqB;UASnBtF,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAyB,UAAA,SAAA4S,GAAA,CAAAlP,UAAA,CAAgB;UAShBnF,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAyB,UAAA,SAAA4S,GAAA,CAAAlP,UAAA,CAAgB;UAShBnF,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAyB,UAAA,SAAA4S,GAAA,CAAAlP,UAAA,CAAgB;UAShBnF,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAAyB,UAAA,SAAA4S,GAAA,CAAAlP,UAAA,IAAAkP,GAAA,CAAAjP,aAAA,CAAiC;UAgBhCpF,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAA6W,gBAAA,UAAAxC,GAAA,CAAAzQ,YAAA,CAAwB;UAGQ5D,EAAA,CAAAI,SAAA,EAAO;UAAPJ,EAAA,CAAAyB,UAAA,YAAA4S,GAAA,CAAA5Q,IAAA,CAAO;UAWvCzD,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAA6W,gBAAA,UAAAxC,GAAA,CAAA1P,kBAAA,CAA8B;UAIR3E,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAyB,UAAA,YAAA4S,GAAA,CAAAzP,uBAAA,CAA0B;UAWV5E,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAA6W,gBAAA,YAAAxC,GAAA,CAAAhG,gBAAA,CAA8B;UAiBtErO,EAAA,CAAAI,SAAA,GAAc;UAWdJ,EAXA,CAAAyB,UAAA,OAAA4S,GAAA,CAAA3Q,OAAA,CAAc,YAAA2Q,GAAA,CAAArQ,qBAAA,CAEmB,4BAEN,cAAAqQ,GAAA,CAAA1Q,gBAAA,CACG,aAAA0Q,GAAA,CAAA5P,QAAA,CACT,oBAAAzE,EAAA,CAAA2W,eAAA,KAAAC,GAAA,EACgB,0BACZ,yBACD,uBACF,kBACL;UAaN5W,EAAA,CAAAI,SAAA,GAA0B;UAS1BJ,EATA,CAAAyB,UAAA,OAAA4S,GAAA,CAAA3N,mBAAA,CAA0B,YAAA2N,GAAA,CAAA9O,mBAAA,CACK,4BAEJ,cAAA8O,GAAA,CAAAvP,iBAAA,CACI,aAAAuP,GAAA,CAAApP,SAAA,CACT,0BACG,wBACF,wBACA,mBACL;UAqB1BjF,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAA6W,gBAAA,UAAAxC,GAAA,CAAAjO,qBAAA,CAAiC;UAEApG,EAAA,CAAAI,SAAA,EAAO;UAAPJ,EAAA,CAAAyB,UAAA,YAAA4S,GAAA,CAAA5Q,IAAA,CAAO;UAUvCzD,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAA6W,gBAAA,UAAAxC,GAAA,CAAA/N,oBAAA,CAAgC;UAEAtG,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAyB,UAAA,YAAA4S,GAAA,CAAAnO,0BAAA,CAA6B;UAOnBlG,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAA6W,gBAAA,UAAAxC,GAAA,CAAA5N,sBAAA,CAAkC;UAE3CzG,EAAA,CAAAI,SAAA,EAAY;UAAZJ,EAAA,CAAAyB,UAAA,YAAA4S,GAAA,CAAAtE,SAAA,CAAY;UASH/P,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA6W,gBAAA,UAAAxC,GAAA,CAAA9N,qBAAA,CAAiC;UAE3CvG,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAyB,UAAA,YAAA4S,GAAA,CAAAlO,kBAAA,CAAqB;UAa9CnG,EAAA,CAAAI,SAAA,GAAuB;UAWvBJ,EAXA,CAAAyB,UAAA,OAAA4S,GAAA,CAAA1N,gBAAA,CAAuB,YAAA0N,GAAA,CAAAtO,gBAAA,CACK,4BAGD,cAAAsO,GAAA,CAAAxN,oBAAA,CACO,aAAAwN,GAAA,CAAAzN,iBAAA,CACJ,oBAAA5G,EAAA,CAAA2W,eAAA,KAAAC,GAAA,EACO,0BACZ,yBACD,uBACF,kBACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}