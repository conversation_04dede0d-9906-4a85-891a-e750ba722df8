{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport { DashboardListingType } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\nimport { format } from 'date-fns';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/ca-dashboard-service\";\nimport * as i2 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/divider\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/card\";\nimport * as i12 from \"@angular/material/tooltip\";\nimport * as i13 from \"@angular/common\";\nimport * as i14 from \"./statistics-charts/es-letter-status.component\";\nimport * as i15 from \"./statistics-charts/tax-residents-outside-bahamas.component\";\nimport * as i16 from \"./red-flags-events/red-flags-summary.component\";\nfunction CADashboardSummaryComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CADashboardSummaryComponent_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.RedFlagsSettingsClicked());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"Settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CADashboardSummaryComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1, \"Stats Summary:\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CADashboardSummaryComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\")(2, \"div\", 19)(3, \"div\", 20);\n    i0.ɵɵtext(4, \"# Of Entities*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6, \"# Of ES Filings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 20);\n    i0.ɵɵtext(8, \"# Of Entities with Filing Overdue*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 20);\n    i0.ɵɵtext(10, \"# Of Pending Assessments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 20);\n    i0.ɵɵtext(12, \"# Of declarations submitted in the OTAS as reported by RAs\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 19)(14, \"div\", 21);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 21);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CADashboardSummaryComponent_div_17_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClicked($event, \"overdue\"));\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CADashboardSummaryComponent_div_17_Template_a_click_22_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClicked($event, \"pending\"));\n    });\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 21);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(26, \"mat-divider\", 23);\n    i0.ɵɵelementStart(27, \"div\");\n    i0.ɵɵelement(28, \"app-tax-resident-outside-bahamas\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"mat-divider\", 23);\n    i0.ɵɵelementStart(30, \"div\");\n    i0.ɵɵelement(31, \"app-es-letter-status\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.numberOfEntities);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.numberOfESFilings);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.numberOfEntitiesWithFilingOverdue);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.numberOfPendingAssessments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.numberOfDeclarationSubmittedOTAS);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"statsSummaryData\", ctx_r1.statsSummaryData);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"statsSummaryData\", ctx_r1.statsSummaryData)(\"selectedYear\", ctx_r1.dashboardData == null ? null : ctx_r1.dashboardData.fiscalYear);\n  }\n}\nfunction CADashboardSummaryComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\");\n    i0.ɵɵelement(2, \"app-red-flags-summary\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"statsSummaryData\", ctx_r1.statsSummaryData);\n  }\n}\nfunction CADashboardSummaryComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n}\nfunction CADashboardSummaryComponent_mat_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const year_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", year_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", year_r4, \" \");\n  }\n}\n/** Rendering Summary section in Dashboard page. #1083. R4.8 ES ESS Monitoring – Stats Summary*/\nexport class CADashboardSummaryComponent extends AppComponentBase {\n  /**\n   * @constructor\n   * @param {Injector} injector\n   */\n  constructor(injector, dashboardService, CADashboardController, router) {\n    super(injector);\n    this.dashboardService = dashboardService;\n    this.CADashboardController = CADashboardController;\n    this.router = router;\n    this.years = [];\n    this.selectedFiscalYear = new Date().getFullYear();\n    this.currentTab = 0;\n    /** Display latest statistic updated datetime and format as local datetime yyyy-MM-DD hh:mm:ss */\n    this.latestStatisticDateTime = '';\n    // Summary data\n    this.numberOfEntities = 0;\n    this.numberOfESFilings = 0;\n    this.numberOfEntitiesWithFilingOverdue = 0;\n    this.numberOfPendingAssessments = 0;\n    this.numberOfDeclarationSubmittedOTAS = 0;\n    this.lastUpdatedText = \"STATS ARE REFRESHED NIGHTLY. LAST UPDATE AS OF\";\n    this.dashboardService.onDashboardTabChanged.subscribe(value => {\n      switch (value) {\n        case 0:\n          // Display summary information for \"Statistics\" tab.\n          //console.log('Statistics tab selected');\n          this.lastUpdatedText = \"STATS ARE REFRESHED NIGHTLY. LAST UPDATE AS OF\";\n          break;\n        case 1:\n          // Display summary information for \"Red Flag Events\" tab.\n          //console.log('Red Flag Events tab selected');\n          this.lastUpdatedText = \"RED FLAG EVENTS ARE IDENTIFIED NIGHTLY. LAST UPDATE AS OF \";\n          break;\n        case 2:\n          // Display summary information for \"Additional Statistics\" tab.\n          //console.log('Additional Statistics tab selected');\n          this.lastUpdatedText = \"STATS ARE REFRESHED NIGHTLY. LAST UPDATE AS OF\";\n          break;\n      }\n      this.scrollToTop();\n    });\n    this.dashboardService.onDashboardTabChanged.subscribe(value => {\n      this.currentTab = value;\n    });\n  }\n  ngOnInit() {\n    this.dashboardService.getFiscalYears();\n    this.dashboardService.getLatestStatisticDateTime();\n    this.dashboardService.fiscalYearsObservable.subscribe(value => {\n      this.years = value;\n    });\n    //Note: Returned datetime is UTC datetime.\n    this.dashboardService.latestStatisticDateTimeObservable.subscribe(value => {\n      //\n      // TODO: There is bug in ABP-cli, return DateTime? type as String with \"\", which caused datetime conversion error.\n      // Solution: Remove \"\" from datetime string.\n      //\n      value = value?.toString().replaceAll('\"', '');\n      //\n      //Note: convert the returned UTC datetime to local datetime string.\n      //\n      this.latestStatisticDateTime = format(new Date(value), 'yyyy-MM-dd hh:mm:ss aa');\n    });\n    // Default value is current year.\n    this.selectedFiscalYear = this.dashboardService.getSelectedFiscalYear();\n  }\n  ngOnChanges(changes) {\n    if (changes.dashboardData) {\n      if (this.dashboardData && this.dashboardData.fiscalYear) {\n        this.updateStatsSummary();\n      } else {\n        this.numberOfEntities = 0;\n        this.numberOfESFilings = 0;\n        this.numberOfEntitiesWithFilingOverdue = 0;\n        this.numberOfPendingAssessments = 0;\n        this.numberOfDeclarationSubmittedOTAS = 0;\n      }\n    }\n  }\n  updateStatsSummary() {\n    this.CADashboardController.getDashboardStatsSummaryByYear(this.dashboardData.fiscalYear).subscribe(result => {\n      this.statsSummaryData = result;\n      this.numberOfEntities = result.numOfEntities ?? 0;\n      this.numberOfESFilings = result.numOfFilingSubmitted ?? 0;\n      this.numberOfEntitiesWithFilingOverdue = result.numOfEntitiesFilingOverdue ?? 0;\n      this.numberOfPendingAssessments = result.numOfAssessmentPending ?? 0;\n      this.numberOfDeclarationSubmittedOTAS = result.numOfFiledOTAS ?? 0;\n    });\n  }\n  onClicked(event, source) {\n    if (source === 'pending') {\n      this.router.navigate(['/search-result'], {\n        queryParams: {\n          source: \"dashboard\" /* DashboardConstants.DASHBOARD */,\n          type: \"Stats Summary\" /* DashboardConstants.STATS_SUMMARY */,\n          year: this.dashboardData.fiscalYear,\n          listingType: DashboardListingType.NumOfAssessmentPending_4_8\n        }\n      });\n    } else if (source === 'overdue') {\n      this.router.navigate(['/search-result'], {\n        queryParams: {\n          source: \"dashboard\" /* DashboardConstants.DASHBOARD */,\n          type: \"Stats Summary\" /* DashboardConstants.STATS_SUMMARY */,\n          year: this.dashboardData.fiscalYear,\n          listingType: DashboardListingType.NumOfEntitiesFilingOverdue_4_8\n        }\n      });\n    }\n  }\n  onFiscalYearChanged(event) {\n    this.dashboardService.setSelectedFiscalYear(event.value);\n  }\n  RedFlagsSettingsClicked() {\n    this.router.navigate(['/redflags']);\n  }\n  scrollToTop() {\n    const element = document.querySelector('.ps'); // of the lepton x content area\n    element.scroll({\n      top: 0,\n      left: 0,\n      behavior: 'smooth'\n    });\n  }\n  static {\n    this.ɵfac = function CADashboardSummaryComponent_Factory(t) {\n      return new (t || CADashboardSummaryComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.CADashboardService), i0.ɵɵdirectiveInject(i2.CADashboardContorllerService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CADashboardSummaryComponent,\n      selectors: [[\"app-ca-dashboard-summary\"]],\n      inputs: {\n        dashboardData: \"dashboardData\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 27,\n      vars: 9,\n      consts: [[1, \"dashboard-summary-section\", \"dashboard-card-title\"], [1, \"col-md-5\"], [1, \"col-md-5\", \"dashboard-status\"], [\"class\", \"col-md-2\", 4, \"ngIf\"], [1, \"divider-margin\"], [1, \"row\"], [1, \"col-md-9\"], [1, \"dashboard-summary\"], [1, \"dashboard-card-sub-title\"], [\"class\", \"dashboard-card-title\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"col-md-3\"], [1, \"dashboard-years\"], [1, \"dashboard-years-label\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-2\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"Red Flag Settings\", 1, \"ui-button\", 3, \"click\"], [1, \"dashboard-card-title\"], [1, \"dashboard-table\"], [1, \"col\", \"title\"], [1, \"col\", \"item\"], [1, \"clickable\", 3, \"click\"], [1, \"divider-margin-extra\"], [3, \"statsSummaryData\"], [3, \"statsSummaryData\", \"selectedYear\"], [3, \"value\"]],\n      template: function CADashboardSummaryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-card-header\")(2, \"div\", 0)(3, \"div\", 1);\n          i0.ɵɵtext(4, \"ECONOMIC SUBSTANCE (ES) COMPLIANCE MONITORING\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 2);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, CADashboardSummaryComponent_div_7_Template, 5, 0, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-card-content\");\n          i0.ɵɵelement(9, \"mat-divider\", 4);\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"mat-card\", 7)(13, \"mat-card-header\")(14, \"mat-card-title\", 8);\n          i0.ɵɵtemplate(15, CADashboardSummaryComponent_div_15_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"mat-card-content\");\n          i0.ɵɵtemplate(17, CADashboardSummaryComponent_div_17_Template, 32, 8, \"div\", 10)(18, CADashboardSummaryComponent_div_18_Template, 3, 1, \"div\", 10)(19, CADashboardSummaryComponent_div_19_Template, 1, 0, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12)(22, \"mat-form-field\")(23, \"mat-label\", 13);\n          i0.ɵɵtext(24, \"Fiscal Period End\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-select\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CADashboardSummaryComponent_Template_mat_select_ngModelChange_25_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedFiscalYear, $event) || (ctx.selectedFiscalYear = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CADashboardSummaryComponent_Template_mat_select_selectionChange_25_listener($event) {\n            return ctx.onFiscalYearChanged($event);\n          });\n          i0.ɵɵtemplate(26, CADashboardSummaryComponent_mat_option_26_Template, 2, 2, \"mat-option\", 15);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate2(\" \", ctx.lastUpdatedText, \" \", ctx.latestStatisticDateTime, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab == 1);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab == 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab == 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab == 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab == 2);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedFiscalYear);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.years);\n        }\n      },\n      dependencies: [i4.NgControlStatus, i5.MatFormField, i5.MatLabel, i6.MatIcon, i7.MatDivider, i8.MatButton, i9.MatSelect, i10.MatOption, i11.MatCard, i11.MatCardContent, i11.MatCardHeader, i11.MatCardTitle, i12.MatTooltip, i13.NgForOf, i13.NgIf, i4.NgModel, i14.EsLetterStatusComponent, i15.TaxResidentsOutsideBahamasComponent, i16.RedFlagsSummaryComponent],\n      styles: [\".dashboard-years {\\n  display: flex;\\n  flex-direction: row-reverse;\\n}\\n\\n.dashboard-years-label {\\n  font-size: 1.1rem;\\n  color: #000000;\\n}\\n\\n.dashboard-summary {\\n  box-shadow: none !important;\\n}\\n\\n.dashboard-summary-section {\\n  display: flex;\\n  width: 100%!important;\\n\\n}\\n\\n.dashboard-status{\\n  display: flex;\\n  flex-direction:column-reverse;\\n  align-items: flex-end;\\n  padding-bottom: 5px;\\n  padding-right: 5px;\\n}\\n\\n.divider-margin {\\n  margin: 1em !important;\\n}\\n\\n.divider-margin-extra {\\n  margin: 2em !important;\\n}\\n.clickable {\\n  cursor: pointer;\\n  color: #657c91;\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNhLWRhc2hib2FyZC1zdW1tYXJ5LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFhO0VBQ2IsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSwyQkFBMkI7QUFDN0I7O0FBRUE7RUFDRSxhQUFhO0VBQ2IscUJBQXFCOztBQUV2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYiw2QkFBNkI7RUFDN0IscUJBQXFCO0VBQ3JCLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxzQkFBc0I7QUFDeEI7QUFDQTtFQUNFLGVBQWU7RUFDZixjQUFjO0VBQ2QsMEJBQTBCO0FBQzVCIiwiZmlsZSI6ImNhLWRhc2hib2FyZC1zdW1tYXJ5LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuZGFzaGJvYXJkLXllYXJzIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiByb3ctcmV2ZXJzZTtcclxufVxyXG5cclxuLmRhc2hib2FyZC15ZWFycy1sYWJlbCB7XHJcbiAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgY29sb3I6ICMwMDAwMDA7XHJcbn1cclxuXHJcbi5kYXNoYm9hcmQtc3VtbWFyeSB7XHJcbiAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uZGFzaGJvYXJkLXN1bW1hcnktc2VjdGlvbiB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICB3aWR0aDogMTAwJSFpbXBvcnRhbnQ7XHJcblxyXG59XHJcblxyXG4uZGFzaGJvYXJkLXN0YXR1c3tcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOmNvbHVtbi1yZXZlcnNlO1xyXG4gIGFsaWduLWl0ZW1zOiBmbGV4LWVuZDtcclxuICBwYWRkaW5nLWJvdHRvbTogNXB4O1xyXG4gIHBhZGRpbmctcmlnaHQ6IDVweDtcclxufVxyXG5cclxuLmRpdmlkZXItbWFyZ2luIHtcclxuICBtYXJnaW46IDFlbSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uZGl2aWRlci1tYXJnaW4tZXh0cmEge1xyXG4gIG1hcmdpbjogMmVtICFpbXBvcnRhbnQ7XHJcbn1cclxuLmNsaWNrYWJsZSB7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGNvbG9yOiAjNjU3YzkxO1xyXG4gIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbnRhaW5lcnMvY2EtZGFzaGJvYXJkLXN1bW1hcnkuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQWE7RUFDYiwyQkFBMkI7QUFDN0I7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixxQkFBcUI7O0FBRXZCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDZCQUE2QjtFQUM3QixxQkFBcUI7RUFDckIsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLHNCQUFzQjtBQUN4QjtBQUNBO0VBQ0UsZUFBZTtFQUNmLGNBQWM7RUFDZCwwQkFBMEI7QUFDNUI7QUFDQSxnakRBQWdqRCIsInNvdXJjZXNDb250ZW50IjpbIi5kYXNoYm9hcmQteWVhcnMge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IHJvdy1yZXZlcnNlO1xyXG59XHJcblxyXG4uZGFzaGJvYXJkLXllYXJzLWxhYmVsIHtcclxuICBmb250LXNpemU6IDEuMXJlbTtcclxuICBjb2xvcjogIzAwMDAwMDtcclxufVxyXG5cclxuLmRhc2hib2FyZC1zdW1tYXJ5IHtcclxuICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5kYXNoYm9hcmQtc3VtbWFyeS1zZWN0aW9uIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIHdpZHRoOiAxMDAlIWltcG9ydGFudDtcclxuXHJcbn1cclxuXHJcbi5kYXNoYm9hcmQtc3RhdHVze1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246Y29sdW1uLXJldmVyc2U7XHJcbiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xyXG4gIHBhZGRpbmctYm90dG9tOiA1cHg7XHJcbiAgcGFkZGluZy1yaWdodDogNXB4O1xyXG59XHJcblxyXG4uZGl2aWRlci1tYXJnaW4ge1xyXG4gIG1hcmdpbjogMWVtICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5kaXZpZGVyLW1hcmdpbi1leHRyYSB7XHJcbiAgbWFyZ2luOiAyZW0gIWltcG9ydGFudDtcclxufVxyXG4uY2xpY2thYmxlIHtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgY29sb3I6ICM2NTdjOTE7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponentBase", "DashboardListingType", "format", "i0", "ɵɵelementStart", "ɵɵlistener", "CADashboardSummaryComponent_div_7_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "RedFlagsSettingsClicked", "ɵɵtext", "ɵɵelementEnd", "CADashboardSummaryComponent_div_17_Template_a_click_19_listener", "$event", "_r3", "onClicked", "CADashboardSummaryComponent_div_17_Template_a_click_22_listener", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "numberOfEntities", "numberOfESFilings", "numberOfEntitiesWithFilingOverdue", "numberOfPendingAssessments", "numberOfDeclarationSubmittedOTAS", "ɵɵproperty", "statsSummaryData", "dashboardData", "fiscalYear", "year_r4", "ɵɵtextInterpolate1", "CADashboardSummaryComponent", "constructor", "injector", "dashboardService", "CADashboardController", "router", "years", "selectedFiscalYear", "Date", "getFullYear", "currentTab", "latestStatisticDateTime", "lastUpdatedText", "onDashboardTabChanged", "subscribe", "value", "scrollToTop", "ngOnInit", "getFiscalYears", "getLatestStatisticDateTime", "fiscalYearsObservable", "latestStatisticDateTimeObservable", "toString", "replaceAll", "getSelectedFiscalYear", "ngOnChanges", "changes", "updateStatsSummary", "getDashboardStatsSummaryByYear", "result", "numOfEntities", "numOfFilingSubmitted", "numOfEntitiesFilingOverdue", "numOfAssessmentPending", "numOfFiledOTAS", "event", "source", "navigate", "queryParams", "type", "year", "listingType", "NumOfAssessmentPending_4_8", "NumOfEntitiesFilingOverdue_4_8", "onFiscalYearChanged", "setSelectedFiscalYear", "element", "document", "querySelector", "scroll", "top", "left", "behavior", "ɵɵdirectiveInject", "Injector", "i1", "CADashboardService", "i2", "CADashboardContorllerService", "i3", "Router", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "CADashboardSummaryComponent_Template", "rf", "ctx", "ɵɵtemplate", "CADashboardSummaryComponent_div_7_Template", "CADashboardSummaryComponent_div_15_Template", "CADashboardSummaryComponent_div_17_Template", "CADashboardSummaryComponent_div_18_Template", "CADashboardSummaryComponent_div_19_Template", "ɵɵtwoWayListener", "CADashboardSummaryComponent_Template_mat_select_ngModelChange_25_listener", "ɵɵtwoWayBindingSet", "CADashboardSummaryComponent_Template_mat_select_selectionChange_25_listener", "CADashboardSummaryComponent_mat_option_26_Template", "ɵɵtextInterpolate2", "ɵɵtwoWayProperty"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\ca-dashboard-summary.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\dashboard\\containers\\ca-dashboard-summary.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  Injector,\r\n  Input,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { MatSelectChange } from '@angular/material/select';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { CADashboardService } from '../services/ca-dashboard-service';\r\nimport { DashboardListingType, StatisticMainDto, DashboardStatsSummaryDto } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/monitoring-dashboard';\r\nimport { format } from 'date-fns';\r\nimport{CADashboardContorllerService} from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers/cadashboard-contorller.service'\r\nimport { DashboardConstants } from '@app/shared/constants';\r\nimport { Router } from '@angular/router';\r\n\r\n/** Rendering Summary section in Dashboard page. #1083. R4.8 ES ESS Monitoring – Stats Summary*/\r\n@Component({\r\n  encapsulation: ViewEncapsulation.None,\r\n  selector: 'app-ca-dashboard-summary',\r\n  templateUrl: './ca-dashboard-summary.component.html',\r\n  styleUrls: ['./ca-dashboard-summary.component.css'],\r\n})\r\nexport class CADashboardSummaryComponent\r\n  extends AppComponentBase\r\n  implements OnInit, OnChanges\r\n{\r\n  @Input() dashboardData: StatisticMainDto;\r\n  years: number[] = [];\r\n  selectedFiscalYear: number = new Date().getFullYear();\r\n  currentTab = 0;\r\n\r\n  /** Display latest statistic updated datetime and format as local datetime yyyy-MM-DD hh:mm:ss */\r\n  latestStatisticDateTime = '';\r\n\r\n  // Summary data\r\n  numberOfEntities: number = 0;\r\n  numberOfESFilings: number = 0;\r\n  numberOfEntitiesWithFilingOverdue: number = 0;\r\n  numberOfPendingAssessments: number = 0;\r\n  numberOfDeclarationSubmittedOTAS: number = 0;\r\n  statsSummaryData: DashboardStatsSummaryDto\r\n  lastUpdatedText = \"STATS ARE REFRESHED NIGHTLY. LAST UPDATE AS OF\";\r\n  /**\r\n   * @constructor\r\n   * @param {Injector} injector\r\n   */\r\n  constructor(\r\n    injector: Injector,\r\n    private dashboardService: CADashboardService,\r\n    private CADashboardController: CADashboardContorllerService,\r\n    private router: Router\r\n  ) {\r\n    super(injector);\r\n    this.dashboardService.onDashboardTabChanged.subscribe((value) => {\r\n      switch (value) {\r\n        case 0:\r\n          // Display summary information for \"Statistics\" tab.\r\n          //console.log('Statistics tab selected');\r\n          this.lastUpdatedText = \"STATS ARE REFRESHED NIGHTLY. LAST UPDATE AS OF\";\r\n          break;\r\n        case 1:\r\n          // Display summary information for \"Red Flag Events\" tab.\r\n          //console.log('Red Flag Events tab selected');\r\n          this.lastUpdatedText = \"RED FLAG EVENTS ARE IDENTIFIED NIGHTLY. LAST UPDATE AS OF \";\r\n          break;\r\n        case 2:\r\n          // Display summary information for \"Additional Statistics\" tab.\r\n          //console.log('Additional Statistics tab selected');\r\n          this.lastUpdatedText = \"STATS ARE REFRESHED NIGHTLY. LAST UPDATE AS OF\";\r\n          break;\r\n      }\r\n      this.scrollToTop();\r\n    });\r\n\r\n    this.dashboardService.onDashboardTabChanged.subscribe((value) => {\r\n      this.currentTab = value;\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.dashboardService.getFiscalYears();\r\n    this.dashboardService.getLatestStatisticDateTime();\r\n    this.dashboardService.fiscalYearsObservable.subscribe((value) => {\r\n      this.years = value;\r\n    });\r\n    //Note: Returned datetime is UTC datetime.\r\n    this.dashboardService.latestStatisticDateTimeObservable.subscribe(\r\n      (value) => {\r\n        //\r\n        // TODO: There is bug in ABP-cli, return DateTime? type as String with \"\", which caused datetime conversion error.\r\n        // Solution: Remove \"\" from datetime string.\r\n        //\r\n        value = value?.toString().replaceAll('\"', '');\r\n        //\r\n        //Note: convert the returned UTC datetime to local datetime string.\r\n        //\r\n        this.latestStatisticDateTime = format(\r\n          new Date(value),\r\n          'yyyy-MM-dd hh:mm:ss aa'\r\n        );\r\n      }\r\n    );\r\n\r\n    // Default value is current year.\r\n    this.selectedFiscalYear = this.dashboardService.getSelectedFiscalYear();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.dashboardData) {\r\n      if(this.dashboardData && this.dashboardData.fiscalYear){\r\n        this.updateStatsSummary();\r\n      }else{\r\n        this.numberOfEntities = 0;\r\n        this.numberOfESFilings = 0;\r\n        this.numberOfEntitiesWithFilingOverdue = 0;\r\n        this.numberOfPendingAssessments = 0;\r\n        this.numberOfDeclarationSubmittedOTAS = 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  updateStatsSummary(){\r\n    this.CADashboardController.getDashboardStatsSummaryByYear(this.dashboardData.fiscalYear).subscribe(result =>{\r\n      this.statsSummaryData = result;\r\n      this.numberOfEntities = result.numOfEntities ?? 0;\r\n      this.numberOfESFilings = result.numOfFilingSubmitted ?? 0;\r\n      this.numberOfEntitiesWithFilingOverdue = result.numOfEntitiesFilingOverdue ?? 0;\r\n      this.numberOfPendingAssessments = result.numOfAssessmentPending ?? 0;\r\n      this.numberOfDeclarationSubmittedOTAS = result.numOfFiledOTAS ?? 0;\r\n    });\r\n  }\r\n  \r\n  onClicked(event, source){\r\n    if(source === 'pending'){\r\n      this.router.navigate(['/search-result'], { queryParams: {source: DashboardConstants.DASHBOARD, type: DashboardConstants.STATS_SUMMARY, year: this.dashboardData.fiscalYear, listingType: DashboardListingType.NumOfAssessmentPending_4_8}});\r\n    }\r\n    else if(source === 'overdue'){\r\n      this.router.navigate(['/search-result'], { queryParams: {source: DashboardConstants.DASHBOARD, type: DashboardConstants.STATS_SUMMARY, year: this.dashboardData.fiscalYear, listingType: DashboardListingType.NumOfEntitiesFilingOverdue_4_8}});\r\n    }\r\n  }\r\n\r\n  onFiscalYearChanged(event: MatSelectChange) {\r\n    this.dashboardService.setSelectedFiscalYear(event.value as number);\r\n  }\r\n\r\n  RedFlagsSettingsClicked(){\r\n    this.router.navigate(['/redflags']);\r\n  }\r\n\r\n  scrollToTop(){\r\n    const element = document.querySelector('.ps'); // of the lepton x content area\r\n    element.scroll({\r\n      top: 0,\r\n      left: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  }\r\n}\r\n", "<mat-card>\r\n  <mat-card-header>\r\n    <div class=\"dashboard-summary-section dashboard-card-title\">\r\n      <div class=\"col-md-5\">ECONOMIC SUBSTANCE (ES) COMPLIANCE MONITORING</div>\r\n      <div class=\"col-md-5 dashboard-status\">\r\n        {{ lastUpdatedText }}\r\n        {{ latestStatisticDateTime }}\r\n      </div>\r\n      <div class=\"col-md-2\" *ngIf=\"currentTab==1\">\r\n        <button mat-raised-button class=\"ui-button\" matTooltip=\"Red Flag Settings\" (click)=\"RedFlagsSettingsClicked()\"><mat-icon>settings</mat-icon>Settings</button>\r\n      </div>\r\n   \r\n    </div>\r\n  </mat-card-header>\r\n  <mat-card-content>\r\n    <mat-divider class=\"divider-margin\"></mat-divider>\r\n    <div class=\"row\">\r\n      <div class=\"col-md-9\">\r\n        <mat-card class=\"dashboard-summary\">\r\n          <mat-card-header>\r\n            <mat-card-title class=\"dashboard-card-sub-title\">\r\n              <div *ngIf=\"currentTab==0\" class=\"dashboard-card-title\">Stats Summary:</div>\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"currentTab==0\">\r\n              <div>\r\n                <div class=\"dashboard-table\">\r\n                  <div class=\"col title\"># Of Entities*</div>\r\n                  <div class=\"col title\"># Of ES Filings</div>\r\n                  <div class=\"col title\"># Of Entities with Filing Overdue*</div>\r\n                  <div class=\"col title\"># Of Pending Assessments</div>\r\n                  <div class=\"col title\"># Of declarations submitted in the OTAS as reported by RAs</div>\r\n                </div>\r\n                <div class=\"dashboard-table\">\r\n                  <div class=\"col item\">{{ numberOfEntities }}</div>\r\n                  <div class=\"col item\">{{ numberOfESFilings }}</div>\r\n                  <div class=\"col item\"><a class=\"clickable\" (click)=\"onClicked($event, 'overdue')\">{{ numberOfEntitiesWithFilingOverdue }}</a></div>\r\n                  <div class=\"col item\"><a class=\"clickable\" (click)=\"onClicked($event, 'pending')\">{{ numberOfPendingAssessments }}</a></div>\r\n                  <div class=\"col item\">{{ numberOfDeclarationSubmittedOTAS }}</div>\r\n                </div> \r\n              </div>\r\n              <mat-divider class=\"divider-margin-extra\"></mat-divider>\r\n              <div>\r\n                <app-tax-resident-outside-bahamas [statsSummaryData] = 'statsSummaryData'></app-tax-resident-outside-bahamas>\r\n              </div>\r\n              <mat-divider class=\"divider-margin-extra\"></mat-divider>\r\n              <div>\r\n                <app-es-letter-status [statsSummaryData] = 'statsSummaryData' [selectedYear] = 'dashboardData?.fiscalYear'></app-es-letter-status>\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"currentTab == 1\">\r\n\r\n              <div>\r\n                <app-red-flags-summary [statsSummaryData] = 'statsSummaryData'></app-red-flags-summary>\r\n              </div>\r\n\r\n            </div>\r\n            <div *ngIf=\"currentTab == 2\"></div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"dashboard-years\">\r\n          <mat-form-field>\r\n            <mat-label class=\"dashboard-years-label\"\r\n              >Fiscal Period End</mat-label\r\n            >\r\n            <mat-select\r\n              [(ngModel)]=\"selectedFiscalYear\"\r\n              (selectionChange)=\"onFiscalYearChanged($event)\"\r\n            >\r\n              <mat-option *ngFor=\"let year of years\" [value]=\"year\">\r\n                {{ year }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </mat-card-content>\r\n</mat-card>\r\n"], "mappings": "AAUA,SAASA,gBAAgB,QAAQ,yBAAyB;AAE1D,SAASC,oBAAoB,QAAoD,4FAA4F;AAC7K,SAASC,MAAM,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;ICJzBC,EADF,CAAAC,cAAA,cAA4C,iBACqE;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,uBAAA,EAAyB;IAAA,EAAC;IAACT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAAAX,EAAA,CAAAU,MAAA,eAAQ;IACtJV,EADsJ,CAAAW,YAAA,EAAS,EACzJ;;;;;IAWEX,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAOxEX,EAHN,CAAAC,cAAA,UAA2B,UACpB,cAC0B,cACJ;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC3CX,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC5CX,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAU,MAAA,yCAAkC;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC/DX,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAU,MAAA,gCAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACrDX,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAU,MAAA,kEAA0D;IACnFV,EADmF,CAAAW,YAAA,EAAM,EACnF;IAEJX,EADF,CAAAC,cAAA,eAA6B,eACL;IAAAD,EAAA,CAAAU,MAAA,IAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAClDX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,IAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC7BX,EAAtB,CAAAC,cAAA,eAAsB,aAA4D;IAAvCD,EAAA,CAAAE,UAAA,mBAAAU,gEAAAC,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,SAAA,CAAAF,MAAA,EAAkB,SAAS,CAAC;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,IAAuC;IAAIV,EAAJ,CAAAW,YAAA,EAAI,EAAM;IAC7GX,EAAtB,CAAAC,cAAA,eAAsB,aAA4D;IAAvCD,EAAA,CAAAE,UAAA,mBAAAc,gEAAAH,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,SAAA,CAAAF,MAAA,EAAkB,SAAS,CAAC;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,IAAgC;IAAIV,EAAJ,CAAAW,YAAA,EAAI,EAAM;IAC5HX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,IAAsC;IAEhEV,EAFgE,CAAAW,YAAA,EAAM,EAC9D,EACF;IACNX,EAAA,CAAAiB,SAAA,uBAAwD;IACxDjB,EAAA,CAAAC,cAAA,WAAK;IACHD,EAAA,CAAAiB,SAAA,4CAA6G;IAC/GjB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAiB,SAAA,uBAAwD;IACxDjB,EAAA,CAAAC,cAAA,WAAK;IACHD,EAAA,CAAAiB,SAAA,gCAAkI;IAEtIjB,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAfsBX,EAAA,CAAAkB,SAAA,IAAsB;IAAtBlB,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAc,gBAAA,CAAsB;IACtBpB,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAe,iBAAA,CAAuB;IACqCrB,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAgB,iCAAA,CAAuC;IACvCtB,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAiB,0BAAA,CAAgC;IAC5FvB,EAAA,CAAAkB,SAAA,GAAsC;IAAtClB,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAkB,gCAAA,CAAsC;IAK5BxB,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAyB,UAAA,qBAAAnB,MAAA,CAAAoB,gBAAA,CAAuC;IAInD1B,EAAA,CAAAkB,SAAA,GAAuC;IAAClB,EAAxC,CAAAyB,UAAA,qBAAAnB,MAAA,CAAAoB,gBAAA,CAAuC,iBAAApB,MAAA,CAAAqB,aAAA,kBAAArB,MAAA,CAAAqB,aAAA,CAAAC,UAAA,CAA6C;;;;;IAK5G5B,EAFF,CAAAC,cAAA,UAA6B,UAEtB;IACHD,EAAA,CAAAiB,SAAA,gCAAuF;IAG3FjB,EAFE,CAAAW,YAAA,EAAM,EAEF;;;;IAHqBX,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAyB,UAAA,qBAAAnB,MAAA,CAAAoB,gBAAA,CAAuC;;;;;IAIlE1B,EAAA,CAAAiB,SAAA,UAAmC;;;;;IAcjCjB,EAAA,CAAAC,cAAA,qBAAsD;IACpDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;IAF0BX,EAAA,CAAAyB,UAAA,UAAAI,OAAA,CAAc;IACnD7B,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAA8B,kBAAA,MAAAD,OAAA,MACF;;;ADxDd;AAOA,OAAM,MAAOE,2BACX,SAAQlC,gBAAgB;EAmBxB;;;;EAIAmC,YACEC,QAAkB,EACVC,gBAAoC,EACpCC,qBAAmD,EACnDC,MAAc;IAEtB,KAAK,CAACH,QAAQ,CAAC;IAJP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,MAAM,GAANA,MAAM;IAvBhB,KAAAC,KAAK,GAAa,EAAE;IACpB,KAAAC,kBAAkB,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACrD,KAAAC,UAAU,GAAG,CAAC;IAEd;IACA,KAAAC,uBAAuB,GAAG,EAAE;IAE5B;IACA,KAAAtB,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,iCAAiC,GAAW,CAAC;IAC7C,KAAAC,0BAA0B,GAAW,CAAC;IACtC,KAAAC,gCAAgC,GAAW,CAAC;IAE5C,KAAAmB,eAAe,GAAG,gDAAgD;IAYhE,IAAI,CAACT,gBAAgB,CAACU,qBAAqB,CAACC,SAAS,CAAEC,KAAK,IAAI;MAC9D,QAAQA,KAAK;QACX,KAAK,CAAC;UACJ;UACA;UACA,IAAI,CAACH,eAAe,GAAG,gDAAgD;UACvE;QACF,KAAK,CAAC;UACJ;UACA;UACA,IAAI,CAACA,eAAe,GAAG,4DAA4D;UACnF;QACF,KAAK,CAAC;UACJ;UACA;UACA,IAAI,CAACA,eAAe,GAAG,gDAAgD;UACvE;MACJ;MACA,IAAI,CAACI,WAAW,EAAE;IACpB,CAAC,CAAC;IAEF,IAAI,CAACb,gBAAgB,CAACU,qBAAqB,CAACC,SAAS,CAAEC,KAAK,IAAI;MAC9D,IAAI,CAACL,UAAU,GAAGK,KAAK;IACzB,CAAC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACd,gBAAgB,CAACe,cAAc,EAAE;IACtC,IAAI,CAACf,gBAAgB,CAACgB,0BAA0B,EAAE;IAClD,IAAI,CAAChB,gBAAgB,CAACiB,qBAAqB,CAACN,SAAS,CAAEC,KAAK,IAAI;MAC9D,IAAI,CAACT,KAAK,GAAGS,KAAK;IACpB,CAAC,CAAC;IACF;IACA,IAAI,CAACZ,gBAAgB,CAACkB,iCAAiC,CAACP,SAAS,CAC9DC,KAAK,IAAI;MACR;MACA;MACA;MACA;MACAA,KAAK,GAAGA,KAAK,EAAEO,QAAQ,EAAE,CAACC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC;MAC7C;MACA;MACA;MACA,IAAI,CAACZ,uBAAuB,GAAG3C,MAAM,CACnC,IAAIwC,IAAI,CAACO,KAAK,CAAC,EACf,wBAAwB,CACzB;IACH,CAAC,CACF;IAED;IACA,IAAI,CAACR,kBAAkB,GAAG,IAAI,CAACJ,gBAAgB,CAACqB,qBAAqB,EAAE;EACzE;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC9B,aAAa,EAAE;MACzB,IAAG,IAAI,CAACA,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,UAAU,EAAC;QACrD,IAAI,CAAC8B,kBAAkB,EAAE;MAC3B,CAAC,MAAI;QACH,IAAI,CAACtC,gBAAgB,GAAG,CAAC;QACzB,IAAI,CAACC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACC,iCAAiC,GAAG,CAAC;QAC1C,IAAI,CAACC,0BAA0B,GAAG,CAAC;QACnC,IAAI,CAACC,gCAAgC,GAAG,CAAC;MAC3C;IACF;EACF;EAEAkC,kBAAkBA,CAAA;IAChB,IAAI,CAACvB,qBAAqB,CAACwB,8BAA8B,CAAC,IAAI,CAAChC,aAAa,CAACC,UAAU,CAAC,CAACiB,SAAS,CAACe,MAAM,IAAG;MAC1G,IAAI,CAAClC,gBAAgB,GAAGkC,MAAM;MAC9B,IAAI,CAACxC,gBAAgB,GAAGwC,MAAM,CAACC,aAAa,IAAI,CAAC;MACjD,IAAI,CAACxC,iBAAiB,GAAGuC,MAAM,CAACE,oBAAoB,IAAI,CAAC;MACzD,IAAI,CAACxC,iCAAiC,GAAGsC,MAAM,CAACG,0BAA0B,IAAI,CAAC;MAC/E,IAAI,CAACxC,0BAA0B,GAAGqC,MAAM,CAACI,sBAAsB,IAAI,CAAC;MACpE,IAAI,CAACxC,gCAAgC,GAAGoC,MAAM,CAACK,cAAc,IAAI,CAAC;IACpE,CAAC,CAAC;EACJ;EAEAlD,SAASA,CAACmD,KAAK,EAAEC,MAAM;IACrB,IAAGA,MAAM,KAAK,SAAS,EAAC;MACtB,IAAI,CAAC/B,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;QAAEC,WAAW,EAAE;UAACF,MAAM;UAAgCG,IAAI;UAAoCC,IAAI,EAAE,IAAI,CAAC5C,aAAa,CAACC,UAAU;UAAE4C,WAAW,EAAE1E,oBAAoB,CAAC2E;QAA0B;MAAC,CAAC,CAAC;IAC7O,CAAC,MACI,IAAGN,MAAM,KAAK,SAAS,EAAC;MAC3B,IAAI,CAAC/B,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;QAAEC,WAAW,EAAE;UAACF,MAAM;UAAgCG,IAAI;UAAoCC,IAAI,EAAE,IAAI,CAAC5C,aAAa,CAACC,UAAU;UAAE4C,WAAW,EAAE1E,oBAAoB,CAAC4E;QAA8B;MAAC,CAAC,CAAC;IACjP;EACF;EAEAC,mBAAmBA,CAACT,KAAsB;IACxC,IAAI,CAAChC,gBAAgB,CAAC0C,qBAAqB,CAACV,KAAK,CAACpB,KAAe,CAAC;EACpE;EAEArC,uBAAuBA,CAAA;IACrB,IAAI,CAAC2B,MAAM,CAACgC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEArB,WAAWA,CAAA;IACT,MAAM8B,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/CF,OAAO,CAACG,MAAM,CAAC;MACbC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;KACX,CAAC;EACJ;;;uBAtIWpD,2BAA2B,EAAA/B,EAAA,CAAAoF,iBAAA,CAAApF,EAAA,CAAAqF,QAAA,GAAArF,EAAA,CAAAoF,iBAAA,CAAAE,EAAA,CAAAC,kBAAA,GAAAvF,EAAA,CAAAoF,iBAAA,CAAAI,EAAA,CAAAC,4BAAA,GAAAzF,EAAA,CAAAoF,iBAAA,CAAAM,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA3B5D,2BAA2B;MAAA6D,SAAA;MAAAC,MAAA;QAAAlE,aAAA;MAAA;MAAAmE,QAAA,GAAA9F,EAAA,CAAA+F,0BAAA,EAAA/F,EAAA,CAAAgG,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBlCtG,EAHN,CAAAC,cAAA,eAAU,sBACS,aAC6C,aACpC;UAAAD,EAAA,CAAAU,MAAA,oDAA6C;UAAAV,EAAA,CAAAW,YAAA,EAAM;UACzEX,EAAA,CAAAC,cAAA,aAAuC;UACrCD,EAAA,CAAAU,MAAA,GAEF;UAAAV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAwG,UAAA,IAAAC,0CAAA,iBAA4C;UAKhDzG,EADE,CAAAW,YAAA,EAAM,EACU;UAClBX,EAAA,CAAAC,cAAA,uBAAkB;UAChBD,EAAA,CAAAiB,SAAA,qBAAkD;UAK1CjB,EAJR,CAAAC,cAAA,cAAiB,cACO,mBACgB,uBACjB,yBACkC;UAC/CD,EAAA,CAAAwG,UAAA,KAAAE,2CAAA,iBAAwD;UAE5D1G,EADE,CAAAW,YAAA,EAAiB,EACD;UAClBX,EAAA,CAAAC,cAAA,wBAAkB;UAkChBD,EAjCA,CAAAwG,UAAA,KAAAG,2CAAA,mBAA2B,KAAAC,2CAAA,kBA0BE,KAAAC,2CAAA,kBAOA;UAGnC7G,EAFI,CAAAW,YAAA,EAAmB,EACV,EACP;UAIAX,EAHN,CAAAC,cAAA,eAAsB,eACS,sBACX,qBAEX;UAAAD,EAAA,CAAAU,MAAA,yBAAiB;UAAAV,EAAA,CAAAW,YAAA,EACnB;UACDX,EAAA,CAAAC,cAAA,sBAGC;UAFCD,EAAA,CAAA8G,gBAAA,2BAAAC,0EAAAlG,MAAA;YAAAb,EAAA,CAAAgH,kBAAA,CAAAT,GAAA,CAAAjE,kBAAA,EAAAzB,MAAA,MAAA0F,GAAA,CAAAjE,kBAAA,GAAAzB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAChCb,EAAA,CAAAE,UAAA,6BAAA+G,4EAAApG,MAAA;YAAA,OAAmB0F,GAAA,CAAA5B,mBAAA,CAAA9D,MAAA,CAA2B;UAAA,EAAC;UAE/Cb,EAAA,CAAAwG,UAAA,KAAAU,kDAAA,yBAAsD;UASpElH,EANY,CAAAW,YAAA,EAAa,EACE,EACb,EACF,EACF,EACW,EACV;;;UA5EHX,EAAA,CAAAkB,SAAA,GAEF;UAFElB,EAAA,CAAAmH,kBAAA,MAAAZ,GAAA,CAAA5D,eAAA,OAAA4D,GAAA,CAAA7D,uBAAA,MAEF;UACuB1C,EAAA,CAAAkB,SAAA,EAAmB;UAAnBlB,EAAA,CAAAyB,UAAA,SAAA8E,GAAA,CAAA9D,UAAA,MAAmB;UAa5BzC,EAAA,CAAAkB,SAAA,GAAmB;UAAnBlB,EAAA,CAAAyB,UAAA,SAAA8E,GAAA,CAAA9D,UAAA,MAAmB;UAIrBzC,EAAA,CAAAkB,SAAA,GAAmB;UAAnBlB,EAAA,CAAAyB,UAAA,SAAA8E,GAAA,CAAA9D,UAAA,MAAmB;UA0BnBzC,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAAyB,UAAA,SAAA8E,GAAA,CAAA9D,UAAA,MAAqB;UAOrBzC,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAAyB,UAAA,SAAA8E,GAAA,CAAA9D,UAAA,MAAqB;UAWzBzC,EAAA,CAAAkB,SAAA,GAAgC;UAAhClB,EAAA,CAAAoH,gBAAA,YAAAb,GAAA,CAAAjE,kBAAA,CAAgC;UAGHtC,EAAA,CAAAkB,SAAA,EAAQ;UAARlB,EAAA,CAAAyB,UAAA,YAAA8E,GAAA,CAAAlE,KAAA,CAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}