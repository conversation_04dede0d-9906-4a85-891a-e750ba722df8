{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['fø<PERSON>', '<PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apr.', 'mai', 'juni', 'juli', 'aug.', 'sep.', 'okt.', 'nov.', 'des.'],\n  wide: ['januar', 'februar', 'mars', 'april', 'mai', 'juni', 'juli', 'august', 'september', 'oktober', 'november', 'desember']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'O', 'T', 'F', 'L'],\n  short: ['su', 'må', 'ty', 'on', 'to', 'fr', 'lau'],\n  abbreviated: ['sun', 'mån', 'tys', 'ons', 'tor', 'fre', 'laur'],\n  wide: ['sundag', 'måndag', 'tysdag', 'onsdag', 'torsdag', 'fredag', 'laurdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på etterm.',\n    evening: 'på kvelden',\n    night: 'på natta'\n  },\n  abbreviated: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på etterm.',\n    evening: 'på kvelden',\n    night: 'på natta'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morgonen',\n    afternoon: 'på ettermiddagen',\n    evening: 'på kvelden',\n    night: 'på natta'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/nn/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['fø<PERSON>', '<PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apr.', 'mai', 'juni', 'juli', 'aug.', 'sep.', 'okt.', 'nov.', 'des.'],\n  wide: ['januar', 'februar', 'mars', 'april', 'mai', 'juni', 'juli', 'august', 'september', 'oktober', 'november', 'desember']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'O', 'T', 'F', 'L'],\n  short: ['su', 'må', 'ty', 'on', 'to', 'fr', 'lau'],\n  abbreviated: ['sun', 'mån', 'tys', 'ons', 'tor', 'fre', 'laur'],\n  wide: ['sundag', 'måndag', 'tysdag', 'onsdag', 'torsdag', 'fredag', 'laurdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på etterm.',\n    evening: 'på kvelden',\n    night: 'på natta'\n  },\n  abbreviated: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på etterm.',\n    evening: 'på kvelden',\n    night: 'på natta'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morgonen',\n    afternoon: 'på ettermiddagen',\n    evening: 'på kvelden',\n    night: 'på natta'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC1BC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC/BC,IAAI,EAAE,CAAC,aAAa,EAAE,eAAe;AACvC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC5GC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAC9H,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;EAClDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC/DC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS;AAC/E,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}