{"ast": null, "code": "import { utcToZonedTime, format } from 'date-fns-tz';\nexport class DateHelper {\n  static formatEstUtcDate(dateStr, formatStr) {\n    if (!dateStr) return '';\n    if (dateStr.length === 10) return dateStr; //avoid this method called twice\n    const dt = utcToZonedTime(dateStr, 'America/New_York');\n    return format(dt, formatStr);\n  }\n  /**\n   * Converts UTC date string to user's browser local time and formats it\n   * @param dateStr UTC date string (ISO format)\n   * @param formatStr Format string for date-fns (default: 'dd/MM/yyyy')\n   * @returns Formatted date string in user's local timezone\n   */\n  static formatUtcToLocalDate(dateStr, formatStr = 'dd/MM/yyyy') {\n    if (!dateStr) return '';\n    if (dateStr.length === 10) return dateStr; // avoid this method called twice for date-only strings\n    // Get user's browser timezone\n    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n    // Convert UTC to user's local timezone\n    const localDateTime = utcToZonedTime(dateStr, userTimezone);\n    // Format and return\n    return format(localDateTime, formatStr, {\n      timeZone: userTimezone\n    });\n  }\n  /**\n   * Converts UTC date string to user's browser local Date object\n   * @param dateStr UTC date string (ISO format)\n   * @returns Date object in user's local timezone\n   */\n  static convertUtcToLocalDate(dateStr) {\n    if (!dateStr) return null;\n    // Get user's browser timezone\n    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n    // Convert UTC to user's local timezone and return as Date object\n    return utcToZonedTime(dateStr, userTimezone);\n  }\n}", "map": {"version": 3, "names": ["utcToZonedTime", "format", "Date<PERSON>elper", "formatEstUtcDate", "dateStr", "formatStr", "length", "dt", "formatUtcToLocalDate", "userTimezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "localDateTime", "convertUtcToLocalDate"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\utils\\date-helper.ts"], "sourcesContent": ["import { utcToZonedTime, format } from 'date-fns-tz';\r\nexport class DateHelper {\r\n\r\n    static formatEstUtcDate(dateStr: string, formatStr:string): string {\r\n        if (!dateStr) return '';\r\n        if (dateStr.length === 10) return dateStr; //avoid this method called twice\r\n        const dt=  utcToZonedTime(dateStr, 'America/New_York');\r\n        return format(dt,formatStr);\r\n    }\r\n\r\n    /**\r\n     * Converts UTC date string to user's browser local time and formats it\r\n     * @param dateStr UTC date string (ISO format)\r\n     * @param formatStr Format string for date-fns (default: 'dd/MM/yyyy')\r\n     * @returns Formatted date string in user's local timezone\r\n     */\r\n    static formatUtcToLocalDate(dateStr: string, formatStr: string = 'dd/MM/yyyy'): string {\r\n        if (!dateStr) return '';\r\n        if (dateStr.length === 10) return dateStr; // avoid this method called twice for date-only strings\r\n\r\n        // Get user's browser timezone\r\n        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n\r\n        // Convert UTC to user's local timezone\r\n        const localDateTime = utcToZonedTime(dateStr, userTimezone);\r\n\r\n        // Format and return\r\n        return format(localDateTime, formatStr, { timeZone: userTimezone });\r\n    }\r\n\r\n    /**\r\n     * Converts UTC date string to user's browser local Date object\r\n     * @param dateStr UTC date string (ISO format)\r\n     * @returns Date object in user's local timezone\r\n     */\r\n    static convertUtcToLocalDate(dateStr: string): Date | null {\r\n        if (!dateStr) return null;\r\n\r\n        // Get user's browser timezone\r\n        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n\r\n        // Convert UTC to user's local timezone and return as Date object\r\n        return utcToZonedTime(dateStr, userTimezone);\r\n    }\r\n}"], "mappings": "AAAA,SAASA,cAAc,EAAEC,MAAM,QAAQ,aAAa;AACpD,OAAM,MAAOC,UAAU;EAEnB,OAAOC,gBAAgBA,CAACC,OAAe,EAAEC,SAAgB;IACrD,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;IACvB,IAAIA,OAAO,CAACE,MAAM,KAAK,EAAE,EAAE,OAAOF,OAAO,CAAC,CAAC;IAC3C,MAAMG,EAAE,GAAGP,cAAc,CAACI,OAAO,EAAE,kBAAkB,CAAC;IACtD,OAAOH,MAAM,CAACM,EAAE,EAACF,SAAS,CAAC;EAC/B;EAEA;;;;;;EAMA,OAAOG,oBAAoBA,CAACJ,OAAe,EAAEC,SAAA,GAAoB,YAAY;IACzE,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;IACvB,IAAIA,OAAO,CAACE,MAAM,KAAK,EAAE,EAAE,OAAOF,OAAO,CAAC,CAAC;IAE3C;IACA,MAAMK,YAAY,GAAGC,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,EAAE,CAACC,QAAQ;IAErE;IACA,MAAMC,aAAa,GAAGd,cAAc,CAACI,OAAO,EAAEK,YAAY,CAAC;IAE3D;IACA,OAAOR,MAAM,CAACa,aAAa,EAAET,SAAS,EAAE;MAAEQ,QAAQ,EAAEJ;IAAY,CAAE,CAAC;EACvE;EAEA;;;;;EAKA,OAAOM,qBAAqBA,CAACX,OAAe;IACxC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB;IACA,MAAMK,YAAY,GAAGC,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,EAAE,CAACC,QAAQ;IAErE;IACA,OAAOb,cAAc,CAACI,OAAO,EAAEK,YAAY,CAAC;EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}