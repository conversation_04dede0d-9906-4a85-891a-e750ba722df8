{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, NgModule } from '@angular/core';\nimport { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { getYear, getMonth, getDate, getDay, getDaysInMonth, parseISO, parse, format, addYears, addMonths, addDays, formatISO, isDate, isValid } from 'date-fns';\n\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n// date-fns doesn't have a way to read/print month names or days of the week directly,\n// so we get them by formatting a date with a format that produces the desired month/day.\nconst MONTH_FORMATS = {\n  long: 'LLLL',\n  short: 'LLL',\n  narrow: 'LLLLL'\n};\nconst DAY_OF_WEEK_FORMATS = {\n  long: 'EEEE',\n  short: 'EEE',\n  narrow: 'EEEEE'\n};\n/** Adds date-fns support to Angular Material. */\nlet DateFnsAdapter = /*#__PURE__*/(() => {\n  class DateFnsAdapter extends DateAdapter {\n    constructor(matDateLocale) {\n      super();\n      this.setLocale(matDateLocale);\n    }\n    getYear(date) {\n      return getYear(date);\n    }\n    getMonth(date) {\n      return getMonth(date);\n    }\n    getDate(date) {\n      return getDate(date);\n    }\n    getDayOfWeek(date) {\n      return getDay(date);\n    }\n    getMonthNames(style) {\n      const pattern = MONTH_FORMATS[style];\n      return range(12, i => this.format(new Date(2017, i, 1), pattern));\n    }\n    getDateNames() {\n      const dtf = typeof Intl !== 'undefined' ? new Intl.DateTimeFormat(this.locale.code, {\n        day: 'numeric',\n        timeZone: 'utc'\n      }) : null;\n      return range(31, i => {\n        if (dtf) {\n          // date-fns doesn't appear to support this functionality.\n          // Fall back to `Intl` on supported browsers.\n          const date = new Date();\n          date.setUTCFullYear(2017, 0, i + 1);\n          date.setUTCHours(0, 0, 0, 0);\n          return dtf.format(date).replace(/[\\u200e\\u200f]/g, '');\n        }\n        return i + '';\n      });\n    }\n    getDayOfWeekNames(style) {\n      const pattern = DAY_OF_WEEK_FORMATS[style];\n      return range(7, i => this.format(new Date(2017, 0, i + 1), pattern));\n    }\n    getYearName(date) {\n      return this.format(date, 'y');\n    }\n    getFirstDayOfWeek() {\n      return this.locale.options?.weekStartsOn ?? 0;\n    }\n    getNumDaysInMonth(date) {\n      return getDaysInMonth(date);\n    }\n    clone(date) {\n      return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Check for invalid month and date (except upper bound on date which we have to check after\n        // creating the Date).\n        if (month < 0 || month > 11) {\n          throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n        }\n        if (date < 1) {\n          throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n        }\n      }\n      // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n      // To work around this we use `setFullYear` and `setHours` instead.\n      const result = new Date();\n      result.setFullYear(year, month, date);\n      result.setHours(0, 0, 0, 0);\n      // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n      if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n      }\n      return result;\n    }\n    today() {\n      return new Date();\n    }\n    parse(value, parseFormat) {\n      if (typeof value == 'string' && value.length > 0) {\n        const iso8601Date = parseISO(value);\n        if (this.isValid(iso8601Date)) {\n          return iso8601Date;\n        }\n        const formats = Array.isArray(parseFormat) ? parseFormat : [parseFormat];\n        if (!parseFormat.length) {\n          throw Error('Formats array must not be empty.');\n        }\n        for (const currentFormat of formats) {\n          const fromFormat = parse(value, currentFormat, new Date(), {\n            locale: this.locale\n          });\n          if (this.isValid(fromFormat)) {\n            return fromFormat;\n          }\n        }\n        return this.invalid();\n      } else if (typeof value === 'number') {\n        return new Date(value);\n      } else if (value instanceof Date) {\n        return this.clone(value);\n      }\n      return null;\n    }\n    format(date, displayFormat) {\n      if (!this.isValid(date)) {\n        throw Error('DateFnsAdapter: Cannot format invalid date.');\n      }\n      return format(date, displayFormat, {\n        locale: this.locale\n      });\n    }\n    addCalendarYears(date, years) {\n      return addYears(date, years);\n    }\n    addCalendarMonths(date, months) {\n      return addMonths(date, months);\n    }\n    addCalendarDays(date, days) {\n      return addDays(date, days);\n    }\n    toIso8601(date) {\n      return formatISO(date, {\n        representation: 'date'\n      });\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n      if (typeof value === 'string') {\n        if (!value) {\n          return null;\n        }\n        const date = parseISO(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n      return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n      return isDate(obj);\n    }\n    isValid(date) {\n      return isValid(date);\n    }\n    invalid() {\n      return new Date(NaN);\n    }\n    static {\n      this.ɵfac = function DateFnsAdapter_Factory(t) {\n        return new (t || DateFnsAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: DateFnsAdapter,\n        factory: DateFnsAdapter.ɵfac\n      });\n    }\n  }\n  return DateFnsAdapter;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst MAT_DATE_FNS_FORMATS = {\n  parse: {\n    dateInput: 'P'\n  },\n  display: {\n    dateInput: 'P',\n    monthYearLabel: 'LLL uuuu',\n    dateA11yLabel: 'PP',\n    monthYearA11yLabel: 'LLLL uuuu'\n  }\n};\nlet DateFnsModule = /*#__PURE__*/(() => {\n  class DateFnsModule {\n    static {\n      this.ɵfac = function DateFnsModule_Factory(t) {\n        return new (t || DateFnsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: DateFnsModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [{\n          provide: DateAdapter,\n          useClass: DateFnsAdapter,\n          deps: [MAT_DATE_LOCALE]\n        }]\n      });\n    }\n  }\n  return DateFnsModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatDateFnsModule = /*#__PURE__*/(() => {\n  class MatDateFnsModule {\n    static {\n      this.ɵfac = function MatDateFnsModule_Factory(t) {\n        return new (t || MatDateFnsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatDateFnsModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [provideDateFnsAdapter()]\n      });\n    }\n  }\n  return MatDateFnsModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction provideDateFnsAdapter(formats = MAT_DATE_FNS_FORMATS) {\n  return [{\n    provide: DateAdapter,\n    useClass: DateFnsAdapter,\n    deps: [MAT_DATE_LOCALE]\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: formats\n  }];\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DateFnsAdapter, DateFnsModule, MAT_DATE_FNS_FORMATS, MatDateFnsModule, provideDateFnsAdapter };\n//# sourceMappingURL=material-date-fns-adapter.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}