{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val));\n  if (i === 0 || n === 1) return 1;\n  return 5;\n}\nexport default [\"as\", [[\"পূৰ্বাহ্ন\", \"অপৰাহ্ন\"], u, u], u, [[\"দ\", \"স\", \"ম\", \"ব\", \"ব\", \"শ\", \"শ\"], [\"দেও\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহ\", \"শুক্ৰ\", \"শনি\"], [\"দেওবাৰ\", \"সোমবাৰ\", \"মঙ্গলবাৰ\", \"বুধবাৰ\", \"বৃহস্পতিবাৰ\", \"শুক্ৰবাৰ\", \"শনিবাৰ\"], [\"দেও\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহ\", \"শুক্ৰ\", \"শনি\"]], u, [[\"জ\", \"ফ\", \"ম\", \"এ\", \"ম\", \"জ\", \"জ\", \"আ\", \"ছ\", \"অ\", \"ন\", \"ড\"], [\"জানু\", \"ফেব্ৰু\", \"মাৰ্চ\", \"এপ্ৰিল\", \"মে’\", \"জুন\", \"জুলাই\", \"আগ\", \"ছেপ্তে\", \"অক্টো\", \"নৱে\", \"ডিচে\"], [\"জানুৱাৰী\", \"ফেব্ৰুৱাৰী\", \"মাৰ্চ\", \"এপ্ৰিল\", \"মে’\", \"জুন\", \"জুলাই\", \"আগষ্ট\", \"ছেপ্তেম্বৰ\", \"অক্টোবৰ\", \"নৱেম্বৰ\", \"ডিচেম্বৰ\"]], u, [[\"খ্ৰীঃ পূঃ\", \"খ্ৰীঃ\"], u, [\"খ্ৰীষ্টপূৰ্ব\", \"খ্ৰীষ্টাব্দ\"]], 0, [0, 0], [\"d-M-y\", \"dd-MM-y\", \"d MMMM, y\", \"EEEE, d MMMM, y\"], [\"a h.mm\", \"a h.mm.ss\", \"a h.mm.ss z\", \"a h.mm.ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤ #,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"ভাৰতীয় ৰুপী\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/as.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val));\n    if (i === 0 || n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"as\", [[\"পূৰ্বাহ্ন\", \"অপৰাহ্ন\"], u, u], u, [[\"দ\", \"স\", \"ম\", \"ব\", \"ব\", \"শ\", \"শ\"], [\"দেও\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহ\", \"শুক্ৰ\", \"শনি\"], [\"দেওবাৰ\", \"সোমবাৰ\", \"মঙ্গলবাৰ\", \"বুধবাৰ\", \"বৃহস্পতিবাৰ\", \"শুক্ৰবাৰ\", \"শনিবাৰ\"], [\"দেও\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহ\", \"শুক্ৰ\", \"শনি\"]], u, [[\"জ\", \"ফ\", \"ম\", \"এ\", \"ম\", \"জ\", \"জ\", \"আ\", \"ছ\", \"অ\", \"ন\", \"ড\"], [\"জানু\", \"ফেব্ৰু\", \"মাৰ্চ\", \"এপ্ৰিল\", \"মে’\", \"জুন\", \"জুলাই\", \"আগ\", \"ছেপ্তে\", \"অক্টো\", \"নৱে\", \"ডিচে\"], [\"জানুৱাৰী\", \"ফেব্ৰুৱাৰী\", \"মাৰ্চ\", \"এপ্ৰিল\", \"মে’\", \"জুন\", \"জুলাই\", \"আগষ্ট\", \"ছেপ্তেম্বৰ\", \"অক্টোবৰ\", \"নৱেম্বৰ\", \"ডিচেম্বৰ\"]], u, [[\"খ্ৰীঃ পূঃ\", \"খ্ৰীঃ\"], u, [\"খ্ৰীষ্টপূৰ্ব\", \"খ্ৰীষ্টাব্দ\"]], 0, [0, 0], [\"d-M-y\", \"dd-MM-y\", \"d MMMM, y\", \"EEEE, d MMMM, y\"], [\"a h.mm\", \"a h.mm.ss\", \"a h.mm.ss z\", \"a h.mm.ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤ #,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"ভাৰতীয় ৰুপী\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;EAC5C,IAAIE,CAAC,KAAK,CAAC,IAAID,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}