{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _index = _interopRequireDefault(require(\"../../locale/en-US/index.js\"));\nvar _default = _index.default;\nexports.default = _default;\nmodule.exports = exports.default;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}