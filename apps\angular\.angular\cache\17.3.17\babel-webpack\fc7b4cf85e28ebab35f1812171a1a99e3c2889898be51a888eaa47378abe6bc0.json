{"ast": null, "code": "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Norwegian Nynorsk locale.\n * @language Norwegian Nynorsk\n * @iso-639-2 nno\n * <AUTHOR> By<PERSON> [@draperunner]{@link https://github.com/draperunner}\n */\nvar locale = {\n  code: 'nn',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}