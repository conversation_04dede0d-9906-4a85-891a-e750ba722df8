{"ast": null, "code": "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Finnish locale.\n * @language Finnish\n * @iso-639-2 fin\n * <AUTHOR> [@Pyppe]{@link https://github.com/Pyppe}\n * <AUTHOR> [@mikolajgrzyb]{@link https://github.com/mikolajgrzyb}\n * <AUTHOR> [@sjuvonen]{@link https://github.com/sjuvonen}\n */\nvar locale = {\n  code: 'fi',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}