{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/fr-HT\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    var i = Math.floor(Math.abs(n));\n    if (i === 0 || i === 1) return 1;\n    return 5;\n  }\n  exports.default = ['fr-HT', [['AM', 'PM'], u, u], u, [['D', 'L', 'M', 'M', 'J', 'V', 'S'], ['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'], ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'], ['di', 'lu', 'ma', 'me', 'je', 've', 'sa']], u, [['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'], ['janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin', 'juil.', 'août', 'sept.', 'oct.', 'nov.', 'déc.'], ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']], u, [['av. J.-C.', 'ap. J.-C.'], u, ['avant Jésus-Christ', 'après Jésus-Christ']], 1, [6, 0], ['dd/MM/y', 'd MMM y', 'd MMMM y', 'EEEE d MMMM y'], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1} {0}', '{1} \\'à\\' {0}', u, u], [',', '\\u202f', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'], 'HTG', 'G', 'gourde haïtienne', {\n    'ARS': ['$AR', '$'],\n    'AUD': ['$AU', '$'],\n    'BEF': ['FB'],\n    'BMD': ['$BM', '$'],\n    'BND': ['$BN', '$'],\n    'BZD': ['$BZ', '$'],\n    'CAD': ['$CA', '$'],\n    'CLP': ['$CL', '$'],\n    'CNY': [u, '¥'],\n    'COP': ['$CO', '$'],\n    'CYP': ['£CY'],\n    'EGP': [u, '£E'],\n    'FJD': ['$FJ', '$'],\n    'FKP': ['£FK', '£'],\n    'FRF': ['F'],\n    'GBP': ['£GB', '£'],\n    'GIP': ['£GI', '£'],\n    'HKD': [u, '$'],\n    'HTG': ['G'],\n    'IEP': ['£IE'],\n    'ILP': ['£IL'],\n    'ITL': ['₤IT'],\n    'JPY': [u, '¥'],\n    'KMF': [u, 'FC'],\n    'LBP': ['£LB', '£L'],\n    'MTP': ['£MT'],\n    'MXN': ['$MX', '$'],\n    'NAD': ['$NA', '$'],\n    'NIO': [u, '$C'],\n    'NZD': ['$NZ', '$'],\n    'RHD': ['$RH'],\n    'RON': [u, 'L'],\n    'RWF': [u, 'FR'],\n    'SBD': ['$SB', '$'],\n    'SGD': ['$SG', '$'],\n    'SRD': ['$SR', '$'],\n    'TOP': [u, '$T'],\n    'TTD': ['$TT', '$'],\n    'TWD': [u, 'NT$'],\n    'USD': ['$US', '$'],\n    'UYU': ['$UY', '$'],\n    'WST': ['$WS'],\n    'XCD': [u, '$'],\n    'XPF': ['FCFP'],\n    'ZMW': [u, 'Kw']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "i", "Math", "floor", "abs", "default"], "sources": ["C:/Temp/node_modules/@angular/common/locales/fr-HT.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/fr-HT\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        var i = Math.floor(Math.abs(n));\n        if (i === 0 || i === 1)\n            return 1;\n        return 5;\n    }\n    exports.default = [\n        'fr-HT',\n        [['AM', 'PM'], u, u],\n        u,\n        [\n            ['D', 'L', 'M', 'M', 'J', 'V', 'S'], ['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],\n            ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'],\n            ['di', 'lu', 'ma', 'me', 'je', 've', 'sa']\n        ],\n        u,\n        [\n            ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n            [\n                'janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin', 'juil.', 'août', 'sept.', 'oct.', 'nov.',\n                'déc.'\n            ],\n            [\n                'janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre',\n                'octobre', 'novembre', 'décembre'\n            ]\n        ],\n        u,\n        [['av. J.-C.', 'ap. J.-C.'], u, ['avant Jésus-Christ', 'après Jésus-Christ']],\n        1,\n        [6, 0],\n        ['dd/MM/y', 'd MMM y', 'd MMMM y', 'EEEE d MMMM y'],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1} {0}', '{1} \\'à\\' {0}', u, u],\n        [',', '\\u202f', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0 %', '#,##0.00 ¤', '#E0'],\n        'HTG',\n        'G',\n        'gourde haïtienne',\n        {\n            'ARS': ['$AR', '$'],\n            'AUD': ['$AU', '$'],\n            'BEF': ['FB'],\n            'BMD': ['$BM', '$'],\n            'BND': ['$BN', '$'],\n            'BZD': ['$BZ', '$'],\n            'CAD': ['$CA', '$'],\n            'CLP': ['$CL', '$'],\n            'CNY': [u, '¥'],\n            'COP': ['$CO', '$'],\n            'CYP': ['£CY'],\n            'EGP': [u, '£E'],\n            'FJD': ['$FJ', '$'],\n            'FKP': ['£FK', '£'],\n            'FRF': ['F'],\n            'GBP': ['£GB', '£'],\n            'GIP': ['£GI', '£'],\n            'HKD': [u, '$'],\n            'HTG': ['G'],\n            'IEP': ['£IE'],\n            'ILP': ['£IL'],\n            'ITL': ['₤IT'],\n            'JPY': [u, '¥'],\n            'KMF': [u, 'FC'],\n            'LBP': ['£LB', '£L'],\n            'MTP': ['£MT'],\n            'MXN': ['$MX', '$'],\n            'NAD': ['$NA', '$'],\n            'NIO': [u, '$C'],\n            'NZD': ['$NZ', '$'],\n            'RHD': ['$RH'],\n            'RON': [u, 'L'],\n            'RWF': [u, 'FR'],\n            'SBD': ['$SB', '$'],\n            'SGD': ['$SG', '$'],\n            'SRD': ['$SR', '$'],\n            'TOP': [u, '$T'],\n            'TTD': ['$TT', '$'],\n            'TWD': [u, 'NT$'],\n            'USD': ['$US', '$'],\n            'UYU': ['$UY', '$'],\n            'WST': ['$WS'],\n            'XCD': [u, '$'],\n            'XPF': ['FCFP'],\n            'ZMW': [u, 'Kw']\n        },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC5E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;IAC/B,IAAIC,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;IACZ,OAAO,CAAC;EACZ;EACAZ,OAAO,CAACgB,OAAO,GAAG,CACd,OAAO,EACP,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEP,CAAC,EAAEA,CAAC,CAAC,EACpBA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAC7F,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,EACzE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAC7C,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CACI,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EACzF,MAAM,CACT,EACD,CACI,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EACpF,SAAS,EAAE,UAAU,EAAE,UAAU,CACpC,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAEA,CAAC,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,EAC7E,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EACnD,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,SAAS,EAAE,eAAe,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAClC,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EACnE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAC7C,KAAK,EACL,GAAG,EACH,kBAAkB,EAClB;IACI,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,IAAI,CAAC;IACb,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IACpB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;IACjB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI;EACnB,CAAC,EACD,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}