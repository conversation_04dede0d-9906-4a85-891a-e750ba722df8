{"ast": null, "code": "import * as i1 from '@angular/cdk/bidi';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, isDataSource, _ViewRepeaterOperation, _DisposeViewRepeaterStrategy } from '@angular/cdk/collections';\nexport { DataSource } from '@angular/cdk/collections';\nimport * as i2 from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, booleanAttribute, Inject, Optional, Input, ContentChild, Injectable, Component, ChangeDetectionStrategy, ViewEncapsulation, inject, EmbeddedViewRef, EventEmitter, NgZone, Attribute, SkipSelf, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, from, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction CdkTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction CdkTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 0);\n    i0.ɵɵelementContainer(3, 2)(4, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CdkTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 2)(2, 3)(3, 4);\n  }\n}\nfunction CdkTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction CdkTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nconst CDK_TABLE = /*#__PURE__*/new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = /*#__PURE__*/new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nlet CdkCellDef = /*#__PURE__*/(() => {\n  class CdkCellDef {\n    constructor(/** @docs-private */template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function CdkCellDef_Factory(t) {\n        return new (t || CdkCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkCellDef,\n        selectors: [[\"\", \"cdkCellDef\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkCellDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nlet CdkHeaderCellDef = /*#__PURE__*/(() => {\n  class CdkHeaderCellDef {\n    constructor(/** @docs-private */template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function CdkHeaderCellDef_Factory(t) {\n        return new (t || CdkHeaderCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkHeaderCellDef,\n        selectors: [[\"\", \"cdkHeaderCellDef\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkHeaderCellDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nlet CdkFooterCellDef = /*#__PURE__*/(() => {\n  class CdkFooterCellDef {\n    constructor(/** @docs-private */template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function CdkFooterCellDef_Factory(t) {\n        return new (t || CdkFooterCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkFooterCellDef,\n        selectors: [[\"\", \"cdkFooterCellDef\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkFooterCellDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nlet CdkColumnDef = /*#__PURE__*/(() => {\n  class CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n      return this._name;\n    }\n    set name(name) {\n      this._setNameInput(name);\n    }\n    /** Whether the cell is sticky. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(value) {\n      if (value !== this._sticky) {\n        this._sticky = value;\n        this._hasStickyChanged = true;\n      }\n    }\n    /**\n     * Whether this column should be sticky positioned on the end of the row. Should make sure\n     * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n     * has been changed.\n     */\n    get stickyEnd() {\n      return this._stickyEnd;\n    }\n    set stickyEnd(value) {\n      if (value !== this._stickyEnd) {\n        this._stickyEnd = value;\n        this._hasStickyChanged = true;\n      }\n    }\n    constructor(_table) {\n      this._table = _table;\n      this._hasStickyChanged = false;\n      this._sticky = false;\n      this._stickyEnd = false;\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this.resetStickyChanged();\n      return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    /**\n     * Overridable method that sets the css classes that will be added to every cell in this\n     * column.\n     * In the future, columnCssClassName will change from type string[] to string and this\n     * will set a single string value.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n      this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setNameInput(value) {\n      // If the directive is set without a name (updated programmatically), then this setter will\n      // trigger with an empty string and should not overwrite the programmatically set value.\n      if (value) {\n        this._name = value;\n        this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n        this._updateColumnCssClassName();\n      }\n    }\n    static {\n      this.ɵfac = function CdkColumnDef_Factory(t) {\n        return new (t || CdkColumnDef)(i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkColumnDef,\n        selectors: [[\"\", \"cdkColumnDef\", \"\"]],\n        contentQueries: function CdkColumnDef_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CdkCellDef, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkHeaderCellDef, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkFooterCellDef, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerCell = _t.first);\n          }\n        },\n        inputs: {\n          name: [i0.ɵɵInputFlags.None, \"cdkColumnDef\", \"name\"],\n          sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"sticky\", \"sticky\", booleanAttribute],\n          stickyEnd: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stickyEnd\", \"stickyEnd\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n          useExisting: CdkColumnDef\n        }]), i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return CdkColumnDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n/** Header cell template container that adds the right classes and role. */\nlet CdkHeaderCell = /*#__PURE__*/(() => {\n  class CdkHeaderCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n      super(columnDef, elementRef);\n    }\n    static {\n      this.ɵfac = function CdkHeaderCell_Factory(t) {\n        return new (t || CdkHeaderCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkHeaderCell,\n        selectors: [[\"cdk-header-cell\"], [\"th\", \"cdk-header-cell\", \"\"]],\n        hostAttrs: [\"role\", \"columnheader\", 1, \"cdk-header-cell\"],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return CdkHeaderCell;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Footer cell template container that adds the right classes and role. */\nlet CdkFooterCell = /*#__PURE__*/(() => {\n  class CdkFooterCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n      super(columnDef, elementRef);\n      const role = columnDef._table?._getCellRole();\n      if (role) {\n        elementRef.nativeElement.setAttribute('role', role);\n      }\n    }\n    static {\n      this.ɵfac = function CdkFooterCell_Factory(t) {\n        return new (t || CdkFooterCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkFooterCell,\n        selectors: [[\"cdk-footer-cell\"], [\"td\", \"cdk-footer-cell\", \"\"]],\n        hostAttrs: [1, \"cdk-footer-cell\"],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return CdkFooterCell;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Cell template container that adds the right classes and role. */\nlet CdkCell = /*#__PURE__*/(() => {\n  class CdkCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n      super(columnDef, elementRef);\n      const role = columnDef._table?._getCellRole();\n      if (role) {\n        elementRef.nativeElement.setAttribute('role', role);\n      }\n    }\n    static {\n      this.ɵfac = function CdkCell_Factory(t) {\n        return new (t || CdkCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkCell,\n        selectors: [[\"cdk-cell\"], [\"td\", \"cdk-cell\", \"\"]],\n        hostAttrs: [1, \"cdk-cell\"],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return CdkCell;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @docs-private\n */\nclass _Schedule {\n  constructor() {\n    this.tasks = [];\n    this.endTasks = [];\n  }\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = /*#__PURE__*/new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nlet _CoalescedStyleScheduler = /*#__PURE__*/(() => {\n  class _CoalescedStyleScheduler {\n    constructor(_ngZone) {\n      this._ngZone = _ngZone;\n      this._currentSchedule = null;\n      this._destroyed = new Subject();\n    }\n    /**\n     * Schedules the specified task to run at the end of the current VM turn.\n     */\n    schedule(task) {\n      this._createScheduleIfNeeded();\n      this._currentSchedule.tasks.push(task);\n    }\n    /**\n     * Schedules the specified task to run after other scheduled tasks at the end of the current\n     * VM turn.\n     */\n    scheduleEnd(task) {\n      this._createScheduleIfNeeded();\n      this._currentSchedule.endTasks.push(task);\n    }\n    /** Prevent any further tasks from running. */\n    ngOnDestroy() {\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    _createScheduleIfNeeded() {\n      if (this._currentSchedule) {\n        return;\n      }\n      this._currentSchedule = new _Schedule();\n      this._getScheduleObservable().pipe(takeUntil(this._destroyed)).subscribe(() => {\n        while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n          const schedule = this._currentSchedule;\n          // Capture new tasks scheduled by the current set of tasks.\n          this._currentSchedule = new _Schedule();\n          for (const task of schedule.tasks) {\n            task();\n          }\n          for (const task of schedule.endTasks) {\n            task();\n          }\n        }\n        this._currentSchedule = null;\n      });\n    }\n    _getScheduleObservable() {\n      // Use onStable when in the context of an ongoing change detection cycle so that we\n      // do not accidentally trigger additional cycles.\n      return this._ngZone.isStable ? from(Promise.resolve(undefined)) : this._ngZone.onStable.pipe(take(1));\n    }\n    static {\n      this.ɵfac = function _CoalescedStyleScheduler_Factory(t) {\n        return new (t || _CoalescedStyleScheduler)(i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: _CoalescedStyleScheduler,\n        factory: _CoalescedStyleScheduler.ɵfac\n      });\n    }\n  }\n  return _CoalescedStyleScheduler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nlet BaseRowDef = /*#__PURE__*/(() => {\n  class BaseRowDef {\n    constructor(/** @docs-private */template, _differs) {\n      this.template = template;\n      this._differs = _differs;\n    }\n    ngOnChanges(changes) {\n      // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n      // of the columns property or an empty array if none is provided.\n      if (!this._columnsDiffer) {\n        const columns = changes['columns'] && changes['columns'].currentValue || [];\n        this._columnsDiffer = this._differs.find(columns).create();\n        this._columnsDiffer.diff(columns);\n      }\n    }\n    /**\n     * Returns the difference between the current columns and the columns from the last diff, or null\n     * if there is no difference.\n     */\n    getColumnsDiff() {\n      return this._columnsDiffer.diff(this.columns);\n    }\n    /** Gets this row def's relevant cell template from the provided column def. */\n    extractCellTemplate(column) {\n      if (this instanceof CdkHeaderRowDef) {\n        return column.headerCell.template;\n      }\n      if (this instanceof CdkFooterRowDef) {\n        return column.footerCell.template;\n      } else {\n        return column.cell.template;\n      }\n    }\n    static {\n      this.ɵfac = function BaseRowDef_Factory(t) {\n        return new (t || BaseRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BaseRowDef,\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return BaseRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nlet CdkHeaderRowDef = /*#__PURE__*/(() => {\n  class CdkHeaderRowDef extends BaseRowDef {\n    /** Whether the row is sticky. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(value) {\n      if (value !== this._sticky) {\n        this._sticky = value;\n        this._hasStickyChanged = true;\n      }\n    }\n    constructor(template, _differs, _table) {\n      super(template, _differs);\n      this._table = _table;\n      this._hasStickyChanged = false;\n      this._sticky = false;\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n      super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this.resetStickyChanged();\n      return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    static {\n      this.ɵfac = function CdkHeaderRowDef_Factory(t) {\n        return new (t || CdkHeaderRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkHeaderRowDef,\n        selectors: [[\"\", \"cdkHeaderRowDef\", \"\"]],\n        inputs: {\n          columns: [i0.ɵɵInputFlags.None, \"cdkHeaderRowDef\", \"columns\"],\n          sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return CdkHeaderRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nlet CdkFooterRowDef = /*#__PURE__*/(() => {\n  class CdkFooterRowDef extends BaseRowDef {\n    /** Whether the row is sticky. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(value) {\n      if (value !== this._sticky) {\n        this._sticky = value;\n        this._hasStickyChanged = true;\n      }\n    }\n    constructor(template, _differs, _table) {\n      super(template, _differs);\n      this._table = _table;\n      this._hasStickyChanged = false;\n      this._sticky = false;\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n      super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this.resetStickyChanged();\n      return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    static {\n      this.ɵfac = function CdkFooterRowDef_Factory(t) {\n        return new (t || CdkFooterRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkFooterRowDef,\n        selectors: [[\"\", \"cdkFooterRowDef\", \"\"]],\n        inputs: {\n          columns: [i0.ɵɵInputFlags.None, \"cdkFooterRowDef\", \"columns\"],\n          sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return CdkFooterRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nlet CdkRowDef = /*#__PURE__*/(() => {\n  class CdkRowDef extends BaseRowDef {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    constructor(template, _differs, _table) {\n      super(template, _differs);\n      this._table = _table;\n    }\n    static {\n      this.ɵfac = function CdkRowDef_Factory(t) {\n        return new (t || CdkRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkRowDef,\n        selectors: [[\"\", \"cdkRowDef\", \"\"]],\n        inputs: {\n          columns: [i0.ɵɵInputFlags.None, \"cdkRowDefColumns\", \"columns\"],\n          when: [i0.ɵɵInputFlags.None, \"cdkRowDefWhen\", \"when\"]\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return CdkRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nlet CdkCellOutlet = /*#__PURE__*/(() => {\n  class CdkCellOutlet {\n    /**\n     * Static property containing the latest constructed instance of this class.\n     * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n     * createEmbeddedView. After one of these components are created, this property will provide\n     * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n     * construct the cells with the provided context.\n     */\n    static {\n      this.mostRecentCellOutlet = null;\n    }\n    constructor(_viewContainer) {\n      this._viewContainer = _viewContainer;\n      CdkCellOutlet.mostRecentCellOutlet = this;\n    }\n    ngOnDestroy() {\n      // If this was the last outlet being rendered in the view, remove the reference\n      // from the static property after it has been destroyed to avoid leaking memory.\n      if (CdkCellOutlet.mostRecentCellOutlet === this) {\n        CdkCellOutlet.mostRecentCellOutlet = null;\n      }\n    }\n    static {\n      this.ɵfac = function CdkCellOutlet_Factory(t) {\n        return new (t || CdkCellOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkCellOutlet,\n        selectors: [[\"\", \"cdkCellOutlet\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkCellOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nlet CdkHeaderRow = /*#__PURE__*/(() => {\n  class CdkHeaderRow {\n    static {\n      this.ɵfac = function CdkHeaderRow_Factory(t) {\n        return new (t || CdkHeaderRow)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkHeaderRow,\n        selectors: [[\"cdk-header-row\"], [\"tr\", \"cdk-header-row\", \"\"]],\n        hostAttrs: [\"role\", \"row\", 1, \"cdk-header-row\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 0,\n        consts: [[\"cdkCellOutlet\", \"\"]],\n        template: function CdkHeaderRow_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementContainer(0, 0);\n          }\n        },\n        dependencies: [CdkCellOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkHeaderRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nlet CdkFooterRow = /*#__PURE__*/(() => {\n  class CdkFooterRow {\n    static {\n      this.ɵfac = function CdkFooterRow_Factory(t) {\n        return new (t || CdkFooterRow)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkFooterRow,\n        selectors: [[\"cdk-footer-row\"], [\"tr\", \"cdk-footer-row\", \"\"]],\n        hostAttrs: [\"role\", \"row\", 1, \"cdk-footer-row\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 0,\n        consts: [[\"cdkCellOutlet\", \"\"]],\n        template: function CdkFooterRow_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementContainer(0, 0);\n          }\n        },\n        dependencies: [CdkCellOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkFooterRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nlet CdkRow = /*#__PURE__*/(() => {\n  class CdkRow {\n    static {\n      this.ɵfac = function CdkRow_Factory(t) {\n        return new (t || CdkRow)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkRow,\n        selectors: [[\"cdk-row\"], [\"tr\", \"cdk-row\", \"\"]],\n        hostAttrs: [\"role\", \"row\", 1, \"cdk-row\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 0,\n        consts: [[\"cdkCellOutlet\", \"\"]],\n        template: function CdkRow_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementContainer(0, 0);\n          }\n        },\n        dependencies: [CdkCellOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nlet CdkNoDataRow = /*#__PURE__*/(() => {\n  class CdkNoDataRow {\n    constructor(templateRef) {\n      this.templateRef = templateRef;\n      this._contentClassName = 'cdk-no-data-row';\n    }\n    static {\n      this.ɵfac = function CdkNoDataRow_Factory(t) {\n        return new (t || CdkNoDataRow)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkNoDataRow,\n        selectors: [[\"ng-template\", \"cdkNoDataRow\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkNoDataRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   */\n  constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener) {\n    this._isNativeHtmlTable = _isNativeHtmlTable;\n    this._stickCellCss = _stickCellCss;\n    this.direction = direction;\n    this._coalescedStyleScheduler = _coalescedStyleScheduler;\n    this._isBrowser = _isBrowser;\n    this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n    this._positionListener = _positionListener;\n    this._cachedCellWidths = [];\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`\n    };\n  }\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows, stickyDirections) {\n    const elementsToClear = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n      elementsToClear.push(row);\n      for (let i = 0; i < row.children.length; i++) {\n        elementsToClear.push(row.children[i]);\n      }\n    }\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      for (const element of elementsToClear) {\n        this._removeStickyStyle(element, stickyDirections);\n      }\n    });\n  }\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   */\n  updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true) {\n    if (!rows.length || !this._isBrowser || !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n      if (this._positionListener) {\n        this._positionListener.stickyColumnsUpdated({\n          sizes: []\n        });\n        this._positionListener.stickyEndColumnsUpdated({\n          sizes: []\n        });\n      }\n      return;\n    }\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      const firstRow = rows[0];\n      const numCells = firstRow.children.length;\n      const cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n      const startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n      const endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n      const lastStickyStart = stickyStartStates.lastIndexOf(true);\n      const firstStickyEnd = stickyEndStates.indexOf(true);\n      const isRtl = this.direction === 'rtl';\n      const start = isRtl ? 'right' : 'left';\n      const end = isRtl ? 'left' : 'right';\n      for (const row of rows) {\n        for (let i = 0; i < numCells; i++) {\n          const cell = row.children[i];\n          if (stickyStartStates[i]) {\n            this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n          }\n          if (stickyEndStates[i]) {\n            this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n          }\n        }\n      }\n      if (this._positionListener) {\n        this._positionListener.stickyColumnsUpdated({\n          sizes: lastStickyStart === -1 ? [] : cellWidths.slice(0, lastStickyStart + 1).map((width, index) => stickyStartStates[index] ? width : null)\n        });\n        this._positionListener.stickyEndColumnsUpdated({\n          sizes: firstStickyEnd === -1 ? [] : cellWidths.slice(firstStickyEnd).map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null).reverse()\n        });\n      }\n    });\n  }\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick, stickyStates, position) {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n      // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n      // sticky states need to be reversed as well.\n      const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n      const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n      // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n      const stickyOffsets = [];\n      const stickyCellHeights = [];\n      const elementsToStick = [];\n      for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n        if (!states[rowIndex]) {\n          continue;\n        }\n        stickyOffsets[rowIndex] = stickyOffset;\n        const row = rows[rowIndex];\n        elementsToStick[rowIndex] = this._isNativeHtmlTable ? Array.from(row.children) : [row];\n        const height = row.getBoundingClientRect().height;\n        stickyOffset += height;\n        stickyCellHeights[rowIndex] = height;\n      }\n      const borderedRowIndex = states.lastIndexOf(true);\n      for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n        if (!states[rowIndex]) {\n          continue;\n        }\n        const offset = stickyOffsets[rowIndex];\n        const isBorderedRowIndex = rowIndex === borderedRowIndex;\n        for (const element of elementsToStick[rowIndex]) {\n          this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n        }\n      }\n      if (position === 'top') {\n        this._positionListener?.stickyHeaderRowsUpdated({\n          sizes: stickyCellHeights,\n          offsets: stickyOffsets,\n          elements: elementsToStick\n        });\n      } else {\n        this._positionListener?.stickyFooterRowsUpdated({\n          sizes: stickyCellHeights,\n          offsets: stickyOffsets,\n          elements: elementsToStick\n        });\n      }\n    });\n  }\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement, stickyStates) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      const tfoot = tableElement.querySelector('tfoot');\n      if (tfoot) {\n        if (stickyStates.some(state => !state)) {\n          this._removeStickyStyle(tfoot, ['bottom']);\n        } else {\n          this._addStickyStyle(tfoot, 'bottom', 0, false);\n        }\n      }\n    });\n  }\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element, stickyDirections) {\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(element, dir, dirValue, isBorderElement) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element) {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1\n    };\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n    return zIndex ? `${zIndex}` : '';\n  }\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row, recalculateCellWidths = true) {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n    const cellWidths = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      let cell = firstRowCells[i];\n      cellWidths.push(cell.getBoundingClientRect().width);\n    }\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n  return Error(`Could not find a matching row definition for the` + `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n  return Error('Missing definitions for header, footer, and row; ' + 'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = /*#__PURE__*/new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nlet CdkRecycleRows = /*#__PURE__*/(() => {\n  class CdkRecycleRows {\n    static {\n      this.ɵfac = function CdkRecycleRows_Factory(t) {\n        return new (t || CdkRecycleRows)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkRecycleRows,\n        selectors: [[\"cdk-table\", \"recycleRows\", \"\"], [\"table\", \"cdk-table\", \"\", \"recycleRows\", \"\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: _VIEW_REPEATER_STRATEGY,\n          useClass: _RecycleViewRepeaterStrategy\n        }])]\n      });\n    }\n  }\n  return CdkRecycleRows;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nlet DataRowOutlet = /*#__PURE__*/(() => {\n  class DataRowOutlet {\n    constructor(viewContainer, elementRef) {\n      this.viewContainer = viewContainer;\n      this.elementRef = elementRef;\n      const table = inject(CDK_TABLE);\n      table._rowOutlet = this;\n      table._outletAssigned();\n    }\n    static {\n      this.ɵfac = function DataRowOutlet_Factory(t) {\n        return new (t || DataRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: DataRowOutlet,\n        selectors: [[\"\", \"rowOutlet\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return DataRowOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nlet HeaderRowOutlet = /*#__PURE__*/(() => {\n  class HeaderRowOutlet {\n    constructor(viewContainer, elementRef) {\n      this.viewContainer = viewContainer;\n      this.elementRef = elementRef;\n      const table = inject(CDK_TABLE);\n      table._headerRowOutlet = this;\n      table._outletAssigned();\n    }\n    static {\n      this.ɵfac = function HeaderRowOutlet_Factory(t) {\n        return new (t || HeaderRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: HeaderRowOutlet,\n        selectors: [[\"\", \"headerRowOutlet\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return HeaderRowOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nlet FooterRowOutlet = /*#__PURE__*/(() => {\n  class FooterRowOutlet {\n    constructor(viewContainer, elementRef) {\n      this.viewContainer = viewContainer;\n      this.elementRef = elementRef;\n      const table = inject(CDK_TABLE);\n      table._footerRowOutlet = this;\n      table._outletAssigned();\n    }\n    static {\n      this.ɵfac = function FooterRowOutlet_Factory(t) {\n        return new (t || FooterRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: FooterRowOutlet,\n        selectors: [[\"\", \"footerRowOutlet\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return FooterRowOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nlet NoDataRowOutlet = /*#__PURE__*/(() => {\n  class NoDataRowOutlet {\n    constructor(viewContainer, elementRef) {\n      this.viewContainer = viewContainer;\n      this.elementRef = elementRef;\n      const table = inject(CDK_TABLE);\n      table._noDataRowOutlet = this;\n      table._outletAssigned();\n    }\n    static {\n      this.ɵfac = function NoDataRowOutlet_Factory(t) {\n        return new (t || NoDataRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: NoDataRowOutlet,\n        selectors: [[\"\", \"noDataRowOutlet\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return NoDataRowOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE =\n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"/>\n  <ng-content select=\"colgroup, col\"/>\n\n  <!--\n    Unprojected content throws a hydration error so we need this to capture it.\n    It gets removed on the client so it doesn't affect the layout.\n  -->\n  @if (_isServer) {\n    <ng-content/>\n  }\n\n  @if (_isNativeHtmlTable) {\n    <thead role=\"rowgroup\">\n      <ng-container headerRowOutlet/>\n    </thead>\n    <tbody role=\"rowgroup\">\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n    </tbody>\n    <tfoot role=\"rowgroup\">\n      <ng-container footerRowOutlet/>\n    </tfoot>\n  } @else {\n    <ng-container headerRowOutlet/>\n    <ng-container rowOutlet/>\n    <ng-container noDataRowOutlet/>\n    <ng-container footerRowOutlet/>\n  }\n`;\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nclass RowViewRef extends EmbeddedViewRef {}\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nlet CdkTable = /*#__PURE__*/(() => {\n  class CdkTable {\n    /** Aria role to apply to the table's cells based on the table's own role. */\n    _getCellRole() {\n      if (this._cellRoleInternal === undefined) {\n        // Perform this lazily in case the table's role was updated by a directive after construction.\n        const role = this._elementRef.nativeElement.getAttribute('role');\n        const cellRole = role === 'grid' || role === 'treegrid' ? 'gridcell' : 'cell';\n        this._cellRoleInternal = this._isNativeHtmlTable && cellRole === 'cell' ? null : cellRole;\n      }\n      return this._cellRoleInternal;\n    }\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n     * relative to the function to know if a row should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    get trackBy() {\n      return this._trackByFn;\n    }\n    set trackBy(fn) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n        console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n      }\n      this._trackByFn = fn;\n    }\n    /**\n     * The table's source of data, which can be provided in three ways (in order of complexity):\n     *   - Simple data array (each object represents one table row)\n     *   - Stream that emits a data array each time the array changes\n     *   - `DataSource` object that implements the connect/disconnect interface.\n     *\n     * If a data array is provided, the table must be notified when the array's objects are\n     * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n     * render the diff since the last table render. If the data array reference is changed, the table\n     * will automatically trigger an update to the rows.\n     *\n     * When providing an Observable stream, the table will trigger an update automatically when the\n     * stream emits a new array of data.\n     *\n     * Finally, when providing a `DataSource` object, the table will use the Observable stream\n     * provided by the connect function and trigger updates when that stream emits new data array\n     * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n     * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n     * subscriptions registered during the connect process).\n     */\n    get dataSource() {\n      return this._dataSource;\n    }\n    set dataSource(dataSource) {\n      if (this._dataSource !== dataSource) {\n        this._switchDataSource(dataSource);\n      }\n    }\n    /**\n     * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n     * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n     * dataobject will render the first row that evaluates its when predicate to true, in the order\n     * defined in the table, or otherwise the default row which does not have a when predicate.\n     */\n    get multiTemplateDataRows() {\n      return this._multiTemplateDataRows;\n    }\n    set multiTemplateDataRows(value) {\n      this._multiTemplateDataRows = value;\n      // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n      // this setter will be invoked before the row outlet has been defined hence the null check.\n      if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n        this._forceRenderDataRows();\n        this.updateStickyColumnStyles();\n      }\n    }\n    /**\n     * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n     * and optimize rendering sticky styles for native tables. No-op for flex tables.\n     */\n    get fixedLayout() {\n      return this._fixedLayout;\n    }\n    set fixedLayout(value) {\n      this._fixedLayout = value;\n      // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n      this._forceRecalculateCellWidths = true;\n      this._stickyColumnStylesNeedReset = true;\n    }\n    constructor(_differs, _changeDetectorRef, _elementRef, role, _dir, _document, _platform, _viewRepeater, _coalescedStyleScheduler, _viewportRuler,\n    /**\n     * @deprecated `_stickyPositioningListener` parameter to become required.\n     * @breaking-change 13.0.0\n     */\n    _stickyPositioningListener,\n    /**\n     * @deprecated `_ngZone` parameter to become required.\n     * @breaking-change 14.0.0\n     */\n    _ngZone) {\n      this._differs = _differs;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._elementRef = _elementRef;\n      this._dir = _dir;\n      this._platform = _platform;\n      this._viewRepeater = _viewRepeater;\n      this._coalescedStyleScheduler = _coalescedStyleScheduler;\n      this._viewportRuler = _viewportRuler;\n      this._stickyPositioningListener = _stickyPositioningListener;\n      this._ngZone = _ngZone;\n      /** Subject that emits when the component has been destroyed. */\n      this._onDestroy = new Subject();\n      /**\n       * Map of all the user's defined columns (header, data, and footer cell template) identified by\n       * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n       * any custom column definitions added to `_customColumnDefs`.\n       */\n      this._columnDefsByName = new Map();\n      /**\n       * Column definitions that were defined outside of the direct content children of the table.\n       * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n       * column definitions as *its* content child.\n       */\n      this._customColumnDefs = new Set();\n      /**\n       * Data row definitions that were defined outside of the direct content children of the table.\n       * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n       * built-in data rows as *its* content child.\n       */\n      this._customRowDefs = new Set();\n      /**\n       * Header row definitions that were defined outside of the direct content children of the table.\n       * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n       * built-in header rows as *its* content child.\n       */\n      this._customHeaderRowDefs = new Set();\n      /**\n       * Footer row definitions that were defined outside of the direct content children of the table.\n       * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n       * built-in footer row as *its* content child.\n       */\n      this._customFooterRowDefs = new Set();\n      /**\n       * Whether the header row definition has been changed. Triggers an update to the header row after\n       * content is checked. Initialized as true so that the table renders the initial set of rows.\n       */\n      this._headerRowDefChanged = true;\n      /**\n       * Whether the footer row definition has been changed. Triggers an update to the footer row after\n       * content is checked. Initialized as true so that the table renders the initial set of rows.\n       */\n      this._footerRowDefChanged = true;\n      /**\n       * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n       * change.\n       */\n      this._stickyColumnStylesNeedReset = true;\n      /**\n       * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n       * `false`, cached values will be used instead. This is only applicable to tables with\n       * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n       */\n      this._forceRecalculateCellWidths = true;\n      /**\n       * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n       * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n       * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n       * and row template matches, which allows the `IterableDiffer` to check rows by reference\n       * and understand which rows are added/moved/removed.\n       *\n       * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n       * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n       * contains an array of created pairs. The array is necessary to handle cases where the data\n       * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n       * stored.\n       */\n      this._cachedRenderRowsMap = new Map();\n      /**\n       * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n       * table subclasses.\n       */\n      this.stickyCssClass = 'cdk-table-sticky';\n      /**\n       * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n       * the position is set in a selector associated with the value of stickyCssClass. May be\n       * overridden by table subclasses\n       */\n      this.needsPositionStickyOnElement = true;\n      /** Whether the no data row is currently showing anything. */\n      this._isShowingNoDataRow = false;\n      /** Whether the table has rendered out all the outlets for the first time. */\n      this._hasAllOutlets = false;\n      /** Whether the table is done initializing. */\n      this._hasInitialized = false;\n      this._cellRoleInternal = undefined;\n      this._multiTemplateDataRows = false;\n      this._fixedLayout = false;\n      /**\n       * Emits when the table completes rendering a set of data rows based on the latest data from the\n       * data source, even if the set of rows is empty.\n       */\n      this.contentChanged = new EventEmitter();\n      // TODO(andrewseguin): Remove max value as the end index\n      //   and instead calculate the view on init and scroll.\n      /**\n       * Stream containing the latest information on what rows are being displayed on screen.\n       * Can be used by the data source to as a heuristic of what data should be provided.\n       *\n       * @docs-private\n       */\n      this.viewChange = new BehaviorSubject({\n        start: 0,\n        end: Number.MAX_VALUE\n      });\n      if (!role) {\n        _elementRef.nativeElement.setAttribute('role', 'table');\n      }\n      this._document = _document;\n      this._isServer = !_platform.isBrowser;\n      this._isNativeHtmlTable = _elementRef.nativeElement.nodeName === 'TABLE';\n    }\n    ngOnInit() {\n      this._setupStickyStyler();\n      // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n      // the user has provided a custom trackBy, return the result of that function as evaluated\n      // with the values of the `RenderRow`'s data and index.\n      this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n        return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n      });\n      this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n        this._forceRecalculateCellWidths = true;\n      });\n    }\n    ngAfterContentInit() {\n      this._hasInitialized = true;\n    }\n    ngAfterContentChecked() {\n      // Only start re-rendering in `ngAfterContentChecked` after the first render.\n      if (this._canRender()) {\n        this._render();\n      }\n    }\n    ngOnDestroy() {\n      [this._rowOutlet?.viewContainer, this._headerRowOutlet?.viewContainer, this._footerRowOutlet?.viewContainer, this._cachedRenderRowsMap, this._customColumnDefs, this._customRowDefs, this._customHeaderRowDefs, this._customFooterRowDefs, this._columnDefsByName].forEach(def => {\n        def?.clear();\n      });\n      this._headerRowDefs = [];\n      this._footerRowDefs = [];\n      this._defaultRowDef = null;\n      this._onDestroy.next();\n      this._onDestroy.complete();\n      if (isDataSource(this.dataSource)) {\n        this.dataSource.disconnect(this);\n      }\n    }\n    /**\n     * Renders rows based on the table's latest set of data, which was either provided directly as an\n     * input or retrieved through an Observable stream (directly or from a DataSource).\n     * Checks for differences in the data since the last diff to perform only the necessary\n     * changes (add/remove/move rows).\n     *\n     * If the table's data source is a DataSource or Observable, this will be invoked automatically\n     * each time the provided Observable stream emits a new data array. Otherwise if your data is\n     * an array, this function will need to be called to render any changes.\n     */\n    renderRows() {\n      this._renderRows = this._getAllRenderRows();\n      const changes = this._dataDiffer.diff(this._renderRows);\n      if (!changes) {\n        this._updateNoDataRow();\n        this.contentChanged.next();\n        return;\n      }\n      const viewContainer = this._rowOutlet.viewContainer;\n      this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, change => {\n        if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n          this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n        }\n      });\n      // Update the meta context of a row's context data (index, count, first, last, ...)\n      this._updateRowIndexContext();\n      // Update rows that did not get added/removed/moved but may have had their identity changed,\n      // e.g. if trackBy matched data on some property but the actual data reference changed.\n      changes.forEachIdentityChange(record => {\n        const rowView = viewContainer.get(record.currentIndex);\n        rowView.context.$implicit = record.item.data;\n      });\n      this._updateNoDataRow();\n      // Allow the new row data to render before measuring it.\n      // @breaking-change 14.0.0 Remove undefined check once _ngZone is required.\n      if (this._ngZone && NgZone.isInAngularZone()) {\n        this._ngZone.onStable.pipe(take(1), takeUntil(this._onDestroy)).subscribe(() => {\n          this.updateStickyColumnStyles();\n        });\n      } else {\n        this.updateStickyColumnStyles();\n      }\n      this.contentChanged.next();\n    }\n    /** Adds a column definition that was not included as part of the content children. */\n    addColumnDef(columnDef) {\n      this._customColumnDefs.add(columnDef);\n    }\n    /** Removes a column definition that was not included as part of the content children. */\n    removeColumnDef(columnDef) {\n      this._customColumnDefs.delete(columnDef);\n    }\n    /** Adds a row definition that was not included as part of the content children. */\n    addRowDef(rowDef) {\n      this._customRowDefs.add(rowDef);\n    }\n    /** Removes a row definition that was not included as part of the content children. */\n    removeRowDef(rowDef) {\n      this._customRowDefs.delete(rowDef);\n    }\n    /** Adds a header row definition that was not included as part of the content children. */\n    addHeaderRowDef(headerRowDef) {\n      this._customHeaderRowDefs.add(headerRowDef);\n      this._headerRowDefChanged = true;\n    }\n    /** Removes a header row definition that was not included as part of the content children. */\n    removeHeaderRowDef(headerRowDef) {\n      this._customHeaderRowDefs.delete(headerRowDef);\n      this._headerRowDefChanged = true;\n    }\n    /** Adds a footer row definition that was not included as part of the content children. */\n    addFooterRowDef(footerRowDef) {\n      this._customFooterRowDefs.add(footerRowDef);\n      this._footerRowDefChanged = true;\n    }\n    /** Removes a footer row definition that was not included as part of the content children. */\n    removeFooterRowDef(footerRowDef) {\n      this._customFooterRowDefs.delete(footerRowDef);\n      this._footerRowDefChanged = true;\n    }\n    /** Sets a no data row definition that was not included as a part of the content children. */\n    setNoDataRow(noDataRow) {\n      this._customNoDataRow = noDataRow;\n    }\n    /**\n     * Updates the header sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n     * automatically called when the header row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyHeaderRowStyles() {\n      const headerRows = this._getRenderedRows(this._headerRowOutlet);\n      // Hide the thead element if there are no header rows. This is necessary to satisfy\n      // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n      // required child `row`.\n      if (this._isNativeHtmlTable) {\n        const thead = closestTableSection(this._headerRowOutlet, 'thead');\n        if (thead) {\n          thead.style.display = headerRows.length ? '' : 'none';\n        }\n      }\n      const stickyStates = this._headerRowDefs.map(def => def.sticky);\n      this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n      this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n      // Reset the dirty state of the sticky input change since it has been used.\n      this._headerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n     * automatically called when the footer row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyFooterRowStyles() {\n      const footerRows = this._getRenderedRows(this._footerRowOutlet);\n      // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n      // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n      // required child `row`.\n      if (this._isNativeHtmlTable) {\n        const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n        if (tfoot) {\n          tfoot.style.display = footerRows.length ? '' : 'none';\n        }\n      }\n      const stickyStates = this._footerRowDefs.map(def => def.sticky);\n      this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n      this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n      this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n      // Reset the dirty state of the sticky input change since it has been used.\n      this._footerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the column sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the left and right. Then sticky styles are added for the left and right according\n     * to the column definitions for each cell in each row. This is automatically called when\n     * the data source provides a new set of data or when a column definition changes its sticky\n     * input. May be called manually for cases where the cell content changes outside of these events.\n     */\n    updateStickyColumnStyles() {\n      const headerRows = this._getRenderedRows(this._headerRowOutlet);\n      const dataRows = this._getRenderedRows(this._rowOutlet);\n      const footerRows = this._getRenderedRows(this._footerRowOutlet);\n      // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n      // In a table using a fixed layout, row content won't affect column width, so sticky styles\n      // don't need to be cleared unless either the sticky column config changes or one of the row\n      // defs change.\n      if (this._isNativeHtmlTable && !this._fixedLayout || this._stickyColumnStylesNeedReset) {\n        // Clear the left and right positioning from all columns in the table across all rows since\n        // sticky columns span across all table sections (header, data, footer)\n        this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n        this._stickyColumnStylesNeedReset = false;\n      }\n      // Update the sticky styles for each header row depending on the def's sticky state\n      headerRows.forEach((headerRow, i) => {\n        this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n      });\n      // Update the sticky styles for each data row depending on its def's sticky state\n      this._rowDefs.forEach(rowDef => {\n        // Collect all the rows rendered with this row definition.\n        const rows = [];\n        for (let i = 0; i < dataRows.length; i++) {\n          if (this._renderRows[i].rowDef === rowDef) {\n            rows.push(dataRows[i]);\n          }\n        }\n        this._addStickyColumnStyles(rows, rowDef);\n      });\n      // Update the sticky styles for each footer row depending on the def's sticky state\n      footerRows.forEach((footerRow, i) => {\n        this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n      });\n      // Reset the dirty state of the sticky input change since it has been used.\n      Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n    }\n    /** Invoked whenever an outlet is created and has been assigned to the table. */\n    _outletAssigned() {\n      // Trigger the first render once all outlets have been assigned. We do it this way, as\n      // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n      // the next change detection will happen.\n      // Also we can't use queries to resolve the outlets, because they're wrapped in a\n      // conditional, so we have to rely on them being assigned via DI.\n      if (!this._hasAllOutlets && this._rowOutlet && this._headerRowOutlet && this._footerRowOutlet && this._noDataRowOutlet) {\n        this._hasAllOutlets = true;\n        // In some setups this may fire before `ngAfterContentInit`\n        // so we need a check here. See #28538.\n        if (this._canRender()) {\n          this._render();\n        }\n      }\n    }\n    /** Whether the table has all the information to start rendering. */\n    _canRender() {\n      return this._hasAllOutlets && this._hasInitialized;\n    }\n    /** Renders the table if its state has changed. */\n    _render() {\n      // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n      this._cacheRowDefs();\n      this._cacheColumnDefs();\n      // Make sure that the user has at least added header, footer, or data row def.\n      if (!this._headerRowDefs.length && !this._footerRowDefs.length && !this._rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableMissingRowDefsError();\n      }\n      // Render updates if the list of columns have been changed for the header, row, or footer defs.\n      const columnsChanged = this._renderUpdatedColumns();\n      const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n      // Ensure sticky column styles are reset if set to `true` elsewhere.\n      this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n      this._forceRecalculateCellWidths = rowDefsChanged;\n      // If the header row definition has been changed, trigger a render to the header row.\n      if (this._headerRowDefChanged) {\n        this._forceRenderHeaderRows();\n        this._headerRowDefChanged = false;\n      }\n      // If the footer row definition has been changed, trigger a render to the footer row.\n      if (this._footerRowDefChanged) {\n        this._forceRenderFooterRows();\n        this._footerRowDefChanged = false;\n      }\n      // If there is a data source and row definitions, connect to the data source unless a\n      // connection has already been made.\n      if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n        this._observeRenderChanges();\n      } else if (this._stickyColumnStylesNeedReset) {\n        // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n        // called when it row data arrives. Otherwise, we need to call it proactively.\n        this.updateStickyColumnStyles();\n      }\n      this._checkStickyStates();\n    }\n    /**\n     * Get the list of RenderRow objects to render according to the current list of data and defined\n     * row definitions. If the previous list already contained a particular pair, it should be reused\n     * so that the differ equates their references.\n     */\n    _getAllRenderRows() {\n      const renderRows = [];\n      // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n      // new cache while unused ones can be picked up by garbage collection.\n      const prevCachedRenderRows = this._cachedRenderRowsMap;\n      this._cachedRenderRowsMap = new Map();\n      // For each data object, get the list of rows that should be rendered, represented by the\n      // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n      for (let i = 0; i < this._data.length; i++) {\n        let data = this._data[i];\n        const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n        if (!this._cachedRenderRowsMap.has(data)) {\n          this._cachedRenderRowsMap.set(data, new WeakMap());\n        }\n        for (let j = 0; j < renderRowsForData.length; j++) {\n          let renderRow = renderRowsForData[j];\n          const cache = this._cachedRenderRowsMap.get(renderRow.data);\n          if (cache.has(renderRow.rowDef)) {\n            cache.get(renderRow.rowDef).push(renderRow);\n          } else {\n            cache.set(renderRow.rowDef, [renderRow]);\n          }\n          renderRows.push(renderRow);\n        }\n      }\n      return renderRows;\n    }\n    /**\n     * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n     * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n     * `(T, CdkRowDef)` pair.\n     */\n    _getRenderRowsForData(data, dataIndex, cache) {\n      const rowDefs = this._getRowDefs(data, dataIndex);\n      return rowDefs.map(rowDef => {\n        const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n        if (cachedRenderRows.length) {\n          const dataRow = cachedRenderRows.shift();\n          dataRow.dataIndex = dataIndex;\n          return dataRow;\n        } else {\n          return {\n            data,\n            rowDef,\n            dataIndex\n          };\n        }\n      });\n    }\n    /** Update the map containing the content's column definitions. */\n    _cacheColumnDefs() {\n      this._columnDefsByName.clear();\n      const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n      columnDefs.forEach(columnDef => {\n        if (this._columnDefsByName.has(columnDef.name) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw getTableDuplicateColumnNameError(columnDef.name);\n        }\n        this._columnDefsByName.set(columnDef.name, columnDef);\n      });\n    }\n    /** Update the list of all available row definitions that can be used. */\n    _cacheRowDefs() {\n      this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n      this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n      this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n      // After all row definitions are determined, find the row definition to be considered default.\n      const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n      if (!this.multiTemplateDataRows && defaultRowDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableMultipleDefaultRowDefsError();\n      }\n      this._defaultRowDef = defaultRowDefs[0];\n    }\n    /**\n     * Check if the header, data, or footer rows have changed what columns they want to display or\n     * whether the sticky states have changed for the header or footer. If there is a diff, then\n     * re-render that section.\n     */\n    _renderUpdatedColumns() {\n      const columnsDiffReducer = (acc, def) => acc || !!def.getColumnsDiff();\n      // Force re-render data rows if the list of column definitions have changed.\n      const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n      if (dataColumnsChanged) {\n        this._forceRenderDataRows();\n      }\n      // Force re-render header/footer rows if the list of column definitions have changed.\n      const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n      if (headerColumnsChanged) {\n        this._forceRenderHeaderRows();\n      }\n      const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n      if (footerColumnsChanged) {\n        this._forceRenderFooterRows();\n      }\n      return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n    }\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the row outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n      this._data = [];\n      if (isDataSource(this.dataSource)) {\n        this.dataSource.disconnect(this);\n      }\n      // Stop listening for data from the previous data source.\n      if (this._renderChangeSubscription) {\n        this._renderChangeSubscription.unsubscribe();\n        this._renderChangeSubscription = null;\n      }\n      if (!dataSource) {\n        if (this._dataDiffer) {\n          this._dataDiffer.diff([]);\n        }\n        if (this._rowOutlet) {\n          this._rowOutlet.viewContainer.clear();\n        }\n      }\n      this._dataSource = dataSource;\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n      // If no data source has been set, there is nothing to observe for changes.\n      if (!this.dataSource) {\n        return;\n      }\n      let dataStream;\n      if (isDataSource(this.dataSource)) {\n        dataStream = this.dataSource.connect(this);\n      } else if (isObservable(this.dataSource)) {\n        dataStream = this.dataSource;\n      } else if (Array.isArray(this.dataSource)) {\n        dataStream = of(this.dataSource);\n      }\n      if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownDataSourceError();\n      }\n      this._renderChangeSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n        this._data = data || [];\n        this.renderRows();\n      });\n    }\n    /**\n     * Clears any existing content in the header row outlet and creates a new embedded view\n     * in the outlet using the header row definition.\n     */\n    _forceRenderHeaderRows() {\n      // Clear the header row outlet if any content exists.\n      if (this._headerRowOutlet.viewContainer.length > 0) {\n        this._headerRowOutlet.viewContainer.clear();\n      }\n      this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n      this.updateStickyHeaderRowStyles();\n    }\n    /**\n     * Clears any existing content in the footer row outlet and creates a new embedded view\n     * in the outlet using the footer row definition.\n     */\n    _forceRenderFooterRows() {\n      // Clear the footer row outlet if any content exists.\n      if (this._footerRowOutlet.viewContainer.length > 0) {\n        this._footerRowOutlet.viewContainer.clear();\n      }\n      this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n      this.updateStickyFooterRowStyles();\n    }\n    /** Adds the sticky column styles for the rows according to the columns' stick states. */\n    _addStickyColumnStyles(rows, rowDef) {\n      const columnDefs = Array.from(rowDef.columns || []).map(columnName => {\n        const columnDef = this._columnDefsByName.get(columnName);\n        if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw getTableUnknownColumnError(columnName);\n        }\n        return columnDef;\n      });\n      const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n      const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n      this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n    }\n    /** Gets the list of rows that have been rendered in the row outlet. */\n    _getRenderedRows(rowOutlet) {\n      const renderedRows = [];\n      for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n        const viewRef = rowOutlet.viewContainer.get(i);\n        renderedRows.push(viewRef.rootNodes[0]);\n      }\n      return renderedRows;\n    }\n    /**\n     * Get the matching row definitions that should be used for this row data. If there is only\n     * one row definition, it is returned. Otherwise, find the row definitions that has a when\n     * predicate that returns true with the data. If none return true, return the default row\n     * definition.\n     */\n    _getRowDefs(data, dataIndex) {\n      if (this._rowDefs.length == 1) {\n        return [this._rowDefs[0]];\n      }\n      let rowDefs = [];\n      if (this.multiTemplateDataRows) {\n        rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n      } else {\n        let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n        if (rowDef) {\n          rowDefs.push(rowDef);\n        }\n      }\n      if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableMissingMatchingRowDefError(data);\n      }\n      return rowDefs;\n    }\n    _getEmbeddedViewArgs(renderRow, index) {\n      const rowDef = renderRow.rowDef;\n      const context = {\n        $implicit: renderRow.data\n      };\n      return {\n        templateRef: rowDef.template,\n        context,\n        index\n      };\n    }\n    /**\n     * Creates a new row template in the outlet and fills it with the set of cell templates.\n     * Optionally takes a context to provide to the row and cells, as well as an optional index\n     * of where to place the new row template in the outlet.\n     */\n    _renderRow(outlet, rowDef, index, context = {}) {\n      // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n      const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n      this._renderCellTemplateForItem(rowDef, context);\n      return view;\n    }\n    _renderCellTemplateForItem(rowDef, context) {\n      for (let cellTemplate of this._getCellTemplates(rowDef)) {\n        if (CdkCellOutlet.mostRecentCellOutlet) {\n          CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Updates the index-related context for each row to reflect any changes in the index of the rows,\n     * e.g. first/last/even/odd.\n     */\n    _updateRowIndexContext() {\n      const viewContainer = this._rowOutlet.viewContainer;\n      for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n        const viewRef = viewContainer.get(renderIndex);\n        const context = viewRef.context;\n        context.count = count;\n        context.first = renderIndex === 0;\n        context.last = renderIndex === count - 1;\n        context.even = renderIndex % 2 === 0;\n        context.odd = !context.even;\n        if (this.multiTemplateDataRows) {\n          context.dataIndex = this._renderRows[renderIndex].dataIndex;\n          context.renderIndex = renderIndex;\n        } else {\n          context.index = this._renderRows[renderIndex].dataIndex;\n        }\n      }\n    }\n    /** Gets the column definitions for the provided row def. */\n    _getCellTemplates(rowDef) {\n      if (!rowDef || !rowDef.columns) {\n        return [];\n      }\n      return Array.from(rowDef.columns, columnId => {\n        const column = this._columnDefsByName.get(columnId);\n        if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw getTableUnknownColumnError(columnId);\n        }\n        return rowDef.extractCellTemplate(column);\n      });\n    }\n    /**\n     * Forces a re-render of the data rows. Should be called in cases where there has been an input\n     * change that affects the evaluation of which rows should be rendered, e.g. toggling\n     * `multiTemplateDataRows` or adding/removing row definitions.\n     */\n    _forceRenderDataRows() {\n      this._dataDiffer.diff([]);\n      this._rowOutlet.viewContainer.clear();\n      this.renderRows();\n    }\n    /**\n     * Checks if there has been a change in sticky states since last check and applies the correct\n     * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n     * during a change detection and after the inputs are settled (after content check).\n     */\n    _checkStickyStates() {\n      const stickyCheckReducer = (acc, d) => {\n        return acc || d.hasStickyChanged();\n      };\n      // Note that the check needs to occur for every definition since it notifies the definition\n      // that it can reset its dirty state. Using another operator like `some` may short-circuit\n      // remaining definitions and leave them in an unchecked state.\n      if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n        this.updateStickyHeaderRowStyles();\n      }\n      if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n        this.updateStickyFooterRowStyles();\n      }\n      if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n        this._stickyColumnStylesNeedReset = true;\n        this.updateStickyColumnStyles();\n      }\n    }\n    /**\n     * Creates the sticky styler that will be used for sticky rows and columns. Listens\n     * for directionality changes and provides the latest direction to the styler. Re-applies column\n     * stickiness when directionality changes.\n     */\n    _setupStickyStyler() {\n      const direction = this._dir ? this._dir.value : 'ltr';\n      this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener);\n      (this._dir ? this._dir.change : of()).pipe(takeUntil(this._onDestroy)).subscribe(value => {\n        this._stickyStyler.direction = value;\n        this.updateStickyColumnStyles();\n      });\n    }\n    /** Filters definitions that belong to this table from a QueryList. */\n    _getOwnDefs(items) {\n      return items.filter(item => !item._table || item._table === this);\n    }\n    /** Creates or removes the no data row, depending on whether any data is being shown. */\n    _updateNoDataRow() {\n      const noDataRow = this._customNoDataRow || this._noDataRow;\n      if (!noDataRow) {\n        return;\n      }\n      const shouldShow = this._rowOutlet.viewContainer.length === 0;\n      if (shouldShow === this._isShowingNoDataRow) {\n        return;\n      }\n      const container = this._noDataRowOutlet.viewContainer;\n      if (shouldShow) {\n        const view = container.createEmbeddedView(noDataRow.templateRef);\n        const rootNode = view.rootNodes[0];\n        // Only add the attributes if we have a single root node since it's hard\n        // to figure out which one to add it to when there are multiple.\n        if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n          rootNode.setAttribute('role', 'row');\n          rootNode.classList.add(noDataRow._contentClassName);\n        }\n      } else {\n        container.clear();\n      }\n      this._isShowingNoDataRow = shouldShow;\n      this._changeDetectorRef.markForCheck();\n    }\n    static {\n      this.ɵfac = function CdkTable_Factory(t) {\n        return new (t || CdkTable)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵinjectAttribute('role'), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(_COALESCED_STYLE_SCHEDULER), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(STICKY_POSITIONING_LISTENER, 12), i0.ɵɵdirectiveInject(i0.NgZone, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkTable,\n        selectors: [[\"cdk-table\"], [\"table\", \"cdk-table\", \"\"]],\n        contentQueries: function CdkTable_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CdkNoDataRow, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkColumnDef, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkRowDef, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkHeaderRowDef, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkFooterRowDef, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRow = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentColumnDefs = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentRowDefs = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentHeaderRowDefs = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentFooterRowDefs = _t);\n          }\n        },\n        hostAttrs: [1, \"cdk-table\"],\n        hostVars: 2,\n        hostBindings: function CdkTable_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"cdk-table-fixed-layout\", ctx.fixedLayout);\n          }\n        },\n        inputs: {\n          trackBy: \"trackBy\",\n          dataSource: \"dataSource\",\n          multiTemplateDataRows: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute],\n          fixedLayout: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fixedLayout\", \"fixedLayout\", booleanAttribute]\n        },\n        outputs: {\n          contentChanged: \"contentChanged\"\n        },\n        exportAs: [\"cdkTable\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CDK_TABLE,\n          useExisting: CdkTable\n        }, {\n          provide: _VIEW_REPEATER_STRATEGY,\n          useClass: _DisposeViewRepeaterStrategy\n        }, {\n          provide: _COALESCED_STYLE_SCHEDULER,\n          useClass: _CoalescedStyleScheduler\n        },\n        // Prevent nested tables from seeing this table's StickyPositioningListener.\n        {\n          provide: STICKY_POSITIONING_LISTENER,\n          useValue: null\n        }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c1,\n        decls: 5,\n        vars: 2,\n        consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n        template: function CdkTable_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c0);\n            i0.ɵɵprojection(0);\n            i0.ɵɵprojection(1, 1);\n            i0.ɵɵtemplate(2, CdkTable_Conditional_2_Template, 1, 0)(3, CdkTable_Conditional_3_Template, 7, 0)(4, CdkTable_Conditional_4_Template, 4, 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵconditional(2, ctx._isServer ? 2 : -1);\n            i0.ɵɵadvance();\n            i0.ɵɵconditional(3, ctx._isNativeHtmlTable ? 3 : 4);\n          }\n        },\n        dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n        styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkTable;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n  return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n  const uppercaseSection = section.toUpperCase();\n  let current = outlet.viewContainer.element.nativeElement;\n  while (current) {\n    // 1 is an element node.\n    const nodeName = current.nodeType === 1 ? current.nodeName : null;\n    if (nodeName === uppercaseSection) {\n      return current;\n    } else if (nodeName === 'TABLE') {\n      // Stop traversing past the `table` node.\n      break;\n    }\n    current = current.parentNode;\n  }\n  return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nlet CdkTextColumn = /*#__PURE__*/(() => {\n  class CdkTextColumn {\n    /** Column name that should be used to reference this column. */\n    get name() {\n      return this._name;\n    }\n    set name(name) {\n      this._name = name;\n      // With Ivy, inputs can be initialized before static query results are\n      // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n      this._syncColumnDefName();\n    }\n    constructor(\n    // `CdkTextColumn` is always requiring a table, but we just assert it manually\n    // for better error reporting.\n    // tslint:disable-next-line: lightweight-tokens\n    _table, _options) {\n      this._table = _table;\n      this._options = _options;\n      /** Alignment of the cell values. */\n      this.justify = 'start';\n      this._options = _options || {};\n    }\n    ngOnInit() {\n      this._syncColumnDefName();\n      if (this.headerText === undefined) {\n        this.headerText = this._createDefaultHeaderText();\n      }\n      if (!this.dataAccessor) {\n        this.dataAccessor = this._options.defaultDataAccessor || ((data, name) => data[name]);\n      }\n      if (this._table) {\n        // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n        // since the columnDef will not pick up its content by the time the table finishes checking\n        // its content and initializing the rows.\n        this.columnDef.cell = this.cell;\n        this.columnDef.headerCell = this.headerCell;\n        this._table.addColumnDef(this.columnDef);\n      } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        throw getTableTextColumnMissingParentTableError();\n      }\n    }\n    ngOnDestroy() {\n      if (this._table) {\n        this._table.removeColumnDef(this.columnDef);\n      }\n    }\n    /**\n     * Creates a default header text. Use the options' header text transformation function if one\n     * has been provided. Otherwise simply capitalize the column name.\n     */\n    _createDefaultHeaderText() {\n      const name = this.name;\n      if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableTextColumnMissingNameError();\n      }\n      if (this._options && this._options.defaultHeaderTextTransform) {\n        return this._options.defaultHeaderTextTransform(name);\n      }\n      return name[0].toUpperCase() + name.slice(1);\n    }\n    /** Synchronizes the column definition name with the text column name. */\n    _syncColumnDefName() {\n      if (this.columnDef) {\n        this.columnDef.name = this.name;\n      }\n    }\n    static {\n      this.ɵfac = function CdkTextColumn_Factory(t) {\n        return new (t || CdkTextColumn)(i0.ɵɵdirectiveInject(CdkTable, 8), i0.ɵɵdirectiveInject(TEXT_COLUMN_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkTextColumn,\n        selectors: [[\"cdk-text-column\"]],\n        viewQuery: function CdkTextColumn_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(CdkColumnDef, 7);\n            i0.ɵɵviewQuery(CdkCellDef, 7);\n            i0.ɵɵviewQuery(CdkHeaderCellDef, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnDef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n          }\n        },\n        inputs: {\n          name: \"name\",\n          headerText: \"headerText\",\n          dataAccessor: \"dataAccessor\",\n          justify: \"justify\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 0,\n        consts: [[\"cdkColumnDef\", \"\"], [\"cdk-header-cell\", \"\", 3, \"text-align\", 4, \"cdkHeaderCellDef\"], [\"cdk-cell\", \"\", 3, \"text-align\", 4, \"cdkCellDef\"], [\"cdk-header-cell\", \"\"], [\"cdk-cell\", \"\"]],\n        template: function CdkTextColumn_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementContainerStart(0, 0);\n            i0.ɵɵtemplate(1, CdkTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, CdkTextColumn_td_2_Template, 2, 3, \"td\", 2);\n            i0.ɵɵelementContainerEnd();\n          }\n        },\n        dependencies: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkTextColumn;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst EXPORTED_DECLARATIONS = [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet];\nlet CdkTableModule = /*#__PURE__*/(() => {\n  class CdkTableModule {\n    static {\n      this.ɵfac = function CdkTableModule_Factory(t) {\n        return new (t || CdkTableModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: CdkTableModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [ScrollingModule]\n      });\n    }\n  }\n  return CdkTableModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n * @deprecated Implement the `CanStick` interface instead.\n * @breaking-change 19.0.0\n */\nfunction mixinHasStickyInput(base) {\n  return class extends base {\n    /** Whether sticky positioning should be applied. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(v) {\n      const prevValue = this._sticky;\n      this._sticky = coerceBooleanProperty(v);\n      this._hasStickyChanged = prevValue !== this._sticky;\n    }\n    /** Whether the sticky value has changed since this was last called. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this._hasStickyChanged = false;\n      return hasStickyChanged;\n    }\n    /** Resets the dirty check for cases where the sticky state has been used without checking. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    constructor(...args) {\n      super(...args);\n      this._sticky = false;\n      /** Whether the sticky input has changed since it was last checked. */\n      this._hasStickyChanged = false;\n    }\n  };\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };\n//# sourceMappingURL=table.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}