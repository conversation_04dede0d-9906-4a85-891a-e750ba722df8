{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/shared/services/sweetalert.service\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/tooltip\";\nimport * as i8 from \"@ngx-validate/core\";\nimport * as i9 from \"@angular/common\";\nfunction DecryptDataPacketDialogComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r2.selectedFile.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFile.name, \" \");\n  }\n}\nfunction DecryptDataPacketDialogComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DecryptDataPacketDialogComponent_mat_hint_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 24);\n    i0.ɵɵtext(1, \" File is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DecryptDataPacketDialogComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"mat-icon\", 26);\n    i0.ɵɵtext(2, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nexport class DecryptDataPacketDialogComponent {\n  constructor(dialogRef, fb, sweetAlert) {\n    this.dialogRef = dialogRef;\n    this.fb = fb;\n    this.sweetAlert = sweetAlert;\n    this.selectedFile = null;\n    this.error = null;\n    this.form = this.fb.group({\n      file: [null, Validators.required]\n    });\n  }\n  onFileChange(event) {\n    const input = event.target;\n    const file = input.files && input.files[0];\n    this.error = null;\n    if (file) {\n      if (!file.name.toLowerCase().endsWith('.zip')) {\n        this.error = 'Only .zip files are allowed.';\n        this.resetFile();\n        return;\n      }\n      this.selectedFile = file;\n      this.form.get('file')?.setValue(file);\n    } else {\n      this.resetFile();\n    }\n  }\n  resetFile() {\n    this.selectedFile = null;\n    this.form.patchValue({\n      file: null\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.selectedFile) {\n        _this.form.get('file')?.markAsTouched();\n        return;\n      }\n      if (_this.form.valid) {\n        _this.dialogRef.close({\n          file: _this.selectedFile\n        });\n      }\n    })();\n  }\n  onCancel() {\n    if (this.form.dirty) {\n      this.sweetAlert.fireDialog({\n        action: \"delete\",\n        title: \"Are you sure you want to close?\",\n        text: \"Any unsaved changes may be lost\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.dialogRef.close();\n        }\n      });\n    } else {\n      this.dialogRef.close();\n    }\n  }\n  static {\n    this.ɵfac = function DecryptDataPacketDialogComponent_Factory(t) {\n      return new (t || DecryptDataPacketDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.SweetAlertService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DecryptDataPacketDialogComponent,\n      selectors: [[\"app-decrypt-data-packet-dialog\"]],\n      decls: 32,\n      vars: 6,\n      consts: [[\"noFile\", \"\"], [\"fileInput\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"row\", \"align-items-center\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"file-upload-group\"], [1, \"file-upload-label\"], [1, \"required\"], [1, \"file-upload-row\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"class\", \"file-name\", 3, \"matTooltip\", 4, \"ngIf\", \"ngIfElse\"], [\"accept\", \".zip\", \"type\", \"file\", \"formControlName\", \"file\", \"required\", \"\", 1, \"file-input\", 3, \"change\"], [\"class\", \"file-error\", 4, \"ngIf\"], [1, \"status-section\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", 1, \"ui-button\", 3, \"disabled\"], [1, \"file-name\", 3, \"matTooltip\"], [1, \"file-placeholder\"], [1, \"file-error\"], [1, \"error-message\"], [1, \"error-icon\"]],\n      template: function DecryptDataPacketDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"Decrypt Received Data Packet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function DecryptDataPacketDialogComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵelement(6, \"i\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function DecryptDataPacketDialogComponent_Template_form_ngSubmit_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"div\", 9)(10, \"label\", 10);\n          i0.ɵɵtext(11, \"ZIP File \");\n          i0.ɵɵelementStart(12, \"span\", 11);\n          i0.ɵɵtext(13, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function DecryptDataPacketDialogComponent_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r2 = i0.ɵɵreference(23);\n            return i0.ɵɵresetView(fileInput_r2.click());\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"upload_file\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Choose File \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, DecryptDataPacketDialogComponent_span_19_Template, 2, 2, \"span\", 14)(20, DecryptDataPacketDialogComponent_ng_template_20_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 15, 1);\n          i0.ɵɵlistener(\"change\", function DecryptDataPacketDialogComponent_Template_input_change_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, DecryptDataPacketDialogComponent_mat_hint_24_Template, 2, 0, \"mat-hint\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 17);\n          i0.ɵɵtemplate(26, DecryptDataPacketDialogComponent_div_26_Template, 4, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-dialog-actions\", 19)(28, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function DecryptDataPacketDialogComponent_Template_button_click_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵtext(29, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"button\", 21);\n          i0.ɵɵtext(31, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_5_0;\n          const noFile_r4 = i0.ɵɵreference(21);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFile)(\"ngIfElse\", noFile_r4);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.form.get(\"file\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.form.get(\"file\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.FormGroupDirective, i2.FormControlName, i4.MatHint, i5.MatIcon, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i6.MatButton, i7.MatTooltip, i8.ValidationGroupDirective, i8.ValidationDirective, i9.NgIf],\n      styles: [\".title[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.modal-action-button[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n\\n.file-upload-group[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.file-upload-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n\\n.required[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.file-upload-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.file-input[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  max-width: 250px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  color: #333;\\n  cursor: pointer;\\n}\\n\\n.file-placeholder[_ngcontent-%COMP%] {\\n  color: #888;\\n  font-style: italic;\\n}\\n\\n.file-error[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.status-section[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  background: #fdecea;\\n  border: 1px solid #f44336;\\n  border-radius: 4px;\\n  padding: 8px;\\n  margin-top: 12px;\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.98em;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r2", "selectedFile", "name", "ɵɵadvance", "ɵɵtextInterpolate1", "error", "DecryptDataPacketDialogComponent", "constructor", "dialogRef", "fb", "<PERSON><PERSON><PERSON><PERSON>", "form", "group", "file", "required", "onFileChange", "event", "input", "target", "files", "toLowerCase", "endsWith", "resetFile", "get", "setValue", "patchValue", "onSubmit", "_this", "_asyncToGenerator", "<PERSON><PERSON><PERSON><PERSON>ched", "valid", "close", "onCancel", "dirty", "fireDialog", "action", "title", "text", "type", "confirm", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "FormBuilder", "i3", "SweetAlertService", "selectors", "decls", "vars", "consts", "template", "DecryptDataPacketDialogComponent_Template", "rf", "ctx", "ɵɵlistener", "DecryptDataPacketDialogComponent_Template_button_click_5_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "DecryptDataPacketDialogComponent_Template_form_ngSubmit_7_listener", "DecryptDataPacketDialogComponent_Template_button_click_15_listener", "fileInput_r2", "ɵɵreference", "click", "ɵɵtemplate", "DecryptDataPacketDialogComponent_span_19_Template", "DecryptDataPacketDialogComponent_ng_template_20_Template", "ɵɵtemplateRefExtractor", "DecryptDataPacketDialogComponent_Template_input_change_22_listener", "$event", "DecryptDataPacketDialogComponent_mat_hint_24_Template", "DecryptDataPacketDialogComponent_div_26_Template", "DecryptDataPacketDialogComponent_Template_button_click_28_listener", "noFile_r4", "tmp_5_0", "invalid", "touched"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\decrypt-data-packet-dialog\\decrypt-data-packet-dialog.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\decrypt-data-packet-dialog\\decrypt-data-packet-dialog.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { MatDialogRef } from '@angular/material/dialog';\r\nimport { ToasterService } from '@abp/ng.theme.shared';\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\n\r\n@Component({\r\n    selector: 'app-decrypt-data-packet-dialog',\r\n    templateUrl: './decrypt-data-packet-dialog.component.html',\r\n    styleUrls: ['./decrypt-data-packet-dialog.component.scss']\r\n})\r\nexport class DecryptDataPacketDialogComponent {\r\n    form: FormGroup;\r\n    selectedFile: File | null = null;\r\n    error: string | null = null;\r\n\r\n    constructor(\r\n        public dialogRef: MatDialogRef<DecryptDataPacketDialogComponent>,\r\n        private fb: FormBuilder,\r\n        private sweetAlert: SweetAlertService\r\n    ) {\r\n        this.form = this.fb.group({\r\n            file: [null, Validators.required]\r\n        });\r\n    }\r\n\r\n    onFileChange(event: Event) {\r\n        const input = event.target as HTMLInputElement;\r\n        const file = input.files && input.files[0];\r\n        this.error = null;\r\n        if (file) {\r\n            if (!file.name.toLowerCase().endsWith('.zip')) {\r\n                this.error = 'Only .zip files are allowed.';\r\n                this.resetFile();\r\n                return;\r\n            }\r\n            this.selectedFile = file;\r\n            this.form.get('file')?.setValue(file);\r\n        } else {\r\n            this.resetFile();\r\n        }\r\n    }\r\n\r\n    private resetFile() {\r\n        this.selectedFile = null;\r\n        this.form.patchValue({ file: null });\r\n    }\r\n\r\n    async onSubmit() {\r\n        if (!this.selectedFile) {\r\n            this.form.get('file')?.markAsTouched();\r\n            return;\r\n        }\r\n\r\n        if (this.form.valid) {\r\n            this.dialogRef.close({\r\n                file: this.selectedFile\r\n            });\r\n        }\r\n\r\n    }\r\n\r\n    onCancel() {\r\n        if (this.form.dirty) {\r\n            this.sweetAlert.fireDialog({\r\n                action: \"delete\", title: \"Are you sure you want to close?\",\r\n                text: \"Any unsaved changes may be lost\", type: \"confirm\"\r\n            }, (confirm) => {\r\n                if (confirm) {\r\n                    this.dialogRef.close();\r\n                }\r\n            });\r\n        } else {\r\n            this.dialogRef.close();\r\n        }\r\n    }\r\n}", "<div mat-dialog-title>\r\n    <div class=\"row align-items-center\">\r\n        <div class=\"col-8 title\">Decrypt Received Data Packet</div>\r\n        <div class=\"col-4 text-end modal-action-button\">\r\n            <button type=\"button\" mat-raised-button class=\"ui-button\" (click)=\"onCancel()\">\r\n                <i class=\"fas fa-times\"></i>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</div>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\">\r\n    <mat-dialog-content>\r\n        <div class=\"file-upload-group\">\r\n            <label class=\"file-upload-label\">ZIP File <span class=\"required\">*</span></label>\r\n            <div class=\"file-upload-row\">\r\n                <button mat-stroked-button color=\"primary\" type=\"button\" (click)=\"fileInput.click()\">\r\n                    <mat-icon>upload_file</mat-icon>\r\n                    Choose File\r\n                </button>\r\n                <span class=\"file-name\" *ngIf=\"selectedFile; else noFile\" [matTooltip]=\"selectedFile.name\">\r\n                    {{ selectedFile.name }}\r\n                </span>\r\n                <ng-template #noFile>\r\n                    <span class=\"file-placeholder\">No file chosen</span>\r\n                </ng-template>\r\n            </div>\r\n            <input #fileInput accept=\".zip\" type=\"file\" formControlName=\"file\" (change)=\"onFileChange($event)\" class=\"file-input\" required />\r\n            <mat-hint *ngIf=\"form.get('file')?.invalid && form.get('file')?.touched\" class=\"file-error\">\r\n                File is required.\r\n            </mat-hint>\r\n        </div>\r\n        <div class=\"status-section\">\r\n            <div *ngIf=\"error\" class=\"error-message\">\r\n                <mat-icon class=\"error-icon\">error_outline</mat-icon>\r\n                {{ error }}\r\n            </div>\r\n        </div>\r\n        <mat-dialog-actions align=\"end\">\r\n            <button mat-stroked-button color=\"warn\" type=\"button\" (click)=\"onCancel()\">Cancel</button>\r\n            <button mat-raised-button class=\"ui-button\" type=\"submit\" [disabled]=\"form.invalid\">Submit</button>\r\n        </mat-dialog-actions>\r\n    </mat-dialog-content>\r\n</form>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;ICkBnDC,EAAA,CAAAC,cAAA,eAA2F;IACvFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFmDH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAAgC;IACtFP,EAAA,CAAAQ,SAAA,EACJ;IADIR,EAAA,CAAAS,kBAAA,MAAAJ,MAAA,CAAAC,YAAA,CAAAC,IAAA,MACJ;;;;;IAEIP,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI5DH,EAAA,CAAAC,cAAA,mBAA4F;IACxFD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAIPH,EADJ,CAAAC,cAAA,cAAyC,mBACR;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,MAAAJ,MAAA,CAAAK,KAAA,MACJ;;;ADxBZ,OAAM,MAAOC,gCAAgC;EAKzCC,YACWC,SAAyD,EACxDC,EAAe,EACfC,UAA6B;IAF9B,KAAAF,SAAS,GAATA,SAAS;IACR,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IANtB,KAAAT,YAAY,GAAgB,IAAI;IAChC,KAAAI,KAAK,GAAkB,IAAI;IAOvB,IAAI,CAACM,IAAI,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MACtBC,IAAI,EAAE,CAAC,IAAI,EAAEnB,UAAU,CAACoB,QAAQ;KACnC,CAAC;EACN;EAEAC,YAAYA,CAACC,KAAY;IACrB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,MAAML,IAAI,GAAGI,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACd,KAAK,GAAG,IAAI;IACjB,IAAIQ,IAAI,EAAE;MACN,IAAI,CAACA,IAAI,CAACX,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC3C,IAAI,CAAChB,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAACiB,SAAS,EAAE;QAChB;MACJ;MACA,IAAI,CAACrB,YAAY,GAAGY,IAAI;MACxB,IAAI,CAACF,IAAI,CAACY,GAAG,CAAC,MAAM,CAAC,EAAEC,QAAQ,CAACX,IAAI,CAAC;IACzC,CAAC,MAAM;MACH,IAAI,CAACS,SAAS,EAAE;IACpB;EACJ;EAEQA,SAASA,CAAA;IACb,IAAI,CAACrB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACU,IAAI,CAACc,UAAU,CAAC;MAAEZ,IAAI,EAAE;IAAI,CAAE,CAAC;EACxC;EAEMa,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV,IAAI,CAACD,KAAI,CAAC1B,YAAY,EAAE;QACpB0B,KAAI,CAAChB,IAAI,CAACY,GAAG,CAAC,MAAM,CAAC,EAAEM,aAAa,EAAE;QACtC;MACJ;MAEA,IAAIF,KAAI,CAAChB,IAAI,CAACmB,KAAK,EAAE;QACjBH,KAAI,CAACnB,SAAS,CAACuB,KAAK,CAAC;UACjBlB,IAAI,EAAEc,KAAI,CAAC1B;SACd,CAAC;MACN;IAAC;EAEL;EAEA+B,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACrB,IAAI,CAACsB,KAAK,EAAE;MACjB,IAAI,CAACvB,UAAU,CAACwB,UAAU,CAAC;QACvBC,MAAM,EAAE,QAAQ;QAAEC,KAAK,EAAE,iCAAiC;QAC1DC,IAAI,EAAE,iCAAiC;QAAEC,IAAI,EAAE;OAClD,EAAGC,OAAO,IAAI;QACX,IAAIA,OAAO,EAAE;UACT,IAAI,CAAC/B,SAAS,CAACuB,KAAK,EAAE;QAC1B;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACvB,SAAS,CAACuB,KAAK,EAAE;IAC1B;EACJ;;;uBAhESzB,gCAAgC,EAAAX,EAAA,CAAA6C,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA/C,EAAA,CAAA6C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAjD,EAAA,CAAA6C,iBAAA,CAAAK,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAhCxC,gCAAgC;MAAAyC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCTrC1D,EAFR,CAAAC,cAAA,aAAsB,aACkB,aACP;UAAAD,EAAA,CAAAE,MAAA,mCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEvDH,EADJ,CAAAC,cAAA,aAAgD,gBACmC;UAArBD,EAAA,CAAA4D,UAAA,mBAAAC,kEAAA;YAAA7D,EAAA,CAAA8D,aAAA,CAAAC,GAAA;YAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAASL,GAAA,CAAAtB,QAAA,EAAU;UAAA,EAAC;UAC1ErC,EAAA,CAAAiE,SAAA,WAA4B;UAI5CjE,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAiD;UAAxBD,EAAA,CAAA4D,UAAA,sBAAAM,mEAAA;YAAAlE,EAAA,CAAA8D,aAAA,CAAAC,GAAA;YAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAYL,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAGpC/B,EAFR,CAAAC,cAAA,yBAAoB,aACe,iBACM;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UAE7EH,EADJ,CAAAC,cAAA,eAA6B,kBAC4D;UAA5BD,EAAA,CAAA4D,UAAA,mBAAAO,mEAAA;YAAAnE,EAAA,CAAA8D,aAAA,CAAAC,GAAA;YAAA,MAAAK,YAAA,GAAApE,EAAA,CAAAqE,WAAA;YAAA,OAAArE,EAAA,CAAAgE,WAAA,CAASI,YAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UAChFtE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAHA,CAAAuE,UAAA,KAAAC,iDAAA,mBAA2F,KAAAC,wDAAA,gCAAAzE,EAAA,CAAA0E,sBAAA,CAGtE;UAGzB1E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAAiI;UAA9DD,EAAA,CAAA4D,UAAA,oBAAAe,mEAAAC,MAAA;YAAA5E,EAAA,CAAA8D,aAAA,CAAAC,GAAA;YAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAUL,GAAA,CAAAvC,YAAA,CAAAwD,MAAA,CAAoB;UAAA,EAAC;UAAlG5E,EAAA,CAAAG,YAAA,EAAiI;UACjIH,EAAA,CAAAuE,UAAA,KAAAM,qDAAA,uBAA4F;UAGhG7E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4B;UACxBD,EAAA,CAAAuE,UAAA,KAAAO,gDAAA,kBAAyC;UAI7C9E,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,8BAAgC,kBAC+C;UAArBD,EAAA,CAAA4D,UAAA,mBAAAmB,mEAAA;YAAA/E,EAAA,CAAA8D,aAAA,CAAAC,GAAA;YAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAASL,GAAA,CAAAtB,QAAA,EAAU;UAAA,EAAC;UAACrC,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1FH,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGtGF,EAHsG,CAAAG,YAAA,EAAS,EAClF,EACJ,EAClB;;;;;UAhCDH,EAAA,CAAAQ,SAAA,GAAkB;UAAlBR,EAAA,CAAAI,UAAA,cAAAuD,GAAA,CAAA3C,IAAA,CAAkB;UASiBhB,EAAA,CAAAQ,SAAA,IAAoB;UAAAR,EAApB,CAAAI,UAAA,SAAAuD,GAAA,CAAArD,YAAA,CAAoB,aAAA0E,SAAA,CAAW;UAQjDhF,EAAA,CAAAQ,SAAA,GAA4D;UAA5DR,EAAA,CAAAI,UAAA,WAAA6E,OAAA,GAAAtB,GAAA,CAAA3C,IAAA,CAAAY,GAAA,2BAAAqD,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAtB,GAAA,CAAA3C,IAAA,CAAAY,GAAA,2BAAAqD,OAAA,CAAAE,OAAA,EAA4D;UAKjEnF,EAAA,CAAAQ,SAAA,GAAW;UAAXR,EAAA,CAAAI,UAAA,SAAAuD,GAAA,CAAAjD,KAAA,CAAW;UAOyCV,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAI,UAAA,aAAAuD,GAAA,CAAA3C,IAAA,CAAAkE,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}