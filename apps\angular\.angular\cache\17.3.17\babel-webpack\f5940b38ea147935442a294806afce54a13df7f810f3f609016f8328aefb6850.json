{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Properties Plugin, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n/**\r\n* PropertiesPlugin.ts\r\n* @copyright Microsoft 2018\r\n*/\nimport { __extendsFn as __extends } from \"@microsoft/applicationinsights-shims\";\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\nimport { BreezeChannelIdentifier, PageView, PropertiesPluginIdentifier, createDistributedTraceContextFromTrace, utlSetStoragePrefix } from \"@microsoft/applicationinsights-common\";\nimport { BaseTelemetryPlugin, _InternalLogMessage, _logInternalMessage, createProcessTelemetryContext, getNavigator, getSetValue, isNullOrUndefined, objF<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"@microsoft/applicationinsights-core-js\";\nimport { TelemetryContext } from \"./TelemetryContext\";\nimport { _DYN_ACCOUNT_ID, _DYN_APPLY_APPLICATION_CO1, _DYN_APPLY_DEVICE_CONTEXT, _DYN_APPLY_INTERNAL_CONTE5, _DYN_APPLY_LOCATION_CONTE4, _DYN_APPLY_OPERATING_SYST3, _DYN_APPLY_OPERATION_CONT2, _DYN_APPLY_SESSION_CONTEX0, _DYN_APPLY_USER_CONTEXT, _DYN_COOKIE_DOMAIN, _DYN_GET_NEW_ID, _DYN_GET_SESSION_ID, _DYN_GET_TRACE_CTX, _DYN_ID_LENGTH, _DYN_IS_NEW_USER, _DYN_IS_USER_COOKIE_SET, _DYN_NAME_PREFIX, _DYN_SDK_EXTENSION, _DYN_SESSION_COOKIE_POSTF6, _DYN_SESSION_MANAGER, _DYN_TELEMETRY_TRACE, _DYN_UPDATE, _DYN_USER_COOKIE_POSTFIX } from \"./__DynamicConstants\";\nvar PropertiesPlugin = /** @class */function (_super) {\n  __extends(PropertiesPlugin, _super);\n  function PropertiesPlugin() {\n    var _this = _super.call(this) || this;\n    _this.priority = 110;\n    _this.identifier = PropertiesPluginIdentifier;\n    var _extensionConfig;\n    var _distributedTraceCtx;\n    var _previousTraceCtx;\n    dynamicProto(PropertiesPlugin, _this, function (_self, _base) {\n      _initDefaults();\n      _self.initialize = function (config, core, extensions, pluginChain) {\n        _base.initialize(config, core, extensions, pluginChain);\n        _populateDefaults(config);\n      };\n      /**\r\n       * Add Part A fields to the event\r\n       * @param event The event that needs to be processed\r\n       */\n      _self.processTelemetry = function (event, itemCtx) {\n        if (isNullOrUndefined(event)) {\n          // TODO(barustum): throw an internal event once we have support for internal logging\n        } else {\n          itemCtx = _self._getTelCtx(itemCtx);\n          // If the envelope is PageView, reset the internal message count so that we can send internal telemetry for the new page.\n          if (event.name === PageView.envelopeType) {\n            itemCtx.diagLog().resetInternalMessageCount();\n          }\n          var theContext = _self.context || {};\n          if (theContext.session) {\n            // If customer did not provide custom session id update the session manager\n            if (typeof _self.context.session.id !== \"string\" && theContext[_DYN_SESSION_MANAGER /* @min:%2esessionManager */]) {\n              theContext[_DYN_SESSION_MANAGER /* @min:%2esessionManager */][_DYN_UPDATE /* @min:%2eupdate */]();\n            }\n          }\n          var userCtx = theContext.user;\n          if (userCtx && !userCtx[_DYN_IS_USER_COOKIE_SET /* @min:%2eisUserCookieSet */]) {\n            userCtx[_DYN_UPDATE /* @min:%2eupdate */](theContext.user.id);\n          }\n          _processTelemetryInternal(event, itemCtx);\n          if (userCtx && userCtx[_DYN_IS_NEW_USER /* @min:%2eisNewUser */]) {\n            userCtx[_DYN_IS_NEW_USER /* @min:%2eisNewUser */] = false;\n            var message = new _InternalLogMessage(72 /* _eInternalMessageId.SendBrowserInfoOnUserInit */, (getNavigator() || {}).userAgent || \"\");\n            _logInternalMessage(itemCtx.diagLog(), 1 /* eLoggingSeverity.CRITICAL */, message);\n          }\n          _self.processNext(event, itemCtx);\n        }\n      };\n      _self._doTeardown = function (unloadCtx, unloadState) {\n        var core = (unloadCtx || {}).core();\n        if (core && core[_DYN_GET_TRACE_CTX /* @min:%2egetTraceCtx */]) {\n          var traceCtx = core[_DYN_GET_TRACE_CTX /* @min:%2egetTraceCtx */](false);\n          if (traceCtx === _distributedTraceCtx) {\n            core.setTraceCtx(_previousTraceCtx);\n          }\n        }\n        _initDefaults();\n      };\n      function _initDefaults() {\n        _extensionConfig = null;\n        _distributedTraceCtx = null;\n        _previousTraceCtx = null;\n      }\n      function _populateDefaults(config) {\n        var identifier = _self.identifier;\n        var core = _self.core;\n        var ctx = createProcessTelemetryContext(null, config, core);\n        var defaultConfig = PropertiesPlugin.getDefaultConfig();\n        _extensionConfig = _extensionConfig || {};\n        objForEachKey(defaultConfig, function (field, value) {\n          _extensionConfig[field] = function () {\n            return ctx.getConfig(identifier, field, value());\n          };\n        });\n        if (config.storagePrefix) {\n          utlSetStoragePrefix(config.storagePrefix);\n        }\n        _previousTraceCtx = core[_DYN_GET_TRACE_CTX /* @min:%2egetTraceCtx */](false);\n        _self.context = new TelemetryContext(core, _extensionConfig, _previousTraceCtx);\n        _distributedTraceCtx = createDistributedTraceContextFromTrace(_self.context[_DYN_TELEMETRY_TRACE /* @min:%2etelemetryTrace */], _previousTraceCtx);\n        core.setTraceCtx(_distributedTraceCtx);\n        _self.context.appId = function () {\n          var breezeChannel = core.getPlugin(BreezeChannelIdentifier);\n          return breezeChannel ? breezeChannel.plugin[\"_appId\"] : null;\n        };\n        // Test hook to allow accessing the internal values -- explicitly not defined as an available property on the class\n        _self[\"_extConfig\"] = _extensionConfig;\n      }\n      function _processTelemetryInternal(evt, itemCtx) {\n        // Set Part A fields\n        getSetValue(evt, \"tags\", []);\n        getSetValue(evt, \"ext\", {});\n        var ctx = _self.context;\n        ctx[_DYN_APPLY_SESSION_CONTEX0 /* @min:%2eapplySessionContext */](evt, itemCtx);\n        ctx[_DYN_APPLY_APPLICATION_CO1 /* @min:%2eapplyApplicationContext */](evt, itemCtx);\n        ctx[_DYN_APPLY_DEVICE_CONTEXT /* @min:%2eapplyDeviceContext */](evt, itemCtx);\n        ctx[_DYN_APPLY_OPERATION_CONT2 /* @min:%2eapplyOperationContext */](evt, itemCtx);\n        ctx[_DYN_APPLY_USER_CONTEXT /* @min:%2eapplyUserContext */](evt, itemCtx);\n        ctx[_DYN_APPLY_OPERATING_SYST3 /* @min:%2eapplyOperatingSystemContxt */](evt, itemCtx);\n        ctx.applyWebContext(evt, itemCtx);\n        ctx[_DYN_APPLY_LOCATION_CONTE4 /* @min:%2eapplyLocationContext */](evt, itemCtx); // legacy tags\n        ctx[_DYN_APPLY_INTERNAL_CONTE5 /* @min:%2eapplyInternalContext */](evt, itemCtx); // legacy tags\n        ctx.cleanUp(evt, itemCtx);\n      }\n    });\n    return _this;\n  }\n  PropertiesPlugin.getDefaultConfig = function () {\n    var _a;\n    var defaultValue;\n    var nullValue = null;\n    var defaultConfig = (_a = {\n      instrumentationKey: function () {\n        return defaultValue;\n      }\n    }, _a[_DYN_ACCOUNT_ID /* @min:accountId */] = function () {\n      return nullValue;\n    }, _a.sessionRenewalMs = function () {\n      return 30 * 60 * 1000;\n    }, _a.samplingPercentage = function () {\n      return 100;\n    }, _a.sessionExpirationMs = function () {\n      return 24 * 60 * 60 * 1000;\n    }, _a[_DYN_COOKIE_DOMAIN /* @min:cookieDomain */] = function () {\n      return nullValue;\n    }, _a[_DYN_SDK_EXTENSION /* @min:sdkExtension */] = function () {\n      return nullValue;\n    }, _a.isBrowserLinkTrackingEnabled = function () {\n      return false;\n    }, _a.appId = function () {\n      return nullValue;\n    }, _a[_DYN_GET_SESSION_ID /* @min:getSessionId */] = function () {\n      return nullValue;\n    }, _a[_DYN_NAME_PREFIX /* @min:namePrefix */] = function () {\n      return defaultValue;\n    }, _a[_DYN_SESSION_COOKIE_POSTF6 /* @min:sessionCookiePostfix */] = function () {\n      return defaultValue;\n    }, _a[_DYN_USER_COOKIE_POSTFIX /* @min:userCookiePostfix */] = function () {\n      return defaultValue;\n    }, _a[_DYN_ID_LENGTH /* @min:idLength */] = function () {\n      return 22;\n    }, _a[_DYN_GET_NEW_ID /* @min:getNewId */] = function () {\n      return nullValue;\n    }, _a);\n    return defaultConfig;\n  };\n  // Removed Stub for PropertiesPlugin.prototype.initialize.\n  // Removed Stub for PropertiesPlugin.prototype.processTelemetry.\n  return PropertiesPlugin;\n}(BaseTelemetryPlugin);\nexport default PropertiesPlugin;", "map": {"version": 3, "names": ["__extendsFn", "__extends", "dynamicProto", "BreezeChannelIdentifier", "<PERSON><PERSON><PERSON><PERSON>", "PropertiesPluginIdentifier", "createDistributedTraceContextFromTrace", "utlSetStoragePrefix", "BaseTelemetryPlugin", "_InternalLogMessage", "_logInternalMessage", "createProcessTelemetryContext", "getNavigator", "getSetValue", "isNullOrUndefined", "objFor<PERSON><PERSON><PERSON>", "TelemetryContext", "_DYN_ACCOUNT_ID", "_DYN_APPLY_APPLICATION_CO1", "_DYN_APPLY_DEVICE_CONTEXT", "_DYN_APPLY_INTERNAL_CONTE5", "_DYN_APPLY_LOCATION_CONTE4", "_DYN_APPLY_OPERATING_SYST3", "_DYN_APPLY_OPERATION_CONT2", "_DYN_APPLY_SESSION_CONTEX0", "_DYN_APPLY_USER_CONTEXT", "_DYN_COOKIE_DOMAIN", "_DYN_GET_NEW_ID", "_DYN_GET_SESSION_ID", "_DYN_GET_TRACE_CTX", "_DYN_ID_LENGTH", "_DYN_IS_NEW_USER", "_DYN_IS_USER_COOKIE_SET", "_DYN_NAME_PREFIX", "_DYN_SDK_EXTENSION", "_DYN_SESSION_COOKIE_POSTF6", "_DYN_SESSION_MANAGER", "_DYN_TELEMETRY_TRACE", "_DYN_UPDATE", "_DYN_USER_COOKIE_POSTFIX", "PropertiesPlugin", "_super", "_this", "call", "priority", "identifier", "_extensionConfig", "_distributedTraceCtx", "_previousTraceCtx", "_self", "_base", "_initDefaults", "initialize", "config", "core", "extensions", "pluginChain", "_populateDefaults", "processTelemetry", "event", "itemCtx", "_getTelCtx", "name", "envelopeType", "diagLog", "resetInternalMessageCount", "theContext", "context", "session", "id", "userCtx", "user", "_processTelemetryInternal", "message", "userAgent", "processNext", "_doTeardown", "unloadCtx", "unloadState", "traceCtx", "setTraceCtx", "ctx", "defaultConfig", "getDefaultConfig", "field", "value", "getConfig", "storagePrefix", "appId", "breezeChannel", "getPlugin", "plugin", "evt", "applyWebContext", "cleanUp", "_a", "defaultValue", "nullValue", "<PERSON><PERSON><PERSON>", "sessionR<PERSON>walMs", "samplingPercentage", "sessionExpirationMs", "isBrowserLinkTrackingEnabled"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-properties-js/dist-esm/PropertiesPlugin.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Properties Plugin, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n/**\r\n* PropertiesPlugin.ts\r\n* @copyright Microsoft 2018\r\n*/\r\nimport { __extendsFn as __extends } from \"@microsoft/applicationinsights-shims\";\r\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\r\nimport { BreezeChannelIdentifier, PageView, PropertiesPluginIdentifier, createDistributedTraceContextFromTrace, utlSetStoragePrefix } from \"@microsoft/applicationinsights-common\";\r\nimport { BaseTelemetryPlugin, _InternalLogMessage, _logInternalMessage, createProcessTelemetryContext, getNavigator, getSetValue, isNullOrUndefined, objF<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"@microsoft/applicationinsights-core-js\";\r\nimport { TelemetryContext } from \"./TelemetryContext\";\r\nimport { _DYN_ACCOUNT_ID, _DYN_APPLY_APPLICATION_CO1, _DYN_APPLY_DEVICE_CONTEXT, _DYN_APPLY_INTERNAL_CONTE5, _DYN_APPLY_LOCATION_CONTE4, _DYN_APPLY_OPERATING_SYST3, _DYN_APPLY_OPERATION_CONT2, _DYN_APPLY_SESSION_CONTEX0, _DYN_APPLY_USER_CONTEXT, _DYN_COOKIE_DOMAIN, _DYN_GET_NEW_ID, _DYN_GET_SESSION_ID, _DYN_GET_TRACE_CTX, _DYN_ID_LENGTH, _DYN_IS_NEW_USER, _DYN_IS_USER_COOKIE_SET, _DYN_NAME_PREFIX, _DYN_SDK_EXTENSION, _DYN_SESSION_COOKIE_POSTF6, _DYN_SESSION_MANAGER, _DYN_TELEMETRY_TRACE, _DYN_UPDATE, _DYN_USER_COOKIE_POSTFIX } from \"./__DynamicConstants\";\r\nvar PropertiesPlugin = /** @class */ (function (_super) {\r\n    __extends(PropertiesPlugin, _super);\r\n    function PropertiesPlugin() {\r\n        var _this = _super.call(this) || this;\r\n        _this.priority = 110;\r\n        _this.identifier = PropertiesPluginIdentifier;\r\n        var _extensionConfig;\r\n        var _distributedTraceCtx;\r\n        var _previousTraceCtx;\r\n        dynamicProto(PropertiesPlugin, _this, function (_self, _base) {\r\n            _initDefaults();\r\n            _self.initialize = function (config, core, extensions, pluginChain) {\r\n                _base.initialize(config, core, extensions, pluginChain);\r\n                _populateDefaults(config);\r\n            };\r\n            /**\r\n             * Add Part A fields to the event\r\n             * @param event The event that needs to be processed\r\n             */\r\n            _self.processTelemetry = function (event, itemCtx) {\r\n                if (isNullOrUndefined(event)) {\r\n                    // TODO(barustum): throw an internal event once we have support for internal logging\r\n                }\r\n                else {\r\n                    itemCtx = _self._getTelCtx(itemCtx);\r\n                    // If the envelope is PageView, reset the internal message count so that we can send internal telemetry for the new page.\r\n                    if (event.name === PageView.envelopeType) {\r\n                        itemCtx.diagLog().resetInternalMessageCount();\r\n                    }\r\n                    var theContext = (_self.context || {});\r\n                    if (theContext.session) {\r\n                        // If customer did not provide custom session id update the session manager\r\n                        if (typeof _self.context.session.id !== \"string\" && theContext[_DYN_SESSION_MANAGER /* @min:%2esessionManager */]) {\r\n                            theContext[_DYN_SESSION_MANAGER /* @min:%2esessionManager */][_DYN_UPDATE /* @min:%2eupdate */]();\r\n                        }\r\n                    }\r\n                    var userCtx = theContext.user;\r\n                    if (userCtx && !userCtx[_DYN_IS_USER_COOKIE_SET /* @min:%2eisUserCookieSet */]) {\r\n                        userCtx[_DYN_UPDATE /* @min:%2eupdate */](theContext.user.id);\r\n                    }\r\n                    _processTelemetryInternal(event, itemCtx);\r\n                    if (userCtx && userCtx[_DYN_IS_NEW_USER /* @min:%2eisNewUser */]) {\r\n                        userCtx[_DYN_IS_NEW_USER /* @min:%2eisNewUser */] = false;\r\n                        var message = new _InternalLogMessage(72 /* _eInternalMessageId.SendBrowserInfoOnUserInit */, ((getNavigator() || {}).userAgent || \"\"));\r\n                        _logInternalMessage(itemCtx.diagLog(), 1 /* eLoggingSeverity.CRITICAL */, message);\r\n                    }\r\n                    _self.processNext(event, itemCtx);\r\n                }\r\n            };\r\n            _self._doTeardown = function (unloadCtx, unloadState) {\r\n                var core = (unloadCtx || {}).core();\r\n                if (core && core[_DYN_GET_TRACE_CTX /* @min:%2egetTraceCtx */]) {\r\n                    var traceCtx = core[_DYN_GET_TRACE_CTX /* @min:%2egetTraceCtx */](false);\r\n                    if (traceCtx === _distributedTraceCtx) {\r\n                        core.setTraceCtx(_previousTraceCtx);\r\n                    }\r\n                }\r\n                _initDefaults();\r\n            };\r\n            function _initDefaults() {\r\n                _extensionConfig = null;\r\n                _distributedTraceCtx = null;\r\n                _previousTraceCtx = null;\r\n            }\r\n            function _populateDefaults(config) {\r\n                var identifier = _self.identifier;\r\n                var core = _self.core;\r\n                var ctx = createProcessTelemetryContext(null, config, core);\r\n                var defaultConfig = PropertiesPlugin.getDefaultConfig();\r\n                _extensionConfig = _extensionConfig || {};\r\n                objForEachKey(defaultConfig, function (field, value) {\r\n                    _extensionConfig[field] = function () { return ctx.getConfig(identifier, field, value()); };\r\n                });\r\n                if (config.storagePrefix) {\r\n                    utlSetStoragePrefix(config.storagePrefix);\r\n                }\r\n                _previousTraceCtx = core[_DYN_GET_TRACE_CTX /* @min:%2egetTraceCtx */](false);\r\n                _self.context = new TelemetryContext(core, _extensionConfig, _previousTraceCtx);\r\n                _distributedTraceCtx = createDistributedTraceContextFromTrace(_self.context[_DYN_TELEMETRY_TRACE /* @min:%2etelemetryTrace */], _previousTraceCtx);\r\n                core.setTraceCtx(_distributedTraceCtx);\r\n                _self.context.appId = function () {\r\n                    var breezeChannel = core.getPlugin(BreezeChannelIdentifier);\r\n                    return breezeChannel ? breezeChannel.plugin[\"_appId\"] : null;\r\n                };\r\n                // Test hook to allow accessing the internal values -- explicitly not defined as an available property on the class\r\n                _self[\"_extConfig\"] = _extensionConfig;\r\n            }\r\n            function _processTelemetryInternal(evt, itemCtx) {\r\n                // Set Part A fields\r\n                getSetValue(evt, \"tags\", []);\r\n                getSetValue(evt, \"ext\", {});\r\n                var ctx = _self.context;\r\n                ctx[_DYN_APPLY_SESSION_CONTEX0 /* @min:%2eapplySessionContext */](evt, itemCtx);\r\n                ctx[_DYN_APPLY_APPLICATION_CO1 /* @min:%2eapplyApplicationContext */](evt, itemCtx);\r\n                ctx[_DYN_APPLY_DEVICE_CONTEXT /* @min:%2eapplyDeviceContext */](evt, itemCtx);\r\n                ctx[_DYN_APPLY_OPERATION_CONT2 /* @min:%2eapplyOperationContext */](evt, itemCtx);\r\n                ctx[_DYN_APPLY_USER_CONTEXT /* @min:%2eapplyUserContext */](evt, itemCtx);\r\n                ctx[_DYN_APPLY_OPERATING_SYST3 /* @min:%2eapplyOperatingSystemContxt */](evt, itemCtx);\r\n                ctx.applyWebContext(evt, itemCtx);\r\n                ctx[_DYN_APPLY_LOCATION_CONTE4 /* @min:%2eapplyLocationContext */](evt, itemCtx); // legacy tags\r\n                ctx[_DYN_APPLY_INTERNAL_CONTE5 /* @min:%2eapplyInternalContext */](evt, itemCtx); // legacy tags\r\n                ctx.cleanUp(evt, itemCtx);\r\n            }\r\n        });\r\n        return _this;\r\n    }\r\n    PropertiesPlugin.getDefaultConfig = function () {\r\n        var _a;\r\n        var defaultValue;\r\n        var nullValue = null;\r\n        var defaultConfig = (_a = {\r\n                instrumentationKey: function () { return defaultValue; }\r\n            },\r\n            _a[_DYN_ACCOUNT_ID /* @min:accountId */] = function () { return nullValue; },\r\n            _a.sessionRenewalMs = function () { return 30 * 60 * 1000; },\r\n            _a.samplingPercentage = function () { return 100; },\r\n            _a.sessionExpirationMs = function () { return 24 * 60 * 60 * 1000; },\r\n            _a[_DYN_COOKIE_DOMAIN /* @min:cookieDomain */] = function () { return nullValue; },\r\n            _a[_DYN_SDK_EXTENSION /* @min:sdkExtension */] = function () { return nullValue; },\r\n            _a.isBrowserLinkTrackingEnabled = function () { return false; },\r\n            _a.appId = function () { return nullValue; },\r\n            _a[_DYN_GET_SESSION_ID /* @min:getSessionId */] = function () { return nullValue; },\r\n            _a[_DYN_NAME_PREFIX /* @min:namePrefix */] = function () { return defaultValue; },\r\n            _a[_DYN_SESSION_COOKIE_POSTF6 /* @min:sessionCookiePostfix */] = function () { return defaultValue; },\r\n            _a[_DYN_USER_COOKIE_POSTFIX /* @min:userCookiePostfix */] = function () { return defaultValue; },\r\n            _a[_DYN_ID_LENGTH /* @min:idLength */] = function () { return 22; },\r\n            _a[_DYN_GET_NEW_ID /* @min:getNewId */] = function () { return nullValue; },\r\n            _a);\r\n        return defaultConfig;\r\n    };\r\n// Removed Stub for PropertiesPlugin.prototype.initialize.\r\n// Removed Stub for PropertiesPlugin.prototype.processTelemetry.\r\n    return PropertiesPlugin;\r\n}(BaseTelemetryPlugin));\r\nexport default PropertiesPlugin;\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,IAAIC,SAAS,QAAQ,sCAAsC;AAC/E,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,uBAAuB,EAAEC,QAAQ,EAAEC,0BAA0B,EAAEC,sCAAsC,EAAEC,mBAAmB,QAAQ,uCAAuC;AAClL,SAASC,mBAAmB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,6BAA6B,EAAEC,YAAY,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,wCAAwC;AAClN,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,EAAEC,0BAA0B,EAAEC,yBAAyB,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,wBAAwB,QAAQ,sBAAsB;AAChjB,IAAIC,gBAAgB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACpDxC,SAAS,CAACuC,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,QAAQ,GAAG,GAAG;IACpBF,KAAK,CAACG,UAAU,GAAGxC,0BAA0B;IAC7C,IAAIyC,gBAAgB;IACpB,IAAIC,oBAAoB;IACxB,IAAIC,iBAAiB;IACrB9C,YAAY,CAACsC,gBAAgB,EAAEE,KAAK,EAAE,UAAUO,KAAK,EAAEC,KAAK,EAAE;MAC1DC,aAAa,CAAC,CAAC;MACfF,KAAK,CAACG,UAAU,GAAG,UAAUC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAChEN,KAAK,CAACE,UAAU,CAACC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,CAAC;QACvDC,iBAAiB,CAACJ,MAAM,CAAC;MAC7B,CAAC;MACD;AACZ;AACA;AACA;MACYJ,KAAK,CAACS,gBAAgB,GAAG,UAAUC,KAAK,EAAEC,OAAO,EAAE;QAC/C,IAAI9C,iBAAiB,CAAC6C,KAAK,CAAC,EAAE;UAC1B;QAAA,CACH,MACI;UACDC,OAAO,GAAGX,KAAK,CAACY,UAAU,CAACD,OAAO,CAAC;UACnC;UACA,IAAID,KAAK,CAACG,IAAI,KAAK1D,QAAQ,CAAC2D,YAAY,EAAE;YACtCH,OAAO,CAACI,OAAO,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC;UACjD;UACA,IAAIC,UAAU,GAAIjB,KAAK,CAACkB,OAAO,IAAI,CAAC,CAAE;UACtC,IAAID,UAAU,CAACE,OAAO,EAAE;YACpB;YACA,IAAI,OAAOnB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,EAAE,KAAK,QAAQ,IAAIH,UAAU,CAAC9B,oBAAoB,CAAC,6BAA6B,EAAE;cAC/G8B,UAAU,CAAC9B,oBAAoB,CAAC,6BAA6B,CAACE,WAAW,CAAC,qBAAqB,CAAC,CAAC;YACrG;UACJ;UACA,IAAIgC,OAAO,GAAGJ,UAAU,CAACK,IAAI;UAC7B,IAAID,OAAO,IAAI,CAACA,OAAO,CAACtC,uBAAuB,CAAC,8BAA8B,EAAE;YAC5EsC,OAAO,CAAChC,WAAW,CAAC,qBAAqB,CAAC4B,UAAU,CAACK,IAAI,CAACF,EAAE,CAAC;UACjE;UACAG,yBAAyB,CAACb,KAAK,EAAEC,OAAO,CAAC;UACzC,IAAIU,OAAO,IAAIA,OAAO,CAACvC,gBAAgB,CAAC,wBAAwB,EAAE;YAC9DuC,OAAO,CAACvC,gBAAgB,CAAC,wBAAwB,GAAG,KAAK;YACzD,IAAI0C,OAAO,GAAG,IAAIhE,mBAAmB,CAAC,EAAE,CAAC,qDAAsD,CAACG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE8D,SAAS,IAAI,EAAG,CAAC;YACvIhE,mBAAmB,CAACkD,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,iCAAiCS,OAAO,CAAC;UACtF;UACAxB,KAAK,CAAC0B,WAAW,CAAChB,KAAK,EAAEC,OAAO,CAAC;QACrC;MACJ,CAAC;MACDX,KAAK,CAAC2B,WAAW,GAAG,UAAUC,SAAS,EAAEC,WAAW,EAAE;QAClD,IAAIxB,IAAI,GAAG,CAACuB,SAAS,IAAI,CAAC,CAAC,EAAEvB,IAAI,CAAC,CAAC;QACnC,IAAIA,IAAI,IAAIA,IAAI,CAACzB,kBAAkB,CAAC,0BAA0B,EAAE;UAC5D,IAAIkD,QAAQ,GAAGzB,IAAI,CAACzB,kBAAkB,CAAC,0BAA0B,CAAC,KAAK,CAAC;UACxE,IAAIkD,QAAQ,KAAKhC,oBAAoB,EAAE;YACnCO,IAAI,CAAC0B,WAAW,CAAChC,iBAAiB,CAAC;UACvC;QACJ;QACAG,aAAa,CAAC,CAAC;MACnB,CAAC;MACD,SAASA,aAAaA,CAAA,EAAG;QACrBL,gBAAgB,GAAG,IAAI;QACvBC,oBAAoB,GAAG,IAAI;QAC3BC,iBAAiB,GAAG,IAAI;MAC5B;MACA,SAASS,iBAAiBA,CAACJ,MAAM,EAAE;QAC/B,IAAIR,UAAU,GAAGI,KAAK,CAACJ,UAAU;QACjC,IAAIS,IAAI,GAAGL,KAAK,CAACK,IAAI;QACrB,IAAI2B,GAAG,GAAGtE,6BAA6B,CAAC,IAAI,EAAE0C,MAAM,EAAEC,IAAI,CAAC;QAC3D,IAAI4B,aAAa,GAAG1C,gBAAgB,CAAC2C,gBAAgB,CAAC,CAAC;QACvDrC,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,CAAC;QACzC/B,aAAa,CAACmE,aAAa,EAAE,UAAUE,KAAK,EAAEC,KAAK,EAAE;UACjDvC,gBAAgB,CAACsC,KAAK,CAAC,GAAG,YAAY;YAAE,OAAOH,GAAG,CAACK,SAAS,CAACzC,UAAU,EAAEuC,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC;UAAE,CAAC;QAC/F,CAAC,CAAC;QACF,IAAIhC,MAAM,CAACkC,aAAa,EAAE;UACtBhF,mBAAmB,CAAC8C,MAAM,CAACkC,aAAa,CAAC;QAC7C;QACAvC,iBAAiB,GAAGM,IAAI,CAACzB,kBAAkB,CAAC,0BAA0B,CAAC,KAAK,CAAC;QAC7EoB,KAAK,CAACkB,OAAO,GAAG,IAAInD,gBAAgB,CAACsC,IAAI,EAAER,gBAAgB,EAAEE,iBAAiB,CAAC;QAC/ED,oBAAoB,GAAGzC,sCAAsC,CAAC2C,KAAK,CAACkB,OAAO,CAAC9B,oBAAoB,CAAC,6BAA6B,EAAEW,iBAAiB,CAAC;QAClJM,IAAI,CAAC0B,WAAW,CAACjC,oBAAoB,CAAC;QACtCE,KAAK,CAACkB,OAAO,CAACqB,KAAK,GAAG,YAAY;UAC9B,IAAIC,aAAa,GAAGnC,IAAI,CAACoC,SAAS,CAACvF,uBAAuB,CAAC;UAC3D,OAAOsF,aAAa,GAAGA,aAAa,CAACE,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI;QAChE,CAAC;QACD;QACA1C,KAAK,CAAC,YAAY,CAAC,GAAGH,gBAAgB;MAC1C;MACA,SAAS0B,yBAAyBA,CAACoB,GAAG,EAAEhC,OAAO,EAAE;QAC7C;QACA/C,WAAW,CAAC+E,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC;QAC5B/E,WAAW,CAAC+E,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAIX,GAAG,GAAGhC,KAAK,CAACkB,OAAO;QACvBc,GAAG,CAACzD,0BAA0B,CAAC,kCAAkC,CAACoE,GAAG,EAAEhC,OAAO,CAAC;QAC/EqB,GAAG,CAAC/D,0BAA0B,CAAC,sCAAsC,CAAC0E,GAAG,EAAEhC,OAAO,CAAC;QACnFqB,GAAG,CAAC9D,yBAAyB,CAAC,iCAAiC,CAACyE,GAAG,EAAEhC,OAAO,CAAC;QAC7EqB,GAAG,CAAC1D,0BAA0B,CAAC,oCAAoC,CAACqE,GAAG,EAAEhC,OAAO,CAAC;QACjFqB,GAAG,CAACxD,uBAAuB,CAAC,+BAA+B,CAACmE,GAAG,EAAEhC,OAAO,CAAC;QACzEqB,GAAG,CAAC3D,0BAA0B,CAAC,yCAAyC,CAACsE,GAAG,EAAEhC,OAAO,CAAC;QACtFqB,GAAG,CAACY,eAAe,CAACD,GAAG,EAAEhC,OAAO,CAAC;QACjCqB,GAAG,CAAC5D,0BAA0B,CAAC,mCAAmC,CAACuE,GAAG,EAAEhC,OAAO,CAAC,CAAC,CAAC;QAClFqB,GAAG,CAAC7D,0BAA0B,CAAC,mCAAmC,CAACwE,GAAG,EAAEhC,OAAO,CAAC,CAAC,CAAC;QAClFqB,GAAG,CAACa,OAAO,CAACF,GAAG,EAAEhC,OAAO,CAAC;MAC7B;IACJ,CAAC,CAAC;IACF,OAAOlB,KAAK;EAChB;EACAF,gBAAgB,CAAC2C,gBAAgB,GAAG,YAAY;IAC5C,IAAIY,EAAE;IACN,IAAIC,YAAY;IAChB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIf,aAAa,IAAIa,EAAE,GAAG;MAClBG,kBAAkB,EAAE,SAAAA,CAAA,EAAY;QAAE,OAAOF,YAAY;MAAE;IAC3D,CAAC,EACDD,EAAE,CAAC9E,eAAe,CAAC,qBAAqB,GAAG,YAAY;MAAE,OAAOgF,SAAS;IAAE,CAAC,EAC5EF,EAAE,CAACI,gBAAgB,GAAG,YAAY;MAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE,CAAC,EAC5DJ,EAAE,CAACK,kBAAkB,GAAG,YAAY;MAAE,OAAO,GAAG;IAAE,CAAC,EACnDL,EAAE,CAACM,mBAAmB,GAAG,YAAY;MAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE,CAAC,EACpEN,EAAE,CAACrE,kBAAkB,CAAC,wBAAwB,GAAG,YAAY;MAAE,OAAOuE,SAAS;IAAE,CAAC,EAClFF,EAAE,CAAC7D,kBAAkB,CAAC,wBAAwB,GAAG,YAAY;MAAE,OAAO+D,SAAS;IAAE,CAAC,EAClFF,EAAE,CAACO,4BAA4B,GAAG,YAAY;MAAE,OAAO,KAAK;IAAE,CAAC,EAC/DP,EAAE,CAACP,KAAK,GAAG,YAAY;MAAE,OAAOS,SAAS;IAAE,CAAC,EAC5CF,EAAE,CAACnE,mBAAmB,CAAC,wBAAwB,GAAG,YAAY;MAAE,OAAOqE,SAAS;IAAE,CAAC,EACnFF,EAAE,CAAC9D,gBAAgB,CAAC,sBAAsB,GAAG,YAAY;MAAE,OAAO+D,YAAY;IAAE,CAAC,EACjFD,EAAE,CAAC5D,0BAA0B,CAAC,gCAAgC,GAAG,YAAY;MAAE,OAAO6D,YAAY;IAAE,CAAC,EACrGD,EAAE,CAACxD,wBAAwB,CAAC,6BAA6B,GAAG,YAAY;MAAE,OAAOyD,YAAY;IAAE,CAAC,EAChGD,EAAE,CAACjE,cAAc,CAAC,oBAAoB,GAAG,YAAY;MAAE,OAAO,EAAE;IAAE,CAAC,EACnEiE,EAAE,CAACpE,eAAe,CAAC,oBAAoB,GAAG,YAAY;MAAE,OAAOsE,SAAS;IAAE,CAAC,EAC3EF,EAAE,CAAC;IACP,OAAOb,aAAa;EACxB,CAAC;EACL;EACA;EACI,OAAO1C,gBAAgB;AAC3B,CAAC,CAAChC,mBAAmB,CAAE;AACvB,eAAegC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}