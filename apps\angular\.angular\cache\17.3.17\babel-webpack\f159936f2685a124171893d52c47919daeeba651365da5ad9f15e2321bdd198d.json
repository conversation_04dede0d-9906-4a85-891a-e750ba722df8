{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nfunction ErrorsComponent_div_1_h1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ERROR \", ctx_r0.routeParams[\"status\"], \"\");\n  }\n}\nfunction ErrorsComponent_div_1_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.routeParams[\"message\"]);\n  }\n}\nfunction ErrorsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ErrorsComponent_div_1_h1_1_Template, 2, 1, \"h1\", 1)(2, ErrorsComponent_div_1_p_2_Template, 2, 1, \"p\", 1);\n    i0.ɵɵelementStart(3, \"button\", 2);\n    i0.ɵɵtext(4, \"HOME\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.routeParams == null ? null : ctx_r0.routeParams.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.routeParams == null ? null : ctx_r0.routeParams.message);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/home\");\n  }\n}\nfunction ErrorsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h1\");\n    i0.ɵɵtext(2, \"Apologies. There was an unknown error.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 2);\n    i0.ɵɵtext(4, \"HOME\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", \"/home\");\n  }\n}\nexport let ErrorsComponent = /*#__PURE__*/(() => {\n  class ErrorsComponent {\n    constructor(activatedRoute) {\n      this.activatedRoute = activatedRoute;\n    }\n    ngOnInit() {\n      this.routeParams = this.activatedRoute.snapshot.queryParams;\n      this.data = this.activatedRoute.snapshot.data;\n    }\n    static {\n      this.ɵfac = function ErrorsComponent_Factory(t) {\n        return new (t || ErrorsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ErrorsComponent,\n        selectors: [[\"app-errors\"]],\n        decls: 3,\n        vars: 2,\n        consts: [[1, \"error-container\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"routerLink\"]],\n        template: function ErrorsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, ErrorsComponent_div_1_Template, 5, 3, \"div\", 1)(2, ErrorsComponent_div_2_Template, 5, 1, \"div\", 1);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.routeParams.message);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.routeParams && !ctx.data);\n          }\n        },\n        dependencies: [i2.MatButton, i1.RouterLink],\n        styles: [\".error-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:space-evenly;height:100vh;text-align:center}\"]\n      });\n    }\n  }\n  return ErrorsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}