{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'llai na eiliad',\n    other: 'llai na {{count}} eiliad'\n  },\n  xSeconds: {\n    one: '1 eiliad',\n    other: '{{count}} eiliad'\n  },\n  halfAMinute: 'hanner munud',\n  lessThanXMinutes: {\n    one: 'llai na munud',\n    two: 'llai na 2 funud',\n    other: 'llai na {{count}} munud'\n  },\n  xMinutes: {\n    one: '1 munud',\n    two: '2 funud',\n    other: '{{count}} munud'\n  },\n  aboutXHours: {\n    one: 'tua 1 awr',\n    other: 'tua {{count}} awr'\n  },\n  xHours: {\n    one: '1 awr',\n    other: '{{count}} awr'\n  },\n  xDays: {\n    one: '1 diwrnod',\n    two: '2 ddiwrnod',\n    other: '{{count}} diwrnod'\n  },\n  aboutXWeeks: {\n    one: 'tua 1 wythnos',\n    two: 'tua pythefnos',\n    other: 'tua {{count}} wythnos'\n  },\n  xWeeks: {\n    one: '1 wythnos',\n    two: 'pythefnos',\n    other: '{{count}} wythnos'\n  },\n  aboutXMonths: {\n    one: 'tua 1 mis',\n    two: 'tua 2 fis',\n    other: 'tua {{count}} mis'\n  },\n  xMonths: {\n    one: '1 mis',\n    two: '2 fis',\n    other: '{{count}} mis'\n  },\n  aboutXYears: {\n    one: 'tua 1 flwyddyn',\n    two: 'tua 2 flynedd',\n    other: 'tua {{count}} mlynedd'\n  },\n  xYears: {\n    one: '1 flwyddyn',\n    two: '2 flynedd',\n    other: '{{count}} mlynedd'\n  },\n  overXYears: {\n    one: 'dros 1 flwyddyn',\n    two: 'dros 2 flynedd',\n    other: 'dros {{count}} mlynedd'\n  },\n  almostXYears: {\n    one: 'bron 1 flwyddyn',\n    two: 'bron 2 flynedd',\n    other: 'bron {{count}} mlynedd'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'mewn ' + result;\n    } else {\n      return result + ' yn ôl';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "two", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns/esm/locale/cy/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'llai na eiliad',\n    other: 'llai na {{count}} eiliad'\n  },\n  xSeconds: {\n    one: '1 eiliad',\n    other: '{{count}} eiliad'\n  },\n  halfAMinute: 'hanner munud',\n  lessThanXMinutes: {\n    one: 'llai na munud',\n    two: 'llai na 2 funud',\n    other: 'llai na {{count}} munud'\n  },\n  xMinutes: {\n    one: '1 munud',\n    two: '2 funud',\n    other: '{{count}} munud'\n  },\n  aboutXHours: {\n    one: 'tua 1 awr',\n    other: 'tua {{count}} awr'\n  },\n  xHours: {\n    one: '1 awr',\n    other: '{{count}} awr'\n  },\n  xDays: {\n    one: '1 diwrnod',\n    two: '2 ddiwrnod',\n    other: '{{count}} diwrnod'\n  },\n  aboutXWeeks: {\n    one: 'tua 1 wythnos',\n    two: 'tua pythefnos',\n    other: 'tua {{count}} wythnos'\n  },\n  xWeeks: {\n    one: '1 wythnos',\n    two: 'pythefnos',\n    other: '{{count}} wythnos'\n  },\n  aboutXMonths: {\n    one: 'tua 1 mis',\n    two: 'tua 2 fis',\n    other: 'tua {{count}} mis'\n  },\n  xMonths: {\n    one: '1 mis',\n    two: '2 fis',\n    other: '{{count}} mis'\n  },\n  aboutXYears: {\n    one: 'tua 1 flwyddyn',\n    two: 'tua 2 flynedd',\n    other: 'tua {{count}} mlynedd'\n  },\n  xYears: {\n    one: '1 flwyddyn',\n    two: '2 flynedd',\n    other: '{{count}} mlynedd'\n  },\n  overXYears: {\n    one: 'dros 1 flwyddyn',\n    two: 'dros 2 flynedd',\n    other: 'dros {{count}} mlynedd'\n  },\n  almostXYears: {\n    one: 'bron 1 flwyddyn',\n    two: 'bron 2 flynedd',\n    other: 'bron {{count}} mlynedd'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'mewn ' + result;\n    } else {\n      return result + ' yn ôl';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,eAAe;IACpBK,GAAG,EAAE,iBAAiB;IACtBJ,KAAK,EAAE;EACT,CAAC;EACDK,QAAQ,EAAE;IACRN,GAAG,EAAE,SAAS;IACdK,GAAG,EAAE,SAAS;IACdJ,KAAK,EAAE;EACT,CAAC;EACDM,WAAW,EAAE;IACXP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDO,MAAM,EAAE;IACNR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,KAAK,EAAE;IACLT,GAAG,EAAE,WAAW;IAChBK,GAAG,EAAE,YAAY;IACjBJ,KAAK,EAAE;EACT,CAAC;EACDS,WAAW,EAAE;IACXV,GAAG,EAAE,eAAe;IACpBK,GAAG,EAAE,eAAe;IACpBJ,KAAK,EAAE;EACT,CAAC;EACDU,MAAM,EAAE;IACNX,GAAG,EAAE,WAAW;IAChBK,GAAG,EAAE,WAAW;IAChBJ,KAAK,EAAE;EACT,CAAC;EACDW,YAAY,EAAE;IACZZ,GAAG,EAAE,WAAW;IAChBK,GAAG,EAAE,WAAW;IAChBJ,KAAK,EAAE;EACT,CAAC;EACDY,OAAO,EAAE;IACPb,GAAG,EAAE,OAAO;IACZK,GAAG,EAAE,OAAO;IACZJ,KAAK,EAAE;EACT,CAAC;EACDa,WAAW,EAAE;IACXd,GAAG,EAAE,gBAAgB;IACrBK,GAAG,EAAE,eAAe;IACpBJ,KAAK,EAAE;EACT,CAAC;EACDc,MAAM,EAAE;IACNf,GAAG,EAAE,YAAY;IACjBK,GAAG,EAAE,WAAW;IAChBJ,KAAK,EAAE;EACT,CAAC;EACDe,UAAU,EAAE;IACVhB,GAAG,EAAE,iBAAiB;IACtBK,GAAG,EAAE,gBAAgB;IACrBJ,KAAK,EAAE;EACT,CAAC;EACDgB,YAAY,EAAE;IACZjB,GAAG,EAAE,iBAAiB;IACtBK,GAAG,EAAE,gBAAgB;IACrBJ,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,KAAK,CAAC,IAAI,CAAC,CAACG,UAAU,CAAClB,GAAG,EAAE;IAC1CiB,MAAM,GAAGC,UAAU,CAAClB,GAAG;EACzB,CAAC,MAAM;IACLiB,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGL,MAAM;IACzB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}