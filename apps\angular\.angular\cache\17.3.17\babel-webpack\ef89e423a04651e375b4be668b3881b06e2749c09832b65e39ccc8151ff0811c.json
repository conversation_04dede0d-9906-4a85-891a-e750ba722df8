{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Core, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n\"use strict\";\n\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\nimport { _DYN_CREATE_NEW, _DYN_DIAG_LOG, _DYN_GET_NEXT, _DYN_GET_PROCESS_TEL_CONT0, _DYN_INITIALIZE, _DYN_IS_ASYNC, _DYN_IS_INITIALIZED, _DYN_PROCESS_NEXT, _DYN_PUSH, _DYN_SET_NEXT_PLUGIN, _DYN_TEARDOWN, _DYN_UPDATE, _DYN__DO_TEARDOWN } from \"../__DynamicConstants\";\nimport { arrForEach, isArray, isFunction, isNullOrUndefined, proxyFunctionAs, setValue } from \"./HelperFuncs\";\nimport { STR_CORE, STR_EXTENSION_CONFIG, STR_PROCESS_TELEMETRY } from \"./InternalConstants\";\nimport { createProcessTelemetryContext, createProcessTelemetryUnloadContext, createProcessTelemetryUpdateContext } from \"./ProcessTelemetryContext\";\nimport { createUnloadHandlerContainer } from \"./UnloadHandlerContainer\";\nvar strGetPlugin = \"getPlugin\";\n/**\r\n * BaseTelemetryPlugin provides a basic implementation of the ITelemetryPlugin interface so that plugins\r\n * can avoid implementation the same set of boiler plate code as well as provide a base\r\n * implementation so that new default implementations can be added without breaking all plugins.\r\n */\nvar BaseTelemetryPlugin = /** @class */function () {\n  function BaseTelemetryPlugin() {\n    var _self = this; // Setting _self here as it's used outside of the dynamicProto as well\n    // NOTE!: DON'T set default values here, instead set them in the _initDefaults() function as it is also called during teardown()\n    var _isinitialized;\n    var _rootCtx; // Used as the root context, holding the current config and initialized core\n    var _nextPlugin; // Used for backward compatibility where plugins don't call the main pipeline\n    var _unloadHandlerContainer;\n    var _hooks;\n    _initDefaults();\n    dynamicProto(BaseTelemetryPlugin, _self, function (_self) {\n      _self[_DYN_INITIALIZE /* @min:%2einitialize */] = function (config, core, extensions, pluginChain) {\n        _setDefaults(config, core, pluginChain);\n        _isinitialized = true;\n      };\n      _self[_DYN_TEARDOWN /* @min:%2eteardown */] = function (unloadCtx, unloadState) {\n        var _a;\n        // If this plugin has already been torn down (not operational) or is not initialized (core is not set)\n        // or the core being used for unload was not the same core used for initialization.\n        var core = _self[STR_CORE /* @min:%2ecore */];\n        if (!core || unloadCtx && core !== unloadCtx[STR_CORE /* @min:%2ecore */]()) {\n          // Do Nothing as either the plugin is not initialized or was not initialized by the current core\n          return;\n        }\n        var result;\n        var unloadDone = false;\n        var theUnloadCtx = unloadCtx || createProcessTelemetryUnloadContext(null, core, _nextPlugin && _nextPlugin[strGetPlugin] ? _nextPlugin[strGetPlugin]() : _nextPlugin);\n        var theUnloadState = unloadState || (_a = {\n          reason: 0 /* TelemetryUnloadReason.ManualTeardown */\n        }, _a[_DYN_IS_ASYNC /* @min:isAsync */] = false, _a);\n        function _unloadCallback() {\n          if (!unloadDone) {\n            unloadDone = true;\n            _unloadHandlerContainer.run(theUnloadCtx, unloadState);\n            var oldHooks = _hooks;\n            _hooks = [];\n            // Remove all instrumentation hooks\n            arrForEach(oldHooks, function (fn) {\n              fn.rm();\n            });\n            if (result === true) {\n              theUnloadCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](theUnloadState);\n            }\n            _initDefaults();\n          }\n        }\n        if (!_self[_DYN__DO_TEARDOWN /* @min:%2e_doTeardown */] || _self[_DYN__DO_TEARDOWN /* @min:%2e_doTeardown */](theUnloadCtx, theUnloadState, _unloadCallback) !== true) {\n          _unloadCallback();\n        } else {\n          // Tell the caller that we will be calling processNext()\n          result = true;\n        }\n        return result;\n      };\n      _self[_DYN_UPDATE /* @min:%2eupdate */] = function (updateCtx, updateState) {\n        // If this plugin has already been torn down (not operational) or is not initialized (core is not set)\n        // or the core being used for unload was not the same core used for initialization.\n        var core = _self[STR_CORE /* @min:%2ecore */];\n        if (!core || updateCtx && core !== updateCtx[STR_CORE /* @min:%2ecore */]()) {\n          // Do Nothing\n          return;\n        }\n        var result;\n        var updateDone = false;\n        var theUpdateCtx = updateCtx || createProcessTelemetryUpdateContext(null, core, _nextPlugin && _nextPlugin[strGetPlugin] ? _nextPlugin[strGetPlugin]() : _nextPlugin);\n        var theUpdateState = updateState || {\n          reason: 0 /* TelemetryUpdateReason.Unknown */\n        };\n        function _updateCallback() {\n          if (!updateDone) {\n            updateDone = true;\n            _setDefaults(theUpdateCtx.getCfg(), theUpdateCtx.core(), theUpdateCtx[_DYN_GET_NEXT /* @min:%2egetNext */]());\n          }\n        }\n        if (!_self._doUpdate || _self._doUpdate(theUpdateCtx, theUpdateState, _updateCallback) !== true) {\n          _updateCallback();\n        } else {\n          result = true;\n        }\n        return result;\n      };\n      _self._addHook = function (hooks) {\n        if (hooks) {\n          if (isArray(hooks)) {\n            _hooks = _hooks.concat(hooks);\n          } else {\n            _hooks[_DYN_PUSH /* @min:%2epush */](hooks);\n          }\n        }\n      };\n      proxyFunctionAs(_self, \"_addUnloadCb\", function () {\n        return _unloadHandlerContainer;\n      }, \"add\");\n    });\n    // These are added after the dynamicProto so that are not moved to the prototype\n    _self[_DYN_DIAG_LOG /* @min:%2ediagLog */] = function (itemCtx) {\n      return _getTelCtx(itemCtx)[_DYN_DIAG_LOG /* @min:%2ediagLog */]();\n    };\n    _self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */] = function () {\n      return _isinitialized;\n    };\n    _self.setInitialized = function (isInitialized) {\n      _isinitialized = isInitialized;\n    };\n    // _self.getNextPlugin = () => DO NOT IMPLEMENT\n    // Sub-classes of this base class *should* not be relying on this value and instead\n    // should use processNext() function. If you require access to the plugin use the\n    // IProcessTelemetryContext.getNext().getPlugin() while in the pipeline, Note getNext() may return null.\n    _self[_DYN_SET_NEXT_PLUGIN /* @min:%2esetNextPlugin */] = function (next) {\n      _nextPlugin = next;\n    };\n    _self[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */] = function (env, itemCtx) {\n      if (itemCtx) {\n        // Normal core execution sequence\n        itemCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](env);\n      } else if (_nextPlugin && isFunction(_nextPlugin[STR_PROCESS_TELEMETRY /* @min:%2eprocessTelemetry */])) {\n        // Looks like backward compatibility or out of band processing. And as it looks\n        // like a ITelemetryPlugin or ITelemetryPluginChain, just call processTelemetry\n        _nextPlugin[STR_PROCESS_TELEMETRY /* @min:%2eprocessTelemetry */](env, null);\n      }\n    };\n    _self._getTelCtx = _getTelCtx;\n    function _getTelCtx(currentCtx) {\n      if (currentCtx === void 0) {\n        currentCtx = null;\n      }\n      var itemCtx = currentCtx;\n      if (!itemCtx) {\n        var rootCtx = _rootCtx || createProcessTelemetryContext(null, {}, _self[STR_CORE /* @min:%2ecore */]);\n        // tslint:disable-next-line: prefer-conditional-expression\n        if (_nextPlugin && _nextPlugin[strGetPlugin]) {\n          // Looks like a chain object\n          itemCtx = rootCtx[_DYN_CREATE_NEW /* @min:%2ecreateNew */](null, _nextPlugin[strGetPlugin]);\n        } else {\n          itemCtx = rootCtx[_DYN_CREATE_NEW /* @min:%2ecreateNew */](null, _nextPlugin);\n        }\n      }\n      return itemCtx;\n    }\n    function _setDefaults(config, core, pluginChain) {\n      if (config) {\n        // Make sure the extensionConfig exists\n        setValue(config, STR_EXTENSION_CONFIG, [], null, isNullOrUndefined);\n      }\n      if (!pluginChain && core) {\n        // Get the first plugin from the core\n        pluginChain = core[_DYN_GET_PROCESS_TEL_CONT0 /* @min:%2egetProcessTelContext */]()[_DYN_GET_NEXT /* @min:%2egetNext */]();\n      }\n      var nextPlugin = _nextPlugin;\n      if (_nextPlugin && _nextPlugin[strGetPlugin]) {\n        // If it looks like a proxy/chain then get the plugin\n        nextPlugin = _nextPlugin[strGetPlugin]();\n      }\n      // Support legacy plugins where core was defined as a property\n      _self[STR_CORE /* @min:%2ecore */] = core;\n      _rootCtx = createProcessTelemetryContext(pluginChain, config, core, nextPlugin);\n    }\n    function _initDefaults() {\n      _isinitialized = false;\n      _self[STR_CORE /* @min:%2ecore */] = null;\n      _rootCtx = null;\n      _nextPlugin = null;\n      _hooks = [];\n      _unloadHandlerContainer = createUnloadHandlerContainer();\n    }\n  }\n  // Removed Stub for BaseTelemetryPlugin.prototype.initialize.\n  // Removed Stub for BaseTelemetryPlugin.prototype.teardown.\n  // Removed Stub for BaseTelemetryPlugin.prototype.update.\n  // Removed Stub for BaseTelemetryPlugin.prototype._addUnloadCb.\n  // Removed Stub for BaseTelemetryPlugin.prototype._addHook.\n  // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n  // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n  // this will be removed when ES3 support is dropped.\n  BaseTelemetryPlugin.__ieDyn = 1;\n  return BaseTelemetryPlugin;\n}();\nexport { BaseTelemetryPlugin };", "map": {"version": 3, "names": ["dynamicProto", "_DYN_CREATE_NEW", "_DYN_DIAG_LOG", "_DYN_GET_NEXT", "_DYN_GET_PROCESS_TEL_CONT0", "_DYN_INITIALIZE", "_DYN_IS_ASYNC", "_DYN_IS_INITIALIZED", "_DYN_PROCESS_NEXT", "_DYN_PUSH", "_DYN_SET_NEXT_PLUGIN", "_DYN_TEARDOWN", "_DYN_UPDATE", "_DYN__DO_TEARDOWN", "arrFor<PERSON>ach", "isArray", "isFunction", "isNullOrUndefined", "proxyFunctionAs", "setValue", "STR_CORE", "STR_EXTENSION_CONFIG", "STR_PROCESS_TELEMETRY", "createProcessTelemetryContext", "createProcessTelemetryUnloadContext", "createProcessTelemetryUpdateContext", "createUnloadHandlerContainer", "strGetPlugin", "BaseTelemetryPlugin", "_self", "_isinitialized", "_rootCtx", "_nextPlugin", "_unloadHandlerContainer", "_hooks", "_initDefaults", "config", "core", "extensions", "pluginChain", "_setDefaults", "unloadCtx", "unloadState", "_a", "result", "unloadDone", "theUnloadCtx", "theUnloadState", "reason", "_unloadCallback", "run", "oldHooks", "fn", "rm", "updateCtx", "updateState", "updateDone", "theUpdateCtx", "theUpdateState", "_updateCallback", "getCfg", "_doUpdate", "_addHook", "hooks", "concat", "itemCtx", "_getTelCtx", "setInitialized", "isInitialized", "next", "env", "currentCtx", "rootCtx", "nextPlugin", "__ie<PERSON>yn"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/BaseTelemetryPlugin.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Core, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\n\"use strict\";\r\nimport dynamicProto from \"@microsoft/dynamicproto-js\";\r\nimport { _DYN_CREATE_NEW, _DYN_DIAG_LOG, _DYN_GET_NEXT, _DYN_GET_PROCESS_TEL_CONT0, _DYN_INITIALIZE, _DYN_IS_ASYNC, _DYN_IS_INITIALIZED, _DYN_PROCESS_NEXT, _DYN_PUSH, _DYN_SET_NEXT_PLUGIN, _DYN_TEARDOWN, _DYN_UPDATE, _DYN__DO_TEARDOWN } from \"../__DynamicConstants\";\r\nimport { arrForEach, isArray, isFunction, isNullOrUndefined, proxyFunctionAs, setValue } from \"./HelperFuncs\";\r\nimport { STR_CORE, STR_EXTENSION_CONFIG, STR_PROCESS_TELEMETRY } from \"./InternalConstants\";\r\nimport { createProcessTelemetryContext, createProcessTelemetryUnloadContext, createProcessTelemetryUpdateContext } from \"./ProcessTelemetryContext\";\r\nimport { createUnloadHandlerContainer } from \"./UnloadHandlerContainer\";\r\nvar strGetPlugin = \"getPlugin\";\r\n/**\r\n * BaseTelemetryPlugin provides a basic implementation of the ITelemetryPlugin interface so that plugins\r\n * can avoid implementation the same set of boiler plate code as well as provide a base\r\n * implementation so that new default implementations can be added without breaking all plugins.\r\n */\r\nvar BaseTelemetryPlugin = /** @class */ (function () {\r\n    function BaseTelemetryPlugin() {\r\n        var _self = this; // Setting _self here as it's used outside of the dynamicProto as well\r\n        // NOTE!: DON'T set default values here, instead set them in the _initDefaults() function as it is also called during teardown()\r\n        var _isinitialized;\r\n        var _rootCtx; // Used as the root context, holding the current config and initialized core\r\n        var _nextPlugin; // Used for backward compatibility where plugins don't call the main pipeline\r\n        var _unloadHandlerContainer;\r\n        var _hooks;\r\n        _initDefaults();\r\n        dynamicProto(BaseTelemetryPlugin, _self, function (_self) {\r\n            _self[_DYN_INITIALIZE /* @min:%2einitialize */] = function (config, core, extensions, pluginChain) {\r\n                _setDefaults(config, core, pluginChain);\r\n                _isinitialized = true;\r\n            };\r\n            _self[_DYN_TEARDOWN /* @min:%2eteardown */] = function (unloadCtx, unloadState) {\r\n                var _a;\r\n                // If this plugin has already been torn down (not operational) or is not initialized (core is not set)\r\n                // or the core being used for unload was not the same core used for initialization.\r\n                var core = _self[STR_CORE /* @min:%2ecore */];\r\n                if (!core || (unloadCtx && core !== unloadCtx[STR_CORE /* @min:%2ecore */]())) {\r\n                    // Do Nothing as either the plugin is not initialized or was not initialized by the current core\r\n                    return;\r\n                }\r\n                var result;\r\n                var unloadDone = false;\r\n                var theUnloadCtx = unloadCtx || createProcessTelemetryUnloadContext(null, core, _nextPlugin && _nextPlugin[strGetPlugin] ? _nextPlugin[strGetPlugin]() : _nextPlugin);\r\n                var theUnloadState = unloadState || (_a = {\r\n                        reason: 0 /* TelemetryUnloadReason.ManualTeardown */\r\n                    },\r\n                    _a[_DYN_IS_ASYNC /* @min:isAsync */] = false,\r\n                    _a);\r\n                function _unloadCallback() {\r\n                    if (!unloadDone) {\r\n                        unloadDone = true;\r\n                        _unloadHandlerContainer.run(theUnloadCtx, unloadState);\r\n                        var oldHooks = _hooks;\r\n                        _hooks = [];\r\n                        // Remove all instrumentation hooks\r\n                        arrForEach(oldHooks, function (fn) {\r\n                            fn.rm();\r\n                        });\r\n                        if (result === true) {\r\n                            theUnloadCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](theUnloadState);\r\n                        }\r\n                        _initDefaults();\r\n                    }\r\n                }\r\n                if (!_self[_DYN__DO_TEARDOWN /* @min:%2e_doTeardown */] || _self[_DYN__DO_TEARDOWN /* @min:%2e_doTeardown */](theUnloadCtx, theUnloadState, _unloadCallback) !== true) {\r\n                    _unloadCallback();\r\n                }\r\n                else {\r\n                    // Tell the caller that we will be calling processNext()\r\n                    result = true;\r\n                }\r\n                return result;\r\n            };\r\n            _self[_DYN_UPDATE /* @min:%2eupdate */] = function (updateCtx, updateState) {\r\n                // If this plugin has already been torn down (not operational) or is not initialized (core is not set)\r\n                // or the core being used for unload was not the same core used for initialization.\r\n                var core = _self[STR_CORE /* @min:%2ecore */];\r\n                if (!core || (updateCtx && core !== updateCtx[STR_CORE /* @min:%2ecore */]())) {\r\n                    // Do Nothing\r\n                    return;\r\n                }\r\n                var result;\r\n                var updateDone = false;\r\n                var theUpdateCtx = updateCtx || createProcessTelemetryUpdateContext(null, core, _nextPlugin && _nextPlugin[strGetPlugin] ? _nextPlugin[strGetPlugin]() : _nextPlugin);\r\n                var theUpdateState = updateState || {\r\n                    reason: 0 /* TelemetryUpdateReason.Unknown */\r\n                };\r\n                function _updateCallback() {\r\n                    if (!updateDone) {\r\n                        updateDone = true;\r\n                        _setDefaults(theUpdateCtx.getCfg(), theUpdateCtx.core(), theUpdateCtx[_DYN_GET_NEXT /* @min:%2egetNext */]());\r\n                    }\r\n                }\r\n                if (!_self._doUpdate || _self._doUpdate(theUpdateCtx, theUpdateState, _updateCallback) !== true) {\r\n                    _updateCallback();\r\n                }\r\n                else {\r\n                    result = true;\r\n                }\r\n                return result;\r\n            };\r\n            _self._addHook = function (hooks) {\r\n                if (hooks) {\r\n                    if (isArray(hooks)) {\r\n                        _hooks = _hooks.concat(hooks);\r\n                    }\r\n                    else {\r\n                        _hooks[_DYN_PUSH /* @min:%2epush */](hooks);\r\n                    }\r\n                }\r\n            };\r\n            proxyFunctionAs(_self, \"_addUnloadCb\", function () { return _unloadHandlerContainer; }, \"add\");\r\n        });\r\n        // These are added after the dynamicProto so that are not moved to the prototype\r\n        _self[_DYN_DIAG_LOG /* @min:%2ediagLog */] = function (itemCtx) {\r\n            return _getTelCtx(itemCtx)[_DYN_DIAG_LOG /* @min:%2ediagLog */]();\r\n        };\r\n        _self[_DYN_IS_INITIALIZED /* @min:%2eisInitialized */] = function () {\r\n            return _isinitialized;\r\n        };\r\n        _self.setInitialized = function (isInitialized) {\r\n            _isinitialized = isInitialized;\r\n        };\r\n        // _self.getNextPlugin = () => DO NOT IMPLEMENT\r\n        // Sub-classes of this base class *should* not be relying on this value and instead\r\n        // should use processNext() function. If you require access to the plugin use the\r\n        // IProcessTelemetryContext.getNext().getPlugin() while in the pipeline, Note getNext() may return null.\r\n        _self[_DYN_SET_NEXT_PLUGIN /* @min:%2esetNextPlugin */] = function (next) {\r\n            _nextPlugin = next;\r\n        };\r\n        _self[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */] = function (env, itemCtx) {\r\n            if (itemCtx) {\r\n                // Normal core execution sequence\r\n                itemCtx[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](env);\r\n            }\r\n            else if (_nextPlugin && isFunction(_nextPlugin[STR_PROCESS_TELEMETRY /* @min:%2eprocessTelemetry */])) {\r\n                // Looks like backward compatibility or out of band processing. And as it looks\r\n                // like a ITelemetryPlugin or ITelemetryPluginChain, just call processTelemetry\r\n                _nextPlugin[STR_PROCESS_TELEMETRY /* @min:%2eprocessTelemetry */](env, null);\r\n            }\r\n        };\r\n        _self._getTelCtx = _getTelCtx;\r\n        function _getTelCtx(currentCtx) {\r\n            if (currentCtx === void 0) { currentCtx = null; }\r\n            var itemCtx = currentCtx;\r\n            if (!itemCtx) {\r\n                var rootCtx = _rootCtx || createProcessTelemetryContext(null, {}, _self[STR_CORE /* @min:%2ecore */]);\r\n                // tslint:disable-next-line: prefer-conditional-expression\r\n                if (_nextPlugin && _nextPlugin[strGetPlugin]) {\r\n                    // Looks like a chain object\r\n                    itemCtx = rootCtx[_DYN_CREATE_NEW /* @min:%2ecreateNew */](null, _nextPlugin[strGetPlugin]);\r\n                }\r\n                else {\r\n                    itemCtx = rootCtx[_DYN_CREATE_NEW /* @min:%2ecreateNew */](null, _nextPlugin);\r\n                }\r\n            }\r\n            return itemCtx;\r\n        }\r\n        function _setDefaults(config, core, pluginChain) {\r\n            if (config) {\r\n                // Make sure the extensionConfig exists\r\n                setValue(config, STR_EXTENSION_CONFIG, [], null, isNullOrUndefined);\r\n            }\r\n            if (!pluginChain && core) {\r\n                // Get the first plugin from the core\r\n                pluginChain = core[_DYN_GET_PROCESS_TEL_CONT0 /* @min:%2egetProcessTelContext */]()[_DYN_GET_NEXT /* @min:%2egetNext */]();\r\n            }\r\n            var nextPlugin = _nextPlugin;\r\n            if (_nextPlugin && _nextPlugin[strGetPlugin]) {\r\n                // If it looks like a proxy/chain then get the plugin\r\n                nextPlugin = _nextPlugin[strGetPlugin]();\r\n            }\r\n            // Support legacy plugins where core was defined as a property\r\n            _self[STR_CORE /* @min:%2ecore */] = core;\r\n            _rootCtx = createProcessTelemetryContext(pluginChain, config, core, nextPlugin);\r\n        }\r\n        function _initDefaults() {\r\n            _isinitialized = false;\r\n            _self[STR_CORE /* @min:%2ecore */] = null;\r\n            _rootCtx = null;\r\n            _nextPlugin = null;\r\n            _hooks = [];\r\n            _unloadHandlerContainer = createUnloadHandlerContainer();\r\n        }\r\n    }\r\n// Removed Stub for BaseTelemetryPlugin.prototype.initialize.\r\n// Removed Stub for BaseTelemetryPlugin.prototype.teardown.\r\n// Removed Stub for BaseTelemetryPlugin.prototype.update.\r\n// Removed Stub for BaseTelemetryPlugin.prototype._addUnloadCb.\r\n// Removed Stub for BaseTelemetryPlugin.prototype._addHook.\r\n    // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any\n    // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.\n    // this will be removed when ES3 support is dropped.\n    BaseTelemetryPlugin.__ieDyn=1;\n\n    return BaseTelemetryPlugin;\r\n}());\r\nexport { BaseTelemetryPlugin };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,YAAY;;AACZ,OAAOA,YAAY,MAAM,4BAA4B;AACrD,SAASC,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,0BAA0B,EAAEC,eAAe,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,uBAAuB;AACzQ,SAASC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7G,SAASC,QAAQ,EAAEC,oBAAoB,EAAEC,qBAAqB,QAAQ,qBAAqB;AAC3F,SAASC,6BAA6B,EAAEC,mCAAmC,EAAEC,mCAAmC,QAAQ,2BAA2B;AACnJ,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,IAAIC,YAAY,GAAG,WAAW;AAC9B;AACA;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,aAAe,YAAY;EACjD,SAASA,mBAAmBA,CAAA,EAAG;IAC3B,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;IAClB;IACA,IAAIC,cAAc;IAClB,IAAIC,QAAQ,CAAC,CAAC;IACd,IAAIC,WAAW,CAAC,CAAC;IACjB,IAAIC,uBAAuB;IAC3B,IAAIC,MAAM;IACVC,aAAa,CAAC,CAAC;IACfnC,YAAY,CAAC4B,mBAAmB,EAAEC,KAAK,EAAE,UAAUA,KAAK,EAAE;MACtDA,KAAK,CAACxB,eAAe,CAAC,yBAAyB,GAAG,UAAU+B,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAC/FC,YAAY,CAACJ,MAAM,EAAEC,IAAI,EAAEE,WAAW,CAAC;QACvCT,cAAc,GAAG,IAAI;MACzB,CAAC;MACDD,KAAK,CAAClB,aAAa,CAAC,uBAAuB,GAAG,UAAU8B,SAAS,EAAEC,WAAW,EAAE;QAC5E,IAAIC,EAAE;QACN;QACA;QACA,IAAIN,IAAI,GAAGR,KAAK,CAACT,QAAQ,CAAC,mBAAmB;QAC7C,IAAI,CAACiB,IAAI,IAAKI,SAAS,IAAIJ,IAAI,KAAKI,SAAS,CAACrB,QAAQ,CAAC,mBAAmB,CAAC,CAAE,EAAE;UAC3E;UACA;QACJ;QACA,IAAIwB,MAAM;QACV,IAAIC,UAAU,GAAG,KAAK;QACtB,IAAIC,YAAY,GAAGL,SAAS,IAAIjB,mCAAmC,CAAC,IAAI,EAAEa,IAAI,EAAEL,WAAW,IAAIA,WAAW,CAACL,YAAY,CAAC,GAAGK,WAAW,CAACL,YAAY,CAAC,CAAC,CAAC,GAAGK,WAAW,CAAC;QACrK,IAAIe,cAAc,GAAGL,WAAW,KAAKC,EAAE,GAAG;UAClCK,MAAM,EAAE,CAAC,CAAC;QACd,CAAC,EACDL,EAAE,CAACrC,aAAa,CAAC,mBAAmB,GAAG,KAAK,EAC5CqC,EAAE,CAAC;QACP,SAASM,eAAeA,CAAA,EAAG;UACvB,IAAI,CAACJ,UAAU,EAAE;YACbA,UAAU,GAAG,IAAI;YACjBZ,uBAAuB,CAACiB,GAAG,CAACJ,YAAY,EAAEJ,WAAW,CAAC;YACtD,IAAIS,QAAQ,GAAGjB,MAAM;YACrBA,MAAM,GAAG,EAAE;YACX;YACApB,UAAU,CAACqC,QAAQ,EAAE,UAAUC,EAAE,EAAE;cAC/BA,EAAE,CAACC,EAAE,CAAC,CAAC;YACX,CAAC,CAAC;YACF,IAAIT,MAAM,KAAK,IAAI,EAAE;cACjBE,YAAY,CAACtC,iBAAiB,CAAC,0BAA0B,CAACuC,cAAc,CAAC;YAC7E;YACAZ,aAAa,CAAC,CAAC;UACnB;QACJ;QACA,IAAI,CAACN,KAAK,CAAChB,iBAAiB,CAAC,0BAA0B,IAAIgB,KAAK,CAAChB,iBAAiB,CAAC,0BAA0B,CAACiC,YAAY,EAAEC,cAAc,EAAEE,eAAe,CAAC,KAAK,IAAI,EAAE;UACnKA,eAAe,CAAC,CAAC;QACrB,CAAC,MACI;UACD;UACAL,MAAM,GAAG,IAAI;QACjB;QACA,OAAOA,MAAM;MACjB,CAAC;MACDf,KAAK,CAACjB,WAAW,CAAC,qBAAqB,GAAG,UAAU0C,SAAS,EAAEC,WAAW,EAAE;QACxE;QACA;QACA,IAAIlB,IAAI,GAAGR,KAAK,CAACT,QAAQ,CAAC,mBAAmB;QAC7C,IAAI,CAACiB,IAAI,IAAKiB,SAAS,IAAIjB,IAAI,KAAKiB,SAAS,CAAClC,QAAQ,CAAC,mBAAmB,CAAC,CAAE,EAAE;UAC3E;UACA;QACJ;QACA,IAAIwB,MAAM;QACV,IAAIY,UAAU,GAAG,KAAK;QACtB,IAAIC,YAAY,GAAGH,SAAS,IAAI7B,mCAAmC,CAAC,IAAI,EAAEY,IAAI,EAAEL,WAAW,IAAIA,WAAW,CAACL,YAAY,CAAC,GAAGK,WAAW,CAACL,YAAY,CAAC,CAAC,CAAC,GAAGK,WAAW,CAAC;QACrK,IAAI0B,cAAc,GAAGH,WAAW,IAAI;UAChCP,MAAM,EAAE,CAAC,CAAC;QACd,CAAC;QACD,SAASW,eAAeA,CAAA,EAAG;UACvB,IAAI,CAACH,UAAU,EAAE;YACbA,UAAU,GAAG,IAAI;YACjBhB,YAAY,CAACiB,YAAY,CAACG,MAAM,CAAC,CAAC,EAAEH,YAAY,CAACpB,IAAI,CAAC,CAAC,EAAEoB,YAAY,CAACtD,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC;UACjH;QACJ;QACA,IAAI,CAAC0B,KAAK,CAACgC,SAAS,IAAIhC,KAAK,CAACgC,SAAS,CAACJ,YAAY,EAAEC,cAAc,EAAEC,eAAe,CAAC,KAAK,IAAI,EAAE;UAC7FA,eAAe,CAAC,CAAC;QACrB,CAAC,MACI;UACDf,MAAM,GAAG,IAAI;QACjB;QACA,OAAOA,MAAM;MACjB,CAAC;MACDf,KAAK,CAACiC,QAAQ,GAAG,UAAUC,KAAK,EAAE;QAC9B,IAAIA,KAAK,EAAE;UACP,IAAIhD,OAAO,CAACgD,KAAK,CAAC,EAAE;YAChB7B,MAAM,GAAGA,MAAM,CAAC8B,MAAM,CAACD,KAAK,CAAC;UACjC,CAAC,MACI;YACD7B,MAAM,CAACzB,SAAS,CAAC,mBAAmB,CAACsD,KAAK,CAAC;UAC/C;QACJ;MACJ,CAAC;MACD7C,eAAe,CAACW,KAAK,EAAE,cAAc,EAAE,YAAY;QAAE,OAAOI,uBAAuB;MAAE,CAAC,EAAE,KAAK,CAAC;IAClG,CAAC,CAAC;IACF;IACAJ,KAAK,CAAC3B,aAAa,CAAC,sBAAsB,GAAG,UAAU+D,OAAO,EAAE;MAC5D,OAAOC,UAAU,CAACD,OAAO,CAAC,CAAC/D,aAAa,CAAC,sBAAsB,CAAC,CAAC;IACrE,CAAC;IACD2B,KAAK,CAACtB,mBAAmB,CAAC,4BAA4B,GAAG,YAAY;MACjE,OAAOuB,cAAc;IACzB,CAAC;IACDD,KAAK,CAACsC,cAAc,GAAG,UAAUC,aAAa,EAAE;MAC5CtC,cAAc,GAAGsC,aAAa;IAClC,CAAC;IACD;IACA;IACA;IACA;IACAvC,KAAK,CAACnB,oBAAoB,CAAC,4BAA4B,GAAG,UAAU2D,IAAI,EAAE;MACtErC,WAAW,GAAGqC,IAAI;IACtB,CAAC;IACDxC,KAAK,CAACrB,iBAAiB,CAAC,0BAA0B,GAAG,UAAU8D,GAAG,EAAEL,OAAO,EAAE;MACzE,IAAIA,OAAO,EAAE;QACT;QACAA,OAAO,CAACzD,iBAAiB,CAAC,0BAA0B,CAAC8D,GAAG,CAAC;MAC7D,CAAC,MACI,IAAItC,WAAW,IAAIhB,UAAU,CAACgB,WAAW,CAACV,qBAAqB,CAAC,+BAA+B,CAAC,EAAE;QACnG;QACA;QACAU,WAAW,CAACV,qBAAqB,CAAC,+BAA+B,CAACgD,GAAG,EAAE,IAAI,CAAC;MAChF;IACJ,CAAC;IACDzC,KAAK,CAACqC,UAAU,GAAGA,UAAU;IAC7B,SAASA,UAAUA,CAACK,UAAU,EAAE;MAC5B,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;QAAEA,UAAU,GAAG,IAAI;MAAE;MAChD,IAAIN,OAAO,GAAGM,UAAU;MACxB,IAAI,CAACN,OAAO,EAAE;QACV,IAAIO,OAAO,GAAGzC,QAAQ,IAAIR,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEM,KAAK,CAACT,QAAQ,CAAC,mBAAmB,CAAC;QACrG;QACA,IAAIY,WAAW,IAAIA,WAAW,CAACL,YAAY,CAAC,EAAE;UAC1C;UACAsC,OAAO,GAAGO,OAAO,CAACvE,eAAe,CAAC,wBAAwB,CAAC,IAAI,EAAE+B,WAAW,CAACL,YAAY,CAAC,CAAC;QAC/F,CAAC,MACI;UACDsC,OAAO,GAAGO,OAAO,CAACvE,eAAe,CAAC,wBAAwB,CAAC,IAAI,EAAE+B,WAAW,CAAC;QACjF;MACJ;MACA,OAAOiC,OAAO;IAClB;IACA,SAASzB,YAAYA,CAACJ,MAAM,EAAEC,IAAI,EAAEE,WAAW,EAAE;MAC7C,IAAIH,MAAM,EAAE;QACR;QACAjB,QAAQ,CAACiB,MAAM,EAAEf,oBAAoB,EAAE,EAAE,EAAE,IAAI,EAAEJ,iBAAiB,CAAC;MACvE;MACA,IAAI,CAACsB,WAAW,IAAIF,IAAI,EAAE;QACtB;QACAE,WAAW,GAAGF,IAAI,CAACjC,0BAA0B,CAAC,mCAAmC,CAAC,CAAC,CAACD,aAAa,CAAC,sBAAsB,CAAC,CAAC;MAC9H;MACA,IAAIsE,UAAU,GAAGzC,WAAW;MAC5B,IAAIA,WAAW,IAAIA,WAAW,CAACL,YAAY,CAAC,EAAE;QAC1C;QACA8C,UAAU,GAAGzC,WAAW,CAACL,YAAY,CAAC,CAAC,CAAC;MAC5C;MACA;MACAE,KAAK,CAACT,QAAQ,CAAC,mBAAmB,GAAGiB,IAAI;MACzCN,QAAQ,GAAGR,6BAA6B,CAACgB,WAAW,EAAEH,MAAM,EAAEC,IAAI,EAAEoC,UAAU,CAAC;IACnF;IACA,SAAStC,aAAaA,CAAA,EAAG;MACrBL,cAAc,GAAG,KAAK;MACtBD,KAAK,CAACT,QAAQ,CAAC,mBAAmB,GAAG,IAAI;MACzCW,QAAQ,GAAG,IAAI;MACfC,WAAW,GAAG,IAAI;MAClBE,MAAM,GAAG,EAAE;MACXD,uBAAuB,GAAGP,4BAA4B,CAAC,CAAC;IAC5D;EACJ;EACJ;EACA;EACA;EACA;EACA;EACI;EACA;EACA;EACAE,mBAAmB,CAAC8C,OAAO,GAAC,CAAC;EAE7B,OAAO9C,mBAAmB;AAC9B,CAAC,CAAC,CAAE;AACJ,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}