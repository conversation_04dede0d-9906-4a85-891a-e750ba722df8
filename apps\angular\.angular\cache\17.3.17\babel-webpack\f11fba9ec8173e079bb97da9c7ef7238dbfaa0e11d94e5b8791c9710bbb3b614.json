{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 0) return 0;\n  if (n === 1) return 1;\n  if (n === 2) return 2;\n  if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10) return 3;\n  if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99) return 4;\n  return 5;\n}\nexport default [\"ar-TD\", [[\"ص\", \"م\"], u, u], [[\"ص\", \"م\"], u, [\"صباحًا\", \"مساءً\"]], [[\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"], [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"], u, [\"أحد\", \"إثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"]], u, [[\"ي\", \"ف\", \"م\", \"أ\", \"و\", \"ن\", \"ل\", \"غ\", \"س\", \"ك\", \"ب\", \"د\"], [\"يناير\", \"فبراير\", \"مارس\", \"أبريل\", \"مايو\", \"يونيو\", \"يوليو\", \"أغسطس\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"], u], u, [[\"ق.م\", \"م\"], u, [\"قبل الميلاد\", \"ميلادي\"]], 1, [6, 0], [\"d‏/M‏/y\", \"dd‏/MM‏/y\", \"d MMMM y\", \"EEEE، d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} في {0}\", u], [\".\", \",\", \";\", \"‎%‎\", \"‎+\", \"‎-\", \"E\", \"×\", \"‰\", \"∞\", \"ليس رقمًا\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"XAF\", \"FCFA\", \"فرنك وسط أفريقي\", {\n  \"AED\": [\"د.إ.‏\"],\n  \"ARS\": [u, \"AR$\"],\n  \"AUD\": [\"AU$\"],\n  \"BBD\": [u, \"BB$\"],\n  \"BHD\": [\"د.ب.‏\"],\n  \"BMD\": [u, \"BM$\"],\n  \"BND\": [u, \"BN$\"],\n  \"BSD\": [u, \"BS$\"],\n  \"BYN\": [u, \"р.\"],\n  \"BZD\": [u, \"BZ$\"],\n  \"CAD\": [\"CA$\"],\n  \"CLP\": [u, \"CL$\"],\n  \"CNY\": [\"CN¥\"],\n  \"COP\": [u, \"CO$\"],\n  \"CUP\": [u, \"CU$\"],\n  \"DOP\": [u, \"DO$\"],\n  \"DZD\": [\"د.ج.‏\"],\n  \"EGP\": [\"ج.م.‏\", \"E£\"],\n  \"FJD\": [u, \"FJ$\"],\n  \"GBP\": [\"UK£\"],\n  \"GYD\": [u, \"GY$\"],\n  \"HKD\": [\"HK$\"],\n  \"IQD\": [\"د.ع.‏\"],\n  \"IRR\": [\"ر.إ.\"],\n  \"JMD\": [u, \"JM$\"],\n  \"JOD\": [\"د.أ.‏\"],\n  \"JPY\": [\"JP¥\"],\n  \"KWD\": [\"د.ك.‏\"],\n  \"KYD\": [u, \"KY$\"],\n  \"LBP\": [\"ل.ل.‏\", \"L£\"],\n  \"LRD\": [u, \"$LR\"],\n  \"LYD\": [\"د.ل.‏\"],\n  \"MAD\": [\"د.م.‏\"],\n  \"MRU\": [\"أ.م.\"],\n  \"MXN\": [\"MX$\"],\n  \"NZD\": [\"NZ$\"],\n  \"OMR\": [\"ر.ع.‏\"],\n  \"PHP\": [u, \"₱\"],\n  \"QAR\": [\"ر.ق.‏\"],\n  \"SAR\": [\"ر.س.‏\"],\n  \"SBD\": [u, \"SB$\"],\n  \"SDD\": [\"د.س.‏\"],\n  \"SDG\": [\"ج.س.\"],\n  \"SRD\": [u, \"SR$\"],\n  \"SYP\": [\"ل.س.‏\", \"£\"],\n  \"THB\": [\"฿\"],\n  \"TND\": [\"د.ت.‏\"],\n  \"TTD\": [u, \"TT$\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\"],\n  \"UYU\": [u, \"UY$\"],\n  \"YER\": [\"ر.ي.‏\"]\n}, \"rtl\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "Math", "floor"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/ar-TD.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 0)\n        return 0;\n    if (n === 1)\n        return 1;\n    if (n === 2)\n        return 2;\n    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 3 && n % 100 <= 10))\n        return 3;\n    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 99))\n        return 4;\n    return 5;\n}\nexport default [\"ar-TD\", [[\"ص\", \"م\"], u, u], [[\"ص\", \"م\"], u, [\"صباحًا\", \"مساءً\"]], [[\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"], [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"], u, [\"أحد\", \"إثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"]], u, [[\"ي\", \"ف\", \"م\", \"أ\", \"و\", \"ن\", \"ل\", \"غ\", \"س\", \"ك\", \"ب\", \"د\"], [\"يناير\", \"فبراير\", \"مارس\", \"أبريل\", \"مايو\", \"يونيو\", \"يوليو\", \"أغسطس\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"], u], u, [[\"ق.م\", \"م\"], u, [\"قبل الميلاد\", \"ميلادي\"]], 1, [6, 0], [\"d‏/M‏/y\", \"dd‏/MM‏/y\", \"d MMMM y\", \"EEEE، d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} في {0}\", u], [\".\", \",\", \";\", \"‎%‎\", \"‎+\", \"‎-\", \"E\", \"×\", \"‰\", \"∞\", \"ليس رقمًا\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"XAF\", \"FCFA\", \"فرنك وسط أفريقي\", { \"AED\": [\"د.إ.‏\"], \"ARS\": [u, \"AR$\"], \"AUD\": [\"AU$\"], \"BBD\": [u, \"BB$\"], \"BHD\": [\"د.ب.‏\"], \"BMD\": [u, \"BM$\"], \"BND\": [u, \"BN$\"], \"BSD\": [u, \"BS$\"], \"BYN\": [u, \"р.\"], \"BZD\": [u, \"BZ$\"], \"CAD\": [\"CA$\"], \"CLP\": [u, \"CL$\"], \"CNY\": [\"CN¥\"], \"COP\": [u, \"CO$\"], \"CUP\": [u, \"CU$\"], \"DOP\": [u, \"DO$\"], \"DZD\": [\"د.ج.‏\"], \"EGP\": [\"ج.م.‏\", \"E£\"], \"FJD\": [u, \"FJ$\"], \"GBP\": [\"UK£\"], \"GYD\": [u, \"GY$\"], \"HKD\": [\"HK$\"], \"IQD\": [\"د.ع.‏\"], \"IRR\": [\"ر.إ.\"], \"JMD\": [u, \"JM$\"], \"JOD\": [\"د.أ.‏\"], \"JPY\": [\"JP¥\"], \"KWD\": [\"د.ك.‏\"], \"KYD\": [u, \"KY$\"], \"LBP\": [\"ل.ل.‏\", \"L£\"], \"LRD\": [u, \"$LR\"], \"LYD\": [\"د.ل.‏\"], \"MAD\": [\"د.م.‏\"], \"MRU\": [\"أ.م.\"], \"MXN\": [\"MX$\"], \"NZD\": [\"NZ$\"], \"OMR\": [\"ر.ع.‏\"], \"PHP\": [u, \"₱\"], \"QAR\": [\"ر.ق.‏\"], \"SAR\": [\"ر.س.‏\"], \"SBD\": [u, \"SB$\"], \"SDD\": [\"د.س.‏\"], \"SDG\": [\"ج.س.\"], \"SRD\": [u, \"SR$\"], \"SYP\": [\"ل.س.‏\", \"£\"], \"THB\": [\"฿\"], \"TND\": [\"د.ت.‏\"], \"TTD\": [u, \"TT$\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\"], \"UYU\": [u, \"UY$\"], \"YER\": [\"ر.ي.‏\"] }, \"rtl\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAKA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAG,EAClE,OAAO,CAAC;EACZ,IAAIA,CAAC,GAAG,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAKA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAG,EACnE,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAEA,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAEA,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,YAAY,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAiB,EAAE;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,OAAO;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}