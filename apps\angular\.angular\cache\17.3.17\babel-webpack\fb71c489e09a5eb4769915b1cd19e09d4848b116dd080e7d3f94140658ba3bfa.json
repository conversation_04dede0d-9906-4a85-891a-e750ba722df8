{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i1 from '@abp/ng.core';\nimport { CoreModule, ConfigStateService, mapEnumToOptions, SubscriptionService, TrackByService, LocalizationModule } from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { NgModule, ChangeDetectorRef, Component, Input, Injectable, ChangeDetectionStrategy, inject } from '@angular/core';\nimport * as i4 from '@abp/ng.theme.shared';\nimport { ToasterService, ThemeSharedModule, collapse } from '@abp/ng.theme.shared';\nimport * as i5 from '@ng-bootstrap/ng-bootstrap';\nimport { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\nimport { finalize, map } from 'rxjs/operators';\nimport * as i1$1 from '@angular/common';\nimport { Ng<PERSON><PERSON>, AsyncPipe, JsonPipe, NgIf } from '@angular/common';\nimport * as i2 from '@angular/forms';\nimport { Validators, FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { of, firstValueFrom } from 'rxjs';\nimport * as i4$1 from '@ngx-validate/core';\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\nimport { TimeZoneSettingsService } from '@abp/ng.setting-management/proxy';\nfunction AccountSettingsGeneralComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵlistener(\"keyup.enter\", function AccountSettingsGeneralComponent_ng_container_0_Template_div_keyup_enter_1_listener() {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit(settings_r2));\n    });\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 2)(6, \"input\", 3);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsGeneralComponent_ng_container_0_Template_input_ngModelChange_6_listener($event) {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(settings_r2.isSelfRegistrationEnabled, $event) || (settings_r2.isSelfRegistrationEnabled = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"label\", 4);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 2)(11, \"input\", 5);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsGeneralComponent_ng_container_0_Template_input_ngModelChange_11_listener($event) {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(settings_r2.enableLocalLogin, $event) || (settings_r2.enableLocalLogin = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 6);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"abpLocalization\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(15, \"hr\", 7);\n    i0.ɵɵelementStart(16, \"div\")(17, \"abp-button\", 8);\n    i0.ɵɵlistener(\"click\", function AccountSettingsGeneralComponent_ng_container_0_Template_abp_button_click_17_listener() {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit(settings_r2));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const settings_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 7, \"AbpAccount::AccountSettingsGeneral\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", settings_r2.isSelfRegistrationEnabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 9, \"AbpAccount::DisplayName:IsSelfRegistrationEnabled\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", settings_r2.enableLocalLogin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 11, \"AbpAccount::DisplayName:EnableLocalLogin\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"loading\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 13, \"AbpAccount::Save\"));\n  }\n}\nfunction AccountSettingsTwoFactorComponent_ng_container_0_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", option_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r4.key, \" \");\n  }\n}\nfunction AccountSettingsTwoFactorComponent_ng_container_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"input\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsTwoFactorComponent_ng_container_0_div_12_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const settings_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(settings_r2.usersCanChange, $event) || (settings_r2.usersCanChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const settings_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", settings_r2.usersCanChange);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"AbpIdentity::DisplayName:Abp.Identity.UsersCanChange\"));\n  }\n}\nfunction AccountSettingsTwoFactorComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵlistener(\"keyup.enter\", function AccountSettingsTwoFactorComponent_ng_container_0_Template_div_keyup_enter_1_listener() {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit(settings_r2));\n    });\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"div\", 2)(7, \"label\", 3);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 4);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsTwoFactorComponent_ng_container_0_Template_select_ngModelChange_10_listener($event) {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(settings_r2.twoFactorBehaviour, $event) || (settings_r2.twoFactorBehaviour = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(11, AccountSettingsTwoFactorComponent_ng_container_0_option_11_Template, 2, 2, \"option\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, AccountSettingsTwoFactorComponent_ng_container_0_div_12_Template, 5, 4, \"div\", 6);\n    i0.ɵɵelementStart(13, \"div\", 7)(14, \"input\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsTwoFactorComponent_ng_container_0_Template_input_ngModelChange_14_listener($event) {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(settings_r2.isRememberBrowserEnabled, $event) || (settings_r2.isRememberBrowserEnabled = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 9);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"abpLocalization\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(18, \"hr\", 10);\n    i0.ɵɵelementStart(19, \"div\")(20, \"abp-button\", 11);\n    i0.ɵɵlistener(\"click\", function AccountSettingsTwoFactorComponent_ng_container_0_Template_abp_button_click_20_listener() {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit(settings_r2));\n    });\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"abpLocalization\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const settings_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 9, \"AbpAccount::TwoFactorAuthentication\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 11, \"AbpIdentity::DisplayName:Abp.Identity.TwoFactorBehaviour\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", settings_r2.twoFactorBehaviour);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.twoFactorBehaviourOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", settings_r2.twoFactorBehaviour === ctx_r2.eTwoFactorBehaviour.Optional);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", settings_r2.isRememberBrowserEnabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 13, \"AbpAccount::RememberBrowser\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"loading\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 15, \"AbpAccount::Save\"));\n  }\n}\nfunction AccountSettingsCaptchaComponent_form_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelement(2, \"input\", 16);\n    i0.ɵɵelementStart(3, \"label\", 17);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"input\", 18);\n    i0.ɵɵelementStart(8, \"label\", 19);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 6)(12, \"label\", 10);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"AbpAccount::Description:UseCaptchaOnLogin\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 5, \"AbpAccount::Description:UseCaptchaOnRegistration\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 7, \"AbpAccount::DisplayName:VerifyBaseUrl\"));\n  }\n}\nfunction AccountSettingsCaptchaComponent_form_4_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"label\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"AbpAccount::DisplayName:Score\"));\n  }\n}\nfunction AccountSettingsCaptchaComponent_form_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 4);\n    i0.ɵɵtemplate(1, AccountSettingsCaptchaComponent_form_4_ng_container_1_Template, 16, 9, \"ng-container\", 5);\n    i0.ɵɵelementStart(2, \"div\", 6)(3, \"label\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 8)(7, \"option\", 9);\n    i0.ɵɵtext(8, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"option\", 9);\n    i0.ɵɵtext(10, \"3\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 6)(12, \"label\", 10);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 11);\n    i0.ɵɵelementStart(16, \"small\", 12);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 6)(20, \"label\", 10);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"input\", 13);\n    i0.ɵɵelementStart(24, \"small\", 12);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, AccountSettingsCaptchaComponent_form_4_div_27_Template, 5, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.form);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isTenant);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 10, \"AbpAccount::DisplayName:Version\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngValue\", 3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 12, \"AbpAccount::DisplayName:SiteKey\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 14, \"AbpAccount::SetNullWillUseGlobalSettings\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 16, \"AbpAccount::DisplayName:SiteSecret\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 18, \"AbpAccount::SetNullWillUseGlobalSettings\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.form.controls.version.value === 3);\n  }\n}\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nconst _c1 = a0 => ({\n  $implicit: a0,\n  type: \"password\"\n});\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_container_1_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0)(1);\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"input\", 9);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_container_1_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const provider_r5 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(provider_r5.useHostSettings, $event) || (provider_r5.useHostSettings = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 11);\n    i0.ɵɵtemplate(10, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_container_1_ng_container_10_Template, 1, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"hr\", 13);\n    i0.ɵɵelementContainerEnd()();\n  }\n  if (rf & 2) {\n    const provider_r5 = i0.ɵɵnextContext().$implicit;\n    const propertySetsTemplate_r6 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(provider_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", provider_r5.useHostSettings);\n    i0.ɵɵattribute(\"id\", provider_r5.name + \".use-host-settings\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", provider_r5.name + \".use-host-settings\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 8, \"AbpAccount::ExternalProviderUseHostSettings\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"show\", !provider_r5.useHostSettings);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", propertySetsTemplate_r6);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_2_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 14)(3, \"input\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_2_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const provider_r5 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(provider_r5.enabled, $event) || (provider_r5.enabled = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_2_ng_container_7_Template, 1, 0, \"ng-container\", 12);\n  }\n  if (rf & 2) {\n    const provider_r5 = i0.ɵɵnextContext().$implicit;\n    const propertySetsTemplate_r6 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(provider_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", provider_r5.enabled);\n    i0.ɵɵattribute(\"id\", provider_r5.name + \".enabled\")(\"name\", provider_r5.name + \".enabled\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"for\", provider_r5.name + \".enabled\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 7, \"AbpAccount::ExternalProviderEnabled\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", propertySetsTemplate_r6);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"label\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_template_2_ng_container_0_Template_input_ngModelChange_5_listener($event) {\n      const property_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(property_r9.value, $event) || (property_r9.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const property_r9 = ctx.$implicit;\n    const type_r10 = i0.ɵɵnextContext().type;\n    const provider_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"for\", provider_r5.name + \".\" + property_r9.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 5, \"::ExternalProvider:\" + provider_r5.name + \":\" + property_r9.name));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"type\", type_r10 || \"text\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", property_r9.value);\n    i0.ɵɵattribute(\"id\", provider_r5.name + \".\" + property_r9.name);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_template_2_ng_container_0_Template, 6, 7, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const properties_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngForOf\", properties_r11);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_container_0_Template, 1, 0, \"ng-container\", 17)(1, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_container_1_Template, 1, 0, \"ng-container\", 17)(2, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_ng_template_2_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const singlePropertySetTemplate_r12 = i0.ɵɵreference(3);\n    const provider_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngTemplateOutlet\", singlePropertySetTemplate_r12)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c0, provider_r5.properties));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", singlePropertySetTemplate_r12)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c1, provider_r5.secretProperties));\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_container_1_Template, 12, 10, \"ng-container\", 7)(2, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_2_Template, 8, 9, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_ng_template_4_Template, 4, 8, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const host_r13 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTenant)(\"ngIfElse\", host_r13);\n  }\n}\nfunction AccountSettingsExternalProviderComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵlistener(\"keyup.enter\", function AccountSettingsExternalProviderComponent_ng_container_0_Template_div_keyup_enter_1_listener() {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit(settings_r2.settings));\n    });\n    i0.ɵɵtemplate(2, AccountSettingsExternalProviderComponent_ng_container_0_ng_container_2_Template, 6, 2, \"ng-container\", 5);\n    i0.ɵɵelementStart(3, \"div\")(4, \"abp-button\", 6);\n    i0.ɵɵlistener(\"click\", function AccountSettingsExternalProviderComponent_ng_container_0_Template_abp_button_click_4_listener() {\n      const settings_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit(settings_r2.settings));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"abpLocalization\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const settings_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", settings_r2.settings);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"loading\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 3, \"AbpAccount::Save\"));\n  }\n}\nfunction AccountSettingsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-account-settings-general\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"isTenant\", ctx_r0.isTenant);\n  }\n}\nfunction AccountSettingsComponent_li_9_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-account-settings-two-factor\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"isTenant\", ctx_r0.isTenant);\n  }\n}\nfunction AccountSettingsComponent_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 4)(1, \"a\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AccountSettingsComponent_li_9_ng_template_4_Template, 1, 1, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"AbpAccount::AccountSettingsTwoFactor\"));\n  }\n}\nfunction AccountSettingsComponent_li_10_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-account-settings-captcha\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"isTenant\", ctx_r0.isTenant);\n  }\n}\nfunction AccountSettingsComponent_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 4)(1, \"a\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AccountSettingsComponent_li_10_ng_template_4_Template, 1, 1, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"AbpAccount::Captcha\"), \" \");\n  }\n}\nfunction AccountSettingsComponent_ng_container_12_li_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-account-settings-external-provider\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"isTenant\", ctx_r0.isTenant);\n  }\n}\nfunction AccountSettingsComponent_ng_container_12_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 4)(1, \"a\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AccountSettingsComponent_ng_container_12_li_1_ng_template_4_Template, 1, 1, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"AbpAccount::AccountExternalProviderSettings\"), \" \");\n  }\n}\nfunction AccountSettingsComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccountSettingsComponent_ng_container_12_li_1_Template, 5, 3, \"li\", 7);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 1, ctx_r0.isExternalProviderExists$));\n  }\n}\nfunction TimeZoneSettingComponent_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", item_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r1.name, \" \");\n  }\n}\nlet AccountAdminModule = /*#__PURE__*/(() => {\n  class AccountAdminModule {\n    static {\n      this.ɵfac = function AccountAdminModule_Factory(t) {\n        return new (t || AccountAdminModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: AccountAdminModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CoreModule]\n      });\n    }\n  }\n  return AccountAdminModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass AbstractAccountSettingsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpAccountAdmin';\n  }\n  getSettings() {\n    return this.restService.request({\n      method: 'GET',\n      url: this.url\n    }, {\n      apiName: this.apiName\n    });\n  }\n  updateSettings(body) {\n    return this.restService.request({\n      method: 'PUT',\n      url: this.url,\n      body\n    }, {\n      apiName: this.apiName\n    });\n  }\n}\nlet AbstractAccountSettingsComponent = /*#__PURE__*/(() => {\n  class AbstractAccountSettingsComponent {\n    set loading(value) {\n      this._loading = value;\n      this.cdr.markForCheck();\n    }\n    get loading() {\n      return this._loading;\n    }\n    constructor(injector) {\n      this.injector = injector;\n      this.service = injector.get(AbstractAccountSettingsService);\n      this.toaster = injector.get(ToasterService);\n      this.cdr = injector.get(ChangeDetectorRef);\n      this.configState = injector.get(ConfigStateService);\n    }\n    ngOnInit() {\n      this.settings$ = this.service.getSettings();\n    }\n    submit(newSettings) {\n      this.loading = true;\n      this.service.updateSettings(this.isTenant ? this.mapTenantSettingsForSubmit(newSettings) : newSettings).pipe(finalize(() => this.loading = false)).subscribe(() => {\n        this.toaster.success('AbpSettingManagement::SuccessfullySaved', null);\n        this.configState.refreshAppState().subscribe();\n      });\n    }\n    /**\n     * should be overriden by children components\n     * if it is not overridden,\n     * it means that there is no difference between host and tenant for the particular child\n     */\n    mapTenantSettingsForSubmit(newSettings) {\n      return newSettings;\n    }\n    static {\n      this.ɵfac = function AbstractAccountSettingsComponent_Factory(t) {\n        return new (t || AbstractAccountSettingsComponent)(i0.ɵɵdirectiveInject(i0.Injector));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: AbstractAccountSettingsComponent,\n        selectors: [[\"ng-component\"]],\n        inputs: {\n          isTenant: \"isTenant\"\n        },\n        decls: 0,\n        vars: 0,\n        template: function AbstractAccountSettingsComponent_Template(rf, ctx) {},\n        encapsulation: 2\n      });\n    }\n  }\n  return AbstractAccountSettingsComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountSettingsService = /*#__PURE__*/(() => {\n  class AccountSettingsService extends AbstractAccountSettingsService {\n    constructor(restService) {\n      super(restService);\n      this.url = '/api/account-admin/settings';\n    }\n    static {\n      this.ɵfac = function AccountSettingsService_Factory(t) {\n        return new (t || AccountSettingsService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AccountSettingsService,\n        factory: AccountSettingsService.ɵfac\n      });\n    }\n  }\n  return AccountSettingsService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountSettingsGeneralComponent = /*#__PURE__*/(() => {\n  class AccountSettingsGeneralComponent extends AbstractAccountSettingsComponent {\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵAccountSettingsGeneralComponent_BaseFactory;\n        return function AccountSettingsGeneralComponent_Factory(t) {\n          return (ɵAccountSettingsGeneralComponent_BaseFactory || (ɵAccountSettingsGeneralComponent_BaseFactory = i0.ɵɵgetInheritedFactory(AccountSettingsGeneralComponent)))(t || AccountSettingsGeneralComponent);\n        };\n      })();\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: AccountSettingsGeneralComponent,\n        selectors: [[\"abp-account-settings-general\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: AbstractAccountSettingsService,\n          useClass: AccountSettingsService\n        }]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[4, \"ngIf\"], [3, \"keyup.enter\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"IsSelfRegistrationEnabled\", \"name\", \"IsSelfRegistrationEnabled\", \"autofocus\", \"\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"IsSelfRegistrationEnabled\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"EnableLocalLogin\", \"name\", \"EnableLocalLogin\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"EnableLocalLogin\", 1, \"form-check-label\"], [1, \"my-3\"], [\"iconClass\", \"fa fa-save\", 3, \"click\", \"loading\"]],\n        template: function AccountSettingsGeneralComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AccountSettingsGeneralComponent_ng_container_0_Template, 20, 15, \"ng-container\", 0);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.settings$));\n          }\n        },\n        dependencies: [i1$1.NgIf, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgModel, i1.AutofocusDirective, i4.ButtonComponent, i1$1.AsyncPipe, i1.LocalizationPipe],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return AccountSettingsGeneralComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountTwoFactorSettingService = /*#__PURE__*/(() => {\n  class AccountTwoFactorSettingService extends AbstractAccountSettingsService {\n    constructor(restService) {\n      super(restService);\n      this.url = '/api/account-admin/settings/two-factor';\n    }\n    static {\n      this.ɵfac = function AccountTwoFactorSettingService_Factory(t) {\n        return new (t || AccountTwoFactorSettingService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AccountTwoFactorSettingService,\n        factory: AccountTwoFactorSettingService.ɵfac\n      });\n    }\n  }\n  return AccountTwoFactorSettingService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nvar eTwoFactorBehaviour = /*#__PURE__*/function (eTwoFactorBehaviour) {\n  eTwoFactorBehaviour[eTwoFactorBehaviour[\"Optional\"] = 0] = \"Optional\";\n  eTwoFactorBehaviour[eTwoFactorBehaviour[\"Disabled\"] = 1] = \"Disabled\";\n  eTwoFactorBehaviour[eTwoFactorBehaviour[\"Forced\"] = 2] = \"Forced\";\n  return eTwoFactorBehaviour;\n}(eTwoFactorBehaviour || {});\nconst twoFactorBehaviourOptions = mapEnumToOptions(eTwoFactorBehaviour);\nlet AccountSettingsTwoFactorComponent = /*#__PURE__*/(() => {\n  class AccountSettingsTwoFactorComponent extends AbstractAccountSettingsComponent {\n    constructor() {\n      super(...arguments);\n      this.eTwoFactorBehaviour = eTwoFactorBehaviour;\n      this.twoFactorBehaviourOptions = twoFactorBehaviourOptions;\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵAccountSettingsTwoFactorComponent_BaseFactory;\n        return function AccountSettingsTwoFactorComponent_Factory(t) {\n          return (ɵAccountSettingsTwoFactorComponent_BaseFactory || (ɵAccountSettingsTwoFactorComponent_BaseFactory = i0.ɵɵgetInheritedFactory(AccountSettingsTwoFactorComponent)))(t || AccountSettingsTwoFactorComponent);\n        };\n      })();\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: AccountSettingsTwoFactorComponent,\n        selectors: [[\"abp-account-settings-two-factor\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: AbstractAccountSettingsService,\n          useClass: AccountTwoFactorSettingService\n        }]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[4, \"ngIf\"], [3, \"keyup.enter\"], [1, \"mb-3\"], [\"for\", \"AccountTwoFactorSettings_TwoFactorBehaviour\", 1, \"form-label\"], [\"name\", \"TwoFactorBehaviour\", 1, \"form-select\", \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"form-check mb-2\", 4, \"ngIf\"], [1, \"form-check\", \"mb-2\"], [\"name\", \"IsRememberBrowserEnabled\", \"type\", \"checkbox\", \"checked\", \"checked\", \"id\", \"AccountTwoFactorSettings_IsRememberBrowserEnabled\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"AccountTwoFactorSettings_IsRememberBrowserEnabled\", 1, \"form-check-label\"], [1, \"my-3\"], [\"iconClass\", \"fa fa-save\", 3, \"click\", \"loading\"], [3, \"ngValue\"], [\"name\", \"UsersCanChange\", \"type\", \"checkbox\", \"checked\", \"checked\", \"id\", \"AccountTwoFactorSettings_UsersCanChange\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"AccountTwoFactorSettings_UsersCanChange\", 1, \"form-check-label\"]],\n        template: function AccountSettingsTwoFactorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AccountSettingsTwoFactorComponent_ng_container_0_Template, 23, 17, \"ng-container\", 0);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.settings$));\n          }\n        },\n        dependencies: [i1$1.NgForOf, i1$1.NgIf, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.CheckboxControlValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel, i4.ButtonComponent, i1$1.AsyncPipe, i1.LocalizationPipe],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return AccountSettingsTwoFactorComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountCaptchaService = /*#__PURE__*/(() => {\n  class AccountCaptchaService extends AbstractAccountSettingsService {\n    constructor(restService) {\n      super(restService);\n      this.url = '/api/account-admin/settings/recaptcha';\n    }\n    static {\n      this.ɵfac = function AccountCaptchaService_Factory(t) {\n        return new (t || AccountCaptchaService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AccountCaptchaService,\n        factory: AccountCaptchaService.ɵfac\n      });\n    }\n  }\n  return AccountCaptchaService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountExternalProviderService = /*#__PURE__*/(() => {\n  class AccountExternalProviderService extends AbstractAccountSettingsService {\n    constructor(restService) {\n      super(restService);\n      this.url = '/api/account-admin/settings/external-provider';\n    }\n    static {\n      this.ɵfac = function AccountExternalProviderService_Factory(t) {\n        return new (t || AccountExternalProviderService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AccountExternalProviderService,\n        factory: AccountExternalProviderService.ɵfac\n      });\n    }\n  }\n  return AccountExternalProviderService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountSettingsCaptchaComponent = /*#__PURE__*/(() => {\n  class AccountSettingsCaptchaComponent extends AbstractAccountSettingsComponent {\n    constructor(injector, fb, subscription) {\n      super(injector);\n      this.injector = injector;\n      this.fb = fb;\n      this.subscription = subscription;\n    }\n    ngOnInit() {\n      super.ngOnInit();\n      this.subscription.addOne(this.settings$, settings => this.buildForm(settings));\n    }\n    buildForm(settings) {\n      this.form = this.fb.group({\n        useCaptchaOnLogin: [settings.useCaptchaOnLogin],\n        useCaptchaOnRegistration: [settings.useCaptchaOnRegistration],\n        verifyBaseUrl: [settings.verifyBaseUrl, [Validators.required]],\n        siteKey: [settings.siteKey],\n        siteSecret: [settings.siteSecret],\n        version: [settings.version, [Validators.required]],\n        score: [settings.score, [Validators.required, Validators.min(0), Validators.max(1)]]\n      });\n      this.cdr.detectChanges();\n    }\n    mapTenantSettingsForSubmit(newSettings) {\n      return {\n        version: newSettings.version,\n        siteKey: newSettings.siteKey,\n        siteSecret: newSettings.siteSecret\n      };\n    }\n    submit() {\n      if (this.form.invalid) return;\n      super.submit(this.form.value);\n    }\n    static {\n      this.ɵfac = function AccountSettingsCaptchaComponent_Factory(t) {\n        return new (t || AccountSettingsCaptchaComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder), i0.ɵɵdirectiveInject(i1.SubscriptionService));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: AccountSettingsCaptchaComponent,\n        selectors: [[\"abp-account-settings-captcha\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: AbstractAccountSettingsService,\n          useClass: AccountCaptchaService\n        }, SubscriptionService]), i0.ɵɵInheritDefinitionFeature],\n        decls: 10,\n        vars: 8,\n        consts: [[3, \"keyup.enter\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"my-3\"], [\"iconClass\", \"fa fa-save\", 3, \"click\", \"loading\"], [3, \"formGroup\"], [4, \"ngIf\"], [1, \"mb-3\"], [\"for\", \"captcha-version\", 1, \"form-label\"], [\"id\", \"captcha-version\", \"formControlName\", \"version\", 1, \"form-control\"], [3, \"ngValue\"], [1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"siteKey\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"type\", \"text\", \"formControlName\", \"siteSecret\", 1, \"form-control\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"use-captcha-on-login\", \"name\", \"useCaptchaOnLogin\", \"formControlName\", \"useCaptchaOnLogin\", \"autofocus\", \"\", 1, \"form-check-input\"], [\"for\", \"use-captcha-on-login\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"use-captcha-on-registration\", \"name\", \"useCaptchaOnRegistration\", \"formControlName\", \"useCaptchaOnRegistration\", 1, \"form-check-input\"], [\"for\", \"use-captcha-on-registration\", 1, \"form-check-label\"], [\"type\", \"text\", \"formControlName\", \"verifyBaseUrl\", 1, \"form-control\"], [\"type\", \"number\", \"formControlName\", \"score\", \"min\", \"0\", \"max\", \"1\", 1, \"form-control\"]],\n        template: function AccountSettingsCaptchaComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵlistener(\"keyup.enter\", function AccountSettingsCaptchaComponent_Template_div_keyup_enter_0_listener() {\n              return ctx.submit();\n            });\n            i0.ɵɵelementStart(1, \"h4\");\n            i0.ɵɵtext(2);\n            i0.ɵɵpipe(3, \"abpLocalization\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, AccountSettingsCaptchaComponent_form_4_Template, 28, 20, \"form\", 1);\n            i0.ɵɵelement(5, \"hr\", 2);\n            i0.ɵɵelementStart(6, \"div\")(7, \"abp-button\", 3);\n            i0.ɵɵlistener(\"click\", function AccountSettingsCaptchaComponent_Template_abp_button_click_7_listener() {\n              return ctx.submit();\n            });\n            i0.ɵɵtext(8);\n            i0.ɵɵpipe(9, \"abpLocalization\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"AbpAccount::Captcha\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.form);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"loading\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 6, \"AbpAccount::Save\"));\n          }\n        },\n        dependencies: [i1$1.NgIf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.CheckboxControlValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MinValidator, i2.MaxValidator, i2.FormGroupDirective, i2.FormControlName, i1.AutofocusDirective, i4$1.ValidationGroupDirective, i4$1.ValidationDirective, i4.ButtonComponent, i1.LocalizationPipe],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return AccountSettingsCaptchaComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountSettingsExternalProviderComponent = /*#__PURE__*/(() => {\n  class AccountSettingsExternalProviderComponent extends AbstractAccountSettingsComponent {\n    constructor() {\n      super(...arguments);\n      this.mapInitialTenantSettings = result => ({\n        settings: result.settings.filter(setting => setting.enabled).map(this.setUseHostSettingsOf)\n      });\n    }\n    ngOnInit() {\n      if (this.isTenant) {\n        this.settings$ = this.service.getSettings().pipe(map(this.mapInitialTenantSettings));\n      } else {\n        super.ngOnInit();\n      }\n    }\n    mapTenantSettingsForSubmit(newSettings) {\n      return newSettings.map(this.clearPropertyValues);\n    }\n    clearPropertyValues(setting) {\n      if (setting.useHostSettings) {\n        setting.properties.forEach(prop => prop.value = '');\n        setting.secretProperties.forEach(prop => prop.value = '');\n      }\n      const {\n        useHostSettings,\n        ...mappedSetting\n      } = setting;\n      return mappedSetting;\n    }\n    setUseHostSettingsOf(setting) {\n      const useHostSettings = !(setting.properties.some(prop => prop.value) || setting.secretProperties.some(prop => prop.value));\n      return {\n        ...setting,\n        useHostSettings\n      };\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵAccountSettingsExternalProviderComponent_BaseFactory;\n        return function AccountSettingsExternalProviderComponent_Factory(t) {\n          return (ɵAccountSettingsExternalProviderComponent_BaseFactory || (ɵAccountSettingsExternalProviderComponent_BaseFactory = i0.ɵɵgetInheritedFactory(AccountSettingsExternalProviderComponent)))(t || AccountSettingsExternalProviderComponent);\n        };\n      })();\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: AccountSettingsExternalProviderComponent,\n        selectors: [[\"abp-account-settings-external-provider\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: AbstractAccountSettingsService,\n          useClass: AccountExternalProviderService\n        }]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[\"host\", \"\"], [\"propertySetsTemplate\", \"\"], [\"singlePropertySetTemplate\", \"\"], [4, \"ngIf\"], [3, \"keyup.enter\"], [4, \"ngFor\", \"ngForOf\"], [\"iconClass\", \"fa fa-save\", 3, \"click\", \"loading\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-check-label\", 3, \"for\"], [1, \"collapse\"], [4, \"ngTemplateOutlet\"], [1, \"my-4\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"autofocus\", \"\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-check-label\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"mb-3\"], [1, \"form-label\"], [1, \"form-control\", 3, \"ngModelChange\", \"type\", \"ngModel\"]],\n        template: function AccountSettingsExternalProviderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AccountSettingsExternalProviderComponent_ng_container_0_Template, 7, 5, \"ng-container\", 3);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.settings$));\n          }\n        },\n        dependencies: [i1$1.NgForOf, i1$1.NgIf, i1$1.NgTemplateOutlet, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgModel, i1.AutofocusDirective, i4.ButtonComponent, i1$1.AsyncPipe, i1.LocalizationPipe],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return AccountSettingsExternalProviderComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccountSettingsComponent = /*#__PURE__*/(() => {\n  class AccountSettingsComponent {\n    constructor(configStateService, sessionStateService, captchaService, externalProviderService) {\n      this.configStateService = configStateService;\n      this.sessionStateService = sessionStateService;\n      this.captchaService = captchaService;\n      this.externalProviderService = externalProviderService;\n    }\n    ngOnInit() {\n      this.isTwoFactorSettingsEnabled = this.configStateService.getFeature('Identity.TwoFactor') === eTwoFactorBehaviour[eTwoFactorBehaviour.Optional];\n      this.isExternalProviderExists$ = this.externalProviderService.getSettings().pipe(map(data => data?.settings?.length > 0));\n      this.isTenant = this.sessionStateService.getTenant()?.isAvailable;\n      if (this.isTenant) {\n        this.isExternalProviderEnabled$ = this.externalProviderService.getSettings().pipe(map(result => result.settings.some(settings => settings.enabled)));\n        this.isCaptchaEnabled$ = this.captchaService.getSettings().pipe(map(result => result.useCaptchaOnLogin || result.useCaptchaOnRegistration));\n      } else {\n        this.isExternalProviderEnabled$ = of(true);\n        this.isCaptchaEnabled$ = of(true);\n      }\n    }\n    static {\n      this.ɵfac = function AccountSettingsComponent_Factory(t) {\n        return new (t || AccountSettingsComponent)(i0.ɵɵdirectiveInject(i1.ConfigStateService), i0.ɵɵdirectiveInject(i1.SessionStateService), i0.ɵɵdirectiveInject(AccountCaptchaService), i0.ɵɵdirectiveInject(AccountExternalProviderService));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: AccountSettingsComponent,\n        selectors: [[\"abp-account-settings\"]],\n        features: [i0.ɵɵProvidersFeature([AccountExternalProviderService, AccountCaptchaService])],\n        decls: 15,\n        vars: 11,\n        consts: [[\"nav\", \"ngbNav\"], [\"id\", \"AccountSettingsForm\", 1, \"row\", \"abp-md-form\"], [1, \"col-md-12\"], [\"ngbNav\", \"\", 1, \"nav-tabs\"], [\"ngbNavItem\", \"\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"ngbNavOutlet\"], [3, \"isTenant\"]],\n        template: function AccountSettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"ul\", 3, 0)(4, \"li\", 4)(5, \"a\", 5);\n            i0.ɵɵtext(6);\n            i0.ɵɵpipe(7, \"abpLocalization\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, AccountSettingsComponent_ng_template_8_Template, 1, 1, \"ng-template\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(9, AccountSettingsComponent_li_9_Template, 5, 3, \"li\", 7)(10, AccountSettingsComponent_li_10_Template, 5, 3, \"li\", 7);\n            i0.ɵɵpipe(11, \"async\");\n            i0.ɵɵtemplate(12, AccountSettingsComponent_ng_container_12_Template, 3, 3, \"ng-container\", 8);\n            i0.ɵɵpipe(13, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"div\", 9);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const nav_r2 = i0.ɵɵreference(3);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 5, \"AbpAccount::AccountSettingsGeneral\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isTwoFactorSettingsEnabled);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(11, 7, ctx.isCaptchaEnabled$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(13, 9, ctx.isExternalProviderEnabled$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngbNavOutlet\", nav_r2);\n          }\n        },\n        dependencies: [i1$1.NgIf, i5.NgbNavContent, i5.NgbNav, i5.NgbNavItem, i5.NgbNavItemRole, i5.NgbNavLink, i5.NgbNavLinkBase, i5.NgbNavOutlet, AccountSettingsGeneralComponent, AccountSettingsTwoFactorComponent, AccountSettingsCaptchaComponent, AccountSettingsExternalProviderComponent, i1$1.AsyncPipe, i1.LocalizationPipe],\n        encapsulation: 2\n      });\n    }\n  }\n  return AccountSettingsComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst components = [AbstractAccountSettingsComponent, AccountSettingsComponent, AccountSettingsGeneralComponent, AccountSettingsTwoFactorComponent, AccountSettingsCaptchaComponent, AccountSettingsExternalProviderComponent];\nlet AccountSettingsModule = /*#__PURE__*/(() => {\n  class AccountSettingsModule {\n    static {\n      this.ɵfac = function AccountSettingsModule_Factory(t) {\n        return new (t || AccountSettingsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: AccountSettingsModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CoreModule, ThemeSharedModule, NgbNavModule, NgxValidateCoreModule]\n      });\n    }\n  }\n  return AccountSettingsModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TimeZoneSettingComponent = /*#__PURE__*/(() => {\n  class TimeZoneSettingComponent {\n    constructor() {\n      this.timeZoneSettingsService = inject(TimeZoneSettingsService);\n      this.subscription = inject(SubscriptionService);\n      this.toasterService = inject(ToasterService);\n      this.configState = inject(ConfigStateService);\n      this.fb = inject(FormBuilder);\n      this.track = inject(TrackByService);\n      this.timezoneForm = this.fb.group({\n        selectedTimeZone: ['UTC']\n      });\n      this.loading = false;\n      this.init();\n    }\n    init() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.selectedTimeZone = yield firstValueFrom(_this.timeZoneSettingsService.get());\n        _this._timeZones = _this.timeZoneSettingsService.getTimezones();\n      })();\n    }\n    get timeZones() {\n      return this._timeZones;\n    }\n    get selectedTimeZone() {\n      return this.timezoneForm.value.selectedTimeZone;\n    }\n    set selectedTimeZone(val) {\n      this.timezoneForm.setValue({\n        selectedTimeZone: val\n      });\n    }\n    onSubmit() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const value = _this2.selectedTimeZone;\n        _this2.loading = true;\n        yield firstValueFrom(_this2.timeZoneSettingsService.update(value));\n        _this2.loading = false;\n        _this2.toasterService.success('AbpSettingManagement::SuccessfullySaved');\n      })();\n    }\n    static {\n      this.ɵfac = function TimeZoneSettingComponent_Factory(t) {\n        return new (t || TimeZoneSettingComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: TimeZoneSettingComponent,\n        selectors: [[\"abp-time-zone-setting\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([SubscriptionService]), i0.ɵɵStandaloneFeature],\n        decls: 16,\n        vars: 15,\n        consts: [[1, \"py-2\", \"abp-md-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"mb-3\", \"form-group\"], [1, \"form-label\"], [1, \"ms-1\"], [\"formControlName\", \"selectedTimeZone\", 1, \"form-select\", \"form-control\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"iconClass\", \"fa fa-save\", \"buttonType\", \"submit\", 3, \"loading\"], [3, \"ngValue\"]],\n        template: function TimeZoneSettingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"form\", 0);\n            i0.ɵɵlistener(\"ngSubmit\", function TimeZoneSettingComponent_Template_form_ngSubmit_0_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"label\", 2);\n            i0.ɵɵtext(3);\n            i0.ɵɵpipe(4, \"abpLocalization\");\n            i0.ɵɵelement(5, \"span\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"select\", 4);\n            i0.ɵɵtemplate(7, TimeZoneSettingComponent_option_7_Template, 2, 2, \"option\", 5);\n            i0.ɵɵpipe(8, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"small\");\n            i0.ɵɵtext(10);\n            i0.ɵɵpipe(11, \"abpLocalization\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(12, \"hr\");\n            i0.ɵɵelementStart(13, \"abp-button\", 6);\n            i0.ɵɵtext(14);\n            i0.ɵɵpipe(15, \"abpLocalization\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"formGroup\", ctx.timezoneForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 7, \"AbpSettingManagement::Menu:TimeZone\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(8, 9, ctx.timeZones))(\"ngForTrackBy\", ctx.track.by(\"value\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 11, \"AbpSettingManagement::TimezoneHelpText\"), \"\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"loading\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 13, \"AbpUi::Save\"), \" \");\n          }\n        },\n        dependencies: [LocalizationModule, i1.LocalizationPipe, NgFor, ReactiveFormsModule, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, ThemeSharedModule, i4$1.ValidationGroupDirective, i4$1.ValidationDirective, i4.ButtonComponent, AsyncPipe],\n        encapsulation: 2,\n        data: {\n          animation: [collapse]\n        }\n      });\n    }\n  }\n  return TimeZoneSettingComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AbstractAccountSettingsComponent, AbstractAccountSettingsService, AccountAdminModule, AccountCaptchaService, AccountExternalProviderService, AccountSettingsCaptchaComponent, AccountSettingsComponent, AccountSettingsExternalProviderComponent, AccountSettingsGeneralComponent, AccountSettingsModule, AccountSettingsService, AccountSettingsTwoFactorComponent, AccountTwoFactorSettingService, TimeZoneSettingComponent, eTwoFactorBehaviour, twoFactorBehaviourOptions };\n//# sourceMappingURL=volo-abp.ng.account-admin.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}