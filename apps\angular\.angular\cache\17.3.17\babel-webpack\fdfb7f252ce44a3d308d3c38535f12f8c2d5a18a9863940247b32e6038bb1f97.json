{"ast": null, "code": "import { createActionGroup, emptyProps, props } from '@ngrx/store';\nimport { APP } from '@app/shared/constants';\nexport const AppStoreActions = createActionGroup({\n  source: APP,\n  events: {\n    'Is Signed In': props(),\n    // 'Get Check OAuth Access Token': emptyProps(),\n    // 'Set Check OAuth Access Token': props<{ payload: { value: boolean } }>(),\n    // 'Show Header': props<{ payload: { value: boolean } }>(),\n    // 'Show SideNav': props<{ payload: { value: boolean } }>(),\n    'Reset State': emptyProps(),\n    'Global Failure': props()\n  }\n});", "map": {"version": 3, "names": ["createActionGroup", "emptyProps", "props", "APP", "AppStoreActions", "source", "events"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\root-store\\app-store\\actions.ts"], "sourcesContent": ["\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\n\r\nimport { createActionGroup, emptyProps, props } from '@ngrx/store';\r\n\r\nimport { APP } from '@app/shared/constants';\r\n\r\n\r\n\r\nexport const AppStoreActions = createActionGroup({\r\n    source: APP,\r\n    events: {\r\n\r\n        'Is Signed In': props<{ payload: { value: boolean } }>(),\r\n\r\n        // 'Get Check OAuth Access Token': emptyProps(),\r\n        // 'Set Check OAuth Access Token': props<{ payload: { value: boolean } }>(),\r\n\r\n        // 'Show Header': props<{ payload: { value: boolean } }>(),\r\n        // 'Show SideNav': props<{ payload: { value: boolean } }>(),\r\n\r\n\r\n\r\n        'Reset State': emptyProps(),\r\n        'Global Failure': props<{ payload: { error: HttpErrorResponse } }>()\r\n    }\r\n});\r\n"], "mappings": "AAGA,SAASA,iBAAiB,EAAEC,UAAU,EAAEC,KAAK,QAAQ,aAAa;AAElE,SAASC,GAAG,QAAQ,uBAAuB;AAI3C,OAAO,MAAMC,eAAe,GAAGJ,iBAAiB,CAAC;EAC7CK,MAAM,EAAEF,GAAG;EACXG,MAAM,EAAE;IAEJ,cAAc,EAAEJ,KAAK,EAAmC;IAExD;IACA;IAEA;IACA;IAIA,aAAa,EAAED,UAAU,EAAE;IAC3B,gBAAgB,EAAEC,KAAK;;CAE9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}