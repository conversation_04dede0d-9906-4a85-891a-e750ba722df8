{"ast": null, "code": "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Italian locale.\n * @language Italian\n * @iso-639-2 ita\n * <AUTHOR> [@albertorestifo]{@link https://github.com/albertorestifo}\n * <AUTHOR> [@giofilo]{@link https://github.com/giofilo}\n * <AUTHOR> [@vin-car]{@link https://github.com/vin-car}\n */\nvar locale = {\n  code: 'it',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}