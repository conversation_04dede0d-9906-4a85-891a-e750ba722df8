{"ast": null, "code": "// Same as fr\nimport formatDistance from \"../fr/_lib/formatDistance/index.js\";\nimport localize from \"../fr/_lib/localize/index.js\";\nimport match from \"../fr/_lib/match/index.js\";\n// Unique for fr-CH\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale (Switzerland).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n * <AUTHOR> [@vanvuongngo]{@link https://github.com/vanvuongngo}\n * <AUTHOR> [@dcbn]{@link https://github.com/dcbn}\n */\nvar locale = {\n  code: 'fr-CH',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}