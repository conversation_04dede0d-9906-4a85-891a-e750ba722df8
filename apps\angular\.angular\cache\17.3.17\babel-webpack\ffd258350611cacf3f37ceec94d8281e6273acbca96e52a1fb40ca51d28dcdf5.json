{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"yo\", [[\"Àárọ̀\", \"Ọ̀sán\"], u, u], u, [[\"À\", \"A\", \"Ì\", \"Ọ\", \"Ọ\", \"Ẹ\", \"À\"], [\"Àìk\", \"Aj\", \"Ìsẹ́g\", \"Ọjọ́r\", \"Ọjọ́b\", \"Ẹt\", \"Àbám\"], [\"Ọjọ́ Àìkú\", \"Ọjọ́ Ajé\", \"Ọjọ́ Ìsẹ́gun\", \"Ọjọ́rú\", \"Ọjọ́bọ\", \"Ọjọ́ Ẹtì\", \"Ọjọ́ Àbámẹ́ta\"], [\"Àìk\", \"Aj\", \"Ìsẹ́g\", \"Ọjọ́r\", \"Ọjọ́b\", \"Ẹt\", \"Àbám\"]], [[\"À\", \"A\", \"Ì\", \"Ọ\", \"Ọ\", \"Ẹ\", \"À\"], [\"Àìk\", \"Aj\", \"Ìsẹ́g\", \"Ọjọ́r\", \"Ọjọ́b\", \"Ẹt\", \"Àbám\"], [\"Àìkú\", \"Ajé\", \"Ìsẹ́gun\", \"Ọjọ́rú\", \"Ọjọ́bọ\", \"Ẹtì\", \"Àbámẹ́ta\"], [\"Àìk\", \"Aj\", \"Ìsẹ́g\", \"Ọjọ́r\", \"Ọjọ́b\", \"Ẹt\", \"Àbám\"]], [[\"S\", \"È\", \"Ẹ\", \"Ì\", \"Ẹ̀\", \"Ò\", \"A\", \"Ò\", \"O\", \"Ọ̀\", \"B\", \"Ọ̀\"], [\"Ṣẹ́r\", \"Èrèl\", \"Ẹrẹ̀n\", \"Ìgb\", \"Ẹ̀bi\", \"Òkú\", \"Agẹ\", \"Ògú\", \"Owe\", \"Ọ̀wà\", \"Bél\", \"Ọ̀pẹ\"], [\"Oṣù Ṣẹ́rẹ́\", \"Oṣù Èrèlè\", \"Oṣù Ẹrẹ̀nà\", \"Oṣù Ìgbé\", \"Oṣù Ẹ̀bibi\", \"Oṣù Òkúdu\", \"Oṣù Agẹmọ\", \"Oṣù Ògún\", \"Oṣù Owewe\", \"Oṣù Ọ̀wàrà\", \"Oṣù Bélú\", \"Oṣù Ọ̀pẹ̀\"]], [[\"S\", \"È\", \"Ẹ\", \"Ì\", \"Ẹ̀\", \"Ò\", \"A\", \"Ò\", \"O\", \"Ọ̀\", \"B\", \"Ọ̀\"], [\"Ṣẹ́\", \"Èr\", \"Ẹr\", \"Ìg\", \"Ẹ̀b\", \"Òk\", \"Ag\", \"Òg\", \"Ow\", \"Ọ̀w\", \"Bé\", \"Ọ̀p\"], [\"Ṣẹ́rẹ́\", \"Èrèlè\", \"Ẹrẹ̀nà\", \"Ìgbé\", \"Ẹ̀bibi\", \"Òkúdu\", \"Agẹmọ\", \"Ògún\", \"Owewe\", \"Ọ̀wàrà\", \"Bélú\", \"Ọ̀pẹ̀\"]], [[\"BCE\", \"AD\"], u, [\"Saju Kristi\", \"Lehin Kristi\"]], 1, [6, 0], [\"d/M/y\", \"d MM y\", \"d MMM y\", \"EEEE, d MMM y\"], [\"H:m\", \"H:m:s\", \"H:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"NGN\", \"₦\", \"Náírà Nàìjíríà\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"NGN\": [\"₦\"],\n  \"RUB\": [\"₽\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}