{"ast": null, "code": "import { ExchangeReason, InformationExchangeStatus } from '../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/shared/constants/cts-package-request';\nexport const TIMEOUT_SECONDS = 3600; /** default timeout in seconds (30 minutes = 1800) */\nexport const EMPTY_USER = {\n  isAuthenticated: false,\n  id: '',\n  tenantId: '',\n  userName: '',\n  name: '',\n  surName: '',\n  email: '',\n  emailVerified: false,\n  phoneNumber: '',\n  phoneNumberVerified: false,\n  roles: []\n};\nexport const SEARCH_MAX_COUNT = 20;\nexport const DATE_TIME_FORMAT = 'dd/MM/yyyy HH:mm';\nexport const DATE_FORMAT = 'yyyy-MM-dd';\nexport const DATE_YEAR = 'yyyy';\nexport const DATE_HHMM = 'HHmm';\nexport var UploadedDeclarationStatus;\n(function (UploadedDeclarationStatus) {\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"Pending\"] = 0] = \"Pending\";\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"WithWarnings\"] = 1] = \"WithWarnings\";\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"WithErrors\"] = 2] = \"WithErrors\";\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"Invalid\"] = 3] = \"Invalid\";\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"Imported\"] = 4] = \"Imported\";\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"Remaining\"] = 5] = \"Remaining\";\n  UploadedDeclarationStatus[UploadedDeclarationStatus[\"All\"] = -1] = \"All\";\n})(UploadedDeclarationStatus || (UploadedDeclarationStatus = {}));\nexport var EntityPersonType;\n(function (EntityPersonType) {\n  EntityPersonType[EntityPersonType[\"UltimateBO\"] = 0] = \"UltimateBO\";\n  EntityPersonType[EntityPersonType[\"UltimateParent\"] = 1] = \"UltimateParent\";\n  EntityPersonType[EntityPersonType[\"ImmediateParent\"] = 2] = \"ImmediateParent\";\n  EntityPersonType[EntityPersonType[\"ReportableEntity\"] = 3] = \"ReportableEntity\";\n})(EntityPersonType || (EntityPersonType = {}));\nexport const AllEntityPeronStatus = [{\n  value: EntityPersonType.UltimateBO,\n  description: 'Ultimate Beneficial Owner'\n}, {\n  value: EntityPersonType.UltimateParent,\n  description: 'Ultimate Parent'\n}, {\n  value: EntityPersonType.ImmediateParent,\n  description: 'Immediate Parent'\n}, {\n  value: EntityPersonType.ReportableEntity,\n  description: 'Reportable Entity'\n}];\nexport var UBOType;\n(function (UBOType) {\n  UBOType[UBOType[\"LegalPerson\"] = 0] = \"LegalPerson\";\n  UBOType[UBOType[\"LegalPersonOther\"] = 1] = \"LegalPersonOther\";\n  UBOType[UBOType[\"LegalPersonSenior\"] = 2] = \"LegalPersonSenior\";\n  UBOType[UBOType[\"LegalSettlor\"] = 3] = \"LegalSettlor\";\n  UBOType[UBOType[\"LegalTrustee\"] = 4] = \"LegalTrustee\";\n  UBOType[UBOType[\"LegalProtector\"] = 5] = \"LegalProtector\";\n  UBOType[UBOType[\"LegalBeneficiary\"] = 6] = \"LegalBeneficiary\";\n  UBOType[UBOType[\"LegalOther\"] = 7] = \"LegalOther\";\n  UBOType[UBOType[\"LegalSettlorEquivalent\"] = 8] = \"LegalSettlorEquivalent\";\n  UBOType[UBOType[\"LegalTrusteeEquivalent\"] = 9] = \"LegalTrusteeEquivalent\";\n  UBOType[UBOType[\"LegalProtectorEquivalent\"] = 10] = \"LegalProtectorEquivalent\";\n  UBOType[UBOType[\"LegalBeneficiaryEquivalent\"] = 11] = \"LegalBeneficiaryEquivalent\";\n  UBOType[UBOType[\"LegalOtherEquivalent\"] = 12] = \"LegalOtherEquivalent\";\n})(UBOType || (UBOType = {}));\n/**\n * Collection of UBOType enum values with description.\n * Work for get UBO Type descriptin by enum value.\n */\nexport const AllUBOStatus = [{\n  value: UBOType.LegalPerson,\n  description: 'Legal person–ownership'\n}, {\n  value: UBOType.LegalPersonOther,\n  description: 'Legal person–other means'\n}, {\n  value: UBOType.LegalPersonSenior,\n  description: 'Legal person–senior managing official'\n}, {\n  value: UBOType.LegalSettlor,\n  description: 'Legal arrangement–trust–settlor'\n}, {\n  value: UBOType.LegalTrustee,\n  description: 'Legal arrangement–trust–trustee'\n}, {\n  value: UBOType.LegalProtector,\n  description: 'Legal arrangement–trust–protector'\n}, {\n  value: UBOType.LegalBeneficiary,\n  description: 'Legal arrangement–trust–beneficiary'\n}, {\n  value: UBOType.LegalOther,\n  description: 'Legal arrangement–trust–other'\n}, {\n  value: UBOType.LegalSettlorEquivalent,\n  description: 'Legal arrangement–other-settlor-equivalent'\n}, {\n  value: UBOType.LegalTrusteeEquivalent,\n  description: 'Legal arrangement–other–trustee-equivalent'\n}, {\n  value: UBOType.LegalProtectorEquivalent,\n  description: 'Legal arrangement–other–protector-equivalent'\n}, {\n  value: UBOType.LegalBeneficiaryEquivalent,\n  description: 'Legal arrangement–other–beneficiary-equivalent'\n}, {\n  value: UBOType.LegalOtherEquivalent,\n  description: 'Legal arrangement–other–other-equivalent'\n}];\nexport const ExchangeReasonDic = [{\n  value: ExchangeReason.HighRisk,\n  description: 'High Risk'\n}, {\n  value: ExchangeReason.NonCompliance,\n  description: 'Non-compliance'\n}, {\n  value: ExchangeReason.NonResidence,\n  description: 'Non-resident'\n}, {\n  value: ExchangeReason.OtherCases,\n  description: 'Other Cases'\n}];\nexport const InformationExchangeStatusDic = [{\n  value: InformationExchangeStatus.None,\n  description: 'All'\n}, {\n  value: InformationExchangeStatus.NotStarted,\n  description: 'Not Started'\n}, {\n  value: InformationExchangeStatus.NotRequired,\n  description: 'Not Required'\n}, {\n  value: InformationExchangeStatus.WaitingForAppeal,\n  description: 'Waiting for Appeal'\n}, {\n  value: InformationExchangeStatus.ReadyForExchange,\n  description: 'Ready for Exchange'\n}, {\n  value: InformationExchangeStatus.InformationExchanged,\n  description: 'Data Packet Generated'\n}, {\n  value: InformationExchangeStatus.WaitingReview,\n  description: 'Waiting for Review'\n}];\nexport const CTSUploadStatusDic = [{\n  value: -1,\n  description: 'All'\n}, {\n  value: CTSUploadStatus.ReceivingCountryNotEnrolled,\n  description: 'Receiving Country Not Enrolled'\n}, {\n  value: CTSUploadStatus.DataPacketGenerationFailed,\n  description: 'Data Packet Generation Failed'\n}, {\n  value: CTSUploadStatus.DoNotUpload,\n  description: 'Do Not Upload'\n}, {\n  value: CTSUploadStatus.NotStarted,\n  description: 'Not Started'\n}, {\n  value: CTSUploadStatus.Uploading,\n  description: 'Uploading'\n}, {\n  value: CTSUploadStatus.Uploaded,\n  description: 'Uploaded'\n}, {\n  value: CTSUploadStatus.UploadFailed,\n  description: 'Upload Failed'\n}, {\n  value: CTSUploadStatus.RegenerationInProgress,\n  description: 'Regeneration in Progress'\n}];\nexport const CTSUploadExchangeReasonDic = [{\n  value: -1,\n  description: 'All'\n}, {\n  value: ExchangeReason.HighRisk,\n  description: 'High Risk'\n}, {\n  value: ExchangeReason.NonCompliance,\n  description: 'Non-compliance'\n}, {\n  value: ExchangeReason.NonResidence,\n  description: 'Non-resident'\n}, {\n  value: ExchangeReason.OtherCases,\n  description: 'Other Cases'\n}];", "map": {"version": 3, "names": ["ExchangeReason", "InformationExchangeStatus", "CTSUploadStatus", "TIMEOUT_SECONDS", "EMPTY_USER", "isAuthenticated", "id", "tenantId", "userName", "name", "surName", "email", "emailVerified", "phoneNumber", "phoneNumberVerified", "roles", "SEARCH_MAX_COUNT", "DATE_TIME_FORMAT", "DATE_FORMAT", "DATE_YEAR", "DATE_HHMM", "UploadedDeclarationStatus", "EntityPersonType", "AllEntityPeronStatus", "value", "UltimateBO", "description", "UltimateParent", "ImmediateParent", "ReportableEntity", "UBOType", "AllUBOStatus", "<PERSON><PERSON><PERSON>", "LegalPersonOther", "Legal<PERSON>erson<PERSON><PERSON><PERSON>", "LegalSettlor", "LegalT<PERSON>ee", "LegalProtector", "LegalBeneficiary", "LegalOther", "LegalSettlorEquivalent", "LegalTrusteeEquivalent", "LegalProtectorEquivalent", "LegalBeneficiaryEquivalent", "LegalOtherEquivalent", "ExchangeReasonDic", "HighRisk", "NonCompliance", "NonResidence", "OtherCases", "InformationExchangeStatusDic", "None", "NotStarted", "NotRequired", "Waiting<PERSON>or<PERSON><PERSON><PERSON>", "ReadyForExchange", "InformationExchanged", "WaitingReview", "CTSUploadStatusDic", "ReceivingCountryNotEnrolled", "DataPacketGenerationFailed", "DoNotUpload", "Uploading", "Uploaded", "UploadFailed", "RegenerationInProgress", "CTSUploadExchangeReasonDic"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\constants\\general.constants.ts"], "sourcesContent": ["import { EventType } from '@angular/router';\r\nimport {\r\n  ExchangeReason,\r\n  InformationExchangeStatus,\r\n} from '../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\r\nimport { IUser } from '../interfaces';\r\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/shared/constants/cts-package-request';\r\n\r\nexport const TIMEOUT_SECONDS = 3600; /** default timeout in seconds (30 minutes = 1800) */\r\n\r\nexport const EMPTY_USER: IUser = {\r\n  isAuthenticated: false,\r\n  id: '',\r\n  tenantId: '',\r\n  userName: '',\r\n  name: '',\r\n  surName: '',\r\n  email: '',\r\n  emailVerified: false,\r\n  phoneNumber: '',\r\n  phoneNumberVerified: false,\r\n  roles: [],\r\n};\r\n\r\nexport const SEARCH_MAX_COUNT = 20;\r\n\r\nexport const DATE_TIME_FORMAT = 'dd/MM/yyyy HH:mm';\r\nexport const DATE_FORMAT = 'yyyy-MM-dd';\r\nexport const DATE_YEAR = 'yyyy';\r\nexport const DATE_HHMM = 'HHmm';\r\n\r\nexport const enum DialogStatus {\r\n  SUCCESS = 'success',\r\n  CLOSED = 'closed',\r\n  FAILED = 'fail',\r\n}\r\nexport const enum ResponseStatus {\r\n  SUCCESS = 'success',\r\n  FAILED = 'fail',\r\n}\r\n\r\nexport const enum Permissions {\r\n  ES_SERVICE_TEMPLATES = 'EsService.Templates',\r\n  ABPIDENTITY = 'AbpIdentity.Roles || AbpIdentity.Users || AuditLogging.AuditLogs || AbpIdentity.SettingManagement || TextTemplateManagement.TextTemplates',\r\n  ABPIDENTITY_ROLES = 'AbpIdentity.Roles',\r\n  ABPIDENTITY_USERS = 'AbpIdentity.Users',\r\n  AUDITLOGGING_AUDITLOGS = 'AuditLogging.AuditLogs',\r\n  ABPIDENTITY_SETTINGS = 'AbpIdentity.SettingManagement',\r\n  ABPIDENTITY_TEXTTEMPLATES = 'TextTemplateManagement.TextTemplates',\r\n  DECLARATION_IMPORT = 'EsService.DeclarationImportFile',\r\n  DECLARATION_IMPORT_FILE = 'EsService.DeclarationImportFile.Import',\r\n  DECLARATION_IMPORT_DELETE = 'EsService.DeclarationImport.Delete',\r\n  DECLARATION_IMPORT_SUBMIT = 'EsService.DeclarationImport.Submit',\r\n  DECLARATION_IMPORT_EDIT = 'EsService.DeclarationImport.Edit',\r\n  DASHBOARD_MONITORING_DASHBOARD = 'DashboardService.Dashboard.MonitoringDashboard',\r\n  DASHBOARD_RED_FLAG_EVENTS = 'DashboardService.Dashboard.RedFlagEvents',\r\n  DASHBOARD_MANAGE_RED_FLAG_EVENTS = 'DashboardService.Dashboard.ManageRedFlagSetting',\r\n  DASHBOARD_INFORMATION_EXCHANGE = 'DashboardService.Dashboard.InformationExchange',\r\n  DASHBOARD_SPONTANEOUS_INFORMATION_EXCHANGE_DETAILS = 'DashboardService.Dashboard.SpontaneousInformationExchangeDetails',\r\n  DASHBOARD_GENERATE_XML = 'DashboardService.Dashboard.GenerateXML',\r\n  /** Same setting as server side  EconomicSubstanceServicePermissionDefinitionProvider */\r\n  INFORMATION_EXCHANGE_IMPORT_FIle = 'CAPortal.InformationExchangeImportFile',\r\n  //\r\n  // Note: Below three permissions are applied in UI, corporate with the server side Web Api authentication.\r\n  //\r\n  INFORMATION_EXCHANGE_IMPORT_FILE_IMPORT = 'CAPortal.InformationExchangeImportFile.Import',\r\n  INFORMATION_EXCHANGE_IMPORT_FILE_SUBMIT = 'CAPortal.InformationExchangeImportFile.Submit',\r\n  INFORMATION_EXCHANGE_IMPORT_FILE_DISCARD = 'CAPortal.InformationExchangeImportFile.Discard',\r\n\r\n  ASSESSMENT_ASSIGNUSER = 'CAPortal.DeclarationAssessment.UpdateAssessment',\r\n  ASSESSMENT_ACTION = 'CAPortal.DeclarationAssessment.AssessmentAction',\r\n  ASSESSMENT_REQUIRERESUBMISSION = 'CAPortal.DeclarationAssessment.RequireResubmission',\r\n\r\n  CAPORTAL_REDFLAG_DEFAULT = 'CAPortal.RedFlag',\r\n  CAPORTAL_REDFLAG_VIEW = 'CAPortal.RedFlag.View',\r\n  CAPORTAL_REDFLAG_EDIT = 'CAPortal.RedFlag.Edit',\r\n\r\n  //\r\n  // Same setting as AuditService.PermissionDefinitionProvider in Bdo.Ess.AuditService.Application.Contract.\r\n  // Same as AuditServicePermissions.cs settings.\r\n  // Work for CA Portal now.\r\n  AUDIT_TRAIL_DEFAULT = 'AuditService.AuditTrail',\r\n  AUDIT_TRAIL_SEARCH_OWN = 'AuditService.AuditTrail.SearchOwn',\r\n  AUDIT_TRAIL_SEARCH_ALL = 'AuditService.AuditTrail.SearchAll',\r\n  AUDIT_TRAIL_VIEW_OWN = 'AuditService.AuditTrail.ViewOwn',\r\n  AUDIT_TRAIL_VIEW_ALL = 'AuditService.AuditTrail.ViewAll',\r\n\r\n  CAPORTAL_ASSIGNMENT_LIST_VIEW = 'CAPortal.AssignmentList.ViewAssignmentList',\r\n\r\n  BASIC_SEARCH = 'SearchService.BasicSearch'\r\n}\r\n\r\nexport const enum Features {\r\n  ES_SERVICE_TEMPLATES = 'EsService.Templates',\r\n}\r\n\r\nexport const enum ComponentNames {\r\n  ES_SEARCH = 'ES Search',\r\n  ES_DECLARATION = 'ES Declaration',\r\n  ES_DECLARATION_TEMPLATES = 'Declaration Templates',\r\n  ES_IMPORT = 'ES Import',\r\n  ES_DASHBOARD = 'Dashboard',\r\n  ES_REDFLAGS = 'Red Flags Settings',\r\n  LOGOUT = 'Log out',\r\n  RESOURCE_PAGE = 'Resource Page',\r\n  ES_INFORMATION_EXCHANGE = 'Information Exchange',\r\n  ES_INFORMATION_EXCHANGE_IMPORT = 'Info Exchange Import',\r\n  ES_COMPLIANCE_EMAILS = 'Compliance Emails',\r\n  ES_AUDIT_TRAIL = 'Audit Trail',\r\n  ES_ASSESSMENT_LIST = 'ES Assessment List'\r\n}\r\n\r\nexport const enum RABasicSearchStatus {\r\n  ANY = 'Any',\r\n  NOT_STARTED = 'Not Started',\r\n  DRAFT = 'Draft',\r\n  SUBMITTED = 'Submitted',\r\n  RESUBMITTED = 'Resubmitted',\r\n  REOPENED = 'Reopened',\r\n}\r\n\r\nexport const enum RABasicSearchSortFields {\r\n  ID = 'Id',\r\n  ENTITY_NAME = 'entityName',\r\n  UNIQUE_ID = 'uniqueId',\r\n  INCORP_NUM = 'incorporationNumber',\r\n  INCORP_DATE = 'dateOfIncorporation',\r\n  SUBMISSION_TIME = 'submissionTime',\r\n  STATUS = 'declarationStatus',\r\n  RA_NAME = 'raName',\r\n  ASSESSMENT_STATUS = 'AssessmentStatus',\r\n}\r\n\r\nexport const enum RASearchResultTableColumns {\r\n  ID = 'Id',\r\n  ENTITY_NAME = 'entityName',\r\n  UNIQUE_ID = 'uniqueId',\r\n  INCORP_NUM = 'incorporationNumber',\r\n  INCORP_DATE = 'dateOfIncorporation',\r\n  SUBMISSION_TIME = 'submissionTime',\r\n  STATUS = 'declarationStatus',\r\n  RA_NAME = 'raName',\r\n  ASSESSMENT_STATUS = 'assessmentStatus',\r\n}\r\n\r\n/** Refer to properties in  DeclarationImportFileDto in server side. */\r\nexport const enum ESSImportTableColumns {\r\n  ID = 'id',\r\n  DATE = 'uploadedDateTime',\r\n  FILE_NAME = 'fileName',\r\n  SUBMITTED_BY = 'submitterName',\r\n  STATUS = 'statusName',\r\n  DECLERATIONS_NO = 'numberOfDeclarations',\r\n  DECLERATIONS_WITHERROR = 'numberOfWithErrorDeclarations',\r\n  DECLERATIONS_TO_TRIAGE = 'numberOfToTriageDeclarations',\r\n}\r\n\r\nexport const enum RedFlagsTableColumns {\r\n  ID = 'id',\r\n  CATEGORY = 'category',\r\n  CATEGORY_DESCRIPTION = 'categoryDescription',\r\n  EVENTTYPE = 'eventType',\r\n  EVENTTYPE_DESCRIPTION = 'eventTypeDescription',\r\n  PRIORITY = 'priority',\r\n  DESCRIPTION = 'description',\r\n  PARAM_VALUE = 'paramValue',\r\n  STATUS = 'status',\r\n  TYPE = 'type',\r\n}\r\n\r\nexport const enum ESSImportDetailTableColumns {\r\n  ID = 'fileId',\r\n  ENTITY_UNIQUE_ID = 'entityUniqueId',\r\n  ENTITY_NAME = 'entityName',\r\n  INCORPORATION_NO = 'entityIncorporationNumber',\r\n  FINANCIAL_PERIOD = 'financialPeriodEndDate',\r\n  STATUS = 'status',\r\n}\r\n\r\nexport const enum SortDirection {\r\n  ASCENDING = 'asc',\r\n  DESCENDING = 'desc',\r\n}\r\n\r\nexport const enum StatusId {\r\n  EMPTY_ID = '00000000-0000-0000-0000-000000000000',\r\n}\r\n\r\nexport enum UploadedDeclarationStatus {\r\n  Pending = 0,\r\n  WithWarnings = 1,\r\n  WithErrors = 2,\r\n  Invalid = 3,\r\n  Imported = 4,\r\n  Remaining = 5,\r\n  All = -1,\r\n}\r\n\r\nexport const enum InformationExchangeTableColumns {\r\n  ID = 'id',\r\n  EXCHANGE_REASON = 'exchangeReason',\r\n  RA_CODE = 'raCode',\r\n  ENTITY_NAME = 'entityName',\r\n  INCROP_NUMBER = 'incopFormationNo',\r\n  FINANCIAL_PERIOD = 'financialPeriod',\r\n  DUE_DATE = 'dueDate',\r\n  INFORMATIONEXCH_STATUS = 'informationExchangeStatus',\r\n  VIEW_DECLARATION = 'viewDeclarations',\r\n  XML_DATA = 'xmlData',\r\n  VIEW_HISTORY = 'viewHistory',\r\n}\r\n\r\nexport const enum ExchangeSummaryTableColumns {\r\n  ID = 'id',\r\n  TOTAL_REPORT = 'totalReport',\r\n  TOTAL_RPOERT_SENT = 'totalReportSent',\r\n  TOTAL_RPOERT_READY = 'totalReportReady',\r\n  TOTAL_RPOERT_REVIEW = 'totalReportReview',\r\n  TOTAL_RPOERT_NOT_SENT = 'totalReportNotSent',\r\n}\r\n\r\nexport const enum RecipientDetailsTableColumns {\r\n  ID = 'id',\r\n  PERSON_NAME = 'prsonName',\r\n  JURISDICTION_RESIDENCE = 'jurisdictionResidence',\r\n  ADDRESS = 'address',\r\n  TIN = 'tin',\r\n  OTHER_IDENTIFICATION = 'otherIdentification',\r\n  TYPE_ENTITY = 'typeEntity',\r\n  BO_NATIONALITY = 'boNationality',\r\n  UBO_TYPE = 'uboType',\r\n  ACTION = 'action',\r\n}\r\nexport interface Status {\r\n  value: number;\r\n  description: string;\r\n}\r\n\r\nexport enum EntityPersonType {\r\n  UltimateBO = 0,\r\n  UltimateParent = 1,\r\n  ImmediateParent = 2,\r\n  ReportableEntity = 3,\r\n}\r\n\r\nexport const AllEntityPeronStatus: Status[] = [\r\n  {\r\n    value: EntityPersonType.UltimateBO,\r\n    description: 'Ultimate Beneficial Owner',\r\n  },\r\n  { value: EntityPersonType.UltimateParent, description: 'Ultimate Parent' },\r\n  { value: EntityPersonType.ImmediateParent, description: 'Immediate Parent' },\r\n  {\r\n    value: EntityPersonType.ReportableEntity,\r\n    description: 'Reportable Entity',\r\n  },\r\n];\r\nexport enum UBOType {\r\n  LegalPerson = 0,\r\n  LegalPersonOther = 1,\r\n  LegalPersonSenior = 2,\r\n  LegalSettlor = 3,\r\n  LegalTrustee = 4,\r\n  LegalProtector = 5,\r\n  LegalBeneficiary = 6,\r\n  LegalOther = 7,\r\n  LegalSettlorEquivalent = 8,\r\n  LegalTrusteeEquivalent = 9,\r\n  LegalProtectorEquivalent = 10,\r\n  LegalBeneficiaryEquivalent = 11,\r\n  LegalOtherEquivalent = 12,\r\n}\r\n\r\n/**\r\n * Collection of UBOType enum values with description.\r\n * Work for get UBO Type descriptin by enum value.\r\n */\r\nexport const AllUBOStatus: Status[] = [\r\n  { value: UBOType.LegalPerson, description: 'Legal person–ownership' },\r\n  { value: UBOType.LegalPersonOther, description: 'Legal person–other means' },\r\n  {\r\n    value: UBOType.LegalPersonSenior,\r\n    description: 'Legal person–senior managing official',\r\n  },\r\n  {\r\n    value: UBOType.LegalSettlor,\r\n    description: 'Legal arrangement–trust–settlor',\r\n  },\r\n  {\r\n    value: UBOType.LegalTrustee,\r\n    description: 'Legal arrangement–trust–trustee',\r\n  },\r\n  {\r\n    value: UBOType.LegalProtector,\r\n    description: 'Legal arrangement–trust–protector',\r\n  },\r\n  {\r\n    value: UBOType.LegalBeneficiary,\r\n    description: 'Legal arrangement–trust–beneficiary',\r\n  },\r\n  { value: UBOType.LegalOther, description: 'Legal arrangement–trust–other' },\r\n  {\r\n    value: UBOType.LegalSettlorEquivalent,\r\n    description: 'Legal arrangement–other-settlor-equivalent',\r\n  },\r\n  {\r\n    value: UBOType.LegalTrusteeEquivalent,\r\n    description: 'Legal arrangement–other–trustee-equivalent',\r\n  },\r\n  {\r\n    value: UBOType.LegalProtectorEquivalent,\r\n    description: 'Legal arrangement–other–protector-equivalent',\r\n  },\r\n  {\r\n    value: UBOType.LegalBeneficiaryEquivalent,\r\n    description: 'Legal arrangement–other–beneficiary-equivalent',\r\n  },\r\n  {\r\n    value: UBOType.LegalOtherEquivalent,\r\n    description: 'Legal arrangement–other–other-equivalent',\r\n  },\r\n];\r\n\r\nexport const ExchangeReasonDic: Status[] = [\r\n  { value: ExchangeReason.HighRisk, description: 'High Risk' },\r\n  { value: ExchangeReason.NonCompliance, description: 'Non-compliance' },\r\n  { value: ExchangeReason.NonResidence, description: 'Non-resident' },\r\n  { value: ExchangeReason.OtherCases, description: 'Other Cases' },\r\n];\r\n\r\nexport const InformationExchangeStatusDic: Status[] = [\r\n  { value: InformationExchangeStatus.None, description: 'All' },\r\n  { value: InformationExchangeStatus.NotStarted, description: 'Not Started' },\r\n  { value: InformationExchangeStatus.NotRequired, description: 'Not Required' },\r\n  {\r\n    value: InformationExchangeStatus.WaitingForAppeal,\r\n    description: 'Waiting for Appeal',\r\n  },\r\n  {\r\n    value: InformationExchangeStatus.ReadyForExchange,\r\n    description: 'Ready for Exchange',\r\n  },\r\n  {\r\n    value: InformationExchangeStatus.InformationExchanged,\r\n    description: 'Data Packet Generated',\r\n  },\r\n  {\r\n    value: InformationExchangeStatus.WaitingReview,\r\n    description: 'Waiting for Review',\r\n  },\r\n];\r\n\r\n/** Matching properties in InfoExchangeImportFileDto in server side. */\r\nexport const enum ESSInfoExchangeImportTableColumns {\r\n  ID = 'id',\r\n  UPLOAD_DATE = 'uploadedDateTime',\r\n  FILE_NAME = 'fileName',\r\n  SUBMITTED_BY = 'submitterName',\r\n  STATUS = 'statusName',\r\n  /** Total number of Declarations */\r\n  DECLARATIONS_NO = 'numberOfDeclarations',\r\n  /** Number of Declarations with error */\r\n  DECLARATIONS_WITH_ERROR = 'numberOfWithError',\r\n  /** Number of Declarations without error */\r\n  DECLARATIONS_WITHOUT_ERROR = 'numberOfWithoutError',\r\n\r\n  //NUMBER_OF_ERRORS = 'numberOfErrors',\r\n  ACTIONS = 'actions',\r\n}\r\n\r\nexport const enum ESSInfoExchangeHistoryTableColumns {\r\n  ID = 'id',\r\n  ENTITY_NAME = 'entityName',\r\n  INCORPORATION_NUMBER = 'incorporationNumber',\r\n  STATUS = 'status',\r\n  CREATED_AT = 'createdAt',\r\n  ACTIONS = 'actions',\r\n}\r\n\r\nexport const enum DashboardConstants {\r\n  DASHBOARD = 'dashboard',\r\n  ENTITIES_FILINGS_ASSESSMENT_OVERVIEW = 'Entities, Filings, Assessments Overview',\r\n  INFO_REQUESTED_RECEIVED = 'Information, Requested and Received',\r\n  TAX_RESIDENTS = 'Tax Residents',\r\n  RELEVANT_ACTIVITY_OVERVIEW = 'Relevant Activity Overview',\r\n  OVERVIEW_BY_RA = 'Overview By RA',\r\n  RED_FLAG_LISTING = 'Red Flags',\r\n  STATS_SUMMARY = 'Stats Summary',\r\n  INFO_REQESTED = 'Information Requested',\r\n  INFO_RECEIVED = 'Information Received',\r\n  DUE_DATE_PASSED = 'Due Date Passed',\r\n  AVG_ASSESSMENT_PROCESSING = 'Average Assessment Processing Days',\r\n  AVG_INFO_REQESTED = 'Average Information Requested Days',\r\n  NUM_ASSESSMENTS_FAILED = '# Of assessments failed',\r\n  NUM_ASSESSMENTS_STARTED = '# Of assessments started',\r\n  NUM_ASSESSMENTS_COMPLETED = '# Of assessments completed',\r\n  NUM_FILINGS_OVERDUE = '# Of filings overdue*',\r\n  NUM_ENTITIES = '# Of entities*',\r\n  NUM_FILINGS_SUBMITTED = '# Of filings submitted',\r\n}\r\n\r\nexport const enum DashboardRelevantActOverviewTableColumns {\r\n  ID = 'Id',\r\n  NUM_OF_CLP = 'numberOfCLP',\r\n  NUM_OF_D = 'numberOfD',\r\n  NUM_OF_ELP = 'numberOfELP',\r\n  NUM_OF_F = 'numberOfF',\r\n  NUM_OF_FC = 'numberOfFC',\r\n  NUM_OF_FAILURES = 'numberOfFailures',\r\n  NUM_OF_FILINGS = 'numberOfFilings',\r\n  NUM_OF_IBC = 'numberOfIBC',\r\n  NUM_OF_LP = 'numberOfLP',\r\n  NUM_OF_IF = 'numberOfIF',\r\n  NUM_OF_RESIDENTS = 'numberOfResidents',\r\n  RELEVANT_ACTIVITY = 'relevantActivityName',\r\n  NUM_OF_BAHAMIAN = 'numberOf100Bahamian',\r\n  NUM_OF_INVESTMENT = 'numberOfInvestmentFund',\r\n  NUM_OF_NON_RESIDENT = 'numberOfNonResidents'\r\n}\r\n\r\nexport const enum DashboardOverviewByRaTableColumns {\r\n  ID = 'Id',\r\n  RA_CODE = 'raCode',\r\n  RA_NAME  = 'raName',\r\n  TOTAL_NUM_ENTITIES  = 'numberOfEntities',\r\n  NUM_ES_FILINGS_SUBMITTED  = 'numberOfESFilingsSubmitted',\r\n  PERC_ES_FILINGS_SUBMITTED  = 'percentageOfESFilingsSubmitted',\r\n  NUM_ES_FILINGS_OVERDUE  = 'numberOfEntitiesFilingsOverdue',\r\n  PERC_NON_RESIDENT_COMPANIES  = 'percentageOfNonResidentCompanies',\r\n  PERC_BAHAMIAN_OWNED  = 'percentageOfBahamianOwned',\r\n  PERC_ES_FILINGS_RED_FLAGS  = 'percentageOfESFilingsWithRedFlagEvents',\r\n  NUM_ASSESSMENT_NOT_STARTED  = 'numberOfAssessmentNotStarted',\r\n  NUM_ASSESSMENT_CLOSED  = 'numberOfESAssessmentClosed',\r\n  NUM_ASSESSMENT_COMPLETED  = 'numberOfESAssessmentCompleted',\r\n  PERC_ASSESSMENT_PASSED  = 'percentageOfESAssessmentPassed',\r\n  PERC_ASSESSMENT_FAILED  = 'percentageOfESAssessmentFailed',\r\n}\r\n\r\nexport const enum DashbaordRedFlagsTableColumns { // TO DO replace numbers with actual dto column name\r\n  ID = 'Id',\r\n  DISPLAY_PRIORITY = 'priority',\r\n  CATEGORY  = 'categoryDescription',\r\n  EVENT_TYPE  = 'eventTypeDescription',\r\n  NUM_OF_EVENTS  = 'numOfEvents',\r\n  NUM_OF_ASSESMENTS_NOT_STARTED  = 'numOfAssessmentNotStarted',\r\n  PERC_OF_ASSESMENTS_NOT_STARTED  = 'percentageOfAssessmentNotStarted',\r\n  NUM_OF_ASSESMENTS_COMPLETED  = 'numOfAssessmentCompleted',\r\n  PERC_OF_ASSESSMENTS_PASSED = 'percentageOfAssessmentPassed',\r\n  PERC_OF_ASSESMENTS_FAILED  = 'percentageOfAssessmentFailed',\r\n  NUM_ASSESSMENT_CLOSED  = 'numOfAssessmentClosed',\r\n}\r\n\r\nexport const enum EsAssesmentListTableColumns {\r\n  ID = 'Id',\r\n  ENTITY_NAME = 'entityName',\r\n  RA_NAME = 'raName',\r\n  INCORPORATION_NUMBER = 'incorporationNumber',\r\n  FINANCIAL_PERIOD_END = 'periodEndYear',\r\n  SUBMISSION_TIME = 'submissionTime',\r\n  ASSESSMENT_STATUS = 'assessmentStatusId',\r\n  ASSIGNED_LEVEL = 'assessmentAssignedLevel',\r\n  ASSIGNED_USER = 'assessmentAssignedTo',\r\n  ACTION = 'action'\r\n}\r\n\r\nexport const enum DashboardBarLabelConstants {\r\n  COLOUR = 'grey',\r\n  FONT = 'bold 10px sans-serif',\r\n  ALIGN = 'center',\r\n  ALIGN_LEFT = 'left'\r\n}\r\n\r\nexport const CTSUploadStatusDic: Status[] = [\r\n  { value: -1, description: 'All' },\r\n  { value: CTSUploadStatus.ReceivingCountryNotEnrolled, description: 'Receiving Country Not Enrolled' },\r\n  { value: CTSUploadStatus.DataPacketGenerationFailed, description: 'Data Packet Generation Failed' },\r\n  { value: CTSUploadStatus.DoNotUpload, description: 'Do Not Upload' },\r\n  { value: CTSUploadStatus.NotStarted, description: 'Not Started' },\r\n  { value: CTSUploadStatus.Uploading, description: 'Uploading' },\r\n  { value: CTSUploadStatus.Uploaded, description: 'Uploaded' },\r\n  { value: CTSUploadStatus.UploadFailed, description: 'Upload Failed' },\r\n  { value: CTSUploadStatus.RegenerationInProgress, description: 'Regeneration in Progress' },\r\n];\r\n\r\nexport const CTSUploadExchangeReasonDic: Status[] = [\r\n  { value: -1, description: 'All' },\r\n  { value: ExchangeReason.HighRisk, description: 'High Risk' },\r\n  { value: ExchangeReason.NonCompliance, description: 'Non-compliance' },\r\n  { value: ExchangeReason.NonResidence, description: 'Non-resident' },\r\n  { value: ExchangeReason.OtherCases, description: 'Other Cases' },\r\n];\r\n"], "mappings": "AACA,SACEA,cAAc,EACdC,yBAAyB,QACpB,6GAA6G;AAEpH,SAASC,eAAe,QAAQ,+FAA+F;AAE/H,OAAO,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;AAErC,OAAO,MAAMC,UAAU,GAAU;EAC/BC,eAAe,EAAE,KAAK;EACtBC,EAAE,EAAE,EAAE;EACNC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,EAAE;EACZC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,KAAK,EAAE,EAAE;EACTC,aAAa,EAAE,KAAK;EACpBC,WAAW,EAAE,EAAE;EACfC,mBAAmB,EAAE,KAAK;EAC1BC,KAAK,EAAE;CACR;AAED,OAAO,MAAMC,gBAAgB,GAAG,EAAE;AAElC,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAClD,OAAO,MAAMC,WAAW,GAAG,YAAY;AACvC,OAAO,MAAMC,SAAS,GAAG,MAAM;AAC/B,OAAO,MAAMC,SAAS,GAAG,MAAM;AA+J/B,WAAYC,yBAQX;AARD,WAAYA,yBAAyB;EACnCA,yBAAA,CAAAA,yBAAA,4BAAW;EACXA,yBAAA,CAAAA,yBAAA,sCAAgB;EAChBA,yBAAA,CAAAA,yBAAA,kCAAc;EACdA,yBAAA,CAAAA,yBAAA,4BAAW;EACXA,yBAAA,CAAAA,yBAAA,8BAAY;EACZA,yBAAA,CAAAA,yBAAA,gCAAa;EACbA,yBAAA,CAAAA,yBAAA,qBAAQ;AACV,CAAC,EARWA,yBAAyB,KAAzBA,yBAAyB;AAkDrC,WAAYC,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,kCAAc;EACdA,gBAAA,CAAAA,gBAAA,0CAAkB;EAClBA,gBAAA,CAAAA,gBAAA,4CAAmB;EACnBA,gBAAA,CAAAA,gBAAA,8CAAoB;AACtB,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB;AAO5B,OAAO,MAAMC,oBAAoB,GAAa,CAC5C;EACEC,KAAK,EAAEF,gBAAgB,CAACG,UAAU;EAClCC,WAAW,EAAE;CACd,EACD;EAAEF,KAAK,EAAEF,gBAAgB,CAACK,cAAc;EAAED,WAAW,EAAE;AAAiB,CAAE,EAC1E;EAAEF,KAAK,EAAEF,gBAAgB,CAACM,eAAe;EAAEF,WAAW,EAAE;AAAkB,CAAE,EAC5E;EACEF,KAAK,EAAEF,gBAAgB,CAACO,gBAAgB;EACxCH,WAAW,EAAE;CACd,CACF;AACD,WAAYI,OAcX;AAdD,WAAYA,OAAO;EACjBA,OAAA,CAAAA,OAAA,oCAAe;EACfA,OAAA,CAAAA,OAAA,8CAAoB;EACpBA,OAAA,CAAAA,OAAA,gDAAqB;EACrBA,OAAA,CAAAA,OAAA,sCAAgB;EAChBA,OAAA,CAAAA,OAAA,sCAAgB;EAChBA,OAAA,CAAAA,OAAA,0CAAkB;EAClBA,OAAA,CAAAA,OAAA,8CAAoB;EACpBA,OAAA,CAAAA,OAAA,kCAAc;EACdA,OAAA,CAAAA,OAAA,0DAA0B;EAC1BA,OAAA,CAAAA,OAAA,0DAA0B;EAC1BA,OAAA,CAAAA,OAAA,+DAA6B;EAC7BA,OAAA,CAAAA,OAAA,mEAA+B;EAC/BA,OAAA,CAAAA,OAAA,uDAAyB;AAC3B,CAAC,EAdWA,OAAO,KAAPA,OAAO;AAgBnB;;;;AAIA,OAAO,MAAMC,YAAY,GAAa,CACpC;EAAEP,KAAK,EAAEM,OAAO,CAACE,WAAW;EAAEN,WAAW,EAAE;AAAwB,CAAE,EACrE;EAAEF,KAAK,EAAEM,OAAO,CAACG,gBAAgB;EAAEP,WAAW,EAAE;AAA0B,CAAE,EAC5E;EACEF,KAAK,EAAEM,OAAO,CAACI,iBAAiB;EAChCR,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACK,YAAY;EAC3BT,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACM,YAAY;EAC3BV,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACO,cAAc;EAC7BX,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACQ,gBAAgB;EAC/BZ,WAAW,EAAE;CACd,EACD;EAAEF,KAAK,EAAEM,OAAO,CAACS,UAAU;EAAEb,WAAW,EAAE;AAA+B,CAAE,EAC3E;EACEF,KAAK,EAAEM,OAAO,CAACU,sBAAsB;EACrCd,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACW,sBAAsB;EACrCf,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACY,wBAAwB;EACvChB,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACa,0BAA0B;EACzCjB,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEM,OAAO,CAACc,oBAAoB;EACnClB,WAAW,EAAE;CACd,CACF;AAED,OAAO,MAAMmB,iBAAiB,GAAa,CACzC;EAAErB,KAAK,EAAExB,cAAc,CAAC8C,QAAQ;EAAEpB,WAAW,EAAE;AAAW,CAAE,EAC5D;EAAEF,KAAK,EAAExB,cAAc,CAAC+C,aAAa;EAAErB,WAAW,EAAE;AAAgB,CAAE,EACtE;EAAEF,KAAK,EAAExB,cAAc,CAACgD,YAAY;EAAEtB,WAAW,EAAE;AAAc,CAAE,EACnE;EAAEF,KAAK,EAAExB,cAAc,CAACiD,UAAU;EAAEvB,WAAW,EAAE;AAAa,CAAE,CACjE;AAED,OAAO,MAAMwB,4BAA4B,GAAa,CACpD;EAAE1B,KAAK,EAAEvB,yBAAyB,CAACkD,IAAI;EAAEzB,WAAW,EAAE;AAAK,CAAE,EAC7D;EAAEF,KAAK,EAAEvB,yBAAyB,CAACmD,UAAU;EAAE1B,WAAW,EAAE;AAAa,CAAE,EAC3E;EAAEF,KAAK,EAAEvB,yBAAyB,CAACoD,WAAW;EAAE3B,WAAW,EAAE;AAAc,CAAE,EAC7E;EACEF,KAAK,EAAEvB,yBAAyB,CAACqD,gBAAgB;EACjD5B,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEvB,yBAAyB,CAACsD,gBAAgB;EACjD7B,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEvB,yBAAyB,CAACuD,oBAAoB;EACrD9B,WAAW,EAAE;CACd,EACD;EACEF,KAAK,EAAEvB,yBAAyB,CAACwD,aAAa;EAC9C/B,WAAW,EAAE;CACd,CACF;AA0HD,OAAO,MAAMgC,kBAAkB,GAAa,CAC1C;EAAElC,KAAK,EAAE,CAAC,CAAC;EAAEE,WAAW,EAAE;AAAK,CAAE,EACjC;EAAEF,KAAK,EAAEtB,eAAe,CAACyD,2BAA2B;EAAEjC,WAAW,EAAE;AAAgC,CAAE,EACrG;EAAEF,KAAK,EAAEtB,eAAe,CAAC0D,0BAA0B;EAAElC,WAAW,EAAE;AAA+B,CAAE,EACnG;EAAEF,KAAK,EAAEtB,eAAe,CAAC2D,WAAW;EAAEnC,WAAW,EAAE;AAAe,CAAE,EACpE;EAAEF,KAAK,EAAEtB,eAAe,CAACkD,UAAU;EAAE1B,WAAW,EAAE;AAAa,CAAE,EACjE;EAAEF,KAAK,EAAEtB,eAAe,CAAC4D,SAAS;EAAEpC,WAAW,EAAE;AAAW,CAAE,EAC9D;EAAEF,KAAK,EAAEtB,eAAe,CAAC6D,QAAQ;EAAErC,WAAW,EAAE;AAAU,CAAE,EAC5D;EAAEF,KAAK,EAAEtB,eAAe,CAAC8D,YAAY;EAAEtC,WAAW,EAAE;AAAe,CAAE,EACrE;EAAEF,KAAK,EAAEtB,eAAe,CAAC+D,sBAAsB;EAAEvC,WAAW,EAAE;AAA0B,CAAE,CAC3F;AAED,OAAO,MAAMwC,0BAA0B,GAAa,CAClD;EAAE1C,KAAK,EAAE,CAAC,CAAC;EAAEE,WAAW,EAAE;AAAK,CAAE,EACjC;EAAEF,KAAK,EAAExB,cAAc,CAAC8C,QAAQ;EAAEpB,WAAW,EAAE;AAAW,CAAE,EAC5D;EAAEF,KAAK,EAAExB,cAAc,CAAC+C,aAAa;EAAErB,WAAW,EAAE;AAAgB,CAAE,EACtE;EAAEF,KAAK,EAAExB,cAAc,CAACgD,YAAY;EAAEtB,WAAW,EAAE;AAAc,CAAE,EACnE;EAAEF,KAAK,EAAExB,cAAc,CAACiD,UAAU;EAAEvB,WAAW,EAAE;AAAa,CAAE,CACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}