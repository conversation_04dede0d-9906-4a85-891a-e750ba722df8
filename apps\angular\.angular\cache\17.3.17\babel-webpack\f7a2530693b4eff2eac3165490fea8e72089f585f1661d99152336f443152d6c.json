{"ast": null, "code": "import { FormProp } from '@abp/ng.components/extensible';\nimport { Validators } from '@angular/forms';\nimport { PhoneNumberComponent } from '@app/shared/components';\nexport const DEFAULT_USERS_CREATE_FORM_PROPS = FormProp.createMany([{\n  type: \"string\" /* ePropType.String */,\n  name: 'userName',\n  displayName: 'AbpIdentity::UserName',\n  id: 'user-name',\n  validators: () => {\n    return [Validators.required, Validators.maxLength(256)];\n  },\n  disabled: data => !!data.record.id,\n  readonly: data => !!data.record.id\n}, {\n  type: \"passwordinputgroup\" /* ePropType.PasswordInputGroup */,\n  name: 'password',\n  displayName: 'AbpIdentity::Password',\n  id: 'password',\n  autocomplete: 'new-password',\n  visible: () => false,\n  defaultValue: \"1q2w3E*\"\n  // validators: data => [Validators.required, ...getPasswordValidators({ get: data.getInjected })],\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'name',\n  displayName: 'AbpIdentity::DisplayName:Name',\n  id: 'name',\n  validators: () => [Validators.maxLength(64), Validators.required]\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'surname',\n  displayName: 'AbpIdentity::DisplayName:Surname',\n  id: 'surname',\n  validators: () => [Validators.maxLength(64), Validators.required]\n}, {\n  type: \"email\" /* ePropType.Email */,\n  name: 'email',\n  displayName: 'AbpIdentity::EmailAddress',\n  id: 'email',\n  validators: () => [Validators.required, Validators.maxLength(256), Validators.email]\n}, {\n  type: \"string\" /* ePropType.String */,\n  name: 'phoneNumber',\n  displayName: 'AbpIdentity::PhoneNumber',\n  id: 'phone-number',\n  template: PhoneNumberComponent,\n  validators: () => [Validators.maxLength(16), Validators.required]\n}, {\n  type: \"boolean\" /* ePropType.Boolean */,\n  name: 'isActive',\n  displayName: 'AbpIdentity::DisplayName:IsActive',\n  id: 'active-checkbox',\n  defaultValue: true\n}\n// {\n//   type: ePropType.Boolean,\n//   name: 'shouldChangePasswordOnNextLogin',\n//   displayName: 'AbpIdentity::DisplayName:ShouldChangePasswordOnNextLogin',\n//   id: 'shouldChangePasswordOnNextLogin',\n//   defaultValue: true,\n//   disabled: data => !data.record.id\n// },\n/*{\n  type: ePropType.Boolean,\n  name: 'lockoutEnabled',\n  displayName: 'AbpIdentity::DisplayName:LockoutEnabled',\n  id: 'lockout-checkbox',\n  defaultValue: true,\n},*/]);\nexport const DEFAULT_USERS_EDIT_FORM_PROPS = DEFAULT_USERS_CREATE_FORM_PROPS.filter(prop => prop.name !== 'password');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}