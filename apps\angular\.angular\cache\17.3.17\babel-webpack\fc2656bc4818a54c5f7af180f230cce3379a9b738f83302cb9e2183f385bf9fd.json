{"ast": null, "code": "import { Subject, Subscription, from, EMPTY } from 'rxjs';\nimport { ɵNG_PIPE_DEF, ɵgetLContext, ɵglobal } from '@angular/core';\nimport { mergeMap, takeUntil } from 'rxjs/operators';\nconst NG_PIPE_DEF = ɵNG_PIPE_DEF;\nfunction isPipe(target) {\n  return !!target[NG_PIPE_DEF];\n}\n\n/**\n * Applied to instances and stores `Subject` instance when\n * no custom destroy method is provided.\n */\nconst DESTROY = Symbol('__destroy');\n/**\n * Applied to definitions and informs that class is decorated\n */\nconst DECORATOR_APPLIED = Symbol('__decoratorApplied');\n/**\n * If we use the `untilDestroyed` operator multiple times inside the single\n * instance providing different `destroyMethodName`, then all streams will\n * subscribe to the single subject. If any method is invoked, the subject will\n * emit and all streams will be unsubscribed. We wan't to prevent this behavior,\n * thus we store subjects under different symbols.\n */\nfunction getSymbol(destroyMethodName) {\n  if (typeof destroyMethodName === 'string') {\n    return Symbol(`__destroy__${destroyMethodName}`);\n  } else {\n    return DESTROY;\n  }\n}\nfunction markAsDecorated(type) {\n  // Store this property on the prototype if it's an injectable class, component or directive.\n  // We will be able to handle class extension this way.\n  type.prototype[DECORATOR_APPLIED] = true;\n}\nfunction createSubjectOnTheInstance(instance, symbol) {\n  if (!instance[symbol]) {\n    instance[symbol] = new Subject();\n  }\n}\nfunction completeSubjectOnTheInstance(instance, symbol) {\n  if (instance[symbol]) {\n    instance[symbol].next();\n    instance[symbol].complete();\n    // We also have to re-assign this property thus in the future\n    // we will be able to create new subject on the same instance.\n    instance[symbol] = null;\n  }\n}\nfunction unsubscribe(property) {\n  if (property instanceof Subscription) {\n    property.unsubscribe();\n  }\n}\nfunction unsubscribeIfPropertyIsArrayLike(property) {\n  Array.isArray(property) && property.forEach(unsubscribe);\n}\nfunction decorateNgOnDestroy(ngOnDestroy, options) {\n  return function () {\n    // Invoke the original `ngOnDestroy` if it exists\n    ngOnDestroy && ngOnDestroy.call(this);\n    // It's important to use `this` instead of caching instance\n    // that may lead to memory leaks\n    completeSubjectOnTheInstance(this, getSymbol());\n    // Check if subscriptions are pushed to some array\n    if (options.arrayName) {\n      unsubscribeIfPropertyIsArrayLike(this[options.arrayName]);\n    }\n    // Loop through the properties and find subscriptions\n    if (options.checkProperties) {\n      for (const property in this) {\n        if (options.blackList?.includes(property)) {\n          continue;\n        }\n        unsubscribe(this[property]);\n      }\n    }\n  };\n}\nfunction decorateProviderDirectiveOrComponent(type, options) {\n  type.prototype.ngOnDestroy = decorateNgOnDestroy(type.prototype.ngOnDestroy, options);\n}\nfunction decoratePipe(type, options) {\n  const def = type.ɵpipe;\n  def.onDestroy = decorateNgOnDestroy(def.onDestroy, options);\n}\nfunction UntilDestroy(options = {}) {\n  return type => {\n    if (isPipe(type)) {\n      decoratePipe(type, options);\n    } else {\n      decorateProviderDirectiveOrComponent(type, options);\n    }\n    markAsDecorated(type);\n  };\n}\n\n// `LView` is an array where each index matches the specific data structure.\n// The 7th element in an `LView` is an array of cleanup listeners. They are\n// invoked when the view is removed (similar to `ComponentRef.onDestroy`).\nconst CLEANUP = 7;\nconst CheckerHasBeenSet = Symbol('CheckerHasBeenSet');\nfunction setupSubjectUnsubscribedChecker(instance, destroy$) {\n  // This function is used within the `untilDestroyed` operator and setups a function that\n  // listens for the view removal and checks if the `destroy$` subject has any observers (usually `takeUntil`).\n  // Note: this code will not be shipped into production since it's guarded with `ngDevMode`,\n  // this means it'll exist only in development mode.\n  if (instance[CheckerHasBeenSet] || isAngularInTestMode()) {\n    return;\n  }\n  runOutsideAngular(() => from(Promise.resolve()).pipe(mergeMap(() => {\n    let lContext;\n    try {\n      // The `ɵgetLContext` might not work for a pipe, because it's not a component nor a directive,\n      // which means there's no `RNode` for an instance.\n      lContext = ɵgetLContext(instance);\n    } catch {\n      lContext = null;\n    }\n    const lView = lContext?.lView;\n    if (lView == null) {\n      return EMPTY;\n    }\n    const lCleanup = lView[CLEANUP] || (lView[CLEANUP] = []);\n    const cleanupHasBeenExecuted$ = new Subject();\n    // Note: this function is named for debugging purposes.\n    lCleanup.push(function untilDestroyedLCleanup() {\n      // We leave the Angular zone, so RxJS will also call subsequent `next` functions\n      // outside of the Angular zone, which is done to avoid scheduling a microtask (through\n      // `asapScheduler`) within the Angular zone.\n      runOutsideAngular(() => {\n        cleanupHasBeenExecuted$.next();\n        cleanupHasBeenExecuted$.complete();\n      });\n    });\n    return cleanupHasBeenExecuted$;\n  }),\n  // We can't use `observeOn(asapScheduler)` because this might break the app's change detection.\n  // RxJS schedulers coalesce tasks and then flush the queue, which means our task might be scheduled\n  // within the root zone, and then all of the tasks (that were set up by developers in the Angular zone)\n  // will also be flushed in the root zone.\n  mergeMap(() => Promise.resolve())).subscribe(() => {\n    // Note: The `observed` property is available only in RxJS@7.2.0, which will throw\n    // an error in lower versions. We have integration test with RxJS@6 to ensure we don't\n    // import operators from `rxjs`; that's why it's wrapped into braces. The `observers`\n    // property is also being deprecated.\n    const observed = destroy$['observed'] ?? destroy$['observers'].length > 0;\n    if (observed) {\n      console.warn(createMessage(instance));\n    }\n  }));\n  instance[CheckerHasBeenSet] = true;\n}\nfunction isAngularInTestMode() {\n  // Gets whether the code is currently running in a test environment.\n  // We don't use `declare const` because it might cause conflicts with the real typings.\n  return (\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ ||\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine ||\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    typeof jest !== 'undefined' && !!jest ||\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha ||\n    // Jest is not defined in ESM mode since it must be access only by importing from `@jest/globals`.\n    // There's no way to check if we're in Jest ESM mode or not, but we can check if the `process` is defined.\n    // Note: it's required to check for `[object process]` because someone might be running unit tests with\n    // Webpack and shimming `process`.\n    typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]'\n  );\n}\nfunction runOutsideAngular(fn) {\n  // We cannot inject the `NgZone` class when running the checker. The `__ngContext__` is read\n  // for the first time within a microtask which triggers change detection; we want to avoid that.\n  // The `Zone` is always available globally when the `zone.js` is imported. Otherwise, it may be\n  // nooped through bootstrap options. The `NgZone.runOutsideAngular` calls `Zone.root.run`, so we're\n  // safe calling that function directly.\n  const Zone = ɵglobal.Zone;\n  const isNgZoneEnabled = !!Zone && typeof Zone.root?.run === 'function';\n  return isNgZoneEnabled ? Zone.root.run(fn) : fn();\n}\nfunction createMessage(instance) {\n  return `\n  The ${instance.constructor.name} still has subscriptions that haven't been unsubscribed.\n  This may happen if the class extends another class decorated with @UntilDestroy().\n  The child class implements its own ngOnDestroy() method but doesn't call super.ngOnDestroy().\n  Let's look at the following example:\n  @UntilDestroy()\n  @Directive()\n  export abstract class BaseDirective {}\n  @Component({ template: '' })\n  export class ConcreteComponent extends BaseDirective implements OnDestroy {\n    constructor() {\n      super();\n      someObservable$.pipe(untilDestroyed(this)).subscribe();\n    }\n    ngOnDestroy(): void {\n      // Some logic here...\n    }\n  }\n  The BaseDirective.ngOnDestroy() will not be called since Angular will call ngOnDestroy()\n  on the ConcreteComponent, but not on the BaseDirective.\n  One of the solutions is to declare an empty ngOnDestroy method on the BaseDirective:\n  @UntilDestroy()\n  @Directive()\n  export abstract class BaseDirective {\n    ngOnDestroy(): void {}\n  }\n  @Component({ template: '' })\n  export class ConcreteComponent extends BaseDirective implements OnDestroy {\n    constructor() {\n      super();\n      someObservable$.pipe(untilDestroyed(this)).subscribe();\n    }\n    ngOnDestroy(): void {\n      // Some logic here...\n      super.ngOnDestroy();\n    }\n  }\n  `;\n}\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || ngDevMode;\nfunction overrideNonDirectiveInstanceMethod(instance, destroyMethodName, symbol) {\n  const originalDestroy = instance[destroyMethodName];\n  if (NG_DEV_MODE && typeof originalDestroy !== 'function') {\n    throw new Error(`${instance.constructor.name} is using untilDestroyed but doesn't implement ${destroyMethodName}`);\n  }\n  createSubjectOnTheInstance(instance, symbol);\n  instance[destroyMethodName] = function () {\n    // eslint-disable-next-line prefer-rest-params\n    originalDestroy.apply(this, arguments);\n    completeSubjectOnTheInstance(this, symbol);\n    // We have to re-assign this property back to the original value.\n    // If the `untilDestroyed` operator is called for the same instance\n    // multiple times, then we will be able to get the original\n    // method again and not the patched one.\n    instance[destroyMethodName] = originalDestroy;\n  };\n}\nfunction untilDestroyed(instance, destroyMethodName) {\n  return source => {\n    const symbol = getSymbol(destroyMethodName);\n    // If `destroyMethodName` is passed then the developer applies\n    // this operator to something non-related to Angular DI system\n    if (typeof destroyMethodName === 'string') {\n      overrideNonDirectiveInstanceMethod(instance, destroyMethodName, symbol);\n    } else {\n      NG_DEV_MODE && ensureClassIsDecorated(instance);\n      createSubjectOnTheInstance(instance, symbol);\n    }\n    const destroy$ = instance[symbol];\n    NG_DEV_MODE && setupSubjectUnsubscribedChecker(instance, destroy$);\n    return source.pipe(takeUntil(destroy$));\n  };\n}\nfunction ensureClassIsDecorated(instance) {\n  const prototype = Object.getPrototypeOf(instance);\n  const missingDecorator = !(DECORATOR_APPLIED in prototype);\n  if (missingDecorator) {\n    throw new Error('untilDestroyed operator cannot be used inside directives or ' + 'components or providers that are not decorated with UntilDestroy decorator');\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { UntilDestroy, untilDestroyed };", "map": {"version": 3, "names": ["Subject", "Subscription", "from", "EMPTY", "ɵNG_PIPE_DEF", "ɵgetLContext", "ɵglobal", "mergeMap", "takeUntil", "NG_PIPE_DEF", "isPipe", "target", "DESTROY", "Symbol", "DECORATOR_APPLIED", "getSymbol", "destroyMethodName", "markAsDecorated", "type", "prototype", "createSubjectOnTheInstance", "instance", "symbol", "completeSubjectOnTheInstance", "next", "complete", "unsubscribe", "property", "unsubscribeIfPropertyIsArrayLike", "Array", "isArray", "for<PERSON>ach", "decorateNgOnDestroy", "ngOnDestroy", "options", "call", "arrayName", "checkProperties", "blackList", "includes", "decorateProviderDirectiveOrComponent", "decoratePipe", "def", "ɵpipe", "onDestroy", "Until<PERSON><PERSON><PERSON>", "CLEANUP", "CheckerHasBeenSet", "setupSubjectUnsubscribedChecker", "destroy$", "isAngularInTestMode", "runOutsideAngular", "Promise", "resolve", "pipe", "lContext", "lView", "lCleanup", "cleanupHasBeenExecuted$", "push", "untilDestroyed<PERSON>leanup", "subscribe", "observed", "length", "console", "warn", "createMessage", "__karma__", "jasmine", "jest", "<PERSON><PERSON>", "process", "Object", "toString", "fn", "Zone", "isNgZoneEnabled", "root", "run", "constructor", "name", "NG_DEV_MODE", "ngDevMode", "overrideNonDirectiveInstanceMethod", "original<PERSON><PERSON>roy", "Error", "apply", "arguments", "untilDestroyed", "source", "ensureClassIsDecorated", "getPrototypeOf", "missingDecorator"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@ngneat/until-destroy/fesm2020/ngneat-until-destroy.mjs"], "sourcesContent": ["import { Subject, Subscription, from, EMPTY } from 'rxjs';\nimport { ɵNG_PIPE_DEF, ɵgetLContext, ɵglobal } from '@angular/core';\nimport { mergeMap, takeUntil } from 'rxjs/operators';\n\nconst NG_PIPE_DEF = ɵNG_PIPE_DEF;\nfunction isPipe(target) {\n    return !!target[NG_PIPE_DEF];\n}\n\n/**\n * Applied to instances and stores `Subject` instance when\n * no custom destroy method is provided.\n */\nconst DESTROY = Symbol('__destroy');\n/**\n * Applied to definitions and informs that class is decorated\n */\nconst DECORATOR_APPLIED = Symbol('__decoratorApplied');\n/**\n * If we use the `untilDestroyed` operator multiple times inside the single\n * instance providing different `destroyMethodName`, then all streams will\n * subscribe to the single subject. If any method is invoked, the subject will\n * emit and all streams will be unsubscribed. We wan't to prevent this behavior,\n * thus we store subjects under different symbols.\n */\nfunction getSymbol(destroyMethodName) {\n    if (typeof destroyMethodName === 'string') {\n        return Symbol(`__destroy__${destroyMethodName}`);\n    }\n    else {\n        return DESTROY;\n    }\n}\nfunction markAsDecorated(type) {\n    // Store this property on the prototype if it's an injectable class, component or directive.\n    // We will be able to handle class extension this way.\n    type.prototype[DECORATOR_APPLIED] = true;\n}\nfunction createSubjectOnTheInstance(instance, symbol) {\n    if (!instance[symbol]) {\n        instance[symbol] = new Subject();\n    }\n}\nfunction completeSubjectOnTheInstance(instance, symbol) {\n    if (instance[symbol]) {\n        instance[symbol].next();\n        instance[symbol].complete();\n        // We also have to re-assign this property thus in the future\n        // we will be able to create new subject on the same instance.\n        instance[symbol] = null;\n    }\n}\n\nfunction unsubscribe(property) {\n    if (property instanceof Subscription) {\n        property.unsubscribe();\n    }\n}\nfunction unsubscribeIfPropertyIsArrayLike(property) {\n    Array.isArray(property) && property.forEach(unsubscribe);\n}\nfunction decorateNgOnDestroy(ngOnDestroy, options) {\n    return function () {\n        // Invoke the original `ngOnDestroy` if it exists\n        ngOnDestroy && ngOnDestroy.call(this);\n        // It's important to use `this` instead of caching instance\n        // that may lead to memory leaks\n        completeSubjectOnTheInstance(this, getSymbol());\n        // Check if subscriptions are pushed to some array\n        if (options.arrayName) {\n            unsubscribeIfPropertyIsArrayLike(this[options.arrayName]);\n        }\n        // Loop through the properties and find subscriptions\n        if (options.checkProperties) {\n            for (const property in this) {\n                if (options.blackList?.includes(property)) {\n                    continue;\n                }\n                unsubscribe(this[property]);\n            }\n        }\n    };\n}\nfunction decorateProviderDirectiveOrComponent(type, options) {\n    type.prototype.ngOnDestroy = decorateNgOnDestroy(type.prototype.ngOnDestroy, options);\n}\nfunction decoratePipe(type, options) {\n    const def = type.ɵpipe;\n    def.onDestroy = decorateNgOnDestroy(def.onDestroy, options);\n}\nfunction UntilDestroy(options = {}) {\n    return (type) => {\n        if (isPipe(type)) {\n            decoratePipe(type, options);\n        }\n        else {\n            decorateProviderDirectiveOrComponent(type, options);\n        }\n        markAsDecorated(type);\n    };\n}\n\n// `LView` is an array where each index matches the specific data structure.\n// The 7th element in an `LView` is an array of cleanup listeners. They are\n// invoked when the view is removed (similar to `ComponentRef.onDestroy`).\nconst CLEANUP = 7;\nconst CheckerHasBeenSet = Symbol('CheckerHasBeenSet');\nfunction setupSubjectUnsubscribedChecker(instance, destroy$) {\n    // This function is used within the `untilDestroyed` operator and setups a function that\n    // listens for the view removal and checks if the `destroy$` subject has any observers (usually `takeUntil`).\n    // Note: this code will not be shipped into production since it's guarded with `ngDevMode`,\n    // this means it'll exist only in development mode.\n    if (instance[CheckerHasBeenSet] || isAngularInTestMode()) {\n        return;\n    }\n    runOutsideAngular(() => from(Promise.resolve())\n        .pipe(mergeMap(() => {\n        let lContext;\n        try {\n            // The `ɵgetLContext` might not work for a pipe, because it's not a component nor a directive,\n            // which means there's no `RNode` for an instance.\n            lContext = ɵgetLContext(instance);\n        }\n        catch {\n            lContext = null;\n        }\n        const lView = lContext?.lView;\n        if (lView == null) {\n            return EMPTY;\n        }\n        const lCleanup = lView[CLEANUP] || (lView[CLEANUP] = []);\n        const cleanupHasBeenExecuted$ = new Subject();\n        // Note: this function is named for debugging purposes.\n        lCleanup.push(function untilDestroyedLCleanup() {\n            // We leave the Angular zone, so RxJS will also call subsequent `next` functions\n            // outside of the Angular zone, which is done to avoid scheduling a microtask (through\n            // `asapScheduler`) within the Angular zone.\n            runOutsideAngular(() => {\n                cleanupHasBeenExecuted$.next();\n                cleanupHasBeenExecuted$.complete();\n            });\n        });\n        return cleanupHasBeenExecuted$;\n    }), \n    // We can't use `observeOn(asapScheduler)` because this might break the app's change detection.\n    // RxJS schedulers coalesce tasks and then flush the queue, which means our task might be scheduled\n    // within the root zone, and then all of the tasks (that were set up by developers in the Angular zone)\n    // will also be flushed in the root zone.\n    mergeMap(() => Promise.resolve()))\n        .subscribe(() => {\n        // Note: The `observed` property is available only in RxJS@7.2.0, which will throw\n        // an error in lower versions. We have integration test with RxJS@6 to ensure we don't\n        // import operators from `rxjs`; that's why it's wrapped into braces. The `observers`\n        // property is also being deprecated.\n        const observed = destroy$['observed'] ?? destroy$['observers'].length > 0;\n        if (observed) {\n            console.warn(createMessage(instance));\n        }\n    }));\n    instance[CheckerHasBeenSet] = true;\n}\nfunction isAngularInTestMode() {\n    // Gets whether the code is currently running in a test environment.\n    // We don't use `declare const` because it might cause conflicts with the real typings.\n    return (\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        (typeof jasmine !== 'undefined' && !!jasmine) ||\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        (typeof jest !== 'undefined' && !!jest) ||\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        (typeof Mocha !== 'undefined' && !!Mocha) ||\n        // Jest is not defined in ESM mode since it must be access only by importing from `@jest/globals`.\n        // There's no way to check if we're in Jest ESM mode or not, but we can check if the `process` is defined.\n        // Note: it's required to check for `[object process]` because someone might be running unit tests with\n        // Webpack and shimming `process`.\n        (typeof process !== 'undefined' &&\n            Object.prototype.toString.call(process) === '[object process]'));\n}\nfunction runOutsideAngular(fn) {\n    // We cannot inject the `NgZone` class when running the checker. The `__ngContext__` is read\n    // for the first time within a microtask which triggers change detection; we want to avoid that.\n    // The `Zone` is always available globally when the `zone.js` is imported. Otherwise, it may be\n    // nooped through bootstrap options. The `NgZone.runOutsideAngular` calls `Zone.root.run`, so we're\n    // safe calling that function directly.\n    const Zone = ɵglobal.Zone;\n    const isNgZoneEnabled = !!Zone && typeof Zone.root?.run === 'function';\n    return isNgZoneEnabled ? Zone.root.run(fn) : fn();\n}\nfunction createMessage(instance) {\n    return `\n  The ${instance.constructor.name} still has subscriptions that haven't been unsubscribed.\n  This may happen if the class extends another class decorated with @UntilDestroy().\n  The child class implements its own ngOnDestroy() method but doesn't call super.ngOnDestroy().\n  Let's look at the following example:\n  @UntilDestroy()\n  @Directive()\n  export abstract class BaseDirective {}\n  @Component({ template: '' })\n  export class ConcreteComponent extends BaseDirective implements OnDestroy {\n    constructor() {\n      super();\n      someObservable$.pipe(untilDestroyed(this)).subscribe();\n    }\n    ngOnDestroy(): void {\n      // Some logic here...\n    }\n  }\n  The BaseDirective.ngOnDestroy() will not be called since Angular will call ngOnDestroy()\n  on the ConcreteComponent, but not on the BaseDirective.\n  One of the solutions is to declare an empty ngOnDestroy method on the BaseDirective:\n  @UntilDestroy()\n  @Directive()\n  export abstract class BaseDirective {\n    ngOnDestroy(): void {}\n  }\n  @Component({ template: '' })\n  export class ConcreteComponent extends BaseDirective implements OnDestroy {\n    constructor() {\n      super();\n      someObservable$.pipe(untilDestroyed(this)).subscribe();\n    }\n    ngOnDestroy(): void {\n      // Some logic here...\n      super.ngOnDestroy();\n    }\n  }\n  `;\n}\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || ngDevMode;\nfunction overrideNonDirectiveInstanceMethod(instance, destroyMethodName, symbol) {\n    const originalDestroy = instance[destroyMethodName];\n    if (NG_DEV_MODE && typeof originalDestroy !== 'function') {\n        throw new Error(`${instance.constructor.name} is using untilDestroyed but doesn't implement ${destroyMethodName}`);\n    }\n    createSubjectOnTheInstance(instance, symbol);\n    instance[destroyMethodName] = function () {\n        // eslint-disable-next-line prefer-rest-params\n        originalDestroy.apply(this, arguments);\n        completeSubjectOnTheInstance(this, symbol);\n        // We have to re-assign this property back to the original value.\n        // If the `untilDestroyed` operator is called for the same instance\n        // multiple times, then we will be able to get the original\n        // method again and not the patched one.\n        instance[destroyMethodName] = originalDestroy;\n    };\n}\nfunction untilDestroyed(instance, destroyMethodName) {\n    return (source) => {\n        const symbol = getSymbol(destroyMethodName);\n        // If `destroyMethodName` is passed then the developer applies\n        // this operator to something non-related to Angular DI system\n        if (typeof destroyMethodName === 'string') {\n            overrideNonDirectiveInstanceMethod(instance, destroyMethodName, symbol);\n        }\n        else {\n            NG_DEV_MODE && ensureClassIsDecorated(instance);\n            createSubjectOnTheInstance(instance, symbol);\n        }\n        const destroy$ = instance[symbol];\n        NG_DEV_MODE && setupSubjectUnsubscribedChecker(instance, destroy$);\n        return source.pipe(takeUntil(destroy$));\n    };\n}\nfunction ensureClassIsDecorated(instance) {\n    const prototype = Object.getPrototypeOf(instance);\n    const missingDecorator = !(DECORATOR_APPLIED in prototype);\n    if (missingDecorator) {\n        throw new Error('untilDestroyed operator cannot be used inside directives or ' +\n            'components or providers that are not decorated with UntilDestroy decorator');\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { UntilDestroy, untilDestroyed };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AACzD,SAASC,YAAY,EAAEC,YAAY,EAAEC,OAAO,QAAQ,eAAe;AACnE,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAEpD,MAAMC,WAAW,GAAGL,YAAY;AAChC,SAASM,MAAMA,CAACC,MAAM,EAAE;EACpB,OAAO,CAAC,CAACA,MAAM,CAACF,WAAW,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA,MAAMG,OAAO,GAAGC,MAAM,CAAC,WAAW,CAAC;AACnC;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGD,MAAM,CAAC,oBAAoB,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACC,iBAAiB,EAAE;EAClC,IAAI,OAAOA,iBAAiB,KAAK,QAAQ,EAAE;IACvC,OAAOH,MAAM,CAAC,cAAcG,iBAAiB,EAAE,CAAC;EACpD,CAAC,MACI;IACD,OAAOJ,OAAO;EAClB;AACJ;AACA,SAASK,eAAeA,CAACC,IAAI,EAAE;EAC3B;EACA;EACAA,IAAI,CAACC,SAAS,CAACL,iBAAiB,CAAC,GAAG,IAAI;AAC5C;AACA,SAASM,0BAA0BA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAClD,IAAI,CAACD,QAAQ,CAACC,MAAM,CAAC,EAAE;IACnBD,QAAQ,CAACC,MAAM,CAAC,GAAG,IAAItB,OAAO,CAAC,CAAC;EACpC;AACJ;AACA,SAASuB,4BAA4BA,CAACF,QAAQ,EAAEC,MAAM,EAAE;EACpD,IAAID,QAAQ,CAACC,MAAM,CAAC,EAAE;IAClBD,QAAQ,CAACC,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC;IACvBH,QAAQ,CAACC,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC3B;IACA;IACAJ,QAAQ,CAACC,MAAM,CAAC,GAAG,IAAI;EAC3B;AACJ;AAEA,SAASI,WAAWA,CAACC,QAAQ,EAAE;EAC3B,IAAIA,QAAQ,YAAY1B,YAAY,EAAE;IAClC0B,QAAQ,CAACD,WAAW,CAAC,CAAC;EAC1B;AACJ;AACA,SAASE,gCAAgCA,CAACD,QAAQ,EAAE;EAChDE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,IAAIA,QAAQ,CAACI,OAAO,CAACL,WAAW,CAAC;AAC5D;AACA,SAASM,mBAAmBA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/C,OAAO,YAAY;IACf;IACAD,WAAW,IAAIA,WAAW,CAACE,IAAI,CAAC,IAAI,CAAC;IACrC;IACA;IACAZ,4BAA4B,CAAC,IAAI,EAAER,SAAS,CAAC,CAAC,CAAC;IAC/C;IACA,IAAImB,OAAO,CAACE,SAAS,EAAE;MACnBR,gCAAgC,CAAC,IAAI,CAACM,OAAO,CAACE,SAAS,CAAC,CAAC;IAC7D;IACA;IACA,IAAIF,OAAO,CAACG,eAAe,EAAE;MACzB,KAAK,MAAMV,QAAQ,IAAI,IAAI,EAAE;QACzB,IAAIO,OAAO,CAACI,SAAS,EAAEC,QAAQ,CAACZ,QAAQ,CAAC,EAAE;UACvC;QACJ;QACAD,WAAW,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC/B;IACJ;EACJ,CAAC;AACL;AACA,SAASa,oCAAoCA,CAACtB,IAAI,EAAEgB,OAAO,EAAE;EACzDhB,IAAI,CAACC,SAAS,CAACc,WAAW,GAAGD,mBAAmB,CAACd,IAAI,CAACC,SAAS,CAACc,WAAW,EAAEC,OAAO,CAAC;AACzF;AACA,SAASO,YAAYA,CAACvB,IAAI,EAAEgB,OAAO,EAAE;EACjC,MAAMQ,GAAG,GAAGxB,IAAI,CAACyB,KAAK;EACtBD,GAAG,CAACE,SAAS,GAAGZ,mBAAmB,CAACU,GAAG,CAACE,SAAS,EAAEV,OAAO,CAAC;AAC/D;AACA,SAASW,YAAYA,CAACX,OAAO,GAAG,CAAC,CAAC,EAAE;EAChC,OAAQhB,IAAI,IAAK;IACb,IAAIR,MAAM,CAACQ,IAAI,CAAC,EAAE;MACduB,YAAY,CAACvB,IAAI,EAAEgB,OAAO,CAAC;IAC/B,CAAC,MACI;MACDM,oCAAoC,CAACtB,IAAI,EAAEgB,OAAO,CAAC;IACvD;IACAjB,eAAe,CAACC,IAAI,CAAC;EACzB,CAAC;AACL;;AAEA;AACA;AACA;AACA,MAAM4B,OAAO,GAAG,CAAC;AACjB,MAAMC,iBAAiB,GAAGlC,MAAM,CAAC,mBAAmB,CAAC;AACrD,SAASmC,+BAA+BA,CAAC3B,QAAQ,EAAE4B,QAAQ,EAAE;EACzD;EACA;EACA;EACA;EACA,IAAI5B,QAAQ,CAAC0B,iBAAiB,CAAC,IAAIG,mBAAmB,CAAC,CAAC,EAAE;IACtD;EACJ;EACAC,iBAAiB,CAAC,MAAMjD,IAAI,CAACkD,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAC1CC,IAAI,CAAC/C,QAAQ,CAAC,MAAM;IACrB,IAAIgD,QAAQ;IACZ,IAAI;MACA;MACA;MACAA,QAAQ,GAAGlD,YAAY,CAACgB,QAAQ,CAAC;IACrC,CAAC,CACD,MAAM;MACFkC,QAAQ,GAAG,IAAI;IACnB;IACA,MAAMC,KAAK,GAAGD,QAAQ,EAAEC,KAAK;IAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,OAAOrD,KAAK;IAChB;IACA,MAAMsD,QAAQ,GAAGD,KAAK,CAACV,OAAO,CAAC,KAAKU,KAAK,CAACV,OAAO,CAAC,GAAG,EAAE,CAAC;IACxD,MAAMY,uBAAuB,GAAG,IAAI1D,OAAO,CAAC,CAAC;IAC7C;IACAyD,QAAQ,CAACE,IAAI,CAAC,SAASC,sBAAsBA,CAAA,EAAG;MAC5C;MACA;MACA;MACAT,iBAAiB,CAAC,MAAM;QACpBO,uBAAuB,CAAClC,IAAI,CAAC,CAAC;QAC9BkC,uBAAuB,CAACjC,QAAQ,CAAC,CAAC;MACtC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOiC,uBAAuB;EAClC,CAAC,CAAC;EACF;EACA;EACA;EACA;EACAnD,QAAQ,CAAC,MAAM6C,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAC7BQ,SAAS,CAAC,MAAM;IACjB;IACA;IACA;IACA;IACA,MAAMC,QAAQ,GAAGb,QAAQ,CAAC,UAAU,CAAC,IAAIA,QAAQ,CAAC,WAAW,CAAC,CAACc,MAAM,GAAG,CAAC;IACzE,IAAID,QAAQ,EAAE;MACVE,OAAO,CAACC,IAAI,CAACC,aAAa,CAAC7C,QAAQ,CAAC,CAAC;IACzC;EACJ,CAAC,CAAC,CAAC;EACHA,QAAQ,CAAC0B,iBAAiB,CAAC,GAAG,IAAI;AACtC;AACA,SAASG,mBAAmBA,CAAA,EAAG;EAC3B;EACA;EACA;IACA;IACA;IACC,OAAOiB,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;IAC5C;IACA;IACC,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAQ;IAC7C;IACA;IACC,OAAOC,IAAI,KAAK,WAAW,IAAI,CAAC,CAACA,IAAK;IACvC;IACA;IACC,OAAOC,KAAK,KAAK,WAAW,IAAI,CAAC,CAACA,KAAM;IACzC;IACA;IACA;IACA;IACC,OAAOC,OAAO,KAAK,WAAW,IAC3BC,MAAM,CAACrD,SAAS,CAACsD,QAAQ,CAACtC,IAAI,CAACoC,OAAO,CAAC,KAAK;EAAmB;AAC3E;AACA,SAASpB,iBAAiBA,CAACuB,EAAE,EAAE;EAC3B;EACA;EACA;EACA;EACA;EACA,MAAMC,IAAI,GAAGrE,OAAO,CAACqE,IAAI;EACzB,MAAMC,eAAe,GAAG,CAAC,CAACD,IAAI,IAAI,OAAOA,IAAI,CAACE,IAAI,EAAEC,GAAG,KAAK,UAAU;EACtE,OAAOF,eAAe,GAAGD,IAAI,CAACE,IAAI,CAACC,GAAG,CAACJ,EAAE,CAAC,GAAGA,EAAE,CAAC,CAAC;AACrD;AACA,SAASR,aAAaA,CAAC7C,QAAQ,EAAE;EAC7B,OAAO;AACX,QAAQA,QAAQ,CAAC0D,WAAW,CAACC,IAAI;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AAEA,MAAMC,WAAW,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS;AACjE,SAASC,kCAAkCA,CAAC9D,QAAQ,EAAEL,iBAAiB,EAAEM,MAAM,EAAE;EAC7E,MAAM8D,eAAe,GAAG/D,QAAQ,CAACL,iBAAiB,CAAC;EACnD,IAAIiE,WAAW,IAAI,OAAOG,eAAe,KAAK,UAAU,EAAE;IACtD,MAAM,IAAIC,KAAK,CAAC,GAAGhE,QAAQ,CAAC0D,WAAW,CAACC,IAAI,kDAAkDhE,iBAAiB,EAAE,CAAC;EACtH;EACAI,0BAA0B,CAACC,QAAQ,EAAEC,MAAM,CAAC;EAC5CD,QAAQ,CAACL,iBAAiB,CAAC,GAAG,YAAY;IACtC;IACAoE,eAAe,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACtChE,4BAA4B,CAAC,IAAI,EAAED,MAAM,CAAC;IAC1C;IACA;IACA;IACA;IACAD,QAAQ,CAACL,iBAAiB,CAAC,GAAGoE,eAAe;EACjD,CAAC;AACL;AACA,SAASI,cAAcA,CAACnE,QAAQ,EAAEL,iBAAiB,EAAE;EACjD,OAAQyE,MAAM,IAAK;IACf,MAAMnE,MAAM,GAAGP,SAAS,CAACC,iBAAiB,CAAC;IAC3C;IACA;IACA,IAAI,OAAOA,iBAAiB,KAAK,QAAQ,EAAE;MACvCmE,kCAAkC,CAAC9D,QAAQ,EAAEL,iBAAiB,EAAEM,MAAM,CAAC;IAC3E,CAAC,MACI;MACD2D,WAAW,IAAIS,sBAAsB,CAACrE,QAAQ,CAAC;MAC/CD,0BAA0B,CAACC,QAAQ,EAAEC,MAAM,CAAC;IAChD;IACA,MAAM2B,QAAQ,GAAG5B,QAAQ,CAACC,MAAM,CAAC;IACjC2D,WAAW,IAAIjC,+BAA+B,CAAC3B,QAAQ,EAAE4B,QAAQ,CAAC;IAClE,OAAOwC,MAAM,CAACnC,IAAI,CAAC9C,SAAS,CAACyC,QAAQ,CAAC,CAAC;EAC3C,CAAC;AACL;AACA,SAASyC,sBAAsBA,CAACrE,QAAQ,EAAE;EACtC,MAAMF,SAAS,GAAGqD,MAAM,CAACmB,cAAc,CAACtE,QAAQ,CAAC;EACjD,MAAMuE,gBAAgB,GAAG,EAAE9E,iBAAiB,IAAIK,SAAS,CAAC;EAC1D,IAAIyE,gBAAgB,EAAE;IAClB,MAAM,IAAIP,KAAK,CAAC,8DAA8D,GAC1E,4EAA4E,CAAC;EACrF;AACJ;;AAEA;AACA;AACA;;AAEA,SAASxC,YAAY,EAAE2C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}