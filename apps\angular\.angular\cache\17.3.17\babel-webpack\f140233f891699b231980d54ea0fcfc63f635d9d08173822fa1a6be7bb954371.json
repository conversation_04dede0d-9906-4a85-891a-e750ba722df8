{"ast": null, "code": "import { map, tap } from 'rxjs/operators';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { CountryISO } from 'ngx-intl-tel-input';\nimport { PhoneNumberUtil } from 'google-libphonenumber';\nimport { EXTENSIBLE_FORM_VIEW_PROVIDER, EXTENSIONS_FORM_PROP, EXTENSIONS_FORM_PROP_DATA } from '@abp/ng.components/extensible';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@abp/ng.core\";\nimport * as i3 from \"../../../services\";\nimport * as i4 from \"@abp/ng.theme.shared\";\nimport * as i5 from \"@ngx-validate/core\";\nimport * as i6 from \"ngx-intl-tel-input\";\nimport * as i7 from \"@abp/ng.components/extensible\";\nconst _c0 = () => ({\n  size: \"sm\"\n});\nfunction PersonalSettingsPhoneNumberComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"AbpAccount::Verify\"));\n  }\n}\nfunction PersonalSettingsPhoneNumberComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function PersonalSettingsPhoneNumberComponent_ng_template_13_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmPhoneNumber());\n    });\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 4)(6, \"label\", 12);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PersonalSettingsPhoneNumberComponent_ng_template_13_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.token, $event) || (ctx_r2.token = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 3, \"AbpAccount::ConfirmationTokenSentMessage\", ctx_r2.formControl.value), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 6, \"AbpAccount::PhoneConfirmationToken\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.token);\n  }\n}\nfunction PersonalSettingsPhoneNumberComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"abp-button\", 15);\n    i0.ɵɵlistener(\"click\", function PersonalSettingsPhoneNumberComponent_ng_template_15_Template_abp_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmPhoneNumber());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpAccount::Cancel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"AbpAccount::Save\"), \" \");\n  }\n}\nexport class PersonalSettingsPhoneNumberComponent {\n  constructor(formProp, propData, formGroupDirective, configState, accountService, toasterService, manageProfileState, confirmationService, cdr) {\n    this.formProp = formProp;\n    this.propData = propData;\n    this.formGroupDirective = formGroupDirective;\n    this.configState = configState;\n    this.accountService = accountService;\n    this.toasterService = toasterService;\n    this.manageProfileState = manageProfileState;\n    this.confirmationService = confirmationService;\n    this.cdr = cdr;\n    this.phoneNumber = '';\n    this.styles = ['form-control'];\n    this.phoneForm = new FormGroup({\n      internationalPhone: new FormControl(undefined, [Validators.required])\n    });\n    this.preferredCountries = [CountryISO.Bahamas];\n    this.styleOptions = {\n      formControl: \"form-control\",\n      invalid: \"invalid-form\"\n    };\n    this.initPhoneNumberConfirmation = () => {\n      if (this.formControl.invalid) {\n        return;\n      }\n      const phoneNumber = this.formControl.value;\n      const userId = this.userId;\n      this.accountService.sendPhoneNumberConfirmationToken({\n        phoneNumber,\n        userId\n      }).pipe(tap(() => this.token = '')).subscribe(this.openModal);\n    };\n    this.openModal = () => {\n      this.modalVisible = true;\n      this.cdr.detectChanges();\n    };\n    this.removeModal = () => {\n      this.modalVisible = false;\n    };\n    this.setPhoneNumberAsConfirmed = () => {\n      const profile = {\n        ...this.manageProfileState.getProfile(),\n        phoneNumberConfirmed: true\n      };\n      this.manageProfileState.setProfile(profile);\n    };\n    this.name = formProp.name;\n    this.id = formProp.id;\n    this.isVerified = propData.phoneNumberConfirmed;\n    this.displayName = formProp.displayName;\n    this.formGroup = this.formGroupDirective.control;\n    this.formControl = this.formGroup.controls[this.name];\n    this.isEnablePhoneNumberConfirmation = this.getIsEnablePhoneNumberConfirmation();\n    this.initialValue = propData.phoneNumber;\n    this.isValueChanged$ = this.formControl.valueChanges.pipe(map(value => value !== this.initialValue));\n  }\n  ngOnInit() {\n    const phoneNumberUtil = PhoneNumberUtil.getInstance();\n    if (this.initialValue) {\n      this.initialValue = this.initialValue.includes('+') ? this.initialValue : '+' + this.initialValue; // if old data (before country code added ) without + getRegionCodeForNumber has error\n      var phoneObj = {};\n      phoneObj.countryCode = phoneNumberUtil.getRegionCodeForNumber(phoneNumberUtil.parse(this.initialValue));\n      phoneObj.number = phoneNumberUtil.getNationalSignificantNumber(phoneNumberUtil.parse(this.initialValue));\n      this.phoneForm.get(\"internationalPhone\").setValue(phoneObj);\n    }\n    this.phoneForm.get(\"internationalPhone\").valueChanges.subscribe(result => {\n      if (result) {\n        this.formControl.setValue(result.e164Number);\n      }\n    });\n  }\n  getIsEnablePhoneNumberConfirmation() {\n    return this.configState.getSetting('Abp.Identity.SignIn.EnablePhoneNumberConfirmation') === 'True';\n  }\n  get userId() {\n    return this.configState.getDeep('currentUser.id');\n  }\n  confirmPhoneNumber() {\n    this.accountService.confirmPhoneNumber({\n      token: this.token,\n      userId: this.userId\n    }).pipe(tap(this.setPhoneNumberAsConfirmed), tap(this.removeModal)).subscribe(() => {\n      this.toasterService.success('AbpAccount::Verified', '', {\n        life: 5000\n      });\n    });\n  }\n  static {\n    this.ɵfac = function PersonalSettingsPhoneNumberComponent_Factory(t) {\n      return new (t || PersonalSettingsPhoneNumberComponent)(i0.ɵɵdirectiveInject(EXTENSIONS_FORM_PROP), i0.ɵɵdirectiveInject(EXTENSIONS_FORM_PROP_DATA), i0.ɵɵdirectiveInject(i1.FormGroupDirective), i0.ɵɵdirectiveInject(i2.ConfigStateService), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.ToasterService), i0.ɵɵdirectiveInject(i3.ManageProfileStateService), i0.ɵɵdirectiveInject(i4.ConfirmationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalSettingsPhoneNumberComponent,\n      selectors: [[\"abp-personal-settings-phone-number\"]],\n      features: [i0.ɵɵProvidersFeature([], [EXTENSIBLE_FORM_VIEW_PROVIDER])],\n      decls: 17,\n      vars: 18,\n      consts: [[\"f\", \"ngForm\"], [\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [1, \"mb-3\"], [1, \"form-label\"], [\"validationTarget\", \"\", \"validationStyle\", \"\", 1, \"input-group\"], [3, \"formGroup\"], [\"formControlName\", \"internationalPhone\", \"name\", \"internationalPhone\", \"inputId\", \"phone-number\", 3, \"cssClass\", \"preferredCountries\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"searchCountryFlag\", \"selectFirstCountry\", \"maxLength\", \"phoneValidation\", \"separateDialCode\"], [3, \"visibleChange\", \"visible\", \"busy\", \"options\"], [3, \"ngSubmit\"], [1, \"mt-2\"], [\"for\", \"confirm-phone-number\", 1, \"form-label\"], [\"id\", \"confirm-phone-number\", \"name\", \"confirm-phone-number\", \"type\", \"text\", \"autofocus\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"abpClose\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"abp-button\", \"iconClass\", \"fa fa-check\", 3, \"click\"]],\n      template: function PersonalSettingsPhoneNumberComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4)(1, \"label\", 5);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"abpLocalization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵtext(5, \" * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"form\", 7, 0);\n          i0.ɵɵelement(9, \"ngx-intl-tel-input\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"abp-modal\", 9);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function PersonalSettingsPhoneNumberComponent_Template_abp_modal_visibleChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.modalVisible, $event) || (ctx.modalVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(11, PersonalSettingsPhoneNumberComponent_ng_template_11_Template, 3, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(13, PersonalSettingsPhoneNumberComponent_ng_template_13_Template, 10, 8, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(15, PersonalSettingsPhoneNumberComponent_ng_template_15_Template, 6, 6, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"for\", ctx.id);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 15, ctx.displayName));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.phoneForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"cssClass\", ctx.styles)(\"preferredCountries\", ctx.preferredCountries)(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"maxLength\", 15)(\"phoneValidation\", false)(\"separateDialCode\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.modalVisible);\n          i0.ɵɵproperty(\"busy\", ctx.modalBusy)(\"options\", i0.ɵɵpureFunction0(17, _c0));\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.NgForm, i1.FormGroupDirective, i1.FormControlName, i2.AutofocusDirective, i5.ValidationGroupDirective, i5.ValidationStyleDirective, i5.ValidationTargetDirective, i5.ValidationDirective, i4.ButtonComponent, i4.ModalComponent, i4.ModalCloseDirective, i6.NgxIntlTelInputComponent, i6.NativeElementInjectorDirective, i2.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "tap", "FormControl", "FormGroup", "Validators", "CountryISO", "PhoneNumberUtil", "EXTENSIBLE_FORM_VIEW_PROVIDER", "EXTENSIONS_FORM_PROP", "EXTENSIONS_FORM_PROP_DATA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵlistener", "PersonalSettingsPhoneNumberComponent_ng_template_13_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "confirmPhoneNumber", "ɵɵtwoWayListener", "PersonalSettingsPhoneNumberComponent_ng_template_13_Template_input_ngModelChange_9_listener", "$event", "ɵɵtwoWayBindingSet", "token", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "formControl", "value", "ɵɵtwoWayProperty", "PersonalSettingsPhoneNumberComponent_ng_template_15_Template_abp_button_click_3_listener", "_r4", "PersonalSettingsPhoneNumberComponent", "constructor", "formProp", "propData", "formGroupDirective", "configState", "accountService", "toasterService", "manageProfileState", "confirmationService", "cdr", "phoneNumber", "styles", "phoneForm", "internationalPhone", "undefined", "required", "preferredCountries", "Bahamas", "styleOptions", "invalid", "initPhoneNumberConfirmation", "userId", "sendPhoneNumberConfirmationToken", "pipe", "subscribe", "openModal", "modalVisible", "detectChanges", "removeModal", "setPhoneNumberAsConfirmed", "profile", "getProfile", "phoneNumberConfirmed", "setProfile", "name", "id", "isVerified", "displayName", "formGroup", "control", "controls", "isEnablePhoneNumberConfirmation", "getIsEnablePhoneNumberConfirmation", "initialValue", "isValueChanged$", "valueChanges", "ngOnInit", "phoneNumberUtil", "getInstance", "includes", "phoneObj", "countryCode", "getRegionCodeForNumber", "parse", "number", "getNationalSignificantNumber", "get", "setValue", "result", "e164Number", "getSetting", "getDeep", "success", "life", "ɵɵdirectiveInject", "i1", "FormGroupDirective", "i2", "ConfigStateService", "i3", "AccountService", "i4", "ToasterService", "ManageProfileStateService", "ConfirmationService", "ChangeDetectorRef", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "PersonalSettingsPhoneNumberComponent_Template", "rf", "ctx", "ɵɵelement", "PersonalSettingsPhoneNumberComponent_Template_abp_modal_visibleChange_10_listener", "_r1", "ɵɵtemplate", "PersonalSettingsPhoneNumberComponent_ng_template_11_Template", "ɵɵtemplateRefExtractor", "PersonalSettingsPhoneNumberComponent_ng_template_13_Template", "PersonalSettingsPhoneNumberComponent_ng_template_15_Template", "ɵɵproperty", "modalBusy", "ɵɵpureFunction0", "_c0"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\components\\personal-settings\\personal-settings-phone-number\\personal-settings-phone-number.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\public\\src\\components\\personal-settings\\personal-settings-phone-number\\personal-settings-phone-number.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Inject } from '@angular/core';\r\nimport { AbstractControl, UntypedFormGroup, FormGroupDirective } from '@angular/forms';\r\nimport {\r\n  FormProp,\r\n} from '@abp/ng.components/extensible';\r\nimport { ConfigStateService } from '@abp/ng.core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, tap } from 'rxjs/operators';\r\nimport { ConfirmationService, ToasterService } from '@abp/ng.theme.shared';\r\nimport { AccountService, ManageProfileStateService } from '../../../services';\r\nimport { ProfileDto } from '@volo/abp.ng.account/public/proxy';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\n\r\nimport { CountryISO } from 'ngx-intl-tel-input';\r\nimport { PhoneNumberUtil } from 'google-libphonenumber';\r\nimport { EXTENSIBLE_FORM_VIEW_PROVIDER, EXTENSIONS_FORM_PROP, EXTENSIONS_FORM_PROP_DATA } from '@abp/ng.components/extensible';\r\n\r\n@Component({\r\n  selector: 'abp-personal-settings-phone-number',\r\n  templateUrl: './personal-settings-phone-number.component.html',\r\n  viewProviders: [EXTENSIBLE_FORM_VIEW_PROVIDER],\r\n})\r\nexport class PersonalSettingsPhoneNumberComponent {\r\n  public displayName: string;\r\n  public name: string;\r\n  public id: string;\r\n  public isEnablePhoneNumberConfirmation: boolean;\r\n  public initialValue: string;\r\n  public isValueChanged$: Observable<boolean>;\r\n  public isVerified: boolean;\r\n  public modalVisible: boolean;\r\n  public token: string;\r\n  private formGroup: UntypedFormGroup;\r\n  public formControl: AbstractControl;\r\n  modalBusy: boolean;\r\n\r\n  constructor(\r\n    @Inject(EXTENSIONS_FORM_PROP) private formProp: FormProp,\r\n    @Inject(EXTENSIONS_FORM_PROP_DATA) private propData: ProfileDto,\r\n    private formGroupDirective: FormGroupDirective,\r\n    private configState: ConfigStateService,\r\n    private accountService: AccountService,\r\n    private toasterService: ToasterService,\r\n    private manageProfileState: ManageProfileStateService,\r\n    private confirmationService: ConfirmationService,\r\n    private cdr: ChangeDetectorRef,\r\n  ) {\r\n    this.name = formProp.name;\r\n    this.id = formProp.id;\r\n    this.isVerified = propData.phoneNumberConfirmed;\r\n\r\n    this.displayName = formProp.displayName;\r\n    this.formGroup = this.formGroupDirective.control;\r\n    this.formControl = this.formGroup.controls[this.name];\r\n    this.isEnablePhoneNumberConfirmation = this.getIsEnablePhoneNumberConfirmation();\r\n    this.initialValue = propData.phoneNumber;\r\n    this.isValueChanged$ = this.formControl.valueChanges.pipe(\r\n      map(value => value !== this.initialValue),\r\n    );\r\n  }\r\n\r\n  public phoneNumber = '';\r\n  public styles = ['form-control'];\r\n  public phoneForm = new FormGroup({\r\n      internationalPhone: new FormControl(undefined, [Validators.required])\r\n  });\r\n  public preferredCountries = [CountryISO.Bahamas];\r\n\r\n  private styleOptions = { formControl: \"form-control\", invalid: \"invalid-form\" }\r\n\r\n  ngOnInit()\r\n  {\r\n    const phoneNumberUtil = PhoneNumberUtil.getInstance();\r\n\r\n    if(this.initialValue)\r\n    {\r\n      this.initialValue = this.initialValue.includes('+')? this.initialValue : ('+'+ this.initialValue); // if old data (before country code added ) without + getRegionCodeForNumber has error\r\n      var phoneObj = {} as PhoneNumberObject;\r\n      phoneObj.countryCode = phoneNumberUtil.getRegionCodeForNumber(phoneNumberUtil.parse(this.initialValue));\r\n      phoneObj.number = phoneNumberUtil.getNationalSignificantNumber(phoneNumberUtil.parse(this.initialValue));\r\n\r\n      this.phoneForm.get(\"internationalPhone\").setValue(phoneObj);\r\n    }\r\n\r\n    this.phoneForm.get(\"internationalPhone\").valueChanges.subscribe(result => {\r\n      if (result)\r\n      {\r\n        this.formControl.setValue(result.e164Number);\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n\r\n  getIsEnablePhoneNumberConfirmation() {\r\n    return (\r\n      this.configState.getSetting('Abp.Identity.SignIn.EnablePhoneNumberConfirmation') === 'True'\r\n    );\r\n  }\r\n\r\n  get userId(): string {\r\n    return this.configState.getDeep('currentUser.id');\r\n  }\r\n\r\n  initPhoneNumberConfirmation = () => {\r\n    if (this.formControl.invalid) {\r\n      return;\r\n    }\r\n    const phoneNumber = this.formControl.value;\r\n    const userId = this.userId;\r\n    this.accountService\r\n      .sendPhoneNumberConfirmationToken({\r\n        phoneNumber,\r\n        userId,\r\n      })\r\n      .pipe(tap(() => (this.token = '')))\r\n      .subscribe(this.openModal);\r\n  };\r\n\r\n  openModal = () => {\r\n    this.modalVisible = true;\r\n    this.cdr.detectChanges();\r\n  };\r\n\r\n  removeModal = () => {\r\n    this.modalVisible = false;\r\n  };\r\n\r\n  setPhoneNumberAsConfirmed = () => {\r\n    const profile = { ...this.manageProfileState.getProfile(), phoneNumberConfirmed: true };\r\n    this.manageProfileState.setProfile(profile);\r\n  };\r\n\r\n  confirmPhoneNumber() {\r\n    this.accountService\r\n      .confirmPhoneNumber({ token: this.token, userId: this.userId })\r\n      .pipe(tap(this.setPhoneNumberAsConfirmed), tap(this.removeModal))\r\n      .subscribe(() => {\r\n        this.toasterService.success('AbpAccount::Verified', '', { life: 5000 });\r\n      });\r\n  }\r\n}\r\n\r\nexport interface PhoneNumberObject {\r\n  countryCode: string;\r\n  dialCode: string;\r\n  e164Number: string;\r\n  internationalNumber: string;\r\n  nationalNumber: string;\r\n  number: string;\r\n}\r\n", "<div class=\"mb-3\">\r\n  <label [attr.for]=\"id\" class=\"form-label\">{{\r\n    displayName | abpLocalization\r\n    }}</label\r\n  > <span> * </span>\r\n  <div class=\"input-group\" validationTarget validationStyle>\r\n    <form #f=\"ngForm\" [formGroup]=\"phoneForm\">\r\n          <ngx-intl-tel-input\r\n            [cssClass]=\"styles\"\r\n            [preferredCountries] = \"preferredCountries\"\r\n            [enableAutoCountrySelect]=\"true\"\r\n            [enablePlaceholder]=\"true\"\r\n            [searchCountryFlag]=\"true\"\r\n            [selectFirstCountry]=\"false\"\r\n            [maxLength]=\"15\"\r\n            [phoneValidation]=\"false\"\r\n            [separateDialCode]=\"true\"\r\n            formControlName=\"internationalPhone\"\r\n            name=\"internationalPhone\"\r\n            inputId=\"phone-number\"\r\n          ></ngx-intl-tel-input>\r\n    </form>\r\n  </div>\r\n</div>\r\n\r\n\r\n<abp-modal [(visible)]=\"modalVisible\" [busy]=\"modalBusy\" [options]=\"{ size: 'sm' }\">\r\n  <ng-template #abpHeader>\r\n    <h5>{{ 'AbpAccount::Verify' | abpLocalization }}</h5>\r\n  </ng-template>\r\n  <ng-template #abpBody>\r\n    <form (ngSubmit)=\"confirmPhoneNumber()\">\r\n      <div class=\"mt-2\">\r\n        <p>\r\n          {{ 'AbpAccount::ConfirmationTokenSentMessage' | abpLocalization: formControl.value }}\r\n        </p>\r\n        <div class=\"mb-3\">\r\n          <label class=\"form-label\" for=\"confirm-phone-number\">{{\r\n            'AbpAccount::PhoneConfirmationToken' | abpLocalization\r\n            }}</label>\r\n          <input\r\n            [(ngModel)]=\"token\"\r\n            id=\"confirm-phone-number\"\r\n            name=\"confirm-phone-number\"\r\n            class=\"form-control\"\r\n            type=\"text\"\r\n            autofocus\r\n          />\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </ng-template>\r\n  <ng-template #abpFooter>\r\n    <button abpClose type=\"button\" class=\"btn btn-secondary\">\r\n      {{ 'AbpAccount::Cancel' | abpLocalization }}\r\n    </button>\r\n    <abp-button type=\"abp-button\" iconClass=\"fa fa-check\" (click)=\"confirmPhoneNumber()\">\r\n      {{ 'AbpAccount::Save' | abpLocalization }}\r\n    </abp-button>\r\n  </ng-template>\r\n</abp-modal>\r\n"], "mappings": "AAOA,SAASA,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AAIzC,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,6BAA6B,EAAEC,oBAAoB,EAAEC,yBAAyB,QAAQ,+BAA+B;;;;;;;;;;;;;;ICa1HC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA4C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAjDH,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,6BAA4C;;;;;;IAGhDN,EAAA,CAAAC,cAAA,eAAwC;IAAlCD,EAAA,CAAAO,UAAA,sBAAAC,sFAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAYF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAEnCd,EADF,CAAAC,cAAA,cAAkB,QACb;IACDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,aAAkB,gBACqC;IAAAD,EAAA,CAAAE,MAAA,GAEjD;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACZH,EAAA,CAAAC,cAAA,gBAOE;IANAD,EAAA,CAAAe,gBAAA,2BAAAC,4FAAAC,MAAA;MAAAjB,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAkB,kBAAA,CAAAP,MAAA,CAAAQ,KAAA,EAAAF,MAAA,MAAAN,MAAA,CAAAQ,KAAA,GAAAF,MAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAAI,MAAA;IAAA,EAAmB;IAS3BjB,EAVM,CAAAG,YAAA,EAOE,EACE,EACF,EACD;;;;IAhBDH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAqB,WAAA,mDAAAV,MAAA,CAAAW,WAAA,CAAAC,KAAA,OACF;IAEuDvB,EAAA,CAAAI,SAAA,GAEjD;IAFiDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,6CAEjD;IAEFN,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAwB,gBAAA,YAAAb,MAAA,CAAAQ,KAAA,CAAmB;;;;;;IAY3BnB,EAAA,CAAAC,cAAA,iBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,qBAAqF;IAA/BD,EAAA,CAAAO,UAAA,mBAAAkB,yFAAA;MAAAzB,EAAA,CAAAS,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAClFd,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;IAJXH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAM,WAAA,kCACF;IAEEN,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAM,WAAA,gCACF;;;ADpCJ,OAAM,MAAOqB,oCAAoC;EAc/CC,YACwCC,QAAkB,EACbC,QAAoB,EACvDC,kBAAsC,EACtCC,WAA+B,EAC/BC,cAA8B,EAC9BC,cAA8B,EAC9BC,kBAA6C,EAC7CC,mBAAwC,EACxCC,GAAsB;IARQ,KAAAR,QAAQ,GAARA,QAAQ;IACH,KAAAC,QAAQ,GAARA,QAAQ;IAC3C,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,GAAG,GAAHA,GAAG;IAgBN,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,MAAM,GAAG,CAAC,cAAc,CAAC;IACzB,KAAAC,SAAS,GAAG,IAAI/C,SAAS,CAAC;MAC7BgD,kBAAkB,EAAE,IAAIjD,WAAW,CAACkD,SAAS,EAAE,CAAChD,UAAU,CAACiD,QAAQ,CAAC;KACvE,CAAC;IACK,KAAAC,kBAAkB,GAAG,CAACjD,UAAU,CAACkD,OAAO,CAAC;IAExC,KAAAC,YAAY,GAAG;MAAExB,WAAW,EAAE,cAAc;MAAEyB,OAAO,EAAE;IAAc,CAAE;IAoC/E,KAAAC,2BAA2B,GAAG,MAAK;MACjC,IAAI,IAAI,CAAC1B,WAAW,CAACyB,OAAO,EAAE;QAC5B;MACF;MACA,MAAMT,WAAW,GAAG,IAAI,CAAChB,WAAW,CAACC,KAAK;MAC1C,MAAM0B,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAI,CAAChB,cAAc,CAChBiB,gCAAgC,CAAC;QAChCZ,WAAW;QACXW;OACD,CAAC,CACDE,IAAI,CAAC5D,GAAG,CAAC,MAAO,IAAI,CAAC4B,KAAK,GAAG,EAAG,CAAC,CAAC,CAClCiC,SAAS,CAAC,IAAI,CAACC,SAAS,CAAC;IAC9B,CAAC;IAED,KAAAA,SAAS,GAAG,MAAK;MACf,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACjB,GAAG,CAACkB,aAAa,EAAE;IAC1B,CAAC;IAED,KAAAC,WAAW,GAAG,MAAK;MACjB,IAAI,CAACF,YAAY,GAAG,KAAK;IAC3B,CAAC;IAED,KAAAG,yBAAyB,GAAG,MAAK;MAC/B,MAAMC,OAAO,GAAG;QAAE,GAAG,IAAI,CAACvB,kBAAkB,CAACwB,UAAU,EAAE;QAAEC,oBAAoB,EAAE;MAAI,CAAE;MACvF,IAAI,CAACzB,kBAAkB,CAAC0B,UAAU,CAACH,OAAO,CAAC;IAC7C,CAAC;IApFC,IAAI,CAACI,IAAI,GAAGjC,QAAQ,CAACiC,IAAI;IACzB,IAAI,CAACC,EAAE,GAAGlC,QAAQ,CAACkC,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGlC,QAAQ,CAAC8B,oBAAoB;IAE/C,IAAI,CAACK,WAAW,GAAGpC,QAAQ,CAACoC,WAAW;IACvC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACnC,kBAAkB,CAACoC,OAAO;IAChD,IAAI,CAAC7C,WAAW,GAAG,IAAI,CAAC4C,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACN,IAAI,CAAC;IACrD,IAAI,CAACO,+BAA+B,GAAG,IAAI,CAACC,kCAAkC,EAAE;IAChF,IAAI,CAACC,YAAY,GAAGzC,QAAQ,CAACQ,WAAW;IACxC,IAAI,CAACkC,eAAe,GAAG,IAAI,CAAClD,WAAW,CAACmD,YAAY,CAACtB,IAAI,CACvD7D,GAAG,CAACiC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAACgD,YAAY,CAAC,CAC1C;EACH;EAWAG,QAAQA,CAAA;IAEN,MAAMC,eAAe,GAAG/E,eAAe,CAACgF,WAAW,EAAE;IAErD,IAAG,IAAI,CAACL,YAAY,EACpB;MACE,IAAI,CAACA,YAAY,GAAG,IAAI,CAACA,YAAY,CAACM,QAAQ,CAAC,GAAG,CAAC,GAAE,IAAI,CAACN,YAAY,GAAI,GAAG,GAAE,IAAI,CAACA,YAAa,CAAC,CAAC;MACnG,IAAIO,QAAQ,GAAG,EAAuB;MACtCA,QAAQ,CAACC,WAAW,GAAGJ,eAAe,CAACK,sBAAsB,CAACL,eAAe,CAACM,KAAK,CAAC,IAAI,CAACV,YAAY,CAAC,CAAC;MACvGO,QAAQ,CAACI,MAAM,GAAGP,eAAe,CAACQ,4BAA4B,CAACR,eAAe,CAACM,KAAK,CAAC,IAAI,CAACV,YAAY,CAAC,CAAC;MAExG,IAAI,CAAC/B,SAAS,CAAC4C,GAAG,CAAC,oBAAoB,CAAC,CAACC,QAAQ,CAACP,QAAQ,CAAC;IAC7D;IAEA,IAAI,CAACtC,SAAS,CAAC4C,GAAG,CAAC,oBAAoB,CAAC,CAACX,YAAY,CAACrB,SAAS,CAACkC,MAAM,IAAG;MACvE,IAAIA,MAAM,EACV;QACE,IAAI,CAAChE,WAAW,CAAC+D,QAAQ,CAACC,MAAM,CAACC,UAAU,CAAC;MAC9C;IACF,CAAC,CAAC;EAEJ;EAGAjB,kCAAkCA,CAAA;IAChC,OACE,IAAI,CAACtC,WAAW,CAACwD,UAAU,CAAC,mDAAmD,CAAC,KAAK,MAAM;EAE/F;EAEA,IAAIvC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACjB,WAAW,CAACyD,OAAO,CAAC,gBAAgB,CAAC;EACnD;EA+BA3E,kBAAkBA,CAAA;IAChB,IAAI,CAACmB,cAAc,CAChBnB,kBAAkB,CAAC;MAAEK,KAAK,EAAE,IAAI,CAACA,KAAK;MAAE8B,MAAM,EAAE,IAAI,CAACA;IAAM,CAAE,CAAC,CAC9DE,IAAI,CAAC5D,GAAG,CAAC,IAAI,CAACkE,yBAAyB,CAAC,EAAElE,GAAG,CAAC,IAAI,CAACiE,WAAW,CAAC,CAAC,CAChEJ,SAAS,CAAC,MAAK;MACd,IAAI,CAAClB,cAAc,CAACwD,OAAO,CAAC,sBAAsB,EAAE,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAI,CAAE,CAAC;IACzE,CAAC,CAAC;EACN;;;uBAtHWhE,oCAAoC,EAAA3B,EAAA,CAAA4F,iBAAA,CAerC9F,oBAAoB,GAAAE,EAAA,CAAA4F,iBAAA,CACpB7F,yBAAyB,GAAAC,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA9F,EAAA,CAAA4F,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAhG,EAAA,CAAA4F,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlG,EAAA,CAAA4F,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAApG,EAAA,CAAA4F,iBAAA,CAAAK,EAAA,CAAAI,yBAAA,GAAArG,EAAA,CAAA4F,iBAAA,CAAAO,EAAA,CAAAG,mBAAA,GAAAtG,EAAA,CAAA4F,iBAAA,CAAA5F,EAAA,CAAAuG,iBAAA;IAAA;EAAA;;;YAhBxB5E,oCAAoC;MAAA6E,SAAA;MAAAC,QAAA,GAAAzG,EAAA,CAAA0G,kBAAA,KAFhC,CAAC7G,6BAA6B,CAAC;MAAA8G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnB9ChH,EADF,CAAAC,cAAA,aAAkB,eAC0B;UAAAD,EAAA,CAAAE,MAAA,GAEtC;;UAAAF,EAAA,CAAAG,YAAA,EACH;UAACH,EAAA,CAAAC,cAAA,WAAM;UAACD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhBH,EADF,CAAAC,cAAA,aAA0D,iBACd;UACpCD,EAAA,CAAAkH,SAAA,4BAasB;UAGhClH,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;UAGNH,EAAA,CAAAC,cAAA,oBAAoF;UAAzED,EAAA,CAAAe,gBAAA,2BAAAoG,kFAAAlG,MAAA;YAAAjB,EAAA,CAAAS,aAAA,CAAA2G,GAAA;YAAApH,EAAA,CAAAkB,kBAAA,CAAA+F,GAAA,CAAA3D,YAAA,EAAArC,MAAA,MAAAgG,GAAA,CAAA3D,YAAA,GAAArC,MAAA;YAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAAI,MAAA;UAAA,EAA0B;UA0BnCjB,EAzBA,CAAAqH,UAAA,KAAAC,4DAAA,gCAAAtH,EAAA,CAAAuH,sBAAA,CAAwB,KAAAC,4DAAA,iCAAAxH,EAAA,CAAAuH,sBAAA,CAGF,KAAAE,4DAAA,gCAAAzH,EAAA,CAAAuH,sBAAA,CAsBE;UAQ1BvH,EAAA,CAAAG,YAAA,EAAY;;;UA3DHH,EAAA,CAAAI,SAAA,EAAe;;UAAoBJ,EAAA,CAAAI,SAAA,EAEtC;UAFsCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,QAAA2G,GAAA,CAAAhD,WAAA,EAEtC;UAGgBjE,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA0H,UAAA,cAAAT,GAAA,CAAAzE,SAAA,CAAuB;UAEjCxC,EAAA,CAAAI,SAAA,GAAmB;UAQnBJ,EARA,CAAA0H,UAAA,aAAAT,GAAA,CAAA1E,MAAA,CAAmB,uBAAA0E,GAAA,CAAArE,kBAAA,CACwB,iCACX,2BACN,2BACA,6BACE,iBACZ,0BACS,0BACA;UAU1B5C,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAwB,gBAAA,YAAAyF,GAAA,CAAA3D,YAAA,CAA0B;UAAoBtD,EAAnB,CAAA0H,UAAA,SAAAT,GAAA,CAAAU,SAAA,CAAkB,YAAA3H,EAAA,CAAA4H,eAAA,KAAAC,GAAA,EAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}