{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"af-NA\", [[\"v\", \"n\"], [\"vm.\", \"nm.\"], u], u, [[\"S\", \"M\", \"D\", \"W\", \"D\", \"V\", \"S\"], [\"So.\", \"Ma.\", \"Di.\", \"Wo.\", \"Do.\", \"Vr.\", \"Sa.\"], [\"Sondag\", \"Maandag\", \"Dinsdag\", \"Woensdag\", \"Donderdag\", \"Vrydag\", \"Saterdag\"], [\"So.\", \"Ma.\", \"Di.\", \"Wo.\", \"<PERSON>.\", \"Vr.\", \"<PERSON>.\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan.\", \"Feb.\", \"Mrt.\", \"Apr.\", \"<PERSON>\", \"Jun.\", \"Jul.\", \"Aug.\", \"Sep.\", \"Okt.\", \"Nov.\", \"<PERSON>.\"], [\"<PERSON>uarie\", \"Februarie\", \"Maart\", \"April\", \"Mei\", \"Junie\", \"<PERSON>\", \"<PERSON>\", \"September\", \"Oktober\", \"November\", \"<PERSON>ember\"]], u, [[\"v.C.\", \"n.C.\"], u, [\"voor Christus\", \"na Christus\"]], 1, [6, 0], [\"y-MM-dd\", \"dd MMM y\", \"dd MMMM y\", \"EEEE dd MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"ZAR\", \"R\", \"Suid-Afrikaanse rand\", {\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"MXN\": [u, \"$\"],\n  \"NAD\": [\"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"leu\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"ZAR\": [\"R\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/af-NA.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"af-NA\", [[\"v\", \"n\"], [\"vm.\", \"nm.\"], u], u, [[\"S\", \"M\", \"D\", \"W\", \"D\", \"V\", \"S\"], [\"So.\", \"Ma.\", \"Di.\", \"Wo.\", \"Do.\", \"Vr.\", \"Sa.\"], [\"Sondag\", \"Maandag\", \"Dinsdag\", \"Woensdag\", \"Donderdag\", \"Vrydag\", \"Saterdag\"], [\"So.\", \"Ma.\", \"Di.\", \"Wo.\", \"<PERSON>.\", \"Vr.\", \"<PERSON>.\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan.\", \"Feb.\", \"Mrt.\", \"Apr.\", \"<PERSON>\", \"Jun.\", \"Jul.\", \"Aug.\", \"Sep.\", \"Okt.\", \"Nov.\", \"<PERSON>.\"], [\"<PERSON>uarie\", \"Februarie\", \"Maart\", \"April\", \"Mei\", \"Junie\", \"<PERSON>\", \"<PERSON>\", \"September\", \"Oktober\", \"November\", \"<PERSON>ember\"]], u, [[\"v.C.\", \"n.C.\"], u, [\"voor Christus\", \"na Christus\"]], 1, [6, 0], [\"y-MM-dd\", \"dd MMM y\", \"dd MMMM y\", \"EEEE dd MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"ZAR\", \"R\", \"Suid-Afrikaanse rand\", { \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"JPY\": [\"JP¥\", \"¥\"], \"MXN\": [u, \"$\"], \"NAD\": [\"$\"], \"PHP\": [u, \"₱\"], \"RON\": [u, \"leu\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [u, \"$\"], \"ZAR\": [\"R\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAEJ,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,sBAAsB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}