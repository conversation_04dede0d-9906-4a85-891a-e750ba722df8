{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport let FileUploadService = /*#__PURE__*/(() => {\n  class FileUploadService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'EconomicSubstanceService';\n      this.uploadDeclarationImportExcelByFile = file => this.restService.request({\n        method: 'POST',\n        url: '/api/ESService/Import/File/UploadDeclarationImportExcel',\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      /**\n       * Since Proxy client cannot generate associated web API method since IFormFile compile error, it needs to be manually called here.\n       * Called by CA portal information exchange import page, upload excel file.\n       *\n       */\n      this.uploadInformationExchangeImportExcelByFile = file => this.restService.request({\n        method: 'POST',\n        url: '/api/ESService/Import/InfoExchange/UploadInfoExchangeImportExcel',\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      this.uploadBahamasCertificate = (file, password) => this.restService.request({\n        method: 'POST',\n        url: '/api/CtsIntegration/certificate/UploadBahamasCertificate',\n        params: {\n          password\n        },\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      this.uploadHistoricalXml = (file, receivingCountry, financialPeriodEnd) => this.restService.request({\n        method: 'POST',\n        url: '/api/ESService/CtsIntegration/UploadHistoricalXml',\n        params: {\n          receivingCountry,\n          financialPeriodEnd\n        },\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n    }\n    static {\n      this.ɵfac = function FileUploadService_Factory(t) {\n        return new (t || FileUploadService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileUploadService,\n        factory: FileUploadService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileUploadService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}