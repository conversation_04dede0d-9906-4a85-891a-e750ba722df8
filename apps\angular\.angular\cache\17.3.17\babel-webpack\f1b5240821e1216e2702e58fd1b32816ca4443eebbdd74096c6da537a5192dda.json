{"ast": null, "code": "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Wednesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\nexport default function nextWednesday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 3);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}