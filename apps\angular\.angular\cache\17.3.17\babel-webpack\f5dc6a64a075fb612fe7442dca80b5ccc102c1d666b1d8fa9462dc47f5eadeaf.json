{"ast": null, "code": "import { CoreModule } from '@abp/ng.core';\nimport { AccountSettingsModule } from '@volo/abp.ng.account/admin';\nimport { ACCOUNT_SETTING_TAB_PROVIDERS } from './providers/setting-tab.provider';\nimport * as i0 from \"@angular/core\";\nexport class AccountAdminConfigModule {\n  static forRoot() {\n    return {\n      ngModule: AccountAdminConfigModule,\n      providers: [ACCOUNT_SETTING_TAB_PROVIDERS]\n    };\n  }\n  static {\n    this.ɵfac = function AccountAdminConfigModule_Factory(t) {\n      return new (t || AccountAdminConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccountAdminConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CoreModule, AccountSettingsModule, AccountSettingsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccountAdminConfigModule, {\n    imports: [CoreModule, AccountSettingsModule],\n    exports: [AccountSettingsModule]\n  });\n})();", "map": {"version": 3, "names": ["CoreModule", "AccountSettingsModule", "ACCOUNT_SETTING_TAB_PROVIDERS", "AccountAdminConfigModule", "forRoot", "ngModule", "providers", "imports", "exports"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\abp-modules\\account\\admin\\config\\src\\account-admin-config.module.ts"], "sourcesContent": ["\r\nimport { CoreModule } from '@abp/ng.core';\r\nimport { ModuleWithProviders, NgModule } from '@angular/core';\r\n\r\nimport { AccountSettingsModule } from '@volo/abp.ng.account/admin';\r\n\r\nimport { ACCOUNT_SETTING_TAB_PROVIDERS } from './providers/setting-tab.provider';\r\n\r\n\r\n\r\n@NgModule({\r\n  imports: [\r\n    CoreModule,\r\n    AccountSettingsModule],\r\n  exports: [\r\n    AccountSettingsModule\r\n  ],\r\n  declarations: [],\r\n})\r\nexport class AccountAdminConfigModule {\r\n  static forRoot(): ModuleWithProviders<AccountAdminConfigModule> {\r\n    return {\r\n      ngModule: AccountAdminConfigModule,\r\n      providers: [ACCOUNT_SETTING_TAB_PROVIDERS],\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,cAAc;AAGzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAElE,SAASC,6BAA6B,QAAQ,kCAAkC;;AAahF,OAAM,MAAOC,wBAAwB;EACnC,OAAOC,OAAOA,CAAA;IACZ,OAAO;MACLC,QAAQ,EAAEF,wBAAwB;MAClCG,SAAS,EAAE,CAACJ,6BAA6B;KAC1C;EACH;;;uBANWC,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAPjCH,UAAU,EACVC,qBAAqB,EAErBA,qBAAqB;IAAA;EAAA;;;2EAIZE,wBAAwB;IAAAI,OAAA,GAPjCP,UAAU,EACVC,qBAAqB;IAAAO,OAAA,GAErBP,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}