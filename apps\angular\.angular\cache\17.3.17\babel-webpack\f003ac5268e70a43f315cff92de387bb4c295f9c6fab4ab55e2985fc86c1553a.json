{"ast": null, "code": "import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nclass TemplateContentService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'TextTemplateManagement';\n    this.get = input => this.restService.request({\n      method: 'GET',\n      url: '/api/text-template-management/template-contents',\n      params: {\n        templateName: input.templateName,\n        cultureName: input.cultureName\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.restoreToDefault = input => this.restService.request({\n      method: 'PUT',\n      url: '/api/text-template-management/template-contents/restore-to-default',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.update = input => this.restService.request({\n      method: 'PUT',\n      url: '/api/text-template-management/template-contents',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function TemplateContentService_Factory(t) {\n      return new (t || TemplateContentService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TemplateContentService,\n      factory: TemplateContentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplateContentService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\nclass TemplateDefinitionService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'TextTemplateManagement';\n    this.get = name => this.restService.request({\n      method: 'GET',\n      url: `/api/text-template-management/template-definitions/${name}`\n    }, {\n      apiName: this.apiName\n    });\n    this.getList = input => this.restService.request({\n      method: 'GET',\n      url: '/api/text-template-management/template-definitions',\n      params: {\n        filterText: input.filterText,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function TemplateDefinitionService_Factory(t) {\n      return new (t || TemplateDefinitionService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TemplateDefinitionService,\n      factory: TemplateDefinitionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplateDefinitionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TemplateContentService, TemplateDefinitionService };", "map": {"version": 3, "names": ["i1", "i0", "Injectable", "TemplateContentService", "constructor", "restService", "apiName", "get", "input", "request", "method", "url", "params", "templateName", "cultureName", "restoreToDefault", "body", "update", "ɵfac", "TemplateContentService_Factory", "t", "ɵɵinject", "RestService", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "TemplateDefinitionService", "name", "getList", "filterText", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "TemplateDefinitionService_Factory"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@volo/abp.ng.text-template-management/fesm2022/volo-abp.ng.text-template-management-proxy.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\nclass TemplateContentService {\n    constructor(restService) {\n        this.restService = restService;\n        this.apiName = 'TextTemplateManagement';\n        this.get = (input) => this.restService.request({\n            method: 'GET',\n            url: '/api/text-template-management/template-contents',\n            params: { templateName: input.templateName, cultureName: input.cultureName },\n        }, { apiName: this.apiName });\n        this.restoreToDefault = (input) => this.restService.request({\n            method: 'PUT',\n            url: '/api/text-template-management/template-contents/restore-to-default',\n            body: input,\n        }, { apiName: this.apiName });\n        this.update = (input) => this.restService.request({\n            method: 'PUT',\n            url: '/api/text-template-management/template-contents',\n            body: input,\n        }, { apiName: this.apiName });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TemplateContentService, deps: [{ token: i1.RestService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TemplateContentService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TemplateContentService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: i1.RestService }] });\n\nclass TemplateDefinitionService {\n    constructor(restService) {\n        this.restService = restService;\n        this.apiName = 'TextTemplateManagement';\n        this.get = (name) => this.restService.request({\n            method: 'GET',\n            url: `/api/text-template-management/template-definitions/${name}`,\n        }, { apiName: this.apiName });\n        this.getList = (input) => this.restService.request({\n            method: 'GET',\n            url: '/api/text-template-management/template-definitions',\n            params: { filterText: input.filterText, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },\n        }, { apiName: this.apiName });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TemplateDefinitionService, deps: [{ token: i1.RestService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TemplateDefinitionService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.1.3\", ngImport: i0, type: TemplateDefinitionService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: i1.RestService }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TemplateContentService, TemplateDefinitionService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,cAAc;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;AAE1C,MAAMC,sBAAsB,CAAC;EACzBC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAG,wBAAwB;IACvC,IAAI,CAACC,GAAG,GAAIC,KAAK,IAAK,IAAI,CAACH,WAAW,CAACI,OAAO,CAAC;MAC3CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,iDAAiD;MACtDC,MAAM,EAAE;QAAEC,YAAY,EAAEL,KAAK,CAACK,YAAY;QAAEC,WAAW,EAAEN,KAAK,CAACM;MAAY;IAC/E,CAAC,EAAE;MAAER,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IAC7B,IAAI,CAACS,gBAAgB,GAAIP,KAAK,IAAK,IAAI,CAACH,WAAW,CAACI,OAAO,CAAC;MACxDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,oEAAoE;MACzEK,IAAI,EAAER;IACV,CAAC,EAAE;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IAC7B,IAAI,CAACW,MAAM,GAAIT,KAAK,IAAK,IAAI,CAACH,WAAW,CAACI,OAAO,CAAC;MAC9CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,iDAAiD;MACtDK,IAAI,EAAER;IACV,CAAC,EAAE;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACY,IAAI,YAAAC,+BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFjB,sBAAsB,EAAhCF,EAAE,CAAAoB,QAAA,CAAgDrB,EAAE,CAACsB,WAAW;IAAA,CAA6C;EAAE;EAC/M;IAAS,IAAI,CAACC,KAAK,kBAD6EtB,EAAE,CAAAuB,kBAAA;MAAAC,KAAA,EACYtB,sBAAsB;MAAAuB,OAAA,EAAtBvB,sBAAsB,CAAAe,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACjK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG3B,EAAE,CAAA4B,iBAAA,CAGX1B,sBAAsB,EAAc,CAAC;IACpH2B,IAAI,EAAE5B,UAAU;IAChB6B,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE9B,EAAE,CAACsB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMU,yBAAyB,CAAC;EAC5B5B,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAG,wBAAwB;IACvC,IAAI,CAACC,GAAG,GAAI0B,IAAI,IAAK,IAAI,CAAC5B,WAAW,CAACI,OAAO,CAAC;MAC1CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,sDAAsDsB,IAAI;IACnE,CAAC,EAAE;MAAE3B,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC4B,OAAO,GAAI1B,KAAK,IAAK,IAAI,CAACH,WAAW,CAACI,OAAO,CAAC;MAC/CC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,oDAAoD;MACzDC,MAAM,EAAE;QAAEuB,UAAU,EAAE3B,KAAK,CAAC2B,UAAU;QAAEC,OAAO,EAAE5B,KAAK,CAAC4B,OAAO;QAAEC,SAAS,EAAE7B,KAAK,CAAC6B,SAAS;QAAEC,cAAc,EAAE9B,KAAK,CAAC8B;MAAe;IACrI,CAAC,EAAE;MAAEhC,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACY,IAAI,YAAAqB,kCAAAnB,CAAA;MAAA,YAAAA,CAAA,IAAwFY,yBAAyB,EAxBnC/B,EAAE,CAAAoB,QAAA,CAwBmDrB,EAAE,CAACsB,WAAW;IAAA,CAA6C;EAAE;EAClN;IAAS,IAAI,CAACC,KAAK,kBAzB6EtB,EAAE,CAAAuB,kBAAA;MAAAC,KAAA,EAyBYO,yBAAyB;MAAAN,OAAA,EAAzBM,yBAAyB,CAAAd,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACpK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3BoG3B,EAAE,CAAA4B,iBAAA,CA2BXG,yBAAyB,EAAc,CAAC;IACvHF,IAAI,EAAE5B,UAAU;IAChB6B,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE9B,EAAE,CAACsB;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;;AAEA,SAASnB,sBAAsB,EAAE6B,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}