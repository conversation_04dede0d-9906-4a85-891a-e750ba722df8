{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ksf\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    return 5;\n  }\n  exports.default = ['ksf', [['sárúwá', 'cɛɛ́nko'], u, u], u, [['s', 'l', 'm', 'm', 'j', 'j', 's'], ['sɔ́n', 'lǝn', 'maa', 'mɛk', 'jǝǝ', 'júm', 'sam'], ['sɔ́ndǝ', 'lǝndí', 'maadí', 'mɛkrɛdí', 'jǝǝdí', 'júmbá', 'samdí'], ['sɔ́n', 'lǝn', 'maa', 'mɛk', 'jǝǝ', 'júm', 'sam']], u, [['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], ['ŋ1', 'ŋ2', 'ŋ3', 'ŋ4', 'ŋ5', 'ŋ6', 'ŋ7', 'ŋ8', 'ŋ9', 'ŋ10', 'ŋ11', 'ŋ12'], ['ŋwíí a ntɔ́ntɔ', 'ŋwíí akǝ bɛ́ɛ', 'ŋwíí akǝ ráá', 'ŋwíí akǝ nin', 'ŋwíí akǝ táan', 'ŋwíí akǝ táafɔk', 'ŋwíí akǝ táabɛɛ', 'ŋwíí akǝ táaraa', 'ŋwíí akǝ táanin', 'ŋwíí akǝ ntɛk', 'ŋwíí akǝ ntɛk di bɔ́k', 'ŋwíí akǝ ntɛk di bɛ́ɛ']], u, [['d.Y.', 'k.Y.'], u, ['di Yɛ́sus aká yálɛ', 'cámɛɛn kǝ kǝbɔpka Y']], 1, [6, 0], ['d/M/y', 'd MMM y', 'd MMMM y', 'EEEE d MMMM y'], ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'], ['{1} {0}', u, u, u], [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'], 'XAF', 'FCFA', 'fráŋ', {\n    'JPY': ['JP¥', '¥'],\n    'USD': ['US$', '$']\n  }, 'ltr', plural];\n});", "map": {"version": 3, "names": ["factory", "module", "exports", "v", "undefined", "define", "amd", "require", "Object", "defineProperty", "value", "u", "plural", "n", "default"], "sources": ["c:/Temp/node_modules/@angular/common/locales/ksf.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\n        var v = factory(null, exports);\n        if (v !== undefined) module.exports = v;\n    }\n    else if (typeof define === \"function\" && define.amd) {\n        define(\"@angular/common/locales/ksf\", [\"require\", \"exports\"], factory);\n    }\n})(function (require, exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    // THIS CODE IS GENERATED - DO NOT MODIFY\n    // See angular/tools/gulp-tasks/cldr/extract.js\n    var u = undefined;\n    function plural(n) {\n        return 5;\n    }\n    exports.default = [\n        'ksf',\n        [['sárúwá', 'cɛɛ́nko'], u, u],\n        u,\n        [\n            ['s', 'l', 'm', 'm', 'j', 'j', 's'], ['sɔ́n', 'lǝn', 'maa', 'mɛk', 'jǝǝ', 'júm', 'sam'],\n            ['sɔ́ndǝ', 'lǝndí', 'maadí', 'mɛkrɛdí', 'jǝǝdí', 'júmbá', 'samdí'],\n            ['sɔ́n', 'lǝn', 'maa', 'mɛk', 'jǝǝ', 'júm', 'sam']\n        ],\n        u,\n        [\n            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n            ['ŋ1', 'ŋ2', 'ŋ3', 'ŋ4', 'ŋ5', 'ŋ6', 'ŋ7', 'ŋ8', 'ŋ9', 'ŋ10', 'ŋ11', 'ŋ12'],\n            [\n                'ŋwíí a ntɔ́ntɔ', 'ŋwíí akǝ bɛ́ɛ', 'ŋwíí akǝ ráá', 'ŋwíí akǝ nin', 'ŋwíí akǝ táan',\n                'ŋwíí akǝ táafɔk', 'ŋwíí akǝ táabɛɛ', 'ŋwíí akǝ táaraa', 'ŋwíí akǝ táanin', 'ŋwíí akǝ ntɛk',\n                'ŋwíí akǝ ntɛk di bɔ́k', 'ŋwíí akǝ ntɛk di bɛ́ɛ'\n            ]\n        ],\n        u,\n        [['d.Y.', 'k.Y.'], u, ['di Yɛ́sus aká yálɛ', 'cámɛɛn kǝ kǝbɔpka Y']],\n        1,\n        [6, 0],\n        ['d/M/y', 'd MMM y', 'd MMMM y', 'EEEE d MMMM y'],\n        ['HH:mm', 'HH:mm:ss', 'HH:mm:ss z', 'HH:mm:ss zzzz'],\n        ['{1} {0}', u, u, u],\n        [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n        ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'],\n        'XAF',\n        'FCFA',\n        'fráŋ',\n        { 'JPY': ['JP¥', '¥'], 'USD': ['US$', '$'] },\n        'ltr',\n        plural\n    ];\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUA,OAAO,EAAE;EAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClE,IAAIC,CAAC,GAAGH,OAAO,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC9B,IAAIC,CAAC,KAAKC,SAAS,EAAEH,MAAM,CAACC,OAAO,GAAGC,CAAC;EAC3C,CAAC,MACI,IAAI,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACjDD,MAAM,CAAC,6BAA6B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEL,OAAO,CAAC;EAC1E;AACJ,CAAC,EAAE,UAAUO,OAAO,EAAEL,OAAO,EAAE;EAC3B,YAAY;;EACZM,MAAM,CAACC,cAAc,CAACP,OAAO,EAAE,YAAY,EAAE;IAAEQ,KAAK,EAAE;EAAK,CAAC,CAAC;EAC7D;EACA;EACA,IAAIC,CAAC,GAAGP,SAAS;EACjB,SAASQ,MAAMA,CAACC,CAAC,EAAE;IACf,OAAO,CAAC;EACZ;EACAX,OAAO,CAACY,OAAO,GAAG,CACd,KAAK,EACL,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAC7BA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACvF,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAClE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CACrD,EACDA,CAAC,EACD,CACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAC/D,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAC3E,CACI,gBAAgB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe,EAClF,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAC3F,uBAAuB,EAAE,uBAAuB,CACnD,CACJ,EACDA,CAAC,EACD,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,oBAAoB,EAAE,qBAAqB,CAAC,CAAC,EACpE,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EACjD,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EACpD,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAC5C,KAAK,EACL,MAAM,EACN,MAAM,EACN;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;EAAE,CAAC,EAC5C,KAAK,EACLC,MAAM,CACT;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}