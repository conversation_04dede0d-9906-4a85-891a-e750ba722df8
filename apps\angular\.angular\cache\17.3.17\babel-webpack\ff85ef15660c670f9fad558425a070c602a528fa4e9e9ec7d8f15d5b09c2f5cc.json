{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"nyn\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"K\", \"R\", \"S\", \"N\", \"T\", \"M\"], [\"SAN\", \"ORK\", \"OKB\", \"OKS\", \"OKN\", \"OKT\", \"OMK\"], [\"Sand<PERSON>\", \"Orwokubanza\", \"Orwakabiri\", \"Orwakashatu\", \"Orwakana\", \"Orwakataano\", \"Orwamukaaga\"], [\"SAN\", \"ORK\", \"OKB\", \"OKS\", \"OKN\", \"OKT\", \"OMK\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"KBZ\", \"KBR\", \"KST\", \"KKN\", \"KTN\", \"KMK\", \"KMS\", \"KMN\", \"KMW\", \"KKM\", \"KNK\", \"KNB\"], [\"Okwokubanza\", \"Okwakabiri\", \"Okwakashatu\", \"Okwakana\", \"Okwakataana\", \"Okwamukaaga\", \"Okwamushanju\", \"Okwamunaana\", \"Okwamwenda\", \"Okwaikumi\", \"Okwaikumi na kumwe\", \"Okwaikumi na ibiri\"]], u, [[\"BC\", \"AD\"], u, [\"Kurisito Atakaijire\", \"Kurisito Yaijire\"]], 1, [0, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"UGX\", \"USh\", \"Eshiringi ya Uganda\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"UGX\": [\"USh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/nyn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"nyn\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"K\", \"R\", \"S\", \"N\", \"T\", \"M\"], [\"SAN\", \"ORK\", \"OKB\", \"OKS\", \"OKN\", \"OKT\", \"OMK\"], [\"Sand<PERSON>\", \"Orwokubanza\", \"Orwakabiri\", \"Orwakashatu\", \"Orwakana\", \"Orwakataano\", \"Orwamukaaga\"], [\"SAN\", \"ORK\", \"OKB\", \"OKS\", \"OKN\", \"OKT\", \"OMK\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"KBZ\", \"KBR\", \"KST\", \"KKN\", \"KTN\", \"KMK\", \"KMS\", \"KMN\", \"KMW\", \"KKM\", \"KNK\", \"KNB\"], [\"Okwokubanza\", \"Okwakabiri\", \"Okwakashatu\", \"Okwakana\", \"Okwakataana\", \"Okwamukaaga\", \"Okwamushanju\", \"Okwamunaana\", \"Okwamwenda\", \"Okwaikumi\", \"Okwaikumi na kumwe\", \"Okwaikumi na ibiri\"]], u, [[\"BC\", \"AD\"], u, [\"Kurisito Atakaijire\", \"Kurisito Yaijire\"]], 1, [0, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"UGX\", \"USh\", \"Eshiringi ya Uganda\", { \"JPY\": [\"JP¥\", \"¥\"], \"UGX\": [\"USh\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}