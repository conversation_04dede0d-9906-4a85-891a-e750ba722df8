{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nvar accusativeWeekdays = ['nedeľu', 'pondelok', 'utorok', 'stredu', 'štvrtok', 'piatok', 'sobotu'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 3: /* Wed */\n    case 6 /* Sat */:\n      return \"'minulú \" + weekday + \" o' p\";\n    default:\n      return \"'minulý' eeee 'o' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  if (day === 4 /* Thu */) {\n    return \"'vo' eeee 'o' p\";\n  } else {\n    return \"'v \" + weekday + \" o' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 4: /* Wed */\n    case 6 /* Sat */:\n      return \"'budú<PERSON> \" + weekday + \" o' p\";\n    default:\n      return \"'budúci' eeee 'o' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'včera o' p\",\n  today: \"'dnes o' p\",\n  tomorrow: \"'zajtra o' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}