{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@ngx-validate/core\";\nimport * as i8 from \"@angular/common\";\nfunction UpdateCaCertificateDialogComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFile.name, \" \");\n  }\n}\nfunction UpdateCaCertificateDialogComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdateCaCertificateDialogComponent_mat_hint_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 24);\n    i0.ɵɵtext(1, \" File is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class UpdateCaCertificateDialogComponent {\n  constructor(dialogRef, data, fb) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.fb = fb;\n    this.selectedFile = null;\n    this.form = this.fb.group({\n      password: [''],\n      // password is now optional\n      file: [null, Validators.required]\n    });\n  }\n  onFileChange(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      this.form.patchValue({\n        file\n      });\n      this.form.get('file')?.updateValueAndValidity();\n    } else {\n      this.selectedFile = null;\n      this.form.patchValue({\n        file: null\n      });\n      this.form.get('file')?.updateValueAndValidity();\n    }\n  }\n  onCancel() {\n    this.dialogRef.close();\n  }\n  onSubmit() {\n    if (!this.selectedFile) {\n      this.form.get('file')?.markAsTouched();\n      return;\n    }\n    if (this.form.valid) {\n      this.dialogRef.close({\n        file: this.selectedFile,\n        password: this.form.value.password\n      });\n    }\n  }\n  static {\n    this.ɵfac = function UpdateCaCertificateDialogComponent_Factory(t) {\n      return new (t || UpdateCaCertificateDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateCaCertificateDialogComponent,\n      selectors: [[\"app-update-ca-certificate-dialog\"]],\n      decls: 34,\n      vars: 5,\n      consts: [[\"noFile\", \"\"], [\"fileInput\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"row\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"file-upload-group\"], [1, \"file-upload-label\"], [1, \"required\"], [1, \"file-upload-row\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"class\", \"file-name\", 4, \"ngIf\", \"ngIfElse\"], [\"accept\", \"*\", \"type\", \"file\", \"required\", \"\", 1, \"file-input\", 3, \"change\"], [\"class\", \"file-error\", 4, \"ngIf\"], [\"appearance\", \"fill\", 1, \"w-100\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"password\"], [\"align\", \"end\", 1, \"action-buttons\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"file-name\"], [1, \"file-placeholder\"], [1, \"file-error\"]],\n      template: function UpdateCaCertificateDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"Update CA Certificate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function UpdateCaCertificateDialogComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵelement(6, \"i\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function UpdateCaCertificateDialogComponent_Template_form_ngSubmit_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"div\", 9)(10, \"label\", 10);\n          i0.ɵɵtext(11, \"Certificate File \");\n          i0.ɵɵelementStart(12, \"span\", 11);\n          i0.ɵɵtext(13, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function UpdateCaCertificateDialogComponent_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r2 = i0.ɵɵreference(23);\n            return i0.ɵɵresetView(fileInput_r2.click());\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"upload_file\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Choose File \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, UpdateCaCertificateDialogComponent_span_19_Template, 2, 1, \"span\", 14)(20, UpdateCaCertificateDialogComponent_ng_template_20_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 15, 1);\n          i0.ɵɵlistener(\"change\", function UpdateCaCertificateDialogComponent_Template_input_change_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, UpdateCaCertificateDialogComponent_mat_hint_24_Template, 2, 0, \"mat-hint\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 17)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Certificate Password (optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"mat-dialog-actions\", 19)(30, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function UpdateCaCertificateDialogComponent_Template_button_click_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵtext(31, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"button\", 21);\n          i0.ɵɵtext(33, \"Upload\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_5_0;\n          const noFile_r4 = i0.ɵɵreference(21);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFile)(\"ngIfElse\", noFile_r4);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.form.get(\"file\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.form.get(\"file\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i3.MatInput, i4.MatFormField, i4.MatLabel, i4.MatHint, i5.MatIcon, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i6.MatButton, i7.ValidationGroupDirective, i7.ValidationDirective, i8.NgIf],\n      styles: [\".title[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.modal-action-button[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n\\n.w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.file-upload-group[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.file-upload-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n\\n.required[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.file-upload-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.file-input[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  color: #333;\\n}\\n\\n.file-placeholder[_ngcontent-%COMP%] {\\n  color: #888;\\n  font-style: italic;\\n}\\n\\n.file-error[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  display: flex;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInVwZGF0ZS1jYS1jZXJ0aWZpY2F0ZS1kaWFsb2cuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0FBQ0Y7O0FBRUE7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQUNGOztBQUVBO0VBQ0UsVUFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLGtCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxnQkFBQTtFQUNBLGFBQUE7QUFDRiIsImZpbGUiOiJ1cGRhdGUtY2EtY2VydGlmaWNhdGUtZGlhbG9nLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLnRpdGxlIHtcclxuICBmb250LXNpemU6IDEuM2VtO1xyXG4gIGNvbG9yOiAjMDA3NzliO1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG59XHJcblxyXG4ubW9kYWwtYWN0aW9uLWJ1dHRvbiB7XHJcbiAgZm9udC1zaXplOiAxZW07XHJcbn1cclxuXHJcbi53LTEwMCB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi5maWxlLXVwbG9hZC1ncm91cCB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbn1cclxuXHJcbi5maWxlLXVwbG9hZC1sYWJlbCB7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbn1cclxuXHJcbi5yZXF1aXJlZCB7XHJcbiAgY29sb3I6IHJlZDtcclxufVxyXG5cclxuLmZpbGUtdXBsb2FkLXJvdyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMTJweDtcclxuICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbn1cclxuXHJcbi5maWxlLWlucHV0IHtcclxuICBkaXNwbGF5OiBub25lO1xyXG59XHJcblxyXG4uZmlsZS1uYW1lIHtcclxuICBmbGV4OiAxIDEgYXV0bztcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbn1cclxuXHJcbi5maWxlLXBsYWNlaG9sZGVyIHtcclxuICBjb2xvcjogIzg4ODtcclxuICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbn1cclxuXHJcbi5maWxlLWVycm9yIHtcclxuICBjb2xvcjogI2Y0NDMzNjtcclxufVxyXG5cclxuLmFjdGlvbi1idXR0b25zIHtcclxuICBtYXJnaW4tdG9wOiAxNnB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7IFxyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "selectedFile", "name", "UpdateCaCertificateDialogComponent", "constructor", "dialogRef", "data", "fb", "form", "group", "password", "file", "required", "onFileChange", "event", "target", "files", "patchValue", "get", "updateValueAndValidity", "onCancel", "close", "onSubmit", "<PERSON><PERSON><PERSON><PERSON>ched", "valid", "value", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "UpdateCaCertificateDialogComponent_Template", "rf", "ctx", "ɵɵlistener", "UpdateCaCertificateDialogComponent_Template_button_click_5_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "UpdateCaCertificateDialogComponent_Template_form_ngSubmit_7_listener", "UpdateCaCertificateDialogComponent_Template_button_click_15_listener", "fileInput_r2", "ɵɵreference", "click", "ɵɵtemplate", "UpdateCaCertificateDialogComponent_span_19_Template", "UpdateCaCertificateDialogComponent_ng_template_20_Template", "ɵɵtemplateRefExtractor", "UpdateCaCertificateDialogComponent_Template_input_change_22_listener", "$event", "UpdateCaCertificateDialogComponent_mat_hint_24_Template", "UpdateCaCertificateDialogComponent_Template_button_click_30_listener", "ɵɵproperty", "noFile_r4", "tmp_5_0", "invalid", "touched"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\update-ca-certificate-dialog\\update-ca-certificate-dialog.component.ts", "c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\update-ca-certificate-dialog\\update-ca-certificate-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-update-ca-certificate-dialog',\r\n  templateUrl: './update-ca-certificate-dialog.component.html',\r\n  styleUrls: ['./update-ca-certificate-dialog.component.scss']\r\n})\r\nexport class UpdateCaCertificateDialogComponent {\r\n  form: FormGroup;\r\n  selectedFile: File | null = null;\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<UpdateCaCertificateDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: any,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.form = this.fb.group({\r\n      password: [''], // password is now optional\r\n      file: [null, Validators.required]\r\n    });\r\n  }\r\n\r\n  onFileChange(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      this.selectedFile = file;\r\n      this.form.patchValue({ file });\r\n      this.form.get('file')?.updateValueAndValidity();\r\n    } else {\r\n      this.selectedFile = null;\r\n      this.form.patchValue({ file: null });\r\n      this.form.get('file')?.updateValueAndValidity();\r\n    }\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (!this.selectedFile) {\r\n      this.form.get('file')?.markAsTouched();\r\n      return;\r\n    }\r\n    if (this.form.valid) {\r\n      this.dialogRef.close({\r\n        file: this.selectedFile,\r\n        password: this.form.value.password\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<div mat-dialog-title>\r\n  <div class=\"row\">\r\n    <div class=\"col-8 title\">Update CA Certificate</div>\r\n    <div class=\"col-4 text-end modal-action-button\">\r\n      <button\r\n        type=\"button\"\r\n        mat-raised-button\r\n        class=\"ui-button\"\r\n        (click)=\"onCancel()\"\r\n      >\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\">\r\n  <mat-dialog-content>\r\n    <div class=\"file-upload-group\">\r\n      <label class=\"file-upload-label\">Certificate File <span class=\"required\">*</span></label>\r\n      <div class=\"file-upload-row\">\r\n        <button mat-stroked-button color=\"primary\" type=\"button\" (click)=\"fileInput.click()\">\r\n          <mat-icon>upload_file</mat-icon>\r\n          Choose File\r\n        </button>\r\n        <span class=\"file-name\" *ngIf=\"selectedFile; else noFile\">\r\n          {{ selectedFile.name }}\r\n        </span>\r\n        <ng-template #noFile>\r\n          <span class=\"file-placeholder\">No file chosen</span>\r\n        </ng-template>\r\n      </div>\r\n      <input #fileInput accept=\"*\" type=\"file\" (change)=\"onFileChange($event)\" class=\"file-input\" required />\r\n      <mat-hint *ngIf=\"form.get('file')?.invalid && form.get('file')?.touched\" class=\"file-error\">\r\n        File is required.\r\n      </mat-hint>\r\n    </div>\r\n    <mat-form-field appearance=\"fill\" class=\"w-100\">\r\n      <mat-label>Certificate Password (optional)</mat-label>\r\n      <input matInput type=\"password\" formControlName=\"password\" />\r\n    </mat-form-field>\r\n  </mat-dialog-content>\r\n  <mat-dialog-actions align=\"end\" class=\"action-buttons\">\r\n    <button mat-stroked-button color=\"warn\" type=\"button\" (click)=\"onCancel()\">Cancel</button>\r\n    <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"form.invalid\">Upload</button>\r\n  </mat-dialog-actions>\r\n</form>\r\n"], "mappings": "AACA,SAAuBA,eAAe,QAAQ,0BAA0B;AACxE,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;ICsB3DC,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,MACF;;;;;IAEER,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIxDH,EAAA,CAAAC,cAAA,mBAA4F;IAC1FD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;ADzBjB,OAAM,MAAOM,kCAAkC;EAI7CC,YACSC,SAA2D,EAClCC,IAAS,EACjCC,EAAe;IAFhB,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC5B,KAAAC,EAAE,GAAFA,EAAE;IALZ,KAAAN,YAAY,GAAgB,IAAI;IAO9B,IAAI,CAACO,IAAI,GAAG,IAAI,CAACD,EAAE,CAACE,KAAK,CAAC;MACxBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MAAE;MAChBC,IAAI,EAAE,CAAC,IAAI,EAAElB,UAAU,CAACmB,QAAQ;KACjC,CAAC;EACJ;EAEAC,YAAYA,CAACC,KAAU;IACrB,MAAMH,IAAI,GAAGG,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIL,IAAI,EAAE;MACR,IAAI,CAACV,YAAY,GAAGU,IAAI;MACxB,IAAI,CAACH,IAAI,CAACS,UAAU,CAAC;QAAEN;MAAI,CAAE,CAAC;MAC9B,IAAI,CAACH,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC,EAAEC,sBAAsB,EAAE;IACjD,CAAC,MAAM;MACL,IAAI,CAAClB,YAAY,GAAG,IAAI;MACxB,IAAI,CAACO,IAAI,CAACS,UAAU,CAAC;QAAEN,IAAI,EAAE;MAAI,CAAE,CAAC;MACpC,IAAI,CAACH,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC,EAAEC,sBAAsB,EAAE;IACjD;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACf,SAAS,CAACgB,KAAK,EAAE;EACxB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACrB,YAAY,EAAE;MACtB,IAAI,CAACO,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC,EAAEK,aAAa,EAAE;MACtC;IACF;IACA,IAAI,IAAI,CAACf,IAAI,CAACgB,KAAK,EAAE;MACnB,IAAI,CAACnB,SAAS,CAACgB,KAAK,CAAC;QACnBV,IAAI,EAAE,IAAI,CAACV,YAAY;QACvBS,QAAQ,EAAE,IAAI,CAACF,IAAI,CAACiB,KAAK,CAACf;OAC3B,CAAC;IACJ;EACF;;;uBA3CWP,kCAAkC,EAAAT,EAAA,CAAAgC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAlC,EAAA,CAAAgC,iBAAA,CAMnClC,eAAe,GAAAE,EAAA,CAAAgC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YANd3B,kCAAkC;MAAA4B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCP3C3C,EAFJ,CAAAC,cAAA,aAAsB,aACH,aACU;UAAAD,EAAA,CAAAE,MAAA,4BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAElDH,EADF,CAAAC,cAAA,aAAgD,gBAM7C;UADCD,EAAA,CAAA6C,UAAA,mBAAAC,oEAAA;YAAA9C,EAAA,CAAA+C,aAAA,CAAAC,GAAA;YAAA,OAAAhD,EAAA,CAAAiD,WAAA,CAASL,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UAEpB1B,EAAA,CAAAkD,SAAA,WAA4B;UAIpClD,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiD;UAAxBD,EAAA,CAAA6C,UAAA,sBAAAM,qEAAA;YAAAnD,EAAA,CAAA+C,aAAA,CAAAC,GAAA;YAAA,OAAAhD,EAAA,CAAAiD,WAAA,CAAYL,GAAA,CAAAhB,QAAA,EAAU;UAAA,EAAC;UAG1C5B,EAFJ,CAAAC,cAAA,yBAAoB,aACa,iBACI;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UAEvFH,EADF,CAAAC,cAAA,eAA6B,kBAC0D;UAA5BD,EAAA,CAAA6C,UAAA,mBAAAO,qEAAA;YAAApD,EAAA,CAAA+C,aAAA,CAAAC,GAAA;YAAA,MAAAK,YAAA,GAAArD,EAAA,CAAAsD,WAAA;YAAA,OAAAtD,EAAA,CAAAiD,WAAA,CAASI,YAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UAClFvD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAHA,CAAAwD,UAAA,KAAAC,mDAAA,mBAA0D,KAAAC,0DAAA,gCAAA1D,EAAA,CAAA2D,sBAAA,CAGrC;UAGvB3D,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAAuG;UAA9DD,EAAA,CAAA6C,UAAA,oBAAAe,qEAAAC,MAAA;YAAA7D,EAAA,CAAA+C,aAAA,CAAAC,GAAA;YAAA,OAAAhD,EAAA,CAAAiD,WAAA,CAAUL,GAAA,CAAAzB,YAAA,CAAA0C,MAAA,CAAoB;UAAA,EAAC;UAAxE7D,EAAA,CAAAG,YAAA,EAAuG;UACvGH,EAAA,CAAAwD,UAAA,KAAAM,uDAAA,uBAA4F;UAG9F9D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,0BAAgD,iBACnC;UAAAD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtDH,EAAA,CAAAkD,SAAA,iBAA6D;UAEjElD,EADE,CAAAG,YAAA,EAAiB,EACE;UAEnBH,EADF,CAAAC,cAAA,8BAAuD,kBACsB;UAArBD,EAAA,CAAA6C,UAAA,mBAAAkB,qEAAA;YAAA/D,EAAA,CAAA+C,aAAA,CAAAC,GAAA;YAAA,OAAAhD,EAAA,CAAAiD,WAAA,CAASL,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UAAC1B,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1FH,EAAA,CAAAC,cAAA,kBAAkF;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAE5FF,EAF4F,CAAAG,YAAA,EAAS,EAC9E,EAChB;;;;;UA9BDH,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAgE,UAAA,cAAApB,GAAA,CAAA9B,IAAA,CAAkB;UASSd,EAAA,CAAAI,SAAA,IAAoB;UAAAJ,EAApB,CAAAgE,UAAA,SAAApB,GAAA,CAAArC,YAAA,CAAoB,aAAA0D,SAAA,CAAW;UAQ/CjE,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAgE,UAAA,WAAAE,OAAA,GAAAtB,GAAA,CAAA9B,IAAA,CAAAU,GAAA,2BAAA0C,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAtB,GAAA,CAAA9B,IAAA,CAAAU,GAAA,2BAAA0C,OAAA,CAAAE,OAAA,EAA4D;UAWjBpE,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAgE,UAAA,aAAApB,GAAA,CAAA9B,IAAA,CAAAqD,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}