{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 0) return 0;\n  if (n === 1) return 1;\n  if (n === 2) return 2;\n  if (n === 3) return 3;\n  if (n === 6) return 4;\n  return 5;\n}\nexport default [\"cy\", [[\"b\", \"h\"], [\"AM\", \"PM\"], [\"yb\", \"yh\"]], [[\"AM\", \"PM\"], u, u], [[\"S\", \"Ll\", \"M\", \"M\", \"I\", \"G\", \"S\"], [\"<PERSON>\", \"<PERSON>lun\", \"Maw\", \"Mer\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>yd<PERSON>\", \"<PERSON>yd<PERSON>lun\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>yd<PERSON>rn\"], [\"<PERSON>\", \"<PERSON>l\", \"<PERSON>\", \"<PERSON>\", \"Ia\", \"Gw\", \"Sa\"]], [[\"S\", \"Ll\", \"<PERSON>\", \"<PERSON>\", \"I\", \"G\", \"S\"], [\"<PERSON>\", \"Llun\", \"Maw\", \"Mer\", \"Iau\", \"Gwe\", \"Sad\"], [\"Dydd <PERSON>\", \"<PERSON>ydd Llun\", \"<PERSON>ydd <PERSON>wrth\", \"<PERSON>ydd Mercher\", \"Dydd Iau\", \"Dydd Gwener\", \"<PERSON>ydd <PERSON>wrn\"], [\"Su\", \"Ll\", \"Ma\", \"Me\", \"Ia\", \"Gw\", \"Sa\"]], [[\"I\", \"Ch\", \"M\", \"E\", \"M\", \"M\", \"G\", \"A\", \"M\", \"H\", \"T\", \"Rh\"], [\"Ion\", \"Chwef\", \"Maw\", \"Ebr\", \"Mai\", \"Meh\", \"Gorff\", \"Awst\", \"Medi\", \"Hyd\", \"Tach\", \"Rhag\"], [\"Ionawr\", \"Chwefror\", \"Mawrth\", \"Ebrill\", \"Mai\", \"Mehefin\", \"Gorffennaf\", \"Awst\", \"Medi\", \"Hydref\", \"Tachwedd\", \"Rhagfyr\"]], [[\"I\", \"Ch\", \"M\", \"E\", \"M\", \"M\", \"G\", \"A\", \"M\", \"H\", \"T\", \"Rh\"], [\"Ion\", \"Chw\", \"Maw\", \"Ebr\", \"Mai\", \"Meh\", \"Gor\", \"Awst\", \"Medi\", \"Hyd\", \"Tach\", \"Rhag\"], [\"Ionawr\", \"Chwefror\", \"Mawrth\", \"Ebrill\", \"Mai\", \"Mehefin\", \"Gorffennaf\", \"Awst\", \"Medi\", \"Hydref\", \"Tachwedd\", \"Rhagfyr\"]], [[\"C\", \"O\"], [\"CC\", \"OC\"], [\"Cyn Crist\", \"Oed Crist\"]], 1, [6, 0], [\"dd/MM/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, \"{1} 'am' {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"GBP\", \"£\", \"Punt Prydain\", {\n  \"BDT\": [u, \"TK\"],\n  \"BWP\": [],\n  \"BYN\": [u, \"р.\"],\n  \"HKD\": [\"HK$\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"XXX\": [],\n  \"ZAR\": [],\n  \"ZMW\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/cy.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 0)\n        return 0;\n    if (n === 1)\n        return 1;\n    if (n === 2)\n        return 2;\n    if (n === 3)\n        return 3;\n    if (n === 6)\n        return 4;\n    return 5;\n}\nexport default [\"cy\", [[\"b\", \"h\"], [\"AM\", \"PM\"], [\"yb\", \"yh\"]], [[\"AM\", \"PM\"], u, u], [[\"S\", \"Ll\", \"M\", \"M\", \"I\", \"G\", \"S\"], [\"<PERSON>\", \"<PERSON>lun\", \"Maw\", \"Mer\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON>yd<PERSON>\", \"<PERSON>yd<PERSON>lun\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>yd<PERSON>rn\"], [\"<PERSON>\", \"<PERSON>l\", \"<PERSON>\", \"<PERSON>\", \"Ia\", \"Gw\", \"Sa\"]], [[\"S\", \"Ll\", \"<PERSON>\", \"<PERSON>\", \"I\", \"G\", \"S\"], [\"<PERSON>\", \"Llun\", \"Maw\", \"Mer\", \"Iau\", \"Gwe\", \"Sad\"], [\"Dydd <PERSON>\", \"<PERSON>ydd Llun\", \"<PERSON>ydd <PERSON>wrth\", \"<PERSON>ydd Mercher\", \"Dydd Iau\", \"Dydd Gwener\", \"<PERSON>ydd <PERSON>wrn\"], [\"Su\", \"Ll\", \"Ma\", \"Me\", \"Ia\", \"Gw\", \"Sa\"]], [[\"I\", \"Ch\", \"M\", \"E\", \"M\", \"M\", \"G\", \"A\", \"M\", \"H\", \"T\", \"Rh\"], [\"Ion\", \"Chwef\", \"Maw\", \"Ebr\", \"Mai\", \"Meh\", \"Gorff\", \"Awst\", \"Medi\", \"Hyd\", \"Tach\", \"Rhag\"], [\"Ionawr\", \"Chwefror\", \"Mawrth\", \"Ebrill\", \"Mai\", \"Mehefin\", \"Gorffennaf\", \"Awst\", \"Medi\", \"Hydref\", \"Tachwedd\", \"Rhagfyr\"]], [[\"I\", \"Ch\", \"M\", \"E\", \"M\", \"M\", \"G\", \"A\", \"M\", \"H\", \"T\", \"Rh\"], [\"Ion\", \"Chw\", \"Maw\", \"Ebr\", \"Mai\", \"Meh\", \"Gor\", \"Awst\", \"Medi\", \"Hyd\", \"Tach\", \"Rhag\"], [\"Ionawr\", \"Chwefror\", \"Mawrth\", \"Ebrill\", \"Mai\", \"Mehefin\", \"Gorffennaf\", \"Awst\", \"Medi\", \"Hydref\", \"Tachwedd\", \"Rhagfyr\"]], [[\"C\", \"O\"], [\"CC\", \"OC\"], [\"Cyn Crist\", \"Oed Crist\"]], 1, [6, 0], [\"dd/MM/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, \"{1} 'am' {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"GBP\", \"£\", \"Punt Prydain\", { \"BDT\": [u, \"TK\"], \"BWP\": [], \"BYN\": [u, \"р.\"], \"HKD\": [\"HK$\"], \"JPY\": [\"JP¥\", \"¥\"], \"KRW\": [u, \"₩\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"], \"USD\": [\"US$\", \"$\"], \"XXX\": [], \"ZAR\": [], \"ZMW\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,IAAIA,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAE,cAAc,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}