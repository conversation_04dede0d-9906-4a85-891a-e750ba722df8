{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"dav\", [[\"Luma lwa K\", \"luma lwa p\"], u, u], u, [[\"J\", \"J\", \"K\", \"K\", \"K\", \"K\", \"N\"], [\"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>w\", \"Kad\", \"Kan\", \"Ka<PERSON>\", \"<PERSON>u\"], [\"<PERSON>uku ja jumwa\", \"<PERSON><PERSON><PERSON> jimweri\", \"<PERSON><PERSON>uka kawi\", \"<PERSON><PERSON>uka kadadu\", \"<PERSON><PERSON>uka kana\", \"<PERSON><PERSON><PERSON> kasanu\", \"<PERSON><PERSON>a nguwo\"], [\"Jum\", \"<PERSON>\", \"<PERSON>w\", \"Kad\", \"Kan\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"W\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"Imb\", \"Kaw\", \"Kad\", \"Kan\", \"<PERSON>s\", \"Kar\", \"<PERSON>fu\", \"<PERSON>n\", \"Ike\", \"Iku\", \"Imw\", \"Iwi\"], [\"<PERSON>ri ghwa imbiri\", \"<PERSON>ri ghwa kawi\", \"<PERSON>ri ghwa kadadu\", \"<PERSON>ri ghwa kana\", \"<PERSON>ri ghwa kasanu\", \"<PERSON>ri ghwa ka<PERSON>adu\", \"<PERSON>ri ghwa mfungade\", \"<PERSON>ri ghwa wunyanya\", \"<PERSON>ri ghwa ikenda\", \"Mori ghwa ikumi\", \"Mori ghwa ikumi na imweri\", \"Mori ghwa ikumi na iwi\"]], u, [[\"KK\", \"BK\"], u, [\"Kabla ya Kristo\", \"Baada ya Kristo\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"KES\", \"Ksh\", \"Shilingi ya Kenya\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KES\": [\"Ksh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/dav.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"dav\", [[\"Luma lwa K\", \"luma lwa p\"], u, u], u, [[\"J\", \"J\", \"K\", \"K\", \"K\", \"K\", \"N\"], [\"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>w\", \"Kad\", \"Kan\", \"Ka<PERSON>\", \"<PERSON>u\"], [\"<PERSON>uku ja jumwa\", \"<PERSON><PERSON><PERSON> jimweri\", \"<PERSON><PERSON>uka kawi\", \"<PERSON><PERSON>uka kadadu\", \"<PERSON><PERSON>uka kana\", \"<PERSON><PERSON><PERSON> kasanu\", \"<PERSON><PERSON>a nguwo\"], [\"Jum\", \"<PERSON>\", \"<PERSON>w\", \"Kad\", \"Kan\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"W\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"], [\"Imb\", \"Kaw\", \"Kad\", \"Kan\", \"<PERSON>s\", \"Kar\", \"<PERSON>fu\", \"<PERSON>n\", \"Ike\", \"Iku\", \"Imw\", \"Iwi\"], [\"<PERSON>ri ghwa imbiri\", \"<PERSON>ri ghwa kawi\", \"<PERSON>ri ghwa kadadu\", \"<PERSON>ri ghwa kana\", \"<PERSON>ri ghwa kasanu\", \"<PERSON>ri ghwa ka<PERSON>adu\", \"<PERSON>ri ghwa mfungade\", \"<PERSON>ri ghwa wunyanya\", \"<PERSON>ri ghwa ikenda\", \"Mori ghwa ikumi\", \"Mori ghwa ikumi na imweri\", \"Mori ghwa ikumi na iwi\"]], u, [[\"KK\", \"BK\"], u, [\"Kabla ya Kristo\", \"Baada ya Kristo\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"KES\", \"Ksh\", \"Shilingi ya Kenya\", { \"JPY\": [\"JP¥\", \"¥\"], \"KES\": [\"Ksh\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,2BAA2B,EAAE,wBAAwB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}