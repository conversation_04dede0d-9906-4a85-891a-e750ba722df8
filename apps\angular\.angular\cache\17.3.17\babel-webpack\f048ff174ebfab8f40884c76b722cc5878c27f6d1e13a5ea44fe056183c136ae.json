{"ast": null, "code": "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n  const color = new TinyColor(firstColor);\n  const hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n  let secondHex8String = hex8String;\n  const gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n  if (secondColor) {\n    const s = new TinyColor(secondColor);\n    secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n  }\n  return `progid:DXImageTransform.Microsoft.gradient(${gradientType}startColorstr=${hex8String},endColorstr=${secondHex8String})`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}