{"ast": null, "code": "import { createSelector, createFeatureSelector } from '@ngrx/store';\nimport { APP } from '@app/shared/constants';\nconst isSignedInState = state => state.isSignedIn;\nconst selectState = createFeatureSelector(APP);\nexport const selectIsSignedIn = createSelector(selectState, isSignedInState);\n/** Example how to combine multiple selectors */\n// export const userAndHasValidAccessToken = createSelector(\n//     user,\n//     hasValidAccessToken,\n//     (userD: IUser | null, hasValidAccessTokenD: boolean) => {\n//         return {\n//             user: userD,\n//             hasValidAccessToken: hasValidAccessTokenD\n//         };\n//     }\n// );", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}