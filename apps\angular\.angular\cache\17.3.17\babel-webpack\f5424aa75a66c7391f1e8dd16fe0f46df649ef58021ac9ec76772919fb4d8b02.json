{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"te\", [[\"ఉ\", \"సా\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"ఆ\", \"సో\", \"మ\", \"బు\", \"గు\", \"శు\", \"శ\"], [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"], [\"ఆదివారం\", \"సోమవారం\", \"మంగళవారం\", \"బుధవారం\", \"గురువారం\", \"శుక్రవారం\", \"శనివారం\"], [\"ఆది\", \"సోమ\", \"మం\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"]], u, [[\"జ\", \"ఫి\", \"మా\", \"ఏ\", \"మే\", \"జూ\", \"జు\", \"ఆ\", \"సె\", \"అ\", \"న\", \"డి\"], [\"జన\", \"ఫిబ్ర\", \"మార్చి\", \"ఏప్రి\", \"మే\", \"జూన్\", \"జులై\", \"ఆగ\", \"సెప్టెం\", \"అక్టో\", \"నవం\", \"డిసెం\"], [\"జనవరి\", \"ఫిబ్రవరి\", \"మార్చి\", \"ఏప్రిల్\", \"మే\", \"జూన్\", \"జులై\", \"ఆగస్టు\", \"సెప్టెంబర్\", \"అక్టోబర్\", \"నవంబర్\", \"డిసెంబర్\"]], u, [[\"క్రీపూ\", \"క్రీశ\"], u, [\"క్రీస్తు పూర్వం\", \"క్రీస్తు శకం\"]], 0, [0, 0], [\"dd-MM-yy\", \"d MMM, y\", \"d MMMM, y\", \"d, MMMM y, EEEE\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, \"{1} {0}కి\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##0%\", \"¤#,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"భారతదేశ రూపాయి\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/te.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"te\", [[\"ఉ\", \"సా\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"ఆ\", \"సో\", \"మ\", \"బు\", \"గు\", \"శు\", \"శ\"], [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"], [\"ఆదివారం\", \"సోమవారం\", \"మంగళవారం\", \"బుధవారం\", \"గురువారం\", \"శుక్రవారం\", \"శనివారం\"], [\"ఆది\", \"సోమ\", \"మం\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"]], u, [[\"జ\", \"ఫి\", \"మా\", \"ఏ\", \"మే\", \"జూ\", \"జు\", \"ఆ\", \"సె\", \"అ\", \"న\", \"డి\"], [\"జన\", \"ఫిబ్ర\", \"మార్చి\", \"ఏప్రి\", \"మే\", \"జూన్\", \"జులై\", \"ఆగ\", \"సెప్టెం\", \"అక్టో\", \"నవం\", \"డిసెం\"], [\"జనవరి\", \"ఫిబ్రవరి\", \"మార్చి\", \"ఏప్రిల్\", \"మే\", \"జూన్\", \"జులై\", \"ఆగస్టు\", \"సెప్టెంబర్\", \"అక్టోబర్\", \"నవంబర్\", \"డిసెంబర్\"]], u, [[\"క్రీపూ\", \"క్రీశ\"], u, [\"క్రీస్తు పూర్వం\", \"క్రీస్తు శకం\"]], 0, [0, 0], [\"dd-MM-yy\", \"d MMM, y\", \"d MMMM, y\", \"d, MMMM y, EEEE\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", u, \"{1} {0}కి\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##0%\", \"¤#,##,##0.00\", \"#E0\"], \"INR\", \"₹\", \"భారతదేశ రూపాయి\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TWD\": [\"NT$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAE,WAAW,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,gBAAgB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}