{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Dependencies Plugin, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n// @skip-file-minify\n// ##############################################################\n// AUTO GENERATED FILE: This file is Auto Generated during build.\n// ##############################################################\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n// Note: DON'T Export these const from the package as we are still targeting ES3 this will export a mutable variables that someone could change!!!\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\nexport var _DYN_REQUEST_URL = \"requestUrl\"; // Count: 12\nexport var _DYN_INST = \"inst\"; // Count: 5\nexport var _DYN_LENGTH = \"length\"; // Count: 10\nexport var _DYN_TRACE_ID = \"traceID\"; // Count: 9\nexport var _DYN_SPAN_ID = \"spanID\"; // Count: 8\nexport var _DYN_TRACE_FLAGS = \"traceFlags\"; // Count: 13\nexport var _DYN_CONTEXT = \"context\"; // Count: 7\nexport var _DYN_ABORTED = \"aborted\"; // Count: 7\nexport var _DYN_TRACE_ID0 = \"traceId\"; // Count: 5\nexport var _DYN_SPAN_ID1 = \"spanId\"; // Count: 5\nexport var _DYN_CORE = \"core\"; // Count: 8\nexport var _DYN_INCLUDE_CORRELATION_2 = \"includeCorrelationHeaders\"; // Count: 4\nexport var _DYN_CAN_INCLUDE_CORRELAT3 = \"canIncludeCorrelationHeader\"; // Count: 2\nexport var _DYN_GET_ABSOLUTE_URL = \"getAbsoluteUrl\"; // Count: 3\nexport var _DYN_HEADERS = \"headers\"; // Count: 6\nexport var _DYN_REQUEST_HEADERS = \"requestHeaders\"; // Count: 13\nexport var _DYN_APP_ID = \"appId\"; // Count: 5\nexport var _DYN_SET_REQUEST_HEADER = \"setRequestHeader\"; // Count: 3\nexport var _DYN_TRACK_DEPENDENCY_DAT4 = \"trackDependencyDataInternal\"; // Count: 2\nexport var _DYN_DISTRIBUTED_TRACING_5 = \"distributedTracingMode\"; // Count: 3\nexport var _DYN_START_TIME = \"startTime\"; // Count: 6\nexport var _DYN_TO_LOWER_CASE = \"toLowerCase\"; // Count: 6\nexport var _DYN_ENABLE_REQUEST_HEADE6 = \"enableRequestHeaderTracking\"; // Count: 2\nexport var _DYN_ENABLE_AJAX_ERROR_ST7 = \"enableAjaxErrorStatusText\"; // Count: 2\nexport var _DYN_ENABLE_AJAX_PERF_TRA8 = \"enableAjaxPerfTracking\"; // Count: 2\nexport var _DYN_MAX_AJAX_CALLS_PER_V9 = \"maxAjaxCallsPerView\"; // Count: 2\nexport var _DYN_ENABLE_RESPONSE_HEAD10 = \"enableResponseHeaderTracking\"; // Count: 2\nexport var _DYN_EXCLUDE_REQUEST_FROM11 = \"excludeRequestFromAutoTrackingPatterns\"; // Count: 2\nexport var _DYN_ADD_REQUEST_CONTEXT = \"addRequestContext\"; // Count: 2\nexport var _DYN_DISABLE_AJAX_TRACKIN12 = \"disableAjaxTracking\"; // Count: 2\nexport var _DYN_DISABLE_FETCH_TRACKI13 = \"disableFetchTracking\"; // Count: 2\nexport var _DYN_STATUS = \"status\"; // Count: 11\nexport var _DYN_STATUS_TEXT = \"statusText\"; // Count: 9\nexport var _DYN_HEADER_MAP = \"headerMap\"; // Count: 8\nexport var _DYN_OPEN_DONE = \"openDone\"; // Count: 3\nexport var _DYN_SEND_DONE = \"sendDone\"; // Count: 3\nexport var _DYN_REQUEST_SENT_TIME = \"requestSentTime\"; // Count: 9\nexport var _DYN_ABORT_DONE = \"abortDone\"; // Count: 3\nexport var _DYN_GET_TRACE_ID = \"getTraceId\"; // Count: 3\nexport var _DYN_GET_TRACE_FLAGS = \"getTraceFlags\"; // Count: 3\nexport var _DYN_METHOD = \"method\"; // Count: 8\nexport var _DYN_ERROR_STATUS_TEXT = \"errorStatusText\"; // Count: 3\nexport var _DYN_STATE_CHANGE_ATTACHE14 = \"stateChangeAttached\"; // Count: 2\nexport var _DYN_RESPONSE_TEXT = \"responseText\"; // Count: 6\nexport var _DYN_RESPONSE_FINISHED_TI15 = \"responseFinishedTime\"; // Count: 7\nexport var _DYN__CREATE_TRACK_ITEM = \"CreateTrackItem\"; // Count: 3\nexport var _DYN_RESPONSE = \"response\"; // Count: 4\nexport var _DYN_GET_ALL_RESPONSE_HEA16 = \"getAllResponseHeaders\"; // Count: 2\nexport var _DYN_GET_PART_APROPS = \"getPartAProps\"; // Count: 3\nexport var _DYN_GET_CORRELATION_CONT17 = \"getCorrelationContext\"; // Count: 2\nexport var _DYN_PERF_MARK = \"perfMark\"; // Count: 4\nexport var _DYN_AJAX_PERF_LOOKUP_DEL18 = \"ajaxPerfLookupDelay\"; // Count: 2\nexport var _DYN_NAME = \"name\"; // Count: 6\nexport var _DYN_PERF_TIMING = \"perfTiming\"; // Count: 3\nexport var _DYN_AJAX_DIAGNOSTICS_MES19 = \"ajaxDiagnosticsMessage\"; // Count: 3\nexport var _DYN_CORRELATION_CONTEXT = \"correlationContext\"; // Count: 3\nexport var _DYN_AJAX_TOTAL_DURATION = \"ajaxTotalDuration\"; // Count: 3\nexport var _DYN_EVENT_TRACE_CTX = \"eventTraceCtx\"; // Count: 3\n//# sourceMappingURL=__DynamicConstants.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}