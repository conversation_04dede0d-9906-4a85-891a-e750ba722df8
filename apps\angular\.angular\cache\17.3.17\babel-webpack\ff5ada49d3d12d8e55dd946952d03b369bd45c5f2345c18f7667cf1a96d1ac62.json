{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Web Analytics, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n// @skip-file-minify\n// ##############################################################\n// AUTO GENERATED FILE: This file is Auto Generated during build.\n// ##############################################################\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n// Note: DON'T Export these const from the package as we are still targeting ES3 this will export a mutable variables that someone could change!!!\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\nexport var _DYN_TO_STRING = \"toString\"; // Count: 4\nexport var _DYN_DISABLE_EXCEPTION_TR0 = \"disableExceptionTracking\"; // Count: 3\nexport var _DYN_AUTO_TRACK_PAGE_VISI1 = \"autoTrackPageVisitTime\"; // Count: 4\nexport var _DYN_OVERRIDE_PAGE_VIEW_D2 = \"overridePageViewDuration\"; // Count: 3\nexport var _DYN_ENABLE_UNHANDLED_PRO3 = \"enableUnhandledPromiseRejectionTracking\"; // Count: 3\nexport var _DYN_SAMPLING_PERCENTAGE = \"samplingPercentage\"; // Count: 4\nexport var _DYN_IS_STORAGE_USE_DISAB4 = \"isStorageUseDisabled\"; // Count: 4\nexport var _DYN_IS_BROWSER_LINK_TRAC5 = \"isBrowserLinkTrackingEnabled\"; // Count: 4\nexport var _DYN_ENABLE_AUTO_ROUTE_TR6 = \"enableAutoRouteTracking\"; // Count: 3\nexport var _DYN_NAME_PREFIX = \"namePrefix\"; // Count: 3\nexport var _DYN_DISABLE_FLUSH_ON_BEF7 = \"disableFlushOnBeforeUnload\"; // Count: 3\nexport var _DYN_DISABLE_FLUSH_ON_UNL8 = \"disableFlushOnUnload\"; // Count: 2\nexport var _DYN_CORE = \"core\"; // Count: 7\nexport var _DYN_DATA_TYPE = \"dataType\"; // Count: 8\nexport var _DYN_ENVELOPE_TYPE = \"envelopeType\"; // Count: 7\nexport var _DYN_DIAG_LOG = \"diagLog\"; // Count: 13\nexport var _DYN_TRACK = \"track\"; // Count: 7\nexport var _DYN_TRACK_PAGE_VIEW = \"trackPageView\"; // Count: 4\nexport var _DYN_TRACK_PREVIOUS_PAGE_9 = \"trackPreviousPageVisit\"; // Count: 3\nexport var _DYN_SEND_PAGE_VIEW_INTER10 = \"sendPageViewInternal\"; // Count: 7\nexport var _DYN_SEND_PAGE_VIEW_PERFO11 = \"sendPageViewPerformanceInternal\"; // Count: 3\nexport var _DYN_POPULATE_PAGE_VIEW_P12 = \"populatePageViewPerformanceEvent\"; // Count: 3\nexport var _DYN_HREF = \"href\"; // Count: 6\nexport var _DYN_SEND_EXCEPTION_INTER13 = \"sendExceptionInternal\"; // Count: 2\nexport var _DYN_EXCEPTION = \"exception\"; // Count: 3\nexport var _DYN_ERROR = \"error\"; // Count: 5\nexport var _DYN__ONERROR = \"_onerror\"; // Count: 3\nexport var _DYN_ERROR_SRC = \"errorSrc\"; // Count: 3\nexport var _DYN_LINE_NUMBER = \"lineNumber\"; // Count: 5\nexport var _DYN_COLUMN_NUMBER = \"columnNumber\"; // Count: 5\nexport var _DYN_MESSAGE = \"message\"; // Count: 4\nexport var _DYN__CREATE_AUTO_EXCEPTI14 = \"CreateAutoException\"; // Count: 3\nexport var _DYN_ADD_TELEMETRY_INITIA15 = \"addTelemetryInitializer\"; // Count: 4\nexport var _DYN_DURATION = \"duration\"; // Count: 10\nexport var _DYN_LENGTH = \"length\"; // Count: 5\nexport var _DYN_IS_PERFORMANCE_TIMIN16 = \"isPerformanceTimingSupported\"; // Count: 2\nexport var _DYN_GET_PERFORMANCE_TIMI17 = \"getPerformanceTiming\"; // Count: 2\nexport var _DYN_NAVIGATION_START = \"navigationStart\"; // Count: 4\nexport var _DYN_SHOULD_COLLECT_DURAT18 = \"shouldCollectDuration\"; // Count: 3\nexport var _DYN_IS_PERFORMANCE_TIMIN19 = \"isPerformanceTimingDataReady\"; // Count: 2\nexport var _DYN_GET_ENTRIES_BY_TYPE = \"getEntriesByType\"; // Count: 3\nexport var _DYN_RESPONSE_START = \"responseStart\"; // Count: 5\nexport var _DYN_REQUEST_START = \"requestStart\"; // Count: 3\nexport var _DYN_LOAD_EVENT_END = \"loadEventEnd\"; // Count: 4\nexport var _DYN_RESPONSE_END = \"responseEnd\"; // Count: 5\nexport var _DYN_CONNECT_END = \"connectEnd\"; // Count: 4\nexport var _DYN_PAGE_VISIT_START_TIM20 = \"pageVisitStartTime\"; // Count: 2", "map": {"version": 3, "names": ["_DYN_TO_STRING", "_DYN_DISABLE_EXCEPTION_TR0", "_DYN_AUTO_TRACK_PAGE_VISI1", "_DYN_OVERRIDE_PAGE_VIEW_D2", "_DYN_ENABLE_UNHANDLED_PRO3", "_DYN_SAMPLING_PERCENTAGE", "_DYN_IS_STORAGE_USE_DISAB4", "_DYN_IS_BROWSER_LINK_TRAC5", "_DYN_ENABLE_AUTO_ROUTE_TR6", "_DYN_NAME_PREFIX", "_DYN_DISABLE_FLUSH_ON_BEF7", "_DYN_DISABLE_FLUSH_ON_UNL8", "_DYN_CORE", "_DYN_DATA_TYPE", "_DYN_ENVELOPE_TYPE", "_DYN_DIAG_LOG", "_DYN_TRACK", "_DYN_TRACK_PAGE_VIEW", "_DYN_TRACK_PREVIOUS_PAGE_9", "_DYN_SEND_PAGE_VIEW_INTER10", "_DYN_SEND_PAGE_VIEW_PERFO11", "_DYN_POPULATE_PAGE_VIEW_P12", "_DYN_HREF", "_DYN_SEND_EXCEPTION_INTER13", "_DYN_EXCEPTION", "_DYN_ERROR", "_DYN__ONERROR", "_DYN_ERROR_SRC", "_DYN_LINE_NUMBER", "_DYN_COLUMN_NUMBER", "_DYN_MESSAGE", "_DYN__CREATE_AUTO_EXCEPTI14", "_DYN_ADD_TELEMETRY_INITIA15", "_DYN_DURATION", "_DYN_LENGTH", "_DYN_IS_PERFORMANCE_TIMIN16", "_DYN_GET_PERFORMANCE_TIMI17", "_DYN_NAVIGATION_START", "_DYN_SHOULD_COLLECT_DURAT18", "_DYN_IS_PERFORMANCE_TIMIN19", "_DYN_GET_ENTRIES_BY_TYPE", "_DYN_RESPONSE_START", "_DYN_REQUEST_START", "_DYN_LOAD_EVENT_END", "_DYN_RESPONSE_END", "_DYN_CONNECT_END", "_DYN_PAGE_VISIT_START_TIM20"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-analytics-js/dist-esm/__DynamicConstants.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Web Analytics, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\n// @skip-file-minify\r\n// ##############################################################\r\n// AUTO GENERATED FILE: This file is Auto Generated during build.\r\n// ##############################################################\r\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\r\n// Note: DON'T Export these const from the package as we are still targeting ES3 this will export a mutable variables that someone could change!!!\r\n// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\r\nexport var _DYN_TO_STRING = \"toString\"; // Count: 4\r\nexport var _DYN_DISABLE_EXCEPTION_TR0 = \"disableExceptionTracking\"; // Count: 3\r\nexport var _DYN_AUTO_TRACK_PAGE_VISI1 = \"autoTrackPageVisitTime\"; // Count: 4\r\nexport var _DYN_OVERRIDE_PAGE_VIEW_D2 = \"overridePageViewDuration\"; // Count: 3\r\nexport var _DYN_ENABLE_UNHANDLED_PRO3 = \"enableUnhandledPromiseRejectionTracking\"; // Count: 3\r\nexport var _DYN_SAMPLING_PERCENTAGE = \"samplingPercentage\"; // Count: 4\r\nexport var _DYN_IS_STORAGE_USE_DISAB4 = \"isStorageUseDisabled\"; // Count: 4\r\nexport var _DYN_IS_BROWSER_LINK_TRAC5 = \"isBrowserLinkTrackingEnabled\"; // Count: 4\r\nexport var _DYN_ENABLE_AUTO_ROUTE_TR6 = \"enableAutoRouteTracking\"; // Count: 3\r\nexport var _DYN_NAME_PREFIX = \"namePrefix\"; // Count: 3\r\nexport var _DYN_DISABLE_FLUSH_ON_BEF7 = \"disableFlushOnBeforeUnload\"; // Count: 3\r\nexport var _DYN_DISABLE_FLUSH_ON_UNL8 = \"disableFlushOnUnload\"; // Count: 2\r\nexport var _DYN_CORE = \"core\"; // Count: 7\r\nexport var _DYN_DATA_TYPE = \"dataType\"; // Count: 8\r\nexport var _DYN_ENVELOPE_TYPE = \"envelopeType\"; // Count: 7\r\nexport var _DYN_DIAG_LOG = \"diagLog\"; // Count: 13\r\nexport var _DYN_TRACK = \"track\"; // Count: 7\r\nexport var _DYN_TRACK_PAGE_VIEW = \"trackPageView\"; // Count: 4\r\nexport var _DYN_TRACK_PREVIOUS_PAGE_9 = \"trackPreviousPageVisit\"; // Count: 3\r\nexport var _DYN_SEND_PAGE_VIEW_INTER10 = \"sendPageViewInternal\"; // Count: 7\r\nexport var _DYN_SEND_PAGE_VIEW_PERFO11 = \"sendPageViewPerformanceInternal\"; // Count: 3\r\nexport var _DYN_POPULATE_PAGE_VIEW_P12 = \"populatePageViewPerformanceEvent\"; // Count: 3\r\nexport var _DYN_HREF = \"href\"; // Count: 6\r\nexport var _DYN_SEND_EXCEPTION_INTER13 = \"sendExceptionInternal\"; // Count: 2\r\nexport var _DYN_EXCEPTION = \"exception\"; // Count: 3\r\nexport var _DYN_ERROR = \"error\"; // Count: 5\r\nexport var _DYN__ONERROR = \"_onerror\"; // Count: 3\r\nexport var _DYN_ERROR_SRC = \"errorSrc\"; // Count: 3\r\nexport var _DYN_LINE_NUMBER = \"lineNumber\"; // Count: 5\r\nexport var _DYN_COLUMN_NUMBER = \"columnNumber\"; // Count: 5\r\nexport var _DYN_MESSAGE = \"message\"; // Count: 4\r\nexport var _DYN__CREATE_AUTO_EXCEPTI14 = \"CreateAutoException\"; // Count: 3\r\nexport var _DYN_ADD_TELEMETRY_INITIA15 = \"addTelemetryInitializer\"; // Count: 4\r\nexport var _DYN_DURATION = \"duration\"; // Count: 10\r\nexport var _DYN_LENGTH = \"length\"; // Count: 5\r\nexport var _DYN_IS_PERFORMANCE_TIMIN16 = \"isPerformanceTimingSupported\"; // Count: 2\r\nexport var _DYN_GET_PERFORMANCE_TIMI17 = \"getPerformanceTiming\"; // Count: 2\r\nexport var _DYN_NAVIGATION_START = \"navigationStart\"; // Count: 4\r\nexport var _DYN_SHOULD_COLLECT_DURAT18 = \"shouldCollectDuration\"; // Count: 3\r\nexport var _DYN_IS_PERFORMANCE_TIMIN19 = \"isPerformanceTimingDataReady\"; // Count: 2\r\nexport var _DYN_GET_ENTRIES_BY_TYPE = \"getEntriesByType\"; // Count: 3\r\nexport var _DYN_RESPONSE_START = \"responseStart\"; // Count: 5\r\nexport var _DYN_REQUEST_START = \"requestStart\"; // Count: 3\r\nexport var _DYN_LOAD_EVENT_END = \"loadEventEnd\"; // Count: 4\r\nexport var _DYN_RESPONSE_END = \"responseEnd\"; // Count: 5\r\nexport var _DYN_CONNECT_END = \"connectEnd\"; // Count: 4\r\nexport var _DYN_PAGE_VISIT_START_TIM20 = \"pageVisitStartTime\"; // Count: 2\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,cAAc,GAAG,UAAU,CAAC,CAAC;AACxC,OAAO,IAAIC,0BAA0B,GAAG,0BAA0B,CAAC,CAAC;AACpE,OAAO,IAAIC,0BAA0B,GAAG,wBAAwB,CAAC,CAAC;AAClE,OAAO,IAAIC,0BAA0B,GAAG,0BAA0B,CAAC,CAAC;AACpE,OAAO,IAAIC,0BAA0B,GAAG,yCAAyC,CAAC,CAAC;AACnF,OAAO,IAAIC,wBAAwB,GAAG,oBAAoB,CAAC,CAAC;AAC5D,OAAO,IAAIC,0BAA0B,GAAG,sBAAsB,CAAC,CAAC;AAChE,OAAO,IAAIC,0BAA0B,GAAG,8BAA8B,CAAC,CAAC;AACxE,OAAO,IAAIC,0BAA0B,GAAG,yBAAyB,CAAC,CAAC;AACnE,OAAO,IAAIC,gBAAgB,GAAG,YAAY,CAAC,CAAC;AAC5C,OAAO,IAAIC,0BAA0B,GAAG,4BAA4B,CAAC,CAAC;AACtE,OAAO,IAAIC,0BAA0B,GAAG,sBAAsB,CAAC,CAAC;AAChE,OAAO,IAAIC,SAAS,GAAG,MAAM,CAAC,CAAC;AAC/B,OAAO,IAAIC,cAAc,GAAG,UAAU,CAAC,CAAC;AACxC,OAAO,IAAIC,kBAAkB,GAAG,cAAc,CAAC,CAAC;AAChD,OAAO,IAAIC,aAAa,GAAG,SAAS,CAAC,CAAC;AACtC,OAAO,IAAIC,UAAU,GAAG,OAAO,CAAC,CAAC;AACjC,OAAO,IAAIC,oBAAoB,GAAG,eAAe,CAAC,CAAC;AACnD,OAAO,IAAIC,0BAA0B,GAAG,wBAAwB,CAAC,CAAC;AAClE,OAAO,IAAIC,2BAA2B,GAAG,sBAAsB,CAAC,CAAC;AACjE,OAAO,IAAIC,2BAA2B,GAAG,iCAAiC,CAAC,CAAC;AAC5E,OAAO,IAAIC,2BAA2B,GAAG,kCAAkC,CAAC,CAAC;AAC7E,OAAO,IAAIC,SAAS,GAAG,MAAM,CAAC,CAAC;AAC/B,OAAO,IAAIC,2BAA2B,GAAG,uBAAuB,CAAC,CAAC;AAClE,OAAO,IAAIC,cAAc,GAAG,WAAW,CAAC,CAAC;AACzC,OAAO,IAAIC,UAAU,GAAG,OAAO,CAAC,CAAC;AACjC,OAAO,IAAIC,aAAa,GAAG,UAAU,CAAC,CAAC;AACvC,OAAO,IAAIC,cAAc,GAAG,UAAU,CAAC,CAAC;AACxC,OAAO,IAAIC,gBAAgB,GAAG,YAAY,CAAC,CAAC;AAC5C,OAAO,IAAIC,kBAAkB,GAAG,cAAc,CAAC,CAAC;AAChD,OAAO,IAAIC,YAAY,GAAG,SAAS,CAAC,CAAC;AACrC,OAAO,IAAIC,2BAA2B,GAAG,qBAAqB,CAAC,CAAC;AAChE,OAAO,IAAIC,2BAA2B,GAAG,yBAAyB,CAAC,CAAC;AACpE,OAAO,IAAIC,aAAa,GAAG,UAAU,CAAC,CAAC;AACvC,OAAO,IAAIC,WAAW,GAAG,QAAQ,CAAC,CAAC;AACnC,OAAO,IAAIC,2BAA2B,GAAG,8BAA8B,CAAC,CAAC;AACzE,OAAO,IAAIC,2BAA2B,GAAG,sBAAsB,CAAC,CAAC;AACjE,OAAO,IAAIC,qBAAqB,GAAG,iBAAiB,CAAC,CAAC;AACtD,OAAO,IAAIC,2BAA2B,GAAG,uBAAuB,CAAC,CAAC;AAClE,OAAO,IAAIC,2BAA2B,GAAG,8BAA8B,CAAC,CAAC;AACzE,OAAO,IAAIC,wBAAwB,GAAG,kBAAkB,CAAC,CAAC;AAC1D,OAAO,IAAIC,mBAAmB,GAAG,eAAe,CAAC,CAAC;AAClD,OAAO,IAAIC,kBAAkB,GAAG,cAAc,CAAC,CAAC;AAChD,OAAO,IAAIC,mBAAmB,GAAG,cAAc,CAAC,CAAC;AACjD,OAAO,IAAIC,iBAAiB,GAAG,aAAa,CAAC,CAAC;AAC9C,OAAO,IAAIC,gBAAgB,GAAG,YAAY,CAAC,CAAC;AAC5C,OAAO,IAAIC,2BAA2B,GAAG,oBAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}