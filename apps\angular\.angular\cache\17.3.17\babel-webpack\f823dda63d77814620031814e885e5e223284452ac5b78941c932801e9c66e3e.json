{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { toISOString } from \"@microsoft/applicationinsights-core-js\";\nimport { strNotSpecified } from \"../../Constants\";\nimport { _DYN_NAME } from \"../../__DynamicConstants\";\nimport { dataSanitizeString } from \"./DataSanitizer\";\nvar Envelope = /** @class */function () {\n  /**\r\n   * Constructs a new instance of telemetry data.\r\n   */\n  function Envelope(logger, data, name) {\n    var _this = this;\n    var _self = this;\n    _self.ver = 1;\n    _self.sampleRate = 100.0;\n    _self.tags = {};\n    _self[_DYN_NAME /* @min:%2ename */] = dataSanitizeString(logger, name) || strNotSpecified;\n    _self.data = data;\n    _self.time = toISOString(new Date());\n    _self.aiDataContract = {\n      time: 1 /* FieldType.Required */,\n      iKey: 1 /* FieldType.Required */,\n      name: 1 /* FieldType.Required */,\n      sampleRate: function () {\n        return _this.sampleRate === 100 ? 4 /* FieldType.Hidden */ : 1 /* FieldType.Required */;\n      },\n      tags: 1 /* FieldType.Required */,\n      data: 1 /* FieldType.Required */\n    };\n  }\n  return Envelope;\n}();\nexport { Envelope };", "map": {"version": 3, "names": ["toISOString", "strNotSpecified", "_DYN_NAME", "dataSanitizeString", "Envelope", "logger", "data", "name", "_this", "_self", "ver", "sampleRate", "tags", "time", "Date", "aiDataContract", "i<PERSON>ey"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/Telemetry/Common/Envelope.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { toISOString } from \"@microsoft/applicationinsights-core-js\";\r\nimport { strNotSpecified } from \"../../Constants\";\r\nimport { _DYN_NAME } from \"../../__DynamicConstants\";\r\nimport { dataSanitizeString } from \"./DataSanitizer\";\r\nvar Envelope = /** @class */ (function () {\r\n    /**\r\n     * Constructs a new instance of telemetry data.\r\n     */\r\n    function Envelope(logger, data, name) {\r\n        var _this = this;\r\n        var _self = this;\r\n        _self.ver = 1;\r\n        _self.sampleRate = 100.0;\r\n        _self.tags = {};\r\n        _self[_DYN_NAME /* @min:%2ename */] = dataSanitizeString(logger, name) || strNotSpecified;\r\n        _self.data = data;\r\n        _self.time = toISOString(new Date());\r\n        _self.aiDataContract = {\r\n            time: 1 /* FieldType.Required */,\r\n            iKey: 1 /* FieldType.Required */,\r\n            name: 1 /* FieldType.Required */,\r\n            sampleRate: function () {\r\n                return (_this.sampleRate === 100) ? 4 /* FieldType.Hidden */ : 1 /* FieldType.Required */;\r\n            },\r\n            tags: 1 /* FieldType.Required */,\r\n            data: 1 /* FieldType.Required */\r\n        };\r\n    }\r\n    return Envelope;\r\n}());\r\nexport { Envelope };\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,WAAW,QAAQ,wCAAwC;AACpE,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC;AACJ;AACA;EACI,SAASA,QAAQA,CAACC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAClC,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,KAAK,GAAG,IAAI;IAChBA,KAAK,CAACC,GAAG,GAAG,CAAC;IACbD,KAAK,CAACE,UAAU,GAAG,KAAK;IACxBF,KAAK,CAACG,IAAI,GAAG,CAAC,CAAC;IACfH,KAAK,CAACP,SAAS,CAAC,mBAAmB,GAAGC,kBAAkB,CAACE,MAAM,EAAEE,IAAI,CAAC,IAAIN,eAAe;IACzFQ,KAAK,CAACH,IAAI,GAAGA,IAAI;IACjBG,KAAK,CAACI,IAAI,GAAGb,WAAW,CAAC,IAAIc,IAAI,CAAC,CAAC,CAAC;IACpCL,KAAK,CAACM,cAAc,GAAG;MACnBF,IAAI,EAAE,CAAC,CAAC;MACRG,IAAI,EAAE,CAAC,CAAC;MACRT,IAAI,EAAE,CAAC,CAAC;MACRI,UAAU,EAAE,SAAAA,CAAA,EAAY;QACpB,OAAQH,KAAK,CAACG,UAAU,KAAK,GAAG,GAAI,CAAC,CAAC,yBAAyB,CAAC,CAAC;MACrE,CAAC;MACDC,IAAI,EAAE,CAAC,CAAC;MACRN,IAAI,EAAE,CAAC,CAAC;IACZ,CAAC;EACL;EACA,OAAOF,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,SAASA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}