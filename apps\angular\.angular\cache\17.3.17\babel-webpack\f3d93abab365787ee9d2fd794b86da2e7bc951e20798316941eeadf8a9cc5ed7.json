{"ast": null, "code": "import { SharedModule } from '@app/shared/shared.module';\nimport { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';\nimport { MAT_DIALOG_DEFAULT_OPTIONS } from '@angular/material/dialog';\n// TODO remove temparary import\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { InformationExchangeRoutingModule } from './information-exchange.routing.module';\nimport { InformationExchangeMainComponent } from './containers/information-exchange-main/information-exchange-main.component';\nimport { InformationExchangeDetailsComponent } from './containers/information-exchange-details/information-exchange-details.component';\nimport { RecipientDialogBoxComponent } from './containers/recipient-dialog-box/recipient-dialog-box.component';\nimport { TwoDigitDecimalNumberDirective } from '../../shared/directives/two-digit-decimal-number.directive';\nimport { NumberToLetterPipe } from '../../shared/pipes/number-to-letter-pipe';\nimport { InformationExchangeImportComponent } from './containers/information-exchange-import/information-exchange-import.component';\nimport { InformationExchangeHistoryComponent } from './containers/information-exchange-history/information-exchange-history.component';\nimport { UpdateCaCertificateDialogComponent } from './containers/update-ca-certificate-dialog/update-ca-certificate-dialog.component';\nimport { ViewAssociatedExchangeRecordsComponent } from './containers/view-associated-exchange-records/view-associated-exchange-records.component';\nimport * as i0 from \"@angular/core\";\nexport class InformationExchangeModule {\n  static {\n    this.ɵfac = function InformationExchangeModule_Factory(t) {\n      return new (t || InformationExchangeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: InformationExchangeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: MAT_DIALOG_DEFAULT_OPTIONS,\n        useValue: {\n          autoFocus: true,\n          disableClose: true,\n          restoreFocus: false,\n          width: '50%'\n        }\n      }],\n      imports: [SharedModule, InformationExchangeRoutingModule, CommonModule, FormsModule, SweetAlert2Module]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(InformationExchangeModule, {\n    declarations: [InformationExchangeMainComponent, InformationExchangeDetailsComponent, InformationExchangeImportComponent, RecipientDialogBoxComponent, InformationExchangeHistoryComponent, TwoDigitDecimalNumberDirective, NumberToLetterPipe, UpdateCaCertificateDialogComponent, ViewAssociatedExchangeRecordsComponent],\n    imports: [SharedModule, InformationExchangeRoutingModule, CommonModule, FormsModule, SweetAlert2Module]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "SweetAlert2Module", "MAT_DIALOG_DEFAULT_OPTIONS", "CommonModule", "FormsModule", "InformationExchangeRoutingModule", "InformationExchangeMainComponent", "InformationExchangeDetailsComponent", "RecipientDialogBoxComponent", "TwoDigitDecimalNumberDirective", "NumberToLetterPipe", "InformationExchangeImportComponent", "InformationExchangeHistoryComponent", "UpdateCaCertificateDialogComponent", "ViewAssociatedExchangeRecordsComponent", "InformationExchangeModule", "provide", "useValue", "autoFocus", "disableClose", "restoreFocus", "width", "imports", "declarations"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\information-exchange.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { SharedModule } from '@app/shared/shared.module';\r\nimport { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';\r\n\r\nimport { MAT_DIALOG_DEFAULT_OPTIONS } from '@angular/material/dialog';\r\n// TODO remove temparary import\r\n\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { InformationExchangeRoutingModule } from './information-exchange.routing.module';\r\nimport { InformationExchangeMainComponent } from './containers/information-exchange-main/information-exchange-main.component';\r\nimport { InformationExchangeDetailsComponent } from './containers/information-exchange-details/information-exchange-details.component';\r\nimport { RecipientDialogBoxComponent } from './containers/recipient-dialog-box/recipient-dialog-box.component';\r\nimport { TwoDigitDecimalNumberDirective } from '../../shared/directives/two-digit-decimal-number.directive';\r\nimport { NumberToLetterPipe } from '../../shared/pipes/number-to-letter-pipe';\r\nimport { InformationExchangeImportComponent } from './containers/information-exchange-import/information-exchange-import.component';\r\nimport { InformationExchangeHistoryComponent } from './containers/information-exchange-history/information-exchange-history.component';\r\nimport { UpdateCaCertificateDialogComponent } from './containers/update-ca-certificate-dialog/update-ca-certificate-dialog.component';\r\nimport { ViewAssociatedExchangeRecordsComponent } from './containers/view-associated-exchange-records/view-associated-exchange-records.component';\r\n\r\n@NgModule({\r\n  imports: [\r\n    SharedModule,\r\n    InformationExchangeRoutingModule,\r\n    CommonModule,\r\n    FormsModule,\r\n    SweetAlert2Module,\r\n  ],\r\n  declarations: [\r\n    InformationExchangeMainComponent,\r\n    InformationExchangeDetailsComponent,\r\n    InformationExchangeImportComponent,\r\n    RecipientDialogBoxComponent,\r\n    InformationExchangeHistoryComponent,\r\n    TwoDigitDecimalNumberDirective,\r\n    NumberToLetterPipe,\r\n    UpdateCaCertificateDialogComponent,\r\n    ViewAssociatedExchangeRecordsComponent,\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: MAT_DIALOG_DEFAULT_OPTIONS,\r\n      useValue: {\r\n        autoFocus: true,\r\n        disableClose: true,\r\n        restoreFocus: false,\r\n        width: '50%',\r\n      },\r\n    }\r\n  ],\r\n})\r\nexport class InformationExchangeModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,8BAA8B;AAEhE,SAASC,0BAA0B,QAAQ,0BAA0B;AACrE;AAEA,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gCAAgC,QAAQ,uCAAuC;AACxF,SAASC,gCAAgC,QAAQ,4EAA4E;AAC7H,SAASC,mCAAmC,QAAQ,kFAAkF;AACtI,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,8BAA8B,QAAQ,4DAA4D;AAC3G,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,kCAAkC,QAAQ,gFAAgF;AACnI,SAASC,mCAAmC,QAAQ,kFAAkF;AACtI,SAASC,kCAAkC,QAAQ,kFAAkF;AACrI,SAASC,sCAAsC,QAAQ,0FAA0F;;AAiCjJ,OAAM,MAAOC,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;iBAZzB,CACT;QACEC,OAAO,EAAEd,0BAA0B;QACnCe,QAAQ,EAAE;UACRC,SAAS,EAAE,IAAI;UACfC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,KAAK;UACnBC,KAAK,EAAE;;OAEV,CACF;MAAAC,OAAA,GA3BCtB,YAAY,EACZK,gCAAgC,EAChCF,YAAY,EACZC,WAAW,EACXH,iBAAiB;IAAA;EAAA;;;2EAyBRc,yBAAyB;IAAAQ,YAAA,GAtBlCjB,gCAAgC,EAChCC,mCAAmC,EACnCI,kCAAkC,EAClCH,2BAA2B,EAC3BI,mCAAmC,EACnCH,8BAA8B,EAC9BC,kBAAkB,EAClBG,kCAAkC,EAClCC,sCAAsC;IAAAQ,OAAA,GAftCtB,YAAY,EACZK,gCAAgC,EAChCF,YAAY,EACZC,WAAW,EACXH,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}