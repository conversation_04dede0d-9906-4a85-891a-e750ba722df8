{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"sa\", [[\"AM\", \"PM\"], u, [\"पूर्वाह्न\", \"अपराह्न\"]], [[\"AM\", \"PM\"], u, u], [[\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"], [\"रवि\", \"सोम\", \"मंगल\", \"बुध\", \"गुरु\", \"शुक्र\", \"शनि\"], [\"रविवासरः\", \"सोमवासरः\", \"मंगलवासरः\", \"बुधवासरः\", \"गुरुवासर:\", \"शुक्रवासरः\", \"शनिवासरः\"], [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"]], u, [[\"ज\", \"फ\", \"मा\", \"अ\", \"म\", \"जू\", \"जु\", \"अ\", \"सि\", \"अ\", \"न\", \"दि\"], [\"जनवरी:\", \"फरवरी:\", \"मार्च:\", \"अप्रैल:\", \"मई\", \"जून:\", \"जुलाई:\", \"अगस्त:\", \"सितंबर:\", \"अक्तूबर:\", \"नवंबर:\", \"दिसंबर:\"], [\"जनवरीमासः\", \"फरवरीमासः\", \"मार्चमासः\", \"अप्रैलमासः\", \"मईमासः\", \"जूनमासः\", \"जुलाईमासः\", \"अगस्तमासः\", \"सितंबरमासः\", \"अक्तूबरमासः\", \"नवंबरमासः\", \"दिसंबरमासः\"]], [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"जनवरी:\", \"फरवरी:\", \"मार्च:\", \"अप्रैल:\", \"मई\", \"जून:\", \"जुलाई:\", \"अगस्त:\", \"सितंबर:\", \"अक्तूबर:\", \"नवंबर:\", \"दिसंबर:\"], [\"जनवरीमासः\", \"फरवरीमासः\", \"मार्चमासः\", \"अप्रैलमासः\", \"मईमासः\", \"जूनमासः\", \"जुलाईमासः\", \"अगस्तमासः\", \"सितंबरमासः\", \"अक्तूबरमासः\", \"नवंबरमासः\", \"दिसंबरमासः\"]], [[\"BCE\", \"CE\"], u, u], 0, [0, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} तदा {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤#,##,##0.00\", \"[#E0]\"], \"INR\", \"₹\", \"भारतीय रूप्यकम्\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/sa.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"sa\", [[\"AM\", \"PM\"], u, [\"पूर्वाह्न\", \"अपराह्न\"]], [[\"AM\", \"PM\"], u, u], [[\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"], [\"रवि\", \"सोम\", \"मंगल\", \"बुध\", \"गुरु\", \"शुक्र\", \"शनि\"], [\"रविवासरः\", \"सोमवासरः\", \"मंगलवासरः\", \"बुधवासरः\", \"गुरुवासर:\", \"शुक्रवासरः\", \"शनिवासरः\"], [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"]], u, [[\"ज\", \"फ\", \"मा\", \"अ\", \"म\", \"जू\", \"जु\", \"अ\", \"सि\", \"अ\", \"न\", \"दि\"], [\"जनवरी:\", \"फरवरी:\", \"मार्च:\", \"अप्रैल:\", \"मई\", \"जून:\", \"जुलाई:\", \"अगस्त:\", \"सितंबर:\", \"अक्तूबर:\", \"नवंबर:\", \"दिसंबर:\"], [\"जनवरीमासः\", \"फरवरीमासः\", \"मार्चमासः\", \"अप्रैलमासः\", \"मईमासः\", \"जूनमासः\", \"जुलाईमासः\", \"अगस्तमासः\", \"सितंबरमासः\", \"अक्तूबरमासः\", \"नवंबरमासः\", \"दिसंबरमासः\"]], [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"जनवरी:\", \"फरवरी:\", \"मार्च:\", \"अप्रैल:\", \"मई\", \"जून:\", \"जुलाई:\", \"अगस्त:\", \"सितंबर:\", \"अक्तूबर:\", \"नवंबर:\", \"दिसंबर:\"], [\"जनवरीमासः\", \"फरवरीमासः\", \"मार्चमासः\", \"अप्रैलमासः\", \"मईमासः\", \"जूनमासः\", \"जुलाईमासः\", \"अगस्तमासः\", \"सितंबरमासः\", \"अक्तूबरमासः\", \"नवंबरमासः\", \"दिसंबरमासः\"]], [[\"BCE\", \"CE\"], u, u], 0, [0, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} तदा {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤#,##,##0.00\", \"[#E0]\"], \"INR\", \"₹\", \"भारतीय रूप्यकम्\", { \"JPY\": [\"JP¥\", \"¥\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAEA,CAAC,EAAE,aAAa,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,iBAAiB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}