{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class AssessmentBlobService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.downloadInternalNoteAttachmentByDownloadDto = (downloadDto, config) => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/ESService/Assessment/DownloadInternalNoteAttachment',\n      params: {\n        declarationId: downloadDto.declarationId,\n        internalNoteId: downloadDto.internalNoteId,\n        fileName: downloadDto.fileName\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.uploadInternalNoteAttachmentByUploadDto = (uploadDto, config) => this.restService.request({\n      method: 'POST',\n      responseType: 'text',\n      url: '/api/ESService/Assessment/UploadInternalNoteAttachment',\n      body: uploadDto\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function AssessmentBlobService_Factory(t) {\n      return new (t || AssessmentBlobService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AssessmentBlobService,\n      factory: AssessmentBlobService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AssessmentBlobService", "constructor", "restService", "apiName", "downloadInternalNoteAttachmentByDownloadDto", "downloadDto", "config", "request", "method", "responseType", "url", "params", "declarationId", "internalNoteId", "fileName", "uploadInternalNoteAttachmentByUploadDto", "uploadDto", "body", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["c:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\economic-service\\lib\\proxy\\bdo\\ess\\economic-substance-service\\assessment\\assessment-blob.service.ts"], "sourcesContent": ["import type { AssessmentFileDownloadDto, AssessmentUploadFileDto } from './models';\r\nimport { RestService, Rest } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AssessmentBlobService {\r\n  apiName = 'EconomicSubstanceService';\r\n  \r\n\r\n  downloadInternalNoteAttachmentByDownloadDto = (downloadDto: AssessmentFileDownloadDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string>({\r\n      method: 'GET',\r\n      responseType: 'text',\r\n      url: '/api/ESService/Assessment/DownloadInternalNoteAttachment',\r\n      params: { declarationId: downloadDto.declarationId, internalNoteId: downloadDto.internalNoteId, fileName: downloadDto.fileName },\r\n    },\r\n    { apiName: this.apiName,...config });\r\n  \r\n\r\n  uploadInternalNoteAttachmentByUploadDto = (uploadDto: AssessmentUploadFileDto, config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, string>({\r\n      method: 'POST',\r\n      responseType: 'text',\r\n      url: '/api/ESService/Assessment/UploadInternalNoteAttachment',\r\n      body: uploadDto,\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,qBAAqB;EAuBhCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAtB/B,KAAAC,OAAO,GAAG,0BAA0B;IAGpC,KAAAC,2CAA2C,GAAG,CAACC,WAAsC,EAAEC,MAA6B,KAClH,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,MAAM;MACpBC,GAAG,EAAE,0DAA0D;MAC/DC,MAAM,EAAE;QAAEC,aAAa,EAAEP,WAAW,CAACO,aAAa;QAAEC,cAAc,EAAER,WAAW,CAACQ,cAAc;QAAEC,QAAQ,EAAET,WAAW,CAACS;MAAQ;KAC/H,EACD;MAAEX,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;IAGtC,KAAAS,uCAAuC,GAAG,CAACC,SAAkC,EAAEV,MAA6B,KAC1G,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAc;MACpCC,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,MAAM;MACpBC,GAAG,EAAE,wDAAwD;MAC7DO,IAAI,EAAED;KACP,EACD;MAAEb,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGG;IAAM,CAAE,CAAC;EAES;;;uBAvBpCN,qBAAqB,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAArBrB,qBAAqB;MAAAsB,OAAA,EAArBtB,qBAAqB,CAAAuB,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}