{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';\nimport { AppComponentBase } from '@app/app-component-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@abp/ng.core\";\nlet DashboardComponent = class DashboardComponent extends AppComponentBase {\n  constructor(injector, router, abpRouteservice) {\n    super(injector);\n    this.router = router;\n    this.abpRouteservice = abpRouteservice;\n  }\n  ngOnInit() {\n    if (this.authService.isAuthenticated) {\n      this.router.navigate(['/home/<USER>']);\n    } else {\n      // Hide routes in left menu\n      this.abpRouteservice.remove([\"ES Search\" /* ComponentNames.ES_SEARCH */, \"ES Declaration\" /* ComponentNames.ES_DECLARATION */, \"Declaration Templates\" /* ComponentNames.ES_DECLARATION_TEMPLATES */, '::Administration']);\n    }\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.RoutesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"main-app-loader\"], [\"id\", \"cover-spin\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".main-app-loader[_ngcontent-%COMP%]{position:fixed;inset:0;width:100vw;height:100vh;background-image:url(bg-transparent.91f53164804518aa.png)}.main-app-loader[_ngcontent-%COMP%]   #cover-spin[_ngcontent-%COMP%]{position:fixed;inset:0;width:100%}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_spinner{to{transform:rotate(360deg)}}.main-app-loader[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]:before{position:absolute;top:50%;left:50%;box-sizing:border-box;width:20px;height:20px;margin-top:-10px;margin-left:-10px;content:\\\"\\\";border:2px solid #fff;border-top-color:#000;border-radius:50%;animation:_ngcontent-%COMP%_spinner .8s linear infinite}.main-app-loader[_ngcontent-%COMP%]   #cover-spin[_ngcontent-%COMP%]:after{position:absolute;top:50%;left:50%;display:block;width:40px;height:40px;content:\\\"\\\";border-color:#0b163f80;border-style:solid;border-width:4px;border-top-color:transparent;border-radius:50%;transform:translate(-50%,-50%);animation:_ngcontent-%COMP%_spin .8s linear infinite}\"]\n    });\n  }\n};\nDashboardComponent = __decorate([UntilDestroy({\n  checkProperties: true\n})], DashboardComponent);\nexport { DashboardComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}