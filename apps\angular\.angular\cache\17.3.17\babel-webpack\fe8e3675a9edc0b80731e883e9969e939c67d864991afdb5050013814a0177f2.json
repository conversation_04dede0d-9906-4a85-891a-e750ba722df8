{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\n/**\r\n * This is an internal property used to cause internal (reporting) requests to be ignored from reporting\r\n * additional telemetry, to handle polyfil implementations ALL urls used with a disabled request will\r\n * also be ignored for future requests even when this property is not provided.\r\n * Tagging as Ignore as this is an internal value and is not expected to be used outside of the SDK\r\n * @ignore\r\n */\nexport var DisabledPropertyName = \"Microsoft_ApplicationInsights_BypassAjaxInstrumentation\";\nexport var SampleRate = \"sampleRate\";\nexport var ProcessLegacy = \"ProcessLegacy\";\nexport var HttpMethod = \"http.method\";\nexport var DEFAULT_BREEZE_ENDPOINT = \"https://dc.services.visualstudio.com\";\nexport var DEFAULT_BREEZE_PATH = \"/v2/track\";\nexport var strNotSpecified = \"not_specified\";\nexport var strIkey = \"iKey\";\n//# sourceMappingURL=Constants.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}