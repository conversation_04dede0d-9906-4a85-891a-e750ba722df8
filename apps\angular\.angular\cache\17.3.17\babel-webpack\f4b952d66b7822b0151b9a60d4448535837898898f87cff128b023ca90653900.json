{"ast": null, "code": "import tzIntlTimeZoneName from '../../_lib/tzIntlTimeZoneName/index.js';\nimport tzParseTimezone from '../../_lib/tzParseTimezone/index.js';\nvar MILLISECONDS_IN_MINUTE = 60 * 1000;\nvar formatters = {\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, localize, options) {\n    var timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n    if (timezoneOffset === 0) {\n      return 'Z';\n    }\n    switch (token) {\n      // Hours and optional minutes\n      case 'X':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case 'XXXX':\n      case 'XX':\n        // Hours and minutes without `:` delimeter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case 'XXXXX':\n      case 'XXX': // Hours and minutes with `:` delimeter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, localize, options) {\n    var timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n    switch (token) {\n      // Hours and optional minutes\n      case 'x':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case 'xxxx':\n      case 'xx':\n        // Hours and minutes without `:` delimeter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case 'xxxxx':\n      case 'xxx': // Hours and minutes with `:` delimeter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (GMT)\n  O: function (date, token, localize, options) {\n    var timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n    switch (token) {\n      // Short\n      case 'O':\n      case 'OO':\n      case 'OOO':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n      case 'OOOO':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (specific non-location)\n  z: function (date, token, localize, options) {\n    switch (token) {\n      // Short\n      case 'z':\n      case 'zz':\n      case 'zzz':\n        return tzIntlTimeZoneName('short', date, options);\n      // Long\n      case 'zzzz':\n      default:\n        return tzIntlTimeZoneName('long', date, options);\n    }\n  }\n};\nfunction getTimeZoneOffset(timeZone, originalDate) {\n  var timeZoneOffset = timeZone ? tzParseTimezone(timeZone, originalDate, true) / MILLISECONDS_IN_MINUTE : originalDate.getTimezoneOffset();\n  if (Number.isNaN(timeZoneOffset)) {\n    throw new RangeError('Invalid time zone specified: ' + timeZone);\n  }\n  return timeZoneOffset;\n}\nfunction addLeadingZeros(number, targetLength) {\n  var sign = number < 0 ? '-' : '';\n  var output = Math.abs(number).toString();\n  while (output.length < targetLength) {\n    output = '0' + output;\n  }\n  return sign + output;\n}\nfunction formatTimezone(offset, dirtyDelimeter) {\n  var delimeter = dirtyDelimeter || '';\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n  var minutes = addLeadingZeros(Math.floor(absOffset % 60), 2);\n  return sign + hours + delimeter + minutes;\n}\nfunction formatTimezoneWithOptionalMinutes(offset, dirtyDelimeter) {\n  if (offset % 60 === 0) {\n    var sign = offset > 0 ? '-' : '+';\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, dirtyDelimeter);\n}\nfunction formatTimezoneShort(offset, dirtyDelimiter) {\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = Math.floor(absOffset / 60);\n  var minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  var delimiter = dirtyDelimiter || '';\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\nexport default formatters;", "map": {"version": 3, "names": ["tzIntlTimeZoneName", "tzParseTimezone", "MILLISECONDS_IN_MINUTE", "formatters", "X", "date", "token", "localize", "options", "timezoneOffset", "getTimeZoneOffset", "timeZone", "formatTimezoneWithOptionalMinutes", "formatTimezone", "x", "O", "formatTimezoneShort", "z", "originalDate", "timeZoneOffset", "getTimezoneOffset", "Number", "isNaN", "RangeError", "addLeadingZeros", "number", "targetLength", "sign", "output", "Math", "abs", "toString", "length", "offset", "dirtyDelimeter", "delimeter", "absOffset", "hours", "floor", "minutes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "String", "delimiter"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/date-fns-tz/esm/format/formatters/index.js"], "sourcesContent": ["import tzIntlTimeZoneName from '../../_lib/tzIntlTimeZoneName/index.js'\nimport tzParseTimezone from '../../_lib/tzParseTimezone/index.js'\n\nvar MILLISECONDS_IN_MINUTE = 60 * 1000\n\nvar formatters = {\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, localize, options) {\n    var timezoneOffset = getTimeZoneOffset(options.timeZone, date)\n\n    if (timezoneOffset === 0) {\n      return 'Z'\n    }\n\n    switch (token) {\n      // Hours and optional minutes\n      case 'X':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset)\n\n      // Hours, minutes and optional seconds without `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case 'XXXX':\n      case 'XX': // Hours and minutes without `:` delimeter\n        return formatTimezone(timezoneOffset)\n\n      // Hours, minutes and optional seconds with `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case 'XXXXX':\n      case 'XXX': // Hours and minutes with `:` delimeter\n      default:\n        return formatTimezone(timezoneOffset, ':')\n    }\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, localize, options) {\n    var timezoneOffset = getTimeZoneOffset(options.timeZone, date)\n\n    switch (token) {\n      // Hours and optional minutes\n      case 'x':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset)\n\n      // Hours, minutes and optional seconds without `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case 'xxxx':\n      case 'xx': // Hours and minutes without `:` delimeter\n        return formatTimezone(timezoneOffset)\n\n      // Hours, minutes and optional seconds with `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case 'xxxxx':\n      case 'xxx': // Hours and minutes with `:` delimeter\n      default:\n        return formatTimezone(timezoneOffset, ':')\n    }\n  },\n\n  // Timezone (GMT)\n  O: function (date, token, localize, options) {\n    var timezoneOffset = getTimeZoneOffset(options.timeZone, date)\n\n    switch (token) {\n      // Short\n      case 'O':\n      case 'OO':\n      case 'OOO':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':')\n      // Long\n      case 'OOOO':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':')\n    }\n  },\n\n  // Timezone (specific non-location)\n  z: function (date, token, localize, options) {\n    switch (token) {\n      // Short\n      case 'z':\n      case 'zz':\n      case 'zzz':\n        return tzIntlTimeZoneName('short', date, options)\n      // Long\n      case 'zzzz':\n      default:\n        return tzIntlTimeZoneName('long', date, options)\n    }\n  },\n}\n\nfunction getTimeZoneOffset(timeZone, originalDate) {\n  var timeZoneOffset = timeZone\n    ? tzParseTimezone(timeZone, originalDate, true) / MILLISECONDS_IN_MINUTE\n    : originalDate.getTimezoneOffset()\n  if (Number.isNaN(timeZoneOffset)) {\n    throw new RangeError('Invalid time zone specified: ' + timeZone)\n  }\n  return timeZoneOffset\n}\n\nfunction addLeadingZeros(number, targetLength) {\n  var sign = number < 0 ? '-' : ''\n  var output = Math.abs(number).toString()\n  while (output.length < targetLength) {\n    output = '0' + output\n  }\n  return sign + output\n}\n\nfunction formatTimezone(offset, dirtyDelimeter) {\n  var delimeter = dirtyDelimeter || ''\n  var sign = offset > 0 ? '-' : '+'\n  var absOffset = Math.abs(offset)\n  var hours = addLeadingZeros(Math.floor(absOffset / 60), 2)\n  var minutes = addLeadingZeros(Math.floor(absOffset % 60), 2)\n  return sign + hours + delimeter + minutes\n}\n\nfunction formatTimezoneWithOptionalMinutes(offset, dirtyDelimeter) {\n  if (offset % 60 === 0) {\n    var sign = offset > 0 ? '-' : '+'\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2)\n  }\n  return formatTimezone(offset, dirtyDelimeter)\n}\n\nfunction formatTimezoneShort(offset, dirtyDelimiter) {\n  var sign = offset > 0 ? '-' : '+'\n  var absOffset = Math.abs(offset)\n  var hours = Math.floor(absOffset / 60)\n  var minutes = absOffset % 60\n  if (minutes === 0) {\n    return sign + String(hours)\n  }\n  var delimiter = dirtyDelimiter || ''\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2)\n}\n\nexport default formatters\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,wCAAwC;AACvE,OAAOC,eAAe,MAAM,qCAAqC;AAEjE,IAAIC,sBAAsB,GAAG,EAAE,GAAG,IAAI;AAEtC,IAAIC,UAAU,GAAG;EACf;EACAC,CAAC,EAAE,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC3C,IAAIC,cAAc,GAAGC,iBAAiB,CAACF,OAAO,CAACG,QAAQ,EAAEN,IAAI,CAAC;IAE9D,IAAII,cAAc,KAAK,CAAC,EAAE;MACxB,OAAO,GAAG;IACZ;IAEA,QAAQH,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOM,iCAAiC,CAACH,cAAc,CAAC;;MAE1D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QAAE;QACT,OAAOI,cAAc,CAACJ,cAAc,CAAC;;MAEvC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACE,OAAOI,cAAc,CAACJ,cAAc,EAAE,GAAG,CAAC;IAC9C;EACF,CAAC;EAED;EACAK,CAAC,EAAE,SAAAA,CAAUT,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC3C,IAAIC,cAAc,GAAGC,iBAAiB,CAACF,OAAO,CAACG,QAAQ,EAAEN,IAAI,CAAC;IAE9D,QAAQC,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOM,iCAAiC,CAACH,cAAc,CAAC;;MAE1D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QAAE;QACT,OAAOI,cAAc,CAACJ,cAAc,CAAC;;MAEvC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACE,OAAOI,cAAc,CAACJ,cAAc,EAAE,GAAG,CAAC;IAC9C;EACF,CAAC;EAED;EACAM,CAAC,EAAE,SAAAA,CAAUV,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC3C,IAAIC,cAAc,GAAGC,iBAAiB,CAACF,OAAO,CAACG,QAAQ,EAAEN,IAAI,CAAC;IAE9D,QAAQC,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAO,KAAK,GAAGU,mBAAmB,CAACP,cAAc,EAAE,GAAG,CAAC;MACzD;MACA,KAAK,MAAM;MACX;QACE,OAAO,KAAK,GAAGI,cAAc,CAACJ,cAAc,EAAE,GAAG,CAAC;IACtD;EACF,CAAC;EAED;EACAQ,CAAC,EAAE,SAAAA,CAAUZ,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC3C,QAAQF,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAON,kBAAkB,CAAC,OAAO,EAAEK,IAAI,EAAEG,OAAO,CAAC;MACnD;MACA,KAAK,MAAM;MACX;QACE,OAAOR,kBAAkB,CAAC,MAAM,EAAEK,IAAI,EAAEG,OAAO,CAAC;IACpD;EACF;AACF,CAAC;AAED,SAASE,iBAAiBA,CAACC,QAAQ,EAAEO,YAAY,EAAE;EACjD,IAAIC,cAAc,GAAGR,QAAQ,GACzBV,eAAe,CAACU,QAAQ,EAAEO,YAAY,EAAE,IAAI,CAAC,GAAGhB,sBAAsB,GACtEgB,YAAY,CAACE,iBAAiB,CAAC,CAAC;EACpC,IAAIC,MAAM,CAACC,KAAK,CAACH,cAAc,CAAC,EAAE;IAChC,MAAM,IAAII,UAAU,CAAC,+BAA+B,GAAGZ,QAAQ,CAAC;EAClE;EACA,OAAOQ,cAAc;AACvB;AAEA,SAASK,eAAeA,CAACC,MAAM,EAAEC,YAAY,EAAE;EAC7C,IAAIC,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;EAChC,IAAIG,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,CAAC;EACxC,OAAOH,MAAM,CAACI,MAAM,GAAGN,YAAY,EAAE;IACnCE,MAAM,GAAG,GAAG,GAAGA,MAAM;EACvB;EACA,OAAOD,IAAI,GAAGC,MAAM;AACtB;AAEA,SAASf,cAAcA,CAACoB,MAAM,EAAEC,cAAc,EAAE;EAC9C,IAAIC,SAAS,GAAGD,cAAc,IAAI,EAAE;EACpC,IAAIP,IAAI,GAAGM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACjC,IAAIG,SAAS,GAAGP,IAAI,CAACC,GAAG,CAACG,MAAM,CAAC;EAChC,IAAII,KAAK,GAAGb,eAAe,CAACK,IAAI,CAACS,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1D,IAAIG,OAAO,GAAGf,eAAe,CAACK,IAAI,CAACS,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5D,OAAOT,IAAI,GAAGU,KAAK,GAAGF,SAAS,GAAGI,OAAO;AAC3C;AAEA,SAAS3B,iCAAiCA,CAACqB,MAAM,EAAEC,cAAc,EAAE;EACjE,IAAID,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;IACrB,IAAIN,IAAI,GAAGM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACjC,OAAON,IAAI,GAAGH,eAAe,CAACK,IAAI,CAACC,GAAG,CAACG,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACzD;EACA,OAAOpB,cAAc,CAACoB,MAAM,EAAEC,cAAc,CAAC;AAC/C;AAEA,SAASlB,mBAAmBA,CAACiB,MAAM,EAAEO,cAAc,EAAE;EACnD,IAAIb,IAAI,GAAGM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACjC,IAAIG,SAAS,GAAGP,IAAI,CAACC,GAAG,CAACG,MAAM,CAAC;EAChC,IAAII,KAAK,GAAGR,IAAI,CAACS,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC;EACtC,IAAIG,OAAO,GAAGH,SAAS,GAAG,EAAE;EAC5B,IAAIG,OAAO,KAAK,CAAC,EAAE;IACjB,OAAOZ,IAAI,GAAGc,MAAM,CAACJ,KAAK,CAAC;EAC7B;EACA,IAAIK,SAAS,GAAGF,cAAc,IAAI,EAAE;EACpC,OAAOb,IAAI,GAAGc,MAAM,CAACJ,KAAK,CAAC,GAAGK,SAAS,GAAGlB,eAAe,CAACe,OAAO,EAAE,CAAC,CAAC;AACvE;AAEA,eAAepC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}