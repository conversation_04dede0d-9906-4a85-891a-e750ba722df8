{"ast": null, "code": "/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\nimport { createValueMap } from \"@microsoft/applicationinsights-core-js\";\nexport var RequestHeaders = createValueMap({\n  requestContextHeader: [0 /* eRequestHeaders.requestContextHeader */, \"Request-Context\"],\n  requestContextTargetKey: [1 /* eRequestHeaders.requestContextTargetKey */, \"appId\"],\n  requestContextAppIdFormat: [2 /* eRequestHeaders.requestContextAppIdFormat */, \"appId=cid-v1:\"],\n  requestIdHeader: [3 /* eRequestHeaders.requestIdHeader */, \"Request-Id\"],\n  traceParentHeader: [4 /* eRequestHeaders.traceParentHeader */, \"traceparent\"],\n  traceStateHeader: [5 /* eRequestHeaders.traceStateHeader */, \"tracestate\"],\n  sdkContextHeader: [6 /* eRequestHeaders.sdkContextHeader */, \"Sdk-Context\"],\n  sdkContextHeaderAppIdRequest: [7 /* eRequestHeaders.sdkContextHeaderAppIdRequest */, \"appId\"],\n  requestContextHeaderLowerCase: [8 /* eRequestHeaders.requestContextHeaderLowerCase */, \"request-context\"]\n});", "map": {"version": 3, "names": ["createValueMap", "RequestHeaders", "requestContextHeader", "requestContextTargetKey", "requestContextAppIdFormat", "requestIdHeader", "traceParentHeader", "traceStateHeader", "sdkContextHeader", "sdkContextHeaderAppIdRequest", "requestContextHeaderLowerCase"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@microsoft/applicationinsights-common/dist-esm/RequestResponseHeaders.js"], "sourcesContent": ["/*\n * Application Insights JavaScript SDK - Common, 2.8.18\n * Copyright (c) Microsoft and contributors. All rights reserved.\n */\n\r\n\r\nimport { createValueMap } from \"@microsoft/applicationinsights-core-js\";\r\nexport var RequestHeaders = createValueMap({\r\n    requestContextHeader: [0 /* eRequestHeaders.requestContextHeader */, \"Request-Context\"],\r\n    requestContextTargetKey: [1 /* eRequestHeaders.requestContextTargetKey */, \"appId\"],\r\n    requestContextAppIdFormat: [2 /* eRequestHeaders.requestContextAppIdFormat */, \"appId=cid-v1:\"],\r\n    requestIdHeader: [3 /* eRequestHeaders.requestIdHeader */, \"Request-Id\"],\r\n    traceParentHeader: [4 /* eRequestHeaders.traceParentHeader */, \"traceparent\"],\r\n    traceStateHeader: [5 /* eRequestHeaders.traceStateHeader */, \"tracestate\"],\r\n    sdkContextHeader: [6 /* eRequestHeaders.sdkContextHeader */, \"Sdk-Context\"],\r\n    sdkContextHeaderAppIdRequest: [7 /* eRequestHeaders.sdkContextHeaderAppIdRequest */, \"appId\"],\r\n    requestContextHeaderLowerCase: [8 /* eRequestHeaders.requestContextHeaderLowerCase */, \"request-context\"]\r\n});\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAGA,SAASA,cAAc,QAAQ,wCAAwC;AACvE,OAAO,IAAIC,cAAc,GAAGD,cAAc,CAAC;EACvCE,oBAAoB,EAAE,CAAC,CAAC,CAAC,4CAA4C,iBAAiB,CAAC;EACvFC,uBAAuB,EAAE,CAAC,CAAC,CAAC,+CAA+C,OAAO,CAAC;EACnFC,yBAAyB,EAAE,CAAC,CAAC,CAAC,iDAAiD,eAAe,CAAC;EAC/FC,eAAe,EAAE,CAAC,CAAC,CAAC,uCAAuC,YAAY,CAAC;EACxEC,iBAAiB,EAAE,CAAC,CAAC,CAAC,yCAAyC,aAAa,CAAC;EAC7EC,gBAAgB,EAAE,CAAC,CAAC,CAAC,wCAAwC,YAAY,CAAC;EAC1EC,gBAAgB,EAAE,CAAC,CAAC,CAAC,wCAAwC,aAAa,CAAC;EAC3EC,4BAA4B,EAAE,CAAC,CAAC,CAAC,oDAAoD,OAAO,CAAC;EAC7FC,6BAA6B,EAAE,CAAC,CAAC,CAAC,qDAAqD,iBAAiB;AAC5G,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}