{"ast": null, "code": "import startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute (and hour and day)?\n *\n * @description\n * Are the given dates in the same minute (and hour and day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same minute (and hour and day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 5 September 2014 06:30:00 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 5, 6, 30)\n * )\n * //=> false\n */\nexport default function isSameMinute(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfMinute = startOfMinute(dirtyDateLeft);\n  var dateRightStartOfMinute = startOfMinute(dirtyDateRight);\n  return dateLeftStartOfMinute.getTime() === dateRightStartOfMinute.getTime();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}