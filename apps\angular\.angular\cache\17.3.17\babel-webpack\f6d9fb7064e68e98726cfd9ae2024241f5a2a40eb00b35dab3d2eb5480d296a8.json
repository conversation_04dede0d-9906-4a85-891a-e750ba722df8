{"ast": null, "code": "import { AppComponentBase } from '@app/app-component-base';\nimport { PreviewFileComponent } from '@app/features/ca-action-page/containers/attachements-component/preview-file/preview-file.component';\nimport { BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { getBase64Header } from '@app/shared/utils/base64-header';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/dialog\";\nfunction AssessmentActionViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h1\", 3);\n    i0.ɵɵtext(2, \"ACTIONS ENFORCED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"textarea\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"bdo-table\", 5);\n    i0.ɵɵlistener(\"onActionClick\", function AssessmentActionViewComponent_div_0_Template_bdo_table_onActionClick_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.actionButtonClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.actionEnforcedObj.comments);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r1.actionEnforcedTableId)(\"columns\", ctx_r1.actionEnforcedUploadedColumns)(\"hidePagination\", true)(\"rowSelectable\", false);\n  }\n}\nfunction AssessmentActionViewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-information-request-card\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"informationRequiredData\", info_r3)(\"declarationId\", ctx_r1.declarationId)(\"historyId\", ctx_r1.historyId);\n  }\n}\nexport let AssessmentActionViewComponent = /*#__PURE__*/(() => {\n  class AssessmentActionViewComponent extends AppComponentBase {\n    constructor(injector, declarationService, datePipe, dialog) {\n      super(injector);\n      this.declarationService = declarationService;\n      this.datePipe = datePipe;\n      this.dialog = dialog;\n      this.actionEnforcedObj = undefined;\n      this.infoRequiredData = [];\n      this.actionEnforcedTableId = 'actionEnforcedFileUploadedTable';\n      this.actionEnforcedUploadedColumns = [{\n        columnId: 'fileName',\n        type: BdoTableColumnType.String,\n        minWidth: 100,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'File Name'\n      }, {\n        columnId: 'fileType',\n        type: BdoTableColumnType.String,\n        minWidth: 100,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'File Type'\n      }, {\n        columnId: 'actions',\n        type: BdoTableColumnType.ThreeDotActions,\n        minWidth: 100,\n        isSortable: false,\n        columnName: 'Actions'\n      }];\n      this.fileUploadActionButtons = [{\n        actionType: 'view',\n        icon: 'search',\n        displayName: 'View'\n      }];\n    }\n    ngOnChanges() {\n      this.infoRequiredData = [];\n      this.intialize();\n    }\n    actionButtonClicked(event) {\n      const dialogRef = this.dialog.open(PreviewFileComponent, {\n        data: {\n          file: {\n            name: event.data.rawData.name,\n            type: event.data.rawData.type,\n            content: event.data.rawData.content\n          },\n          isHistory: false\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        console.log('The dialog was closed');\n        console.log(result);\n      });\n    }\n    intialize() {\n      // renders aciton enforced if assessment was failed by CA\n      if (this.declarationData?.actionEnforcedComments) {\n        var tempFiles = this.fileParserhelper(this.declarationData.actionEnforcedFileNames);\n        this.actionEnforcedObj = {\n          comments: this.declarationData.actionEnforcedComments,\n          files: tempFiles\n        };\n      }\n      //renders information requested if the assessment was marked as 'information required' by CA\n      if (this.declarationData?.informationRequestedArray.length > 0) {\n        var infoArray = this.declarationData.informationRequestedArray.filter(x => x != null);\n        infoArray.sort((a, b) => a.createdDate > b.createdDate ? -1 : 1);\n        infoArray.forEach((info, index) => {\n          if (info) {\n            this.infoRequiredData.push({\n              id: info.id,\n              isSubmitted: info.isSubmitted || info.raAttachmentFileNames?.length > 0 || info.raComments?.length > 0,\n              CAComments: info.caComments,\n              RAComments: info.raComments,\n              dueDate: this.datePipe.transform(info.dueDate, 'dd/MM/yyyy', 'local'),\n              type: this.type,\n              CAFiles: info.caAttachmentFileNames,\n              RAFiles: info.raAttachmentFileNames,\n              assessmentStatus: this.declarationData.assessmentStatus,\n              index\n            });\n          }\n        });\n      }\n    }\n    setTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = true;\n      tableData.tableId = this.actionEnforcedTableId;\n      tableData.totalRecords = this.actionEnforcedObj.files.length;\n      tableData.data = this.actionEnforcedObj.files.map(x => {\n        var cells = [];\n        cells = [{\n          columnId: 'fileName',\n          value: x.name\n        }, {\n          columnId: 'fileType',\n          value: x.type\n        }, {\n          columnId: 'actions',\n          value: this.fileUploadActionButtons\n        }];\n        return {\n          id: x.name,\n          rawData: x,\n          cells: cells\n        };\n      });\n      this.tableService.setGridData(tableData);\n    }\n    fileParserhelper(files) {\n      let tempFiles = [];\n      files.forEach(element => {\n        var slash = element.includes('\\\\') ? '\\\\' : '/';\n        const fileName = element.split(slash)[3];\n        var docType = element.split(slash)[2];\n        var linksource = getBase64Header(fileName);\n        const fileType = linksource.split(':')[1].split(';')[0];\n        this.type == 'CA' ? this.declarationService.downloadCADeclarationDocument(this.declarationId, fileName, docType, false).subscribe(result => {\n          tempFiles.push({\n            name: fileName,\n            type: fileType,\n            content: linksource + result\n          });\n          this.setTableData();\n        }) : this.declarationService.downloadDeclarationDocument(this.declarationId, fileName, docType, false).subscribe(result => {\n          tempFiles.push({\n            name: fileName,\n            type: fileType,\n            content: linksource + result\n          });\n          this.setTableData();\n        });\n      });\n      return tempFiles;\n    }\n    static {\n      this.ɵfac = function AssessmentActionViewComponent_Factory(t) {\n        return new (t || AssessmentActionViewComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.DeclarationService), i0.ɵɵdirectiveInject(i2.DatePipe), i0.ɵɵdirectiveInject(i3.MatDialog));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AssessmentActionViewComponent,\n        selectors: [[\"app-assessment-action-view\"]],\n        inputs: {\n          declarationData: \"declarationData\",\n          declarationId: \"declarationId\",\n          type: \"type\",\n          historyId: \"historyId\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"action-container\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"action-container\"], [1, \"action-header\"], [\"readonly\", \"\", 1, \"action-textarea\"], [\"scrollHeight\", \"100%\", 3, \"onActionClick\", \"id\", \"columns\", \"hidePagination\", \"rowSelectable\"], [3, \"informationRequiredData\", \"declarationId\", \"historyId\"]],\n        template: function AssessmentActionViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AssessmentActionViewComponent_div_0_Template, 6, 5, \"div\", 0)(1, AssessmentActionViewComponent_div_1_Template, 2, 3, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.actionEnforcedObj);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.infoRequiredData);\n          }\n        },\n        styles: [\".action-container[_ngcontent-%COMP%]{width:100%;margin-bottom:2em;background-color:#fff;padding:1em;box-shadow:1px 1px 5px 5px #0000001a}.action-header[_ngcontent-%COMP%]{color:#00779b;font-size:x-large}.action-textarea[_ngcontent-%COMP%]{width:100%;background:#80808015;padding:.5em}\"]\n      });\n    }\n  }\n  return AssessmentActionViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}