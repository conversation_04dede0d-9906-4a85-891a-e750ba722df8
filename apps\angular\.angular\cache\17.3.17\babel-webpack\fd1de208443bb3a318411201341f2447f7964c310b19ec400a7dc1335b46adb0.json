{"ast": null, "code": "import { createSelector, createFeatureSelector } from '@ngrx/store';\nimport { APP } from '@app/shared/constants';\nconst isSignedInState = state => state.isSignedIn;\nconst selectState = createFeatureSelector(APP);\nexport const selectIsSignedIn = createSelector(selectState, isSignedInState);\n/** Example how to combine multiple selectors */\n// export const userAndHasValidAccessToken = createSelector(\n//     user,\n//     hasValidAccessToken,\n//     (userD: IUser | null, hasValidAccessTokenD: boolean) => {\n//         return {\n//             user: userD,\n//             hasValidAccessToken: hasValidAccessTokenD\n//         };\n//     }\n// );", "map": {"version": 3, "names": ["createSelector", "createFeatureSelector", "APP", "isSignedInState", "state", "isSignedIn", "selectState", "selectIsSignedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\root-store\\app-store\\selectors.ts"], "sourcesContent": ["\r\nimport { createSelector, createFeatureSelector } from '@ngrx/store';\r\n\r\nimport { State } from './state';\r\nimport { APP } from '@app/shared/constants';\r\n\r\n\r\n\r\nconst isSignedInState = (state: State): boolean => state.isSignedIn;\r\n\r\nconst selectState = createFeatureSelector<State>(APP);\r\n\r\n\r\n\r\nexport const selectIsSignedIn = createSelector(selectState, isSignedInState);\r\n\r\n\r\n\r\n/** Example how to combine multiple selectors */\r\n\r\n// export const userAndHasValidAccessToken = createSelector(\r\n\r\n//     user,\r\n//     hasValidAccessToken,\r\n\r\n//     (userD: IUser | null, hasValidAccessTokenD: boolean) => {\r\n\r\n//         return {\r\n//             user: userD,\r\n//             hasValidAccessToken: hasValidAccessTokenD\r\n//         };\r\n//     }\r\n// );\r\n"], "mappings": "AACA,SAASA,cAAc,EAAEC,qBAAqB,QAAQ,aAAa;AAGnE,SAASC,GAAG,QAAQ,uBAAuB;AAI3C,MAAMC,eAAe,GAAIC,KAAY,IAAcA,KAAK,CAACC,UAAU;AAEnE,MAAMC,WAAW,GAAGL,qBAAqB,CAAQC,GAAG,CAAC;AAIrD,OAAO,MAAMK,gBAAgB,GAAGP,cAAc,CAACM,WAAW,EAAEH,eAAe,CAAC;AAI5E;AAEA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}