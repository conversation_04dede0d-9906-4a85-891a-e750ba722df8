{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"seh\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"P\", \"C\", \"T\", \"N\", \"S\", \"S\"], [\"Dim\", \"<PERSON>s\", \"<PERSON>r\", \"Tat\", \"Nai\", \"<PERSON>ha\", \"Sab\"], [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>u\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Sa<PERSON><PERSON>\"], [\"Dim\", \"<PERSON><PERSON>\", \"<PERSON>r\", \"Tat\", \"Nai\", \"<PERSON>ha\", \"Sab\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"O\", \"N\", \"<PERSON>\"], [\"<PERSON>\", \"<PERSON>v\", \"<PERSON>\", \"<PERSON>b<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>tu\", \"Nov\", \"<PERSON>\"], [\"Janeiro\", \"Fevreiro\", \"<PERSON>\", \"<PERSON>bril\", \"<PERSON>o\", \"<PERSON>ho\", \"<PERSON>ho\", \"Augusto\", \"<PERSON>embro\", \"<PERSON>tubro\", \"<PERSON>embro\", \"<PERSON>embro\"]], u, [[\"<PERSON>\", \"AD\"], u, [\"<PERSON><PERSON> de <PERSON><PERSON>\", \"<PERSON>o <PERSON><PERSON>\"]], 0, [6, 0], [\"d/<PERSON>/y\", \"d 'de' MMM 'de' y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00¤\", \"#E0\"], \"MZN\", \"MTn\", \"Metical de Moçambique\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"MZN\": [\"MTn\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/seh.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === 1)\n        return 1;\n    return 5;\n}\nexport default [\"seh\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"P\", \"C\", \"T\", \"N\", \"S\", \"S\"], [\"Dim\", \"<PERSON>s\", \"<PERSON>r\", \"Tat\", \"Nai\", \"<PERSON>ha\", \"Sab\"], [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>u\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Sa<PERSON><PERSON>\"], [\"Dim\", \"<PERSON><PERSON>\", \"<PERSON>r\", \"Tat\", \"Nai\", \"<PERSON>ha\", \"Sab\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"O\", \"N\", \"<PERSON>\"], [\"<PERSON>\", \"<PERSON>v\", \"<PERSON>\", \"<PERSON>b<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>tu\", \"Nov\", \"<PERSON>\"], [\"Janeiro\", \"Fevreiro\", \"<PERSON>\", \"<PERSON>bril\", \"<PERSON>o\", \"<PERSON>ho\", \"<PERSON>ho\", \"Augusto\", \"<PERSON>embro\", \"<PERSON>tubro\", \"<PERSON>embro\", \"<PERSON>embro\"]], u, [[\"<PERSON>\", \"AD\"], u, [\"<PERSON><PERSON> de <PERSON><PERSON>\", \"<PERSON>o <PERSON><PERSON>\"]], 0, [6, 0], [\"d/<PERSON>/y\", \"d 'de' MMM 'de' y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00¤\", \"#E0\"], \"MZN\", \"MTn\", \"Metical de Moçambique\", { \"JPY\": [\"JP¥\", \"¥\"], \"MZN\": [\"MTn\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAK,CAAC,EACP,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,0BAA0B,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}