{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"mzn\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"], u, u], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ژانویه\", \"فوریه\", \"مارس\", \"آوریل\", \"مه\", \"ژوئن\", \"ژوئیه\", \"اوت\", \"سپتامبر\", \"اکتبر\", \"نوامبر\", \"دسامبر\"], u], u, [[\"پ.م\", \"م.\"], u, [\"قبل میلاد\", \"بعد میلاد\"]], 6, [5, 5], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"IRR\", \"IRR\", \"ایران ریال\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"]\n}, \"rtl\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/mzn.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"mzn\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"], u, u], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"ژانویه\", \"فوریه\", \"مارس\", \"آوریل\", \"مه\", \"ژوئن\", \"ژوئیه\", \"اوت\", \"سپتامبر\", \"اکتبر\", \"نوامبر\", \"دسامبر\"], u], u, [[\"پ.م\", \"م.\"], u, [\"قبل میلاد\", \"بعد میلاد\"]], 6, [5, 5], [\"y-MM-dd\", \"y MMM d\", \"y MMMM d\", \"y MMMM d, EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"IRR\", \"IRR\", \"ایران ریال\", { \"BYN\": [u, \"р.\"], \"JPY\": [\"JP¥\", \"¥\"], \"PHP\": [u, \"₱\"] }, \"rtl\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}