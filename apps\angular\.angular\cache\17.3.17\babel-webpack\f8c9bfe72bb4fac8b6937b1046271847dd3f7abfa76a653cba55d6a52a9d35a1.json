{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"my\", [[\"နံနက်\", \"ညနေ\"], u, u], u, [[\"တ\", \"တ\", \"အ\", \"ဗ\", \"က\", \"သ\", \"စ\"], [\"တနင်္ဂနွေ\", \"တနင်္လာ\", \"အင်္ဂါ\", \"ဗုဒ္ဓဟူး\", \"ကြာသပတေး\", \"သောကြာ\", \"စနေ\"], u, u], u, [[\"ဇ\", \"ဖ\", \"မ\", \"ဧ\", \"မ\", \"ဇ\", \"ဇ\", \"ဩ\", \"စ\", \"အ\", \"န\", \"ဒ\"], [\"ဇန်\", \"ဖေ\", \"မတ်\", \"ဧ\", \"မေ\", \"ဇွန်\", \"ဇူ\", \"ဩ\", \"စက်\", \"အောက်\", \"နို\", \"ဒီ\"], [\"ဇန်နဝါရီ\", \"ဖေဖော်ဝါရီ\", \"မတ်\", \"ဧပြီ\", \"မေ\", \"ဇွန်\", \"ဇူလိုင်\", \"ဩဂုတ်\", \"စက်တင်ဘာ\", \"အောက်တိုဘာ\", \"နိုဝင်ဘာ\", \"ဒီဇင်ဘာ\"]], u, [[\"ဘီစီ\", \"အဒေီ\"], u, [\"ခရစ်တော် မပေါ်မီနှစ်\", \"ခရစ်နှစ်\"]], 0, [6, 0], [\"dd-MM-yy\", \"y- MMM d\", \"y- MMMM d\", \"y- MMMM d- EEEE\"], [\"H:mm\", \"H:mm:ss\", \"z HH:mm:ss\", \"zzzz HH:mm:ss\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"ဂဏန်းမဟုတ်သော\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"MMK\", \"K\", \"မြန်မာ ကျပ်\", {\n  \"ANG\": [\"NAf\"],\n  \"AWG\": [\"Afl\"],\n  \"BBD\": [u, \"Bds$\"],\n  \"BSD\": [u, \"B$\"],\n  \"BYN\": [u, \"р.\"],\n  \"HTG\": [\"G\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"MMK\": [\"K\"],\n  \"PAB\": [\"B/.\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TTD\": [\"TT$\", \"$\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/my.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"my\", [[\"နံနက်\", \"ညနေ\"], u, u], u, [[\"တ\", \"တ\", \"အ\", \"ဗ\", \"က\", \"သ\", \"စ\"], [\"တနင်္ဂနွေ\", \"တနင်္လာ\", \"အင်္ဂါ\", \"ဗုဒ္ဓဟူး\", \"ကြာသပတေး\", \"သောကြာ\", \"စနေ\"], u, u], u, [[\"ဇ\", \"ဖ\", \"မ\", \"ဧ\", \"မ\", \"ဇ\", \"ဇ\", \"ဩ\", \"စ\", \"အ\", \"န\", \"ဒ\"], [\"ဇန်\", \"ဖေ\", \"မတ်\", \"ဧ\", \"မေ\", \"ဇွန်\", \"ဇူ\", \"ဩ\", \"စက်\", \"အောက်\", \"နို\", \"ဒီ\"], [\"ဇန်နဝါရီ\", \"ဖေဖော်ဝါရီ\", \"မတ်\", \"ဧပြီ\", \"မေ\", \"ဇွန်\", \"ဇူလိုင်\", \"ဩဂုတ်\", \"စက်တင်ဘာ\", \"အောက်တိုဘာ\", \"နိုဝင်ဘာ\", \"ဒီဇင်ဘာ\"]], u, [[\"ဘီစီ\", \"အဒေီ\"], u, [\"ခရစ်တော် မပေါ်မီနှစ်\", \"ခရစ်နှစ်\"]], 0, [6, 0], [\"dd-MM-yy\", \"y- MMM d\", \"y- MMMM d\", \"y- MMMM d- EEEE\"], [\"H:mm\", \"H:mm:ss\", \"z HH:mm:ss\", \"zzzz HH:mm:ss\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"ဂဏန်းမဟုတ်သော\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"MMK\", \"K\", \"မြန်မာ ကျပ်\", { \"ANG\": [\"NAf\"], \"AWG\": [\"Afl\"], \"BBD\": [u, \"Bds$\"], \"BSD\": [u, \"B$\"], \"BYN\": [u, \"р.\"], \"HTG\": [\"G\"], \"JPY\": [\"JP¥\", \"¥\"], \"MMK\": [\"K\"], \"PAB\": [\"B/.\"], \"PHP\": [u, \"₱\"], \"THB\": [\"฿\"], \"TTD\": [\"TT$\", \"$\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,MAAM,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}