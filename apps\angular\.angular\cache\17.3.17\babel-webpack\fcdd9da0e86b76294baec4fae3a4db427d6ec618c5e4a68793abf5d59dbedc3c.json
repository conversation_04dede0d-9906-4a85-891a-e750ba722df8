{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === Math.floor(n) && n >= 0 && n <= 1) return 1;\n  return 5;\n}\nexport default [\"mg\", [[\"AM\", \"PM\"], u, u], u, [[\"A\", \"A\", \"T\", \"A\", \"A\", \"Z\", \"A\"], [\"Alah\", \"Alats\", \"<PERSON>l\", \"<PERSON>ar\", \"<PERSON><PERSON>\", \"<PERSON>om\", \"<PERSON><PERSON>\"], [\"<PERSON><PERSON><PERSON>\", \"Alats<PERSON><PERSON><PERSON>\", \"<PERSON>lat<PERSON>\", \"Alarobia\", \"Alakamisy\", \"Zoma\", \"Asab<PERSON><PERSON>\"], [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>l\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A\", \"<PERSON>\", \"<PERSON>\", \"N\", \"<PERSON>\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>l\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>t\", \"<PERSON>\", \"<PERSON>\"], [\"<PERSON><PERSON>y\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Aprily\", \"<PERSON>y\", \"<PERSON>a\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>ra\", \"<PERSON><PERSON><PERSON>\"]], u, [[\"<PERSON>\", \"<PERSON>\"], u, [\"<PERSON>oh<PERSON>’i JK\", \"Aorian’i JK\"]], 1, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"MGA\", \"Ar\", \"Ariary\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"MGA\": [\"Ar\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "Math", "floor"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/mg.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    if (n === Math.floor(n) && (n >= 0 && n <= 1))\n        return 1;\n    return 5;\n}\nexport default [\"mg\", [[\"AM\", \"PM\"], u, u], u, [[\"A\", \"A\", \"T\", \"A\", \"A\", \"Z\", \"A\"], [\"Alah\", \"Alats\", \"<PERSON>l\", \"<PERSON>ar\", \"<PERSON><PERSON>\", \"<PERSON>om\", \"<PERSON><PERSON>\"], [\"<PERSON><PERSON><PERSON>\", \"Alats<PERSON>in<PERSON>\", \"<PERSON>lata\", \"Alarobia\", \"Alakamisy\", \"Zoma\", \"Asabotsy\"], [\"<PERSON><PERSON>\", \"Alats\", \"<PERSON>l\", \"<PERSON>ar\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"J\", \"A\", \"<PERSON>\", \"O\", \"N\", \"D\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>y\", \"<PERSON>\", \"<PERSON>l\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>t\", \"<PERSON>\", \"Des\"], [\"<PERSON>oary\", \"<PERSON>roa<PERSON>\", \"<PERSON>sa\", \"<PERSON>y\", \"<PERSON>y\", \"<PERSON>a\", \"<PERSON><PERSON>\", \"<PERSON>ogosi<PERSON>\", \"Septambra\", \"<PERSON>to<PERSON>\", \"<PERSON><PERSON>ra\", \"<PERSON>am<PERSON>\"]], u, [[\"BC\", \"<PERSON>\"], u, [\"<PERSON>oh<PERSON>’i JK\", \"Aorian’i JK\"]], 1, [6, 0], [\"y-MM-dd\", \"y MMM d\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"MGA\", \"Ar\", \"Ariary\", { \"JPY\": [\"JP¥\", \"¥\"], \"MGA\": [\"Ar\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,IAAIC,CAAC,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC,IAAKA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAE,EACzC,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEJ,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}