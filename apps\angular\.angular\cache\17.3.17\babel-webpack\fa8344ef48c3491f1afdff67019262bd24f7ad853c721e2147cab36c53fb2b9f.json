{"ast": null, "code": "import { APP_INITIALIZER } from '@angular/core';\nimport { ManageProfileTabsService } from '../services/manage-profile-tabs.service';\nimport { PersonalSettingsComponent } from '../../../src/components';\nimport { ChangePasswordComponent } from '../../../src/components';\nexport const ACCOUNT_MANAGE_PROFILE_TAB_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureTabs,\n  deps: [ManageProfileTabsService],\n  multi: true\n}];\nexport function configureTabs(tabs) {\n  return () => {\n    tabs.add([\n    /*{\n      name: eAccountManageProfileTabNames.ProfilePicture,\n      order: 1,\n      component: null,\n    },*/\n    {\n      name: \"MY PROFILE\",\n      //eAccountManageProfileTabNames.PersonalInfo,\n      order: 2,\n      component: PersonalSettingsComponent\n    }, {\n      name: \"CHANGE PASSWORD\",\n      //eAccountManageProfileTabNames.ChangePassword,\n      order: 3,\n      component: ChangePasswordComponent\n    }, {\n      name: \"AbpAccount::ProfileTab:TwoFactor\" /* eAccountManageProfileTabNames.TwoFactor */,\n      order: 4,\n      component: null\n    }]);\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}