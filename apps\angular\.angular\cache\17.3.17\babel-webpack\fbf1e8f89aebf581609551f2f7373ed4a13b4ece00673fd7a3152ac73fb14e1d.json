{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  if (n === 2) return 2;\n  return 5;\n}\nexport default [\"se-FI\", [[\"i\", \"e\"], [\"ib\", \"eb\"], u], [[\"ib\", \"eb\"], u, u], [[\"S\", \"M\", \"D\", \"G\", \"D\", \"B\", \"L\"], [\"so\", \"má\", \"di\", \"ga\", \"du\", \"be\", \"lá\"], [\"sotnabeaivi\", \"mánnodat\", \"disdat\", \"gaskavahkku\", \"duorastat\", \"bearjadat\", \"lávvordat\"], [\"so\", \"má\", \"di\", \"ga\", \"du\", \"be\", \"lá\"]], u, [[\"O\", \"G\", \"N\", \"C\", \"M\", \"G\", \"S\", \"B\", \"Č\", \"G\", \"S\", \"J\"], [\"ođđj\", \"guov\", \"njuk\", \"cuoŋ\", \"mies\", \"geas\", \"suoi\", \"borg\", \"čakč\", \"golg\", \"skáb\", \"juov\"], [\"ođđajagemánnu\", \"guovvamánnu\", \"njukčamánnu\", \"cuoŋománnu\", \"miessemánnu\", \"geassemánnu\", \"suoidnemánnu\", \"borgemánnu\", \"čakčamánnu\", \"golggotmánnu\", \"skábmamánnu\", \"juovlamánnu\"]], u, [[\"oKr.\", \"mKr.\"], u, [\"ovdal Kristusa\", \"maŋŋel Kristusa\"]], 1, [6, 0], [\"dd.MM.y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"−\", \"·10^\", \"·\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"DKK\": [\"Dkr\", \"kr\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"NOK\": [\"kr\"],\n  \"SEK\": [\"Skr\", \"kr\"],\n  \"THB\": [\"฿\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2UtRkkuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9zZS1GSS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwwQ0FBMEM7QUFDMUMsTUFBTSxDQUFDLEdBQUcsU0FBUyxDQUFDO0FBRXBCLFNBQVMsTUFBTSxDQUFDLEdBQVc7SUFDM0IsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBRWQsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsT0FBTyxDQUFDLENBQUM7QUFDVCxDQUFDO0FBRUQsZUFBZSxDQUFDLE9BQU8sRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsYUFBYSxFQUFDLFVBQVUsRUFBQyxRQUFRLEVBQUMsYUFBYSxFQUFDLFdBQVcsRUFBQyxXQUFXLEVBQUMsV0FBVyxDQUFDLEVBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxDQUFDLEVBQUMsQ0FBQyxlQUFlLEVBQUMsYUFBYSxFQUFDLGFBQWEsRUFBQyxZQUFZLEVBQUMsYUFBYSxFQUFDLGFBQWEsRUFBQyxjQUFjLEVBQUMsWUFBWSxFQUFDLFlBQVksRUFBQyxjQUFjLEVBQUMsYUFBYSxFQUFDLGFBQWEsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUMsTUFBTSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsZ0JBQWdCLEVBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLFNBQVMsRUFBQyxTQUFTLEVBQUMsVUFBVSxFQUFDLGVBQWUsQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLFVBQVUsRUFBQyxZQUFZLEVBQUMsZUFBZSxDQUFDLEVBQUMsQ0FBQyxTQUFTLEVBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLE1BQU0sRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxXQUFXLEVBQUMsU0FBUyxFQUFDLFlBQVksRUFBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsR0FBRyxFQUFDLE1BQU0sRUFBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEtBQUssRUFBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsSUFBSSxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLElBQUksQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxFQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUSElTIENPREUgSVMgR0VORVJBVEVEIC0gRE8gTk9UIE1PRElGWS5cbmNvbnN0IHUgPSB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIHBsdXJhbCh2YWw6IG51bWJlcik6IG51bWJlciB7XG5jb25zdCBuID0gdmFsO1xuXG5pZiAobiA9PT0gMSlcbiAgICByZXR1cm4gMTtcbmlmIChuID09PSAyKVxuICAgIHJldHVybiAyO1xucmV0dXJuIDU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IFtcInNlLUZJXCIsW1tcImlcIixcImVcIl0sW1wiaWJcIixcImViXCJdLHVdLFtbXCJpYlwiLFwiZWJcIl0sdSx1XSxbW1wiU1wiLFwiTVwiLFwiRFwiLFwiR1wiLFwiRFwiLFwiQlwiLFwiTFwiXSxbXCJzb1wiLFwibcOhXCIsXCJkaVwiLFwiZ2FcIixcImR1XCIsXCJiZVwiLFwibMOhXCJdLFtcInNvdG5hYmVhaXZpXCIsXCJtw6Fubm9kYXRcIixcImRpc2RhdFwiLFwiZ2Fza2F2YWhra3VcIixcImR1b3Jhc3RhdFwiLFwiYmVhcmphZGF0XCIsXCJsw6F2dm9yZGF0XCJdLFtcInNvXCIsXCJtw6FcIixcImRpXCIsXCJnYVwiLFwiZHVcIixcImJlXCIsXCJsw6FcIl1dLHUsW1tcIk9cIixcIkdcIixcIk5cIixcIkNcIixcIk1cIixcIkdcIixcIlNcIixcIkJcIixcIsSMXCIsXCJHXCIsXCJTXCIsXCJKXCJdLFtcIm/EkcSRalwiLFwiZ3VvdlwiLFwibmp1a1wiLFwiY3VvxYtcIixcIm1pZXNcIixcImdlYXNcIixcInN1b2lcIixcImJvcmdcIixcIsSNYWvEjVwiLFwiZ29sZ1wiLFwic2vDoWJcIixcImp1b3ZcIl0sW1wib8SRxJFhamFnZW3DoW5udVwiLFwiZ3VvdnZhbcOhbm51XCIsXCJuanVrxI1hbcOhbm51XCIsXCJjdW/Fi29tw6FubnVcIixcIm1pZXNzZW3DoW5udVwiLFwiZ2Vhc3NlbcOhbm51XCIsXCJzdW9pZG5lbcOhbm51XCIsXCJib3JnZW3DoW5udVwiLFwixI1ha8SNYW3DoW5udVwiLFwiZ29sZ2dvdG3DoW5udVwiLFwic2vDoWJtYW3DoW5udVwiLFwianVvdmxhbcOhbm51XCJdXSx1LFtbXCJvS3IuXCIsXCJtS3IuXCJdLHUsW1wib3ZkYWwgS3Jpc3R1c2FcIixcIm1hxYvFi2VsIEtyaXN0dXNhXCJdXSwxLFs2LDBdLFtcImRkLk1NLnlcIixcImQgTU1NIHlcIixcImQgTU1NTSB5XCIsXCJFRUVFIGQgTU1NTSB5XCJdLFtcIkhIOm1tXCIsXCJISDptbTpzc1wiLFwiSEg6bW06c3MgelwiLFwiSEg6bW06c3Mgenp6elwiXSxbXCJ7MX0gezB9XCIsdSx1LHVdLFtcIixcIixcIsKgXCIsXCI7XCIsXCIlXCIsXCIrXCIsXCLiiJJcIixcIsK3MTBeXCIsXCLCt1wiLFwi4oCwXCIsXCLiiJ5cIixcIk5hTlwiLFwiOlwiXSxbXCIjLCMjMC4jIyNcIixcIiMsIyMwwqAlXCIsXCIjLCMjMC4wMMKgwqRcIixcIiNFMFwiXSxcIkVVUlwiLFwi4oKsXCIsXCJldXJvXCIse1wiREtLXCI6W1wiRGtyXCIsXCJrclwiXSxcIkpQWVwiOltcIkpQwqVcIixcIsKlXCJdLFwiTk9LXCI6W1wia3JcIl0sXCJTRUtcIjpbXCJTa3JcIixcImtyXCJdLFwiVEhCXCI6W1wi4Li/XCJdLFwiVVNEXCI6W1wiVVMkXCIsXCIkXCJdfSxcImx0clwiLCBwbHVyYWxdO1xuIl19", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}