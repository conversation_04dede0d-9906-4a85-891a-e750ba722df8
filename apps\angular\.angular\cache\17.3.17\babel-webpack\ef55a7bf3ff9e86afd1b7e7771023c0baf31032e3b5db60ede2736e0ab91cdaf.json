{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var weekday = date.getUTCDay();\n    var last = weekday === 0 || weekday === 6 ? 'último' : 'última';\n    return \"'\" + last + \"' eeee 'às' p\";\n  },\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}