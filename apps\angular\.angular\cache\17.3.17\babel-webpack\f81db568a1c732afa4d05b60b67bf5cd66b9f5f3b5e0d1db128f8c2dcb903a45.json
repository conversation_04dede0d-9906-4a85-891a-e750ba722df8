{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\proxies-audit-service\\lib\\proxy\\bdo\\ess\\audit-service\\models.ts"], "sourcesContent": ["import type { AuditObjectTypeEnum } from '../shared/hosting/microservices/audit/audit-object-type-enum.enum';\r\nimport type { PagedAndSortedResultRequestDto } from '@abp/ng.core';\r\nimport type { AuditActionEnum } from '../shared/hosting/microservices/audit/audit-action-enum.enum';\r\n\r\nexport interface AuditActionDto {\r\n  auditActionId: number;\r\n  actionName: string;\r\n  actionDisplayName?: string;\r\n  isCA: boolean;\r\n  isRA: boolean;\r\n  auditObjectType: AuditObjectTypeEnum;\r\n  isDisplayOldValue: boolean;\r\n  isDisplayNewValue: boolean;\r\n  isDisplay: boolean;\r\n  actionGroup?: string;\r\n  fieldColumnWidth: number;\r\n  oldValueColumnWidth: number;\r\n  newValueColumnWidth: number;\r\n}\r\n\r\nexport interface GetAuditLogsDto extends PagedAndSortedResultRequestDto {\r\n  auditStartDate?: string;\r\n  auditEndDate?: string;\r\n  year?: number;\r\n  entityName?: string;\r\n  formationNumber?: string;\r\n  entityUniqueId?: string;\r\n  userIds: string[];\r\n  actions: AuditActionEnum[];\r\n  isAllUsers: boolean;\r\n  isAllActions: boolean;\r\n  source?: string;\r\n}\r\n\r\nexport interface AuditItemDto {\r\n  propertyName?: string;\r\n  propertyDisplayName?: string;\r\n  propertyDisplayOrder?: number;\r\n  isObject: boolean;\r\n  isCollection: boolean;\r\n  level: number;\r\n  value?: string;\r\n  childAuditItems: AuditItemDto[];\r\n  skipCurrentNode: boolean;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}