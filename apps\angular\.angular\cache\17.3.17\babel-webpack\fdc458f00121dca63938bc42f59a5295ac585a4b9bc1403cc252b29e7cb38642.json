{"ast": null, "code": "import { environment } from \"@environments/environment\";\nexport const viewDeclaration = {\n  \"title\": \"Economic Substance Declaration\",\n  \"logoPosition\": \"right\",\n  \"pages\": [{\n    \"name\": \"ES_Declaration\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"financialPeriodPanel\",\n      \"elements\": [{\n        \"type\": \"boolean\",\n        \"name\": \"financialPeriodChange\",\n        \"title\": \"2a Has an application been made and confirmed with Minister of Finance to change your financial period?\",\n        \"isRequired\": true\n      }, {\n        \"type\": \"text\",\n        \"name\": \"financialPeriodStartDate\",\n        \"title\": \"2b Financial Period Start Date:\",\n        \"enableIf\": \"{financialPeriodChange} = false\",\n        \"isRequired\": true,\n        \"inputType\": \"date\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"financialPeriodEndDate\",\n        \"title\": \"2c Financial Period End Date:\",\n        \"enableIf\": \"{financialPeriodChange} = false\",\n        \"isRequired\": true,\n        \"inputType\": \"date\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"subjectToReclassification\",\n        \"title\": \"2d Is the entity subject to reclassification of a non-included passive holding entity to a pure equity holding (PEH) entity under the Act (CESRA 2023)?\",\n        \"isRequired\": true\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"esFiledOnOTAS\",\n        \"visibleIf\": \"{financialPeriodEndDate} >= '2022-01-01' and {financialPeriodEndDate} <= '2023-12-31'\",\n        \"title\": \"2e Has the Economic Substance declaration been filed in the Online Tax Administration System (OTAS)?\",\n        \"requiredIf\": \"{financialPeriodEndDate} >= '2022-01-01' and {financialPeriodEndDate} <= '2023-12-31'\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"otasReceipt\",\n        \"visibleIf\": \"{esFiledOnOTAS} = true\",\n        \"title\": \"2f Do you have the receipt from the Online Tax Administration System (OTAS)?\",\n        \"requiredIf\": \"{esFiledOnOTAS} = true\"\n      }, {\n        \"type\": \"file\",\n        \"name\": \"otasReceiptUpload\",\n        \"visibleIf\": \"{otasReceipt} = true\",\n        \"title\": \"2g Upload evidence from the Online Tax Administration System (OTAS), i.e., a copy of the receipt\",\n        \"requiredIf\": \"{otasReceipt} = true\"\n      }],\n      \"title\": \"1.2.1 FINANCIAL PERIOD\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"entityDetailsPanel\",\n      \"elements\": [{\n        \"type\": \"text\",\n        \"name\": \"entityDetailsTIN\",\n        \"title\": \"3a Entity Tax Payer Identification Number (TIN)\",\n        \"maxLength\": 100\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"entityDetailsBusinessSameAsRegisteredAddress\",\n        \"title\": \"3c Physical Business Address is same as registered address\",\n        \"isRequired\": true\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"entityDetailsAnnualIncome\",\n        \"title\": \"3b Gross total annual income of the entity\",\n        \"hideNumber\": true,\n        \"isRequired\": true,\n        \"columns\": [{\n          \"name\": \"currency\",\n          \"title\": \"Currency\",\n          \"cellType\": \"text\",\n          \"readOnly\": true,\n          \"defaultValueExpression\": \"USD\"\n        }, {\n          \"name\": \"currencyValue\",\n          \"title\": \"Annual Income\",\n          \"cellType\": \"text\",\n          \"inputType\": \"number\",\n          \"min\": 0\n        }],\n        \"allowAddRows\": false,\n        \"allowRemoveRows\": false,\n        \"rowCount\": 1,\n        \"maxRowCount\": 1\n      }, {\n        \"type\": \"text\",\n        \"name\": \"entityDetailsEnterDifferntBusinessAddress\",\n        \"visible\": false,\n        \"visibleIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n        \"title\": \"3c.1 Physical Business Address\",\n        \"requiredIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\n        \"maxLength\": 100\n      }, {\n        \"type\": \"text\",\n        \"name\": \"entityDetailsNameMNE\",\n        \"title\": \"3d Name of the Multinational Enterprise (MNE) group\",\n        \"maxLength\": 100\n      }],\n      \"title\": \"ENTITY DETAILS\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"relevantActivityPanel\",\n      \"elements\": [{\n        \"type\": \"tagbox\",\n        \"name\": \"relevantActRelevantActivities\",\n        \"title\": \"4a Relevant activity that was carried on during Financial Period\",\n        \"isRequired\": true,\n        \"choicesByUrl\": {\n          \"url\": environment['apis']['default']['url'] + \"api/lookup-service/relevantActivity\",\n          \"path\": \"items\",\n          \"valueName\": \"name\",\n          \"titleName\": \"name\"\n        },\n        \"showNoneItem\": true,\n        \"allowClear\": false\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"relevantActPartOfFinancialPeriod\",\n        \"title\": \"4b Carried on for only part of the Financial Period?\",\n        \"enableIf\": \"{relevantActRelevantActivities} notcontains 'none' and {relevantActRelevantActivities} notempty\",\n        \"requiredIf\": \"{relevantActRelevantActivities} <> ['none'] and {relevantActRelevantActivities} notempty\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"relevantActPartOfFinancialPeriodStartDate\",\n        \"title\": \"4c Start Date\",\n        \"enableIf\": \"{relevantActPartOfFinancialPeriod} = true\",\n        \"requiredIf\": \"{relevantActPartOfFinancialPeriod} = true\",\n        \"inputType\": \"date\",\n        \"minValueExpression\": \"{FinancialPeriodStartDate}\"\n      }, {\n        \"type\": \"text\",\n        \"name\": \"relevantActPartOfFinancialPeriodEndDate\",\n        \"title\": \"4d End Date\",\n        \"enableIf\": \"{relevantActPartOfFinancialPeriod} = true\",\n        \"requiredIf\": \"{relevantActPartOfFinancialPeriod} = true\",\n        \"inputType\": \"date\",\n        \"minValueExpression\": \"{PartOfFinancialPeriodStartDate}\",\n        \"maxValueExpression\": \"{FinancialPeriodEndDate}\"\n      }],\n      \"title\": \"1.2.3 Relevant Activity\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"taxResidencyPanel\",\n      \"elements\": [{\n        \"type\": \"boolean\",\n        \"name\": \"taxResidency100PercentBahamian\",\n        \"title\": \"5a Are you a 100% Bahamian/resident-owned and Core Income Generated Activity (CIGA) conducted in the Bahamas?\",\n        \"isRequired\": true\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"taxResidencyIsInvestmentFund\",\n        \"visibleIf\": \"{taxResidency100PercentBahamian} = false\",\n        \"title\": \"5b Are you an Investment Fund according to the Investment Funds Act, 2019 (No. 2 of 2019)?\",\n        \"requiredIf\": \"{taxResidency100PercentBahamian} = false\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"taxResidencyOutsideBahamas\",\n        \"visibleIf\": \"{taxResidencyIsInvestmentFund} = false\",\n        \"title\": \"5c Does the entity intend to make a claim of tax residency outside of the Bahamas under Rule 10(2)?\",\n        \"requiredIf\": \"{taxResidencyIsInvestmentFund} = false\"\n      }, {\n        \"type\": \"dropdown\",\n        \"name\": \"taxResidencyJurisdictionEntityIsTaxResident\",\n        \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"title\": \"5c.i Jurisdiction in which the entity is tax resident\",\n        \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"choicesByUrl\": {\n          \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n          \"path\": \"\",\n          \"valueName\": \"name\"\n        }\n      }, {\n        \"type\": \"text\",\n        \"name\": \"taxResidencyTaxpayerIDNumber\",\n        \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"title\": \"5c.ii Taxpayer Identification Number\",\n        \"isRequired\": true,\n        \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\"\n      }, {\n        \"type\": \"file\",\n        \"name\": \"taxResidencyEvidenceOfTaxResidency\",\n        \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\n        \"title\": \"5c.iii Upload evidence of Tax Residency in another jurisdiction which meets rule 10(2)(b).\",\n        \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\"\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"taxResidencyHasParent\",\n        \"title\": \"5d Does Entity have an ultimate parent entity?\",\n        \"isRequired\": true\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"taxResidencyUltimateParentEntityInfo\",\n        \"visibleIf\": \"{taxResidencyHasParent} = true\",\n        \"title\": \"5d.1,2,3,4,5 Ultimate Parent Entity\",\n        \"requiredIf\": \"{taxResidencyHasParent} = true\",\n        \"columns\": [{\n          \"name\": \"name\",\n          \"title\": \"Name\",\n          \"cellType\": \"text\",\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"alternativeName\",\n          \"title\": \"Alternative Name\",\n          \"cellType\": \"text\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"incorporationNumber\",\n          \"title\": \"Incorporation Number or its equivalent\",\n          \"cellType\": \"text\",\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"identificationNumber\",\n          \"title\": \"Identification Number\",\n          \"cellType\": \"text\",\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"jurisdiction\",\n          \"title\": \"Jurisdiction of Formation\",\n          \"cellType\": \"dropdown\",\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\n          \"choicesByUrl\": {\n            \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n            \"path\": \"\",\n            \"valueName\": \"id\"\n          }\n        }],\n        \"rowCount\": 0\n      }, {\n        \"type\": \"boolean\",\n        \"name\": \"hasImmediateParent\",\n        \"title\": \"5e Does Entity have an immediate parent entity?\",\n        \"isRequired\": true\n      }, {\n        \"type\": \"matrixdynamic\",\n        \"name\": \"taxResidencyImmediateParentEntity\",\n        \"visibleIf\": \"{hasImmediateParent} = true\",\n        \"title\": \"5e.1,2,3,4,5 Immediate Parent Entity\",\n        \"requiredIf\": \"{hasImmediateParent} = true\",\n        \"columns\": [{\n          \"name\": \"name\",\n          \"title\": \"Name\",\n          \"cellType\": \"text\",\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"alternativeName\",\n          \"title\": \"Alternative Name\",\n          \"cellType\": \"text\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"incorporationNumber\",\n          \"title\": \"Incorporation Number or its equivalent\",\n          \"cellType\": \"text\",\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"identificationNumber\",\n          \"title\": \"Identification Number\",\n          \"cellType\": \"text\",\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"validators\": [{\n            \"type\": \"text\",\n            \"maxLength\": 100\n          }]\n        }, {\n          \"name\": \"jurisdiction\",\n          \"title\": \"Jurisdiction of Formation\",\n          \"cellType\": \"dropdown\",\n          \"requiredIf\": \"{hasImmediateParent} = true\",\n          \"choicesByUrl\": {\n            \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\n            \"path\": \"\",\n            \"valueName\": \"id\"\n          }\n        }],\n        \"rowCount\": 0\n      }],\n      \"title\": \"1.2.4 Tax Residency\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"activityDetailPanel\",\n      \"elements\": [{\n        \"type\": \"intellectualpropertybusiness\",\n        \"name\": \"intellectualPropertyBusiness\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Intellectual property business']\",\n        \"title\": \"1.1.5.1 Intellectual Property Business (Low & High Risk)\"\n      }, {\n        \"type\": \"outsourcing\",\n        \"name\": \"outsourcingIntellectualPropertyBusiness\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['other', 'Intellectual property business']\",\n        \"title\": \"Outsourcing Intellectual Property Business\"\n      }, {\n        \"type\": \"holdingbusinessquestions\",\n        \"name\": \"holdingBusinessQuestions\",\n        \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Holding business']\",\n        \"title\": \"Holding Business\"\n      }, {\n        \"type\": \"panel\",\n        \"name\": \"OtherRelevantActivityQuestionsPanel\",\n        \"elements\": [{\n          \"type\": \"bankingotherrelevantactivities\",\n          \"name\": \"bankingQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Banking business']\",\n          \"title\": \"Banking Business\"\n        }, {\n          \"type\": \"insuranceotherrelevantactivities\",\n          \"name\": \"insuranceQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Insurance business']\",\n          \"title\": \"Insurance Business\"\n        }, {\n          \"type\": \"fundmanagmentotherrelevantactivities\",\n          \"name\": \"fundManagmentQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Fund management business']\",\n          \"title\": \"Fund Management Business\"\n        }, {\n          \"type\": \"financeotherrelevantactivities\",\n          \"name\": \"financeQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Finance and leasing business']\",\n          \"title\": \"Finance and Leasing Business\"\n        }, {\n          \"type\": \"headquartersotherrelevantactivities\",\n          \"name\": \"headquartersQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Headquarters business']\",\n          \"title\": \"Headquarters Business\"\n        }, {\n          \"type\": \"shippingotherrelevantactivities\",\n          \"name\": \"shippingQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Shipping business']\",\n          \"title\": \"Shipping Business\"\n        }, {\n          \"type\": \"distributionotherrelevantactivities\",\n          \"name\": \"distributionQuestions\",\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Distribution and service centre business']\",\n          \"title\": \"Distribution and Service Centre Business\"\n        }],\n        \"title\": \"Other Relevant Activity Questions\"\n      }],\n      \"title\": \"Activity Details\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"supportingDetailPanel\",\n      \"elements\": [{\n        \"type\": \"comment\",\n        \"name\": \"comments\",\n        \"title\": \"10a Provide comments supporting your Economic Substance Declaration, and if applicable, include the Tax Residence Country, full addresses (with country) and TIN (including issue country) for the Ultimate and Immediate Parents\",\n        \"maxLength\": 255\n      }, {\n        \"type\": \"file\",\n        \"name\": \"supportingAttachment\",\n        \"title\": \"Supporting Attachments\"\n      }],\n      \"title\": \"1.1.6 Supporting Details\"\n    }],\n    \"title\": \"ES Declaration\"\n  }],\n  \"showTitle\": false,\n  \"widthMode\": \"responsive\"\n};\nexport const viewDeclarationEmpty = {\n  \"title\": \"Economic Substance Declaration\",\n  \"logoPosition\": \"right\",\n  \"pages\": [{\n    \"name\": \"ES_Declaration\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"financialPeriod\",\n      \"title\": \"1.2.1 FINANCIAL PERIOD\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"entityDetails\",\n      \"title\": \"ENTITY DETAILS\",\n      \"visibleIf\": \"{esFiledOnOTAS} <> true\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"relevantActivity\",\n      \"title\": \"1.2.3 Relevant Activity\",\n      \"visibleIf\": \"{esFiledOnOTAS} <> true\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"taxResidency\",\n      \"title\": \"1.2.4 Tax Residency\",\n      \"visibleIf\": \"{relevantActRelevantActivities} <> ['none'] and {esFiledOnOTAS} <> true\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"activityDetail\",\n      \"title\": \"Activity Details\",\n      \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false and {taxResidencyOutsideBahamas} = false and {relevantActRelevantActivities} <> ['none'] and {esFiledOnOTAS} <> true\"\n    }, {\n      \"type\": \"panel\",\n      \"name\": \"Supporting_Details\",\n      \"title\": \"1.1.6 Supporting Details\"\n    }],\n    \"title\": \"ES Declaration\"\n  }],\n  \"widthMode\": \"responsive\",\n  \"showTitle\": false,\n  \"showQuestionNumbers\": 'false'\n};\nexport const viewRelevantActivites = {\n  \"pages\": [{\n    \"name\": \"ES_Declaration\",\n    \"elements\": [{\n      \"type\": \"panel\",\n      \"name\": \"activityDetail\",\n      \"title\": \"Activity Details\",\n      \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false and {taxResidencyOutsideBahamas} = false and {relevantActRelevantActivities} <> ['none'] and {esFiledOnOTAS} <> true\"\n    }]\n    //\"title\": \"ES Declaration\"\n  }],\n  \"widthMode\": \"responsive\",\n  \"showTitle\": false\n};", "map": {"version": 3, "names": ["environment", "viewDeclaration", "viewDeclarationEmpty", "viewRelevantActivites"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\declaration-jsons\\view-declaration-json.ts"], "sourcesContent": ["import { SurveyDto } from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates\"\r\nimport { environment } from \"@environments/environment\"\r\n\r\nexport const viewDeclaration: SurveyDto = {\r\n    \"title\": \"Economic Substance Declaration\",\r\n    \"logoPosition\": \"right\",\r\n    \"pages\": [\r\n     {\r\n      \"name\": \"ES_Declaration\",\r\n      \"elements\": [\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"financialPeriodPanel\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"financialPeriodChange\",\r\n          \"title\": \"2a Has an application been made and confirmed with Minister of Finance to change your financial period?\",\r\n          \"isRequired\": true\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"financialPeriodStartDate\",\r\n          \"title\": \"2b Financial Period Start Date:\",\r\n          \"enableIf\": \"{financialPeriodChange} = false\",\r\n          \"isRequired\": true,\r\n          \"inputType\": \"date\"\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"financialPeriodEndDate\",\r\n          \"title\": \"2c Financial Period End Date:\",\r\n          \"enableIf\": \"{financialPeriodChange} = false\",\r\n          \"isRequired\": true,\r\n          \"inputType\": \"date\"\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"subjectToReclassification\",\r\n          \"title\": \"2d Is the entity subject to reclassification of a non-included passive holding entity to a pure equity holding (PEH) entity under the Act (CESRA 2023)?\",\r\n          \"isRequired\": true\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"esFiledOnOTAS\",\r\n          \"visibleIf\": \"{financialPeriodEndDate} >= '2022-01-01' and {financialPeriodEndDate} <= '2023-12-31'\",\r\n          \"title\": \"2e Has the Economic Substance declaration been filed in the Online Tax Administration System (OTAS)?\",\r\n          \"requiredIf\": \"{financialPeriodEndDate} >= '2022-01-01' and {financialPeriodEndDate} <= '2023-12-31'\"\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"otasReceipt\",\r\n          \"visibleIf\": \"{esFiledOnOTAS} = true\",\r\n          \"title\": \"2f Do you have the receipt from the Online Tax Administration System (OTAS)?\",\r\n          \"requiredIf\": \"{esFiledOnOTAS} = true\"\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"otasReceiptUpload\",\r\n          \"visibleIf\": \"{otasReceipt} = true\",\r\n          \"title\": \"2g Upload evidence from the Online Tax Administration System (OTAS), i.e., a copy of the receipt\",\r\n          \"requiredIf\": \"{otasReceipt} = true\"\r\n         }\r\n        ],\r\n        \"title\": \"1.2.1 FINANCIAL PERIOD\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"entityDetailsPanel\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"entityDetailsTIN\",\r\n          \"title\": \"3a Entity Tax Payer Identification Number (TIN)\",\r\n          \"maxLength\": 100\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"entityDetailsBusinessSameAsRegisteredAddress\",\r\n          \"title\": \"3c Physical Business Address is same as registered address\",\r\n          \"isRequired\": true\r\n         },\r\n         {\r\n          \"type\": \"matrixdynamic\",\r\n          \"name\": \"entityDetailsAnnualIncome\",\r\n          \"title\": \"3b Gross total annual income of the entity\",\r\n          \"hideNumber\": true,\r\n          \"isRequired\": true,\r\n          \"columns\": [\r\n           {\r\n            \"name\": \"currency\",\r\n            \"title\": \"Currency\",\r\n            \"cellType\": \"text\",\r\n            \"readOnly\": true,\r\n            \"defaultValueExpression\": \"USD\"\r\n           },\r\n           {\r\n            \"name\": \"currencyValue\",\r\n            \"title\": \"Annual Income\",\r\n            \"cellType\": \"text\",\r\n            \"inputType\": \"number\",\r\n            \"min\": 0\r\n           }\r\n          ],\r\n          \"allowAddRows\": false,\r\n          \"allowRemoveRows\": false,\r\n          \"rowCount\": 1,\r\n          \"maxRowCount\": 1\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"entityDetailsEnterDifferntBusinessAddress\",\r\n          \"visible\": false,\r\n          \"visibleIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\r\n          \"title\": \"3c.1 Physical Business Address\",\r\n          \"requiredIf\": \"{entityDetailsBusinessSameAsRegisteredAddress} = false\",\r\n          \"maxLength\": 100\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"entityDetailsNameMNE\",\r\n          \"title\": \"3d Name of the Multinational Enterprise (MNE) group\",\r\n          \"maxLength\": 100\r\n         }\r\n        ],\r\n        \"title\": \"ENTITY DETAILS\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"relevantActivityPanel\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"tagbox\",\r\n          \"name\": \"relevantActRelevantActivities\",\r\n          \"title\": \"4a Relevant activity that was carried on during Financial Period\",\r\n          \"isRequired\": true,\r\n          \"choicesByUrl\": {\r\n           \"url\": environment['apis']['default']['url'] + \"api/lookup-service/relevantActivity\",\r\n           \"path\": \"items\",\r\n           \"valueName\": \"name\",\r\n           \"titleName\": \"name\"\r\n          },\r\n          \"showNoneItem\": true,\r\n          \"allowClear\": false\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"relevantActPartOfFinancialPeriod\",\r\n          \"title\": \"4b Carried on for only part of the Financial Period?\",\r\n          \"enableIf\": \"{relevantActRelevantActivities} notcontains 'none' and {relevantActRelevantActivities} notempty\",\r\n          \"requiredIf\": \"{relevantActRelevantActivities} <> ['none'] and {relevantActRelevantActivities} notempty\"\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"relevantActPartOfFinancialPeriodStartDate\",\r\n          \"title\": \"4c Start Date\",\r\n          \"enableIf\": \"{relevantActPartOfFinancialPeriod} = true\",\r\n          \"requiredIf\": \"{relevantActPartOfFinancialPeriod} = true\",\r\n          \"inputType\": \"date\",\r\n          \"minValueExpression\": \"{FinancialPeriodStartDate}\"\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"relevantActPartOfFinancialPeriodEndDate\",\r\n          \"title\": \"4d End Date\",\r\n          \"enableIf\": \"{relevantActPartOfFinancialPeriod} = true\",\r\n          \"requiredIf\": \"{relevantActPartOfFinancialPeriod} = true\",\r\n          \"inputType\": \"date\",\r\n          \"minValueExpression\": \"{PartOfFinancialPeriodStartDate}\",\r\n          \"maxValueExpression\": \"{FinancialPeriodEndDate}\"\r\n         }\r\n        ],\r\n        \"title\": \"1.2.3 Relevant Activity\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"taxResidencyPanel\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"taxResidency100PercentBahamian\",\r\n          \"title\": \"5a Are you a 100% Bahamian/resident-owned and Core Income Generated Activity (CIGA) conducted in the Bahamas?\",\r\n          \"isRequired\": true\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"taxResidencyIsInvestmentFund\",\r\n          \"visibleIf\": \"{taxResidency100PercentBahamian} = false\",\r\n          \"title\": \"5b Are you an Investment Fund according to the Investment Funds Act, 2019 (No. 2 of 2019)?\",\r\n          \"requiredIf\": \"{taxResidency100PercentBahamian} = false\"\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"taxResidencyOutsideBahamas\",\r\n          \"visibleIf\": \"{taxResidencyIsInvestmentFund} = false\",\r\n          \"title\": \"5c Does the entity intend to make a claim of tax residency outside of the Bahamas under Rule 10(2)?\",\r\n          \"requiredIf\": \"{taxResidencyIsInvestmentFund} = false\"\r\n         },\r\n         {\r\n          \"type\": \"dropdown\",\r\n          \"name\": \"taxResidencyJurisdictionEntityIsTaxResident\",\r\n          \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\r\n          \"title\": \"5c.i Jurisdiction in which the entity is tax resident\",\r\n          \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\",\r\n          \"choicesByUrl\": {\r\n           \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\r\n           \"path\": \"\",\r\n           \"valueName\": \"name\"\r\n          }\r\n         },\r\n         {\r\n          \"type\": \"text\",\r\n          \"name\": \"taxResidencyTaxpayerIDNumber\",\r\n          \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\r\n          \"title\": \"5c.ii Taxpayer Identification Number\",\r\n          \"isRequired\": true,\r\n          \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\"\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"taxResidencyEvidenceOfTaxResidency\",\r\n          \"visibleIf\": \"{taxResidencyOutsideBahamas} = true\",\r\n          \"title\": \"5c.iii Upload evidence of Tax Residency in another jurisdiction which meets rule 10(2)(b).\",\r\n          \"requiredIf\": \"{taxResidencyOutsideBahamas} = true\"\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"taxResidencyHasParent\",\r\n          \"title\": \"5d Does Entity have an ultimate parent entity?\",\r\n          \"isRequired\": true\r\n         },\r\n         {\r\n          \"type\": \"matrixdynamic\",\r\n          \"name\": \"taxResidencyUltimateParentEntityInfo\",\r\n          \"visibleIf\": \"{taxResidencyHasParent} = true\",\r\n          \"title\": \"5d.1,2,3,4,5 Ultimate Parent Entity\",\r\n          \"requiredIf\": \"{taxResidencyHasParent} = true\",\r\n          \"columns\": [\r\n           {\r\n            \"name\": \"name\",\r\n            \"title\": \"Name\",\r\n            \"cellType\": \"text\",\r\n            \"requiredIf\": \"{taxResidencyHasParent} = true\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"alternativeName\",\r\n            \"title\": \"Alternative Name\",\r\n            \"cellType\": \"text\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"incorporationNumber\",\r\n            \"title\": \"Incorporation Number or its equivalent\",\r\n            \"cellType\": \"text\",\r\n            \"requiredIf\": \"{taxResidencyHasParent} = true\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"identificationNumber\",\r\n            \"title\": \"Identification Number\",\r\n            \"cellType\": \"text\",\r\n            \"requiredIf\": \"{taxResidencyHasParent} = true\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"jurisdiction\",\r\n            \"title\": \"Jurisdiction of Formation\",\r\n            \"cellType\": \"dropdown\",\r\n            \"requiredIf\": \"{taxResidencyHasParent} = true\",\r\n            \"choicesByUrl\": {\r\n             \"url\": environment['apis']['default']['url'] + \"api/lookup-service/country/GetAllCountriesOrdered\",\r\n             \"path\": \"\",\r\n             \"valueName\": \"id\"\r\n            }\r\n           }\r\n          ],\r\n          \"rowCount\": 0\r\n         },\r\n         {\r\n          \"type\": \"boolean\",\r\n          \"name\": \"hasImmediateParent\",\r\n          \"title\": \"5e Does Entity have an immediate parent entity?\",\r\n          \"isRequired\": true\r\n         },\r\n         {\r\n          \"type\": \"matrixdynamic\",\r\n          \"name\": \"taxResidencyImmediateParentEntity\",\r\n          \"visibleIf\": \"{hasImmediateParent} = true\",\r\n          \"title\": \"5e.1,2,3,4,5 Immediate Parent Entity\",\r\n          \"requiredIf\": \"{hasImmediateParent} = true\",\r\n          \"columns\": [\r\n           {\r\n            \"name\": \"name\",\r\n            \"title\": \"Name\",\r\n            \"cellType\": \"text\",\r\n            \"requiredIf\": \"{hasImmediateParent} = true\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"alternativeName\",\r\n            \"title\": \"Alternative Name\",\r\n            \"cellType\": \"text\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"incorporationNumber\",\r\n            \"title\": \"Incorporation Number or its equivalent\",\r\n            \"cellType\": \"text\",\r\n            \"requiredIf\": \"{hasImmediateParent} = true\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"identificationNumber\",\r\n            \"title\": \"Identification Number\",\r\n            \"cellType\": \"text\",\r\n            \"requiredIf\": \"{hasImmediateParent} = true\",\r\n            \"validators\": [\r\n             {\r\n              \"type\": \"text\",\r\n              \"maxLength\": 100\r\n             }\r\n            ]\r\n           },\r\n           {\r\n            \"name\": \"jurisdiction\",\r\n            \"title\": \"Jurisdiction of Formation\",\r\n            \"cellType\": \"dropdown\",\r\n            \"requiredIf\": \"{hasImmediateParent} = true\",\r\n            \"choicesByUrl\": {\r\n             \"url\": environment['apis']['default']['url'] +  \"api/lookup-service/country/GetAllCountriesOrdered\",\r\n             \"path\": \"\",\r\n             \"valueName\": \"id\"\r\n            }\r\n           }\r\n          ],\r\n          \"rowCount\": 0\r\n         }\r\n        ],\r\n        \"title\": \"1.2.4 Tax Residency\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"activityDetailPanel\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"intellectualpropertybusiness\",\r\n          \"name\": \"intellectualPropertyBusiness\",\r\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Intellectual property business']\",\r\n          \"title\": \"1.1.5.1 Intellectual Property Business (Low & High Risk)\"\r\n         },\r\n         {\r\n          \"type\": \"outsourcing\",\r\n          \"name\": \"outsourcingIntellectualPropertyBusiness\",\r\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['other', 'Intellectual property business']\",\r\n          \"title\": \"Outsourcing Intellectual Property Business\"\r\n         },\r\n         {\r\n          \"type\": \"holdingbusinessquestions\",\r\n          \"name\": \"holdingBusinessQuestions\",\r\n          \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Holding business']\",\r\n          \"title\": \"Holding Business\"\r\n         },\r\n         {\r\n          \"type\": \"panel\",\r\n          \"name\": \"OtherRelevantActivityQuestionsPanel\",\r\n          \"elements\": [\r\n           {\r\n            \"type\": \"bankingotherrelevantactivities\",\r\n            \"name\": \"bankingQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Banking business']\",\r\n            \"title\": \"Banking Business\"\r\n           },\r\n           {\r\n            \"type\": \"insuranceotherrelevantactivities\",\r\n            \"name\": \"insuranceQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Insurance business']\",\r\n            \"title\": \"Insurance Business\"\r\n           },\r\n           {\r\n            \"type\": \"fundmanagmentotherrelevantactivities\",\r\n            \"name\": \"fundManagmentQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Fund management business']\",\r\n            \"title\": \"Fund Management Business\"\r\n           },\r\n           {\r\n            \"type\": \"financeotherrelevantactivities\",\r\n            \"name\": \"financeQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Finance and leasing business']\",\r\n            \"title\": \"Finance and Leasing Business\"\r\n           },\r\n           {\r\n            \"type\": \"headquartersotherrelevantactivities\",\r\n            \"name\": \"headquartersQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Headquarters business']\",\r\n            \"title\": \"Headquarters Business\"\r\n           },\r\n           {\r\n            \"type\": \"shippingotherrelevantactivities\",\r\n            \"name\": \"shippingQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Shipping business']\",\r\n            \"title\": \"Shipping Business\"\r\n           },\r\n           {\r\n            \"type\": \"distributionotherrelevantactivities\",\r\n            \"name\": \"distributionQuestions\",\r\n            \"visibleIf\": \"{relevantActRelevantActivities} anyof ['Distribution and service centre business']\",\r\n            \"title\": \"Distribution and Service Centre Business\"\r\n           }\r\n          ],\r\n          \"title\": \"Other Relevant Activity Questions\"\r\n         }\r\n        ],\r\n        \"title\": \"Activity Details\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"supportingDetailPanel\",\r\n        \"elements\": [\r\n         {\r\n          \"type\": \"comment\",\r\n          \"name\": \"comments\",\r\n          \"title\": \"10a Provide comments supporting your Economic Substance Declaration, and if applicable, include the Tax Residence Country, full addresses (with country) and TIN (including issue country) for the Ultimate and Immediate Parents\",\r\n          \"maxLength\": 255\r\n         },\r\n         {\r\n          \"type\": \"file\",\r\n          \"name\": \"supportingAttachment\",\r\n          \"title\": \"Supporting Attachments\"\r\n         }\r\n        ],\r\n        \"title\": \"1.1.6 Supporting Details\"\r\n       }\r\n      ],\r\n      \"title\": \"ES Declaration\"\r\n     }\r\n    ],\r\n    \"showTitle\": false,\r\n    \"widthMode\": \"responsive\"\r\n   } as SurveyDto\r\n\r\nexport const viewDeclarationEmpty: SurveyDto = {\r\n    \"title\": \"Economic Substance Declaration\",\r\n    \"logoPosition\": \"right\",\r\n    \"pages\": [\r\n     {\r\n      \"name\": \"ES_Declaration\",\r\n      \"elements\": [\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"financialPeriod\",\r\n        \"title\": \"1.2.1 FINANCIAL PERIOD\"\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"entityDetails\",\r\n        \"title\": \"ENTITY DETAILS\",\r\n        \"visibleIf\": \"{esFiledOnOTAS} <> true\",\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"relevantActivity\",\r\n        \"title\": \"1.2.3 Relevant Activity\",\r\n        \"visibleIf\": \"{esFiledOnOTAS} <> true\",\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"taxResidency\",\r\n        \"title\": \"1.2.4 Tax Residency\",\r\n        \"visibleIf\": \"{relevantActRelevantActivities} <> ['none'] and {esFiledOnOTAS} <> true\",\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"activityDetail\",\r\n        \"title\": \"Activity Details\",\r\n        \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false and {taxResidencyOutsideBahamas} = false and {relevantActRelevantActivities} <> ['none'] and {esFiledOnOTAS} <> true\",\r\n       },\r\n       {\r\n        \"type\": \"panel\",\r\n        \"name\": \"Supporting_Details\",\r\n        \"title\": \"1.1.6 Supporting Details\"\r\n       }\r\n      ],\r\n      \"title\": \"ES Declaration\"\r\n     }\r\n    ],\r\n    \"widthMode\": \"responsive\",\r\n    \"showTitle\": false,\r\n    \"showQuestionNumbers\": 'false'\r\n   } as SurveyDto\r\n\r\nexport const viewRelevantActivites: SurveyDto = {\r\n  \"pages\": [\r\n   {\r\n    \"name\": \"ES_Declaration\",\r\n    \"elements\": [\r\n\r\n     {\r\n      \"type\": \"panel\",\r\n      \"name\": \"activityDetail\",\r\n      \"title\": \"Activity Details\",\r\n      \"visibleIf\": \"{taxResidency100PercentBahamian} = false and {taxResidencyIsInvestmentFund} = false and {taxResidencyOutsideBahamas} = false and {relevantActRelevantActivities} <> ['none'] and {esFiledOnOTAS} <> true\",\r\n     },\r\n    ],\r\n    //\"title\": \"ES Declaration\"\r\n   }\r\n  ],\r\n  \"widthMode\": \"responsive\",\r\n  \"showTitle\": false\r\n } as SurveyDto\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,2BAA2B;AAEvD,OAAO,MAAMC,eAAe,GAAc;EACtC,OAAO,EAAE,gCAAgC;EACzC,cAAc,EAAE,OAAO;EACvB,OAAO,EAAE,CACR;IACC,MAAM,EAAE,gBAAgB;IACxB,UAAU,EAAE,CACX;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,sBAAsB;MAC9B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,uBAAuB;QAC/B,OAAO,EAAE,yGAAyG;QAClH,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,0BAA0B;QAClC,OAAO,EAAE,iCAAiC;QAC1C,UAAU,EAAE,iCAAiC;QAC7C,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,wBAAwB;QAChC,OAAO,EAAE,+BAA+B;QACxC,UAAU,EAAE,iCAAiC;QAC7C,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,2BAA2B;QACnC,OAAO,EAAE,yJAAyJ;QAClK,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,eAAe;QACvB,WAAW,EAAE,uFAAuF;QACpG,OAAO,EAAE,sGAAsG;QAC/G,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,aAAa;QACrB,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,8EAA8E;QACvF,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,mBAAmB;QAC3B,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,kGAAkG;QAC3G,YAAY,EAAE;OACd,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,oBAAoB;MAC5B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,kBAAkB;QAC1B,OAAO,EAAE,iDAAiD;QAC1D,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,8CAA8C;QACtD,OAAO,EAAE,4DAA4D;QACrE,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,2BAA2B;QACnC,OAAO,EAAE,4CAA4C;QACrD,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE,CACV;UACC,MAAM,EAAE,UAAU;UAClB,OAAO,EAAE,UAAU;UACnB,UAAU,EAAE,MAAM;UAClB,UAAU,EAAE,IAAI;UAChB,wBAAwB,EAAE;SAC1B,EACD;UACC,MAAM,EAAE,eAAe;UACvB,OAAO,EAAE,eAAe;UACxB,UAAU,EAAE,MAAM;UAClB,WAAW,EAAE,QAAQ;UACrB,KAAK,EAAE;SACP,CACD;QACD,cAAc,EAAE,KAAK;QACrB,iBAAiB,EAAE,KAAK;QACxB,UAAU,EAAE,CAAC;QACb,aAAa,EAAE;OACf,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,2CAA2C;QACnD,SAAS,EAAE,KAAK;QAChB,WAAW,EAAE,wDAAwD;QACrE,OAAO,EAAE,gCAAgC;QACzC,YAAY,EAAE,wDAAwD;QACtE,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,sBAAsB;QAC9B,OAAO,EAAE,qDAAqD;QAC9D,WAAW,EAAE;OACb,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,uBAAuB;MAC/B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,+BAA+B;QACvC,OAAO,EAAE,kEAAkE;QAC3E,YAAY,EAAE,IAAI;QAClB,cAAc,EAAE;UACf,KAAK,EAAED,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,qCAAqC;UACpF,MAAM,EAAE,OAAO;UACf,WAAW,EAAE,MAAM;UACnB,WAAW,EAAE;SACb;QACD,cAAc,EAAE,IAAI;QACpB,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,kCAAkC;QAC1C,OAAO,EAAE,sDAAsD;QAC/D,UAAU,EAAE,iGAAiG;QAC7G,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,2CAA2C;QACnD,OAAO,EAAE,eAAe;QACxB,UAAU,EAAE,2CAA2C;QACvD,YAAY,EAAE,2CAA2C;QACzD,WAAW,EAAE,MAAM;QACnB,oBAAoB,EAAE;OACtB,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,yCAAyC;QACjD,OAAO,EAAE,aAAa;QACtB,UAAU,EAAE,2CAA2C;QACvD,YAAY,EAAE,2CAA2C;QACzD,WAAW,EAAE,MAAM;QACnB,oBAAoB,EAAE,kCAAkC;QACxD,oBAAoB,EAAE;OACtB,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,mBAAmB;MAC3B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,gCAAgC;QACxC,OAAO,EAAE,+GAA+G;QACxH,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,8BAA8B;QACtC,WAAW,EAAE,0CAA0C;QACvD,OAAO,EAAE,4FAA4F;QACrG,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,4BAA4B;QACpC,WAAW,EAAE,wCAAwC;QACrD,OAAO,EAAE,qGAAqG;QAC9G,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,6CAA6C;QACrD,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,uDAAuD;QAChE,YAAY,EAAE,qCAAqC;QACnD,cAAc,EAAE;UACf,KAAK,EAAEA,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,mDAAmD;UAClG,MAAM,EAAE,EAAE;UACV,WAAW,EAAE;;OAEd,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,8BAA8B;QACtC,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,sCAAsC;QAC/C,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,oCAAoC;QAC5C,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,4FAA4F;QACrG,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,uBAAuB;QAC/B,OAAO,EAAE,gDAAgD;QACzD,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,sCAAsC;QAC9C,WAAW,EAAE,gCAAgC;QAC7C,OAAO,EAAE,qCAAqC;QAC9C,YAAY,EAAE,gCAAgC;QAC9C,SAAS,EAAE,CACV;UACC,MAAM,EAAE,MAAM;UACd,OAAO,EAAE,MAAM;UACf,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,gCAAgC;UAC9C,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,iBAAiB;UACzB,OAAO,EAAE,kBAAkB;UAC3B,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,qBAAqB;UAC7B,OAAO,EAAE,wCAAwC;UACjD,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,gCAAgC;UAC9C,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,sBAAsB;UAC9B,OAAO,EAAE,uBAAuB;UAChC,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,gCAAgC;UAC9C,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,cAAc;UACtB,OAAO,EAAE,2BAA2B;UACpC,UAAU,EAAE,UAAU;UACtB,YAAY,EAAE,gCAAgC;UAC9C,cAAc,EAAE;YACf,KAAK,EAAEA,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,mDAAmD;YAClG,MAAM,EAAE,EAAE;YACV,WAAW,EAAE;;SAEd,CACD;QACD,UAAU,EAAE;OACZ,EACD;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,oBAAoB;QAC5B,OAAO,EAAE,iDAAiD;QAC1D,YAAY,EAAE;OACd,EACD;QACC,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mCAAmC;QAC3C,WAAW,EAAE,6BAA6B;QAC1C,OAAO,EAAE,sCAAsC;QAC/C,YAAY,EAAE,6BAA6B;QAC3C,SAAS,EAAE,CACV;UACC,MAAM,EAAE,MAAM;UACd,OAAO,EAAE,MAAM;UACf,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,6BAA6B;UAC3C,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,iBAAiB;UACzB,OAAO,EAAE,kBAAkB;UAC3B,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,qBAAqB;UAC7B,OAAO,EAAE,wCAAwC;UACjD,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,6BAA6B;UAC3C,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,sBAAsB;UAC9B,OAAO,EAAE,uBAAuB;UAChC,UAAU,EAAE,MAAM;UAClB,YAAY,EAAE,6BAA6B;UAC3C,YAAY,EAAE,CACb;YACC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE;WACb;SAEF,EACD;UACC,MAAM,EAAE,cAAc;UACtB,OAAO,EAAE,2BAA2B;UACpC,UAAU,EAAE,UAAU;UACtB,YAAY,EAAE,6BAA6B;UAC3C,cAAc,EAAE;YACf,KAAK,EAAEA,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAI,mDAAmD;YACnG,MAAM,EAAE,EAAE;YACV,WAAW,EAAE;;SAEd,CACD;QACD,UAAU,EAAE;OACZ,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,qBAAqB;MAC7B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,8BAA8B;QACtC,MAAM,EAAE,8BAA8B;QACtC,WAAW,EAAE,0EAA0E;QACvF,OAAO,EAAE;OACT,EACD;QACC,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,yCAAyC;QACjD,WAAW,EAAE,mFAAmF;QAChG,OAAO,EAAE;OACT,EACD;QACC,MAAM,EAAE,0BAA0B;QAClC,MAAM,EAAE,0BAA0B;QAClC,WAAW,EAAE,4DAA4D;QACzE,OAAO,EAAE;OACT,EACD;QACC,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,qCAAqC;QAC7C,UAAU,EAAE,CACX;UACC,MAAM,EAAE,gCAAgC;UACxC,MAAM,EAAE,kBAAkB;UAC1B,WAAW,EAAE,4DAA4D;UACzE,OAAO,EAAE;SACT,EACD;UACC,MAAM,EAAE,kCAAkC;UAC1C,MAAM,EAAE,oBAAoB;UAC5B,WAAW,EAAE,8DAA8D;UAC3E,OAAO,EAAE;SACT,EACD;UACC,MAAM,EAAE,sCAAsC;UAC9C,MAAM,EAAE,wBAAwB;UAChC,WAAW,EAAE,oEAAoE;UACjF,OAAO,EAAE;SACT,EACD;UACC,MAAM,EAAE,gCAAgC;UACxC,MAAM,EAAE,kBAAkB;UAC1B,WAAW,EAAE,wEAAwE;UACrF,OAAO,EAAE;SACT,EACD;UACC,MAAM,EAAE,qCAAqC;UAC7C,MAAM,EAAE,uBAAuB;UAC/B,WAAW,EAAE,iEAAiE;UAC9E,OAAO,EAAE;SACT,EACD;UACC,MAAM,EAAE,iCAAiC;UACzC,MAAM,EAAE,mBAAmB;UAC3B,WAAW,EAAE,6DAA6D;UAC1E,OAAO,EAAE;SACT,EACD;UACC,MAAM,EAAE,qCAAqC;UAC7C,MAAM,EAAE,uBAAuB;UAC/B,WAAW,EAAE,oFAAoF;UACjG,OAAO,EAAE;SACT,CACD;QACD,OAAO,EAAE;OACT,CACD;MACD,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,uBAAuB;MAC/B,UAAU,EAAE,CACX;QACC,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,UAAU;QAClB,OAAO,EAAE,mOAAmO;QAC5O,WAAW,EAAE;OACb,EACD;QACC,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,sBAAsB;QAC9B,OAAO,EAAE;OACT,CACD;MACD,OAAO,EAAE;KACT,CACD;IACD,OAAO,EAAE;GACT,CACD;EACD,WAAW,EAAE,KAAK;EAClB,WAAW,EAAE;CACA;AAEjB,OAAO,MAAME,oBAAoB,GAAc;EAC3C,OAAO,EAAE,gCAAgC;EACzC,cAAc,EAAE,OAAO;EACvB,OAAO,EAAE,CACR;IACC,MAAM,EAAE,gBAAgB;IACxB,UAAU,EAAE,CACX;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,iBAAiB;MACzB,OAAO,EAAE;KACT,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,eAAe;MACvB,OAAO,EAAE,gBAAgB;MACzB,WAAW,EAAE;KACb,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,kBAAkB;MAC1B,OAAO,EAAE,yBAAyB;MAClC,WAAW,EAAE;KACb,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,cAAc;MACtB,OAAO,EAAE,qBAAqB;MAC9B,WAAW,EAAE;KACb,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,gBAAgB;MACxB,OAAO,EAAE,kBAAkB;MAC3B,WAAW,EAAE;KACb,EACD;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,oBAAoB;MAC5B,OAAO,EAAE;KACT,CACD;IACD,OAAO,EAAE;GACT,CACD;EACD,WAAW,EAAE,YAAY;EACzB,WAAW,EAAE,KAAK;EAClB,qBAAqB,EAAE;CACV;AAEjB,OAAO,MAAMC,qBAAqB,GAAc;EAC9C,OAAO,EAAE,CACR;IACC,MAAM,EAAE,gBAAgB;IACxB,UAAU,EAAE,CAEX;MACC,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,gBAAgB;MACxB,OAAO,EAAE,kBAAkB;MAC3B,WAAW,EAAE;KACb;IAEF;GACA,CACD;EACD,WAAW,EAAE,YAAY;EACzB,WAAW,EAAE;CACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}