{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, makeEnvironmentProviders, CSP_NONCE, Injectable, Optional, Inject } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, mapTo } from 'rxjs/operators';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { generate } from 'ng-zorro-antd/core/color';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { canUseDom, updateCSS } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * User should provide an object implements this interface to set global configurations.\n */\nconst NZ_CONFIG = new InjectionToken('nz-config');\nfunction provideNzConfig(config) {\n  return makeEnvironmentProviders([{\n    provide: NZ_CONFIG,\n    useValue: config\n  }]);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from @ant-design/colors(https://github.com/ant-design/ant-design-colors)\n */\nconst dynamicStyleMark = `-ant-${Date.now()}-${Math.random()}`;\nfunction getStyle(globalPrefixCls, theme) {\n  const variables = {};\n  const formatColor = (color, updater) => {\n    let clone = color.clone();\n    clone = updater?.(clone) || clone;\n    return clone.toRgbString();\n  };\n  const fillColor = (colorVal, type) => {\n    const baseColor = new TinyColor(colorVal);\n    const colorPalettes = generate(baseColor.toRgbString());\n    variables[`${type}-color`] = formatColor(baseColor);\n    variables[`${type}-color-disabled`] = colorPalettes[1];\n    variables[`${type}-color-hover`] = colorPalettes[4];\n    variables[`${type}-color-active`] = colorPalettes[7];\n    variables[`${type}-color-outline`] = baseColor.clone().setAlpha(0.2).toRgbString();\n    variables[`${type}-color-deprecated-bg`] = colorPalettes[1];\n    variables[`${type}-color-deprecated-border`] = colorPalettes[3];\n  };\n  // ================ Primary Color ================\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    const primaryColor = new TinyColor(theme.primaryColor);\n    const primaryColors = generate(primaryColor.toRgbString());\n    // Legacy - We should use semantic naming standard\n    primaryColors.forEach((color, index) => {\n      variables[`primary-${index + 1}`] = color;\n    });\n    // Deprecated\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, c => c.lighten(35));\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, c => c.lighten(20));\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, c => c.tint(20));\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, c => c.tint(50));\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, c => c.setAlpha(c.getAlpha() * 0.12));\n    const primaryActiveColor = new TinyColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, c => c.setAlpha(c.getAlpha() * 0.3));\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, c => c.darken(2));\n  }\n  // ================ Success Color ================\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  }\n  // ================ Warning Color ================\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  }\n  // ================= Error Color =================\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  }\n  // ================= Info Color ==================\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  }\n  // Convert to css variables\n  const cssList = Object.keys(variables).map(key => `--${globalPrefixCls}-${key}: ${variables[key]};`);\n  return `\n  :root {\n    ${cssList.join('\\n')}\n  }\n  `.trim();\n}\nfunction registerTheme(globalPrefixCls, theme, cspNonce) {\n  const style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, `${dynamicStyleMark}-dynamic-theme`, {\n      cspNonce\n    });\n  } else {\n    warn(`NzConfigService: SSR do not support dynamic theme with css variables.`);\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst isDefined = function (value) {\n  return value !== undefined;\n};\nconst defaultPrefixCls = 'ant';\nlet NzConfigService = /*#__PURE__*/(() => {\n  class NzConfigService {\n    constructor(defaultConfig, cspNonce) {\n      this.configUpdated$ = new Subject();\n      this.config = defaultConfig || {};\n      this.cspNonce = cspNonce;\n      if (this.config.theme) {\n        // If theme is set with NZ_CONFIG, register theme to make sure css variables work\n        registerTheme(this.getConfig().prefixCls?.prefixCls || defaultPrefixCls, this.config.theme, cspNonce);\n      }\n    }\n    getConfig() {\n      return this.config;\n    }\n    getConfigForComponent(componentName) {\n      return this.config[componentName];\n    }\n    getConfigChangeEventForComponent(componentName) {\n      return this.configUpdated$.pipe(filter(n => n === componentName), mapTo(undefined));\n    }\n    set(componentName, value) {\n      this.config[componentName] = {\n        ...this.config[componentName],\n        ...value\n      };\n      if (componentName === 'theme' && this.config.theme) {\n        registerTheme(this.getConfig().prefixCls?.prefixCls || defaultPrefixCls, this.config.theme, this.cspNonce);\n      }\n      this.configUpdated$.next(componentName);\n    }\n    static {\n      this.ɵfac = function NzConfigService_Factory(t) {\n        return new (t || NzConfigService)(i0.ɵɵinject(NZ_CONFIG, 8), i0.ɵɵinject(CSP_NONCE, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NzConfigService,\n        factory: NzConfigService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NzConfigService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* eslint-disable no-invalid-this */\n/**\n * This decorator is used to decorate properties. If a property is decorated, it would try to load default value from\n * config.\n */\n// eslint-disable-next-line\nfunction WithConfig() {\n  return function ConfigDecorator(target, propName, originalDescriptor) {\n    const privatePropName = `$$__zorroConfigDecorator__${propName}`;\n    Object.defineProperty(target, privatePropName, {\n      configurable: true,\n      writable: true,\n      enumerable: false\n    });\n    return {\n      get() {\n        const originalValue = originalDescriptor?.get ? originalDescriptor.get.bind(this)() : this[privatePropName];\n        const assignedByUser = (this.propertyAssignCounter?.[propName] || 0) > 1;\n        const configValue = this.nzConfigService.getConfigForComponent(this._nzModuleName)?.[propName];\n        if (assignedByUser && isDefined(originalValue)) {\n          return originalValue;\n        } else {\n          return isDefined(configValue) ? configValue : originalValue;\n        }\n      },\n      set(value) {\n        // If the value is assigned, we consider the newly assigned value as 'assigned by user'.\n        this.propertyAssignCounter = this.propertyAssignCounter || {};\n        this.propertyAssignCounter[propName] = (this.propertyAssignCounter[propName] || 0) + 1;\n        if (originalDescriptor?.set) {\n          originalDescriptor.set.bind(this)(value);\n        } else {\n          this[privatePropName] = value;\n        }\n      },\n      configurable: true,\n      enumerable: true\n    };\n  };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_CONFIG, NzConfigService, WithConfig, getStyle, provideNzConfig, registerTheme };\n//# sourceMappingURL=ng-zorro-antd-core-config.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}