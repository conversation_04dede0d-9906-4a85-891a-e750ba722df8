{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, signal, Inject, APP_INITIALIZER, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport * as i1 from 'angular-oauth2-oidc';\nimport { OAuthService, OAuthErrorEvent, OAuthInfoEvent, OAuthModule, OAuthStorage } from 'angular-oauth2-oidc';\nimport * as i2 from '@abp/ng.core';\nimport { AbpLocalStorageService, ConfigStateService, AuthService, HttpErrorReporterService, EnvironmentService, SessionStateService, TENANT_KEY, noop, AbstractAuthErrorFilter, CORE_OPTIONS, IS_EXTERNAL_REQUEST, NAVIGATE_TO_MANAGE_PROFILE, AuthGuard, authGuard, ApiInterceptor, PIPE_TO_LOGIN_FN_KEY, CHECK_AUTHENTICATION_STATE_FN_KEY, AuthErrorFilterService } from '@abp/ng.core';\nimport { pipe, of, from, lastValueFrom, EMPTY } from 'rxjs';\nimport { switchMap, tap, filter, take, map, finalize } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport compare from 'just-compare';\nconst oAuthStorage = localStorage;\nfunction clearOAuthStorage(storage = oAuthStorage) {\n  const keys = ['access_token', 'id_token', 'refresh_token', 'nonce', 'PKCE_verifier', 'expires_at', 'id_token_claims_obj', 'id_token_expires_at', 'id_token_stored_at', 'access_token_stored_at', 'granted_scopes', 'session_state'];\n  keys.forEach(key => storage.removeItem(key));\n}\nfunction storageFactory() {\n  return oAuthStorage;\n}\nlet RememberMeService = /*#__PURE__*/(() => {\n  class RememberMeService {\n    constructor() {\n      this.#rememberMe = 'remember_me';\n      this.localStorageService = inject(AbpLocalStorageService);\n    }\n    #rememberMe;\n    set(remember) {\n      this.localStorageService.setItem(this.#rememberMe, JSON.stringify(remember));\n    }\n    remove() {\n      this.localStorageService.removeItem(this.#rememberMe);\n    }\n    get() {\n      return Boolean(JSON.parse(this.localStorageService.getItem(this.#rememberMe)));\n    }\n    getFromToken(accessToken) {\n      const tokenBody = accessToken.split('.')[1].replace(/-/g, '+').replace(/_/g, '/');\n      const parsedToken = JSON.parse(atob(tokenBody));\n      return Boolean(parsedToken[this.#rememberMe]);\n    }\n    static {\n      this.ɵfac = function RememberMeService_Factory(t) {\n        return new (t || RememberMeService)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: RememberMeService,\n        factory: RememberMeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RememberMeService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst pipeToLogin = function (params, injector) {\n  const configState = injector.get(ConfigStateService);\n  const router = injector.get(Router);\n  const rememberMeService = injector.get(RememberMeService);\n  const authService = injector.get(AuthService);\n  return pipe(switchMap(() => configState.refreshAppState()), tap(() => {\n    rememberMeService.set(params.rememberMe || rememberMeService.get() || rememberMeService.getFromToken(authService.getAccessToken()));\n    if (params.redirectUrl) router.navigate([params.redirectUrl]);\n  }));\n};\n//Ref: https://github.com/manfredsteyer/angular-oauth2-oidc/issues/1214\nfunction isTokenExpired(expireDate) {\n  const currentDate = new Date().getTime();\n  return expireDate < currentDate;\n}\nconst checkAccessToken = function (injector) {\n  const configState = injector.get(ConfigStateService);\n  const oAuth = injector.get(OAuthService);\n  if (oAuth.hasValidAccessToken() && !configState.getDeep('currentUser.id')) {\n    clearOAuthStorage();\n  }\n};\nclass AuthFlowStrategy {\n  constructor(injector) {\n    this.injector = injector;\n    this.catchError = err => {\n      this.httpErrorReporter.reportError(err);\n      return of(null);\n    };\n    this.httpErrorReporter = injector.get(HttpErrorReporterService);\n    this.environment = injector.get(EnvironmentService);\n    this.configState = injector.get(ConfigStateService);\n    this.oAuthService = injector.get(OAuthService);\n    this.sessionState = injector.get(SessionStateService);\n    this.localStorageService = injector.get(AbpLocalStorageService);\n    this.oAuthConfig = this.environment.getEnvironment().oAuthConfig || {};\n    this.tenantKey = injector.get(TENANT_KEY);\n    this.router = injector.get(Router);\n    this.oAuthErrorFilterService = injector.get(OAuthErrorFilterService);\n    this.rememberMeService = injector.get(RememberMeService);\n    this.listenToOauthErrors();\n  }\n  init() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.oAuthConfig.clientId) {\n        const shouldClear = shouldStorageClear(_this.oAuthConfig.clientId, oAuthStorage);\n        if (shouldClear) clearOAuthStorage(oAuthStorage);\n      }\n      _this.oAuthService.configure(_this.oAuthConfig);\n      _this.oAuthService.events.pipe(filter(event => event.type === 'token_refresh_error')).subscribe(() => _this.navigateToLogin());\n      _this.navigateToPreviousUrl();\n      return _this.oAuthService.loadDiscoveryDocument().then(() => {\n        const isTokenExpire = isTokenExpired(_this.oAuthService.getAccessTokenExpiration());\n        if (isTokenExpire && _this.oAuthService.getRefreshToken()) {\n          return _this.refreshToken();\n        }\n        return Promise.resolve();\n      }).catch(_this.catchError);\n    })();\n  }\n  navigateToPreviousUrl() {\n    const {\n      responseType\n    } = this.oAuthConfig;\n    if (responseType === 'code') {\n      this.oAuthService.events.pipe(filter(event => event.type === 'token_received' && !!this.oAuthService.state), take(1), map(() => {\n        const redirectUri = decodeURIComponent(this.oAuthService.state);\n        if (redirectUri && redirectUri !== '/') {\n          return redirectUri;\n        }\n        return '/';\n      }), switchMap(redirectUri => this.configState.getOne$('currentUser').pipe(filter(user => !!user?.isAuthenticated), tap(() => this.router.navigateByUrl(redirectUri))))).subscribe();\n    }\n  }\n  refreshToken() {\n    return this.oAuthService.refreshToken().catch(() => clearOAuthStorage());\n  }\n  listenToOauthErrors() {\n    this.oAuthService.events.pipe(filter(event => event instanceof OAuthErrorEvent), tap(err => {\n      const shouldSkip = this.oAuthErrorFilterService.run(err);\n      if (!shouldSkip) {\n        clearOAuthStorage();\n      }\n    }), switchMap(() => this.configState.refreshAppState())).subscribe();\n  }\n}\nfunction shouldStorageClear(clientId, storage) {\n  const key = 'abpOAuthClientId';\n  if (!storage.getItem(key)) {\n    storage.setItem(key, clientId);\n    return false;\n  }\n  const shouldClear = storage.getItem(key) !== clientId;\n  if (shouldClear) storage.setItem(key, clientId);\n  return shouldClear;\n}\nclass AuthCodeFlowStrategy extends AuthFlowStrategy {\n  constructor() {\n    super(...arguments);\n    this.isInternalAuth = false;\n  }\n  init() {\n    var _superprop_getInit = () => super.init,\n      _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.checkRememberMeOption();\n      return _superprop_getInit().call(_this2).then(() => _this2.oAuthService.tryLogin().catch(noop)).then(() => _this2.oAuthService.setupAutomaticSilentRefresh());\n    })();\n  }\n  checkRememberMeOption() {\n    const accessToken = this.oAuthService.getAccessToken();\n    const isTokenExpire = isTokenExpired(this.oAuthService.getAccessTokenExpiration());\n    let rememberMe = this.rememberMeService.get();\n    if (accessToken && !rememberMe) {\n      const rememberMeValue = this.rememberMeService.getFromToken(accessToken);\n      this.rememberMeService.set(!!rememberMeValue);\n    }\n    rememberMe = this.rememberMeService.get();\n    if (accessToken && isTokenExpire && !rememberMe) {\n      this.rememberMeService.remove();\n      this.oAuthService.logOut();\n    }\n  }\n  navigateToLogin(queryParams) {\n    let additionalState = '';\n    if (queryParams?.returnUrl) {\n      additionalState = queryParams.returnUrl;\n    }\n    const cultureParams = this.getCultureParams(queryParams);\n    this.oAuthService.initCodeFlow(additionalState, cultureParams);\n  }\n  checkIfInternalAuth(queryParams) {\n    this.oAuthService.initCodeFlow('', this.getCultureParams(queryParams));\n    return false;\n  }\n  logout(queryParams) {\n    this.rememberMeService.remove();\n    return from(this.oAuthService.revokeTokenAndLogout(this.getCultureParams(queryParams)));\n  }\n  login(queryParams) {\n    this.oAuthService.initCodeFlow('', this.getCultureParams(queryParams));\n    return of(null);\n  }\n  getCultureParams(queryParams) {\n    const lang = this.sessionState.getLanguage();\n    const culture = {\n      culture: lang,\n      'ui-culture': lang\n    };\n    return {\n      ...(lang && culture),\n      ...queryParams\n    };\n  }\n}\nclass AuthPasswordFlowStrategy extends AuthFlowStrategy {\n  constructor() {\n    super(...arguments);\n    this.isInternalAuth = true;\n  }\n  listenToTokenExpiration() {\n    this.oAuthService.events.pipe(filter(event => event instanceof OAuthInfoEvent && event.type === 'token_expires' && event.info === 'access_token')).subscribe(() => {\n      if (this.oAuthService.getRefreshToken()) {\n        this.refreshToken();\n      } else {\n        this.oAuthService.logOut();\n        this.rememberMeService.remove();\n        this.configState.refreshAppState().subscribe();\n      }\n    });\n  }\n  init() {\n    var _superprop_getInit2 = () => super.init,\n      _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.checkRememberMeOption();\n      return _superprop_getInit2().call(_this3).then(() => _this3.listenToTokenExpiration());\n    })();\n  }\n  checkRememberMeOption() {\n    const accessToken = this.oAuthService.getAccessToken();\n    const isTokenExpire = isTokenExpired(this.oAuthService.getAccessTokenExpiration());\n    const rememberMe = this.rememberMeService.get();\n    if (accessToken && isTokenExpire && !rememberMe) {\n      this.rememberMeService.remove();\n      this.oAuthService.logOut();\n    }\n  }\n  navigateToLogin(queryParams) {\n    const router = this.injector.get(Router);\n    return router.navigate(['/account/login'], {\n      queryParams\n    });\n  }\n  checkIfInternalAuth() {\n    return true;\n  }\n  login(params) {\n    const tenant = this.sessionState.getTenant();\n    return from(this.oAuthService.fetchTokenUsingPasswordFlow(params.username, params.password, new HttpHeaders({\n      ...(tenant && tenant.id && {\n        [this.tenantKey]: tenant.id\n      })\n    }))).pipe(pipeToLogin(params, this.injector));\n  }\n  logout() {\n    const router = this.injector.get(Router);\n    const noRedirectToLogoutUrl = true;\n    return from(this.oAuthService.revokeTokenAndLogout(noRedirectToLogoutUrl)).pipe(switchMap(() => this.configState.refreshAppState()), tap(() => {\n      this.rememberMeService.remove();\n      router.navigateByUrl('/');\n    }));\n  }\n  refreshToken() {\n    return this.oAuthService.refreshToken().catch(() => {\n      clearOAuthStorage();\n      this.rememberMeService.remove();\n    });\n  }\n}\nconst AUTH_FLOW_STRATEGY = {\n  Code(injector) {\n    return new AuthCodeFlowStrategy(injector);\n  },\n  Password(injector) {\n    return new AuthPasswordFlowStrategy(injector);\n  }\n};\nlet AbpOAuthService = /*#__PURE__*/(() => {\n  class AbpOAuthService {\n    get oidc() {\n      return this.oAuthService.oidc;\n    }\n    set oidc(value) {\n      this.oAuthService.oidc = value;\n    }\n    get isInternalAuth() {\n      return this.strategy.isInternalAuth;\n    }\n    constructor(injector) {\n      this.injector = injector;\n      this.oAuthService = this.injector.get(OAuthService);\n    }\n    init() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        const environmentService = _this4.injector.get(EnvironmentService);\n        const result$ = environmentService.getEnvironment$().pipe(map(env => env?.oAuthConfig), filter(Boolean), tap(oAuthConfig => {\n          _this4.strategy = oAuthConfig.responseType === 'code' ? AUTH_FLOW_STRATEGY.Code(_this4.injector) : AUTH_FLOW_STRATEGY.Password(_this4.injector);\n        }), switchMap(() => from(_this4.strategy.init())), take(1));\n        return yield lastValueFrom(result$);\n      })();\n    }\n    logout(queryParams) {\n      if (!this.strategy) {\n        return EMPTY;\n      }\n      return this.strategy.logout(queryParams);\n    }\n    navigateToLogin(queryParams) {\n      this.strategy.navigateToLogin(queryParams);\n    }\n    login(params) {\n      return this.strategy.login(params);\n    }\n    get isAuthenticated() {\n      return this.oAuthService.hasValidAccessToken();\n    }\n    loginUsingGrant(grantType, parameters, headers) {\n      const {\n        clientId: client_id,\n        dummyClientSecret: client_secret\n      } = this.oAuthService;\n      const access_token = this.oAuthService.getAccessToken();\n      const p = {\n        access_token,\n        grant_type: grantType,\n        client_id,\n        ...parameters\n      };\n      if (client_secret) {\n        p['client_secret'] = client_secret;\n      }\n      return this.oAuthService.fetchTokenUsingGrant(grantType, p, headers);\n    }\n    getRefreshToken() {\n      return this.oAuthService.getRefreshToken();\n    }\n    getAccessToken() {\n      return this.oAuthService.getAccessToken();\n    }\n    refreshToken() {\n      return this.oAuthService.refreshToken();\n    }\n    getAccessTokenExpiration() {\n      return this.oAuthService.getAccessTokenExpiration();\n    }\n    static {\n      this.ɵfac = function AbpOAuthService_Factory(t) {\n        return new (t || AbpOAuthService)(i0.ɵɵinject(i0.Injector));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AbpOAuthService,\n        factory: AbpOAuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AbpOAuthService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet OAuthErrorFilterService = /*#__PURE__*/(() => {\n  class OAuthErrorFilterService extends AbstractAuthErrorFilter {\n    constructor() {\n      super(...arguments);\n      this._filters = signal([]);\n      this.filters = this._filters.asReadonly();\n    }\n    get(id) {\n      return this._filters().find(({\n        id: _id\n      }) => _id === id);\n    }\n    add(filter) {\n      this._filters.update(items => [...items, filter]);\n    }\n    patch(item) {\n      const _item = this.filters().find(({\n        id\n      }) => id === item.id);\n      if (!_item) {\n        return;\n      }\n      Object.assign(_item, item);\n    }\n    remove(id) {\n      const item = this.filters().find(({\n        id: _id\n      }) => _id === id);\n      if (!item) {\n        return;\n      }\n      this._filters.update(items => items.filter(({\n        id: _id\n      }) => _id !== id));\n    }\n    run(event) {\n      return this.filters().filter(({\n        executable\n      }) => !!executable).map(({\n        execute\n      }) => execute(event)).some(item => item);\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵOAuthErrorFilterService_BaseFactory;\n        return function OAuthErrorFilterService_Factory(t) {\n          return (ɵOAuthErrorFilterService_BaseFactory || (ɵOAuthErrorFilterService_BaseFactory = i0.ɵɵgetInheritedFactory(OAuthErrorFilterService)))(t || OAuthErrorFilterService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: OAuthErrorFilterService,\n        factory: OAuthErrorFilterService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return OAuthErrorFilterService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet OAuthConfigurationHandler = /*#__PURE__*/(() => {\n  class OAuthConfigurationHandler {\n    constructor(oAuthService, environmentService, options) {\n      this.oAuthService = oAuthService;\n      this.environmentService = environmentService;\n      this.options = options;\n      this.listenToSetEnvironment();\n    }\n    listenToSetEnvironment() {\n      this.environmentService.createOnUpdateStream(state => state).pipe(map(environment => environment.oAuthConfig), filter(config => !compare(config, this.options.environment.oAuthConfig))).subscribe(config => {\n        this.oAuthService.configure(config);\n      });\n    }\n    static {\n      this.ɵfac = function OAuthConfigurationHandler_Factory(t) {\n        return new (t || OAuthConfigurationHandler)(i0.ɵɵinject(i1.OAuthService), i0.ɵɵinject(i2.EnvironmentService), i0.ɵɵinject(CORE_OPTIONS));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: OAuthConfigurationHandler,\n        factory: OAuthConfigurationHandler.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return OAuthConfigurationHandler;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet OAuthApiInterceptor = /*#__PURE__*/(() => {\n  class OAuthApiInterceptor {\n    constructor(oAuthService, sessionState, httpWaitService, tenantKey) {\n      this.oAuthService = oAuthService;\n      this.sessionState = sessionState;\n      this.httpWaitService = httpWaitService;\n      this.tenantKey = tenantKey;\n    }\n    intercept(request, next) {\n      this.httpWaitService.addRequest(request);\n      const isExternalRequest = request.context?.get(IS_EXTERNAL_REQUEST);\n      const newRequest = isExternalRequest ? request : request.clone({\n        setHeaders: this.getAdditionalHeaders(request.headers)\n      });\n      return next.handle(newRequest).pipe(finalize(() => this.httpWaitService.deleteRequest(request)));\n    }\n    getAdditionalHeaders(existingHeaders) {\n      const headers = {};\n      const token = this.oAuthService.getAccessToken();\n      if (!existingHeaders?.has('Authorization') && token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const lang = this.sessionState.getLanguage();\n      if (!existingHeaders?.has('Accept-Language') && lang) {\n        headers['Accept-Language'] = lang;\n      }\n      const tenant = this.sessionState.getTenant();\n      if (!existingHeaders?.has(this.tenantKey) && tenant?.id) {\n        headers[this.tenantKey] = tenant.id;\n      }\n      headers['X-Requested-With'] = 'XMLHttpRequest';\n      return headers;\n    }\n    static {\n      this.ɵfac = function OAuthApiInterceptor_Factory(t) {\n        return new (t || OAuthApiInterceptor)(i0.ɵɵinject(i1.OAuthService), i0.ɵɵinject(i2.SessionStateService), i0.ɵɵinject(i2.HttpWaitService), i0.ɵɵinject(TENANT_KEY));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: OAuthApiInterceptor,\n        factory: OAuthApiInterceptor.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return OAuthApiInterceptor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @deprecated Use `abpOAuthGuard` *function* instead.\n */\nlet AbpOAuthGuard = /*#__PURE__*/(() => {\n  class AbpOAuthGuard {\n    constructor() {\n      this.oAuthService = inject(OAuthService);\n      this.authService = inject(AuthService);\n    }\n    canActivate(route, state) {\n      const hasValidAccessToken = this.oAuthService.hasValidAccessToken();\n      if (hasValidAccessToken) {\n        return true;\n      }\n      const params = {\n        returnUrl: state.url\n      };\n      this.authService.navigateToLogin(params);\n      return false;\n    }\n    static {\n      this.ɵfac = function AbpOAuthGuard_Factory(t) {\n        return new (t || AbpOAuthGuard)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AbpOAuthGuard,\n        factory: AbpOAuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AbpOAuthGuard;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst abpOAuthGuard = (route, state) => {\n  const oAuthService = inject(OAuthService);\n  const authService = inject(AuthService);\n  const hasValidAccessToken = oAuthService.hasValidAccessToken();\n  if (hasValidAccessToken) {\n    return true;\n  }\n  const params = {\n    returnUrl: state.url\n  };\n  authService.navigateToLogin(params);\n  return false;\n};\nconst NavigateToManageProfileProvider = {\n  provide: NAVIGATE_TO_MANAGE_PROFILE,\n  useFactory: () => {\n    const environment = inject(EnvironmentService);\n    return () => {\n      const env = environment.getEnvironment();\n      if (!env.oAuthConfig) {\n        console.warn('The oAuthConfig env is missing on environment.ts');\n        return;\n      }\n      const {\n        issuer\n      } = env.oAuthConfig;\n      const path = issuer.endsWith('/') ? issuer : `${issuer}/`;\n      window.open(`${path}Account/Manage?returnUrl=${window.location.href}`, '_self');\n    };\n  }\n};\nlet AbpOAuthModule = /*#__PURE__*/(() => {\n  class AbpOAuthModule {\n    static forRoot() {\n      return {\n        ngModule: AbpOAuthModule,\n        providers: [{\n          provide: AuthService,\n          useClass: AbpOAuthService\n        }, {\n          provide: AuthGuard,\n          useClass: AbpOAuthGuard\n        }, {\n          provide: authGuard,\n          useValue: abpOAuthGuard\n        }, {\n          provide: ApiInterceptor,\n          useClass: OAuthApiInterceptor\n        }, {\n          provide: PIPE_TO_LOGIN_FN_KEY,\n          useValue: pipeToLogin\n        }, {\n          provide: CHECK_AUTHENTICATION_STATE_FN_KEY,\n          useValue: checkAccessToken\n        }, {\n          provide: HTTP_INTERCEPTORS,\n          useExisting: ApiInterceptor,\n          multi: true\n        }, NavigateToManageProfileProvider, {\n          provide: APP_INITIALIZER,\n          multi: true,\n          deps: [OAuthConfigurationHandler],\n          useFactory: noop\n        }, OAuthModule.forRoot().providers, {\n          provide: OAuthStorage,\n          useClass: AbpLocalStorageService\n        }, {\n          provide: AuthErrorFilterService,\n          useExisting: OAuthErrorFilterService\n        }]\n      };\n    }\n    static {\n      this.ɵfac = function AbpOAuthModule_Factory(t) {\n        return new (t || AbpOAuthModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: AbpOAuthModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule, OAuthModule]\n      });\n    }\n  }\n  return AbpOAuthModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTH_FLOW_STRATEGY, AbpOAuthGuard, AbpOAuthModule, AbpOAuthService, AuthCodeFlowStrategy, AuthFlowStrategy, AuthPasswordFlowStrategy, NavigateToManageProfileProvider, OAuthApiInterceptor, OAuthConfigurationHandler, OAuthErrorFilterService, abpOAuthGuard, checkAccessToken, clearOAuthStorage, isTokenExpired, oAuthStorage, pipeToLogin, storageFactory };\n//# sourceMappingURL=abp-ng.oauth.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}