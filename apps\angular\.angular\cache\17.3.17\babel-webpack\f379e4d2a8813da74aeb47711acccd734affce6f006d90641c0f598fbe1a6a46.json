{"ast": null, "code": "import { ToolbarAction } from '@abp/ng.components/extensible';\nimport { ClaimsComponent } from '../../components/claims/claims.component';\nexport const DEFAULT_CLAIMS_TOOLBAR_ACTIONS = ToolbarAction.createMany([{\n  text: 'AbpIdentity::NewClaimType',\n  action: data => {\n    const component = data.getInjected(ClaimsComponent);\n    component.onAdd();\n  },\n  permission: 'AbpIdentity.ClaimTypes.Create',\n  icon: 'fa fa-plus'\n}]);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}