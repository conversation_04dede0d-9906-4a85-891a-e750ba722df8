{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n  if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11)) return 1;\n  if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 && !(i % 100 >= 12 && i % 100 <= 14) || f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 && !(f % 100 >= 12 && f % 100 <= 14)) return 3;\n  return 5;\n}\nexport default [\"hr-BA\", [[\"AM\", \"PM\"], u, u], u, [[\"N\", \"P\", \"U\", \"S\", \"Č\", \"P\", \"S\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"], [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"]], u, [[\"1.\", \"2.\", \"3.\", \"4.\", \"5.\", \"6.\", \"7.\", \"8.\", \"9.\", \"10.\", \"11.\", \"12.\"], [\"sij\", \"velj\", \"ožu\", \"tra\", \"svi\", \"lip\", \"srp\", \"kol\", \"ruj\", \"lis\", \"stu\", \"pro\"], [\"siječnja\", \"veljače\", \"ožujka\", \"travnja\", \"svibnja\", \"lipnja\", \"srpnja\", \"kolovoza\", \"rujna\", \"listopada\", \"studenoga\", \"prosinca\"]], [[\"1.\", \"2.\", \"3.\", \"4.\", \"5.\", \"6.\", \"7.\", \"8.\", \"9.\", \"10.\", \"11.\", \"12.\"], [\"sij\", \"velj\", \"ožu\", \"tra\", \"svi\", \"lip\", \"srp\", \"kol\", \"ruj\", \"lis\", \"stu\", \"pro\"], [\"siječanj\", \"veljača\", \"ožujak\", \"travanj\", \"svibanj\", \"lipanj\", \"srpanj\", \"kolovoz\", \"rujan\", \"listopad\", \"studeni\", \"prosinac\"]], [[\"pr.n.e.\", \"AD\"], [\"pr. Kr.\", \"po. Kr.\"], [\"prije Krista\", \"poslije Krista\"]], 1, [6, 0], [\"d. M. yy.\", \"d. MMM y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss (zzzz)\"], [\"{1} {0}\", u, \"{1} 'u' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"−\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"BAM\", \"KM\", \"konvertibilna marka\", {\n  \"AUD\": [u, \"$\"],\n  \"BAM\": [\"KM\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"EUR\": [u, \"€\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"HRK\": [\"kn\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [u, \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RUR\": [u, \"р.\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"],\n  \"XCD\": [u, \"$\"],\n  \"XPF\": [],\n  \"XXX\": []\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length", "f", "parseInt"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/hr-BA.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n    if (v === 0 && (i % 10 === 1 && !(i % 100 === 11)) || f % 10 === 1 && !(f % 100 === 11))\n        return 1;\n    if (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)) || f % 10 === Math.floor(f % 10) && (f % 10 >= 2 && f % 10 <= 4) && !(f % 100 >= 12 && f % 100 <= 14))\n        return 3;\n    return 5;\n}\nexport default [\"hr-BA\", [[\"AM\", \"PM\"], u, u], u, [[\"N\", \"P\", \"U\", \"S\", \"Č\", \"P\", \"S\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"], [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"], [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"]], u, [[\"1.\", \"2.\", \"3.\", \"4.\", \"5.\", \"6.\", \"7.\", \"8.\", \"9.\", \"10.\", \"11.\", \"12.\"], [\"sij\", \"velj\", \"ožu\", \"tra\", \"svi\", \"lip\", \"srp\", \"kol\", \"ruj\", \"lis\", \"stu\", \"pro\"], [\"siječnja\", \"veljače\", \"ožujka\", \"travnja\", \"svibnja\", \"lipnja\", \"srpnja\", \"kolovoza\", \"rujna\", \"listopada\", \"studenoga\", \"prosinca\"]], [[\"1.\", \"2.\", \"3.\", \"4.\", \"5.\", \"6.\", \"7.\", \"8.\", \"9.\", \"10.\", \"11.\", \"12.\"], [\"sij\", \"velj\", \"ožu\", \"tra\", \"svi\", \"lip\", \"srp\", \"kol\", \"ruj\", \"lis\", \"stu\", \"pro\"], [\"siječanj\", \"veljača\", \"ožujak\", \"travanj\", \"svibanj\", \"lipanj\", \"srpanj\", \"kolovoz\", \"rujan\", \"listopad\", \"studeni\", \"prosinac\"]], [[\"pr.n.e.\", \"AD\"], [\"pr. Kr.\", \"po. Kr.\"], [\"prije Krista\", \"poslije Krista\"]], 1, [6, 0], [\"d. M. yy.\", \"d. MMM y.\", \"d. MMMM y.\", \"EEEE, d. MMMM y.\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss (zzzz)\"], [\"{1} {0}\", u, \"{1} 'u' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"−\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"BAM\", \"KM\", \"konvertibilna marka\", { \"AUD\": [u, \"$\"], \"BAM\": [\"KM\"], \"BRL\": [u, \"R$\"], \"BYN\": [u, \"р.\"], \"CAD\": [u, \"$\"], \"CNY\": [u, \"¥\"], \"EUR\": [u, \"€\"], \"GBP\": [u, \"£\"], \"HKD\": [u, \"$\"], \"HRK\": [\"kn\"], \"ILS\": [u, \"₪\"], \"INR\": [u, \"₹\"], \"JPY\": [u, \"¥\"], \"KRW\": [u, \"₩\"], \"MXN\": [u, \"$\"], \"NZD\": [u, \"$\"], \"PHP\": [u, \"₱\"], \"RUR\": [u, \"р.\"], \"TWD\": [u, \"NT$\"], \"USD\": [u, \"$\"], \"VND\": [u, \"₫\"], \"XCD\": [u, \"$\"], \"XPF\": [], \"XXX\": [] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;IAAEC,CAAC,GAAGC,QAAQ,CAACX,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;EAChK,IAAIF,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAE,IAAIQ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAEA,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,EACnF,OAAO,CAAC;EACZ,IAAIJ,CAAC,KAAK,CAAC,IAAKJ,CAAC,GAAG,EAAE,KAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAE,IAAIQ,CAAC,GAAG,EAAE,KAAKP,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,IAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAE,IAAI,EAAEA,CAAC,GAAG,GAAG,IAAI,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,EACrN,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEb,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAE,aAAa,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,qBAAqB,EAAE;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,EAAE;EAAE,KAAK,EAAE;AAAG,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}