{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (factory) {\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    var v = factory(null, exports);\n    if (v !== undefined) module.exports = v;\n  } else if (typeof define === \"function\" && define.amd) {\n    define(\"@angular/common/locales/ta-SG\", [\"require\", \"exports\"], factory);\n  }\n})(function (require, exports) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  // THIS CODE IS GENERATED - DO NOT MODIFY\n  // See angular/tools/gulp-tasks/cldr/extract.js\n  var u = undefined;\n  function plural(n) {\n    if (n === 1) return 1;\n    return 5;\n  }\n  exports.default = ['ta-SG', [['மு.ப', 'பி.ப'], ['முற்பகல்', 'பிற்பகல்'], u], u, [['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'], ['ஞாயி.', 'திங்.', 'செவ்.', 'புத.', 'வியா.', 'வெள்.', 'சனி'], ['ஞாயிறு', 'திங்கள்', 'செவ்வாய்', 'புதன்', 'வியாழன்', 'வெள்ளி', 'சனி'], ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச']], u, [['ஜ', 'பி', 'மா', 'ஏ', 'மே', 'ஜூ', 'ஜூ', 'ஆ', 'செ', 'அ', 'ந', 'டி'], ['ஜன.', 'பிப்.', 'மார்.', 'ஏப்.', 'மே', 'ஜூன்', 'ஜூலை', 'ஆக.', 'செப்.', 'அக்.', 'நவ.', 'டிச.'], ['ஜனவரி', 'பிப்ரவரி', 'மார்ச்', 'ஏப்ரல்', 'மே', 'ஜூன்', 'ஜூலை', 'ஆகஸ்ட்', 'செப்டம்பர்', 'அக்டோபர்', 'நவம்பர்', 'டிசம்பர்']], u, [['கி.மு.', 'கி.பி.'], u, ['கிறிஸ்துவுக்கு முன்', 'அன்னோ டோமினி']], 0, [6, 0], ['d/M/yy', 'd MMM, y', 'd MMMM, y', 'EEEE, d MMMM, y'], ['a h:mm', 'a h:mm:ss', 'a h:mm:ss z', 'a h:mm:ss zzzz'], ['{1}, {0}', u, '{1} ’அன்று’ {0}', u], ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'], ['#,##0.###', '#,##0%', '¤ #,##0.00', '#E0'], 'SGD', '$', 'சிங்கப்பூர் டாலர்', {\n    'MYR': ['RM'],\n    'SGD': ['$'],\n    'THB': ['฿'],\n    'TWD': ['NT$'],\n    'USD': ['US$', '$']\n  }, 'ltr', plural];\n});\n//# sourceMappingURL=data:application/json;base64,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", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}