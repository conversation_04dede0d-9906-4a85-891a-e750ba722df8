{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n  if (i === 0 || i === 1) return 1;\n  if (e === 0 && !(i === 0) && i % 1000000 === 0 && v === 0 || !(e >= 0 && e <= 5)) return 4;\n  return 5;\n}\nexport default [\"fr-DJ\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"], [\"dimanche\", \"lundi\", \"mardi\", \"mercredi\", \"jeudi\", \"vendredi\", \"samedi\"], [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"janv.\", \"févr.\", \"mars\", \"avr.\", \"mai\", \"juin\", \"juil.\", \"août\", \"sept.\", \"oct.\", \"nov.\", \"déc.\"], [\"janvier\", \"février\", \"mars\", \"avril\", \"mai\", \"juin\", \"juillet\", \"août\", \"septembre\", \"octobre\", \"novembre\", \"décembre\"]], u, [[\"av. J.-C.\", \"ap. J.-C.\"], u, [\"avant Jésus-Christ\", \"après Jésus-Christ\"]], 6, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", \"{1}, {0}\", \"{1} 'à' {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"DJF\", \"Fdj\", \"franc djiboutien\", {\n  \"ARS\": [\"$AR\", \"$\"],\n  \"AUD\": [\"$AU\", \"$\"],\n  \"BEF\": [\"FB\"],\n  \"BMD\": [\"$BM\", \"$\"],\n  \"BND\": [\"$BN\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"BZD\": [\"$BZ\", \"$\"],\n  \"CAD\": [\"$CA\", \"$\"],\n  \"CLP\": [\"$CL\", \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"COP\": [\"$CO\", \"$\"],\n  \"CYP\": [\"£CY\"],\n  \"DJF\": [\"Fdj\"],\n  \"EGP\": [u, \"£E\"],\n  \"FJD\": [\"$FJ\", \"$\"],\n  \"FKP\": [\"£FK\", \"£\"],\n  \"FRF\": [\"F\"],\n  \"GBP\": [\"£GB\", \"£\"],\n  \"GIP\": [\"£GI\", \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"IEP\": [\"£IE\"],\n  \"ILP\": [\"£IL\"],\n  \"ITL\": [\"₤IT\"],\n  \"JPY\": [u, \"¥\"],\n  \"KMF\": [u, \"FC\"],\n  \"LBP\": [\"£LB\", \"£L\"],\n  \"MTP\": [\"£MT\"],\n  \"MXN\": [\"$MX\", \"$\"],\n  \"NAD\": [\"$NA\", \"$\"],\n  \"NIO\": [u, \"$C\"],\n  \"NZD\": [\"$NZ\", \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RHD\": [\"$RH\"],\n  \"RON\": [u, \"L\"],\n  \"RWF\": [u, \"FR\"],\n  \"SBD\": [\"$SB\", \"$\"],\n  \"SGD\": [\"$SG\", \"$\"],\n  \"SRD\": [\"$SR\", \"$\"],\n  \"TOP\": [u, \"$T\"],\n  \"TTD\": [\"$TT\", \"$\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [\"$US\", \"$\"],\n  \"UYU\": [\"$UY\", \"$\"],\n  \"WST\": [\"$WS\"],\n  \"XCD\": [u, \"$\"],\n  \"XPF\": [\"FCFP\"],\n  \"ZMW\": [u, \"Kw\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n", "i", "Math", "floor", "abs", "v", "toString", "replace", "length", "e", "parseInt"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/fr-DJ.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n    if (i === 0 || i === 1)\n        return 1;\n    if (e === 0 && (!(i === 0) && (i % 1000000 === 0 && v === 0)) || !(e >= 0 && e <= 5))\n        return 4;\n    return 5;\n}\nexport default [\"fr-DJ\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"], [\"dimanche\", \"lundi\", \"mardi\", \"mercredi\", \"jeudi\", \"vendredi\", \"samedi\"], [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"janv.\", \"févr.\", \"mars\", \"avr.\", \"mai\", \"juin\", \"juil.\", \"août\", \"sept.\", \"oct.\", \"nov.\", \"déc.\"], [\"janvier\", \"février\", \"mars\", \"avril\", \"mai\", \"juin\", \"juillet\", \"août\", \"septembre\", \"octobre\", \"novembre\", \"décembre\"]], u, [[\"av. J.-C.\", \"ap. J.-C.\"], u, [\"avant Jésus-Christ\", \"après Jésus-Christ\"]], 6, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1} {0}\", \"{1}, {0}\", \"{1} 'à' {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"DJF\", \"Fdj\", \"franc djiboutien\", { \"ARS\": [\"$AR\", \"$\"], \"AUD\": [\"$AU\", \"$\"], \"BEF\": [\"FB\"], \"BMD\": [\"$BM\", \"$\"], \"BND\": [\"$BN\", \"$\"], \"BYN\": [u, \"р.\"], \"BZD\": [\"$BZ\", \"$\"], \"CAD\": [\"$CA\", \"$\"], \"CLP\": [\"$CL\", \"$\"], \"CNY\": [u, \"¥\"], \"COP\": [\"$CO\", \"$\"], \"CYP\": [\"£CY\"], \"DJF\": [\"Fdj\"], \"EGP\": [u, \"£E\"], \"FJD\": [\"$FJ\", \"$\"], \"FKP\": [\"£FK\", \"£\"], \"FRF\": [\"F\"], \"GBP\": [\"£GB\", \"£\"], \"GIP\": [\"£GI\", \"£\"], \"HKD\": [u, \"$\"], \"IEP\": [\"£IE\"], \"ILP\": [\"£IL\"], \"ITL\": [\"₤IT\"], \"JPY\": [u, \"¥\"], \"KMF\": [u, \"FC\"], \"LBP\": [\"£LB\", \"£L\"], \"MTP\": [\"£MT\"], \"MXN\": [\"$MX\", \"$\"], \"NAD\": [\"$NA\", \"$\"], \"NIO\": [u, \"$C\"], \"NZD\": [\"$NZ\", \"$\"], \"PHP\": [u, \"₱\"], \"RHD\": [\"$RH\"], \"RON\": [u, \"L\"], \"RWF\": [u, \"FR\"], \"SBD\": [\"$SB\", \"$\"], \"SGD\": [\"$SG\", \"$\"], \"SRD\": [\"$SR\", \"$\"], \"TOP\": [u, \"$T\"], \"TTD\": [\"$TT\", \"$\"], \"TWD\": [u, \"NT$\"], \"USD\": [\"$US\", \"$\"], \"UYU\": [\"$UY\", \"$\"], \"WST\": [\"$WS\"], \"XCD\": [u, \"$\"], \"XPF\": [\"FCFP\"], \"ZMW\": [u, \"Kw\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;IAAEE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC;IAAEM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,MAAM;IAAEC,CAAC,GAAGC,QAAQ,CAACX,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;EACzK,IAAIN,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAClB,OAAO,CAAC;EACZ,IAAIQ,CAAC,KAAK,CAAC,IAAK,EAAER,CAAC,KAAK,CAAC,CAAC,IAAKA,CAAC,GAAG,OAAO,KAAK,CAAC,IAAII,CAAC,KAAK,CAAG,IAAI,EAAEI,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAChF,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEb,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAEA,CAAC,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,MAAM,CAAC;EAAE,KAAK,EAAE,CAACA,CAAC,EAAE,IAAI;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}