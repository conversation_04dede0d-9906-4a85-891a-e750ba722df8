{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"kam\", [[\"Ĩyakwakya\", \"Ĩyawĩoo\"], u, u], u, [[\"Y\", \"W\", \"E\", \"A\", \"A\", \"A\", \"A\"], [\"Wky\", \"Wkw\", \"Wkl\", \"Wtũ\", \"Wkn\", \"Wtn\", \"Wth\"], [\"Wa kyumwa\", \"Wa kwambĩlĩlya\", \"Wa kelĩ\", \"Wa katatũ\", \"Wa kana\", \"Wa katano\", \"Wa thanthatũ\"], [\"Wky\", \"Wkw\", \"Wkl\", \"Wtũ\", \"Wkn\", \"Wtn\", \"Wth\"]], u, [[\"M\", \"K\", \"K\", \"K\", \"K\", \"T\", \"M\", \"N\", \"K\", \"Ĩ\", \"Ĩ\", \"Ĩ\"], [\"Mbe\", \"Kel\", \"Kt<PERSON>\", \"Kan\", \"Ktn\", \"Tha\", \"<PERSON>o\", \"Nya\", \"Knd\", \"Ĩku\", \"Ĩkm\", \"Ĩkl\"], [\"Mwai wa mbee\", \"Mwai wa kelĩ\", \"Mwai wa katatũ\", \"Mwai wa kana\", \"Mwai wa katano\", \"Mwai wa thanthatũ\", \"Mwai wa muonza\", \"Mwai wa nyaanya\", \"Mwai wa kenda\", \"Mwai wa ĩkumi\", \"Mwai wa ĩkumi na ĩmwe\", \"Mwai wa ĩkumi na ilĩ\"]], u, [[\"MY\", \"IY\"], u, [\"Mbee wa Yesũ\", \"Ĩtina wa Yesũ\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"KES\", \"Ksh\", \"Silingi ya Kenya\", {\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KES\": [\"Ksh\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];", "map": {"version": 3, "names": ["u", "undefined", "plural", "val", "n"], "sources": ["C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular/common/locales/kam.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n    const n = val;\n    return 5;\n}\nexport default [\"kam\", [[\"Ĩyakwakya\", \"Ĩyawĩoo\"], u, u], u, [[\"Y\", \"W\", \"E\", \"A\", \"A\", \"A\", \"A\"], [\"Wky\", \"Wkw\", \"Wkl\", \"Wtũ\", \"Wkn\", \"Wtn\", \"Wth\"], [\"Wa kyumwa\", \"Wa kwambĩlĩlya\", \"Wa kelĩ\", \"Wa katatũ\", \"Wa kana\", \"Wa katano\", \"Wa thanthatũ\"], [\"Wky\", \"Wkw\", \"Wkl\", \"Wtũ\", \"Wkn\", \"Wtn\", \"Wth\"]], u, [[\"M\", \"K\", \"K\", \"K\", \"K\", \"T\", \"M\", \"N\", \"K\", \"Ĩ\", \"Ĩ\", \"Ĩ\"], [\"Mbe\", \"Kel\", \"Kt<PERSON>\", \"Kan\", \"Ktn\", \"Tha\", \"<PERSON>o\", \"Nya\", \"Knd\", \"Ĩku\", \"Ĩkm\", \"Ĩkl\"], [\"Mwai wa mbee\", \"Mwai wa kelĩ\", \"Mwai wa katatũ\", \"Mwai wa kana\", \"Mwai wa katano\", \"Mwai wa thanthatũ\", \"Mwai wa muonza\", \"Mwai wa nyaanya\", \"Mwai wa kenda\", \"Mwai wa ĩkumi\", \"Mwai wa ĩkumi na ĩmwe\", \"Mwai wa ĩkumi na ilĩ\"]], u, [[\"MY\", \"IY\"], u, [\"Mbee wa Yesũ\", \"Ĩtina wa Yesũ\"]], 0, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"KES\", \"Ksh\", \"Silingi ya Kenya\", { \"JPY\": [\"JP¥\", \"¥\"], \"KES\": [\"Ksh\"], \"USD\": [\"US$\", \"$\"] }, \"ltr\", plural];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGC,SAAS;AACnB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,CAAC,GAAGD,GAAG;EACb,OAAO,CAAC;AACZ;AACA,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,sBAAsB,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG;AAAE,CAAC,EAAE,KAAK,EAAEE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}