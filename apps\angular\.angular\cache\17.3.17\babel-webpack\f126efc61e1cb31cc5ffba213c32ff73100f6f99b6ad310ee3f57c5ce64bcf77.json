{"ast": null, "code": "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n  if (list === void 0) {\n    list = [];\n  }\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList :\n  // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "map": {"version": 3, "names": ["getScrollParent", "getParentNode", "getWindow", "isScrollParent", "listScrollParents", "element", "list", "_element$ownerDocumen", "scrollParent", "isBody", "ownerDocument", "body", "win", "target", "concat", "visualViewport", "updatedList"], "sources": ["c:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js"], "sourcesContent": ["import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,sBAAsB;AAClD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,cAAc,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACvD,IAAIC,qBAAqB;EAEzB,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,EAAE;EACX;EAEA,IAAIE,YAAY,GAAGR,eAAe,CAACK,OAAO,CAAC;EAC3C,IAAII,MAAM,GAAGD,YAAY,MAAM,CAACD,qBAAqB,GAAGF,OAAO,CAACK,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAACI,IAAI,CAAC;EAC7H,IAAIC,GAAG,GAAGV,SAAS,CAACM,YAAY,CAAC;EACjC,IAAIK,MAAM,GAAGJ,MAAM,GAAG,CAACG,GAAG,CAAC,CAACE,MAAM,CAACF,GAAG,CAACG,cAAc,IAAI,EAAE,EAAEZ,cAAc,CAACK,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC,GAAGA,YAAY;EAC7H,IAAIQ,WAAW,GAAGV,IAAI,CAACQ,MAAM,CAACD,MAAM,CAAC;EACrC,OAAOJ,MAAM,GAAGO,WAAW;EAAG;EAC9BA,WAAW,CAACF,MAAM,CAACV,iBAAiB,CAACH,aAAa,CAACY,MAAM,CAAC,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}